{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 盘口数据特征\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- tick数据量太大，如果保存所有数据，没有足够的存储空间\n", "    - 只保存主连合约\n", "    - 保存文件格式除考虑支持压缩\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import json\n", "from datetime import datetime, date"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode\n", "ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["markcode = {}\n", "markcode['SC'] = ds.get_all_fut_codes('SC')\n", "markcode['ZC'] = ds.get_all_fut_codes('ZC')\n", "markcode['DC'] = ds.get_all_fut_codes('DC')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def load_main_code_date_list(ds: DataSource):\n", "    names = ds.get_all_fut_names(\"\")\n", "    with open('d:/wh6/sys/ChangeMonthEx.json', encoding='gbk') as json_file:\n", "        data = json.load(json_file)\n", "    maintb = {}\n", "    for val in data.values():\n", "        if 'Name' in val.keys() and val['Name'] in names:\n", "            code = ds.get_fut_code_by_name(val['Name'])\n", "            lsdt = []\n", "            for i in range(int(val['num'])):\n", "                txt = val[\"{0:03d}\".format(i)].split(\",\")\n", "                lsdt.append((txt[0].replace(\"/\", \"\"), txt[1]))\n", "            maintb[code] = lsdt\n", "    return maintb"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_main_code_by_date(market: str, code: str, datestr: str, maintb: dict):\n", "    code = code.upper()\n", "    if code not in maintb.keys() or len(datestr) != 8:\n", "        return None\n", "    if len(maintb[code]) <= 1:\n", "        return None\n", "    for i in range(len(maintb[code]) - 1):\n", "        if datestr >= maintb[code][i][0] and datestr < maintb[code][i+1][0]:\n", "            if market.upper() == 'ZC':\n", "                return f\"{code}{maintb[code][i][1][1:]}\"\n", "            else:\n", "                return f\"{code}{maintb[code][i][1]}\"\n", "    today = str(date.today()).replace('-', '')\n", "    if datestr >= maintb[code][-1][0] and datestr <= today:\n", "        if market.upper() == 'ZC':\n", "            return f\"{code}{maintb[code][i][1][1:]}\"\n", "        else:\n", "            return f\"{code}{maintb[code][i][1]}\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def is_exist_folder(path: str, sub_folder: str):\n", "    return sub_folder in os.listdir(path) \n", "    \n", "def is_exist_file(filename: str):\n", "    return os.path.isfile(filename)\n", "\n", "def get_dir_list(dir_path):\n", "    # Get a list of the content inside the directory\n", "    files = os.listdir(dir_path)\n", "\n", "    # Print out the list of files\n", "    dir_list = []\n", "    for file in files:\n", "        dir_list.append(file)\n", "    return dir_list    "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['TradingDay']} {row['UpdateTime']} {row['UpdateMillisec']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def read_csv_file(fname, combin_dt=False):\n", "    columns=[\n", "        'localtime',\n", "        'InstrumentID',\n", "        'TradingDay',\n", "        'ActionDay',\n", "        'UpdateTime',\n", "        'UpdateMillisec',\n", "        'LastPrice',\n", "        'Volume',\n", "        'HighestPrice',\n", "        'LowestPrice',\n", "        'OpenPrice',\n", "        'ClosePrice',\n", "        'AveragePrice',\n", "        'AskPrice1',\n", "        'AskVolume1',\n", "        'BidPrice1',\n", "        'BidVolume1',\n", "        'UpperLimitPrice',\n", "        'LowerLimitPrice',\n", "        'OpenInterest',\n", "        'Turnover',\n", "        'PreClosePrice',\n", "        'PreOpenInterest',\n", "        'PreSettlementPrice']\n", "    #read csv file \n", "    df = pd.read_csv(fname, header = None, names = columns)\n", "    if combin_dt:\n", "        df['TradingTime'] = df.apply(combine_datetime, axis=1)\n", "        df = df[['InstrumentID',\n", "                'TradingTime',\n", "                'LastPrice',\n", "                'Volume',\n", "                'AskPrice1',\n", "                'AskVolume1',\n", "                'BidPrice1',\n", "                'BidVolume1']]\n", "    else:\n", "        df = df[['InstrumentID',\n", "                'TradingDay',\n", "                'UpdateTime',\n", "                'UpdateMillisec',\n", "                'LastPrice',\n", "                'Volume',\n", "                'AskPrice1',\n", "                'AskVolume1',\n", "                'BidPrice1',\n", "                'BidVolume1']]\n", "    return df\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def test_signle_code():\n", "    # 加载主力日期切换表\n", "    maintb = load_main_code_date_list(ds)\n", "    # print(maintb)\n", "    # 起止日期间所有日期列表\n", "    start = '20190823'\n", "    end = '20200214'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td < td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = 'd:/hqraw/tick/2019/' \n", "    # 有交易数据的日期列表\n", "    sub_dir = get_dir_list(directory)\n", "    market = 'ZC' # 'SC', 'DC'\n", "    code='SA' # SA\n", "    dfs = pd.DataFrame()\n", "    for day in all_day:\n", "        if day not in sub_dir:\n", "            continue\n", "        # 获取当日主力期货的代码名即文件名\n", "        lb = get_main_code_by_date(market, code, day, maintb)\n", "        fname = f'{directory}/{day}/{lb}.csv'\n", "        if not is_exist_file(fname):\n", "            continue\n", "        # print(f'read csv file: {fname}')\n", "        df = read_csv_file(fname)\n", "        dfs = pd.concat([dfs, df])\n", "        # print(dfs.shape)\n", "    # 将按年汇总的tick数据写入文件\n", "    print(f'{code}: {dfs.shape}')\n", "    # dfs.to_parquet(f'e:/hqdata/tick/{code}2020.parquet')\n", "    print(dfs.tail())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载主力日期切换表\n", "maintb = load_main_code_date_list(ds)\n", "# print(maintb)\n", "# 起止日期间所有日期列表\n", "start = '20190823'\n", "end = '20200214'\n", "td=datetime.strptime(start, '%Y%m%d').date()\n", "td_end=datetime.strptime(end, '%Y%m%d').date()\n", "all_day = []\n", "while td < td_end:\n", "    td = date.fromordinal(td.toordinal() + 1)\n", "    all_day.append(td.strftime(\"%Y%m%d\"))\n", "print(f'all day: {len(all_day)}')\n", "# Define directory to list from\n", "directory = 'f:/hqdata/raw/2019/' \n", "# 有交易数据的日期列表\n", "sub_dir = get_dir_list(directory)\n", "cnt = 0\n", "markets=['SC', 'ZC', 'DC'] # \n", "for mk in markets:\n", "    codes = ds.get_all_fut_codes(mk)\n", "    # print(f'codes: {len(codes)}')\n", "    for code in codes:\n", "        # print(f'{cnt}: {code}')\n", "        # if cnt > 5:\n", "        #     break\n", "        if is_exist_file(f'f:/hqdata/tick/2019/{code}2019.parquet'):\n", "            continue\n", "        dfs = pd.DataFrame()\n", "        for day in all_day:\n", "            if day not in sub_dir:\n", "                continue\n", "            # 获取当日主力期货的代码名即文件名\n", "            lb = get_main_code_by_date(mk, code, day, maintb)\n", "            fname = f'{directory}/{day}/{lb}.csv'\n", "            if not is_exist_file(fname):\n", "                continue\n", "            df = read_csv_file(fname)\n", "            df1 = df.loc[(df['UpdateTime'] >= '09:00:00') & (df['UpdateTime'] <= '15:00:00')]\n", "            df2 = df.loc[(df['UpdateTime'] >= '21:00:00') & (df['UpdateTime'] <= '24:00:00')]\n", "            dfs = pd.concat([dfs, df1, df2])\n", "        # 将按年汇总的tick数据写入文件\n", "        print(f'{cnt} {code}: {dfs.shape}')\n", "        if dfs.shape[0] > 0:\n", "            dfs.to_parquet(f'f:/hqdata/tick/2019/{code}2019.parquet')\n", "        cnt = cnt + 1\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["---\n", "### 测试\n", "- 加载导出汇总的parquet数据 "]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import numpy as np\n", "from datetime import datetime, date\n", "from pyqlab.const import MAIN_FUT_MARKET_CODES"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def clear_out_time_range():\n", "    # 清除非交易时间的数据\n", "    codes = ds.get_all_fut_codes('')\n", "    for code in codes:\n", "        filename = f'e:/hqdata/tick/{code}2020.parquet'\n", "        if os.path.isfile(filename):\n", "            df = pd.read_parquet(filename)\n", "            df1 = df.loc[(df['UpdateTime'] >= '09:00:00') & (df['UpdateTime'] <= '15:00:00')]\n", "            df2 = df.loc[(df['UpdateTime'] >= '21:00:00') & (df['UpdateTime'] <= '24:00:00')]\n", "            df = pd.concat([df1, df2])\n", "            df.to_parquet(filename)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def generate_date_segments(days: int):\n", "    '''\n", "    生成日期段\n", "    '''\n", "    start = datetime.strptime('20200101', '%Y%m%d')\n", "    end = datetime.strptime('20201201', '%Y%m%d')\n", "    dates = pd.date_range(start, end, freq='D')\n", "    date_segments = []\n", "    for i in range(0, len(dates), days):\n", "        if i + days - 1 < len(dates):\n", "            date_segments.append((int(dates[i].strftime('%Y%m%d')), int(dates[i + days - 1].strftime('%Y%m%d'))))\n", "        else:\n", "            date_segments.append((int(dates[i].strftime('%Y%m%d')), int(dates[-1].strftime('%Y%m%d'))))\n", "    return date_segments"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(20200101, 20200130), (20200131, 20200229), (20200301, 20200330), (20200331, 20200429), (20200430, 20200529), (20200530, 20200628), (20200629, 20200728), (20200729, 20200827), (20200828, 20200926), (20200927, 20201026), (20201027, 20201125), (20201126, 20201201)]\n"]}], "source": ["dsec = generate_date_segments(30)\n", "print(dsec)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['TradingDay']} {row['UpdateTime']} {row['UpdateMillisec']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "# 读取tick数据parquet文件\n", "def load_tick_data(code, start, end):\n", "    filename = f'f:/hqdata/tick/2020/{code}2020.parquet'\n", "    if os.path.isfile(filename):\n", "        df = pd.read_parquet(filename)\n", "        # df = df.loc[(df['TradingDay'] >= start) & (df['TradingDay'] <= end)]\n", "        return df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "def load_all_tick_data(start, end):\n", "    dfs = {}\n", "    codes = ds.get_all_fut_codes('')\n", "    for code in codes:\n", "        df = load_tick_data(code, start, end)\n", "        if df.shape[0] > 0:\n", "            dfs[code] = df\n", "    return dfs\n", "\n", "def load_all_combine_tick_data(start, end):\n", "    dfs = pd.DataFrame()\n", "    codes = ds.get_all_fut_codes('')\n", "    for code in codes:\n", "        df = load_tick_data(code, start, end)\n", "        if df.shape[0] > 0:\n", "            df['code'] = code\n", "            dfs = pd.concat([dfs, df])\n", "    dfs['datetime'] = dfs.apply(combine_datetime, axis=1)\n", "    dfs = dfs.sort_values(by='datetime', ascending=True)\n", "    return dfs\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df=load_tick_data('A', 20200101, 20201231)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["import datetime\n", "\n", "def year_month_segment(year: int):\n", "    seg = []\n", "    for month in range(1, 13):\n", "        first_day = datetime.date(year, month, 1)\n", "        if month == 12:\n", "            last_day = datetime.date(year+1, 1, 1) - datetime.timedelta(days=1)\n", "        else:\n", "            last_day = datetime.date(year, month+1, 1) - datetime.timedelta(days=1)\n", "        seg.append((first_day.strftime('%Y%m%d'), last_day.strftime('%Y%m%d')))\n", "        # print(f\"Month {month}: First day: {first_day.strftime('%Y%m%d')}, Last day: {last_day.strftime('%Y%m%d')}\")\n", "    return seg"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('20200101', '20200131'),\n", " ('20200201', '20200229'),\n", " ('20200301', '20200331'),\n", " ('20200401', '20200430'),\n", " ('20200501', '20200531'),\n", " ('20200601', '20200630'),\n", " ('20200701', '20200731'),\n", " ('20200801', '20200831'),\n", " ('20200901', '20200930'),\n", " ('20201001', '20201031'),\n", " ('20201101', '20201130'),\n", " ('20201201', '20201231')]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["dt_seg = year_month_segment(2020)\n", "dt_seg"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["dt_seg = [\n", " ('20200201', '20200229'),\n", " ('20200301', '20200331'),\n", " ('20200401', '20200430'),\n", " ('20200501', '20200531'),\n", " ('20200601', '20200630'),\n", " ('20200701', '20200731'),\n", "]"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('20200201', '20200229') (4668658, 4)\n", "('20200301', '20200331') (9011332, 4)\n", "('20200401', '20200430') (9592544, 4)\n", "('20200501', '20200531') (13009259, 4)\n", "('20200601', '20200630') (13589874, 4)\n", "('20200701', '20200731') (13150784, 4)\n"]}], "source": ["for seg in dt_seg:\n", "    dfs = pd.DataFrame()\n", "    dt0 = datetime.strptime(seg[0], '%Y%m%d')\n", "    dt1 = datetime.strptime(seg[1], '%Y%m%d')\n", "    for mk, codes in MAIN_FUT_MARKET_CODES.items():\n", "        for code in codes:\n", "            filename = f'f:/hqdata/tick/2020/{code}2020.parquet'\n", "            if os.path.isfile(filename):\n", "                df = pd.read_parquet(filename)\n", "                df = df.loc[(df['DateTime'] >= dt0) & (df['DateTime'] <= dt1)]\n", "                if df.empty:\n", "                    continue\n", "                df['DateTime'] = df['DateTime'].dt.floor('1s') # 去掉毫秒部分\n", "                df = df.groupby(['InstrumentID', 'DateTime']).agg({'LastPrice': 'last', 'Volume': 'last'}).reset_index()\n", "                df['InstrumentID'] = f'{code}9999.{mk}'\n", "                df = df.rename(columns={'InstrumentID': 'code', 'DateTime': 'datetime', 'LastPrice': 'price','Volume': 'volume'})\n", "                dfs = pd.concat([dfs, df])\n", "    dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    dfs.to_parquet(f'f:/hqdata/tick/2020/{seg[0][0:6]}.parquet', engine='fastparquet')\n", "    print(seg, dfs.shape)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["df1 = pd.read_parquet('f:/hqdata/tick/2020/202002a.parquet')\n", "df2 = pd.read_parquet('f:/hqdata/tick/2020/202002b.parquet')\n", "df = pd.concat([df1, df2])\n", "df.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "df.to_parquet(f'f:/hqdata/tick/2020/202002.parquet', engine='fastparquet')\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CU9999.SC</td>\n", "      <td>2020-02-03 09:00:00</td>\n", "      <td>44780.0</td>\n", "      <td>2226.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SP9999.SC</td>\n", "      <td>2020-02-03 09:00:00</td>\n", "      <td>4412.0</td>\n", "      <td>1155.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SC9999.SC</td>\n", "      <td>2020-02-03 09:00:00</td>\n", "      <td>415.5</td>\n", "      <td>76.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SR9999.ZC</td>\n", "      <td>2020-02-03 09:00:00</td>\n", "      <td>5484.0</td>\n", "      <td>4462.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HC9999.SC</td>\n", "      <td>2020-02-03 09:00:00</td>\n", "      <td>3246.0</td>\n", "      <td>3982.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133177</th>\n", "      <td>BU9999.SC</td>\n", "      <td>2020-02-28 15:00:00</td>\n", "      <td>2736.0</td>\n", "      <td>668988.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131318</th>\n", "      <td>Y9999.DC</td>\n", "      <td>2020-02-28 15:00:00</td>\n", "      <td>5566.0</td>\n", "      <td>731307.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108249</th>\n", "      <td>SN9999.SC</td>\n", "      <td>2020-02-28 15:00:00</td>\n", "      <td>131770.0</td>\n", "      <td>20157.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128678</th>\n", "      <td>PP9999.DC</td>\n", "      <td>2020-02-28 15:00:00</td>\n", "      <td>6834.0</td>\n", "      <td>433085.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117685</th>\n", "      <td>EG9999.DC</td>\n", "      <td>2020-02-28 15:00:00</td>\n", "      <td>4336.0</td>\n", "      <td>213409.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8732524 rows × 4 columns</p>\n", "</div>"], "text/plain": ["             code            datetime     price    volume\n", "index                                                    \n", "0       CU9999.SC 2020-02-03 09:00:00   44780.0    2226.0\n", "0       SP9999.SC 2020-02-03 09:00:00    4412.0    1155.0\n", "0       SC9999.SC 2020-02-03 09:00:00     415.5      76.0\n", "0       SR9999.ZC 2020-02-03 09:00:00    5484.0    4462.0\n", "0       HC9999.SC 2020-02-03 09:00:00    3246.0    3982.0\n", "...           ...                 ...       ...       ...\n", "133177  BU9999.SC 2020-02-28 15:00:00    2736.0  668988.0\n", "131318   Y9999.DC 2020-02-28 15:00:00    5566.0  731307.0\n", "108249  SN9999.SC 2020-02-28 15:00:00  131770.0   20157.0\n", "128678  PP9999.DC 2020-02-28 15:00:00    6834.0  433085.0\n", "117685  EG9999.DC 2020-02-28 15:00:00    4336.0  213409.0\n", "\n", "[8732524 rows x 4 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for code in codes:\n", "    filename = f'f:/hqdata/tick/2019/{code}2019.parquet'\n", "    if os.path.isfile(filename):\n", "        print(filename)\n", "        df = pd.read_parquet(filename)\n", "        # df = df.loc[(df['TradingDay'] >= start) & (df['TradingDay'] <= end)]\n", "        df.dropna(axis=0, how='any', inplace=True)\n", "        df['TradingDay'] = df['TradingDay'].astype(int)\n", "        df['DateTime'] = df.apply(combine_datetime, axis=1)\n", "        df[['LastPrice', 'Volume']] = df[['LastPrice', 'Volume']].astype(np.float32)\n", "        df = df[['InstrumentID','DateTime','LastPrice','Volume']]\n", "        df.to_parquet(filename, engine='fastparquet')"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["filename = f'f:/hqdata/tick/2020/M2020.parquet'\n", "if os.path.isfile(filename):\n", "    df = pd.read_parquet(filename)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InstrumentID</th>\n", "      <th>DateTime</th>\n", "      <th>LastPrice</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>m2005</td>\n", "      <td>2020-02-17 09:00:00.309</td>\n", "      <td>2668.0</td>\n", "      <td>1836.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>m2005</td>\n", "      <td>2020-02-17 09:00:00.806</td>\n", "      <td>2668.0</td>\n", "      <td>2243.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>m2005</td>\n", "      <td>2020-02-17 09:00:01.308</td>\n", "      <td>2668.0</td>\n", "      <td>2435.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>m2005</td>\n", "      <td>2020-02-17 09:00:01.809</td>\n", "      <td>2668.0</td>\n", "      <td>2642.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>m2005</td>\n", "      <td>2020-02-17 09:00:02.308</td>\n", "      <td>2667.0</td>\n", "      <td>2907.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12097</th>\n", "      <td>m2009</td>\n", "      <td>2020-07-24 22:59:58.387</td>\n", "      <td>2962.0</td>\n", "      <td>318078.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12098</th>\n", "      <td>m2009</td>\n", "      <td>2020-07-24 22:59:58.857</td>\n", "      <td>2962.0</td>\n", "      <td>318084.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12099</th>\n", "      <td>m2009</td>\n", "      <td>2020-07-24 22:59:59.348</td>\n", "      <td>2961.0</td>\n", "      <td>318108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12100</th>\n", "      <td>m2009</td>\n", "      <td>2020-07-24 22:59:59.828</td>\n", "      <td>2962.0</td>\n", "      <td>318114.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12101</th>\n", "      <td>m2009</td>\n", "      <td>2020-07-24 23:00:00.106</td>\n", "      <td>2962.0</td>\n", "      <td>318114.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3243223 rows × 4 columns</p>\n", "</div>"], "text/plain": ["      InstrumentID                DateTime  LastPrice    Volume\n", "index                                                          \n", "1            m2005 2020-02-17 09:00:00.309     2668.0    1836.0\n", "2            m2005 2020-02-17 09:00:00.806     2668.0    2243.0\n", "3            m2005 2020-02-17 09:00:01.308     2668.0    2435.0\n", "4            m2005 2020-02-17 09:00:01.809     2668.0    2642.0\n", "5            m2005 2020-02-17 09:00:02.308     2667.0    2907.0\n", "...            ...                     ...        ...       ...\n", "12097        m2009 2020-07-24 22:59:58.387     2962.0  318078.0\n", "12098        m2009 2020-07-24 22:59:58.857     2962.0  318084.0\n", "12099        m2009 2020-07-24 22:59:59.348     2961.0  318108.0\n", "12100        m2009 2020-07-24 22:59:59.828     2962.0  318114.0\n", "12101        m2009 2020-07-24 23:00:00.106     2962.0  318114.0\n", "\n", "[3243223 rows x 4 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InstrumentID</th>\n", "      <th>TradingDay</th>\n", "      <th>UpdateTime</th>\n", "      <th>UpdateMillisec</th>\n", "      <th>LastPrice</th>\n", "      <th>Volume</th>\n", "      <th>AskPrice1</th>\n", "      <th>AskVolume1</th>\n", "      <th>BidPrice1</th>\n", "      <th>BidVolume1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>12068</th>\n", "      <td>m2001</td>\n", "      <td>20190826</td>\n", "      <td>09:00:00</td>\n", "      <td>500</td>\n", "      <td>2972.0</td>\n", "      <td>1100930</td>\n", "      <td>2972.0</td>\n", "      <td>20</td>\n", "      <td>2970.0</td>\n", "      <td>1018</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12069</th>\n", "      <td>m2001</td>\n", "      <td>20190826</td>\n", "      <td>09:00:01</td>\n", "      <td>0</td>\n", "      <td>2979.0</td>\n", "      <td>1112550</td>\n", "      <td>2980.0</td>\n", "      <td>1510</td>\n", "      <td>2979.0</td>\n", "      <td>226</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12070</th>\n", "      <td>m2001</td>\n", "      <td>20190826</td>\n", "      <td>09:00:02</td>\n", "      <td>0</td>\n", "      <td>2980.0</td>\n", "      <td>1121460</td>\n", "      <td>2981.0</td>\n", "      <td>116</td>\n", "      <td>2979.0</td>\n", "      <td>53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12071</th>\n", "      <td>m2001</td>\n", "      <td>20190826</td>\n", "      <td>09:00:03</td>\n", "      <td>0</td>\n", "      <td>2979.0</td>\n", "      <td>1123110</td>\n", "      <td>2980.0</td>\n", "      <td>3535</td>\n", "      <td>2979.0</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12072</th>\n", "      <td>m2001</td>\n", "      <td>20190826</td>\n", "      <td>09:00:03</td>\n", "      <td>500</td>\n", "      <td>2977.0</td>\n", "      <td>1125480</td>\n", "      <td>2978.0</td>\n", "      <td>29</td>\n", "      <td>2977.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24572</th>\n", "      <td>m2005</td>\n", "      <td>20200214</td>\n", "      <td>14:59:58</td>\n", "      <td>446</td>\n", "      <td>2669.0</td>\n", "      <td>602953</td>\n", "      <td>2669.0</td>\n", "      <td>115</td>\n", "      <td>2668.0</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24573</th>\n", "      <td>m2005</td>\n", "      <td>20200214</td>\n", "      <td>14:59:58</td>\n", "      <td>952</td>\n", "      <td>2668.0</td>\n", "      <td>602971</td>\n", "      <td>2669.0</td>\n", "      <td>125</td>\n", "      <td>2668.0</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24574</th>\n", "      <td>m2005</td>\n", "      <td>20200214</td>\n", "      <td>14:59:59</td>\n", "      <td>411</td>\n", "      <td>2669.0</td>\n", "      <td>602987</td>\n", "      <td>2669.0</td>\n", "      <td>123</td>\n", "      <td>2668.0</td>\n", "      <td>195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24575</th>\n", "      <td>m2005</td>\n", "      <td>20200214</td>\n", "      <td>14:59:59</td>\n", "      <td>952</td>\n", "      <td>2668.0</td>\n", "      <td>603097</td>\n", "      <td>2669.0</td>\n", "      <td>114</td>\n", "      <td>2668.0</td>\n", "      <td>207</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24576</th>\n", "      <td>m2005</td>\n", "      <td>20200214</td>\n", "      <td>15:00:00</td>\n", "      <td>43</td>\n", "      <td>2668.0</td>\n", "      <td>603097</td>\n", "      <td>2669.0</td>\n", "      <td>114</td>\n", "      <td>2668.0</td>\n", "      <td>207</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3181758 rows × 10 columns</p>\n", "</div>"], "text/plain": ["      InstrumentID  TradingDay UpdateTime  UpdateMillisec  LastPrice   Volume  \\\n", "index                                                                           \n", "12068        m2001    20190826   09:00:00             500     2972.0  1100930   \n", "12069        m2001    20190826   09:00:01               0     2979.0  1112550   \n", "12070        m2001    20190826   09:00:02               0     2980.0  1121460   \n", "12071        m2001    20190826   09:00:03               0     2979.0  1123110   \n", "12072        m2001    20190826   09:00:03             500     2977.0  1125480   \n", "...            ...         ...        ...             ...        ...      ...   \n", "24572        m2005    20200214   14:59:58             446     2669.0   602953   \n", "24573        m2005    20200214   14:59:58             952     2668.0   602971   \n", "24574        m2005    20200214   14:59:59             411     2669.0   602987   \n", "24575        m2005    20200214   14:59:59             952     2668.0   603097   \n", "24576        m2005    20200214   15:00:00              43     2668.0   603097   \n", "\n", "       AskPrice1  AskVolume1  BidPrice1  BidVolume1  \n", "index                                                \n", "12068     2972.0          20     2970.0        1018  \n", "12069     2980.0        1510     2979.0         226  \n", "12070     2981.0         116     2979.0          53  \n", "12071     2980.0        3535     2979.0          18  \n", "12072     2978.0          29     2977.0          10  \n", "...          ...         ...        ...         ...  \n", "24572     2669.0         115     2668.0         204  \n", "24573     2669.0         125     2668.0         204  \n", "24574     2669.0         123     2668.0         195  \n", "24575     2669.0         114     2668.0         207  \n", "24576     2669.0         114     2668.0         207  \n", "\n", "[3181758 rows x 10 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dropna(axis=0, how='any', inplace=True)\n", "df['TradingDay'] = df['TradingDay'].astype(int)\n", "df"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_1464\\935733425.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df[['LastPrice', 'Volume']] = df[['LastPrice', 'Volume']].astype(np.float32)\n"]}], "source": ["df[['LastPrice', 'Volume']] = df[['LastPrice', 'Volume']].astype(np.float32)\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InstrumentID</th>\n", "      <th>datetime</th>\n", "      <th>LastPrice</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>a2005</td>\n", "      <td>2020-02-17 09:00:00.309</td>\n", "      <td>4144.0</td>\n", "      <td>247.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>a2005</td>\n", "      <td>2020-02-17 09:00:00.784</td>\n", "      <td>4144.0</td>\n", "      <td>301.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>a2005</td>\n", "      <td>2020-02-17 09:00:01.301</td>\n", "      <td>4145.0</td>\n", "      <td>327.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>a2005</td>\n", "      <td>2020-02-17 09:00:01.809</td>\n", "      <td>4144.0</td>\n", "      <td>355.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>a2005</td>\n", "      <td>2020-02-17 09:00:02.296</td>\n", "      <td>4145.0</td>\n", "      <td>444.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10432</th>\n", "      <td>a2009</td>\n", "      <td>2020-07-24 22:59:57.311</td>\n", "      <td>4615.0</td>\n", "      <td>70335.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10433</th>\n", "      <td>a2009</td>\n", "      <td>2020-07-24 22:59:58.387</td>\n", "      <td>4615.0</td>\n", "      <td>70342.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10434</th>\n", "      <td>a2009</td>\n", "      <td>2020-07-24 22:59:58.884</td>\n", "      <td>4613.0</td>\n", "      <td>70352.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10435</th>\n", "      <td>a2009</td>\n", "      <td>2020-07-24 22:59:59.318</td>\n", "      <td>4613.0</td>\n", "      <td>70352.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10436</th>\n", "      <td>a2009</td>\n", "      <td>2020-07-24 22:59:59.828</td>\n", "      <td>4613.0</td>\n", "      <td>70353.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2897512 rows × 4 columns</p>\n", "</div>"], "text/plain": ["      InstrumentID                datetime  LastPrice   Volume\n", "index                                                         \n", "1            a2005 2020-02-17 09:00:00.309     4144.0    247.0\n", "2            a2005 2020-02-17 09:00:00.784     4144.0    301.0\n", "3            a2005 2020-02-17 09:00:01.301     4145.0    327.0\n", "4            a2005 2020-02-17 09:00:01.809     4144.0    355.0\n", "5            a2005 2020-02-17 09:00:02.296     4145.0    444.0\n", "...            ...                     ...        ...      ...\n", "10432        a2009 2020-07-24 22:59:57.311     4615.0  70335.0\n", "10433        a2009 2020-07-24 22:59:58.387     4615.0  70342.0\n", "10434        a2009 2020-07-24 22:59:58.884     4613.0  70352.0\n", "10435        a2009 2020-07-24 22:59:59.318     4613.0  70352.0\n", "10436        a2009 2020-07-24 22:59:59.828     4613.0  70353.0\n", "\n", "[2897512 rows x 4 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}