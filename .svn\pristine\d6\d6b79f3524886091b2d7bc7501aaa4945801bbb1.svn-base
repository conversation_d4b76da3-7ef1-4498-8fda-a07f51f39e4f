import pandas as pd
import numpy as np
from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES
from pyqlab.utils.timefeatures import time_features


class Pipeline:

    def __init__(self, data_path, market, block_name, period,
                  start_year, end_year,
                  start_time="", end_time="",
                  block_size=15, timeenc=0,
                  sel_codes=None, is_bar=True):
        self.data_path = data_path  # d:/RoboQuant2/store/barenc/sf
        self.market = market
        self.block_name = block_name
        self.period = period
        self.start_year = start_year
        self.end_year = end_year
        self.start_time = start_time
        self.end_time = end_time
        self.block_size = block_size
        # self.step_size = step_size
        self.code_size = len(SF_FUT_CODES + MAIN_FUT_CODES)
        self.is_sf = False
        self.sel_codes =sel_codes
        self.is_bar = is_bar
        self.fut_codes_dict = {c: i for i, c in enumerate(sorted(SF_FUT_CODES + MAIN_FUT_CODES))}
        self.timeenc = timeenc
        
    @staticmethod
    def get_vocab():
        bar_set = []
        for i in range(-12, 13):
            for j in range(-12, 13):
                for k in range(0, 8):
                    for l in range(0, 8):
                        bar_set.append(f'{i}|{j}|{k}|{l}')
        bar_set = sorted(bar_set)
        return bar_set
    
    def _load_data(self):
        df = pd.DataFrame()
        if self.is_bar:
            if self.period == 'day':
                df = pd.read_csv(f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}.csv', header=0)
                df.columns = ['symbol', 'datetime', 'bar']
            elif self.period == 'min5':
                for y in range(self.start_year, self.end_year + 1):
                    df2 = pd.read_csv(f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}_{y}.csv', header=0)
                    df = pd.concat([df, df2], axis=0, ignore_index=True)
                # df['datetime']是timestamp， 删除每日时间在00:00:00到06:00:00之间的数据
                df.columns = ['symbol', 'datetime', 'bar']
                print(df.shape)
                df = df[(pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 6]
                print(df.shape)
            else:
                raise ValueError(f"Invalid period: {self.period}")
        else:
            for y in range(self.start_year, self.end_year + 1):
                df2 = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
                df = pd.concat([df, df2], axis=0, ignore_index=True)
            df.columns = ['symbol', 'datetime', 'change', 'entity', 'upline', 'downline']
        # print(df['symbol'].unique())
        if "IF" in df['symbol'].values:
            self.is_sf = True

        print(f"load data shape: {df.shape}")
        if self.start_time != "" and self.end_time !="" and self.start_time < self.end_time:
            start_time = int(pd.Timestamp(self.start_time).timestamp())
            end_time = int(pd.Timestamp(self.end_time).timestamp())
            df = df[(df['datetime'] >= start_time) & (df['datetime'] <= end_time)]
            print(f"after time filter: {df.shape}")

        return df
    
    @staticmethod
    def to_bar(df):
        # 将列change的值限定在-10到10之间
        df.loc[df['change'] < -12, 'change'] = -12
        df.loc[df['change'] > 12, 'change'] = 12
        df.loc[df['entity'] < -12, 'entity'] = -12
        df.loc[df['entity'] > 12, 'entity'] = 12
        df.loc[df['upline'] > 7, 'upline'] = 7
        df.loc[df['downline'] > 7, 'downline'] = 7

        # 将3,4,5,6列的值转换为bar_set中的索引
        if not hasattr(Pipeline, 'bar_set'):
            Pipeline.bar_set = Pipeline.get_vocab()
        df['bar'] = df['change'].astype(str) + '|' + df['entity'].astype(str) + '|' + df['upline'].astype(str) + '|' + df['downline'].astype(str)
        df['bar'] = df['bar'].apply(lambda x: Pipeline.bar_set.index(x))
        df['bar'] = df['bar'].astype(int)
        df.drop(columns=['change', 'entity', 'upline', 'downline'], inplace=True)
        return df
    
    def _to_time_tf(self, df):
        # 将date列由timestamp转换为东8区日期时间
        df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)
        if self.timeenc == 0:
            df['Month'] = df.datetime.apply(lambda row: row.month - 1, 1)
            df['Day'] = df.datetime.apply(lambda row: row.day - 1, 1)
            # 星期日设为0为一周的开端,weekday()返回0-6，对应星期一到星期日
            df['DayOfWeek'] = df.datetime.apply(lambda row: row.weekday() + 1 if row.weekday() < 6 else 0, 1)
            df['Hour'] = df.datetime.apply(lambda row: row.hour, 1)
            df['Minute'] = df.datetime.apply(lambda row: row.minute, 1)
            df['Minute'] = df.Minute.map(lambda x: x // 5)
            # df = df.drop(['date'], axis=1).values
        elif self.timeenc == 1:
            df_stamp= time_features(pd.to_datetime(df['datetime'].values), freq='t').transpose(1, 0)
            df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])
            df = pd.concat([df, df_tf], axis=1)
        else:
            raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
        return df

    def _prepare_data(self):
        # 将列change的值限定在-10到10之间
        df=self._load_data()
        if self.sel_codes is not None and self.market == 'fut' and self.block_name == 'main':
            df = df[df['symbol'].isin(self.sel_codes)]

        df['code_encoded'] = df['symbol'].apply(lambda x: self.fut_codes_dict[x])
        df.drop(columns=['symbol'], inplace=True)
        # move code_encoded to the first column
        cols = df.columns.tolist()
        cols = cols[-1:] + cols[:-1]
        df = df[cols]
        # TODO: remove this line
        # print(f"{df.shape}=====remove========")
        # df = df.iloc[-3000:]
        # print(f"{df.shape}=====remove========")
        df = df.sort_values(by=['code_encoded', 'datetime']).reset_index(drop=True)

        if not self.is_bar:
            df=self._to_bar(df)

        df=self._to_time_tf(df)
        if self.timeenc == 0:
            df = df[['code_encoded', 'bar', 'Month', 'Day', 'DayOfWeek', 'Hour', 'Minute']]
        else:
            df = df[['code_encoded', 'bar', 'MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear']]

        return df
    
    def _convert_to_bar_file(self):
        for y in range(self.start_year, self.end_year + 1):
            df = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
            df.columns = ['symbol', 'datetime', 'change', 'entity', 'upline', 'downline']
            df = Pipeline.to_bar(df)
            df.to_csv(f'{self.data_path}/bar_{y}.csv', index=False, header=True)
            print(f'bar_{y}.csv saved.')
    
    def get_data(self):
        df = self._prepare_data()
        print('============================')
        print(f'code_size: {self.code_size}, bar_size: {len(self.get_vocab())}')
        print(f'df shape: {df.shape}')
        print(f'df head: {df.head()}')
        print('============================')
        return df
        """
        # dataframe group by symbol to ndarray
        df = df.groupby('code_encoded')
        data = []
        data_mark = []
        for _, group in df:
            for i in range(0, len(group) - self.block_size, self.step_size):
                data.append(group.iloc[i:i+self.block_size+1, :2].values)
                data_mark.append(group.iloc[i:i+self.block_size+1, 2:].values)
        data = np.array(data)
        data_mark = np.array(data_mark)
        return data, data_mark
        """

if __name__ == '__main__':
    data_path = 'd:/RoboQuant2/store/barenc/sf'
    start_year = 2024
    end_year = 2024
    pipeline = Pipeline(data_path, start_year, end_year)
    pipeline._convert_to_bar_file()