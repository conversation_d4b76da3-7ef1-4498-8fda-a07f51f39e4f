"""
修复版本的K线LLM示例

演示如何使用修复后的CandlestickLLM模型进行预测
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
from torch.utils.data import DataLoader

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入相关模块
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset

def load_sample_data():
    """加载示例数据"""
    # 这里使用随机生成的数据作为示例
    np.random.seed(42)

    # 生成100个交易日的数据
    n_days = 100

    # 初始价格
    initial_price = 100.0

    # 生成随机价格变动
    daily_returns = np.random.normal(0.0005, 0.015, n_days)

    # 计算价格序列
    prices = initial_price * (1 + np.cumsum(daily_returns))

    # 生成OHLC数据
    data = pd.DataFrame()
    data['datetime'] = pd.date_range(start='2023-01-01', periods=n_days)
    data['close'] = prices

    # 生成开盘价、最高价和最低价
    data['open'] = data['close'].shift(1)
    data.loc[0, 'open'] = initial_price * 0.995  # 第一天的开盘价

    # 生成日内波动
    daily_volatility = np.random.uniform(0.005, 0.02, n_days)
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + daily_volatility)
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - daily_volatility)

    # 生成交易量
    data['volume'] = np.random.lognormal(10, 1, n_days)

    # 设置索引
    data.set_index('datetime', inplace=True)

    return data

def create_model(tokenizer, device='cpu'):
    """创建模型"""
    # 模型参数
    model_config = {
        'vocab_size': tokenizer.vocab_size,
        'code_size': 10,  # 假设有10个不同的证券代码
        'block_size': 64,
        'n_layer': 4,
        'n_head': 4,
        'd_model': 128,
        'dropout': 0.1,
        'bias': False,
        'use_time_features': False,
        'n_time_features': 5
    }

    # 创建模型
    model = CandlestickLLM(**model_config)
    model.to(device)

    return model

def prepare_data(data, tokenizer, seq_len=50, pred_len=10):
    """准备数据"""
    # 创建数据集
    dataset = CandlestickDataset(
        data=data,
        tokenizer=tokenizer,
        seq_len=seq_len,
        pred_len=pred_len,
        code_ids=[0],  # 使用单一代码ID
        use_time_features=False
    )

    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=1,
        shuffle=False
    )

    return dataset, dataloader

def predict(model, dataloader, tokenizer, device='cpu', max_new_tokens=10):
    """使用模型进行预测"""
    model.eval()

    # 获取一个批次的数据
    batch = next(iter(dataloader))
    input_tokens = batch['input_tokens']
    code_ids = batch['code_id']
    time_features = batch['time_features']

    # 移动到设备
    input_tokens = input_tokens.to(device)
    code_ids = code_ids.to(device)
    if time_features is not None:
        time_features = time_features.to(device)

    # 打印输入信息
    print(f"输入tokens形状: {input_tokens.shape}")
    print(f"代码IDs形状: {code_ids.shape}")
    if time_features is not None:
        print(f"时间特征形状: {time_features.shape}")

    # 生成预测
    with torch.no_grad():
        # 使用模型生成新的token序列
        output_tokens = model.generate(
            input_tokens,
            code_ids,
            time_features,
            max_new_tokens=max_new_tokens,
            temperature=1.0,
            top_k=50
        )

    # 打印输出信息
    print(f"输出tokens形状: {output_tokens.shape}")

    # 获取预测的token序列
    pred_tokens = output_tokens[0, -max_new_tokens:].cpu().numpy().tolist()

    # 获取最后一个已知价格和ATR
    last_known_price = dataloader.dataset.data[0]['close'].iloc[dataloader.dataset.seq_len - 1]

    # 计算ATR
    df = dataloader.dataset.data[0]
    atr_window = tokenizer.atr_window
    high_low = df['high'] - df['low']
    high_close = (df['high'] - df['close'].shift(1)).abs()
    low_close = (df['low'] - df['close'].shift(1)).abs()
    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = tr.rolling(window=atr_window).mean()

    # 获取最后一个已知ATR值
    last_atr = atr.iloc[dataloader.dataset.seq_len - 1]

    print(f"最后一个已知价格: {last_known_price}")
    print(f"ATR值: {last_atr}")

    # 将token转换回K线数据
    pred_ohlc = tokenizer.tokens_to_candlesticks(pred_tokens, last_known_price, last_atr)

    return pred_ohlc

def visualize_prediction(data, pred_ohlc, seq_len, pred_len):
    """可视化预测结果"""
    # 获取历史数据
    hist_data = data.iloc[seq_len-pred_len:seq_len]

    # 创建图表
    plt.figure(figsize=(12, 6))

    # 绘制历史收盘价
    plt.plot(range(pred_len), hist_data['close'].values, 'b-', label='历史收盘价')

    # 绘制预测收盘价
    plt.plot(range(pred_len, pred_len + len(pred_ohlc)), pred_ohlc['close'].values, 'r-', label='预测收盘价')

    # 添加标签和图例
    plt.xlabel('时间步')
    plt.ylabel('价格')
    plt.title('K线预测结果')
    plt.legend()
    plt.grid(True)

    # 保存图表
    save_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../checkpoints/basic_candlestick_llm')
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, 'prediction.png'))

    # 显示图表
    plt.close()

    print(f"预测结果保存在 {save_dir} 目录下")

def main():
    """主函数"""
    try:
        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")

        # 加载示例数据
        print("加载示例数据...")
        data = load_sample_data()
        print(f"数据形状: {data.shape}")
        print(data.head())

        # 创建tokenizer
        print("\n创建tokenizer...")
        tokenizer = CandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            atr_window=20,
            atr_mult=0.88,
            scale=10,
            include_volume=True
        )
        print(f"词汇表大小: {tokenizer.vocab_size}")

        # 准备数据
        print("\n准备数据...")
        seq_len = 50
        pred_len = 10
        dataset, dataloader = prepare_data(data, tokenizer, seq_len, pred_len)
        print(f"数据集大小: {len(dataset)}")

        # 创建模型
        print("\n创建模型...")
        model = create_model(tokenizer, device)
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

        # 进行预测
        print("\n进行预测...")
        pred_ohlc = predict(model, dataloader, tokenizer, device, max_new_tokens=pred_len)
        print("\n预测结果:")
        print(pred_ohlc)

        # 可视化预测结果
        print("\n可视化预测结果...")
        visualize_prediction(data, pred_ohlc, seq_len, pred_len)

        print("\n示例完成，结果保存在 checkpoints/basic_candlestick_llm/ 目录下")

    except Exception as e:
        import traceback
        print(f"示例运行出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
