"""
增强版DeepLOB模型

基于原始DeepLOB模型，结合Transformer和LSTM的优点，更好地捕捉orderbook数据的时序特性和空间特性
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

class SelfAttention(nn.Module):
    """自注意力机制模块"""
    def __init__(self, input_dim, head_dim, num_heads, dropout=0.1):
        super(SelfAttention, self).__init__()
        self.input_dim = input_dim
        self.head_dim = head_dim
        self.num_heads = num_heads
        self.dropout = dropout
        
        # 确保输入维度可以被头数整除
        assert input_dim % num_heads == 0, "input_dim must be divisible by num_heads"
        
        # 定义线性变换
        self.q_linear = nn.Linear(input_dim, input_dim)
        self.k_linear = nn.Linear(input_dim, input_dim)
        self.v_linear = nn.Linear(input_dim, input_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.out = nn.Linear(input_dim, input_dim)
    
    def forward(self, x):
        batch_size = x.size(0)
        seq_len = x.size(1)
        
        # 线性变换
        q = self.q_linear(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_linear(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_linear(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        
        # 应用softmax
        attention = F.softmax(scores, dim=-1)
        attention = self.dropout(attention)
        
        # 应用注意力权重
        out = torch.matmul(attention, v)
        
        # 重塑张量
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, self.input_dim)
        
        # 最终线性变换
        out = self.out(out)
        
        return out

class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, input_dim, head_dim, num_heads, ff_dim, dropout=0.1):
        super(TransformerBlock, self).__init__()
        self.attention = SelfAttention(input_dim, head_dim, num_heads, dropout)
        self.norm1 = nn.LayerNorm(input_dim)
        self.norm2 = nn.LayerNorm(input_dim)
        self.ff = nn.Sequential(
            nn.Linear(input_dim, ff_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, input_dim)
        )
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        # 自注意力 + 残差连接 + 层归一化
        attention_output = self.attention(x)
        x = self.norm1(x + self.dropout(attention_output))
        
        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.ff(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x

class EnhancedDeepLOB(nn.Module):
    """
    增强版DeepLOB模型
    
    结合了原始DeepLOB的CNN架构、Transformer的自注意力机制和LSTM的时序建模能力
    """
    def __init__(self, 
                 input_channels=1,           # 输入通道数
                 time_steps=100,             # 时间步长
                 num_features=40,            # 每个时间步的特征数
                 num_classes=3,              # 输出类别数
                 dropout=0.1,                # Dropout比率
                 use_transformer=True,       # 是否使用Transformer
                 num_transformer_layers=2,   # Transformer层数
                 num_heads=4,                # 注意力头数
                 head_dim=16,                # 每个头的维度
                 ff_dim=256,                 # 前馈网络维度
                 lstm_hidden_size=64,        # LSTM隐藏层大小
                 lstm_layers=1,              # LSTM层数
                 use_gru=False,              # 是否使用GRU代替LSTM
                 use_code_embedding=False,   # 是否使用证券代码嵌入
                 num_codes=100,              # 证券代码数量
                 code_embedding_dim=16,      # 证券代码嵌入维度
                 probabilistic=False):       # 是否进行概率预测
        super(EnhancedDeepLOB, self).__init__()
        
        self.input_channels = input_channels
        self.time_steps = time_steps
        self.num_features = num_features
        self.num_classes = num_classes
        self.use_transformer = use_transformer
        self.use_code_embedding = use_code_embedding
        self.probabilistic = probabilistic
        
        # 证券代码嵌入
        if use_code_embedding:
            self.code_embedding = nn.Embedding(num_codes, code_embedding_dim)
        
        # 卷积块1
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=input_channels, out_channels=32, kernel_size=(1,2), stride=(1,2)),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(32),
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(32),
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(32),
        )
        
        # 卷积块2
        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(1,2), stride=(1,2)),
            nn.Tanh(),
            nn.BatchNorm2d(32),
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),
            nn.Tanh(),
            nn.BatchNorm2d(32),
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),
            nn.Tanh(),
            nn.BatchNorm2d(32),
        )
        
        # 卷积块3
        self.conv3 = nn.Sequential(
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(1,10)),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(32),
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(32),
            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(32),
        )
        
        # Inception模块1
        self.inp1 = nn.Sequential(
            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=(1,1), padding='same'),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(64),
            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=(3,1), padding='same'),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(64),
        )
        
        # Inception模块2
        self.inp2 = nn.Sequential(
            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=(1,1), padding='same'),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(64),
            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=(5,1), padding='same'),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(64),
        )
        
        # Inception模块3
        self.inp3 = nn.Sequential(
            nn.MaxPool2d((3, 1), stride=(1, 1), padding=(1, 0)),
            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=(1,1), padding='same'),
            nn.LeakyReLU(negative_slope=0.01),
            nn.BatchNorm2d(64),
        )
        
        # 计算Transformer输入维度
        # 假设经过卷积和Inception模块后的特征维度为192
        transformer_input_dim = 192
        
        # Transformer层
        if use_transformer:
            self.transformer_layers = nn.ModuleList([
                TransformerBlock(
                    input_dim=transformer_input_dim,
                    head_dim=head_dim,
                    num_heads=num_heads,
                    ff_dim=ff_dim,
                    dropout=dropout
                )
                for _ in range(num_transformer_layers)
            ])
        
        # LSTM或GRU层
        if use_gru:
            self.rnn = nn.GRU(
                input_size=transformer_input_dim,
                hidden_size=lstm_hidden_size,
                num_layers=lstm_layers,
                batch_first=True,
                dropout=dropout if lstm_layers > 1 else 0
            )
        else:
            self.rnn = nn.LSTM(
                input_size=transformer_input_dim,
                hidden_size=lstm_hidden_size,
                num_layers=lstm_layers,
                batch_first=True,
                dropout=dropout if lstm_layers > 1 else 0
            )
        
        # 输出层
        if probabilistic:
            # 概率预测：输出均值和方差
            self.fc_mean = nn.Linear(lstm_hidden_size, num_classes)
            self.fc_var = nn.Linear(lstm_hidden_size, num_classes)
        else:
            # 确定性预测
            self.fc = nn.Linear(lstm_hidden_size, num_classes)
    
    def forward(self, x, code_ids=None):
        # x的形状: [batch_size, input_channels, time_steps, num_features]
        
        # 证券代码嵌入
        if self.use_code_embedding and code_ids is not None:
            code_embed = self.code_embedding(code_ids)  # [batch_size, code_embedding_dim]
            code_embed = code_embed.unsqueeze(1).unsqueeze(1)  # [batch_size, 1, 1, code_embedding_dim]
            code_embed = code_embed.expand(-1, -1, x.size(2), -1)  # [batch_size, 1, time_steps, code_embedding_dim]
            
            # 将代码嵌入与输入特征连接
            x = torch.cat([x, code_embed], dim=3)
        
        # 卷积块
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        
        # Inception模块
        x_inp1 = self.inp1(x)
        x_inp2 = self.inp2(x)
        x_inp3 = self.inp3(x)
        
        # 连接Inception模块的输出
        x = torch.cat((x_inp1, x_inp2, x_inp3), dim=1)
        
        # 重塑张量以适应Transformer和RNN
        x = x.permute(0, 2, 1, 3)  # [batch_size, time_steps, channels, features]
        batch_size, seq_len, channels, features = x.shape
        x = x.reshape(batch_size, seq_len, channels * features)  # [batch_size, time_steps, channels*features]
        
        # Transformer层
        if self.use_transformer:
            for transformer_layer in self.transformer_layers:
                x = transformer_layer(x)
        
        # RNN层
        if isinstance(self.rnn, nn.LSTM):
            h0 = torch.zeros(self.rnn.num_layers, x.size(0), self.rnn.hidden_size).to(x.device)
            c0 = torch.zeros(self.rnn.num_layers, x.size(0), self.rnn.hidden_size).to(x.device)
            x, _ = self.rnn(x, (h0, c0))
        else:  # GRU
            h0 = torch.zeros(self.rnn.num_layers, x.size(0), self.rnn.hidden_size).to(x.device)
            x, _ = self.rnn(x, h0)
        
        # 取最后一个时间步的输出
        x = x[:, -1, :]
        
        # 输出层
        if self.probabilistic:
            # 概率预测
            mean = self.fc_mean(x)
            log_var = self.fc_var(x)
            return mean, log_var
        else:
            # 确定性预测
            return self.fc(x)

class EnhancedDeepLOBRegression(EnhancedDeepLOB):
    """
    用于回归任务的增强版DeepLOB模型
    
    继承自EnhancedDeepLOB，但修改了输出层以适应回归任务
    """
    def __init__(self, 
                 input_channels=1,
                 time_steps=100,
                 num_features=40,
                 output_size=1,            # 回归输出维度
                 horizon=10,               # 预测时间范围
                 dropout=0.1,
                 use_transformer=True,
                 num_transformer_layers=2,
                 num_heads=4,
                 head_dim=16,
                 ff_dim=256,
                 lstm_hidden_size=64,
                 lstm_layers=1,
                 use_gru=False,
                 use_code_embedding=False,
                 num_codes=100,
                 code_embedding_dim=16,
                 probabilistic=False):
        # 调用父类构造函数，但将num_classes设置为output_size * horizon
        super(EnhancedDeepLOBRegression, self).__init__(
            input_channels=input_channels,
            time_steps=time_steps,
            num_features=num_features,
            num_classes=output_size * horizon,  # 修改为回归输出维度 * 预测时间范围
            dropout=dropout,
            use_transformer=use_transformer,
            num_transformer_layers=num_transformer_layers,
            num_heads=num_heads,
            head_dim=head_dim,
            ff_dim=ff_dim,
            lstm_hidden_size=lstm_hidden_size,
            lstm_layers=lstm_layers,
            use_gru=use_gru,
            use_code_embedding=use_code_embedding,
            num_codes=num_codes,
            code_embedding_dim=code_embedding_dim,
            probabilistic=probabilistic
        )
        
        self.output_size = output_size
        self.horizon = horizon
    
    def forward(self, x, code_ids=None):
        # 调用父类的forward方法
        output = super(EnhancedDeepLOBRegression, self).forward(x, code_ids)
        
        if self.probabilistic:
            # 概率预测
            mean, log_var = output
            # 重塑输出以适应多步预测
            mean = mean.view(-1, self.horizon, self.output_size)
            log_var = log_var.view(-1, self.horizon, self.output_size)
            return mean, log_var
        else:
            # 确定性预测
            # 重塑输出以适应多步预测
            return output.view(-1, self.horizon, self.output_size)

if __name__ == "__main__":
    # 测试模型
    batch_size = 32
    input_channels = 1
    time_steps = 100
    num_features = 40
    num_classes = 3
    
    # 创建随机输入
    x = torch.randn(batch_size, input_channels, time_steps, num_features)
    code_ids = torch.randint(0, 100, (batch_size,))
    
    # 测试分类模型
    model = EnhancedDeepLOB(
        input_channels=input_channels,
        time_steps=time_steps,
        num_features=num_features,
        num_classes=num_classes,
        use_code_embedding=True
    )
    output = model(x, code_ids)
    print(f"分类模型输出形状: {output.shape}")
    
    # 测试回归模型
    regression_model = EnhancedDeepLOBRegression(
        input_channels=input_channels,
        time_steps=time_steps,
        num_features=num_features,
        output_size=1,
        horizon=10,
        use_code_embedding=True
    )
    regression_output = regression_model(x, code_ids)
    print(f"回归模型输出形状: {regression_output.shape}")
    
    # 测试概率回归模型
    prob_regression_model = EnhancedDeepLOBRegression(
        input_channels=input_channels,
        time_steps=time_steps,
        num_features=num_features,
        output_size=1,
        horizon=10,
        use_code_embedding=True,
        probabilistic=True
    )
    mean, log_var = prob_regression_model(x, code_ids)
    print(f"概率回归模型均值输出形状: {mean.shape}")
    print(f"概率回归模型方差输出形状: {log_var.shape}")
