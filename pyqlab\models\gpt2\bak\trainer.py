"""
Candlestick LLM Trainer

K线LLM模型训练器

注意：现在有交叉验证版本的训练器可用，请参考 cv_trainer.py
"""

import os
import time
import math
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.optim import AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional, Union, Any, Callable
from datetime import datetime
from tqdm import tqdm
import logging

# 导入模型和tokenizer
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('candlestick_llm_training.log')
    ]
)
logger = logging.getLogger('CandlestickLLMTrainer')


class CandlestickLLMTrainer:
    """K线LLM模型训练器"""
    def __init__(self,
                 model: Union[CandlestickLLM, AdvancedCandlestickLLM],
                 tokenizer: Union[CandlestickTokenizer, NonlinearCandlestickTokenizer],
                 train_data: List[pd.DataFrame],
                 train_code_ids: List[int],
                 stride: int = 1,
                 val_data: List[pd.DataFrame] = None,
                 val_code_ids: List[int] = None,
                 seq_len: int = 30,
                 batch_size: int = 32,
                 learning_rate: float = 5e-4,  # 增加初始学习率
                 weight_decay: float = 0.05,   # 增加权重衰减
                 betas: Tuple[float, float] = (0.9, 0.95),  # 修改beta2
                 lr_scheduler: str = 'one_cycle',  # 默认使用one_cycle调度器
                 warmup_steps: int = 1000,
                 max_epochs: int = 10,
                 grad_clip: float = 2.0,  # 增加梯度裁剪阈值
                 device: str = None,
                 checkpoint_dir: str = './checkpoints',
                 log_interval: int = 10,
                 eval_interval: int = 100,
                 save_interval: int = 1000,
                 early_stopping_patience: int = 10,  # 早停耐心值
                 early_stopping_min_delta: float = 0.0001,  # 最小改进阈值
                 label_smoothing: float = 0.1,  # 标签平滑系数
                 loss_scale: float = 1.0):  # 损失缩放因子
        """
        初始化K线LLM训练器

        Args:
            model: K线LLM模型
            tokenizer: K线tokenizer
            train_data: 训练数据列表
            train_code_ids: 训练数据对应的证券代码ID列表
            stride：滑动窗口步长
            val_data: 验证数据列表
            val_code_ids: 验证数据对应的证券代码ID列表
            seq_len: 序列长度
            batch_size: 批大小
            learning_rate: 学习率
            weight_decay: 权重衰减
            betas: Adam优化器的beta参数
            lr_scheduler: 学习率调度器类型，'cosine'或'plateau'
            warmup_steps: 预热步数
            max_epochs: 最大训练轮数
            grad_clip: 梯度裁剪阈值
            device: 设备，'cuda'或'cpu'
            checkpoint_dir: 检查点保存目录
            log_interval: 日志记录间隔（批次）
            eval_interval: 评估间隔（批次）
            save_interval: 保存间隔（批次）
        """
        self.model = model
        self.tokenizer = tokenizer
        self.seq_len = seq_len
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.betas = betas
        self.lr_scheduler_type = lr_scheduler
        self.warmup_steps = warmup_steps
        self.max_epochs = max_epochs
        self.grad_clip = grad_clip
        self.log_interval = log_interval
        self.eval_interval = eval_interval
        self.save_interval = save_interval

        # 设置设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        logger.info(f"使用设备: {self.device}")

        # 创建数据集和数据加载器
        self.train_dataset = CandlestickDataset(
            data=train_data,
            code_ids=train_code_ids,
            tokenizer=tokenizer,
            seq_len=seq_len,
            stride=stride,
            use_time_features=model.use_time_features
        )

        self.train_loader = DataLoader(
            self.train_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )

        if val_data is not None and val_code_ids is not None:
            self.val_dataset = CandlestickDataset(
                data=val_data,
                code_ids=val_code_ids,
                tokenizer=tokenizer,
                seq_len=seq_len,
                stride=stride,
                # use_time_features=model.use_time_features
            )

            self.val_loader = DataLoader(
                self.val_dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=0,
                pin_memory=True
            )
        else:
            self.val_dataset = None
            self.val_loader = None

        # 将模型移动到设备
        self.model.to(self.device)

        # 创建优化器
        self.optimizer = self.model.configure_optimizers(
            weight_decay=weight_decay,
            learning_rate=learning_rate,
            betas=betas,
            device_type=self.device.type
        )

        # 创建学习率调度器
        if lr_scheduler == 'cosine':
            self.lr_scheduler = CosineAnnealingLR(
                self.optimizer,
                T_max=len(self.train_loader) * max_epochs,
                eta_min=learning_rate / 20  # 降低最小学习率
            )
        elif lr_scheduler == 'plateau':
            self.lr_scheduler = ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=0.3,  # 更激进的学习率降低
                patience=3,   # 减少耐心值
                verbose=True
            )
        elif lr_scheduler == 'one_cycle':
            # 添加OneCycleLR调度器
            self.lr_scheduler = torch.optim.lr_scheduler.OneCycleLR(
                self.optimizer,
                max_lr=learning_rate * 10,  # 最大学习率
                total_steps=len(self.train_loader) * max_epochs,
                pct_start=0.3,  # 前30%的步骤用于预热
                div_factor=25.0,  # 初始学习率 = max_lr / div_factor
                final_div_factor=10000.0  # 最终学习率 = max_lr / final_div_factor
            )
        else:
            self.lr_scheduler = None

        # 创建检查点目录
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(checkpoint_dir, exist_ok=True)

        # 训练状态
        self.global_step = 0
        self.best_val_loss = float('inf')
        self.train_losses = []
        self.val_losses = []
        self.lr_history = []

        # 早停设置
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_min_delta = early_stopping_min_delta
        self.early_stopping_counter = 0
        self.early_stopping_best_loss = float('inf')

        # 损失函数设置
        self.label_smoothing = label_smoothing
        self.loss_scale = loss_scale

    def train_step(self, batch):
        """执行一个训练步骤"""

        # 将数据移动到设备
        input_tokens = batch['input_tokens'].to(self.device)
        target_tokens = batch['target_tokens'].to(self.device)
        code_ids = batch['code_id'].to(self.device)
        time_features = batch['time_features'].to(self.device)

        # 额外的目标
        # direction_target = batch['direction_target'].to(self.device)
        # volatility_target = batch['volatility_target'].to(self.device)
        # volume_target = batch['volume_target'].to(self.device)

        # 检查是否使用额外目标 - 当前版本不使用多任务学习
        # 注释掉未使用的变量，避免IDE警告
        # use_multi_task = hasattr(self.model, 'use_multi_task') and self.model.use_multi_task
        # use_direction = (hasattr(self.model, 'use_direction_prediction') and self.model.use_direction_prediction) or use_multi_task
        # use_volatility = (hasattr(self.model, 'use_volatility_prediction') and self.model.use_volatility_prediction) or use_multi_task
        # use_volume = (hasattr(self.model, 'use_volume_prediction') and self.model.use_volume_prediction) or use_multi_task

        # 如果模型不使用这些目标，将它们设置为None
        # direction_target = None
        # volatility_target = None
        # volume_target = None

        # 前向传播
        outputs = self.model(
            input_tokens=input_tokens,
            code_ids=code_ids,
            time_features=time_features,
            targets=target_tokens,
            # direction_targets=direction_target,
            # volatility_targets=volatility_target,
            # volume_targets=volume_target
        )

        # 处理输出
        if isinstance(outputs, tuple):
            logits, loss = outputs
        else:
            logits, loss = outputs[0], outputs[1]

        # 如果模型没有计算损失，手动计算带标签平滑的交叉熵损失
        if loss is None and logits is not None and target_tokens is not None:
            loss = F.cross_entropy(
                logits.view(-1, logits.size(-1)),
                target_tokens.view(-1),
                ignore_index=-1,
                label_smoothing=self.label_smoothing
            )

        # 应用损失缩放
        if loss is not None:
            loss = loss * self.loss_scale

        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()

        # 梯度裁剪
        if self.grad_clip > 0:
            # 增加梯度裁剪阈值，允许更大的梯度
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip)

            # 检查梯度是否有NaN或Inf
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                        logger.warning(f"检测到NaN或Inf梯度: {name}")
                        param.grad.data = torch.where(
                            torch.isfinite(param.grad.data),
                            param.grad.data,
                            torch.zeros_like(param.grad.data)
                        )

        # 更新参数
        self.optimizer.step()

        # 更新学习率
        if self.lr_scheduler is not None:
            if self.lr_scheduler_type in ['cosine', 'one_cycle']:
                self.lr_scheduler.step()

            # 记录当前学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            if current_lr < 1e-6:  # 如果学习率太小
                logger.warning(f"学习率过小: {current_lr:.8f}，重置为初始学习率的1/10")
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = self.learning_rate / 10.0

        return loss.item()

    def evaluate(self, data_loader):
        """评估模型"""
        self.model.eval()
        total_loss = 0
        total_samples = 0

        with torch.no_grad():
            for batch in data_loader:
                # 将数据移动到设备
                input_tokens = batch['input_tokens'].to(self.device)
                target_tokens = batch['target_tokens'].to(self.device)
                code_ids = batch['code_id'].to(self.device)
                time_features = batch['time_features'].to(self.device)

                # 额外的目标
                # direction_target = batch['direction_target'].to(self.device)
                # volatility_target = batch['volatility_target'].to(self.device)
                # volume_target = batch['volume_target'].to(self.device)

                # 检查是否使用额外目标 - 当前版本不使用多任务学习
                # 注释掉未使用的变量，避免IDE警告
                # use_multi_task = hasattr(self.model, 'use_multi_task') and self.model.use_multi_task
                # use_direction = (hasattr(self.model, 'use_direction_prediction') and self.model.use_direction_prediction) or use_multi_task
                # use_volatility = (hasattr(self.model, 'use_volatility_prediction') and self.model.use_volatility_prediction) or use_multi_task
                # use_volume = (hasattr(self.model, 'use_volume_prediction') and self.model.use_volume_prediction) or use_multi_task

                # 如果模型不使用这些目标，将它们设置为None
                # if not use_direction:
                #     direction_target = None
                # if not use_volatility:
                #     volatility_target = None
                # if not use_volume:
                #     volume_target = None

                # 前向传播
                outputs = self.model(
                    input_tokens=input_tokens,
                    code_ids=code_ids,
                    time_features=time_features,
                    targets=target_tokens,
                    # direction_targets=direction_target,
                    # volatility_targets=volatility_target,
                    # volume_targets=volume_target
                )

                # 处理输出
                if isinstance(outputs, tuple):
                    logits, loss = outputs
                else:
                    logits, loss = outputs[0], outputs[1]

                # 如果模型没有计算损失，手动计算带标签平滑的交叉熵损失
                if loss is None and logits is not None and target_tokens is not None:
                    loss = F.cross_entropy(
                        logits.view(-1, logits.size(-1)),
                        target_tokens.view(-1),
                        ignore_index=-1,
                        label_smoothing=self.label_smoothing
                    )

                # 应用损失缩放
                if loss is not None:
                    loss = loss * self.loss_scale

                # 累加损失
                total_loss += loss.item() * input_tokens.size(0)
                total_samples += input_tokens.size(0)

        # 计算平均损失
        avg_loss = total_loss / max(1, total_samples)  # 避免除以零

        self.model.train()
        return avg_loss

    def save_checkpoint(self, epoch, loss, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'loss': loss,
            'best_val_loss': self.best_val_loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'lr_history': self.lr_history
        }

        # 保存检查点
        checkpoint_path = os.path.join(self.checkpoint_dir, f'checkpoint_step_{self.global_step}.pt')
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"保存检查点到 {checkpoint_path}")

        # 如果是最佳模型，保存一个额外的副本
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_model.pt')
            torch.save(checkpoint, best_path)
            logger.info(f"保存最佳模型到 {best_path}")

    def load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        if not os.path.exists(checkpoint_path):
            logger.warning(f"检查点 {checkpoint_path} 不存在")
            return False

        logger.info(f"加载检查点 {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        # 加载模型和优化器状态
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # 恢复训练状态
        self.global_step = checkpoint.get('global_step', 0)
        self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
        self.train_losses = checkpoint.get('train_losses', [])
        self.val_losses = checkpoint.get('val_losses', [])
        self.lr_history = checkpoint.get('lr_history', [])

        return True

    def train(self, resume_from=None):
        """
        训练模型

        Args:
            resume_from: 从检查点恢复训练
        """
        # 恢复训练
        if resume_from is not None:
            self.load_checkpoint(resume_from)

        # 设置模型为训练模式
        self.model.train()

        # 记录开始时间
        start_time = time.time()

        # 训练循环
        logger.info(f"开始训练，最大轮数: {self.max_epochs}")

        for epoch in range(self.max_epochs):
            epoch_loss = 0
            epoch_steps = 0

            # 创建进度条
            pbar = tqdm(self.train_loader, desc=f"Epoch {epoch+1}/{self.max_epochs}")

            for batch in pbar:
                # 执行训练步骤
                loss = self.train_step(batch)

                # 更新进度条
                pbar.set_postfix(loss=f"{loss:.4f}")

                # 记录损失
                epoch_loss += loss
                epoch_steps += 1
                self.train_losses.append(loss)

                # 记录学习率
                current_lr = self.optimizer.param_groups[0]['lr']
                self.lr_history.append(current_lr)

                # 定期记录日志
                if self.global_step % self.log_interval == 0:
                    elapsed = time.time() - start_time
                    logger.info(
                        f"Step {self.global_step} | Loss: {loss:.4f} | "
                        f"LR: {current_lr:.6f} | {elapsed:.2f}s elapsed"
                    )

                # 定期评估
                if self.val_loader is not None and self.global_step % self.eval_interval == 0:
                    val_loss = self.evaluate(self.val_loader)
                    self.val_losses.append(val_loss)

                    logger.info(f"Validation Loss: {val_loss:.4f}")

                    # 更新学习率（如果使用ReduceLROnPlateau）
                    if self.lr_scheduler is not None and self.lr_scheduler_type == 'plateau':
                        self.lr_scheduler.step(val_loss)

                    # 检查是否是最佳模型
                    is_best = val_loss < self.best_val_loss
                    if is_best:
                        self.best_val_loss = val_loss
                        logger.info(f"新的最佳验证损失: {val_loss:.4f}")

                    # 早停逻辑
                    if val_loss < self.early_stopping_best_loss - self.early_stopping_min_delta:
                        # 如果验证损失有显著改善，重置计数器
                        self.early_stopping_best_loss = val_loss
                        self.early_stopping_counter = 0
                        logger.info(f"验证损失改善，重置早停计数器")
                    else:
                        # 如果验证损失没有显著改善，增加计数器
                        self.early_stopping_counter += 1
                        logger.info(f"验证损失未改善，早停计数器: {self.early_stopping_counter}/{self.early_stopping_patience}")

                        # 如果达到耐心值，触发早停
                        if self.early_stopping_counter >= self.early_stopping_patience:
                            logger.info(f"触发早停，训练终止")
                            # 保存最终模型
                            self.save_checkpoint(epoch, val_loss, is_best)
                            return {
                                'train_losses': self.train_losses,
                                'val_losses': self.val_losses,
                                'lr_history': self.lr_history,
                                'best_val_loss': self.best_val_loss,
                                'global_step': self.global_step,
                                'early_stopped': True
                            }

                    # 保存检查点
                    if self.global_step % self.save_interval == 0 or is_best:
                        self.save_checkpoint(epoch, val_loss, is_best)

                # 更新全局步数
                self.global_step += 1

            # 计算轮平均损失
            avg_epoch_loss = epoch_loss / epoch_steps
            logger.info(f"Epoch {epoch+1} 完成，平均损失: {avg_epoch_loss:.4f}")

            # 如果没有验证集，根据训练损失保存检查点
            if self.val_loader is None and epoch % 1 == 0:
                self.save_checkpoint(epoch, avg_epoch_loss)

        # 训练完成
        logger.info(f"训练完成，总步数: {self.global_step}")

        # 保存最终模型
        final_path = os.path.join(self.checkpoint_dir, 'final_model.pt')
        torch.save(self.model.state_dict(), final_path)
        logger.info(f"保存最终模型到 {final_path}")

        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'lr_history': self.lr_history,
            'best_val_loss': self.best_val_loss,
            'global_step': self.global_step,
            'early_stopped': False
        }

    def plot_training_history(self):
        """绘制训练历史"""
        plt.figure(figsize=(15, 10))

        # 绘制训练损失
        plt.subplot(2, 1, 1)
        plt.plot(self.train_losses, label='Training Loss', alpha=0.3)

        # 平滑训练损失
        window_size = min(100, len(self.train_losses) // 10)
        if window_size > 0:
            smoothed_losses = pd.Series(self.train_losses).rolling(window=window_size).mean().values
            plt.plot(smoothed_losses, label=f'Smoothed Training Loss (window={window_size})')

        # 绘制验证损失
        if self.val_losses:
            # 计算验证损失的x坐标
            val_x = np.linspace(0, len(self.train_losses), len(self.val_losses))
            plt.plot(val_x, self.val_losses, label='Validation Loss', color='red')

        plt.xlabel('Steps')
        plt.ylabel('Loss')
        plt.title('Training and Validation Loss')
        plt.legend()
        plt.grid(True)

        # 绘制学习率
        plt.subplot(2, 1, 2)
        plt.plot(self.lr_history)
        plt.xlabel('Steps')
        plt.ylabel('Learning Rate')
        plt.title('Learning Rate Schedule')
        plt.grid(True)
        plt.yscale('log')

        plt.tight_layout()

        # 保存图表
        plt.savefig(os.path.join(self.checkpoint_dir, 'training_history.png'))
        plt.close()

        logger.info(f"训练历史图表已保存到 {os.path.join(self.checkpoint_dir, 'training_history.png')}")

    def generate_sample(self, input_df, code_id, max_new_tokens=10, temperature=1.0, top_k=None):
        """生成样本预测"""
        self.model.eval()

        # 将K线数据转换为token
        input_tokens = self.tokenizer.tokenize(input_df)

        # 确保输入token的长度是模型期望的长度
        seq_len = self.seq_len
        if len(input_tokens) < seq_len:
            # 如果输入长度不足，填充到seq_len
            input_tokens = input_tokens + [0] * (seq_len - len(input_tokens))
        elif len(input_tokens) > seq_len:
            # 如果输入长度超过seq_len，截断
            input_tokens = input_tokens[-seq_len:]  # 使用最后的seq_len个token

        input_tokens = torch.tensor([input_tokens], dtype=torch.long).to(self.device)

        # 准备代码ID
        code_ids = torch.tensor([code_id], dtype=torch.long).to(self.device)

        # 准备时间特征
        time_features = None
        if hasattr(self.model, 'use_time_features') and self.model.use_time_features and 'datetime' in input_df.columns:
            # 提取时间特征
            dt = pd.to_datetime(input_df['datetime'])

            # 确保时间特征的长度与输入序列相同
            if len(dt) > seq_len:
                dt = dt.iloc[-seq_len:]  # 使用最后的seq_len个时间点

            time_features = np.zeros((1, len(dt), 5))

            # 小时 (0-23) -> (0-1)
            time_features[0, :, 0] = dt.dt.hour / 23.0

            # 星期几 (0-6) -> (0-1)
            time_features[0, :, 1] = dt.dt.dayofweek / 6.0

            # 月份 (1-12) -> (0-1)
            time_features[0, :, 2] = (dt.dt.month - 1) / 11.0

            # 月内日 (1-31) -> (0-1)
            time_features[0, :, 3] = (dt.dt.day - 1) / 30.0

            # 是否为交易日 (0 or 1)
            time_features[0, :, 4] = (dt.dt.dayofweek < 5).astype(float)

            # 如果时间特征长度不足，填充
            if len(dt) < seq_len:
                padding = np.zeros((1, seq_len - len(dt), 5))
                time_features = np.concatenate([time_features, padding], axis=1)

            time_features = torch.tensor(time_features, dtype=torch.float).to(self.device)

        # 生成预测
        with torch.no_grad():
            try:
                generated_tokens = self.model.generate(
                    input_tokens=input_tokens,
                    code_ids=code_ids,
                    time_features=time_features,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    top_k=top_k
                )

                # 将生成的token转换回K线数据
                generated_tokens = generated_tokens[0, -max_new_tokens:].cpu().numpy().tolist()

                # 获取最后一个K线的收盘价和ATR，用于还原K线
                last_close = input_df['close'].iloc[-1]
                atr = self.tokenizer._calculate_atr(input_df).iloc[-1]

                # 将token转换为K线数据
                if hasattr(self.tokenizer, 'tokens_to_candlesticks'):
                    predicted_df = self.tokenizer.tokens_to_candlesticks(
                        generated_tokens,
                        start_price=last_close,
                        atr=atr
                    )
                else:
                    # 如果tokenizer没有实现tokens_to_candlesticks方法，返回原始token
                    predicted_df = pd.DataFrame({'token': generated_tokens})

                self.model.train()
                return predicted_df

            except Exception as e:
                print(f"生成预测时出现错误: {e}")
                self.model.train()
                return None
