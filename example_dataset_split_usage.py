#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集划分功能使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import argparse
import pandas as pd
import numpy as np
from torch.utils.data import Dataset

# 导入自定义模块
from pyqlab.models.gpt2.train_candlestick_vq_gpt import split_dataset

class ExampleCandlestickDataset(Dataset):
    """示例K线数据集"""
    def __init__(self, size=1000):
        self.size = size
        # 创建模拟的K线数据
        self.data = []
        base_price = 100.0
        
        for i in range(size):
            # 模拟时间戳
            timestamp = pd.Timestamp('2023-01-01') + pd.Timedelta(minutes=i)
            
            # 模拟价格波动
            price_change = np.random.normal(0, 0.01)  # 1%的波动
            base_price *= (1 + price_change)
            
            # 模拟OHLC数据
            open_price = base_price
            high_price = open_price * (1 + abs(np.random.normal(0, 0.005)))
            low_price = open_price * (1 - abs(np.random.normal(0, 0.005)))
            close_price = open_price * (1 + np.random.normal(0, 0.005))
            volume = np.random.randint(1000, 10000)
            
            sample = {
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'index': i  # 用于验证顺序
            }
            self.data.append(sample)
    
    def __len__(self):
        return self.size
    
    def __getitem__(self, idx):
        return self.data[idx]

def demonstrate_time_ordered_split():
    """演示按时间顺序划分"""
    print("=== 按时间顺序划分演示 ===")
    
    # 创建示例数据集
    dataset = ExampleCandlestickDataset(1000)
    
    # 按时间顺序划分
    train_dataset, val_dataset = split_dataset(
        dataset, 
        val_ratio=0.2, 
        seed=42, 
        shuffle=False  # 按时间顺序
    )
    
    print(f"总数据量: {len(dataset)}")
    print(f"训练集: {len(train_dataset)} 样本")
    print(f"验证集: {len(val_dataset)} 样本")
    
    # 分析时间分布
    train_timestamps = [train_dataset[i]['timestamp'] for i in range(min(5, len(train_dataset)))]
    val_timestamps = [val_dataset[i]['timestamp'] for i in range(min(5, len(val_dataset)))]
    
    print(f"\n训练集前5个时间戳:")
    for i, ts in enumerate(train_timestamps):
        print(f"  {i}: {ts}")
    
    print(f"\n验证集前5个时间戳:")
    for i, ts in enumerate(val_timestamps):
        print(f"  {i}: {ts}")
    
    # 验证时间顺序
    train_max_time = max([train_dataset[i]['timestamp'] for i in range(len(train_dataset))])
    val_min_time = min([val_dataset[i]['timestamp'] for i in range(len(val_dataset))])
    
    print(f"\n时间分界:")
    print(f"训练集最晚时间: {train_max_time}")
    print(f"验证集最早时间: {val_min_time}")
    print(f"时间连续性: {'✅' if train_max_time < val_min_time else '❌'}")
    
    print("\n优点: 符合实际交易场景，用历史数据预测未来")
    print("缺点: 可能存在时间偏差，验证集性能可能不能很好反映模型泛化能力")

def demonstrate_random_split():
    """演示随机划分"""
    print("\n=== 随机划分演示 ===")
    
    # 创建示例数据集
    dataset = ExampleCandlestickDataset(1000)
    
    # 随机划分
    train_dataset, val_dataset = split_dataset(
        dataset, 
        val_ratio=0.2, 
        seed=42, 
        shuffle=True  # 随机划分
    )
    
    print(f"总数据量: {len(dataset)}")
    print(f"训练集: {len(train_dataset)} 样本")
    print(f"验证集: {len(val_dataset)} 样本")
    
    # 分析索引分布
    train_indices = sorted(train_dataset.indices[:10])
    val_indices = sorted(val_dataset.indices[:10])
    
    print(f"\n训练集前10个索引: {train_indices}")
    print(f"验证集前10个索引: {val_indices}")
    
    # 分析时间分布
    train_times = [dataset[i]['timestamp'] for i in train_indices]
    val_times = [dataset[i]['timestamp'] for i in val_indices]
    
    print(f"\n对应的时间戳:")
    print("训练集:")
    for i, (idx, ts) in enumerate(zip(train_indices, train_times)):
        print(f"  索引{idx}: {ts}")
    
    print("验证集:")
    for i, (idx, ts) in enumerate(zip(val_indices, val_times)):
        print(f"  索引{idx}: {ts}")
    
    # 统计时间分布
    all_train_times = [dataset[i]['timestamp'] for i in train_dataset.indices]
    all_val_times = [dataset[i]['timestamp'] for i in val_dataset.indices]
    
    train_time_range = (min(all_train_times), max(all_train_times))
    val_time_range = (min(all_val_times), max(all_val_times))
    
    print(f"\n时间分布:")
    print(f"训练集时间范围: {train_time_range[0]} ~ {train_time_range[1]}")
    print(f"验证集时间范围: {val_time_range[0]} ~ {val_time_range[1]}")
    
    print("\n优点: 更好地评估模型泛化能力，避免时间偏差")
    print("缺点: 不符合实际交易场景（用未来数据训练预测过去）")

def demonstrate_reproducibility():
    """演示可重现性"""
    print("\n=== 可重现性演示 ===")
    
    dataset = ExampleCandlestickDataset(100)
    
    # 使用相同种子进行多次划分
    results = []
    for i in range(3):
        train_dataset, val_dataset = split_dataset(
            dataset, 
            val_ratio=0.2, 
            seed=42,  # 相同种子
            shuffle=True
        )
        results.append((train_dataset.indices[:5], val_dataset.indices[:5]))
    
    print("使用相同种子(42)进行3次随机划分:")
    for i, (train_idx, val_idx) in enumerate(results):
        print(f"第{i+1}次 - 训练集前5个索引: {train_idx}")
        print(f"第{i+1}次 - 验证集前5个索引: {val_idx}")
    
    # 验证结果一致性
    all_same = all(results[0] == result for result in results[1:])
    print(f"\n结果一致性: {'✅ 完全一致' if all_same else '❌ 不一致'}")
    
    # 使用不同种子
    train_dataset_diff, val_dataset_diff = split_dataset(
        dataset, 
        val_ratio=0.2, 
        seed=123,  # 不同种子
        shuffle=True
    )
    
    print(f"\n使用不同种子(123):")
    print(f"训练集前5个索引: {train_dataset_diff.indices[:5]}")
    print(f"验证集前5个索引: {val_dataset_diff.indices[:5]}")
    
    different = results[0][0] != train_dataset_diff.indices[:5]
    print(f"与种子42的结果不同: {'✅ 确实不同' if different else '❌ 意外相同'}")

def main():
    """主演示函数"""
    parser = argparse.ArgumentParser(description='数据集划分功能演示')
    parser.add_argument('--mode', type=str, default='all', 
                       choices=['all', 'time', 'random', 'repro'],
                       help='演示模式')
    
    args = parser.parse_args()
    
    print("数据集划分功能演示")
    print("=" * 50)
    
    if args.mode in ['all', 'time']:
        demonstrate_time_ordered_split()
    
    if args.mode in ['all', 'random']:
        demonstrate_random_split()
    
    if args.mode in ['all', 'repro']:
        demonstrate_reproducibility()
    
    if args.mode == 'all':
        print("\n" + "=" * 50)
        print("总结:")
        print("1. 时间顺序划分适合实际交易场景的模型训练")
        print("2. 随机划分适合评估模型的泛化能力")
        print("3. 通过设置随机种子可以确保结果的可重现性")
        print("4. 可以通过命令行参数灵活选择划分方式")
        
        print("\n使用方法:")
        print("# 使用时间顺序划分（默认）")
        print("python train_candlestick_vq_gpt.py --data_dir /path/to/data")
        
        print("\n# 使用随机划分")
        print("python train_candlestick_vq_gpt.py --data_dir /path/to/data --shuffle_split")
        
        print("\n# 指定随机种子")
        print("python train_candlestick_vq_gpt.py --data_dir /path/to/data --shuffle_split --seed 123")

if __name__ == "__main__":
    main()
