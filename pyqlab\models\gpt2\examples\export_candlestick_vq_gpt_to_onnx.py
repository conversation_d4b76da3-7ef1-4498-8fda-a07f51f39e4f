"""
导出CandlestickVQGPT模型和码本为ONNX格式

该脚本用于将训练好的CandlestickVQGPT模型和码本导出为ONNX格式，以便在C++中使用。
"""

import os
import sys
import argparse
import json
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和tokenizer
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod

def parse_args():
    parser = argparse.ArgumentParser(description='导出CandlestickVQGPT模型和码本为ONNX格式')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本路径')
    parser.add_argument('--output_dir', type=str, default='', help='输出目录，默认为模型所在目录')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--vectorization_method', type=str, default='percent_change',
                        choices=['atr_based', 'percent_change', 'log_return'],
                        help='向量化方法')
    parser.add_argument('--device', type=str, default='cpu', help='设备')
    return parser.parse_args()

def export_codebook_to_onnx(tokenizer, output_dir):
    """
    将码本导出为ONNX格式，分别导出编码和解码功能

    Args:
        tokenizer: CandlestickVQTokenizer实例
        output_dir: 输出目录
    """
    # 1. 导出编码功能（向量 -> token ID）
    encoder_path = os.path.join(output_dir, 'codebook_encoder.onnx')
    print(f"导出码本编码器到 {encoder_path}")

    # 创建一个简单的模块来包装VQ层的编码功能
    class CodebookEncoderModule(torch.nn.Module):
        def __init__(self, vq_layer):
            super().__init__()
            self.vq_layer = vq_layer

        def forward(self, x):
            return self.vq_layer(x)

    # 创建编码模块
    encoder_module = CodebookEncoderModule(tokenizer.vq_layer)
    encoder_module.eval()

    # 准备示例输入
    dummy_input = torch.randn(1, tokenizer.embedding_dim)

    # 导出编码模型
    torch.onnx.export(
        encoder_module,
        dummy_input,
        encoder_path,
        export_params=True,
        opset_version=12,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )

    print(f"码本编码器已导出到 {encoder_path}")

    # 验证导出的编码模型
    import onnx
    encoder_model = onnx.load(encoder_path)
    onnx.checker.check_model(encoder_model)
    print("编码器ONNX模型验证通过")

    # 2. 导出解码功能（token ID -> 向量）
    decoder_path = os.path.join(output_dir, 'codebook_decoder.onnx')
    print(f"导出码本解码器到 {decoder_path}")

    # 创建一个模块来包装VQ层的解码功能
    class CodebookDecoderModule(torch.nn.Module):
        def __init__(self, vq_layer):
            super().__init__()
            self.embedding = vq_layer.embedding

        def forward(self, indices):
            return self.embedding(indices)

    # 创建解码模块
    decoder_module = CodebookDecoderModule(tokenizer.vq_layer)
    decoder_module.eval()

    # 准备示例输入（索引）
    dummy_indices = torch.randint(0, tokenizer.num_embeddings, (1,), dtype=torch.long)
    print("dummy_indices:", dummy_indices)

    # 导出解码模型
    torch.onnx.export(
        decoder_module,
        dummy_indices,
        decoder_path,
        export_params=True,
        opset_version=12,
        do_constant_folding=True,
        input_names=['indices'],
        output_names=['embeddings'],
        dynamic_axes={
            'indices': {0: 'batch_size'},
            'embeddings': {0: 'batch_size'}
        }
    )

    print(f"码本解码器已导出到 {decoder_path}")

    # 验证导出的解码模型
    decoder_model = onnx.load(decoder_path)
    onnx.checker.check_model(decoder_model)
    print("解码器ONNX模型验证通过")

    # 3. 为了向后兼容，也导出一个统一的码本模型（但不推荐使用）
    legacy_path = os.path.join(output_dir, 'codebook.onnx')
    print(f"为了向后兼容，导出统一码本模型到 {legacy_path}")

    # 复制编码器模型作为统一模型
    import shutil
    shutil.copy2(encoder_path, legacy_path)

    print(f"统一码本模型已导出到 {legacy_path}")
    print("警告: 统一码本模型仅支持编码功能，不支持解码功能。建议使用专用的编码器和解码器模型。")

def main():
    args = parse_args()

    # 设置输出目录
    if not args.output_dir:
        args.output_dir = os.path.dirname(args.model_path)

    os.makedirs(args.output_dir, exist_ok=True)

    # 设置向量化方法
    if args.vectorization_method == 'atr_based':
        vectorization_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vectorization_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vectorization_method = VectorizationMethod.LOG_RETURN
    else:
        vectorization_method = VectorizationMethod.PERCENT_CHANGE

    # 加载模型配置
    config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {}
        print(f"警告: 未找到模型配置文件: {config_path}")

    # 创建Tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=config.get('num_embeddings', 1024),
        embedding_dim=config.get('embedding_dim', 5),
        atr_period=config.get('atr_period', 100),
        ma_volume_period=config.get('ma_volume_period', 100),
        vectorization_method=vectorization_method,
        detect_gaps=False,
        gap_threshold=7.0
    )

    print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")

    # 加载模型
    device = torch.device(args.device)
    model = CandlestickVQGPT.from_pretrained(args.model_path, device=device)
    model.eval()

    print(f"模型参数数量: {model.get_num_params():,}")

    # 导出模型为ONNX格式
    model_output_path = os.path.join(args.output_dir, 'model.onnx')

    # 准备时间特征形状
    time_shape = None
    if model.use_time_features:
        time_shape = (1, args.seq_len, 8)

    # 导出模型
    model.to_onnx(
        model_output_path,
        input_shape=(1, args.seq_len),
        code_shape=(1,),
        time_shape=time_shape
    )

    # 导出码本为ONNX格式
    export_codebook_to_onnx(tokenizer, args.output_dir)

    print(f"\n导出完成!")
    print(f"模型已导出到: {model_output_path}")
    print(f"码本编码器已导出到: {os.path.join(args.output_dir, 'codebook_encoder.onnx')}")
    print(f"码本解码器已导出到: {os.path.join(args.output_dir, 'codebook_decoder.onnx')}")
    print(f"兼容性码本已导出到: {os.path.join(args.output_dir, 'codebook.onnx')} (仅支持编码功能)")
    print(f"\n使用C++程序加载这些ONNX模型的建议:")
    print(f"1. 对于编码功能 (向量 -> token ID)，使用 'codebook_encoder.onnx'，输入名称为'input'，输出名称为'output'")
    print(f"2. 对于解码功能 (token ID -> 向量)，使用 'codebook_decoder.onnx'，输入名称为'indices'，输出名称为'embeddings'")
    print(f"3. 不建议使用兼容性码本'codebook.onnx'，因为它只支持编码功能")

if __name__ == "__main__":
    main()
