"""
CandlestickVQGPT Token不平衡问题解决示例
演示如何分析和解决token 795等高频token过度集中的问题
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from collections import Counter
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer
from pyqlab.models.gpt.token_balance_utils import TokenBalancer


def create_sample_imbalanced_data():
    """创建模拟的不平衡token数据"""
    print("创建模拟的不平衡token数据...")
    
    vocab_size = 1024
    num_sequences = 1000
    seq_len = 30
    
    # 创建严重不平衡的数据：token 795占60%，其他token分布不均
    token_sequences = []
    
    for _ in range(num_sequences):
        sequence = []
        for _ in range(seq_len):
            rand = np.random.random()
            if rand < 0.6:  # 60%的概率是token 795
                token = 795
            elif rand < 0.8:  # 20%的概率是其他几个高频token
                token = np.random.choice([100, 200, 300, 400])
            else:  # 20%的概率是随机token
                token = np.random.randint(0, vocab_size)
            sequence.append(token)
        token_sequences.append(sequence)
    
    print(f"创建了 {len(token_sequences)} 个序列，每个序列长度 {seq_len}")
    return token_sequences, vocab_size


def demonstrate_problem():
    """演示token不平衡问题"""
    print("\n" + "="*60)
    print("步骤1: 演示Token不平衡问题")
    print("="*60)
    
    token_sequences, vocab_size = create_sample_imbalanced_data()
    
    # 统计token分布
    all_tokens = []
    for seq in token_sequences:
        all_tokens.extend(seq)
    
    counter = Counter(all_tokens)
    total_tokens = len(all_tokens)
    
    print(f"总token数: {total_tokens:,}")
    print(f"唯一token数: {len(counter)}")
    print(f"词汇表利用率: {len(counter)/vocab_size:.2%}")
    
    print(f"\n最常见的tokens:")
    for token_id, count in counter.most_common(10):
        percentage = count / total_tokens
        print(f"  Token {token_id}: {count:,}次 ({percentage:.2%})")
        if percentage > 0.3:
            print(f"    🚨 严重不平衡!")
    
    return token_sequences, vocab_size


def demonstrate_analysis():
    """演示使用TokenBalancer分析"""
    print("\n" + "="*60)
    print("步骤2: 使用TokenBalancer分析")
    print("="*60)
    
    token_sequences, vocab_size = create_sample_imbalanced_data()
    
    # 创建分析器
    balancer = TokenBalancer(vocab_size)
    
    # 分析分布
    stats = balancer.analyze_token_distribution(
        token_sequences,
        save_plot=True,
        plot_path="example_token_distribution.png"
    )
    
    # 计算类别权重
    class_weights = balancer.compute_class_weights(
        method='sqrt_inverse_freq',
        smooth_factor=1.0
    )
    
    print(f"\n类别权重示例:")
    print(f"Token 795权重: {class_weights[795]:.4f}")
    print(f"Token 100权重: {class_weights[100]:.4f}")
    print(f"随机token权重: {class_weights[50]:.4f}")
    
    return balancer, class_weights


def demonstrate_model_training():
    """演示模型训练中的应用"""
    print("\n" + "="*60)
    print("步骤3: 模型训练中的应用")
    print("="*60)
    
    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=1024,
        code_size=100,
        seq_len=30,
        n_layer=2,  # 小模型用于演示
        n_head=4,
        d_model=64,
        dropout=0.1,
        use_auxiliary_loss=True  # 启用辅助损失
    )
    
    # 创建模拟数据
    batch_size = 8
    seq_len = 30
    
    input_tokens = torch.randint(0, 1024, (batch_size, seq_len))
    code_ids = torch.randint(0, 100, (batch_size,))
    targets = torch.full((batch_size, seq_len), 795)  # 模拟不平衡的targets
    
    # 添加一些其他token
    targets[:, :5] = torch.randint(0, 1024, (batch_size, 5))
    
    # 计算类别权重
    token_sequences, vocab_size = create_sample_imbalanced_data()
    balancer = TokenBalancer(vocab_size)
    balancer.analyze_token_distribution(token_sequences, save_plot=False)
    class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')
    
    print("训练前预测:")
    model.eval()
    with torch.no_grad():
        logits, _ = model(input_tokens, code_ids)
        preds = logits.argmax(dim=-1)
        pred_counter = Counter(preds[:, -1].tolist())
        print(f"最常见预测: {pred_counter.most_common(5)}")
    
    # 模拟训练步骤
    model.train()
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    print("\n开始训练...")
    for step in range(10):
        optimizer.zero_grad()
        
        # 使用平衡损失训练
        logits, loss = model(
            input_tokens=input_tokens,
            code_ids=code_ids,
            targets=targets,
            class_weights=class_weights  # 传入类别权重
        )
        
        loss.backward()
        optimizer.step()
        
        if step % 5 == 0:
            print(f"Step {step}, Loss: {loss.item():.4f}")
    
    print("训练后预测:")
    model.eval()
    with torch.no_grad():
        logits, _ = model(input_tokens, code_ids)
        preds = logits.argmax(dim=-1)
        pred_counter = Counter(preds[:, -1].tolist())
        print(f"最常见预测: {pred_counter.most_common(5)}")


def demonstrate_solutions():
    """演示完整的解决方案流程"""
    print("\n" + "="*60)
    print("步骤4: 完整解决方案流程")
    print("="*60)
    
    # 1. 问题识别
    print("1. 问题识别")
    token_sequences, vocab_size = create_sample_imbalanced_data()
    
    all_tokens = []
    for seq in token_sequences:
        all_tokens.extend(seq)
    
    counter = Counter(all_tokens)
    total_tokens = len(all_tokens)
    
    # 检查是否存在高频token问题
    max_freq = max(count/total_tokens for count in counter.values())
    high_freq_threshold = 0.2
    
    if max_freq > high_freq_threshold:
        print(f"✅ 检测到高频token问题，最高频率: {max_freq:.2%}")
    
    # 2. 详细分析
    print("\n2. 详细分析")
    balancer = TokenBalancer(vocab_size)
    stats = balancer.analyze_token_distribution(token_sequences, save_plot=False)
    
    # 计算基尼系数
    frequencies = [count/total_tokens for count in counter.values()]
    frequencies.sort()
    n = len(frequencies)
    gini = (2 * sum((i+1) * freq for i, freq in enumerate(frequencies))) / (n * sum(frequencies)) - (n+1) / n
    print(f"基尼系数: {gini:.4f}")
    
    # 3. 应用解决方案
    print("\n3. 应用解决方案")
    
    # 计算类别权重
    class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')
    print(f"✅ 已计算类别权重")
    
    # 数据重采样
    if max_freq > high_freq_threshold:
        resampled_sequences = balancer.resample_sequences(
            token_sequences,
            target_distribution='uniform'
        )
        print(f"✅ 已完成数据重采样")
    
    # 4. 效果验证
    print("\n4. 效果验证")
    print("建议在实际训练中监控以下指标:")
    print("  - 验证集预测多样性")
    print("  - 各token的预测频率")
    print("  - 模型收敛情况")
    
    print("\n✅ 完整流程演示完成！")


def generate_training_script():
    """生成训练脚本示例"""
    print("\n" + "="*60)
    print("步骤5: 生成训练脚本示例")
    print("="*60)
    
    script_content = '''
# 平衡训练配置示例
import torch
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt.token_balance_utils import TokenBalancer

# 1. 分析数据分布
balancer = TokenBalancer(vocab_size=1024)
stats = balancer.analyze_token_distribution(token_sequences)
class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')

# 2. 创建模型
model = CandlestickVQGPT(
    vocab_size=1024,
    code_size=100,
    seq_len=30,
    n_layer=4,
    n_head=8,
    d_model=128,
    dropout=0.15,  # 增加dropout
    label_smoothing=0.05,  # 减少标签平滑
    use_auxiliary_loss=True  # 启用多样性损失
)

# 3. 训练循环
optimizer = torch.optim.AdamW(model.parameters(), lr=3e-5, weight_decay=0.1)

for epoch in range(epochs):
    for batch in dataloader:
        input_tokens, code_ids, targets = batch
        
        # 使用平衡损失
        logits, loss = model(
            input_tokens=input_tokens,
            code_ids=code_ids,
            targets=targets,
            class_weights=class_weights  # 关键：传入类别权重
        )
        
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
        
        # 监控预测多样性
        if step % 100 == 0:
            with torch.no_grad():
                preds = logits.argmax(dim=-1)
                diversity = len(set(preds[:, -1].tolist())) / len(preds)
                print(f"预测多样性: {diversity:.2%}")
'''
    
    with open("balanced_training_example.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("训练脚本示例已保存到: balanced_training_example.py")


def main():
    """主函数"""
    print("CandlestickVQGPT Token不平衡问题解决示例")
    print("=" * 60)
    
    # 演示问题
    demonstrate_problem()
    
    # 演示分析
    demonstrate_analysis()
    
    # 演示模型训练
    demonstrate_model_training()
    
    # 演示完整解决方案
    demonstrate_solutions()
    
    # 生成训练脚本
    generate_training_script()
    
    print("\n" + "="*60)
    print("示例演示完成！")
    print("="*60)
    print("\n主要解决方案:")
    print("1. 使用TokenBalancer分析token分布")
    print("2. 计算类别权重处理不平衡")
    print("3. 在模型中使用Focal Loss + 多样性损失")
    print("4. 监控训练过程中的预测多样性")
    print("5. 可选择使用数据重采样")
    
    print("\n下一步:")
    print("1. 在实际数据上运行analyze_candlestick_token_balance.py")
    print("2. 使用train_balanced_candlestick_vq_gpt.py进行平衡训练")
    print("3. 监控训练过程中的多样性指标")


if __name__ == "__main__":
    main()
