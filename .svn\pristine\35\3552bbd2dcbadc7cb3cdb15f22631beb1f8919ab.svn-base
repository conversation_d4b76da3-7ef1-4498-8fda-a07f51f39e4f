{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# from pyecharts import online\n", "from datetime import datetime\n", "import talib as ta\n", "import pandas as pd\n", "# online()\n", "# import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "# sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 参数选择\n", "关于ATR，STDDEV等指标的period参数，在BarSize分别为day或min5的情况下，period通常取值20，min5如果取值20会显得太小，合约大部分交易时间波动性并不明显，特别是在期货夜盘时，更是这样。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def func_atr_stats(blkname='ZLQH', barsize=BarSize.day, len=220, win=20):\n", "    fut_codes = ds.get_block_data(blkname) #ZLQH\n", "    sys_atr=[]\n", "    def_atr=[]\n", "    col_atr=[]\n", "    col_natr=[]\n", "    col_chg=[]\n", "    col_prc=[]\n", "    codes=[]\n", "    names=[]\n", "    for symbol in fut_codes:\n", "        real_hist = ds.get_history_data(symbol, len, [BarData.high, BarData.low, BarData.close], barsize)\n", "        if real_hist.shape[0] < win:\n", "            # print(\"{} history bars is None.\".format(symbol))\n", "            continue\n", "        codes.append(symbol)\n", "        names.append(ds.get_sec_name(symbol))\n", "        sys_atr.append(ds.get_rangebar_atr(symbol, barsize, True))\n", "        def_atr.append(ds.get_default_rangebar_atr(symbol, barsize, True))\n", "        real_atr = ta.ATR(real_hist[:, 0], real_hist[:, 1], real_hist[:, 2], win)\n", "        real_natr = ta.NATR(real_hist[:, 0], real_hist[:, 1], real_hist[:, 2], win)\n", "        col_atr.append(real_atr[-1])\n", "        col_natr.append(real_natr[-1])\n", "        col_prc.append(real_hist[2][-1])\n", "        col_chg.append(real_atr[-1]/col_prc[-1]*100)\n", "\n", "    df = pd.DataFrame(\n", "        {\"NAME\": names, \"SYS ATR\": sys_atr, \"DEF ATR\": def_atr, \"ATR\": col_atr, \"NATR\": col_natr, \"CHG\": col_chg, \"PRICE\": col_prc},\n", "        index=codes\n", "    )\n", "    df.sort_values(by=\"NATR\", ascending=False, inplace=True)\n", "    return df\n", "\n", "\n", "def atr_stats_summary(blkname):\n", "    print(\"===================day====================\")\n", "    df = func_atr_stats(blkname=blkname, barsize=BarSize.day, win=15)\n", "    print(df)\n", "    print(df.describe())\n", "    #plt.hist(atr, bins='auto')\n", "    plt.hist(df['CHG'], bins='auto')\n", "\n", "    print(\"===================min5====================\")\n", "    df = func_atr_stats(blkname=blkname, barsize=BarSize.min5, win=15)\n", "    print(df)\n", "    print(df.describe())\n", "    #plt.hist(atr, bins='auto')\n", "    plt.hist(df['CHG'], bins='auto')\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===================day====================\n", "               NAME  SYS ATR  DEF ATR          ATR      NATR       CHG  \\\n", "NI2212.SC    沪镍2212  1681.00  1681.00  7151.807911  3.607833  5.110624   \n", "SN2212.SC    沪锡2212  1685.00  1685.00  6216.334369  3.483516  2.134950   \n", "P2301.DC     棕榈2301   225.00   147.00   273.874280  3.446694  2.924133   \n", "I2301.DC     铁矿2301    18.00    32.00    24.448348  3.372186  3.597991   \n", "JM2301.DC    焦煤2301    57.00    50.00    69.622517  3.340010  3.288735   \n", "J2301.DC     焦炭2301    72.00    29.00    82.853954  3.084659  2.759958   \n", "SC2301.SC    原油2301     6.00     6.00    18.527189  3.071484  4.314669   \n", "BU2301.SC    沥青2301    62.00    62.00   105.290136  3.046590  3.124336   \n", "TA2301.ZC   PTA2301   132.00    69.00   145.914308  2.838800  2.996187   \n", "MA2301.ZC    甲醇2301    55.00    48.00    71.313978  2.805428  2.791154   \n", "EB2212.DC   苯乙烯2212   218.00   218.00   210.619362  2.616390  2.479333   \n", "EG2301.DC   乙二醇2301    83.00    89.00    99.456915  2.610418  2.085051   \n", "SF2301.ZC    硅铁2301   185.00   185.00   207.152238  2.563765  2.495209   \n", "PG2301.DC   液化气2301    97.00    97.00   123.040484  2.532218  2.403604   \n", "FG2301.ZC    玻璃2301    28.00    34.00    33.423536  2.450406  1.847625   \n", "PF2301.ZC    短纤2301   130.00   130.00   161.843654  2.430085  2.377257   \n", "V2301.DC    PVC2301   112.00   125.00   138.007897  2.383968  1.623049   \n", "SS2212.SC   不锈钢2212   268.00   268.00   401.391291  2.383559  2.625188   \n", "Y2301.DC     豆油2301   190.00   137.00   215.253003  2.383226  2.337674   \n", "RM2301.ZC    菜粕2301    61.00    56.00    71.420962  2.371214  2.363367   \n", "LH2301.<PERSON>    生猪2301   409.00   401.00   494.919666  2.368603  3.552905   \n", "OI2301.ZC    菜油2301   222.00   138.00   260.229317  2.335571  2.096595   \n", "UR2301.ZC    尿素2301    48.00    31.00    57.578169  2.286663  2.374358   \n", "CF2301.ZC    郑棉2301   269.00   200.00   303.595022  2.261415  1.408794   \n", "IM2212.SF    中千2212   116.00   116.00   143.023145  2.181162  2.110357   \n", "ZN2212.SC    沪锌2212   212.00   212.00   532.092861  2.181155  2.382861   \n", "AG2212.SC    白银2212    74.00   139.00   104.269396  2.157000  2.114998   \n", "IH2212.SF    上证2212    41.00    41.00    54.964927  2.153461  1.784228   \n", "NR2301.SC  20号胶2301   292.00   292.00   202.920796  2.098457  1.760701   \n", "AP2301.ZC    苹果2301   140.00   129.00   167.190983  2.085456  1.985641   \n", "CJ2301.ZC    红枣2301   198.00   118.00   231.273630  2.078864  1.771533   \n", "SA2301.ZC    纯碱2301    48.00    34.00    54.123807  2.074504  2.242080   \n", "SM2301.ZC    锰硅2301   114.00   170.00   145.273833  2.054211  1.755787   \n", "RU2301.SC    橡胶2301   182.00   355.00   259.762522  2.031776  1.792084   \n", "RB2301.SC    螺纹2301    65.00    78.00    74.198715  2.017914  1.627522   \n", "IF2212.SF    沪深2212    57.00    57.00    75.569849  2.001426  1.899217   \n", "B2212.DC     豆二2212    83.00    83.00   103.313542  1.999488  2.493689   \n", "HC2301.SC    热卷2301    64.00    84.00    71.222504  1.896738  1.510552   \n", "CY2301.ZC    棉纱2301   332.00   241.00   398.759684  1.870794  1.351728   \n", "L2301.DC     乙烯2301   112.00   111.00   142.107667  1.822127  1.662856   \n", "PP2301.DC    丙烯2301   106.00   114.00   135.848529  1.794327  1.689658   \n", "IC2212.SF    中证2212    76.00    76.00   107.649398  1.754219  1.553337   \n", "AL2212.SC    沪铝2212   138.00   138.00   335.115545  1.745393  1.647974   \n", "M2301.DC     豆粕2301    64.00    66.00    71.759537  1.721678  2.131894   \n", "CU2212.SC    沪铜2212   545.00   545.00  1122.097178  1.706871  1.622700   \n", "SP2301.SC    纸浆2301   106.00   122.00   113.391907  1.628258  1.976157   \n", "PK2301.ZC    花生2301   153.00   135.00   164.405704  1.560419  2.060738   \n", "CS2301.DC    淀粉2301    19.00    19.00    45.617710  1.529256  1.502560   \n", "JD2301.DC    鸡蛋2301    52.00    62.00    66.905614  1.523352  1.641856   \n", "A2301.DC     豆一2301   102.00   102.00    73.324623  1.315240  1.208382   \n", "C2301.DC     玉米2301    25.00    16.00    30.524181  1.068774  1.162826   \n", "SR2301.<PERSON><PERSON>    白糖2301    44.00    64.00    54.745315  0.971351  0.957588   \n", "AU2302.SC    黄金2302     3.15     3.15     3.137629  0.774991  0.820853   \n", "RR2301.DC    粳米2301    32.00    32.00    16.612431  0.488314  0.491783   \n", "\n", "                   PRICE  \n", "NI2212.SC  139940.000000  \n", "SN2212.SC  291170.000000  \n", "P2301.DC     9366.000000  \n", "I2301.DC      679.500000  \n", "JM2301.DC    2117.000000  \n", "J2301.DC     3002.000000  \n", "SC2301.SC     429.399994  \n", "BU2301.SC    3370.000000  \n", "TA2301.ZC    4870.000000  \n", "MA2301.ZC    2555.000000  \n", "EB2212.DC    8495.000000  \n", "EG2301.DC    4770.000000  \n", "SF2301.ZC    8302.000000  \n", "PG2301.DC    5119.000000  \n", "FG2301.ZC    1809.000000  \n", "PF2301.ZC    6808.000000  \n", "V2301.DC     8503.000000  \n", "SS2212.SC   15290.000000  \n", "Y2301.DC     9208.000000  \n", "RM2301.ZC    3022.000000  \n", "LH2301.DC   13930.000000  \n", "OI2301.ZC   12412.000000  \n", "UR2301.ZC    2425.000000  \n", "CF2301.ZC   21550.000000  \n", "IM2212.SF    6777.200195  \n", "ZN2212.SC   22330.000000  \n", "AG2212.SC    4930.000000  \n", "IH2212.SF    3080.600098  \n", "NR2301.SC   11525.000000  \n", "AP2301.ZC    8420.000000  \n", "CJ2301.ZC   13055.000000  \n", "SA2301.ZC    2414.000000  \n", "SM2301.ZC    8274.000000  \n", "RU2301.SC   14495.000000  \n", "RB2301.SC    4559.000000  \n", "IF2212.SF    3979.000000  \n", "B2212.DC     4143.000000  \n", "HC2301.SC    4715.000000  \n", "CY2301.ZC   29500.000000  \n", "L2301.DC     8546.000000  \n", "PP2301.DC    8040.000000  \n", "IC2212.SF    6930.200195  \n", "AL2212.SC   20335.000000  \n", "M2301.DC     3366.000000  \n", "CU2212.SC   69150.000000  \n", "SP2301.SC    5738.000000  \n", "PK2301.ZC    7978.000000  \n", "CS2301.DC    3036.000000  \n", "JD2301.DC    4075.000000  \n", "A2301.DC     6068.000000  \n", "C2301.DC     2625.000000  \n", "SR2301.ZC    5717.000000  \n", "AU2302.SC     382.239990  \n", "RR2301.DC    3378.000000  \n", "           SYS ATR      DEF ATR          ATR       NATR        CHG  \\\n", "count    54.000000    54.000000    54.000000  54.000000  54.000000   \n", "mean    183.484259   179.113889   408.131883   2.191883   2.144942   \n", "std     314.733171   314.854759  1258.189639   0.666703   0.820039   \n", "min       3.150000     3.150000     3.137629   0.488314   0.491783   \n", "25%      57.000000    56.250000    71.245372   1.801277   1.643385   \n", "50%     104.000000   106.500000   118.216195   2.155230   2.072894   \n", "75%     188.750000   145.000000   214.094593   2.511765   2.460401   \n", "max    1685.000000  1685.000000  7151.807911   3.607833   5.110624   \n", "\n", "               PRICE  \n", "count      54.000000  \n", "mean    16235.243342  \n", "std     43332.193019  \n", "min       382.239990  \n", "25%      3367.000000  \n", "50%      5903.000000  \n", "75%      9326.500000  \n", "max    291170.000000  \n", "===================min5====================\n", "               NAME  SYS ATR  DEF ATR         ATR      NATR       CHG  \\\n", "I2301.DC     铁矿2301      0.0    32.00    2.539136  0.355124  0.342202   \n", "J2301.DC     焦炭2301      0.0    29.00    9.266329  0.344089  0.335980   \n", "JM2301.DC    焦煤2301      0.0    50.00    6.792442  0.322222  0.315268   \n", "LH2301.DC    生猪2301      0.0    49.00   64.003803  0.311985  0.294473   \n", "UR2301.ZC    尿素2301      0.0    31.00    7.650189  0.304546  0.314693   \n", "P2301.DC     棕榈2301      0.0   147.00   23.623177  0.294847  0.298348   \n", "MA2301.ZC    甲醇2301      0.0    48.00    7.554791  0.294190  0.300748   \n", "FG2301.ZC    玻璃2301      0.0    34.00    3.783524  0.272392  0.275968   \n", "BU2301.SC    沥青2301      0.0    62.00    9.365737  0.271944  0.259439   \n", "SF2301.ZC    硅铁2301      0.0   185.00   21.672475  0.267694  0.252888   \n", "NI2212.SC    沪镍2212      0.0  1681.00  522.041100  0.258744  0.260760   \n", "SN2212.SC    沪锡2212      0.0  1685.00  468.064977  0.256221  0.252190   \n", "PG2301.DC   液化气2301      0.0    97.00   12.213350  0.249915  0.250582   \n", "SM2301.ZC    锰硅2301      0.0   170.00   17.607083  0.248688  0.238967   \n", "IM2212.SF    中千2212      0.0    13.00   16.221127  0.247379  0.242425   \n", "CJ2301.ZC    红枣2301      0.0   118.00   25.909623  0.232895  0.229899   \n", "TA2301.ZC   PTA2301      0.0    69.00   11.965650  0.232523  0.226451   \n", "EB2212.DC   苯乙烯2212      0.0   218.00   18.307161  0.226658  0.219853   \n", "SC2301.SC    原油2301      0.0     6.00    1.360649  0.226172  0.227115   \n", "IC2212.SF    中证2212      0.0    76.00   12.908662  0.210355  0.207561   \n", "IH2212.SF    上证2212      0.0    41.00    5.361184  0.210045  0.208315   \n", "EG2301.DC   乙二醇2301      0.0    89.00    7.982569  0.208858  0.207501   \n", "RM2301.ZC    菜粕2301      0.0    56.00    6.271187  0.208692  0.208692   \n", "AP2301.ZC    苹果2301      0.0   129.00   16.728063  0.208320  0.203504   \n", "V2301.DC    PVC2301      0.0   125.00   12.068482  0.207648  0.208365   \n", "PK2301.ZC    花生2301      0.0    16.00   21.454554  0.203863  0.196255   \n", "HC2301.SC    热卷2301      0.0    84.00    7.624961  0.202845  0.200657   \n", "RB2301.SC    螺纹2301      0.0    78.00    7.367261  0.200688  0.197832   \n", "SA2301.ZC    纯碱2301      0.0    34.00    5.211706  0.199377  0.202004   \n", "IF2212.SF    沪深2212      0.0    57.00    7.499282  0.198604  0.194746   \n", "B2212.DC     豆二2212      0.0     8.00   10.145842  0.198316  0.195037   \n", "SS2212.SC   不锈钢2212      0.0   268.00   33.198932  0.198025  0.201267   \n", "JD2301.DC    鸡蛋2301      0.0    62.00    8.686173  0.197908  0.199178   \n", "Y2301.DC     豆油2301      0.0   137.00   17.922006  0.197640  0.195783   \n", "CF2301.ZC    郑棉2301      0.0   200.00   25.741409  0.192387  0.190960   \n", "PF2301.ZC    短纤2301      0.0    10.00   12.635462  0.189779  0.183815   \n", "ZN2212.SC    沪锌2212      0.0   212.00   45.400067  0.186104  0.186908   \n", "CY2301.ZC    棉纱2301      0.0   241.00   38.575162  0.181019  0.185324   \n", "OI2301.ZC    菜油2301      0.0   138.00   19.652973  0.176276  0.165680   \n", "RU2301.SC    橡胶2301      0.0   355.00   21.211871  0.165847  0.165588   \n", "AL2212.SC    沪铝2212      0.0   138.00   30.830388  0.160575  0.163383   \n", "L2301.DC     乙烯2301      0.0   111.00   11.993401  0.151758  0.152568   \n", "CS2301.DC    淀粉2301      0.0    19.00    4.475310  0.148533  0.151346   \n", "M2301.DC     豆粕2301      0.0    66.00    6.102445  0.146026  0.147438   \n", "PP2301.DC    丙烯2301      0.0   114.00   10.775675  0.141172  0.140546   \n", "NR2301.SC  20号胶2301      0.0   292.00   13.638691  0.141041  0.142218   \n", "AG2212.SC    白银2212      0.0   139.00    6.528425  0.135332  0.133944   \n", "SP2301.SC    纸浆2301      0.0   122.00    9.221219  0.131207  0.133835   \n", "C2301.DC     玉米2301      0.0    16.00    3.462497  0.120687  0.123133   \n", "CU2212.SC    沪铜2212      0.0   545.00   77.049960  0.117204  0.115708   \n", "A2301.DC     豆一2301      0.0   102.00    5.136308  0.092413  0.090651   \n", "SR2301.Z<PERSON>    白糖2301      0.0    64.00    5.143155  0.090740  0.090389   \n", "PB2301.SC    沪铅2301      0.0   158.00   11.394854  0.072417  0.072625   \n", "RR2301.DC    粳米2301      0.0    32.00    2.389509  0.070259  0.070383   \n", "AU2302.SC    黄金2302      0.0     3.15    0.168112  0.041556  0.041224   \n", "\n", "                   PRICE  \n", "I2301.DC      742.000000  \n", "J2301.DC     2758.000000  \n", "JM2301.DC    2154.500000  \n", "LH2301.DC   21735.000000  \n", "UR2301.ZC    2431.000000  \n", "P2301.DC     7918.000000  \n", "MA2301.ZC    2512.000000  \n", "FG2301.ZC    1371.000000  \n", "BU2301.SC    3610.000000  \n", "SF2301.ZC    8570.000000  \n", "NI2212.SC  200200.000000  \n", "SN2212.SC  185600.000000  \n", "PG2301.DC    4874.000000  \n", "SM2301.ZC    7368.000000  \n", "IM2212.SF    6691.200195  \n", "CJ2301.ZC   11270.000000  \n", "TA2301.ZC    5284.000000  \n", "EB2212.DC    8327.000000  \n", "SC2301.SC     599.099976  \n", "IC2212.SF    6219.200195  \n", "IH2212.SF    2573.600098  \n", "EG2301.DC    3847.000000  \n", "RM2301.ZC    3005.000000  \n", "AP2301.ZC    8220.000000  \n", "V2301.DC     5792.000000  \n", "PK2301.ZC   10932.000000  \n", "HC2301.SC    3800.000000  \n", "RB2301.SC    3724.000000  \n", "SA2301.ZC    2580.000000  \n", "IF2212.SF    3850.800049  \n", "B2212.DC     5202.000000  \n", "SS2212.SC   16495.000000  \n", "JD2301.DC    4361.000000  \n", "Y2301.DC     9154.000000  \n", "CF2301.ZC   13480.000000  \n", "PF2301.ZC    6874.000000  \n", "ZN2212.SC   24290.000000  \n", "CY2301.ZC   20815.000000  \n", "OI2301.ZC   11862.000000  \n", "RU2301.SC   12810.000000  \n", "AL2212.SC   18870.000000  \n", "L2301.DC     7861.000000  \n", "CS2301.DC    2957.000000  \n", "M2301.DC     4139.000000  \n", "PP2301.DC    7667.000000  \n", "NR2301.SC    9590.000000  \n", "AG2212.SC    4874.000000  \n", "SP2301.SC    6890.000000  \n", "C2301.DC     2812.000000  \n", "CU2212.SC   66590.000000  \n", "A2301.DC     5666.000000  \n", "SR2301.ZC    5690.000000  \n", "PB2301.SC   15690.000000  \n", "RR2301.DC    3395.000000  \n", "AU2302.SC     407.799988  \n", "       SYS ATR      DEF ATR         ATR       NATR        CHG          PRICE\n", "count     55.0    55.000000   55.000000  55.000000  55.000000      55.000000\n", "mean       0.0   164.748182   32.580001   0.202214   0.200229   15036.367282\n", "std        0.0   313.444659   91.943388   0.068884   0.067233   36213.615437\n", "min        0.0     3.150000    0.168112   0.041556   0.041224     407.799988\n", "25%        0.0    37.500000    6.660433   0.156166   0.157976    3502.500000\n", "50%        0.0    84.000000   11.394854   0.200688   0.200657    5792.000000\n", "75%        0.0   143.000000   20.432422   0.248033   0.240696   10261.000000\n", "max        0.0  1685.000000  522.041100   0.355124   0.342202  200200.000000\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["atr_stats_summary('ZLQH')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===================day====================\n", "                 ATR       NATR        CHG       PRICE\n", "688200.SH  26.725660  11.037731   4.477110  596.940002\n", "300565.SZ   4.359942  10.996069  36.302593   12.010000\n", "603728.SH   3.717572  10.390084  21.932580   16.949999\n", "002896.SZ   2.843564   9.942532  17.910045   15.876925\n", "688601.SH   7.751551   9.798446   4.171537  185.819992\n", "...              ...        ...        ...         ...\n", "601288.SH   0.025829   0.912679   0.951968    2.713200\n", "601988.SH   0.026079   0.855042   0.925107    2.819000\n", "601169.SH   0.034704   0.840280   0.847462    4.095000\n", "601398.SH   0.034619   0.797669   0.796440    4.346700\n", "601328.SH   0.036072   0.785892   0.843800    4.275000\n", "\n", "[4638 rows x 4 columns]\n", "               ATR         NATR          CHG        PRICE\n", "count  4638.000000  4638.000000  4638.000000  4638.000000\n", "mean      1.040662     4.325092     4.278605    25.600437\n", "std       2.355404     1.383459     2.922321    51.138210\n", "min       0.023428     0.785892     0.796440     1.182694\n", "25%       0.227968     3.352963     2.554381     6.322500\n", "50%       0.459934     4.117467     3.449483    12.405050\n", "75%       0.972396     5.085582     4.969142    26.218751\n", "max      80.357898    11.037731    36.302593  1768.334961\n", "===================min5====================\n", "                 ATR       NATR        CHG       PRICE\n", "688200.SH  26.725660  11.037731   4.477110  596.940002\n", "300565.SZ   4.359942  10.996069  36.302593   12.010000\n", "603728.SH   3.717572  10.390084  21.932580   16.949999\n", "002896.SZ   2.843564   9.942532  17.910045   15.876925\n", "688601.SH   7.751551   9.798446   4.171537  185.819992\n", "...              ...        ...        ...         ...\n", "601288.SH   0.025829   0.912679   0.951968    2.713200\n", "601988.SH   0.026079   0.855042   0.925107    2.819000\n", "601169.SH   0.034704   0.840280   0.847462    4.095000\n", "601398.SH   0.034619   0.797669   0.796440    4.346700\n", "601328.SH   0.036072   0.785892   0.843800    4.275000\n", "\n", "[4638 rows x 4 columns]\n", "               ATR         NATR          CHG        PRICE\n", "count  4638.000000  4638.000000  4638.000000  4638.000000\n", "mean      1.040662     4.325092     4.278605    25.600437\n", "std       2.355404     1.383459     2.922321    51.138210\n", "min       0.023428     0.785892     0.796440     1.182694\n", "25%       0.227968     3.352963     2.554381     6.322500\n", "50%       0.459934     4.117467     3.449483    12.405050\n", "75%       0.972396     5.085582     4.969142    26.218751\n", "max      80.357898    11.037731    36.302593  1768.334961\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAigAAAGdCAYAAAA44ojeAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAPYQAAD2EBqD+naQAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nO3df0yUd4LH8c8cP6ZIYSqgM8xJCddiLxY0u9BTuG5FUSw5ta3N6p6bBrNu065KjiCxi01TmnShda/abth67V2jVdvD5LZ0m9R60qj0PGKCZE2Rbrpulra4MuW2hzNA6WDpc380PtcB/DEIzJfh/Uq+Cc/zfOeZ77ffifPp9/k+zzgsy7IEAABgkL+KdAMAAABGIqAAAADjEFAAAIBxCCgAAMA4BBQAAGAcAgoAADAOAQUAABiHgAIAAIwTG+kGjMc333yjixcvKikpSQ6HI9LNAQAAN8CyLPX19cnr9eqv/uracyTTMqBcvHhRGRkZkW4GAAAYh66uLs2bN++adaZlQElKSpL0bQeTk5Mj3BoAAHAjAoGAMjIy7O/xa5mWAeXKZZ3k5GQCCgAA08yNLM9gkSwAADAOAQUAABiHgAIAAIxDQAEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcWIj3YCoVuMase2PTDsAAJhmmEEBAADGIaAAAADjEFAAAIBxCCgAAMA4BBQAAGCcmwoodXV1cjgcqqiosPdZlqWamhp5vV4lJCSoqKhIHR0dIa8LBoMqLy9XWlqaEhMTtXbtWl24cOFmmgIAAKLIuANKa2urXn31VS1cuDBk/65du7R7927V19ertbVVHo9HK1euVF9fn12noqJCjY2Namho0KlTp9Tf36/Vq1dreHh4/D0BAABRY1wBpb+/Xz/+8Y/1r//6r5o9e7a937Isvfjii3ryySe1bt065eTk6PXXX9eXX36pN998U5Lk9/v12muv6YUXXtCKFSv0ve99T4cOHVJ7e7vef//9iekVAACY1sYVULZu3ap/+Id/0IoVK0L2d3Z2yufzqaSkxN7ndDq1dOlStbS0SJLa2tp0+fLlkDper1c5OTl2nZGCwaACgUBIAQAA0SvsJ8k2NDSora1NZ86cGXXM5/NJktxud8h+t9utTz/91K4THx8fMvNypc6V149UV1enZ555JtymAgCAaSqsGZSuri790z/9k9544w3dcsstV63ncDhCti3LGrVvpGvVqa6ult/vt0tXV1c4zQYAANNMWAGlra1NPT09ysvLU2xsrGJjY9Xc3Kxf/epXio2NtWdORs6E9PT02Mc8Ho+GhobU29t71TojOZ1OJScnhxQAABC9wgooxcXFam9v19mzZ+2Sn5+vH//4xzp79qz+5m/+Rh6PR01NTfZrhoaG1NzcrMLCQklSXl6e4uLiQup0d3fr3Llzdp0Zo8b1/wUAANjCWoOSlJSknJyckH2JiYlKTU2191dUVKi2tlbZ2dnKzs5WbW2tZs2apY0bN0qSXC6XNm/erO3btys1NVUpKSmqqqpSbm7uqEW3AABgZgp7kez17NixQ4ODg9qyZYt6e3u1ePFiHTt2TElJSXadPXv2KDY2VuvXr9fg4KCKi4u1f/9+xcTETHRzAADANOSwLMuKdCPCFQgE5HK55Pf7zV6PEs6lmxr/5LUDAAADhPP9zW/xAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACMQ0ABAADGIaAAAADjEFAAAIBxCCgAAMA4BBQAAGAcAgoAADAOAQUAABiHgAIAAIxDQAEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACME1ZA2bt3rxYuXKjk5GQlJyeroKBA7733nn1806ZNcjgcIWXJkiUh5wgGgyovL1daWpoSExO1du1aXbhwYWJ6AwAAokJYAWXevHl67rnndObMGZ05c0bLly/XAw88oI6ODrvO/fffr+7ubrscOXIk5BwVFRVqbGxUQ0ODTp06pf7+fq1evVrDw8MT0yMAADDtxYZTec2aNSHbv/jFL7R3716dPn1ad999tyTJ6XTK4/GM+Xq/36/XXntNBw8e1IoVKyRJhw4dUkZGht5//32tWrVqPH0AAABRZtxrUIaHh9XQ0KCBgQEVFBTY+0+ePKm5c+dq/vz5evTRR9XT02Mfa2tr0+XLl1VSUmLv83q9ysnJUUtLy3ibAgAAokxYMyiS1N7eroKCAn311Ve69dZb1djYqAULFkiSSktL9cMf/lCZmZnq7OzUU089peXLl6utrU1Op1M+n0/x8fGaPXt2yDndbrd8Pt9V3zMYDCoYDNrbgUAg3GYDAIBpJOyActddd+ns2bO6dOmSfvOb36isrEzNzc1asGCBNmzYYNfLyclRfn6+MjMz9e6772rdunVXPadlWXI4HFc9XldXp2eeeSbcpgIAgGkq7Es88fHxuvPOO5Wfn6+6ujotWrRIL7300ph109PTlZmZqfPnz0uSPB6PhoaG1NvbG1Kvp6dHbrf7qu9ZXV0tv99vl66urnCbDQAAppGbfg6KZVkhl1++64svvlBXV5fS09MlSXl5eYqLi1NTU5Ndp7u7W+fOnVNhYeFV38PpdNq3Nl8pAAAgeoV1iWfnzp0qLS1VRkaG+vr61NDQoJMnT+ro0aPq7+9XTU2NHn74YaWnp+uTTz7Rzp07lZaWpoceekiS5HK5tHnzZm3fvl2pqalKSUlRVVWVcnNz7bt6AAAAwgoon3/+uR555BF1d3fL5XJp4cKFOnr0qFauXKnBwUG1t7frwIEDunTpktLT07Vs2TIdPnxYSUlJ9jn27Nmj2NhYrV+/XoODgyouLtb+/fsVExMz4Z0DAADTk8OyLCvSjQhXIBCQy+WS3+83+3JPjSuMuv7JawcAAAYI5/ub3+IBAADGIaAAAADjEFAAAIBxCCgAAMA4YT9JFpNk5IJaFs0CAGYwZlAAAIBxCCgAAMA4BBQAAGAcAgoAADAOAQUAABiHgAIAAIxDQAEAAMYhoAAAAOPwoDZT8eA2AMAMxgwKAAAwDgEFAAAYh4ACAACMQ0ABAADGYZHsRBu5uBUAAISNGRQAAGAcAgoAADAOAQUAABiHgAIAAIxDQAEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxgkroOzdu1cLFy5UcnKykpOTVVBQoPfee88+blmWampq5PV6lZCQoKKiInV0dIScIxgMqry8XGlpaUpMTNTatWt14cKFiekNAACICmEFlHnz5um5557TmTNndObMGS1fvlwPPPCAHUJ27dql3bt3q76+Xq2trfJ4PFq5cqX6+vrsc1RUVKixsVENDQ06deqU+vv7tXr1ag0PD09szwAAwLTlsCzLupkTpKSk6Je//KV+8pOfyOv1qqKiQk888YSkb2dL3G63nn/+eT322GPy+/2aM2eODh48qA0bNkiSLl68qIyMDB05ckSrVq26ofcMBAJyuVzy+/1KTk6+meZPvBrXJJ3XPznnBQBgioTz/T3uNSjDw8NqaGjQwMCACgoK1NnZKZ/Pp5KSEruO0+nU0qVL1dLSIklqa2vT5cuXQ+p4vV7l5OTYdcYSDAYVCARCCgAAiF5hB5T29nbdeuutcjqdevzxx9XY2KgFCxbI5/NJktxud0h9t9ttH/P5fIqPj9fs2bOvWmcsdXV1crlcdsnIyAi32QAAYBoJO6DcddddOnv2rE6fPq2f/exnKisr00cffWQfdzgcIfUtyxq1b6Tr1amurpbf77dLV1dXuM0GAADTSNgBJT4+Xnfeeafy8/NVV1enRYsW6aWXXpLH45GkUTMhPT099qyKx+PR0NCQent7r1pnLE6n075z6EoBAADR66afg2JZloLBoLKysuTxeNTU1GQfGxoaUnNzswoLCyVJeXl5iouLC6nT3d2tc+fO2XUAAABiw6m8c+dOlZaWKiMjQ319fWpoaNDJkyd19OhRORwOVVRUqLa2VtnZ2crOzlZtba1mzZqljRs3SpJcLpc2b96s7du3KzU1VSkpKaqqqlJubq5WrFgxKR0EAADTT1gB5fPPP9cjjzyi7u5uuVwuLVy4UEePHtXKlSslSTt27NDg4KC2bNmi3t5eLV68WMeOHVNSUpJ9jj179ig2Nlbr16/X4OCgiouLtX//fsXExExszwAAwLR1089BiQSegwIAwPQzJc9BAQAAmCwEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACMQ0ABAADGIaAAAADjEFAAAIBxCCgAAMA4BBQAAGAcAgoAADAOAQUAABiHgAIAAIxDQAEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAccIKKHV1dbrnnnuUlJSkuXPn6sEHH9THH38cUmfTpk1yOBwhZcmSJSF1gsGgysvLlZaWpsTERK1du1YXLly4+d4AAICoEFZAaW5u1tatW3X69Gk1NTXp66+/VklJiQYGBkLq3X///eru7rbLkSNHQo5XVFSosbFRDQ0NOnXqlPr7+7V69WoNDw/ffI8AAMC0FxtO5aNHj4Zs79u3T3PnzlVbW5vuu+8+e7/T6ZTH4xnzHH6/X6+99poOHjyoFStWSJIOHTqkjIwMvf/++1q1alW4fQAAAFHmptag+P1+SVJKSkrI/pMnT2ru3LmaP3++Hn30UfX09NjH2tradPnyZZWUlNj7vF6vcnJy1NLSMub7BINBBQKBkDLj1Lj+vwAAEOXGHVAsy1JlZaXuvfde5eTk2PtLS0v1xhtv6Pjx43rhhRfU2tqq5cuXKxgMSpJ8Pp/i4+M1e/bskPO53W75fL4x36uurk4ul8suGRkZ4202AACYBsK6xPNd27Zt04cffqhTp06F7N+wYYP9d05OjvLz85WZmal3331X69atu+r5LMuSw+EY81h1dbUqKyvt7UAgQEgBACCKjWsGpby8XO+8845OnDihefPmXbNuenq6MjMzdf78eUmSx+PR0NCQent7Q+r19PTI7XaPeQ6n06nk5OSQAgAAoldYAcWyLG3btk1vvfWWjh8/rqysrOu+5osvvlBXV5fS09MlSXl5eYqLi1NTU5Ndp7u7W+fOnVNhYWGYzQcAANEorEs8W7du1Ztvvqnf/va3SkpKsteMuFwuJSQkqL+/XzU1NXr44YeVnp6uTz75RDt37lRaWpoeeughu+7mzZu1fft2paamKiUlRVVVVcrNzbXv6gEAADNbWAFl7969kqSioqKQ/fv27dOmTZsUExOj9vZ2HThwQJcuXVJ6erqWLVumw4cPKykpya6/Z88excbGav369RocHFRxcbH279+vmJiYm+8RAACY9hyWZVmRbkS4AoGAXC6X/H6/eetRpuI24Br/5L8HAAATLJzvb36LBwAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACMQ0ABAADGIaAAAADjEFAAAIBxCCgAAMA4BBQAAGAcAgoAADAOAQUAABiHgAIAAIxDQAEAAMaJjXQDpr0aV6RbAABA1GEGBQAAGIeAAgAAjENAAQAAxmENynQ0ct1LjT8y7QAAYJIwgwIAAIxDQAEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBxuM44G3HYMAIgyzKAAAADjhBVQ6urqdM899ygpKUlz587Vgw8+qI8//jikjmVZqqmpkdfrVUJCgoqKitTR0RFSJxgMqry8XGlpaUpMTNTatWt14cKFm+8NAACICmEFlObmZm3dulWnT59WU1OTvv76a5WUlGhgYMCus2vXLu3evVv19fVqbW2Vx+PRypUr1dfXZ9epqKhQY2OjGhoadOrUKfX392v16tUaHh6euJ4BAIBpy2FZljXeF//P//yP5s6dq+bmZt13332yLEter1cVFRV64oknJH07W+J2u/X888/rsccek9/v15w5c3Tw4EFt2LBBknTx4kVlZGToyJEjWrVq1XXfNxAIyOVyye/3Kzk5ebzNnxgj13+YgDUoAAADhfP9fVNrUPz+b78IU1JSJEmdnZ3y+XwqKSmx6zidTi1dulQtLS2SpLa2Nl2+fDmkjtfrVU5Ojl1npGAwqEAgEFIAAED0GndAsSxLlZWVuvfee5WTkyNJ8vl8kiS32x1S1+1228d8Pp/i4+M1e/bsq9YZqa6uTi6Xyy4ZGRnjbTYAAJgGxh1Qtm3bpg8//FD//u//PuqYw+EI2bYsa9S+ka5Vp7q6Wn6/3y5dXV3jbTYAAJgGxhVQysvL9c477+jEiROaN2+evd/j8UjSqJmQnp4ee1bF4/FoaGhIvb29V60zktPpVHJyckgBAADRK6yAYlmWtm3bprfeekvHjx9XVlZWyPGsrCx5PB41NTXZ+4aGhtTc3KzCwkJJUl5enuLi4kLqdHd369y5c3YdAAAws4X1JNmtW7fqzTff1G9/+1slJSXZMyUul0sJCQlyOByqqKhQbW2tsrOzlZ2drdraWs2aNUsbN260627evFnbt29XamqqUlJSVFVVpdzcXK1YsWLiewgAAKadsALK3r17JUlFRUUh+/ft26dNmzZJknbs2KHBwUFt2bJFvb29Wrx4sY4dO6akpCS7/p49exQbG6v169drcHBQxcXF2r9/v2JiYm6uNwAAICrc1HNQIoXnoFwHz0EBABhoyp6DAgAAMBkIKAAAwDgEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACMQ0ABAADGIaAAAADjEFAAAIBxCCgAAMA4BBQAAGAcAgoAADBObKQbgElQ4/rO3/7ItQMAgHFiBgUAABiHgAIAAIxDQAEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxgk7oHzwwQdas2aNvF6vHA6H3n777ZDjmzZtksPhCClLliwJqRMMBlVeXq60tDQlJiZq7dq1unDhws31BAAARI2wA8rAwIAWLVqk+vr6q9a5//771d3dbZcjR46EHK+oqFBjY6MaGhp06tQp9ff3a/Xq1RoeHg6/BwAAIOqE/WvGpaWlKi0tvWYdp9Mpj8cz5jG/36/XXntNBw8e1IoVKyRJhw4dUkZGht5//32tWrUq3CYBAIAoMylrUE6ePKm5c+dq/vz5evTRR9XT02Mfa2tr0+XLl1VSUmLv83q9ysnJUUtLy5jnCwaDCgQCIQUAAESvCQ8opaWleuONN3T8+HG98MILam1t1fLlyxUMBiVJPp9P8fHxmj17dsjr3G63fD7fmOesq6uTy+WyS0ZGxkQ3GwAAGCTsSzzXs2HDBvvvnJwc5efnKzMzU++++67WrVt31ddZliWHwzHmserqalVWVtrbgUCAkAIAQBSb9NuM09PTlZmZqfPnz0uSPB6PhoaG1NvbG1Kvp6dHbrd7zHM4nU4lJyeHFAAAEL0mPaB88cUX6urqUnp6uiQpLy9PcXFxampqsut0d3fr3LlzKiwsnOzmAACAaSDsSzz9/f364x//aG93dnbq7NmzSklJUUpKimpqavTwww8rPT1dn3zyiXbu3Km0tDQ99NBDkiSXy6XNmzdr+/btSk1NVUpKiqqqqpSbm2vf1QMAAGa2sAPKmTNntGzZMnv7ytqQsrIy7d27V+3t7Tpw4IAuXbqk9PR0LVu2TIcPH1ZSUpL9mj179ig2Nlbr16/X4OCgiouLtX//fsXExExAlwAAwHTnsCzLinQjwhUIBORyueT3+yO/HqXGFdn3v54af6RbAACApPC+v/ktHgAAYBwCCgAAMA4BBQAAGIeAAgAAjDPhT5KFYUYu4mXRLABgGmAGBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACMQ0ABAADGIaAAAADj8GOBMw0/HggAmAaYQQEAAMYhoAAAAOMQUAAAgHEIKAAAwDgEFAAAYBwCCgAAMA63GYdr5G26AABgwjGDAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgnLADygcffKA1a9bI6/XK4XDo7bffDjluWZZqamrk9XqVkJCgoqIidXR0hNQJBoMqLy9XWlqaEhMTtXbtWl24cOHmegIAAKJG2AFlYGBAixYtUn19/ZjHd+3apd27d6u+vl6tra3yeDxauXKl+vr67DoVFRVqbGxUQ0ODTp06pf7+fq1evVrDw8Pj7wluXo0rtAAAECEOy7Kscb/Y4VBjY6MefPBBSd/Onni9XlVUVOiJJ56Q9O1sidvt1vPPP6/HHntMfr9fc+bM0cGDB7VhwwZJ0sWLF5WRkaEjR45o1apV133fQCAgl8slv9+v5OTk8TZ/fGbSF3eNP9ItAABEkXC+vyd0DUpnZ6d8Pp9KSkrsfU6nU0uXLlVLS4skqa2tTZcvXw6p4/V6lZOTY9cBAAAz24Q+6t7n80mS3G53yH63261PP/3UrhMfH6/Zs2ePqnPl9SMFg0EFg0F7OxAITGSzAQCAYSblLh6HwxGybVnWqH0jXatOXV2dXC6XXTIyMiasrQAAwDwTGlA8Ho8kjZoJ6enpsWdVPB6PhoaG1Nvbe9U6I1VXV8vv99ulq6trIpsNAAAMM6EBJSsrSx6PR01NTfa+oaEhNTc3q7CwUJKUl5enuLi4kDrd3d06d+6cXWckp9Op5OTkkAIAAKJX2GtQ+vv79cc//tHe7uzs1NmzZ5WSkqLbb79dFRUVqq2tVXZ2trKzs1VbW6tZs2Zp48aNkiSXy6XNmzdr+/btSk1NVUpKiqqqqpSbm6sVK1ZMXM8AAMC0FXZAOXPmjJYtW2ZvV1ZWSpLKysq0f/9+7dixQ4ODg9qyZYt6e3u1ePFiHTt2TElJSfZr9uzZo9jYWK1fv16Dg4MqLi7W/v37FRMTMwFdAgAA091NPQclUngOyhThOSgAgAkUseegAAAATAQCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA40zojwUiyoy8pZrbjgEAU4QZFAAAYBwCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACMw48F4sZ998cD+eFAAMAkYgYFAAAYh4ACAACMQ0ABAADGIaAAAADjEFAAAIBxuIsH4/PdO3ok7uoBAEwoZlAAAIBxCCgAAMA4BBQAAGAcAgoAADAOAQUAABiHgAIAAIwz4QGlpqZGDocjpHg8Hvu4ZVmqqamR1+tVQkKCioqK1NHRMdHNAAAA09ikzKDcfffd6u7utkt7e7t9bNeuXdq9e7fq6+vV2toqj8ejlStXqq+vbzKaAgAApqFJCSixsbHyeDx2mTNnjqRvZ09efPFFPfnkk1q3bp1ycnL0+uuv68svv9Sbb745GU0BAADT0KQElPPnz8vr9SorK0s/+tGP9Kc//UmS1NnZKZ/Pp5KSEruu0+nU0qVL1dLSctXzBYNBBQKBkAIAAKLXhD/qfvHixTpw4IDmz5+vzz//XM8++6wKCwvV0dEhn88nSXK73SGvcbvd+vTTT696zrq6Oj3zzDMT3VRMJB59DwCYQBMeUEpLS+2/c3NzVVBQoDvuuEOvv/66lixZIklyOBwhr7Esa9S+76qurlZlZaW9HQgElJGRMcEtx6QhvAAAwjTptxknJiYqNzdX58+ft+/muTKTckVPT8+oWZXvcjqdSk5ODikAACB6TXpACQaD+v3vf6/09HRlZWXJ4/GoqanJPj40NKTm5mYVFhZOdlMAAMA0MeGXeKqqqrRmzRrdfvvt6unp0bPPPqtAIKCysjI5HA5VVFSotrZW2dnZys7OVm1trWbNmqWNGzdOdFMAAMA0NeEB5cKFC/rHf/xH/eUvf9GcOXO0ZMkSnT59WpmZmZKkHTt2aHBwUFu2bFFvb68WL16sY8eOKSkpaaKbAgAApimHZVlWpBsRrkAgIJfLJb/fP/XrUUYu+MTYvrsQlkWyAACF9/3Nb/EAAADjEFAAAIBxCCgAAMA4BBQAAGCcCb+LB5B07cXELJoFAFwHMygAAMA4zKDcCG4tBgBgSjGDAgAAjENAAQAAxiGgAAAA47AGBWbhDh8AgJhBAQAABmIGBZHHM1MAACMwgwIAAIxDQAEAAMbhEg+iB5eDACBqMIMCAACMwwwKpi9+ggAAohYzKAAAwDgEFAAAYBwu8WBmYkEtABiNGRQAAGAcZlAQvVhECwDTFgEFGInLPwAQcQSUsfB/3uZibABgRmANCgAAMA4zKIA0cTMzXB4CgAlBQAFuBpecAGBScIkHAAAYhxkUIFzhzJp8ty6XewDghhFQgEhhvQoAXBUBBbge1pkAwJSLaEB5+eWX9ctf/lLd3d26++679eKLL+oHP/hBJJsETJ5IBZ2JuszEjA+AKRSxgHL48GFVVFTo5Zdf1t///d/rlVdeUWlpqT766CPdfvvtkWoWYIaw1rmEERQIGeZibIAQEQsou3fv1ubNm/XTn/5UkvTiiy/qP//zP7V3717V1dVFqlnA9DfeRbyjjk3gFySLhQGEKSIBZWhoSG1tbfr5z38esr+kpEQtLS2j6geDQQWDQXvb7//2H7hAIDA5DQxak3Ne4Fqqk6f2dTd73u8er74Qeqxu3o2fN6zXXrj6sZFGnudmXnujrteXa7Vh5L874/3ve602hdOem/nvdzO++74389/zRt/jZs4z3c8bgfe58r1tWTfwPWtFwJ///GdLkvXf//3fIft/8YtfWPPnzx9V//XP53cAAAffSURBVOmnn7YkUSgUCoVCiYLS1dV13awQ0UWyDocjZNuyrFH7JKm6ulqVlZX29jfffKP//d//VWpq6pj1xxIIBJSRkaGuri4lJ0/S/3Eaaib3XZrZ/Z/JfZdmdv9nct+lmd1/k/tuWZb6+vrk9XqvWzciASUtLU0xMTHy+Xwh+3t6euR2u0fVdzqdcjqdIftuu+22cb13cnKycQM2VWZy36WZ3f+Z3HdpZvd/Jvddmtn9N7XvLteNrZOLyKPu4+PjlZeXp6amppD9TU1NKiwsjESTAACAQSJ2iaeyslKPPPKI8vPzVVBQoFdffVWfffaZHn/88Ug1CQAAGCKmpqamJhJvnJOTo9TUVNXW1uqf//mfNTg4qIMHD2rRokWT9p4xMTEqKipSbOzMe4DuTO67NLP7P5P7Ls3s/s/kvkszu//R0HeHZd3IvT4AAABTJyJrUAAAAK6FgAIAAIxDQAEAAMYhoAAAAOPMiIDy8ssvKysrS7fccovy8vL0X//1X5Fu0pSoqamRw+EIKR6PJ9LNmhQffPCB1qxZI6/XK4fDobfffjvkuGVZqqmpkdfrVUJCgoqKitTR0RGh1k686/V/06ZNoz4LS5YsiVBrJ1ZdXZ3uueceJSUlae7cuXrwwQf18ccfh9SJ1vG/kb5H89jv3btXCxcutB9IVlBQoPfee88+Hq3jLl2/79Ew7lEfUA4fPqyKigo9+eST+t3vfqcf/OAHKi0t1WeffRbppk2Ju+++W93d3XZpb2+PdJMmxcDAgBYtWqT6+voxj+/atUu7d+9WfX29Wltb5fF4tHLlSvX19U1xSyfH9fovSffff3/IZ+HIkSNT2MLJ09zcrK1bt+r06dNqamrS119/rZKSEg0MDNh1onX8b6TvUvSO/bx58/Tcc8/pzJkzOnPmjJYvX64HHnjADiHROu7S9fsuRcG43/xP/5nt7/7u76zHH388ZN/f/u3fWj//+c8j1KKp8/TTT1uLFi2KdDOmnCSrsbHR3v7mm28sj8djPffcc/a+r776ynK5XNa//Mu/RKKJk2pk/y3LssrKyqwHHnggQi2aWj09PZYkq7m52bKsmTX+I/tuWTNr7C3LsmbPnm3927/924wa9yuu9N2yomPco3oGZWhoSG1tbSopKQnZX1JSopaWlgi1amqdP39eXq9XWVlZ+tGPfqQ//elPkW7SlOvs7JTP5wv5HDidTi1dunTGfA4k6eTJk5o7d67mz5+vRx99VD09PZFu0qTw+/2SpJSUFEkza/xH9v2KmTD2w8PDamho0MDAgAoKCmbUuI/s+xXTfdyn7yPmbsBf/vIXDQ8Pj/oBQrfbPeqHCqPR4sWLdeDAAc2fP1+ff/65nn32WRUWFqqjo0OpqamRbt6UuTLWY30OPv3000g0acqVlpbqhz/8oTIzM9XZ2amnnnpKy5cvV1tb26gf4pzOLMtSZWWl7r33XuXk5EiaOeM/Vt+l6B/79vZ2FRQU6KuvvtKtt96qxsZGLViwwA4h0TzuV+u7FB3jHtUB5QqHwxGybVnWqH3RqLS01P47NzdXBQUFuuOOO/T666+rsrIygi2LjJn6OZCkDRs22H/n5OQoPz9fmZmZevfdd7Vu3boItmxibdu2TR9++KFOnTo16li0j//V+h7tY3/XXXfp7NmzunTpkn7zm9+orKxMzc3N9vFoHver9X3BggVRMe5RfYknLS1NMTExo2ZLenp6RqXqmSAxMVG5ubk6f/58pJsypa7cucTn4P+lp6crMzMzqj4L5eXleuedd3TixAnNmzfP3j8Txv9qfR9LtI19fHy87rzzTuXn56uurk6LFi3SSy+9NCPG/Wp9H8t0HPeoDijx8fHKy8tTU1NTyP6mpiYVFhZGqFWREwwG9fvf/17p6emRbsqUysrKksfjCfkcDA0Nqbm5eUZ+DiTpiy++UFdXV1R8FizL0rZt2/TWW2/p+PHjysrKCjkezeN/vb6PJZrGfiyWZSkYDEb1uF/Nlb6PZTqOe8R+zXiqJCcn66mnntJf//Vf65ZbblFtba1OnDihffv26bbbbot08yZVVVWVnE6nLMvSH/7wB23btk1/+MMf9Morr0Rd3/v7+/XRRx/J5/PplVde0eLFi5WQkKChoSHddtttGh4eVl1dne666y4NDw9r+/bt+vOf/6xXX3112lyPvZZr9T8mJkY7d+5UUlKShoeHdfbsWf30pz/V5cuXVV9fP+37v3XrVr3xxhv6j//4D3m9XvX396u/v18xMTGKi4uTw+GI2vG/Xt/7+/ujeux37typ+Ph4WZalrq4u/epXv9KhQ4e0a9cu3XHHHVE77tK1++52u6Nj3CNy79AU+/Wvf21lZmZa8fHx1ve///2QW/Ci2YYNG6z09HQrLi7O8nq91rp166yOjo5IN2tSnDhxwpI0qpSVlVmW9e2tpk8//bTl8Xgsp9Np3XfffVZ7e3tkGz2BrtX/L7/80iopKbHmzJljxcXFWbfffrtVVlZmffbZZ5Fu9oQYq9+SrH379tl1onX8r9f3aB/7n/zkJ/a/7XPmzLGKi4utY8eO2cejddwt69p9j5Zxd1iWZU1lIAIAALieqF6DAgAApicCCgAAMA4BBQAAGIeAAgAAjENAAQAAxiGgAAAA4xBQAACAcQgoAADAOAQUAABgHAIKAAAwDgEFAAAYh4ACAACM838IMnlpu/Vo1gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["atr_stats_summary('A股')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CS2301.DC price = 0\n", "EB2212.DC price = 0\n", "BU2301.SC price = 0\n", "SS2212.SC price = 0\n", "             Mean ATR  ATR Percent    ATR  default ATR\n", "AP2301.ZC  129.000000     1.523922  140.0        129.0\n", "CF2301.ZC  200.000000     1.504891  269.0        200.0\n", "CY2301.ZC  241.000000     1.191300  332.0        241.0\n", "FG2301.ZC   34.000000     2.374302   28.0         34.0\n", "MA2301.ZC   48.000000     1.813374   55.0         48.0\n", "OI2301.ZC  138.000000     1.254545  222.0        138.0\n", "PK2301.ZC  168.760076     1.569277  153.0        135.0\n", "RM2301.ZC   56.000000     1.856764   61.0         56.0\n", "SA2301.ZC   34.000000     1.421999   48.0         34.0\n", "SF2301.ZC  185.000000     2.313078  185.0        185.0\n", "SM2301.ZC  170.000000     2.426492  114.0        170.0\n", "SR2301.ZC   64.000000     1.146953   44.0         64.0\n", "TA2301.ZC   69.000000     1.298457  132.0         69.0\n", "UR2301.ZC   31.000000     1.378390   48.0         31.0\n", "A2301.DC   102.000000     1.825340  102.0        102.0\n", "C2301.DC    16.000000     0.555556   25.0         16.0\n", "CS2301.DC   19.000000     0.000000   19.0         19.0\n", "EB2212.DC  218.000000     0.000000  218.0        218.0\n", "EG2301.DC   89.000000     2.215032   83.0         89.0\n", "I2301.DC    32.000000     4.709345   18.0         32.0\n", "JM2301.DC   50.000000     2.439024   57.0         50.0\n", "L2301.DC   111.000000     1.431889  112.0        111.0\n", "M2301.DC    66.000000     1.628825   64.0         66.0\n", "P2301.DC   147.000000     1.832461  225.0        147.0\n", "PP2301.DC  114.000000     1.504355  106.0        114.0\n", "V2301.DC   125.000000     2.121881  112.0        125.0\n", "Y2301.DC   137.000000     1.443929  190.0        137.0\n", "AG2212.SC  139.000000     3.060326   74.0        139.0\n", "BU2301.SC   62.000000     0.000000   62.0         62.0\n", "HC2301.SC   84.000000     2.297593   64.0         84.0\n", "RB2301.SC   78.000000     2.153506   65.0         78.0\n", "RU2301.SC  355.000000     2.885006  182.0        355.0\n", "SP2301.SC  122.000000     1.782063  106.0        122.0\n", "SS2212.SC  268.000000     0.000000  268.0        268.0\n"]}], "source": ["def get_rangebar_atr(codelist, barsize):\n", "    atrs = {}\n", "    for symbol in codelist:\n", "        atrs[symbol] = ds.get_rangebar_atr(symbol, barsize)\n", "    return atrs\n", "\n", "def get_default_rangebar_atr(codelist, barsize, islast=True):\n", "    atrs = {}\n", "    for symbol in codelist:\n", "        atrs[symbol] = ds.get_default_rangebar_atr(symbol, barsize, islast)\n", "    return atrs\n", "\n", "def get_last_price_percent(codelist, barsize):\n", "    prices = {}\n", "    for symbol in codelist:\n", "        price = ds.price(symbol)\n", "        if price > 0:\n", "            prices[symbol] = ds.get_default_rangebar_atr(symbol, barsize, False)/ price * 100\n", "        else:\n", "            prices[symbol] = 0.0\n", "            print(\"%s price = 0\" % symbol)\n", "    return prices\n", "\n", "barsize = BarSize.day\n", "zlqh = ds.get_block_data(\"主选期货\")\n", "mean_atr = get_default_rangebar_atr(zlqh, barsize, False)\n", "atr_percent = get_last_price_percent(zlqh, barsize)\n", "atr_range = get_rangebar_atr(zlqh, barsize)\n", "atr_dflt_range = get_default_rangebar_atr(zlqh, barsize)\n", "\n", "temp_table = {'Mean ATR': mean_atr, 'ATR Percent': atr_percent, 'ATR': atr_range, 'default ATR': atr_dflt_range}\n", "df = pd.DataFrame(temp_table)\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}