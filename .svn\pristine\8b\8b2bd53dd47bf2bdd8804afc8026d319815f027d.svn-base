2022-01-18 21:00:20: -- 分析开始 User Command
2022-01-18 21:00:20: 变更：12， 冲突：0， 复制时间：0， 复制状态：0， 错误： 0, All: 62
2022-01-18 21:00:20: Left to Right: Copy File: 12 
2022-01-18 21:00:20: -- 分析已结束。历时 00:00:00, 速度： Many 文件/秒
2022-01-18 21:00:20: 
2022-01-18 21:00:24: == 同步开始由 User Command
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_long.json' -> 'D:/RoboQuant/model/MLP_5R_long.json' (4,474)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_long.model' -> 'D:/RoboQuant/model/MLP_5R_long.model' (137,150)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_short.json' -> 'D:/RoboQuant/model/MLP_5R_short.json' (4,503)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_short.model' -> 'D:/RoboQuant/model/MLP_5R_short.model' (137,195)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_long.json' -> 'D:/RoboQuant/model/MLP_7R_long.json' (4,479)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_long.model' -> 'D:/RoboQuant/model/MLP_7R_long.model' (137,150)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_short.json' -> 'D:/RoboQuant/model/MLP_7R_short.json' (4,481)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_short.model' -> 'D:/RoboQuant/model/MLP_7R_short.model' (137,195)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_long.json' -> 'D:/RoboQuant/model/MLP_MIX_long.json' (4,481)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_long.model' -> 'D:/RoboQuant/model/MLP_MIX_long.model' (137,195)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_short.json' -> 'D:/RoboQuant/model/MLP_MIX_short.json' (4,487)
2022-01-18 21:00:24: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_short.model' -> 'D:/RoboQuant/model/MLP_MIX_short.model' (137,240)
2022-01-18 21:00:24: == 同步完成. 历时: 00:00:00, 速度: 0 字节/s, 完成: 12, 错误: 0
2022-01-18 21:00:24: 
