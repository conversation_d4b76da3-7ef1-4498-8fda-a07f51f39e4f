{"cells": [{"cell_type": "code", "execution_count": 1, "source": ["import numpy as np\r\n", "import math\r\n", "\r\n", "# Create random input and output data\r\n", "x = np.linspace(-math.pi, math.pi, 2000)\r\n", "y = np.sin(x)\r\n", "\r\n", "# Randomly initialize weights\r\n", "a = np.random.randn()\r\n", "b = np.random.randn()\r\n", "c = np.random.randn()\r\n", "d = np.random.randn()\r\n", "\r\n", "learning_rate = 1e-6\r\n", "for t in range(2000):\r\n", "    # Forward pass: compute predicted y\r\n", "    # y = a + b x + c x^2 + d x^3\r\n", "    y_pred = a + b * x + c * x ** 2 + d * x ** 3\r\n", "\r\n", "    # Compute and print loss\r\n", "    loss = np.square(y_pred - y).sum()\r\n", "    if t % 100 == 99:\r\n", "        print(t, loss)\r\n", "\r\n", "    # Backprop to compute gradients of a, b, c, d with respect to loss\r\n", "    grad_y_pred = 2.0 * (y_pred - y)\r\n", "    grad_a = grad_y_pred.sum()\r\n", "    grad_b = (grad_y_pred * x).sum()\r\n", "    grad_c = (grad_y_pred * x ** 2).sum()\r\n", "    grad_d = (grad_y_pred * x ** 3).sum()\r\n", "\r\n", "    # Update weights\r\n", "    a -= learning_rate * grad_a\r\n", "    b -= learning_rate * grad_b\r\n", "    c -= learning_rate * grad_c\r\n", "    d -= learning_rate * grad_d\r\n", "\r\n", "print(f'Result: y = {a} + {b} x + {c} x^2 + {d} x^3')"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["99 3338.2445037866073\n", "199 2225.6381079794837\n", "299 1485.4731860126194\n", "399 992.8866711506726\n", "499 664.9330020481934\n", "599 446.4949879862836\n", "699 300.9357193112604\n", "799 203.8941142143419\n", "899 139.16592768824862\n", "999 95.96853666016455\n", "1099 67.12411855809438\n", "1199 47.85250331448381\n", "1299 34.96886021602115\n", "1399 26.35027650415804\n", "1499 20.58098607729074\n", "1599 16.716328839824758\n", "1699 14.12564405381743\n", "1799 12.387658391364187\n", "1899 11.220798113360962\n", "1999 10.436744852305855\n", "Result: y = -0.022706447808146627 + 0.8236413214852779 x + 0.003917240083827723 x^2 + -0.08862227032780369 x^3\n"]}], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": [], "outputs": [], "metadata": {}}], "metadata": {"orig_nbformat": 4, "language_info": {"name": "python", "version": "3.7.6", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kernelspec": {"name": "python3", "display_name": "Python 3.7.6 64-bit ('base': conda)"}, "interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}}, "nbformat": 4, "nbformat_minor": 2}