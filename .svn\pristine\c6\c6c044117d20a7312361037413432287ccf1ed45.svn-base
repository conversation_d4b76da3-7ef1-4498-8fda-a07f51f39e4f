# %%
import datetime
import pytz
import struct
import pandas as pd
import sys
import numpy as np

sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

# %%
class TickData():
    def __init__(self, dbfile="d:/RoboQuant/store/tickdata.db") -> None:
        self._dbfile=dbfile
        self._db=None
        self._keys=[]
        self._tz=pytz.timezone('Asia/Shanghai')
        pass

    def open_db(self, mode):
        if self._db:
            raise "already open a db."
        self._db=create_db("leveldb", self._dbfile, mode)

    def close_db(self):
        if not self._db:
            raise "not db open."
        self._db.close()
        del self._db
        self._db=None

    def load_all_keys(self):
        if not self._db:
            raise "first open a db."
        self._keys.clear()
        cursor = self._db.new_cursor()
        while cursor.valid():
            self._keys.append(str(cursor.key()))
            cursor.next()
        del cursor

    def read_keys(self, key):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        while cursor.valid():
            if str(cursor.key()) == key:
                for i in range(len(cursor.value())//48):
                    tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                    print(datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)
                break
            cursor.next()
        del cursor

    def read_index(self, index):
        if not self._db:
            raise "first open a db."
        if index > len(self._keys):
            raise f"index < {len(self._keys)}"

        cursor = self._db.new_cursor()
        while cursor.valid():
            if cursor.key() == self._keys[index]:
                s=str(cursor.key(), encoding='utf-8')
                pos0=s.find(':')
                pos1=s.find(':', pos0+1)
                lb=s[pos0+1:pos1]
                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                data=[lb, datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2]
                break
            cursor.next()
        del cursor
        return data

    def write(self, key, value):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.put(key, value)
        del transaction
        
    def delete(self, key):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.delete(key)
        del transaction
        
    def query(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        while cursor.valid():
            print(cursor.key())
            # print(cursor.key(), cursor.value())
            cursor.next()
        del cursor
        
    def read_all(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        data=[]
        while cursor.valid():
            for i in range(len(cursor.value())//48):
                s=str(cursor.key(), encoding='utf-8')
                pos0=s.find(':')
                pos1=s.find(':', pos0+1)
                lb=s[pos0+1:pos1]
                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                data.append([lb, datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])
            cursor.next()
        del cursor
        return pd.DataFrame(data, columns=['label','datetime','price','volume','bid_price1','bid_price2','bid_volume1','bid_volume2','ask_price1','ask_price2','ask_volume1','ask_volume2'])


# %%
td=TickData()
td.open_db(Mode.read)
df=td.read_all()
td.close_db()

# %%
df.to_csv("../data/tick.csv")

# %%
print(df)

# %%
td.open_db(Mode.read)
td.load_all_keys()
td.close_db()
print(len(td._keys))

# %%
print(td._keys)

# %%



