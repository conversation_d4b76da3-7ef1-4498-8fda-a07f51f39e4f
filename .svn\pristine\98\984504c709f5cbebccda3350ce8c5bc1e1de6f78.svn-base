{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from typing import Text, Union\n", "import copy\n", "from qlib.utils import get_or_create_path\n", "from qlib.log import get_module_logger, set_log_with_config\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "# from torch.nn.utils import weight_norm\n", "from torch.optim import lr_scheduler\n", "\n", "from qlib.model.base import Model\n", "from qlib.data.dataset import DatasetH\n", "from qlib.data.dataset.handler import DataHandlerLP\n", "\n", "import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config, flatten_dict\n", "from pyqlab.data.dataset.handler import DataHandlerAF\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[9908:MainThread](2022-10-20 09:35:38,253) INFO - qlib.Initialization - [config.py:413] - default_conf: client.\n", "[9908:MainThread](2022-10-20 09:35:38,689) INFO - qlib.Initialization - [__init__.py:74] - qlib successfully initialized based on client settings.\n", "[9908:MainThread](2022-10-20 09:35:38,690) INFO - qlib.Initialization - [__init__.py:76] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["\n", "provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def plt_show(df, title=\"\"):\n", "    # wdt = widgets.Output()\n", "    # wdt.clear_output(wait=False)\n", "    # with wdt:\n", "    ylim = [df.min().min(), df.quantile(0.95).max()]\n", "    ylim[0] -= (ylim[1] - ylim[0]) * 0.05\n", "    df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)\n", "    plt.show()\n", "\n", "def display_result(evals_result):\n", "    for key, val in evals_result.items():\n", "        if not isinstance(val, dict):\n", "            plt_show(pd.DataFrame(evals_result), key)\n", "            break\n", "        else:\n", "            plt_show(pd.DataFrame(val), key)\n", "            "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    # \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    # \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    # \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    # \"SQUEEZE_ZERO_BARS\", \n", "    # \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    # \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    # \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    # \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    # \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    # \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_HLR\", \"FAST_QH_HLR_ZSCORE\", \"FAST_QH_HLR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]\n", "\n", "data_handler_config = {\n", "    \"start_time\": \"\",\n", "    \"end_time\": \"\",\n", "    \"instruments\": ['06220831232331000', '01220901173143000'],\n", "    \"data_loader\": {\n", "        \"class\": \"AFDataLoader\",\n", "        \"module_path\": \"pyqlab.data.dataset.loader\",\n", "        \"kwargs\": {\n", "            \"direct\": \"L\",\n", "            \"model_name\": \"MLP_HLR\",\n", "            \"model_name_suff\": \"\",\n", "            \"model_path\": \"e:/lab/RoboQuant/pylab/model\",\n", "            \"data_path\": \"e:/lab/RoboQuant/pylab/data\",\n", "            \"sel_lf_names\": SEL_LONG_FACTOR_NAMES,\n", "            \"sel_sf_names\": SEL_SHORT_FACTOR_NAMES,\n", "            \"sel_ct_names\": SEL_CONTEXT_FACTOR_NAMES,\n", "        }\n", "    },\n", "}\n", "\n", "dataset_config = {\n", "    \"class\": \"AFDatasetH\",\n", "    \"module_path\": \"pyqlab.data.dataset\",\n", "    \"kwargs\": {\n", "        \"handler\": {\n", "            \"class\": \"DataHandlerAF\",\n", "            \"module_path\": \"pyqlab.data.dataset.handler\",\n", "            \"kwargs\": data_handler_config,\n", "        },\n", "        \"segments\": [\"train\", \"valid\"],\n", "        \"col_set\": [\"feature\", \"label\", \"encoded\"],\n", "    },\n", "}\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class MLPTorch(Model):\n", "    \"\"\"MLP Model\n", "\n", "    Parameters\n", "    ----------\n", "    d_feat : int\n", "        input dimension for each time step\n", "    n_chans: int\n", "        number of channels\n", "    metric: str\n", "        the evaluate metric used in early stop\n", "    optimizer : str\n", "        optimizer name\n", "    GPU : str\n", "        the GPU ID(s) used for training\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        num_code=50,\n", "        num_input=95, #87\n", "        layers=(96, 96, 64),\n", "        dropout=0.5,\n", "        n_epochs=200,\n", "        lr=0.0001,\n", "        metric=\"\",\n", "        batch_size=64,\n", "        early_stop=30,\n", "        loss=\"mse\",\n", "        optimizer=\"adam\",\n", "        GPU=0,\n", "        seed=None,\n", "        best_cond=\"loss\",\n", "        **kwargs\n", "    ):\n", "        # Set logger.\n", "        self.logger = get_module_logger(\"MLP\")\n", "        # set_log_with_config(LOGGING_CONFIG)\n", "        self.logger.info(\"=======================================\")\n", "\n", "        # set hyper-parameters.\n", "        self.num_code = num_code\n", "        self.num_input = num_input\n", "        self.layers = layers\n", "        self.dropout = dropout\n", "        self.n_epochs = n_epochs\n", "        self.lr = lr\n", "        self.metric = metric\n", "        self.batch_size = batch_size\n", "        self.early_stop = early_stop\n", "        self.optimizer = optimizer.lower()\n", "        self.loss = loss\n", "        self.device = torch.device(\"cuda:%d\" % (GPU) if torch.cuda.is_available() and GPU >= 0 else \"cpu\")\n", "        self.seed = seed\n", "        self.best_cond = best_cond\n", "\n", "        self.logger.info(\n", "            \"MLP parameters setting:\"\n", "            \"\\nnum_code : {}\"\n", "            \"\\nnum_input : {}\"\n", "            \"\\nlayers : {}\"\n", "            \"\\ndropout : {}\"\n", "            \"\\nn_epochs : {}\"\n", "            \"\\nlr : {}\"\n", "            \"\\nmetric : {}\"\n", "            \"\\nbatch_size : {}\"\n", "            \"\\nearly_stop : {}\"\n", "            \"\\noptimizer : {}\"\n", "            \"\\nloss_type : {}\"\n", "            \"\\nvisible_GPU : {}\"\n", "            \"\\nuse_GPU : {}\"\n", "            \"\\nseed : {}\"\n", "            \"\\nbest_cond : {}\".format(\n", "                num_code,\n", "                num_input,\n", "                layers,\n", "                dropout,\n", "                n_epochs,\n", "                lr,\n", "                metric,\n", "                batch_size,\n", "                early_stop,\n", "                optimizer.lower(),\n", "                loss,\n", "                GPU,\n", "                self.use_gpu,\n", "                seed,\n", "                best_cond,\n", "            )\n", "        )\n", "\n", "        if self.seed is not None:\n", "            np.random.seed(self.seed)\n", "            torch.manual_seed(self.seed)\n", "\n", "        # TODO: batch_norm drop_out\n", "        # self.mlp_model = MLPModel(\n", "        self.mlp_model = Net(\n", "            num_code=self.num_code,\n", "            num_input=self.num_input,\n", "            # output_size=1,\n", "            dropout=self.dropout,\n", "            layers=self.layers,\n", "        )\n", "        self.logger.info(\"model:\\n{:}\".format(self.mlp_model))\n", "        # self.logger.info(\"model size: {:.4f} MB\".format(count_parameters(self.mlp_model)))\n", "\n", "        if optimizer.lower() == \"adam\":\n", "            self.train_optimizer = optim.Adam(self.mlp_model.parameters(), lr=self.lr)\n", "        elif optimizer.lower() == \"gd\":\n", "            self.train_optimizer = optim.SGD(self.mlp_model.parameters(), lr=self.lr)\n", "        else:\n", "            raise NotImplementedError(\"optimizer {} is not supported!\".format(optimizer))\n", "\n", "        self.fitted = False\n", "        self.mlp_model.to(self.device)\n", "\n", "    @property\n", "    def use_gpu(self):\n", "        return self.device != torch.device(\"cpu\")\n", "\n", "    def mse(self, pred, label):\n", "        loss = (pred - label) ** 2\n", "        return torch.mean(loss)\n", "\n", "    def loss_fn(self, pred, label):\n", "        # mask = ~torch.isnan(label)\n", "        # if self.loss == \"mse\":\n", "        #     return self.mse(pred[mask], label[mask])\n", "        if self.loss == \"mse\":\n", "            sqr_loss = torch.mul(pred - label, pred - label)\n", "            loss = sqr_loss.mean()\n", "            return loss\n", "        elif self.loss == \"binary\":\n", "            loss = nn.<PERSON><PERSON><PERSON>()\n", "            return loss(pred, label)\n", "        raise ValueError(\"unknown loss `%s`\" % self.loss)\n", "\n", "    def metric_fn(self, pred, label):\n", "\n", "        mask = torch.isfinite(label)\n", "\n", "        if self.metric == \"\" or self.metric == \"loss\":\n", "            return -self.loss_fn(pred[mask], label[mask])\n", "\n", "        raise ValueError(\"unknown metric `%s`\" % self.metric)\n", "\n", "    def accuracy(self, pred, label):\n", "        if self.use_gpu:\n", "            preds = (pred>0.5).type(torch.IntTensor).cuda()\n", "        else:\n", "            preds = (pred>0.5).type(torch.IntTensor)\n", "        return (preds == label).float().mean()\n", "\n", "    def train_epoch(self, code_train, x_train, y_train):\n", "\n", "        code_train_values = np.squeeze(code_train.values)\n", "        x_train_values = x_train.values\n", "        y_train_values = np.squeeze(y_train.values)\n", "\n", "        self.mlp_model.train()\n", "\n", "        indices = np.arange(len(x_train_values))\n", "        np.random.shuffle(indices)\n", "\n", "        for i in range(len(indices))[:: self.batch_size]:\n", "\n", "            if len(indices) - i < self.batch_size:\n", "                break\n", "\n", "            codes = torch.from_numpy(code_train_values[indices[i : i + self.batch_size]]).long().to(self.device)\n", "            feature = torch.from_numpy(x_train_values[indices[i : i + self.batch_size]]).float().to(self.device)\n", "            label = torch.from_numpy(y_train_values[indices[i : i + self.batch_size]]).float().to(self.device)\n", "\n", "            # print(codes, feature)\n", "            pred = self.mlp_model(codes, feature)\n", "            loss = self.loss_fn(pred, label)\n", "\n", "\n", "            self.train_optimizer.zero_grad()\n", "            loss.backward()\n", "            torch.nn.utils.clip_grad_value_(self.mlp_model.parameters(), 3.0)\n", "            self.train_optimizer.step()\n", "\n", "    def test_epoch(self, data_code, data_x, data_y):\n", "        code_values = np.squeeze(data_code.values)\n", "        x_values = data_x.values\n", "        y_values = np.squeeze(data_y.values)\n", "\n", "        self.mlp_model.eval()\n", "\n", "        losses = []\n", "        # accs = []\n", "        correct = 0\n", "        total = 0\n", "\n", "        indices = np.arange(len(x_values))\n", "\n", "        for i in range(len(indices))[:: self.batch_size]:\n", "\n", "            if len(indices) - i < self.batch_size:\n", "                break\n", "\n", "            codes = torch.from_numpy(code_values[indices[i : i + self.batch_size]]).long().to(self.device)\n", "            feature = torch.from_numpy(x_values[indices[i : i + self.batch_size]]).float().to(self.device)\n", "            label = torch.from_numpy(y_values[indices[i : i + self.batch_size]]).float().to(self.device)\n", "\n", "            with torch.no_grad():\n", "                pred = self.mlp_model(codes, feature)\n", "                loss = self.loss_fn(pred, label)\n", "                losses.append(loss.item())\n", "\n", "                correct += ((pred>0.5).type(torch.IntTensor).to(self.device) == label).sum().item()\n", "                total += label.size(0)\n", "\n", "                # acc = self.accuracy(pred, label)\n", "                # accs.append(acc.item())\n", "\n", "        epoch_acc = correct / total\n", "        return np.mean(losses), epoch_acc # np.mean(accs)\n", "\n", "    def fit(\n", "        self,\n", "        dataset: DatasetH,\n", "        evals_result=dict(),\n", "        save_path=None,\n", "    ):\n", "\n", "        df_train, df_valid = dataset.prepare(\n", "            [\"train\", \"valid\"],\n", "            col_set=[\"feature\", \"label\", \"encoded\"],\n", "            data_key=DataHandlerLP.DK_L,\n", "        )\n", "\n", "        code_train, x_train, y_train = df_train[\"encoded\"], df_train[\"feature\"], df_train[\"label\"]\n", "        code_valid, x_valid, y_valid = df_valid[\"encoded\"], df_valid[\"feature\"], df_valid[\"label\"]\n", "\n", "        # save_path = get_or_create_path(save_path)\n", "        stop_steps = 0\n", "        train_loss = 0\n", "        best_loss = np.inf\n", "        best_acc = -np.inf\n", "        best_epoch = 0\n", "        evals_result[\"loss\"] = {}\n", "        evals_result[\"accuracy\"] = {}\n", "        evals_result[\"loss\"][\"train\"] = []\n", "        evals_result[\"loss\"][\"valid\"] = []\n", "        evals_result[\"accuracy\"][\"train\"] = []\n", "        evals_result[\"accuracy\"][\"valid\"] = []\n", "\n", "        # train\n", "        self.logger.info(\"training...\")\n", "        self.fitted = True\n", "\n", "        # 学习速率衰减设置\n", "        exp_lr_scheduler = lr_scheduler.StepLR(self.train_optimizer, step_size=30, gamma=0.5) # 按步数\n", "        # exp_lr_scheduler = lr_scheduler.MultiStepLR(opt, milestones=[50, 100, 150], gamma=0.1) # 按里程碑\n", "        # exp_lr_scheduler = lr_scheduler.ExponentialLR(opt, gamma=0.1) # 按系数每步\n", "\n", "        for step in range(self.n_epochs):\n", "            self.logger.info(\"Epoch %d:\", step)\n", "            # self.logger.info(\"training...\")\n", "            self.train_epoch(code_train, x_train, y_train)\n", "            exp_lr_scheduler.step()\n", "\n", "            # self.logger.info(\"evaluating...\")\n", "            train_loss, train_acc = self.test_epoch(code_train, x_train, y_train)\n", "            val_loss, val_acc = self.test_epoch(code_valid, x_valid, y_valid)\n", "            self.logger.info(\"loss: train %.3f, valid %.3f\" % (train_loss, val_loss))\n", "            self.logger.info(\"accuracy: train %.3f, valid %.3f\" % (train_acc, val_acc))\n", "            evals_result[\"loss\"][\"train\"].append(train_loss)\n", "            evals_result[\"loss\"][\"valid\"].append(val_loss)\n", "            evals_result[\"accuracy\"][\"train\"].append(train_acc)\n", "            evals_result[\"accuracy\"][\"valid\"].append(val_acc)\n", "\n", "            # TODO: best model cond.\n", "            if self.best_cond == \"loss\":\n", "                if val_loss < best_loss:\n", "                    best_loss = val_loss\n", "\n", "                    if val_acc > best_acc:\n", "                        best_acc = val_acc\n", "\n", "                    stop_steps = 0\n", "                    best_epoch = step\n", "                    best_param = copy.deepcopy(self.mlp_model.state_dict())\n", "                else:\n", "                    stop_steps += 1\n", "                    if stop_steps >= self.early_stop:\n", "                        self.logger.info(\"early stop\")\n", "                        break\n", "            else:\n", "                if val_acc > best_acc:\n", "                    best_acc = val_acc\n", "\n", "                    if val_loss < best_loss:\n", "                        best_loss = val_loss\n", "\n", "                    stop_steps = 0\n", "                    best_epoch = step\n", "                    best_param = copy.deepcopy(self.mlp_model.state_dict())\n", "                else:\n", "                    stop_steps += 1\n", "                    if stop_steps >= self.early_stop:\n", "                        self.logger.info(\"early stop\")\n", "                        break\n", "\n", "        self.logger.info(\"best epoch: %d loss: %.6lf accuracy: %.3lf\" % (best_epoch, best_loss, best_acc))\n", "        self.mlp_model.load_state_dict(best_param)\n", "        # save model\n", "        # model = self.mlp_model.cpu()\n", "        # model.eval() # 如果要使用，要调用eval()表明运行模式\n", "        # sm = torch.jit.script(model)\n", "        # sm.save(save_path)\n", "\n", "        if self.use_gpu:\n", "            torch.cuda.empty_cache()\n", "\n", "        return best_epoch, best_loss, best_acc\n", "\n", "    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = \"test\"):\n", "        if not self.fitted:\n", "            raise ValueError(\"model is not fitted yet!\")\n", "\n", "        x_test = dataset.prepare(segment, col_set=\"feature\", data_key=DataHandlerLP.DK_I)\n", "        # x_test = torch.from_numpy(x_test_pd.values).float().to(self.device)\n", "        index = x_test.index\n", "        self.mlp_model.eval()\n", "        x_values = x_test.values\n", "        sample_num = x_values.shape[0]\n", "        preds = []\n", "\n", "        for begin in range(sample_num)[:: self.batch_size]:\n", "\n", "            if sample_num - begin < self.batch_size:\n", "                end = sample_num\n", "            else:\n", "                end = begin + self.batch_size\n", "\n", "            x_batch = torch.from_numpy(x_values[begin:end]).float().to(self.device)\n", "\n", "            with torch.no_grad():\n", "                pred = self.mlp_model(x_batch).detach().cpu().numpy()\n", "\n", "            preds.append(pred)\n", "\n", "        return pd.Series(np.concatenate(preds), index=index)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class Net(nn.Module):\n", "    '''\n", "    layers=(256, 512, 768, 512, 256, 128, 64)\n", "    '''\n", "    def __init__(self, num_code, num_input, dropout=0.2, output_dim=1, layers=(96, 128, 96, 64), loss=\"binary\"):\n", "        super(Net, self).__init__()\n", "        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=1)\n", "        num_input+=1\n", "        layers = [num_input] + list(layers)\n", "        dnn_layers = []\n", "        drop_input = nn.Dropout(dropout)\n", "        dnn_layers.append(drop_input)\n", "        for i, (num_input, hidden_units) in enumerate(zip(layers[:-1], layers[1:])):\n", "            fc = nn.Linear(num_input, hidden_units)\n", "            activation = nn.LeakyReLU(negative_slope=0.1, inplace=False)\n", "            bn = nn.BatchNorm1d(hidden_units)\n", "            seq = nn.Sequential(fc, bn, activation)\n", "            dnn_layers.append(seq)\n", "        drop_input = nn.Dropout(dropout)\n", "        dnn_layers.append(drop_input)\n", "        fc = nn.Linear(hidden_units, output_dim)\n", "        dnn_layers.append(fc)\n", "        # add sigmoid layer\n", "        dnn_layers.append(nn.Sigmo<PERSON>())\n", "        # optimizer\n", "        self.dnn_layers = nn.ModuleList(dnn_layers)\n", "        self._weight_init()\n", "\n", "    def _weight_init(self):\n", "        for m in self.modules():\n", "            if isinstance(m, nn.Linear):\n", "                nn.init.kaiming_normal_(m.weight, a=0.1, mode=\"fan_in\", nonlinearity=\"leaky_relu\")\n", "\n", "    def forward(self, code_ids, x):\n", "        # 加入code向量\n", "        embedded_code_ids = self.code_embeddings(code_ids)\n", "        cur_output = torch.cat([x, embedded_code_ids], dim=1)\n", "        for i, now_layer in enumerate(self.dnn_layers):\n", "            cur_output = now_layer(cur_output)\n", "        return cur_output.view(-1)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[9908:MainThread](2022-10-20 09:35:53,774) INFO - qlib.DataHandlerAF - [handler.py:83] - \n", "============================\n", "\n", "lb(507, 6) sf(507, 45) lf(507, 0) ct(507, 11)\n", "\n", "============================\n", "[9908:MainThread](2022-10-20 09:35:53,785) INFO - qlib.timer - [log.py:117] - Time cost: 0.066s | Loading data Done\n", "[9908:MainThread](2022-10-20 09:35:53,787) INFO - qlib.DataHandlerAF - [handler.py:130] - train shape(405, 56) valid shape(102, 56)\n", "[9908:MainThread](2022-10-20 09:35:53,788) INFO - qlib.timer - [log.py:117] - Time cost: 0.069s | Init data Done\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Feature total: 507\n", "Today add long count: 0\n"]}], "source": ["data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n", "hd_long: DataHandlerAF = init_instance_by_config(dataset_config[\"kwargs\"][\"handler\"])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["data_handler_config[\"instruments\"] = ['06220831232331000', '01220901173143000']\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"model_name_suff\"] = \"5RF\"\n", "dataset_config[\"kwargs\"][\"handler\"] = hd_long\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[9908:MainThread](2022-10-20 09:35:58,562) INFO - qlib.DataHandlerAF - [handler.py:83] - \n", "============================\n", "\n", "lb(507, 6) sf(507, 45) lf(507, 0) ct(507, 11)\n", "\n", "============================\n", "[9908:MainThread](2022-10-20 09:35:58,571) INFO - qlib.timer - [log.py:117] - Time cost: 0.023s | Loading data Done\n", "[9908:MainThread](2022-10-20 09:35:58,572) INFO - qlib.DataHandlerAF - [handler.py:130] - train shape(405, 56) valid shape(102, 56)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Feature total: 507\n", "Today add long count: 0\n"]}], "source": ["dataset = init_instance_by_config(dataset_config)\n", "dataset.setup_data(handler_kwargs=data_handler_config)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[9908:MainThread](2022-10-20 09:38:49,890) INFO - qlib.MLP - [1653279973.py:39] - =======================================\n", "[9908:MainThread](2022-10-20 09:38:49,893) INFO - qlib.MLP - [1653279973.py:57] - MLP parameters setting:\n", "num_code : 60\n", "num_input : 56\n", "layers : (96, 64, 16)\n", "dropout : 0.2\n", "n_epochs : 200\n", "lr : 0.001\n", "metric : \n", "batch_size : 32\n", "early_stop : 30\n", "optimizer : adam\n", "loss_type : binary\n", "visible_GPU : 0\n", "use_GPU : False\n", "seed : None\n", "best_cond : accuracy\n", "[9908:MainThread](2022-10-20 09:38:49,895) INFO - qlib.MLP - [1653279973.py:105] - model:\n", "Net(\n", "  (code_embeddings): Embedding(60, 1)\n", "  (dnn_layers): ModuleList(\n", "    (0): Dropout(p=0.2, inplace=False)\n", "    (1): Sequential(\n", "      (0): Linear(in_features=57, out_features=96, bias=True)\n", "      (1): BatchNorm1d(96, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (2): LeakyReLU(negative_slope=0.1)\n", "    )\n", "    (2): Sequential(\n", "      (0): Linear(in_features=96, out_features=64, bias=True)\n", "      (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (2): LeakyReLU(negative_slope=0.1)\n", "    )\n", "    (3): Sequential(\n", "      (0): Linear(in_features=64, out_features=16, bias=True)\n", "      (1): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (2): LeakyReLU(negative_slope=0.1)\n", "    )\n", "    (4): Dropout(p=0.2, inplace=False)\n", "    (5): Linear(in_features=16, out_features=1, bias=True)\n", "    (6): <PERSON><PERSON><PERSON><PERSON>()\n", "  )\n", ")\n"]}], "source": ["# model initiaiton\n", "task = {\n", "    \"model\": {\n", "        \"class\": \"MLPTorch\",\n", "        \"module_path\": \"\",\n", "        \"kwargs\": {\n", "            \"loss\": \"binary\",\n", "            \"num_code\": 60,\n", "            \"num_input\": 56, #97 7+45+1=53 \n", "            \"output_dim\": 1,\n", "            \"dropout\": 0.2,\n", "            \"lr\": 0.001,\n", "            \"lr_decay\": 0.96,\n", "            \"lr_decay_steps\": 100,\n", "            \"optimizer\": \"adam\",\n", "            \"batch_size\": 32,\n", "            \"GPU\": 0,\n", "            \"early_stop\": 30,\n", "            \"best_cond\": \"accuracy\", # or loss\n", "            \"layers\": (96, 64, 16), # (128, 256, 128, 64),\n", "        },\n", "    },\n", "}\n", "# model = init_instance_by_config(task[\"model\"])\n", "model = MLPTorch(**task[\"model\"][\"kwargs\"])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[9908:MainThread](2022-10-20 09:38:51,078) INFO - qlib.DatasetH - [__init__.py:83] - data_key[learn] is ignored.\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,094) INFO - qlib.MLP - [1653279973.py:252] - training...\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,096) INFO - qlib.MLP - [1653279973.py:261] - Epoch 0:\n", "[9908:MainThread](2022-10-20 09:38:51,165) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.692, valid 0.737\n", "[9908:MainThread](2022-10-20 09:38:51,166) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.547, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,168) INFO - qlib.MLP - [1653279973.py:261] - Epoch 1:\n", "[9908:MainThread](2022-10-20 09:38:51,231) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.677, valid 0.743\n", "[9908:MainThread](2022-10-20 09:38:51,232) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.573, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,234) INFO - qlib.MLP - [1653279973.py:261] - Epoch 2:\n", "[9908:MainThread](2022-10-20 09:38:51,294) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.660, valid 0.738\n", "[9908:MainThread](2022-10-20 09:38:51,295) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.607, valid 0.521\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,296) INFO - qlib.MLP - [1653279973.py:261] - Epoch 3:\n", "[9908:MainThread](2022-10-20 09:38:51,360) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.646, valid 0.736\n", "[9908:MainThread](2022-10-20 09:38:51,360) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.643, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,361) INFO - qlib.MLP - [1653279973.py:261] - Epoch 4:\n", "[9908:MainThread](2022-10-20 09:38:51,419) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.633, valid 0.734\n", "[9908:MainThread](2022-10-20 09:38:51,420) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.643, valid 0.521\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,421) INFO - qlib.MLP - [1653279973.py:261] - Epoch 5:\n", "[9908:MainThread](2022-10-20 09:38:51,475) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.625, valid 0.734\n", "[9908:MainThread](2022-10-20 09:38:51,476) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.672, valid 0.500\n", "[9908:<PERSON>T<PERSON><PERSON>](2022-10-20 09:38:51,478) INFO - qlib.MLP - [1653279973.py:261] - Epoch 6:\n", "[9908:MainThread](2022-10-20 09:38:51,533) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.616, valid 0.737\n", "[9908:MainThread](2022-10-20 09:38:51,534) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.677, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,535) INFO - qlib.MLP - [1653279973.py:261] - Epoch 7:\n", "[9908:MainThread](2022-10-20 09:38:51,590) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.605, valid 0.725\n", "[9908:MainThread](2022-10-20 09:38:51,591) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.721, valid 0.552\n", "[9908:<PERSON>T<PERSON><PERSON>](2022-10-20 09:38:51,593) INFO - qlib.MLP - [1653279973.py:261] - Epoch 8:\n", "[9908:MainThread](2022-10-20 09:38:51,655) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.593, valid 0.718\n", "[9908:MainThread](2022-10-20 09:38:51,656) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.737, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,656) INFO - qlib.MLP - [1653279973.py:261] - Epoch 9:\n", "[9908:MainThread](2022-10-20 09:38:51,721) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.589, valid 0.716\n", "[9908:MainThread](2022-10-20 09:38:51,722) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.740, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,723) INFO - qlib.MLP - [1653279973.py:261] - Epoch 10:\n", "[9908:MainThread](2022-10-20 09:38:51,787) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.583, valid 0.716\n", "[9908:MainThread](2022-10-20 09:38:51,789) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.740, valid 0.521\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,789) INFO - qlib.MLP - [1653279973.py:261] - Epoch 11:\n", "[9908:MainThread](2022-10-20 09:38:51,857) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.578, valid 0.715\n", "[9908:MainThread](2022-10-20 09:38:51,858) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.758, valid 0.521\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,860) INFO - qlib.MLP - [1653279973.py:261] - Epoch 12:\n", "[9908:MainThread](2022-10-20 09:38:51,919) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.571, valid 0.708\n", "[9908:MainThread](2022-10-20 09:38:51,920) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.771, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,921) INFO - qlib.MLP - [1653279973.py:261] - Epoch 13:\n", "[9908:MainThread](2022-10-20 09:38:51,981) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.565, valid 0.704\n", "[9908:MainThread](2022-10-20 09:38:51,982) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.786, valid 0.521\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:51,983) INFO - qlib.MLP - [1653279973.py:261] - Epoch 14:\n", "[9908:MainThread](2022-10-20 09:38:52,041) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.560, valid 0.709\n", "[9908:MainThread](2022-10-20 09:38:52,042) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.797, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,043) INFO - qlib.MLP - [1653279973.py:261] - Epoch 15:\n", "[9908:MainThread](2022-10-20 09:38:52,102) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.553, valid 0.721\n", "[9908:MainThread](2022-10-20 09:38:52,103) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.792, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,105) INFO - qlib.MLP - [1653279973.py:261] - Epoch 16:\n", "[9908:MainThread](2022-10-20 09:38:52,166) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.552, valid 0.720\n", "[9908:MainThread](2022-10-20 09:38:52,168) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.773, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,169) INFO - qlib.MLP - [1653279973.py:261] - Epoch 17:\n", "[9908:MainThread](2022-10-20 09:38:52,233) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.545, valid 0.712\n", "[9908:MainThread](2022-10-20 09:38:52,234) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.786, valid 0.583\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,237) INFO - qlib.MLP - [1653279973.py:261] - Epoch 18:\n", "[9908:MainThread](2022-10-20 09:38:52,306) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.540, valid 0.721\n", "[9908:MainThread](2022-10-20 09:38:52,308) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.781, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,309) INFO - qlib.MLP - [1653279973.py:261] - Epoch 19:\n", "[9908:MainThread](2022-10-20 09:38:52,368) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.538, valid 0.720\n", "[9908:MainThread](2022-10-20 09:38:52,370) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.784, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,371) INFO - qlib.MLP - [1653279973.py:261] - Epoch 20:\n", "[9908:MainThread](2022-10-20 09:38:52,429) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.529, valid 0.723\n", "[9908:MainThread](2022-10-20 09:38:52,430) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.794, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,431) INFO - qlib.MLP - [1653279973.py:261] - Epoch 21:\n", "[9908:MainThread](2022-10-20 09:38:52,486) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.518, valid 0.730\n", "[9908:MainThread](2022-10-20 09:38:52,487) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.810, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,488) INFO - qlib.MLP - [1653279973.py:261] - Epoch 22:\n", "[9908:MainThread](2022-10-20 09:38:52,544) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.512, valid 0.737\n", "[9908:MainThread](2022-10-20 09:38:52,545) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.810, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,546) INFO - qlib.MLP - [1653279973.py:261] - Epoch 23:\n", "[9908:MainThread](2022-10-20 09:38:52,604) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.505, valid 0.743\n", "[9908:MainThread](2022-10-20 09:38:52,605) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.820, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,606) INFO - qlib.MLP - [1653279973.py:261] - Epoch 24:\n", "[9908:MainThread](2022-10-20 09:38:52,664) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.505, valid 0.743\n", "[9908:MainThread](2022-10-20 09:38:52,665) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.823, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,666) INFO - qlib.MLP - [1653279973.py:261] - Epoch 25:\n", "[9908:MainThread](2022-10-20 09:38:52,727) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.495, valid 0.737\n", "[9908:MainThread](2022-10-20 09:38:52,728) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.839, valid 0.562\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,729) INFO - qlib.MLP - [1653279973.py:261] - Epoch 26:\n", "[9908:MainThread](2022-10-20 09:38:52,789) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.489, valid 0.737\n", "[9908:MainThread](2022-10-20 09:38:52,790) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.839, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,791) INFO - qlib.MLP - [1653279973.py:261] - Epoch 27:\n", "[9908:MainThread](2022-10-20 09:38:52,852) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.480, valid 0.739\n", "[9908:MainThread](2022-10-20 09:38:52,854) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.841, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,855) INFO - qlib.MLP - [1653279973.py:261] - Epoch 28:\n", "[9908:MainThread](2022-10-20 09:38:52,917) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.477, valid 0.737\n", "[9908:MainThread](2022-10-20 09:38:52,918) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.839, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:52,919) INFO - qlib.MLP - [1653279973.py:261] - Epoch 29:\n", "[9908:MainThread](2022-10-20 09:38:52,975) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.470, valid 0.725\n", "[9908:MainThread](2022-10-20 09:38:52,976) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.839, valid 0.594\n", "[9908:MainThr<PERSON>](2022-10-20 09:38:52,978) INFO - qlib.MLP - [1653279973.py:261] - Epoch 30:\n", "[9908:MainThread](2022-10-20 09:38:53,036) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.465, valid 0.725\n", "[9908:MainThread](2022-10-20 09:38:53,037) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.836, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,038) INFO - qlib.MLP - [1653279973.py:261] - Epoch 31:\n", "[9908:MainThread](2022-10-20 09:38:53,103) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.464, valid 0.729\n", "[9908:MainThread](2022-10-20 09:38:53,104) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.831, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,106) INFO - qlib.MLP - [1653279973.py:261] - Epoch 32:\n", "[9908:MainThread](2022-10-20 09:38:53,172) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.459, valid 0.731\n", "[9908:MainThread](2022-10-20 09:38:53,173) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.857, valid 0.573\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,174) INFO - qlib.MLP - [1653279973.py:261] - Epoch 33:\n", "[9908:MainThread](2022-10-20 09:38:53,241) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.454, valid 0.739\n", "[9908:MainThread](2022-10-20 09:38:53,242) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.849, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,242) INFO - qlib.MLP - [1653279973.py:261] - Epoch 34:\n", "[9908:MainThread](2022-10-20 09:38:53,307) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.453, valid 0.742\n", "[9908:MainThread](2022-10-20 09:38:53,308) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.852, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,309) INFO - qlib.MLP - [1653279973.py:261] - Epoch 35:\n", "[9908:MainThread](2022-10-20 09:38:53,367) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.449, valid 0.739\n", "[9908:MainThread](2022-10-20 09:38:53,369) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.852, valid 0.521\n", "[9908:MainThr<PERSON>](2022-10-20 09:38:53,370) INFO - qlib.MLP - [1653279973.py:261] - Epoch 36:\n", "[9908:MainThread](2022-10-20 09:38:53,427) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.444, valid 0.743\n", "[9908:MainThread](2022-10-20 09:38:53,428) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.865, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,429) INFO - qlib.MLP - [1653279973.py:261] - Epoch 37:\n", "[9908:MainThread](2022-10-20 09:38:53,487) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.444, valid 0.735\n", "[9908:MainThread](2022-10-20 09:38:53,488) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.862, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,488) INFO - qlib.MLP - [1653279973.py:261] - Epoch 38:\n", "[9908:MainThread](2022-10-20 09:38:53,546) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.436, valid 0.748\n", "[9908:MainThread](2022-10-20 09:38:53,548) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.865, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,549) INFO - qlib.MLP - [1653279973.py:261] - Epoch 39:\n", "[9908:MainThread](2022-10-20 09:38:53,606) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.441, valid 0.744\n", "[9908:MainThread](2022-10-20 09:38:53,607) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.865, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,608) INFO - qlib.MLP - [1653279973.py:261] - Epoch 40:\n", "[9908:MainThread](2022-10-20 09:38:53,670) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.433, valid 0.743\n", "[9908:MainThread](2022-10-20 09:38:53,671) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.872, valid 0.521\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,672) INFO - qlib.MLP - [1653279973.py:261] - Epoch 41:\n", "[9908:MainThread](2022-10-20 09:38:53,736) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.428, valid 0.750\n", "[9908:MainThread](2022-10-20 09:38:53,737) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.867, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,738) INFO - qlib.MLP - [1653279973.py:261] - Epoch 42:\n", "[9908:MainThread](2022-10-20 09:38:53,803) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.424, valid 0.760\n", "[9908:MainThread](2022-10-20 09:38:53,804) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.885, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,805) INFO - qlib.MLP - [1653279973.py:261] - Epoch 43:\n", "[9908:MainThread](2022-10-20 09:38:53,868) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.417, valid 0.762\n", "[9908:MainThread](2022-10-20 09:38:53,869) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.880, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,870) INFO - qlib.MLP - [1653279973.py:261] - Epoch 44:\n", "[9908:MainThread](2022-10-20 09:38:53,929) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.415, valid 0.756\n", "[9908:MainThread](2022-10-20 09:38:53,931) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.880, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,931) INFO - qlib.MLP - [1653279973.py:261] - Epoch 45:\n", "[9908:MainThread](2022-10-20 09:38:53,986) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.413, valid 0.761\n", "[9908:MainThread](2022-10-20 09:38:53,987) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.888, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:53,988) INFO - qlib.MLP - [1653279973.py:261] - Epoch 46:\n", "[9908:MainThread](2022-10-20 09:38:54,051) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.408, valid 0.758\n", "[9908:MainThread](2022-10-20 09:38:54,052) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.875, valid 0.531\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,053) INFO - qlib.MLP - [1653279973.py:261] - Epoch 47:\n", "[9908:MainThread](2022-10-20 09:38:54,109) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.406, valid 0.761\n", "[9908:MainThread](2022-10-20 09:38:54,110) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.888, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,111) INFO - qlib.MLP - [1653279973.py:261] - Epoch 48:\n", "[9908:MainThread](2022-10-20 09:38:54,167) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.402, valid 0.768\n", "[9908:MainThread](2022-10-20 09:38:54,168) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.888, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,169) INFO - qlib.MLP - [1653279973.py:261] - Epoch 49:\n", "[9908:MainThread](2022-10-20 09:38:54,231) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.397, valid 0.777\n", "[9908:MainThread](2022-10-20 09:38:54,233) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.883, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,234) INFO - qlib.MLP - [1653279973.py:261] - Epoch 50:\n", "[9908:MainThread](2022-10-20 09:38:54,297) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.393, valid 0.774\n", "[9908:MainThread](2022-10-20 09:38:54,298) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.883, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,299) INFO - qlib.MLP - [1653279973.py:261] - Epoch 51:\n", "[9908:MainThread](2022-10-20 09:38:54,357) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.393, valid 0.772\n", "[9908:MainThread](2022-10-20 09:38:54,358) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.880, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,359) INFO - qlib.MLP - [1653279973.py:261] - Epoch 52:\n", "[9908:MainThread](2022-10-20 09:38:54,416) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.388, valid 0.776\n", "[9908:MainThread](2022-10-20 09:38:54,417) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.883, valid 0.500\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,418) INFO - qlib.MLP - [1653279973.py:261] - Epoch 53:\n", "[9908:MainThread](2022-10-20 09:38:54,474) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.391, valid 0.770\n", "[9908:MainThread](2022-10-20 09:38:54,475) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.880, valid 0.542\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,476) INFO - qlib.MLP - [1653279973.py:261] - Epoch 54:\n", "[9908:MainThread](2022-10-20 09:38:54,534) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.382, valid 0.769\n", "[9908:MainThread](2022-10-20 09:38:54,537) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.893, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,537) INFO - qlib.MLP - [1653279973.py:261] - Epoch 55:\n", "[9908:MainThread](2022-10-20 09:38:54,596) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.371, valid 0.779\n", "[9908:MainThread](2022-10-20 09:38:54,597) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.898, valid 0.521\n", "[9908:<PERSON>T<PERSON><PERSON>](2022-10-20 09:38:54,598) INFO - qlib.MLP - [1653279973.py:261] - Epoch 56:\n", "[9908:MainThread](2022-10-20 09:38:54,658) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.368, valid 0.783\n", "[9908:MainThread](2022-10-20 09:38:54,660) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.896, valid 0.510\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,660) INFO - qlib.MLP - [1653279973.py:261] - Epoch 57:\n", "[9908:MainThread](2022-10-20 09:38:54,721) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.371, valid 0.788\n", "[9908:MainThread](2022-10-20 09:38:54,722) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.904, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,723) INFO - qlib.MLP - [1653279973.py:261] - Epoch 58:\n", "[9908:MainThread](2022-10-20 09:38:54,788) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.365, valid 0.791\n", "[9908:MainThread](2022-10-20 09:38:54,789) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.906, valid 0.552\n", "[9908:<PERSON>Thr<PERSON>](2022-10-20 09:38:54,790) INFO - qlib.MLP - [1653279973.py:261] - Epoch 59:\n", "[9908:MainThread](2022-10-20 09:38:54,851) INFO - qlib.MLP - [1653279973.py:269] - loss: train 0.356, valid 0.798\n", "[9908:MainThread](2022-10-20 09:38:54,852) INFO - qlib.MLP - [1653279973.py:270] - accuracy: train 0.904, valid 0.531\n", "[9908:MainThr<PERSON>](2022-10-20 09:38:54,852) INFO - qlib.MLP - [1653279973.py:305] - early stop\n", "[9908:MainThread](2022-10-20 09:38:54,853) INFO - qlib.MLP - [1653279973.py:308] - best epoch: 29 loss: 0.711841 accuracy: 0.594\n"]}], "source": ["result={}\n", "train_result={}\n", "model_name = \"MLP\"\n", "best_epoch, best_loss, best_acc = model.fit(\n", "    dataset,\n", "    evals_result=result,\n", "    save_path=\"./model\",\n", "    # save_jit_script=False\n", ")\n", "train_result[model_name] = []\n", "train_result[model_name].append(best_epoch)\n", "train_result[model_name].append(round(best_loss, 6))\n", "train_result[model_name].append(round(best_acc, 3))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_result(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}