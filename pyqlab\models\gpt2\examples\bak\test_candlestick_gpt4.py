"""
测试CandlestickGPT4模型

加载训练好的模型并进行预测测试
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入相关模块
from pyqlab.models.gpt2.bak.candlestick_gpt4 import CandlestickGPT4
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import (
    NonlinearCandlestickTokenizer,
    LogarithmicMapping,
    SquareRootMapping,
    ExponentialMapping,
    SigmoidMapping
)
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试CandlestickGPT4模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--begin_date', type=str, default='2023-01-01', help='开始日期')
    parser.add_argument('--end_date', type=str, default='2025-12-31', help='结束日期')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--nonlinear_tokenizer', action='store_true', help='是否使用非线性tokenizer')

    # 预测参数
    parser.add_argument('--seq_len', type=int, default=64, help='序列长度')
    parser.add_argument('--pred_len', type=int, default=10, help='预测长度')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数')
    parser.add_argument('--top_k', type=int, default=50, help='Top-K采样')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--output_dir', type=str, default='results/candlestick_gpt4', help='输出目录')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

def load_data(args):
    """加载数据"""
    print(f"加载数据: {args.data_path}")

    # 读取数据
    if args.data_path.endswith('.parquet'):
        df = pd.read_parquet(args.data_path)
    elif args.data_path.endswith('.csv'):
        df = pd.read_csv(args.data_path)
    else:
        raise ValueError(f"不支持的文件格式: {args.data_path}")

    # 过滤日期
    if 'datetime' in df.columns:
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df[(df['datetime'] >= args.begin_date) & (df['datetime'] <= args.end_date)]

    # 按代码分组
    if 'code' in df.columns:
        grouped = df.groupby('code')
        data_list = [group for _, group in grouped]
    else:
        data_list = [df]

    # 打印数据统计
    print(f"数据统计:")
    print(f"总数据量: {len(df)}")
    print(f"代码数量: {len(data_list)}")

    return data_list

def create_tokenizer(args):
    """创建tokenizer"""
    print("创建tokenizer")

    # 根据数据分布设置合适的范围
    change_range = (-60, 125)
    entity_range = (-37, 47)
    shadow_range = (0, 28)

    if args.nonlinear_tokenizer:
        print("使用非线性tokenizer")
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=change_range,
            entity_range=entity_range,
            shadow_range=shadow_range,
            atr_window=50,
            atr_mult=1.0,
            scale=1,
            include_volume=False,
            # 添加非线性映射函数
            mapping_functions={
                'change': LogarithmicMapping(change_range, (-20, 20)),
                'entity': LogarithmicMapping(entity_range, (-15, 15)),
                'upline': SquareRootMapping(shadow_range, (0, 10)),
                'downline': SquareRootMapping(shadow_range, (0, 10))
            }
        )
    else:
        print("使用线性tokenizer")
        tokenizer = CandlestickTokenizer(
            change_range=change_range,
            entity_range=entity_range,
            shadow_range=shadow_range,
            atr_window=50,
            atr_mult=1.0,
            scale=1,
            include_volume=False
        )

    print(f"词汇表大小: {tokenizer.vocab_size}")
    return tokenizer

def load_model(args, tokenizer):
    """加载模型"""
    print(f"加载模型: {args.model_path}")

    # 加载检查点
    checkpoint = torch.load(args.model_path, map_location='cpu')

    # 从检查点中获取模型配置
    config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
    if os.path.exists(config_path):
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)

        # 创建模型
        model = CandlestickGPT4(
            vocab_size=tokenizer.vocab_size,
            code_size=100,  # 假设最多100个不同的证券代码
            block_size=config.get('block_size', args.seq_len),
            n_layer=config.get('n_layer', 4),
            n_head=config.get('n_head', 4),
            d_model=config.get('d_model', 64),
            dropout=config.get('dropout', 0.1),
            use_time_features=config.get('use_time_features', False),
            n_time_features=5,  # 假设有5个时间特征
            label_smoothing=config.get('label_smoothing', 0.1),
            use_auxiliary_loss=config.get('use_auxiliary_loss', True)
        )
    else:
        # 使用默认配置创建模型
        model = CandlestickGPT4(
            vocab_size=tokenizer.vocab_size,
            code_size=100,
            block_size=args.seq_len,
            n_layer=4,
            n_head=4,
            d_model=64,
            dropout=0.1,
            use_time_features=False,
            n_time_features=5,
            label_smoothing=0.1,
            use_auxiliary_loss=True
        )

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])

    # 移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()

    print(f"模型参数数量: {model.get_num_params():,}")
    print(f"使用设备: {device}")

    return model, device

def prepare_data_for_prediction(data, tokenizer, args):
    """准备预测数据"""
    print("准备预测数据")

    # 创建数据集
    dataset = CandlestickDataset(
        data=[data],
        tokenizer=tokenizer,
        seq_len=args.seq_len,
        pred_len=args.pred_len,
        code_ids=[0],
        use_time_features=False,
        stride=1
    )

    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=1,
        shuffle=False
    )

    return dataset, dataloader

def predict(model, dataloader, tokenizer, device, args):
    """使用模型进行预测"""
    print("开始预测")

    # 获取一个批次的数据
    batch = next(iter(dataloader))

    # 获取输入数据
    input_tokens = batch['input_tokens'].to(device)
    code_id = batch['code_id'].to(device)
    time_features = batch['time_features'].to(device) if 'time_features' in batch else None

    # 生成预测
    with torch.no_grad():
        output_tokens = model.generate(
            input_tokens=input_tokens,
            code_ids=code_id,
            time_features=time_features,
            max_new_tokens=args.pred_len,
            temperature=args.temperature,
            top_k=args.top_k
        )

    # 获取预测的token序列
    pred_tokens = output_tokens[0, -args.pred_len:].cpu().numpy().tolist()

    # 获取最后一个已知价格和ATR
    data = dataloader.dataset.data[0]
    last_known_price = data['close'].iloc[args.seq_len - 1]

    # 计算ATR
    high_low = data['high'] - data['low']
    high_close = (data['high'] - data['close'].shift(1)).abs()
    low_close = (data['low'] - data['close'].shift(1)).abs()
    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = tr.rolling(window=tokenizer.atr_window).mean()
    last_atr = atr.iloc[args.seq_len - 1]

    print(f"最后一个已知价格: {last_known_price}")
    print(f"ATR值: {last_atr}")

    # 将token转换回K线数据
    pred_ohlc = tokenizer.tokens_to_candlesticks(pred_tokens, last_known_price, last_atr)

    return pred_ohlc, data.iloc[args.seq_len:args.seq_len+args.pred_len]

def visualize_prediction(pred_ohlc, actual_data, args):
    """可视化预测结果"""
    print("可视化预测结果")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 6))

    # 绘制实际收盘价
    ax.plot(range(len(actual_data)), actual_data['close'].values, 'b-', label='实际收盘价')

    # 绘制预测收盘价
    ax.plot(range(len(pred_ohlc)), pred_ohlc['close'].values, 'r-', label='预测收盘价')

    # 添加标签和图例
    ax.set_xlabel('时间步')
    ax.set_ylabel('价格')
    ax.set_title('K线预测结果')
    ax.legend()
    ax.grid(True)

    # 保存图表
    plt.savefig(os.path.join(args.output_dir, 'prediction.png'))
    plt.close()

    # 保存预测结果
    pred_ohlc.to_csv(os.path.join(args.output_dir, 'prediction.csv'))
    actual_data.to_csv(os.path.join(args.output_dir, 'actual.csv'))

    print(f"结果已保存到 {args.output_dir}")

    # 计算预测误差
    mse = ((pred_ohlc['close'].values - actual_data['close'].values) ** 2).mean()
    mae = np.abs(pred_ohlc['close'].values - actual_data['close'].values).mean()
    mape = np.abs((pred_ohlc['close'].values - actual_data['close'].values) / actual_data['close'].values).mean() * 100

    print(f"预测误差:")
    print(f"MSE: {mse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"MAPE: {mape:.2f}%")

    # 保存误差统计
    with open(os.path.join(args.output_dir, 'metrics.txt'), 'w') as f:
        f.write(f"MSE: {mse:.4f}\n")
        f.write(f"MAE: {mae:.4f}\n")
        f.write(f"MAPE: {mape:.2f}%\n")

def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 加载数据
    data_list = load_data(args)

    # 创建tokenizer
    tokenizer = create_tokenizer(args)

    # 加载模型
    model, device = load_model(args, tokenizer)

    # 选择一个数据进行预测
    data = data_list[0]  # 使用第一个证券的数据

    # 准备预测数据
    dataset, dataloader = prepare_data_for_prediction(data, tokenizer, args)

    # 进行预测
    pred_ohlc, actual_data = predict(model, dataloader, tokenizer, device, args)

    # 可视化预测结果
    visualize_prediction(pred_ohlc, actual_data, args)

if __name__ == "__main__":
    main()
