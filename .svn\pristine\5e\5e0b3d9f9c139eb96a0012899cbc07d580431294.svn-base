{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### MOdel testing"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import ast\n", "# \"E:\\lab\\RoboQuant\\bin\\x64\\Release\\data.json\"\n", "# read data from file\n", "def read_data(file_path):\n", "    with open(file_path, 'r') as file:\n", "        data = file.read()\n", "    return ast.literal_eval(data) \n", "\n", "data = read_data(r\"E:\\lab\\RoboQuant\\bin\\x64\\Release\\data.json\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>109</th>\n", "      <th>110</th>\n", "      <th>111</th>\n", "      <th>112</th>\n", "      <th>113</th>\n", "      <th>114</th>\n", "      <th>115</th>\n", "      <th>116</th>\n", "      <th>117</th>\n", "      <th>118</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-2.317502</td>\n", "      <td>0.820360</td>\n", "      <td>-0.221119</td>\n", "      <td>-0.389944</td>\n", "      <td>-0.229186</td>\n", "      <td>-0.228896</td>\n", "      <td>-0.212291</td>\n", "      <td>0.336496</td>\n", "      <td>0.160706</td>\n", "      <td>0.145598</td>\n", "      <td>...</td>\n", "      <td>-0.006579</td>\n", "      <td>-1.459517</td>\n", "      <td>-1.171299</td>\n", "      <td>1.390256</td>\n", "      <td>-1.059742</td>\n", "      <td>-1.414347</td>\n", "      <td>-1.004107</td>\n", "      <td>-0.724719</td>\n", "      <td>-0.825430</td>\n", "      <td>-0.051357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.047024</td>\n", "      <td>-1.758546</td>\n", "      <td>-0.398812</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>-0.032131</td>\n", "      <td>-0.303098</td>\n", "      <td>0.031402</td>\n", "      <td>0.132581</td>\n", "      <td>...</td>\n", "      <td>-0.596105</td>\n", "      <td>-0.374579</td>\n", "      <td>-0.251352</td>\n", "      <td>1.213260</td>\n", "      <td>-1.533829</td>\n", "      <td>-1.908083</td>\n", "      <td>-0.771559</td>\n", "      <td>0.232856</td>\n", "      <td>0.162614</td>\n", "      <td>0.218318</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.047024</td>\n", "      <td>-2.310853</td>\n", "      <td>-0.016146</td>\n", "      <td>0.174800</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>0.073137</td>\n", "      <td>0.113362</td>\n", "      <td>1.555696</td>\n", "      <td>1.584417</td>\n", "      <td>...</td>\n", "      <td>-0.596568</td>\n", "      <td>-1.115351</td>\n", "      <td>-0.871187</td>\n", "      <td>1.282011</td>\n", "      <td>-2.018364</td>\n", "      <td>-2.444469</td>\n", "      <td>-0.725277</td>\n", "      <td>-1.084422</td>\n", "      <td>-1.092118</td>\n", "      <td>0.240877</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>0.359975</td>\n", "      <td>-0.001522</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>1.081403</td>\n", "      <td>1.163905</td>\n", "      <td>0.659935</td>\n", "      <td>0.672464</td>\n", "      <td>...</td>\n", "      <td>-0.596169</td>\n", "      <td>-0.737652</td>\n", "      <td>-0.557044</td>\n", "      <td>1.237662</td>\n", "      <td>-1.776481</td>\n", "      <td>-2.178452</td>\n", "      <td>-0.749798</td>\n", "      <td>-0.571760</td>\n", "      <td>-0.605777</td>\n", "      <td>0.225216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.047024</td>\n", "      <td>-2.886673</td>\n", "      <td>0.001864</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>0.460710</td>\n", "      <td>0.496163</td>\n", "      <td>2.488838</td>\n", "      <td>1.778529</td>\n", "      <td>...</td>\n", "      <td>-0.599290</td>\n", "      <td>-0.689330</td>\n", "      <td>-0.508536</td>\n", "      <td>1.174854</td>\n", "      <td>-1.727148</td>\n", "      <td>-2.081550</td>\n", "      <td>-0.681727</td>\n", "      <td>-0.438778</td>\n", "      <td>-0.476072</td>\n", "      <td>0.212390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>-102502.812500</td>\n", "      <td>0.107064</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>1.134007</td>\n", "      <td>1.178080</td>\n", "      <td>2.701693</td>\n", "      <td>2.789711</td>\n", "      <td>...</td>\n", "      <td>-0.598982</td>\n", "      <td>-0.075775</td>\n", "      <td>0.013475</td>\n", "      <td>1.149697</td>\n", "      <td>-1.324535</td>\n", "      <td>-1.635721</td>\n", "      <td>-0.714393</td>\n", "      <td>0.358498</td>\n", "      <td>0.287077</td>\n", "      <td>0.209127</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>18.994913</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>9.533707</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>1.716529</td>\n", "      <td>1.817681</td>\n", "      <td>4.423463</td>\n", "      <td>4.504025</td>\n", "      <td>...</td>\n", "      <td>-0.594820</td>\n", "      <td>-0.155044</td>\n", "      <td>-0.044248</td>\n", "      <td>1.088324</td>\n", "      <td>0.666121</td>\n", "      <td>0.642076</td>\n", "      <td>-0.691252</td>\n", "      <td>0.266572</td>\n", "      <td>0.202461</td>\n", "      <td>0.195407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>9.850588</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>3.076979</td>\n", "      <td>3.310745</td>\n", "      <td>0.495892</td>\n", "      <td>4.088521</td>\n", "      <td>...</td>\n", "      <td>-0.577825</td>\n", "      <td>0.260092</td>\n", "      <td>0.273230</td>\n", "      <td>0.997090</td>\n", "      <td>0.250998</td>\n", "      <td>0.162967</td>\n", "      <td>-0.729426</td>\n", "      <td>0.489518</td>\n", "      <td>0.407015</td>\n", "      <td>0.172065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>-0.047024</td>\n", "      <td>51.658638</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.208269</td>\n", "      <td>-5.065033</td>\n", "      <td>3.494771</td>\n", "      <td>2.631510</td>\n", "      <td>5.523672</td>\n", "      <td>1.529189</td>\n", "      <td>...</td>\n", "      <td>-0.578667</td>\n", "      <td>-0.172973</td>\n", "      <td>-0.102993</td>\n", "      <td>0.916004</td>\n", "      <td>0.319732</td>\n", "      <td>0.241524</td>\n", "      <td>-0.744778</td>\n", "      <td>0.232866</td>\n", "      <td>0.161784</td>\n", "      <td>0.154658</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.200512</td>\n", "      <td>-5.266833</td>\n", "      <td>-0.352180</td>\n", "      <td>2.255278</td>\n", "      <td>0.495892</td>\n", "      <td>4.088521</td>\n", "      <td>...</td>\n", "      <td>-0.578691</td>\n", "      <td>-0.169022</td>\n", "      <td>-0.108183</td>\n", "      <td>0.854994</td>\n", "      <td>0.426313</td>\n", "      <td>0.361010</td>\n", "      <td>-0.754080</td>\n", "      <td>-0.150738</td>\n", "      <td>-0.213566</td>\n", "      <td>0.146382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.386529</td>\n", "      <td>-5.398699</td>\n", "      <td>-4.548854</td>\n", "      <td>-5.645588</td>\n", "      <td>-0.610220</td>\n", "      <td>-2.565743</td>\n", "      <td>...</td>\n", "      <td>-0.573463</td>\n", "      <td>-0.302751</td>\n", "      <td>-0.318979</td>\n", "      <td>1.403440</td>\n", "      <td>-0.721188</td>\n", "      <td>-0.968144</td>\n", "      <td>-0.649752</td>\n", "      <td>0.028575</td>\n", "      <td>-0.078549</td>\n", "      <td>0.292086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.049349</td>\n", "      <td>-5.044770</td>\n", "      <td>-2.240683</td>\n", "      <td>-4.592139</td>\n", "      <td>8.247946</td>\n", "      <td>8.397640</td>\n", "      <td>...</td>\n", "      <td>-0.572500</td>\n", "      <td>-0.333290</td>\n", "      <td>-0.344316</td>\n", "      <td>1.404766</td>\n", "      <td>-0.726243</td>\n", "      <td>-0.973800</td>\n", "      <td>-0.649551</td>\n", "      <td>-0.089538</td>\n", "      <td>-0.190013</td>\n", "      <td>0.292688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>11.365624</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.253273</td>\n", "      <td>-5.248512</td>\n", "      <td>5.706836</td>\n", "      <td>6.139944</td>\n", "      <td>-0.006886</td>\n", "      <td>-0.006411</td>\n", "      <td>...</td>\n", "      <td>-0.324529</td>\n", "      <td>0.009100</td>\n", "      <td>-0.050120</td>\n", "      <td>1.203750</td>\n", "      <td>0.003260</td>\n", "      <td>-0.140807</td>\n", "      <td>-0.676441</td>\n", "      <td>-0.012561</td>\n", "      <td>-0.114175</td>\n", "      <td>0.247654</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-4.998129</td>\n", "      <td>-5.025348</td>\n", "      <td>-0.002457</td>\n", "      <td>-0.002112</td>\n", "      <td>-0.006886</td>\n", "      <td>-0.006411</td>\n", "      <td>...</td>\n", "      <td>-0.324529</td>\n", "      <td>0.009100</td>\n", "      <td>-0.050120</td>\n", "      <td>1.203750</td>\n", "      <td>0.003260</td>\n", "      <td>-0.140807</td>\n", "      <td>-0.676441</td>\n", "      <td>-0.012561</td>\n", "      <td>-0.114175</td>\n", "      <td>0.247654</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>0.174800</td>\n", "      <td>-4.793977</td>\n", "      <td>-4.794304</td>\n", "      <td>-0.002457</td>\n", "      <td>-2.635734</td>\n", "      <td>-0.006886</td>\n", "      <td>-0.006411</td>\n", "      <td>...</td>\n", "      <td>-0.220064</td>\n", "      <td>-0.793674</td>\n", "      <td>-0.754744</td>\n", "      <td>1.093816</td>\n", "      <td>-0.883640</td>\n", "      <td>-1.149076</td>\n", "      <td>-0.676037</td>\n", "      <td>-0.550309</td>\n", "      <td>-0.632988</td>\n", "      <td>0.235239</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>15 rows × 119 columns</p>\n", "</div>"], "text/plain": ["              0          1         2         3         4         5    \\\n", "0       -2.317502   0.820360 -0.221119 -0.389944 -0.229186 -0.228896   \n", "1       -0.047024  -1.758546 -0.398812 -0.017599 -5.357572 -5.352718   \n", "2       -0.047024  -2.310853 -0.016146  0.174800 -5.357572 -5.352718   \n", "3       -0.047024   0.005412  0.359975 -0.001522 -5.357572 -5.352718   \n", "4       -0.047024  -2.886673  0.001864 -0.017599 -5.357572 -5.352718   \n", "5  -102502.812500   0.107064 -0.016146 -0.017599 -5.357572 -5.352718   \n", "6       18.994913   0.005412 -0.016146  9.533707 -5.357572 -5.352718   \n", "7       -0.047024   0.005412  9.850588 -0.017599 -5.357572 -5.352718   \n", "8       -0.047024  51.658638 -0.016146 -0.017599 -5.208269 -5.065033   \n", "9       -0.047024   0.005412 -0.016146 -0.017599 -5.200512 -5.266833   \n", "10      -0.047024   0.005412 -0.016146 -0.017599 -5.386529 -5.398699   \n", "11      -0.047024   0.005412 -0.016146 -0.017599 -5.049349 -5.044770   \n", "12      11.365624   0.005412 -0.016146 -0.017599 -5.253273 -5.248512   \n", "13      -0.047024   0.005412 -0.016146 -0.017599 -4.998129 -5.025348   \n", "14      -0.047024   0.005412 -0.016146  0.174800 -4.793977 -4.794304   \n", "\n", "         6         7         8         9    ...       109       110       111  \\\n", "0  -0.212291  0.336496  0.160706  0.145598  ... -0.006579 -1.459517 -1.171299   \n", "1  -0.032131 -0.303098  0.031402  0.132581  ... -0.596105 -0.374579 -0.251352   \n", "2   0.073137  0.113362  1.555696  1.584417  ... -0.596568 -1.115351 -0.871187   \n", "3   1.081403  1.163905  0.659935  0.672464  ... -0.596169 -0.737652 -0.557044   \n", "4   0.460710  0.496163  2.488838  1.778529  ... -0.599290 -0.689330 -0.508536   \n", "5   1.134007  1.178080  2.701693  2.789711  ... -0.598982 -0.075775  0.013475   \n", "6   1.716529  1.817681  4.423463  4.504025  ... -0.594820 -0.155044 -0.044248   \n", "7   3.076979  3.310745  0.495892  4.088521  ... -0.577825  0.260092  0.273230   \n", "8   3.494771  2.631510  5.523672  1.529189  ... -0.578667 -0.172973 -0.102993   \n", "9  -0.352180  2.255278  0.495892  4.088521  ... -0.578691 -0.169022 -0.108183   \n", "10 -4.548854 -5.645588 -0.610220 -2.565743  ... -0.573463 -0.302751 -0.318979   \n", "11 -2.240683 -4.592139  8.247946  8.397640  ... -0.572500 -0.333290 -0.344316   \n", "12  5.706836  6.139944 -0.006886 -0.006411  ... -0.324529  0.009100 -0.050120   \n", "13 -0.002457 -0.002112 -0.006886 -0.006411  ... -0.324529  0.009100 -0.050120   \n", "14 -0.002457 -2.635734 -0.006886 -0.006411  ... -0.220064 -0.793674 -0.754744   \n", "\n", "         112       113       114       115       116       117       118  \n", "0   1.390256 -1.059742 -1.414347 -1.004107 -0.724719 -0.825430 -0.051357  \n", "1   1.213260 -1.533829 -1.908083 -0.771559  0.232856  0.162614  0.218318  \n", "2   1.282011 -2.018364 -2.444469 -0.725277 -1.084422 -1.092118  0.240877  \n", "3   1.237662 -1.776481 -2.178452 -0.749798 -0.571760 -0.605777  0.225216  \n", "4   1.174854 -1.727148 -2.081550 -0.681727 -0.438778 -0.476072  0.212390  \n", "5   1.149697 -1.324535 -1.635721 -0.714393  0.358498  0.287077  0.209127  \n", "6   1.088324  0.666121  0.642076 -0.691252  0.266572  0.202461  0.195407  \n", "7   0.997090  0.250998  0.162967 -0.729426  0.489518  0.407015  0.172065  \n", "8   0.916004  0.319732  0.241524 -0.744778  0.232866  0.161784  0.154658  \n", "9   0.854994  0.426313  0.361010 -0.754080 -0.150738 -0.213566  0.146382  \n", "10  1.403440 -0.721188 -0.968144 -0.649752  0.028575 -0.078549  0.292086  \n", "11  1.404766 -0.726243 -0.973800 -0.649551 -0.089538 -0.190013  0.292688  \n", "12  1.203750  0.003260 -0.140807 -0.676441 -0.012561 -0.114175  0.247654  \n", "13  1.203750  0.003260 -0.140807 -0.676441 -0.012561 -0.114175  0.247654  \n", "14  1.093816 -0.883640 -1.149076 -0.676037 -0.550309 -0.632988  0.235239  \n", "\n", "[15 rows x 119 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "import pandas as pd\n", "ndata = np.array(data['data'])\n", "ndata = ndata.reshape(15, 119)\n", "df = pd.DataFrame(ndata)\n", "df"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>109</th>\n", "      <th>110</th>\n", "      <th>111</th>\n", "      <th>112</th>\n", "      <th>113</th>\n", "      <th>114</th>\n", "      <th>115</th>\n", "      <th>116</th>\n", "      <th>117</th>\n", "      <th>118</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>...</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "      <td>15.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>-6831.685782</td>\n", "      <td>3.045246</td>\n", "      <td>0.628736</td>\n", "      <td>0.621057</td>\n", "      <td>-4.908148</td>\n", "      <td>-4.902762</td>\n", "      <td>0.623554</td>\n", "      <td>0.417633</td>\n", "      <td>1.743617</td>\n", "      <td>1.808415</td>\n", "      <td>...</td>\n", "      <td>-0.489252</td>\n", "      <td>-0.406711</td>\n", "      <td>-0.323095</td>\n", "      <td>1.174245</td>\n", "      <td>-0.673433</td>\n", "      <td>-0.908512</td>\n", "      <td>-0.726308</td>\n", "      <td>-0.135100</td>\n", "      <td>-0.208127</td>\n", "      <td>0.202560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>26466.620631</td>\n", "      <td>13.487990</td>\n", "      <td>2.555730</td>\n", "      <td>2.468692</td>\n", "      <td>1.305788</td>\n", "      <td>1.304760</td>\n", "      <td>2.389251</td>\n", "      <td>2.981765</td>\n", "      <td>2.531853</td>\n", "      <td>2.646450</td>\n", "      <td>...</td>\n", "      <td>0.182683</td>\n", "      <td>0.466089</td>\n", "      <td>0.386487</td>\n", "      <td>0.165977</td>\n", "      <td>0.896788</td>\n", "      <td>1.030356</td>\n", "      <td>0.085984</td>\n", "      <td>0.449540</td>\n", "      <td>0.437054</td>\n", "      <td>0.082179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>-102502.812500</td>\n", "      <td>-2.886673</td>\n", "      <td>-0.398812</td>\n", "      <td>-0.389944</td>\n", "      <td>-5.386529</td>\n", "      <td>-5.398699</td>\n", "      <td>-4.548854</td>\n", "      <td>-5.645588</td>\n", "      <td>-0.610220</td>\n", "      <td>-2.565743</td>\n", "      <td>...</td>\n", "      <td>-0.599290</td>\n", "      <td>-1.459517</td>\n", "      <td>-1.171299</td>\n", "      <td>0.854994</td>\n", "      <td>-2.018364</td>\n", "      <td>-2.444469</td>\n", "      <td>-1.004107</td>\n", "      <td>-1.084422</td>\n", "      <td>-1.092118</td>\n", "      <td>-0.051357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>-0.122211</td>\n", "      <td>-0.152605</td>\n", "      <td>0.012258</td>\n", "      <td>0.063085</td>\n", "      <td>...</td>\n", "      <td>-0.596137</td>\n", "      <td>-0.713491</td>\n", "      <td>-0.532790</td>\n", "      <td>1.091070</td>\n", "      <td>-1.429182</td>\n", "      <td>-1.771902</td>\n", "      <td>-0.747288</td>\n", "      <td>-0.494544</td>\n", "      <td>-0.540924</td>\n", "      <td>0.183736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.017599</td>\n", "      <td>-5.357572</td>\n", "      <td>-5.352718</td>\n", "      <td>0.073137</td>\n", "      <td>0.496163</td>\n", "      <td>0.495892</td>\n", "      <td>1.529189</td>\n", "      <td>...</td>\n", "      <td>-0.578667</td>\n", "      <td>-0.302751</td>\n", "      <td>-0.251352</td>\n", "      <td>1.203750</td>\n", "      <td>-0.726243</td>\n", "      <td>-0.973800</td>\n", "      <td>-0.714393</td>\n", "      <td>-0.012561</td>\n", "      <td>-0.114175</td>\n", "      <td>0.218318</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>-0.047024</td>\n", "      <td>0.005412</td>\n", "      <td>-0.016146</td>\n", "      <td>-0.009560</td>\n", "      <td>-5.124931</td>\n", "      <td>-5.054902</td>\n", "      <td>1.425268</td>\n", "      <td>2.036480</td>\n", "      <td>2.595266</td>\n", "      <td>3.439116</td>\n", "      <td>...</td>\n", "      <td>-0.448515</td>\n", "      <td>-0.115410</td>\n", "      <td>-0.050120</td>\n", "      <td>1.259837</td>\n", "      <td>0.127129</td>\n", "      <td>0.011080</td>\n", "      <td>-0.676441</td>\n", "      <td>0.232861</td>\n", "      <td>0.162199</td>\n", "      <td>0.244266</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>18.994913</td>\n", "      <td>51.658638</td>\n", "      <td>9.850588</td>\n", "      <td>9.533707</td>\n", "      <td>-0.229186</td>\n", "      <td>-0.228896</td>\n", "      <td>5.706836</td>\n", "      <td>6.139944</td>\n", "      <td>8.247946</td>\n", "      <td>8.397640</td>\n", "      <td>...</td>\n", "      <td>-0.006579</td>\n", "      <td>0.260092</td>\n", "      <td>0.273230</td>\n", "      <td>1.404766</td>\n", "      <td>0.666121</td>\n", "      <td>0.642076</td>\n", "      <td>-0.649551</td>\n", "      <td>0.489518</td>\n", "      <td>0.407015</td>\n", "      <td>0.292688</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 119 columns</p>\n", "</div>"], "text/plain": ["                 0          1          2          3          4          5    \\\n", "count      15.000000  15.000000  15.000000  15.000000  15.000000  15.000000   \n", "mean    -6831.685782   3.045246   0.628736   0.621057  -4.908148  -4.902762   \n", "std     26466.620631  13.487990   2.555730   2.468692   1.305788   1.304760   \n", "min   -102502.812500  -2.886673  -0.398812  -0.389944  -5.386529  -5.398699   \n", "25%        -0.047024   0.005412  -0.016146  -0.017599  -5.357572  -5.352718   \n", "50%        -0.047024   0.005412  -0.016146  -0.017599  -5.357572  -5.352718   \n", "75%        -0.047024   0.005412  -0.016146  -0.009560  -5.124931  -5.054902   \n", "max        18.994913  51.658638   9.850588   9.533707  -0.229186  -0.228896   \n", "\n", "             6          7          8          9    ...        109        110  \\\n", "count  15.000000  15.000000  15.000000  15.000000  ...  15.000000  15.000000   \n", "mean    0.623554   0.417633   1.743617   1.808415  ...  -0.489252  -0.406711   \n", "std     2.389251   2.981765   2.531853   2.646450  ...   0.182683   0.466089   \n", "min    -4.548854  -5.645588  -0.610220  -2.565743  ...  -0.599290  -1.459517   \n", "25%    -0.122211  -0.152605   0.012258   0.063085  ...  -0.596137  -0.713491   \n", "50%     0.073137   0.496163   0.495892   1.529189  ...  -0.578667  -0.302751   \n", "75%     1.425268   2.036480   2.595266   3.439116  ...  -0.448515  -0.115410   \n", "max     5.706836   6.139944   8.247946   8.397640  ...  -0.006579   0.260092   \n", "\n", "             111        112        113        114        115        116  \\\n", "count  15.000000  15.000000  15.000000  15.000000  15.000000  15.000000   \n", "mean   -0.323095   1.174245  -0.673433  -0.908512  -0.726308  -0.135100   \n", "std     0.386487   0.165977   0.896788   1.030356   0.085984   0.449540   \n", "min    -1.171299   0.854994  -2.018364  -2.444469  -1.004107  -1.084422   \n", "25%    -0.532790   1.091070  -1.429182  -1.771902  -0.747288  -0.494544   \n", "50%    -0.251352   1.203750  -0.726243  -0.973800  -0.714393  -0.012561   \n", "75%    -0.050120   1.259837   0.127129   0.011080  -0.676441   0.232861   \n", "max     0.273230   1.404766   0.666121   0.642076  -0.649551   0.489518   \n", "\n", "             117        118  \n", "count  15.000000  15.000000  \n", "mean   -0.208127   0.202560  \n", "std     0.437054   0.082179  \n", "min    -1.092118  -0.051357  \n", "25%    -0.540924   0.183736  \n", "50%    -0.114175   0.218318  \n", "75%     0.162199   0.244266  \n", "max     0.407015   0.292688  \n", "\n", "[8 rows x 119 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["df.to_csv(r\"E:\\lab\\RoboQuant\\bin\\x64\\Release\\data.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["51\n", "[7, 43, 44, 45, 51, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 74, 80, 81, 82, 83, 84, 88, 89, 90, 91, 92, 93, 96, 97, 98, 100, 101, 102, 105, 106]\n", "38\n", "51\n"]}], "source": ["sf_sel_index = [8, 44, 45, 46, 52, 53, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 85, 86, 87, 88, 89, 90, 91, 92, 96, 97, 98, 99, 100, 101, 102, 105, 106, 107, 108, 109, 112, 113, 114, 115, 116, 117, 120, 121]\n", "print(len(sf_sel_index))\n", "fast = [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 1, 1, 1, 2, 1, 1, 0, 0, 2, 2, 1, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0]\n", "# fast 中不等于0的index\n", "fast_index = [i for i, x in enumerate(fast) if x != 0]\n", "print(fast_index)\n", "print(len(fast_index))\n", "print(sum(fast))\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>OPEN_2</th>\n", "      <th>HIGH_2</th>\n", "      <th>LOW_2</th>\n", "      <th>CLOSE_2</th>\n", "      <th>VOLUME_1</th>\n", "      <th>VOLUME_2</th>\n", "      <th>TYPICAL_PRICE_2</th>\n", "      <th>NEW_2</th>\n", "      <th>NEW_CHANGE_PERCENT_2</th>\n", "      <th>...</th>\n", "      <th>TREND_INPOSR_2</th>\n", "      <th>TREND_HIGHEST_2</th>\n", "      <th>TREND_LOWEST_2</th>\n", "      <th>TREND_HLR_2</th>\n", "      <th>TREND_LEVEL_2</th>\n", "      <th>HYO_TENKAN_SEN_2</th>\n", "      <th>HYO_KIJUN_SEN_2</th>\n", "      <th>HYO_CROSS_BARS_2</th>\n", "      <th>TATR_2</th>\n", "      <th>NATR_TL_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>153.715697</td>\n", "      <td>40.865894</td>\n", "      <td>0.0</td>\n", "      <td>153.738342</td>\n", "      <td>891.389716</td>\n", "      <td>0.0</td>\n", "      <td>153.728190</td>\n", "      <td>153.738342</td>\n", "      <td>0.739985</td>\n", "      <td>...</td>\n", "      <td>0.883221</td>\n", "      <td>251.902988</td>\n", "      <td>250.257969</td>\n", "      <td>3.479232</td>\n", "      <td>1.525810</td>\n", "      <td>153.823649</td>\n", "      <td>153.987173</td>\n", "      <td>24.219806</td>\n", "      <td>1.243634</td>\n", "      <td>2.208027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AG</td>\n", "      <td>428.187998</td>\n", "      <td>379.598023</td>\n", "      <td>0.0</td>\n", "      <td>428.659456</td>\n", "      <td>734.809479</td>\n", "      <td>0.0</td>\n", "      <td>428.534835</td>\n", "      <td>428.659456</td>\n", "      <td>1.149094</td>\n", "      <td>...</td>\n", "      <td>0.881970</td>\n", "      <td>435.666008</td>\n", "      <td>418.008938</td>\n", "      <td>4.852349</td>\n", "      <td>1.534000</td>\n", "      <td>428.480137</td>\n", "      <td>427.816833</td>\n", "      <td>24.526022</td>\n", "      <td>3.585892</td>\n", "      <td>2.325591</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AL</td>\n", "      <td>529.744286</td>\n", "      <td>372.805783</td>\n", "      <td>0.0</td>\n", "      <td>528.573067</td>\n", "      <td>852.191229</td>\n", "      <td>0.0</td>\n", "      <td>529.049976</td>\n", "      <td>528.573067</td>\n", "      <td>0.677505</td>\n", "      <td>...</td>\n", "      <td>0.888147</td>\n", "      <td>1110.335378</td>\n", "      <td>1082.125270</td>\n", "      <td>3.630985</td>\n", "      <td>1.586104</td>\n", "      <td>528.097366</td>\n", "      <td>526.923655</td>\n", "      <td>22.517041</td>\n", "      <td>7.032230</td>\n", "      <td>2.243600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AO</td>\n", "      <td>149.525851</td>\n", "      <td>102.122573</td>\n", "      <td>0.0</td>\n", "      <td>150.092116</td>\n", "      <td>914.468795</td>\n", "      <td>0.0</td>\n", "      <td>149.806039</td>\n", "      <td>150.092116</td>\n", "      <td>1.572361</td>\n", "      <td>...</td>\n", "      <td>0.898594</td>\n", "      <td>228.388607</td>\n", "      <td>220.978514</td>\n", "      <td>3.621714</td>\n", "      <td>1.564056</td>\n", "      <td>149.900682</td>\n", "      <td>149.371765</td>\n", "      <td>23.253783</td>\n", "      <td>3.506930</td>\n", "      <td>2.404023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AP</td>\n", "      <td>321.975033</td>\n", "      <td>118.441066</td>\n", "      <td>0.0</td>\n", "      <td>321.476500</td>\n", "      <td>1010.986162</td>\n", "      <td>0.0</td>\n", "      <td>321.811985</td>\n", "      <td>321.476500</td>\n", "      <td>1.312924</td>\n", "      <td>...</td>\n", "      <td>0.881069</td>\n", "      <td>659.238385</td>\n", "      <td>651.997142</td>\n", "      <td>5.514550</td>\n", "      <td>1.510454</td>\n", "      <td>321.168877</td>\n", "      <td>320.900646</td>\n", "      <td>23.630814</td>\n", "      <td>3.836375</td>\n", "      <td>2.283148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>AU</td>\n", "      <td>25.976799</td>\n", "      <td>28.099911</td>\n", "      <td>0.0</td>\n", "      <td>25.970622</td>\n", "      <td>804.851351</td>\n", "      <td>0.0</td>\n", "      <td>25.974130</td>\n", "      <td>25.970622</td>\n", "      <td>0.703518</td>\n", "      <td>...</td>\n", "      <td>0.873935</td>\n", "      <td>26.310914</td>\n", "      <td>25.555209</td>\n", "      <td>4.733790</td>\n", "      <td>1.619906</td>\n", "      <td>25.966930</td>\n", "      <td>25.944533</td>\n", "      <td>24.732088</td>\n", "      <td>0.192574</td>\n", "      <td>2.347244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>B</td>\n", "      <td>283.612144</td>\n", "      <td>56.035159</td>\n", "      <td>0.0</td>\n", "      <td>283.355605</td>\n", "      <td>844.571363</td>\n", "      <td>0.0</td>\n", "      <td>283.417636</td>\n", "      <td>283.355605</td>\n", "      <td>1.232316</td>\n", "      <td>...</td>\n", "      <td>0.879491</td>\n", "      <td>355.686006</td>\n", "      <td>352.166971</td>\n", "      <td>4.720953</td>\n", "      <td>1.502948</td>\n", "      <td>283.579169</td>\n", "      <td>283.947221</td>\n", "      <td>25.831949</td>\n", "      <td>1.486501</td>\n", "      <td>2.198008</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>BR</td>\n", "      <td>744.880981</td>\n", "      <td>637.690119</td>\n", "      <td>0.0</td>\n", "      <td>742.998923</td>\n", "      <td>1027.294940</td>\n", "      <td>0.0</td>\n", "      <td>742.977106</td>\n", "      <td>742.998923</td>\n", "      <td>1.962616</td>\n", "      <td>...</td>\n", "      <td>0.887934</td>\n", "      <td>791.747548</td>\n", "      <td>764.814023</td>\n", "      <td>3.040759</td>\n", "      <td>1.594504</td>\n", "      <td>743.019725</td>\n", "      <td>742.629963</td>\n", "      <td>20.781933</td>\n", "      <td>13.875760</td>\n", "      <td>2.316005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>BU</td>\n", "      <td>84.334736</td>\n", "      <td>11.909699</td>\n", "      <td>0.0</td>\n", "      <td>84.279174</td>\n", "      <td>864.135817</td>\n", "      <td>0.0</td>\n", "      <td>84.236507</td>\n", "      <td>84.279174</td>\n", "      <td>0.905332</td>\n", "      <td>...</td>\n", "      <td>0.890099</td>\n", "      <td>203.075480</td>\n", "      <td>201.162305</td>\n", "      <td>3.722803</td>\n", "      <td>1.467588</td>\n", "      <td>84.160712</td>\n", "      <td>83.821294</td>\n", "      <td>21.302324</td>\n", "      <td>1.235406</td>\n", "      <td>2.224454</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>91.931986</td>\n", "      <td>2.981677</td>\n", "      <td>0.0</td>\n", "      <td>79.517789</td>\n", "      <td>952.184079</td>\n", "      <td>0.0</td>\n", "      <td>91.790827</td>\n", "      <td>79.517789</td>\n", "      <td>0.657374</td>\n", "      <td>...</td>\n", "      <td>0.893933</td>\n", "      <td>180.712217</td>\n", "      <td>180.087408</td>\n", "      <td>3.357285</td>\n", "      <td>1.530068</td>\n", "      <td>79.456307</td>\n", "      <td>79.293899</td>\n", "      <td>22.791973</td>\n", "      <td>0.594557</td>\n", "      <td>2.222150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>CF</td>\n", "      <td>678.024116</td>\n", "      <td>796.325375</td>\n", "      <td>0.0</td>\n", "      <td>675.752463</td>\n", "      <td>897.127281</td>\n", "      <td>0.0</td>\n", "      <td>675.883837</td>\n", "      <td>675.752463</td>\n", "      <td>0.771951</td>\n", "      <td>...</td>\n", "      <td>0.924737</td>\n", "      <td>863.767623</td>\n", "      <td>852.453254</td>\n", "      <td>4.040311</td>\n", "      <td>1.478430</td>\n", "      <td>675.508175</td>\n", "      <td>675.485624</td>\n", "      <td>25.516096</td>\n", "      <td>10.613887</td>\n", "      <td>2.428995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CJ</td>\n", "      <td>1300.600563</td>\n", "      <td>866.948466</td>\n", "      <td>0.0</td>\n", "      <td>1300.011939</td>\n", "      <td>1037.048887</td>\n", "      <td>0.0</td>\n", "      <td>1300.273210</td>\n", "      <td>1300.011939</td>\n", "      <td>2.176575</td>\n", "      <td>...</td>\n", "      <td>0.888533</td>\n", "      <td>1604.001582</td>\n", "      <td>1575.134025</td>\n", "      <td>4.575478</td>\n", "      <td>1.571908</td>\n", "      <td>1300.604927</td>\n", "      <td>1302.914807</td>\n", "      <td>22.761372</td>\n", "      <td>10.615168</td>\n", "      <td>2.319936</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CS</td>\n", "      <td>80.011222</td>\n", "      <td>27.005282</td>\n", "      <td>0.0</td>\n", "      <td>59.203404</td>\n", "      <td>939.879822</td>\n", "      <td>0.0</td>\n", "      <td>79.835591</td>\n", "      <td>59.203404</td>\n", "      <td>0.737902</td>\n", "      <td>...</td>\n", "      <td>0.901941</td>\n", "      <td>181.022727</td>\n", "      <td>179.815094</td>\n", "      <td>3.588245</td>\n", "      <td>1.550345</td>\n", "      <td>59.109280</td>\n", "      <td>58.904169</td>\n", "      <td>21.928227</td>\n", "      <td>0.670442</td>\n", "      <td>2.221781</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>CU</td>\n", "      <td>2644.263863</td>\n", "      <td>2004.671935</td>\n", "      <td>0.0</td>\n", "      <td>2645.353568</td>\n", "      <td>786.832108</td>\n", "      <td>0.0</td>\n", "      <td>2644.912354</td>\n", "      <td>2645.353568</td>\n", "      <td>0.665430</td>\n", "      <td>...</td>\n", "      <td>0.880258</td>\n", "      <td>4806.421696</td>\n", "      <td>4678.133379</td>\n", "      <td>4.046517</td>\n", "      <td>1.577063</td>\n", "      <td>2642.617613</td>\n", "      <td>2632.441609</td>\n", "      <td>23.005241</td>\n", "      <td>31.476742</td>\n", "      <td>2.254760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>CY</td>\n", "      <td>762.370447</td>\n", "      <td>366.286647</td>\n", "      <td>0.0</td>\n", "      <td>762.852291</td>\n", "      <td>1082.264647</td>\n", "      <td>0.0</td>\n", "      <td>762.300229</td>\n", "      <td>762.852291</td>\n", "      <td>0.852304</td>\n", "      <td>...</td>\n", "      <td>0.908019</td>\n", "      <td>1333.547861</td>\n", "      <td>1327.376644</td>\n", "      <td>3.636988</td>\n", "      <td>1.637128</td>\n", "      <td>762.059072</td>\n", "      <td>760.606197</td>\n", "      <td>22.921603</td>\n", "      <td>10.710480</td>\n", "      <td>2.248423</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>EB</td>\n", "      <td>444.719526</td>\n", "      <td>219.381110</td>\n", "      <td>0.0</td>\n", "      <td>444.568828</td>\n", "      <td>861.268598</td>\n", "      <td>0.0</td>\n", "      <td>444.571531</td>\n", "      <td>444.568828</td>\n", "      <td>1.002224</td>\n", "      <td>...</td>\n", "      <td>0.884098</td>\n", "      <td>552.107325</td>\n", "      <td>550.715337</td>\n", "      <td>3.537982</td>\n", "      <td>1.549917</td>\n", "      <td>444.615611</td>\n", "      <td>444.335849</td>\n", "      <td>21.034236</td>\n", "      <td>2.908307</td>\n", "      <td>2.214301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>EG</td>\n", "      <td>157.898411</td>\n", "      <td>46.866077</td>\n", "      <td>0.0</td>\n", "      <td>114.902113</td>\n", "      <td>951.429586</td>\n", "      <td>0.0</td>\n", "      <td>157.746808</td>\n", "      <td>114.902113</td>\n", "      <td>0.915961</td>\n", "      <td>...</td>\n", "      <td>0.889307</td>\n", "      <td>295.702884</td>\n", "      <td>287.253999</td>\n", "      <td>3.499431</td>\n", "      <td>1.508835</td>\n", "      <td>114.748182</td>\n", "      <td>114.489767</td>\n", "      <td>21.809272</td>\n", "      <td>2.163555</td>\n", "      <td>2.185766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>FG</td>\n", "      <td>127.340082</td>\n", "      <td>41.106055</td>\n", "      <td>0.0</td>\n", "      <td>127.465512</td>\n", "      <td>967.242201</td>\n", "      <td>0.0</td>\n", "      <td>127.458134</td>\n", "      <td>127.465512</td>\n", "      <td>1.727777</td>\n", "      <td>...</td>\n", "      <td>0.989120</td>\n", "      <td>141.479595</td>\n", "      <td>138.488585</td>\n", "      <td>4.135170</td>\n", "      <td>1.338805</td>\n", "      <td>127.455573</td>\n", "      <td>127.413796</td>\n", "      <td>34.174820</td>\n", "      <td>1.829442</td>\n", "      <td>2.383901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>HC</td>\n", "      <td>178.537833</td>\n", "      <td>45.357537</td>\n", "      <td>0.0</td>\n", "      <td>166.355698</td>\n", "      <td>995.704968</td>\n", "      <td>0.0</td>\n", "      <td>178.486899</td>\n", "      <td>166.355698</td>\n", "      <td>0.717399</td>\n", "      <td>...</td>\n", "      <td>0.877668</td>\n", "      <td>277.307629</td>\n", "      <td>278.072445</td>\n", "      <td>3.449578</td>\n", "      <td>1.496881</td>\n", "      <td>166.341008</td>\n", "      <td>166.285691</td>\n", "      <td>22.959858</td>\n", "      <td>1.156448</td>\n", "      <td>2.155734</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>I</td>\n", "      <td>73.816998</td>\n", "      <td>17.034352</td>\n", "      <td>0.0</td>\n", "      <td>73.770474</td>\n", "      <td>1142.483239</td>\n", "      <td>0.0</td>\n", "      <td>73.773927</td>\n", "      <td>73.770474</td>\n", "      <td>1.588406</td>\n", "      <td>...</td>\n", "      <td>0.886878</td>\n", "      <td>81.181991</td>\n", "      <td>82.192047</td>\n", "      <td>3.775861</td>\n", "      <td>1.563288</td>\n", "      <td>73.758487</td>\n", "      <td>73.734271</td>\n", "      <td>22.465634</td>\n", "      <td>0.351340</td>\n", "      <td>2.141335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>J</td>\n", "      <td>211.853278</td>\n", "      <td>29.631343</td>\n", "      <td>0.0</td>\n", "      <td>207.345485</td>\n", "      <td>947.973366</td>\n", "      <td>0.0</td>\n", "      <td>211.885023</td>\n", "      <td>207.345485</td>\n", "      <td>1.583431</td>\n", "      <td>...</td>\n", "      <td>0.875539</td>\n", "      <td>255.272598</td>\n", "      <td>253.555888</td>\n", "      <td>3.516608</td>\n", "      <td>1.515027</td>\n", "      <td>207.363560</td>\n", "      <td>207.316178</td>\n", "      <td>20.820747</td>\n", "      <td>1.090939</td>\n", "      <td>2.200265</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>JD</td>\n", "      <td>198.826307</td>\n", "      <td>47.769705</td>\n", "      <td>0.0</td>\n", "      <td>198.988729</td>\n", "      <td>959.865584</td>\n", "      <td>0.0</td>\n", "      <td>198.928715</td>\n", "      <td>198.988729</td>\n", "      <td>1.849810</td>\n", "      <td>...</td>\n", "      <td>0.874591</td>\n", "      <td>422.236997</td>\n", "      <td>416.904339</td>\n", "      <td>5.310997</td>\n", "      <td>1.463306</td>\n", "      <td>198.815378</td>\n", "      <td>198.234519</td>\n", "      <td>23.390232</td>\n", "      <td>1.468906</td>\n", "      <td>2.284630</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>JM</td>\n", "      <td>180.879445</td>\n", "      <td>35.609776</td>\n", "      <td>0.0</td>\n", "      <td>176.976147</td>\n", "      <td>1070.755431</td>\n", "      <td>0.0</td>\n", "      <td>180.790023</td>\n", "      <td>176.976147</td>\n", "      <td>2.000890</td>\n", "      <td>...</td>\n", "      <td>0.877398</td>\n", "      <td>202.973321</td>\n", "      <td>201.318662</td>\n", "      <td>3.552529</td>\n", "      <td>1.486621</td>\n", "      <td>176.957228</td>\n", "      <td>176.872043</td>\n", "      <td>22.784356</td>\n", "      <td>1.138325</td>\n", "      <td>2.186285</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>L</td>\n", "      <td>161.945221</td>\n", "      <td>39.531823</td>\n", "      <td>0.0</td>\n", "      <td>161.931666</td>\n", "      <td>891.796214</td>\n", "      <td>0.0</td>\n", "      <td>161.904961</td>\n", "      <td>161.931666</td>\n", "      <td>0.501635</td>\n", "      <td>...</td>\n", "      <td>0.876746</td>\n", "      <td>456.815511</td>\n", "      <td>451.619522</td>\n", "      <td>3.593754</td>\n", "      <td>1.525295</td>\n", "      <td>161.757342</td>\n", "      <td>161.185997</td>\n", "      <td>22.678737</td>\n", "      <td>1.319712</td>\n", "      <td>2.157539</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>LH</td>\n", "      <td>1252.784385</td>\n", "      <td>424.518207</td>\n", "      <td>0.0</td>\n", "      <td>1251.996392</td>\n", "      <td>1005.888431</td>\n", "      <td>0.0</td>\n", "      <td>1251.935441</td>\n", "      <td>1251.996392</td>\n", "      <td>2.789944</td>\n", "      <td>...</td>\n", "      <td>0.880639</td>\n", "      <td>1642.837484</td>\n", "      <td>1616.936473</td>\n", "      <td>4.200303</td>\n", "      <td>1.520949</td>\n", "      <td>1251.501714</td>\n", "      <td>1250.550532</td>\n", "      <td>22.674448</td>\n", "      <td>9.519212</td>\n", "      <td>2.244861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>M</td>\n", "      <td>200.902023</td>\n", "      <td>128.130746</td>\n", "      <td>0.0</td>\n", "      <td>199.371369</td>\n", "      <td>849.619419</td>\n", "      <td>0.0</td>\n", "      <td>200.728221</td>\n", "      <td>199.371369</td>\n", "      <td>1.201508</td>\n", "      <td>...</td>\n", "      <td>0.877187</td>\n", "      <td>309.101642</td>\n", "      <td>304.208010</td>\n", "      <td>4.830268</td>\n", "      <td>1.532672</td>\n", "      <td>199.251571</td>\n", "      <td>198.848755</td>\n", "      <td>24.897128</td>\n", "      <td>1.178550</td>\n", "      <td>2.210609</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MA</td>\n", "      <td>121.942399</td>\n", "      <td>15.177241</td>\n", "      <td>0.0</td>\n", "      <td>121.963591</td>\n", "      <td>904.381201</td>\n", "      <td>0.0</td>\n", "      <td>121.764095</td>\n", "      <td>121.963591</td>\n", "      <td>0.987990</td>\n", "      <td>...</td>\n", "      <td>0.830231</td>\n", "      <td>172.294236</td>\n", "      <td>172.054437</td>\n", "      <td>4.339641</td>\n", "      <td>1.509761</td>\n", "      <td>121.751888</td>\n", "      <td>121.698883</td>\n", "      <td>31.489820</td>\n", "      <td>2.741020</td>\n", "      <td>2.283351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>NI</td>\n", "      <td>15283.134020</td>\n", "      <td>267.687597</td>\n", "      <td>0.0</td>\n", "      <td>15286.697439</td>\n", "      <td>905.677673</td>\n", "      <td>0.0</td>\n", "      <td>15285.714778</td>\n", "      <td>15286.697439</td>\n", "      <td>1.489630</td>\n", "      <td>...</td>\n", "      <td>0.863975</td>\n", "      <td>17259.328703</td>\n", "      <td>16758.337274</td>\n", "      <td>3.930435</td>\n", "      <td>1.618626</td>\n", "      <td>15286.029769</td>\n", "      <td>15285.274151</td>\n", "      <td>24.553080</td>\n", "      <td>108.435861</td>\n", "      <td>2.227547</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>NR</td>\n", "      <td>592.623642</td>\n", "      <td>556.261479</td>\n", "      <td>0.0</td>\n", "      <td>519.513711</td>\n", "      <td>1119.092071</td>\n", "      <td>0.0</td>\n", "      <td>592.459178</td>\n", "      <td>519.513711</td>\n", "      <td>1.224042</td>\n", "      <td>...</td>\n", "      <td>0.896911</td>\n", "      <td>974.071994</td>\n", "      <td>946.517816</td>\n", "      <td>3.520977</td>\n", "      <td>1.592493</td>\n", "      <td>519.062799</td>\n", "      <td>518.339239</td>\n", "      <td>21.451227</td>\n", "      <td>8.502974</td>\n", "      <td>2.283049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>OI</td>\n", "      <td>489.906569</td>\n", "      <td>2.090021</td>\n", "      <td>0.0</td>\n", "      <td>466.542003</td>\n", "      <td>848.414421</td>\n", "      <td>0.0</td>\n", "      <td>489.166070</td>\n", "      <td>466.542003</td>\n", "      <td>1.002468</td>\n", "      <td>...</td>\n", "      <td>0.871939</td>\n", "      <td>636.067996</td>\n", "      <td>626.324229</td>\n", "      <td>4.162384</td>\n", "      <td>1.601702</td>\n", "      <td>466.355960</td>\n", "      <td>466.152895</td>\n", "      <td>23.985989</td>\n", "      <td>5.594898</td>\n", "      <td>2.292178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>P</td>\n", "      <td>436.135807</td>\n", "      <td>48.604603</td>\n", "      <td>0.0</td>\n", "      <td>424.036384</td>\n", "      <td>860.402726</td>\n", "      <td>0.0</td>\n", "      <td>435.904667</td>\n", "      <td>424.036384</td>\n", "      <td>1.143877</td>\n", "      <td>...</td>\n", "      <td>0.878059</td>\n", "      <td>615.594642</td>\n", "      <td>602.969872</td>\n", "      <td>3.681762</td>\n", "      <td>1.565025</td>\n", "      <td>423.949861</td>\n", "      <td>423.808226</td>\n", "      <td>21.532007</td>\n", "      <td>3.078591</td>\n", "      <td>2.178333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>PB</td>\n", "      <td>462.674159</td>\n", "      <td>329.637799</td>\n", "      <td>0.0</td>\n", "      <td>462.871196</td>\n", "      <td>871.241260</td>\n", "      <td>0.0</td>\n", "      <td>462.758896</td>\n", "      <td>462.871196</td>\n", "      <td>0.676215</td>\n", "      <td>...</td>\n", "      <td>0.899375</td>\n", "      <td>881.193066</td>\n", "      <td>858.324673</td>\n", "      <td>3.388717</td>\n", "      <td>1.500195</td>\n", "      <td>462.609151</td>\n", "      <td>461.778532</td>\n", "      <td>20.563199</td>\n", "      <td>6.415495</td>\n", "      <td>2.286301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>PF</td>\n", "      <td>223.695001</td>\n", "      <td>91.501558</td>\n", "      <td>0.0</td>\n", "      <td>157.562197</td>\n", "      <td>822.622346</td>\n", "      <td>0.0</td>\n", "      <td>223.995564</td>\n", "      <td>157.562197</td>\n", "      <td>0.673831</td>\n", "      <td>...</td>\n", "      <td>0.877775</td>\n", "      <td>453.150714</td>\n", "      <td>448.853825</td>\n", "      <td>3.555603</td>\n", "      <td>1.435327</td>\n", "      <td>157.311231</td>\n", "      <td>157.033191</td>\n", "      <td>23.558129</td>\n", "      <td>3.406433</td>\n", "      <td>2.396588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>PG</td>\n", "      <td>391.701482</td>\n", "      <td>193.748697</td>\n", "      <td>0.0</td>\n", "      <td>391.085128</td>\n", "      <td>843.762861</td>\n", "      <td>0.0</td>\n", "      <td>391.981733</td>\n", "      <td>391.085128</td>\n", "      <td>2.167512</td>\n", "      <td>...</td>\n", "      <td>0.886446</td>\n", "      <td>482.072995</td>\n", "      <td>474.672493</td>\n", "      <td>5.693458</td>\n", "      <td>1.522321</td>\n", "      <td>391.131864</td>\n", "      <td>391.063239</td>\n", "      <td>23.960871</td>\n", "      <td>2.398655</td>\n", "      <td>2.219903</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>PK</td>\n", "      <td>451.207658</td>\n", "      <td>128.904562</td>\n", "      <td>0.0</td>\n", "      <td>451.302429</td>\n", "      <td>1018.537754</td>\n", "      <td>0.0</td>\n", "      <td>451.267606</td>\n", "      <td>451.302429</td>\n", "      <td>1.166134</td>\n", "      <td>...</td>\n", "      <td>0.882131</td>\n", "      <td>671.390526</td>\n", "      <td>660.699797</td>\n", "      <td>4.449283</td>\n", "      <td>1.561057</td>\n", "      <td>451.377394</td>\n", "      <td>451.580375</td>\n", "      <td>22.033021</td>\n", "      <td>3.293030</td>\n", "      <td>2.241551</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>PP</td>\n", "      <td>204.957342</td>\n", "      <td>43.814834</td>\n", "      <td>0.0</td>\n", "      <td>204.852121</td>\n", "      <td>870.230218</td>\n", "      <td>0.0</td>\n", "      <td>204.848384</td>\n", "      <td>204.852121</td>\n", "      <td>0.530957</td>\n", "      <td>...</td>\n", "      <td>0.876340</td>\n", "      <td>419.454662</td>\n", "      <td>416.917307</td>\n", "      <td>3.496641</td>\n", "      <td>1.517179</td>\n", "      <td>204.736040</td>\n", "      <td>204.380298</td>\n", "      <td>22.790149</td>\n", "      <td>1.298102</td>\n", "      <td>2.159970</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>PX</td>\n", "      <td>210.269290</td>\n", "      <td>5.739357</td>\n", "      <td>0.0</td>\n", "      <td>210.499727</td>\n", "      <td>815.466244</td>\n", "      <td>0.0</td>\n", "      <td>210.701673</td>\n", "      <td>210.499727</td>\n", "      <td>0.837208</td>\n", "      <td>...</td>\n", "      <td>0.981002</td>\n", "      <td>287.663484</td>\n", "      <td>279.168665</td>\n", "      <td>3.639823</td>\n", "      <td>1.367837</td>\n", "      <td>210.499601</td>\n", "      <td>210.776948</td>\n", "      <td>23.720027</td>\n", "      <td>3.892507</td>\n", "      <td>2.460069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>RB</td>\n", "      <td>191.956019</td>\n", "      <td>34.904157</td>\n", "      <td>0.0</td>\n", "      <td>186.681492</td>\n", "      <td>987.075657</td>\n", "      <td>0.0</td>\n", "      <td>191.959624</td>\n", "      <td>186.681492</td>\n", "      <td>0.774028</td>\n", "      <td>...</td>\n", "      <td>0.871679</td>\n", "      <td>291.805214</td>\n", "      <td>291.383278</td>\n", "      <td>3.693896</td>\n", "      <td>1.502645</td>\n", "      <td>186.660445</td>\n", "      <td>186.650873</td>\n", "      <td>23.953263</td>\n", "      <td>1.226213</td>\n", "      <td>2.152206</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>RM</td>\n", "      <td>197.023291</td>\n", "      <td>164.095562</td>\n", "      <td>0.0</td>\n", "      <td>196.848941</td>\n", "      <td>788.854621</td>\n", "      <td>0.0</td>\n", "      <td>196.680521</td>\n", "      <td>196.848941</td>\n", "      <td>1.316500</td>\n", "      <td>...</td>\n", "      <td>0.845573</td>\n", "      <td>242.835670</td>\n", "      <td>239.642767</td>\n", "      <td>4.466341</td>\n", "      <td>1.479116</td>\n", "      <td>196.585746</td>\n", "      <td>196.284340</td>\n", "      <td>26.909846</td>\n", "      <td>2.619858</td>\n", "      <td>2.433031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>RR</td>\n", "      <td>73.945897</td>\n", "      <td>62.442936</td>\n", "      <td>0.0</td>\n", "      <td>73.984691</td>\n", "      <td>1301.849118</td>\n", "      <td>0.0</td>\n", "      <td>73.955915</td>\n", "      <td>73.984691</td>\n", "      <td>0.463322</td>\n", "      <td>...</td>\n", "      <td>0.905009</td>\n", "      <td>147.929216</td>\n", "      <td>147.544375</td>\n", "      <td>3.455437</td>\n", "      <td>1.566970</td>\n", "      <td>73.971299</td>\n", "      <td>73.933966</td>\n", "      <td>21.571941</td>\n", "      <td>0.661888</td>\n", "      <td>2.249922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>RU</td>\n", "      <td>802.637321</td>\n", "      <td>508.684256</td>\n", "      <td>0.0</td>\n", "      <td>763.338736</td>\n", "      <td>1145.484214</td>\n", "      <td>0.0</td>\n", "      <td>802.227920</td>\n", "      <td>763.338736</td>\n", "      <td>1.184427</td>\n", "      <td>...</td>\n", "      <td>0.885307</td>\n", "      <td>1178.477732</td>\n", "      <td>1149.888073</td>\n", "      <td>4.240349</td>\n", "      <td>1.597517</td>\n", "      <td>763.220836</td>\n", "      <td>762.319984</td>\n", "      <td>23.151322</td>\n", "      <td>8.213292</td>\n", "      <td>2.243134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>SA</td>\n", "      <td>335.689490</td>\n", "      <td>22.964329</td>\n", "      <td>0.0</td>\n", "      <td>335.395387</td>\n", "      <td>952.188355</td>\n", "      <td>0.0</td>\n", "      <td>335.439082</td>\n", "      <td>335.395387</td>\n", "      <td>2.250327</td>\n", "      <td>...</td>\n", "      <td>0.838506</td>\n", "      <td>346.544259</td>\n", "      <td>348.978392</td>\n", "      <td>3.832450</td>\n", "      <td>1.332773</td>\n", "      <td>335.382666</td>\n", "      <td>335.266663</td>\n", "      <td>24.918196</td>\n", "      <td>2.675495</td>\n", "      <td>2.391333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>SC</td>\n", "      <td>50.763985</td>\n", "      <td>35.026247</td>\n", "      <td>0.0</td>\n", "      <td>50.781805</td>\n", "      <td>1217.057958</td>\n", "      <td>0.0</td>\n", "      <td>50.785519</td>\n", "      <td>50.781805</td>\n", "      <td>1.601935</td>\n", "      <td>...</td>\n", "      <td>0.873342</td>\n", "      <td>51.155557</td>\n", "      <td>50.351996</td>\n", "      <td>5.193604</td>\n", "      <td>1.605680</td>\n", "      <td>50.756237</td>\n", "      <td>50.693180</td>\n", "      <td>23.478086</td>\n", "      <td>0.307233</td>\n", "      <td>2.158563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>SF</td>\n", "      <td>308.667341</td>\n", "      <td>35.025845</td>\n", "      <td>0.0</td>\n", "      <td>308.872048</td>\n", "      <td>991.919778</td>\n", "      <td>0.0</td>\n", "      <td>308.733981</td>\n", "      <td>308.872048</td>\n", "      <td>0.987744</td>\n", "      <td>...</td>\n", "      <td>0.888576</td>\n", "      <td>610.020717</td>\n", "      <td>595.013921</td>\n", "      <td>3.525656</td>\n", "      <td>1.525738</td>\n", "      <td>308.808241</td>\n", "      <td>308.228373</td>\n", "      <td>21.275090</td>\n", "      <td>3.558685</td>\n", "      <td>2.270983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>SH</td>\n", "      <td>143.145274</td>\n", "      <td>8.630354</td>\n", "      <td>0.0</td>\n", "      <td>143.306018</td>\n", "      <td>935.565301</td>\n", "      <td>0.0</td>\n", "      <td>143.211599</td>\n", "      <td>143.306018</td>\n", "      <td>1.675918</td>\n", "      <td>...</td>\n", "      <td>0.885179</td>\n", "      <td>164.533113</td>\n", "      <td>161.160318</td>\n", "      <td>3.952984</td>\n", "      <td>1.441528</td>\n", "      <td>143.318859</td>\n", "      <td>143.213945</td>\n", "      <td>21.509288</td>\n", "      <td>2.693155</td>\n", "      <td>2.411862</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>SM</td>\n", "      <td>339.397778</td>\n", "      <td>26.006566</td>\n", "      <td>0.0</td>\n", "      <td>339.508888</td>\n", "      <td>951.531375</td>\n", "      <td>0.0</td>\n", "      <td>339.395832</td>\n", "      <td>339.508888</td>\n", "      <td>1.149189</td>\n", "      <td>...</td>\n", "      <td>0.889068</td>\n", "      <td>642.421215</td>\n", "      <td>613.663975</td>\n", "      <td>3.601941</td>\n", "      <td>1.498498</td>\n", "      <td>338.900111</td>\n", "      <td>336.591814</td>\n", "      <td>21.453331</td>\n", "      <td>4.835988</td>\n", "      <td>2.316555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>SN</td>\n", "      <td>14670.859507</td>\n", "      <td>8467.112419</td>\n", "      <td>0.0</td>\n", "      <td>14666.651909</td>\n", "      <td>878.311232</td>\n", "      <td>0.0</td>\n", "      <td>14665.088978</td>\n", "      <td>14666.651909</td>\n", "      <td>1.544708</td>\n", "      <td>...</td>\n", "      <td>0.865216</td>\n", "      <td>19035.282384</td>\n", "      <td>18216.761371</td>\n", "      <td>3.994337</td>\n", "      <td>1.632754</td>\n", "      <td>14656.005021</td>\n", "      <td>14627.696664</td>\n", "      <td>24.706724</td>\n", "      <td>199.756111</td>\n", "      <td>2.284507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>SP</td>\n", "      <td>376.443657</td>\n", "      <td>28.300980</td>\n", "      <td>0.0</td>\n", "      <td>376.067610</td>\n", "      <td>1071.873133</td>\n", "      <td>0.0</td>\n", "      <td>376.133685</td>\n", "      <td>376.067610</td>\n", "      <td>0.992745</td>\n", "      <td>...</td>\n", "      <td>0.897295</td>\n", "      <td>465.444361</td>\n", "      <td>461.497115</td>\n", "      <td>3.802961</td>\n", "      <td>1.539121</td>\n", "      <td>376.036621</td>\n", "      <td>376.017743</td>\n", "      <td>22.482177</td>\n", "      <td>1.975867</td>\n", "      <td>2.215852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>SR</td>\n", "      <td>269.768432</td>\n", "      <td>248.342295</td>\n", "      <td>0.0</td>\n", "      <td>269.596873</td>\n", "      <td>919.963182</td>\n", "      <td>0.0</td>\n", "      <td>269.354366</td>\n", "      <td>269.596873</td>\n", "      <td>0.745698</td>\n", "      <td>...</td>\n", "      <td>0.878275</td>\n", "      <td>286.946310</td>\n", "      <td>280.845391</td>\n", "      <td>4.854611</td>\n", "      <td>1.550275</td>\n", "      <td>269.313723</td>\n", "      <td>269.242424</td>\n", "      <td>23.835174</td>\n", "      <td>4.585006</td>\n", "      <td>2.341309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>SS</td>\n", "      <td>604.720517</td>\n", "      <td>47.595063</td>\n", "      <td>0.0</td>\n", "      <td>604.595986</td>\n", "      <td>919.497709</td>\n", "      <td>0.0</td>\n", "      <td>604.408946</td>\n", "      <td>604.595986</td>\n", "      <td>0.838714</td>\n", "      <td>...</td>\n", "      <td>0.894287</td>\n", "      <td>1124.669813</td>\n", "      <td>1110.227850</td>\n", "      <td>3.372989</td>\n", "      <td>1.559094</td>\n", "      <td>604.012239</td>\n", "      <td>602.916687</td>\n", "      <td>22.189473</td>\n", "      <td>3.821930</td>\n", "      <td>2.222092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>TA</td>\n", "      <td>175.911551</td>\n", "      <td>192.458133</td>\n", "      <td>0.0</td>\n", "      <td>176.212310</td>\n", "      <td>776.793583</td>\n", "      <td>0.0</td>\n", "      <td>176.058494</td>\n", "      <td>176.212310</td>\n", "      <td>0.885658</td>\n", "      <td>...</td>\n", "      <td>0.814507</td>\n", "      <td>406.290894</td>\n", "      <td>398.973300</td>\n", "      <td>3.513388</td>\n", "      <td>1.379195</td>\n", "      <td>175.991247</td>\n", "      <td>175.911877</td>\n", "      <td>28.963518</td>\n", "      <td>3.091346</td>\n", "      <td>2.456263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>UR</td>\n", "      <td>176.996913</td>\n", "      <td>7.366272</td>\n", "      <td>0.0</td>\n", "      <td>176.928135</td>\n", "      <td>1010.988143</td>\n", "      <td>0.0</td>\n", "      <td>176.937513</td>\n", "      <td>176.928135</td>\n", "      <td>1.850916</td>\n", "      <td>...</td>\n", "      <td>0.877224</td>\n", "      <td>224.139286</td>\n", "      <td>223.038258</td>\n", "      <td>4.288374</td>\n", "      <td>1.476394</td>\n", "      <td>177.003196</td>\n", "      <td>177.016532</td>\n", "      <td>24.311153</td>\n", "      <td>1.509903</td>\n", "      <td>2.204773</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>V</td>\n", "      <td>174.611751</td>\n", "      <td>34.370169</td>\n", "      <td>0.0</td>\n", "      <td>150.384155</td>\n", "      <td>950.562056</td>\n", "      <td>0.0</td>\n", "      <td>174.550280</td>\n", "      <td>150.384155</td>\n", "      <td>0.707263</td>\n", "      <td>...</td>\n", "      <td>0.867388</td>\n", "      <td>420.674375</td>\n", "      <td>415.958879</td>\n", "      <td>3.579724</td>\n", "      <td>1.517702</td>\n", "      <td>150.237084</td>\n", "      <td>149.733761</td>\n", "      <td>21.371101</td>\n", "      <td>1.404425</td>\n", "      <td>2.145831</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>Y</td>\n", "      <td>357.564118</td>\n", "      <td>19.796240</td>\n", "      <td>0.0</td>\n", "      <td>348.456757</td>\n", "      <td>839.153987</td>\n", "      <td>0.0</td>\n", "      <td>357.415393</td>\n", "      <td>348.456757</td>\n", "      <td>0.951131</td>\n", "      <td>...</td>\n", "      <td>0.880755</td>\n", "      <td>539.502925</td>\n", "      <td>534.260532</td>\n", "      <td>3.880320</td>\n", "      <td>1.544701</td>\n", "      <td>348.348825</td>\n", "      <td>348.045549</td>\n", "      <td>23.800117</td>\n", "      <td>2.195719</td>\n", "      <td>2.179129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>ZN</td>\n", "      <td>999.736636</td>\n", "      <td>97.786656</td>\n", "      <td>0.0</td>\n", "      <td>998.635371</td>\n", "      <td>849.041549</td>\n", "      <td>0.0</td>\n", "      <td>998.942451</td>\n", "      <td>998.635371</td>\n", "      <td>0.840385</td>\n", "      <td>...</td>\n", "      <td>0.879000</td>\n", "      <td>1469.042298</td>\n", "      <td>1439.384627</td>\n", "      <td>4.313735</td>\n", "      <td>1.565076</td>\n", "      <td>998.142266</td>\n", "      <td>996.533267</td>\n", "      <td>23.529373</td>\n", "      <td>9.644665</td>\n", "      <td>2.211145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>IC</td>\n", "      <td>408.015013</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>407.591141</td>\n", "      <td>847.914674</td>\n", "      <td>0.0</td>\n", "      <td>407.673998</td>\n", "      <td>407.591141</td>\n", "      <td>1.265434</td>\n", "      <td>...</td>\n", "      <td>0.868179</td>\n", "      <td>409.907272</td>\n", "      <td>404.911975</td>\n", "      <td>3.713604</td>\n", "      <td>1.619675</td>\n", "      <td>407.505542</td>\n", "      <td>407.265596</td>\n", "      <td>22.821795</td>\n", "      <td>4.842405</td>\n", "      <td>2.196759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>IF</td>\n", "      <td>297.237604</td>\n", "      <td>23.557575</td>\n", "      <td>0.0</td>\n", "      <td>296.984043</td>\n", "      <td>908.739991</td>\n", "      <td>0.0</td>\n", "      <td>297.043612</td>\n", "      <td>296.984043</td>\n", "      <td>1.280592</td>\n", "      <td>...</td>\n", "      <td>0.869376</td>\n", "      <td>299.081927</td>\n", "      <td>295.069127</td>\n", "      <td>3.929963</td>\n", "      <td>1.621235</td>\n", "      <td>296.949888</td>\n", "      <td>296.759305</td>\n", "      <td>23.111930</td>\n", "      <td>3.227328</td>\n", "      <td>2.204115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>IH</td>\n", "      <td>215.588396</td>\n", "      <td>17.135245</td>\n", "      <td>0.0</td>\n", "      <td>215.423184</td>\n", "      <td>895.022275</td>\n", "      <td>0.0</td>\n", "      <td>215.461216</td>\n", "      <td>215.423184</td>\n", "      <td>1.297116</td>\n", "      <td>...</td>\n", "      <td>0.867112</td>\n", "      <td>216.923251</td>\n", "      <td>214.029314</td>\n", "      <td>4.015215</td>\n", "      <td>1.612802</td>\n", "      <td>215.392240</td>\n", "      <td>215.271580</td>\n", "      <td>22.593382</td>\n", "      <td>2.283250</td>\n", "      <td>2.233991</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>IM</td>\n", "      <td>351.533455</td>\n", "      <td>38.990901</td>\n", "      <td>0.0</td>\n", "      <td>351.122758</td>\n", "      <td>887.333895</td>\n", "      <td>0.0</td>\n", "      <td>351.193327</td>\n", "      <td>351.122758</td>\n", "      <td>1.056206</td>\n", "      <td>...</td>\n", "      <td>0.868812</td>\n", "      <td>661.205923</td>\n", "      <td>654.438658</td>\n", "      <td>3.713017</td>\n", "      <td>1.598383</td>\n", "      <td>350.841005</td>\n", "      <td>350.008214</td>\n", "      <td>24.161304</td>\n", "      <td>3.997856</td>\n", "      <td>2.211114</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>59 rows × 128 columns</p>\n", "</div>"], "text/plain": ["   code        OPEN_2       HIGH_2  LOW_2       CLOSE_2     VOLUME_1  \\\n", "0     A    153.715697    40.865894    0.0    153.738342   891.389716   \n", "1    AG    428.187998   379.598023    0.0    428.659456   734.809479   \n", "2    AL    529.744286   372.805783    0.0    528.573067   852.191229   \n", "3    AO    149.525851   102.122573    0.0    150.092116   914.468795   \n", "4    AP    321.975033   118.441066    0.0    321.476500  1010.986162   \n", "5    AU     25.976799    28.099911    0.0     25.970622   804.851351   \n", "6     B    283.612144    56.035159    0.0    283.355605   844.571363   \n", "7    BR    744.880981   637.690119    0.0    742.998923  1027.294940   \n", "8    BU     84.334736    11.909699    0.0     84.279174   864.135817   \n", "9     C     91.931986     2.981677    0.0     79.517789   952.184079   \n", "10   CF    678.024116   796.325375    0.0    675.752463   897.127281   \n", "11   CJ   1300.600563   866.948466    0.0   1300.011939  1037.048887   \n", "12   CS     80.011222    27.005282    0.0     59.203404   939.879822   \n", "13   CU   2644.263863  2004.671935    0.0   2645.353568   786.832108   \n", "14   CY    762.370447   366.286647    0.0    762.852291  1082.264647   \n", "15   EB    444.719526   219.381110    0.0    444.568828   861.268598   \n", "16   EG    157.898411    46.866077    0.0    114.902113   951.429586   \n", "17   FG    127.340082    41.106055    0.0    127.465512   967.242201   \n", "18   HC    178.537833    45.357537    0.0    166.355698   995.704968   \n", "19    I     73.816998    17.034352    0.0     73.770474  1142.483239   \n", "20    J    211.853278    29.631343    0.0    207.345485   947.973366   \n", "21   JD    198.826307    47.769705    0.0    198.988729   959.865584   \n", "22   JM    180.879445    35.609776    0.0    176.976147  1070.755431   \n", "23    L    161.945221    39.531823    0.0    161.931666   891.796214   \n", "24   LH   1252.784385   424.518207    0.0   1251.996392  1005.888431   \n", "25    M    200.902023   128.130746    0.0    199.371369   849.619419   \n", "26   MA    121.942399    15.177241    0.0    121.963591   904.381201   \n", "27   NI  15283.134020   267.687597    0.0  15286.697439   905.677673   \n", "28   NR    592.623642   556.261479    0.0    519.513711  1119.092071   \n", "29   OI    489.906569     2.090021    0.0    466.542003   848.414421   \n", "30    P    436.135807    48.604603    0.0    424.036384   860.402726   \n", "31   PB    462.674159   329.637799    0.0    462.871196   871.241260   \n", "32   PF    223.695001    91.501558    0.0    157.562197   822.622346   \n", "33   PG    391.701482   193.748697    0.0    391.085128   843.762861   \n", "34   PK    451.207658   128.904562    0.0    451.302429  1018.537754   \n", "35   PP    204.957342    43.814834    0.0    204.852121   870.230218   \n", "36   PX    210.269290     5.739357    0.0    210.499727   815.466244   \n", "37   RB    191.956019    34.904157    0.0    186.681492   987.075657   \n", "38   RM    197.023291   164.095562    0.0    196.848941   788.854621   \n", "39   RR     73.945897    62.442936    0.0     73.984691  1301.849118   \n", "40   RU    802.637321   508.684256    0.0    763.338736  1145.484214   \n", "41   SA    335.689490    22.964329    0.0    335.395387   952.188355   \n", "42   SC     50.763985    35.026247    0.0     50.781805  1217.057958   \n", "43   SF    308.667341    35.025845    0.0    308.872048   991.919778   \n", "44   SH    143.145274     8.630354    0.0    143.306018   935.565301   \n", "45   SM    339.397778    26.006566    0.0    339.508888   951.531375   \n", "46   SN  14670.859507  8467.112419    0.0  14666.651909   878.311232   \n", "47   SP    376.443657    28.300980    0.0    376.067610  1071.873133   \n", "48   SR    269.768432   248.342295    0.0    269.596873   919.963182   \n", "49   SS    604.720517    47.595063    0.0    604.595986   919.497709   \n", "50   TA    175.911551   192.458133    0.0    176.212310   776.793583   \n", "51   UR    176.996913     7.366272    0.0    176.928135  1010.988143   \n", "52    V    174.611751    34.370169    0.0    150.384155   950.562056   \n", "53    Y    357.564118    19.796240    0.0    348.456757   839.153987   \n", "54   ZN    999.736636    97.786656    0.0    998.635371   849.041549   \n", "55   IC    408.015013     0.000000    0.0    407.591141   847.914674   \n", "56   IF    297.237604    23.557575    0.0    296.984043   908.739991   \n", "57   IH    215.588396    17.135245    0.0    215.423184   895.022275   \n", "58   IM    351.533455    38.990901    0.0    351.122758   887.333895   \n", "\n", "    VOLUME_2  TYPICAL_PRICE_2         NEW_2  NEW_CHANGE_PERCENT_2  ...  \\\n", "0        0.0       153.728190    153.738342              0.739985  ...   \n", "1        0.0       428.534835    428.659456              1.149094  ...   \n", "2        0.0       529.049976    528.573067              0.677505  ...   \n", "3        0.0       149.806039    150.092116              1.572361  ...   \n", "4        0.0       321.811985    321.476500              1.312924  ...   \n", "5        0.0        25.974130     25.970622              0.703518  ...   \n", "6        0.0       283.417636    283.355605              1.232316  ...   \n", "7        0.0       742.977106    742.998923              1.962616  ...   \n", "8        0.0        84.236507     84.279174              0.905332  ...   \n", "9        0.0        91.790827     79.517789              0.657374  ...   \n", "10       0.0       675.883837    675.752463              0.771951  ...   \n", "11       0.0      1300.273210   1300.011939              2.176575  ...   \n", "12       0.0        79.835591     59.203404              0.737902  ...   \n", "13       0.0      2644.912354   2645.353568              0.665430  ...   \n", "14       0.0       762.300229    762.852291              0.852304  ...   \n", "15       0.0       444.571531    444.568828              1.002224  ...   \n", "16       0.0       157.746808    114.902113              0.915961  ...   \n", "17       0.0       127.458134    127.465512              1.727777  ...   \n", "18       0.0       178.486899    166.355698              0.717399  ...   \n", "19       0.0        73.773927     73.770474              1.588406  ...   \n", "20       0.0       211.885023    207.345485              1.583431  ...   \n", "21       0.0       198.928715    198.988729              1.849810  ...   \n", "22       0.0       180.790023    176.976147              2.000890  ...   \n", "23       0.0       161.904961    161.931666              0.501635  ...   \n", "24       0.0      1251.935441   1251.996392              2.789944  ...   \n", "25       0.0       200.728221    199.371369              1.201508  ...   \n", "26       0.0       121.764095    121.963591              0.987990  ...   \n", "27       0.0     15285.714778  15286.697439              1.489630  ...   \n", "28       0.0       592.459178    519.513711              1.224042  ...   \n", "29       0.0       489.166070    466.542003              1.002468  ...   \n", "30       0.0       435.904667    424.036384              1.143877  ...   \n", "31       0.0       462.758896    462.871196              0.676215  ...   \n", "32       0.0       223.995564    157.562197              0.673831  ...   \n", "33       0.0       391.981733    391.085128              2.167512  ...   \n", "34       0.0       451.267606    451.302429              1.166134  ...   \n", "35       0.0       204.848384    204.852121              0.530957  ...   \n", "36       0.0       210.701673    210.499727              0.837208  ...   \n", "37       0.0       191.959624    186.681492              0.774028  ...   \n", "38       0.0       196.680521    196.848941              1.316500  ...   \n", "39       0.0        73.955915     73.984691              0.463322  ...   \n", "40       0.0       802.227920    763.338736              1.184427  ...   \n", "41       0.0       335.439082    335.395387              2.250327  ...   \n", "42       0.0        50.785519     50.781805              1.601935  ...   \n", "43       0.0       308.733981    308.872048              0.987744  ...   \n", "44       0.0       143.211599    143.306018              1.675918  ...   \n", "45       0.0       339.395832    339.508888              1.149189  ...   \n", "46       0.0     14665.088978  14666.651909              1.544708  ...   \n", "47       0.0       376.133685    376.067610              0.992745  ...   \n", "48       0.0       269.354366    269.596873              0.745698  ...   \n", "49       0.0       604.408946    604.595986              0.838714  ...   \n", "50       0.0       176.058494    176.212310              0.885658  ...   \n", "51       0.0       176.937513    176.928135              1.850916  ...   \n", "52       0.0       174.550280    150.384155              0.707263  ...   \n", "53       0.0       357.415393    348.456757              0.951131  ...   \n", "54       0.0       998.942451    998.635371              0.840385  ...   \n", "55       0.0       407.673998    407.591141              1.265434  ...   \n", "56       0.0       297.043612    296.984043              1.280592  ...   \n", "57       0.0       215.461216    215.423184              1.297116  ...   \n", "58       0.0       351.193327    351.122758              1.056206  ...   \n", "\n", "    TREND_INPOSR_2  TREND_HIGHEST_2  TREND_LOWEST_2  TREND_HLR_2  \\\n", "0         0.883221       251.902988      250.257969     3.479232   \n", "1         0.881970       435.666008      418.008938     4.852349   \n", "2         0.888147      1110.335378     1082.125270     3.630985   \n", "3         0.898594       228.388607      220.978514     3.621714   \n", "4         0.881069       659.238385      651.997142     5.514550   \n", "5         0.873935        26.310914       25.555209     4.733790   \n", "6         0.879491       355.686006      352.166971     4.720953   \n", "7         0.887934       791.747548      764.814023     3.040759   \n", "8         0.890099       203.075480      201.162305     3.722803   \n", "9         0.893933       180.712217      180.087408     3.357285   \n", "10        0.924737       863.767623      852.453254     4.040311   \n", "11        0.888533      1604.001582     1575.134025     4.575478   \n", "12        0.901941       181.022727      179.815094     3.588245   \n", "13        0.880258      4806.421696     4678.133379     4.046517   \n", "14        0.908019      1333.547861     1327.376644     3.636988   \n", "15        0.884098       552.107325      550.715337     3.537982   \n", "16        0.889307       295.702884      287.253999     3.499431   \n", "17        0.989120       141.479595      138.488585     4.135170   \n", "18        0.877668       277.307629      278.072445     3.449578   \n", "19        0.886878        81.181991       82.192047     3.775861   \n", "20        0.875539       255.272598      253.555888     3.516608   \n", "21        0.874591       422.236997      416.904339     5.310997   \n", "22        0.877398       202.973321      201.318662     3.552529   \n", "23        0.876746       456.815511      451.619522     3.593754   \n", "24        0.880639      1642.837484     1616.936473     4.200303   \n", "25        0.877187       309.101642      304.208010     4.830268   \n", "26        0.830231       172.294236      172.054437     4.339641   \n", "27        0.863975     17259.328703    16758.337274     3.930435   \n", "28        0.896911       974.071994      946.517816     3.520977   \n", "29        0.871939       636.067996      626.324229     4.162384   \n", "30        0.878059       615.594642      602.969872     3.681762   \n", "31        0.899375       881.193066      858.324673     3.388717   \n", "32        0.877775       453.150714      448.853825     3.555603   \n", "33        0.886446       482.072995      474.672493     5.693458   \n", "34        0.882131       671.390526      660.699797     4.449283   \n", "35        0.876340       419.454662      416.917307     3.496641   \n", "36        0.981002       287.663484      279.168665     3.639823   \n", "37        0.871679       291.805214      291.383278     3.693896   \n", "38        0.845573       242.835670      239.642767     4.466341   \n", "39        0.905009       147.929216      147.544375     3.455437   \n", "40        0.885307      1178.477732     1149.888073     4.240349   \n", "41        0.838506       346.544259      348.978392     3.832450   \n", "42        0.873342        51.155557       50.351996     5.193604   \n", "43        0.888576       610.020717      595.013921     3.525656   \n", "44        0.885179       164.533113      161.160318     3.952984   \n", "45        0.889068       642.421215      613.663975     3.601941   \n", "46        0.865216     19035.282384    18216.761371     3.994337   \n", "47        0.897295       465.444361      461.497115     3.802961   \n", "48        0.878275       286.946310      280.845391     4.854611   \n", "49        0.894287      1124.669813     1110.227850     3.372989   \n", "50        0.814507       406.290894      398.973300     3.513388   \n", "51        0.877224       224.139286      223.038258     4.288374   \n", "52        0.867388       420.674375      415.958879     3.579724   \n", "53        0.880755       539.502925      534.260532     3.880320   \n", "54        0.879000      1469.042298     1439.384627     4.313735   \n", "55        0.868179       409.907272      404.911975     3.713604   \n", "56        0.869376       299.081927      295.069127     3.929963   \n", "57        0.867112       216.923251      214.029314     4.015215   \n", "58        0.868812       661.205923      654.438658     3.713017   \n", "\n", "    TREND_LEVEL_2  HYO_TENKAN_SEN_2  HYO_KIJUN_SEN_2  HYO_CROSS_BARS_2  \\\n", "0        1.525810        153.823649       153.987173         24.219806   \n", "1        1.534000        428.480137       427.816833         24.526022   \n", "2        1.586104        528.097366       526.923655         22.517041   \n", "3        1.564056        149.900682       149.371765         23.253783   \n", "4        1.510454        321.168877       320.900646         23.630814   \n", "5        1.619906         25.966930        25.944533         24.732088   \n", "6        1.502948        283.579169       283.947221         25.831949   \n", "7        1.594504        743.019725       742.629963         20.781933   \n", "8        1.467588         84.160712        83.821294         21.302324   \n", "9        1.530068         79.456307        79.293899         22.791973   \n", "10       1.478430        675.508175       675.485624         25.516096   \n", "11       1.571908       1300.604927      1302.914807         22.761372   \n", "12       1.550345         59.109280        58.904169         21.928227   \n", "13       1.577063       2642.617613      2632.441609         23.005241   \n", "14       1.637128        762.059072       760.606197         22.921603   \n", "15       1.549917        444.615611       444.335849         21.034236   \n", "16       1.508835        114.748182       114.489767         21.809272   \n", "17       1.338805        127.455573       127.413796         34.174820   \n", "18       1.496881        166.341008       166.285691         22.959858   \n", "19       1.563288         73.758487        73.734271         22.465634   \n", "20       1.515027        207.363560       207.316178         20.820747   \n", "21       1.463306        198.815378       198.234519         23.390232   \n", "22       1.486621        176.957228       176.872043         22.784356   \n", "23       1.525295        161.757342       161.185997         22.678737   \n", "24       1.520949       1251.501714      1250.550532         22.674448   \n", "25       1.532672        199.251571       198.848755         24.897128   \n", "26       1.509761        121.751888       121.698883         31.489820   \n", "27       1.618626      15286.029769     15285.274151         24.553080   \n", "28       1.592493        519.062799       518.339239         21.451227   \n", "29       1.601702        466.355960       466.152895         23.985989   \n", "30       1.565025        423.949861       423.808226         21.532007   \n", "31       1.500195        462.609151       461.778532         20.563199   \n", "32       1.435327        157.311231       157.033191         23.558129   \n", "33       1.522321        391.131864       391.063239         23.960871   \n", "34       1.561057        451.377394       451.580375         22.033021   \n", "35       1.517179        204.736040       204.380298         22.790149   \n", "36       1.367837        210.499601       210.776948         23.720027   \n", "37       1.502645        186.660445       186.650873         23.953263   \n", "38       1.479116        196.585746       196.284340         26.909846   \n", "39       1.566970         73.971299        73.933966         21.571941   \n", "40       1.597517        763.220836       762.319984         23.151322   \n", "41       1.332773        335.382666       335.266663         24.918196   \n", "42       1.605680         50.756237        50.693180         23.478086   \n", "43       1.525738        308.808241       308.228373         21.275090   \n", "44       1.441528        143.318859       143.213945         21.509288   \n", "45       1.498498        338.900111       336.591814         21.453331   \n", "46       1.632754      14656.005021     14627.696664         24.706724   \n", "47       1.539121        376.036621       376.017743         22.482177   \n", "48       1.550275        269.313723       269.242424         23.835174   \n", "49       1.559094        604.012239       602.916687         22.189473   \n", "50       1.379195        175.991247       175.911877         28.963518   \n", "51       1.476394        177.003196       177.016532         24.311153   \n", "52       1.517702        150.237084       149.733761         21.371101   \n", "53       1.544701        348.348825       348.045549         23.800117   \n", "54       1.565076        998.142266       996.533267         23.529373   \n", "55       1.619675        407.505542       407.265596         22.821795   \n", "56       1.621235        296.949888       296.759305         23.111930   \n", "57       1.612802        215.392240       215.271580         22.593382   \n", "58       1.598383        350.841005       350.008214         24.161304   \n", "\n", "        TATR_2  NATR_TL_2  \n", "0     1.243634   2.208027  \n", "1     3.585892   2.325591  \n", "2     7.032230   2.243600  \n", "3     3.506930   2.404023  \n", "4     3.836375   2.283148  \n", "5     0.192574   2.347244  \n", "6     1.486501   2.198008  \n", "7    13.875760   2.316005  \n", "8     1.235406   2.224454  \n", "9     0.594557   2.222150  \n", "10   10.613887   2.428995  \n", "11   10.615168   2.319936  \n", "12    0.670442   2.221781  \n", "13   31.476742   2.254760  \n", "14   10.710480   2.248423  \n", "15    2.908307   2.214301  \n", "16    2.163555   2.185766  \n", "17    1.829442   2.383901  \n", "18    1.156448   2.155734  \n", "19    0.351340   2.141335  \n", "20    1.090939   2.200265  \n", "21    1.468906   2.284630  \n", "22    1.138325   2.186285  \n", "23    1.319712   2.157539  \n", "24    9.519212   2.244861  \n", "25    1.178550   2.210609  \n", "26    2.741020   2.283351  \n", "27  108.435861   2.227547  \n", "28    8.502974   2.283049  \n", "29    5.594898   2.292178  \n", "30    3.078591   2.178333  \n", "31    6.415495   2.286301  \n", "32    3.406433   2.396588  \n", "33    2.398655   2.219903  \n", "34    3.293030   2.241551  \n", "35    1.298102   2.159970  \n", "36    3.892507   2.460069  \n", "37    1.226213   2.152206  \n", "38    2.619858   2.433031  \n", "39    0.661888   2.249922  \n", "40    8.213292   2.243134  \n", "41    2.675495   2.391333  \n", "42    0.307233   2.158563  \n", "43    3.558685   2.270983  \n", "44    2.693155   2.411862  \n", "45    4.835988   2.316555  \n", "46  199.756111   2.284507  \n", "47    1.975867   2.215852  \n", "48    4.585006   2.341309  \n", "49    3.821930   2.222092  \n", "50    3.091346   2.456263  \n", "51    1.509903   2.204773  \n", "52    1.404425   2.145831  \n", "53    2.195719   2.179129  \n", "54    9.644665   2.211145  \n", "55    4.842405   2.196759  \n", "56    3.227328   2.204115  \n", "57    2.283250   2.233991  \n", "58    3.997856   2.211114  \n", "\n", "[59 rows x 128 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["sfstd = pd.read_csv(r\"D:\\RoboQuant\\store\\sf_std.csv\")\n", "sfstd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame(data['data'])\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df reshaping (15, 119)\n", "df = df.T\n", "df = df.reset_index()\n", "df.columns = df.iloc[0]\n", "df = df.drop(0)\n", "df = df.reset_index(drop=True)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CV2DR"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test\n", "print(\"=== Testing ===\")\n", "test_dataloader = data_module.test_dataloader()\n", "trainer = Trainer()\n", "trainer.test(model, test_dataloaders=test_dataloader)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Model eval check"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import pandas as pd\n", "import numpy as np\n", "import json"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with open('d:\\\\RoboQuant\\\\rpt\\\\model_test_data.json') as f:\n", "    md_data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for md in md_data:\n", "    # 加载模型\n", "    model = torch.jit.load(f\"d:\\\\RoboQuant\\\\model\\\\{md['model']}.model\")\n", "    model.eval()\n", "    print(md['encoding'])\n", "    encoding = torch.tensor([md['encoding']], dtype=torch.int32)\n", "    input_tensor = torch.tensor(md['data'])\n", "    if '05' in md['model']:\n", "        channel = 5\n", "    elif '10' in md['model']:\n", "        channel = 10\n", "    elif '15' in md['model']:\n", "        channel = 15\n", "    else:\n", "        continue\n", "    input_tensor = input_tensor.reshape(1, channel, len(md['data'])//channel)\n", "    # print(encoding.shape, input_tensor.shape)\n", "    output_tensor = model(encoding, input_tensor)\n", "    print(output_tensor, md['predict'])\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["with open('e:/lab/RoboQuant/bin/x64/Release/rpt/model_test_data.json') as f:\n", "    md_data = json.load(f)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["560"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["len(md_data[0]['feat'])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["feat = md_data[0]['feat'][-11:]\n", "data = md_data[0]['data'][-11:]\n", "means = md_data[0]['means'][-11:]\n", "stds = md_data[0]['stds'][-11:]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.6589628458023071, 0.0, -0.6534249186515808, 0.2763267755508423, 1.0, 0.16439345479011536, -0.7624899744987488, -1.0, -0.02448284812271595, -0.12306361645460129, 1.0]\n", "[1.9540044069290161, -4.955992221832275, -0.12699022889137268, 0.2434171885251999, 0.4251939356327057, -1.3482558727264404, -1.1690670251846313, -0.5492770671844482, -0.0646616667509079, -0.1042919009923935, 0.7405258417129517]\n", "[1.370863914489746, 15.722155570983887, -0.04414869099855423, -0.038297999650239944, -0.05515841394662857, 0.26343804597854614, 0.35119858384132385, 0.16802211105823517, 0.2408970147371292, 0.016221705824136734, -0.04293831065297127]\n", "[0.14744026958942413, 3.1723527908325195, 4.7978196144104, 1.2925331592559814, 2.481593132019043, 0.07346127182245255, 0.9526302218437195, 2.126471757888794, 4.104129791259766, 1.3355334997177124, 1.4083753824234009]\n"]}], "source": ["print(feat)\n", "print(data)\n", "print(means)\n", "print(stds)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 1.95400437 -4.95599216 -0.12699023  0.24341718  0.42519396 -1.34825587\n", " -1.169067   -0.54927704 -0.06466166 -0.1042919   0.7405258 ]\n"]}], "source": ["print((np.array(feat) - np.array(means)) / np.array(stds))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Model dump check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rpt_path = 'd:/RoboQuant2/rpt'\n", "json_file = 'model_test_202306172311.json'\n", "with open(f'{rpt_path}/{json_file}') as file:\n", "    # Load the JSON data\n", "    data = json.load(file)\n", "models = data['models']\n", "data.pop('models')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs = pd.DataFrame()\n", "for code in data.keys():\n", "    df = pd.DataFrame(data[code], columns=['change'] + models)\n", "    df [\"change\"] = df[\"change\"].shift(-1)\n", "    df.dropna(inplace=True)\n", "    df = df.loc[df['change'] != 0.0, :]\n", "    df.insert(0, 'code', code)\n", "    dfs = pd.concat([dfs, df], axis=0)\n", "# 删除loc.columns[2:]列所有列元素都为0的列\n", "# dfs = dfs.loc[:, dfs[dfs.columns[2:]].sum(axis=0) > 0.0]\n", "# # 删除所有行元素都为0的行\n", "dfs = dfs.loc[dfs[dfs.columns[2:]].sum(axis=1) > 0.0, :]\n", "dfs.reset_index(drop=True, inplace=True)\n", "print(dfs.shape)\n", "dfs.to_csv(f'{rpt_path}/{json_file}.csv', index=False)\n", "dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs.describe(percentiles=[0.75, 0.95, 0.98, 0.99])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}