"""
使用训练好的模型进行预测示例

演示如何加载训练好的模型和tokenizer，并对新的K线数据进行预测
"""

import os
import sys
import argparse
import torch

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和工具函数
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.utils import (
    load_klines_from_parquet,
    apply_model_to_klines
)

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='使用训练好的模型进行预测示例')
    parser.add_argument('--model_path', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--model_type', type=str, default='advanced', choices=['basic', 'advanced'],
                        help='模型类型: basic (基础版CandlestickLLM) 或 advanced (高级版AdvancedCandlestickLLM)')
    parser.add_argument('--data_dir', type=str, required=True, help='数据目录，包含parquet文件')
    parser.add_argument('--symbols', type=str, nargs='+', help='证券代码列表，如果不提供则加载所有证券')
    parser.add_argument('--start_date', type=str, help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--timeframe', type=str, default='daily', help='时间周期，如daily, 1h, 15min等')
    parser.add_argument('--output_dir', type=str, default='./predictions', help='预测结果保存目录')
    parser.add_argument('--max_new_tokens', type=int, default=5, help='生成的最大新token数量')
    parser.add_argument('--temperature', type=float, default=0.8, help='温度参数，控制采样的随机性')
    parser.add_argument('--top_k', type=int, default=50, help='只考虑概率最高的前k个token')
    args = parser.parse_args()
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建tokenizer
    if args.model_type == 'basic':
        print("创建基础版tokenizer...")
        tokenizer = CandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            atr_window=100,
            atr_mult=0.88,
            scale=10,
            include_volume=True
        )
    else:
        print("创建非线性tokenizer...")
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            include_volume=True
        )
    
    # 加载模型
    print(f"加载模型: {args.model_path}")
    checkpoint = torch.load(args.model_path, map_location=device)
    
    # 创建模型
    if args.model_type == 'basic':
        print("创建基础版CandlestickLLM模型...")
        model = CandlestickLLM(
            vocab_size=tokenizer.vocab_size,
            code_size=100,  # 这个值会被检查点中的值覆盖
            block_size=30,
            n_layer=2,
            n_head=4,
            d_model=128,
            dropout=0.1,
            use_time_features=True
        )
    else:
        print("创建高级版AdvancedCandlestickLLM模型...")
        model = AdvancedCandlestickLLM(
            vocab_size=tokenizer.vocab_size,
            code_size=100,  # 这个值会被检查点中的值覆盖
            block_size=30,
            n_layer=2,
            n_head=4,
            d_model=128,
            dropout=0.1,
            use_time_features=True,
            use_multi_task=True
        )
    
    # 加载模型参数
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    print("模型加载完成")
    
    # 加载K线数据
    print(f"从{args.data_dir}加载{args.timeframe}周期的K线数据...")
    klines_dict = load_klines_from_parquet(
        data_dir=args.data_dir,
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date,
        timeframe=args.timeframe
    )
    
    if not klines_dict:
        print("未找到数据，请检查数据目录和参数设置")
        return
    
    print(f"加载了 {len(klines_dict)} 个证券的K线数据")
    
    # 创建证券代码到ID的映射
    symbols = list(klines_dict.keys())
    symbol_to_id = {symbol: i for i, symbol in enumerate(symbols)}
    
    # 应用模型进行预测
    print("开始预测...")
    results = apply_model_to_klines(
        model=model,
        tokenizer=tokenizer,
        klines_dict=klines_dict,
        symbol_to_id=symbol_to_id,
        max_new_tokens=args.max_new_tokens,
        temperature=args.temperature,
        top_k=args.top_k,
        save_dir=args.output_dir
    )
    
    print(f"预测完成，结果保存在 {args.output_dir} 目录下")
    
    # 打印部分预测结果
    for symbol, result in list(results.items())[:3]:  # 只打印前3个
        print(f"\n{symbol} 预测结果:")
        print(result['predicted_df'])

if __name__ == '__main__':
    main()
