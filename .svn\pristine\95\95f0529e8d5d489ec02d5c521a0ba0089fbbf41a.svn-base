qlib_init:
    provider_uri: "~/.qlib/qlib_data/cn_data"
    region: cn
market: &market csi300
benchmark: &benchmark SH000300
data_loader_config: &data_loader_config
    class: AFDataLoader
    module_path: pyqlab.data.dataset.loader
    kwargs: 
        direct: long
        model_name: MLP2
        model_path: "e:/lab/RoboQuant/pylab/model"
        data_path: "e:/lab/RoboQuant/pylab/data"
        portfolios: ["00200910081133001", "00171106132928000", "00170623114649000"]
data_handler_config: &data_handler_config
    start_time: 2008-01-01
    end_time: 2020-08-01
    instruments: *market
    data_loader: *data_loader_config

task:
    model:
        class: DNNModelPytorch
        module_path: qlib.contrib.model.pytorch_nn
        kwargs:
            loss: mse
            input_dim: 87
            output_dim: 1
            lr: 0.002
            lr_decay: 0.96
            lr_decay_steps: 100
            optimizer: adam
            max_steps: 8000
            batch_size: 64
            GPU: 0
            weight_decay: 0.0002
    dataset:
        class: AFDatasetH
        module_path: pyqlab.data.dataset
        kwargs:
            handler:
                class: DataHandlerAF
                module_path: pyqlab.data.dataset.handler
                kwargs: *data_handler_config
            segments: [train, valid]
            col_set: [feature, label]

