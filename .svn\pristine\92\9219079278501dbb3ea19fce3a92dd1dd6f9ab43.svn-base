import torch
import torch.nn as nn
import numpy as np
import math

class TimeSeriesModel1dr(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  kernel_size=3,
                  out_channels=(32, 64, 1152, 256),
                  activation="relu",
                  pooling="max",
                ):
        super(TimeSeriesModel1dr, self).__init__()
        self.embedding_layers = nn.ModuleList()
        print(f"num_embeds: {num_embeds}")
        for num_embed in num_embeds:
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embed, embedding_dim=math.ceil(np.sqrt(num_embed))))
        self.flatten = nn.Flatten()

        if activation == "relu":
            activation = nn.ReLU()
        elif activation == "gelu":
            activation = nn.GELU()
        elif activation == "prelu":
            activation = nn.PReLU()
        elif activation == "leakyrelu":
            activation = nn.LeakyReLU()
        else:
            raise Exception("activation must be relu or gelu")
        
        if pooling == "max":
            pooling = nn.MaxPool1d(kernel_size=2)
        elif pooling == "avg":
            pooling = nn.AvgPool1d(kernel_size=2)
        else:
            raise Exception("pooling must be max or avg")
        

        self.conv1 = nn.Sequential(
            nn.Conv1d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=kernel_size, stride=1, padding=1),
            nn.BatchNorm1d(out_channels[0]),
            activation,
            pooling,
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv1d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=kernel_size, stride=1, padding=1),
            nn.BatchNorm1d(out_channels[1]),
            activation,
            pooling,
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation,
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Linear(out_channels[3], 1)

    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([embedded_data, x], dim=-1)

        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)
        return x.view(-1)
