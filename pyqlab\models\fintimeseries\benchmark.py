import numpy as np
import time
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, confusion_matrix
from pyqlab.models.fintimeseries.time_series_model1dc import TimeSeriesModel1dc
from pyqlab.models.fintimeseries.time_series_model2dm import TimeSeriesModel2dm
import onnxruntime as ort
import os
import pandas as pd
import seaborn as sns
import argparse
from pyqlab.data.data_api import get_dataset
from pyqlab.const import *
from torch.utils.data import DataLoader

def load_onnx_session(model_path):
    """
    加载ONNX模型，返回推理会话对象
    """
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 创建推理会话
    try:
        session = ort.InferenceSession(model_path)
        print(f"成功加载模型: {model_path}")
        return session
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None
    
def get_model_info(session):
    """
    获取ONNX模型的输入输出信息
    """
    if session is None:
        return None, None
    
    input_info = {}
    for input in session.get_inputs():
        input_info[input.name] = {
            'name': input.name,
            'shape': input.shape,
            'type': input.type
        }
    
    output_info = {}
    for output in session.get_outputs():
        output_info[output.name] = {
            'name': output.name,
            'shape': output.shape,
            'type': output.type
        }
    
    return input_info, output_info

def evaluate_onnx_model(session, dataloader):
    """
    评估ONNX模型性能
    
    参数:
    session: ONNX模型会话
    dataloader: 数据加载器
    
    返回:
    指标字典
    """
    if session is None:
        return None
    
    input_info, _ = get_model_info(session)
    input_names = list(input_info.keys())
    print(input_names)

    all_preds = []
    all_true = []
    total_inference_time = 0
    
    for batch in dataloader:
        print(batch[0].shape)
        print(batch[1].shape)
        print(batch[2].shape)
        code_ids, X, y_true = batch
        
        # 准备输入数据
        input_dict = {
            input_names[0]: code_ids.numpy(),
            input_names[1]: X.numpy()
        }
        
        # 记录推理开始时间
        start_time = time.time()
        
        # 批量推理
        outputs = session.run(None, input_dict)
        print(outputs.shape)
        print(outputs)
        predictions = outputs[0]
        
        # 记录推理结束时间
        inference_time = time.time() - start_time
        total_inference_time += inference_time
        
        # 收集预测和真实标签
        all_preds.append(predictions)
        all_true.append(y_true.numpy())
    
    # 将所有批次的预测和真实标签合并
    preds = np.concatenate(all_preds, axis=0)
    y_true = np.concatenate(all_true, axis=0)
    
    # 计算回归评估指标
    mse = np.mean((y_true - preds) ** 2)
    mae = np.mean(np.abs(y_true - preds))
    r2 = 1 - np.sum((y_true - preds) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
    # 方向准确率（预测上涨/下跌方向是否正确）
    direction_accuracy = np.mean(np.sign(preds) == np.sign(y_true))
    
    metrics = {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'direction_accuracy': direction_accuracy,
        'inference_time': total_inference_time,
        'predictions': preds,
        'true_labels': y_true
    }
    
    return metrics

def benchmark_model(session, dataloader, num_runs=10):
    """
    对模型进行性能基准测试
    
    参数:
    session: ONNX模型会话
    dataloader: 数据加载器
    num_runs: 运行次数
    
    返回:
    性能指标字典
    """
    if session is None:
        return None
    
    input_info, _ = get_model_info(session)
    input_names = list(input_info.keys())
    
    # 预热
    for batch in dataloader:
        code_ids, X, _ = batch
        input_dict = {
            input_names[0]: code_ids.numpy(),
            input_names[1]: X.numpy()
        }
        session.run(None, input_dict)
        break  # 只预热一个批次
    
    # 记录推理时间
    inference_times = []
    for _ in range(num_runs):
        for batch in dataloader:
            code_ids, X, _ = batch
            input_dict = {
                input_names[0]: code_ids.numpy(),
                input_names[1]: X.numpy()
            }
            start_time = time.time()
            session.run(None, input_dict)
            inference_times.append(time.time() - start_time)
    
    # 计算性能指标
    avg_time = np.mean(inference_times)
    std_time = np.std(inference_times)
    min_time = np.min(inference_times)
    max_time = np.max(inference_times)
    
    performance = {
        'avg_inference_time': avg_time,
        'std_inference_time': std_time,
        'min_inference_time': min_time,
        'max_inference_time': max_time,
        'samples_per_second': 1.0 / avg_time
    }
    
    return performance


def run_model_comparison(config_path=None, models_config=None, test_config=None, args=None):
    """
    根据配置文件或直接传入的配置来比较多个模型
    
    参数:
    config_path: 配置文件路径，JSON格式
    models_config: 直接传入的模型配置列表
    test_config: 测试配置
    args: 数据集参数
    """
    import json
    
    if config_path is not None:
        # 从配置文件加载模型配置
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            models_config = config.get('models', [])
            test_config = config.get('test_config', {})
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return
    elif models_config is None:
        # 如果没有提供配置，使用默认配置
        models_config = [
            {
                "path": "./models/time_series_model1dc.onnx",
                "name": "TimeSeriesModel1dc"
            },
            {
                "path": "./models/time_series_model2dm.onnx",
                "name": "TimeSeriesModel2dm"
            }
        ]

    if test_config is None:
        test_config = {
            "num_runs": 10,
            "batch_size": 32,
            "save_path": 'model_comparison_results'
        }
    
    # 提取模型路径、名称
    model_paths = [model['path'] for model in models_config]
    model_names = [model['name'] for model in models_config]
    
    # 获取测试配置
    num_runs = test_config.get('num_runs', 10)
    save_path = test_config.get('save_path', 'model_comparison_results')
    
    # 确保结果保存目录存在
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    
    print(f"将比较以下 {len(model_paths)} 个模型:")
    for i, (name, path) in enumerate(zip(model_names, model_paths)):
        print(f"{i+1}. {name}: {path} (回归模型)")
    
    # 加载所有模型
    sessions = []
    for model_path in model_paths:
        sessions.append(load_onnx_session(model_path))
    
    # 获取测试数据
    print("\n加载数据集...")
    if args is None:
        raise ValueError("args is None")
    
    # 获取数据集
    dataset = load_dataset(args)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, num_workers=0, shuffle=False)
    
    # 评估每个模型
    evaluation_results = []
    benchmark_results = []
    
    for i, (session, model_name) in enumerate(zip(sessions, model_names)):
        print(f"\n正在评估模型 ({i+1}/{len(model_names)}): {model_name}")
        
        if session is None:
            print(f"跳过模型 {model_name}，因为加载失败")
            continue
        
        # 评估模型
        metrics = evaluate_onnx_model(session, dataloader)
        if metrics:
            metrics['model_name'] = model_name
            evaluation_results.append(metrics)
            
            # 性能基准测试
            print(f"正在进行性能基准测试... (运行 {num_runs} 次)")
            performance = benchmark_model(session, dataloader, num_runs=num_runs)
            performance['model_name'] = model_name
            benchmark_results.append(performance)
    
    if not evaluation_results:
        print("没有成功评估任何模型，请检查模型路径是否正确")
        return
    
    # 打印评估结果
    print("\n=== 模型性能比较 ===")
    
    # 回归模型评估结果
    eval_df = pd.DataFrame([
        {
            '模型': res['model_name'],
            'MSE': res['mse'],
            'MAE': res['mae'],
            'R²': res['r2'],
            '方向准确率': res['direction_accuracy'],
            '推理时间(s)': res['inference_time']
        } for res in evaluation_results
    ])
    
    print("\n评估指标:")
    print(eval_df)
    
    # 保存评估指标到CSV
    eval_csv_path = os.path.join(save_path, 'evaluation_metrics.csv')
    eval_df.to_csv(eval_csv_path, index=False)
    print(f"评估指标已保存到: {eval_csv_path}")
    
    # 打印基准测试结果
    bench_df = pd.DataFrame([
        {
            '模型': res['model_name'],
            '平均推理时间(s)': res['avg_inference_time'],
            '标准差(s)': res['std_inference_time'],
            '最小时间(s)': res['min_inference_time'],
            '最大时间(s)': res['max_inference_time'],
            '每秒样本数': res['samples_per_second']
        } for res in benchmark_results
    ])
    
    print("\n性能基准测试:")
    print(bench_df)
    
    # 保存基准测试结果到CSV
    bench_csv_path = os.path.join(save_path, 'benchmark_results.csv')
    bench_df.to_csv(bench_csv_path, index=False)
    print(f"基准测试结果已保存到: {bench_csv_path}")
    
    # 可视化结果
    fig_path = os.path.join(save_path, 'model_comparison.png')
    visualize_results(evaluation_results, benchmark_results, model_names, save_path=fig_path)
    
    return evaluation_results, benchmark_results

def visualize_results(evaluation_results, benchmark_results, model_names, save_path='model_comparison.png'):
    """可视化回归模型比较结果"""
    # 根据模型数量调整图表
    model_count = len(model_names)
    
    plt.figure(figsize=(18, 10))
    
    # 1. 回归评估指标对比图
    plt.subplot(2, 2, 1)
    metrics = ['mse', 'mae', 'r2', 'direction_accuracy']
    metrics_labels = ['MSE', 'MAE', 'R²', '方向准确率']
    metrics_data = {model: [] for model in model_names}
    
    for metric in metrics:
        for res in evaluation_results:
            if metric in res:
                metrics_data[res['model_name']].append(res[metric])
            else:
                metrics_data[res['model_name']].append(0)
    
    x = np.arange(len(metrics))
    width = 0.8 / model_count
    
    for i, model in enumerate(model_names):
        if model in metrics_data:
            plt.bar(x + i * width, metrics_data[model], width, label=model)
    
    plt.ylabel('分数')
    plt.title('回归模型评估指标对比')
    plt.xticks(x + width * (model_count - 1) / 2, metrics_labels)
    plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=min(5, model_count))
    
    # 2. 预测值与真实值对比图 (仅展示第一个模型的前100个样本)
    plt.subplot(2, 2, 2)
    model_name = evaluation_results[0]['model_name']
    preds = evaluation_results[0]['predictions']
    true_vals = evaluation_results[0]['true_labels']
    
    # 限制显示的样本数量
    sample_limit = min(100, len(preds))
    x_indices = np.arange(sample_limit)
    
    plt.plot(x_indices, true_vals[:sample_limit], label='真实值', marker='o', markersize=3)
    plt.plot(x_indices, preds[:sample_limit], label='预测值', marker='x', markersize=3)
    plt.title(f'{model_name} 预测值与真实值对比 (前{sample_limit}个样本)')
    plt.xlabel('样本索引')
    plt.ylabel('值')
    plt.legend()
    
    # 3. 推理时间对比图
    plt.subplot(2, 2, 3)
    inference_times = [res['inference_time'] for res in evaluation_results]
    model_names_eval = [res['model_name'] for res in evaluation_results]
    
    plt.bar(model_names_eval, inference_times)
    plt.ylabel('时间 (秒)')
    plt.title('模型推理时间对比')
    plt.xticks(rotation=45 if model_count > 3 else 0)
    
    # 4. 基准测试 - 每秒样本数
    plt.subplot(2, 2, 4)
    samples_per_second = [res['samples_per_second'] for res in benchmark_results]
    model_names_bench = [res['model_name'] for res in benchmark_results]
    
    plt.bar(model_names_bench, samples_per_second)
    plt.ylabel('每秒样本数')
    plt.title('模型吞吐量对比')
    plt.xticks(rotation=45 if model_count > 3 else 0)
    
    plt.tight_layout()
    plt.savefig(save_path)
    print(f"可视化结果已保存到: {save_path}")
    plt.show()

def load_dataset(args):
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    args.num_embeds = eval(args.num_embeds)
    args.ds_files = eval(args.ds_files)
    args.fut_codes = eval(args.fut_codes)
    
    # 处理conv_channels参数
    if args.conv_channels is not None:
        args.conv_channels = eval(args.conv_channels)
        
    timeenc = 0
    if args.embed_time == 'timeF':
        timeenc = 1
    elif args.embed_time == 'fixed':
        timeenc = 0

    args.seq_len = args.num_channel

    print(args)
    dataset = get_dataset(ds_files=args.ds_files,
                          ins_nums=args.ins_nums,
                          is_normal=args.is_normal,
                          verbose=args.verbose,
                          fut_codes=args.fut_codes,
                          data_path=args.data_path,
                          start_time=args.start_time,
                          end_time=args.end_time,
                          timeenc=timeenc,
                          model_type=args.model_type,
                          seq_len=args.seq_len,
                          pred_len=args.pred_len,                          
                          )
    
    # 加载数据集: 直接从dataframe中获取数据，不分割打包
    dataset.load_data()
    args.ins_nums = dataset.get_ins_nums()
    print(f"ins_nums: {args.ins_nums}")    
    return dataset

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='比较多个ONNX模型的性能')

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', type=str)
    parser.add_argument('--ds_files', default='["main.2023", "main.2024"]', type=str)
    parser.add_argument('--start_time', default='', type=str)
    parser.add_argument('--end_time', default='', type=str)
    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)
    parser.add_argument('--is_normal', default=True, action='store_true')
    parser.add_argument('--verbose', default=False, action='store_true')
    parser.add_argument('--fut_codes', default='MAIN_SEL_FUT_CODES', choices=['MAIN_FUT_CODES', 'MAIN_SEL_FUT_CODES', 'SF_FUT_CODES'], type=str)
    parser.add_argument('--data_path', default='e:/featdata/main', type=str)
    parser.add_argument('--model_type', default=0, type=int)
    parser.add_argument('--seq_len', type=int, default=30, help='input sequence length')
    parser.add_argument('--pred_len', type=int, default=1, help='prediction sequence length')
    parser.add_argument('--embed_time', type=str, default='fixed', help='time features encoding, options:[timeF, fixed, learned, None]')

    # model
    parser.add_argument('--num_embeds', default='[72, 5, 11]', type=str, help="[72, 5, 11] number of embeddings for each category")
    parser.add_argument('--num_channel', default=10, type=int) # 通道数,与上面的DataHander中的win保持一致
    parser.add_argument('--num_input', default=51, type=int)
    # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
    # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
    # "out_channels": (16, 32, 800, 256), 
    # "out_channels": (24, 48, 1200, 256), 
    # parser.add_argument('--out_channels', default='(16, 32, 800, 256)', type=str)
    parser.add_argument('--out_channels', default='(24, 48, 1200, 1200)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)        

    # 新增 TimeSeriesModel2drV1 的参数
    parser.add_argument('--num_conv_layers', default=2, type=int, help='卷积层数量')
    parser.add_argument('--conv_channels', default=None, type=str, help='每层卷积通道数，格式如"[32,64]"，为None时自动计算')
    parser.add_argument('--use_residual', action='store_true', help='是否使用残差连接')
    parser.add_argument('--use_attention', action='store_true', help='是否使用注意力机制')
    parser.add_argument('--num_outputs', default=1, type=int, help='输出变量数量')
    parser.add_argument('--probabilistic', action='store_true', help='是否进行概率预测')
    parser.add_argument('--weight_decay', default=0.0, type=float, help='L2正则化系数')
    parser.add_argument('--inference_mode', action='store_true', help='是否为推理模式')

    parser.add_argument('--config', type=str, help='配置文件路径，JSON格式')
    parser.add_argument('--models_path', type=str, default="e:/lab/RoboQuant/pylab/model", help='模型文件路径')
    args = parser.parse_args()
    args.data_path = "f:/featdata/trd"
    args.ds_files = "['trd.2024']"
    args.start_time = "2024-10-01"
    args.end_time = "2024-12-31"
    args.ds_name = "15HF"
    args.num_channel = 15
    args.batch_size = 64
    args.out_channels = "(32, 64, 1600, 1600)"
    
    if args.config:
        run_model_comparison(config_path=args.config, args=args)
    else:
        # 示例配置
        models_config = [
            {
                "path": f"{args.models_path}/FUT_CV2DR_F_15HF_1600_031500_0.093_ls.onnx",
                "name": "TimeSeriesModel1dc_v2"
            },
            {
                "path": f"{args.models_path}/FUT_CV2DR_F_15HF_1600_031321_0.094_ls.onnx",
                "name": "TimeSeriesModel1dc_v2"
            },
        ]
        
        test_config = {
            "num_runs": 10,
            "save_path": "./comparison_results"
        }
        
        run_model_comparison(models_config=models_config, test_config=test_config, args=args)
