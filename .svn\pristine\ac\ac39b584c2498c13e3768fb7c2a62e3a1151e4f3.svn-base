import sys, site
from pathlib import Path
sys.path.append("e:/github/qlib")
import numpy as np
import pandas as pd

import matplotlib.pyplot as plt
import ipywidgets as widgets

import qlib
from qlib.config import REG_CN
from qlib.utils import init_instance_by_config, flatten_dict
from qlib.workflow import R

# from sklearn.metrics import roc_auc_score

provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
qlib.init(provider_uri=provider_uri, region=REG_CN)

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": "",
    "data_loader": {
        "class": "AFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "direct": "long",
            "model_name": "GBDT",
            "model_name_suff": "",
            "model_path": "e:/lab/RoboQuant/pylab/model",
            "data_path": "e:/lab/RoboQuant/pylab/data",
            'portfolios': ['00200910081133001', '00171106132928000'],  
        }
    },
}

dataset_config = {
    "class": "AFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    },
}

task = {
    "model_gbdt_long": {
        "class": "LGBModel",
        "module_path": "pyqlab.contrib.model.gbdt",
        "kwargs": {
            "loss": "mse",
            "max_depth": 8,
            "num_threads": 2,

            # 'bagging_fraction': 0.9670993945018912,
            # 'bagging_freq': 1,
            # 'colsample_bytree': 0.8046160203709426,
            # 'feature_fraction': 0.43869704434082607,
            # 'lambda_l1': 0.2153493475269645,
            # 'lambda_l2': 0.003100275287867336,
            # 'learning_rate': 0.0884079898658797,
            # 'min_child_samples': 66,
            # 'min_data_in_leaf': 2,
            # 'num_leaves': 921,
            # 'subsample': 0.3008843823490591,

            'bagging_fraction': 0.7086542347657472,
            'bagging_freq': 1,
            'colsample_bytree': 0.8648827297881316,
            'feature_fraction': 0.5872925150952681,
            'lambda_l1': 5.733422010814397e-07,
            'lambda_l2': 0.0022518114062579993,
            'learning_rate': 0.019132928629388833,
            'max_depth': 48,
            'min_child_samples': 87,
            'min_data_in_leaf': 6,
            'num_leaves': 367,
            'subsample': 0.9663526157250479,
            # 'bagging_fraction': 0.985502811073365,
            # 'bagging_freq': 1,
            # 'boosting_type': 'dart',
            # 'colsample_bytree': 0.7338579930419087,
            # 'feature_fraction': 0.7508471604245855,
            # 'lambda_l1': 0.00010536306916948468,
            # 'lambda_l2': 2.183723144754047,
            # 'learning_rate': 0.09693783615822299,
            # 'max_depth': 63,
            # 'min_child_samples': 50,
            # 'min_data_in_leaf': 3,
            # 'num_leaves': 917,
            # 'subsample': 0.6401441709578959,
            #####           
            # 'bagging_fraction': 0.9192643837711366,
            # 'bagging_freq': 7,
            # 'colsample_bytree': 0.790582482445364,
            # 'feature_fraction': 0.6577789368974852,
            # 'lambda_l1': 2.9680267664240568e-05,
            # 'lambda_l2': 1.2128265654439452,
            # 'learning_rate': 0.09653829841983602,
            # 'min_child_samples': 68,
            # 'min_data_in_leaf': 2,
            # 'num_leaves': 234,
            # 'subsample': 0.552021715835012
        },
    },
    "model_gbdt_short": {
        "class": "LGBModel",
        "module_path": "pyqlab.contrib.model.gbdt",
        "kwargs": {
            "loss": "mse",
            "max_depth": 8,
            "num_threads": 2,

            'bagging_fraction': 0.8449015662753931,
            'bagging_freq': 4,
            'colsample_bytree': 0.6155567340471919,
            'feature_fraction': 0.7048880701726129,
            'lambda_l1': 0.0587275256879762,
            'lambda_l2': 7.941698939519992e-05,
            'learning_rate': 0.05558224437597306,
            'max_depth': 48,
            'min_child_samples': 38,
            'min_data_in_leaf': 10,
            'num_leaves': 311,
            'subsample': 0.45939538844556416
        },
    },
    "model_xgbdt": {
        "class": "XGBModel",
        "module_path": "pyqlab.contrib.model.xgboost",
        "kwargs": {
            "max_depth": 8,
            "learning_rate": 0.1,
            "n_estimators": 1000,
            "verbosity": 0,
            "silent": None,
            "objective": 'binary:logistic',
            "booster": 'gbtree',
            "n_jobs": -1,
            "nthread": None,
            "gamma": 0,
            "min_child_weight": 1,
            "max_delta_step": 0,
            "subsample": 0.7,
            "colsample_bytree": 1,
            "colsample_bylevel": 1,
            "colsample_bynode": 1,
            "reg_alpha": 0,
            "reg_lambda": 1,
            "scale_pos_weight": 1,
            "base_score": 0.5,
            "random_state": 0,
            "seed": None
        }
    },
    "model_tabnet": {
        "class": "TabnetModel",
        "module_path": "pyqlab.contrib.model.pytorch_tabnet",
        "kwargs": {
            "d_feat": 87,
            "out_dim": 1,
            "batch_size": 128,
            "n_epochs": 100,
            "early_stop": 30,
            "pretrain": False,
            "seed": 993,
            "GPU": 1,
        }
    },
}

def plt_show(df, title=""):
    wdt = widgets.Output()
    wdt.clear_output(wait=False)
    with wdt:
        ylim = [df.min().min(), df.quantile(0.95).max()]
        ylim[0] -= (ylim[1] - ylim[0]) * 0.05
        df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)
        plt.show()

def display_result(evals_result):
    for key, val in evals_result.items():
        if not isinstance(val, dict):
            plt_show(pd.DataFrame(evals_result), key)
            break
        else:
            plt_show(pd.DataFrame(val), key)
            
pfs = {
    'MIX': ['00200910081133001', '00171106132928000'],
    'ALL': ['00211229152555000', '00171106132928000', '00200910081133001', '00170623114649000'],
    '5R': ['00211229152555000', '00171106132928000'],
    '7R': ['00200910081133001', '00170623114649000'],
}

def trainer(show=True, pfs_name=None, train_result={}):
    for direct in ["long", "short"]:
        data_handler_config["data_loader"]["kwargs"]["direct"] = direct
        if pfs_name:
            data_handler_config["data_loader"]["kwargs"]["portfolios"] = pfs[pfs_name]
            data_handler_config["data_loader"]["kwargs"]["model_name_suff"] = pfs_name
        # model initiaiton
        model = init_instance_by_config(task[f"model_gbdt_{direct}"])
        dataset = init_instance_by_config(dataset_config)

        # start exp to train model
        with R.start(experiment_name="train_model"):
            R.log_params(**flatten_dict(task))
            path=data_handler_config["data_loader"]["kwargs"]["model_path"]
            name=data_handler_config["data_loader"]["kwargs"]["model_name"]
            
            if pfs_name:
                model_name=f"{name}_{pfs_name}_{direct}"
            else:
                model_name=f"{name}_{direct}"
            model_path=f"{path}/{model_name}.txt"

            result={}
            best_train, best_valid = model.fit(
                dataset,
                evals_result=result,
                save_path=model_path,
                # save_jit_script=False
            )
            train_result[model_name] = []
            train_result[model_name].append(best_train)
            train_result[model_name].append(best_valid)
            # R.save_objects(trained_model=model)
            # rid = R.get_recorder().id
            # preds = np.array(model.predict(dataset, segment="valid"))
            # valid_auc = roc_auc_score(y_score=preds[:,1], y_true=y_valid)
            train_result[model_name].append(model.test(dataset, segment="valid"))
            if show:
                display_result(result)

if __name__ == "__main__":
    result={}
    for pf in pfs.keys():
        trainer(show=False, pfs_name=pf, train_result=result)
    print("============train result=============")
    for key, item in result.items():
        print(key, item)
    print("============train result=============")
