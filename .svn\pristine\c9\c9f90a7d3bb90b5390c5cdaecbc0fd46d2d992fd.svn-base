import torch
import torch.nn as nn
import numpy as np
import math

class TimeSeriesModel2dc(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self,
                  num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  out_channels=(32, 64, 1152, 256),
                  ins_nums=(0,51,51,8),
                  activation="relu",
                  pooling="max",
                ):
        super(TimeSeriesModel2dc, self).__init__()
        print(num_embeds, num_channel, num_input, dropout, out_channels, ins_nums)
        assert len(ins_nums) == 4 and ins_nums[0] == 0 and ins_nums[1] == ins_nums[2]
        num_dims = []
        for num_embed in num_embeds:
            num_dims.append(math.ceil(np.sqrt(num_embed)))
        dims_sum = sum(num_dims)
        for i in range(len(num_dims)):
            num_dims[i] = int(num_dims[i]/dims_sum * (ins_nums[2] - ins_nums[3]))
        
        if sum(num_dims) > ins_nums[2] - ins_nums[3]:
            num_dims[0] -= (sum(num_dims) - (ins_nums[2] - ins_nums[3]))
        elif sum(num_dims) < ins_nums[2] - ins_nums[3]:
            num_dims[0] += ((ins_nums[2] - ins_nums[3]) - sum(num_dims))
        self.embedding_layers = nn.ModuleList()
        for i in range(len(num_dims)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i]))
        self.flatten = nn.Flatten()

        if activation == "relu":
            activation = nn.ReLU()
        elif activation == "gelu":
            activation = nn.GELU()
        elif activation == "prelu":
            activation = nn.PReLU()
        elif activation == "leakyrelu":
            activation = nn.LeakyReLU()
        else:
            raise Exception("activation must be relu or gelu")
        
        if pooling == "max":
            pooling = nn.MaxPool2d(kernel_size=2)
        elif pooling == "avg":
            pooling = nn.AvgPool2d(kernel_size=2)
        else:
            raise Exception("pooling must be max or avg")
        
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=(3,3), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[0]),
            activation, # nn.ReLU(),
            # nn.MaxPool2d(kernel_size=2),
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=(3,3), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[1]),
            activation,
            pooling,
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation,
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Sequential(
            nn.Linear(out_channels[3], 1),
            nn.BatchNorm1d(1),
            nn.Sigmoid(),
        )


    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([x, embedded_data], dim=-1)
        x = x.reshape(x.shape[0], x.shape[1], 3, x.shape[2]//3)
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)
        return x.view(-1)
        