import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary

from pyqlab.models import PLData
from pyqlab.models import PLFtsModel

from pyqlab.data import get_dataset
from pyqlab.data import FTSDataset
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES
from pyqlab.pl.utils import get_best_saved_model_filename

import torch
from torch.utils.data import Subset
from sklearn.model_selection import KFold
import os
import random
from time import time
from datetime import datetime

def get_trainer_name(args):
    time_encoding = 'T' if args.embed_time == 'timeF' else 'F'
    if  "IF" in args.fut_codes:
        market="SF"
    else:
        market="FUT"
    return f"{market}_{args.version}_{time_encoding}_{args.ds_name}_{args.out_channels[2]}"

def save_model_as_to_onnx(args, dataset):
    trainer_name=get_trainer_name(args)
    print(f"=== Model Dir: {trainer_name} ===")
    model_files = get_best_saved_model_filename(
        log_dir=args.log_dir,
        sub_dir=trainer_name
    )
    if len(model_files) == 0:
        raise Exception("No saved model found!")
    print(f"=== Export best model files ===")
    for model_file, best_score in model_files.items():
        if best_score > args.best_score:
            print(f"Skip model file: {model_file}  {best_score}")
            continue
        else:
            print(f"Export model file: {model_file}  {best_score}")
        try:
            model = PLFtsModel.load_from_checkpoint(checkpoint_path=model_file)
        except Exception as e:
            print(f"Error: {e}")

        tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
        model_name = f'{trainer_name}_{tm_str}_%.3f_{args.direct}' % best_score
        embedding = torch.zeros(1, args.num_channel, len(args.num_embeds)).to(torch.int32)
        example_input = torch.rand(1, args.num_channel, sum(args.ins_nums)).to(torch.float32)
        if hasattr(model.model, 'export_onnx'):
            print(f"1.Export model to ONNX: {model_name}.onnx")
            model.model.export_onnx(f"{args.model_dir}/{model_name}.onnx", embedding, example_input)
        else:
            print(f"2.Export model to ONNX: {model_name}.onnx")
            model.eval()
            # 设置为推理模式
            if hasattr(model.model, 'inference_mode'):
                model.model.inference_mode = True
                print("已将模型设置为推理模式")
            
            model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (embedding, example_input), export_params=True)
        dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")
        print(f"Model saved to： {args.model_dir}/{model_name}.onnx")

    
def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        every_n_epochs=args.max_epochs,
        mode='min',
        save_last=False,
        verbose=True,
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    return callbacks


def main(args):
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    args.num_embeds = eval(args.num_embeds)
    args.ds_files = eval(args.ds_files)
    args.fut_codes = eval(args.fut_codes)
    args.seed = random.randint(0, 10000)
    
    # 处理conv_channels参数
    if args.conv_channels is not None:
        args.conv_channels = eval(args.conv_channels)
        
    timeenc = 0
    if args.embed_time == 'timeF':
        timeenc = 1
    elif args.embed_time == 'fixed':
        timeenc = 0

    args.seq_len = args.num_channel

    print(args)
    dataset = get_dataset(ds_files=args.ds_files,
                          ins_nums=args.ins_nums,
                          is_normal=args.is_normal,
                          verbose=args.verbose,
                          fut_codes=args.fut_codes,
                          data_path=args.data_path,
                          start_time=args.start_time,
                          end_time=args.end_time,
                          timeenc=timeenc,
                          model_type=args.model_type,
                          seq_len=args.seq_len,
                          pred_len=args.pred_len,                          
                          )
    
    if args.save_as_to_onnx:
        save_model_as_to_onnx(args, dataset)
        return
        
    # 准备数据集: 从数据集中获取数据并按win分割打包，比较耗时耗内存
    # emb_data, x_data, y_data = dataset.prepare(
    #     direct=args.direct,
    #     win=args.num_channel,
    #     filter_win=args.filter_win,
    # )
    # dataset = TensorDataset(torch.tensor(emb_data), torch.tensor(x_data), torch.tensor(y_data))

    # 加载数据集: 直接从dataframe中获取数据，不分割打包
    dataset.load_data()

    args.ins_nums = dataset.get_ins_nums()
    print(f"ins_nums: {args.ins_nums}")

    pl.seed_everything(args.seed)

    trainer_name=get_trainer_name(args)
    print(f"=== Training {trainer_name} ===")

    model = None

    # 使用 KFold 分割数据集
    kfold = KFold(n_splits=args.k_folds, shuffle=True, random_state=args.seed)

    # 创建一个数据模块列表，每个数据模块对应一个 fold
    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):        
        print(f"=== Training fold {fold} ===")
        train_data = Subset(dataset, train_idx)
        val_data = Subset(dataset, val_idx)
        data_module = PLData(train_data, val_data, batch_size=args.batch_size, num_workers=args.num_workers, seed=args.seed)

        # if fold > 0 and callbacks[0].stopped_epoch is not None:
        #     # 加载之前训练的模型
        #     print(f"Fold: {fold} Loading model from {callbacks[1].best_model_path}")
        #     model = PLModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        if model is None:
            if args.resume:
                model_path, _ = get_best_saved_model_filename(log_dir=args.log_dir, sub_dir=trainer_name, only_best=True)
                if model_path is None:
                    raise Exception("No saved model found!")
                print(f"=== Resume Training ===")
                print(f"Loading model from {model_path}")
                try:
                    model = PLFtsModel.load_from_checkpoint(checkpoint_path=model_path)
                except Exception as e:
                    print(f"Error: {e}")
            else:
                print(f"=== New Training ===")
                model = PLFtsModel(**vars(args))

        # 创建训练器
        logger = TensorBoardLogger(save_dir=args.log_dir, name=trainer_name)
        # 创建回调函数
        callbacks = load_callbacks(args)
        trainer = Trainer(
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
        )
        trainer.fit(model, data_module)
        # 在每个数据集的训练之后，执行其他操作（如保存模型、记录日志等）
        # log_metrics(logger, trainer)

    # 训练完成后，保存编译最后一个模型
    if callbacks[0].stopped_epoch is not None:
        # 加载之前训练的模型
        print(f"Best model to save {callbacks[1].best_model_path}")
        best_score=callbacks[1].best_model_score.cpu().numpy()
        model = PLFtsModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        model.eval()  # 设置模型为推理模式

        tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
        model_name = f'{trainer_name}_{tm_str}_%.3f_{args.direct}' % best_score
        
        # 设置模型为推理模式
        model.freeze()
        if hasattr(model.model, 'inference_mode'):
            model.model.inference_mode = True
            print("已将模型设置为推理模式")
        
        embedding = torch.zeros(1, args.num_channel, len(args.num_embeds)).to(torch.int32)
        example_input = torch.rand(1, args.num_channel, sum(args.ins_nums)).to(torch.float32)
        model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (embedding, example_input), export_params=True)
        dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")

        #查看模型大小
        model.example_input_array = (embedding, example_input)
        summary = ModelSummary(model,max_depth=-1)
        print(summary) 

        print(f"Model saved to： {model_name}.onnx")
        print("=== Training Finished ===\n\n")



if __name__ == '__main__':
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', type=str)
    parser.add_argument('--ds_files', default='["main.2023", "main.2024"]', type=str)
    parser.add_argument('--start_time', default='', type=str)
    parser.add_argument('--end_time', default='', type=str)
    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)
    parser.add_argument('--is_normal', default=True, action='store_true')
    parser.add_argument('--verbose', default=False, action='store_true')
    parser.add_argument('--fut_codes', default='MAIN_SEL_FUT_CODES', choices=['MAIN_FUT_CODES', 'MAIN_SEL_FUT_CODES', 'SF_FUT_CODES'], type=str)
    parser.add_argument('--data_path', default='e:/featdata/main', type=str)
    parser.add_argument('--model_type', default=0, type=int)
    parser.add_argument('--seq_len', type=int, default=30, help='input sequence length')
    parser.add_argument('--pred_len', type=int, default=1, help='prediction sequence length')
    parser.add_argument('--embed_time', type=str, default='fixed', help='time features encoding, options:[timeF, fixed, learned, None]')

    # Data module ===========================
    parser.add_argument('--batch_size', default=256, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)

    # Model Hyperparameters =================
    parser.add_argument('--version', default='CV2DR', type=str)
    parser.add_argument('--model_name', default='time_series_model2dr_v2', type=str)
    parser.add_argument('--model_path', default='pyqlab.models.fintimeseries', type=str)
    parser.add_argument('--loss', default='mse', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)

    # model
    parser.add_argument('--num_embeds', default='[72, 5, 11]', type=str, help="[72, 5, 11] number of embeddings for each category")
    parser.add_argument('--num_channel', default=10, type=int) # 通道数,与上面的DataHander中的win保持一致
    parser.add_argument('--num_input', default=51, type=int)
    # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
    # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
    # "out_channels": (16, 32, 800, 256), 
    # "out_channels": (24, 48, 1200, 256), 
    # parser.add_argument('--out_channels', default='(16, 32, 800, 256)', type=str)
    parser.add_argument('--out_channels', default='(24, 48, 1200, 1200)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)
    parser.add_argument('--dropout', default=0.5, type=float)
    parser.add_argument('--kernel_size', default=3, type=int)
    parser.add_argument('--activation', default='relu', choices=['relu', 'gelu', 'prelu', 'leakyrelu', 'selu', 'elu', 'mish'], type=str)
    parser.add_argument('--pooling', default='max', choices=['max', 'avg', 'adaptive_max', 'adaptive_avg'], type=str)

    # 新增 TimeSeriesModel2drV1 的参数
    parser.add_argument('--num_conv_layers', default=2, type=int, help='卷积层数量')
    parser.add_argument('--conv_channels', default=None, type=str, help='每层卷积通道数，格式如"[32,64]"，为None时自动计算')
    parser.add_argument('--use_residual', action='store_true', help='是否使用残差连接')
    parser.add_argument('--use_attention', action='store_true', help='是否使用注意力机制')
    parser.add_argument('--num_outputs', default=1, type=int, help='输出变量数量')
    parser.add_argument('--probabilistic', action='store_true', help='是否进行概率预测')
    parser.add_argument('--weight_decay', default=0.0, type=float, help='L2正则化系数')
    parser.add_argument('--inference_mode', action='store_true', help='是否为推理模式')

    # LR Scheduler
    # lr_scheduler 可以有以下选项：
    #     'step'：使用步进式学习率调度器
    #     'cosine'：使用余弦退火学习率调度器
    #     'plateau'：使用学习率衰减调度器
    #     'exponential'：使用指数衰减学习率调度器
    #     'reduce_on_plateau'：使用学习率衰减调度器，并在验证集上监测指标不再改善时降低学习率
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)
    parser.add_argument('--optimizer', default='adam', choices=['adam', 'adamw'], type=str)

    # Restart Control
    parser.add_argument('--resume', action='store_true')

    # Training Info
    parser.add_argument('--max_epochs', default=7, type=int)
    parser.add_argument('--early_stop', default=5, type=int)
    parser.add_argument('--min_delta', default=1e-3, type=float) 
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    parser.add_argument('--sub_dir', default='', type=str)
    
    # Others
    parser.add_argument('--model_dir', default='model', type=str)
    # Reset Some Default Trainer Arguments' Default Values
    # parser.set_defaults(max_epochs=10)
    parser.add_argument('--save_as_to_onnx', action='store_true')
    parser.add_argument('--best_score', default=0.6, type=float)

    args = parser.parse_args()

    main(args)
