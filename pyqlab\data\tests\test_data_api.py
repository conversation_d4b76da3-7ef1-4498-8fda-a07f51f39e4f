import unittest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock
import sys
import os

# 添加模块路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from pyqlab.data.data_api import get_dataset

class TestDataAPI(unittest.TestCase):
    """测试data_api模块的功能"""
    
    @patch('pyqlab.data.data_api.DataHandler')
    def test_get_dataset(self, mock_data_handler):
        """测试get_dataset函数"""
        # 设置模拟的DataHandler
        mock_handler_instance = MagicMock()
        mock_data_handler.return_value = mock_handler_instance
        
        # 调用get_dataset函数
        ds_files = ['file1.csv', 'file2.csv']
        ins_nums = 10
        is_normal = True
        verbose = True
        fut_codes = ['A', 'B', 'C']
        data_path = 'test_data_path'
        start_time = '2020-01-01'
        end_time = '2021-01-01'
        timeenc = 1
        model_type = 1
        seq_len = 30
        label_len = 10
        pred_len = 5
        
        dataset = get_dataset(
            ds_files=ds_files,
            ins_nums=ins_nums,
            is_normal=is_normal,
            verbose=verbose,
            fut_codes=fut_codes,
            data_path=data_path,
            start_time=start_time,
            end_time=end_time,
            timeenc=timeenc,
            model_type=model_type,
            seq_len=seq_len,
            label_len=label_len,
            pred_len=pred_len
        )
        
        # 验证DataHandler是否被正确初始化
        mock_data_handler.assert_called_once()
        
        # 验证返回值是否正确
        self.assertEqual(dataset.model_type, model_type)
        self.assertEqual(dataset.seq_len, seq_len)
        self.assertEqual(dataset.label_len, label_len)
        self.assertEqual(dataset.pred_len, pred_len)
        
        # 验证handler的setup_data方法是否被调用
        mock_handler_instance.setup_data.assert_called_once()

if __name__ == '__main__':
    unittest.main() 