@REM market [fut, stk]
@REM period [day, min5, min1]
@REM block_name [main, top, hot, acc, sf, hs300, zz500, zz1000]

python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\tdxhq.py ^
--update_history ^
--start_date 20240101 ^
--rq_path f:/hqdata/tsdb ^
--market fut ^
--period min1 ^
--block_name main

@REM python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\tdxhq.py ^
@REM --update_history ^
@REM --start_date 20250502 ^
@REM --rq_path f:/hqdata/tsdb ^
@REM --market fut ^
@REM --period min5 ^
@REM --block_name sf

@REM python E:\lab\RoboQuant\pylab\pyqlab\data\datatools\tdxhq.py ^
@REM --update_history ^
@REM --start_date 20250502 ^
@REM --rq_path f:/hqdata/tsdb ^
@REM --market fut ^
@REM --period day ^
@REM --block_name sf

