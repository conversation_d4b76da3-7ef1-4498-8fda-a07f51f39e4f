# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
"""
Handler对原始数据做处理，按需提供给模型
"""
# coding=utf-8
import abc
import bisect
import logging
import warnings
from inspect import getfullargspec
from typing import Callable, Dict, Union, Tuple, List, Iterator, Optional

import pandas as pd
import numpy as np
import json
from pprint import pprint
from copy import deepcopy
from sklearn.model_selection import KFold
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

from qlib.log import get_module_logger, TimeInspector
# from qlib.data import D
# from qlib.config import C
# from qlib.utils import parse_config, transform_end_date, init_instance_by_config
# from qlib.utils.serial import Serializable
# from qlib.utils import fetch_df_by_index, fetch_df_by_col
# from qlib.utils import lazy_sort_index
from pathlib import Path
from qlib.data.dataset.loader import DataLoader
from qlib.data.dataset.handler import DataHandler
from .loader import AFDataLoader, AHFDataLoader
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES

# from . import processor as processor_module
from . import loader as data_loader_module
from functools import partial

class DataHandlerAF(DataHandler):
    """
    加载raw data进行加工处理的地方
    """
    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader: Union[dict, str, DataLoader]=None,
        valid_fold = 5,
        is_normal = True,
        n_splits=5,
        shuffle=True,
        random_state=42,
        init_data=False,
        fetch_orig=True,
        **kwargs
    ):
        self._valid_fold = valid_fold
        self.n_splits = n_splits
        self.shuffle = shuffle
        self.random_state = random_state
        self._is_normal = is_normal
        self.train_ft_df = pd.DataFrame()
        self.train_lb_df = pd.DataFrame()
        self.valid_ft_df = pd.DataFrame()
        self.valid_lb_df = pd.DataFrame()
        self.direct = 'long'
        self.model_name = ''
        self.model_name_suff = ''
        self.model_path = ''
        self.data_path = ''
        self.sel_lf_names=''
        self.sel_sf_names=''
        self.sel_ct_names=''
        self.le = LabelEncoder()

        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        self.logger = get_module_logger("DataHandlerAF")
        super().__init__(
            instruments=instruments,
            start_time=start_time,
            end_time=end_time,
            data_loader=data_loader,
            init_data=init_data,
            fetch_orig=fetch_orig,
        )

    def _get_factor_cols(self, factor_type="lf"):
        """
        因子列名称
        """
        col_names = []
        if factor_type == "lf":
            for name in self.sel_lf_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "sf":
            for name in self.sel_sf_names: # SEL_SHORT_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "ct":
            col_names.extend(self.sel_ct_names)

        return col_names
    
    def config(self, **kwargs):
        if "data_loader" in kwargs and isinstance(self.data_loader, AFDataLoader):
            # self.data_loader.config(**kwargs["data_loader"]["kwargs"])
            kwargs.pop("data_loader")
        attr_list = {"win", "step", "direct", "model_name", "model_name_suff", "model_path", 
                     "sel_lf_names", "sel_sf_names", "sel_ct_names"}
        for k, v in kwargs["kwargs"].items():
            if k in attr_list:
                setattr(self, k, v)
        # super().config(**kwargs)
        
        self.lf_mean = pd.read_csv(f'{self.data_loader.data_path}/lf_mean.csv', index_col=0)
        self.lf_std = pd.read_csv(f'{self.data_loader.data_path}/lf_std.csv', index_col=0)
        self.sf_mean = pd.read_csv(f'{self.data_loader.data_path}/sf_mean.csv', index_col=0)
        self.sf_std = pd.read_csv(f'{self.data_loader.data_path}/sf_std.csv', index_col=0)
        self.ct_mean = pd.read_csv(f'{self.data_loader.data_path}/ct_mean.csv', index_col=0)
        self.ct_std = pd.read_csv(f'{self.data_loader.data_path}/ct_std.csv', index_col=0)

    def _long_factor_select(self, n):
        if len(self.sel_lf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_lf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _short_factor_select(self, n):
        if len(self.sel_sf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_sf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _context_select(self, n):
        if len(self.sel_ct_names) == 0:
            return 0
        if SNAPSHOT_CONTEXT[n] in self.sel_ct_names:
            return 1
        return 0


    def _get_factor_cols(self, factor_type="lf"):
        """
        因子列名称
        """
        col_names = []
        if factor_type == "lf":
            for name in self.sel_lf_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "sf":
            for name in self.sel_sf_names: # SEL_SHORT_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "ct":
            col_names.extend(self.sel_ct_names)

        return col_names
    
    def _dump_input_param_json(self, ft_df):
        """
        注意：两边特征向量的顺序要一致
        """
        #if self.model_name_suff == "":
        #    return
        f_sel = {}
        f_sel['codes'] = self.le.classes_.tolist()
        f_sel['slow'] = [self._long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [self._short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [self._context_select(n) for n in range(len(SNAPSHOT_CONTEXT))]
        # f_sel['codes'] = sorted(MAIN_FUT_CODES)# sorted(lb_df.code.unique().tolist())

        all_col_names = self.lf_mean.columns.to_list()
        col_names = self._get_factor_cols("lf")
        f_sel['lf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self.sf_mean.columns.to_list()
        col_names = self._get_factor_cols("sf")
        f_sel['sf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self.ct_mean.columns.to_list()
        col_names = self._get_factor_cols("ct")
        f_sel['ct_sel_index'] = [all_col_names.index(name) for name in col_names]

        f_sel['lf_len'] = sum(f_sel['slow'])
        f_sel['sf_len'] = sum(f_sel['fast'])
        f_sel['ct_len'] = sum(f_sel['context'])

        assert f_sel['lf_len'] == len(f_sel['lf_sel_index'])
        assert f_sel['sf_len'] == len(f_sel['sf_sel_index'])
        assert f_sel['ct_len'] == len(f_sel['ct_sel_index'])
    
        #with open(f'{self.data_path}/using_factor.json', 'r') as using_file:
        #    using_factor = json.load(using_file)
        #    f_sel.update(using_factor)
        #f_sel.update(self.interface_params)
        f_sel["input_dim"] = 1
        f_sel["code_encoding"] = 2
        # f_sel["win"] = self.win
        if self.model_name_suff != "":
            jfile_name = f"{self.model_name}_{self.model_name_suff}_{self.direct}"
        else:
            jfile_name = f"{self.model_name}_{self.direct}"
        with open(f'{self.model_path}/{jfile_name}.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)
       
    def standardize(self, group, means, stds):
        code = group.name
        mean = means.loc[code]
        std = stds.loc[code]
        group = (group - mean) / std
        return group
    
    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AFDataLoader):
            return


        with TimeInspector.logt("Loading data"):
            # 加载原始数据
            lb_df, lf_df, sf_df, ct_df = self.data_loader.load(self.instruments, self.start_time, self.end_time)
            lb_df['code_encoded'] = self.le.fit_transform(lb_df['CODE'].values)
            self.logger.info(f"\n============================\n\nlb{lb_df.shape} sf{sf_df.shape} lf{lf_df.shape} ct{ct_df.shape}\n\n============================")
            # self.logger.info(lb_df.columns.tolist())
            # self.logger.info(lf_df.columns.tolist())
            # self.logger.info(sf_df.columns.tolist())
            # self.logger.info(ct_df.columns.tolist())

            col_names = self._get_factor_cols(factor_type="lf")
            if len(col_names) > 0:
                lf_df = lf_df[col_names + ['CODE']]
                # lf_df = lf_df.astype(np.float32)
                df_mean = self.lf_mean[col_names]
                df_std = self.lf_std[col_names]
                partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                df_standardized = lf_df[col_names + ['CODE']].groupby('CODE')[col_names].apply(partial_func)
                df_standardized.fillna(0.0, inplace=True)
                lf_df = df_standardized[col_names]
            else:
                lf_df = pd.DataFrame()

            col_names = self._get_factor_cols(factor_type="sf")
            if len(col_names) > 0:
                sf_df = sf_df[col_names + ['CODE']]
                # sf_df = sf_df.astype(np.float32)
                df_mean = self.sf_mean[col_names]
                df_std = self.sf_std[col_names]
                partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                df_standardized = sf_df[col_names + ['CODE']].groupby('CODE')[col_names].apply(partial_func)
                df_standardized.fillna(0.0, inplace=True)
                sf_df = df_standardized[col_names]
            else:
                sf_df = pd.DataFrame()

            col_names = self._get_factor_cols(factor_type="ct")
            if len(col_names) > 0:
                ct_df = ct_df[col_names + ['CODE']]
                # ct_df = ct_df.astype(np.float32)
                df_mean = self.ct_mean[col_names]
                df_std = self.ct_std[col_names]
                partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                df_standardized = ct_df[col_names + ['CODE']].groupby('CODE')[col_names].apply(partial_func)
                df_standardized.fillna(0.0, inplace=True)
                ct_df = df_standardized[col_names]
            else:
                ct_df = pd.DataFrame()

            # todo： 当前未做col_sel选择，直接合并特征数据
            # ft_df = pd.merge(lf_df, sf_df, how='inner', left_index=True, right_index=True)
            # ft_df = pd.merge(ft_df, ct_df, how='inner', left_index=True, right_index=True)
            ft_df = pd.concat([lf_df, sf_df, ct_df], axis=1)
            print(ft_df.info())
            print(ft_df.head())

            # self.codes = sorted(lb_df.CODE.unique().tolist())
            # self.ft_lens["lf_len"] = lf_df.shape[1]
            # self.ft_lens["sf_len"] = sf_df.shape[1]
            # self.ft_lens["ct_len"] = ct_df.shape[1]
            # self.ft_mean = ft_df.mean()
            # self.ft_std = ft_df.std()
            self._dump_input_param_json(ft_df)

            # 划分测试集与验证集
            cv = KFold(self._valid_fold, shuffle=True, random_state=1)
            train_ids, valid_ids = next(cv.split(lb_df.index))
            self.train_lb_df = lb_df.iloc[train_ids,:]
            self.valid_lb_df = lb_df.iloc[valid_ids,:]
            # print(self.train_lb_df, self.valid_lb_df)

            self.train_ft_df = ft_df[ft_df.index.isin(self.train_lb_df.index)]
            self.train_lb_df.sort_index(inplace=True)
            self.train_ft_df.sort_index(inplace=True)

            self.valid_ft_df = ft_df[ft_df.index.isin(self.valid_lb_df.index)]
            self.valid_lb_df.sort_index(inplace=True)
            self.valid_ft_df.sort_index(inplace=True)

            assert len(self.train_lb_df) == len(self.train_ft_df) and self.train_lb_df.index[-1] == self.train_ft_df.index[-1],\
                f'dataset processing error: {len(self.train_lb_df)} == {len(self.train_ft_df)} and {self.train_lb_df.index[-1]} == {self.train_ft_df.index[-1]}'
            assert len(self.valid_lb_df) == len(self.valid_ft_df) and self.valid_lb_df.index[-1] == self.valid_ft_df.index[-1],\
                f'dataset processing error: {len(self.valid_lb_df)} == {len(self.valid_ft_df)} and {self.valid_lb_df.index[-1]} == {self.valid_ft_df.index[-1]}'

            # 归一化
            # if self._is_normal:
            #     self.train_ft_df = (self.train_ft_df - self.train_ft_df.mean()) / self.train_ft_df.std()
            #     self.valid_ft_df = (self.valid_ft_df - self.valid_ft_df.mean()) / self.valid_ft_df.std()
            #     self.train_ft_df.fillna(value=0.0, inplace=True)
            #     self.valid_ft_df.fillna(value=0.0, inplace=True)

            # self._dump_input_param_json(self.data_loader.direct, self.data_loader.model_name, self.data_loader.model_path)

        # super().setup_data(enable_cache=enable_cache)
        self.logger.info(f"train shape{self.train_ft_df.shape} valid shape{self.valid_ft_df.shape}")

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set: Union[str, List[str]] = DataHandler.CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None
    ) -> pd.DataFrame:
        """
        
        Returns
        -------
        pd.DataFrame
        """

        if isinstance(selector, str):
            if selector == "train":
                if col_set == DataHandler.CS_ALL:
                    return self.train_ft_df, self.train_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]], "encoded": self.train_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            elif selector == "valid":
                if col_set == DataHandler.CS_ALL:
                    return self.valid_ft_df, self.valid_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]], "encoded": self.valid_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            else:
                raise NotImplementedError(f"{selector} type not supported.")            
        else:
            raise NotImplementedError(f"{selector} type not supported.")


####################################################################################

class DataHandlerAHF(DataHandler):
    """
    加载raw data进行加工处理的地方
    """
    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader: Union[dict, str, DataLoader]=None,
        valid_fold = 5,
        win = 5,
        step = 1,
        filter_win = 0,
        is_class=True,
        is_filter_extreme = False,
        is_normal = True,
        init_data=False,
        fetch_orig=True,
        **kwargs
    ):
        self.win = win
        self.step = step
        self.filter_win = filter_win
        self.is_class = is_class # 是否是分类问题
        self.is_filter_extreme = is_filter_extreme # 是否过滤极端值
        self._valid_fold = valid_fold
        self._is_normal = is_normal
        self.x_data = []
        self.y_data = []
        self.encoded_data = []
        self.direct = 'long'
        self.model_name = ''
        self.model_name_suff = ''
        self.model_path = ''
        self.data_path = ''
        self.sel_lf_names=''
        self.sel_sf_names=''
        self.sel_ct_names=''
        self.le = LabelEncoder()

        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        self.logger = get_module_logger("DataHandlerAHF")
        super().__init__(
            instruments=instruments,
            start_time=start_time,
            end_time=end_time,
            data_loader=data_loader,
            init_data=init_data,
            fetch_orig=fetch_orig,
        )

    def standardize(self, group, means, stds):
        code = group.name
        mean = means.loc[code]
        std = stds.loc[code]
        group = (group - mean) / std
        return group
    
    def config(self, **kwargs):
        if "data_loader" in kwargs and isinstance(self.data_loader, AHFDataLoader):
            #self.data_loader.config(**kwargs["data_loader"]["kwargs"])
            # print(">>>> data_handler")
            # pprint(kwargs)
            kwargs2 = deepcopy(kwargs)
            kwargs2.pop("data_loader")

        # TODO: 对DataHandler新的参数config进行修改，使其支持直接传入参数
        attr_list = {"win", "step", "direct", "model_name", "model_name_suff", "model_path", 
                     "sel_lf_names", "sel_sf_names", "sel_ct_names", "filter_win", "is_class",
                     "is_filter_extreme"}
        for k, v in kwargs2["kwargs"].items():
            if k in attr_list:
                setattr(self, k, v)
        kwargs2.pop("kwargs")
        super().config(**kwargs2)

        self.lf_mean = pd.read_csv(f'{self.data_loader.data_path}/lf_mean.csv', index_col=0)
        self.lf_std = pd.read_csv(f'{self.data_loader.data_path}/lf_std.csv', index_col=0)
        self.sf_mean = pd.read_csv(f'{self.data_loader.data_path}/sf_mean.csv', index_col=0)
        self.sf_std = pd.read_csv(f'{self.data_loader.data_path}/sf_std.csv', index_col=0)
        self.ct_mean = pd.read_csv(f'{self.data_loader.data_path}/ct_mean.csv', index_col=0)
        self.ct_std = pd.read_csv(f'{self.data_loader.data_path}/ct_std.csv', index_col=0)

    def _long_factor_select(self, n):
        if len(self.sel_lf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_lf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _short_factor_select(self, n):
        if len(self.sel_sf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_sf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _context_select(self, n):
        if len(self.sel_ct_names) == 0:
            return 0
        if SNAPSHOT_CONTEXT[n] in self.sel_ct_names:
            return 1
        return 0

    def _factor_select_name(self, sel_list):
        sel_name=[]
        if len(sel_list) == 0:
            return sel_name
        for n in range(len(ALL_FACTOR_NAMES)):
            if sel_list[n] > 0:
                sel_name.append(ALL_FACTOR_NAMES[n])
        return sel_name

    def _context_select_name(self, sel_list):
        sel_name=[]
        if len(sel_list) == 0:
            return sel_name
        for n in range(len(SNAPSHOT_CONTEXT)):
            if sel_list[n] > 0:
                sel_name.append(SNAPSHOT_CONTEXT[n])
        return sel_name

    def _get_factor_cols(self, factor_type="lf", is_all=False):
        """
        因子列名称
        """
        col_names = []
        sel_names = []
        if is_all:
            if factor_type == "lf" or factor_type == "sf":
                sel_names = ALL_FACTOR_NAMES
            elif factor_type == "ct":
                sel_names = SNAPSHOT_CONTEXT
        else:
            if factor_type == "lf":
                sel_names = self.sel_lf_names
            elif factor_type == "sf":
                sel_names = self.sel_sf_names
            elif factor_type == "ct":
                sel_names = self.sel_ct_names
            else:
                raise ValueError(f"factor_type {factor_type} is not supported")            

        if factor_type == "lf" or factor_type == "sf":
            for name in sel_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")
        elif factor_type == "ct":
            col_names = sel_names

        return col_names
    
    def _dump_input_param_json(self):
        """
        注意：两边特征向量的顺序要一致
        """
        #if self.model_name_suff == "":
        #    return
        f_sel = {}
        f_sel['codes'] = self.le.classes_.tolist()
        f_sel['slow'] = [self._long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [self._short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [self._context_select(n) for n in range(len(SNAPSHOT_CONTEXT))]
        # f_sel['codes'] = sorted(MAIN_FUT_CODES)# sorted(lb_df.code.unique().tolist())

        # 重新生成因子名称，以保证因子顺序与系统一致 
        self.sel_lf_names = self._factor_select_name(f_sel['slow'])
        self.sel_sf_names = self._factor_select_name(f_sel['fast'])
        self.sel_ct_names = self._context_select_name(f_sel['context'])

        all_col_names = self._get_factor_cols("lf", is_all=True)
        col_names = self._get_factor_cols("lf")
        f_sel['lf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self._get_factor_cols("sf", is_all=True)
        col_names = self._get_factor_cols("sf")
        f_sel['sf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self._get_factor_cols("ct", is_all=True)
        col_names = self._get_factor_cols("ct")
        f_sel['ct_sel_index'] = [all_col_names.index(name) for name in col_names]

        f_sel['lf_len'] = sum(f_sel['slow'])
        f_sel['sf_len'] = sum(f_sel['fast'])
        f_sel['ct_len'] = sum(f_sel['context'])

        assert f_sel['lf_len'] == len(f_sel['lf_sel_index'])
        assert f_sel['sf_len'] == len(f_sel['sf_sel_index'])
        assert f_sel['ct_len'] == len(f_sel['ct_sel_index'])
    
        #with open(f'{self.data_path}/using_factor.json', 'r') as using_file:
        #    using_factor = json.load(using_file)
        #    f_sel.update(using_factor)
        #f_sel.update(self.interface_params)
        f_sel["input_dim"] = 2
        f_sel["code_encoding"] = 2
        f_sel["win"] = self.win
        f_sel["filter_win"] = self.filter_win
        f_sel["step"] = self.step

        if self.model_name_suff != "":
            jfile_name = f"{self.model_name}_{self.model_name_suff}_{self.direct}"
        else:
            jfile_name = f"{self.model_name}_{self.direct}"
        with open(f'{self.model_path}/{jfile_name}.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)

       
    # TODO: 优化数据处理，减少内存占用
    # 当数据太大时，会出现内存不足的情况
    # 1. 将数据集分成多个子集，分集训练模型
    # 2. 及时释放内存
    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AHFDataLoader):
            return


        with TimeInspector.logt("Loading data"):

            # 加载原始数据
            lf_df, sf_df, ct_df = self.data_loader.load(self.instruments, self.start_time, self.end_time)
            print(lf_df.shape, sf_df.shape, ct_df.shape)

            if not sf_df.empty:
                sf_df['code_encoded'] = self.le.fit_transform(sf_df['code'].values)

                # 生成模型输入数据配置文件
                # 放在数据处理的前面，以保证因子顺序与系统一致
                self._dump_input_param_json()

                if self.is_class: # 分类问题，生成标签
                    print("-----分类问题-----")
                    if self.direct == 'long':
                        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)
                    elif self.direct == 'short':
                        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)
                    else:
                        raise ValueError(f"direct {self.direct} is not supported")
                else:
                    print("-----非分类问题-----")
                    sf_df['label'] = sf_df.loc[:, 'change']

                col_names = self._get_factor_cols(factor_type="lf")
                if len(col_names) > 0:
                    lf_df = lf_df[col_names + ['code']]
                    df_mean = self.lf_mean[col_names]
                    df_std = self.lf_std[col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = lf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    # df_standardized.sort_values(by=['code'], inplace=True)
                    lf_df = df_standardized[col_names]
                else:
                    lf_df = pd.DataFrame()

                col_names = self._get_factor_cols(factor_type="sf")
                if len(col_names) > 0:
                    sf_df = sf_df[col_names + ['code', 'date', 'change', 'code_encoded', 'label']]
                    df_mean = self.sf_mean[col_names]
                    df_std = self.sf_std[col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = sf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    sf_df.reset_index(drop=True, inplace=True)
                    # df_standardized.sort_values(by=['code'], inplace=True)
                    sf_df[col_names] = df_standardized[col_names]
                else:
                    sf_df = pd.DataFrame()

                col_names = self._get_factor_cols(factor_type="ct")
                if len(col_names) > 0:
                    ct_df = ct_df[col_names + ['code']]
                    df_mean = self.ct_mean[col_names]
                    df_std = self.ct_std[col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = ct_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    # df_standardized.sort_values(by=['code'], inplace=True)
                    ct_df = df_standardized[col_names]
                else:
                    ct_df = pd.DataFrame()

                del df_mean, df_std, df_standardized

                print(lf_df.shape, sf_df.shape, ct_df.shape)
                ft_df = pd.concat([lf_df, sf_df, ct_df], axis=1)
                if len(ft_df) != len(sf_df):
                    raise ValueError(f"ft_df {len(ft_df)} != sf_df {len(sf_df)}")
                print(ft_df)
                self.logger.info(f"\n===============\n\nft{ft_df.shape} lf{lf_df.shape} sf{sf_df.shape} ct{ct_df.shape}\n\n================\n")
                # 合并后清除数据
                # ft_df.dropna(axis=0, how='any', inplace=True)
                ft_df.fillna(0.0, inplace=True)
                if 'RSI_2' in ft_df.columns:
                    ft_df = ft_df[ft_df['RSI_2'] != 0.0]
                if 'FAST_QH_NATR_ZSCORE' in ft_df.columns:
                    ft_df = ft_df[ft_df['FAST_QH_NATR_ZSCORE'] != 0.0]

                lb_df = ft_df[['code', 'date', 'change', 'code_encoded', 'label']]
                lb_df.reset_index(drop=True, inplace=True)

                ft_df.drop(['code', 'date', 'change', 'code_encoded', 'label'], axis=1, inplace=True)
                ft_df = ft_df.astype(np.float32)

                self.logger.info(f"\n===============\n\nlb{lb_df.shape} ft{ft_df.shape}\n\n================\n")
                print(ft_df)
                print(lb_df)
                print(self.direct)
                print(lb_df['label'].value_counts())

                data1 = ft_df.values
                data2 = lb_df.values[:, -1]
                data3 = lb_df.values[:, -2]
                # 标准化：按code分别标准化
                
                # data_val = data1.values
                # data1 = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
                # if self._is_normal:
                #     data1 = (data1 - np.mean(data1)) / np.std(data1)
                #     mask = np.isnan(data1)
                #     data1[mask] = 0.0
                filter_count = 0
                extreme_threshold = 3.0 # 极端值阈值
                if self.filter_win > 1:
                    # 计算绝对值最大的行序号，目前固定滑动窗口
                    rolling_max_index = lb_df['change'].rolling(self.filter_win).apply(lambda x: abs(x).idxmax())
                    # 移动窗口的第一个值会返回 NaN，所以可以使用 fillna 方法填充为 0 或其他合适的值
                    rolling_max_index = rolling_max_index.fillna(0)
                    # 将结果转换为整数
                    rolling_max_index = rolling_max_index.astype(int)

                    for i in range(len(rolling_max_index)):
                        if rolling_max_index[i] == lb_df.index[i]:
                            if lb_df.index[i] - self.win < 0:
                                continue
                            if data3[i] != data3[i - self.win]:
                                continue
                            # 筛选过于极端的值
                            if self.is_filter_extreme :
                                if (data1[i:i + self.win, 1] > extreme_threshold).any() or (data1[i:i + self.win, 1] < -1.0*extreme_threshold).any():
                                    filter_count += 1
                                    continue
                            self.x_data.append(data1[i - self.win:i])
                            self.y_data.append(data2[i])
                            self.encoded_data.append(data3[i - self.win:i].tolist())
                else:
                    # 防止跨code取值
                    for i in range(0, len(data1) - self.win, self.step):
                        if data3[i] != data3[i + self.win]:
                            # print(data3[i], data3[i + self.win])
                            continue
                        if self.is_filter_extreme:
                            if (data1[i:i + self.win, 1] > extreme_threshold).any() or (data1[i:i + self.win, 1] < -1.0*extreme_threshold).any():
                                filter_count += 1
                                continue
                        self.x_data.append(data1[i:i + self.win])
                        self.y_data.append(data2[i + self.win])
                        self.encoded_data.append(data3[i:i + self.win].tolist())
                print("******************************")
                print(f"label: {round(sum(self.y_data) / len(self.y_data) * 100, 3)}%, total: {(len(data1) - self.win)/self.step}/{len(self.y_data)} filter win: {self.filter_win} {self.is_filter_extreme} filter: {filter_count}")
                print("******************************")
                del data1, data2, data3, lb_df, ft_df, lf_df, sf_df, ct_df
                # self.x_data = [data1[i:i + self.win] for i in range(0, len(data1) - self.win, self.step)]
                # self.y_data = [data2[i + self.win] for i in range(0, len(data2) - self.win, self.step)]                
                # self.encoded_data = [data3[i + self.win] for i in range(0, len(data3) - self.win, self.step)]                
            # self._dump_input_param_json(self.data_loader.direct, self.data_loader.model_name, self.data_loader.model_path)
        # super().setup_data(enable_cache=enable_cache)
        # self.logger.info(f"x_data shape{self.x_df.shape} valid shape{self.valid_ft_df.shape}")


    def fetch2(self):
        return self.x_data, self.y_data, self.encoded_data

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set: Union[str, List[str]] = DataHandler.CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None
    ) :
        """
        
        Returns
        -------
        pd.DataFrame
        """
        if isinstance(selector, str):
            if selector == "train":
                if col_set == DataHandler.CS_ALL:
                    return self.train_ft_df, self.train_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]], "encoded": self.train_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            elif selector == "valid":
                if col_set == DataHandler.CS_ALL:
                    return self.valid_ft_df, self.valid_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]], "encoded": self.valid_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            else:
                raise NotImplementedError(f"{selector} type not supported.")            
        else:
            raise NotImplementedError(f"{selector} type not supported.")
