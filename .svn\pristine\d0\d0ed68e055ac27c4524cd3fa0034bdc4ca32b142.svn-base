{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config, flatten_dict\n", "from qlib.workflow import R\n", "from qlib.workflow.record_temp import SignalRecord, PortAnaRecord\n", "from qlib.tests.data import GetData\n", "from qlib.tests.config import CSI300_BENCH, CSI300_GBDT_TASK\n", "from qlib.data.dataset import TSDatasetH\n", "import numpy as np\n", "import time\n", "from qlib.data.dataset.handler import DataHandlerLP"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2021-12-14 10:12:39.356 | WARNING  | qlib.tests.data:qlib_data:148 - Data already exists: ~/.qlib/qlib_data/cn_data, the data download will be skipped\n", "\tIf downloading is required: `exists_skip=False` or `change target_dir`\n", "[15068:MainThread](2021-12-14 10:12:39,358) INFO - qlib.Initialization - [config.py:386] - default_conf: client.\n", "[15068:MainThread](2021-12-14 10:12:41,383) WARNING - qlib.Initialization - [config.py:411] - redis connection failed(host=127.0.0.1 port=6379), DiskExpressionCache and DiskDatasetCache will not be used!\n", "[15068:MainThread](2021-12-14 10:12:41,410) INFO - qlib.Initialization - [__init__.py:56] - qlib successfully initialized based on client settings.\n", "[15068:MainThread](2021-12-14 10:12:41,416) INFO - qlib.Initialization - [__init__.py:58] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "# GetData().qlib_data(target_dir=provider_uri, region=REG_CN, exists_skip=True)\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[15068:MainThread](2021-12-14 10:29:31,976) INFO - qlib.timer - [log.py:113] - Time cost: 69.796s | Loading data Done\n", "[15068:MainThread](2021-12-14 10:29:32,006) INFO - qlib.timer - [log.py:113] - Time cost: 0.022s | FilterCol Done\n", "d:\\Anaconda3\\lib\\site-packages\\pandas\\core\\frame.py:3191: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self[k1] = value[k2]\n", "d:\\Anaconda3\\lib\\site-packages\\pandas\\core\\generic.py:7711: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  return self._clip_with_scalar(lower, upper, inplace=inplace)\n", "[15068:MainThread](2021-12-14 10:29:32,031) INFO - qlib.timer - [log.py:113] - Time cost: 0.024s | RobustZScoreNorm Done\n", "[15068:MainThread](2021-12-14 10:29:32,035) INFO - qlib.timer - [log.py:113] - Time cost: 0.003s | Fillna Done\n", "[15068:MainThread](2021-12-14 10:29:32,048) INFO - qlib.timer - [log.py:113] - Time cost: 0.010s | DropnaLabel Done\n", "d:\\Anaconda3\\lib\\site-packages\\pandas\\core\\frame.py:3191: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self[k1] = value[k2]\n", "[15068:MainThread](2021-12-14 10:29:32,075) INFO - qlib.timer - [log.py:113] - Time cost: 0.025s | CSRankNorm Done\n", "[15068:MainThread](2021-12-14 10:29:32,076) INFO - qlib.timer - [log.py:113] - Time cost: 0.099s | fit & process data Done\n", "[15068:MainThread](2021-12-14 10:29:32,078) INFO - qlib.timer - [log.py:113] - Time cost: 69.898s | Init data Done\n"]}], "source": ["tsdh = TSDatasetH(\n", "    handler={\n", "        \"class\": \"Alpha158\",\n", "        \"module_path\": \"qlib.contrib.data.handler\",\n", "        \"kwargs\": {\n", "            \"start_time\": \"2020-01-01\",\n", "            \"end_time\": \"2020-08-01\",\n", "            \"fit_start_time\": \"2020-01-01\",\n", "            \"fit_end_time\": \"2020-03-01\",\n", "            \"instruments\": \"csi300\",\n", "            \"infer_processors\": [\n", "                {\"class\": \"FilterCol\", \"kwargs\": {\"col_list\": [\"RESI5\", \"WVMA5\", \"RSQR5\"]}},\n", "                {\"class\": \"RobustZScoreNorm\", \"kwargs\": {\"fields_group\": \"feature\", \"clip_outlier\": \"true\"}},\n", "                {\"class\": \"Fillna\", \"kwargs\": {\"fields_group\": \"feature\"}},\n", "            ],\n", "            \"learn_processors\": [\n", "                \"DropnaLabel\",\n", "                {\"class\": \"CSRankNorm\", \"kwargs\": {\"fields_group\": \"label\"}},  # CSRankNorm\n", "            ],\n", "        },\n", "    },\n", "    segments={\n", "        # \"train\": (\"2008-01-01\", \"2014-12-31\"),\n", "        # \"valid\": (\"2015-01-01\", \"2016-12-31\"),\n", "        \"train\": (\"2020-01-01\", \"2020-03-01\"),\n", "        \"valid\": (\"2020-03-01\", \"2020-06-01\"),\n", "        \"test\": (\"2020-06-01\", \"2020-08-01\"),\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["tsds_train = tsdh.prepare(\"train\", data_key=DataHandlerLP.DK_L)  # Test the correctness\n", "# tsds_valid = tsdh.prepare(\"valid\", data_key=DataHandlerLP.DK_L)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["data_from_ds = tsds_train[\"2020-02-01\", \"SZ300315\"]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["(30, 4)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data_from_ds.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}