@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2\examples

python export_candlestick_vq_gpt_to_onnx.py ^
--model_path E:\lab\RoboQuant\pylab\models\candlestick_vq_gpt_cv\csvq_gpt_percent_change_fut_top_min1_30_4_8_16_1024_1.9959.pt ^
--codebook_path E:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250515\vqcb_atr_based_fut_top_min1_051510_0.0353.pt ^
--vectorization_method atr_based ^
--seq_len 30 ^
--device cpu ^
--output_dir E:\lab\RoboQuant\pylab\models
