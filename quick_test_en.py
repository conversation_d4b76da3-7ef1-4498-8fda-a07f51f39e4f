"""
Quick test for BarTokenizer training with real data

This script provides a quick validation that real data can work properly,
running a small-scale training test.
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from argparse import ArgumentParser

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset
from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def quick_data_check(data_file: str):
    """Quick check of data file"""
    print(f"=== Checking data file: {data_file} ===")
    
    if not os.path.exists(data_file):
        print(f"❌ Data file does not exist: {data_file}")
        return False
    
    try:
        # Read data
        df = pd.read_parquet(data_file)
        print(f"✅ Data loaded successfully")
        print(f"  Records: {len(df):,}")
        print(f"  Columns: {list(df.columns)}")
        print(f"  Time range: {df['datetime'].min()} to {df['datetime'].max()}")
        print(f"  Number of securities: {df['code'].nunique()}")
        
        # Check required columns
        required_cols = ['datetime', 'code', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ Missing required columns: {missing_cols}")
            return False
        
        # Check data quality
        null_counts = df[required_cols].isnull().sum()
        if null_counts.any():
            print(f"⚠️  Found missing data:")
            for col, count in null_counts.items():
                if count > 0:
                    print(f"    {col}: {count} ({count/len(df):.2%})")
        
        # Check data volume per security
        code_counts = df['code'].value_counts()
        print(f"  Data per security: min={code_counts.min()}, max={code_counts.max()}, avg={code_counts.mean():.0f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data check failed: {str(e)}")
        return False


def quick_tokenizer_test(data_file: str, sample_size: int = 1000):
    """Quick test of BarTokenizer"""
    print(f"\n=== Quick BarTokenizer Test ===")
    
    try:
        # Create configuration
        config = BarTokenizedDataset.get_default_config()
        config.block_size = 20  # Use smaller block_size
        config.tokenizer.n_bins = 50  # Use smaller vocabulary
        config.balance.gini_threshold = 0.8  # Relax threshold
        
        # Sample data if too large
        df = pd.read_parquet(data_file)
        if len(df) > sample_size:
            print(f"Large dataset, sampling {sample_size} records for testing")
            df = df.sample(n=sample_size, random_state=42).sort_values(['code', 'datetime'])
            
            # Save temporary file
            temp_file = "temp_sample_data.parquet"
            df.to_parquet(temp_file, index=False)
            test_file = temp_file
        else:
            test_file = data_file
        
        # Create dataset
        dataset = BarTokenizedDataset(config, test_file)
        
        print(f"✅ BarTokenizer test successful")
        print(f"  Sample count: {len(dataset)}")
        print(f"  Vocabulary size: {dataset.get_vocab_size()}")
        print(f"  Code count: {dataset.get_code_size()}")
        
        # Get distribution statistics
        stats = dataset.get_distribution_stats()
        print(f"  Gini coefficient: {stats.get('gini_coefficient', 'N/A'):.4f}")
        print(f"  Normalized entropy: {stats.get('normalized_entropy', 'N/A'):.4f}")
        
        # Test data loading
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"  Sample shape: code={sample[0].shape}, x={sample[1].shape}, x_mark={sample[2].shape}, y={sample[3].shape}")
        
        # Clean up temporary file
        if test_file != data_file and os.path.exists(test_file):
            os.remove(test_file)
        
        return dataset
        
    except Exception as e:
        print(f"❌ BarTokenizer test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Clean up temporary file
        if 'temp_file' in locals() and os.path.exists(temp_file):
            os.remove(temp_file)
        
        return None


def quick_model_test(dataset):
    """Quick model test"""
    print(f"\n=== Quick Model Test ===")
    
    if dataset is None or len(dataset) == 0:
        print("❌ Invalid dataset, skipping model test")
        return False
    
    try:
        # Create small model
        model = BarGpt4(
            block_size=dataset.config.block_size,
            code_size=dataset.get_code_size(),
            vocab_size=dataset.get_vocab_size(),
            n_layer=2,  # Small model
            n_head=4,
            d_model=64,
            time_encoding='timeF',
            time_embed_type='time_feature',
            freq='t',
            pos_embed_type='rope',
            dropout=0.1
        )
        
        print(f"✅ Model created successfully")
        print(f"  Parameter count: {model.get_num_params():,}")
        
        # Test forward pass
        code, x, x_mark, y = dataset[0]
        
        # Add batch dimension
        code = code.unsqueeze(0)
        x = x.unsqueeze(0)
        x_mark = x_mark.unsqueeze(0)
        y = y.unsqueeze(0)
        
        with torch.no_grad():
            logits, loss = model(code, x, x_mark, y)
        
        print(f"✅ Forward pass test passed")
        print(f"  Output shape: {logits.shape}")
        print(f"  Loss value: {loss.item():.4f}")
        
        # Test prediction diversity
        predictions = torch.argmax(logits, dim=-1)
        unique_preds = torch.unique(predictions).numel()
        
        print(f"  Prediction diversity: {unique_preds}/{dataset.get_vocab_size()} ({unique_preds/dataset.get_vocab_size():.2%})")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def quick_training_test(dataset, steps: int = 5):
    """Quick training test"""
    print(f"\n=== Quick Training Test ===")
    
    if dataset is None or len(dataset) == 0:
        print("❌ Invalid dataset, skipping training test")
        return False
    
    try:
        from torch.utils.data import DataLoader
        
        # Create data loader
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
        
        # Create model
        model = BarGpt4(
            block_size=dataset.config.block_size,
            code_size=dataset.get_code_size(),
            vocab_size=dataset.get_vocab_size(),
            n_layer=2,
            n_head=4,
            d_model=64,
            time_encoding='timeF',
            time_embed_type='time_feature',
            freq='t',
            pos_embed_type='rope',
            dropout=0.1
        )
        
        # Create optimizer
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        print(f"✅ Training components created successfully")
        
        # Execute training steps
        model.train()
        losses = []
        
        for step, batch in enumerate(dataloader):
            if step >= steps:
                break
            
            code, x, x_mark, y = batch
            
            # Forward pass
            logits, loss = model(code, x, x_mark, y)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            losses.append(loss.item())
            print(f"  Step {step + 1}: loss = {loss.item():.4f}")
        
        print(f"✅ Training test passed")
        print(f"  Average loss: {np.mean(losses):.4f}")
        
        if len(losses) > 1:
            print(f"  Loss change: {losses[0]:.4f} -> {losses[-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function"""
    parser = ArgumentParser(description='Quick test for BarTokenizer training with real data')
    parser.add_argument('--data_file', type=str, required=True, help='Data file path')
    parser.add_argument('--sample_size', type=int, default=2000, help='Test sample size')
    parser.add_argument('--training_steps', type=int, default=5, help='Training test steps')
    
    args = parser.parse_args()
    
    print("=== Quick Test for BarTokenizer Training with Real Data ===\n")
    
    # 1. Check data file
    data_ok = quick_data_check(args.data_file)
    if not data_ok:
        print("\n❌ Data check failed, please fix data issues and retry")
        return
    
    # 2. Test BarTokenizer
    dataset = quick_tokenizer_test(args.data_file, args.sample_size)
    if dataset is None:
        print("\n❌ BarTokenizer test failed, please check error messages")
        return
    
    # 3. Test model
    model_ok = quick_model_test(dataset)
    if not model_ok:
        print("\n❌ Model test failed, please check error messages")
        return
    
    # 4. Test training
    training_ok = quick_training_test(dataset, args.training_steps)
    if not training_ok:
        print("\n❌ Training test failed, please check error messages")
        return
    
    # Summary
    print(f"\n=== Test Summary ===")
    print(f"✅ Data check: Passed")
    print(f"✅ BarTokenizer: Passed")
    print(f"✅ Model integration: Passed")
    print(f"✅ Training process: Passed")
    
    print(f"\n🎉 All tests passed! Ready for formal training.")
    print(f"\nSuggested training command:")
    print(f"python train_bar_gpt4_with_tokenizer_en.py \\")
    print(f"  --data_file {args.data_file} \\")
    print(f"  --mapping_strategy quantile \\")
    print(f"  --balancing_strategy frequency \\")
    print(f"  --n_bins 100 \\")
    print(f"  --block_size 30 \\")
    print(f"  --batch_size 64 \\")
    print(f"  --max_epochs 8 \\")
    print(f"  --k_folds 3 \\")
    print(f"  --use_class_weights")


if __name__ == '__main__':
    main()
