{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import warnings\n", "import numpy\n", "\n", "def warn(*args, **kwargs):\n", "    pass\n", "\n", "warnings.warn = warn\n", "warnings.simplefilter(action='ignore', category=FutureWarning)\n", "numpy.seterr(divide = 'ignore') \n", "\n", "sys.path.append(os.path.dirname(os.path.abspath('')))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": true}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'ccxt'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-3-13d24c814704>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[1;32mimport\u001b[0m \u001b[0mccxt\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensortrade\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mstrategies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTensorforceTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'ccxt'"]}], "source": ["import ccxt\n", "\n", "from tensortrade.strategies import TensorforceTradingStrategy"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [], "source": ["from tensortrade.rewards import SimpleProfitStrategy\n", "from tensortrade.actions import DiscreteActionStrategy\n", "from tensortrade.exchanges.simulated import FBMExchange\n", "from tensortrade.features.stationarity import FractionalDifference\n", "from tensortrade.features.scalers import MinMaxNormalizer\n", "from tensortrade.features import FeaturePipeline\n", "\n", "normalize = MinMaxNormalizer(inplace=True)\n", "difference = FractionalDifference(difference_order=0.6,\n", "                                  inplace=True)\n", "feature_pipeline = FeaturePipeline(steps=[normalize, difference])\n", "\n", "reward_strategy = SimpleProfitStrategy()\n", "action_strategy = DiscreteActionStrategy(n_actions=20, instrument_symbol='ETH/BTC')\n", "\n", "exchange = FBMExchange(base_instrument='BTC',\n", "                       timeframe='1h',\n", "                       should_pretransform_obs=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": true}, "outputs": [], "source": ["from tensortrade.environments import TradingEnvironment\n", "\n", "network_spec = [\n", "    dict(type='dense', size=128, activation=\"tanh\"),\n", "    dict(type='dense', size=64, activation=\"tanh\"),\n", "    dict(type='dense', size=32, activation=\"tanh\")\n", "]\n", "\n", "agent_spec = {\n", "    \"type\": \"ppo\",\n", "    \"learning_rate\": 1e-4,\n", "    \"discount\": 0.99,\n", "    \"likelihood_ratio_clipping\": 0.2,\n", "    \"estimate_terminal\": <PERSON><PERSON><PERSON>,\n", "    \"max_episode_timesteps\": 2000,\n", "    \"network\": network_spec,\n", "    \"batch_size\": 10,\n", "    \"update_frequency\": \"never\"\n", "}\n", "\n", "environment = TradingEnvironment(exchange=exchange,\n", "                                 action_strategy=action_strategy,\n", "                                 reward_strategy=reward_strategy,\n", "                                 feature_pipeline=feature_pipeline)\n", "\n", "strategy = TensorforceTradingStrategy(environment=environment, agent_spec=agent_spec)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Episodes:  80%|████████  | 8/10 [01:32, reward=1167.52, ts/ep=1666, sec/ep=11.60, ms/ts=7.0, agent=14.9%]"]}], "source": ["performance = strategy.run(episodes=10, evaluation=False)\n", "\n", "performance[-5:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["performance.balance.plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["strategy.save_agent(directory='agents')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}