# KlineLLM - K线数据预测模型

KlineLLM是一个基于大型语言模型的K线数据预测模型，它将K线数据转换为tokens，并使用LLM进行预测。该模型参考了[ChatTime](https://github.com/ForestsKing/ChatTime)和[Chronos](https://github.com/amazon-science/chronos-forecasting)的设计思路。

## 模型特点

- 将K线数据离散化为tokens，使其适合LLM处理
- 支持多种底层LLM模型（GPT-2, LLaMA等）
- 考虑时间特征和证券代码信息
- 支持多种预测任务（短期预测、长期预测）
- 易于扩展和定制

## 数据预处理

将证券的KLine时序数据离散化，转换为LLM的tokens，以便可以使用transformer模型进行训练。
每个KLine数据的Candlestick Bar包含：
- change：相对前一个Candlestick Bar的涨跌幅度
- entity：实体
- upline：上影线
- downline：下影线
为了使所有的证券的Kline bar的tokens统一的标准，将上面的4个标量统一除以100个Kline bar的ATR值并取整，如下：
```python
def to_bar(df):
    # 将列change的值限定在-10到10之间
    df.loc[df['change'] < -12, 'change'] = -12
    df.loc[df['change'] > 12, 'change'] = 12
    df.loc[df['entity'] < -12, 'entity'] = -12
    df.loc[df['entity'] > 12, 'entity'] = 12
    df.loc[df['upline'] > 7, 'upline'] = 7
    df.loc[df['downline'] > 7, 'downline'] = 7

    # 将3,4,5,6列的值转换为bar_set中的索引
    if not hasattr(Pipeline, 'bar_set'):
        Pipeline.bar_set = Pipeline.get_vocab()
    df['bar'] = df['change'].astype(str) + '|' + df['entity'].astype(str) + '|' + df['upline'].astype(str) + '|' + df['downline'].astype(str)
    df['bar'] = df['bar'].apply(lambda x: Pipeline.bar_set.index(x))
    df['bar'] = df['bar'].astype(int)
    df.drop(columns=['change', 'entity', 'upline', 'downline'], inplace=True)
    return df
```
经过上面的处理后，每个Candle Bar的tokens形成的词汇表大小为40000。
由于金融市场有开盘，收市和休市，时序数据有连续和间隔，所以，在不同交易日间，加入间隔token，以便模型识别。
目前，主要将证券的两个周期的历史行情数据转换为tokens，即长周期的日线，短周期的5分钟线，以便后续的模型训练。

KlineLLM使用以下方法将K线数据转换为tokens：

1. 计算ATR（平均真实范围）作为归一化因子
2. 将K线的4个特征（change, entity, upline, downline）归一化并离散化：
   - change：相对前一个Candlestick Bar的涨跌幅度，范围[-12, 12]
   - entity：实体，范围[-12, 12]
   - upline：上影线，范围[0, 7]
   - downline：下影线，范围[0, 7]
3. 将这4个特征组合成一个token，形如"change|entity|upline|downline"
4. 在不同交易日之间添加分隔token


## 模型架构

KlineLLM模型由以下组件组成：

1. **K线Tokenizer**: 将K线数据转换为tokens
2. **底层LLM模型**: 使用预训练的语言模型（如GPT-2, LLaMA）
3. **K线嵌入层**: 将K线tokens映射到嵌入空间
4. **时间特征嵌入层**: 编码时间信息
5. **证券代码嵌入层**: 编码不同证券的特征
6. **输出投影层**: 将LLM的输出映射回K线特征

