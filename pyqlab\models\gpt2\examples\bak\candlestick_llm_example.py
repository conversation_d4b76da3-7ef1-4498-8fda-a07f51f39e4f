"""
基础版 Candlestick LLM 训练示例

演示如何使用CandlestickLLMTrainer训练基础版CandlestickLLM模型
"""

import os
import sys
import torch

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和训练器
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.trainer import CandlestickLLMTrainer
from pyqlab.models.gpt2.utils import generate_mock_data, compare_candlesticks, save_model_info

# 使用utils.py中的generate_mock_data函数

def main():
    """主函数"""
    print("生成模拟数据...")
    train_data, train_code_ids = generate_mock_data(n_samples=1000, n_securities=5, trend='random')
    val_data, val_code_ids = generate_mock_data(n_samples=200, n_securities=2, trend='random')

    print(f"训练数据: {len(train_data)} 个证券，每个证券 {len(train_data[0])} 个样本")
    print(f"验证数据: {len(val_data)} 个证券，每个证券 {len(val_data[0])} 个样本")

    # 创建tokenizer
    print("创建基础版tokenizer...")
    tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        atr_window=100,
        atr_mult=0.88,
        scale=10,
        include_volume=False
    )

    # 创建模型
    print("创建基础版CandlestickLLM模型...")
    model = CandlestickLLM(
        vocab_size=tokenizer.vocab_size,
        code_size=max(max(train_code_ids), max(val_code_ids)) + 1,
        block_size=30,
        n_layer=2,  # 大幅减少层数以加快训练
        n_head=4,
        d_model=128,  # 大幅减少模型维度以加快训练
        dropout=0.1,
        use_time_features=True
    )

    # 创建训练器
    print("创建训练器...")
    trainer = CandlestickLLMTrainer(
        model=model,
        tokenizer=tokenizer,
        train_data=train_data,
        train_code_ids=train_code_ids,
        val_data=val_data,
        val_code_ids=val_code_ids,
        seq_len=20,
        batch_size=16,
        learning_rate=5e-4,
        max_epochs=3,  # 减少轮数以加快训练
        log_interval=10,
        eval_interval=50,
        save_interval=100,
        checkpoint_dir='./checkpoints/basic_candlestick_llm'
    )

    # 训练模型
    print("开始训练...")
    try:
        results = trainer.train()
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        results = None

    # 绘制训练历史
    print("绘制训练历史...")
    if results is not None:
        try:
            trainer.plot_training_history()
        except Exception as e:
            print(f"绘制训练历史时出现错误: {e}")

    # 生成样本预测
    print("生成样本预测...")
    try:
        sample_df = val_data[0].iloc[-30:].copy()
        predicted_df = trainer.generate_sample(
            input_df=sample_df,
            code_id=val_code_ids[0],
            max_new_tokens=5,
            temperature=0.8,
            top_k=50
        )
    except Exception as e:
        print(f"生成样本预测时出现错误: {e}")
        predicted_df = None

    print("预测结果:")
    print(predicted_df)

    # 可视化预测结果
    if predicted_df is not None:
        try:
            # 使用工具函数比较K线
            title = "基础版CandlestickLLM预测结果"
            compare_candlesticks(
                input_df=sample_df,
                predicted_df=predicted_df,
                title=title,
                save_path='./checkpoints/basic_candlestick_llm/prediction_sample.png'
            )

            # 保存模型信息
            save_model_info(model, tokenizer, './checkpoints/basic_candlestick_llm')
        except Exception as e:
            print(f"可视化预测结果时出现错误: {e}")

    print("示例完成，结果保存在 ./checkpoints/basic_candlestick_llm/ 目录下")

if __name__ == '__main__':
    main()
