# BarTokenizer可视化分析工具

本目录包含了用于分析和可视化BarTokenizer不同方法效果的工具集。

## 📁 文件说明

### 核心文件

1. **`bar_tokenizer_analysis.ipynb`** - Jupyter Notebook版本的完整分析工具
   - 交互式分析环境
   - 包含详细的可视化图表
   - 适合逐步分析和调试

2. **`bar_tokenizer_visualization.py`** - 完整的Python脚本版本
   - 命令行运行
   - 包含所有分析功能
   - 适合批量分析

3. **`demo_bar_tokenizer.py`** - 简化的演示脚本
   - 快速入门演示
   - 基本功能展示
   - 适合初学者

### 辅助文件

4. **`run_bar_tokenizer_analysis.py`** - 快速运行脚本
5. **`README_BarTokenizer_Visualization.md`** - 本说明文件

## 🚀 快速开始

### 方法1: 使用演示脚本（推荐新手）

```bash
cd pyqlab/notebook
python demo_bar_tokenizer.py
```

这将运行三个演示：
1. 单个tokenizer的基本使用
2. 不同方法的对比分析
3. 真实数据分析（如果可用）

### 方法2: 使用完整分析工具

```bash
cd pyqlab/notebook
python bar_tokenizer_visualization.py
```

或者设置数据路径：

```bash
# Windows PowerShell
$env:DATA_PATH = "f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv"
python bar_tokenizer_visualization.py

# 或者直接运行
python run_bar_tokenizer_analysis.py
```

### 方法3: 使用Jupyter Notebook

```bash
cd pyqlab/notebook
jupyter notebook bar_tokenizer_analysis.ipynb
```

## 📊 分析内容

### 1. Token分布对比
- 不同映射策略的token分布可视化
- 高频token识别和标记
- 分布均匀性分析

### 2. 质量指标对比
- **基尼系数**: 衡量分布不平衡程度（越小越好）
- **标准化熵**: 衡量分布随机性（越大越好）
- **词汇表利用率**: 实际使用的token比例（越大越好）
- **变异系数**: 频率分布变异程度（越小越好）

### 3. 方法对比
测试以下6种方法组合：
- Linear (线性映射)
- Linear + Balance (线性映射 + 频率平衡)
- Quantile (分位数映射)
- Quantile + Balance (分位数映射 + 频率平衡)
- Adaptive (自适应映射)
- Adaptive + Balance (自适应映射 + 频率平衡)

## 🔧 配置选项

### 数据源配置

在脚本中修改数据路径：

```python
# 在 bar_tokenizer_visualization.py 中
data_path = "your/data/path.csv"  # 或 .parquet

# 或使用环境变量
import os
os.environ['DATA_PATH'] = "your/data/path.csv"
```

### 分析参数配置

```python
# 修改分析参数
analysis_results, tokenizers = analyze_tokenizer_methods(
    test_data, 
    n_bins=100,        # token数量
    sample_size=5000   # 数据采样大小
)
```

### 可视化参数配置

```python
# 修改可视化参数
plot_token_distributions(
    analysis_results, 
    max_tokens_to_show=50  # 显示的最大token数
)
```

## 📈 输出结果

### 1. 可视化图表
- Token分布对比图（2x3子图）
- 质量指标对比图（2x2子图）
- 频率分布曲线图

### 2. 数据表格
- 方法对比汇总表
- 详细的统计指标
- 最高频token分析

### 3. 控制台输出
- 实时分析进度
- 关键指标摘要
- 错误和警告信息

## 🎯 使用建议

### 1. 数据准备
- 确保数据包含标准OHLCV列：`open`, `high`, `low`, `close`, `volume`
- 数据量建议在1000-10000行之间（太少不准确，太多影响速度）
- 支持CSV和Parquet格式

### 2. 方法选择
根据分析结果选择最佳方法：
- **推荐**: `Quantile + Balance` - 通常表现最好
- **高效**: `Linear` - 计算速度快
- **平衡**: `Adaptive` - 综合性能好

### 3. 指标解读
- **基尼系数 < 0.3**: 分布相对均匀
- **标准化熵 > 0.8**: 分布随机性好
- **词汇表利用率 > 0.7**: 词汇表使用充分

## 🐛 常见问题

### 1. 导入错误
```
ImportError: No module named 'pyqlab.data.tokenizers.bar_tokenizer'
```
**解决**: 确保项目路径正确，运行前执行：
```python
import sys
sys.path.append('/path/to/pyqlab')
```

### 2. 数据格式错误
```
KeyError: 'open'
```
**解决**: 检查数据是否包含必需的OHLCV列，或使用模拟数据

### 3. 内存不足
```
MemoryError
```
**解决**: 减少`sample_size`参数或使用更小的数据集

### 4. 可视化问题
```
No display name and no $DISPLAY environment variable
```
**解决**: 在服务器环境中使用：
```python
import matplotlib
matplotlib.use('Agg')  # 在导入pyplot之前
```

## 📝 自定义扩展

### 添加新的映射策略
```python
# 在analyze_tokenizer_methods函数中添加
methods['Custom'] = {
    'mapping_strategy': 'your_strategy',
    'balancing_strategy': 'your_balance'
}
```

### 添加新的质量指标
```python
# 在plot_quality_metrics_comparison函数中添加
new_metric = [results[m]['balance_metrics']['your_metric'] for m in methods]
```

### 自定义可视化
```python
# 创建自己的绘图函数
def plot_custom_analysis(results):
    # 你的可视化代码
    pass
```

## 📞 技术支持

如果遇到问题，请检查：
1. BarTokenizer模块是否正确安装
2. 数据格式是否符合要求
3. Python环境是否包含必要的依赖包

更多信息请参考：
- `pyqlab/data/tokenizers/README.md`
- `pyqlab/data/tokenizers/SUMMARY.md`
