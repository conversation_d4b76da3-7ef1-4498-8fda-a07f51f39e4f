"""
ONNX模型包装器

为ONNX模型提供与PyTorch模型兼容的接口，以便在回测器中使用。
"""

import torch
import numpy as np
import onnxruntime as ort

class OnnxModelWrapper:
    """ONNX模型包装器，提供与PyTorch模型兼容的接口"""

    def __init__(self, session, device='cpu'):
        """
        初始化ONNX模型包装器

        Args:
            session: ONNX运行时会话
            device: 计算设备
        """
        self.session = session
        self.device = device
        self.is_training = False

        # 获取模型输入输出信息
        self.input_names = [input.name for input in session.get_inputs()]
        self.output_names = [output.name for output in session.get_outputs()]

        # 检查模型提供商
        self.providers = session.get_providers()

    def __call__(self, code, x, x_mark, targets=None):
        """
        模拟PyTorch模型的前向传播

        Args:
            code: 代码输入，形状为(batch_size, seq_len)
            x: 序列输入，形状为(batch_size, seq_len)
            x_mark: 时间标记输入，形状为(batch_size, seq_len, mark_dim)
            targets: 目标输出（可选），形状为(batch_size, seq_len)

        Returns:
            logits: 模型输出
            loss: 损失值（始终为None，因为ONNX模型不计算损失）
        """
        # 准备输入
        inputs = {}

        # 根据输入名称准备数据
        # 使用int64类型以匹配ONNX模型的期望
        if 'code' in self.input_names:
            inputs['code'] = code.cpu().numpy().astype(np.int64)
        elif 'input.3' in self.input_names:  # 某些ONNX模型可能使用不同的输入名称
            inputs['input.3'] = code.cpu().numpy().astype(np.int64)

        if 'x' in self.input_names:
            inputs['x'] = x.cpu().numpy().astype(np.int64)
        elif 'input.1' in self.input_names:
            inputs['input.1'] = x.cpu().numpy().astype(np.int64)

        if 'x_mark' in self.input_names:
            inputs['x_mark'] = x_mark.cpu().numpy().astype(np.float32)
        elif 'onnx::MatMul_2' in self.input_names:
            inputs['onnx::MatMul_2'] = x_mark.cpu().numpy().astype(np.float32)

        # 运行推理
        outputs = self.session.run(None, inputs)

        # 将输出转换为PyTorch张量
        logits = torch.tensor(outputs[0])

        # 如果设备是CUDA，则将输出移动到GPU
        if self.device == 'cuda':
            logits = logits.cuda()

        return logits, None

    def to(self, device):
        """
        模拟PyTorch模型的to方法

        Args:
            device: 计算设备

        Returns:
            self: 返回自身以支持链式调用
        """
        self.device = device
        return self

    def eval(self):
        """
        模拟PyTorch模型的eval方法

        Returns:
            self: 返回自身以支持链式调用
        """
        self.is_training = False
        return self

    def train(self):
        """
        模拟PyTorch模型的train方法

        Returns:
            self: 返回自身以支持链式调用
        """
        self.is_training = True
        return self

    def inference_mode(self):
        """
        模拟BarGpt4模型的inference_mode方法

        Returns:
            self: 返回自身以支持链式调用
        """
        self.is_training = False
        return self

    def train_mode(self):
        """
        模拟BarGpt4模型的train_mode方法

        Returns:
            self: 返回自身以支持链式调用
        """
        self.is_training = True
        return self
