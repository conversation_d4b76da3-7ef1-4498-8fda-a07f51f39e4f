import os
import json
import time
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm
from pathlib import Path
import seaborn as sns
from datetime import datetime
from torch.utils.data import DataLoader
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report
)
import onnxruntime as ort  # 添加onnxruntime导入

from argparse import ArgumentParser

# 导入项目相关模块
from pyqlab.models.pl_gpt_model import PLGptModel
from pyqlab.models.gpt.bar_gpt4 import BarGpt4
from pyqlab.models.gpt.bar_gpt4r import BarGpt4r
from pyqlab.models.gpt.bar_gpt41 import BarGpt41
from pyqlab.models.gpt.bar_gpt42 import BarGpt42
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES
from pyqlab.data.dataset.pipeline import Pipeline
from pyqlab.data.dataset.dataset_bar import BarDataset

class ModelBenchmark:
    """
    用于对多个GPT模型进行性能基准测试的类
    """
    def __init__(self, model_paths, test_dataloader, device='cuda' if torch.cuda.is_available() else 'cpu', task_type='regression'):
        """
        初始化基准测试类
        
        Args:
            model_paths (dict): 模型路径字典 {模型名称: 模型权重路径}
            test_dataloader (DataLoader): 测试数据加载器
            device (str): 运行设备，默认为GPU
            task_type (str): 任务类型，可选 'regression' 或 'classification'
        """
        self.model_paths = model_paths
        self.test_dataloader = test_dataloader
        self.device = device
        self.models = {}
        self.results = {}
        self.task_type = task_type
        
        # 确保保存结果的目录存在
        self.results_dir = Path("comparison_results")
        self.results_dir.mkdir(exist_ok=True)
        
    def load_models(self):
        """加载所有指定的模型"""
        print("正在加载模型...")
        for model_name, model_path in self.model_paths.items():
            try:
                # 根据文件扩展名判断模型类型
                if model_path.endswith('.onnx'):
                    # 加载ONNX模型
                    model = self._load_onnx_model(model_path)
                elif model_path.endswith('.ckpt'):
                    # PyTorch Lightning 模型加载
                    model = PLGptModel.load_from_checkpoint(model_path)
                    model.to(self.device)
                    model.eval()
                else:
                    # 普通PyTorch模型加载
                    model_class = self._get_model_class(model_name)
                    model = model_class.load_model(model_path)
                    model.to(self.device)
                    model.eval()
                
                self.models[model_name] = model
                print(f"已成功加载模型: {model_name}")
            except Exception as e:
                print(f"加载模型 {model_name} 失败: {str(e)}")
    
    def _load_onnx_model(self, model_path):
        """
        加载ONNX模型
        
        Args:
            model_path (str): ONNX模型文件路径
            
        Returns:
            ONNX模型会话对象
        """
        # 配置ONNX运行时选项
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if self.device == 'cuda' else ['CPUExecutionProvider']
        session_options = ort.SessionOptions()
        session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        session_options.intra_op_num_threads = 4
        
        # 创建ONNX运行时会话
        session = ort.InferenceSession(
            model_path,
            providers=providers,
            sess_options=session_options
        )
        
        return session
    
    def _inference_onnx(self, model, code, x, x_mark):
        """
        使用ONNX模型进行推理
        
        Args:
            model: ONNX模型会话对象
            batch: 输入数据批次
            
        Returns:
            模型输出
        """
        
        # 将数据转换为numpy数组
        inputs = {
            'input.3': code.cpu().numpy().astype(np.int32),
            'input.1': x.cpu().numpy().astype(np.int32),
            'onnx::MatMul_2': x_mark.cpu().numpy().astype(np.float32),
            # 'targets': targets.cpu().numpy()
        }
        
        # 执行推理
        outputs = model.run(None, inputs)
        return torch.from_numpy(outputs[0])  # 假设第一个输出是预测结果
    
    def _get_model_class(self, model_name):
        """根据模型名称获取对应的模型类"""
        model_classes = {
            "bar_gpt4": BarGpt4,
            "bar_gpt4r": BarGpt4r,
            "bar_gpt41": BarGpt41,
            "bar_gpt42": BarGpt42,
        }
        
        for key, cls in model_classes.items():
            if key in model_name.lower():
                return cls
        
        raise ValueError(f"未找到模型 {model_name} 对应的类")
    
    def evaluate_models(self, metrics=None, num_classes=None):
        """
        评估所有模型在测试集上的性能
        
        Args:
            metrics (list): 要评估的指标列表
            num_classes (int): 如果是分类任务，指定类别数量
        """
        if not metrics:
            if self.task_type == 'regression':
                metrics = ["mse", "mae", "r2", "inference_time"]
            else:  # 分类任务
                metrics = ["accuracy", "precision", "recall", "f1", "confusion_matrix", "inference_time"]
                if num_classes is None:
                    raise ValueError("对于分类任务，必须指定num_classes参数")
        
        print(f"开始评估模型性能... 任务类型: {self.task_type}")
        
        for model_name, model in self.models.items():
            print(f"正在评估模型: {model_name}")
            
            all_preds = []
            all_targets = []
            all_logits = []  # 保存原始logits输出
            inference_times = []
            
            with torch.no_grad():
                for batch in tqdm(self.test_dataloader, desc=f"测试 {model_name}"):
                    # 将数据移至设备（仅对PyTorch模型）
                    if len(batch) == 5:
                        code, x, x_mark, targets, y_mark = batch
                    else:
                        code, x, x_mark, targets = batch
                    
                    code = code.to(self.device)
                    x = x.to(self.device)
                    x_mark = x_mark.to(self.device)
                    targets = targets.to(self.device)
                    
                    # 测量推理时间
                    start_time = time.time()
                    
                    # 执行前向传播
                    if isinstance(model, ort.InferenceSession):
                        outputs = self._inference_onnx(model, code, x, x_mark)
                    else:
                        if isinstance(model, PLGptModel):
                            outputs, _ = model(code, x, x_mark, targets)
                        else:
                            outputs, _ = model(code, x, x_mark, targets)
                    
                    end_time = time.time()
                    inference_times.append(end_time - start_time)
                    
                    # 收集预测和目标
                    logits = outputs.cpu().numpy()
                    all_logits.append(logits)
                    
                    # 对于分类任务，转换logits为预测类别
                    if self.task_type == 'classification':
                        preds = np.argmax(logits, axis=2)
                    else:
                        preds = logits

                    # print(preds, targets[:, -1])    
                    all_preds.append(preds)
                    all_targets.append(targets[:, -1].cpu().numpy())
            
            # 合并批次结果
            all_preds = np.concatenate(all_preds, axis=0)
            all_targets = np.concatenate(all_targets, axis=0)
            all_logits = np.concatenate(all_logits, axis=0)
            print(all_preds.shape, all_targets.shape)
            
            # 计算评估指标
            model_results = {}
            
            # 回归指标
            if self.task_type == 'regression':
                if "mse" in metrics:
                    model_results["mse"] = mean_squared_error(all_targets, all_preds)
                if "mae" in metrics:
                    model_results["mae"] = mean_absolute_error(all_targets, all_preds)
                if "r2" in metrics:
                    model_results["r2"] = r2_score(all_targets, all_preds)
            
            # 分类指标
            else:  # self.task_type == 'classification'
                if "accuracy" in metrics:
                    model_results["accuracy"] = accuracy_score(all_targets, all_preds)
                if "precision" in metrics:
                    model_results["precision"] = precision_score(all_targets, all_preds, average='macro', zero_division=0)
                    model_results["precision_per_class"] = precision_score(all_targets, all_preds, average=None, zero_division=0)
                if "recall" in metrics:
                    model_results["recall"] = recall_score(all_targets, all_preds, average='macro', zero_division=0)
                    model_results["recall_per_class"] = recall_score(all_targets, all_preds, average=None, zero_division=0)
                if "f1" in metrics:
                    model_results["f1"] = f1_score(all_targets, all_preds, average='macro', zero_division=0)
                    model_results["f1_per_class"] = f1_score(all_targets, all_preds, average=None, zero_division=0)
                if "confusion_matrix" in metrics:
                    model_results["confusion_matrix"] = confusion_matrix(all_targets, all_preds).tolist()
                
                # 保存分类报告
                model_results["classification_report"] = classification_report(
                    all_targets, all_preds, output_dict=True, zero_division=0
                )
            
            # 通用指标
            if "inference_time" in metrics:
                model_results["avg_inference_time"] = np.mean(inference_times)
                model_results["total_inference_time"] = np.sum(inference_times)
            
            self.results[model_name] = model_results
            
            # 输出结果摘要
            if self.task_type == 'regression':
                print(f"模型 {model_name} 评估完成。MSE: {model_results.get('mse', 'N/A')}, MAE: {model_results.get('mae', 'N/A')}")
            else:
                print(f"模型 {model_name} 评估完成。准确率: {model_results.get('accuracy', 'N/A')}, F1: {model_results.get('f1', 'N/A')}")
            
            # 单独保存混淆矩阵图
            if self.task_type == 'classification' and "confusion_matrix" in metrics:
                self._plot_confusion_matrix(
                    model_name, 
                    np.array(model_results["confusion_matrix"]),
                    num_classes
                )
    
    def _plot_confusion_matrix(self, model_name, cm, num_classes):
        """绘制并保存混淆矩阵图"""
        plt.figure(figsize=(10, 8))
        sns.heatmap(
            cm, 
            annot=True, 
            fmt='d', 
            cmap='Blues',
            xticklabels=range(num_classes),
            yticklabels=range(num_classes)
        )
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.title(f'{model_name} - 混淆矩阵')
        
        # 保存图表
        cm_path = self.results_dir / f"{model_name}_confusion_matrix.png"
        plt.savefig(cm_path, dpi=300, bbox_inches="tight")
        plt.close()
    
    def compare_results(self):
        """比较所有模型的性能并返回结果DataFrame"""
        if not self.results:
            print("尚未评估任何模型，请先运行evaluate_models()方法")
            return None
        
        # 创建比较结果DataFrame
        results_df = pd.DataFrame(self.results).T
        
        # 对结果进行排序，默认按MSE从小到大排序
        if "mse" in results_df.columns:
            results_df = results_df.sort_values("mse")
        
        return results_df
    
    def visualize_results(self, save_path=None):
        """
        可视化比较结果
        
        Args:
            save_path (str): 图表保存路径，默认为None（不保存）
        """
        if not self.results:
            print("尚未评估任何模型，请先运行evaluate_models()方法")
            return
        
        results_df = self.compare_results()
        
        # 选择要显示的指标
        if self.task_type == 'regression':
            display_metrics = ["mse", "mae", "r2", "avg_inference_time"]
        else:  # 'classification'
            display_metrics = ["accuracy", "precision", "recall", "f1", "avg_inference_time"]
        
        # 筛选在结果中实际存在的指标
        metrics = [m for m in display_metrics if m in results_df.columns]
        
        if not metrics:
            print("没有可视化的指标")
            return
        
        # 设置图表风格
        sns.set(style="whitegrid")
        
        # 为每个指标创建条形图
        fig, axes = plt.subplots(len(metrics), 1, figsize=(10, 5 * len(metrics)))
        if len(metrics) == 1:
            axes = [axes]
        
        for i, metric in enumerate(metrics):
            # 对于不同指标，判断是高好还是低好
            ascending = metric not in ["r2", "accuracy", "precision", "recall", "f1"]  # 这些指标值越高越好
            
            # 按当前指标排序
            sorted_df = results_df.sort_values(metric, ascending=ascending)
            
            # 创建条形图
            ax = sns.barplot(x=sorted_df.index, y=sorted_df[metric], palette="viridis", ax=axes[i])
            ax.set_title(f"模型比较 - {metric.upper()}")
            ax.set_xlabel("模型")
            ax.set_ylabel(metric.upper())
            
            # 在条形上添加具体数值
            for j, p in enumerate(ax.patches):
                height = p.get_height()
                ax.text(p.get_x() + p.get_width()/2., height + 0.01,
                        f'{height:.4f}', ha="center")
            
            # 优化x轴标签显示
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"图表已保存至: {save_path}")
        
        plt.show()
    
    def save_results(self, filename=None):
        """
        保存比较结果到CSV和JSON文件
        
        Args:
            filename (str): 文件名前缀，默认为当前时间戳
        """
        if not self.results:
            print("尚未评估任何模型，请先运行evaluate_methods()")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"model_comparison_{timestamp}"
        
        # 保存为CSV
        results_df = self.compare_results()
        csv_path = self.results_dir / f"{filename}.csv"
        results_df.to_csv(csv_path)
        
        # 保存为JSON
        json_path = self.results_dir / f"{filename}.json"
        with open(json_path, 'w') as f:
            json.dump(self.results, f, indent=4)
        
        # 保存图表
        plot_path = self.results_dir / f"{filename}.png"
        self.visualize_results(save_path=plot_path)
        
        print(f"结果已保存至:\n- {csv_path}\n- {json_path}\n- {plot_path}")

def get_config():

    C = CN()

    # system
    # C.system = CN()
    # C.system.seed = 3407
    # C.system.model_name = 'BARGPT'
    # C.system.resave_model_onnx = False
    # C.system.init_from = 'scratch'

    # data
    C.data = BarDataset.get_default_config()
    return C

def load_test_dataloader(args):
    """
    加载测试数据集
    
    Args:
        data_path (str): 测试数据路径
        batch_size (int): 批次大小
        num_workers (int): 数据加载线程数
    
    Returns:
        DataLoader: 测试数据加载器
    """
    # 这里需要根据项目中实际的数据加载方式调整
    # 示例代码，实际使用时请替换为项目中的数据加载逻辑
    from torch.utils.data import Dataset, DataLoader
    
    # 创建测试数据集实例
    if args.period == 'min5':
        args.freq = 't'
    elif args.period == 'day':
        args.freq = 'b'
    else:
        raise ValueError(f"Invalid period: {args.period}")
    if args.market == 'fut' and args.block_name == 'sf':
        args.sel_codes = SF_FUT_CODES
    print(args)

    config = get_config()
    # config.system.update_from_dict(vars(args))
    config.data.update_from_dict(vars(args))
    # config.model.update_from_dict(vars(args))
    # config.trainer.update_from_dict(vars(args))
    # set_seed(config.system.seed)

    if args.time_encoding == 'timeF':
        config.data.timeenc = 1
    else:
        config.data.timeenc = 0

    # construct the training dataset
    pipe = Pipeline(
        config.data.data_path,
        config.data.market,
        config.data.block_name,
        config.data.period,
        config.data.start_year,
        config.data.end_year,
        config.data.start_date,
        config.data.end_date,
        config.data.block_size,
        config.data.timeenc,
        config.data.sel_codes
    )
    data = pipe.get_data()

    assert len(data) > 0 and len(data.shape) == 2, 'No data or wrong shape'
    config.data.is_sf = pipe.is_sf
    args.is_sf = pipe.is_sf
    print(f"args.is_sf: {args.is_sf}")

    dataset = BarDataset(config.data, data)    
    # 创建数据加载器
    test_dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=0,
        pin_memory=True
    )
    print(len(test_dataloader))
    
    return test_dataloader


def main(args):
    """主函数"""
    # 设置模型路径
    model_paths = {
        "GPT4_v1": f"{args.model_path}/FUT_GPT4_TRD_MIN5_30_16_8_031810_0.114_ls.onnx",
        # "GPT4_v2": f"{args.model_path}/FUT_GPT4_TRD_MIN5_30_16_8_031810_0.114_ls.onnx",
        # "GPT4R_v1": f"{args.model_path}/FUT_GPT4R_TRD_MIN5_30_16_8_031810_0.114_ls.onnx",
        # 添加更多模型...
    }
    
    # 加载测试数据
    test_dataloader = load_test_dataloader(args)
    
    # 创建benchmark实例，指定任务类型为分类
    benchmark = ModelBenchmark(model_paths, test_dataloader, task_type=args.task_type)
    
    # 加载模型
    benchmark.load_models()
    
    # 评估模型
    benchmark.evaluate_models(num_classes=args.num_classes)
    
    # 比较结果
    results_df = benchmark.compare_results()
    print("\n模型比较结果:")
    print(results_df)
    
    # 可视化结果
    benchmark.visualize_results()
    
    # 保存结果
    benchmark.save_results()


if __name__ == "__main__":
    parser = ArgumentParser()

    # Data module ===========================
    parser.add_argument('--batch_size', default=1, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)
    parser.add_argument('--data_path', default='d:/RoboQuant2/store/barenc', type=str, help='path to the data')  
    parser.add_argument('--market', default='fut', choices=['fut', 'stk'], type=str, help='market')
    parser.add_argument('--block_name', default='sf', type=str, help='block name')
    parser.add_argument('--period', default='min5', choices=['day', 'min5'], type=str, help='period')
    parser.add_argument('--start_year', default=2024, type=int, help='start year of the data')  
    parser.add_argument('--end_year', default=2024, type=int, help='end year of the data')
    parser.add_argument('--start_date', default='', type=str)
    parser.add_argument('--end_date', default='', type=str)
    parser.add_argument('--block_size', default=20, type=int, help='block size')
    parser.add_argument('--step_size', default=1, type=int, help='step size')
    parser.add_argument('--sel_codes', default=MAIN_SEL_FUT_CODES, type=list, choices=[MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES], help='selected codes')
    parser.add_argument('--time_encoding', type=str, default='timeF', help='time features encoding, options:[timeF, fixed, learned]')

    parser.add_argument('--model_path', default='e:/lab/RoboQuant/pylab/model', type=str, help='model path')
    # 添加任务类型和类别数量参数
    parser.add_argument('--task_type', default='classification', choices=['regression', 'classification'], type=str, help='任务类型')
    parser.add_argument('--num_classes', default=40002, type=int, help='分类任务的类别数量')
    
    args = parser.parse_args()
    args.data_path = 'f:/featdata/barenc/db'
    args.market = 'fut'
    args.block_name = 'trd'
    args.period = 'min5'
    args.block_size = 30
    args.start_year = 2022
    args.end_year = 2022
    args.start_date = '2022-01-01'
    args.end_date = '2022-01-10'
    # 设置任务类型为分类
    args.task_type = 'classification'
    args.num_classes = 3  # 默认三分类模型
    print(args)
    main(args)
