{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 导入AICM交易特征数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import leveldb\n", "# import os, sys"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#初始化一个数据库students\n", "def initialize():\n", "    db = leveldb.LevelDB(\"d:/QuantLab/store/kv.db\")\n", "    return db\n", "\n", "#插入\n", "def insert(db, sid, name):\n", "    db.<PERSON>(str(sid), name)\n", "    \n", "#删除\n", "def delete(db, sid):\n", "    db.<PERSON><PERSON>(str(sid))\n", "    \n", "#更新\n", "def update(db, sid, name):\n", "    db.<PERSON>(str(sid), name)\n", "    \n", "#搜索\n", "def search(db, sid):\n", "    name = db.<PERSON>(str(sid))\n", "    return name\n", "\n", "#遍历\n", "def display(db):\n", "    for key, value in db.RangeIter():\n", "        print (key, value)\n", "\n", "def iter_key_values(db):\n", "    # db = leveldb.LevelDB('d:/QuantLab/store/historydata.db')\n", "    keys = list(db.<PERSON>Iter(include_value = False))\n", "    print(keys)\n", "\n", "    # keys_values = list(db.<PERSON>Iter())\n", "    # print keys_values"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "LevelDBError", "evalue": "Corruption: 165 missing files; e.g.: d:/QuantLab/store/kv.db/014187.sst", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mLevelDBError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-3-fdcb2d0efd3d>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mdb\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0minitialize\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[0miter_key_values\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdb\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m<ipython-input-2-1aa3762058a2>\u001b[0m in \u001b[0;36minitialize\u001b[1;34m()\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;31m#初始化一个数据库students\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;32mdef\u001b[0m \u001b[0minitialize\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 3\u001b[1;33m     \u001b[0mdb\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mleveldb\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mLevelDB\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"d:/QuantLab/store/kv.db\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      4\u001b[0m     \u001b[1;32mreturn\u001b[0m \u001b[0mdb\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mLevelDBError\u001b[0m: Corruption: 165 missing files; e.g.: d:/QuantLab/store/kv.db/014187.sst"]}], "source": ["db = initialize()\n", "iter_key_values(db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 2}