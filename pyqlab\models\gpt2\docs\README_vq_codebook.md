# VQ-VAE码本权重训练指南

本文档介绍如何训练VQ-VAE (Vector Quantized Variational Autoencoder) 模型的码本权重，以便在NonlinearVQTokenizer中使用。

## 简介

VQ-VAE是一种结合了向量量化和变分自编码器的深度学习模型，可以将连续的特征向量映射到离散的码本索引。在K线数据tokenization中，VQ-VAE可以捕捉复杂的K线模式关系，提供更强的表示能力。

NonlinearVQTokenizer结合了非线性映射和VQ-VAE的优点，但需要预训练的VQ-VAE码本权重才能发挥最佳效果。本文档介绍如何训练这些码本权重。

## 训练流程

训练VQ-VAE码本权重的流程如下：

1. 准备K线数据
2. 使用非线性映射预处理K线特征
3. 训练VQ-VAE模型
4. 提取码本权重
5. 在NonlinearVQTokenizer中使用码本权重

## 准备工作

### 环境要求

- Python 3.7+
- PyTorch 1.7+
- pandas
- numpy
- matplotlib
- scikit-learn
- tqdm

### 数据准备

训练VQ-VAE码本权重需要大量的K线数据。建议准备至少10,000个K线样本，以确保码本的多样性和代表性。

K线数据应该包含以下列：
- open: 开盘价
- high: 最高价
- low: 最低价
- close: 收盘价
- volume: 成交量（可选）
- datetime: 时间戳（可选，用于检测交易日间隔）

将K线数据保存为CSV文件，放在同一个目录中。

## 使用方法

### 命令行参数

训练脚本支持以下命令行参数：

```
--data_dir DIR          K线数据目录
--save_dir DIR          保存目录
--num_epochs N          训练轮数
--batch_size N          批次大小
--learning_rate N       学习率
--num_embeddings N      码本大小
--embedding_dim N       码向量维度
--hidden_dim N          隐藏层维度
--atr_window N          ATR计算窗口
--ma_volume_period N    成交量移动平均周期
--include_volume        是否包含交易量
--min_samples N         最小样本数
--max_samples N         最大样本数
--save_interval N       保存间隔
--device DEVICE         训练设备
```

### 使用批处理脚本

最简单的方法是使用提供的批处理脚本：

```
train_vq_codebook.bat --data_dir data\klines --save_dir models\vqvae
```

批处理脚本使用默认参数，您可以根据需要修改参数：

```
train_vq_codebook.bat --data_dir data\klines --save_dir models\vqvae --num_epochs 100 --batch_size 256 --num_embeddings 1024
```

### 直接使用Python脚本

您也可以直接使用Python脚本：

```
python -m pyqlab.models.base.train_vq_codebook --data_dir data\klines --save_dir models\vqvae
```

## 参数说明

### 重要参数

- `--num_embeddings`：码本大小，即码本中的码向量数量。这决定了NonlinearVQTokenizer的词汇表大小。建议值：512或1024。
- `--embedding_dim`：码向量维度，应与K线特征向量维度相同。如果包含交易量，则为5，否则为4。
- `--include_volume`：是否包含交易量特征。如果包含，则embedding_dim应为5。

### 其他参数

- `--num_epochs`：训练轮数，建议值：50-100。
- `--batch_size`：批次大小，建议值：128-256。
- `--learning_rate`：学习率，建议值：0.001。
- `--hidden_dim`：隐藏层维度，建议值：64-128。
- `--atr_window`：ATR计算窗口，建议值：100。
- `--ma_volume_period`：成交量移动平均周期，建议值：20。
- `--min_samples`：最小样本数，建议值：1000。
- `--max_samples`：最大样本数，建议值：100000。
- `--save_interval`：保存间隔，每隔多少轮保存一次模型和码本权重，建议值：5-10。
- `--device`：训练设备，可选值：cuda或cpu。

## 训练输出

训练过程中会生成以下文件：

- `vqvae_model_epoch_N.pt`：每N轮保存一次的模型权重。
- `vqvae_codebook_epoch_N.pt`：每N轮保存一次的码本权重。
- `vqvae_codebook_final.pt`：最终的码本权重，用于NonlinearVQTokenizer。
- `loss_curve.png`：训练损失曲线。
- `codebook_visualization.png`：码本可视化。

## 在NonlinearVQTokenizer中使用码本权重

训练完成后，您可以在NonlinearVQTokenizer中使用训练好的码本权重：

```python
from pyqlab.models.base.nonlinear_vq_tokenizer import NonlinearVQTokenizer
from pyqlab.models.llm.nonlinear_tokenizer import SigmoidMapping, SquareRootMapping

# 创建映射函数
mapping_functions = {
    'change': SigmoidMapping((-5, 5), (-1, 1)),
    'entity': SigmoidMapping((-3, 3), (-1, 1)),
    'upline': SquareRootMapping((0, 3), (0, 1)),
    'downline': SquareRootMapping((0, 3), (0, 1))
}

# 初始化tokenizer，使用训练好的码本权重
tokenizer = NonlinearVQTokenizer(
    codebook_weights_path='models/vqvae/vqvae_codebook_final.pt',
    num_embeddings=512,
    embedding_dim=4,
    mapping_functions=mapping_functions
)
```

## 注意事项

1. 训练VQ-VAE需要大量的计算资源，建议使用GPU进行训练。
2. 码本大小（num_embeddings）应根据K线模式的复杂性和多样性来选择。太小的码本可能无法捕捉所有重要的K线模式，太大的码本可能会导致过拟合。
3. 训练数据应该覆盖各种市场条件和K线模式，以确保码本的代表性。
4. 在NonlinearVQTokenizer中使用码本权重时，确保embedding_dim与训练时相同。
5. 如果包含交易量特征，确保在训练和使用时都设置include_volume=True。

## 故障排除

### 训练过程中损失不下降

- 尝试减小学习率
- 尝试增加隐藏层维度
- 检查数据预处理是否正确

### 内存不足

- 减小批次大小
- 减小max_samples
- 使用更小的hidden_dim

### 码本可视化显示异常

- 检查embedding_dim是否与K线特征维度匹配
- 检查数据预处理是否正确
- 尝试增加训练轮数
