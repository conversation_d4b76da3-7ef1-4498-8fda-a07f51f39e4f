# 数据集划分功能更新

## 概述

我们为 `pyqlab/models/gpt2/train_candlestick_vq_gpt.py` 添加了灵活的数据集划分功能，支持两种划分方式：
1. **时间顺序划分**（默认）：按时间顺序将前面的数据作为训练集，后面的数据作为验证集
2. **随机划分**：随机打乱数据后划分训练集和验证集

## 新增功能

### 1. 更新的 `split_dataset` 函数

```python
def split_dataset(dataset, val_ratio, seed=42, shuffle=True):
    """分割数据集为训练集和验证集
    
    Args:
        dataset: 完整数据集
        val_ratio: 验证集比例
        seed: 随机种子
        shuffle: 是否随机打乱数据集，True为随机划分，False为按时间顺序划分
    """
```

### 2. 新增命令行参数

```bash
--shuffle_split    # 是否随机打乱数据集划分（默认按时间顺序划分）
```

### 3. 支持新的向量化方法

同时添加了对 `CANDLESTICK_FEATURES` 向量化方法的支持：

```bash
--vectorization_method candlestick_features
```

## 使用方法

### 基本用法

#### 时间顺序划分（默认）
```bash
python pyqlab/models/gpt2/train_candlestick_vq_gpt.py \
    --data_dir /path/to/data \
    --market fut \
    --block_name sf \
    --period min1
```

#### 随机划分
```bash
python pyqlab/models/gpt2/train_candlestick_vq_gpt.py \
    --data_dir /path/to/data \
    --market fut \
    --block_name sf \
    --period min1 \
    --shuffle_split
```

#### 指定随机种子
```bash
python pyqlab/models/gpt2/train_candlestick_vq_gpt.py \
    --data_dir /path/to/data \
    --shuffle_split \
    --seed 123
```

### 高级用法

#### 使用新的向量化方法
```bash
python pyqlab/models/gpt2/train_candlestick_vq_gpt.py \
    --data_dir /path/to/data \
    --vectorization_method candlestick_features \
    --embedding_dim 5 \
    --shuffle_split
```

#### 调整验证集比例
```bash
python pyqlab/models/gpt2/train_candlestick_vq_gpt.py \
    --data_dir /path/to/data \
    --val_ratio 0.15 \
    --shuffle_split
```

## 两种划分方式的比较

### 时间顺序划分

**优点：**
- 符合实际交易场景：用历史数据预测未来
- 避免数据泄露：验证集的时间晚于训练集
- 更真实地反映模型在实际应用中的性能

**缺点：**
- 可能存在时间偏差（temporal bias）
- 如果数据存在时间趋势，验证集性能可能不能很好反映模型泛化能力
- 训练集和验证集的数据分布可能不同

**适用场景：**
- 时间序列预测任务
- 实际交易策略开发
- 需要模拟真实部署环境的场景

### 随机划分

**优点：**
- 更好地评估模型的泛化能力
- 训练集和验证集的数据分布更相似
- 避免时间偏差的影响
- 更符合传统机器学习的评估方式

**缺点：**
- 不符合实际交易场景（用未来数据训练预测过去）
- 可能存在数据泄露风险
- 验证结果可能过于乐观

**适用场景：**
- 模型算法研究和开发
- 评估模型的理论性能上限
- 需要快速验证模型有效性的场景

## 实验结果示例

### 时间顺序划分示例
```
总数据量: 1000
训练集: 800 样本 (索引 0-799)
验证集: 200 样本 (索引 800-999)

时间分界:
训练集最晚时间: 2023-01-01 13:19:00
验证集最早时间: 2023-01-01 13:20:00
时间连续性: ✅
```

### 随机划分示例
```
总数据量: 1000
训练集: 800 样本 (随机索引)
验证集: 200 样本 (随机索引)

时间分布:
训练集时间范围: 2023-01-01 00:00:00 ~ 2023-01-01 16:39:00
验证集时间范围: 2023-01-01 00:03:00 ~ 2023-01-01 16:38:00
```

## 可重现性

通过设置随机种子，可以确保随机划分的结果完全可重现：

```python
# 相同种子产生相同结果
train1, val1 = split_dataset(dataset, 0.2, seed=42, shuffle=True)
train2, val2 = split_dataset(dataset, 0.2, seed=42, shuffle=True)
assert train1.indices == train2.indices  # ✅ 完全一致

# 不同种子产生不同结果
train3, val3 = split_dataset(dataset, 0.2, seed=123, shuffle=True)
assert train1.indices != train3.indices  # ✅ 确实不同
```

## 最佳实践建议

### 1. 选择划分方式

- **研发阶段**：使用随机划分快速验证模型有效性
- **生产阶段**：使用时间顺序划分评估实际性能
- **论文发表**：两种方式都测试，提供更全面的评估

### 2. 验证集比例

- **小数据集**（< 10k样本）：建议 15-20%
- **中等数据集**（10k-100k样本）：建议 10-15%
- **大数据集**（> 100k样本）：建议 5-10%

### 3. 随机种子设置

- 使用固定种子确保实验可重现
- 在论文中报告使用的随机种子
- 可以尝试多个不同种子验证结果稳定性

### 4. 交叉验证

对于重要的模型评估，建议结合使用：
```bash
# 时间序列交叉验证
for i in range(5):
    # 使用不同的时间窗口进行划分
    python train_candlestick_vq_gpt.py --begin_date 2023-01-01 --end_date 2023-06-01
    python train_candlestick_vq_gpt.py --begin_date 2023-02-01 --end_date 2023-07-01
    # ...
```

## 技术实现细节

### 主要修改

1. **split_dataset 函数**：添加 `shuffle` 参数控制划分方式
2. **命令行参数**：添加 `--shuffle_split` 选项
3. **向量化方法**：支持 `candlestick_features` 方法
4. **随机种子**：确保结果可重现

### 兼容性

- 完全向后兼容：默认行为保持不变（时间顺序划分）
- 新参数为可选：不影响现有脚本
- 日志输出：清楚显示使用的划分方式

## 测试验证

我们提供了完整的测试套件：

1. **功能测试** (`test_dataset_split.py`)：验证基本功能
2. **使用示例** (`example_dataset_split_usage.py`)：演示实际用法
3. **边界情况测试**：小数据集、极端比例等

所有测试都通过验证，确保功能的正确性和稳定性。
