# -*- coding: utf-8 -*-

import os
import os.path
import ctypes
import locale
from gettext import NullTranslations, translation
from typing import Optional

from pyqlab.utils.logger import system_log


class Localization(object):

    def __init__(self, lc=None):
        if lc is None:
            # https://stackoverflow.com/questions/3425294/how-to-detect-the-os-default-language-in-python
            if os.name == "nt":
                lc = locale.windows_locale[ctypes.windll.kernel32.GetUserDefaultUILanguage()]
            else:
                # 修改虚拟环境的本地语言读取
                lc = os.getenv("LANG") or os.getenv("LC_CTYPE")
        self.trans = self.get_trans(lc)

    @classmethod
    def get_trans(cls, lc: Optional[str], trans_dir=None):
        if lc is not None and "cn" in lc.lower():
            locales = ["zh_Hans_CN"]
            try:
                if trans_dir is None:
                    trans_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "translations")
                return translation(domain="messages", localedir=trans_dir, languages=locales,)
            except Exception as e:
                system_log.debug(e)
                return NullTranslations()
        else:
            return NullTranslations()


localization: Optional[Localization] = None


def gettext(message):
    global localization
    if not localization:
        localization = Localization()
    return localization.trans.gettext(message)


def set_locale(lc: str = None):
    global localization
    localization = Localization(lc)
