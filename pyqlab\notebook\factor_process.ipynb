{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyqlab.scripts.factors_etl import AicmFactorsEtl\n", "# from pyqlab.data.dataset.loader import AFDataLoader"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 配置\n", "config_server = {\n", "    \"db_path\": \"c:/TRobot\",\n", "    \"data_path\": 'e:/lab/RoboQuant/pylab/data',\n", "    \"host\": \"**********\",\n", "    \"port\": 51301,\n", "    \"password\": \"wdljshbsjzsszsbbzyjcsz~1974\"\n", "}\n", "\n", "config_local = {\n", "    \"db_path\": \"d:/RoboQuant\",\n", "    \"data_path\": 'e:/lab/RoboQuant/pylab/data',\n", "    \"host\": \"**********\",\n", "    \"port\": 51301,\n", "    \"password\": \"wdljshbsjzsszsbbzyjcsz~1974\"\n", "}\n", "\n", "pfs_name_ids = {\n", "    \"zxjt_pjj\": {\n", "        \"FUT-ZXJT-P21\": \"00210102215917000\",\n", "        \"FUT-ZXJT-JMO\": \"00171009141918000\",\n", "        \"FUT-ZXJT-JHL\": \"00210303161416000\",\n", "    },\n", "    \"zxjt_xzy\": {\n", "        \"FUT-ZXJT-X21\": \"00210102224248000\",\n", "        \"FUT-ZXJT-XMO\": \"00171009141918000\",\n", "        \"FUT-ZXJT-XHL\": \"00210419180454000\",\n", "    },\n", "    \"nhqh_xzy\": {\n", "        \"FUT-NHQH-X21\": \"00210102223323000\",\n", "        \"FUT-NHQH-W1B\": \"00170908115033000\",\n", "        \"FUT-NHQH-NN1\": \"00220123224825000\",\n", "    },\n", "    \"gtja_xzy\": {\n", "        \"FUT-GTJA-X21\": \"00210102225821000\",\n", "        \"FUT-GTJA-W1B\": \"00171009141918000\",\n", "    }\n", "}\n", "\n", "pfs_main = ['00211229152555000', '00170623114649000']\n", "pfs_zxjt_pjj = ['00210102215917000', '00171009141918000', '00210303161416000']\n", "pfs_zxjt_xzy = ['00210102224248000', '00171009141918000', '00210419180454000']\n", "pfs_zxjt_nhqh = ['00210102223323000', '00170908115033000', '00220123224825000']\n", "pfs_zxjt_gtja = ['00210102225821000', '00171009141918000']\n", "pfs_local = ['00200910081133001', '00171106132928000', '06220831232331000']\n", "# pfs_local = ['00200910081133001', '00171106132928000', '00170607084458001', '00171122123535000']\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 导出所有本地数据\n", "# etl = AicmFactorsEtl(**config_server)\n", "# etl.dump_all_factors_from_local(pfs_zxjt_pjj)\n", "\n", "# etl = AicmFactorsEtl(**config_local)\n", "# etl.dump_all_factors_from_local(pfs_local)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 上载数据\n", "# etl = AicmFactorsEtl(**config_server)\n", "# etl.upload_a_week_factors(pfs_main)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["week_idx=int(datetime.now().timestamp()+259200)//604800\n", "print(f\"============== UPDATE FACTOR DATASET: {week_idx}================\")\n", "# # 更新服务器数据到本地\n", "etl = AicmFactorsEtl(**config_server)\n", "etl.update_a_week_factors_from_server(pfs_main, week_idx=week_idx)\n", "# # 更新本地库数据到本地\n", "etl = AicmFactorsEtl(**config_local)\n", "etl.update_a_week_factors_from_local(pfs_local, week_idx=week_idx)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["etl = AicmFactorsEtl(**config_local)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Portfolio: 00200910081133001 export order data: 7675         Today add count: 14\n", "7675 72566 72566 70965\n", "7174 7174 7174 7174\n", "Portfolio: 00171106132928000 export order data: 11109         Today add count: 21\n", "11109 72566 72566 70965\n", "10857 10857 10857 10857\n", "Portfolio: 06220831232331000 export order data: 1100         Today add count: 18\n", "1100 72566 72566 70965\n", "1091 1091 1091 1091\n"]}], "source": ["etl.dump_all_from_local_to_achive(pfs_local, data_path=\"./data\", name_suff=\".loc20221019\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 合并归档"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["lb_df_loc = pd.read_parquet(\"./data/lb_df.local20221019.parquet\", engine=\"fastparquet\")\n", "lf_df_loc = pd.read_parquet(\"./data/lf_df.local20221019.parquet\", engine=\"fastparquet\")\n", "sf_df_loc = pd.read_parquet(\"./data/sf_df.local20221019.parquet\", engine=\"fastparquet\")\n", "ct_df_loc = pd.read_parquet(\"./data/ct_df.local20221019.parquet\", engine=\"fastparquet\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["lb_df_srv = pd.read_parquet(\"./data/lb_df.srv20221019.parquet\", engine=\"fastparquet\")\n", "lf_df_srv = pd.read_parquet(\"./data/lf_df.srv20221019.parquet\", engine=\"fastparquet\")\n", "sf_df_srv = pd.read_parquet(\"./data/sf_df.srv20221019.parquet\", engine=\"fastparquet\")\n", "ct_df_srv = pd.read_parquet(\"./data/ct_df.srv20221019.parquet\", engine=\"fastparquet\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["all_lb_df = pd.concat([lb_df_loc, lb_df_srv]).reset_index(drop=True)\n", "all_lf_df = pd.concat([lf_df_loc, lf_df_srv]).reset_index(drop=True)\n", "all_sf_df = pd.concat([sf_df_loc, sf_df_srv]).reset_index(drop=True)\n", "all_ct_df = pd.concat([ct_df_loc, ct_df_srv]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["all_lb_df.to_parquet(\"./data/lb_df.20221019.parquet\", engine=\"fastparquet\")\n", "all_lf_df.to_parquet(\"./data/lf_df.20221019.parquet\", engine=\"fastparquet\")\n", "all_sf_df.to_parquet(\"./data/sf_df.20221019.parquet\", engine=\"fastparquet\")\n", "all_ct_df.to_parquet(\"./data/ct_df.20221019.parquet\", engine=\"fastparquet\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>portfolio_id</th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "      <th>CODE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922090831255</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 09:24:28</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922095208015</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 13:31:57</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922133807043</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 14:01:11</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922140751125</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210923 09:26:13</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00200910081133001</td>\n", "      <td>210923093149123</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210923 10:46:06</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39153</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221014090102414</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221017 10:48:05</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39154</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221017112646600</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221017 21:44:58</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39155</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221017223500282</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221018 13:30:01</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39156</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018210407060</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221019 13:52:29</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39157</th>\n", "      <td>01220901173143000</td>\n", "      <td>1220901211103180</td>\n", "      <td>ZN2210.SC</td>\n", "      <td>20220902 21:00:12</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>ZN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>39158 rows × 7 columns</p>\n", "</div>"], "text/plain": ["            portfolio_id            ord_id instrument           datetime  \\\n", "0      00200910081133001   210922090831255   A2111.DC  20210922 09:24:28   \n", "1      00200910081133001   210922095208015   A2111.DC  20210922 13:31:57   \n", "2      00200910081133001   210922133807043   A2111.DC  20210922 14:01:11   \n", "3      00200910081133001   210922140751125   A2111.DC  20210923 09:26:13   \n", "4      00200910081133001   210923093149123   A2111.DC  20210923 10:46:06   \n", "...                  ...               ...        ...                ...   \n", "39153  01220901173143000  1221014090102414   Y2301.DC  20221017 10:48:05   \n", "39154  01220901173143000  1221017112646600   Y2301.DC  20221017 21:44:58   \n", "39155  01220901173143000  1221017223500282   Y2301.DC  20221018 13:30:01   \n", "39156  01220901173143000  1221018210407060   Y2301.DC  20221019 13:52:29   \n", "39157  01220901173143000  1220901211103180  ZN2210.SC  20220902 21:00:12   \n", "\n", "      direct  label CODE  \n", "0          L      1    A  \n", "1          S      0    A  \n", "2          L      0    A  \n", "3          L      0    A  \n", "4          L      1    A  \n", "...      ...    ...  ...  \n", "39153      S      1    Y  \n", "39154      S      0    Y  \n", "39155      S      0    Y  \n", "39156      L      1    Y  \n", "39157      S      1   ZN  \n", "\n", "[39158 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(all_lb_df)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>portfolio_id</th>\n", "      <th>ord_id</th>\n", "      <th>lf_OPEN</th>\n", "      <th>lf_HIGH</th>\n", "      <th>lf_LOW</th>\n", "      <th>lf_CLOSE</th>\n", "      <th>lf_VOLUME</th>\n", "      <th>lf_TYPICAL_PRICE</th>\n", "      <th>lf_NEW</th>\n", "      <th>lf_NEW_CHANGE_PERCENT</th>\n", "      <th>...</th>\n", "      <th>lf_TREND_INPOSR_2</th>\n", "      <th>lf_TREND_HIGHEST</th>\n", "      <th>lf_TREND_LOWEST</th>\n", "      <th>lf_TREND_HLR</th>\n", "      <th>lf_TREND_LEVEL</th>\n", "      <th>lf_HYO_TENKAN_SEN</th>\n", "      <th>lf_HYO_KIJUN_SEN</th>\n", "      <th>lf_HYO_CROSS_BARS</th>\n", "      <th>lf_TATR</th>\n", "      <th>lf_TATR_THRESHOLD</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922090006095</td>\n", "      <td>2825.0</td>\n", "      <td>2861.0</td>\n", "      <td>0.0</td>\n", "      <td>2861.0</td>\n", "      <td>210107.0</td>\n", "      <td>2846.666667</td>\n", "      <td>2861.0</td>\n", "      <td>0.210158</td>\n", "      <td>...</td>\n", "      <td>1.682927</td>\n", "      <td>3079.0</td>\n", "      <td>2792.0</td>\n", "      <td>7.000000</td>\n", "      <td>0.0</td>\n", "      <td>2855.5</td>\n", "      <td>2935.5</td>\n", "      <td>-10.0</td>\n", "      <td>45.586666</td>\n", "      <td>3.460966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922090015109</td>\n", "      <td>3270.0</td>\n", "      <td>3164.0</td>\n", "      <td>0.0</td>\n", "      <td>3164.0</td>\n", "      <td>469004.0</td>\n", "      <td>3197.333333</td>\n", "      <td>3164.0</td>\n", "      <td>0.063251</td>\n", "      <td>...</td>\n", "      <td>2.025000</td>\n", "      <td>3326.0</td>\n", "      <td>2954.0</td>\n", "      <td>4.650000</td>\n", "      <td>0.0</td>\n", "      <td>3215.0</td>\n", "      <td>3140.0</td>\n", "      <td>4.0</td>\n", "      <td>86.539036</td>\n", "      <td>7.639543</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922090045119</td>\n", "      <td>12004.0</td>\n", "      <td>13048.0</td>\n", "      <td>0.0</td>\n", "      <td>12920.0</td>\n", "      <td>687740.0</td>\n", "      <td>12131.333333</td>\n", "      <td>12920.0</td>\n", "      <td>5.988515</td>\n", "      <td>...</td>\n", "      <td>0.364672</td>\n", "      <td>13048.0</td>\n", "      <td>6540.0</td>\n", "      <td>18.541311</td>\n", "      <td>0.0</td>\n", "      <td>12387.0</td>\n", "      <td>11261.0</td>\n", "      <td>52.0</td>\n", "      <td>287.139457</td>\n", "      <td>50.483417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922090045123</td>\n", "      <td>8915.0</td>\n", "      <td>8915.0</td>\n", "      <td>0.0</td>\n", "      <td>8865.0</td>\n", "      <td>743713.0</td>\n", "      <td>8803.333333</td>\n", "      <td>8865.0</td>\n", "      <td>1.198630</td>\n", "      <td>...</td>\n", "      <td>2.329545</td>\n", "      <td>9275.0</td>\n", "      <td>7495.0</td>\n", "      <td>10.113636</td>\n", "      <td>0.0</td>\n", "      <td>8980.0</td>\n", "      <td>8627.5</td>\n", "      <td>9.0</td>\n", "      <td>162.718598</td>\n", "      <td>15.880422</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00200910081133001</td>\n", "      <td>210922090053127</td>\n", "      <td>10050.0</td>\n", "      <td>10140.0</td>\n", "      <td>0.0</td>\n", "      <td>10075.0</td>\n", "      <td>821652.0</td>\n", "      <td>9918.333333</td>\n", "      <td>10075.0</td>\n", "      <td>2.544529</td>\n", "      <td>...</td>\n", "      <td>0.917031</td>\n", "      <td>10285.0</td>\n", "      <td>8295.0</td>\n", "      <td>8.689956</td>\n", "      <td>0.0</td>\n", "      <td>9917.5</td>\n", "      <td>9655.0</td>\n", "      <td>32.0</td>\n", "      <td>204.834484</td>\n", "      <td>23.057423</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39153</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018211014094</td>\n", "      <td>5661.0</td>\n", "      <td>5643.0</td>\n", "      <td>0.0</td>\n", "      <td>5642.0</td>\n", "      <td>170082.0</td>\n", "      <td>5651.000000</td>\n", "      <td>5642.0</td>\n", "      <td>0.017727</td>\n", "      <td>...</td>\n", "      <td>0.790698</td>\n", "      <td>5676.0</td>\n", "      <td>5440.0</td>\n", "      <td>5.488372</td>\n", "      <td>2.0</td>\n", "      <td>5613.5</td>\n", "      <td>5558.0</td>\n", "      <td>3.0</td>\n", "      <td>55.147183</td>\n", "      <td>9.433355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39154</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018212958128</td>\n", "      <td>7659.0</td>\n", "      <td>7630.0</td>\n", "      <td>0.0</td>\n", "      <td>7619.0</td>\n", "      <td>408667.0</td>\n", "      <td>7640.333333</td>\n", "      <td>7619.0</td>\n", "      <td>-0.144168</td>\n", "      <td>...</td>\n", "      <td>0.371681</td>\n", "      <td>8225.0</td>\n", "      <td>7577.0</td>\n", "      <td>5.734513</td>\n", "      <td>-2.0</td>\n", "      <td>7901.0</td>\n", "      <td>7901.0</td>\n", "      <td>-2.0</td>\n", "      <td>148.908663</td>\n", "      <td>34.590459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39155</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018215209162</td>\n", "      <td>5661.0</td>\n", "      <td>5644.0</td>\n", "      <td>0.0</td>\n", "      <td>5616.0</td>\n", "      <td>170082.0</td>\n", "      <td>5651.000000</td>\n", "      <td>5616.0</td>\n", "      <td>-0.443184</td>\n", "      <td>...</td>\n", "      <td>1.395349</td>\n", "      <td>5676.0</td>\n", "      <td>5440.0</td>\n", "      <td>5.488372</td>\n", "      <td>2.0</td>\n", "      <td>5613.5</td>\n", "      <td>5558.0</td>\n", "      <td>3.0</td>\n", "      <td>55.147183</td>\n", "      <td>9.433355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39156</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018222438216</td>\n", "      <td>2860.0</td>\n", "      <td>2855.0</td>\n", "      <td>0.0</td>\n", "      <td>2846.0</td>\n", "      <td>316158.0</td>\n", "      <td>2851.666667</td>\n", "      <td>2846.0</td>\n", "      <td>-0.315236</td>\n", "      <td>...</td>\n", "      <td>1.038462</td>\n", "      <td>2873.0</td>\n", "      <td>2742.0</td>\n", "      <td>5.038462</td>\n", "      <td>0.0</td>\n", "      <td>2829.5</td>\n", "      <td>2807.5</td>\n", "      <td>2.0</td>\n", "      <td>34.232481</td>\n", "      <td>2.156054</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39157</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221019133107566</td>\n", "      <td>3966.0</td>\n", "      <td>3948.0</td>\n", "      <td>0.0</td>\n", "      <td>3884.0</td>\n", "      <td>266127.0</td>\n", "      <td>3949.000000</td>\n", "      <td>3884.0</td>\n", "      <td>-1.546261</td>\n", "      <td>...</td>\n", "      <td>2.717647</td>\n", "      <td>4115.0</td>\n", "      <td>3575.0</td>\n", "      <td>6.352941</td>\n", "      <td>0.0</td>\n", "      <td>3998.5</td>\n", "      <td>3845.0</td>\n", "      <td>9.0</td>\n", "      <td>122.802692</td>\n", "      <td>23.109120</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>39158 rows × 128 columns</p>\n", "</div>"], "text/plain": ["            portfolio_id            ord_id  lf_OPEN  lf_HIGH  lf_LOW  \\\n", "0      00200910081133001   210922090006095   2825.0   2861.0     0.0   \n", "1      00200910081133001   210922090015109   3270.0   3164.0     0.0   \n", "2      00200910081133001   210922090045119  12004.0  13048.0     0.0   \n", "3      00200910081133001   210922090045123   8915.0   8915.0     0.0   \n", "4      00200910081133001   210922090053127  10050.0  10140.0     0.0   \n", "...                  ...               ...      ...      ...     ...   \n", "39153  01220901173143000  1221018211014094   5661.0   5643.0     0.0   \n", "39154  01220901173143000  1221018212958128   7659.0   7630.0     0.0   \n", "39155  01220901173143000  1221018215209162   5661.0   5644.0     0.0   \n", "39156  01220901173143000  1221018222438216   2860.0   2855.0     0.0   \n", "39157  01220901173143000  1221019133107566   3966.0   3948.0     0.0   \n", "\n", "       lf_CLOSE  lf_VOLUME  lf_TYPICAL_PRICE   lf_NEW  lf_NEW_CHANGE_PERCENT  \\\n", "0        2861.0   210107.0       2846.666667   2861.0               0.210158   \n", "1        3164.0   469004.0       3197.333333   3164.0               0.063251   \n", "2       12920.0   687740.0      12131.333333  12920.0               5.988515   \n", "3        8865.0   743713.0       8803.333333   8865.0               1.198630   \n", "4       10075.0   821652.0       9918.333333  10075.0               2.544529   \n", "...         ...        ...               ...      ...                    ...   \n", "39153    5642.0   170082.0       5651.000000   5642.0               0.017727   \n", "39154    7619.0   408667.0       7640.333333   7619.0              -0.144168   \n", "39155    5616.0   170082.0       5651.000000   5616.0              -0.443184   \n", "39156    2846.0   316158.0       2851.666667   2846.0              -0.315236   \n", "39157    3884.0   266127.0       3949.000000   3884.0              -1.546261   \n", "\n", "       ...  lf_TREND_INPOSR_2  lf_TREND_HIGHEST  lf_TREND_LOWEST  \\\n", "0      ...           1.682927            3079.0           2792.0   \n", "1      ...           2.025000            3326.0           2954.0   \n", "2      ...           0.364672           13048.0           6540.0   \n", "3      ...           2.329545            9275.0           7495.0   \n", "4      ...           0.917031           10285.0           8295.0   \n", "...    ...                ...               ...              ...   \n", "39153  ...           0.790698            5676.0           5440.0   \n", "39154  ...           0.371681            8225.0           7577.0   \n", "39155  ...           1.395349            5676.0           5440.0   \n", "39156  ...           1.038462            2873.0           2742.0   \n", "39157  ...           2.717647            4115.0           3575.0   \n", "\n", "       lf_TREND_HLR  lf_TREND_LEVEL  lf_HYO_TENKAN_SEN  lf_HYO_KIJUN_SEN  \\\n", "0          7.000000             0.0             2855.5            2935.5   \n", "1          4.650000             0.0             3215.0            3140.0   \n", "2         18.541311             0.0            12387.0           11261.0   \n", "3         10.113636             0.0             8980.0            8627.5   \n", "4          8.689956             0.0             9917.5            9655.0   \n", "...             ...             ...                ...               ...   \n", "39153      5.488372             2.0             5613.5            5558.0   \n", "39154      5.734513            -2.0             7901.0            7901.0   \n", "39155      5.488372             2.0             5613.5            5558.0   \n", "39156      5.038462             0.0             2829.5            2807.5   \n", "39157      6.352941             0.0             3998.5            3845.0   \n", "\n", "       lf_HYO_CROSS_BARS     lf_TATR  lf_TATR_THRESHOLD  \n", "0                  -10.0   45.586666           3.460966  \n", "1                    4.0   86.539036           7.639543  \n", "2                   52.0  287.139457          50.483417  \n", "3                    9.0  162.718598          15.880422  \n", "4                   32.0  204.834484          23.057423  \n", "...                  ...         ...                ...  \n", "39153                3.0   55.147183           9.433355  \n", "39154               -2.0  148.908663          34.590459  \n", "39155                3.0   55.147183           9.433355  \n", "39156                2.0   34.232481           2.156054  \n", "39157                9.0  122.802692          23.109120  \n", "\n", "[39158 rows x 128 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(all_lf_df)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["lb_df = pd.read_parquet(\"./data/lb_df.20221019.parquet\", engine=\"fastparquet\")\n", "ct_df = pd.read_parquet(\"./data/ct_df.20221019.parquet\", engine=\"fastparquet\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["lb_df = lb_df[lb_df['portfolio_id'].isin(['06220831232331000', '01220901173143000'])]\n", "ct_df = ct_df[ct_df['portfolio_id'].isin(['06220831232331000', '01220901173143000'])]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>portfolio_id</th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "      <th>CODE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18031</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220901090413014</td>\n", "      <td>A2211.DC</td>\n", "      <td>20220902 09:14:51</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18032</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220902103209425</td>\n", "      <td>A2211.DC</td>\n", "      <td>20220902 21:40:36</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18033</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220905090112060</td>\n", "      <td>A2211.DC</td>\n", "      <td>20220905 21:05:48</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18034</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220905211444180</td>\n", "      <td>A2211.DC</td>\n", "      <td>20220905 22:12:01</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18035</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220906090607046</td>\n", "      <td>A2211.DC</td>\n", "      <td>20220907 21:45:13</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39153</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221014090102414</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221017 10:48:05</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39154</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221017112646600</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221017 21:44:58</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39155</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221017223500282</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221018 13:30:01</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39156</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018210407060</td>\n", "      <td>Y2301.DC</td>\n", "      <td>20221019 13:52:29</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39157</th>\n", "      <td>01220901173143000</td>\n", "      <td>1220901211103180</td>\n", "      <td>ZN2210.SC</td>\n", "      <td>20220902 21:00:12</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>ZN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2363 rows × 7 columns</p>\n", "</div>"], "text/plain": ["            portfolio_id            ord_id instrument           datetime  \\\n", "18031  06220831232331000  6220901090413014   A2211.DC  20220902 09:14:51   \n", "18032  06220831232331000  6220902103209425   A2211.DC  20220902 21:40:36   \n", "18033  06220831232331000  6220905090112060   A2211.DC  20220905 21:05:48   \n", "18034  06220831232331000  6220905211444180   A2211.DC  20220905 22:12:01   \n", "18035  06220831232331000  6220906090607046   A2211.DC  20220907 21:45:13   \n", "...                  ...               ...        ...                ...   \n", "39153  01220901173143000  1221014090102414   Y2301.DC  20221017 10:48:05   \n", "39154  01220901173143000  1221017112646600   Y2301.DC  20221017 21:44:58   \n", "39155  01220901173143000  1221017223500282   Y2301.DC  20221018 13:30:01   \n", "39156  01220901173143000  1221018210407060   Y2301.DC  20221019 13:52:29   \n", "39157  01220901173143000  1220901211103180  ZN2210.SC  20220902 21:00:12   \n", "\n", "      direct  label CODE  \n", "18031      L      0    A  \n", "18032      S      0    A  \n", "18033      S      1    A  \n", "18034      S      0    A  \n", "18035      S      0    A  \n", "...      ...    ...  ...  \n", "39153      S      1    Y  \n", "39154      S      0    Y  \n", "39155      S      0    Y  \n", "39156      L      1    Y  \n", "39157      S      1   ZN  \n", "\n", "[2363 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(lb_df)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>portfolio_id</th>\n", "      <th>ord_id</th>\n", "      <th>COST_RNG</th>\n", "      <th>DRAWDOWN_RNG</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>PNL</th>\n", "      <th>POS_DAYS</th>\n", "      <th>POS_SHORT_BARS</th>\n", "      <th>POS_LONG_BARS</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>...</th>\n", "      <th>SLOW_QH_HLR_STDDEV</th>\n", "      <th>SLOW_QH_HLR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>SLOW_QH_MOM</th>\n", "      <th>SLOW_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_STDDEV</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "      <th>SLOW_QH_MOM_STDDEV</th>\n", "      <th>SLOW_QH_MOM_DIRECT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18031</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220901090207000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.137554</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.75</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18032</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220901090207002</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.161166</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.00</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18033</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220901090258008</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.252396</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>22.00</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18034</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220901090300010</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.216511</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>19.00</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18035</th>\n", "      <td>06220831232331000</td>\n", "      <td>6220901090402012</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.249837</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.11</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39153</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018211014094</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.436857</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.80</td>\n", "      <td>...</td>\n", "      <td>0.685163</td>\n", "      <td>-1.0</td>\n", "      <td>-1.893947</td>\n", "      <td>-0.612991</td>\n", "      <td>-36.955882</td>\n", "      <td>-0.830117</td>\n", "      <td>5.178425</td>\n", "      <td>-4.0</td>\n", "      <td>57.350264</td>\n", "      <td>-4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39154</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018212958128</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.290377</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>12.00</td>\n", "      <td>...</td>\n", "      <td>0.685168</td>\n", "      <td>-1.0</td>\n", "      <td>7.985619</td>\n", "      <td>1.295021</td>\n", "      <td>-48.661765</td>\n", "      <td>-1.021621</td>\n", "      <td>5.182625</td>\n", "      <td>-3.0</td>\n", "      <td>57.699991</td>\n", "      <td>-4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39155</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018215209162</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.433390</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.80</td>\n", "      <td>...</td>\n", "      <td>0.685229</td>\n", "      <td>-1.0</td>\n", "      <td>-6.938744</td>\n", "      <td>-1.581275</td>\n", "      <td>-54.838235</td>\n", "      <td>-1.121172</td>\n", "      <td>5.190506</td>\n", "      <td>1.0</td>\n", "      <td>57.913475</td>\n", "      <td>-4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39156</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221018222438216</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.370603</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.97</td>\n", "      <td>...</td>\n", "      <td>0.685229</td>\n", "      <td>-1.0</td>\n", "      <td>-4.833326</td>\n", "      <td>-1.169082</td>\n", "      <td>-73.363636</td>\n", "      <td>-1.412576</td>\n", "      <td>5.193863</td>\n", "      <td>-3.0</td>\n", "      <td>58.671117</td>\n", "      <td>-4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39157</th>\n", "      <td>01220901173143000</td>\n", "      <td>1221019133107566</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.425781</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.00</td>\n", "      <td>...</td>\n", "      <td>0.680631</td>\n", "      <td>-1.0</td>\n", "      <td>-5.944702</td>\n", "      <td>-1.368656</td>\n", "      <td>-47.926471</td>\n", "      <td>-0.966072</td>\n", "      <td>5.201783</td>\n", "      <td>1.0</td>\n", "      <td>58.073587</td>\n", "      <td>-4.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2363 rows × 48 columns</p>\n", "</div>"], "text/plain": ["            portfolio_id            ord_id  COST_RNG  DRAWDOWN_RNG  \\\n", "18031  06220831232331000  6220901090207000       0.0           0.0   \n", "18032  06220831232331000  6220901090207002       0.0           0.0   \n", "18033  06220831232331000  6220901090258008       0.0           0.0   \n", "18034  06220831232331000  6220901090300010       0.0           0.0   \n", "18035  06220831232331000  6220901090402012       0.0           0.0   \n", "...                  ...               ...       ...           ...   \n", "39153  01220901173143000  1221018211014094       0.0           0.0   \n", "39154  01220901173143000  1221018212958128       0.0           0.0   \n", "39155  01220901173143000  1221018215209162       0.0           0.0   \n", "39156  01220901173143000  1221018222438216       0.0           0.0   \n", "39157  01220901173143000  1221019133107566       0.0           0.0   \n", "\n", "       STDDEV_RNG  PNL  POS_DAYS  POS_SHORT_BARS  POS_LONG_BARS  SHORT_RANGE  \\\n", "18031    1.137554  0.0       0.0             0.0            0.0         4.75   \n", "18032    1.161166  0.0       0.0             0.0            0.0         6.00   \n", "18033    1.252396  0.0       0.0             0.0            0.0        22.00   \n", "18034    1.216511  0.0       0.0             0.0            0.0        19.00   \n", "18035    1.249837  0.0       0.0             0.0            0.0         4.11   \n", "...           ...  ...       ...             ...            ...          ...   \n", "39153    1.436857  0.0       0.0             0.0            0.0         4.80   \n", "39154    1.290377  0.0       0.0             0.0            0.0        12.00   \n", "39155    1.433390  0.0       0.0             0.0            0.0         4.80   \n", "39156    1.370603  0.0       0.0             0.0            0.0         2.97   \n", "39157    1.425781  0.0       0.0             0.0            0.0         8.00   \n", "\n", "       ...  SLOW_QH_HLR_STDDEV  SLOW_QH_HLR_DIRECT  FAST_QH_MOM  \\\n", "18031  ...            0.000000                 0.0     0.000000   \n", "18032  ...            0.000000                 0.0     0.000000   \n", "18033  ...            0.000000                 0.0     0.000000   \n", "18034  ...            0.000000                 0.0     0.000000   \n", "18035  ...            0.000000                 0.0     0.000000   \n", "...    ...                 ...                 ...          ...   \n", "39153  ...            0.685163                -1.0    -1.893947   \n", "39154  ...            0.685168                -1.0     7.985619   \n", "39155  ...            0.685229                -1.0    -6.938744   \n", "39156  ...            0.685229                -1.0    -4.833326   \n", "39157  ...            0.680631                -1.0    -5.944702   \n", "\n", "       FAST_QH_MOM_ZSCORE  SLOW_QH_MOM  SLOW_QH_MOM_ZSCORE  \\\n", "18031            0.000000     0.000000            0.000000   \n", "18032            0.000000     0.000000            0.000000   \n", "18033            0.000000     0.000000            0.000000   \n", "18034            0.000000     0.000000            0.000000   \n", "18035            0.000000     0.000000            0.000000   \n", "...                   ...          ...                 ...   \n", "39153           -0.612991   -36.955882           -0.830117   \n", "39154            1.295021   -48.661765           -1.021621   \n", "39155           -1.581275   -54.838235           -1.121172   \n", "39156           -1.169082   -73.363636           -1.412576   \n", "39157           -1.368656   -47.926471           -0.966072   \n", "\n", "       FAST_QH_MOM_STDDEV  FAST_QH_MOM_DIRECT  SLOW_QH_MOM_STDDEV  \\\n", "18031            0.000000                 0.0            0.000000   \n", "18032            0.000000                 0.0            0.000000   \n", "18033            0.000000                 0.0            0.000000   \n", "18034            0.000000                 0.0            0.000000   \n", "18035            0.000000                 0.0            0.000000   \n", "...                   ...                 ...                 ...   \n", "39153            5.178425                -4.0           57.350264   \n", "39154            5.182625                -3.0           57.699991   \n", "39155            5.190506                 1.0           57.913475   \n", "39156            5.193863                -3.0           58.671117   \n", "39157            5.201783                 1.0           58.073587   \n", "\n", "       SLOW_QH_MOM_DIRECT  \n", "18031                 0.0  \n", "18032                 0.0  \n", "18033                 0.0  \n", "18034                 0.0  \n", "18035                 0.0  \n", "...                   ...  \n", "39153                -4.0  \n", "39154                -4.0  \n", "39155                -4.0  \n", "39156                -4.0  \n", "39157                -4.0  \n", "\n", "[2363 rows x 48 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(ct_df)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": [" 1.0    586\n", "-1.0    553\n", " 0.0    338\n", "-2.0    219\n", "-4.0    179\n", " 2.0    177\n", " 4.0    152\n", "-3.0     96\n", " 3.0     63\n", "Name: FAST_QH_DIRECT, dtype: int64"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["ct_df['FAST_QH_DIRECT'].value_counts()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["-1.0    587\n", " 1.0    581\n", " 0.0    294\n", "-2.0    199\n", " 2.0    187\n", "-4.0    174\n", " 4.0    146\n", " 3.0    119\n", "-3.0     76\n", "Name: FAST_QH_MOM_DIRECT, dtype: int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["ct_df['FAST_QH_MOM_DIRECT'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}