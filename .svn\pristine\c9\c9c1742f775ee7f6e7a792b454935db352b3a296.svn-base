import inspect
import torch
import importlib
from torch.nn import functional as F
import torch.optim.lr_scheduler as lrs
from torch.optim.lr_scheduler import ReduceLROnPlateau
import pytorch_lightning as pl
from torch.optim import Adam, AdamW
from torchmetrics.functional import accuracy

class DictToObject:
    def __init__(self, dict_data):
        for key, value in dict_data.items():
            if isinstance(value, dict):
                value = DictToObject(value)
            setattr(self, key, value)

class PLTsModel(pl.LightningModule):
    def __init__(self, model_name, loss, lr, **kargs):
        super().__init__()
        self.kargs = kargs
        self.save_hyperparameters()
        self.load_model()
        self.configure_loss()

    def forward(self, *args, **kwargs):
        embds, x, x_mark, y, y_mark = args
        return self.model(embds, x, x_mark, y, y_mark)

    def training_step(self, batch, batch_idx):
        embds, x, x_mark, y, y_mark = batch
        outputs = self(embds, x, x_mark, y, y_mark)
        f_dim = -1 # if self.kargs.features == 'MS' else 0
        outputs = outputs[:, -self.kargs['pred_len']:, f_dim:]
        y = y[:, -self.kargs['pred_len']:, f_dim:]
        loss = self.loss_function(outputs, y)
        self.log('loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        return loss

        # if self.is_acc:
        #     outputs = torch.argmax(outputs,dim=1)
        #     acc = accuracy(outputs, y, task='multiclass', num_classes=3)
        #     self.log('acc', acc, on_step=True, on_epoch=True, prog_bar=True)
        #     return {"loss": loss, "acc": acc}
        # else:
        #     return loss

    def validation_step(self, batch, batch_idx):
        embds, x, x_mark, y, y_mark = batch
        outputs = self(embds, x, x_mark, y, y_mark)
        f_dim = -1 # if self.kargs.features == 'MS' else 0
        outputs = outputs[:, -self.kargs['pred_len']:, f_dim:]
        y = y[:, -self.kargs['pred_len']:, f_dim:]
        loss = self.loss_function(outputs, y)
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        return loss

    def test_step(self, batch, batch_idx):
        # Here we just reuse the validation_step for testing
        return self.validation_step(batch, batch_idx)
    
    # def on_train_epoch_end(self):
    #     # Make the Progress Bar leave there
    #     self.log('dummy', 0, on_step=False, on_epoch=True, prog_bar=True)

    # def on_validation_epoch_end(self):
    #     # Make the Progress Bar leave there
    #     self.log('dummy', 0, on_step=False, on_epoch=True, prog_bar=True)

    def configure_optimizers(self):
        if hasattr(self.hparams, 'weight_decay'):
            weight_decay = self.hparams.weight_decay
        else:
            weight_decay = 0
        # print(self.hparams.optimizer)
        if self.hparams.optimizer == 'adamw':
            optimizer = AdamW(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)
        else:
            optimizer = Adam(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)

        if self.hparams.lr_scheduler is None:
            return optimizer
        else:
            if self.hparams.lr_scheduler == 'step':
                scheduler = lrs.StepLR(optimizer,
                                       step_size=self.hparams.lr_decay_steps,
                                       gamma=self.hparams.lr_decay_rate)
            elif self.hparams.lr_scheduler == 'cosine':
                scheduler = lrs.CosineAnnealingLR(optimizer,
                                                  T_max=self.hparams.lr_decay_steps,
                                                  eta_min=self.hparams.lr_decay_min_lr)
            elif self.hparams.lr_scheduler == 'plateau':
                scheduler = lrs.ReduceLROnPlateau(optimizer,
                                              mode='min',
                                              factor=self.hparams.lr_decay_rate,
                                              patience=self.hparams.lr_decay_steps,
                                              min_lr=self.hparams.lr_decay_min_lr,
                                              verbose=True)
            elif self.hparams.lr_scheduler == 'reduce_on_plateau':
                return {
                    "optimizer": optimizer,
                    "lr_scheduler": {
                        "scheduler": ReduceLROnPlateau(optimizer,
                                              mode='min',
                                              factor=self.hparams.lr_decay_rate,
                                              patience=self.hparams.lr_decay_steps,
                                              min_lr=self.hparams.lr_decay_min_lr,
                                              verbose=True),
                        "interval": "epoch",
                        "monitor": "val_loss",
                    }
                }
            else:
                raise ValueError('Invalid lr_scheduler type!')
            return [optimizer], [scheduler]

    def configure_loss(self):
        loss = self.hparams.loss.lower()
        if loss == 'mse':
            self.loss_function = F.mse_loss
            self.is_acc = False
        elif loss == 'l1':
            self.loss_function = F.l1_loss
            self.is_acc = False
        elif loss == 'bce':
            self.loss_function = F.binary_cross_entropy
            self.is_acc = True
        elif loss == 'ce':
            self.loss_function = F.cross_entropy
            self.is_acc = True
        else:
            raise ValueError("Invalid Loss Type!")

    def configure_model(self):
        self = torch.compile(self, backend='inductor')
                
    def load_model(self):
        name = self.hparams.model_name
        # Change the `snake_case.py` file name to `CamelCase` class name.
        # Please always name your model file name as `snake_case.py` and
        # class name corresponding `CamelCase`.
        # camel_name = ''.join([i.capitalize() for i in name.split('_')])
        camel_name = 'Model'
        mod_path = self.hparams.model_path + '.' + name
        try:
            print(f"Model {mod_path}.{camel_name} Loaded!")
            model = getattr(importlib.import_module(
                mod_path, package=__package__), camel_name)
        except:
            raise ValueError(
                f'Invalid Module File Name or Invalid Class Name {mod_path}.{camel_name}!')
        self.model = self.instancialize(model)


    def instancialize(self, Model, **other_args):
        """ Instancialize a model using the corresponding parameters
            from self.hparams dictionary. You can also input any args
            to overwrite the corresponding value in self.hparams.
        """
        # class_args = inspect.getfullargspec (Model.__init__).args[1:]
        class_args = self.kargs
        inkeys = self.hparams.keys()
        args1 = {}
        for arg in class_args:
            if arg in inkeys:
                args1[arg] = getattr(self.hparams, arg)
            if arg in self.kargs.keys():
                if arg == 'num_embeds':
                    args1[arg] = self.kargs[arg]
                elif arg == 'out_channels':
                    args1[arg] = self.kargs[arg]
                elif arg == 'ins_nums':
                    args1[arg] = self.kargs[arg]
                else:
                    args1[arg] = self.kargs[arg]
        args1.update(other_args)
        args1.update(self.kargs)
        args1 = DictToObject(args1)
        return Model(args1)

