# From: gluonts/src/gluonts/time_feature/_base.py
# Copyright 2018 Amazon.com, Inc. or its affiliates. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License").
# You may not use this file except in compliance with the License.
# A copy of the License is located at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# or in the "license" file accompanying this file. This file is distributed
# on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
# express or implied. See the License for the specific language governing
# permissions and limitations under the License.

from typing import List

import numpy as np
import pandas as pd
from pandas.tseries import offsets
from pandas.tseries.frequencies import to_offset
from datetime import datetime


class TimeFeature:
    def __init__(self):
        pass

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        pass

    def __repr__(self):
        return self.__class__.__name__ + "()"


class SecondOfMinute(TimeFeature):
    """Minute of hour encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return index.second / 59.0 - 0.5


class MinuteOfHour(TimeFeature):
    """Minute of hour encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return index.minute / 59.0 - 0.5


class HourOfDay(TimeFeature):
    """Hour of day encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return index.hour / 23.0 - 0.5


class DayOfWeek(TimeFeature):
    """Hour of day encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return index.dayofweek / 6.0 - 0.5


class DayOfMonth(TimeFeature):
    """Day of month encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return (index.day - 1) / 30.0 - 0.5


class DayOfYear(TimeFeature):
    """Day of year encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return (index.dayofyear - 1) / 365.0 - 0.5


class MonthOfYear(TimeFeature):
    """Month of year encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return (index.month - 1) / 11.0 - 0.5


class WeekOfYear(TimeFeature):
    """Week of year encoded as value between [-0.5, 0.5]"""

    def __call__(self, index: pd.DatetimeIndex) -> np.ndarray:
        return (index.isocalendar().week - 1) / 52.0 - 0.5


def time_features_from_frequency_str(freq_str: str) -> List[TimeFeature]:
    """
    Returns a list of time features that will be appropriate for the given frequency string.
    Parameters
    ----------
    freq_str
        Frequency string of the form [multiple][granularity] such as "12H", "5min", "1D" etc.
    """

    features_by_offsets = {
        offsets.YearEnd: [],
        offsets.QuarterEnd: [MonthOfYear],
        offsets.MonthEnd: [MonthOfYear],
        offsets.Week: [DayOfMonth, WeekOfYear],
        offsets.Day: [DayOfWeek, DayOfMonth, DayOfYear],
        offsets.BusinessDay: [DayOfWeek, DayOfMonth, DayOfYear],
        offsets.Hour: [HourOfDay, DayOfWeek, DayOfMonth, DayOfYear],
        offsets.Minute: [
            MinuteOfHour,
            HourOfDay,
            DayOfWeek,
            DayOfMonth,
            DayOfYear,
        ],
        offsets.Second: [
            SecondOfMinute,
            MinuteOfHour,
            HourOfDay,
            DayOfWeek,
            DayOfMonth,
            DayOfYear,
        ],
    }

    offset = to_offset(freq_str)

    for offset_type, feature_classes in features_by_offsets.items():
        if isinstance(offset, offset_type):
            return [cls() for cls in feature_classes]

    supported_freq_msg = f"""
    Unsupported frequency {freq_str}
    The following frequencies are supported:
        Y   - yearly
            alias: A
        M   - monthly
        W   - weekly
        D   - daily
        B   - business days
        H   - hourly
        T   - minutely
            alias: min
        S   - secondly
    """
    raise RuntimeError(supported_freq_msg)


def time_features(dates, freq='h'):
    return np.vstack([feat(dates) for feat in time_features_from_frequency_str(freq)])


# --- 时间特征辅助函数 ---
def cyclical_encoding(value, max_val):
    """对周期性特征进行 sin/cos 编码"""
    sin = np.sin(2 * np.pi * value / max_val)
    cos = np.cos(2 * np.pi * value / max_val)
    return sin, cos

def datetime_features(dt_list: List[datetime]):
    """
    从 datetime 对象提取特征向量。
    你可以根据需要添加/修改特征。
    """
    features = []
    for dt in dt_list:
        # 示例特征：小时, 星期几, 月份, 年份中的第几天
        minute = dt.hour * 60 + dt.minute
        hour = dt.hour
        day_of_week = dt.weekday() # Monday=0, Sunday=6
        month = dt.month
        day_of_year = dt.timetuple().tm_yday

        # 进行周期编码
        minute_sin, minute_cos = cyclical_encoding(minute, 1440)
        hour_sin, hour_cos = cyclical_encoding(hour, 24)
        dow_sin, dow_cos = cyclical_encoding(day_of_week, 7)
        month_sin, month_cos = cyclical_encoding(month, 12)
        doy_sin, doy_cos = cyclical_encoding(day_of_year, 366) # 考虑闰年

        # 组合特征 (可以添加其他非周期特征，如年份，并进行归一化)
        # 这里只用了周期特征作为示例
        feature = [
            minute_sin, minute_cos,
            hour_sin, hour_cos,
            dow_sin, dow_cos,
            month_sin, month_cos,
            doy_sin, doy_cos
            ]
        features.append(feature)
    return np.array(features, dtype=np.float32)


def convert_time_tf(df, timeenc=0):
    if df.empty or 'datetime' not in df.columns:
        return None

    # 将date列由timestamp转换为东8区日期时间
    df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)
    
    if timeenc == 0:
        # 使用更高效的向量化操作替代apply
        df['Month'] = df.datetime.dt.month - 1
        df['Day'] = df.datetime.dt.day - 1
        # 星期日设为0为一周的开端,weekday()返回0-6，对应星期一到星期日
        df['DayOfWeek'] = np.where(df.datetime.dt.weekday < 6, df.datetime.dt.weekday + 1, 0)
        df['Hour'] = df.datetime.dt.hour
        df['Minute'] = df.datetime.dt.minute // 5
        
    elif timeenc == 1:
        df_stamp= time_features(pd.to_datetime(df['datetime'].values), freq='t').transpose(1, 0)
        df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])
        df = pd.concat([df, df_tf], axis=1)
        
        # 对于特殊标记（非交易日），设置特殊的时间特征
        if 'is_holiday' in df.columns:
            # 对于非交易日，设置特殊的DayOfWeek值
            df.loc[df['is_holiday'], 'DayOfWeek'] = -1
            
            # 删除临时列
            df.drop(columns=['is_holiday'], inplace=True)
    elif timeenc == 2:
        df_stamp= datetime_features(pd.to_datetime(df['datetime'].values)) #.transpose(1, 0)
        df_tf = pd.DataFrame(df_stamp, columns=
                                ['hour_sin', 'hour_cos',
                            'dow_sin', 'dow_cos',
                            'month_sin', 'month_cos',
                            'doy_sin', 'doy_cos'])
        df = pd.concat([df, df_tf], axis=1)
    else:
        raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
    return df

def get_next_time_tf(current_time, timeenc=0, freq='min5'):
    if freq == 'min5':
        next_time = current_time + pd.Timedelta(minutes=5)
    elif freq == 'min1':
        next_time = current_time + pd.Timedelta(minutes=1)
    elif freq == 'day':
        next_time = current_time + pd.Timedelta(days=1)
    else:
        raise ValueError("Invalid interperiod! interperiod should be '5min', '1min' or '1day'.")

    tf = []
    if timeenc == 0:
        tf = [next_time.month - 1, next_time.day - 1, next_time.weekday() + 1 if next_time.weekday() < 6 else 0, next_time.hour, next_time.minute // 5]
        tf = np.array(tf, dtype=np.float32)
    elif timeenc == 1:
        tf = time_features(pd.to_datetime([next_time]), freq='t').transpose(1, 0)[0]
    elif timeenc == 2:
        tf = datetime_features([next_time])[0]
    else:
        raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
    return tf
  
