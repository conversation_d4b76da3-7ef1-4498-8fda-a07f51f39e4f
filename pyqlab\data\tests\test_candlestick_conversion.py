import unittest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import mplfinance as mpf
import talib
from pyqlab.data.dataset.utils import candlestick_to_bar_token, bar_token_to_candlestick, get_vocab

class TestCandlestickConversion(unittest.TestCase):
    def setUp(self):
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=150, freq='D')
        np.random.seed(42)
        
        # 生成模拟的OHLCV数据
        close = 100 + np.random.randn(150).cumsum()
        high = close + np.abs(np.random.randn(150))
        low = close - np.abs(np.random.randn(150))
        open = close + np.random.randn(150)
        volume = np.random.randint(1000, 10000, 150)  # 添加成交量数据
        
        self.df = pd.DataFrame({
            'open': open,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=dates)  # 使用dates作为索引
        
        print(self.df.shape)
        print(self.df[-30:])
        
        # 确保bar_set已初始化
        get_vocab()
    
    def test_conversion_roundtrip(self):
        """测试K线数据转换为bar token后再转回K线数据的过程"""
        # 1. K线转换为bar token
        df_with_datetime = self.df.reset_index()
        df_with_datetime.rename(columns={
            'index': 'datetime',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'volume': 'volume'
        }, inplace=True)
        
        code = 'IF'
        seq_len = 30
        timeperiod = 100
        atr_mult=0.88
        # 技术指标ATR
        atrs = talib.ATR(self.df.iloc[:, 1], self.df.iloc[:, 2], self.df.iloc[:, 3], timeperiod)
        atr = atrs.iloc[-seq_len]
        pre_close = self.df.iloc[-seq_len-1, 3]
        print(f'pre_close: {pre_close}, atr: {atr}')
        code, x, x_mark = candlestick_to_bar_token(code, df_with_datetime[-seq_len:], pre_close, atr, timeenc=2, atr_mult=atr_mult, scale=10)
        print(f"Shapes - code: {code.shape}, x: {x.shape}, x_mark: {x_mark.shape}")
        
        # 2. bar token转换回K线
        begin_date = pd.Timestamp(df_with_datetime['datetime'].iloc[-seq_len])
        begin_price = self.df.iloc[-seq_len, 3]
        print(f'begin_date: {begin_date}, begin_price: {begin_price}, atr: {atr}')
        reconstructed_df = bar_token_to_candlestick(x, begin_date, begin_price, atr, atr_mult=atr_mult, scale=10)
        print(reconstructed_df)

        # 设置datetime索引
        reconstructed_df.set_index('datetime', inplace=True)
        reconstructed_df.index = pd.to_datetime(reconstructed_df.index)
        
        # 添加成交量列（用0填充）
        reconstructed_df['Volume'] = 0
        
        # 重命名列以匹配mplfinance要求
        reconstructed_df.rename(columns={
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close'
        }, inplace=True)
        
        # 3. 绘制原始和重建的K线图进行比较
        self._plot_comparison(self.df.iloc[-len(reconstructed_df):], reconstructed_df)
        
        # 4. 验证基本属性
        self.assertEqual(len(reconstructed_df), len(x))
        self.assertTrue(all(col in reconstructed_df.columns 
                          for col in ['Open', 'High', 'Low', 'Close', 'Volume']))
    
    def _plot_comparison(self, original_df, reconstructed_df):
        """绘制原始和重建的K线图对比"""
        # 创建子图
        fig = plt.figure(figsize=(15, 10))
        
        # 设置绘图样式
        style = mpf.make_mpf_style(base_mpf_style='charles', 
                                 gridstyle='', 
                                 y_on_right=False)
        
        # 创建两个子图
        ax1 = fig.add_subplot(2, 1, 1)
        ax2 = fig.add_subplot(2, 1, 2)
        
        # 绘制原始K线图
        mpf.plot(original_df, 
                type='candle',
                style=style,
                ax=ax1,
                volume=False,
                returnfig=False)  # 不返回图形对象
        ax1.set_title('Original Candlestick Chart')
        
        # 绘制重建的K线图
        mpf.plot(reconstructed_df, 
                type='candle',
                style=style,
                ax=ax2,
                volume=False,
                returnfig=False)  # 不返回图形对象
        ax2.set_title('Reconstructed Candlestick Chart')
        
        # 调整布局并保存
        plt.tight_layout()
        plt.savefig('candlestick_comparison.png')
        plt.close(fig)

if __name__ == '__main__':
    unittest.main()



