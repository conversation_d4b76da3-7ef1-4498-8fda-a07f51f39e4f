"""
Candlestick LLM Cross Validation Trainer

K线LLM模型交叉验证训练器
"""

import os
import time
import math
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, SubsetRandomSampler
from torch.optim import AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional, Union, Any, Callable
from datetime import datetime
from tqdm import tqdm
import logging
from sklearn.model_selection import KFold, TimeSeriesSplit
import copy

# 导入模型和tokenizer
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset
from pyqlab.models.gpt2.bak.trainer import CandlestickLLMTrainer

# 配置日志
logger = logging.getLogger('CrossValidationTrainer')


class CrossValidationTrainer:
    """K线LLM模型交叉验证训练器"""
    def __init__(self,
                 model_class: Union[type(CandlestickLLM), type(AdvancedCandlestickLLM)],
                 model_params: Dict[str, Any],
                 tokenizer: Union[CandlestickTokenizer, NonlinearCandlestickTokenizer],
                 train_data: List[pd.DataFrame],
                 train_code_ids: List[int],
                 n_splits: int = 5,
                 cv_type: str = 'kfold',  # 'kfold' 或 'timeseries'
                 shuffle: bool = True,
                 random_state: int = 42,
                 seq_len: int = 30,
                 stride: int = 1,
                 batch_size: int = 32,
                 learning_rate: float = 5e-4,
                 weight_decay: float = 0.05,
                 betas: Tuple[float, float] = (0.9, 0.95),
                 lr_scheduler: str = 'one_cycle',
                 warmup_steps: int = 1000,
                 max_epochs: int = 10,
                 grad_clip: float = 2.0,
                 device: str = None,
                 checkpoint_dir: str = './checkpoints',
                 log_interval: int = 10,
                 eval_interval: int = 100,
                 save_interval: int = 1000,
                 early_stopping_patience: int = 10,
                 early_stopping_min_delta: float = 0.0001,
                 label_smoothing: float = 0.1,
                 loss_scale: float = 1.0,
                 save_best_models: bool = True,
                 ensemble_prediction: bool = False):
        """
        初始化K线LLM交叉验证训练器

        Args:
            model_class: K线LLM模型类
            model_params: 模型初始化参数
            tokenizer: K线tokenizer
            train_data: 训练数据列表
            train_code_ids: 训练数据对应的证券代码ID列表
            n_splits: 交叉验证折数
            cv_type: 交叉验证类型，'kfold'或'timeseries'
            shuffle: 是否打乱数据
            random_state: 随机种子
            seq_len: 序列长度
            stride: 滑动窗口步长
            batch_size: 批大小
            learning_rate: 学习率
            weight_decay: 权重衰减
            betas: Adam优化器的beta参数
            lr_scheduler: 学习率调度器类型
            warmup_steps: 预热步数
            max_epochs: 最大训练轮数
            grad_clip: 梯度裁剪阈值
            device: 设备，'cuda'或'cpu'
            checkpoint_dir: 检查点保存目录
            log_interval: 日志记录间隔（批次）
            eval_interval: 评估间隔（批次）
            save_interval: 保存间隔（批次）
            early_stopping_patience: 早停耐心值
            early_stopping_min_delta: 最小改进阈值
            label_smoothing: 标签平滑系数
            loss_scale: 损失缩放因子
            save_best_models: 是否保存每个折的最佳模型
            ensemble_prediction: 是否使用集成预测
        """
        self.model_class = model_class
        self.model_params = model_params
        self.tokenizer = tokenizer
        self.train_data = train_data
        self.train_code_ids = train_code_ids
        self.n_splits = n_splits
        self.cv_type = cv_type
        self.shuffle = shuffle
        self.random_state = random_state
        self.seq_len = seq_len
        self.stride = stride
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.betas = betas
        self.lr_scheduler = lr_scheduler
        self.warmup_steps = warmup_steps
        self.max_epochs = max_epochs
        self.grad_clip = grad_clip
        self.log_interval = log_interval
        self.eval_interval = eval_interval
        self.save_interval = save_interval
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_min_delta = early_stopping_min_delta
        self.label_smoothing = label_smoothing
        self.loss_scale = loss_scale
        self.save_best_models = save_best_models
        self.ensemble_prediction = ensemble_prediction

        # 设置设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        logger.info(f"使用设备: {self.device}")

        # 创建检查点目录
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(checkpoint_dir, exist_ok=True)

        # 创建交叉验证目录
        self.cv_dir = os.path.join(checkpoint_dir, f"cv_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.cv_dir, exist_ok=True)

        # 创建数据集
        self.dataset = CandlestickDataset(
            data=train_data,
            code_ids=train_code_ids,
            tokenizer=tokenizer,
            seq_len=seq_len,
            stride=stride,
            use_time_features=self.model_params.get('use_time_features', True)
        )

        # 初始化结果存储
        self.fold_results = []
        self.best_models = []
        self.fold_val_losses = []
        self.fold_train_losses = []

    def _create_data_splits(self):
        """创建交叉验证数据分割"""
        indices = np.arange(len(self.dataset))

        if self.cv_type == 'kfold':
            # 使用KFold进行交叉验证
            cv = KFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
            splits = list(cv.split(indices))
        elif self.cv_type == 'timeseries':
            # 使用TimeSeriesSplit进行时间序列交叉验证
            cv = TimeSeriesSplit(n_splits=self.n_splits)
            splits = list(cv.split(indices))
        else:
            raise ValueError(f"不支持的交叉验证类型: {self.cv_type}")

        return splits

    def train_fold(self, fold_idx, train_indices, val_indices):
        """训练单个折"""
        logger.info(f"开始训练第 {fold_idx+1}/{self.n_splits} 折")

        # 创建当前折的模型
        model = self.model_class(**self.model_params)

        # 创建数据加载器
        train_sampler = SubsetRandomSampler(train_indices)
        val_sampler = SubsetRandomSampler(val_indices)

        train_loader = DataLoader(
            self.dataset,
            batch_size=self.batch_size,
            sampler=train_sampler,
            num_workers=0,
            pin_memory=True
        )

        val_loader = DataLoader(
            self.dataset,
            batch_size=self.batch_size,
            sampler=val_sampler,
            num_workers=0,
            pin_memory=True
        )

        # 创建训练器
        fold_checkpoint_dir = os.path.join(self.cv_dir, f"fold_{fold_idx+1}")
        os.makedirs(fold_checkpoint_dir, exist_ok=True)

        trainer = CandlestickLLMTrainer(
            model=model,
            tokenizer=self.tokenizer,
            train_data=[],  # 空列表，因为我们使用自定义的DataLoader
            train_code_ids=[],  # 空列表，因为我们使用自定义的DataLoader
            seq_len=self.seq_len,
            batch_size=self.batch_size,
            learning_rate=self.learning_rate,
            weight_decay=self.weight_decay,
            betas=self.betas,
            lr_scheduler=self.lr_scheduler,
            warmup_steps=self.warmup_steps,
            max_epochs=self.max_epochs,
            grad_clip=self.grad_clip,
            device=self.device,
            checkpoint_dir=fold_checkpoint_dir,
            log_interval=self.log_interval,
            eval_interval=self.eval_interval,
            save_interval=self.save_interval,
            early_stopping_patience=self.early_stopping_patience,
            early_stopping_min_delta=self.early_stopping_min_delta,
            label_smoothing=self.label_smoothing,
            loss_scale=self.loss_scale
        )

        # 设置自定义数据加载器
        trainer.train_loader = train_loader
        trainer.val_loader = val_loader

        # 训练模型
        results = trainer.train()

        # 保存结果
        if self.save_best_models:
            # 加载最佳模型
            best_model_path = os.path.join(fold_checkpoint_dir, 'best_model.pt')
            if os.path.exists(best_model_path):
                checkpoint = torch.load(best_model_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"加载第 {fold_idx+1} 折的最佳模型，验证损失: {checkpoint['loss']:.4f}")
            self.best_models.append(copy.deepcopy(model))

        return results, trainer, model

    def train(self):
        """执行交叉验证训练"""
        logger.info(f"开始 {self.n_splits} 折交叉验证训练")

        # 创建数据分割
        splits = self._create_data_splits()

        # 存储所有折的结果
        all_val_losses = []
        all_train_losses = []
        all_results = []

        # 训练每个折
        for fold_idx, (train_indices, val_indices) in enumerate(splits):
            # 训练当前折
            results, trainer, model = self.train_fold(fold_idx, train_indices, val_indices)

            # 存储结果
            all_results.append(results)
            all_val_losses.append(results['best_val_loss'])
            all_train_losses.append(np.mean(results['train_losses'][-100:]))  # 最后100步的平均训练损失

            # 保存折的训练历史图表
            trainer.plot_training_history()

            # 记录当前折的结果
            logger.info(f"第 {fold_idx+1} 折完成，最佳验证损失: {results['best_val_loss']:.4f}")

            # 保存当前折的结果
            self.fold_results.append({
                'fold': fold_idx + 1,
                'best_val_loss': results['best_val_loss'],
                'train_loss': np.mean(results['train_losses'][-100:]),
                'global_step': results['global_step'],
                'early_stopped': results.get('early_stopped', False)
            })

        # 计算平均结果
        mean_val_loss = np.mean(all_val_losses)
        std_val_loss = np.std(all_val_losses)
        mean_train_loss = np.mean(all_train_losses)

        logger.info(f"交叉验证完成，平均验证损失: {mean_val_loss:.4f} ± {std_val_loss:.4f}")

        # 保存交叉验证结果
        cv_results = {
            'fold_results': self.fold_results,
            'mean_val_loss': float(mean_val_loss),
            'std_val_loss': float(std_val_loss),
            'mean_train_loss': float(mean_train_loss),
            'n_splits': self.n_splits,
            'cv_type': self.cv_type
        }

        with open(os.path.join(self.cv_dir, 'cv_results.json'), 'w') as f:
            json.dump(cv_results, f, indent=4)

        # 绘制交叉验证结果
        self.plot_cv_results()

        return cv_results

    def plot_cv_results(self):
        """绘制交叉验证结果"""
        plt.figure(figsize=(12, 8))

        # 绘制每个折的验证损失
        val_losses = [result['best_val_loss'] for result in self.fold_results]
        train_losses = [result['train_loss'] for result in self.fold_results]
        folds = [result['fold'] for result in self.fold_results]

        plt.subplot(2, 1, 1)
        plt.bar(folds, val_losses, alpha=0.7, label='验证损失')
        plt.axhline(y=np.mean(val_losses), color='r', linestyle='--', label=f'平均: {np.mean(val_losses):.4f}')
        plt.xlabel('折')
        plt.ylabel('损失')
        plt.title('各折验证损失')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.subplot(2, 1, 2)
        plt.bar(folds, train_losses, alpha=0.7, label='训练损失')
        plt.axhline(y=np.mean(train_losses), color='r', linestyle='--', label=f'平均: {np.mean(train_losses):.4f}')
        plt.xlabel('折')
        plt.ylabel('损失')
        plt.title('各折训练损失')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.cv_dir, 'cv_results.png'))
        plt.close()

        logger.info(f"交叉验证结果图表已保存到 {os.path.join(self.cv_dir, 'cv_results.png')}")

    def generate_ensemble_prediction(self, input_df, code_id, max_new_tokens=10, temperature=1.0, top_k=None):
        """使用模型集成生成预测"""
        if not self.ensemble_prediction or not self.best_models:
            raise ValueError("没有可用的模型进行集成预测，请先训练模型并设置ensemble_prediction=True")

        all_predictions = []

        # 对每个模型进行预测
        for model_idx, model in enumerate(self.best_models):
            model.eval()
            model.to(self.device)

            # 将K线数据转换为token
            input_tokens = self.tokenizer.tokenize(input_df)

            # 确保输入token的长度是模型期望的长度
            seq_len = self.seq_len
            if len(input_tokens) < seq_len:
                # 如果输入长度不足，填充到seq_len
                input_tokens = input_tokens + [0] * (seq_len - len(input_tokens))
            elif len(input_tokens) > seq_len:
                # 如果输入长度超过seq_len，截断
                input_tokens = input_tokens[-seq_len:]  # 使用最后的seq_len个token

            input_tokens = torch.tensor([input_tokens], dtype=torch.long).to(self.device)

            # 准备代码ID
            code_ids = torch.tensor([code_id], dtype=torch.long).to(self.device)

            # 准备时间特征
            time_features = None
            if hasattr(model, 'use_time_features') and model.use_time_features and 'datetime' in input_df.columns:
                # 提取时间特征
                dt = pd.to_datetime(input_df['datetime'])

                # 确保时间特征的长度与输入序列相同
                if len(dt) > seq_len:
                    dt = dt.iloc[-seq_len:]  # 使用最后的seq_len个时间点

                time_features = np.zeros((1, len(dt), 5))

                # 小时 (0-23) -> (0-1)
                time_features[0, :, 0] = dt.dt.hour / 23.0

                # 星期几 (0-6) -> (0-1)
                time_features[0, :, 1] = dt.dt.dayofweek / 6.0

                # 月份 (1-12) -> (0-1)
                time_features[0, :, 2] = (dt.dt.month - 1) / 11.0

                # 月内日 (1-31) -> (0-1)
                time_features[0, :, 3] = (dt.dt.day - 1) / 30.0

                # 是否为交易日 (0 or 1)
                time_features[0, :, 4] = (dt.dt.dayofweek < 5).astype(float)

                # 如果时间特征长度不足，填充
                if len(dt) < seq_len:
                    padding = np.zeros((1, seq_len - len(dt), 5))
                    time_features = np.concatenate([time_features, padding], axis=1)

                time_features = torch.tensor(time_features, dtype=torch.float).to(self.device)

            # 生成预测
            with torch.no_grad():
                try:
                    generated_tokens = model.generate(
                        input_tokens=input_tokens,
                        code_ids=code_ids,
                        time_features=time_features,
                        max_new_tokens=max_new_tokens,
                        temperature=temperature,
                        top_k=top_k
                    )

                    # 将生成的token转换回K线数据
                    generated_tokens = generated_tokens[0, -max_new_tokens:].cpu().numpy().tolist()

                    # 获取最后一个K线的收盘价和ATR，用于还原K线
                    last_close = input_df['close'].iloc[-1]
                    atr = self.tokenizer._calculate_atr(input_df).iloc[-1]

                    # 将token转换为K线数据
                    if hasattr(self.tokenizer, 'tokens_to_candlesticks'):
                        predicted_df = self.tokenizer.tokens_to_candlesticks(
                            generated_tokens,
                            start_price=last_close,
                            atr=atr
                        )
                    else:
                        # 如果tokenizer没有实现tokens_to_candlesticks方法，返回原始token
                        predicted_df = pd.DataFrame({'token': generated_tokens})

                    all_predictions.append(predicted_df)

                except Exception as e:
                    logger.error(f"模型 {model_idx+1} 生成预测时出现错误: {e}")

        # 如果没有成功的预测，返回None
        if not all_predictions:
            return None

        # 合并所有预测结果
        # 这里简单地取平均值，可以根据需要实现更复杂的集成方法
        ensemble_df = pd.DataFrame()

        # 对于每个时间点，计算所有模型预测的平均值
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if all(col in df.columns for df in all_predictions):
                ensemble_df[col] = np.mean([df[col].values for df in all_predictions], axis=0)

        return ensemble_df


def run_cross_validation(
    model_class,
    model_params,
    tokenizer,
    train_data,
    train_code_ids,
    n_splits=5,
    cv_type='kfold',
    seq_len=30,
    batch_size=32,
    learning_rate=5e-4,
    max_epochs=10,
    checkpoint_dir='./checkpoints',
    save_best_models=True,
    ensemble_prediction=False
):
    """
    运行交叉验证训练

    Args:
        model_class: 模型类
        model_params: 模型参数
        tokenizer: 分词器
        train_data: 训练数据列表
        train_code_ids: 训练数据对应的证券代码ID列表
        n_splits: 交叉验证折数
        cv_type: 交叉验证类型，'kfold'或'timeseries'
        seq_len: 序列长度
        batch_size: 批大小
        learning_rate: 学习率
        max_epochs: 最大训练轮数
        checkpoint_dir: 检查点保存目录
        save_best_models: 是否保存每个折的最佳模型
        ensemble_prediction: 是否使用集成预测

    Returns:
        交叉验证训练器对象
    """
    # 创建交叉验证训练器
    cv_trainer = CrossValidationTrainer(
        model_class=model_class,
        model_params=model_params,
        tokenizer=tokenizer,
        train_data=train_data,
        train_code_ids=train_code_ids,
        n_splits=n_splits,
        cv_type=cv_type,
        seq_len=seq_len,
        batch_size=batch_size,
        learning_rate=learning_rate,
        max_epochs=max_epochs,
        checkpoint_dir=checkpoint_dir,
        save_best_models=save_best_models,
        ensemble_prediction=ensemble_prediction
    )

    # 执行交叉验证训练
    cv_results = cv_trainer.train()

    # 打印结果
    print(f"交叉验证完成，平均验证损失: {cv_results['mean_val_loss']:.4f} ± {cv_results['std_val_loss']:.4f}")
    print(f"各折验证损失:")
    for result in cv_results['fold_results']:
        print(f"  折 {result['fold']}: {result['best_val_loss']:.4f}")

    return cv_trainer
