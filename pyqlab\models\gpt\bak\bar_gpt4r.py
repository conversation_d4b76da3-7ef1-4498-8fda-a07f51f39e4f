"""
## GPT-2 to GPT-4的主要优化和变更包括：
- 使用RMSNorm替代LayerNorm，这是GPT-4中的一个改进。
- 实现了更高效的RotaryEmbedding，并在Attention模块中应用。
- 简化了Attention模块，使用PyTorch的scaled_dot_product_attention函数，
  这在支持的硬件上可以提供更好的性能。
- 调整了MLP结构，使用GELU激活函数。
- 在Block中使用了更现代的残差连接方式。
- 优化了权重初始化方法。
- 移除了MoE（Mixture of Experts）相关的代码，因为标准的GPT-4模型不使用MoE。
  如果你特别需要MoE，可以考虑将其作为一个可选的特性。
- 简化了forward方法，使其更加清晰和高效。
  这些变更应该能够提高模型的性能和训练稳定性。
  请注意，这些修改可能需要相应地调整训练脚本和其他相关代码。
  此外，你可能需要根据具体的任务和数据集来微调超参数。

## BarGpt4r模型使用说明

BarGpt4r是一个基于GPT架构的模型，专门为处理时间序列数据而设计。
该模型结合了GPT-4的多项先进技术，并针对时间序列数据进行了特殊优化。

### 主要特点

1. 支持多种时间编码方法：
   - periodic: 周期性时间编码
   - relative: 相对时间编码
   - continuous: 连续时间编码
   - multiscale: 多尺度时间编码
   - adaptive: 自适应时间编码
   - time_feature: 时间特征编码

2. 支持多种位置编码方法：
   - rope: 旋转位置编码（默认）
   - alibi: 线性偏置位置编码

3. 使用CP分解线性变换，减少参数数量和计算复杂度

### 使用示例

```python
import torch
from pyqlab.models.gpt.bar_gpt4r import BarGpt4r

# 创建模型
model = BarGpt4r(
    block_size=1024,        # 序列块大小
    code_size=100,          # 代码嵌入大小
    vocab_size=10000,       # 词汇表大小
    n_layer=12,             # Transformer层数
    n_head=12,              # 注意力头数
    d_model=768,            # 嵌入维度
    time_encoding='timeF',  # 时间编码方法
    time_embed_type='periodic',  # 时间嵌入类型
    freq='t',               # 频率类型
    pos_embed_type='rope',  # 位置编码类型
    dropout=0.1             # Dropout概率
)

# 准备输入数据
batch_size = 4
seq_len = 512
code = torch.randint(0, 100, (batch_size, seq_len))  # 代码输入
x = torch.randint(0, 10000, (batch_size, seq_len))   # 序列输入
x_mark = torch.randn(batch_size, seq_len, 4)         # 时间标记输入
targets = torch.randint(0, 10000, (batch_size, seq_len))  # 目标输出

# 前向传播
logits, loss = model(code, x, x_mark, targets)
print(f"Loss: {loss.item()}")

# 生成新序列
with torch.no_grad():
    generated = model.generate(
        code=code[:, :10],           # 使用前10个时间步的代码
        x=x[:, :10],                 # 使用前10个时间步的序列
        x_mark=x_mark[:, :10],       # 使用前10个时间步的时间标记
        max_new_tokens=20,           # 生成20个新标记
        temperature=0.8,             # 温度参数
        top_k=50                     # 从top 50个标记中采样
    )
    print(f"Generated sequence shape: {generated.shape}")

# 保存模型
model.save_model("./saved_models", "bar_gpt4r")

# 加载模型
loaded_model = BarGpt4r.load_model("./saved_models", "bar_gpt4r", device="cuda")
```

### 推理模式和ONNX导出示例

```python
import torch
from pyqlab.models.gpt.bar_gpt4r import BarGpt4r

# 1. 创建或加载模型
# 方法1: 创建新模型，启用推理模式
model = BarGpt4r(
    block_size=1024,
    code_size=100,
    vocab_size=10000,
    n_layer=12,
    n_head=12,
    d_model=768,
    time_encoding='timeF',
    time_embed_type='periodic',
    freq='t',
    pos_embed_type='rope',
    dropout=0.1,
    inference_mode=True  # 启用推理模式
)

# 方法2: 从保存的模型加载，启用推理模式
model = BarGpt4r.from_pretrained(
    model_path="./saved_models/bar_gpt4r.pt",
    device="cuda",
    inference_mode=True
)

# 2. 优化模型以进行推理
optimized_model = model.optimize_for_inference(device="cuda", dtype=torch.float16)

# 3. 单步推理
batch_size = 1
seq_len = 10
code = torch.randint(0, 100, (batch_size, seq_len))
x = torch.randint(0, 10000, (batch_size, seq_len))
x_mark = torch.randn(batch_size, seq_len, 4)

# 移动到GPU
code = code.cuda()
x = x.cuda()
x_mark = x_mark.cuda()

# 执行单步推理
next_token = model.inference_step(code, x, x_mark)
print(f"预测的下一个token: {next_token.item()}")

# 4. 导出为ONNX格式
onnx_path = model.export_onnx(
    save_path="./saved_models/bar_gpt4r.onnx",
    batch_size=1,
    seq_len=10,
    code_size=1,
    x_mark_size=4,
    dynamic_axes=True,
    opset_version=13
)

# 5. 使用ONNX Runtime进行推理 (需要安装onnxruntime)
try:
    import onnxruntime as ort
    
    # 创建ONNX Runtime会话
    ort_session = ort.InferenceSession(onnx_path)
    
    # 准备输入
    ort_inputs = {
        'code': code.cpu().numpy(),
        'x': x.cpu().numpy(),
        'x_mark': x_mark.cpu().numpy()
    }
    
    # 运行推理
    ort_outputs = ort_session.run(None, ort_inputs)
    
    # 处理输出
    ort_logits = torch.tensor(ort_outputs[0])
    ort_pred = torch.argmax(ort_logits[:, -1, :], dim=-1)
    
    print(f"ONNX Runtime预测的下一个token: {ort_pred.item()}")
    
except ImportError:
    print("要使用ONNX Runtime进行推理，请安装onnxruntime包")
```

### 时间编码方法选择指南

- **periodic**: 适用于具有明显周期性的时间序列数据
- **relative**: 适用于相对时间关系重要的数据
- **continuous**: 适用于连续时间数据，无明显周期性
- **multiscale**: 适用于具有多尺度时间模式的数据
- **adaptive**: 适用于需要自适应捕捉时间关系的数据
- **time_feature**: 适用于直接使用时间特征的数据

### 位置编码方法选择指南

- **rope**: 旋转位置编码，适用于大多数场景，特别是长序列
- **alibi**: 线性偏置位置编码，适用于需要外推到更长序列的场景
"""

import math
import torch
import torch.nn as nn
from torch.nn import functional as F
from pyqlab.models.layers.Embed import (
    TimeFeatureEmbedding, TemporalEmbedding,
    PeriodicTimeEncoding, RelativeTimeEncoding,
    ContinuousTimeEmbedding, MultiScaleTimeEncoding,
    AdaptiveTimeEncoding
)
from typing import Tuple
import os
import json
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
import torch.onnx

class RMSNorm(nn.Module):
    """
    RMSNorm层
    使用均方根归一化进行层归一化。
    """
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        return self._norm(x) * self.weight

class RotaryEmbedding(nn.Module):
    """
    RoPE（Rotary Position Embedding）
    使用旋转位置嵌入进行位置编码。
    """
    def __init__(self, dim, max_position_embeddings=2048, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(self.max_seq_len_cached, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        参数:
            x: 输入张量，形状为 (batch_size, seq_len, dim)
            
        返回:
            cos_cached: 余弦编码
            sin_cached: 正弦编码
        """
        seq_len = x.size(1)  # 从输入张量获取序列长度
        
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len
            t = torch.arange(self.max_seq_len_cached, device=x.device).type_as(self.inv_freq)
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)
            self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
            self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)
        
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

def rotate_half(x):
    """
    将输入向量的后半部分旋转到前半部分。
    """
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def apply_rotary_emb(x: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor) -> torch.Tensor:
    """
    应用旋转位置编码到输入张量。
    
    参数:
        x: 输入张量，形状为 (..., dim)
        cos: 余弦编码，形状为 (seq_len, dim/2)
        sin: 正弦编码，形状为 (seq_len, dim/2)
        
    返回:
        应用了旋转编码的张量，形状与输入相同
    """
    # 将输入转换为复数形式以便进行旋转
    x_dtype = x.dtype
    x = torch.view_as_complex(x.float().reshape(*x.shape[:-1], -1, 2))
    
    # 扩展 cos 和 sin 的维度以匹配输入
    freqs_cis = torch.view_as_complex(torch.stack([cos, sin], dim=-1))
    freqs_cis = freqs_cis.view(1, x.size(1), 1, x.size(-1))
    
    # 应用旋转
    x_out = torch.view_as_real(x * freqs_cis).flatten(3)
    
    # 恢复原始数据类型
    return x_out.type(x_dtype)

class CPLinear(nn.Module):
    """使用 CP 分解的线性变换"""
    def __init__(self, in_features, n_head, head_dim, rank: int = 1, q_rank: int = 12):
        super(CPLinear, self).__init__()
        self.in_features = in_features
        self.n_head = n_head
        self.head_dim = head_dim
        self.rank = rank
        self.q_rank = q_rank

        # 定义 A 投影的线性变换
        self.W_A_q = nn.Linear(in_features, n_head * q_rank, bias=False)
        self.W_A_k = nn.Linear(in_features, n_head * rank, bias=False)
        self.W_A_v = nn.Linear(in_features, n_head * rank, bias=False)

        # 定义 B 投影参数
        self.W_B_q = nn.Linear(in_features, q_rank * head_dim, bias=False)
        self.W_B_k = nn.Linear(in_features, rank * head_dim, bias=False)
        self.W_B_v = nn.Linear(in_features, rank * head_dim, bias=False)
        self.rotary = RotaryEmbedding(self.head_dim)
        self.reset_parameters()

    def reset_parameters(self):
        # 初始化权重
        W_A_q_tensor = self.W_A_q.weight.view(self.n_head * self.q_rank, self.in_features)
        W_A_k_tensor = self.W_A_k.weight.view(self.n_head * self.rank, self.in_features)
        W_A_v_tensor = self.W_A_v.weight.view(self.n_head * self.rank, self.in_features)
        nn.init.xavier_uniform_(W_A_q_tensor)
        nn.init.xavier_uniform_(W_A_k_tensor)
        nn.init.xavier_uniform_(W_A_v_tensor)

        W_B_q_tensor = self.W_B_q.weight.view(self.q_rank * self.head_dim, self.in_features)
        W_B_k_tensor = self.W_B_k.weight.view(self.rank * self.head_dim, self.in_features)
        W_B_v_tensor = self.W_B_v.weight.view(self.rank * self.head_dim, self.in_features)
        nn.init.xavier_uniform_(W_B_q_tensor)
        nn.init.xavier_uniform_(W_B_k_tensor)
        nn.init.xavier_uniform_(W_B_v_tensor)

    def forward(self, x):
        batch_size, seq_len, _ = x.size()

        # 计算 Q、K、V 的中间变量 A 和 B
        # 使用单次矩阵乘法计算所有投影，减少计算开销
        A_q = self.W_A_q(x).reshape(batch_size, seq_len, self.n_head, self.q_rank)
        A_k = self.W_A_k(x).reshape(batch_size, seq_len, self.n_head, self.rank)
        A_v = self.W_A_v(x).reshape(batch_size, seq_len, self.n_head, self.rank)

        B_q = self.W_B_q(x).reshape(batch_size, seq_len, self.q_rank, self.head_dim)
        B_k = self.W_B_k(x).reshape(batch_size, seq_len, self.rank, self.head_dim)
        B_v = self.W_B_v(x).reshape(batch_size, seq_len, self.rank, self.head_dim)

        # 应用旋转位置编码
        cos, sin = self.rotary(x)
        B_q, B_k = apply_rotary_emb(B_q, cos, sin), apply_rotary_emb(B_k, cos, sin)

        # 使用einsum进行批量矩阵乘法，避免reshape和bmm操作
        q = torch.einsum('bshr,bsrd->bshd', A_q, B_q) / math.sqrt(self.q_rank)
        k = torch.einsum('bshr,bsrd->bshd', A_k, B_k) / math.sqrt(self.rank)
        v = torch.einsum('bshr,bsrd->bshd', A_v, B_v) / math.sqrt(self.rank)

        return q, k, v

class Attention(nn.Module):
    """使用 TPA 的多头注意力机制"""
    def __init__(self, dim: int, n_heads: int, dropout: float = 0.1):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        
        # 使用 CPLinear 替代原来的线性投影
        self.c_qkv = CPLinear(
            in_features=dim,
            n_head=n_heads,
            head_dim=self.head_dim,
            rank=2,  # CP 分解的秩
            q_rank=6  # 查询向量的秩
        )
        
        # 输出投影
        self.proj = nn.Linear(dim, dim, bias=False)
        self.dropout = nn.Dropout(dropout)
        
        # 使用 RMSNorm 进行归一化
        self.using_groupnorm = True
        if self.using_groupnorm:
            self.subln = RMSNorm(self.head_dim)

    def forward(self, x: torch.Tensor):
        B, T, C = x.size()

        # 使用 CPLinear 计算 Q、K、V
        q, k, v = self.c_qkv(x)

        # 使用 scaled_dot_product_attention
        y = F.scaled_dot_product_attention(
            q.transpose(1, 2),  # (B, n_head, T, head_dim)
            k.transpose(1, 2),
            v.transpose(1, 2),
            dropout_p=self.dropout.p if self.training else 0,
            is_causal=True
        )

        if self.using_groupnorm:
            # 对每个注意力头的输出应用 RMSNorm
            y = self.subln(y)

        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.dropout(self.proj(y))

        return y

class MLP(nn.Module):
    """使用 SwiGLU 激活函数的 MLP"""
    def __init__(self, dim: int, hidden_dim: int):
        super().__init__()
        self.w1 = nn.Linear(dim, hidden_dim, bias=False)
        self.w2 = nn.Linear(hidden_dim, dim, bias=False)
        self.w3 = nn.Linear(dim, hidden_dim, bias=False)

    def forward(self, x):
        # SwiGLU 激活函数
        return self.w2(F.silu(self.w1(x)) * self.w3(x))

class AlibiPositionalEmbedding(nn.Module):
    """
    ALiBi（Attention with Linear Biases）位置嵌入
    使用线性偏置进行位置嵌入，适用于处理变长序列和外推。
    """
    def __init__(self, num_heads, max_seq_len=2048):
        super().__init__()
        self.num_heads = num_heads
        self.max_seq_len = max_seq_len
        slopes = torch.Tensor(self._get_slopes(num_heads))
        self.register_buffer("slopes", slopes)
        self.register_buffer("bias", self._get_bias(max_seq_len))
        
    def _get_slopes(self, num_heads):
        def get_slopes_power_of_2(n):
            start = (2**(-2**-(math.log2(n)-3)))
            ratio = start
            return [start*ratio**i for i in range(n)]

        if math.log2(num_heads).is_integer():
            return get_slopes_power_of_2(num_heads)
        else:
            closest_power_of_2 = 2**math.floor(math.log2(num_heads))
            return get_slopes_power_of_2(closest_power_of_2) + self._get_slopes(2*closest_power_of_2)[0::2][:num_heads-closest_power_of_2]

    def _get_bias(self, max_seq_len):
        bias = torch.arange(max_seq_len).unsqueeze(0).unsqueeze(0)
        return bias * self.slopes.unsqueeze(1).unsqueeze(1)

    def forward(self, x, seq_len):
        return self.bias[:, :, :seq_len, :seq_len]

class Block(nn.Module):
    """
    Transformer Block
    包含RMSNorm、注意力机制和MLP。
    """
    def __init__(self, dim, n_heads, dropout, pos_embed_type='rope'):
        super().__init__()
        self.ln_1 = RMSNorm(dim)
        self.attn = Attention(dim, n_heads, dropout)
        self.ln_2 = RMSNorm(dim)
        self.mlp = MLP(dim, 4 * dim)
        self.pos_embed_type = pos_embed_type

    def forward(self, x, rotary_emb=None):
        x = x + self.attn(self.ln_1(x))
        x = x + self.mlp(self.ln_2(x))
        return x

class BarGpt4r(nn.Module):
    """
    BarGpt4模型
    支持多种时间编码嵌入方法和特征向量合并方法。
    """
    def __init__(self, block_size, code_size, vocab_size, n_layer, n_head, d_model, time_encoding,
                 time_embed_type='periodic', freq='t', pos_embed_type='rope', dropout=0.1, inference_mode=False):
        """
        初始化BarGpt4模型。

        参数:
        - block_size: 序列块大小
        - code_size: 代码嵌入大小
        - vocab_size: 词汇表大小
        - n_layer: Transformer层数
        - n_head: 注意力头数
        - d_model: 嵌入维度
        - time_embed_type: 时间编码嵌入方法
        - dropout: Dropout概率
        - pos_embed_type: 位置嵌入方法
        - inference_mode: 是否为推理模式
        """
        super().__init__()
        self.block_size = block_size
        self.vocab_size = vocab_size
        self.n_layer = n_layer
        self.n_head = n_head
        self.d_model = d_model
        self.time_encoding = time_encoding
        self.pos_embed_type = pos_embed_type
        self.time_embed_type = time_embed_type
        self.freq = freq
        self.inference_mode = inference_mode

        self.transformer = nn.ModuleDict(dict(
            bar_eb = nn.Embedding(vocab_size, d_model),
            code_eb = nn.Embedding(code_size, d_model),
            tf_eb = TimeFeatureEmbedding(d_model=d_model, freq=freq) if time_encoding =='timeF' else TemporalEmbedding(d_model=d_model, embed_type=time_encoding, freq=freq),
            time_eb = self._get_time_embedding(time_embed_type, d_model),
            drop = nn.Dropout(dropout if not inference_mode else 0.0),  # 推理模式下禁用dropout
            h = nn.ModuleList([Block(d_model, n_head, dropout if not inference_mode else 0.0, pos_embed_type) for _ in range(n_layer)]),
            ln_f = RMSNorm(d_model),
        ))
        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)
        
        if pos_embed_type == 'rope':
            self.rotary_emb = RotaryEmbedding(d_model // n_head)
        elif pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_head, block_size)

        # Initialize weights
        self.apply(self._init_weights)
        for pn, p in self.named_parameters():
            if pn.endswith('proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        # Weight tying
        self.transformer.bar_eb.weight = self.lm_head.weight

        if not inference_mode:
            print("="*30)
            print(f"Number of parameters: {sum(p.numel() for p in self.parameters())/1e6:.2f}M")
            print("="*30)

    def _init_weights(self, module):
        """
        初始化权重
        """
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()

    def _get_time_embedding(self, time_embed_type, d_model):
        """
        获取时间编码嵌入方法。

        参数:
        - time_embed_type: 时间编码嵌入方法
        - d_model: 嵌入维度

        返回:
        - 时间编码嵌入层
        """
        if time_embed_type == 'periodic':
            return PeriodicTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'relative':
            return RelativeTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'time_feature':
            return TimeFeatureEmbedding(d_model=d_model, freq=self.freq)
        elif time_embed_type == 'continuous':
            return ContinuousTimeEmbedding(d_model=d_model)
        elif time_embed_type == 'multiscale':
            return MultiScaleTimeEncoding(d_model=d_model)
        elif time_embed_type == 'adaptive':
            return AdaptiveTimeEncoding(d_model=d_model, max_len=self.block_size)
        else:
            raise ValueError(f"Unknown time embedding type: {time_embed_type}")

    def forward(self, code, x, x_mark, targets=None):
        """
        前向传播。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - targets: 目标输出（可选）

        返回:
        - logits: 模型输出
        - loss: 损失（如果提供了目标输出）
        """
        b, t = x.size()
        assert t <= self.block_size, f"Cannot forward sequence of length {t}, block size is only {self.block_size}"
        
        bar_emb = self.transformer.bar_eb(x)
        code_emb = self.transformer.code_eb(code)
        
        # 简化时间编码处理逻辑
        # 首先处理x_mark
        processed_x_mark = x_mark if self.freq == 't' else x_mark[:, :, -3:]
        time_emb = self.transformer.tf_eb(processed_x_mark)
        
        # 如果不是time_feature类型，则应用额外的时间编码
        if self.time_embed_type != 'time_feature':
            time_emb = self.transformer.time_eb(time_emb)

        x = self.transformer.drop(bar_emb + code_emb + time_emb)
        
        if self.pos_embed_type == 'rope':
            rotary_emb = self.rotary_emb(x)
        else:
            rotary_emb = None
        
        for block in self.transformer.h:
            x = block(x, rotary_emb)
        x = self.transformer.ln_f(x)

        if targets is not None and not self.inference_mode:
            logits = self.lm_head(x)
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
        else:
            # 推理模式下只计算最后一个时间步的logits，或者不计算loss
            logits = self.lm_head(x[:, [-1], :] if targets is None else x)
            loss = None

        return logits, loss

    def crop_block_size(self, block_size):
        """
        裁剪序列块大小。

        参数:
        - block_size: 新的序列块大小
        """
        # model surgery to decrease the block size if necessary
        # e.g. we may load the GPT2 pretrained model checkpoint (block size 1024)
        # but want to use a smaller block size for some smaller, simpler model
        assert block_size <= self.block_size
        self.block_size = block_size
        
        # 更新位置编码相关的参数
        if self.pos_embed_type == 'rope':
            self.rotary_emb = RotaryEmbedding(self.d_model // self.n_head, max_position_embeddings=block_size)
        elif self.pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(self.n_head, block_size)
        
        # 更新时间编码相关的参数
        if hasattr(self.transformer, 'time_eb') and hasattr(self.transformer.time_eb, 'max_len'):
            self.transformer.time_eb.max_len = block_size

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        """
        配置优化器。

        参数:
        - weight_decay: 权重衰减系数
        - learning_rate: 学习率
        - betas: Adam优化器的beta参数
        - device_type: 设备类型

        返回:
        - 优化器
        """
        import inspect
        # start with all of the candidate parameters
        param_dict = {pn: p for pn, p in self.named_parameters()}
        # filter out those that do not require grad
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        # create optim groups. Any parameters that is 2D will be weight decayed, otherwise no.
        # i.e. all weight tensors in matmuls + embeddings decay, all biases and layernorms don't.
        decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
        nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]
        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters")
        print(f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters")
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"using fused AdamW: {use_fused}")

        return optimizer

    def estimate_mfu(self, fwdbwd_per_iter, dt):
        """
        估计模型FLOPs利用率（MFU），单位为A100 bfloat16峰值FLOPS。

        参数:
        - fwdbwd_per_iter: 每次迭代的前向传播和反向传播次数
        - dt: 迭代时间

        返回:
        - MFU
        """
        # 计算模型参数总数
        N = sum(p.numel() for p in self.parameters())
        
        # 使用模型的实际属性而不是config
        L, H, Q, T = self.n_layer, self.n_head, self.d_model//self.n_head, self.block_size
        
        # 估计每个token的浮点运算次数
        flops_per_token = 6*N + 12*L*H*Q*T
        flops_per_fwdbwd = flops_per_token * T
        flops_per_iter = flops_per_fwdbwd * fwdbwd_per_iter
        
        # 计算实际达到的FLOPS与A100 GPU峰值FLOPS的比率
        flops_achieved = flops_per_iter * (1.0/dt) # per second
        flops_promised = 312e12 # A100 GPU bfloat16 peak flops is 312 TFLOPS
        mfu = flops_achieved / flops_promised
        return mfu

    @torch.no_grad()
    def generate(self, code, x, x_mark, max_new_tokens, temperature=1.0, top_k=None):
        """
        生成新的序列。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - max_new_tokens: 最大生成的新标记数
        - temperature: 生成时的温度参数
        - top_k: 从top k个标记中进行采样

        返回:
        - 生成的序列的索引
        """
        for _ in range(max_new_tokens):
            # 如果序列太长，则裁剪到block_size
            if x.size(1) > self.block_size:
                x_cond = x[:, -self.block_size:]
                code_cond = code[:, -self.block_size:] if code.size(1) > self.block_size else code
                x_mark_cond = x_mark[:, -self.block_size:] if x_mark.size(1) > self.block_size else x_mark
            else:
                x_cond = x
                code_cond = code
                x_mark_cond = x_mark
            
            # 前向传播获取logits
            logits, _ = self(code_cond, x_cond, x_mark_cond)
            
            # 获取最后一步的logits并应用温度
            logits = logits[:, -1, :] / temperature
            
            # 可选地只保留top k个选项
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')
            
            # 应用softmax转换为概率
            probs = F.softmax(logits, dim=-1)
            
            # 从分布中采样
            idx_next = torch.multinomial(probs, num_samples=1)
            
            # 将采样的索引添加到序列中
            x = torch.cat((x, idx_next), dim=1)
            
            # 更新code和x_mark
            # 这里假设code和x_mark的更新方式与x相同
            # 实际应用中可能需要根据具体情况调整
            code = torch.cat((code, code[:, [-1]]), dim=1)
            x_mark = torch.cat((x_mark, x_mark[:, [-1]]), dim=1)

        return x

    def save_model(self, save_dir, filename="model"):
        """
        保存模型参数和配置。

        参数:
        - save_dir: 保存目录
        - filename: 文件名前缀
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存模型参数
        model_path = os.path.join(save_dir, f"{filename}.pt")
        torch.save(self.state_dict(), model_path)
        
        # 保存模型配置
        config = {
            "block_size": self.block_size,
            "vocab_size": self.vocab_size,
            "n_layer": self.n_layer,
            "n_head": self.n_head,
            "d_model": self.d_model,
            "time_encoding": self.time_encoding,
            "time_embed_type": self.time_embed_type,
            "freq": self.freq,
            "pos_embed_type": self.pos_embed_type
        }
        config_path = os.path.join(save_dir, f"{filename}_config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"模型已保存到 {save_dir}")
    
    @classmethod
    def load_model(cls, load_dir, filename="model", device="cpu"):
        """
        加载模型参数和配置。

        参数:
        - load_dir: 加载目录
        - filename: 文件名前缀
        - device: 设备

        返回:
        - 加载的模型
        """
        # 加载模型配置
        config_path = os.path.join(load_dir, f"{filename}_config.json")
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 创建模型实例
        model = cls(**config)
        
        # 加载模型参数
        model_path = os.path.join(load_dir, f"{filename}.pt")
        model.load_state_dict(torch.load(model_path, map_location=device))
        
        model.to(device)
        print(f"模型已从 {load_dir} 加载")
        return model

    @torch.no_grad()
    def evaluate(self, code, x, x_mark, targets, metrics=None):
        """
        评估模型性能。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - targets: 目标输出
        - metrics: 评估指标列表，默认为['loss', 'mse', 'mae']

        返回:
        - 评估结果字典
        """
        if metrics is None:
            metrics = ['loss', 'mse', 'mae']
        
        results = {}
        
        # 前向传播
        logits, loss = self(code, x, x_mark, targets)
        
        # 计算预测值
        preds = torch.argmax(logits, dim=-1)
        
        # 计算各种指标
        if 'loss' in metrics:
            results['loss'] = loss.item()
        
        if 'mse' in metrics:
            mse = mean_squared_error(targets.cpu().numpy().flatten(), preds.cpu().numpy().flatten())
            results['mse'] = mse
        
        if 'mae' in metrics:
            mae = mean_absolute_error(targets.cpu().numpy().flatten(), preds.cpu().numpy().flatten())
            results['mae'] = mae
        
        return results
    
    def visualize_attention(self, code, x, x_mark, layer_idx=0, head_idx=0, save_path=None):
        """
        可视化注意力权重。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - layer_idx: 要可视化的层索引
        - head_idx: 要可视化的注意力头索引
        - save_path: 保存路径，如果为None则显示图像
        """
        self.eval()
        with torch.no_grad():
            # 获取输入嵌入
            bar_emb = self.transformer.bar_eb(x)
            code_emb = self.transformer.code_eb(code)
            
            # 处理时间编码
            processed_x_mark = x_mark if self.freq == 't' else x_mark[:, :, -3:]
            time_emb = self.transformer.tf_eb(processed_x_mark)
            if self.time_embed_type != 'time_feature':
                time_emb = self.transformer.time_eb(time_emb)
            
            # 合并嵌入
            combined_emb = self.transformer.drop(bar_emb + code_emb + time_emb)
            
            # 获取位置编码
            if self.pos_embed_type == 'rope':
                rotary_emb = self.rotary_emb(combined_emb)
            else:
                rotary_emb = None
            
            # 应用层归一化
            norm_emb = self.transformer.h[layer_idx].ln_1(combined_emb)
            
            # 获取注意力权重
            q, k, v = self.transformer.h[layer_idx].attn.c_qkv(norm_emb)
            
            # 计算注意力分数
            q = q[:, :, head_idx:head_idx+1]  # 选择特定的注意力头
            k = k[:, :, head_idx:head_idx+1]
            
            # 转置为注意力计算所需的形状
            q = q.transpose(1, 2)  # [batch, 1, seq_len, head_dim]
            k = k.transpose(1, 2)  # [batch, 1, seq_len, head_dim]
            
            # 计算注意力分数
            attn_weights = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.d_model // self.n_head)
            
            # 应用因果掩码
            seq_len = attn_weights.size(-1)
            causal_mask = torch.triu(torch.ones(seq_len, seq_len, device=attn_weights.device), diagonal=1).bool()
            attn_weights.masked_fill_(causal_mask, float('-inf'))
            
            # 应用softmax得到最终的注意力权重
            attn_weights = F.softmax(attn_weights, dim=-1)
            
            # 可视化
            plt.figure(figsize=(10, 8))
            plt.imshow(attn_weights[0, 0].cpu().numpy(), cmap='viridis')
            plt.colorbar()
            plt.title(f'Attention Weights (Layer {layer_idx}, Head {head_idx})')
            plt.xlabel('Key Position')
            plt.ylabel('Query Position')
            
            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()
    
    def visualize_embeddings(self, code, x, x_mark, method='pca', save_path=None):
        """
        可视化嵌入向量。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - method: 降维方法，'pca'或'tsne'
        - save_path: 保存路径，如果为None则显示图像
        """
        try:
            from sklearn.decomposition import PCA
            from sklearn.manifold import TSNE
        except ImportError:
            print("请安装scikit-learn库以使用此功能")
            return
        
        self.eval()
        with torch.no_grad():
            # 获取输入嵌入
            bar_emb = self.transformer.bar_eb(x)
            code_emb = self.transformer.code_eb(code)
            
            # 处理时间编码
            processed_x_mark = x_mark if self.freq == 't' else x_mark[:, :, -3:]
            time_emb = self.transformer.tf_eb(processed_x_mark)
            if self.time_embed_type != 'time_feature':
                time_emb = self.transformer.time_eb(time_emb)
            
            # 获取最终输出嵌入
            combined_emb = bar_emb + code_emb + time_emb
            
            # 将嵌入转换为numpy数组
            embeddings = combined_emb[0].cpu().numpy()  # 使用第一个批次样本
            
            # 降维
            if method == 'pca':
                reducer = PCA(n_components=2)
            else:  # tsne
                reducer = TSNE(n_components=2)
            
            reduced_embeddings = reducer.fit_transform(embeddings)
            
            # 可视化
            plt.figure(figsize=(10, 8))
            plt.scatter(reduced_embeddings[:, 0], reduced_embeddings[:, 1], c=np.arange(len(reduced_embeddings)), cmap='viridis')
            plt.colorbar(label='Position in Sequence')
            plt.title(f'Embedding Visualization using {method.upper()}')
            
            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()

    def set_inference_mode(self, mode=True):
        """
        设置模型为推理模式。

        参数:
        - mode: 是否启用推理模式
        """
        self.inference_mode = mode
        # 设置dropout为0
        self.transformer.drop.p = 0.0 if mode else self.transformer.drop.p
        for block in self.transformer.h:
            block.attn.dropout.p = 0.0 if mode else block.attn.dropout.p
        
        # 如果启用推理模式，则将模型设置为eval模式
        if mode:
            self.eval()
        
        return self
    
    def export_onnx(self, save_path, batch_size=1, seq_len=1, code_size=1, x_mark_size=4, dynamic_axes=True, opset_version=13):
        """
        将模型导出为ONNX格式。

        参数:
        - save_path: 保存路径
        - batch_size: 批次大小
        - seq_len: 序列长度
        - code_size: 代码大小
        - x_mark_size: 时间标记大小
        - dynamic_axes: 是否使用动态轴
        - opset_version: ONNX操作集版本
        """
        # 确保模型处于推理模式
        self.set_inference_mode(True)
        
        # 创建示例输入
        code = torch.zeros(batch_size, seq_len, dtype=torch.long)
        x = torch.zeros(batch_size, seq_len, dtype=torch.long)
        x_mark = torch.zeros(batch_size, seq_len, x_mark_size, dtype=torch.float)
        
        # 定义动态轴
        if dynamic_axes:
            dynamic_axes = {
                'code': {0: 'batch_size', 1: 'seq_len'},
                'x': {0: 'batch_size', 1: 'seq_len'},
                'x_mark': {0: 'batch_size', 1: 'seq_len'},
                'output': {0: 'batch_size', 1: 'seq_len'}
            }
        else:
            dynamic_axes = None
        
        # 导出模型
        torch.onnx.export(
            self,
            (code, x, x_mark),
            save_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['code', 'x', 'x_mark'],
            output_names=['output'],
            dynamic_axes=dynamic_axes,
            verbose=False
        )
        
        print(f"模型已导出到 {save_path}")
        
        return save_path
    
    def optimize_for_inference(self, device='cuda', dtype=torch.float16):
        """
        优化模型以进行推理。

        参数:
        - device: 推理设备
        - dtype: 数据类型，如torch.float16用于半精度推理
        
        返回:
        - 优化后的模型
        """
        # 设置推理模式
        self.set_inference_mode(True)
        
        # 移动到指定设备
        self.to(device)
        
        # 转换为指定数据类型
        if dtype != torch.float32:
            self.to(dtype)
        
        # 使用torch.jit.script优化模型（可选）
        try:
            optimized_model = torch.jit.script(self)
            print("模型已使用TorchScript优化")
            return optimized_model
        except Exception as e:
            print(f"TorchScript优化失败: {e}")
            print("返回原始模型")
            return self
    
    def inference_step(self, code, x, x_mark):
        """
        单步推理。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        
        返回:
        - 预测的下一个token
        """
        # 确保模型处于推理模式
        if not self.inference_mode:
            self.set_inference_mode(True)
        
        with torch.no_grad():
            # 前向传播
            logits, _ = self(code, x, x_mark)
            
            # 获取预测
            probs = F.softmax(logits[:, -1, :], dim=-1)
            pred = torch.argmax(probs, dim=-1)
            
            return pred
    
    @classmethod
    def from_pretrained(cls, model_path, device='cpu', inference_mode=True):
        """
        从预训练模型加载。

        参数:
        - model_path: 模型路径
        - device: 设备
        - inference_mode: 是否为推理模式
        
        返回:
        - 加载的模型
        """
        # 检查路径是目录还是文件
        if os.path.isdir(model_path):
            # 尝试加载config.json和model.pt
            config_path = os.path.join(model_path, "model_config.json")
            if not os.path.exists(config_path):
                config_path = os.path.join(model_path, "config.json")
            
            model_file = os.path.join(model_path, "model.pt")
            if not os.path.exists(model_file):
                model_files = [f for f in os.listdir(model_path) if f.endswith('.pt') or f.endswith('.bin')]
                if model_files:
                    model_file = os.path.join(model_path, model_files[0])
                else:
                    raise FileNotFoundError(f"在 {model_path} 中找不到模型文件")
        else:
            # 假设model_path是模型文件
            model_file = model_path
            config_path = os.path.splitext(model_path)[0] + "_config.json"
            if not os.path.exists(config_path):
                config_path = os.path.splitext(model_path)[0] + ".json"
        
        # 加载配置
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # 添加inference_mode参数
            config['inference_mode'] = inference_mode
            
            # 创建模型实例
            model = cls(**config)
            
            # 加载模型参数
            model.load_state_dict(torch.load(model_file, map_location=device))
        else:
            # 如果找不到配置文件，尝试直接加载模型
            model = torch.load(model_file, map_location=device)
            if hasattr(model, 'set_inference_mode'):
                model.set_inference_mode(inference_mode)
        
        model.to(device)
        if inference_mode:
            model.eval()
        
        print(f"模型已从 {model_path} 加载")
        return model

