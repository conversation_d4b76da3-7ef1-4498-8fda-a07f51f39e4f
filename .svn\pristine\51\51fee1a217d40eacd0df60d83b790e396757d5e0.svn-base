{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pylab as plt\n", "import datetime"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["data_raw = pd.read_csv('IVE_tickbidask.txt', header=None, names=['Date','Time','Price','Bid','Ask','Size'])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["data = data_raw.iloc[:100000]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/ipykernel_launcher.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: http://pandas.pydata.org/pandas-docs/stable/indexing.html#indexing-view-versus-copy\n", "  \"\"\"Entry point for launching an IPython kernel.\n"]}], "source": ["data['DateTime'] = pd.to_datetime(data['Date'] + ' ' + data['Time'])\n", "data = data.drop(columns = ['Date', 'Time'])\n", "data = data.set_index('DateTime')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["from scipy import stats\n", "from bars import *"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     close   high    low   open  volume\n", "DateTime                                               \n", "2009-09-28 09:47:22  51.06  51.07  50.71  50.79   30044\n", "2009-09-28 09:54:38  51.13  51.15  51.06  51.06   28975\n", "2009-09-28 10:00:15  51.21  51.21  51.08  51.13   32841\n", "2009-09-28 10:05:50  51.21  51.28  51.20  51.21   33764\n", "2009-09-28 10:15:13  51.25  51.29  51.19  51.22   37104\n"]}], "source": ["bars = TickBarSeries(data)\n", "tick_bars = bars.process_ticks(frequency = 100)\n", "print tick_bars.head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["from statsmodels.tsa.stattools import adfuller"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [], "source": ["def getWeights(d,size):\n", "    w=[1.]\n", "    for k in range(1,size):\n", "        w_=-w[-1]/k*(d-k+1)\n", "        w.append(w_)\n", "    w=np.array(w[::-1]).reshape(-1,1)\n", "    return w\n", "\n", "def fracDiff(series,d,thres=.001):\n", "\n", "    #1) Compute weights for the longest series\n", "    w=getWeights(d,series.shape[0])\n", "    #2) Determine initial calcs to be skipped based on weight-loss threshold\n", "    w_=np.cumsum(abs(w))\n", "    w_/=w_[-1]\n", "    skip=w_[w_>thres].shape[0]\n", "    #3) Apply weights to values\n", "    df={}\n", "    for name in series.columns:\n", "        seriesF,df_=series[[name]].fillna(method='ffill').dropna(),pd.Series()\n", "        for iloc in range(skip,seriesF.shape[0]):\n", "            loc=seriesF.index[iloc]\n", "            if not np.isfinite(series.loc[loc,name]):continue # exclude NAs\n", "            df_[loc]=np.dot(w[-(iloc+1):,:].T,seriesF.loc[:loc])[0,0]\n", "        df[name]=df_.copy(deep=True)\n", "    df=pd.concat(df,axis=1)\n", "    return df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Differentiation for tick bars"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-13-90c9a3f99f6b>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0md\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m0.0\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m2.1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m0.1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0mts1\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlog\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtick_bars\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m     \u001b[0mts2\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mfracDiff\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mts1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0md\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m     \u001b[0mcorr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcorrcoef\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mts1\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mts2\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mindex\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m'close'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mts2\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'close'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<ipython-input-12-ddab42a7d121>\u001b[0m in \u001b[0;36mfracDiff\u001b[0;34m(series, d, thres)\u001b[0m\n\u001b[1;32m     22\u001b[0m             \u001b[0mloc\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mseriesF\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mindex\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0miloc\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     23\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0misfinite\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mseries\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;32mcontinue\u001b[0m \u001b[0;31m# exclude NAs\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 24\u001b[0;31m             \u001b[0mdf_\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mw\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0miloc\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mT\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mseriesF\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     25\u001b[0m         \u001b[0mdf\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdf_\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcopy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdeep\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     26\u001b[0m     \u001b[0mdf\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mconcat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdf\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0maxis\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/series.pyc\u001b[0m in \u001b[0;36m__setitem__\u001b[0;34m(self, key, value)\u001b[0m\n\u001b[1;32m    937\u001b[0m         \u001b[0;31m# do the setitem\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    938\u001b[0m         \u001b[0mcacher_needs_updating\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_check_is_chained_assignment_possible\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 939\u001b[0;31m         \u001b[0msetitem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mvalue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    940\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mcacher_needs_updating\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    941\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_update_cacher\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/series.pyc\u001b[0m in \u001b[0;36msetitem\u001b[0;34m(key, value)\u001b[0m\n\u001b[1;32m    913\u001b[0m                             \u001b[0;32mpass\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    914\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 915\u001b[0;31m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mvalue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    916\u001b[0m                 \u001b[0;32mreturn\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    917\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexing.pyc\u001b[0m in \u001b[0;36m__setitem__\u001b[0;34m(self, key, value)\u001b[0m\n\u001b[1;32m    187\u001b[0m             \u001b[0mkey\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcom\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_apply_if_callable\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mobj\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    188\u001b[0m         \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_setitem_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 189\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_setitem_with_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mvalue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    190\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    191\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_validate_key\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexing.pyc\u001b[0m in \u001b[0;36m_setitem_with_indexer\u001b[0;34m(self, indexer, value)\u001b[0m\n\u001b[1;32m    403\u001b[0m                     \u001b[0;31m# GH12246\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    404\u001b[0m                     \u001b[0;32mif\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mis_unique\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 405\u001b[0;31m                         \u001b[0mnew_indexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnew_index\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    406\u001b[0m                         \u001b[0;32mif\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mnew_indexer\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0many\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    407\u001b[0m                             return self._setitem_with_indexer(new_indexer,\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexes/base.pyc\u001b[0m in \u001b[0;36mget_indexer\u001b[0;34m(self, target, method, limit, tolerance)\u001b[0m\n\u001b[1;32m   3220\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mget_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtarget\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlimit\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtolerance\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3221\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmissing\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mclean_reindex_fill_method\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmethod\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 3222\u001b[0;31m         \u001b[0mtarget\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0m_ensure_index\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtarget\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   3223\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mtolerance\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3224\u001b[0m             \u001b[0mtolerance\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_convert_tolerance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtolerance\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtarget\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexes/base.pyc\u001b[0m in \u001b[0;36m_ensure_index\u001b[0;34m(index_like, copy)\u001b[0m\n\u001b[1;32m   4972\u001b[0m             \u001b[0mindex_like\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcopy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindex_like\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4973\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 4974\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0mIndex\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindex_like\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   4975\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4976\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexes/base.pyc\u001b[0m in \u001b[0;36m__new__\u001b[0;34m(cls, data, dtype, copy, name, fastpath, tupleize_cols, **kwargs)\u001b[0m\n\u001b[1;32m    415\u001b[0m                             \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    416\u001b[0m                                 return DatetimeIndex(subarr, copy=copy,\n\u001b[0;32m--> 417\u001b[0;31m                                                      name=name, **kwargs)\n\u001b[0m\u001b[1;32m    418\u001b[0m                             \u001b[0;32mexcept\u001b[0m \u001b[0mlibts\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mOutOfBoundsDatetime\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    419\u001b[0m                                 \u001b[0;32mpass\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexes/datetimes.pyc\u001b[0m in \u001b[0;36m__new__\u001b[0;34m(cls, data, freq, start, end, periods, tz, normalize, closed, ambiguous, dayfirst, yearfirst, dtype, copy, name, verify_integrity)\u001b[0m\n\u001b[1;32m    446\u001b[0m                 \u001b[0msubarr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msubarr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mview\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_NS_DTYPE\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    447\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 448\u001b[0;31m         \u001b[0msubarr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_simple_new\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msubarr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfreq\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfreq\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtz\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtz\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    449\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mdtype\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    450\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mis_dtype_equal\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msubarr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/indexes/datetimes.pyc\u001b[0m in \u001b[0;36m_simple_new\u001b[0;34m(cls, values, name, freq, tz, dtype, **kwargs)\u001b[0m\n\u001b[1;32m    641\u001b[0m             \u001b[0mvalues\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcopy\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mFalse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    642\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 643\u001b[0;31m         \u001b[0;32mif\u001b[0m \u001b[0mis_object_dtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    644\u001b[0m             return cls(values, name=name, freq=freq, tz=tz,\n\u001b[1;32m    645\u001b[0m                        dtype=dtype, **kwargs).values\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/dtypes/common.pyc\u001b[0m in \u001b[0;36mis_object_dtype\u001b[0;34m(arr_or_dtype)\u001b[0m\n\u001b[1;32m    116\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0marr_or_dtype\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    117\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mFalse\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 118\u001b[0;31m     \u001b[0mtipo\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0m_get_dtype_type\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0marr_or_dtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    119\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0missubclass\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtipo\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mobject_\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    120\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/anaconda2/lib/python2.7/site-packages/pandas/core/dtypes/common.pyc\u001b[0m in \u001b[0;36m_get_dtype_type\u001b[0;34m(arr_or_dtype)\u001b[0m\n\u001b[1;32m   1861\u001b[0m     \u001b[0;32melif\u001b[0m \u001b[0misinstance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0marr_or_dtype\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mPeriodDtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1862\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mPeriodDtypeType\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1863\u001b[0;31m     \u001b[0;32melif\u001b[0m \u001b[0misinstance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0marr_or_dtype\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mstring_types\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1864\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mis_categorical_dtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0marr_or_dtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1865\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mCategoricalDtypeType\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["corrs, adfs = [], []\n", "\n", "for d in np.arange(0.0, 2.1, 0.1):\n", "    ts1 = np.log(tick_bars)\n", "    ts2 = fracDiff(ts1, d)\n", "\n", "    corr = np.corrcoef(ts1.loc[ts2.index,'close'],ts2['close'])[0,1]\n", "    adf = adfuller(ts2.close)[0]\n", "\n", "    corrs.append(corr)\n", "    adfs.append(adf)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["stats = pd.DataFrame({\n", "    'd': np.arange(0.0, 2.1, 0.1),\n", "    'corr': corrs,\n", "    'adfs': adfs\n", "})\n", "stats.index = stats['d']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["stats[['corr', 'adfs']].plot(secondary_y='adfs')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 335, "metadata": {}, "outputs": [{"data": {"text/plain": ["-18.301437043431015"]}, "execution_count": 335, "metadata": {}, "output_type": "execute_result"}], "source": ["adfuller(tick_bars.close.diff().fillna(1e-5))[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Exercises"]}, {"cell_type": "code", "execution_count": 157, "metadata": {"collapsed": true}, "outputs": [], "source": ["gauss = np.random.normal(0, 1, 1000)"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXYAAAD8CAYAAABjAo9vAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nrkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.plot(gauss)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [{"data": {"text/plain": ["(-30.721619887470883,\n", " 0.0,\n", " 0,\n", " 999,\n", " {'1%': -3.4369127451400474,\n", "  '10%': -2.568312754566378,\n", "  '5%': -2.864437475834273},\n", " 2766.951680753896)"]}, "execution_count": 159, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON><PERSON>(gauss)"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [{"data": {"text/plain": ["(-1.6300726870515139,\n", " 0.4674526661720234,\n", " 0,\n", " 999,\n", " {'1%': -3.4369127451400474,\n", "  '10%': -2.568312754566378,\n", "  '5%': -2.864437475834273},\n", " 2765.4906005929283)"]}, "execution_count": 161, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON><PERSON>(np.cumsum(gauss))"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"text/plain": ["(-14.66133248162864,\n", " 3.377743248351254e-27,\n", " 14,\n", " 983,\n", " {'1%': -3.4370198458812156,\n", "  '10%': -2.568337912084273,\n", "  '5%': -2.864484708707697},\n", " 2829.3679503081694)"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["adfuller(np.diff(np.diff(np.cumsum(gauss))))"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.14"}}, "nbformat": 4, "nbformat_minor": 2}