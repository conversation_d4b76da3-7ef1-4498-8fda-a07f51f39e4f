import threading
import time
import json
import struct
import zlib
from pprint import pprint
# import logging
from datetime import datetime

import pandas as pd
import numpy as np

from pyqlab.net.message import Message
from pyqlab.net.client_socket_utils import ClientSocketUtils

from pyqlab.rl.data.utils import calc_5min_tick_features
from pyqlab.rl.predict import FuturesTrading

class RlClient(ClientSocketUtils):

    def __init__(self, ip: str, port: int, portf_name: str, model_path: str):
        super().__init__(ip=ip, port=port)
        # logging.setLevel(print)
        self.portf_name = portf_name
        # init futurestrading
        self.model_path= model_path
        self.long_agents = dict()
        self.short_agents = dict()

    def load_model(self, label, point_num, env_config):
        cwd=f"{self.model_path}/FuturesDiscEnv_ppo_{label}_long/checkpoint_{point_num:06d}/checkpoint-{point_num}"
        # print(cwd)
        self.long_agents[label] = FuturesTrading(
            env_config=env_config,
            cwd=cwd,
            agent='ppo')

        env_config['direct'] = "short"
        cwd=f"{self.model_path}/FuturesDiscEnv_ppo_{label}_short/checkpoint_{point_num:06d}/checkpoint-{point_num}"
        self.short_agents[label] = FuturesTrading(
            env_config=env_config,
            cwd=cwd,
            agent='ppo')
        print(f"Load model: {label}: {point_num}")

    def process_tickdata(self, label, data):
        buff = []
        for i in range(len(data)//48):
            tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", data[i*48:(i+1)*48])
            # print(i, datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)
            buff.append([label, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])
        return pd.DataFrame(buff, columns=['label', 'datetime', 'price', 'size', 'bid_price1', 'bid_price2', 'bid_size1', 'bid_size2', 'ask_price1', 'ask_price2', 'ask_size1', 'ask_size2'])

    def process_recv_data(self, msg: Message):
        if msg.command == 3: # 是压缩包
            msg.body = zlib.decompress(msg.body, zlib.MAX_WBITS|32)
        if msg.business_type == 1:
            label = str(msg.body[0:9], encoding='utf-8')
            if label[8] == ' ':
                label = label[0:8]
            df_tick = self.process_tickdata(label, msg.body[9:])

            state, _ = calc_5min_tick_features(df_tick, double_second=True)
            state = state.values.reshape(-1) * 2 ** -7
            # action = self.trading.predict(state)
            # print(action)
            self.commit(label, state)
        else:
            print(datetime.now().strftime('%Y-%m-%d %H:%M'), f"Unknow business_type={msg.business_type} message")

    def send_long_action(self, label, action):
        trade_action = ["Hold", "OpenLong", "CloseLong"]
        msg = Message()
        msg.app_id = 1
        msg.command = 2
        msg.business_type = 10
        msg.body = json.dumps({"label": label, "action": trade_action[action], "portfolio": self.portf_name})
        print(datetime.now().strftime('%Y-%m-%d %H:%M'), msg.body)
        self.send(msg.package_msg())

    def send_short_action(self, label, action):
        trade_action = ["Hold", "OpenShort", "CloseShort"]
        msg = Message()
        msg.app_id = 1
        msg.command = 2
        msg.business_type = 10
        msg.body = json.dumps({"label": label, "action": trade_action[action], "portfolio": self.portf_name})
        print(datetime.now().strftime('%Y-%m-%d %H:%M'), msg.body)
        self.send(msg.package_msg())

    def trade(self, label, state):
        if label not in self.long_agents or label not in self.short_agents:
            print(datetime.now().strftime('%Y-%m-%d %H:%M'), f"{label}: not find agent.")
            return

        long_action = self.long_agents[label].predict(state)
        short_action = self.short_agents[label].predict(state)
        if long_action == 1 and short_action == 1:
            print(datetime.now().strftime('%Y-%m-%d %H:%M'), f"{label}: LONG & SHORT open at the same time.")
            return
        if long_action > 0:
            self.send_long_action(label, long_action)
        else:
            print(datetime.now().strftime('%Y-%m-%d %H:%M'), f"{label}: LONG HOLD.")

        if short_action > 0:
            self.send_short_action(label, short_action)
        else:
            print(datetime.now().strftime('%Y-%m-%d %H:%M'), f"{label}: SHORT HOLD.")

    def commit(self, label, state):
        trade = threading.Thread(target=self.trade, args=(label, state))
        trade.start()
        trade.join()

    def check_connect(self):
        while True:
            if not self.is_connect():
                if self.connect():
                    self.regist()
                    self.start()
                    print("RL connect server success!")
            time.sleep(30)

    def run(self):
        self.thrd_check = threading.Thread(target = self.check_connect)
        self.thrd_check.setDaemon(True)
        self.thrd_check.start()

if __name__ == "__main__":
    env_config = {
        "name": "FuturesDiscEnv",
        "initial_amount": 1e5,
        "gamma": 0.98,
        "direct": "short",
        "mode": "train",
        "label": "HC8888.SC",
        "data_path": "e:/lab/roboquant/pylab/data",
    }

    clt = RlClient(
        '127.0.0.1', 10520,
        portf_name="FUT-RL-1.0",
        model_path="E:/lab/RoboQuant/pylab/model_rl"
    )
    clt.load_model("RB8888.SC", 200, env_config)
    clt.load_model("HC8888.SC", 200, env_config)
    clt.run()
    
    while True:
        item = input("waiting input e or q to exit!\n")
        if item == 'q' or item == 'e':
            break;
    # clt.stop()

    