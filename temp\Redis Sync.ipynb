{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from redis import Redis\n", "import sys\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["111\n", "[b'day_RU8888.SC', b'min5_RM8888.ZC', b'mainfuturecodetable', b'day_IC8888.SF', b'min5_FG8888.ZC', b'day_PB8888.SC', b'day_C8888.DC', b'wechat:user:oT-02uOC6bIMQ_N_ONyC7d3j20ik', b'min5_NI8888.SC', b'cs_rq003:oT-02uKZ7G5uqT8nYRdmqILQy1ug:investreport:future', b'day_L8888.DC', b'min5_AP8888.ZC', b'min5_AU8888.SC', b'min5_AL8888.SC', b'cs_rq003:oT-02uHr2Hw0oUmkG5VONLuCcpiw:investreport:future', b'day_AG8888.SC', b'min5_L8888.DC', b'min5_SC8888.SC', b'min5_IH8888.SF', b'day_AL8888.SC', b'day_JD8888.DC', b'min5_RU8888.SC', b'min5_OI8888.ZC', b'min5_SR8888.ZC', b'min5_P8888.DC', b'day_CS8888.DC', b'min5_MA8888.ZC', b'min5_J8888.DC', b'PB8888.SC', b'cs_rq002:oT-02uHr2Hw0oUmkG5VONLuCcpiw:investreport:future', b'min5_BU8888.SC', b'_kombu.binding.celery', b'cs_rq001:oT-02uKZ7G5uqT8nYRdmqILQy1ug:investreport:future', b'day_M8888.DC', b'min5_SN8888.SC', b'min5_IC8888.SF', b'min5_PB8888.SC', b'ZN8888.SC', b'min5_M8888.DC', b'day_SN8888.SC', b'min5_A8888.DC', b'wechat:user:oT-02uE4bPmnzblwYBdlyrjc1Gk0', b'day_HC8888.SC', b'min5_HC8888.SC', b'day_PP8888.DC', b'wechat:user:oT-02uHr2Hw0oUmkG5VONLuCcpiw', b'day_J8888.DC', b'day_MA8888.ZC', b'min5_CF8888.ZC', b'day_IH8888.SF', b'day_OI8888.ZC', b'day_FG8888.ZC', b'day_CY8888.ZC', b'day_NI8888.SC', b'min5_CY8888.ZC', b'min5_ZN8888.SC', b'day_SR8888.ZC', b'day_ZN8888.SC', b'min5_V8888.DC', b'min5_TA8888.ZC', b'min5_IF8888.SF', b'day_TA8888.ZC', b'cs_rq001:oT-02uHr2Hw0oUmkG5VONLuCcpiw:investreport:future', b'day_V8888.DC', b'day_AU8888.SC', b'min5_RB8888.SC', b'rangebarconfig', b'day_RM8888.ZC', b'day_RB8888.SC', b'day_A8888.DC', b'day_AP8888.ZC', b'min5_ZC8888.ZC', b'day_SM8888.ZC', b'day_IF8888.SF', b'wechat:user:oT-02uGnm8jqR0QrefcHydlla-LE', b'min5_SM8888.ZC', b'day_BU8888.SC', b'min5_JD8888.DC', b'min5_AG8888.SC', b'AL8888.SC', b'day_I8888.DC', b'day_ZC8888.ZC', b'min5_Y8888.DC', b'_kombu.binding.celeryev', b'cs_rq002:oT-02uKZ7G5uqT8nYRdmqILQy1ug:investreport:future', b'wechat:user:oT-02uCNLg_HdqOvNgGvJfcPfIGs', b'min5_C8888.DC', b'day_CU8888.SC', b'CU8888.SC', b'SN8888.SC', b'day_CF8888.ZC', b'min5_PP8888.DC', b'min5_SF8888.ZC', b'min5_CU8888.SC', b'day_P8888.DC', b'day_Y8888.DC', b'wechat:user:oT-02uFDbjj7udVAk5TEAQD1-2Os', b'oT-02uKZ7G5uqT8nYRdmqILQy1ug_message_positions', b'cs_rq004:oT-02uKZ7G5uqT8nYRdmqILQy1ug:investreport:future', b'day_JM8888.DC', b'min5_SP8888.SC', b'min5_CS8888.DC', b'IC8888.SF', b'min5_I8888.DC', b'wechatpy:wx3f450b642b328947_access_token', b'day_SF8888.ZC', b'NI8888.SC', b'day_SP8888.SC', b'min5_JM8888.DC', b'day_SC8888.SC', b'wechat:user:oT-02uKZ7G5uqT8nYRdmqILQy1ug']\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["r = Redis(host='*************',password='wdljshbsjzsszsbbzyjcsz~1974',db=0,port=51301)\n", "# rb = r.get(\"rangebarconfig\")\n", "# print(rb)\n", "# mf = r.get(\"mainfuturecodetable\")\n", "# print(mf)\n", "print(r.dbsize())   #库里有多少key，多少条数据\n", "print(r.keys())\n", "r.delete(\"hello\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# fut_codes = ds.get_block_data(\"ZLLX\") #ZLQH\n", "# for code in fut_codes:\n", "#     r.delete(code)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}