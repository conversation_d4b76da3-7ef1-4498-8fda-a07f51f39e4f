# CandlestickVQTokenizer码本训练

本文档介绍如何训练CandlestickVQTokenizer的码本权重。CandlestickVQTokenizer使用VQ-VAE（Vector Quantized Variational Autoencoder）进行向量量化，将连续的K线特征向量映射到离散的码本索引。

## 概述

训练过程包括以下步骤：

1. 从K线数据中提取特征向量
2. 训练VQ-VAE模型
3. 保存码本权重
4. 在CandlestickVQTokenizer中使用训练好的码本权重

## 训练码本

### 使用批处理脚本

最简单的方法是使用提供的批处理脚本：

```
train_candlestick_vq_codebook.bat
```

批处理脚本使用默认参数，您可以根据需要修改参数：

```
train_candlestick_vq_codebook.bat --data_dir f:\hqdata\tsdb --market fut --block_name top --period day
```

### 直接使用Python脚本

您也可以直接使用Python脚本：

```
python -m pyqlab.models.base.train_candlestick_vq_codebook --data_dir f:\hqdata\tsdb --market fut --block_name top --period day
```

## 参数说明

### 数据参数

- `--data_dir`：K线数据目录，包含parquet或csv文件
- `--market`：市场类型，如all, stock, future
- `--block_name`：板块名称
- `--period`：K线周期，如day, min1, min5
- `--output_name`：输出文件名前缀

### 保存参数

- `--save_dir`：保存目录

### 训练参数

- `--num_epochs`：训练轮数，默认50
- `--batch_size`：批次大小，默认128
- `--learning_rate`：学习率，默认0.001

### 模型参数

- `--num_embeddings`：码本大小，默认512
- `--embedding_dim`：码向量维度，默认5
- `--hidden_dim`：隐藏层维度，默认64

### 数据处理参数

- `--atr_window`：ATR计算窗口，默认14
- `--ma_volume_period`：成交量移动平均周期，默认20
- `--include_volume`：是否包含交易量
- `--min_samples`：最小样本数，默认1000
- `--max_samples`：最大样本数，默认100000

### 其他参数

- `--save_interval`：保存间隔，默认5
- `--device`：训练设备，默认cuda

## 训练输出

训练过程中会生成以下文件：

1. `params.txt`：训练参数记录
2. `vqvae_model_epoch_N.pt`：每N个epoch保存的模型权重
3. `vqvae_codebook_epoch_N.pt`：每N个epoch保存的码本权重
4. `loss_plot.png`：损失曲线图
5. `vqvae_codebook_MARKET_PERIOD_BLOCK.pt`：最终的码本权重

## 在CandlestickVQTokenizer中使用训练好的码本

训练完成后，您可以在CandlestickVQTokenizer中使用训练好的码本权重：

```python
from pyqlab.models.base.vq_tokenizer import CandlestickVQTokenizer

# 初始化tokenizer
tokenizer = CandlestickVQTokenizer(
    codebook_weights_path='path/to/vqvae_codebook.pt',
    num_embeddings=512,
    embedding_dim=5,
    atr_period=14,
    ma_volume_period=20
)

# 使用tokenizer
token_ids = tokenizer.encode(df_ohlcv)
```

## 推荐配置

### 股票日线数据

```
--market stock --period day --num_embeddings 512 --embedding_dim 5 --atr_window 14 --ma_volume_period 20 --include_volume
```

### 期货日线数据

```
--market fut --period day --num_embeddings 512 --embedding_dim 4 --atr_window 14 --ma_volume_period 20
```

### 分钟线数据

```
--market fut --period min1 --num_embeddings 1024 --embedding_dim 4 --atr_window 100 --ma_volume_period 100
```

## 注意事项

1. 确保数据目录中包含足够的K线数据
2. 对于不同的市场和周期，可能需要调整参数
3. 训练过程可能需要较长时间，特别是对于大量数据
4. 如果显存不足，可以减小batch_size或使用CPU训练

## 故障排除

### 找不到数据文件

确保数据目录中包含匹配的文件，文件名格式应为：`MARKET_BLOCK_PERIOD*.parquet`或`MARKET_BLOCK_PERIOD*.csv`

### 训练过程中内存不足

减小`--max_samples`参数或增加`--min_samples`参数

### 训练过程中显存不足

减小`--batch_size`参数或使用`--device cpu`

### 训练损失不收敛

尝试调整`--learning_rate`、`--hidden_dim`或增加`--num_epochs`
