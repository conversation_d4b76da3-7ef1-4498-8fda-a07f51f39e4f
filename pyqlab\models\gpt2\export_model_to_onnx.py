"""
将CandlestickVQGPT模型导出为ONNX格式

该脚本用于将训练好的CandlestickVQGPT模型导出为ONNX格式，以便在推理时使用。
"""

import os
import sys
import argparse
import torch
import logging

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("export_model_to_onnx.log")
    ]
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='将CandlestickVQGPT模型导出为ONNX格式')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--output_path', type=str, default=None, help='输出ONNX文件路径，默认为模型路径加.onnx后缀')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--batch_size', type=int, default=1, help='批次大小')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    parser.add_argument('--n_time_features', type=int, default=5, help='时间特征数量')
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置输出路径
    if args.output_path is None:
        args.output_path = os.path.splitext(args.model_path)[0] + '.onnx'
    
    # 检查输出目录是否存在
    output_dir = os.path.dirname(args.output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 加载模型
    logger.info(f"加载模型: {args.model_path}")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 加载模型
        model = CandlestickVQGPT.from_pretrained(args.model_path, device=device)
        model.eval()
        
        # 设置输入形状
        input_shape = (args.batch_size, args.seq_len)
        code_shape = (args.batch_size,)
        time_shape = (args.batch_size, args.seq_len, args.n_time_features) if args.use_time_features else None
        
        # 导出为ONNX格式
        logger.info(f"导出模型为ONNX格式: {args.output_path}")
        onnx_path = model.to_onnx(
            path=args.output_path,
            input_shape=input_shape,
            code_shape=code_shape,
            time_shape=time_shape
        )
        
        if onnx_path:
            logger.info(f"模型已成功导出为ONNX格式: {onnx_path}")
        else:
            logger.error("导出ONNX模型失败")
            return 1
        
    except Exception as e:
        logger.error(f"导出模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
