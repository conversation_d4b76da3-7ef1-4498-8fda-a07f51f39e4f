"""
Candlestick Dataset

为LLM模型提供K线数据的PyTorch数据集
"""

import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Tuple, Optional, Union, Callable
import pickle
from datetime import datetime, timedelta
import logging

from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.utils.timefeatures import time_features, datetime_features

# 获取logger
logger = logging.getLogger('CandlestickDataset')

class CandlestickDataset(Dataset):
    """
    K线数据集
    """
    def __init__(self,
                 data: Union[pd.DataFrame, List[pd.DataFrame]],
                 tokenizer: CandlestickTokenizer,
                 seq_len: int = 30,
                 pred_len: int = 1,
                 stride: int = 1,
                 add_special_tokens: bool = True,
                 transform: Optional[Callable] = None,
                 code_ids: Optional[List[int]] = None,
                 use_time_features: bool = True,
                 timeenc: int = 1,
                 time_feature_columns: List[str] = None):
        """
        初始化K线数据集

        Args:
            data: 单个DataFrame或DataFrame列表，每个DataFrame必须包含datetime, open, high, low, close列
            tokenizer: 用于将K线数据转换为token的tokenizer
            seq_len: 输入序列长度
            pred_len: 预测序列长度
            stride: 滑动窗口的步长
            add_special_tokens: 是否添加特殊token
            transform: 数据转换函数
            code_ids: 证券代码ID列表，与data列表一一对应
            use_time_features: 是否使用时间特征
            timeenc: 时间编码方式，0：基本时间特征，1：时间戳特征，2：时间戳正弦余弦特征
            time_feature_columns: 时间特征列名列表
        """
        self.tokenizer = tokenizer
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.stride = stride
        self.add_special_tokens = add_special_tokens
        self.transform = transform
        self.use_time_features = use_time_features
        self.timeenc = timeenc
        self.time_feature_columns = time_feature_columns or []

        # 处理输入数据
        if isinstance(data, pd.DataFrame):
            self.data = [data]
        else:
            self.data = data

        # 处理代码ID
        if code_ids is None:
            self.code_ids = list(range(len(self.data)))
        else:
            if len(code_ids) != len(self.data):
                raise ValueError("code_ids的长度必须与data列表长度相同")
            self.code_ids = code_ids
        logger.info(f"代码ID: {self.code_ids}")

        # 预处理数据
        self.samples = self._preprocess_data()

    def _preprocess_data(self):
        """
        预处理数据，使用优化的方法计算样本数量和位置

        Returns:
            预处理后的数据字典，包含input_tokens, target_tokens, code_ids, time_features等numpy数组
        """
        # 对每个DataFrame进行tokenize并计算可用样本数
        self.tokenized_data = []
        self.time_features_data = []
        sample_counts = []

        for i, df in enumerate(self.data):
            # 确保数据按时间排序
            if 'datetime' in df.columns:
                df = df.sort_values('datetime')

            # 检查数据长度是否足够
            if len(df) < self.tokenizer.atr_window + self.seq_len + self.pred_len + 1:
                continue

            # 对整个数据集进行tokenize
            tokens = self.tokenizer.tokenize(df)
            assert len(tokens) == len(df), f"tokenize后的长度与原始数据长度不一致: {len(tokens)} vs {len(df)}"

            # 计算可用样本数
            valid_length = len(tokens) - self.seq_len - self.pred_len + 1
            if valid_length <= 0:
                continue

            # 计算实际样本数（考虑stride）
            sample_count = (valid_length + self.stride - 1) // self.stride
            sample_counts.append(sample_count)

            # 保存tokenized数据
            self.tokenized_data.append({
                'tokens': tokens,
                'df': df,
                'code_id': self.code_ids[i]
            })

            # 提取时间特征
            if self.use_time_features and 'datetime' in df.columns:
                if self.timeenc == 0:
                    time_feats = self._extract_time_features(df)
                    self.time_features_data.append(time_feats)
                elif self.timeenc == 1:
                    df_stamp= time_features(pd.to_datetime(df['datetime'].values), freq='5min').transpose(1, 0)
                    df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])
                    # df = pd.concat([df, df_tf], axis=1)
                    self.time_features_data.append(df_tf.values)
                    
                    # 对于特殊标记（非交易日），设置特殊的时间特征
                    if 'is_holiday' in df.columns:
                        # 对于非交易日，设置特殊的DayOfWeek值
                        df.loc[df['is_holiday'], 'DayOfWeek'] = -1
                        
                        # 删除临时列
                        df.drop(columns=['is_holiday'], inplace=True)
                elif self.timeenc == 2:
                    df_stamp= datetime_features(pd.to_datetime(df['datetime'].values)) #.transpose(1, 0)
                    df_tf = pd.DataFrame(df_stamp, columns=
                                        ['hour_sin', 'hour_cos',
                                        'dow_sin', 'dow_cos',
                                        'month_sin', 'month_cos',
                                        'doy_sin', 'doy_cos'])
                    # df = pd.concat([df, df_tf], axis=1)
                    self.time_features_data.append(df_tf.values)
            else:
                self.time_features_data.append(None)

        # 计算累积和，用于快速定位
        if sample_counts:
            self.sample_counts = np.array(sample_counts)
            self.cum_counts = np.cumsum(self.sample_counts)
            self.length = self.cum_counts[-1]
        else:
            self.sample_counts = np.array([0])
            self.cum_counts = np.array([0])
            self.length = 0

        logger.info(f"滑动窗口 {self.stride}， 生成了 {self.length} 个训练样本")

        # 返回一个空字典，保持与原来的接口兼容
        del self.data

        return {}

    def _get_data_index(self, idx):
        """根据全局索引获取数据索引和局部索引"""
        if idx >= self.length:
            raise IndexError(f"索引 {idx} 超出范围 (0-{self.length-1})")

        # 找到对应的数据集
        data_idx = np.searchsorted(self.cum_counts, idx, side='right')

        # 计算局部索引
        if data_idx == 0:
            local_idx = idx
        else:
            local_idx = idx - self.cum_counts[data_idx - 1]

        # 转换为原始数据的起始索引
        start_idx = local_idx * self.stride

        return data_idx, start_idx

    def __len__(self):
        """获取数据集长度"""
        return self.length

    def __getitem__(self, idx):
        """获取一个样本"""
        if self.length == 0:
            raise IndexError("数据集为空")

        # 获取数据索引和局部索引
        data_idx, start_idx = self._get_data_index(idx)

        # 获取对应的数据
        data = self.tokenized_data[data_idx]
        tokens = data['tokens']
        df = data['df']
        code_id = data['code_id']

        # 计算结束索引
        end_idx = start_idx + self.seq_len
        target_end_idx = end_idx + self.pred_len

        # 获取输入和目标序列
        input_tokens = tokens[start_idx:end_idx]
        target_tokens = tokens[start_idx+self.pred_len:target_end_idx]

        # 确保长度一致
        if len(input_tokens) < self.seq_len:
            input_tokens = input_tokens + [0] * (self.seq_len - len(input_tokens))

        if len(target_tokens) < self.seq_len:
            target_tokens = target_tokens + [0] * (self.seq_len - len(target_tokens))

        # 准备额外的目标
        # direction_target = 2  # 默认为持平
        # volatility_target = 0  # 默认为低波动
        # volume_target = 0  # 默认为正常交易量

        # 计算方向目标（上涨/下跌/持平）
        if end_idx + 1 < len(df):
            next_close = df.iloc[end_idx + 1]['close']
            curr_close = df.iloc[end_idx]['close']
            price_change = (next_close - curr_close) / curr_close

            if price_change > 0.005:  # 上涨
                direction_target = 0
            elif price_change < -0.005:  # 下跌
                direction_target = 1
            else:  # 持平
                direction_target = 2

        # 获取时间特征
        time_feats = self.time_features_data[data_idx]
        if time_feats is not None:
            time_features = time_feats[start_idx:end_idx]

            # 确保长度一致
            if len(time_features) < self.seq_len:
                padding = np.zeros((self.seq_len - len(time_features), time_features.shape[1]))
                time_features = np.vstack([time_features, padding])
        else:
            # 创建全零时间特征
            time_features = np.zeros((self.seq_len, 5))

        # 转换为PyTorch张量
        input_tokens = torch.tensor(input_tokens, dtype=torch.long)
        target_tokens = torch.tensor(target_tokens, dtype=torch.long)
        code_id = torch.tensor(code_id, dtype=torch.long)
        # direction_target = torch.tensor(direction_target, dtype=torch.long)
        # volatility_target = torch.tensor(volatility_target, dtype=torch.long)
        # volume_target = torch.tensor(volume_target, dtype=torch.long)
        time_features = torch.tensor(time_features, dtype=torch.float)

        # 创建结果字典
        result = {
            'input_tokens': input_tokens,
            'target_tokens': target_tokens,
            'code_id': code_id,
            # 'direction_target': direction_target,
            # 'volatility_target': volatility_target,
            # 'volume_target': volume_target,
            'time_features': time_features
        }

        # 应用转换
        if self.transform is not None:
            result = self.transform(result)

        return result

    def _extract_time_features(self, df):
        """提取时间特征"""
        if 'datetime' in df.columns:
            # 基本时间特征
            dt = pd.to_datetime(df['datetime'])

            # 创建时间特征
            time_features = np.zeros((len(df), 5))

            # 小时 (0-23) -> (0-1)
            time_features[:, 0] = dt.dt.hour / 23.0

            # 星期几 (0-6) -> (0-1)
            time_features[:, 1] = dt.dt.dayofweek / 6.0

            # 月份 (1-12) -> (0-1)
            time_features[:, 2] = (dt.dt.month - 1) / 11.0

            # 月内日 (1-31) -> (0-1)
            time_features[:, 3] = (dt.dt.day - 1) / 30.0

            # 是否为交易日 (0 or 1)
            time_features[:, 4] = (dt.dt.dayofweek < 5).astype(float)

            return time_features

        # 如果没有datetime列，返回零矩阵
        return np.zeros((self.seq_len, 5))

    def get_dataloader(self, batch_size=32, shuffle=True, num_workers=0, pin_memory=False, persistent_workers=False):
        """
        创建DataLoader

        Args:
            batch_size: 批大小
            shuffle: 是否打乱数据
            num_workers: 数据加载的工作进程数
            pin_memory: 是否将数据加载到固定内存中
            persistent_workers: 是否使用持久化工作进程

        Returns:
            DataLoader对象
        """
        return DataLoader(
            self,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=pin_memory,
            persistent_workers=persistent_workers and num_workers > 0
        )

class TimeSeriesCandlestickDataset(CandlestickDataset):
    """
    带有时间特征的K线数据集，使用优化的数据处理方式
    """
    def __init__(self,
                 data: Union[pd.DataFrame, List[pd.DataFrame]],
                 tokenizer: CandlestickTokenizer,
                 seq_len: int = 30,
                 pred_len: int = 5,
                 stride: int = 1,
                 add_special_tokens: bool = True,
                 transform: Optional[Callable] = None,
                 code_ids: Optional[List[int]] = None,
                 time_features: bool = True):
        """
        初始化带有时间特征的K线数据集

        Args:
            data: 单个DataFrame或DataFrame列表，每个DataFrame必须包含datetime, open, high, low, close列
            tokenizer: 用于将K线数据转换为token的tokenizer
            seq_len: 输入序列长度
            pred_len: 预测序列长度
            stride: 滑动窗口的步长
            add_special_tokens: 是否添加特殊token
            transform: 数据转换函数
            code_ids: 证券代码ID列表，与data列表一一对应
            time_features: 是否添加时间特征
        """
        # 设置use_time_features参数
        self.use_time_features = time_features
        super().__init__(data, tokenizer, seq_len, pred_len, stride,
                        add_special_tokens, transform, code_ids, time_features)

    def _extract_time_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        提取时间特征

        Args:
            df: 包含datetime列的DataFrame

        Returns:
            时间特征数组
        """
        if 'datetime' in df.columns:
            # 转换为pandas datetime
            dt = pd.to_datetime(df['datetime'])

            # 提取时间特征
            hour = dt.dt.hour / 23.0  # 归一化到[0, 1]
            minute = dt.dt.minute / 59.0
            weekday = dt.dt.dayofweek / 6.0
            month = (dt.dt.month - 1) / 11.0
            day = (dt.dt.day - 1) / 30.0

            # 组合特征
            features = np.stack([hour, minute, weekday, month, day], axis=1)
            return features

        # 如果没有datetime列，返回零矩阵
        return np.zeros((len(df), 5))

    def __getitem__(self, idx):
        """获取一个样本"""
        # 使用父类的__getitem__方法获取基本样本
        result = super().__getitem__(idx)

        # 获取数据索引和局部索引
        data_idx, start_idx = self._get_data_index(idx)

        # 获取时间特征
        time_feats = self.time_features_data[data_idx]
        if time_feats is not None:
            # 计算结束索引
            end_idx = start_idx + self.seq_len
            target_end_idx = end_idx + self.pred_len

            # 获取输入和目标时间特征
            input_time_feats = time_feats[start_idx:end_idx]
            target_time_feats = time_feats[start_idx+self.pred_len:target_end_idx]

            # 确保长度一致
            if len(input_time_feats) < self.seq_len:
                padding = np.zeros((self.seq_len - len(input_time_feats), input_time_feats.shape[1]))
                input_time_feats = np.vstack([input_time_feats, padding])

            if len(target_time_feats) < self.seq_len:
                padding = np.zeros((self.seq_len - len(target_time_feats), target_time_feats.shape[1]))
                target_time_feats = np.vstack([target_time_feats, padding])

            # 转换为PyTorch张量
            input_time_features = torch.tensor(input_time_feats, dtype=torch.float)
            target_time_features = torch.tensor(target_time_feats, dtype=torch.float)

            # 添加到结果中
            result['input_time_features'] = input_time_features
            result['target_time_features'] = target_time_features

            # 删除原来的time_features
            if 'time_features' in result:
                del result['time_features']

        return result

