e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python backtest_candlestick_llm.py ^
--model_path e:/lab/RoboQuant/pylab/checkpoints/basic_candlestick_llm/basic_ct_llm_FUT_TOP_MIN1_30_16_8_050718_8.956.onnx ^
--data_path f:/hqdata/fut_top_min1.parquet ^
--initial_capital 10000.0 ^
--seq_len 30 ^
--commission 0.001 ^
--leverage 8.0 ^
--threshold 0.003 ^
--stop_loss 0.05 ^
--signal_type topk ^
--take_profit 0.1 ^
--temperature 0.8 ^
--print_interval 50

@REM python backtest_candlestick_llm.py ^
@REM --model_path e:/lab/RoboQuant/pylab/checkpoints/basic_candlestick_llm/basic_candlestick_llm_050714_8.197.onnx ^
@REM --data_path f:/hqdata/fut_sf_min5.parquet ^
@REM --initial_capital 10000.0 ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --leverage 8.0 ^
@REM --threshold 0.003 ^
@REM --stop_loss 0.05 ^
@REM --signal_type topk ^
@REM --take_profit 0.1 ^
@REM --temperature 0.8 ^
@REM --print_interval 50

@REM python backtest_candlestick_llm.py ^
@REM --model_path e:/lab/RoboQuant/pylab/checkpoints/candlestick_gpt4/best_model.pt ^
@REM --data_path f:/hqdata/fut_sf_min5.parquet ^
@REM --initial_capital 10000.0 ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --threshold 0.6 ^
@REM --stop_loss 0.05 ^
@REM --take_profit 0.1