"""
预测多样性深度分析脚本
专门分析模型预测的多样性问题，并提供改进建议
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import argparse
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from candlestick_vq_gpt import CandlestickVQGPT
from vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod

# 尝试导入增强模型
try:
    from fix_diversity_problem import DiversityEnhancedCandlestickVQGPT
    ENHANCED_MODEL_AVAILABLE = True
except ImportError:
    ENHANCED_MODEL_AVAILABLE = False

def analyze_token_distribution(model_path, codebook_path, num_samples=1000):
    """分析token分布和预测多样性"""
    print("=== Token分布分析 ===")

    # 加载模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 尝试加载增强模型或普通模型
    try:
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint.get('config', {})

        # 检查是否是增强模型
        state_dict = checkpoint['model_state_dict']
        is_enhanced = 'token_usage_count' in state_dict or 'prediction_history' in state_dict

        if is_enhanced and ENHANCED_MODEL_AVAILABLE:
            print("检测到增强模型，使用DiversityEnhancedCandlestickVQGPT加载")
            model = DiversityEnhancedCandlestickVQGPT(
                vocab_size=config.get('vocab_size', 518),
                code_size=config.get('code_size', 100),
                seq_len=config.get('seq_len', 30),
                n_layer=config.get('n_layer', 4),
                n_head=config.get('n_head', 8),
                d_model=config.get('d_model', 64),
                dropout=config.get('dropout', 0.1),
                use_time_features=config.get('use_time_features', True),
                n_time_features=config.get('n_time_features', 8),
                label_smoothing=config.get('label_smoothing', 0.0),
                use_auxiliary_loss=config.get('use_auxiliary_loss', True),
                diversity_weight=config.get('diversity_weight', 2.0),
                anti_collapse_weight=config.get('anti_collapse_weight', 3.0)
            )
            model.load_state_dict(state_dict)
        else:
            print("使用标准CandlestickVQGPT加载")
            model = CandlestickVQGPT.from_pretrained(model_path, device=device)

        model = model.to(device)
        model.eval()

    except Exception as e:
        print(f"加载模型失败: {e}")
        print("尝试使用标准方法加载...")
        model = CandlestickVQGPT.from_pretrained(model_path, device=device)
        model.eval()

    # 创建tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=codebook_path,
        num_embeddings=512,
        embedding_dim=4,
        atr_period=14,
        ma_volume_period=14,
        vectorization_method=VectorizationMethod.ATR_BASED
    )

    print(f"模型词汇表大小: {model.vocab_size}")
    print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")

    # 生成随机输入进行测试
    predictions = []
    logits_stats = []

    with torch.no_grad():
        for i in range(num_samples):
            # 创建随机输入
            seq_len = np.random.randint(10, 31)
            input_tokens = torch.randint(0, model.vocab_size, (1, seq_len), device=device)
            code_ids = torch.randint(0, 100, (1,), device=device)
            time_features = torch.randn(1, seq_len, 8, device=device) if model.use_time_features else None

            # 前向传播
            logits, _ = model(input_tokens, code_ids, time_features)
            last_logits = logits[0, -1, :]

            # 统计信息
            pred_token = last_logits.argmax().item()
            predictions.append(pred_token)

            # Logits统计
            probs = F.softmax(last_logits, dim=-1)
            entropy = -torch.sum(probs * torch.log(probs + 1e-8)).item()
            max_prob = probs.max().item()

            logits_stats.append({
                'entropy': entropy,
                'max_prob': max_prob,
                'std': last_logits.std().item(),
                'mean': last_logits.mean().item()
            })

    # 分析结果
    pred_counter = Counter(predictions)
    unique_preds = len(pred_counter)
    total_preds = len(predictions)
    diversity_ratio = unique_preds / total_preds

    print(f"\n=== 预测多样性分析 ===")
    print(f"总预测数: {total_preds}")
    print(f"唯一预测数: {unique_preds}")
    print(f"多样性比例: {diversity_ratio:.4f}")

    # 最频繁的预测
    most_common = pred_counter.most_common(10)
    print(f"\n最频繁的10个预测:")
    for token, count in most_common:
        percentage = count / total_preds * 100
        print(f"  Token {token}: {count} 次 ({percentage:.2f}%)")

    # Logits统计
    stats_df = pd.DataFrame(logits_stats)
    print(f"\n=== Logits统计 ===")
    print(f"平均熵: {stats_df['entropy'].mean():.4f} ± {stats_df['entropy'].std():.4f}")
    print(f"平均最大概率: {stats_df['max_prob'].mean():.4f} ± {stats_df['max_prob'].std():.4f}")
    print(f"平均标准差: {stats_df['std'].mean():.4f} ± {stats_df['std'].std():.4f}")

    # 问题诊断
    print(f"\n=== 问题诊断 ===")
    if diversity_ratio < 0.1:
        print("❌ 严重问题: 预测多样性极低")
    elif diversity_ratio < 0.3:
        print("⚠️  问题: 预测多样性较低")
    else:
        print("✅ 预测多样性正常")

    if stats_df['entropy'].mean() < 3.0:
        print("❌ 严重问题: 预测熵过低，模型过于确定")
    elif stats_df['entropy'].mean() < 5.0:
        print("⚠️  问题: 预测熵较低")
    else:
        print("✅ 预测熵正常")

    if stats_df['max_prob'].mean() > 0.5:
        print("❌ 严重问题: 最大概率过高，模型过于确定")
    elif stats_df['max_prob'].mean() > 0.2:
        print("⚠️  问题: 最大概率较高")
    else:
        print("✅ 最大概率正常")

    return predictions, logits_stats, pred_counter

def analyze_input_sensitivity(model_path, codebook_path, num_tests=100):
    """分析模型对输入变化的敏感性"""
    print("\n=== 输入敏感性分析 ===")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 使用相同的加载逻辑
    try:
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint.get('config', {})
        state_dict = checkpoint['model_state_dict']
        is_enhanced = 'token_usage_count' in state_dict or 'prediction_history' in state_dict

        if is_enhanced and ENHANCED_MODEL_AVAILABLE:
            model = DiversityEnhancedCandlestickVQGPT(
                vocab_size=config.get('vocab_size', 518),
                code_size=config.get('code_size', 100),
                seq_len=config.get('seq_len', 30),
                n_layer=config.get('n_layer', 4),
                n_head=config.get('n_head', 8),
                d_model=config.get('d_model', 64),
                dropout=config.get('dropout', 0.1),
                use_time_features=config.get('use_time_features', True),
                n_time_features=config.get('n_time_features', 8),
                label_smoothing=config.get('label_smoothing', 0.0),
                use_auxiliary_loss=config.get('use_auxiliary_loss', True),
                diversity_weight=config.get('diversity_weight', 2.0),
                anti_collapse_weight=config.get('anti_collapse_weight', 3.0)
            )
            model.load_state_dict(state_dict)
        else:
            model = CandlestickVQGPT.from_pretrained(model_path, device=device)

        model = model.to(device)
        model.eval()

    except Exception as e:
        print(f"加载模型失败: {e}")
        return [0.0] * num_tests

    sensitivity_scores = []

    with torch.no_grad():
        for i in range(num_tests):
            # 创建基础输入
            seq_len = 20
            base_input = torch.randint(0, model.vocab_size, (1, seq_len), device=device)
            code_ids = torch.randint(0, 100, (1,), device=device)
            time_features = torch.randn(1, seq_len, 8, device=device) if model.use_time_features else None

            # 获取基础预测
            base_logits, _ = model(base_input, code_ids, time_features)
            base_pred = base_logits[0, -1, :].argmax().item()

            # 测试小幅修改输入
            different_preds = 0
            for j in range(10):
                # 随机修改一个token
                modified_input = base_input.clone()
                pos = np.random.randint(0, seq_len)
                modified_input[0, pos] = torch.randint(0, model.vocab_size, (1,), device=device)

                # 获取修改后的预测
                modified_logits, _ = model(modified_input, code_ids, time_features)
                modified_pred = modified_logits[0, -1, :].argmax().item()

                if modified_pred != base_pred:
                    different_preds += 1

            sensitivity = different_preds / 10.0
            sensitivity_scores.append(sensitivity)

    avg_sensitivity = np.mean(sensitivity_scores)
    print(f"平均输入敏感性: {avg_sensitivity:.4f}")

    if avg_sensitivity < 0.1:
        print("❌ 严重问题: 模型对输入变化不敏感")
    elif avg_sensitivity < 0.3:
        print("⚠️  问题: 模型敏感性较低")
    else:
        print("✅ 模型敏感性正常")

    return sensitivity_scores

def plot_analysis_results(predictions, logits_stats, pred_counter, save_dir):
    """绘制分析结果"""
    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # 1. 预测分布
    top_20 = dict(pred_counter.most_common(20))
    axes[0, 0].bar(range(len(top_20)), list(top_20.values()))
    axes[0, 0].set_title('Top 20 预测Token分布')
    axes[0, 0].set_xlabel('Token排名')
    axes[0, 0].set_ylabel('预测次数')

    # 2. 熵分布
    entropies = [stat['entropy'] for stat in logits_stats]
    axes[0, 1].hist(entropies, bins=50, alpha=0.7)
    axes[0, 1].axvline(np.mean(entropies), color='red', linestyle='--', label=f'均值: {np.mean(entropies):.2f}')
    axes[0, 1].set_title('预测熵分布')
    axes[0, 1].set_xlabel('熵')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].legend()

    # 3. 最大概率分布
    max_probs = [stat['max_prob'] for stat in logits_stats]
    axes[0, 2].hist(max_probs, bins=50, alpha=0.7)
    axes[0, 2].axvline(np.mean(max_probs), color='red', linestyle='--', label=f'均值: {np.mean(max_probs):.3f}')
    axes[0, 2].set_title('最大概率分布')
    axes[0, 2].set_xlabel('最大概率')
    axes[0, 2].set_ylabel('频次')
    axes[0, 2].legend()

    # 4. 预测频率累积分布
    freqs = list(pred_counter.values())
    freqs.sort(reverse=True)
    cumsum = np.cumsum(freqs) / sum(freqs)
    axes[1, 0].plot(cumsum)
    axes[1, 0].set_title('预测频率累积分布')
    axes[1, 0].set_xlabel('Token排名')
    axes[1, 0].set_ylabel('累积频率')
    axes[1, 0].grid(True)

    # 5. 熵 vs 最大概率
    axes[1, 1].scatter(entropies, max_probs, alpha=0.6)
    axes[1, 1].set_title('熵 vs 最大概率')
    axes[1, 1].set_xlabel('熵')
    axes[1, 1].set_ylabel('最大概率')
    axes[1, 1].grid(True)

    # 6. Logits标准差分布
    stds = [stat['std'] for stat in logits_stats]
    axes[1, 2].hist(stds, bins=50, alpha=0.7)
    axes[1, 2].axvline(np.mean(stds), color='red', linestyle='--', label=f'均值: {np.mean(stds):.3f}')
    axes[1, 2].set_title('Logits标准差分布')
    axes[1, 2].set_xlabel('标准差')
    axes[1, 2].set_ylabel('频次')
    axes[1, 2].legend()

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'diversity_analysis.png'), dpi=300, bbox_inches='tight')
    plt.show()

def generate_improvement_suggestions(predictions, logits_stats, pred_counter, sensitivity_scores):
    """生成改进建议"""
    print("\n=== 改进建议 ===")

    diversity_ratio = len(set(predictions)) / len(predictions)
    avg_entropy = np.mean([stat['entropy'] for stat in logits_stats])
    avg_max_prob = np.mean([stat['max_prob'] for stat in logits_stats])
    avg_sensitivity = np.mean(sensitivity_scores)

    suggestions = []

    if diversity_ratio < 0.1:
        suggestions.append("1. 增加多样性损失权重，当前预测过于集中")
        suggestions.append("2. 减少标签平滑，可能过度平滑了预测分布")
        suggestions.append("3. 增加模型容量（d_model, n_layer）")

    if avg_entropy < 3.0:
        suggestions.append("4. 增加熵正则化权重，鼓励更高的预测不确定性")
        suggestions.append("5. 使用温度缩放在推理时增加随机性")

    if avg_max_prob > 0.3:
        suggestions.append("6. 模型过于确定，考虑增加dropout或其他正则化")
        suggestions.append("7. 减少训练轮数，可能存在过拟合")

    if avg_sensitivity < 0.2:
        suggestions.append("8. 模型对输入不敏感，考虑减少模型容量或增加学习率")
        suggestions.append("9. 检查数据质量，可能存在重复或相似样本")

    # 检查最频繁的预测
    most_common_freq = pred_counter.most_common(1)[0][1] / len(predictions)
    if most_common_freq > 0.5:
        suggestions.append("10. 最频繁预测占比过高，需要强制多样性约束")
        suggestions.append("11. 考虑使用对抗训练或其他多样性增强技术")

    if not suggestions:
        suggestions.append("✅ 模型预测多样性表现良好，无需特别改进")

    for suggestion in suggestions:
        print(suggestion)

    return suggestions

def main():
    parser = argparse.ArgumentParser(description='分析CandlestickVQGPT预测多样性')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本路径')
    parser.add_argument('--num_samples', type=int, default=1000, help='分析样本数量')
    parser.add_argument('--save_dir', type=str, default='./diversity_analysis', help='结果保存目录')

    args = parser.parse_args()

    print("开始预测多样性深度分析...")

    # Token分布分析
    predictions, logits_stats, pred_counter = analyze_token_distribution(
        args.model_path, args.codebook_path, args.num_samples
    )

    # 输入敏感性分析
    sensitivity_scores = analyze_input_sensitivity(
        args.model_path, args.codebook_path, num_tests=100
    )

    # 绘制分析结果
    plot_analysis_results(predictions, logits_stats, pred_counter, args.save_dir)

    # 生成改进建议
    suggestions = generate_improvement_suggestions(
        predictions, logits_stats, pred_counter, sensitivity_scores
    )

    # 保存分析报告
    report_path = os.path.join(args.save_dir, 'diversity_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("CandlestickVQGPT 预测多样性分析报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"分析样本数: {args.num_samples}\n")
        f.write(f"唯一预测数: {len(set(predictions))}\n")
        f.write(f"多样性比例: {len(set(predictions)) / len(predictions):.4f}\n")
        f.write(f"平均熵: {np.mean([s['entropy'] for s in logits_stats]):.4f}\n")
        f.write(f"平均敏感性: {np.mean(sensitivity_scores):.4f}\n\n")
        f.write("改进建议:\n")
        for suggestion in suggestions:
            f.write(f"{suggestion}\n")

    print(f"\n分析报告已保存到: {report_path}")

if __name__ == "__main__":
    main()
