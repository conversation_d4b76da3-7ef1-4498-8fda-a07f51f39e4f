"""
PyTorch中实现一个完整的TCN（Temporal Convolutional Network）模型，
我们需要关注几个关键组件：因果卷积层、扩张卷积和残差连接。下面是一个
示例实现，包括这些关键组件的解释：

因果卷积（Causal Convolutions）：为了实现因果卷积，我们需要确保在
计算当前时间步的输出时，只使用当前和之前的时间步的信息。这可以通过在
卷积操作中添加适当的填充（padding）来实现。

扩张卷积（Dilated Convolutions）：扩张卷积通过间隔地采样输入数据来
扩大模型的感受野，从而使模型能够捕捉到更长范围内的依赖关系。

残差连接（Residual Connections）：残差连接有助于缓解梯度消失问题，
并且可以帮助模型在学习深层表示时保持信息的流动。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

import torch.nn as nn
from torch.nn.utils import weight_norm
 
 
class Crop(nn.Module):
 
    def __init__(self, crop_size):
        super(Crop, self).__init__()
        self.crop_size = crop_size
 
    def forward(self, x):
        return x[:, :, :-self.crop_size].contiguous()
 
 
class TemporalCasualLayer(nn.Module):
 
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, dropout = 0.2, activation="relu"):
        super(TemporalCasualLayer, self).__init__()
        padding = (kernel_size - 1) * dilation
        conv_params = {
            'kernel_size': kernel_size,
            'stride':      stride,
            'padding':     padding,
            'dilation':    dilation
        }
 
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, **conv_params))
        self.crop1 = Crop(padding)
        if activation == "relu":
            self.relu1 = nn.ReLU()
        elif activation == "gelu":
            self.relu1 = nn.GELU()
        elif activation == "prelu":
            self.relu1 = nn.PReLU()
        elif activation == "leakyrelu":
            self.relu1 = nn.LeakyReLU()
        else:
            raise Exception("activation must be relu or gelu etc.")
        self.dropout1 = nn.Dropout(dropout)
 
        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, **conv_params))
        self.crop2 = Crop(padding)
        if activation == "relu":
            self.relu2 = nn.ReLU()
        elif activation == "gelu":
            self.relu2 = nn.GELU()
        elif activation == "prelu":
            self.relu2 = nn.PReLU()
        elif activation == "leakyrelu":
            self.relu2 = nn.LeakyReLU()
        else:
            raise Exception("activation must be relu or gelu etc.")
        self.dropout2 = nn.Dropout(dropout)
 
        self.net = nn.Sequential(self.conv1, self.crop1, self.relu1, self.dropout1,
                                 self.conv2, self.crop2, self.relu2, self.dropout2)
        #shortcut connect
        self.bias = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        # self.init_weights() # 在交叉验证中，不要初始化权重

    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.bias is not None:
            self.bias.weight.data.normal_(0, 0.01)

    def forward(self, x):
        y = self.net(x)
        b = x if self.bias is None else self.bias(x)
        return self.relu(y + b)
 
 
class TemporalConvolutionNetwork(nn.Module):
 
    def __init__(self, num_inputs, num_channels, kernel_size = 2, dropout = 0.2, activation="relu"):
        super(TemporalConvolutionNetwork, self).__init__()
        layers = []
        num_levels = len(num_channels)
        tcl_param = {
            'kernel_size': kernel_size,
            'stride':      1,
            'dropout':     dropout
        }
        for i in range(num_levels):
            dilation = 2**i
            in_ch = num_inputs if i == 0 else num_channels[i - 1]
            out_ch = num_channels[i]
            tcl_param['dilation'] = dilation
            tcl = TemporalCasualLayer(in_ch, out_ch, **tcl_param, activation=activation)
            layers.append(tcl)
 
        self.network = nn.Sequential(*layers)
 
    def forward(self, x):
        return self.network(x)
 
 
class TemporalConvNet(nn.Module):
 
    def __init__(self,
                 # input_size,
                 # output_size,
                 kernel_size=3,
                 dropout=0.2,
                 out_channels=(16, 32, 64),
                 num_embeds=[72],
                 ins_nums=(0,51,51,0),
                 activation="relu",
                 ):
        super(TemporalConvNet, self).__init__()

        print(num_embeds, dropout, out_channels, ins_nums)
        assert len(ins_nums) == 4
        num_dims = []
        for num_embed in num_embeds:
            if num_embed == 0:
                continue
            num_dims.append(math.ceil(np.sqrt(num_embed)))
        self.embedding_layers = nn.ModuleList()
        for i in range(len(num_dims)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i]))
        input_size = sum(ins_nums) + sum(num_dims)
        output_size = 1
        self.tcn = TemporalConvolutionNetwork(input_size,
                                              out_channels,
                                              kernel_size = kernel_size,
                                              dropout = dropout,
                                              activation=activation
                                             )
        self.linear = nn.Linear(out_channels[-1], output_size)
 
    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        # assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        if len(self.embedding_layers) > 0:
            embedded_data = None
            n = 0
            if code_ids.shape[-1] > len(self.embedding_layers):
                # 当对单个合约进行预测时，跳过对合约代码的嵌入特征
                n = code_ids.shape[-1] - len(self.embedding_layers)
            for i in range(len(self.embedding_layers)):
                category_data = self.embedding_layers[i](code_ids[:, :, i+n])
                if embedded_data is None:
                    embedded_data = category_data
                else:
                    embedded_data = torch.cat([embedded_data, category_data], dim=-1)
        
            x = torch.cat([x, embedded_data], dim=-1)

        # x = x.permute(0, 2, 1)
        x = x.transpose(1, 2)

        y = self.tcn(x)#[N,C_out,L_out=L_in]
        return self.linear(y[:, :, -1]).view(-1)
    

