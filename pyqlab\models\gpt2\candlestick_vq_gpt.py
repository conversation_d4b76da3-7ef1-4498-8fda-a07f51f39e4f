"""
CandlestickVQGPT 模型

基于最新GPT架构的K线预测模型，结合VQ-VAE的tokenization方法，针对金融时间序列数据优化。
主要特点：
1. 使用CandlestickVQTokenizer进行K线数据的tokenization
2. 添加证券代码维度的嵌入
3. 添加时间维度的嵌入，支持多种时间特征编码方法
4. 使用RMSNorm替代LayerNorm，提高训练稳定性
5. 实现旋转位置编码(RoPE)，更好地处理序列位置信息
6. 使用现代化残差连接
7. 优化的权重初始化
8. 支持标签平滑和辅助损失函数
9. 实现自适应学习率调整
"""

import math
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Any, Callable
import onnx
import onnxruntime as ort
from torch.onnx import export

class RMSNorm(nn.Module):
    """
    RMSNorm层
    使用均方根归一化进行层归一化，比传统LayerNorm更稳定。
    """
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        return self._norm(x) * self.weight

class RotaryEmbedding(nn.Module):
    """
    RoPE（Rotary Position Embedding）
    使用旋转位置嵌入进行位置编码，更好地处理序列位置信息。
    """
    def __init__(self, dim, max_position_embeddings=2048, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(self.max_seq_len_cached, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x, seq_len=None):
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len
            t = torch.arange(self.max_seq_len_cached, device=x.device).type_as(self.inv_freq)
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)
            self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
            self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

def rotate_half(x):
    """将输入向量的后半部分旋转到前半部分"""
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def apply_rotary_pos_emb(q, k, cos, sin):
    """应用旋转位置编码到查询和键"""
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed

class MLP(nn.Module):
    """
    多层感知机
    包含两个线性层和一个GELU激活函数。
    """
    def __init__(self, dim, hidden_dim, dropout=0.1):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim),
            nn.Dropout(dropout),
        )

    def forward(self, x):
        return self.net(x)

class Attention(nn.Module):
    """
    多头注意力机制
    支持Flash Attention加速。
    """
    def __init__(self, dim, n_heads, dropout=0.1, bias=False):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        assert self.head_dim * n_heads == dim, "dim must be divisible by n_heads"

        # 查询、键、值的线性投影
        self.wq = nn.Linear(dim, dim, bias=bias)
        self.wk = nn.Linear(dim, dim, bias=bias)
        self.wv = nn.Linear(dim, dim, bias=bias)
        self.proj = nn.Linear(dim, dim, bias=bias)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)

        # 检测是否支持Flash Attention
        self.flash = hasattr(torch.nn.functional, 'scaled_dot_product_attention')

    def forward(self, x, rotary_emb=None):
        B, T, C = x.size() # 批大小，序列长度，嵌入维度

        # 计算query, key, values
        q, k, v = self.wq(x), self.wk(x), self.wv(x)
        q = q.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2) # (B, nh, T, hs)
        k = k.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2) # (B, nh, T, hs)
        v = v.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2) # (B, nh, T, hs)

        # 应用旋转位置嵌入
        if rotary_emb is not None:
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)

        # 注意力计算
        if self.flash:
            # 使用Flash Attention
            y = torch.nn.functional.scaled_dot_product_attention(
                q, k, v, attn_mask=None, dropout_p=self.attn_dropout.p if self.training else 0, is_causal=True
            )
        else:
            # 手动实现注意力
            att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
            att = att.masked_fill(torch.triu(torch.ones(T, T, device=x.device), diagonal=1).bool().unsqueeze(0).unsqueeze(0), float('-inf'))
            att = F.softmax(att, dim=-1)
            att = self.attn_dropout(att)
            y = att @ v # (B, nh, T, T) x (B, nh, T, hs) -> (B, nh, T, hs)

        y = y.transpose(1, 2).contiguous().view(B, T, C) # 重组所有头的输出
        y = self.resid_dropout(self.proj(y))
        return y

class Block(nn.Module):
    """
    Transformer Block
    包含RMSNorm、注意力机制和MLP。
    """
    def __init__(self, dim, n_heads, dropout=0.1):
        super().__init__()
        self.ln_1 = RMSNorm(dim)
        self.attn = Attention(dim, n_heads, dropout)
        self.ln_2 = RMSNorm(dim)
        self.mlp = MLP(dim, 4 * dim, dropout)

    def forward(self, x, rotary_emb=None):
        x = x + self.attn(self.ln_1(x), rotary_emb)
        x = x + self.mlp(self.ln_2(x))
        return x

class TimeFeatureEmbedding(nn.Module):
    """
    时间特征嵌入
    将时间特征转换为嵌入向量。
    """
    def __init__(self, d_model, n_time_features=8):
        super().__init__()
        self.time_proj = nn.Linear(n_time_features, d_model)

    def forward(self, time_features):
        """
        Args:
            time_features: [batch_size, seq_len, n_time_features]
        """
        return self.time_proj(time_features)

class CandlestickVQGPT(nn.Module):
    """
    CandlestickVQGPT模型
    基于最新GPT架构的K线预测模型，结合VQ-VAE的tokenization方法，针对金融时间序列数据优化。
    """
    def __init__(self,
                 vocab_size: int,
                 code_size: int,
                 seq_len: int = 64,
                 n_layer: int = 8,
                 n_head: int = 8,
                 d_model: int = 128,
                 dropout: float = 0.1,
                 bias: bool = False,
                 use_time_features: bool = True,
                 n_time_features: int = 8,
                 label_smoothing: float = 0.1,
                 use_auxiliary_loss: bool = True):
        """
        初始化CandlestickVQGPT模型

        Args:
            vocab_size: 词汇表大小
            code_size: 证券代码数量
            seq_len: 最大序列长度
            n_layer: Transformer层数
            n_head: 注意力头数
            d_model: 模型维度
            dropout: Dropout比例
            bias: 是否使用偏置
            use_time_features: 是否使用时间特征
            n_time_features: 时间特征数量
            label_smoothing: 标签平滑系数
            use_auxiliary_loss: 是否使用辅助损失
        """
        super().__init__()
        self.seq_len = seq_len
        self.vocab_size = vocab_size
        self.code_size = code_size
        self.use_time_features = use_time_features
        self.d_model = d_model
        self.n_head = n_head
        self.n_layer = n_layer
        self.label_smoothing = label_smoothing
        self.use_auxiliary_loss = use_auxiliary_loss

        # 各种嵌入
        self.token_embedding = nn.Embedding(vocab_size, d_model)
        self.code_embedding = nn.Embedding(code_size, d_model)
        self.rotary_emb = RotaryEmbedding(d_model // n_head)

        if use_time_features:
            self.time_embedding = TimeFeatureEmbedding(d_model, n_time_features)

        self.dropout = nn.Dropout(dropout)

        # Transformer块
        self.blocks = nn.ModuleList([Block(d_model, n_head, dropout) for _ in range(n_layer)])

        # 输出层
        self.ln_f = RMSNorm(d_model)
        self.head = nn.Linear(d_model, vocab_size, bias=False)

        # 初始化权重
        self.apply(self._init_weights)

        # 更好地初始化最终层 - 使用更小的初始化避免预测偏向
        with torch.no_grad():
            # 使用均匀分布初始化输出层，避免某些token被偏向
            nn.init.uniform_(self.head.weight, -0.01, 0.01)
            # 添加小的随机偏置，打破对称性
            if hasattr(self.head, 'bias') and self.head.bias is not None:
                nn.init.uniform_(self.head.bias, -0.001, 0.001)

        print(f"CandlestickVQGPT模型参数数量: {self.get_num_params():,}")

    def get_num_params(self):
        """获取模型参数数量"""
        return sum(p.numel() for p in self.parameters())

    def _init_weights(self, module):
        """改进的权重初始化方法"""
        if isinstance(module, nn.Linear):
            # 使用Xavier初始化，更适合深度网络
            if hasattr(module, 'weight') and module.weight is not None:
                torch.nn.init.xavier_uniform_(module.weight, gain=1.0)
            if hasattr(module, 'bias') and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            # 嵌入层使用较小的标准差
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.01)
        elif isinstance(module, RMSNorm):
            # RMSNorm的权重初始化为1
            if hasattr(module, 'weight') and module.weight is not None:
                torch.nn.init.ones_(module.weight)

    def forward(self, input_tokens, code_ids, time_features=None, targets=None, class_weights=None, **kwargs):
        """
        前向传播

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            targets: 目标token序列 [batch_size, seq_len]
            class_weights: 类别权重 [vocab_size] 用于处理token不平衡
            **kwargs: 其他参数

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size]
            loss: 损失值 (如果提供了targets)
        """
        batch_size, seq_len = input_tokens.size()
        # assert seq_len <= self.seq_len, f"输入序列长度{seq_len}超过了最大长度{self.seq_len}"

        # 获取token嵌入
        token_emb = self.token_embedding(input_tokens)  # [batch_size, seq_len, d_model]

        # 获取证券代码嵌入并扩展到序列长度
        code_emb = self.code_embedding(code_ids).unsqueeze(1).expand(-1, seq_len, -1)  # [batch_size, seq_len, d_model]

        # 组合嵌入 - 使用更小的系数避免梯度爆炸
        x = token_emb + 0.1 * code_emb

        # 添加时间特征嵌入
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + 0.1 * time_emb

        x = self.dropout(x)

        # 获取旋转位置编码
        cos, sin = self.rotary_emb(x, seq_len=seq_len)
        rotary_emb = (cos, sin)

        # 通过Transformer块
        for block in self.blocks:
            x = block(x, rotary_emb)

        # 最终层归一化和输出投影
        x = self.ln_f(x)

        # 计算损失
        if targets is not None:
            logits = self.head(x)

            # 检查logits是否包含NaN或无穷值
            if torch.isnan(logits).any() or torch.isinf(logits).any():
                print("警告: logits包含NaN或无穷值")
                return logits, torch.tensor(float('inf'), device=logits.device)

            # 计算基础损失
            loss = self._compute_balanced_loss(logits, targets, class_weights)

            # 改进的辅助损失 - 更强的多样性约束
            if self.use_auxiliary_loss:
                aux_loss = self._compute_auxiliary_loss(logits, targets)
                loss = loss + aux_loss

            return logits, loss
        else:
            logits = self.head(x[:, [-1], :])
            return logits, None

    def _compute_balanced_loss(self, logits, targets, class_weights=None):
        """
        计算平衡的损失函数，处理token分布不均衡问题

        Args:
            logits: 模型输出 [batch_size, seq_len, vocab_size]
            targets: 目标token [batch_size, seq_len]
            class_weights: 类别权重 [vocab_size]

        Returns:
            loss: 平衡后的损失值
        """
        # 展平logits和targets
        logits_flat = logits.view(-1, logits.size(-1))
        targets_flat = targets.view(-1)

        # 过滤有效的token（非-1）
        valid_mask = targets_flat != -1
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)

        valid_logits = logits_flat[valid_mask]
        valid_targets = targets_flat[valid_mask]

        # 1. 基础交叉熵损失
        if class_weights is not None:
            # 使用类别权重的交叉熵损失
            loss = F.cross_entropy(
                valid_logits,
                valid_targets,
                weight=class_weights.to(logits.device),
                label_smoothing=self.label_smoothing * 0.3  # 减少标签平滑
            )
        else:
            # 标准交叉熵损失
            loss = F.cross_entropy(
                valid_logits,
                valid_targets,
                label_smoothing=self.label_smoothing * 0.3
            )

        # 2. Focal Loss - 专门处理类别不平衡
        alpha = 0.25
        gamma = 2.0

        # 计算focal loss
        ce_loss = F.cross_entropy(valid_logits, valid_targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = alpha * (1 - pt) ** gamma * ce_loss
        focal_loss = focal_loss.mean()

        # 组合损失：70% 加权交叉熵 + 30% Focal Loss
        combined_loss = 0.7 * loss + 0.3 * focal_loss

        return combined_loss

    def _compute_auxiliary_loss(self, logits, targets):
        """
        计算辅助损失，增强预测多样性

        Args:
            logits: 模型输出 [batch_size, seq_len, vocab_size]
            targets: 目标token [batch_size, seq_len]

        Returns:
            aux_loss: 辅助损失值
        """
        logits_flat = logits.view(-1, logits.size(-1))
        targets_flat = targets.view(-1)
        valid_mask = targets_flat != -1

        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device)

        valid_logits = logits_flat[valid_mask]
        valid_targets = targets_flat[valid_mask]

        aux_loss = 0.0

        # 1. 预测多样性损失 - 强化版本
        pred_tokens = valid_logits.argmax(dim=-1)
        unique_preds = torch.unique(pred_tokens).numel()
        total_preds = pred_tokens.numel()
        diversity_ratio = unique_preds / total_preds

        # 更严格的多样性要求
        target_diversity = 0.4  # 期望至少40%的预测是不同的
        if diversity_ratio < target_diversity:
            diversity_penalty = (target_diversity - diversity_ratio) * 3.0
            aux_loss += diversity_penalty

        # 2. 增强的熵正则化
        probs = F.softmax(valid_logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1).mean()

        # 动态目标熵：根据词汇表大小调整
        target_entropy = torch.log(torch.tensor(self.vocab_size / 3.0, device=logits.device))
        entropy_loss = F.mse_loss(entropy, target_entropy) * 0.15
        aux_loss += entropy_loss

        # 3. 反频率损失 - 强化版本
        pred_counts = torch.bincount(pred_tokens, minlength=self.vocab_size)
        pred_freqs = pred_counts.float() / pred_counts.sum()

        # 计算频率的基尼系数，惩罚分布不均匀
        sorted_freqs, _ = torch.sort(pred_freqs)
        n = len(sorted_freqs)
        index = torch.arange(1, n + 1, device=logits.device).float()
        gini = (2 * torch.sum(index * sorted_freqs)) / (n * torch.sum(sorted_freqs)) - (n + 1) / n

        # 惩罚高基尼系数（不均匀分布）
        gini_penalty = torch.clamp(gini - 0.3, min=0) * 1.0
        aux_loss += gini_penalty

        # 4. 特定高频token惩罚
        # 如果某个token预测频率超过30%，额外惩罚
        max_freq = torch.max(pred_freqs)
        if max_freq > 0.3:
            high_freq_penalty = (max_freq - 0.3) * 2.0
            aux_loss += high_freq_penalty

        # 5. 对比损失 - 鼓励不同输入产生不同输出
        if valid_logits.size(0) > 1:
            # 计算logits之间的余弦相似度
            logits_norm = F.normalize(valid_logits, p=2, dim=1)
            similarity_matrix = torch.mm(logits_norm, logits_norm.t())

            # 移除对角线元素
            mask = torch.eye(similarity_matrix.size(0), device=similarity_matrix.device).bool()
            similarity_matrix = similarity_matrix.masked_fill(mask, 0)

            # 惩罚过高的相似度
            high_sim_penalty = torch.clamp(similarity_matrix - 0.7, min=0).mean() * 0.3
            aux_loss += high_sim_penalty

        return aux_loss

    @torch.no_grad()
    def generate(self,
                input_tokens,
                code_ids,
                time_features=None,
                max_new_tokens=10,
                temperature=1.0,
                top_k=None,
                **kwargs):
        """
        生成新的token序列

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            max_new_tokens: 生成的最大新token数量
            temperature: 温度参数，控制采样的随机性
            top_k: 只考虑概率最高的前k个token
            **kwargs: 其他参数

        Returns:
            generated_tokens: 生成的token序列 [batch_size, seq_len + max_new_tokens]
        """
        batch_size, seq_len = input_tokens.size()
        device = input_tokens.device

        # 复制输入以避免修改原始数据
        tokens = input_tokens.clone()

        # 准备时间特征
        if self.use_time_features and time_features is not None:
            # 如果需要，扩展时间特征以容纳新生成的token
            extended_time_features = torch.cat([
                time_features,
                torch.zeros(batch_size, max_new_tokens, time_features.size(2), device=device)
            ], dim=1)
        else:
            extended_time_features = None

        # 生成新token
        for i in range(max_new_tokens):
            # 获取当前序列长度
            curr_seq_len = tokens.size(1)

            # 如果序列长度超过seq_len，截断序列
            if curr_seq_len > self.seq_len:
                tokens = tokens[:, -self.seq_len:]
                curr_seq_len = self.seq_len
                if extended_time_features is not None:
                    extended_time_features = extended_time_features[:, -self.seq_len:]

            # 获取当前时间特征
            curr_time_features = extended_time_features[:, :curr_seq_len] if extended_time_features is not None else None

            # 前向传播获取logits
            logits, _ = self.forward(tokens, code_ids, curr_time_features)

            # 只关注最后一个时间步的logits
            logits = logits[:, -1, :] / (temperature if temperature > 0 else 1.0)

            # 可选的top-k采样
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')

            # 应用softmax获取概率分布
            probs = F.softmax(logits, dim=-1)

            # 采样下一个token
            next_token = torch.multinomial(probs, num_samples=1)

            # 将新token添加到序列中
            tokens = torch.cat([tokens, next_token], dim=1)

            # 更新时间特征（如果需要）
            # 这里假设时间特征是按顺序排列的，可以简单地复制最后一个时间步的特征
            # 在实际应用中，可能需要更复杂的逻辑来更新时间特征
            if extended_time_features is not None and i < max_new_tokens - 1:
                if curr_seq_len < extended_time_features.size(1):
                    # 使用预先准备的时间特征
                    pass
                else:
                    # 复制最后一个时间步的特征
                    last_time_feature = extended_time_features[:, -1:, :]
                    extended_time_features = torch.cat([extended_time_features, last_time_feature], dim=1)

        return tokens

    def to_onnx(self, path, input_shape=(1, 30), code_shape=(1,), time_shape=None, dynamic_axes=None):
        """
        将模型导出为ONNX格式

        Args:
            path: 保存路径
            input_shape: 输入token形状 (batch_size, seq_len)
            code_shape: 证券代码形状 (batch_size,)
            time_shape: 时间特征形状 (batch_size, seq_len, n_time_features)
            dynamic_axes: 动态轴配置

        Returns:
            str: 保存的ONNX文件路径
        """
        try:
            # 设置为评估模式
            self.eval()

            # 准备示例输入 - 使用int32类型以确保C++兼容性
            dummy_input = torch.randint(0, self.vocab_size, input_shape, dtype=torch.int32)
            dummy_code = torch.randint(0, self.code_size, code_shape, dtype=torch.int32)

            if self.use_time_features and time_shape is not None:
                # 时间特征使用float32类型
                dummy_time = torch.rand(time_shape, dtype=torch.float32)
                inputs = (dummy_input, dummy_code, dummy_time)
            else:
                dummy_time = None
                inputs = (dummy_input, dummy_code)

            # 设置动态轴
            if dynamic_axes is None:
                dynamic_axes = {
                    'input_tokens': {0: 'batch_size', 1: 'seq_len'},
                    'code_ids': {0: 'batch_size'},
                    'output': {0: 'batch_size', 1: 'seq_len'}
                }
                if self.use_time_features and time_shape is not None:
                    dynamic_axes['time_features'] = {0: 'batch_size', 1: 'seq_len'}

            # 创建一个模块化的前向传播函数
            class ForwardModule(torch.nn.Module):
                def __init__(self, model):
                    super().__init__()
                    self.model = model

                def forward(self, *inputs):
                    if len(inputs) == 3:
                        input_tokens, code_ids, time_features = inputs
                        logits, _ = self.model(input_tokens, code_ids, time_features)
                    else:
                        input_tokens, code_ids = inputs
                        logits, _ = self.model(input_tokens, code_ids)
                    # 确保输出是float32类型，避免C++中的精度问题
                    return logits.to(torch.float32)

            # 创建前向传播模块
            forward_module = ForwardModule(self)

            # 导出模型
            torch.onnx.export(
                forward_module,
                inputs,
                path,
                export_params=True,
                opset_version=14,
                do_constant_folding=True,
                input_names=['input_tokens', 'code_ids'] + (['time_features'] if dummy_time is not None else []),
                output_names=['output'],
                dynamic_axes=dynamic_axes
            )

            print(f"模型已导出到 {path}")

            # 验证导出的模型
            import onnx
            onnx_model = onnx.load(path)
            onnx.checker.check_model(onnx_model)
            print("ONNX模型验证通过")

            # 验证模型输出类型
            print("验证模型输入输出类型:")
            for input in onnx_model.graph.input:
                print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
            for output in onnx_model.graph.output:
                print(f"输出 {output.name}: {output.type.tensor_type.elem_type}")

            return path
        except Exception as e:
            print(f"导出模型为ONNX格式时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    @classmethod
    def from_pretrained(cls, model_path, device='cpu'):
        """
        从预训练模型加载

        Args:
            model_path: 模型路径
            device: 设备

        Returns:
            model: 加载的模型
        """
        checkpoint = torch.load(model_path, map_location=device)

        # 获取模型配置
        config = checkpoint.get('config', {})

        # 创建模型
        model = cls(
            vocab_size=config.get('vocab_size', 1030),
            code_size=config.get('code_size', 100),
            seq_len=config.get('seq_len', 30),
            n_layer=config.get('n_layer', 4),
            n_head=config.get('n_head', 8),
            d_model=config.get('d_model', 16),
            dropout=config.get('dropout', 0.1),
            bias=config.get('bias', False),
            use_time_features=config.get('use_time_features', True),
            n_time_features=config.get('n_time_features', 8),
            label_smoothing=config.get('label_smoothing', 0.1),
            use_auxiliary_loss=config.get('use_auxiliary_loss', True)
        )

        # 加载模型权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(device)
        model.eval()

        return model

    # 第二个to_onnx方法已被移除，避免方法重复定义导致的问题
