# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# 树形模型，包括GBDT、XGBoost、LightGBM等
# 特征数据可以是任意类型，但是标签数据必须是数值型，且只能有一个标签，不用做归一化处理
import numpy as np
import pandas as pd
import lightgbm as lgb
import xgboost as xgb
from typing import Text, Union
from qlib.model.base import ModelFT
from qlib.model.interpret.base import LightGBMFInt
from sklearn.metrics import roc_auc_score
from qlib.data.dataset import DatasetH


class LGBModel(ModelFT, LightGBMFInt):
    """LightGBM Model"""

    def __init__(self, objective="binary", early_stopping_rounds=50, **kwargs):
        if objective not in {"regression", "mse", "binary"}:
            raise NotImplementedError
        self.params = {"objective": objective, "verbose": -1}
        self.params.update(kwargs)
        self.early_stopping_rounds = early_stopping_rounds
        self.model = None


    def fit(
        self,
        train_data: lgb.Dataset,
        valid_data: lgb.Dataset,
        num_boost_round=1000,
        early_stopping_rounds=None,
        verbose_eval=20,
        evals_result=dict(),
        save_path=None,
        **kwargs
    ):

        self.model = lgb.train(
            self.params,
            train_data,
            num_boost_round=num_boost_round,
            valid_sets=[train_data, valid_data],
            valid_names=["train", "valid"],
            early_stopping_rounds=10, #(self.early_stopping_rounds if early_stopping_rounds is None else early_stopping_rounds),
            verbose_eval=verbose_eval,
            evals_result=evals_result,
            **kwargs
        )
        evals_result["train"] = list(evals_result["train"].values())[0]
        evals_result["valid"] = list(evals_result["valid"].values())[0]
        if save_path:
            self.model.save_model(save_path)
        return np.max(evals_result["train"]), np.max(evals_result["valid"])

    def test(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        if self.model is None:
            raise ValueError("model is not fitted yet!")
        x_test = dataset.prepare(segment, col_set=["feature", "label", "encoded"], data_key=None)
        x_data = pd.concat([x_test["feature"], x_test["encoded"]], axis=1)

        preds = np.array(self.model.predict(x_data.values))
        valid_auc = roc_auc_score(y_score=preds, y_true=np.squeeze(x_test["label"].values))
        return valid_auc

    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        if self.model is None:
            raise ValueError("model is not fitted yet!")
        x_test = dataset.prepare(segment, col_set=["feature", "label", "encoded"], data_key=None)
        x_data = pd.concat([x_test["feature"], x_test["encoded"]], axis=1)
        return pd.Series(self.model.predict(x_data.values), index=x_test.index)

    def finetune(self, dataset: DatasetH, num_boost_round=10, verbose_eval=20):
        """
        finetune model

        Parameters
        ----------
        dataset : DatasetH
            dataset for finetuning
        num_boost_round : int
            number of round to finetune model
        verbose_eval : int
            verbose level
        """
        # Based on existing model and finetune by train more rounds
        dtrain, _ = self._prepare_data(dataset)
        if dtrain.empty:
            raise ValueError("Empty data from dataset, please check your dataset config.")
        self.model = lgb.train(
            self.params,
            dtrain,
            num_boost_round=num_boost_round,
            init_model=self.model,
            valid_sets=[dtrain],
            valid_names=["train"],
            verbose_eval=verbose_eval,
        )
