"""
增强版K线数据离散化与LLM预测模型示例
"""

import os
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import argparse
import json
from datetime import datetime

from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset, TimeSeriesCandlestickDataset
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.multi_timeframe_tokenizer import MultiTimeframeTokenizer
from pyqlab.models.gpt2.bak.data_augmentation import CandlestickDataAugmentation

def load_sample_data(file_path=None):
    """
    加载示例数据，如果没有提供文件路径，则生成随机数据
    """
    if file_path and os.path.exists(file_path):
        # 加载真实数据
        df = pd.read_csv(file_path)
        # 确保列名正确
        if 'date' in df.columns and 'datetime' not in df.columns:
            df.rename(columns={'date': 'datetime'}, inplace=True)
        return df
    else:
        # 生成随机数据
        print("未找到数据文件，生成随机数据...")
        np.random.seed(42)
        n_samples = 500
        
        # 生成日期时间
        start_date = pd.Timestamp('2020-01-01')
        dates = [start_date + pd.Timedelta(days=i) for i in range(n_samples)]
        
        # 生成价格
        close = np.random.normal(loc=100, scale=1, size=n_samples).cumsum() + 1000
        daily_volatility = 0.01
        
        high = close * (1 + np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        low = close * (1 - np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        open_price = low + (high - low) * np.random.random(size=n_samples)
        volume = np.random.normal(loc=1000000, scale=200000, size=n_samples).clip(100000, None)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
        
        return df

def demo_enhanced_tokenizer(df):
    """
    演示增强版tokenizer的功能
    """
    print("\n=== 演示增强版Tokenizer ===")
    
    # 创建带有交易量特征的tokenizer
    tokenizer = CandlestickTokenizer(
        change_range=(-15, 15),
        entity_range=(-15, 15),
        shadow_range=(0, 10),
        volume_range=(-9, 9),
        atr_window=100,
        atr_mult=0.88,
        scale=10,
        include_volume=True,
        detect_anomalies=True,
        anomaly_threshold=7.0
    )
    
    # 将K线数据转换为token
    tokens = tokenizer.tokenize(df)
    
    print(f"生成的token数量: {len(tokens)}")
    print(f"词汇表大小: {tokenizer.vocab_size}")
    
    # 可视化tokenization结果
    tokenizer.visualize_tokenization(df, tokens, show_volume=True)
    
    return tokenizer, tokens

def demo_multi_timeframe(df):
    """
    演示多时间框架功能
    """
    print("\n=== 演示多时间框架Tokenizer ===")
    
    # 创建基础tokenizer
    base_tokenizer = CandlestickTokenizer(
        change_range=(-15, 15),
        entity_range=(-15, 15),
        shadow_range=(0, 10),
        include_volume=True
    )
    
    # 创建多时间框架tokenizer
    mtf_tokenizer = MultiTimeframeTokenizer(
        base_tokenizer=base_tokenizer,
        timeframes=['5m', '15m', '1h', '4h', '1d']
    )
    
    # 对多个时间框架进行tokenize
    tokens_dict = mtf_tokenizer.tokenize_multi_timeframe(df)
    
    # 打印每个时间框架的token数量
    for tf, tokens in tokens_dict.items():
        print(f"时间框架 {tf}: {len(tokens)}个tokens")
        
    # 组合tokens
    combined_tokens = mtf_tokenizer.combine_tokens(tokens_dict, method='concat')
    print(f"组合后的token数量: {len(combined_tokens)}")
    
    # 可视化多时间框架
    mtf_tokenizer.visualize_multi_timeframe(df, tokens_dict)
    
    return mtf_tokenizer, tokens_dict, combined_tokens

def demo_data_augmentation(df):
    """
    演示数据增强功能
    """
    print("\n=== 演示数据增强 ===")
    
    # 创建tokenizer
    tokenizer = CandlestickTokenizer(
        include_volume=True
    )
    
    # 创建数据增强器
    augmenter = CandlestickDataAugmentation(tokenizer)
    
    # 演示时间扭曲
    print("\n--- 时间扭曲 ---")
    time_warped_df = augmenter.time_warp(df, warp_factor=0.2)
    augmenter.visualize_augmentation(df, time_warped_df, method='time_warp')
    
    # 演示幅度扭曲
    print("\n--- 幅度扭曲 ---")
    magnitude_warped_df = augmenter.magnitude_warp(df, warp_factor=0.2)
    augmenter.visualize_augmentation(df, magnitude_warped_df, method='magnitude_warp')
    
    # 演示抖动
    print("\n--- 抖动 ---")
    jittered_df = augmenter.jitter(df, jitter_factor=0.01)
    augmenter.visualize_augmentation(df, jittered_df, method='jitter')
    
    # 演示缩放
    print("\n--- 缩放 ---")
    scaled_df = augmenter.scaling(df, scale_factor=1.2)
    augmenter.visualize_augmentation(df, scaled_df, method='scaling')
    
    # 演示窗口切片
    print("\n--- 窗口切片 ---")
    sliced_df = augmenter.window_slice(df, slice_ratio=0.8)
    augmenter.visualize_augmentation(df, sliced_df, method='window_slice')
    
    # 生成增强数据集
    print("\n--- 生成增强数据集 ---")
    augmented_dfs = augmenter.generate_augmented_dataset(df, n_augmentations=5)
    print(f"生成了 {len(augmented_dfs)} 个数据集")
    
    # 对原始数据和增强数据进行tokenize并比较
    print("\n--- Tokenize比较 ---")
    original_tokens, augmented_tokens = augmenter.tokenize_and_compare(df, augmented_dfs[1])
    
    return augmenter, augmented_dfs

def demo_anomaly_detection(df):
    """
    演示异常检测功能
    """
    print("\n=== 演示异常检测 ===")
    
    # 创建带有异常检测的tokenizer
    tokenizer = CandlestickTokenizer(
        detect_anomalies=True,
        anomaly_threshold=2.5  # 较低的阈值，以便检测更多异常
    )
    
    # 添加一些人工异常
    df_with_anomalies = df.copy()
    
    # 在随机位置添加价格异常
    np.random.seed(42)
    anomaly_indices = np.random.choice(range(10, len(df) - 10), size=5, replace=False)
    
    for idx in anomaly_indices:
        # 价格突然上涨或下跌
        multiplier = np.random.choice([0.8, 1.2])
        df_with_anomalies.loc[df.index[idx], 'close'] *= multiplier
        df_with_anomalies.loc[df.index[idx], 'high'] *= multiplier
        df_with_anomalies.loc[df.index[idx], 'low'] *= multiplier
        df_with_anomalies.loc[df.index[idx], 'open'] *= multiplier
        
        # 交易量异常
        df_with_anomalies.loc[df.index[idx], 'volume'] *= 3
        
    # 检测异常
    df_detected = tokenizer.detect_anomalies(df_with_anomalies)
    
    # 打印异常统计
    anomaly_count = df_detected['is_anomaly'].sum()
    print(f"检测到 {anomaly_count} 个异常值 ({anomaly_count/len(df_detected):.2%})")
    
    # 修正异常
    df_corrected = tokenizer.correct_anomalies(df_detected, method='interpolate')
    
    # 可视化原始数据、异常数据和修正后的数据
    fig, axes = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
    
    # 绘制原始数据
    ax1 = axes[0]
    ax1.set_title('原始数据')
    for i in range(len(df)):
        x = i
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'
        ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)
        
    # 绘制异常数据
    ax2 = axes[1]
    ax2.set_title('包含异常的数据')
    for i in range(len(df_with_anomalies)):
        x = i
        open_price = df_with_anomalies['open'].iloc[i]
        close_price = df_with_anomalies['close'].iloc[i]
        high_price = df_with_anomalies['high'].iloc[i]
        low_price = df_with_anomalies['low'].iloc[i]
        
        # 使用不同颜色标记异常
        if i in anomaly_indices:
            color = 'blue'
            ax2.scatter(x, close_price, color='blue', s=100, marker='o')
        else:
            color = 'red' if close_price >= open_price else 'green'
            
        ax2.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        ax2.plot([x, x], [low_price, high_price], color=color, linewidth=1)
        
    # 绘制修正后的数据
    ax3 = axes[2]
    ax3.set_title('修正后的数据')
    for i in range(len(df_corrected)):
        x = i
        open_price = df_corrected['open'].iloc[i]
        close_price = df_corrected['close'].iloc[i]
        high_price = df_corrected['high'].iloc[i]
        low_price = df_corrected['low'].iloc[i]
        
        # 使用不同颜色标记修正的异常
        if i in anomaly_indices:
            color = 'purple'
            ax3.scatter(x, close_price, color='purple', s=100, marker='x')
        else:
            color = 'red' if close_price >= open_price else 'green'
            
        ax3.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        ax3.plot([x, x], [low_price, high_price], color=color, linewidth=1)
        
    # 添加网格线
    for ax in axes:
        ax.grid(True, linestyle='--', alpha=0.7)
        
    plt.tight_layout()
    plt.show()
    
    return tokenizer, df_with_anomalies, df_detected, df_corrected

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='增强版K线数据离散化与LLM预测模型示例')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, default=None, help='数据文件路径')
    parser.add_argument('--demo', type=str, default='all', 
                       choices=['all', 'tokenizer', 'multi_timeframe', 'data_augmentation', 'anomaly_detection'],
                       help='要运行的演示')
    
    args = parser.parse_args()
    
    # 加载数据
    df = load_sample_data(args.data_path)
    print(f'数据形状: {df.shape}')
    
    # 运行演示
    if args.demo in ['all', 'tokenizer']:
        tokenizer, tokens = demo_enhanced_tokenizer(df)
        
    if args.demo in ['all', 'multi_timeframe']:
        mtf_tokenizer, tokens_dict, combined_tokens = demo_multi_timeframe(df)
        
    if args.demo in ['all', 'data_augmentation']:
        augmenter, augmented_dfs = demo_data_augmentation(df)
        
    if args.demo in ['all', 'anomaly_detection']:
        tokenizer, df_with_anomalies, df_detected, df_corrected = demo_anomaly_detection(df)
        
    print("\n演示完成!")

if __name__ == '__main__':
    main()
