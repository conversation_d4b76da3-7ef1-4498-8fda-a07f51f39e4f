{"system": {"seed": 3407, "work_dir": "./out/chargpt"}, "data": {"block_size": 15, "is_sf": false, "data_path": "d:/RoboQuant2/store/barenc/sf", "start_year": null, "end_year": null, "start_time": "", "end_time": "", "timeenc": 0, "step_size": 1}, "model": {"model_type": "gpt-mini", "device": "cpu", "n_layer": null, "n_head": null, "n_embd": null, "vocab_size": null, "block_size": null, "code_size": null, "pos_size": null, "embd_pdrop": 0.1, "resid_pdrop": 0.1, "attn_pdrop": 0.1}, "trainer": {"device": "auto", "num_workers": 0, "max_iters": null, "batch_size": 64, "learning_rate": 0.0005, "betas": [0.9, 0.95], "weight_decay": 0.1, "grad_norm_clip": 1.0}}