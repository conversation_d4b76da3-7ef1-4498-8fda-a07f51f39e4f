{"model_type": 0, "seq_len": 15, "label_len": 0, "pred_len": 0, "timeenc": 0, "codes": ["A", "AG", "AL", "AO", "AP", "AU", "B", "BB", "BR", "BU", "C", "CF", "CJ", "CS", "CU", "CY", "EB", "EG", "FB", "FG", "FU", "HC", "I", "IC", "IF", "IH", "IM", "J", "JD", "JM", "JR", "L", "LH", "LR", "M", "MA", "NI", "NR", "OI", "P", "PB", "PF", "PG", "PK", "PM", "PP", "PX", "RB", "RI", "RM", "RR", "RS", "RU", "SA", "SC", "SF", "SH", "SM", "SN", "SP", "SR", "SS", "TA", "UR", "V", "WR", "Y", "ZC", "ZN", "PR"], "slow": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "fast": [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 1, 1, 1, 2, 1, 1, 0, 0, 2, 2, 1, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "main": [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 1, 1, 1, 2, 1, 1, 0, 0, 2, 2, 1, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "context": [0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "context_cat": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "lf_sel_index": [], "sf_sel_index": [8, 44, 45, 46, 52, 53, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 85, 86, 87, 88, 89, 90, 91, 92, 96, 97, 98, 99, 100, 101, 102, 105, 106, 107, 108, 109, 112, 113, 114, 115, 116, 117, 120, 121], "mf_sel_index": [8, 44, 45, 46, 52, 53, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 85, 86, 87, 88, 89, 90, 91, 92, 96, 97, 98, 99, 100, 101, 102, 105, 106, 107, 108, 109, 112, 113, 114, 115, 116, 117, 120, 121], "ct_sel_index": [2, 7, 16, 17, 18, 24, 25, 26, 32, 33, 34, 40, 41, 42, 48, 49, 50], "lf_len": 0, "sf_len": 51, "mf_len": 51, "ct_len": 17, "cat_len": 2, "input_dim": 2, "code_encoding": 2, "win": 15, "filter_win": 0, "step": 1}