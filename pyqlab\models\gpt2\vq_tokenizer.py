import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from pyqlab.models.gpt2.utils import load_single_data
import matplotlib.gridspec as gridspec
import onnxruntime as ort
import talib

# --- 1. K线数据向量化 ---
# 定义不同的向量化方法
class VectorizationMethod:
    """向量化方法的枚举类"""
    ATR_BASED = "atr_based"           # 基于ATR的归一化
    PERCENT_CHANGE = "percent_change"  # 基于前收盘价的百分比变化
    ZSCORE = "zscore"                  # 基于Z-Score的标准化
    LOG_RETURN = "log_return"          # 基于对数收益率
    MINMAX = "minmax"                  # 基于最小-最大归一化
    CANDLESTICK_FEATURES = "candlestick_features"  # 基于K线特征的向量化

def candlestick_to_vector(ohlcv_row, prev_close, ma_volume, atr_val, vector_dim=5, method=VectorizationMethod.ATR_BASED):
    """
    将单根K线转换为数值向量。
    支持多种向量化方法，通过method参数选择。

    Args:
        ohlcv_row: 包含OHLCV数据的Series
        prev_close: 前一根K线的收盘价
        ma_volume: 成交量移动平均
        atr_val: ATR值
        vector_dim: 向量维度
        method: 向量化方法，使用VectorizationMethod类中的常量

    Returns:
        numpy.ndarray: 向量化后的K线数据
    """
    o, h, l, c, v = ohlcv_row['open'], ohlcv_row['high'], ohlcv_row['low'], ohlcv_row['close'], ohlcv_row['volume']

    # 检查必要的值是否有效
    if prev_close is None or pd.isna(prev_close) or prev_close == 0:
        return np.zeros(vector_dim)

    if method == VectorizationMethod.ATR_BASED:
        # 检查ATR值是否有效
        if atr_val is None or pd.isna(atr_val) or atr_val == 0:
            return np.zeros(vector_dim)

        # 基于ATR的归一化 (相对波动)
        if vector_dim == 5:
            # 包含成交量的5维向量
            if ma_volume is None or pd.isna(ma_volume) or ma_volume == 0:
                return np.zeros(vector_dim)

            vec = [
                (o - prev_close) / atr_val,  # 开盘价相对于前收盘价的变化
                (h - o) / atr_val,           # 最高价相对于开盘价的差
                (l - o) / atr_val,           # 最低价相对于开盘价的差
                (c - o) / atr_val,           # 收盘价相对于开盘价的差 (实体)
                v / ma_volume,               # 成交量相对于移动平均的比例
            ]
        elif vector_dim == 4:
            # 不包含成交量的4维向量
            vec = [
                (o - prev_close) / atr_val,  # 开盘价相对于前收盘价的变化
                (h - o) / atr_val,           # 最高价相对于开盘价的差
                (l - o) / atr_val,           # 最低价相对于开盘价的差
                (c - o) / atr_val,           # 收盘价相对于开盘价的差 (实体)
            ]
        else:
            raise ValueError(f"Invalid vector_dim: {vector_dim} for method: {method}")
        # print(vec)

    elif method == VectorizationMethod.PERCENT_CHANGE:
        if vector_dim == 5:
            # 检查MA_Volume值是否有效
            if ma_volume is None or pd.isna(ma_volume) or ma_volume == 0:
                return np.zeros(vector_dim)

            vec = [
                (o - prev_close) / prev_close * 100,  # 开盘价相对于前收盘价的百分比变化
                (h - prev_close) / prev_close * 100,  # 最高价相对于前收盘价的百分比变化
                (l - prev_close) / prev_close * 100,  # 最低价相对于前收盘价的百分比变化
                (c - prev_close) / prev_close * 100,  # 收盘价相对于前收盘价的百分比变化
                v / ma_volume,                  # 成交量相对于移动平均的比例
            ]
        elif vector_dim == 4:
            vec = [
                (o - prev_close) / prev_close * 100,  # 开盘价相对于前收盘价的百分比变化
                (h - prev_close) / prev_close * 100,  # 最高价相对于前收盘价的百分比变化
                (l - prev_close) / prev_close * 100,  # 最低价相对于前收盘价的百分比变化
                (c - prev_close) / prev_close * 100,  # 收盘价相对于前收盘价的百分比变化
            ]
        else:
            raise ValueError(f"Invalid vector_dim: {vector_dim} for method: {method}")


    elif method == VectorizationMethod.LOG_RETURN:
        # 基于对数收益率
        if vector_dim == 5:
            # 检查MA_Volume值是否有效
            if ma_volume is None or pd.isna(ma_volume) or ma_volume == 0:
                return np.zeros(vector_dim)

            # 避免对数中的零或负值
            if o <= 0 or h <= 0 or l <= 0 or c <= 0 or prev_close <= 0:
                return np.zeros(vector_dim)

            vec = [
                np.log(o / prev_close * 100),  # 开盘价的对数收益率
                np.log(h / o * 100),           # 最高价相对于开盘价的对数比率
                np.log(l / o * 100),           # 最低价相对于开盘价的对数比率
                np.log(c / o * 100),           # 收盘价相对于开盘价的对数比率
                v / ma_volume,           # 成交量相对于移动平均的比例
            ]
        elif vector_dim == 4:
            # 避免对数中的零或负值
            if o <= 0 or h <= 0 or l <= 0 or c <= 0 or prev_close <= 0:
                return np.zeros(vector_dim)

            vec = [
                np.log(o / prev_close * 100),  # 开盘价的对数收益率
                np.log(h / o * 100),           # 最高价相对于开盘价的对数比率
                np.log(l / o * 100),           # 最低价相对于开盘价的对数比率
                np.log(c / o * 100),           # 收盘价相对于开盘价的对数比率
            ]
        else:
            raise ValueError(f"Invalid vector_dim: {vector_dim} for method: {method}")

    elif method == VectorizationMethod.ZSCORE:
        # 基于Z-Score的标准化
        # 注意：这种方法通常需要一个窗口的数据来计算均值和标准差
        # 这里我们使用ATR作为标准差的近似，前收盘价作为均值的近似
        if atr_val is None or pd.isna(atr_val) or atr_val == 0:
            return np.zeros(vector_dim)

        if vector_dim == 5:
            # 检查MA_Volume值是否有效
            if ma_volume is None or pd.isna(ma_volume) or ma_volume == 0:
                return np.zeros(vector_dim)

            vec = [
                (o - prev_close) / atr_val,  # 开盘价的Z-Score
                (h - o) / atr_val,           # 最高价相对于开盘价的Z-Score
                (l - o) / atr_val,           # 最低价相对于开盘价的Z-Score
                (c - o) / atr_val,           # 收盘价相对于开盘价的Z-Score
                (v - ma_volume) / (ma_volume * 0.1 if ma_volume * 0.1 != 0 else 1),  # 成交量的Z-Score
            ]
        elif vector_dim == 4:
            vec = [
                (o - prev_close) / atr_val,  # 开盘价的Z-Score
                (h - o) / atr_val,           # 最高价相对于开盘价的Z-Score
                (l - o) / atr_val,           # 最低价相对于开盘价的Z-Score
                (c - o) / atr_val,           # 收盘价相对于开盘价的Z-Score
            ]
        else:
            raise ValueError(f"Invalid vector_dim: {vector_dim} for method: {method}")

    elif method == VectorizationMethod.MINMAX:
        # 基于最小-最大归一化
        # 使用当天的价格范围进行归一化
        price_range = h - l
        if price_range == 0:  # 避免除以零
            return np.zeros(vector_dim)

        if vector_dim == 5:
            # 检查MA_Volume值是否有效
            if ma_volume is None or pd.isna(ma_volume) or ma_volume == 0:
                return np.zeros(vector_dim)

            vec = [
                (o - l) / price_range,  # 开盘价在当天价格范围内的位置
                (h - o) / price_range,  # 最高价相对于开盘价的归一化差
                (o - l) / price_range,  # 开盘价相对于最低价的归一化差
                (c - l) / price_range,  # 收盘价在当天价格范围内的位置
                v / ma_volume,          # 成交量相对于移动平均的比例
            ]
        elif vector_dim == 4:
            vec = [
                (o - l) / price_range,  # 开盘价在当天价格范围内的位置
                (h - o) / price_range,  # 最高价相对于开盘价的归一化差
                (o - l) / price_range,  # 开盘价相对于最低价的归一化差
                (c - l) / price_range,  # 收盘价在当天价格范围内的位置
            ]
        else:
            raise ValueError(f"Invalid vector_dim: {vector_dim} for method: {method}")

    elif method == VectorizationMethod.CANDLESTICK_FEATURES:
        # 基于K线特征的向量化方法
        # body = (Close - Open) / Open
        # upper_shadow = (High - max(Open, Close)) / Open
        # lower_shadow = (min(Open, Close) - Low) / Open
        # volume_ratio = Volume / MovingAverage(Volume, 20)

        # 检查开盘价是否有效（避免除零）
        if o == 0:
            return np.zeros(vector_dim)

        if vector_dim == 5:
            # 检查MA_Volume值是否有效
            if ma_volume is None or pd.isna(ma_volume) or ma_volume == 0:
                return np.zeros(vector_dim)

            # 计算K线特征
            body = (c - o) / o  # 实体：(收盘价 - 开盘价) / 开盘价
            upper_shadow = (h - max(o, c)) / o  # 上影线：(最高价 - max(开盘价, 收盘价)) / 开盘价
            lower_shadow = (min(o, c) - l) / o  # 下影线：(min(开盘价, 收盘价) - 最低价) / 开盘价
            volume_ratio = v / ma_volume  # 成交量比率：成交量 / 成交量移动平均

            vec = [
                body,           # K线实体
                upper_shadow,   # 上影线
                lower_shadow,   # 下影线
                volume_ratio,   # 成交量比率
                0.0             # 预留维度，可用于其他特征
            ]
        elif vector_dim == 4:
            # 不包含成交量的4维向量
            body = (c - o) / o  # 实体：(收盘价 - 开盘价) / 开盘价
            upper_shadow = (h - max(o, c)) / o  # 上影线：(最高价 - max(开盘价, 收盘价)) / 开盘价
            lower_shadow = (min(o, c) - l) / o  # 下影线：(min(开盘价, 收盘价) - 最低价) / 开盘价

            # 计算价格变化（相对于前收盘价）
            price_change = (c - prev_close) / prev_close if prev_close != 0 else 0.0

            vec = [
                body,           # K线实体
                upper_shadow,   # 上影线
                lower_shadow,   # 下影线
                price_change    # 价格变化
            ]
        else:
            raise ValueError(f"Invalid vector_dim: {vector_dim} for method: {method}")
    else:
        raise ValueError(f"Unknown vectorization method: {method}")

    # 确保向量维度与 vector_dim 匹配
    if len(vec) != vector_dim:
        raise ValueError(f"Generated vector length {len(vec)} does not match vector_dim {vector_dim}")

    # 处理无穷大和NaN值
    vec = np.array(vec, dtype=np.float32)
    vec = np.nan_to_num(vec, nan=0.0, posinf=0.0, neginf=0.0)

    # 规范化向量值，限制在合理范围内
    if method == VectorizationMethod.ATR_BASED:
        # 对于ATR_BASED方法，限制在[-10, 10]范围内
        vec = np.clip(vec, -10.0, 10.0)
    elif method == VectorizationMethod.PERCENT_CHANGE:
        # 对于PERCENT_CHANGE方法，限制在[-20, 20]范围内（百分比）
        vec = np.clip(vec, -20.0, 20.0)
    elif method == VectorizationMethod.LOG_RETURN:
        # 对于LOG_RETURN方法，限制在[-5, 5]范围内
        vec = np.clip(vec, -5.0, 5.0)
    elif method == VectorizationMethod.ZSCORE:
        # 对于ZSCORE方法，限制在[-5, 5]范围内
        vec = np.clip(vec, -5.0, 5.0)
    elif method == VectorizationMethod.MINMAX:
        # 对于MINMAX方法，限制在[0, 1]范围内
        vec = np.clip(vec, 0.0, 1.0)
    elif method == VectorizationMethod.CANDLESTICK_FEATURES:
        # 对于CANDLESTICK_FEATURES方法，限制在合理范围内
        # body: 实体可能在[-1, 1]范围内（涨跌100%）
        # upper_shadow, lower_shadow: 影线通常在[0, 1]范围内
        # volume_ratio: 成交量比率通常在[0, 10]范围内
        # price_change: 价格变化通常在[-0.2, 0.2]范围内（涨跌20%）
        if vector_dim == 5:
            vec[0] = np.clip(vec[0], -1.0, 1.0)    # body
            vec[1] = np.clip(vec[1], 0.0, 1.0)     # upper_shadow
            vec[2] = np.clip(vec[2], 0.0, 1.0)     # lower_shadow
            vec[3] = np.clip(vec[3], 0.0, 10.0)    # volume_ratio
            vec[4] = np.clip(vec[4], -1.0, 1.0)    # 预留维度
        elif vector_dim == 4:
            vec[0] = np.clip(vec[0], -1.0, 1.0)    # body
            vec[1] = np.clip(vec[1], 0.0, 1.0)     # upper_shadow
            vec[2] = np.clip(vec[2], 0.0, 1.0)     # lower_shadow
            vec[3] = np.clip(vec[3], -0.2, 0.2)    # price_change
        vec = np.array(vec)

    # 打印向量值范围，用于调试
    # print(f"Vector range: min={np.min(vec)}, max={np.max(vec)}, method={method}")

    return vec


# --- 2. 模拟VQ-VAE的组件 (假设已训练好) ---
class VQEmbedding(nn.Module):
    """
    Vector Quantizer Layer (简化版，仅用于演示Tokenizer的核心逻辑)
    在实际的VQ-VAE中，这个模块通常在编码器之后，解码器之前。
    """
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super(VQEmbedding, self).__init__()
        self.embedding_dim = embedding_dim
        self.num_embeddings = num_embeddings
        self.commitment_cost = commitment_cost # 在tokenizer中通常不直接使用

        # 码本 (Codebook) - 这是VQ-VAE训练后得到的
        # 每一行是一个码向量 (code vector)
        self.embedding = nn.Embedding(self.num_embeddings, self.embedding_dim)
        # 通常，码本的权重是训练得到的，这里我们用随机初始化代替
        # 在实际应用中，你需要加载训练好的码本权重
        self.embedding.weight.data.uniform_(-1.0 / self.num_embeddings, 1.0 / self.num_embeddings)

    def forward(self, latents_e):
        """
        将编码器输出的连续向量映射到离散的码本索引。
        latents_e: (batch_size, embedding_dim) or (embedding_dim)
        """
        if latents_e.ndim == 1: # 单个向量
            latents_e = latents_e.unsqueeze(0) # 变成 (1, embedding_dim)

        # 计算输入向量与码本中所有码向量的L2距离的平方
        # latents_e_flat: (batch_size, embedding_dim)
        # self.embedding.weight: (num_embeddings, embedding_dim)
        distances = (torch.sum(latents_e**2, dim=1, keepdim=True)
                     - 2 * torch.matmul(latents_e, self.embedding.weight.t())
                     + torch.sum(self.embedding.weight**2, dim=1, keepdim=True).t())
        # distances: (batch_size, num_embeddings)

        # 找到最近的码向量的索引
        # encoding_indices: (batch_size, 1)
        encoding_indices = torch.argmin(distances, dim=1)

        return encoding_indices # 返回的是token ID (码本索引)

    def quantize(self, encoding_indices):
        """从索引获取量化后的向量 (码本中的向量)"""
        return self.embedding(encoding_indices)


# --- 3. CandlestickVQTokenizer ---
class CandlestickVQTokenizer:
    def __init__(self, codebook_weights_path=None, num_embeddings=512, embedding_dim=5,
                 atr_period=14, ma_volume_period=20, vectorization_method=VectorizationMethod.ATR_BASED,
                 gap_threshold=3.0, detect_gaps=True, encoder_onnx_path=None, decoder_onnx_path=None,
                 use_code_dim=False, code_size=100, code_dim=5):
        """
        初始化Tokenizer。

        Args:
            codebook_weights_path: 预训练的VQ-VAE码本权重文件路径 (e.g., .pt or .pth)
            num_embeddings: 码本中的码向量数量 (词汇表大小)
            embedding_dim: K线向量的维度，也是码向量的维度
            atr_period: 计算ATR的周期
            ma_volume_period: 计算移动平均成交量的周期
            vectorization_method: 向量化方法，使用VectorizationMethod类中的常量
            gap_threshold: 跳空检测阈值，以ATR的倍数表示
            detect_gaps: 是否检测跳空
            encoder_onnx_path: 编码器ONNX模型路径，如果提供则使用ONNX模型进行编码
            decoder_onnx_path: 解码器ONNX模型路径，如果提供则使用ONNX模型进行解码
            use_code_dim: 是否使用证券代码维度
            code_size: 证券代码数量
            code_dim: 证券代码嵌入维度
        """
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.atr_period = atr_period
        self.ma_volume_period = ma_volume_period
        self.vectorization_method = vectorization_method
        self.gap_threshold = gap_threshold
        self.detect_gaps = detect_gaps
        self.use_code_dim = use_code_dim
        self.code_size = code_size
        self.code_dim = code_dim

        # 保存ONNX模型路径
        self.encoder_onnx_path = encoder_onnx_path
        self.decoder_onnx_path = decoder_onnx_path

        # 初始化ONNX会话为None，在需要时才创建
        self.encoder_session = None
        self.decoder_session = None

        self.vq_layer = VQEmbedding(num_embeddings, embedding_dim)

        # 如果使用证券代码维度，创建证券代码嵌入层
        if use_code_dim:
            self.code_embedding = nn.Embedding(code_size, code_dim)
            print(f"启用证券代码维度，代码数量: {code_size}，嵌入维度: {code_dim}")

            # 创建证券代码到ID的映射
            self.code_to_id = {}
            self.next_code_id = 0

        if codebook_weights_path:
            try:
                # 加载PyTorch格式的码本权重
                state_dict = torch.load(codebook_weights_path)

                # 检查state_dict的类型
                if isinstance(state_dict, dict):
                    # 如果是字典，可能是整个embedding层的state_dict
                    if 'weight' in state_dict:
                        self.vq_layer.embedding.load_state_dict(state_dict)
                    else:
                        # 可能是其他格式的字典，尝试直接使用
                        self.vq_layer.embedding.weight.data.copy_(state_dict)
                else:
                    # 如果是张量，直接使用
                    self.vq_layer.embedding.weight.data.copy_(state_dict)

                print(f"Loaded PyTorch codebook weights from {codebook_weights_path}")
            except Exception as e:
                print(f"Warning: Could not load codebook weights from {codebook_weights_path}. Using random init. Error: {e}")
        else:
            print("Warning: No codebook_weights_path provided. Using randomly initialized codebook.")

        self.vq_layer.eval() # 设置为评估模式

        # 特殊tokens
        self.gap_up_token = "<GAP_UP>"    # 向上跳空标记
        self.gap_down_token = "<GAP_DOWN>" # 向下跳空标记
        self.bos_token = "<BOS>"
        self.eos_token = "<EOS>"
        self.pad_token = "<PAD>"
        self.unk_token = "<UNK>" # 用于无法处理的K线或作为默认

        self.special_tokens = [self.gap_up_token, self.gap_down_token, self.pad_token, self.unk_token, self.bos_token, self.eos_token]
        self.vocab_size = self.num_embeddings + len(self.special_tokens)

        # 建立token到ID的映射
        self.token_to_id_map = {}
        self.id_to_token_map = {}

        # 码本中的token (ID从0到num_embeddings-1)
        for i in range(self.num_embeddings):
            token_name = f"CODE_{i}"
            self.token_to_id_map[token_name] = i
            self.id_to_token_map[i] = token_name

        # 特殊token的ID
        offset = self.num_embeddings
        for i, token in enumerate(self.special_tokens):
            self.token_to_id_map[token] = offset + i
            self.id_to_token_map[offset + i] = token

        # ID for unknown K-line token
        self.unk_token_id = self.token_to_id_map[self.unk_token]
        # ID for gap tokens
        self.gap_up_token_id = self.token_to_id_map[self.gap_up_token]
        self.gap_down_token_id = self.token_to_id_map[self.gap_down_token]

    def _preprocess_df(self, df_ohlcv):
        """辅助函数，计算ATR, MA_Volume, Prev_Close以及跳空检测"""
        df = df_ohlcv.copy()
        # ATR
        df['ATR'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=self.atr_period)

        # MA Volume
        df['MA_Volume'] = talib.SMA(df['volume'], timeperiod=self.ma_volume_period)

        # Prev Close
        df['Prev_Close'] = df['close'].shift(1)

        # 跳空检测
        if self.detect_gaps:
            # 计算开盘价与前收盘价的差距
            df['Gap'] = df['open'] - df['Prev_Close']
            # 标记跳空（以ATR的倍数为阈值）
            df['Gap_Up'] = (df['Gap'] > df['ATR'] * self.gap_threshold)
            df['Gap_Down'] = (df['Gap'] < -df['ATR'] * self.gap_threshold)

        return df

    def _detect_gap(self, row):
        """
        检测当前K线是否存在跳空

        Args:
            row: 包含预处理数据的DataFrame行

        Returns:
            int: 跳空类型的token ID，如果没有跳空则返回None
        """
        if not self.detect_gaps:
            return None

        if pd.isna(row['ATR']) or pd.isna(row['Prev_Close']):
            return None

        if row['Gap_Up']:
            return self.gap_up_token_id
        elif row['Gap_Down']:
            return self.gap_down_token_id

        return None

    def tokenize_single_candlestick(self, ohlcv_row, prev_close, ma_volume, atr_val, code_id=None, debug=False, use_onnx=False):
        """
        将单个K线数据点（已提取相关特征）转换为token ID

        Args:
            ohlcv_row: 包含OHLCV数据的Series
            prev_close: 前一根K线的收盘价
            ma_volume: 成交量移动平均
            atr_val: ATR值
            code_id: 证券代码ID，如果使用证券代码维度则必须提供
            debug: 是否打印调试信息
            use_onnx: 是否使用ONNX模型进行编码
        """
        vector = candlestick_to_vector(
            ohlcv_row,
            prev_close,
            ma_volume,
            atr_val,
            self.embedding_dim,
            self.vectorization_method
        )

        if np.all(vector == 0): # 假设vectorizer在数据不足时返回0向量
             return self.unk_token_id # 或者可以抛出异常或返回None

        if debug:
            print(f"原始向量: {vector}")
            print(f"向量范围: min={np.min(vector)}, max={np.max(vector)}")
            print(f"向量方法: {self.vectorization_method}")
            if self.use_code_dim and code_id is not None:
                print(f"证券代码ID: {code_id}")

        # 使用ONNX模型进行编码
        if use_onnx and hasattr(self, 'encoder_onnx_path') and self.encoder_onnx_path:
            try:
                import onnxruntime as ort

                # 创建ONNX运行时会话（如果尚未创建）
                if not hasattr(self, 'encoder_session') or self.encoder_session is None:
                    self.encoder_session = ort.InferenceSession(self.encoder_onnx_path)
                    # 获取所有输入名称
                    self.encoder_input_names = [input.name for input in self.encoder_session.get_inputs()]
                    if debug:
                        print(f"ONNX编码器输入名称: {self.encoder_input_names}")

                # 准备输入
                vector_reshaped = vector.reshape(1, self.embedding_dim).astype(np.float32)
                if debug:
                    print(f"ONNX编码输入值: {vector_reshaped}")

                # 检查ONNX模型是否需要证券代码ID
                if len(self.encoder_session.get_inputs()) > 1 and 'code_id' in [input.name for input in self.encoder_session.get_inputs()]:
                    # 如果模型需要证券代码ID但没有提供，使用默认值0
                    if code_id is None:
                        code_id = 0

                    # 准备包含证券代码ID的输入
                    onnx_input = {
                        'input_vector': vector_reshaped,
                        'code_id': np.array([code_id], dtype=np.int64)
                    }

                    if debug:
                        print(f"使用证券代码ID: {code_id}")
                else:
                    # 旧版模型只需要向量输入
                    input_name = self.encoder_session.get_inputs()[0].name
                    onnx_input = {input_name: vector_reshaped}

                # 运行推理
                onnx_output = self.encoder_session.run(None, onnx_input)
                token_id = onnx_output[0][0]

                if debug:
                    print(f"ONNX编码: 向量 -> Token ID {token_id}")

                return token_id
            except Exception as e:
                if debug:
                    print(f"ONNX编码错误: {e}，回退到PyTorch模型")
                # 如果ONNX模型失败，回退到PyTorch模型

        # 使用PyTorch模型进行编码
        vector_tensor = torch.tensor(vector, dtype=torch.float32)

        # 如果使用证券代码维度
        if self.use_code_dim and code_id is not None:
            # 获取证券代码嵌入
            code_tensor = torch.tensor([code_id], dtype=torch.long)
            code_emb = self.code_embedding(code_tensor).squeeze(0)

            # 将证券代码嵌入与向量拼接
            # 注意：这里我们不直接拼接，而是使用证券代码嵌入来调整向量
            # 这样可以保持向量维度不变，同时利用证券代码信息
            vector_tensor = vector_tensor + 0.1 * code_emb[:self.embedding_dim]

            if debug:
                print(f"证券代码嵌入: {code_emb.detach().numpy()}")
                print(f"调整后的向量: {vector_tensor.detach().numpy()}")

        with torch.no_grad():
            token_id_tensor = self.vq_layer(vector_tensor) # VQEmbedding的forward返回索引

            if debug:
                # 获取量化后的向量，用于调试
                quantized_vec = self.vq_layer.quantize(token_id_tensor)
                print(f"量化后向量: {quantized_vec.squeeze().numpy()}")
                print(f"Token ID: {token_id_tensor.item()}")

        return token_id_tensor.item() # .item() 将单元素tensor转为python number

    def encode(self, df_ohlcv_history, add_bos_eos=False, code_id=None):
        """
        将K线历史数据 (Pandas DataFrame) 转换为token ID序列。
        包含对跳空情况的检测和特殊标记。

        Args:
            df_ohlcv_history: DataFrame with columns ['open', 'high', 'low', 'close', 'volume', 'datetime']
            add_bos_eos: 是否在序列开始和结束添加BOS和EOS标记
            code_id: 证券代码ID，如果使用证券代码维度则必须提供

        Returns:
            list: token ID序列
        """
        if code_id is None and self.use_code_dim:
            raise ValueError("证券代码ID不能为空")

        if not all(col in df_ohlcv_history.columns for col in ['open', 'high', 'low', 'close', 'volume']):
            raise ValueError("Input DataFrame must contain open, high, low, close, volume columns.")

        processed_df = self._preprocess_df(df_ohlcv_history)
        token_ids = []

        if add_bos_eos:
            token_ids.append(self.token_to_id_map[self.bos_token])

        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            # 检查是否有足够的历史数据来计算ATR, MA_Volume, Prev_Close
            if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
                # 在序列开始部分，这些值可能是NaN，可以跳过或用unk_token
                # token_ids.append(self.unk_token_id)
                continue # 跳过无法计算完整特征的早期K线

            # 检测跳空
            gap_token_id = self._detect_gap(row)
            if gap_token_id is not None:
                # 添加跳空标记
                print(f"Detected gap at index {i}")
                token_ids.append(gap_token_id)

            # 正常的K线tokenization，加入证券代码ID
            token_id = self.tokenize_single_candlestick(
                row,
                row['Prev_Close'],
                row['MA_Volume'],
                row['ATR'],
                code_id=code_id
            )
            token_ids.append(token_id)

        if add_bos_eos:
            token_ids.append(self.token_to_id_map[self.eos_token])

        return token_ids

    def decode_id(self, token_id):
        """将单个token ID转换回其符号表示 (e.g., "CODE_123" or "<BOS>")"""
        if token_id not in self.id_to_token_map:
            # raise ValueError(f"Unknown token ID: {token_id}")
            return self.unk_token # Fallback for unknown IDs
        return self.id_to_token_map[token_id]

    def decode_sequence(self, token_ids_sequence):
        """将token ID序列转换回符号表示序列"""
        return [self.decode_id(tid) for tid in token_ids_sequence]

    def get_quantized_vector(self, token_id, debug=False, use_onnx=False, code_id=None):
        """
        (可选) 根据token ID (码本索引) 获取对应的码本向量。
        这对于理解token代表的"原型"K线或用于VQ-VAE解码器可能有用。

        Args:
            token_id: 码本索引
            debug: 是否打印调试信息
            use_onnx: 是否使用ONNX模型进行解码
            code_id: 证券代码ID，如果使用证券代码维度则必须提供
        """
        if token_id < 0 or token_id >= self.num_embeddings:
            # raise ValueError(f"Token ID {token_id} is out of codebook range [0, {self.num_embeddings-1}]")
            print(f"Warning: Token ID {token_id} is out of codebook range. Returning None.")
            return None

        # 使用ONNX模型进行解码
        if use_onnx and hasattr(self, 'decoder_onnx_path') and self.decoder_onnx_path:
            try:
                import onnxruntime as ort

                # 创建ONNX运行时会话（如果尚未创建）
                if not hasattr(self, 'decoder_session') or self.decoder_session is None:
                    self.decoder_session = ort.InferenceSession(self.decoder_onnx_path)
                    # 获取所有输入名称
                    self.decoder_input_names = [input.name for input in self.decoder_session.get_inputs()]
                    if debug:
                        print(f"ONNX解码器输入名称: {self.decoder_input_names}")

                # 准备输入
                token_id_array = np.array([token_id], dtype=np.int64)

                # 检查ONNX模型是否需要证券代码ID
                if len(self.decoder_session.get_inputs()) > 1 and 'code_id' in [input.name for input in self.decoder_session.get_inputs()]:
                    # 如果模型需要证券代码ID但没有提供，使用默认值0
                    code_id_value = 0

                    # 准备包含证券代码ID的输入
                    onnx_input = {
                        'token_id': token_id_array,
                        'code_id': np.array([code_id_value], dtype=np.int64)
                    }

                    if debug:
                        print(f"使用证券代码ID: {code_id_value}")
                else:
                    # 旧版模型只需要token_id输入
                    input_name = self.decoder_session.get_inputs()[0].name
                    onnx_input = {input_name: token_id_array}

                # 运行推理
                onnx_output = self.decoder_session.run(None, onnx_input)
                vector = onnx_output[0][0]

                if debug:
                    print(f"Token ID: {token_id}")
                    print(f"ONNX解码: Token ID {token_id} -> 向量 {vector}")
            except Exception as e:
                if debug:
                    print(f"ONNX解码错误: {e}，回退到PyTorch模型")
                # 如果ONNX模型失败，回退到PyTorch模型
                with torch.no_grad():
                    token_tensor = torch.tensor([token_id], dtype=torch.int64)
                    quantized_vec = self.vq_layer.quantize(token_tensor)
                    vector = quantized_vec.squeeze().numpy()
        else:
            # 使用PyTorch模型进行解码
            with torch.no_grad():
                token_tensor = torch.tensor([token_id], dtype=torch.int64)
                quantized_vec = self.vq_layer.quantize(token_tensor)

                if debug:
                    print(f"Token ID: {token_id}")
                    print(f"量化后向量 (原始): {quantized_vec.squeeze().numpy()}")

            # 将向量转换为numpy数组
            vector = quantized_vec.squeeze().numpy()

        # 规范化向量值，确保在合理范围内
        if self.vectorization_method == VectorizationMethod.ATR_BASED:
            # 对于ATR_BASED方法，限制在[-10, 10]范围内
            vector = np.clip(vector, -10.0, 10.0)
        elif self.vectorization_method == VectorizationMethod.PERCENT_CHANGE:
            # 对于PERCENT_CHANGE方法，限制在[-20, 20]范围内（百分比）
            vector = np.clip(vector, -20.0, 20.0)
        elif self.vectorization_method == VectorizationMethod.LOG_RETURN:
            # 对于LOG_RETURN方法，限制在[-5, 5]范围内
            vector = np.clip(vector, -5.0, 5.0)
        elif self.vectorization_method == VectorizationMethod.ZSCORE:
            # 对于ZSCORE方法，限制在[-5, 5]范围内
            vector = np.clip(vector, -5.0, 5.0)
        elif self.vectorization_method == VectorizationMethod.MINMAX:
            # 对于MINMAX方法，限制在[0, 1]范围内
            vector = np.clip(vector, 0.0, 1.0)
        elif self.vectorization_method == VectorizationMethod.CANDLESTICK_FEATURES:
            # 对于CANDLESTICK_FEATURES方法，限制在合理范围内
            if len(vector) == 5:
                vector[0] = np.clip(vector[0], -1.0, 1.0)    # body
                vector[1] = np.clip(vector[1], 0.0, 1.0)     # upper_shadow
                vector[2] = np.clip(vector[2], 0.0, 1.0)     # lower_shadow
                vector[3] = np.clip(vector[3], 0.0, 10.0)    # volume_ratio
                vector[4] = np.clip(vector[4], -1.0, 1.0)    # 预留维度
            elif len(vector) == 4:
                vector[0] = np.clip(vector[0], -1.0, 1.0)    # body
                vector[1] = np.clip(vector[1], 0.0, 1.0)     # upper_shadow
                vector[2] = np.clip(vector[2], 0.0, 1.0)     # lower_shadow
                vector[3] = np.clip(vector[3], -0.2, 0.2)    # price_change

        if debug:
            print(f"量化后向量 (规范化): {vector}")
            print(f"向量范围: min={np.min(vector)}, max={np.max(vector)}")

        return vector

    def vector_to_candlestick(self, vector, prev_close, ma_volume, atr_val, debug=False):
        """
        将向量转换回K线数据。
        这是candlestick_to_vector的逆操作，用于可视化和评估向量化方法的保真度。

        Args:
            vector: 向量化后的K线数据
            prev_close: 前一根K线的收盘价
            ma_volume: 成交量移动平均
            atr_val: ATR值
            debug: 是否打印调试信息

        Returns:
            dict: 包含open, high, low, close, volume的字典
        """
        if np.all(vector == 0):
            return {
                'open': prev_close,
                'high': prev_close,
                'low': prev_close,
                'close': prev_close,
                'volume': ma_volume
            }

        # 规范化向量值，确保在合理范围内
        if self.vectorization_method == VectorizationMethod.ATR_BASED:
            # 对于ATR_BASED方法，限制在[-10, 10]范围内
            vector = np.clip(vector, -10.0, 10.0)
        elif self.vectorization_method == VectorizationMethod.PERCENT_CHANGE:
            # 对于PERCENT_CHANGE方法，限制在[-20, 20]范围内（百分比）
            vector = np.clip(vector, -20.0, 20.0)
        elif self.vectorization_method == VectorizationMethod.LOG_RETURN:
            # 对于LOG_RETURN方法，限制在[-5, 5]范围内
            vector = np.clip(vector, -5.0, 5.0)
        elif self.vectorization_method == VectorizationMethod.ZSCORE:
            # 对于ZSCORE方法，限制在[-5, 5]范围内
            vector = np.clip(vector, -5.0, 5.0)
        elif self.vectorization_method == VectorizationMethod.MINMAX:
            # 对于MINMAX方法，限制在[0, 1]范围内
            vector = np.clip(vector, 0.0, 1.0)
        elif self.vectorization_method == VectorizationMethod.CANDLESTICK_FEATURES:
            # 对于CANDLESTICK_FEATURES方法，限制在合理范围内
            if len(vector) == 5:
                vector[0] = np.clip(vector[0], -1.0, 1.0)    # body
                vector[1] = np.clip(vector[1], 0.0, 1.0)     # upper_shadow
                vector[2] = np.clip(vector[2], 0.0, 1.0)     # lower_shadow
                vector[3] = np.clip(vector[3], 0.0, 10.0)    # volume_ratio
                vector[4] = np.clip(vector[4], -1.0, 1.0)    # 预留维度
            elif len(vector) == 4:
                vector[0] = np.clip(vector[0], -1.0, 1.0)    # body
                vector[1] = np.clip(vector[1], 0.0, 1.0)     # upper_shadow
                vector[2] = np.clip(vector[2], 0.0, 1.0)     # lower_shadow
                vector[3] = np.clip(vector[3], -0.2, 0.2)    # price_change

        if debug:
            print(f"规范化后向量: {vector}")
            print(f"向量范围: min={np.min(vector)}, max={np.max(vector)}")
            print(f"向量方法: {self.vectorization_method}")

        # 根据不同的向量化方法进行逆变换
        if self.vectorization_method == VectorizationMethod.ATR_BASED:
            if len(vector) == 5:
                o = prev_close + vector[0] * atr_val

                # 确保high-open >= 0（最高价不低于开盘价）
                high_open_value = max(0, vector[1]) * atr_val
                h = o + high_open_value

                # 确保low-open <= 0（最低价不高于开盘价）
                low_open_value = min(0, vector[2]) * atr_val
                l = o + low_open_value

                c = o + vector[3] * atr_val
                v = vector[4] * ma_volume
                print(f"o: {o}, h: {h}, l: {l}, c: {c}, v: {v} atr: {atr_val}")
            elif len(vector) == 4:
                o = prev_close + vector[0] * atr_val

                # 确保high-open >= 0（最高价不低于开盘价）
                high_open_value = max(0, vector[1]) * atr_val
                h = o + high_open_value

                # 确保low-open <= 0（最低价不高于开盘价）
                low_open_value = min(0, vector[2]) * atr_val
                l = o + low_open_value

                c = o + vector[3] * atr_val
                v = ma_volume
            else:
                raise ValueError(f"Invalid vector dimension: {len(vector)}")

        elif self.vectorization_method == VectorizationMethod.PERCENT_CHANGE:
            # 确保向量值在合理范围内

            if len(vector) == 5:
                o = prev_close * (1 + vector[0] / 100)

                # 确保最高价不低于开盘价和前收盘价
                h_pct = max(vector[0], vector[1]) / 100
                h = prev_close * (1 + h_pct)

                # 确保最低价不高于开盘价和前收盘价
                l_pct = min(vector[0], vector[2]) / 100
                l = prev_close * (1 + l_pct)

                c = prev_close * (1 + vector[3] / 100)
                v = vector[4] * ma_volume
            elif len(vector) == 4:
                o = prev_close * (1 + vector[0] / 100)

                # 确保最高价不低于开盘价和前收盘价
                h_pct = max(vector[0], vector[1]) / 100
                h = prev_close * (1 + h_pct)

                # 确保最低价不高于开盘价和前收盘价
                l_pct = min(vector[0], vector[2]) / 100
                l = prev_close * (1 + l_pct)

                c = prev_close * (1 + vector[3] / 100)
                v = ma_volume
            else:
                raise ValueError(f"Invalid vector dimension: {len(vector)}")

        elif self.vectorization_method == VectorizationMethod.LOG_RETURN:
            if len(vector) == 5:
                o = prev_close * np.exp(vector[0]) / 100

                # 确保high-open >= 0（最高价不低于开盘价）
                high_factor = np.exp(max(0, vector[1])) / 100
                h = o * high_factor

                # 确保low-open <= 0（最低价不高于开盘价）
                low_factor = np.exp(min(0, vector[2])) / 100
                l = o * low_factor

                c = o * np.exp(vector[3]) / 100
                v = vector[4] * ma_volume
            elif len(vector) == 4:
                o = prev_close * np.exp(vector[0])

                # 确保high-open >= 0（最高价不低于开盘价）
                high_factor = np.exp(max(0, vector[1])) / 100
                h = o * high_factor

                # 确保low-open <= 0（最低价不高于开盘价）
                low_factor = np.exp(min(0, vector[2])) / 100
                l = o * low_factor

                c = o * np.exp(vector[3]) / 100
                v = ma_volume
            else:
                raise ValueError(f"Invalid vector dimension: {len(vector)}")

        elif self.vectorization_method == VectorizationMethod.ZSCORE:
            if len(vector) == 5:
                o = prev_close + vector[0] * atr_val

                # 确保high-open >= 0（最高价不低于开盘价）
                high_open_value = max(0, vector[1]) * atr_val
                h = o + high_open_value

                # 确保low-open <= 0（最低价不高于开盘价）
                low_open_value = min(0, vector[2]) * atr_val
                l = o + low_open_value

                c = o + vector[3] * atr_val
                v = ma_volume + vector[4] * (ma_volume * 0.1)
            elif len(vector) == 4:
                o = prev_close + vector[0] * atr_val

                # 确保high-open >= 0（最高价不低于开盘价）
                high_open_value = max(0, vector[1]) * atr_val
                h = o + high_open_value

                # 确保low-open <= 0（最低价不高于开盘价）
                low_open_value = min(0, vector[2]) * atr_val
                l = o + low_open_value

                c = o + vector[3] * atr_val
                v = ma_volume
            else:
                raise ValueError(f"Invalid vector dimension: {len(vector)}")

        elif self.vectorization_method == VectorizationMethod.MINMAX:
            if len(vector) == 5:
                price_range = 1.0  # 归一化后的范围
                l_base = 0.0       # 最低价的基准值

                # 从归一化的值恢复原始值
                o = l_base + vector[0] * price_range

                # 确保high-open >= 0（最高价不低于开盘价）
                high_open_value = max(0, vector[1]) * price_range
                h = o + high_open_value

                l = l_base
                c = l_base + vector[3] * price_range

                # 调整到实际价格范围
                actual_range = h - l
                scale_factor = atr_val / actual_range if actual_range != 0 else 1.0

                o = prev_close + (o - l_base) * scale_factor
                h = prev_close + (h - l_base) * scale_factor
                l = prev_close + (l - l_base) * scale_factor
                c = prev_close + (c - l_base) * scale_factor
                v = vector[4] * ma_volume
            elif len(vector) == 4:
                price_range = 1.0  # 归一化后的范围
                l_base = 0.0       # 最低价的基准值

                # 从归一化的值恢复原始值
                o = l_base + vector[0] * price_range

                # 确保high-open >= 0（最高价不低于开盘价）
                high_open_value = max(0, vector[1]) * price_range
                h = o + high_open_value

                l = l_base
                c = l_base + vector[3] * price_range

                # 调整到实际价格范围
                actual_range = h - l
                scale_factor = atr_val / actual_range if actual_range != 0 else 1.0

                o = prev_close + (o - l_base) * scale_factor
                h = prev_close + (h - l_base) * scale_factor
                l = prev_close + (l - l_base) * scale_factor
                c = prev_close + (c - l_base) * scale_factor
                v = ma_volume
            else:
                raise ValueError(f"Invalid vector dimension: {len(vector)}")

        elif self.vectorization_method == VectorizationMethod.CANDLESTICK_FEATURES:
            # 基于K线特征的逆变换
            # 从向量中恢复K线特征：body, upper_shadow, lower_shadow, volume_ratio/price_change

            if len(vector) == 5:
                body = vector[0]           # 实体：(收盘价 - 开盘价) / 开盘价
                upper_shadow = vector[1]   # 上影线：(最高价 - max(开盘价, 收盘价)) / 开盘价
                lower_shadow = vector[2]   # 下影线：(min(开盘价, 收盘价) - 最低价) / 开盘价
                volume_ratio = vector[3]   # 成交量比率：成交量 / 成交量移动平均

                # 从实体计算开盘价和收盘价
                # body = (c - o) / o => c = o * (1 + body)
                # 假设开盘价等于前收盘价（简化处理）
                o = prev_close
                c = o * (1 + body)

                # 从影线计算最高价和最低价
                # upper_shadow = (h - max(o, c)) / o => h = max(o, c) + upper_shadow * o
                # lower_shadow = (min(o, c) - l) / o => l = min(o, c) - lower_shadow * o
                h = max(o, c) + upper_shadow * o
                l = min(o, c) - lower_shadow * o

                # 从成交量比率计算成交量
                v = volume_ratio * ma_volume

            elif len(vector) == 4:
                body = vector[0]           # 实体：(收盘价 - 开盘价) / 开盘价
                upper_shadow = vector[1]   # 上影线：(最高价 - max(开盘价, 收盘价)) / 开盘价
                lower_shadow = vector[2]   # 下影线：(min(开盘价, 收盘价) - 最低价) / 开盘价
                price_change = vector[3]   # 价格变化：(收盘价 - 前收盘价) / 前收盘价

                # 从价格变化计算收盘价
                # price_change = (c - prev_close) / prev_close => c = prev_close * (1 + price_change)
                c = prev_close * (1 + price_change)

                # 从实体计算开盘价
                # body = (c - o) / o => o = c / (1 + body)
                if body != -1:  # 避免除零
                    o = c / (1 + body)
                else:
                    o = prev_close  # 回退到前收盘价

                # 从影线计算最高价和最低价
                h = max(o, c) + upper_shadow * o
                l = min(o, c) - lower_shadow * o

                # 使用默认成交量
                v = ma_volume

            else:
                raise ValueError(f"Invalid vector dimension: {len(vector)}")
        else:
            raise ValueError(f"Unknown vectorization method: {self.vectorization_method}")

        # 确保价格的合理性
        h = max(h, o, c)  # 最高价应该是最高的
        l = min(l, o, c)  # 最低价应该是最低的
        v = max(0, v)     # 成交量应该是正数

        # 额外检查，确保high-open >= 0（最高价不低于开盘价）
        if h < o:
            h = o

        # 确保low-open <= 0（最低价不高于开盘价）
        if l > o:
            l = o

        return {
            'open': float(o),
            'high': float(h),
            'low': float(l),
            'close': float(c),
            'volume': float(v)
        }

    def tokens_to_candlesticks(self, token_ids, base_df, code_id=None):
        """
        将token ID序列转换回K线数据序列。
        处理跳空标记并在结果中添加跳空信息。

        Args:
            token_ids: token ID序列
            base_df: 原始K线数据DataFrame，用于获取基准值
            code_id: 证券代码ID，如果使用证券代码维度则必须提供

        Returns:
            pd.DataFrame: 重建的K线数据
        """

        if code_id is None and self.use_code_dim:
            raise ValueError("证券代码ID不能为空")

        # 获取预处理后的DataFrame
        processed_df = self._preprocess_df(base_df)

        # 找到第一个有效的K线位置
        start_idx = 0
        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            if not pd.isna(row['ATR']) and not pd.isna(row['MA_Volume']) and not pd.isna(row['Prev_Close']):
                start_idx = i
                break

        # 创建结果DataFrame
        result_data = []

        # 获取基准值
        prev_close = processed_df.iloc[start_idx]['Prev_Close']
        ma_volume = processed_df.iloc[start_idx]['MA_Volume']
        atr_val = processed_df.iloc[start_idx]['ATR']
        print(f"Start index: {start_idx}, prev_close: {prev_close}, ma_volume: {ma_volume}, atr_val: {atr_val}")

        # 处理token序列
        i = 0  # 结果数据索引
        j = 0  # 原始数据索引偏移

        # 标记是否有跳空
        has_gap_up = False
        has_gap_down = False

        for token_id in token_ids:
            # 跳过特殊的BOS和EOS标记
            if token_id == self.token_to_id_map[self.bos_token] or token_id == self.token_to_id_map[self.eos_token]:
                continue

            # 处理跳空标记
            if token_id == self.gap_up_token_id:
                has_gap_up = True
                continue
            elif token_id == self.gap_down_token_id:
                has_gap_down = True
                continue

            # 跳过其他特殊标记
            if token_id >= self.num_embeddings:
                continue

            # 获取量化向量，使用ONNX模型进行解码
            vector = self.get_quantized_vector(token_id, debug=True, use_onnx=True, code_id=code_id)
            if vector is None:
                continue

            # 转换为K线数据
            print(f"Token ID {token_id} -> 向量: {vector}")
            candlestick = self.vector_to_candlestick(vector, prev_close, ma_volume, atr_val, debug=True)

            # 如果有跳空标记，调整K线数据
            if has_gap_up:
                # 向上跳空，调整开盘价
                gap_size = atr_val * self.gap_threshold
                candlestick['open'] = prev_close + gap_size
                candlestick['high'] = max(candlestick['high'], candlestick['open'])
                candlestick['low'] = max(candlestick['low'], candlestick['open'])
                candlestick['close'] = max(candlestick['close'], candlestick['open'])
                candlestick['gap_up'] = True
                has_gap_up = False
            elif has_gap_down:
                # 向下跳空，调整开盘价
                gap_size = atr_val * self.gap_threshold
                candlestick['open'] = prev_close - gap_size
                candlestick['high'] = min(candlestick['high'], candlestick['open'])
                candlestick['low'] = min(candlestick['low'], candlestick['open'])
                candlestick['close'] = min(candlestick['close'], candlestick['open'])
                candlestick['gap_down'] = True
                has_gap_down = False
            else:
                candlestick['gap_up'] = False
                candlestick['gap_down'] = False

            # 添加时间戳
            if i + start_idx + j < len(processed_df):
                candlestick['datetime'] = processed_df.iloc[i + start_idx + j]['datetime']
            else:
                # 如果超出原始数据范围，使用最后一个时间戳加上适当的偏移
                last_dt = processed_df.iloc[-1]['datetime']
                if isinstance(last_dt, pd.Timestamp):
                    # 假设是日线数据，每个时间戳加1天
                    candlestick['datetime'] = last_dt + pd.Timedelta(days=i - len(processed_df) + start_idx + j + 1)
                else:
                    candlestick['datetime'] = None

            result_data.append(candlestick)

            # 更新基准值
            prev_close = candlestick['close']

            # 更新ATR (简化计算)
            # tr = max(
            #     candlestick['high'] - candlestick['low'],
            #     abs(candlestick['high'] - prev_close),
            #     abs(candlestick['low'] - prev_close)
            # )
            # atr_val = (atr_val * (self.atr_period - 1) + tr) / self.atr_period

            # 更新MA_Volume (简化计算)
            # ma_volume = (ma_volume * (self.ma_volume_period - 1) + candlestick['volume']) / self.ma_volume_period
            if i + start_idx + j < len(processed_df):
                ma_volume = processed_df.iloc[i + start_idx + j]['MA_Volume']
                atr_val = processed_df.iloc[i + start_idx + j]['ATR']

            i += 1

        return pd.DataFrame(result_data)

    def visualize_tokenization(self, df_original, num_candles=20, figsize=(20, 15), show_vectors=True, show_details=True, code_id=None):
        """
        可视化原始K线、向量化后的数据、量化后的数据和重建的K线数据。

        Args:
            df_original: 原始K线数据
            num_candles: 要显示的K线数量
            figsize: 图形大小
            show_vectors: 是否显示向量化和量化后的数据
            show_details: 是否显示详细的数据比较
            code_id: 证券代码ID，如果使用证券代码维度则必须提供

        Returns:
            matplotlib.figure.Figure: 图形对象
        """
        if code_id is None and self.use_code_dim:
            raise ValueError("证券代码ID不能为空")

        # 确保数据足够
        if len(df_original) < num_candles + self.atr_period:
            print(f"Warning: Not enough data for visualization. Need at least {num_candles + self.atr_period + 1} candles.")
            num_candles = max(5, len(df_original) - self.atr_period)
        print(f"num_candles: {num_candles} / {len(df_original)}")

        # 选择要显示的数据
        df_subset = df_original.iloc[:num_candles + self.atr_period].copy()

        # 预处理数据
        processed_df = self._preprocess_df(df_subset)

        # 创建图形
        if show_vectors:
            fig = plt.figure(figsize=figsize)
            gs = fig.add_gridspec(4, 2, height_ratios=[3, 3, 2, 2])
            ax_orig = fig.add_subplot(gs[0, :])
            ax_recon = fig.add_subplot(gs[1, :], sharex=ax_orig)
            ax_vec_orig = fig.add_subplot(gs[2, 0])
            ax_vec_quant = fig.add_subplot(gs[2, 1], sharey=ax_vec_orig)
            ax_diff = fig.add_subplot(gs[3, :])
        else:
            fig = plt.figure(figsize=(figsize[0], figsize[1] * 0.6))
            gs = fig.add_gridspec(3, 1, height_ratios=[3, 3, 2])
            ax_orig = fig.add_subplot(gs[0, :])
            ax_recon = fig.add_subplot(gs[1, :], sharex=ax_orig)
            ax_diff = fig.add_subplot(gs[2, :])

        # 收集向量化和量化数据
        original_vectors = []
        quantized_vectors = []
        token_ids = []
        valid_indices = []

        # 从预处理后的数据中提取有效的K线
        valid_data = []

        # 处理证券代码
        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
                continue

            # 检测跳空
            gap_token_id = self._detect_gap(row)
            if gap_token_id is not None:
                token_ids.append(gap_token_id)

            # 向量化
            vector = candlestick_to_vector(
                row,
                row['Prev_Close'],
                row['MA_Volume'],
                row['ATR'],
                self.embedding_dim,
                self.vectorization_method
            )

            if np.all(vector == 0):
                token_ids.append(self.unk_token_id)
                continue

            original_vectors.append(vector)

            # 量化
            vector_tensor = torch.tensor(vector, dtype=torch.float32)

            # 如果使用证券代码维度
            if self.use_code_dim and code_id is not None:
                # 获取证券代码嵌入
                code_tensor = torch.tensor([code_id], dtype=torch.long)
                code_emb = self.code_embedding(code_tensor).squeeze(0)

                # 调整向量
                vector_tensor = vector_tensor + 0.1 * code_emb[:self.embedding_dim]

            with torch.no_grad():
                token_id = self.vq_layer(vector_tensor).item()

            token_ids.append(token_id)

            # 获取量化后的向量，使用ONNX模型进行解码
            if token_id < self.num_embeddings:
                quantized_vector = self.get_quantized_vector(token_id, debug=False, use_onnx=False, code_id=code_id)
                quantized_vectors.append(quantized_vector)
                valid_indices.append(i)
                valid_data.append(row)

        # 解码
        df_reconstructed = self.tokens_to_candlesticks(token_ids, df_subset, code_id=code_id)
        # print(df_subset)
        # print(df_reconstructed)

        # 绘制原始K线
        self._plot_candlesticks(ax_orig, processed_df.iloc[valid_indices],
                               title=f"Original Candlesticks ({self.vectorization_method})")

        # 绘制重建的K线
        self._plot_candlesticks(ax_recon, df_reconstructed,
                               title="Reconstructed Candlesticks")

        # 确保两个K线图的Y轴范围一致
        if len(processed_df.iloc[valid_indices]) > 0 and len(df_reconstructed) > 0:
            # 获取两个图表的Y轴范围
            orig_ymin, orig_ymax = ax_orig.get_ylim()
            recon_ymin, recon_ymax = ax_recon.get_ylim()

            # 使用共同的Y轴范围
            ymin = min(orig_ymin, recon_ymin)
            ymax = max(orig_ymax, recon_ymax)

            # 设置相同的Y轴范围
            ax_orig.set_ylim(ymin, ymax)
            ax_recon.set_ylim(ymin, ymax)

        if show_vectors and original_vectors and quantized_vectors:
            # 绘制原始向量
            original_vectors_array = np.array(original_vectors)
            ax_vec_orig.imshow(original_vectors_array.T, aspect='auto', cmap='viridis')
            ax_vec_orig.set_title("Original Vectors")
            ax_vec_orig.set_xlabel("Candle Index")
            ax_vec_orig.set_ylabel("Vector Dimension")

            # 绘制量化后的向量
            quantized_vectors_array = np.array(quantized_vectors)
            im = ax_vec_quant.imshow(quantized_vectors_array.T, aspect='auto', cmap='viridis')
            ax_vec_quant.set_title("Quantized Vectors")
            ax_vec_quant.set_xlabel("Candle Index")

            # 添加颜色条
            cbar = fig.colorbar(im, ax=[ax_vec_orig, ax_vec_quant], orientation='vertical', pad=0.01)
            cbar.set_label('Vector Value')

            # 计算向量差异
            if len(original_vectors_array) == len(quantized_vectors_array):
                vector_diff = np.abs(original_vectors_array - quantized_vectors_array)
                vector_diff_mean = np.mean(vector_diff, axis=1)

                # 绘制向量差异
                ax_diff.bar(range(len(vector_diff_mean)), vector_diff_mean, color='skyblue')
                ax_diff.set_title("Mean Absolute Error Between Original and Quantized Vectors")
                ax_diff.set_xlabel("Candle Index")
                ax_diff.set_ylabel("Mean Absolute Error")
                ax_diff.grid(True, alpha=0.3)
        else:
            # 计算价格差异
            if not df_reconstructed.empty and not processed_df.empty:
                # 确保两个DataFrame有相同的索引
                min_len = min(len(df_reconstructed), len(processed_df.iloc[valid_indices]))

                if min_len > 0:
                    orig_prices = processed_df.iloc[valid_indices[:min_len]]['close'].values
                    recon_prices = df_reconstructed['close'].iloc[:min_len].values

                    # 计算价格差异百分比
                    price_diff_pct = np.abs(recon_prices - orig_prices) / orig_prices * 100

                    # 绘制价格差异
                    ax_diff.bar(range(len(price_diff_pct)), price_diff_pct, color='salmon')
                    ax_diff.set_title("Price Difference (%) Between Original and Reconstructed Candlesticks")
                    ax_diff.set_xlabel("Candle Index")
                    ax_diff.set_ylabel("Price Difference (%)")
                    ax_diff.grid(True, alpha=0.3)

        # 如果需要显示详细数据比较
        if show_details and not df_reconstructed.empty and len(valid_data) > 0:
            # 创建详细比较表格
            valid_df = pd.DataFrame(valid_data)
            comparison_data = []

            for i in range(min(len(df_reconstructed), len(valid_df))):
                orig = valid_df.iloc[i]
                recon = df_reconstructed.iloc[i]

                # 计算价格差异百分比
                open_diff_pct = abs(recon['open'] - orig['open']) / orig['open'] * 100 if orig['open'] != 0 else 0
                high_diff_pct = abs(recon['high'] - orig['high']) / orig['high'] * 100 if orig['high'] != 0 else 0
                low_diff_pct = abs(recon['low'] - orig['low']) / orig['low'] * 100 if orig['low'] != 0 else 0
                close_diff_pct = abs(recon['close'] - orig['close']) / orig['close'] * 100 if orig['close'] != 0 else 0

                comparison_data.append({
                    'Index': i,
                    'Token ID': token_ids[i] if i < len(token_ids) else None,
                    'Orig Open': orig['open'],
                    'Recon Open': recon['open'],
                    'Open Diff %': open_diff_pct,
                    'Orig High': orig['high'],
                    'Recon High': recon['high'],
                    'High Diff %': high_diff_pct,
                    'Orig Low': orig['low'],
                    'Recon Low': recon['low'],
                    'Low Diff %': low_diff_pct,
                    'Orig Close': orig['close'],
                    'Recon Close': recon['close'],
                    'Close Diff %': close_diff_pct,
                })

            comparison_df = pd.DataFrame(comparison_data)

            # 打印详细比较
            print("\n详细数据比较 (前5行):")
            pd.set_option('display.precision', 2)
            print(comparison_df.head().to_string())

            # 计算平均差异
            avg_open_diff = comparison_df['Open Diff %'].mean()
            avg_high_diff = comparison_df['High Diff %'].mean()
            avg_low_diff = comparison_df['Low Diff %'].mean()
            avg_close_diff = comparison_df['Close Diff %'].mean()

            print(f"\n平均差异百分比:")
            print(f"Open: {avg_open_diff:.2f}%")
            print(f"High: {avg_high_diff:.2f}%")
            print(f"Low: {avg_low_diff:.2f}%")
            print(f"Close: {avg_close_diff:.2f}%")
            print(f"Overall: {(avg_open_diff + avg_high_diff + avg_low_diff + avg_close_diff) / 4:.2f}%")

            # 添加文本注释到图表
            text = f"平均差异百分比:\nOpen: {avg_open_diff:.2f}%\nHigh: {avg_high_diff:.2f}%\nLow: {avg_low_diff:.2f}%\nClose: {avg_close_diff:.2f}%"
            fig.text(0.02, 0.02, text, fontsize=10, bbox=dict(facecolor='white', alpha=0.8))

        plt.tight_layout()
        return fig

    def _plot_candlesticks(self, ax, df, title=None):
        """辅助函数，绘制K线图，包括跳空标记"""
        if len(df) == 0:
            ax.text(0.5, 0.5, "No data to plot", ha='center', va='center')
            if title:
                ax.set_title(title)
            return

        # 设置时间轴
        if 'datetime' in df.columns:
            dates = df['datetime']
            if len(dates) > 0 and isinstance(dates.iloc[0], pd.Timestamp):
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(df)//10)))
        else:
            dates = range(len(df))

        # 计算每个K线的宽度
        width = 0.6

        # 绘制K线
        for i, row in enumerate(df.iterrows()):
            # row是一个元组(index, Series)，我们需要Series部分
            row = row[1]

            # 获取OHLC值
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']

            # 确定颜色
            color = 'red' if close_price > open_price else 'green'

            # 绘制影线
            ax.plot([i, i], [low_price, high_price], color=color, linewidth=1)

            # 绘制实体
            rect = Rectangle(
                xy=(i - width/2, min(open_price, close_price)),
                width=width,
                height=abs(close_price - open_price),
                facecolor=color,
                edgecolor=color,
                alpha=0.8
            )
            ax.add_patch(rect)

            # 标记跳空
            if 'gap_up' in row and row['gap_up']:
                # 在K线上方添加向上箭头标记跳空
                ax.annotate('↑', xy=(i, high_price), xytext=(i, high_price * 1.01),
                           ha='center', va='bottom', color='red', fontsize=12, weight='bold')
            elif 'gap_down' in row and row['gap_down']:
                # 在K线下方添加向下箭头标记跳空
                ax.annotate('↓', xy=(i, low_price), xytext=(i, low_price * 0.99),
                           ha='center', va='top', color='green', fontsize=12, weight='bold')

        # 设置标题和标签
        if title:
            ax.set_title(title)
        ax.set_ylabel('Price')
        ax.grid(True, alpha=0.3)

        # 设置x轴刻度
        ax.set_xticks(range(len(df)))
        if 'datetime' in df.columns:
            date_labels = [d.strftime('%Y-%m-%d') if isinstance(d, pd.Timestamp) else str(i)
                          for i, d in enumerate(df['datetime'])]
            ax.set_xticklabels(date_labels, rotation=45)

        # 自动调整y轴范围
        ax.autoscale_view()


def validate_onnx_codebook_models(df_ohlcv, tokenizer, encoder_onnx_path, decoder_onnx_path, visualize=True, num_samples=None):
    """
    验证ONNX格式的码本编码器和解码器模型的有效性。

    Args:
        df_ohlcv: 原始K线数据DataFrame
        tokenizer: CandlestickVQTokenizer实例
        encoder_onnx_path: 编码器ONNX模型路径
        decoder_onnx_path: 解码器ONNX模型路径
        visualize: 是否可视化结果
        num_samples: 要检查的样本数量，None表示全部

    Returns:
        dict: 包含验证结果的字典
    """
    print("开始验证ONNX码本模型的有效性...")

    try:
        # 1. 加载ONNX模型
        print(f"加载编码器模型: {encoder_onnx_path}")
        encoder_session = ort.InferenceSession(encoder_onnx_path)

        print(f"加载解码器模型: {decoder_onnx_path}")
        decoder_session = ort.InferenceSession(decoder_onnx_path)

        # 2. 预处理原始数据
        processed_df = tokenizer._preprocess_df(df_ohlcv)
        print(f"预处理后数据形状: {processed_df.shape} / {df_ohlcv.shape}")

        # 找到有效的K线起始位置
        start_idx = 0
        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            if not pd.isna(row['ATR']) and not pd.isna(row['MA_Volume']) and not pd.isna(row['Prev_Close']):
                start_idx = i
                break

        # 有效的原始数据
        valid_df = processed_df.iloc[start_idx:].reset_index(drop=True)
        print(valid_df)

        # 限制样本数量
        if num_samples is not None and num_samples < len(valid_df):
            valid_df = valid_df.iloc[:num_samples].copy()

        print(f"使用 {len(valid_df)} 个K线样本进行验证")

        # 3. 准备向量化数据
        vectors = []
        for i in range(len(valid_df)):
            row = valid_df.iloc[i]
            vector = candlestick_to_vector(
                row,
                row['Prev_Close'],
                row['MA_Volume'],
                row['ATR'],
                tokenizer.embedding_dim,
                tokenizer.vectorization_method
            )
            vectors.append(vector)

        vectors_array = np.array(vectors, dtype=np.float32)
        print(f"向量化数据形状: {vectors_array.shape}")

        # 4. 使用PyTorch模型进行编码和解码（作为基准）
        print("使用PyTorch模型进行编码和解码...")
        pytorch_token_ids = []
        pytorch_reconstructed_vectors = []

        with torch.no_grad():
            for vector in vectors:
                vector_tensor = torch.tensor(vector, dtype=torch.float32)
                token_id = tokenizer.vq_layer(vector_tensor).item()
                pytorch_token_ids.append(token_id)

                if token_id < tokenizer.num_embeddings:
                    reconstructed_vector = tokenizer.get_quantized_vector(token_id)
                    pytorch_reconstructed_vectors.append(reconstructed_vector)
                else:
                    pytorch_reconstructed_vectors.append(np.zeros_like(vector))

        # 5. 使用ONNX模型进行编码
        print("使用ONNX编码器模型进行编码...")
        encoder_input_name = encoder_session.get_inputs()[0].name
        encoder_output_name = encoder_session.get_outputs()[0].name

        onnx_token_ids = []
        for vector in vectors:
            # 准备输入数据
            input_data = np.array([vector], dtype=np.float32)

            # 运行ONNX编码器模型
            encoder_outputs = encoder_session.run([encoder_output_name], {encoder_input_name: input_data})
            token_id = encoder_outputs[0][0]
            onnx_token_ids.append(int(token_id))

        # 6. 使用ONNX模型进行解码
        print("使用ONNX解码器模型进行解码...")
        decoder_input_name = decoder_session.get_inputs()[0].name
        decoder_output_name = decoder_session.get_outputs()[0].name

        onnx_reconstructed_vectors = []
        for token_id in onnx_token_ids:
            if token_id < tokenizer.num_embeddings:
                # 准备输入数据
                input_data = np.array([token_id], dtype=np.int64)

                # 运行ONNX解码器模型
                decoder_outputs = decoder_session.run([decoder_output_name], {decoder_input_name: input_data})
                reconstructed_vector = decoder_outputs[0][0]
                print(token_id, reconstructed_vector)
                onnx_reconstructed_vectors.append(reconstructed_vector)
            else:
                onnx_reconstructed_vectors.append(np.zeros(tokenizer.embedding_dim, dtype=np.float32))

        # 7. 比较PyTorch和ONNX模型的结果
        print("比较PyTorch和ONNX模型的结果...")

        # 编码结果比较
        encoding_match_count = sum(1 for pt_id, onnx_id in zip(pytorch_token_ids, onnx_token_ids) if pt_id == onnx_id)
        encoding_match_rate = encoding_match_count / len(pytorch_token_ids) * 100

        # 解码结果比较
        decoding_diff = []
        for pt_vec, onnx_vec in zip(pytorch_reconstructed_vectors, onnx_reconstructed_vectors):
            if len(pt_vec) == len(onnx_vec):
                diff = np.mean(np.abs(pt_vec - onnx_vec))
                decoding_diff.append(diff)

        avg_decoding_diff = np.mean(decoding_diff) if decoding_diff else 0

        # 8. 检查ONNX解码结果的合理性
        print("检查ONNX解码结果的合理性...")

        # 使用ONNX解码结果重建K线
        onnx_reconstructed_candles = []

        for i, vector in enumerate(onnx_reconstructed_vectors):
            row = valid_df.iloc[i]
            candle = tokenizer.vector_to_candlestick(
                vector,
                row['Prev_Close'],
                row['MA_Volume'],
                row['ATR']
            )
            onnx_reconstructed_candles.append(candle)

        df_onnx_reconstructed = pd.DataFrame(onnx_reconstructed_candles)

        # 检查最高价是否真的是最高点
        high_is_highest_onnx = ((df_onnx_reconstructed['high'] >= df_onnx_reconstructed['open']) &
                               (df_onnx_reconstructed['high'] >= df_onnx_reconstructed['close']) &
                               (df_onnx_reconstructed['high'] >= df_onnx_reconstructed['low'])).all()

        # 检查最低价是否真的是最低点
        low_is_lowest_onnx = ((df_onnx_reconstructed['low'] <= df_onnx_reconstructed['open']) &
                             (df_onnx_reconstructed['low'] <= df_onnx_reconstructed['close']) &
                             (df_onnx_reconstructed['low'] <= df_onnx_reconstructed['high'])).all()

        # 计算不符合条件的K线数量和比例
        high_not_highest_count = (~((df_onnx_reconstructed['high'] >= df_onnx_reconstructed['open']) &
                                  (df_onnx_reconstructed['high'] >= df_onnx_reconstructed['close']) &
                                  (df_onnx_reconstructed['high'] >= df_onnx_reconstructed['low']))).sum()

        low_not_lowest_count = (~((df_onnx_reconstructed['low'] <= df_onnx_reconstructed['open']) &
                                (df_onnx_reconstructed['low'] <= df_onnx_reconstructed['close']) &
                                (df_onnx_reconstructed['low'] <= df_onnx_reconstructed['high']))).sum()

        # 计算原始数据和ONNX重建数据之间的差异百分比
        open_diff_pct = np.abs(df_onnx_reconstructed['open'].values - valid_df['open'].values) / valid_df['open'].values * 100
        high_diff_pct = np.abs(df_onnx_reconstructed['high'].values - valid_df['high'].values) / valid_df['high'].values * 100
        low_diff_pct = np.abs(df_onnx_reconstructed['low'].values - valid_df['low'].values) / valid_df['low'].values * 100
        close_diff_pct = np.abs(df_onnx_reconstructed['close'].values - valid_df['close'].values) / valid_df['close'].values * 100

        # 计算平均差异
        avg_open_diff = np.nanmean(open_diff_pct)
        avg_high_diff = np.nanmean(high_diff_pct)
        avg_low_diff = np.nanmean(low_diff_pct)
        avg_close_diff = np.nanmean(close_diff_pct)
        avg_overall_diff = (avg_open_diff + avg_high_diff + avg_low_diff + avg_close_diff) / 4

        # 9. 输出验证结果
        print("\nONNX码本模型验证结果:")
        print(f"编码匹配率: {encoding_match_rate:.2f}% ({encoding_match_count}/{len(pytorch_token_ids)})")
        print(f"解码平均差异: {avg_decoding_diff:.6f}")
        print(f"ONNX重建数据中最高价是否真的最高: {high_is_highest_onnx}")
        print(f"ONNX重建数据中最低价是否真的最低: {low_is_lowest_onnx}")
        print(f"ONNX重建数据中最高价不是最高点的K线数量: {high_not_highest_count} ({high_not_highest_count/len(df_onnx_reconstructed)*100:.2f}%)")
        print(f"ONNX重建数据中最低价不是最低点的K线数量: {low_not_lowest_count} ({low_not_lowest_count/len(df_onnx_reconstructed)*100:.2f}%)")
        print("\n平均差异百分比:")
        print(f"开盘价: {avg_open_diff:.2f}%")
        print(f"最高价: {avg_high_diff:.2f}%")
        print(f"最低价: {avg_low_diff:.2f}%")
        print(f"收盘价: {avg_close_diff:.2f}%")
        print(f"总体平均: {avg_overall_diff:.2f}%")

        # 10. 找出不符合条件的K线索引
        high_not_highest_indices = np.where(~((df_onnx_reconstructed['high'] >= df_onnx_reconstructed['open']) &
                                            (df_onnx_reconstructed['high'] >= df_onnx_reconstructed['close']) &
                                            (df_onnx_reconstructed['high'] >= df_onnx_reconstructed['low'])))[0]

        low_not_lowest_indices = np.where(~((df_onnx_reconstructed['low'] <= df_onnx_reconstructed['open']) &
                                          (df_onnx_reconstructed['low'] <= df_onnx_reconstructed['close']) &
                                          (df_onnx_reconstructed['low'] <= df_onnx_reconstructed['high'])))[0]

        problem_indices = np.unique(np.concatenate([high_not_highest_indices, low_not_lowest_indices]))

        if len(problem_indices) > 0:
            print(f"\n发现 {len(problem_indices)} 个存在问题的K线:")
            for idx in problem_indices[:min(10, len(problem_indices))]:  # 只显示前10个
                orig = valid_df.iloc[idx]
                recon = df_onnx_reconstructed.iloc[idx]
                print(f"\n索引 {idx}:")
                print(f"原始 - 开:{orig['open']:.2f} 高:{orig['high']:.2f} 低:{orig['low']:.2f} 收:{orig['close']:.2f}")
                print(f"ONNX重建 - 开:{recon['open']:.2f} 高:{recon['high']:.2f} 低:{recon['low']:.2f} 收:{recon['close']:.2f}")

                # 检查具体问题
                high_is_highest = ((recon['high'] >= recon['open']) &
                                  (recon['high'] >= recon['close']) &
                                  (recon['high'] >= recon['low']))

                low_is_lowest = ((recon['low'] <= recon['open']) &
                                (recon['low'] <= recon['close']) &
                                (recon['low'] <= recon['high']))

                if not high_is_highest:
                    print("  问题: 最高价不是最高点")
                if not low_is_lowest:
                    print("  问题: 最低价不是最低点")

        # 11. 可视化结果
        if visualize and len(problem_indices) > 0:
            # 创建一个图形来显示有问题的K线
            n_problems_to_show = min(5, len(problem_indices))  # 最多显示5个问题K线

            fig = plt.figure(figsize=(15, 5 * n_problems_to_show))
            gs = gridspec.GridSpec(n_problems_to_show, 2, figure=fig)

            for i, idx in enumerate(problem_indices[:n_problems_to_show]):
                # 原始K线
                ax_orig = fig.add_subplot(gs[i, 0])
                # 重建K线
                ax_recon = fig.add_subplot(gs[i, 1])

                # 获取数据
                orig_row = valid_df.iloc[idx]
                recon_row = df_onnx_reconstructed.iloc[idx]

                # 绘制原始K线
                _plot_single_candlestick(ax_orig, orig_row, title=f"原始K线 (索引 {idx})")

                # 绘制重建K线
                _plot_single_candlestick(ax_recon, recon_row, title=f"ONNX重建K线 (索引 {idx})")

                # 确保两个K线图的Y轴范围一致
                # 获取两个图表的Y轴范围
                orig_ymin, orig_ymax = ax_orig.get_ylim()
                recon_ymin, recon_ymax = ax_recon.get_ylim()

                # 使用共同的Y轴范围
                ymin = min(orig_ymin, recon_ymin)
                ymax = max(orig_ymax, recon_ymax)

                # 设置相同的Y轴范围
                ax_orig.set_ylim(ymin, ymax)
                ax_recon.set_ylim(ymin, ymax)

                # 添加问题标记
                high_is_highest = ((recon_row['high'] >= recon_row['open']) &
                                  (recon_row['high'] >= recon_row['close']) &
                                  (recon_row['high'] >= recon_row['low']))

                low_is_lowest = ((recon_row['low'] <= recon_row['open']) &
                                (recon_row['low'] <= recon_row['close']) &
                                (recon_row['low'] <= recon_row['high']))

                if not high_is_highest:
                    ax_recon.annotate('最高价不是最高点!',
                                     xy=(0, recon_row['high']),
                                     xytext=(0, recon_row['high'] * 1.05),
                                     arrowprops=dict(facecolor='red', shrink=0.05),
                                     color='red', fontsize=12)

                if not low_is_lowest:
                    ax_recon.annotate('最低价不是最低点!',
                                     xy=(0, recon_row['low']),
                                     xytext=(0, recon_row['low'] * 0.95),
                                     arrowprops=dict(facecolor='red', shrink=0.05),
                                     color='red', fontsize=12)

            plt.tight_layout()
            plt.savefig("onnx_codebook_validation_problems.png")
            print(f"问题K线可视化已保存到 onnx_codebook_validation_problems.png")
            plt.close(fig)

        # 12. 返回验证结果
        return {
            "encoding_match_rate": encoding_match_rate,
            "encoding_match_count": encoding_match_count,
            "total_tokens": len(pytorch_token_ids),
            "avg_decoding_diff": avg_decoding_diff,
            "high_is_highest_onnx": high_is_highest_onnx,
            "low_is_lowest_onnx": low_is_lowest_onnx,
            "high_not_highest_count": high_not_highest_count,
            "high_not_highest_pct": high_not_highest_count/len(df_onnx_reconstructed)*100,
            "low_not_lowest_count": low_not_lowest_count,
            "low_not_lowest_pct": low_not_lowest_count/len(df_onnx_reconstructed)*100,
            "avg_open_diff": avg_open_diff,
            "avg_high_diff": avg_high_diff,
            "avg_low_diff": avg_low_diff,
            "avg_close_diff": avg_close_diff,
            "avg_overall_diff": avg_overall_diff,
            "problem_indices": problem_indices.tolist() if len(problem_indices) > 0 else []
        }

    except Exception as e:
        print(f"验证ONNX码本模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def validate_codebook_accuracy(df_ohlcv, tokenizer, visualize=True, num_samples=None):
    """
    验证码本的有效性，特别是检查通过token还原的K线是否保持了原始数据的关键特性。

    Args:
        df_ohlcv: 原始K线数据DataFrame
        tokenizer: CandlestickVQTokenizer实例
        visualize: 是否可视化结果
        num_samples: 要检查的样本数量，None表示全部

    Returns:
        dict: 包含验证结果的字典
    """
    print("开始验证码本的有效性...")

    # 1. 编码原始数据
    token_ids = tokenizer.encode(df_ohlcv)
    print(f"原始数据编码为 {len(token_ids)} 个token")

    # 2. 解码回K线数据
    df_reconstructed = tokenizer.tokens_to_candlesticks(token_ids, df_ohlcv)
    print(f"重建的K线数据包含 {len(df_reconstructed)} 行")

    # 3. 预处理原始数据以便比较
    processed_df = tokenizer._preprocess_df(df_ohlcv)

    # 找到有效的K线起始位置
    start_idx = 0
    for i in range(len(processed_df)):
        row = processed_df.iloc[i]
        if not pd.isna(row['ATR']) and not pd.isna(row['MA_Volume']) and not pd.isna(row['Prev_Close']):
            start_idx = i
            break

    # 有效的原始数据
    valid_df = processed_df.iloc[start_idx:].reset_index(drop=True)

    # 4. 确保比较的数据长度一致
    min_len = min(len(valid_df), len(df_reconstructed))
    if min_len == 0:
        print("没有足够的数据进行比较")
        return None

    # 限制样本数量
    if num_samples is not None and num_samples < min_len:
        min_len = num_samples

    valid_df = valid_df.iloc[:min_len].copy()
    df_reconstructed = df_reconstructed.iloc[:min_len].copy()

    # 5. 验证关键特性
    # 检查最高价是否真的是最高点
    high_is_highest_orig = ((valid_df['high'] >= valid_df['open']) &
                           (valid_df['high'] >= valid_df['close']) &
                           (valid_df['high'] >= valid_df['low'])).all()

    high_is_highest_recon = ((df_reconstructed['high'] >= df_reconstructed['open']) &
                            (df_reconstructed['high'] >= df_reconstructed['close']) &
                            (df_reconstructed['high'] >= df_reconstructed['low'])).all()

    # 检查最低价是否真的是最低点
    low_is_lowest_orig = ((valid_df['low'] <= valid_df['open']) &
                         (valid_df['low'] <= valid_df['close']) &
                         (valid_df['low'] <= valid_df['high'])).all()

    low_is_lowest_recon = ((df_reconstructed['low'] <= df_reconstructed['open']) &
                          (df_reconstructed['low'] <= df_reconstructed['close']) &
                          (df_reconstructed['low'] <= df_reconstructed['high'])).all()

    # 6. 计算不符合条件的K线数量和比例
    high_not_highest_count = (~((df_reconstructed['high'] >= df_reconstructed['open']) &
                              (df_reconstructed['high'] >= df_reconstructed['close']) &
                              (df_reconstructed['high'] >= df_reconstructed['low']))).sum()

    low_not_lowest_count = (~((df_reconstructed['low'] <= df_reconstructed['open']) &
                            (df_reconstructed['low'] <= df_reconstructed['close']) &
                            (df_reconstructed['low'] <= df_reconstructed['high']))).sum()

    # 7. 计算原始数据和还原数据之间的差异百分比
    # 计算OHLC的差异百分比
    open_diff_pct = np.abs(df_reconstructed['open'].values - valid_df['open'].values) / valid_df['open'].values * 100
    high_diff_pct = np.abs(df_reconstructed['high'].values - valid_df['high'].values) / valid_df['high'].values * 100
    low_diff_pct = np.abs(df_reconstructed['low'].values - valid_df['low'].values) / valid_df['low'].values * 100
    close_diff_pct = np.abs(df_reconstructed['close'].values - valid_df['close'].values) / valid_df['close'].values * 100

    # 计算平均差异
    avg_open_diff = np.nanmean(open_diff_pct)
    avg_high_diff = np.nanmean(high_diff_pct)
    avg_low_diff = np.nanmean(low_diff_pct)
    avg_close_diff = np.nanmean(close_diff_pct)
    avg_overall_diff = (avg_open_diff + avg_high_diff + avg_low_diff + avg_close_diff) / 4

    # 8. 输出验证结果
    print("\n码本有效性验证结果:")
    print(f"检查的K线数量: {min_len}")
    print(f"原始数据中最高价是否真的最高: {high_is_highest_orig}")
    print(f"重建数据中最高价是否真的最高: {high_is_highest_recon}")
    print(f"原始数据中最低价是否真的最低: {low_is_lowest_orig}")
    print(f"重建数据中最低价是否真的最低: {low_is_lowest_recon}")
    print(f"重建数据中最高价不是最高点的K线数量: {high_not_highest_count} ({high_not_highest_count/min_len*100:.2f}%)")
    print(f"重建数据中最低价不是最低点的K线数量: {low_not_lowest_count} ({low_not_lowest_count/min_len*100:.2f}%)")
    print("\n平均差异百分比:")
    print(f"开盘价: {avg_open_diff:.2f}%")
    print(f"最高价: {avg_high_diff:.2f}%")
    print(f"最低价: {avg_low_diff:.2f}%")
    print(f"收盘价: {avg_close_diff:.2f}%")
    print(f"总体平均: {avg_overall_diff:.2f}%")

    # 9. 找出不符合条件的K线索引
    high_not_highest_indices = np.where(~((df_reconstructed['high'] >= df_reconstructed['open']) &
                                        (df_reconstructed['high'] >= df_reconstructed['close']) &
                                        (df_reconstructed['high'] >= df_reconstructed['low'])))[0]

    low_not_lowest_indices = np.where(~((df_reconstructed['low'] <= df_reconstructed['open']) &
                                      (df_reconstructed['low'] <= df_reconstructed['close']) &
                                      (df_reconstructed['low'] <= df_reconstructed['high'])))[0]

    problem_indices = np.unique(np.concatenate([high_not_highest_indices, low_not_lowest_indices]))

    if len(problem_indices) > 0:
        print(f"\n发现 {len(problem_indices)} 个存在问题的K线:")
        for idx in problem_indices[:min(10, len(problem_indices))]:  # 只显示前10个
            orig = valid_df.iloc[idx]
            recon = df_reconstructed.iloc[idx]
            print(f"\n索引 {idx}:")
            print(f"原始 - 开:{orig['open']:.2f} 高:{orig['high']:.2f} 低:{orig['low']:.2f} 收:{orig['close']:.2f}")
            print(f"重建 - 开:{recon['open']:.2f} 高:{recon['high']:.2f} 低:{recon['low']:.2f} 收:{recon['close']:.2f}")

            # 检查具体问题
            high_is_highest = ((recon['high'] >= recon['open']) &
                              (recon['high'] >= recon['close']) &
                              (recon['high'] >= recon['low']))

            low_is_lowest = ((recon['low'] <= recon['open']) &
                            (recon['low'] <= recon['close']) &
                            (recon['low'] <= recon['high']))

            if not high_is_highest:
                print("  问题: 最高价不是最高点")
            if not low_is_lowest:
                print("  问题: 最低价不是最低点")

    # 10. 可视化结果
    if visualize and len(problem_indices) > 0:
        # 创建一个图形来显示有问题的K线
        n_problems_to_show = min(5, len(problem_indices))  # 最多显示5个问题K线

        fig = plt.figure(figsize=(15, 5 * n_problems_to_show))
        gs = gridspec.GridSpec(n_problems_to_show, 2, figure=fig)

        for i, idx in enumerate(problem_indices[:n_problems_to_show]):
            # 原始K线
            ax_orig = fig.add_subplot(gs[i, 0])
            # 重建K线
            ax_recon = fig.add_subplot(gs[i, 1])

            # 获取数据
            orig_row = valid_df.iloc[idx]
            recon_row = df_reconstructed.iloc[idx]

            # 绘制原始K线
            _plot_single_candlestick(ax_orig, orig_row, title=f"原始K线 (索引 {idx})")

            # 绘制重建K线
            _plot_single_candlestick(ax_recon, recon_row, title=f"重建K线 (索引 {idx})")

            # 确保两个K线图的Y轴范围一致
            # 获取两个图表的Y轴范围
            orig_ymin, orig_ymax = ax_orig.get_ylim()
            recon_ymin, recon_ymax = ax_recon.get_ylim()

            # 使用共同的Y轴范围
            ymin = min(orig_ymin, recon_ymin)
            ymax = max(orig_ymax, recon_ymax)

            # 设置相同的Y轴范围
            ax_orig.set_ylim(ymin, ymax)
            ax_recon.set_ylim(ymin, ymax)

            # 添加问题标记
            high_is_highest = ((recon_row['high'] >= recon_row['open']) &
                              (recon_row['high'] >= recon_row['close']) &
                              (recon_row['high'] >= recon_row['low']))

            low_is_lowest = ((recon_row['low'] <= recon_row['open']) &
                            (recon_row['low'] <= recon_row['close']) &
                            (recon_row['low'] <= recon_row['high']))

            if not high_is_highest:
                ax_recon.annotate('最高价不是最高点!',
                                 xy=(0, recon_row['high']),
                                 xytext=(0, recon_row['high'] * 1.05),
                                 arrowprops=dict(facecolor='red', shrink=0.05),
                                 color='red', fontsize=12)

            if not low_is_lowest:
                ax_recon.annotate('最低价不是最低点!',
                                 xy=(0, recon_row['low']),
                                 xytext=(0, recon_row['low'] * 0.95),
                                 arrowprops=dict(facecolor='red', shrink=0.05),
                                 color='red', fontsize=12)

        plt.tight_layout()
        plt.savefig("codebook_validation_problems.png")
        print(f"问题K线可视化已保存到 codebook_validation_problems.png")
        plt.close(fig)

    # 11. 返回验证结果
    return {
        "high_is_highest_orig": high_is_highest_orig,
        "high_is_highest_recon": high_is_highest_recon,
        "low_is_lowest_orig": low_is_lowest_orig,
        "low_is_lowest_recon": low_is_lowest_recon,
        "high_not_highest_count": high_not_highest_count,
        "high_not_highest_pct": high_not_highest_count/min_len*100,
        "low_not_lowest_count": low_not_lowest_count,
        "low_not_lowest_pct": low_not_lowest_count/min_len*100,
        "avg_open_diff": avg_open_diff,
        "avg_high_diff": avg_high_diff,
        "avg_low_diff": avg_low_diff,
        "avg_close_diff": avg_close_diff,
        "avg_overall_diff": avg_overall_diff,
        "problem_indices": problem_indices.tolist() if len(problem_indices) > 0 else []
    }

def _plot_single_candlestick(ax, row, title=None):
    """
    绘制单个K线图

    Args:
        ax: matplotlib轴对象
        row: 包含OHLC数据的Series或dict
        title: 图表标题
    """
    # 获取OHLC值
    if isinstance(row, pd.Series):
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']
    else:
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']

    # 确定颜色
    color = 'red' if close_price > open_price else 'green'

    # 绘制影线
    ax.plot([0, 0], [low_price, high_price], color=color, linewidth=2)

    # 绘制实体
    width = 0.6
    rect = Rectangle(
        xy=(-width/2, min(open_price, close_price)),
        width=width,
        height=abs(close_price - open_price),
        facecolor=color,
        edgecolor=color,
        alpha=0.8
    )
    ax.add_patch(rect)

    # 标记OHLC值
    ax.annotate(f'O: {open_price:.2f}', xy=(-0.8, open_price), fontsize=10, color='black')
    ax.annotate(f'H: {high_price:.2f}', xy=(-0.8, high_price), fontsize=10, color='black')
    ax.annotate(f'L: {low_price:.2f}', xy=(-0.8, low_price), fontsize=10, color='black')
    ax.annotate(f'C: {close_price:.2f}', xy=(-0.8, close_price), fontsize=10, color='black')

    # 设置标题和标签
    if title:
        ax.set_title(title)
    ax.set_ylabel('Price')
    ax.grid(True, alpha=0.3)

    # 设置x轴范围
    ax.set_xlim(-1, 1)

    # 自动调整y轴范围，留出一些空间用于标注
    y_min = low_price * 0.9
    y_max = high_price * 1.1
    ax.set_ylim(y_min, y_max)

    # 隐藏x轴刻度
    ax.set_xticks([])

# --- 示例用法 ---
if __name__ == '__main__':
    import argparse
    import os

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='验证码本的有效性')
    parser.add_argument('--data_file', type=str, default='f:/hqdata/fut_top_min1.parquet',
                        help='K线数据文件路径')
    parser.add_argument('--codebook_path', type=str,
                        default='e:/lab/RoboQuant/pylab/models/codebook.onnx',
                        help='码本权重文件路径')
    parser.add_argument('--encoder_onnx_path', type=str, default='',
                        help='编码器ONNX模型路径')
    parser.add_argument('--decoder_onnx_path', type=str, default='',
                        help='解码器ONNX模型路径')
    parser.add_argument('--num_samples', type=int, default=50,
                        help='要检查的样本数量')
    parser.add_argument('--method', type=str, default='all',
                        choices=['all', 'atr_based', 'percent_change', 'log_return', 'zscore', 'minmax', 'candlestick_features'],
                        help='要测试的向量化方法')
    parser.add_argument('--validate_onnx', action='store_true',
                        help='是否验证ONNX编码器和解码器模型')

    args = parser.parse_args()

    # 0. 准备一些K线数据
    try:
        if os.path.exists(args.data_file):
            df_ohlcv, _  = load_single_data(args.data_file)
            df_ohlcv = df_ohlcv[0][-200:]  # 使用最后100根K线
            print(f"成功加载数据文件: {args.data_file}")
            print(df_ohlcv.head())
        else:
            raise FileNotFoundError(f"找不到数据文件: {args.data_file}")
    except Exception as e:
        print(f"无法加载示例数据: {e}")
        print("创建模拟K线数据用于测试...")
        # 创建一些伪K线数据
        data = {
            'datetime': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05',
                                        '2023-01-06', '2023-01-07', '2023-01-08', '2023-01-09', '2023-01-10',
                                        '2023-01-11', '2023-01-12', '2023-01-13', '2023-01-14', '2023-01-15',
                                        '2023-01-16', '2023-01-17', '2023-01-18', '2023-01-19', '2023-01-20']),
            'open':  np.random.rand(20) * 10 + 100,
            'high':  np.random.rand(20) * 5 + 105,
            'low':   np.random.rand(20) * -5 + 95,
            'close': np.random.rand(20) * 10 + 100,
            'volume':np.random.randint(1000, 5000, size=20)
        }
        df_ohlcv = pd.DataFrame(data)
        df_ohlcv['high'] = df_ohlcv[['open', 'close', 'high']].max(axis=1)
        df_ohlcv['low'] = df_ohlcv[['open', 'close', 'low']].min(axis=1)

        # 添加一些人工跳空
        # 在第10个K线添加向上跳空
        df_ohlcv.loc[10, 'open'] = df_ohlcv.loc[9, 'close'] * 1.05
        df_ohlcv.loc[10, 'high'] = max(df_ohlcv.loc[10, 'high'], df_ohlcv.loc[10, 'open'] * 1.02)
        df_ohlcv.loc[10, 'close'] = df_ohlcv.loc[10, 'open'] * 1.01

        # 在第15个K线添加向下跳空
        df_ohlcv.loc[15, 'open'] = df_ohlcv.loc[14, 'close'] * 0.95
        df_ohlcv.loc[15, 'low'] = min(df_ohlcv.loc[15, 'low'], df_ohlcv.loc[15, 'open'] * 0.98)
        df_ohlcv.loc[15, 'close'] = df_ohlcv.loc[15, 'open'] * 0.99

    # 1. 初始化Tokenizer
    CODEBOOK_PATH = args.codebook_path
    EMBEDDING_DIM = 5     # K线向量的维度
    NUM_EMBEDDINGS = 1024  # 码本大小 (词汇表大小，不含特殊token)
    ATR_PERIOD = 100      # ATR计算周期
    MA_VOL_PERIOD = 100   # 成交量移动平均周期
    GAP_THRESHOLD = 10.0  # 跳空检测阈值（ATR的倍数）

    print(f"\n使用码本: {CODEBOOK_PATH}")

    # 根据命令行参数选择要测试的向量化方法
    methods_to_test = []
    if args.method == 'all':
        methods_to_test = [
            VectorizationMethod.ATR_BASED,
            VectorizationMethod.PERCENT_CHANGE,
            VectorizationMethod.LOG_RETURN,
            VectorizationMethod.ZSCORE,
            VectorizationMethod.MINMAX,
            VectorizationMethod.CANDLESTICK_FEATURES
        ]
    else:
        method_map = {
            'atr_based': VectorizationMethod.ATR_BASED,
            'percent_change': VectorizationMethod.PERCENT_CHANGE,
            'log_return': VectorizationMethod.LOG_RETURN,
            'zscore': VectorizationMethod.ZSCORE,
            'minmax': VectorizationMethod.MINMAX,
            'candlestick_features': VectorizationMethod.CANDLESTICK_FEATURES
        }
        methods_to_test = [method_map[args.method]]

    # 创建tokenizer字典
    tokenizers = {}
    for method in methods_to_test:
        method_name = method.upper()
        tokenizers[method_name] = CandlestickVQTokenizer(
            codebook_weights_path=CODEBOOK_PATH,
            num_embeddings=NUM_EMBEDDINGS,
            embedding_dim=EMBEDDING_DIM,
            atr_period=ATR_PERIOD,
            ma_volume_period=MA_VOL_PERIOD,
            vectorization_method=method,
            gap_threshold=GAP_THRESHOLD,
            detect_gaps=True
        )

    # 2. 验证码本的有效性
    if args.validate_onnx and args.encoder_onnx_path and args.decoder_onnx_path:
        # 验证ONNX编码器和解码器模型
        print("\n验证ONNX编码器和解码器模型:")
        for method_name, tokenizer in tokenizers.items():
            print(f"\n使用 {method_name} 方法验证ONNX码本模型:")
            print(f"Tokenizer vocabulary size: {tokenizer.vocab_size}")
            print(f"跳空检测阈值: {tokenizer.gap_threshold} * ATR")
            print(f"编码器模型: {args.encoder_onnx_path}")
            print(f"解码器模型: {args.decoder_onnx_path}")

            # 执行ONNX模型验证
            results = validate_onnx_codebook_models(
                df_ohlcv,
                tokenizer,
                args.encoder_onnx_path,
                args.decoder_onnx_path,
                visualize=True,
                num_samples=args.num_samples
            )

            # 保存验证结果
            if results:
                output_file = f"onnx_codebook_validation_{method_name}.txt"
                with open(output_file, 'w') as f:
                    f.write(f"ONNX码本模型验证结果 - {method_name}\n")
                    f.write(f"使用数据文件: {args.data_file}\n")
                    f.write(f"使用码本: {CODEBOOK_PATH}\n")
                    f.write(f"编码器模型: {args.encoder_onnx_path}\n")
                    f.write(f"解码器模型: {args.decoder_onnx_path}\n")
                    f.write(f"检查的K线数量: {args.num_samples}\n\n")

                    f.write(f"编码匹配率: {results['encoding_match_rate']:.2f}% ({results['encoding_match_count']}/{results['total_tokens']})\n")
                    f.write(f"解码平均差异: {results['avg_decoding_diff']:.6f}\n\n")

                    f.write(f"ONNX重建数据中最高价是否真的最高: {results['high_is_highest_onnx']}\n")
                    f.write(f"ONNX重建数据中最低价是否真的最低: {results['low_is_lowest_onnx']}\n\n")

                    f.write(f"ONNX重建数据中最高价不是最高点的K线数量: {results['high_not_highest_count']} ({results['high_not_highest_pct']:.2f}%)\n")
                    f.write(f"ONNX重建数据中最低价不是最低点的K线数量: {results['low_not_lowest_count']} ({results['low_not_lowest_pct']:.2f}%)\n\n")

                    f.write("平均差异百分比:\n")
                    f.write(f"开盘价: {results['avg_open_diff']:.2f}%\n")
                    f.write(f"最高价: {results['avg_high_diff']:.2f}%\n")
                    f.write(f"最低价: {results['avg_low_diff']:.2f}%\n")
                    f.write(f"收盘价: {results['avg_close_diff']:.2f}%\n")
                    f.write(f"总体平均: {results['avg_overall_diff']:.2f}%\n\n")

                    if results['problem_indices']:
                        f.write(f"发现 {len(results['problem_indices'])} 个存在问题的K线索引:\n")
                        f.write(str(results['problem_indices'][:50]) + "\n")  # 只显示前50个

                print(f"ONNX验证结果已保存到 {output_file}")
    else:
        # 验证普通码本
        print("\n验证码本的有效性:")
        for method_name, tokenizer in tokenizers.items():
            print(f"\n使用 {method_name} 方法验证码本有效性:")
            print(f"Tokenizer vocabulary size: {tokenizer.vocab_size}")
            print(f"跳空检测阈值: {tokenizer.gap_threshold} * ATR")

            # 执行验证
            results = validate_codebook_accuracy(df_ohlcv, tokenizer, num_samples=args.num_samples)

            # 保存验证结果
            if results:
                output_file = f"codebook_validation_{method_name}.txt"
                with open(output_file, 'w') as f:
                    f.write(f"码本验证结果 - {method_name}\n")
                    f.write(f"使用数据文件: {args.data_file}\n")
                    f.write(f"使用码本: {CODEBOOK_PATH}\n")
                    f.write(f"检查的K线数量: {args.num_samples}\n\n")

                    f.write(f"原始数据中最高价是否真的最高: {results['high_is_highest_orig']}\n")
                    f.write(f"重建数据中最高价是否真的最高: {results['high_is_highest_recon']}\n")
                    f.write(f"原始数据中最低价是否真的最低: {results['low_is_lowest_orig']}\n")
                    f.write(f"重建数据中最低价是否真的最低: {results['low_is_lowest_recon']}\n\n")

                    f.write(f"重建数据中最高价不是最高点的K线数量: {results['high_not_highest_count']} ({results['high_not_highest_pct']:.2f}%)\n")
                    f.write(f"重建数据中最低价不是最低点的K线数量: {results['low_not_lowest_count']} ({results['low_not_lowest_pct']:.2f}%)\n\n")

                    f.write("平均差异百分比:\n")
                    f.write(f"开盘价: {results['avg_open_diff']:.2f}%\n")
                    f.write(f"最高价: {results['avg_high_diff']:.2f}%\n")
                    f.write(f"最低价: {results['avg_low_diff']:.2f}%\n")
                    f.write(f"收盘价: {results['avg_close_diff']:.2f}%\n")
                    f.write(f"总体平均: {results['avg_overall_diff']:.2f}%\n\n")

                    if results['problem_indices']:
                        f.write(f"发现 {len(results['problem_indices'])} 个存在问题的K线索引:\n")
                        f.write(str(results['problem_indices'][:50]) + "\n")  # 只显示前50个

                print(f"验证结果已保存到 {output_file}")

            # 可视化原始K线和重建的K线
            try:
                print("\n可视化原始K线和重建的K线...")
                fig = tokenizer.visualize_tokenization(df_ohlcv, num_candles=min(20, len(df_ohlcv)))

                # 保存图像
                output_file = f"candlestick_visualization_{method_name}.png"
                fig.savefig(output_file)
                print(f"可视化结果已保存到 {output_file}")

                # 关闭图形以释放内存
                plt.close(fig)
            except Exception as e:
                print(f"可视化过程中出错: {e}")
