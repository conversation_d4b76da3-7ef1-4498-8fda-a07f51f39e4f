{"model": {"path": "./model/best_model.ckpt", "type": "pytorch"}, "data": {"path": "e:/featdata/main", "files": ["main.2024"], "fut_codes": ["IF", "IH", "IC"], "seq_len": 30, "batch_size": 1}, "backtest": {"initial_capital": 10000.0, "commission": 0.001, "threshold": 0.5, "stop_loss": null, "take_profit": null, "leverage": 1.0, "print_interval": 100}, "output": {"dir": "./backtest_results", "save_chart": true}, "description": "TimeSeriesModel2drV2模型回测配置文件", "notes": ["model.path: 模型文件路径，支持.ckpt和.pth格式", "model.type: 模型类型，支持pytorch和onnx", "data.path: 数据根目录路径", "data.files: 数据文件列表", "data.fut_codes: 期货代码列表", "data.seq_len: 输入序列长度", "backtest.initial_capital: 初始资金", "backtest.commission: 交易手续费率", "backtest.threshold: 交易信号阈值", "backtest.stop_loss: 止损比例（null表示不使用）", "backtest.take_profit: 止盈比例（null表示不使用）", "backtest.leverage: 杠杆倍数", "output.dir: 结果输出目录", "output.save_chart: 是否保存图表"]}