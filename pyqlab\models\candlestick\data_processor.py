"""
Candlestick Data Processor for xLSTM Model

This module provides utilities for processing candlestick data for use with the xLSTM model.
It includes functions for data normalization, feature extraction, and batch preparation.
"""

import numpy as np
import pandas as pd
import torch
from typing import Tuple, List, Dict, Optional, Union
import talib


class CandlestickProcessor:
    """
    处理蜡烛图数据的工具类，用于xLSTM模型的数据准备
    """
    def __init__(self, 
                 seq_len: int = 30, 
                 pred_len: int = 5,
                 use_time_features: bool = True,
                 time_encoding: str = 'timeF',
                 normalize_method: str = 'zscore',
                 feature_columns: List[str] = None,
                 target_columns: List[str] = None,
                 atr_window: int = 14,
                 atr_mult: float = 0.88,
                 scale: int = 10):
        """
        初始化蜡烛图数据处理器
        
        Args:
            seq_len: 输入序列长度
            pred_len: 预测序列长度
            use_time_features: 是否使用时间特征
            time_encoding: 时间编码方式，'timeF'或'fixed'
            normalize_method: 归一化方法，'zscore'、'minmax'或'atr'
            feature_columns: 特征列名，默认为['open', 'high', 'low', 'close', 'volume']
            target_columns: 目标列名，默认为['open', 'high', 'low', 'close', 'volume']
            atr_window: ATR计算窗口
            atr_mult: ATR乘数
            scale: 缩放因子
        """
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.use_time_features = use_time_features
        self.time_encoding = time_encoding
        self.normalize_method = normalize_method
        self.atr_window = atr_window
        self.atr_mult = atr_mult
        self.scale = scale
        
        # 默认特征和目标列
        self.feature_columns = feature_columns or ['open', 'high', 'low', 'close', 'volume']
        self.target_columns = target_columns or ['open', 'high', 'low', 'close', 'volume']
        
        # 归一化统计信息
        self.stats = {}
    
    def _calc_atr(self, df: pd.DataFrame) -> np.ndarray:
        """
        计算ATR (Average True Range)
        
        Args:
            df: 包含high, low, close列的DataFrame
            
        Returns:
            ATR值数组
        """
        high = df['high'].values
        low = df['low'].values
        close = np.concatenate([[high[0]], df['close'].values[:-1]])  # 使用第一个high作为初始close
        
        atr = talib.ATR(high, low, close, timeperiod=self.atr_window)
        # 填充NaN值
        atr[:self.atr_window] = atr[self.atr_window]
        
        return atr
    
    def _normalize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        归一化数据
        
        Args:
            df: 输入DataFrame
            
        Returns:
            归一化后的DataFrame
        """
        df_normalized = df.copy()
        
        if self.normalize_method == 'zscore':
            # Z-score归一化
            for col in self.feature_columns:
                if col in df.columns:
                    mean = df[col].mean()
                    std = df[col].std()
                    if std != 0:
                        df_normalized[col] = (df[col] - mean) / std
                    self.stats[f'{col}_mean'] = mean
                    self.stats[f'{col}_std'] = std
        
        elif self.normalize_method == 'minmax':
            # Min-Max归一化
            for col in self.feature_columns:
                if col in df.columns:
                    min_val = df[col].min()
                    max_val = df[col].max()
                    if max_val > min_val:
                        df_normalized[col] = (df[col] - min_val) / (max_val - min_val)
                    self.stats[f'{col}_min'] = min_val
                    self.stats[f'{col}_max'] = max_val
        
        elif self.normalize_method == 'atr':
            # 基于ATR的归一化
            atr = self._calc_atr(df) / self.scale
            
            # 使用ATR归一化价格数据
            for col in ['open', 'high', 'low', 'close']:
                if col in self.feature_columns and col in df.columns:
                    # 使用前一个收盘价作为基准
                    prev_close = np.concatenate([[df['close'].iloc[0]], df['close'].values[:-1]])
                    df_normalized[col] = (df[col].values - prev_close) / atr
            
            # 对成交量使用对数变换
            if 'volume' in self.feature_columns and 'volume' in df.columns:
                if df['volume'].min() > 0:
                    df_normalized['volume'] = np.log1p(df['volume'])
                    self.stats['volume_log'] = True
        
        return df_normalized
    
    def _extract_time_features(self, dates: pd.Series) -> np.ndarray:
        """
        从日期中提取时间特征
        
        Args:
            dates: 日期序列
            
        Returns:
            时间特征数组
        """
        if self.time_encoding == 'timeF':
            # 使用周期性时间编码
            time_features = []
            
            # 小时特征 - 24小时周期
            hour = dates.dt.hour
            time_features.append(np.sin(2 * np.pi * hour / 24.0))
            time_features.append(np.cos(2 * np.pi * hour / 24.0))
            
            # 星期特征 - 7天周期
            day_of_week = dates.dt.dayofweek
            time_features.append(np.sin(2 * np.pi * day_of_week / 7.0))
            time_features.append(np.cos(2 * np.pi * day_of_week / 7.0))
            
            # 月份特征 - 12个月周期
            month = dates.dt.month
            time_features.append(np.sin(2 * np.pi * month / 12.0))
            time_features.append(np.cos(2 * np.pi * month / 12.0))
            
            # 一年中的天 - 365天周期
            day_of_year = dates.dt.dayofyear
            time_features.append(np.sin(2 * np.pi * day_of_year / 365.25))
            time_features.append(np.cos(2 * np.pi * day_of_year / 365.25))
            
            return np.column_stack(time_features)
        
        elif self.time_encoding == 'fixed':
            # 使用离散时间编码
            time_features = []
            
            # 月份 (1-12)
            time_features.append(dates.dt.month)
            
            # 日 (1-31)
            time_features.append(dates.dt.day)
            
            # 星期几 (0-6)
            time_features.append(dates.dt.dayofweek)
            
            # 小时 (0-23)
            time_features.append(dates.dt.hour)
            
            # 分钟 (0-59)，转换为5分钟间隔 (0-11)
            minutes = dates.dt.minute
            time_features.append(minutes // 5)
            
            return np.column_stack(time_features)
        
        else:
            raise ValueError(f"Unsupported time encoding: {self.time_encoding}")
    
    def _extract_candlestick_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取蜡烛图特征
        
        Args:
            df: 输入DataFrame，包含OHLCV数据
            
        Returns:
            添加了蜡烛图特征的DataFrame
        """
        df_features = df.copy()
        
        # 计算实体和影线
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # 计算ATR
            atr = self._calc_atr(df)
            df_features['atr'] = atr
            
            # 计算实体大小
            df_features['body'] = (df['close'] - df['open']) / atr
            
            # 计算上影线和下影线
            df_features['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / atr
            df_features['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / atr
            
            # 计算相对前一个收盘价的变化
            df_features['close_change'] = df['close'].pct_change().fillna(0)
            
            # 计算振幅
            df_features['amplitude'] = (df['high'] - df['low']) / atr
        
        return df_features
    
    def prepare_data(self, 
                    df: pd.DataFrame, 
                    code: Optional[str] = None) -> Tuple[np.ndarray, Optional[np.ndarray], Optional[np.ndarray]]:
        """
        准备模型输入数据
        
        Args:
            df: 输入DataFrame，包含OHLCV和datetime列
            code: 证券代码
            
        Returns:
            (features, time_features, targets)
            - features: 特征数组 [samples, seq_len, n_features]
            - time_features: 时间特征数组 [samples, seq_len, n_time_features] 或 None
            - targets: 目标数组 [samples, pred_len, n_targets] 或 None
        """
        # 确保数据按时间排序
        if 'datetime' in df.columns:
            df = df.sort_values('datetime')
        
        # 提取蜡烛图特征
        df_features = self._extract_candlestick_features(df)
        
        # 归一化数据
        df_normalized = self._normalize_data(df_features)
        
        # 提取时间特征
        time_feats = None
        if self.use_time_features and 'datetime' in df.columns:
            time_feats = self._extract_time_features(pd.to_datetime(df['datetime']))
        
        # 创建滑动窗口样本
        features = []
        targets = []
        time_features = []
        
        total_len = self.seq_len + self.pred_len
        
        if len(df_normalized) >= total_len:
            for i in range(len(df_normalized) - total_len + 1):
                # 输入特征窗口
                feat_window = df_normalized.iloc[i:i+self.seq_len][self.feature_columns].values
                features.append(feat_window)
                
                # 目标窗口
                target_window = df_normalized.iloc[i+self.seq_len:i+total_len][self.target_columns].values
                targets.append(target_window)
                
                # 时间特征窗口
                if time_feats is not None:
                    time_feat_window = time_feats[i:i+self.seq_len]
                    time_features.append(time_feat_window)
        
        # 转换为numpy数组
        features = np.array(features)
        targets = np.array(targets) if targets else None
        time_features = np.array(time_features) if time_features else None
        
        return features, time_features, targets
    
    def create_torch_dataset(self, 
                           features: np.ndarray, 
                           time_features: Optional[np.ndarray] = None, 
                           targets: Optional[np.ndarray] = None,
                           codes: Optional[np.ndarray] = None) -> torch.utils.data.Dataset:
        """
        创建PyTorch数据集
        
        Args:
            features: 特征数组 [samples, seq_len, n_features]
            time_features: 时间特征数组 [samples, seq_len, n_time_features] 或 None
            targets: 目标数组 [samples, pred_len, n_targets] 或 None
            codes: 证券代码数组 [samples] 或 None
            
        Returns:
            PyTorch数据集
        """
        class CandlestickDataset(torch.utils.data.Dataset):
            def __init__(self, features, time_features, targets, codes):
                self.features = torch.tensor(features, dtype=torch.float32)
                self.time_features = torch.tensor(time_features, dtype=torch.float32) if time_features is not None else None
                self.targets = torch.tensor(targets, dtype=torch.float32) if targets is not None else None
                self.codes = torch.tensor(codes, dtype=torch.long) if codes is not None else None
            
            def __len__(self):
                return len(self.features)
            
            def __getitem__(self, idx):
                item = {'features': self.features[idx]}
                
                if self.time_features is not None:
                    item['time_features'] = self.time_features[idx]
                
                if self.targets is not None:
                    item['targets'] = self.targets[idx]
                
                if self.codes is not None:
                    item['codes'] = self.codes[idx]
                
                return item
        
        return CandlestickDataset(features, time_features, targets, codes)
    
    def inverse_transform(self, 
                         predictions: Union[np.ndarray, torch.Tensor], 
                         base_price: Optional[float] = None) -> np.ndarray:
        """
        将归一化的预测值转换回原始值
        
        Args:
            predictions: 模型预测值
            base_price: 基准价格，用于ATR归一化方法
            
        Returns:
            转换后的预测值
        """
        # 转换为numpy数组
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        
        if self.normalize_method == 'zscore':
            # 反向Z-score归一化
            result = predictions.copy()
            for i, col in enumerate(self.target_columns):
                if f'{col}_mean' in self.stats and f'{col}_std' in self.stats:
                    mean = self.stats[f'{col}_mean']
                    std = self.stats[f'{col}_std']
                    result[..., i] = predictions[..., i] * std + mean
            
            return result
        
        elif self.normalize_method == 'minmax':
            # 反向Min-Max归一化
            result = predictions.copy()
            for i, col in enumerate(self.target_columns):
                if f'{col}_min' in self.stats and f'{col}_max' in self.stats:
                    min_val = self.stats[f'{col}_min']
                    max_val = self.stats[f'{col}_max']
                    result[..., i] = predictions[..., i] * (max_val - min_val) + min_val
            
            return result
        
        elif self.normalize_method == 'atr':
            # 反向ATR归一化
            if base_price is None:
                raise ValueError("base_price is required for ATR normalization")
            
            result = predictions.copy()
            
            # 假设ATR值已经存储在stats中
            if 'atr' in self.stats:
                atr = self.stats['atr'] / self.scale
            else:
                # 使用默认ATR值
                atr = 1.0
            
            # 反向变换价格数据
            for i, col in enumerate(self.target_columns):
                if col in ['open', 'high', 'low', 'close']:
                    result[..., i] = predictions[..., i] * atr + base_price
            
            # 反向变换成交量
            if 'volume' in self.target_columns:
                vol_idx = self.target_columns.index('volume')
                if self.stats.get('volume_log', False):
                    result[..., vol_idx] = np.expm1(predictions[..., vol_idx])
            
            return result
        
        else:
            # 不支持的归一化方法，直接返回
            return predictions
