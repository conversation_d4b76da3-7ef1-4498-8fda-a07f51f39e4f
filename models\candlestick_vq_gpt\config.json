{"data_dir": "f:/hqdata/tsdb", "market": "fut", "block_name": "top", "period": "min1", "multi_data": false, "begin_date": "2025-03-01", "end_date": "2025-12-31", "val_ratio": 0.1, "stride": 1, "shuffle_split": false, "codebook_path": "e:\\lab\\RoboQuant\\pylab\\models\\vqvae\\vqvae_20250525\\vqcb_atr_based_fut_top_min1_512_0.0209.pt", "num_embeddings": 512, "embedding_dim": 4, "atr_period": 14, "ma_volume_period": 14, "vectorization_method": "atr_based", "seq_len": 30, "code_size": 100, "n_layer": 6, "n_head": 8, "d_model": 128, "dropout": 0.15, "use_time_features": true, "label_smoothing": 0.02, "use_auxiliary_loss": true, "batch_size": 16, "epochs": 10, "learning_rate": 0.0002, "weight_decay": 0.01, "warmup_ratio": 0.2, "grad_clip": 1.0, "grad_accum_steps": 8, "early_stopping": 3, "mixed_precision": false, "num_workers": 4, "save_dir": "e:/lab/RoboQuant/pylab/models/candlestick_vq_gpt", "log_interval": 50, "eval_interval": 200, "save_interval": 500, "seed": 42}