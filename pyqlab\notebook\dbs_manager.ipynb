{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## SQLite 数据库管理"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功合并 C:/Users/<USER>/Desktop/td_store.nhqh.db 和 C:/Users/<USER>/Desktop/td_store.zxjt.db 到 C:/Users/<USER>/Desktop/td_store.db。\n"]}], "source": ["import sqlite3\n", "import os\n", "import time\n", "\n", "def create_connection(db_path):\n", "    conn = sqlite3.connect(\n", "        db_path,\n", "        detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES,\n", "        uri=True\n", "    )\n", "    # 设置 text_factory 以使用 gbk 解码\n", "    conn.text_factory = lambda b: b.decode('gbk') if isinstance(b, bytes) else b\n", "    return conn\n", "\n", "def merge_sqlite_dbs(db1_path, db2_path, new_db_path, timeout=30, retry_attempts=3, retry_delay=5):\n", "    \"\"\"\n", "    合并两个 SQLite 数据库到一个新的数据库中。\n", "\n", "    参数:\n", "    db1_path (str): 第一个源数据库的路径。\n", "    db2_path (str): 第二个源数据库的路径。\n", "    new_db_path (str): 合并后的新数据库路径。\n", "    timeout (int): 连接超时时间，单位为秒，默认为 30 秒。\n", "    retry_attempts (int): 重试次数，默认为 3 次。\n", "    retry_delay (int): 重试延迟时间，单位为秒，默认为 5 秒。\n", "\n", "    \"\"\"\n", "    # 连接第一个数据库\n", "    try:\n", "        conn1 = create_connection(db1_path)\n", "    except sqlite3.Error as e:\n", "        print(f\"连接 {db1_path} 失败: {e}\")\n", "        return\n", "\n", "    # 连接第二个数据库\n", "    try:\n", "        conn2 = create_connection(db2_path)\n", "    except sqlite3.Error as e:\n", "        print(f\"连接 {db2_path} 失败: {e}\")\n", "        conn1.close()\n", "        return\n", "\n", "    # 创建一个新的数据库连接\n", "    try:\n", "        new_conn = create_connection(new_db_path)\n", "    except sqlite3.Error as e:\n", "        print(f\"创建 {new_db_path} 失败: {e}\")\n", "        conn1.close()\n", "        conn2.close()\n", "        return\n", "\n", "    # 从第一个数据库中复制数据到新数据库\n", "    try:\n", "        with conn1:\n", "            cursor1 = conn1.cursor()\n", "            cursor1.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "            tables = cursor1.fetchall()\n", "            for table in tables:\n", "                table_name = table[0]\n", "                cursor1.execute(f\"SELECT * FROM {table_name}\")\n", "                rows = cursor1.fetchall()\n", "                with new_conn:\n", "                    cursor_new = new_conn.cursor()\n", "                    cursor_new.execute(f\"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM {table_name}\")\n", "                    for row in rows:\n", "                        # 将 bytes 类型的数据转换为 gbk 编码的字符串\n", "                        row = tuple(map(lambda x: x.decode('gbk') if isinstance(x, bytes) else x, row))\n", "                        cursor_new.execute(f\"INSERT OR IGNORE INTO {table_name} VALUES {row}\")\n", "    except sqlite3.Error as e:\n", "        print(f\"从 {db1_path} 复制数据到 {new_db_path} 失败: {e}\")\n", "        conn1.close()\n", "        conn2.close()\n", "        new_conn.close()\n", "        return\n", "\n", "    # 从第二个数据库中复制数据到新数据库\n", "    try:\n", "        with conn2:\n", "            cursor2 = conn2.cursor()\n", "            cursor2.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "            tables = cursor2.fetchall()\n", "            for table in tables:\n", "                table_name = table[0]\n", "                cursor2.execute(f\"SELECT * FROM {table_name}\")\n", "                rows = cursor2.fetchall()\n", "                with new_conn:\n", "                    cursor_new = new_conn.cursor()\n", "                    cursor_new.execute(f\"CREATE TABLE IF NOT EXISTS {table_name} AS SELECT * FROM {table_name}\")\n", "                    for row in rows:\n", "                        # 将 bytes 类型的数据转换为 gbk 编码的字符串\n", "                        row = tuple(map(lambda x: x.decode('gbk') if isinstance(x, bytes) else x, row))\n", "                        cursor_new.execute(f\"INSERT OR IGNORE INTO {table_name} VALUES {row}\")\n", "    except sqlite3.Error as e:\n", "        print(f\"从 {db2_path} 复制数据到 {new_db_path} 失败: {e}\")\n", "        conn1.close()\n", "        conn2.close()\n", "        new_conn.close()\n", "        return\n", "\n", "    # 关闭所有连接\n", "    conn1.close()\n", "    conn2.close()\n", "    new_conn.close()\n", "    print(f\"成功合并 {db1_path} 和 {db2_path} 到 {new_db_path}。\")\n", "\n", "# 示例使用\n", "db1 = \"C:/Users/<USER>/Desktop/td_store.nhqh.db\"\n", "db2 = \"C:/Users/<USER>/Desktop/td_store.zxjt.db\"\n", "new_db = \"C:/Users/<USER>/Desktop/td_store.db\"\n", "\n", "merge_sqlite_dbs(db1, db2, new_db)\n", "\n", "# 示例使用\n", "# db_names = ['td', 'ot']\n", "# db_tables = {\n", "#     'td': [\n", "#         'T_Account<PERSON>mount', 'T_BrokerAccount', 'T_Portfolio',\n", "#         'T_StockStrategy', 'T_FuturesStrategy', 'T_FactorData',\n", "#         'T_AccountStock'\n", "#     ],\n", "#     'ot': ['T_Open_Order', 'T_Filled_Order', 'T_Trade']\n", "# }\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["T_AccountAmount - DB1: (1, 2), DB2: (1, 2)\n", "After concat: (2, 2)\n", "After drop duplicates: (1, 2)\n", "Data for T_AccountAmount merged successfully.\n", "T_BrokerAccount - DB1: (16, 2), DB2: (23, 2)\n", "After concat: (39, 2)\n", "After drop duplicates: (39, 2)\n", "Data for T_BrokerAccount merged successfully.\n", "T_Portfolio - DB1: (4, 2), DB2: (9, 2)\n", "After concat: (13, 2)\n", "After drop duplicates: (13, 2)\n", "Data for T_Portfolio merged successfully.\n", "T_StockStrategy - DB1: (0, 2), DB2: (0, 2)\n", "After concat: (0, 2)\n", "After drop duplicates: (0, 2)\n", "Data for T_StockStrategy merged successfully.\n", "T_FuturesStrategy - DB1: (2, 2), DB2: (4, 2)\n", "After concat: (6, 2)\n", "After drop duplicates: (6, 2)\n", "Data for T_FuturesStrategy merged successfully.\n", "T_FactorData - DB1: (68, 2), DB2: (68, 2)\n", "After concat: (136, 2)\n", "After drop duplicates: (76, 2)\n", "Data for T_FactorData merged successfully.\n", "T_AccountStock - DB1: (3, 3), DB2: (3, 3)\n", "After concat: (6, 3)\n", "After drop duplicates: (3, 3)\n", "Data for T_AccountStock merged successfully.\n", "T_Open_Order - DB1: (0, 2), DB2: (0, 2)\n", "After concat: (0, 2)\n", "After drop duplicates: (0, 2)\n", "Data for T_Open_Order merged successfully.\n", "T_Filled_Order - DB1: (8440, 2), DB2: (8534, 2)\n", "After concat: (16974, 2)\n", "After drop duplicates: (16956, 2)\n", "Data for T_Filled_Order merged successfully.\n", "T_Trade - DB1: (10741, 2), DB2: (11623, 2)\n", "After concat: (22364, 2)\n", "After drop duplicates: (22339, 2)\n", "Data for T_Trade merged successfully.\n"]}], "source": ["import sqlite3\n", "import pandas as pd\n", "def create_connection(db_path):\n", "    conn = sqlite3.connect(\n", "        db_path,\n", "        detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES,\n", "        uri=True\n", "    )\n", "    # 设置 text_factory 以使用 gbk 解码\n", "    conn.text_factory = lambda b: b.decode('gbk') if isinstance(b, bytes) else b\n", "    return conn\n", "\n", "def merge_dbs(tables, db1, db2, new_db):\n", "    conn1 = create_connection(db1)\n", "    conn2 = create_connection(db2)\n", "    conn_new = create_connection(new_db)\n", "\n", "    for table in tables:\n", "        try:\n", "            df1 = pd.read_sql_query(f\"SELECT * FROM {table}\", conn1)\n", "            df2 = pd.read_sql_query(f\"SELECT * FROM {table}\", conn2)\n", "            print(f\"{table} - DB1: {df1.shape}, DB2: {df2.shape}\")\n", "            \n", "            df_new = pd.concat([df1, df2])\n", "            print(f\"After concat: {df_new.shape}\")\n", "            \n", "            df_new.drop_duplicates(inplace=True, keep='first')\n", "            print(f\"After drop duplicates: {df_new.shape}\")\n", "            \n", "            df_new.to_sql(\n", "                table,\n", "                conn_new,\n", "                if_exists='append',\n", "                index=False,\n", "                # dtype={'account_name': 'TEXT'}\n", "            )\n", "            print(f\"Data for {table} merged successfully.\")\n", "        except Exception as e:\n", "            print(f\"Error processing table {table}: {e}\")\n", "\n", "    conn_new.close()\n", "    conn1.close()\n", "    conn2.close()\n", "\n", "\n", "db_names = ['td', 'ot']\n", "db_tables = {\n", "    'td': [\n", "        'T_Account<PERSON>mount', 'T_BrokerAccount', 'T_Portfolio',\n", "        'T_StockStrategy', 'T_FuturesStrategy', 'T_FactorData',\n", "        'T_AccountStock'\n", "    ],\n", "    'ot': ['T_Open_Order', 'T_Filled_Order', 'T_Trade']\n", "}\n", "\n", "for db_name in db_names:\n", "    db1 = f\"C:/Users/<USER>/Desktop/{db_name}_store.nhqh.db\"\n", "    db2 = f\"C:/Users/<USER>/Desktop/{db_name}_store.zxjt.db\"  # 假设有一个备份数据库\n", "    new_db = f\"C:/Users/<USER>/Desktop/{db_name}_store.db\"\n", "    tables = db_tables.get(db_name, [])\n", "    merge_dbs(tables, db1, db2, new_db)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Redis 数据库管理"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deleted 29 keys\n"]}], "source": ["import redis\n", "\n", "# 设置 Redis 服务器参数\n", "host = '**************'\n", "port = 51301\n", "password = 'wdljshbsjzsszsbbzyjcsz~1974'\n", "db = 1\n", "\n", "# 连接到 Redis\n", "r = redis.Redis(host=host, port=port, password=password, db=db, decode_responses=True)\n", "\n", "# 获取所有匹配的键\n", "keys = r.keys('positions:*') # portfolios performance\n", "\n", "# 删除所有匹配的键\n", "if keys:\n", "    r.delete(*keys)\n", "\n", "print(f\"Deleted {len(keys)} keys\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}