activation: relu
batch_size: 64
best_score: 0.6
conv_channels: !!python/tuple
- 32
- 64
- 128
data_path: f:/featdata/trd
direct: ls
dropout: 0.5
ds_files:
- trd.2024
ds_name: 15HF
early_stop: 5
embed_time: fixed
end_time: ''
filter_win: 0
fut_codes:
- AG
- RB
- HC
- BU
- RU
- SP
- SC
- SS
- AO
- BR
- SR
- CF
- FG
- TA
- MA
- OI
- RM
- CY
- SF
- SM
- AP
- UR
- SA
- PK
- PX
- SH
- PR
- M
- Y
- A
- P
- JM
- I
- C
- CS
- L
- V
- PP
- EG
- EB
inference_mode: true
ins_nums: !!python/tuple
- 0
- 51
- 51
- 17
is_normal: true
k_folds: 5
kernel_size: 3
log_dir: lightning_logs
loss: mse
lr: 0.001
lr_decay_min_lr: 1.0e-06
lr_decay_rate: 0.1
lr_decay_steps: 5
lr_scheduler: reduce_on_plateau
max_epochs: 5
min_delta: 0.01
model_dir: model
model_name: time_series_model2dr_v1
model_path: pyqlab.models.fintimeseries
model_type: 0
num_channel: 15
num_classes: 3
num_conv_layers: 3
num_embeds:
- 72
- 5
- 11
num_input: 51
num_outputs: 3
num_workers: 0
optimizer: adam
out_channels: !!python/tuple
- 32
- 64
- 1600
- 1600
pooling: max
pred_len: 1
probabilistic: true
resume: false
save_as_to_onnx: false
seed: 8810
seq_len: 15
start_time: ''
sub_dir: ''
use_attention: true
use_residual: true
verbose: false
version: CV2DR
weight_decay: 0.001
