"""
xLSTM Candlestick Time Series Prediction Model

This module implements an extended LSTM (xLSTM) model for predicting candlestick time series data.
xLSTM combines the strengths of stabilized LSTM (sLSTM) and multi-head LSTM (mLSTM) to achieve
better long-term dependency modeling and more stable training.

References:
- xLSTMTime: Long-term Time Series Forecasting With xLSTM (https://arxiv.org/abs/2407.10240)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math


class CausalConv1D(nn.Module):
    """
    因果卷积层，确保模型只使用过去的信息进行预测
    """
    def __init__(self, in_channels, out_channels, kernel_size, dilation=1, **kwargs):
        super(CausalConv1D, self).__init__()
        self.padding = (kernel_size - 1) * dilation
        self.conv = nn.Conv1d(in_channels, out_channels, kernel_size, 
                             padding=self.padding, dilation=dilation, **kwargs)
        
    def forward(self, x):
        x = self.conv(x)
        return x[:, :, :-self.padding]


class BlockDiagonal(nn.Module):
    """
    块对角线层，用于实现多头机制
    """
    def __init__(self, in_features, out_features, num_blocks):
        super(BlockDiagonal, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.num_blocks = num_blocks
        
        assert out_features % num_blocks == 0
        block_out_features = out_features // num_blocks
        
        self.blocks = nn.ModuleList([
            nn.Linear(in_features, block_out_features)
            for _ in range(num_blocks)
        ])
        
    def forward(self, x):
        x = [block(x) for block in self.blocks]
        x = torch.cat(x, dim=-1)
        return x


class sLSTMBlock(nn.Module):
    """
    稳定化LSTM块，使用指数门控机制提高训练稳定性
    """
    def __init__(self, input_size, head_size, num_heads, proj_factor=4/3):
        super(sLSTMBlock, self).__init__()
        self.input_size = input_size
        self.head_size = head_size
        self.hidden_size = head_size * num_heads
        self.num_heads = num_heads
        self.proj_factor = proj_factor
        
        assert proj_factor > 0
        
        self.layer_norm = nn.LayerNorm(input_size)
        self.causal_conv = CausalConv1D(1, 1, 4)
        
        # 门控权重矩阵
        self.Wz = BlockDiagonal(input_size, self.hidden_size, num_heads)
        self.Wi = BlockDiagonal(input_size, self.hidden_size, num_heads)
        self.Wf = BlockDiagonal(input_size, self.hidden_size, num_heads)
        self.Wo = BlockDiagonal(input_size, self.hidden_size, num_heads)
        
        # 循环权重矩阵
        self.Rz = BlockDiagonal(self.hidden_size, self.hidden_size, num_heads)
        self.Ri = BlockDiagonal(self.hidden_size, self.hidden_size, num_heads)
        self.Rf = BlockDiagonal(self.hidden_size, self.hidden_size, num_heads)
        self.Ro = BlockDiagonal(self.hidden_size, self.hidden_size, num_heads)
        
        self.group_norm = nn.GroupNorm(num_heads, self.hidden_size)
        
        # 投影层
        self.up_proj_left = nn.Linear(self.hidden_size, int(self.hidden_size * proj_factor))
        self.up_proj_right = nn.Linear(self.hidden_size, int(self.hidden_size * proj_factor))
        self.down_proj = nn.Linear(int(self.hidden_size * proj_factor), input_size)
        
    def forward(self, x, prev_state):
        assert x.size(-1) == self.input_size
        
        h_prev, c_prev, n_prev, m_prev = prev_state
        h_prev = h_prev.to(x.device)
        c_prev = c_prev.to(x.device)
        n_prev = n_prev.to(x.device)
        m_prev = m_prev.to(x.device)
        
        x_norm = self.layer_norm(x)
        x_conv = F.silu(self.causal_conv(x_norm.unsqueeze(1)).squeeze(1))
        
        # 计算门控值
        z = torch.tanh(self.Wz(x_norm) + self.Rz(h_prev))
        o = torch.sigmoid(self.Wo(x_norm) + self.Ro(h_prev))
        
        i_tilde = self.Wi(x_conv) + self.Ri(h_prev)
        f_tilde = self.Wf(x_conv) + self.Rf(h_prev)
        
        # 稳定化机制
        m_t = torch.max(f_tilde + m_prev, i_tilde)
        i = torch.exp(i_tilde - m_t)
        f = torch.exp(f_tilde + m_prev - m_t)
        
        c_t = f * c_prev + i * z
        n_t = f * n_prev + i
        h_t = o * c_t / n_t
        
        output = h_t
        
        # 残差连接和投影
        output_norm = self.group_norm(output)
        output_left = self.up_proj_left(output_norm)
        output_right = self.up_proj_right(output_norm)
        output_gated = F.gelu(output_right)
        output = output_left * output_gated
        output = self.down_proj(output)
        
        final_output = output + x
        
        return final_output, (h_t, c_t, n_t, m_t)


class mLSTMBlock(nn.Module):
    """
    多头LSTM块，结合了注意力机制和LSTM
    """
    def __init__(self, input_size, head_size, num_heads, proj_factor=2):
        super(mLSTMBlock, self).__init__()
        self.input_size = input_size
        self.head_size = head_size
        self.hidden_size = head_size * num_heads
        self.num_heads = num_heads
        self.proj_factor = proj_factor
        
        assert proj_factor > 0
        
        self.layer_norm = nn.LayerNorm(input_size)
        
        # 投影层
        self.up_proj_left = nn.Linear(input_size, int(input_size * proj_factor))
        self.up_proj_right = nn.Linear(input_size, self.hidden_size)
        self.down_proj = nn.Linear(self.hidden_size, input_size)
        
        self.causal_conv = CausalConv1D(1, 1, 4)
        self.skip_connection = nn.Linear(int(input_size * proj_factor), self.hidden_size)
        
        # 注意力权重
        self.Wq = BlockDiagonal(int(input_size * proj_factor), self.hidden_size, num_heads)
        self.Wk = BlockDiagonal(int(input_size * proj_factor), self.hidden_size, num_heads)
        self.Wv = BlockDiagonal(int(input_size * proj_factor), self.hidden_size, num_heads)
        
        # 门控权重
        self.Wi = nn.Linear(int(input_size * proj_factor), self.hidden_size)
        self.Wf = nn.Linear(int(input_size * proj_factor), self.hidden_size)
        self.Wo = nn.Linear(int(input_size * proj_factor), self.hidden_size)
        
        self.group_norm = nn.GroupNorm(num_heads, self.hidden_size)
        
    def forward(self, x, prev_state):
        h_prev, c_prev, n_prev, m_prev = prev_state
        h_prev = h_prev.to(x.device)
        c_prev = c_prev.to(x.device)
        n_prev = n_prev.to(x.device)
        m_prev = m_prev.to(x.device)
        
        assert x.size(-1) == self.input_size
        
        x_norm = self.layer_norm(x)
        x_up_left = self.up_proj_left(x_norm)
        x_up_right = self.up_proj_right(x_norm)
        
        x_conv = F.silu(self.causal_conv(x_up_left.unsqueeze(1)).squeeze(1))
        x_skip = self.skip_connection(x_conv)
        
        # 计算注意力
        q = self.Wq(x_conv)
        k = self.Wk(x_conv) / (self.head_size ** 0.5)
        v = self.Wv(x_up_left)
        
        # 计算门控值
        i_tilde = self.Wi(x_conv)
        f_tilde = self.Wf(x_conv)
        o = torch.sigmoid(self.Wo(x_up_left))
        
        # 稳定化机制
        m_t = torch.max(f_tilde + m_prev, i_tilde)
        i = torch.exp(i_tilde - m_t)
        f = torch.exp(f_tilde + m_prev - m_t)
        
        # 注意力与LSTM结合
        c_t = f * c_prev + i * (v * k)  # 元素乘法代替矩阵乘法
        n_t = f * n_prev + i * k
        
        # 计算输出
        h_t = o * (c_t * q) / torch.clamp(torch.abs(n_t * q), min=1.0)
        
        output = h_t
        
        # 残差连接和投影
        output_norm = self.group_norm(output)
        output = output_norm + x_skip
        output = output * F.silu(x_up_right)
        output = self.down_proj(output)
        
        final_output = output + x
        
        return final_output, (h_t, c_t, n_t, m_t)


class xLSTM(nn.Module):
    """
    扩展LSTM模型，结合了sLSTM和mLSTM的优点
    """
    def __init__(self, input_size, head_size, num_heads, layers, batch_first=True, 
                 proj_factor_slstm=4/3, proj_factor_mlstm=2):
        super(xLSTM, self).__init__()
        self.input_size = input_size
        self.head_size = head_size
        self.hidden_size = head_size * num_heads
        self.num_heads = num_heads
        self.layers_config = layers
        self.num_layers = len(layers)
        self.batch_first = batch_first
        self.proj_factor_slstm = proj_factor_slstm
        self.proj_factor_mlstm = proj_factor_mlstm
        
        self.layers = nn.ModuleList()
        for layer_type in layers:
            if layer_type == 's':
                layer = sLSTMBlock(input_size, head_size, num_heads, proj_factor_slstm)
            elif layer_type == 'm':
                layer = mLSTMBlock(input_size, head_size, num_heads, proj_factor_mlstm)
            else:
                raise ValueError(f"Invalid layer type: {layer_type}. Choose 's' for sLSTM or 'm' for mLSTM.")
            self.layers.append(layer)
        
    def forward(self, x, state=None):
        assert x.ndim == 3
        
        if not self.batch_first: 
            x = x.transpose(0, 1)
            
        batch_size, seq_len, _ = x.size()
        
        if state is not None:
            state = torch.stack(list(state)).to(x.device)
            assert state.ndim == 4
            num_hidden, state_num_layers, state_batch_size, state_hidden_size = state.size()
            assert num_hidden == 4
            assert state_num_layers == self.num_layers
            assert state_batch_size == batch_size
            assert state_hidden_size == self.hidden_size
            state = state.transpose(0, 1)
        else:
            state = torch.zeros(self.num_layers, 4, batch_size, self.hidden_size, device=x.device)
        
        outputs = []
        for t in range(seq_len):
            x_t = x[:, t, :]
            for layer in range(self.num_layers):
                x_t, state_tuple = self.layers[layer](x_t, tuple(state[layer].clone()))
                state[layer] = torch.stack(list(state_tuple))
            outputs.append(x_t)
        
        outputs = torch.stack(outputs, dim=1)  # [batch_size, seq_len, input_size]
        
        if not self.batch_first:
            outputs = outputs.transpose(0, 1)
            
        state = tuple(state.transpose(0, 1))
        
        return outputs, state


class CandlestickXLSTMModel(nn.Module):
    """
    基于xLSTM的蜡烛图时序预测模型
    """
    def __init__(self, 
                 input_size=5,           # OHLCV特征数量
                 hidden_size=64,         # 隐藏层大小
                 head_size=16,           # 每个头的大小
                 num_heads=4,            # 头的数量
                 num_layers=3,           # 层数
                 layer_config='sms',     # 层配置，s表示sLSTM，m表示mLSTM
                 dropout=0.1,            # Dropout比率
                 output_size=5,          # 输出维度(预测未来的OHLCV)
                 seq_len=30,             # 输入序列长度
                 pred_len=5,             # 预测序列长度
                 use_time_features=True, # 是否使用时间特征
                 time_features_size=4,   # 时间特征维度
                 use_code_embedding=True,# 是否使用证券代码嵌入
                 code_embedding_size=16, # 证券代码嵌入维度
                 num_codes=100,          # 证券代码数量
                 probabilistic=False):   # 是否进行概率预测
        super(CandlestickXLSTMModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.head_size = head_size
        self.num_heads = num_heads
        self.output_size = output_size
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.use_time_features = use_time_features
        self.use_code_embedding = use_code_embedding
        self.probabilistic = probabilistic
        
        # 计算实际输入大小
        actual_input_size = input_size
        if use_time_features:
            actual_input_size += time_features_size
            self.time_feature_proj = nn.Linear(time_features_size, time_features_size)
            
        if use_code_embedding:
            self.code_embedding = nn.Embedding(num_codes, code_embedding_size)
            actual_input_size += code_embedding_size
        
        # 输入投影层
        self.input_proj = nn.Linear(actual_input_size, hidden_size)
        self.dropout = nn.Dropout(dropout)
        
        # xLSTM层
        self.xlstm = xLSTM(
            input_size=hidden_size,
            head_size=head_size,
            num_heads=num_heads,
            layers=layer_config,
            batch_first=True
        )
        
        # 输出层
        if probabilistic:
            # 概率预测 - 输出均值和方差
            self.output_mean = nn.Linear(hidden_size, output_size * pred_len)
            self.output_var = nn.Linear(hidden_size, output_size * pred_len)
        else:
            # 确定性预测
            self.output_proj = nn.Linear(hidden_size, output_size * pred_len)
    
    def forward(self, x, time_features=None, codes=None):
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, seq_len, input_size]
            time_features: 时间特征 [batch_size, seq_len, time_features_size]
            codes: 证券代码 [batch_size]
            
        Returns:
            如果probabilistic=True:
                (mean, var): 预测分布的均值和方差
            否则:
                predictions: 预测值 [batch_size, pred_len, output_size]
        """
        batch_size, seq_len, _ = x.shape
        
        # 合并输入特征
        inputs = [x]
        
        if self.use_time_features and time_features is not None:
            time_features = self.time_feature_proj(time_features)
            inputs.append(time_features)
            
        if self.use_code_embedding and codes is not None:
            # [batch_size] -> [batch_size, code_embedding_size]
            code_embeds = self.code_embedding(codes)
            # [batch_size, code_embedding_size] -> [batch_size, seq_len, code_embedding_size]
            code_embeds = code_embeds.unsqueeze(1).expand(-1, seq_len, -1)
            inputs.append(code_embeds)
        
        # 合并所有特征
        x = torch.cat(inputs, dim=-1)
        
        # 投影到隐藏维度
        x = self.input_proj(x)
        x = self.dropout(x)
        
        # 通过xLSTM处理序列
        outputs, _ = self.xlstm(x)
        
        # 只使用最后一个时间步的输出进行预测
        last_hidden = outputs[:, -1, :]
        
        if self.probabilistic:
            # 概率预测
            mean = self.output_mean(last_hidden)
            var = F.softplus(self.output_var(last_hidden)) + 1e-6
            
            # 重塑为 [batch_size, pred_len, output_size]
            mean = mean.view(batch_size, self.pred_len, self.output_size)
            var = var.view(batch_size, self.pred_len, self.output_size)
            
            return mean, var
        else:
            # 确定性预测
            predictions = self.output_proj(last_hidden)
            
            # 重塑为 [batch_size, pred_len, output_size]
            predictions = predictions.view(batch_size, self.pred_len, self.output_size)
            
            return predictions
