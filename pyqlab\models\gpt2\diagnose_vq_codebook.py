"""
诊断VQ码本问题的工具

这个脚本用于诊断VQ码本训练和使用过程中可能出现的问题，
通过可视化原始K线数据、向量化后的数据、量化后的数据和重建的K线数据，
帮助分析码本训练和使用过程中可能出现的问题。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入相关模块
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.utils import load_single_data

def diagnose_codebook(
    data_path,
    codebook_path,
    encoder_onnx_path,
    decoder_onnx_path,
    output_dir='./vq_diagnosis',
    num_embeddings=512,
    embedding_dim=5,
    atr_period=100,
    ma_volume_period=100,
    vectorization_method='percent_change',
    num_candles=30,
    show_vectors=True,
    show_details=True
):
    """
    诊断VQ码本问题

    Args:
        data_path: K线数据路径
        codebook_path: 码本权重路径
        encoder_onnx_path: 编码器ONNX模型路径
        decoder_onnx_path: 解码器ONNX模型路径
        output_dir: 输出目录
        num_embeddings: 码本大小
        embedding_dim: 向量维度
        atr_period: ATR计算周期
        ma_volume_period: 成交量移动平均周期
        vectorization_method: 向量化方法
        num_candles: 要显示的K线数量
        show_vectors: 是否显示向量化和量化后的数据
        show_details: 是否显示详细的数据比较
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载数据
    print(f"加载数据: {data_path}")
    try:
        if data_path.endswith('.parquet'):
            df_list, code_ids = load_single_data(data_path)
            if not df_list:
                raise ValueError("未能加载到任何数据")
            df_ohlcv = df_list[4]
            code_id = code_ids[4] if code_ids else 0
            print(f"已加载数据，证券代码: {code_id}, 数据量: {len(df_ohlcv)}")
            print(df_ohlcv.head())
        else:
            # 尝试直接加载CSV或其他格式
            df_ohlcv = pd.read_csv(data_path)
            print(f"已加载数据，数据量: {len(df_ohlcv)}")
            
        # 确保列名小写
        df_ohlcv.columns = [col.lower() for col in df_ohlcv.columns]
        
        # 确保有必要的列
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df_ohlcv.columns]
        if missing_cols:
            raise ValueError(f"数据缺少必要的列: {missing_cols}")
            
        # 如果没有datetime列，添加一个
        if 'datetime' not in df_ohlcv.columns:
            df_ohlcv['datetime'] = pd.date_range(start='2023-01-01', periods=len(df_ohlcv))
            
    except Exception as e:
        print(f"加载数据失败: {e}")
        print("创建模拟数据...")
        # 创建模拟数据
        data = {
            'datetime': pd.date_range(start='2023-01-01', periods=100),
            'open': np.random.rand(100) * 10 + 100,
            'high': np.random.rand(100) * 5 + 105,
            'low': np.random.rand(100) * -5 + 95,
            'close': np.random.rand(100) * 10 + 100,
            'volume': np.random.randint(1000, 5000, size=100)
        }
        df_ohlcv = pd.DataFrame(data)
        df_ohlcv['high'] = df_ohlcv[['open', 'close', 'high']].max(axis=1)
        df_ohlcv['low'] = df_ohlcv[['open', 'close', 'low']].min(axis=1)
        
        # 添加一些人工跳空
        # 在第10个K线添加向上跳空
        df_ohlcv.loc[10, 'open'] = df_ohlcv.loc[9, 'close'] * 1.05
        df_ohlcv.loc[10, 'high'] = max(df_ohlcv.loc[10, 'high'], df_ohlcv.loc[10, 'open'] * 1.02)
        df_ohlcv.loc[10, 'close'] = df_ohlcv.loc[10, 'open'] * 1.01
        
        # 在第15个K线添加向下跳空
        df_ohlcv.loc[15, 'open'] = df_ohlcv.loc[14, 'close'] * 0.95
        df_ohlcv.loc[15, 'low'] = min(df_ohlcv.loc[15, 'low'], df_ohlcv.loc[15, 'open'] * 0.98)
        df_ohlcv.loc[15, 'close'] = df_ohlcv.loc[15, 'open'] * 0.99

    # 选择向量化方法
    if vectorization_method == 'atr_based':
        vec_method = VectorizationMethod.ATR_BASED
    elif vectorization_method == 'percent_change':
        vec_method = VectorizationMethod.PERCENT_CHANGE
    elif vectorization_method == 'log_return':
        vec_method = VectorizationMethod.LOG_RETURN
    elif vectorization_method == 'zscore':
        vec_method = VectorizationMethod.ZSCORE
    elif vectorization_method == 'minmax':
        vec_method = VectorizationMethod.MINMAX
    else:
        print(f"未知的向量化方法: {vectorization_method}，使用默认的percent_change")
        vec_method = VectorizationMethod.PERCENT_CHANGE

    # 创建tokenizer
    print(f"创建tokenizer，使用码本: {codebook_path}")
    print(f"向量化方法: {vec_method}")
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=codebook_path,
        encoder_onnx_path=encoder_onnx_path,
        decoder_onnx_path=decoder_onnx_path,
        num_embeddings=num_embeddings,
        embedding_dim=embedding_dim,
        atr_period=atr_period,
        ma_volume_period=ma_volume_period,
        vectorization_method=vec_method,
        gap_threshold=3.0,
        detect_gaps=True,
        use_code_dim=True,
        code_size=100,
        code_dim=4
    )

    # 检查码本权重
    print("\n码本权重统计:")
    with torch.no_grad():
        weights = tokenizer.vq_layer.embedding.weight.data
        print(f"码本大小: {weights.shape}")
        print(f"权重均值: {weights.mean().item()}")
        print(f"权重标准差: {weights.std().item()}")
        print(f"权重最小值: {weights.min().item()}")
        print(f"权重最大值: {weights.max().item()}")
        
        # 检查是否有NaN或无穷值
        has_nan = torch.isnan(weights).any().item()
        has_inf = torch.isinf(weights).any().item()
        print(f"是否有NaN值: {has_nan}")
        print(f"是否有无穷值: {has_inf}")
        
        # 检查权重分布
        plt.figure(figsize=(10, 6))
        plt.hist(weights.flatten().numpy(), bins=50)
        plt.title("码本权重分布")
        plt.xlabel("权重值")
        plt.ylabel("频率")
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, "codebook_weights_distribution.png"))
        plt.close()

    for code, df_ohlcv in zip(code_ids, df_list):
        # 可视化原始K线和重建的K线
        print("\n可视化原始K线和重建的K线...")
        fig = tokenizer.visualize_tokenization(
            df_ohlcv, 
            num_candles=num_candles,
            show_vectors=show_vectors,
            show_details=show_details,
            code_id=code_id
        )
        
        # 保存图像
        output_file = os.path.join(output_dir, f"vq_diagnosis_{vectorization_method}_{code}.png")
        fig.savefig(output_file)
        print(f"可视化结果已保存到 {output_file}")
        
        # 关闭图形以释放内存
        plt.close(fig)
    
    print("\n诊断完成!")

def main():
    parser = argparse.ArgumentParser(description="诊断VQ码本问题")
    parser.add_argument("--data_path", type=str, default="", help="K线数据路径")
    parser.add_argument("--codebook_path", type=str, required=True, help="码本权重路径")
    parser.add_argument("--encoder_onnx_path", type=str, default="", help="编码器ONNX模型路径")
    parser.add_argument("--decoder_onnx_path", type=str, default="", help="解码器ONNX模型路径")
    parser.add_argument("--output_dir", type=str, default="./vq_diagnosis", help="输出目录")
    parser.add_argument("--num_embeddings", type=int, default=512, help="码本大小")
    parser.add_argument("--embedding_dim", type=int, default=5, help="向量维度")
    parser.add_argument("--atr_period", type=int, default=100, help="ATR计算周期")
    parser.add_argument("--ma_volume_period", type=int, default=100, help="成交量移动平均周期")
    parser.add_argument("--vectorization_method", type=str, default="percent_change", 
                        choices=["atr_based", "percent_change", "log_return", "zscore", "minmax", "candlestick_features"],
                        help="向量化方法")
    parser.add_argument("--num_candles", type=int, default=30, help="要显示的K线数量")
    parser.add_argument("--no_vectors", action="store_true", help="不显示向量化和量化后的数据")
    parser.add_argument("--no_details", action="store_true", help="不显示详细的数据比较")
    
    args = parser.parse_args()
    
    diagnose_codebook(
        data_path=args.data_path,
        codebook_path=args.codebook_path,
        encoder_onnx_path=args.encoder_onnx_path,
        decoder_onnx_path=args.decoder_onnx_path,
        output_dir=args.output_dir,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim,
        atr_period=args.atr_period,
        ma_volume_period=args.ma_volume_period,
        vectorization_method=args.vectorization_method,
        num_candles=args.num_candles,
        show_vectors=not args.no_vectors,
        show_details=not args.no_details
    )

if __name__ == "__main__":
    main()
