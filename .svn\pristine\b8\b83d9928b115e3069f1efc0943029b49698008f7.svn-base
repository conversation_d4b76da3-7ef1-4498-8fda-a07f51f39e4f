{"cells": [{"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[28736:MainThread](2022-10-17 22:51:12,238) INFO - qlib.Initialization - [config.py:413] - default_conf: client.\n", "[28736:MainThread](2022-10-17 22:51:12,244) INFO - qlib.Initialization - [__init__.py:74] - qlib successfully initialized based on client settings.\n", "[28736:MainThread](2022-10-17 22:51:12,246) INFO - qlib.Initialization - [__init__.py:76] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "# from typing import Text, Union\n", "# from qlib.utils import get_or_create_path\n", "\n", "import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config\n", "from qlib.model.base import Model\n", "from qlib.data.dataset import DatasetH\n", "from qlib.data.dataset.handler import DataHandlerLP\n", "\n", "from pyqlab.data.dataset.handler import DataHandlerAF\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import tensorflow as tf\n", "from tensorflow.keras import layers\n", "from tensorflow.keras import regularizers\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install git+https://github.com/tensorflow/docs\n", "\n", "import tensorflow_docs as tfdocs\n", "import tensorflow_docs.modeling\n", "import tensorflow_docs.plots"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    # \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    # \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    # \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    # \"SQUEEZE_ZERO_BARS\", \n", "    # \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    # \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    # \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    # \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    # \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    # \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_HLR\", \"FAST_QH_HLR_ZSCORE\", \"FAST_QH_HLR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]\n", "\n", "data_handler_config = {\n", "    \"start_time\": \"\",\n", "    \"end_time\": \"\",\n", "    \"instruments\": ['06220831232331000', '01220901173143000'],\n", "    \"data_loader\": {\n", "        \"class\": \"AFDataLoader\",\n", "        \"module_path\": \"pyqlab.data.dataset.loader\",\n", "        \"kwargs\": {\n", "            \"direct\": \"L\",\n", "            \"model_name\": \"MLP_HLR\",\n", "            \"model_name_suff\": \"\",\n", "            \"model_path\": \"e:/lab/RoboQuant/pylab/model\",\n", "            \"data_path\": \"e:/lab/RoboQuant/pylab/data\",\n", "            \"sel_lf_names\": SEL_LONG_FACTOR_NAMES,\n", "            \"sel_sf_names\": SEL_SHORT_FACTOR_NAMES,\n", "            \"sel_ct_names\": SEL_CONTEXT_FACTOR_NAMES,\n", "        }\n", "    },\n", "}\n", "\n", "dataset_config = {\n", "    \"class\": \"AFDatasetH\",\n", "    \"module_path\": \"pyqlab.data.dataset\",\n", "    \"kwargs\": {\n", "        \"handler\": {\n", "            \"class\": \"DataHandlerAF\",\n", "            \"module_path\": \"pyqlab.data.dataset.handler\",\n", "            \"kwargs\": data_handler_config,\n", "        },\n", "        \"segments\": [\"train\", \"valid\"],\n", "        \"col_set\": [\"feature\", \"label\", \"encoded\"],\n", "    },\n", "}\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[28736:MainThread](2022-10-17 13:32:38,520) INFO - qlib.DataHandlerAF - [handler.py:83] - \n", "============================\n", "\n", "lb(478, 6) sf(478, 45) lf(478, 0) ct(478, 11)\n", "\n", "============================\n", "[28736:MainThread](2022-10-17 13:32:38,530) INFO - qlib.timer - [log.py:117] - Time cost: 0.143s | Loading data Done\n", "[28736:MainThread](2022-10-17 13:32:38,531) INFO - qlib.DataHandlerAF - [handler.py:130] - train shape(382, 56) valid shape(96, 56)\n", "[28736:MainThread](2022-10-17 13:32:38,532) INFO - qlib.timer - [log.py:117] - Time cost: 0.145s | Init data Done\n", "[28736:MainThr<PERSON>](2022-10-17 13:32:38,544) INFO - qlib.DataHandlerAF - [handler.py:83] - \n", "============================\n", "\n", "lb(478, 6) sf(478, 45) lf(478, 0) ct(478, 11)\n", "\n", "============================\n", "[28736:MainThread](2022-10-17 13:32:38,553) INFO - qlib.timer - [log.py:117] - Time cost: 0.017s | Loading data Done\n", "[28736:MainThread](2022-10-17 13:32:38,554) INFO - qlib.DataHandlerAF - [handler.py:130] - train shape(382, 56) valid shape(96, 56)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Feature total: 478\n", "Today add long count: 0\n", "Feature total: 478\n", "Today add long count: 0\n"]}], "source": ["data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n", "hd_long: DataHandlerAF = init_instance_by_config(dataset_config[\"kwargs\"][\"handler\"])\n", "dataset_config[\"kwargs\"][\"handler\"] = hd_long\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n", "\n", "dataset = init_instance_by_config(dataset_config)\n", "dataset.setup_data(handler_kwargs=data_handler_config)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[28736:MainThread](2022-10-17 13:32:48,997) INFO - qlib.DatasetH - [__init__.py:83] - data_key[learn] is ignored.\n"]}], "source": ["df_train, df_valid = dataset.prepare(\n", "    [\"train\", \"valid\"],\n", "    col_set=[\"feature\", \"label\", \"encoded\"],\n", "    data_key=DataHandlerLP.DK_L,\n", ")\n", "code_train, x_train, y_train = df_train[\"encoded\"], df_train[\"feature\"], df_train[\"label\"]\n", "code_valid, x_valid, y_valid = df_valid[\"encoded\"], df_valid[\"feature\"], df_valid[\"label\"]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"19\" halign=\"left\">feature</th>\n", "      <th>label</th>\n", "      <th>encoded</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>sf_MACD</th>\n", "      <th>sf_MACD_DIFF</th>\n", "      <th>sf_MACD_DEA</th>\n", "      <th>sf_MOM</th>\n", "      <th>sf_RSI_1</th>\n", "      <th>sf_RSI_2</th>\n", "      <th>sf_LR_SLOPE_FAST_1</th>\n", "      <th>sf_LR_SLOPE_FAST_2</th>\n", "      <th>sf_LR_SLOPE_MIDD_1</th>\n", "      <th>sf_LR_SLOPE_MIDD_2</th>\n", "      <th>...</th>\n", "      <th>FAST_QH_ZSCORE</th>\n", "      <th>FAST_QH_DIRECT</th>\n", "      <th>FAST_QH_HLR</th>\n", "      <th>FAST_QH_HLR_ZSCORE</th>\n", "      <th>FAST_QH_HLR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "      <th>label</th>\n", "      <th>code_encoded</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ord_id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1220901210032038</th>\n", "      <td>0.775471</td>\n", "      <td>-0.480253</td>\n", "      <td>-0.592198</td>\n", "      <td>-0.507684</td>\n", "      <td>-0.532087</td>\n", "      <td>-0.714286</td>\n", "      <td>1.467646</td>\n", "      <td>1.055867</td>\n", "      <td>0.040629</td>\n", "      <td>0.051607</td>\n", "      <td>...</td>\n", "      <td>-1.482909</td>\n", "      <td>-0.323974</td>\n", "      <td>0.093696</td>\n", "      <td>0.814938</td>\n", "      <td>-0.22575</td>\n", "      <td>-0.719016</td>\n", "      <td>-0.161427</td>\n", "      <td>-0.274482</td>\n", "      <td>0</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1220901210305156</th>\n", "      <td>3.897825</td>\n", "      <td>-1.948347</td>\n", "      <td>-2.525443</td>\n", "      <td>-2.315362</td>\n", "      <td>-1.594746</td>\n", "      <td>-1.593523</td>\n", "      <td>3.798071</td>\n", "      <td>4.004816</td>\n", "      <td>1.102600</td>\n", "      <td>2.170616</td>\n", "      <td>...</td>\n", "      <td>-2.098967</td>\n", "      <td>-0.323974</td>\n", "      <td>0.667698</td>\n", "      <td>1.859753</td>\n", "      <td>-0.22575</td>\n", "      <td>-0.719016</td>\n", "      <td>-0.161427</td>\n", "      <td>-0.274482</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1220901210321158</th>\n", "      <td>3.264353</td>\n", "      <td>-4.016288</td>\n", "      <td>-4.425778</td>\n", "      <td>-3.847957</td>\n", "      <td>-2.584611</td>\n", "      <td>-2.633490</td>\n", "      <td>4.772612</td>\n", "      <td>4.465590</td>\n", "      <td>-1.950567</td>\n", "      <td>-0.292552</td>\n", "      <td>...</td>\n", "      <td>-2.098967</td>\n", "      <td>-0.323974</td>\n", "      <td>0.667698</td>\n", "      <td>1.859753</td>\n", "      <td>-0.22575</td>\n", "      <td>-0.719016</td>\n", "      <td>-0.161427</td>\n", "      <td>-0.274482</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1220901210639174</th>\n", "      <td>-0.454980</td>\n", "      <td>-0.452114</td>\n", "      <td>-0.363716</td>\n", "      <td>-0.488036</td>\n", "      <td>-0.724160</td>\n", "      <td>-0.692971</td>\n", "      <td>-0.672107</td>\n", "      <td>-0.971536</td>\n", "      <td>-0.460111</td>\n", "      <td>-0.438504</td>\n", "      <td>...</td>\n", "      <td>-1.598235</td>\n", "      <td>-0.323974</td>\n", "      <td>0.476666</td>\n", "      <td>1.507223</td>\n", "      <td>-0.22575</td>\n", "      <td>-0.719016</td>\n", "      <td>-0.161427</td>\n", "      <td>-0.274482</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1220901211241188</th>\n", "      <td>0.210850</td>\n", "      <td>-0.026013</td>\n", "      <td>-0.059688</td>\n", "      <td>0.769479</td>\n", "      <td>-0.927352</td>\n", "      <td>-0.603304</td>\n", "      <td>-0.905149</td>\n", "      <td>-0.142144</td>\n", "      <td>-0.132446</td>\n", "      <td>0.275040</td>\n", "      <td>...</td>\n", "      <td>-1.625810</td>\n", "      <td>-0.323974</td>\n", "      <td>0.501721</td>\n", "      <td>1.545605</td>\n", "      <td>-0.22575</td>\n", "      <td>-0.719016</td>\n", "      <td>-0.161427</td>\n", "      <td>-0.274482</td>\n", "      <td>1</td>\n", "      <td>29</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 58 columns</p>\n", "</div>"], "text/plain": ["                   feature                                               \\\n", "                   sf_MACD sf_MACD_DIFF sf_MACD_DEA    sf_MOM  sf_RSI_1   \n", "ord_id                                                                    \n", "1220901210032038  0.775471    -0.480253   -0.592198 -0.507684 -0.532087   \n", "1220901210305156  3.897825    -1.948347   -2.525443 -2.315362 -1.594746   \n", "1220901210321158  3.264353    -4.016288   -4.425778 -3.847957 -2.584611   \n", "1220901210639174 -0.454980    -0.452114   -0.363716 -0.488036 -0.724160   \n", "1220901211241188  0.210850    -0.026013   -0.059688  0.769479 -0.927352   \n", "\n", "                                                                  \\\n", "                  sf_RSI_2 sf_LR_SLOPE_FAST_1 sf_LR_SLOPE_FAST_2   \n", "ord_id                                                             \n", "1220901210032038 -0.714286           1.467646           1.055867   \n", "1220901210305156 -1.593523           3.798071           4.004816   \n", "1220901210321158 -2.633490           4.772612           4.465590   \n", "1220901210639174 -0.692971          -0.672107          -0.971536   \n", "1220901211241188 -0.603304          -0.905149          -0.142144   \n", "\n", "                                                        ...                 \\\n", "                 sf_LR_SLOPE_MIDD_1 sf_LR_SLOPE_MIDD_2  ... FAST_QH_ZSCORE   \n", "ord_id                                                  ...                  \n", "1220901210032038           0.040629           0.051607  ...      -1.482909   \n", "1220901210305156           1.102600           2.170616  ...      -2.098967   \n", "1220901210321158          -1.950567          -0.292552  ...      -2.098967   \n", "1220901210639174          -0.460111          -0.438504  ...      -1.598235   \n", "1220901211241188          -0.132446           0.275040  ...      -1.625810   \n", "\n", "                                                                \\\n", "                 FAST_QH_DIRECT FAST_QH_HLR FAST_QH_HLR_ZSCORE   \n", "ord_id                                                           \n", "1220901210032038      -0.323974    0.093696           0.814938   \n", "1220901210305156      -0.323974    0.667698           1.859753   \n", "1220901210321158      -0.323974    0.667698           1.859753   \n", "1220901210639174      -0.323974    0.476666           1.507223   \n", "1220901211241188      -0.323974    0.501721           1.545605   \n", "\n", "                                                                    \\\n", "                 FAST_QH_HLR_DIRECT FAST_QH_MOM FAST_QH_MOM_ZSCORE   \n", "ord_id                                                               \n", "1220901210032038           -0.22575   -0.719016          -0.161427   \n", "1220901210305156           -0.22575   -0.719016          -0.161427   \n", "1220901210321158           -0.22575   -0.719016          -0.161427   \n", "1220901210639174           -0.22575   -0.719016          -0.161427   \n", "1220901211241188           -0.22575   -0.719016          -0.161427   \n", "\n", "                                    label      encoded  \n", "                 FAST_QH_MOM_DIRECT label code_encoded  \n", "ord_id                                                  \n", "1220901210032038          -0.274482     0           27  \n", "1220901210305156          -0.274482     0            7  \n", "1220901210321158          -0.274482     0            5  \n", "1220901210639174          -0.274482     0           15  \n", "1220901211241188          -0.274482     1           29  \n", "\n", "[5 rows x 58 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["code_train_values = np.squeeze(code_train.values)\n", "x_train_values = x_train.values\n", "y_train_values = np.squeeze(y_train.values)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["code_valid_values = np.squeeze(code_valid.values)\n", "x_valid_values = x_valid.values\n", "y_valid_values = np.squeeze(y_valid.values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["embedd = tf.keras.layers.Embedding(input_dim=60, output_dim=1)\n", "output = embedd((code_train_values))\n", "print(output)"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [], "source": ["# onehot独热编码\n", "# oh = tf.keras.utils.to_categorical()\n", "class MLP(tf.keras.Model):\n", "    def __init__(self, num_code, num_input, dropout=0.5, output_dim=1):\n", "        super().__init__()\n", "        self.embedding = layers.Embedding(input_dim=num_code, output_dim=1)\n", "        self.concat = layers.Concatenate(axis=1)\n", "        self.flatten = layers.Flatten()    # Flatten层将除第一维（batch_size）以外的维度展平\n", "        self.dense1 = layers.Dense(units=96, activation='relu', kernel_regularizer=regularizers.l2(0.0001))\n", "        self.dense2 = layers.Dense(units=32, activation='relu', kernel_regularizer=regularizers.l2(0.0001))\n", "        self.dense3 = layers.Dense(units=1)\n", "        self.dropout = layers.Dropout(rate=dropout)\n", "        self.bn = layers.BatchNormalization(axis=1)\n", "\n", "    def call(self, inputs):         # [128, 28, 28]\n", "        embedding = self.embedding(inputs[0])\n", "        x = self.concat([inputs[1], embedding])\n", "        x = self.dense1(x)\n", "        x = self.dropout(x)\n", "        x = self.dense2(x)\n", "        x = self.dropout(x)\n", "        x = self.dense3(x)\n", "        output = tf.nn.sigmoid(x)\n", "        return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["N_VALIDATION = int(1e3)\n", "N_TRAIN = int(1e4)\n", "BUFFER_SIZE = int(1e4)\n", "BATCH_SIZE = 500\n", "STEPS_PER_EPOCH = N_TRAIN//BATCH_SIZE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lr_schedule = tf.keras.optimizers.schedules.InverseTimeDecay(\n", "  0.001,\n", "  decay_steps=STEPS_PER_EPOCH*1000,\n", "  decay_rate=1,\n", "  staircase=False)\n", "\n", "def get_optimizer():\n", "  return tf.keras.optimizers.Adam(lr_schedule)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_callbacks(name):\n", "  return [\n", "    # tfdocs.modeling.EpochDots(),\n", "    tf.keras.callbacks.EarlyStopping(monitor='val_binary_crossentropy', patience=20),\n", "    # tf.keras.callbacks.TensorBoard(logdir/name),\n", "  ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compile_and_fit(model, name, optimizer=None, max_epochs=100):\n", "  if optimizer is None:\n", "    optimizer = get_optimizer()\n", "  model.compile(optimizer=optimizer,\n", "                loss=tf.keras.losses.BinaryCrossentropy(from_logits=True),\n", "                metrics=[\n", "                  tf.keras.losses.BinaryCrossentropy(\n", "                      from_logits=True, name='binary_crossentropy'),\n", "                  'accuracy'])\n", "\n", "  model.summary()\n", "\n", "  history = model.fit(\n", "    [code_train_values, x_train_values],\n", "    steps_per_epoch = STEPS_PER_EPOCH,\n", "    epochs=max_epochs,\n", "    validation_data=([code_valid_values, x_valid_values], y_valid_values),\n", "    callbacks=get_callbacks(name),\n", "    verbose=0)\n", "  return history"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["model = MLP(60, 56, 0.3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["hist = compile_and_fit(model, 'sizes/Tiny')"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [], "source": ["model.compile(\n", "    optimizer=tf.keras.optimizers.<PERSON>(learning_rate=0.001),\n", "    loss = tf.keras.losses.binary_crossentropy,\n", "    metrics=['acc']\n", ")"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/50\n", "12/12 [==============================] - 1s 14ms/step - loss: 0.7661 - acc: 0.5366 - val_loss: 0.7208 - val_acc: 0.5625\n", "Epoch 2/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.7409 - acc: 0.5262 - val_loss: 0.7163 - val_acc: 0.5521\n", "Epoch 3/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.7149 - acc: 0.5602 - val_loss: 0.7152 - val_acc: 0.5625\n", "Epoch 4/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6994 - acc: 0.5812 - val_loss: 0.7099 - val_acc: 0.5417\n", "Epoch 5/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6818 - acc: 0.6099 - val_loss: 0.7153 - val_acc: 0.5312\n", "Epoch 6/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6877 - acc: 0.5916 - val_loss: 0.7207 - val_acc: 0.5104\n", "Epoch 7/50\n", "12/12 [==============================] - 0s 5ms/step - loss: 0.6646 - acc: 0.5759 - val_loss: 0.7164 - val_acc: 0.5312\n", "Epoch 8/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6863 - acc: 0.5890 - val_loss: 0.7188 - val_acc: 0.5312\n", "Epoch 9/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6708 - acc: 0.6335 - val_loss: 0.7153 - val_acc: 0.5208\n", "Epoch 10/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6787 - acc: 0.5995 - val_loss: 0.7121 - val_acc: 0.5208\n", "Epoch 11/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6591 - acc: 0.6440 - val_loss: 0.7139 - val_acc: 0.5000\n", "Epoch 12/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6481 - acc: 0.6492 - val_loss: 0.7137 - val_acc: 0.5625\n", "Epoch 13/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6410 - acc: 0.6126 - val_loss: 0.7140 - val_acc: 0.5104\n", "Epoch 14/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6412 - acc: 0.6204 - val_loss: 0.7257 - val_acc: 0.4792\n", "Epoch 15/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6407 - acc: 0.6545 - val_loss: 0.7252 - val_acc: 0.4896\n", "Epoch 16/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6256 - acc: 0.6597 - val_loss: 0.7277 - val_acc: 0.4896\n", "Epoch 17/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5871 - acc: 0.7147 - val_loss: 0.7309 - val_acc: 0.5104\n", "Epoch 18/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6097 - acc: 0.6937 - val_loss: 0.7296 - val_acc: 0.4792\n", "Epoch 19/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.6110 - acc: 0.6806 - val_loss: 0.7373 - val_acc: 0.4688\n", "Epoch 20/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.5914 - acc: 0.7173 - val_loss: 0.7384 - val_acc: 0.4688\n", "Epoch 21/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.5994 - acc: 0.7068 - val_loss: 0.7464 - val_acc: 0.4583\n", "Epoch 22/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5944 - acc: 0.6571 - val_loss: 0.7543 - val_acc: 0.4479\n", "Epoch 23/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5760 - acc: 0.6754 - val_loss: 0.7586 - val_acc: 0.4583\n", "Epoch 24/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.6095 - acc: 0.6859 - val_loss: 0.7515 - val_acc: 0.4896\n", "Epoch 25/50\n", "12/12 [==============================] - 0s 5ms/step - loss: 0.5696 - acc: 0.7225 - val_loss: 0.7575 - val_acc: 0.5104\n", "Epoch 26/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5660 - acc: 0.7330 - val_loss: 0.7643 - val_acc: 0.4688\n", "Epoch 27/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5794 - acc: 0.7199 - val_loss: 0.7598 - val_acc: 0.4896\n", "Epoch 28/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5584 - acc: 0.7277 - val_loss: 0.7604 - val_acc: 0.4792\n", "Epoch 29/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.5861 - acc: 0.6990 - val_loss: 0.7596 - val_acc: 0.4896\n", "Epoch 30/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5439 - acc: 0.7304 - val_loss: 0.7641 - val_acc: 0.4896\n", "Epoch 31/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5175 - acc: 0.7356 - val_loss: 0.7762 - val_acc: 0.5208\n", "Epoch 32/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.5306 - acc: 0.7592 - val_loss: 0.7738 - val_acc: 0.4896\n", "Epoch 33/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5337 - acc: 0.7565 - val_loss: 0.7648 - val_acc: 0.5000\n", "Epoch 34/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4976 - acc: 0.7723 - val_loss: 0.7663 - val_acc: 0.5312\n", "Epoch 35/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5094 - acc: 0.7592 - val_loss: 0.7715 - val_acc: 0.5000\n", "Epoch 36/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.5291 - acc: 0.7304 - val_loss: 0.7720 - val_acc: 0.4896\n", "Epoch 37/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4907 - acc: 0.7644 - val_loss: 0.7766 - val_acc: 0.5000\n", "Epoch 38/50\n", "12/12 [==============================] - 0s 5ms/step - loss: 0.4943 - acc: 0.7880 - val_loss: 0.7901 - val_acc: 0.5000\n", "Epoch 39/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4818 - acc: 0.7592 - val_loss: 0.7875 - val_acc: 0.5000\n", "Epoch 40/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.4933 - acc: 0.7801 - val_loss: 0.7959 - val_acc: 0.4688\n", "Epoch 41/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.4892 - acc: 0.7827 - val_loss: 0.7992 - val_acc: 0.5000\n", "Epoch 42/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.4700 - acc: 0.7749 - val_loss: 0.7963 - val_acc: 0.4792\n", "Epoch 43/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.4876 - acc: 0.7749 - val_loss: 0.8029 - val_acc: 0.5208\n", "Epoch 44/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4924 - acc: 0.7592 - val_loss: 0.8240 - val_acc: 0.5000\n", "Epoch 45/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.4725 - acc: 0.7801 - val_loss: 0.8244 - val_acc: 0.5104\n", "Epoch 46/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4198 - acc: 0.8194 - val_loss: 0.8308 - val_acc: 0.5208\n", "Epoch 47/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4398 - acc: 0.7984 - val_loss: 0.8302 - val_acc: 0.5000\n", "Epoch 48/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4418 - acc: 0.8194 - val_loss: 0.8327 - val_acc: 0.5312\n", "Epoch 49/50\n", "12/12 [==============================] - 0s 4ms/step - loss: 0.4370 - acc: 0.7906 - val_loss: 0.8327 - val_acc: 0.5312\n", "Epoch 50/50\n", "12/12 [==============================] - 0s 3ms/step - loss: 0.4389 - acc: 0.8010 - val_loss: 0.8318 - val_acc: 0.5104\n"]}], "source": ["hist = model.fit(\n", "    x=[code_train_values, x_train_values],\n", "    y=y_train_values,\n", "    epochs=50,\n", "    batch_size=32,\n", "    verbose=1,\n", "    validation_data=([code_valid_values, x_valid_values], y_valid_values),\n", "    # validation_split=0.1,\n", "    # callbacks=[cp_callback]\n", ")"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x29d705d7700>"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(hist.epoch, hist.history.get('acc'), label='acc')\n", "plt.plot(hist.epoch, hist.history.get('val_acc'), label='val_acc')\n", "plt.legend()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["checkpoint_path = \"./training/cp.ckpt\"\n", "checkpoint_dir = os.path.dirname(checkpoint_path)\n", "\n", "#创建一个检查点回调\n", "cp_callback = tf.keras.callbacks.ModelCheckpoint(\n", "    checkpoint_path,\n", "    save_weights_only=True,\n", "    verbose=1,\n", "    period=5 # 每5个周期保存一次\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["latest = tf.train.latest_checkpoint(checkpoint_dir) #查看生成的检查点并选择最新的检查点\n", "#model = create_model()\n", "model.load_weights(latest) # 重置模型并加载最新的检查点\n", "#loss, acc = model.evaluate(test_images, test_labels)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3/3 - 0s - loss: 1.2106 - acc: 0.5208 - 21ms/epoch - 7ms/step\n"]}], "source": ["loss, acc = model.evaluate([code_valid_values,x_valid_values],y_valid_values,verbose=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["保存整个模型\n", "模型和优化器可以保存到包含其状态（权重和变量）和模型配置的文件中，这允许您导出模型，以便可以在不访问原始python代码的情况下使用它。由于恢复了优化器状态，您甚至可以从中断的位置恢复训练。\n", "\n", "保存完整的模型非常有用，您可以在TensorFlow.js(HDF5, Saved Model) 中加载它们，然后在Web浏览器中训练和运行它们，或者使用TensorFlow Lite(HDF5, Saved Model)将它们转换为在移动设备上运行。"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["#保存整个模型到HDF5文件 \n", "# model.save_weights('./model/tf2_mlp.h5')\n", "tf.saved_model.save(model, \"./model/tf2_mlp\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load_weights('./model/tf2_mlp.h5')"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'_UserObject' object has no attribute 'summary'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn [102], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[39m# new_model = tf.keras.models.load_model('./model/tf2_mlp.h5')\u001b[39;00m\n\u001b[0;32m      2\u001b[0m new_model \u001b[39m=\u001b[39m tf\u001b[39m.\u001b[39msaved_model\u001b[39m.\u001b[39mload(\u001b[39m\"\u001b[39m\u001b[39m./model/tf2_mlp\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m----> 3\u001b[0m new_model\u001b[39m.\u001b[39msummary()\n", "\u001b[1;31mAttributeError\u001b[0m: '_UserObject' object has no attribute 'summary'"]}], "source": ["# new_model = tf.keras.models.load_model('./model/tf2_mlp.h5')\n", "new_model = tf.saved_model.load(\"./model/tf2_mlp\")\n", "new_model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loss, acc = new_model.evaluate([code_valid_values,x_valid_values],y_valid_values,verbose=2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}