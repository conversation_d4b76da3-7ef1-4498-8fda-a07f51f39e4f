# RoboQuant量化交易实验室

这是一个基于Python的量化交易实验项目，用于研究和实现各种交易策略。项目集成了多种量化交易功能，包括数据处理、策略回测、实时交易等。

## 📝 开发计划

- [ ] 自下而上使用Cursor重新优化，规范项目，遵从AI工具开发流程
- [ ] bar encoding time feature 去掉年月部分
- [ ] 增加更多策略模型，优化模型结构
- [ ] 模型性能比较
- [ ] 添加更多单元测试

## 🚀 主要特性

- 数据处理与分析

  - 支持通过tushare获取A股市场数据
  - 支持通过通达信导出数据
  - 集成数据预处理和特征工程工具
- 策略研究与回测

  - 基于backtrader的策略回测框架
  - 支持多种机器学习和深度学习模型
  - 强化学习策略研究(基于Ray RLlib)
- 实盘交易

  - 模拟交易服务
  - 实时行情订阅
  - 风险控制模块

## 📁 项目结构

```
pyqlab/
├── app/          # Web应用界面
├── core/         # 核心功能模块
├── data/         # 数据处理模块
├── models/       # 模型定义
├── portfolio/    # 投资组合管理
├── rl/           # 强化学习相关
├── scripts/      # 工具脚本
├── trading/      # 交易相关功能
└── utils/        # 工具函数
```

## 🔧 环境要求

- Python 3.7+
- PyTorch 1.11.0
- 其他依赖见 requirements.txt

## 📦 安装说明

1. 克隆项目到本地
2. 创建并激活虚拟环境（推荐）
3. 安装依赖：

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 🎮 使用方法

### 数据获取

- 使用 `export_tdx_barenc.bat` 导出通达信数据
- 使用 `export_tdx_min1.bat` 导出分钟级数据

### 模型训练

- `train_bar_gpt.bat`: GPT模型训练
- `train_mktt_rl.bat`: 强化学习模型训练
- `train_conv_r.bat`: 卷积网络模型训练

### 模拟交易

运行 `simuquotionsrv.bat` 启动模拟交易服务

## 📄 许可证

本项目采用 MIT 许可证
