{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyecharts import online\n", "import datetime\n", "import talib as ta\n", "import pandas as pd\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def stats(order_df, group=[]):\n", "    df = order_df.set_index('ID')\n", "    df['cnt'] = 1\n", "    df['win'] = 1\n", "    df.loc[df.pnl<0, 'win'] = 0\n", "    # idx = {'year': df.index.year, 'month': df.index.month, 'week': df.index.week}\n", "    pnl_sum = df.groupby(by=group).pnl.sum()\n", "    pnl_min = df.groupby(by=group).pnl.min()\n", "    pnl_max = df.groupby(by=group).pnl.max()\n", "    pnl_cnt = df.groupby(by=group).cnt.sum()\n", "    pnl_win = df.groupby(by=group).win.sum()\n", "    df = {}\n", "    df[\"sum\"] = pnl_sum\n", "    df[\"min\"] = pnl_min\n", "    df[\"max\"] = pnl_max\n", "    df[\"win\"] = pnl_win\n", "    df[\"cnt\"] = pnl_cnt\n", "    df['win'] = df['win']*100.0/df['cnt']\n", "\n", "    df = pd.DataFrame(df)\n", "    ds = df.to_xarray()\n", "    ds = ds.fillna(0)\n", "    return ds\n", "\n", "def get_stats_report(self, group, period_index=-1):\n", "    ds = self.stats(group)\n", "    df = ds.sel(trading_dt=ds.coords['trading_dt'][period_index]).to_dataframe()\n", "    if len(ds.trading_dt.values) == 0 or len(ds.trading_dt.values) < abs(period_index) :\n", "        return {}\n", "    df = df.drop('trading_dt', 1)\n", "    df.reset_index(col_level=1, inplace=True)\n", "    df = df.loc[(df[group] != '')]\n", "    df = df.sort_values(by='sum', ascending=False)\n", "    # return len(ds.coords['trading_dt']), (ds.coords['trading_dt'][period_index]), df.to_json(orient='records')\n", "    return len(ds.coords['trading_dt']), (ds.trading_dt.values[period_index]), df.to_json(orient='records')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               pnl\n", "name              \n", "甲醇1905   184100.00\n", "丙烯1905    64575.00\n", "PTA1905   64280.00\n", "螺纹1905    46960.00\n", "棕榈1905    42470.00\n", "苹果1905    24250.00\n", "淀粉1905    -3752.46\n", "沪锌1902    -8995.00\n", "鸡蛋1905   -10520.00\n", "玉米1905   -15519.81\n", "热卷1905   -17000.00\n", "郑棉1905   -21540.00\n", "菜粕1905   -49670.00\n", "豆粕1905   -55580.00\n", "焦煤1905   -59700.00\n", "乙烯1905   -70810.00\n", "白糖1905   -89640.00\n", "动煤1905  -142124.77\n"]}], "source": ["csv_ptf = 'd:/QuantLab/rpt/00181218082801024.ptf.csv'\n", "csv_ord = 'd:/QuantLab/rpt/00181218082801024.ord.csv'\n", "df_ptf = pd.read_csv(csv_ptf, encoding='gbk')\n", "df_ord = pd.read_csv(csv_ord, encoding='gbk')\n", "#print(df_ptf)\n", "df_ord = df_ord.fillna(0)\n", "# print(df_ord.head(5))\n", "# print(stats(df_ord, ['name']))\n", "df = {}\n", "df = pd.DataFrame(df_ord.groupby([\"name\"])[\"pnl\"].sum())\n", "df = df.sort_values(by='pnl', ascending=False)\n", "print(df)\n", "# print(df_ord.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 2}