{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pylab as plt\n", "import datetime\n", "\n", "from sklearn.preprocessing import LabelBinarizer\n", "from sklearn.utils import shuffle\n", "from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/h5py/__init__.py:34: FutureWarning: Conversion of the second argument of issubdtype from `float` to `np.floating` is deprecated. In future, it will be treated as `np.float64 == np.dtype(float).type`.\n", "  from ._conv import register_converters as _register_converters\n", "Using TensorFlow backend.\n"]}], "source": ["from keras.models import Model\n", "from keras.layers import Dense, Dropout, GlobalAveragePooling1D, GaussianNoise, Conv1D, MaxPool1D, GlobalMaxPooling1D\n", "from keras.layers import Input, concatenate, SpatialDropout1D, Flatten, AvgPool1D, LSTM, Lambda, Reshape, GaussianDropout\n", "from keras.layers import BatchNormalization\n", "from keras.callbacks import ModelCheckpoint, EarlyStopping\n", "from keras import regularizers\n", "from keras.optimizers import RMSprop, Adam, SGD, Nadam\n", "import keras.backend as K\n", "from keras.regularizers import l1, l2 , l1_l2\n", "\n", "\n", "from sklearn.metrics import confusion_matrix\n", "from sklearn.metrics import classification_report, accuracy_score\n", "from sklearn.metrics import r2_score\n", "from sklearn.metrics import matthews_corrcoef\n", "from sklearn.metrics import f1_score"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["tick_bars = pd.read_csv('tick_bars.csv')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAD8CAYAAAB5Pm/hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\niFRySvQiIpWcEr2ISCX3/6RNaVnziObMAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.plot(tick_bars.close[:int(len(tick_bars) * 0.5)])\n", "plt.plot(tick_bars.close[int(len(tick_bars) * 0.5):int(len(tick_bars) * 0.7)])\n", "plt.plot(tick_bars.close[int(len(tick_bars) * 0.7):])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Making features"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": true}, "outputs": [], "source": ["from scipy.stats import entropy\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["N_BARS = 64000\n", "WINDOW_LONG = 100\n", "WINDOW_SHORT = 50\n", "HORIZON = 25\n", "T = 0.01\n", "H = 0.05"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi4yLCBo\ndHRwOi8vbWF0cGxvdGxpYi5vcmcvhp/UCwAAFa9JREFUeJzt3X+QnVV9x/H3l/AztSUkWSnNhm4o\nmArtNDo7/HLGSUkkPwDDCCJtwWhx1hGYMS2ZmiAOaiADVkp0alIzwhCxmqRQhxDiOEkkg50oEGD9\nEeKSFeJkU5D8AoUAnei3f9yTeAm77N3de87ZZ8/nNXNnn+c85977/Waz93vPOc99rrk7IiJSnqNy\nByAiInmoAIiIFEoFQESkUCoAIiKFUgEQESmUCoCISKFUAERECqUCICJSKBUAEZFCHZ07gLczfvx4\nb2tryx2GDFNdXV0ATJ48OXMk6ZSYswzcE088scfdW/rrN6wLQFtbG1u2bMkdhgxTU6dOBWDTpk1Z\n40ipxJxl4MzsV4300xSQiEihhvUIQOTt3HTTTblDSK7EnCUeFQCprOnTp+cOIbkSc5Z4NAUkldXZ\n2UlnZ2fuMJIqMWeJRyMAqax58+YBZS2IlpizxKMRgIhIoVQAREQKpQIgIlIoFQARkUJpEVgqa/Hi\nxVmfv23BQ4e3d9x2UZLnzJ2zjCwqAFJZ559/fu4QkisxZ4lHU0BSWZs3b2bz5s25w0iqxJwlHo0A\npLJuvPFGoKxz4kvMWeLRCEBEpFAqACIihVIBEBEplAqAiEihtAgslbVkyZLcISRXYs4SjwqAVNaU\nKVNyh5BciTlLPJoCksrasGEDGzZsyB1GUiXmLPFoBCCVdcsttwBlfUtWiTlLPBoBiIgUSgVARKRQ\nKgAiIoVSARARKZQWgaWyvv71r+cOIbkSc5Z4VACksiZPnpw7hMNSfTnMcMpZqk9TQFJZDz74IA8+\n+GDuMJIqMWeJRyMAqaw77rgDgEsuuSRzJOmUmLPEoxGAiEihVABERAqlAiAiUigVABGRQmkRWCrr\n3nvvTf6c9ad75pAjZxm5Gh4BmNkoM3vKzNaG/Ulm9qiZdZvZKjM7NrQfF/a7w/G2usdYGNq7zGxG\ns5ORskycOJGJEyfmDiOpEnOWeAYyBfRpYFvd/u3Ane5+OrAfuCa0XwPsD+13hn6Y2ZnAlcBZwExg\nqZmNGlr4UrJVq1axatWq3GEkVWLOEk9DBcDMWoGLgG+EfQMuAO4LXVYAl4btOWGfcHxa6D8HWOnu\nb7j7c0A3cHYzkpAyLVu2jGXLluUOI6kSc5Z4Gh0BLAH+Bfh92B8HvOTuB8N+DzAhbE8AdgKE4y+H\n/ofbe7nPYWbWYWZbzGzL7t27B5CKiIgMRL8FwMwuBl509ycSxIO7L3f3dndvb2lpSfGUIiJFauQs\noPcBHzSz2cDxwJ8AXwHGmNnR4V1+K7Ar9N8FTAR6zOxo4ERgb137IfX3ERGRxPodAbj7Qndvdfc2\naou4P3D3fwAeBi4P3eYCD4TtNWGfcPwH7u6h/cpwltAk4AzgsaZlIiIiAzKUzwF8BlhpZrcATwF3\nhfa7gHvNrBvYR61o4O5bzWw18DRwELjO3X83hOeXwt133339dxphSsxZ4hlQAXD3TcCmsP0svZzF\n4+6vAx/u4/63ArcONEiR3owfPz53CMmVmLPEo0tBSGXdc8893HPPPbnDSKrEnCUeFQCprBJfDEvM\nWeLRtYBE+jHQ6/+k+npIkaHSCEBEpFAqACIihVIBEBEplNYApLLWrVuXO4TkSsxZ4lEBkMoaPXp0\n7hCSKzFniUdTQFJZS5cuZenSpbnDSKrEnCUeFQCprNWrV7N69ercYSRVYs4SjwqAiEihVABERAql\nAiAiUigVABGRQuk0UKmsTZs25Q4huRJzlng0AhARKZRGAFJZX/7ylwGYP39+0x97oFcATSVmzlIe\njQCkstauXcvatWtzh5FUiTlLPCoAIiKFUgEQESmUCoCISKG0CCyVdcIJJ+QOIbkSc5Z4VACksr73\nve/lDiG5EnOWeDQFJCJSKBUAqaxFixaxaNGi3GEkVWLOEo8KgFTWxo0b2bhxY+4w3lbbgocO35qh\nCjlLdagAiIgUSgVARKRQKgAiIoXSaaBSWePGjcsdQnIl5izxqABIZd1///25Q0iuxJwlHk0BiYgU\nSgVAKmvhwoUsXLgwdxhJlZizxKMpIKmsH/3oR7lDSK7EnCUejQBERArVbwEws+PN7DEz+4mZbTWz\nL4T2SWb2qJl1m9kqMzs2tB8X9rvD8ba6x1oY2rvMbEaspEREpH+NjADeAC5w978BpgAzzexc4Hbg\nTnc/HdgPXBP6XwPsD+13hn6Y2ZnAlcBZwExgqZmNamYyIiLSuH4LgNe8EnaPCTcHLgDuC+0rgEvD\n9pywTzg+zcwstK909zfc/TmgGzi7KVlIkVpbW2ltbc0dRlIl5izxNLQIHN6pPwGcDnwN+CXwkrsf\nDF16gAlhewKwE8DdD5rZy8C40P7juoetv4/IgH3rW9/KHUJyJeYs8TRUANz9d8AUMxsDfBf4y1gB\nmVkH0AFw6qmnxnoakbdo1hU7RapiQGcBuftLwMPAecAYMztUQFqBXWF7FzARIBw/Edhb397Lfeqf\nY7m7t7t7e0tLy0DCk8LMmzePefPm5Q4jqRJzlngaOQuoJbzzx8xOAD4AbKNWCC4P3eYCD4TtNWGf\ncPwH7u6h/cpwltAk4AzgsWYlIuXp7Oyks7MzdxhJlZizxNPIFNApwIqwDnAUsNrd15rZ08BKM7sF\neAq4K/S/C7jXzLqBfdTO/MHdt5rZauBp4CBwXZhaEhGRDPotAO7+U+A9vbQ/Sy9n8bj768CH+3is\nW4FbBx6mSPXVrzHsuO2ijJGI1OiTwCIihdK1gKSy3vWud+UOIbkSc5Z4VACkspYvX547hORKzFni\n0RSQiEihVACksjo6Oujo6MgdRlIl5izxaApIKuuZZ57JHUJyJeYs8WgEICJSKBUAEZFCqQCIiBRK\nawBSWVOmTMkdQnIl5izxqABIZS1ZsiR3CMmVmLPEoykgEZFCqQBIZV111VVcddVVucNIqsScJR5N\nAUll9fT05A4huRJzlng0AhARKZRGACIZ6LsBZDjQCEBEpFAaAUhlnXfeeblDSK7EnCUeq31f+/DU\n3t7uW7ZsyR2GjGD1UzG5aApIms3MnnD39v76aQpIRKRQKgBSWZdddhmXXXZZ7jCSKjFniUdrAFJZ\ne/fuzR1CciXmLPFoBCAiUigVABGRQqkAiIgUSmsAUlnTpk3LHUJyJeYs8agASGV97nOfyx1CciXm\nLPFoCkhEpFAqAFJZs2bNYtasWbnDSKrEnCUeTQFJZb322mu5Q2iKgVwZdKTkLMODRgAiIoVSARAR\nKZQKgIhIobQGIJV18cUX5w4huRJzlnhUAKSy5s+fnzuE5ErMWeLRFJCISKH6LQBmNtHMHjazp81s\nq5l9OrSPNbP1ZrY9/DwptJuZfdXMus3sp2b23rrHmhv6bzezufHSkhJMnTqVqVOn5g4jqRJzlnga\nGQEcBG5w9zOBc4HrzOxMYAGw0d3PADaGfYBZwBnh1gEsg1rBAG4GzgHOBm4+VDRERCS9fguAuz/v\n7k+G7d8C24AJwBxgRei2Arg0bM8Bvuk1PwbGmNkpwAxgvbvvc/f9wHpgZlOzERGRhg1oDcDM2oD3\nAI8CJ7v78+HQC8DJYXsCsLPubj2hra/2I5+jw8y2mNmW3bt3DyQ8EREZgIbPAjKzdwD3A/Pc/Tdm\ndviYu7uZeTMCcvflwHKA9vb2pjymyCH1l10QKV1DBcDMjqH24v+f7v7fofnXZnaKuz8fpnheDO27\ngIl1d28NbbuAqUe0bxp86FK6K664IncIyZWYs8TTbwGw2lv9u4Bt7v5vdYfWAHOB28LPB+rarzez\nldQWfF8OReL7wOK6hd8LgYXNSUNKdO211+YOIbkSc5Z4GhkBvA+4GviZmXWGthupvfCvNrNrgF8B\nh96arANmA93AAeDjAO6+z8wWAY+Hfl90931NyUKKdODAAQBGjx6dOZJ0SsxZ4um3ALj7/wDWx+G3\nfD+duztwXR+PdTdw90ACFOnL7NmzAdi0aVPeQBIqMWeJR58EFhEplAqAiEihVABERAqlAiAiUihd\nDloq62Mf+1hD/UbSh78azVmkESoAUlklvhiWmLPEoykgqaw9e/awZ8+e3GEkVWLOEo9GAFJZl19+\nOVDWOfEl5izxqACIDCP16xU7brsoYyRSAk0BiYgUSgVARKRQKgAiIoXSGoBU1qc+9ancISRXYs4S\njwqAVNZHPvKR3CEkV2LOEo+mgKSydu7cyc6dO/vvOIKUmLPEoxGAVNbVV18NlHVOfIk5SzwaAYiI\nFEoFQESkUJoCEhmm9KlgiU0FQEakkXQJaJFYVACksm644YbcISRXYs4SjwqAVNYll1ySO4TkSsxZ\n4tEisFRWV1cXXV1ducNIqsScJR6NAKSyPvnJTwJlnRNfYs4Sj0YAIiKFUgEQESmUCoCISKFUAERE\nCqVFYKmsm266KXcIyZWYs8SjAiCVNX369Dftl/Dp3yNzFhkKTQFJZXV2dtLZ2Zk7jKRKzFni0QhA\nKmvevHlAGefEHxrdvPDtBZx72rgicpb4NAIQESmUCoCISKFUAERECtVvATCzu83sRTP7eV3bWDNb\nb2bbw8+TQruZ2VfNrNvMfmpm7627z9zQf7uZzY2TjoiINKqREcA9wMwj2hYAG939DGBj2AeYBZwR\nbh3AMqgVDOBm4BzgbODmQ0VDZLAWL17ML1ovpm3BQ0WcAgow5v1zWbx4ce4wZITotwC4+yPAviOa\n5wArwvYK4NK69m96zY+BMWZ2CjADWO/u+9x9P7CetxYVkQE5//zzOb713bnDSOr41nfz92v2F1X0\nJJ7BrgGc7O7Ph+0XgJPD9gRgZ12/ntDWV7vIoG3evJnXe7blDiOp13u2FZezxDPkRWB3d8CbEAsA\nZtZhZlvMbMvu3bub9bAyAt1444289MiK/juOIC89sqK4nCWewRaAX4epHcLPF0P7LmBiXb/W0NZX\n+1u4+3J3b3f39paWlkGGJyIi/RlsAVgDHDqTZy7wQF37R8PZQOcCL4epou8DF5rZSWHx98LQJiIi\nmfR7KQgz+w4wFRhvZj3Uzua5DVhtZtcAvwKuCN3XAbOBbuAA8HEAd99nZouAx0O/L7r7kQvLIiKS\nUL8FwN3/ro9D03rp68B1fTzO3cDdA4pOpBeHr4vz7N7MkeRXfybQjtsuyhiJVJEuBieVNXZaR+4Q\nkisxZ4lHBUAq69iTT8sdQnIl5izx6FpAUlmv7ejktR1lXRu/xJwlHo0ApLJe3rwSgBPapmSOJJ0S\nc5Z4NAIQESmUCoCISKFUAERECqU1AKkEXflSpPlUAKSyxs24PncIyb1dzvpQmAyUCoBU1jHjWnOH\nkFyJOUs8WgOQyjrQ/SgHuh/NHUZSJeYs8WgEIJX1m8e+C8Do08/JHEk6JeYs8agAiIxAWg+QRqgA\nyLClM39E4tIagIhIoVQAREQKpSkgqazxF9+QO4TkSsxZ4lEBkMo6+k9acoeQ3GBy1oKw9EVTQFJZ\nr257hFe3PZI7jKRKzFni0QhAhpWBnPnz26fWAfBH735/rHCGnRJzlnhUACQ7ne6ZjqaDpJ6mgERE\nCqURgGShd/0i+WkEICJSKI0ApLJaLl2YO4Tkmpmz1gNEBUAqa9ToE3OHkFyJOUs8KgCSTLPn/V/5\n2QYA3vHX05v6uMNZiTlLPCoAElXMxd4SXwxLzFni0SKwiEihNAIQkbeM1LQoXAYVAGk6neNffTpD\nqAyaAhIRKZRGADJoud/pv/PDn8/6/DmUmLPEowIgA5L7Rb/eUcccnzuE5HLk3NfvXFND1acCIP0a\nTi/69X77ZC2uP35vOS9EwylnrRNUnwqAVNarv/ghMDxeDFMZrjmrGFRT8gJgZjOBrwCjgG+4+22p\nY5A/GK7v7qW6VAyqI2kBMLNRwNeADwA9wONmtsbdn47xfPqP2Du96Esqjfxfq//b1N9sWqlHAGcD\n3e7+LICZrQTmAFEKQAn0Yi5V19f/4UYWn7VAPTSpC8AEYGfdfg9wTuIYhmQo/ylFZOga+fsa6N/g\nUP9+h3L/nCMgc/foT3L4ycwuB2a6+yfC/tXAOe5+fV2fDqAj7E4GupIF2DzjgT25g0hMOZehtJyr\nmu+fu3tLf51SjwB2ARPr9ltD22HuvhxYnjKoZjOzLe7enjuOlJRzGUrLeaTnm/pSEI8DZ5jZJDM7\nFrgSWJM4BhERIfEIwN0Pmtn1wPepnQZ6t7tvTRmDiIjUJP8cgLuvA9alft7EKj2FNUjKuQyl5Tyi\n8026CCwiIsOHLgctIlIoFYBBMrOxZrbezLaHnyf10W9u6LPdzOb2cnyNmf08fsRDN5SczWy0mT1k\nZr8ws61mNmwvAWJmM82sy8y6zWxBL8ePM7NV4fijZtZWd2xhaO8ysxkp4x6KweZsZh8wsyfM7Gfh\n5wWpYx+sofyew/FTzewVM5ufKuamc3fdBnEDvgQsCNsLgNt76TMWeDb8PClsn1R3/EPAt4Gf584n\nds7AaOBvQ59jgR8Cs3Ln1Ev8o4BfAqeFOH8CnHlEn2uB/wjbVwKrwvaZof9xwKTwOKNy5xQ55/cA\nfxa2/wrYlTuf2DnXHb8P+C9gfu58BnvTCGDw5gArwvYK4NJe+swA1rv7PnffD6wHZgKY2TuAfwZu\nSRBrsww6Z3c/4O4PA7j7/wFPUvscyHBz+HIlIc5DlyupV//vcB8wzcwstK909zfc/TmgOzzecDfo\nnN39KXf/39C+FTjBzI5LEvXQDOX3jJldCjxHLefKUgEYvJPd/fmw/QJwci99erv0xYSwvQi4AzgQ\nLcLmG2rOAJjZGOASYGOMIIeo3/jr+7j7QeBlYFyD9x2OhpJzvcuAJ939jUhxNtOgcw5v3j4DfCFB\nnFHp+wDehpltAP60l0Ofrd9xdzezhk+nMrMpwF+4+z8dOa+YW6yc6x7/aOA7wFc9XBRQqs/MzgJu\nBy7MHUsCnwfudPdXwoCgslQA3oa7T+/rmJn92sxOcffnzewU4MVeuu0CptbttwKbgPOAdjPbQe13\n8E4z2+TuU8ksYs6HLAe2u/uSJoQbQ7+XK6nr0xMK2onA3gbvOxwNJWfMrBX4LvBRd/9l/HCbYig5\nnwNcbmZfAsYAvzez19393+OH3WS5FyGqegP+lTcviH6plz5jqc0TnhRuzwFjj+jTRnUWgYeUM7X1\njvuBo3Ln8jY5Hk1t4XoSf1gcPOuIPtfx5sXB1WH7LN68CPws1VgEHkrOY0L/D+XOI1XOR/T5PBVe\nBM4eQFVv1OY/NwLbgQ11L3Lt1L7p7FC/f6S2GNgNfLyXx6lSARh0ztTeYTmwDegMt0/kzqmPPGcD\nz1A7S+Szoe2LwAfD9vHUzv7oBh4DTqu772fD/boYhmc5NTtn4Cbg1brfaSfwztz5xP491z1GpQuA\nPgksIlIonQUkIlIoFQARkUKpAIiIFEoFQESkUCoAIiKFUgEQESmUCoCISKFUAERECvX/7pKMaqZ6\nsTAAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(np.clip(tick_bars.close.pct_change(HORIZON), -H, H).dropna(), bins = 100)\n", "plt.axvline(-T, color = 'black', ls = '--')\n", "plt.axvline(T, color = 'black', ls = '--')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.04359708998044693\n", "(30474097.664683025, 0.0)\n", "(0.727247953414917, 0.0)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/scipy/stats/morestats.py:1310: UserWarning: p-value may not be accurate for N > 5000.\n", "  warnings.warn(\"p-value may not be accurate for N > 5000.\")\n"]}], "source": ["print pd.Series.autocorr(np.clip(tick_bars.close.pct_change(), -H, H).dropna())\n", "print stats.jarque_bera(np.clip(tick_bars.close.pct_change(), -H, H).dropna())\n", "print stats.shapiro(np.clip(tick_bars.close.pct_change(), -H, H).dropna())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["tick_bars['pct_change'] = np.clip(tick_bars.close.pct_change(), -H, H)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['pct_change']\n"]}], "source": ["FEATURES = ['pct_change']\n", "print FEATURES"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": true}, "outputs": [], "source": ["def make_features_from_window(X_train_b, X_val, X_test, features):   \n", "    \n", "    X_train_normts = np.array([(x.close - x.close.iloc[0]) / np.std(x.close) for x in X_train_b]).reshape((len(X_train_b), WINDOW_LONG, 1))\n", "    X_val_normts = np.array([(x.close - x.close.iloc[0]) / np.std(x.close) for x in X_val]).reshape((len(X_val), WINDOW_LONG, 1))\n", "    X_test_normts = np.array([(x.close - x.close.iloc[0]) / np.std(x.close) for x in X_test]).reshape((len(X_test), WINDOW_LONG, 1))\n", "    \n", "    X_train_normv = np.array([(x.volume - x.volume.iloc[0]) / np.std(x.volume) for x in X_train_b]).reshape((len(X_train_b), WINDOW_LONG, 1))\n", "    X_val_normvv = np.array([(x.volume - x.volume.iloc[0]) / np.std(x.volume) for x in X_val]).reshape((len(X_val), WINDOW_LONG, 1))\n", "    X_test_normv = np.array([(x.volume - x.volume.iloc[0]) / np.std(x.volume) for x in X_test]).reshape((len(X_test), WINDOW_LONG, 1))\n", "  \n", "    X_train = np.array([x[features].fillna(0.).values.tolist() for x in X_train_b]) \n", "    X_val = np.array([x[features].fillna(0.).values.tolist() for x in X_val])\n", "    X_test = np.array([x[features].fillna(0.).values.tolist() for x in X_test])\n", "\n", "    X_train = np.concatenate((X_train, X_train_normts, X_train_normv), axis = -1)\n", "    X_val = np.concatenate((X_val, X_val_normts, X_val_normvv), axis = -1)\n", "    X_test = np.concatenate((X_test, X_test_normts, X_test_normv), axis = -1)\n", "    \n", "    return X_train, X_val, X_test"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_class_weights(y):\n", "    y = [np.argmax(x) for x in y]\n", "    counter = Counter(y)\n", "    majority = max(counter.values())\n", "    return  {cls: round(float(majority)/float(count), 2) for cls, count in counter.items()}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fixed horizon"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["X, labels = [], []\n", "for i in range(WINDOW_LONG, N_BARS, 1):\n", "    \n", "    window = tick_bars.iloc[i-WINDOW_LONG:i]\n", "#     window = tick_bars.iloc[i]\n", "    now = tick_bars.close[i]\n", "    future = tick_bars.close[i+HORIZON]\n", "    ret = (future - now) / now\n", "    \n", "    X.append(window)\n", "    if ret > T:\n", "        labels.append(1)\n", "    elif ret < -T:\n", "        labels.append(-1)\n", "    else:\n", "        labels.append(0)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi4yLCBo\ndHRwOi8vbWF0cGxvdGxpYi5vcmcvhp/UCwAAE4xJREFUeJzt3W+QXfV93/H3x1LAblOMMColiLHw\nRK2ruBOMFazWnSaGFATtWHSKXTFNkF3FamLcSaftNFA/oLXD1O6DkjJ1nDJGBZzUmJJ6UFNRVebP\nZDoTYUSNwUCxFlwPUjFSEOBmPMYBf/vg/tZzrN+u9u5qd69A79fMnXvO9/zOud97drWfe8859ypV\nhSRJQ2+adAOSpBOP4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqTOykk3sFBnnnlm\nrV27dtJtSNLrxsMPP/zHVbV6nLGv23BYu3Yt+/btm3QbkvS6keTb4471sJIkqWM4SJI6hoMkqWM4\nSJI6hoMkqWM4SJI6hoMkqWM4SJI6hoMkqfO6/YS0NJdttz406RaW1S0f/rlJt6A3EN85SJI6hoMk\nqWM4SJI6Y4VDkv+T5LEkjyTZ12pnJNmTZH+7X9XqSXJTkqkkjya5YLCdrW38/iRbB/X3tO1PtXWz\n2E9UkjS++bxzeH9VnV9VG9r8tcC9VbUOuLfNA1wGrGu37cDnYBQmwPXAe4ELgeunA6WN+ehgvU0L\nfkaSpON2PIeVNgO3tenbgCsG9dtrZC9wepKzgUuBPVV1pKpeBPYAm9qy06pqb1UVcPtgW5KkCRg3\nHAr4H0keTrK91c6qqufa9HeAs9r0OcCzg3UPtNqx6gdmqHeSbE+yL8m+w4cPj9m6JGm+xv2cw1+v\nqoNJ/jywJ8n/Hi6sqkpSi9/ej6uqm4GbATZs2LDkjydJJ6ux3jlU1cF2fwj4MqNzBs+3Q0K0+0Nt\n+EHg3MHqa1rtWPU1M9QlSRMyZzgk+bNJ/tz0NHAJ8A1gJzB9xdFW4O42vRO4ul21tBF4uR1+2g1c\nkmRVOxF9CbC7Lftuko3tKqWrB9uSJE3AOIeVzgK+3K4uXQn8p6r670keAu5Msg34NvChNn4XcDkw\nBXwP+AhAVR1J8ilg+jsNPllVR9r0x4BbgbcA97SbJGlC5gyHqnoG+NkZ6i8AF89QL+CaWba1A9gx\nQ30f8K4x+pUkLQM/IS1J6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO\n4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ\n6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6owdDklWJPlakj9o8+cleTDJVJIv\nJTml1U9t81Nt+drBNq5r9aeSXDqob2q1qSTXLt7TkyQtxHzeOfw68ORg/jPAjVX108CLwLZW3wa8\n2Oo3tnEkWQ9sAX4G2AT8dgucFcBngcuA9cBVbawkaULGCocka4C/BXy+zQe4CLirDbkNuKJNb27z\ntOUXt/GbgTuq6pWq+hYwBVzYblNV9UxV/QC4o42VJE3IuO8cfgv458AP2/zbgJeq6tU2fwA4p02f\nAzwL0Ja/3Mb/qH7UOrPVO0m2J9mXZN/hw4fHbF2SNF9zhkOSvw0cqqqHl6GfY6qqm6tqQ1VtWL16\n9aTbkaQ3rJVjjHkf8IEklwNvBk4D/h1wepKV7d3BGuBgG38QOBc4kGQl8FbghUF92nCd2eqSpAmY\n851DVV1XVWuqai2jE8r3VdXfB+4HrmzDtgJ3t+mdbZ62/L6qqlbf0q5mOg9YB3wVeAhY165+OqU9\nxs5FeXaSpAUZ553DbH4DuCPJbwJfA25p9VuALySZAo4w+mNPVT2e5E7gCeBV4Jqqeg0gyceB3cAK\nYEdVPX4cfUmSjtO8wqGqHgAeaNPPMLrS6Ogx3wc+OMv6NwA3zFDfBeyaTy+SpKXjJ6QlSR3DQZLU\nMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwk\nSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3D\nQZLUMRwkSR3DQZLUMRwkSZ05wyHJm5N8NcnXkzye5F+1+nlJHkwyleRLSU5p9VPb/FRbvnawreta\n/akklw7qm1ptKsm1i/80JUnzMc47h1eAi6rqZ4HzgU1JNgKfAW6sqp8GXgS2tfHbgBdb/cY2jiTr\ngS3AzwCbgN9OsiLJCuCzwGXAeuCqNlaSNCFzhkON/Emb/Yl2K+Ai4K5Wvw24ok1vbvO05RcnSavf\nUVWvVNW3gCngwnabqqpnquoHwB1trCRpQsY659Be4T8CHAL2AE8DL1XVq23IAeCcNn0O8CxAW/4y\n8LZh/ah1ZqvP1Mf2JPuS7Dt8+PA4rUuSFmCscKiq16rqfGANo1f671zSrmbv4+aq2lBVG1avXj2J\nFiTppDCvq5Wq6iXgfuCvAqcnWdkWrQEOtumDwLkAbflbgReG9aPWma0uSZqQca5WWp3k9Db9FuBv\nAk8yCokr27CtwN1temebpy2/r6qq1be0q5nOA9YBXwUeAta1q59OYXTSeudiPDlJ0sKsnHsIZwO3\ntauK3gTcWVV/kOQJ4I4kvwl8Dbiljb8F+EKSKeAIoz/2VNXjSe4EngBeBa6pqtcAknwc2A2sAHZU\n1eOL9gwlSfM2ZzhU1aPAu2eoP8Po/MPR9e8DH5xlWzcAN8xQ3wXsGqNfSdIy8BPSkqSO4SBJ6hgO\nkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO\n4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ6hgOkqSO4SBJ\n6hgOkqSO4SBJ6swZDknOTXJ/kieSPJ7k11v9jCR7kuxv96taPUluSjKV5NEkFwy2tbWN359k66D+\nniSPtXVuSpKleLKSpPGM887hVeCfVtV6YCNwTZL1wLXAvVW1Dri3zQNcBqxrt+3A52AUJsD1wHuB\nC4HrpwOljfnoYL1Nx//UJEkLNWc4VNVzVfW/2vT/A54EzgE2A7e1YbcBV7TpzcDtNbIXOD3J2cCl\nwJ6qOlJVLwJ7gE1t2WlVtbeqCrh9sC1J0gTM65xDkrXAu4EHgbOq6rm26DvAWW36HODZwWoHWu1Y\n9QMz1CVJEzJ2OCT5SeD3gX9cVd8dLmuv+GuRe5uph+1J9iXZd/jw4aV+OEk6aY0VDkl+glEw/F5V\n/ZdWfr4dEqLdH2r1g8C5g9XXtNqx6mtmqHeq6uaq2lBVG1avXj1O65KkBRjnaqUAtwBPVtW/HSza\nCUxfcbQVuHtQv7pdtbQReLkdftoNXJJkVTsRfQmwuy37bpKN7bGuHmxLkjQBK8cY8z7gl4HHkjzS\nav8C+DRwZ5JtwLeBD7Vlu4DLgSnge8BHAKrqSJJPAQ+1cZ+sqiNt+mPArcBbgHvaTZI0IXOGQ1X9\nT2C2zx1cPMP4Aq6ZZVs7gB0z1PcB75qrF0nS8vAT0pKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoY\nDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKk\njuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkjuEgSeoYDpKkzpzh\nkGRHkkNJvjGonZFkT5L97X5VqyfJTUmmkjya5ILBOlvb+P1Jtg7q70nyWFvnpiRZ7CcpSZqfcd45\n3ApsOqp2LXBvVa0D7m3zAJcB69ptO/A5GIUJcD3wXuBC4PrpQGljPjpY7+jHkiQtsznDoar+EDhy\nVHkzcFubvg24YlC/vUb2AqcnORu4FNhTVUeq6kVgD7CpLTutqvZWVQG3D7YlSZqQhZ5zOKuqnmvT\n3wHOatPnAM8Oxh1otWPVD8xQlyRN0HGfkG6v+GsReplTku1J9iXZd/jw4eV4SEk6KS00HJ5vh4Ro\n94da/SBw7mDcmlY7Vn3NDPUZVdXNVbWhqjasXr16ga1Lkuay0HDYCUxfcbQVuHtQv7pdtbQReLkd\nftoNXJJkVTsRfQmwuy37bpKN7SqlqwfbkiRNyMq5BiT5IvALwJlJDjC66ujTwJ1JtgHfBj7Uhu8C\nLgemgO8BHwGoqiNJPgU81MZ9sqqmT3J/jNEVUW8B7mk3SdIEzRkOVXXVLIsunmFsAdfMsp0dwI4Z\n6vuAd83VhyRp+fgJaUlSx3CQJHUMB0lSx3CQJHUMB0lSx3CQJHUMB0lSx3CQJHUMB0lSx3CQJHXm\n/PoMSTpRbbv1obkHvcHc8uGfW5bH8Z2DJKlzUr5z8NWGJB2b7xwkSR3DQZLUMRwkSR3DQZLUMRwk\nSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3DQZLUMRwkSR3D\nQZLUOWHCIcmmJE8lmUpy7aT7kaST2QkRDklWAJ8FLgPWA1clWT/ZriTp5HVChANwITBVVc9U1Q+A\nO4DNE+5Jkk5aJ0o4nAM8O5g/0GqSpAlIVU26B5JcCWyqql9p878MvLeqPn7UuO3A9jb7l4CnFviQ\nZwJ/vMB1l5J9zY99zY99zc8bsa+3V9XqcQauXOADLLaDwLmD+TWt9mOq6mbg5uN9sCT7qmrD8W5n\nsdnX/NjX/NjX/JzsfZ0oh5UeAtYlOS/JKcAWYOeEe5Kkk9YJ8c6hql5N8nFgN7AC2FFVj0+4LUk6\naZ0Q4QBQVbuAXcv0cMd9aGqJ2Nf82Nf82Nf8nNR9nRAnpCVJJ5YT5ZyDJOkE8oYNhyQfTPJ4kh8m\nmfXM/mxf29FOjj/Y6l9qJ8oXo68zkuxJsr/dr5phzPuTPDK4fT/JFW3ZrUm+NVh2/nL11ca9Nnjs\nnYP6JPfX+Un+qP28H03y9wbLFnV/zfU1L0lObc9/qu2PtYNl17X6U0kuPZ4+FtDXP0nyRNs/9yZ5\n+2DZjD/TZerrw0kODx7/VwbLtraf+/4kW5e5rxsHPX0zyUuDZUuyv5LsSHIoyTdmWZ4kN7WeH01y\nwWDZ4u+rqnpD3oC/zOizEA8AG2YZswJ4GngHcArwdWB9W3YnsKVN/w7wa4vU178Brm3T1wKfmWP8\nGcAR4M+0+VuBK5dgf43VF/Ans9Qntr+Avwisa9M/BTwHnL7Y++tYvy+DMR8DfqdNbwG+1KbXt/Gn\nAue17axYxr7eP/gd+rXpvo71M12mvj4M/PsZ1j0DeKbdr2rTq5arr6PG/yNGF8ks9f76G8AFwDdm\nWX45cA8QYCPw4FLuqzfsO4eqerKq5vqQ3Ixf25EkwEXAXW3cbcAVi9Ta5ra9cbd7JXBPVX1vkR5/\nNvPt60cmvb+q6ptVtb9N/1/gEDDWB33maZyveRn2exdwcds/m4E7quqVqvoWMNW2tyx9VdX9g9+h\nvYw+S7TUjudrcS4F9lTVkap6EdgDbJpQX1cBX1ykx55VVf0hoxeCs9kM3F4je4HTk5zNEu2rN2w4\njGm2r+14G/BSVb16VH0xnFVVz7Xp7wBnzTF+C/0v5g3tbeWNSU5d5r7enGRfkr3Th7o4gfZXkgsZ\nvRp8elBerP01zte8/GhM2x8vM9o/S/kVMfPd9jZGr0CnzfQzXc6+/m77+dyVZPrDsCfE/mqH384D\n7huUl2p/zWW2vpdkX50wl7IuRJKvAH9hhkWfqKq7l7ufacfqazhTVZVk1svF2quCv8Lo8x/TrmP0\nR/IURpe0/QbwyWXs6+1VdTDJO4D7kjzG6A/ggi3y/voCsLWqftjKC95fb0RJfgnYAPz8oNz9TKvq\n6Zm3sOj+K/DFqnolyT9k9K7romV67HFsAe6qqtcGtUnur2Xzug6HqvrF49zEbF/b8QKjt2wr26u/\nGb/OYyF9JXk+ydlV9Vz7Y3boGJv6EPDlqvrTwbanX0W/kuQ/Av9sOfuqqoPt/pkkDwDvBn6fCe+v\nJKcB/43RC4O9g20veH/NYJyveZkecyDJSuCtjH6fxvqKmCXsiyS/yChwf76qXpmuz/IzXYw/dnP2\nVVUvDGY/z+gc0/S6v3DUug8sQk9j9TWwBbhmWFjC/TWX2fpekn11sh9WmvFrO2p0lud+Rsf7AbYC\ni/VOZGfb3jjb7Y51tj+Q08f5rwBmvLJhKfpKsmr6sEySM4H3AU9Men+1n92XGR2PveuoZYu5v8b5\nmpdhv1cC97X9sxPYktHVTOcB64CvHkcv8+orybuB/wB8oKoODeoz/kyXsa+zB7MfAJ5s07uBS1p/\nq4BL+PF30EvaV+vtnYxO8P7RoLaU+2suO4Gr21VLG4GX24ufpdlXi3m2/US6AX+H0bG3V4Dngd2t\n/lPArsG4y4FvMkr+Twzq72D0j3cK+M/AqYvU19uAe4H9wFeAM1p9A/D5wbi1jF4RvOmo9e8DHmP0\nR+53gZ9crr6Av9Ye++vtftuJsL+AXwL+FHhkcDt/KfbXTL8vjA5TfaBNv7k9/6m2P94xWPcTbb2n\ngMsW+fd9rr6+0v4dTO+fnXP9TJepr38NPN4e/37gnYN1/0Hbj1PAR5azrzb/L4FPH7Xeku0vRi8E\nn2u/ywcYnRv6VeBX2/Iw+k/Rnm6PvWGw7qLvKz8hLUnqnOyHlSRJMzAcJEkdw0GS1DEcJEkdw0GS\n1DEcJEkdw0GS1DEcJEmd/w8bM79CUvwezgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(labels, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"collapsed": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = labels[:int(len(X) * 0.5)], labels[int(len(X) * 0.6):int(len(X) * 0.7)], labels[int(len(X) * 0.8):]\n", "\n", "lbr = LabelBinarizer()\n", "Y_train = lbr.fit_transform(Y_train)\n", "Y_val = lbr.transform(Y_val)\n", "Y_test = lbr.transform(Y_test)\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "main_input (InputLayer)      (None, 100, 3)            0         \n", "_________________________________________________________________\n", "flatten_1 (Flatten)          (None, 300)               0         \n", "_________________________________________________________________\n", "dropout_1 (Dropout)          (None, 300)               0         \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 3)                 903       \n", "=================================================================\n", "Total params: 903\n", "Trainable params: 903\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "Train on 31950 samples, validate on 6390 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 6s 179us/step - loss: 4.1670 - acc: 0.3157 - val_loss: 1.4236 - val_acc: 0.2360\n", "Epoch 2/100\n", "31950/31950 [==============================] - 6s 179us/step - loss: 4.1343 - acc: 0.3158 - val_loss: 1.9156 - val_acc: 0.1768\n", "Epoch 3/100\n", "31950/31950 [==============================] - 6s 177us/step - loss: 4.1417 - acc: 0.3126 - val_loss: 1.6744 - val_acc: 0.2171\n", "Epoch 4/100\n", "31950/31950 [==============================] - 6s 192us/step - loss: 4.2166 - acc: 0.3118 - val_loss: 1.3438 - val_acc: 0.3152\n", "Epoch 5/100\n", "31950/31950 [==============================] - 6s 189us/step - loss: 4.1442 - acc: 0.3108 - val_loss: 1.6506 - val_acc: 0.3433\n", "Epoch 6/100\n", "31950/31950 [==============================] - 5s 169us/step - loss: 4.1671 - acc: 0.3121 - val_loss: 1.5837 - val_acc: 0.2828\n", "Epoch 7/100\n", "31950/31950 [==============================] - 5s 163us/step - loss: 4.1246 - acc: 0.3140 - val_loss: 1.9486 - val_acc: 0.2042\n", "Epoch 8/100\n", "31950/31950 [==============================] - 5s 164us/step - loss: 4.1799 - acc: 0.3092 - val_loss: 1.5888 - val_acc: 0.2413\n", "Epoch 9/100\n", "31950/31950 [==============================] - 7s 207us/step - loss: 4.1771 - acc: 0.3120 - val_loss: 1.6529 - val_acc: 0.3759\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    output = Dense(3, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input], outputs=[output])\n", "    return final_model\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit(X_train, Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = (X_val, Y_val),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val)))\n", "                )\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.02      0.15      0.04       211\n", "           1       0.92      0.32      0.47      5934\n", "           2       0.04      0.41      0.06       245\n", "\n", "   micro avg       0.32      0.32      0.32      6390\n", "   macro avg       0.32      0.29      0.19      6390\n", "weighted avg       0.85      0.32      0.44      6390\n", "\n", "[[  32   83   96]\n", " [1413 1882 2639]\n", " [  57   88  100]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nEikFgIhIpBQAIiKRUgCIiETq/wEfBfbTBC1JwQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.07      0.22      0.10       899\n", "           1       0.87      0.33      0.47     11116\n", "           2       0.06      0.46      0.11       765\n", "\n", "   micro avg       0.33      0.33      0.33     12780\n", "   macro avg       0.33      0.33      0.23     12780\n", "weighted avg       0.76      0.33      0.43     12780\n", "\n", "[[ 194  283  422]\n", " [**************]\n", " [ 147  263  355]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict(X_val)\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict(X_test)\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.27972084, 0.67968315, 0.04059596],\n", "       [0.49318126, 0.46420133, 0.04261736],\n", "       [0.05589748, 0.77083486, 0.1732677 ],\n", "       ...,\n", "       [0.676218  , 0.16055585, 0.16322614],\n", "       [0.20033889, 0.18110037, 0.61856073],\n", "       [0.05610908, 0.13044934, 0.8134416 ]], dtype=float32)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["pred"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Volatility horizon"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["X, labels = [], []\n", "for i in range(WINDOW_LONG, N_BARS, 1):\n", "    window = tick_bars.iloc[i-WINDOW_LONG:i]\n", "    now = tick_bars.close[i]\n", "    future = tick_bars.close[i+HORIZON]\n", "    ret = (future - now) / now\n", "    \n", "    window_abs_returns = np.abs(window.close.pct_change())\n", "    Ti = np.std(window_abs_returns) + np.mean(window_abs_returns)\n", "    \n", "    X.append(window)\n", "    if ret > Ti:\n", "        labels.append(1)\n", "    elif ret < -Ti:\n", "        labels.append(-1)\n", "    else:\n", "        labels.append(0)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ndQwHSVLn/wMChZuz54nZiQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(labels, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"collapsed": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = labels[:int(len(X) * 0.5)], labels[int(len(X) * 0.6):int(len(X) * 0.7)], labels[int(len(X) * 0.8):]\n", "\n", "lbr = LabelBinarizer()\n", "Y_train = lbr.fit_transform(Y_train)\n", "Y_val = lbr.transform(Y_val)\n", "Y_test = lbr.transform(Y_test)\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "main_input (InputLayer)      (None, 100, 3)            0         \n", "_________________________________________________________________\n", "flatten_2 (<PERSON>ten)          (None, 300)               0         \n", "_________________________________________________________________\n", "dropout_2 (Dropout)          (None, 300)               0         \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 3)                 903       \n", "=================================================================\n", "Total params: 903\n", "Trainable params: 903\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "Train on 31950 samples, validate on 6390 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 6s 191us/step - loss: 1.9171 - acc: 0.3330 - val_loss: 1.4332 - val_acc: 0.3546\n", "Epoch 2/100\n", "31950/31950 [==============================] - 9s 281us/step - loss: 1.9045 - acc: 0.3363 - val_loss: 1.6701 - val_acc: 0.3302\n", "Epoch 3/100\n", "31950/31950 [==============================] - 7s 209us/step - loss: 1.8894 - acc: 0.3382 - val_loss: 1.3361 - val_acc: 0.3286\n", "Epoch 4/100\n", "31950/31950 [==============================] - 6s 187us/step - loss: 1.9157 - acc: 0.3359 - val_loss: 1.3701 - val_acc: 0.3094\n", "Epoch 5/100\n", "31950/31950 [==============================] - 7s 207us/step - loss: 1.9298 - acc: 0.3301 - val_loss: 1.5820 - val_acc: 0.3286\n", "Epoch 6/100\n", "31950/31950 [==============================] - 7s 205us/step - loss: 1.9317 - acc: 0.3339 - val_loss: 1.2592 - val_acc: 0.3457\n", "Epoch 7/100\n", "31950/31950 [==============================] - 7s 221us/step - loss: 1.8741 - acc: 0.3351 - val_loss: 1.4276 - val_acc: 0.3285\n", "Epoch 8/100\n", "31950/31950 [==============================] - 8s 243us/step - loss: 1.8851 - acc: 0.3326 - val_loss: 1.3108 - val_acc: 0.3088\n", "Epoch 9/100\n", "31950/31950 [==============================] - 6s 199us/step - loss: 1.9460 - acc: 0.3276 - val_loss: 1.4281 - val_acc: 0.3498\n", "Epoch 10/100\n", "31950/31950 [==============================] - 7s 234us/step - loss: 1.9420 - acc: 0.3289 - val_loss: 1.3733 - val_acc: 0.3743\n", "Epoch 11/100\n", "31950/31950 [==============================] - 7s 219us/step - loss: 1.9033 - acc: 0.3394 - val_loss: 1.3412 - val_acc: 0.3023\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    output = Dense(3, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input], outputs=[output])\n", "    return final_model\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit(X_train, Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = (X_val, Y_val),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val)))\n", "                )\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.33      0.29      0.31      2095\n", "           1       0.24      0.31      0.27      1518\n", "           2       0.44      0.41      0.42      2777\n", "\n", "   micro avg       0.35      0.35      0.35      6390\n", "   macro avg       0.34      0.34      0.33      6390\n", "weighted avg       0.36      0.35      0.35      6390\n", "\n", "[[ 603  680  812]\n", " [ 442  478  598]\n", " [ 810  839 1128]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi4yLCBo\ndHRwOi8vbWF0cGxvdGxpYi5vcmcvhp/UCwAAGbtJREFUeJzt3X9wVPW9//Hn2xCMAqMI0fIVFOpg\nEYmENFpSpNWLiMAoZa5WaIuoWLxFrtav7Qi2c3H8McXxV6VaHO41FUcrcqsoOlhBTYe5Ir/ChN8i\nAVFDlV8qhQoo8L5/7Ce5S0jIJtnsEj+vx8zOnv2czznnvSebfWXP5+yJuT<PERSON><PERSON>hKfE7JdgIiIZIcC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\nidT/AqBXHJW5Jpc+AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.37      0.27      0.31      4922\n", "           1       0.24      0.33      0.28      2896\n", "           2       0.39      0.41      0.40      4962\n", "\n", "   micro avg       0.34      0.34      0.34     12780\n", "   macro avg       0.33      0.34      0.33     12780\n", "weighted avg       0.35      0.34      0.34     12780\n", "\n", "[[1317 1577 2028]\n", " [ 814  961 1121]\n", " [1443 1504 2015]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict(X_val)\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict(X_test)\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0: 1.23, 1: 1.82, 2: 1.0}\n", "[-1  0  1]\n"]}], "source": ["print get_class_weights(np.concatenate((Y_train, Y_val)))\n", "print lbr.classes_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Meta labeling"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_meta_barier(future_window, last_close, min_ret, tp, sl, vertical_zero = False):\n", "    '''\n", "        XXX\n", "    '''\n", "    if vertical_zero:\n", "        min_ret_situation = [0, 0, 0]\n", "    else:\n", "        min_ret_situation = [0, 0]\n", "        \n", "        \n", "    differences = np.array([(fc - last_close) / last_close for fc in future_window])\n", "    \n", "    # Are there gonna be fluctuations within min_ret???\n", "    min_ret_ups = np.where((differences >= min_ret) == True)[0]\n", "    min_ret_downs = np.where((differences < -min_ret) == True)[0]\n", "  \n", "    if (len(min_ret_ups) == 0) and (len(min_ret_downs) == 0):\n", "        if vertical_zero:\n", "            min_ret_situation[2] = 1\n", "        else:\n", "            if differences[-1] > 0:\n", "                min_ret_situation[0] = 1\n", "            else:\n", "                min_ret_situation[1] = 1            \n", "    else:\n", "        if len(min_ret_ups) == 0: min_ret_ups = [np.inf]\n", "        if len(min_ret_downs) == 0: min_ret_downs = [np.inf]\n", "        if min_ret_ups[0] > min_ret_downs[0]:\n", "            min_ret_situation[0] = 1\n", "        else:\n", "            min_ret_situation[1] = 1\n", "        \n", "    #  Take profit and stop losses indices\n", "    take_profit = np.where((differences >= tp) == True)[0]\n", "    stop_loss = np.where((differences < sl) == True)[0]\n", "    \n", "    # Fluctuation directions coincide with take profit / stop loss actions?\n", "    if min_ret_situation[0] == 1 and len(take_profit) != 0:\n", "        take_action = 1\n", "    elif min_ret_situation[1] == 1 and len(stop_loss) != 0:\n", "        take_action = 1\n", "    else:\n", "        take_action = 0.\n", "    \n", "    return min_ret_situation, take_action"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"collapsed": true}, "outputs": [], "source": ["TP = T\n", "SL = -T\n", "\n", "X, Y, Y2 = [], [], []\n", "for i in range(WINDOW_LONG, N_BARS, 1):\n", "    window = tick_bars.iloc[i-WINDOW_LONG:i]\n", "    now = tick_bars.close[i]\n", "    future_window = tick_bars.close[i:i+HORIZON]\n", "    \n", "    window_abs_returns = np.abs(window.close.pct_change())\n", "    Ti = np.std(window_abs_returns) + np.mean(window_abs_returns)\n", "    \n", "    min_ret_situation, take_action = get_meta_barier(future_window, now, Ti, TP, SL, True)\n", "    X.append(window)\n", "    Y.append(min_ret_situation)\n", "    Y2.append(take_action)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist([np.argmax(x) for x in Y], alpha = 0.5, bins = 5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = Y[:int(len(X) * 0.5)], Y[int(len(X) * 0.6):int(len(X) * 0.7)], Y[int(len(X) * 0.8):]\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"collapsed": true}, "outputs": [], "source": ["Y_train, Y_val, Y_test = np.array(Y_train), np.array(Y_val), np.array(Y_test)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "main_input (InputLayer)      (None, 100, 3)            0         \n", "_________________________________________________________________\n", "flatten_3 (<PERSON>ten)          (None, 300)               0         \n", "_________________________________________________________________\n", "dropout_3 (Dropout)          (None, 300)               0         \n", "_________________________________________________________________\n", "dense_3 (<PERSON><PERSON>)              (None, 3)                 903       \n", "=================================================================\n", "Total params: 903\n", "Trainable params: 903\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "Train on 31950 samples, validate on 6390 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 6s 203us/step - loss: 3.2392 - acc: 0.3451 - val_loss: 2.1423 - val_acc: 0.3293\n", "Epoch 2/100\n", "31950/31950 [==============================] - 6s 196us/step - loss: 3.3246 - acc: 0.3436 - val_loss: 1.1839 - val_acc: 0.3764\n", "Epoch 3/100\n", "31950/31950 [==============================] - 6s 196us/step - loss: 3.2117 - acc: 0.3480 - val_loss: 1.4006 - val_acc: 0.3388\n", "Epoch 4/100\n", "31950/31950 [==============================] - 8s 249us/step - loss: 3.5023 - acc: 0.3459 - val_loss: 1.4190 - val_acc: 0.3488\n", "Epoch 5/100\n", "31950/31950 [==============================] - 6s 174us/step - loss: 3.3483 - acc: 0.3546 - val_loss: 1.2302 - val_acc: 0.3831\n", "Epoch 6/100\n", "31950/31950 [==============================] - 6s 179us/step - loss: 3.1861 - acc: 0.3443 - val_loss: 1.7920 - val_acc: 0.3286\n", "Epoch 7/100\n", "31950/31950 [==============================] - 6s 176us/step - loss: 3.2031 - acc: 0.3422 - val_loss: 4.6242 - val_acc: 0.2419\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    output = Dense(3, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input], outputs=[output])\n", "    return final_model\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit(X_train, Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = (X_val, Y_val),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val)))\n", "                )\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.44      0.39      0.41      2865\n", "           1       0.52      0.37      0.43      3307\n", "           2       0.04      0.25      0.06       218\n", "\n", "   micro avg       0.38      0.38      0.38      6390\n", "   macro avg       0.33      0.34      0.30      6390\n", "weighted avg       0.47      0.38      0.41      6390\n", "\n", "[[1130 1066  669]\n", " [1362 1221  724]\n", " [ 101   63   54]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n5QAwM8soB4CZWUb9fxGinjHgDM0PAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.49      0.41      0.45      6225\n", "           1       0.49      0.38      0.43      6189\n", "           2       0.03      0.23      0.05       366\n", "\n", "   micro avg       0.39      0.39      0.39     12780\n", "   macro avg       0.34      0.34      0.31     12780\n", "weighted avg       0.48      0.39      0.43     12780\n", "\n", "[[**************]\n", " [**************]\n", " [ 142  138   86]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nICISKQWAiEikFAAiIpFSAIiIROr/A+148tVul92oAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict(X_val)\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict(X_test)\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = Y2[:int(len(X) * 0.5)], Y2[int(len(X) * 0.6):int(len(X) * 0.7)], Y2[int(len(X) * 0.8):]\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)\n", "P_train, P_val, P_test = model.predict(X_train), model.predict(X_val), model.predict(X_test)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true}, "outputs": [], "source": ["Y_train = np.array([[1, 0] if x == 1 else [0, 1] for x in Y_train])\n", "Y_val = np.array([[1, 0] if x == 1 else [0, 1] for x in Y_val])\n", "Y_test = np.array([[1, 0] if x == 1 else [0, 1] for x in Y_test])"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["__________________________________________________________________________________________________\n", "Layer (type)                    Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", "main_input (InputLayer)         (None, 100, 3)       0                                            \n", "__________________________________________________________________________________________________\n", "flatten_4 (<PERSON><PERSON>)             (None, 300)          0           main_input[0][0]                 \n", "__________________________________________________________________________________________________\n", "dropout_4 (Dropout)             (None, 300)          0           flatten_4[0][0]                  \n", "__________________________________________________________________________________________________\n", "meta (InputLayer)               (None, 3)            0                                            \n", "__________________________________________________________________________________________________\n", "concatenate_1 (Concatenate)     (None, 303)          0           dropout_4[0][0]                  \n", "                                                                 meta[0][0]                       \n", "__________________________________________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)                 (None, 2)            608         concatenate_1[0][0]              \n", "==================================================================================================\n", "Total params: 608\n", "Trainable params: 608\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n", "Train on 31950 samples, validate on 12780 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 6s 198us/step - loss: 2.9098 - acc: 0.4804 - val_loss: 1.0998 - val_acc: 0.3777\n", "Epoch 2/100\n", "31950/31950 [==============================] - 5s 167us/step - loss: 2.8330 - acc: 0.4821 - val_loss: 1.3706 - val_acc: 0.3956\n", "Epoch 3/100\n", "31950/31950 [==============================] - 5s 156us/step - loss: 2.8462 - acc: 0.4787 - val_loss: 1.3436 - val_acc: 0.3833\n", "Epoch 4/100\n", "31950/31950 [==============================] - 5s 156us/step - loss: 2.9461 - acc: 0.4768 - val_loss: 1.5707 - val_acc: 0.4860\n", "Epoch 5/100\n", "31950/31950 [==============================] - 5s 157us/step - loss: 2.7058 - acc: 0.4846 - val_loss: 1.6371 - val_acc: 0.5117\n", "Epoch 6/100\n", "31950/31950 [==============================] - 5s 156us/step - loss: 2.9673 - acc: 0.4821 - val_loss: 1.9138 - val_acc: 0.3987\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    aux_input = Input((3, ), name='meta')\n", "    \n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    x = concatenate([x, aux_input])\n", "    output = Dense(2, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input, aux_input], outputs=[output])\n", "    return final_model\n", "\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit([X_train, P_train], Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = ([X_test, P_test], Y_test),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val))))\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.02      0.59      0.03       120\n", "           1       0.98      0.38      0.55      6270\n", "\n", "   micro avg       0.38      0.38      0.38      6390\n", "   macro avg       0.50      0.49      0.29      6390\n", "weighted avg       0.96      0.38      0.54      6390\n", "\n", "[[  71   49]\n", " [3897 2373]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAIiIREoBICISqf8PWd51e5ntBIEAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.03      0.62      0.05       360\n", "           1       0.97      0.37      0.54     12420\n", "\n", "   micro avg       0.38      0.38      0.38     12780\n", "   macro avg       0.50      0.50      0.30     12780\n", "weighted avg       0.94      0.38      0.52     12780\n", "\n", "[[ 225  135]\n", " [7818 4602]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict([X_val, P_val])\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict([X_test, P_test])\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAD8CAYAAAB5Pm/hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n0UtS50z0ktQ5E70kdc5EL0md+7+d99fv5PVnWgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(pred, bins = 50)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 14.8, 1: 1.0}"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["get_class_weights(np.concatenate((Y_train, Y_val)))"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.14"}}, "nbformat": 4, "nbformat_minor": 2}