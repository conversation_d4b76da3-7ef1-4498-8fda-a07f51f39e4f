"""
Advanced Candlestick LLM Model

高级版K线预测模型，融合多种先进功能
"""

import math
import inspect
import numpy as np
from dataclasses import dataclass
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Tuple, Optional, Union, Any
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM, LayerNorm, Block
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer

class MultiScaleTimeEmbedding(nn.Module):
    """多尺度时间特征嵌入"""
    def __init__(self, d_model):
        super().__init__()
        self.day_embed = nn.Linear(5, d_model // 4)  # 日内特征
        self.week_embed = nn.Linear(2, d_model // 4)  # 周特征
        self.month_embed = nn.Linear(2, d_model // 4)  # 月特征
        self.season_embed = nn.Linear(2, d_model // 4)  # 季节特征
        self.proj = nn.Linear(d_model, d_model)

    def forward(self, dt):
        """
        处理单个datetime对象

        Args:
            dt: datetime对象
        """
        # 提取各种时间尺度特征
        day_feats = torch.tensor([
            dt.hour / 23.0, dt.minute / 59.0,
            dt.second / 59.0, dt.microsecond / 1e6,
            (dt.hour * 60 + dt.minute) / (24 * 60)  # 日内进度
        ], dtype=torch.float32)

        week_feats = torch.tensor([
            dt.weekday() / 6.0,  # 星期几
            float(dt.weekday() < 5)  # 是否工作日
        ], dtype=torch.float32)

        month_feats = torch.tensor([
            (dt.day - 1) / 30.0,  # 月内天数
            float(dt.day <= 15)  # 上半月/下半月
        ], dtype=torch.float32)

        season_feats = torch.tensor([
            ((dt.month - 1) % 3) / 2.0,  # 季度内月份
            ((dt.month - 1) // 3) / 3.0  # 季度
        ], dtype=torch.float32)

        # 嵌入并组合
        day_emb = self.day_embed(day_feats)
        week_emb = self.week_embed(week_feats)
        month_emb = self.month_embed(month_feats)
        season_emb = self.season_embed(season_feats)

        combined = torch.cat([day_emb, week_emb, month_emb, season_emb], dim=-1)
        return self.proj(combined)

    def batch_forward(self, datetime_batch):
        """
        处理一批datetime对象

        Args:
            datetime_batch: 包含datetime对象的列表或数组 [batch_size, seq_len]
        """
        batch_size = len(datetime_batch)
        seq_len = len(datetime_batch[0]) if isinstance(datetime_batch[0], (list, tuple)) else 1

        # 初始化输出张量
        output = torch.zeros(batch_size, seq_len, self.proj.out_features)

        # 处理每个样本
        for i in range(batch_size):
            if seq_len > 1:
                for j in range(seq_len):
                    output[i, j] = self.forward(datetime_batch[i][j])
            else:
                output[i, 0] = self.forward(datetime_batch[i])

        return output

class CrossSecurityAttention(nn.Module):
    """跨证券注意力层"""
    def __init__(self, d_model, n_head):
        super().__init__()
        self.mha = nn.MultiheadAttention(d_model, n_head)

    def forward(self, x_batch):
        """
        x_batch: [batch_size, seq_len, d_model]
        每个样本代表一个证券的序列
        """
        # 转置为[seq_len, batch_size, d_model]
        x_t = x_batch.transpose(0, 1)

        # 应用跨证券注意力
        attn_output, _ = self.mha(x_t, x_t, x_t)

        # 转回原始形状
        return attn_output.transpose(0, 1)

class MixtureOfExperts(nn.Module):
    """混合专家模型"""
    def __init__(self, d_model, n_experts=4):
        super().__init__()
        self.gate = nn.Linear(d_model, n_experts)
        self.experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model * 4),
                nn.GELU(),
                nn.Linear(d_model * 4, d_model)
            ) for _ in range(n_experts)
        ])

    def forward(self, x):
        # 计算门控权重
        gate_logits = self.gate(x)  # [batch_size, seq_len, n_experts]
        gate_weights = F.softmax(gate_logits, dim=-1)

        # 应用每个专家
        expert_outputs = []
        for i, expert in enumerate(self.experts):
            expert_out = expert(x)
            # 加权专家输出
            weighted_out = expert_out * gate_weights[:, :, i].unsqueeze(-1)
            expert_outputs.append(weighted_out)

        # 组合所有专家的输出
        combined = sum(expert_outputs)
        return combined


class MultiTimeframeBlock(nn.Module):
    """多时间框架处理块"""
    def __init__(self, d_model, n_head, dropout=0.1):
        super().__init__()
        self.norm = LayerNorm(d_model)
        self.attention = nn.MultiheadAttention(d_model, n_head, dropout=dropout)
        self.mlp = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.GELU(),
            nn.Linear(d_model * 4, d_model),
            nn.Dropout(dropout)
        )

    def forward(self, x_dict):
        """
        处理多个时间框架的特征

        Args:
            x_dict: 字典，键为时间框架名称，值为对应的特征 [batch_size, seq_len, d_model]
        """
        # 合并所有时间框架的特征
        combined_features = []
        for tf, features in x_dict.items():
            combined_features.append(features)

        # 拼接特征 [batch_size, total_seq_len, d_model]
        if combined_features:
            x = torch.cat(combined_features, dim=1)

            # 应用自注意力
            x_norm = self.norm(x)
            x_t = x_norm.transpose(0, 1)  # [total_seq_len, batch_size, d_model]
            attn_output, _ = self.attention(x_t, x_t, x_t)
            attn_output = attn_output.transpose(0, 1)  # [batch_size, total_seq_len, d_model]

            # 残差连接
            x = x + attn_output

            # MLP
            x = x + self.mlp(self.norm(x))

            # 分割回各个时间框架
            outputs = {}
            start_idx = 0
            for tf, features in x_dict.items():
                seq_len = features.size(1)
                outputs[tf] = x[:, start_idx:start_idx+seq_len, :]
                start_idx += seq_len

            return outputs
        else:
            return x_dict


class AdvancedCandlestickLLM(CandlestickLLM):
    """高级版K线预测模型"""
    def __init__(self,
                 vocab_size: int,
                 code_size: int,
                 block_size: int = 30,
                 n_layer: int = 12,
                 n_head: int = 12,
                 d_model: int = 768,
                 dropout: float = 0.1,
                 bias: bool = True,
                 use_time_features: bool = True,
                 n_time_features: int = 5,
                 use_multi_timeframe: bool = False,
                 timeframes: List[str] = None,
                 use_mixture_of_experts: bool = False,
                 n_experts: int = 4,
                 use_cross_security: bool = False,
                 use_multi_task: bool = False,
                 n_tasks: int = 3):
        """
        初始化高级版K线LLM模型

        Args:
            vocab_size: 词汇表大小
            code_size: 证券代码数量
            block_size: 最大序列长度
            n_layer: Transformer层数
            n_head: 注意力头数
            d_model: 模型维度
            dropout: Dropout比例
            bias: 是否使用偏置
            use_time_features: 是否使用时间特征
            n_time_features: 时间特征数量
            use_multi_timeframe: 是否使用多时间框架
            timeframes: 时间框架列表，如['1m', '5m', '15m', '1h', '1d']
            use_mixture_of_experts: 是否使用混合专家模型
            n_experts: 专家数量
            use_cross_security: 是否使用跨证券注意力
            use_multi_task: 是否使用多任务学习
            n_tasks: 任务数量
        """
        super().__init__(
            vocab_size=vocab_size,
            code_size=code_size,
            block_size=block_size,
            n_layer=n_layer,
            n_head=n_head,
            d_model=d_model,
            dropout=dropout,
            bias=bias,
            use_time_features=use_time_features,
            n_time_features=n_time_features
        )

        # 保存配置
        self.use_multi_timeframe = use_multi_timeframe
        self.timeframes = timeframes or ['1m', '5m', '15m', '1h', '1d']
        self.use_mixture_of_experts = use_mixture_of_experts
        self.n_experts = n_experts
        self.use_cross_security = use_cross_security
        self.use_multi_task = use_multi_task
        self.n_tasks = n_tasks

        # 多时间框架处理
        if use_multi_timeframe:
            self.timeframe_blocks = nn.ModuleList([
                MultiTimeframeBlock(d_model, n_head, dropout)
                for _ in range(2)  # 使用2个多时间框架块
            ])

            # 时间框架融合层
            self.timeframe_fusion = nn.Linear(d_model * len(self.timeframes), d_model)

        # 混合专家模型
        if use_mixture_of_experts:
            # 替换部分Transformer块为混合专家模型
            moe_indices = [n_layer // 3, 2 * n_layer // 3]  # 在1/3和2/3处使用MoE
            for idx in moe_indices:
                self.blocks[idx] = MixtureOfExperts(d_model, n_experts)

        # 跨证券注意力
        if use_cross_security:
            self.cross_security_attn = CrossSecurityAttention(d_model, n_head)

        # 多任务学习
        if use_multi_task:
            # 额外的预测头
            self.direction_head = nn.Linear(d_model, 3)  # 上涨/下跌/持平
            self.volatility_head = nn.Linear(d_model, 5)  # 波动性分类
            self.volume_head = nn.Linear(d_model, 5)  # 交易量分类

        # 高级时间特征嵌入
        if use_time_features:
            self.advanced_time_embedding = MultiScaleTimeEmbedding(d_model)

        # 重新计算参数数量
        print(f"高级模型参数数量: {self.get_num_params()/1e6:.2f}M")

    def get_model_info(self):
        """获取模型信息，覆盖基类方法"""
        # 获取基类信息
        info = super().get_model_info()

        # 添加高级模型特有的信息
        info.update({
            'use_multi_timeframe': self.use_multi_timeframe,
            'timeframes': self.timeframes if self.use_multi_timeframe else None,
            'use_mixture_of_experts': self.use_mixture_of_experts,
            'n_experts': self.n_experts if self.use_mixture_of_experts else None,
            'use_cross_security': self.use_cross_security,
            'use_multi_task': self.use_multi_task,
            'n_tasks': self.n_tasks if self.use_multi_task else None
        })

        return info

    def forward_with_multi_timeframe(self, input_tokens_dict, code_ids, time_features_dict=None):
        """
        使用多时间框架前向传播

        Args:
            input_tokens_dict: 字典，键为时间框架名称，值为对应的token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features_dict: 字典，键为时间框架名称，值为对应的时间特征 [batch_size, seq_len, n_time_features]

        Returns:
            features_dict: 字典，键为时间框架名称，值为对应的特征 [batch_size, seq_len, d_model]
        """
        features_dict = {}

        # 处理每个时间框架
        for tf, tokens in input_tokens_dict.items():
            # 获取token嵌入
            token_emb = self.token_embedding(tokens)  # [batch_size, seq_len, d_model]

            # 扩展code_ids并获取嵌入
            seq_len = tokens.size(1)
            code_ids_expanded = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
            code_emb = self.code_embedding(code_ids_expanded)  # [batch_size, seq_len, d_model]

            # 位置编码
            pos_emb = self.position_encoding(token_emb)  # [1, seq_len, d_model]

            # 组合嵌入
            x = token_emb + code_emb + pos_emb

            # 添加时间特征
            if self.use_time_features and time_features_dict is not None and tf in time_features_dict:
                time_emb = self.time_embedding(time_features_dict[tf])
                x = x + time_emb

            x = self.dropout(x)

            # 保存特征
            features_dict[tf] = x

        # 应用多时间框架处理块
        for block in self.timeframe_blocks:
            features_dict = block(features_dict)

        return features_dict

    def forward_with_attention(self, input_tokens, code_ids, time_features=None):
        """
        前向传播并返回注意力权重

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size]
            attention_weights: 注意力权重列表 [n_layer, batch_size, n_head, seq_len, seq_len]
        """
        batch_size, seq_len = input_tokens.size()
        assert seq_len <= self.block_size, f"输入序列长度{seq_len}超过了最大长度{self.block_size}"

        # 获取各种嵌入
        token_emb = self.token_embedding(input_tokens)  # [batch_size, seq_len, d_model]

        # 扩展code_ids并获取嵌入
        code_ids = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
        code_emb = self.code_embedding(code_ids)  # [batch_size, seq_len, d_model]

        # 位置编码
        pos_emb = self.position_encoding(token_emb)  # [1, seq_len, d_model]

        # 组合嵌入
        x = token_emb + code_emb + pos_emb

        # 添加时间特征
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + time_emb

        x = self.dropout(x)

        # 收集注意力权重
        attention_weights = []

        # 通过Transformer块
        for i, block in enumerate(self.blocks):
            if isinstance(block, Block):  # 只收集标准Transformer块的注意力权重
                # 这里需要修改Block类以返回注意力权重
                # 简化起见，我们假设已经修改了Block类
                x, attn = block(x, return_attention=True)
                attention_weights.append(attn)
            else:
                x = block(x)

        x = self.ln_f(x)

        # 计算logits
        logits = self.lm_head(x)  # [batch_size, seq_len, vocab_size]

        return logits, attention_weights

    def forward_with_embeddings(self, embeddings, code_ids, time_features=None, targets=None):
        """
        使用预计算的嵌入进行前向传播

        Args:
            embeddings: 预计算的token嵌入 [batch_size, seq_len, d_model]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            targets: 目标token序列 [batch_size, seq_len]

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size]
            loss: 损失值（如果提供了targets）
        """
        batch_size, seq_len, _ = embeddings.size()
        assert seq_len <= self.block_size, f"输入序列长度{seq_len}超过了最大长度{self.block_size}"

        # 扩展code_ids并获取嵌入
        code_ids = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
        code_emb = self.code_embedding(code_ids)  # [batch_size, seq_len, d_model]

        # 位置编码
        pos_emb = self.position_encoding(embeddings)  # [1, seq_len, d_model]

        # 组合嵌入
        x = embeddings + code_emb + pos_emb

        # 添加时间特征
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + time_emb

        x = self.dropout(x)

        # 通过Transformer块
        for block in self.blocks:
            x = block(x)

        x = self.ln_f(x)

        # 计算logits
        logits = self.lm_head(x)  # [batch_size, seq_len, vocab_size]

        # 计算损失
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)

        return logits, loss

    def get_features(self, input_tokens, code_ids, time_features=None):
        """
        获取特征表示

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]

        Returns:
            features: 特征表示 [batch_size, seq_len, d_model]
        """
        batch_size, seq_len = input_tokens.size()
        assert seq_len <= self.block_size, f"输入序列长度{seq_len}超过了最大长度{self.block_size}"

        # 获取各种嵌入
        token_emb = self.token_embedding(input_tokens)  # [batch_size, seq_len, d_model]

        # 扩展code_ids并获取嵌入
        code_ids = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
        code_emb = self.code_embedding(code_ids)  # [batch_size, seq_len, d_model]

        # 位置编码
        pos_emb = self.position_encoding(token_emb)  # [1, seq_len, d_model]

        # 组合嵌入
        x = token_emb + code_emb + pos_emb

        # 添加时间特征
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + time_emb

        x = self.dropout(x)

        # 通过Transformer块
        for block in self.blocks:
            x = block(x)

        x = self.ln_f(x)

        return x

    def forward(self, input_tokens, code_ids, time_features=None, targets=None,
               direction_targets=None, volatility_targets=None, volume_targets=None):
        """
        前向传播

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len] 或 字典 {timeframe: tokens}
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features] 或 字典 {timeframe: features}
            targets: 目标token序列 [batch_size, seq_len]
            direction_targets: 方向预测目标 [batch_size]
            volatility_targets: 波动性预测目标 [batch_size]
            volume_targets: 交易量预测目标 [batch_size]

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size] 或 字典
            loss: 损失值
        """
        # 处理多时间框架输入
        if self.use_multi_timeframe and isinstance(input_tokens, dict):
            features_dict = self.forward_with_multi_timeframe(input_tokens, code_ids, time_features)

            # 融合不同时间框架的特征
            batch_size = next(iter(features_dict.values())).size(0)
            fused_features = []

            for tf in self.timeframes:
                if tf in features_dict:
                    # 获取每个时间框架的最后一个时间步特征
                    tf_features = features_dict[tf]
                    last_features = tf_features[:, -1, :]  # [batch_size, d_model]
                    fused_features.append(last_features)

            # 拼接所有时间框架的特征
            if fused_features:
                fused_features = torch.cat(fused_features, dim=1)  # [batch_size, n_timeframes * d_model]
                fused_features = self.timeframe_fusion(fused_features)  # [batch_size, d_model]

                # 扩展为序列
                fused_features = fused_features.unsqueeze(1)  # [batch_size, 1, d_model]

                # 计算logits
                logits = self.lm_head(fused_features)  # [batch_size, 1, vocab_size]

                # 计算损失
                loss = None
                if targets is not None:
                    loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)

                # 多任务学习
                if self.use_multi_task:
                    # 获取最后一个时间步的特征
                    last_features = fused_features[:, -1, :]

                    # 方向预测
                    direction_logits = self.direction_head(last_features)
                    direction_loss = None
                    if direction_targets is not None:
                        direction_loss = F.cross_entropy(direction_logits, direction_targets)

                    # 波动性预测
                    volatility_logits = self.volatility_head(last_features)
                    volatility_loss = None
                    if volatility_targets is not None:
                        volatility_loss = F.cross_entropy(volatility_logits, volatility_targets)

                    # 交易量预测
                    volume_logits = self.volume_head(last_features)
                    volume_loss = None
                    if volume_targets is not None:
                        volume_loss = F.cross_entropy(volume_logits, volume_targets)

                    # 组合损失
                    total_loss = loss if loss is not None else 0
                    if direction_loss is not None:
                        total_loss = total_loss + 0.2 * direction_loss
                    if volatility_loss is not None:
                        total_loss = total_loss + 0.2 * volatility_loss
                    if volume_loss is not None:
                        total_loss = total_loss + 0.2 * volume_loss

                    return {
                        'token_logits': logits,
                        'direction_logits': direction_logits,
                        'volatility_logits': volatility_logits,
                        'volume_logits': volume_logits
                    }, total_loss

                return logits, loss

        # 标准前向传播 - 我们需要获取特征x，而不仅仅是logits
        batch_size, seq_len = input_tokens.size()

        # 获取各种嵌入
        token_emb = self.token_embedding(input_tokens)  # [batch_size, seq_len, d_model]

        # 扩展code_ids并获取嵌入
        code_ids_expanded = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
        code_emb = self.code_embedding(code_ids_expanded)  # [batch_size, seq_len, d_model]

        # 位置编码
        pos_emb = self.position_encoding(token_emb)  # [1, seq_len, d_model]

        # 组合嵌入
        x = token_emb + code_emb + pos_emb

        # 添加时间特征
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + time_emb

        x = self.dropout(x)

        # 通过Transformer块
        for block in self.blocks:
            x = block(x)

        # 应用层归一化
        x = self.ln_f(x)

        # 计算logits
        logits = self.lm_head(x)  # [batch_size, seq_len, vocab_size]

        # 计算损失
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)

        # 跨证券注意力
        if self.use_cross_security and input_tokens.size(0) > 1:
            # 应用跨证券注意力
            cross_features = self.cross_security_attn(x)  # 使用x而不是logits
            x = x + cross_features

            # 重新计算logits
            logits = self.lm_head(x)

            # 重新计算损失
            if targets is not None:
                loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)

        # 多任务学习
        if self.use_multi_task:
            # 获取最后一个时间步的特征
            last_features = x[:, -1, :]  # [batch_size, d_model]

            # 方向预测
            direction_logits = self.direction_head(last_features)
            direction_loss = None
            if direction_targets is not None:
                direction_loss = F.cross_entropy(direction_logits, direction_targets)

            # 波动性预测
            volatility_logits = self.volatility_head(last_features)
            volatility_loss = None
            if volatility_targets is not None:
                volatility_loss = F.cross_entropy(volatility_logits, volatility_targets)

            # 交易量预测
            volume_logits = self.volume_head(last_features)
            volume_loss = None
            if volume_targets is not None:
                volume_loss = F.cross_entropy(volume_logits, volume_targets)

            # 组合损失
            total_loss = loss
            if direction_loss is not None:
                total_loss = total_loss + 0.2 * direction_loss
            if volatility_loss is not None:
                total_loss = total_loss + 0.2 * volatility_loss
            if volume_loss is not None:
                total_loss = total_loss + 0.2 * volume_loss

            return {
                'token_logits': logits,
                'direction_logits': direction_logits,
                'volatility_logits': volatility_logits,
                'volume_logits': volume_logits
            }, total_loss

        return logits, loss
