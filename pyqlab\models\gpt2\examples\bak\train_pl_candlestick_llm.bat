@echo off
e:
cd e:\lab\RoboQuant\pylab

python .\pyqlab\models\llm\examples\train_pl_candlestick_llm.py ^
--data_path f:/hqdata/fut_sf_min5.parquet ^
--start_date 2024-09-01 ^
--end_date 2025-01-30 ^
--seq_len 30 ^
--pred_len 1 ^
--stride 1 ^
--batch_size 32 ^
--tokenizer_type linear ^
--nonlinear_mapping logarithmic ^
--n_layer 6 ^
--n_head 8 ^
--d_model 96 ^
--dropout 0.1 ^
--use_time_features ^
--learning_rate 3e-4 ^
--weight_decay 0.01 ^
--max_epochs 3 ^
--lr_scheduler one_cycle ^
--warmup_ratio 0.1 ^
--grad_clip 1.0 ^
--early_stopping 20 ^
--checkpoint_dir e:/lab/RoboQuant/pylab/checkpoints ^
--log_dir e:/lab/RoboQuant/pylab/logs ^
--precision 32-true ^
--accelerator cpu ^
--use_rich_progress_bar ^
--use_model_summary ^
--use_csv_logger ^
--log_every_n_steps 5 ^
--min_delta 0.0001 ^
--accumulate_grad_batches 0 ^
--market fut ^
--block_name sf ^
--period min5 ^
--num_workers 0
