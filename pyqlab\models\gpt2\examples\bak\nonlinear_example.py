"""
非线性K线数据离散化示例
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import argparse
from datetime import datetime
import talib
from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.base.nonlinear_tokenizer import (
    NonlinearCandlestickTokenizer,
    LinearMapping,
    LogarithmicMapping,
    ExponentialMapping,
    SigmoidMapping,
    QuantileMapping
)
import matplotlib
matplotlib.rcParams['font.family'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif']

def load_sample_data(file_path=None):
    """
    加载示例数据，如果没有提供文件路径，则生成随机数据
    """
    if file_path and os.path.exists(file_path):
        # 加载真实数据
        # df = pd.read_csv(file_path)
        df = pd.read_parquet(file_path)
        # 确保列名正确
        if 'date' in df.columns and 'datetime' not in df.columns:
            df.rename(columns={'date': 'datetime'}, inplace=True)
        return df
    else:
        # 生成随机数据
        print("未找到数据文件，生成随机数据...")
        np.random.seed(42)
        n_samples = 500

        # 生成日期时间
        start_date = pd.Timestamp('2020-01-01')
        dates = [start_date + pd.Timedelta(days=i) for i in range(n_samples)]

        # 生成价格
        close = np.random.normal(loc=100, scale=1, size=n_samples).cumsum() + 1000
        daily_volatility = 0.01

        high = close * (1 + np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        low = close * (1 - np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        open_price = low + (high - low) * np.random.random(size=n_samples)
        volume = np.random.normal(loc=1000000, scale=200000, size=n_samples).clip(100000, None)

        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })

        return df

def demo_mapping_functions():
    """
    演示不同的映射函数
    """
    print("\n=== 演示不同的映射函数 ===")

    # 创建输入数据
    np.random.seed(42)

    # 正态分布数据
    normal_data = np.random.normal(0, 1, 1000)

    # 右偏分布数据（对数正态）
    right_skewed_data = np.random.lognormal(0, 1, 1000)

    # 左偏分布数据
    left_skewed_data = -np.random.lognormal(0, 1, 1000)

    # 创建映射函数
    output_range = (-12, 12)

    # 正态分布数据的映射函数
    normal_input_range = (np.min(normal_data), np.max(normal_data))
    linear_mapping = LinearMapping(normal_input_range, output_range)
    sigmoid_mapping = SigmoidMapping(normal_input_range, output_range)

    # 右偏分布数据的映射函数
    right_input_range = (np.min(right_skewed_data), np.max(right_skewed_data))
    log_mapping = LogarithmicMapping(right_input_range, output_range)
    quantile_mapping = QuantileMapping(right_skewed_data, output_range)

    # 左偏分布数据的映射函数
    left_input_range = (np.min(left_skewed_data), np.max(left_skewed_data))
    exp_mapping = ExponentialMapping(left_input_range, output_range)

    # 可视化映射函数
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))

    # 正态分布数据
    axes[0, 0].hist(normal_data, bins=30, alpha=0.7)
    axes[0, 0].set_title('正态分布数据')

    # 线性映射结果
    linear_mapped = np.array([linear_mapping(x) for x in normal_data])
    axes[0, 1].hist(linear_mapped, bins=output_range[1] - output_range[0] + 1,
                   range=(output_range[0] - 0.5, output_range[1] + 0.5),
                   alpha=0.7)
    axes[0, 1].set_title('线性映射结果')

    # 右偏分布数据
    axes[1, 0].hist(right_skewed_data, bins=30, alpha=0.7)
    axes[1, 0].set_title('右偏分布数据')

    # 对数映射结果
    log_mapped = np.array([log_mapping(x) for x in right_skewed_data])
    axes[1, 1].hist(log_mapped, bins=output_range[1] - output_range[0] + 1,
                   range=(output_range[0] - 0.5, output_range[1] + 0.5),
                   alpha=0.7)
    axes[1, 1].set_title('对数映射结果')

    # 左偏分布数据
    axes[2, 0].hist(left_skewed_data, bins=30, alpha=0.7)
    axes[2, 0].set_title('左偏分布数据')

    # 指数映射结果
    exp_mapped = np.array([exp_mapping(x) for x in left_skewed_data])
    axes[2, 1].hist(exp_mapped, bins=output_range[1] - output_range[0] + 1,
                   range=(output_range[0] - 0.5, output_range[1] + 0.5),
                   alpha=0.7)
    axes[2, 1].set_title('指数映射结果')
    plt.tight_layout()
    plt.show()

    # 可视化S形映射和分位数映射
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # S形映射
    sigmoid_mapped = np.array([sigmoid_mapping(x) for x in normal_data])
    axes[0, 0].hist(sigmoid_mapped, bins=output_range[1] - output_range[0] + 1,
                   range=(output_range[0] - 0.5, output_range[1] + 0.5),
                   alpha=0.7)
    axes[0, 0].set_title('S形映射结果 (正态分布)')

    # 分位数映射
    quantile_mapped = np.array([quantile_mapping(x) for x in right_skewed_data])
    axes[0, 1].hist(quantile_mapped, bins=output_range[1] - output_range[0] + 1,
                   range=(output_range[0] - 0.5, output_range[1] + 0.5),
                   alpha=0.7)
    axes[0, 1].set_title('分位数映射结果 (右偏分布)')

    # 映射函数曲线
    x_linear = np.linspace(normal_input_range[0], normal_input_range[1], 100)
    y_linear = np.array([linear_mapping(xi) for xi in x_linear])
    axes[1, 0].plot(x_linear, y_linear)
    axes[1, 0].set_title('线性映射函数')
    axes[1, 0].grid(True)

    x_sigmoid = np.linspace(normal_input_range[0], normal_input_range[1], 100)
    y_sigmoid = np.array([sigmoid_mapping(xi) for xi in x_sigmoid])
    axes[1, 1].plot(x_sigmoid, y_sigmoid)
    axes[1, 1].set_title('S形映射函数')
    axes[1, 1].grid(True)

    plt.tight_layout()
    plt.show()

    return {
        'linear': linear_mapping,
        'sigmoid': sigmoid_mapping,
        'logarithmic': log_mapping,
        'exponential': exp_mapping,
        'quantile': quantile_mapping
    }

def demo_nonlinear_tokenizer(df):
    """
    演示非线性K线tokenizer
    """
    print("\n=== 演示非线性K线Tokenizer ===")

    # 创建线性tokenizer（作为基准）
    linear_tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )

    # 创建非线性tokenizer
    nonlinear_tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )

    # 对数据进行tokenize
    linear_tokens = linear_tokenizer.tokenize(df)
    nonlinear_tokens = nonlinear_tokenizer.tokenize(df)

    print(f"线性tokenizer生成的token数量: {len(linear_tokens)}")
    print(f"非线性tokenizer生成的token数量: {len(nonlinear_tokens)}")

    # 可视化tokenization结果
    print("\n=== 线性Tokenizer结果 ===")
    linear_tokenizer.visualize_tokenization(df, linear_tokens, title="线性Tokenizer结果")

    print("\n=== 非线性Tokenizer结果（使用非线性还原） ===")
    # 非线性tokenizer现在会使用自己的tokens_to_candlesticks方法进行还原
    nonlinear_tokenizer.visualize_tokenization(df, nonlinear_tokens, title="非线性Tokenizer结果")

    # 可视化映射函数
    nonlinear_tokenizer.visualize_mapping_functions()

    # 比较不同特征的映射效果
    for feature in ['change', 'entity', 'upline', 'downline']:
        # 提取原始特征值
        if feature == 'change':
            raw_values = (df['close'] - df['close'].shift(1)).dropna().values
        elif feature == 'entity':
            raw_values = (df['close'] - df['open']).values
        elif feature == 'upline':
            raw_values = (df['high'] - df[['open', 'close']].max(axis=1)).values
        elif feature == 'downline':
            raw_values = (df[['open', 'close']].min(axis=1) - df['low']).values

        # 比较不同映射函数
        nonlinear_tokenizer.compare_mappings(raw_values, feature)

    return linear_tokenizer, nonlinear_tokenizer, linear_tokens, nonlinear_tokens

def demo_custom_mapping(df):
    """
    演示自定义映射函数
    """
    print("\n=== 演示自定义映射函数 ===")

    # 提取原始特征值
    atr = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=100)
    # 填充NaN值
    atr = np.nan_to_num(atr, nan=np.nanmean(atr))
    atr_scaled = atr * 0.88 / 10

    # 确保数组长度匹配
    change_values = (df['close'] - df['close'].shift(1)).fillna(0).values
    # 使用广播确保长度匹配
    atr_for_change = np.ones_like(change_values) * np.mean(atr_scaled)
    change = change_values / atr_for_change

    entity = (df['close'] - df['open']).values / np.ones_like(df['close'].values) * np.mean(atr_scaled)
    upline = (df['high'] - df[['open', 'close']].max(axis=1)).values / np.ones_like(df['close'].values) * np.mean(atr_scaled)
    downline = (df[['open', 'close']].min(axis=1) - df['low']).values / np.ones_like(df['close'].values) * np.mean(atr_scaled)

    # 创建自定义映射函数
    change_mapping = SigmoidMapping(
        input_range=(np.percentile(change, 1), np.percentile(change, 99)),
        output_range=(-12, 12),
        steepness=1.5  # 更陡峭的S形曲线
    )

    entity_mapping = LogarithmicMapping(
        input_range=(1e-6, np.percentile(np.abs(entity), 99)),
        output_range=(-12, 12),
        base=2.0  # 使用2为底的对数
    )

    shadow_mapping = ExponentialMapping(
        input_range=(0, np.percentile(np.concatenate([upline, downline]), 99)),
        output_range=(0, 7),
        exponent=0.5  # 使用平方根（指数为0.5）
    )

    # 创建带有自定义映射函数的tokenizer
    custom_tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True,
        mapping_functions={
            'change': change_mapping,
            'entity': entity_mapping,
            'upline': shadow_mapping,
            'downline': shadow_mapping
        }
    )

    # 对数据进行tokenize
    custom_tokens = custom_tokenizer.tokenize(df)

    print(f"自定义映射tokenizer生成的token数量: {len(custom_tokens)}")

    # 可视化tokenization结果
    custom_tokenizer.visualize_tokenization(df, custom_tokens, title="自定义映射Tokenizer结果")

    # 可视化映射函数
    custom_tokenizer.visualize_mapping_functions()

    return custom_tokenizer, custom_tokens

def demo_reconstruction_comparison(df):
    """
    演示非线性还原与线性还原的比较
    """
    print("\n=== 演示非线性还原与线性还原的比较 ===")

    # 创建非线性tokenizer
    nonlinear_tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )

    # 比较重建效果
    original, linear_reconstructed, nonlinear_reconstructed = nonlinear_tokenizer.compare_reconstruction(df)

    # 打印改进百分比
    print("\n改进百分比:")

    # 计算每个特征的改进百分比
    for col in ['close', 'open', 'high', 'low']:
        # 计算线性还原的误差
        linear_error = np.mean(np.abs(original[col].values - linear_reconstructed[col].values))
        # 计算非线性还原的误差
        nonlinear_error = np.mean(np.abs(original[col].values - nonlinear_reconstructed[col].values))
        # 计算改进百分比
        improvement = (linear_error - nonlinear_error) / linear_error * 100
        print(f"{col}: 改进了 {improvement:.2f}%")

    return nonlinear_tokenizer

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='非线性K线数据离散化示例')

    # 数据参数
    parser.add_argument('--data_path', type=str, default='f:/hqdata/fut_sf_day.parquet', help='数据文件路径')
    parser.add_argument('--demo', type=str, default='all',
                       choices=['all', 'mapping', 'tokenizer', 'custom', 'reconstruction'],
                       help='要运行的演示')

    args = parser.parse_args()

    # 加载数据
    df = load_sample_data(args.data_path)
    df = df[df['code'] == 'IF9999.SF'][-150:]
    print(f'数据形状: {df.shape}')
    print(df)

    # 运行演示
    if args.demo in ['all', 'mapping']:
        mapping_funcs = demo_mapping_functions()

    if args.demo in ['all', 'tokenizer']:
        linear_tokenizer, nonlinear_tokenizer, linear_tokens, nonlinear_tokens = demo_nonlinear_tokenizer(df)

    if args.demo in ['all', 'custom']:
        custom_tokenizer, custom_tokens = demo_custom_mapping(df)

    if args.demo in ['all', 'reconstruction']:
        nonlinear_tokenizer = demo_reconstruction_comparison(df)

    print("\n演示完成!")

if __name__ == '__main__':
    main()
