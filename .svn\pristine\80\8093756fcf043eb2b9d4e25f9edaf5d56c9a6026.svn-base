{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config\n", "from qlib.tests.data import GetData\n", "from qlib.tests.config import CSI300_GBDT_TASK"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2021-12-06 13:46:41.675 | WARNING  | qlib.tests.data:qlib_data:148 - Data already exists: ~/.qlib/qlib_data/cn_data, the data download will be skipped\n", "\tIf downloading is required: `exists_skip=False` or `change target_dir`\n", "[19296:MainThread](2021-12-06 13:46:43,471) INFO - qlib.Initialization - [config.py:386] - default_conf: client.\n", "[19296:MainThread](2021-12-06 13:46:45,520) WARNING - qlib.Initialization - [config.py:411] - redis connection failed(host=127.0.0.1 port=6379), DiskExpressionCache and DiskDatasetCache will not be used!\n", "[19296:MainThread](2021-12-06 13:46:47,478) INFO - qlib.Initialization - [__init__.py:56] - qlib successfully initialized based on client settings.\n", "[19296:MainThread](2021-12-06 13:46:47,479) INFO - qlib.Initialization - [__init__.py:58] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["    # use default data\n", "    provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "    GetData().qlib_data(target_dir=provider_uri, region=REG_CN, exists_skip=True)\n", "\n", "    qlib.init(provider_uri=provider_uri, region=REG_CN)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[19296:MainThread](2021-12-06 13:49:31,784) INFO - qlib.timer - [log.py:113] - Time cost: 139.884s | Loading data Done\n", "[19296:MainThread](2021-12-06 13:49:33,094) INFO - qlib.timer - [log.py:113] - Time cost: 1.106s | DropnaLabel Done\n", "d:\\Anaconda3\\lib\\site-packages\\pandas\\core\\frame.py:3191: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self[k1] = value[k2]\n", "[19296:MainThread](2021-12-06 13:49:41,495) INFO - qlib.timer - [log.py:113] - Time cost: 8.399s | CSZScoreNorm Done\n", "[19296:MainThread](2021-12-06 13:49:41,546) INFO - qlib.timer - [log.py:113] - Time cost: 9.762s | fit & process data Done\n", "[19296:MainThread](2021-12-06 13:49:41,548) INFO - qlib.timer - [log.py:113] - Time cost: 149.648s | Init data Done\n"]}], "source": ["dataset = init_instance_by_config(CSI300_GBDT_TASK[\"dataset\"])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["model = init_instance_by_config(CSI300_GBDT_TASK[\"model\"])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\lightgbm\\engine.py:181: User<PERSON>arning: 'early_stopping_rounds' argument is deprecated and will be removed in a future release of LightGBM. Pass 'early_stopping()' callback via 'callbacks' argument instead.\n", "  _log_warning(\"'early_stopping_rounds' argument is deprecated and will be removed in a future release of LightGBM. \"\n", "d:\\Anaconda3\\lib\\site-packages\\lightgbm\\engine.py:239: UserWarning: 'verbose_eval' argument is deprecated and will be removed in a future release of LightGBM. Pass 'log_evaluation()' callback via 'callbacks' argument instead.\n", "  _log_warning(\"'verbose_eval' argument is deprecated and will be removed in a future release of LightGBM. \"\n", "d:\\Anaconda3\\lib\\site-packages\\lightgbm\\engine.py:260: UserWarning: 'evals_result' argument is deprecated and will be removed in a future release of LightGBM. Pass 'record_evaluation()' callback via 'callbacks' argument instead.\n", "  _log_warning(\"'evals_result' argument is deprecated and will be removed in a future release of LightGBM. \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Training until validation scores don't improve for 50 rounds\n", "[20]\ttrain's l2: 0.990316\tvalid's l2: 0.994077\n", "[40]\ttrain's l2: 0.986569\tvalid's l2: 0.993315\n", "[60]\ttrain's l2: 0.983942\tvalid's l2: 0.993108\n", "[80]\ttrain's l2: 0.981795\tvalid's l2: 0.993041\n", "[100]\ttrain's l2: 0.979894\tvalid's l2: 0.992909\n", "[120]\ttrain's l2: 0.978106\tvalid's l2: 0.992912\n", "[140]\ttrain's l2: 0.976331\tvalid's l2: 0.992908\n", "Early stopping, best iteration is:\n", "[109]\ttrain's l2: 0.979065\tvalid's l2: 0.992876\n"]}], "source": ["model.fit(dataset)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["feature importance:\n", "RESI5     242\n", "RESI10    200\n", "CORR10    181\n", "STD20     181\n", "STD5      164\n", "         ... \n", "CNTD5       7\n", "CNTN10      6\n", "CNTN5       3\n", "VWAP0       0\n", "CNTP5       0\n", "Length: 158, dtype: int32\n"]}], "source": ["    # get model feature importance\n", "    feature_importance = model.get_feature_importance()\n", "    print(\"feature importance:\")\n", "    print(feature_importance)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from qlib.data.dataset.handler import DataHandlerLP"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th colspan=\"20\" halign=\"left\">feature</th>\n", "      <th>label</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>KMID</th>\n", "      <th>KLEN</th>\n", "      <th>KMID2</th>\n", "      <th>KUP</th>\n", "      <th>KUP2</th>\n", "      <th>KLOW</th>\n", "      <th>KLOW2</th>\n", "      <th>KSFT</th>\n", "      <th>KSFT2</th>\n", "      <th>OPEN0</th>\n", "      <th>...</th>\n", "      <th>VSUMN10</th>\n", "      <th>VSUMN20</th>\n", "      <th>VSUMN30</th>\n", "      <th>VSUMN60</th>\n", "      <th>VSUMD5</th>\n", "      <th>VSUMD10</th>\n", "      <th>VSUMD20</th>\n", "      <th>VSUMD30</th>\n", "      <th>VSUMD60</th>\n", "      <th>LABEL0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th>instrument</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2008-01-02</th>\n", "      <th>SH600000</th>\n", "      <td>0.010377</td>\n", "      <td>0.061132</td>\n", "      <td>0.169754</td>\n", "      <td>0.028302</td>\n", "      <td>0.462962</td>\n", "      <td>0.022453</td>\n", "      <td>0.367285</td>\n", "      <td>0.004528</td>\n", "      <td>0.074077</td>\n", "      <td>0.989729</td>\n", "      <td>...</td>\n", "      <td>0.550639</td>\n", "      <td>0.490807</td>\n", "      <td>0.485962</td>\n", "      <td>0.510337</td>\n", "      <td>-0.022349</td>\n", "      <td>-0.101278</td>\n", "      <td>0.018386</td>\n", "      <td>0.028076</td>\n", "      <td>-0.020675</td>\n", "      <td>1.226919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SH600004</th>\n", "      <td>0.057279</td>\n", "      <td>0.059666</td>\n", "      <td>0.960000</td>\n", "      <td>0.002387</td>\n", "      <td>0.040000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.054893</td>\n", "      <td>0.920001</td>\n", "      <td>0.945824</td>\n", "      <td>...</td>\n", "      <td>0.328894</td>\n", "      <td>0.392360</td>\n", "      <td>0.423984</td>\n", "      <td>0.514932</td>\n", "      <td>0.633267</td>\n", "      <td>0.342211</td>\n", "      <td>0.215280</td>\n", "      <td>0.152031</td>\n", "      <td>-0.029863</td>\n", "      <td>-0.362926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SH600006</th>\n", "      <td>0.012673</td>\n", "      <td>0.040323</td>\n", "      <td>0.314285</td>\n", "      <td>0.008065</td>\n", "      <td>0.200000</td>\n", "      <td>0.019585</td>\n", "      <td>0.485715</td>\n", "      <td>0.024194</td>\n", "      <td>0.600000</td>\n", "      <td>0.987486</td>\n", "      <td>...</td>\n", "      <td>0.522838</td>\n", "      <td>0.433405</td>\n", "      <td>0.494553</td>\n", "      <td>0.524930</td>\n", "      <td>-0.064064</td>\n", "      <td>-0.045677</td>\n", "      <td>0.133190</td>\n", "      <td>0.010894</td>\n", "      <td>-0.049861</td>\n", "      <td>-0.696644</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SH600007</th>\n", "      <td>0.066977</td>\n", "      <td>0.084186</td>\n", "      <td>0.795579</td>\n", "      <td>0.007907</td>\n", "      <td>0.093923</td>\n", "      <td>0.009302</td>\n", "      <td>0.110498</td>\n", "      <td>0.068372</td>\n", "      <td>0.812153</td>\n", "      <td>0.937228</td>\n", "      <td>...</td>\n", "      <td>0.242611</td>\n", "      <td>0.291541</td>\n", "      <td>0.352613</td>\n", "      <td>0.420448</td>\n", "      <td>0.878055</td>\n", "      <td>0.514779</td>\n", "      <td>0.416918</td>\n", "      <td>0.294774</td>\n", "      <td>0.159104</td>\n", "      <td>-0.079643</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SH600008</th>\n", "      <td>0.051163</td>\n", "      <td>0.082326</td>\n", "      <td>0.621469</td>\n", "      <td>0.027907</td>\n", "      <td>0.338984</td>\n", "      <td>0.003256</td>\n", "      <td>0.039547</td>\n", "      <td>0.026512</td>\n", "      <td>0.322033</td>\n", "      <td>0.951327</td>\n", "      <td>...</td>\n", "      <td>0.425714</td>\n", "      <td>0.381852</td>\n", "      <td>0.400486</td>\n", "      <td>0.469882</td>\n", "      <td>0.482783</td>\n", "      <td>0.148572</td>\n", "      <td>0.236296</td>\n", "      <td>0.199027</td>\n", "      <td>0.060235</td>\n", "      <td>-0.946646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2014-12-31</th>\n", "      <th>SZ300070</th>\n", "      <td>0.026549</td>\n", "      <td>0.034808</td>\n", "      <td>0.762713</td>\n", "      <td>0.001475</td>\n", "      <td>0.042373</td>\n", "      <td>0.006785</td>\n", "      <td>0.194914</td>\n", "      <td>0.031858</td>\n", "      <td>0.915255</td>\n", "      <td>0.974138</td>\n", "      <td>...</td>\n", "      <td>0.505419</td>\n", "      <td>0.646531</td>\n", "      <td>0.562941</td>\n", "      <td>0.544498</td>\n", "      <td>-0.135964</td>\n", "      <td>-0.010839</td>\n", "      <td>-0.293062</td>\n", "      <td>-0.125882</td>\n", "      <td>-0.088996</td>\n", "      <td>1.641685</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SZ300124</th>\n", "      <td>0.003093</td>\n", "      <td>0.028179</td>\n", "      <td>0.109754</td>\n", "      <td>0.011340</td>\n", "      <td>0.402439</td>\n", "      <td>0.013746</td>\n", "      <td>0.487807</td>\n", "      <td>0.005498</td>\n", "      <td>0.195123</td>\n", "      <td>0.996917</td>\n", "      <td>...</td>\n", "      <td>0.725620</td>\n", "      <td>0.601075</td>\n", "      <td>0.503495</td>\n", "      <td>0.507122</td>\n", "      <td>-0.120979</td>\n", "      <td>-0.451240</td>\n", "      <td>-0.202151</td>\n", "      <td>-0.006989</td>\n", "      <td>-0.014245</td>\n", "      <td>0.760669</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SZ300133</th>\n", "      <td>-0.006733</td>\n", "      <td>0.038812</td>\n", "      <td>-0.173468</td>\n", "      <td>0.007525</td>\n", "      <td>0.193878</td>\n", "      <td>0.024555</td>\n", "      <td>0.632654</td>\n", "      <td>0.010297</td>\n", "      <td>0.265307</td>\n", "      <td>1.006778</td>\n", "      <td>...</td>\n", "      <td>0.523729</td>\n", "      <td>0.608872</td>\n", "      <td>0.506058</td>\n", "      <td>0.482053</td>\n", "      <td>0.264180</td>\n", "      <td>-0.047458</td>\n", "      <td>-0.217744</td>\n", "      <td>-0.012117</td>\n", "      <td>0.035895</td>\n", "      <td>1.637536</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SZ300146</th>\n", "      <td>0.027262</td>\n", "      <td>0.032398</td>\n", "      <td>0.841464</td>\n", "      <td>0.005136</td>\n", "      <td>0.158536</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.022126</td>\n", "      <td>0.682928</td>\n", "      <td>0.973462</td>\n", "      <td>...</td>\n", "      <td>0.556447</td>\n", "      <td>0.780794</td>\n", "      <td>0.486670</td>\n", "      <td>0.578597</td>\n", "      <td>0.004723</td>\n", "      <td>-0.112895</td>\n", "      <td>-0.561589</td>\n", "      <td>0.026660</td>\n", "      <td>-0.157194</td>\n", "      <td>1.316203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SZ300251</th>\n", "      <td>-0.006723</td>\n", "      <td>0.065126</td>\n", "      <td>-0.103226</td>\n", "      <td>0.035294</td>\n", "      <td>0.541936</td>\n", "      <td>0.023109</td>\n", "      <td>0.354838</td>\n", "      <td>-0.018908</td>\n", "      <td>-0.290323</td>\n", "      <td>1.006768</td>\n", "      <td>...</td>\n", "      <td>0.576962</td>\n", "      <td>0.498046</td>\n", "      <td>0.474092</td>\n", "      <td>0.469374</td>\n", "      <td>0.148154</td>\n", "      <td>-0.153924</td>\n", "      <td>0.003908</td>\n", "      <td>0.051816</td>\n", "      <td>0.061251</td>\n", "      <td>1.750998</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>484974 rows × 159 columns</p>\n", "</div>"], "text/plain": ["                        feature                                          \\\n", "                           KMID      KLEN     KMID2       KUP      KUP2   \n", "datetime   instrument                                                     \n", "2008-01-02 SH600000    0.010377  0.061132  0.169754  0.028302  0.462962   \n", "           SH600004    0.057279  0.059666  0.960000  0.002387  0.040000   \n", "           SH600006    0.012673  0.040323  0.314285  0.008065  0.200000   \n", "           SH600007    0.066977  0.084186  0.795579  0.007907  0.093923   \n", "           SH600008    0.051163  0.082326  0.621469  0.027907  0.338984   \n", "...                         ...       ...       ...       ...       ...   \n", "2014-12-31 SZ300070    0.026549  0.034808  0.762713  0.001475  0.042373   \n", "           SZ300124    0.003093  0.028179  0.109754  0.011340  0.402439   \n", "           SZ300133   -0.006733  0.038812 -0.173468  0.007525  0.193878   \n", "           SZ300146    0.027262  0.032398  0.841464  0.005136  0.158536   \n", "           SZ300251   -0.006723  0.065126 -0.103226  0.035294  0.541936   \n", "\n", "                                                                         ...  \\\n", "                           KLOW     KLOW2      KSFT     KSFT2     OPEN0  ...   \n", "datetime   instrument                                                    ...   \n", "2008-01-02 SH600000    0.022453  0.367285  0.004528  0.074077  0.989729  ...   \n", "           SH600004    0.000000  0.000000  0.054893  0.920001  0.945824  ...   \n", "           SH600006    0.019585  0.485715  0.024194  0.600000  0.987486  ...   \n", "           SH600007    0.009302  0.110498  0.068372  0.812153  0.937228  ...   \n", "           SH600008    0.003256  0.039547  0.026512  0.322033  0.951327  ...   \n", "...                         ...       ...       ...       ...       ...  ...   \n", "2014-12-31 SZ300070    0.006785  0.194914  0.031858  0.915255  0.974138  ...   \n", "           SZ300124    0.013746  0.487807  0.005498  0.195123  0.996917  ...   \n", "           SZ300133    0.024555  0.632654  0.010297  0.265307  1.006778  ...   \n", "           SZ300146    0.000000  0.000000  0.022126  0.682928  0.973462  ...   \n", "           SZ300251    0.023109  0.354838 -0.018908 -0.290323  1.006768  ...   \n", "\n", "                                                                         \\\n", "                        VSUMN10   VSUMN20   VSUMN30   VSUMN60    VSUMD5   \n", "datetime   instrument                                                     \n", "2008-01-02 SH600000    0.550639  0.490807  0.485962  0.510337 -0.022349   \n", "           SH600004    0.328894  0.392360  0.423984  0.514932  0.633267   \n", "           SH600006    0.522838  0.433405  0.494553  0.524930 -0.064064   \n", "           SH600007    0.242611  0.291541  0.352613  0.420448  0.878055   \n", "           SH600008    0.425714  0.381852  0.400486  0.469882  0.482783   \n", "...                         ...       ...       ...       ...       ...   \n", "2014-12-31 SZ300070    0.505419  0.646531  0.562941  0.544498 -0.135964   \n", "           SZ300124    0.725620  0.601075  0.503495  0.507122 -0.120979   \n", "           SZ300133    0.523729  0.608872  0.506058  0.482053  0.264180   \n", "           SZ300146    0.556447  0.780794  0.486670  0.578597  0.004723   \n", "           SZ300251    0.576962  0.498046  0.474092  0.469374  0.148154   \n", "\n", "                                                                  label  \n", "                        VSUMD10   VSUMD20   VSUMD30   VSUMD60    LABEL0  \n", "datetime   instrument                                                    \n", "2008-01-02 SH600000   -0.101278  0.018386  0.028076 -0.020675  1.226919  \n", "           SH600004    0.342211  0.215280  0.152031 -0.029863 -0.362926  \n", "           SH600006   -0.045677  0.133190  0.010894 -0.049861 -0.696644  \n", "           SH600007    0.514779  0.416918  0.294774  0.159104 -0.079643  \n", "           SH600008    0.148572  0.236296  0.199027  0.060235 -0.946646  \n", "...                         ...       ...       ...       ...       ...  \n", "2014-12-31 SZ300070   -0.010839 -0.293062 -0.125882 -0.088996  1.641685  \n", "           SZ300124   -0.451240 -0.202151 -0.006989 -0.014245  0.760669  \n", "           SZ300133   -0.047458 -0.217744 -0.012117  0.035895  1.637536  \n", "           SZ300146   -0.112895 -0.561589  0.026660 -0.157194  1.316203  \n", "           SZ300251   -0.153924  0.003908  0.051816  0.061251  1.750998  \n", "\n", "[484974 rows x 159 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train = dataset.prepare(\"train\", col_set=[\"feature\", \"label\"], data_key=DataHandlerLP.DK_L)\n", "df_train"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}