import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error
import matplotlib.pyplot as plt
from pytrends.request import TrendReq
import yfinance as yf
from datetime import datetime, timedelta
import faiss
from pyqlab.const import MAIN_HOT_FUT_CODES


# 1. 数据获取与预处理
class FinancialDataProcessor:
    def __init__(self, data_path, market, block_name, period, ticker, start_date, end_date):
        self.data_path = data_path
        self.market = market
        self.block_name = block_name
        self.period = period
        self.ticker = ticker
        self.start_date = start_date
        self.end_date = end_date
        self.price_data = None
        self.trend_data = None
        self.combined_data = None
        self.scaler_price = StandardScaler()
        self.scaler_trend = StandardScaler()
        # self.scaler_price = MinMaxScaler(feature_range=(0, 1))
        # self.scaler_trend = MinMaxScaler(feature_range=(0, 1))

    def load_historical_data(self):
        """从本地文件中加载历史数据"""
        path = f'{self.data_path}/store/{self.market}_{self.block_name}_{self.period}.parquet'
        self.df = pd.read_parquet(path)        
        self.df['code'] = self.df['code'].str[:-7]
        self.df = self.df[self.df['code'].isin(self.ticker)]
        self.df['datetime'] = pd.to_datetime(self.df['datetime'], unit='s')  # Ensure datetime is in proper format
        self.df = self.df[self.df['datetime'] >= pd.Timestamp(self.start_date)]
        self.df = self.df[self.df['datetime'] <= pd.Timestamp(self.end_date)]
        self.code_list = self.df['code'].unique()
        print(self.df.head())
        print(self.code_list)
        print(f'load {path} success, {self.df.shape}')

    def fetch_price_data(self):
        """获取价格数据"""
        self.price_data = yf.download(self.ticker, start=self.start_date, end=self.end_date)
        return self.price_data

    def fetch_trend_data(self, keywords):
        """获取Google Trends数据"""
        pytrend = TrendReq(hl='en-US', tz=360)
        pytrend.build_payload(keywords, timeframe=f'{self.start_date} {self.end_date}')
        self.trend_data = pytrend.interest_over_time()
        return self.trend_data

    def combine_data(self):
        """合并价格和趋势数据"""
        if self.price_data is None or self.trend_data is None:
            raise ValueError("Price data and trend data must be fetched before combining")

        # 确保两个数据集的日期一致
        self.trend_data = self.trend_data.resample('D').mean().fillna(method='ffill')
        self.price_data = self.price_data.resample('D').last().fillna(method='ffill')

        # 合并数据
        self.combined_data = pd.merge(
            self.price_data[['Close']], 
            self.trend_data, 
            left_index=True, 
            right_index=True, 
            how='inner'
        )
        return self.combined_data

    def preprocess_data(self, seq_length=30):
        """预处理数据，创建时间序列样本"""
        # if self.combined_data is None:
        #     raise ValueError("Data must be combined before preprocessing")

        # 标准化数据
        # price_values = self.combined_data[['Open', 'High', 'Low', 'Close', 'Volume']].values
        # trend_values = self.combined_data.iloc[:, 1:].values

        # scaled_price = self.scaler_price.fit_transform(price_values)
        # scaled_trend = self.scaler_trend.fit_transform(trend_values)

        # 创建时间序列样本
        X_price, y = [], []

        for code, df in self.df.groupby('code'):
            print(code, df.shape)
            df = df.sort_values(by='datetime', ascending=True)
            price_values = df[['open', 'high', 'low', 'close', 'volume']].values
            self.df['change'] = self.df['close'].shift(-3) / self.df['close'] - 1
            change_values = self.df[['change']].values
            scaled_price = self.scaler_price.fit_transform(price_values)

            for i in range(len(scaled_price) - seq_length - 3):
                X_price.append(scaled_price[i:i+seq_length])
                y.append(change_values[i+seq_length])

        return np.array(X_price), np.array(y)

# 2. 时间序列数据集
class KlineSeriesDataset(Dataset):
    def __init__(self, X_price, X_trend, y):
        self.X_price = torch.FloatTensor(X_price)
        self.y = torch.FloatTensor(y)

    def __len__(self):
        return len(self.y)

    def __getitem__(self, idx):
        return self.X_price[idx], self.y[idx]

# 3. 检索组件
class TimeSeriesRetriever:
    def __init__(self, embedding_dim, n_neighbors=5):
        self.embedding_dim = embedding_dim
        self.n_neighbors = n_neighbors
        self.index = None
        self.price_embeddings = None
        self.targets = None

    def build_index(self, price_embeddings, targets):
        """构建FAISS索引"""
        self.price_embeddings = price_embeddings
        self.targets = targets

        # 构建索引
        self.index = faiss.IndexFlatL2(self.price_embeddings.shape[1])
        self.index.add(self.price_embeddings)

    def retrieve(self, price_query):
        """检索最相似的历史序列"""
        # 执行检索
        distances, indices = self.index.search(price_query, self.n_neighbors)

        # 返回检索结果
        retrieved_prices = self.price_embeddings[indices]
        retrieved_targets = self.targets[indices]

        return retrieved_prices, retrieved_targets, distances

# 4. TimeRAG编码器
class KlineSeriesEncoder(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers=2):
        super(KlineSeriesEncoder, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # LSTM编码器
        self.lstm = nn.LSTM(
            input_dim, 
            hidden_dim, 
            num_layers=num_layers, 
            batch_first=True, 
            bidirectional=True
        )

        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1),
            nn.Softmax(dim=1)
        )

    def forward(self, x):
        # LSTM编码
        lstm_out, _ = self.lstm(x)

        # 注意力加权
        attention_weights = self.attention(lstm_out)
        context_vector = torch.sum(attention_weights * lstm_out, dim=1)

        return context_vector

# 5. TimeRAG生成器
class KlineSeriesGenerator(nn.Module):
    def __init__(self, context_dim, hidden_dim, output_dim):
        super(KlineSeriesGenerator, self).__init__()

        self.context_processor = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.retrieval_processor = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.Sigmoid()  # 使用Sigmoid确保输出在0-1范围内（因为使用了MinMaxScaler）
        )

    def forward(self, context_embedding, retrieval_embedding):
        # 处理上下文嵌入
        processed_context = self.context_processor(context_embedding)

        # 处理检索嵌入
        processed_retrieval = self.retrieval_processor(retrieval_embedding)

        # 融合
        fusion = torch.cat([processed_context, processed_retrieval], dim=1)
        fusion_output = self.fusion_layer(fusion)

        # 生成预测
        prediction = self.output_layer(fusion_output)

        return prediction

# 6. 完整的TimeRAG模型
class KlineRAG(nn.Module):
    def __init__(self, price_dim, hidden_dim, output_dim, retriever, n_neighbors=5):
        super(KlineRAG, self).__init__()

        # 编码器
        self.price_encoder = KlineSeriesEncoder(price_dim, hidden_dim)

        # 检索器
        self.retriever = retriever
        self.n_neighbors = n_neighbors

        # 生成器
        context_dim = hidden_dim * 2  # 因为LSTM是双向的
        self.generator = KlineSeriesGenerator(context_dim * 2, hidden_dim, output_dim)

    def encode(self, price_seq):
        """编码输入序列"""
        price_embedding = self.price_encoder(price_seq)
        return price_embedding

    def forward(self, price_seq, is_training=True):
        # 编码输入序列
        price_embedding = self.encode(price_seq)

        if is_training:
            # 在训练时，使用批处理
            batch_size = price_seq.size(0)

            # 转换为numpy以用于FAISS检索
            price_np = price_embedding.detach().cpu().numpy()

            # 执行检索
            retrieved_prices, _, _ = self.retriever.retrieve(price_np)

            # 转换回PyTorch张量并重塑维度
            retrieved_prices = torch.FloatTensor(retrieved_prices).to(price_seq.device)

            # 调整形状为 [batch_size, n_neighbors, embedding_dim]
            retrieved_prices = retrieved_prices.view(batch_size, self.n_neighbors, -1)

            # 为每个样本计算平均检索嵌入
            avg_retrieved_price = torch.mean(retrieved_prices, dim=1)

            # 合并编码嵌入和检索嵌入
            context_embedding = torch.cat([price_embedding, avg_retrieved_price], dim=1)
        
            # 生成预测
            prediction = self.generator(context_embedding)

        else:
            # 在推理时，使用单例处理

            # 转换为numpy以用于FAISS检索
            price_np = price_embedding.detach().cpu().numpy()

            # 执行检索
            retrieved_prices, _, _ = self.retriever.retrieve(price_np)

            # 转换回PyTorch张量
            retrieved_prices = torch.FloatTensor(retrieved_prices).to(price_seq.device)

            # 计算平均检索嵌入
            avg_retrieved_price = torch.mean(retrieved_prices, dim=1)

            # 合并编码嵌入和检索嵌入
            context_embedding = torch.cat([price_embedding, avg_retrieved_price], dim=1)

            # 生成预测
            prediction = self.generator(context_embedding)

        return prediction

# 7. 训练与评估
def train_kline_rag(model, train_loader, val_loader, criterion, optimizer, device, epochs=50):
    """训练KlineRAG模型"""
    model.to(device)
    best_val_loss = float('inf')
    train_losses, val_losses = [], []

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0

        for price_seq, trend_seq, target in train_loader:
            price_seq, trend_seq, target = price_seq.to(device), trend_seq.to(device), target.to(device)

            # 清除梯度
            optimizer.zero_grad()

            # 前向传播
            output = model(price_seq, trend_seq)

            # 计算损失
            loss = criterion(output, target)

            # 反向传播
            loss.backward()

            # 更新参数
            optimizer.step()

            train_loss += loss.item()

        train_loss /= len(train_loader)
        train_losses.append(train_loss)

        # 验证阶段
        model.eval()
        val_loss = 0

        with torch.no_grad():
            for price_seq, trend_seq, target in val_loader:
                price_seq, trend_seq, target = price_seq.to(device), trend_seq.to(device), target.to(device)

                # 前向传播
                output = model(price_seq, trend_seq, is_training=False)

                # 计算损失
                loss = criterion(output, target)
                val_loss += loss.item()

        val_loss /= len(val_loader)
        val_losses.append(val_loss)

        # 打印进度
        print(f'Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_kline_rag_model.pth')

    return train_losses, val_losses

def evaluate_kline_rag(model, test_loader, scaler, device):
    """评估KlineRAG模型"""
    model.to(device)
    model.eval()
    predictions, actuals = [], []

    with torch.no_grad():
        for price_seq, trend_seq, target in test_loader:
            price_seq, trend_seq, target = price_seq.to(device), trend_seq.to(device), target.to(device)

            # 前向传播
            output = model(price_seq, trend_seq, is_training=False)

            # 转换回CPU
            output = output.cpu().numpy()
            target = target.cpu().numpy()

            # 逆标准化
            output = scaler.inverse_transform(output)
            target = scaler.inverse_transform(target)

            predictions.extend(output)
            actuals.extend(target)

    # 计算评估指标
    predictions = np.array(predictions)
    actuals = np.array(actuals)

    rmse = np.sqrt(mean_squared_error(actuals, predictions))
    mape = mean_absolute_percentage_error(actuals, predictions) * 100

    # 计算方向准确率
    direction_actual = np.diff(actuals.flatten())
    direction_pred = np.diff(predictions.flatten())
    direction_accuracy = np.mean((direction_actual * direction_pred) > 0) * 100

    return rmse, mape, direction_accuracy, predictions, actuals

# 8. 回测交易策略
def backtest_strategy(predictions, actuals, initial_capital=10000):
    """回测简单的交易策略"""
    capital = initial_capital
    position = 0
    trades = []

    for i in range(1, len(predictions)):
        # 预测方向
        pred_direction = predictions[i] - actuals[i-1]

        # 实际方向
        actual_direction = actuals[i] - actuals[i-1]

        # 交易逻辑：预测上涨则买入，预测下跌则卖出
        if pred_direction > 0 and position <= 0:
            # 买入
            position = 1
            entry_price = actuals[i-1]
            trades.append(('buy', i-1, entry_price))
        elif pred_direction < 0 and position >= 0:
            # 卖出
            position = -1
            entry_price = actuals[i-1]
            trades.append(('sell', i-1, entry_price))

    # 计算交易绩效
    returns = []
    current_position = None

    for i in range(len(trades)):
        action, day, price = trades[i]

        if i < len(trades) - 1:
            next_action, next_day, next_price = trades[i+1]

            if action == 'buy' and next_action == 'sell':
                # 完成一次买入-卖出交易
                returns.append((next_price - price) / price)
            elif action == 'sell' and next_action == 'buy':
                # 完成一次卖出-买入交易
                returns.append((price - next_price) / price)

    # 计算绩效指标
    returns = np.array(returns)
    cumulative_return = np.prod(1 + returns) - 1
    annual_return = (1 + cumulative_return) ** (252 / len(actuals)) - 1
    volatility = np.std(returns) * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility != 0 else 0

    # 计算最大回撤
    cumulative_returns = np.cumprod(1 + returns) - 1
    peak = np.maximum.accumulate(cumulative_returns)
    drawdown = (peak - cumulative_returns) / (1 + peak)
    max_drawdown = np.max(drawdown)

    # 计算胜率
    win_rate = np.mean(returns > 0) * 100

    return {
        'cumulative_return': cumulative_return * 100,
        'annual_return': annual_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown * 100,
        'win_rate': win_rate
    }

# 9. 主函数
def main():
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 1. 数据准备
    processor = FinancialDataProcessor(
        data_path='d:/RoboQuant2',
        market='fut',
        block_name='main',
        period='min5',
        ticker=MAIN_HOT_FUT_CODES,
        start_date='2024-01-01',
        end_date='2024-12-31'
    )

    # 获取价格数据
    # price_data = processor.fetch_price_data()

    # 获取Google Trends数据
    # trend_data = processor.fetch_trend_data(
    #     keywords=['stock market', 'financial crisis', 'economic recession', 'bull market']
    # )

    # 加载历史数据
    processor.load_historical_data()

    # 合并数据
    # combined_data = processor.combine_data()

    # 预处理数据
    X_price, y = processor.preprocess_data(seq_length=30)
    print(X_price.shape, y.shape)
    print(X_price[0])
    print(y[0])

    return

    # 分割数据集
    train_size = int(len(X_price) * 0.7)
    val_size = int(len(X_price) * 0.1)
    test_size = len(X_price) - train_size - val_size

    X_price_train, X_price_val, X_price_test = X_price[:train_size], X_price[train_size:train_size+val_size], X_price[train_size+val_size:]
    X_trend_train, X_trend_val, X_trend_test = X_trend[:train_size], X_trend[train_size:train_size+val_size], X_trend[train_size+val_size:]
    y_train, y_val, y_test = y[:train_size], y[train_size:train_size+val_size], y[train_size+val_size:]

    # 创建数据加载器
    train_dataset = KlineSeriesDataset(X_price_train, X_trend_train, y_train)
    val_dataset = KlineSeriesDataset(X_price_val, X_trend_val, y_val)
    test_dataset = KlineSeriesDataset(X_price_test, X_trend_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    # 2. 构建检索器
    # 首先需要为训练集生成嵌入
    hidden_dim = 128
    price_encoder = KlineSeriesEncoder(1, hidden_dim).to(device)
    trend_encoder = KlineSeriesEncoder(X_trend.shape[2], hidden_dim).to(device)

    # 生成嵌入
    price_embeddings, trend_embeddings = [], []
    targets = []

    with torch.no_grad():
        for price_seq, trend_seq, target in train_loader:
            price_seq, trend_seq = price_seq.to(device), trend_seq.to(device)
            price_emb = price_encoder(price_seq).cpu().numpy()
            trend_emb = trend_encoder(trend_seq).cpu().numpy()

            price_embeddings.append(price_emb)
            trend_embeddings.append(trend_emb)
            targets.append(target.numpy())

    price_embeddings = np.vstack(price_embeddings)
    trend_embeddings = np.vstack(trend_embeddings)
    targets = np.vstack(targets)

    # 构建检索器
    retriever = TimeSeriesRetriever(embedding_dim=hidden_dim*2, n_neighbors=5)
    retriever.build_index(price_embeddings, trend_embeddings, targets)

    # 3. 构建TimeRAG模型
    kline_rag_model = KlineRAG(
        price_dim=1,
        trend_dim=X_trend.shape[2],
        hidden_dim=hidden_dim,
        output_dim=1,
        retriever=retriever,
        n_neighbors=5
    )

    # 4. 训练模型
    criterion = nn.MSELoss()
    optimizer = optim.Adam(kline_rag_model.parameters(), lr=0.001)

    train_losses, val_losses = train_kline_rag(
        model=kline_rag_model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        device=device,
        epochs=50
    )

    # 5. 评估模型
    # 加载最佳模型
    kline_rag_model.load_state_dict(torch.load('best_kline_rag_model.pth'))

    rmse, mape, direction_accuracy, predictions, actuals = evaluate_kline_rag(
        model=kline_rag_model,
        test_loader=test_loader,
        scaler=processor.scaler_price,
        device=device
    )

    print(f'Test RMSE: {rmse:.2f}')
    print(f'Test MAPE: {mape:.2f}%')
    print(f'Direction Accuracy: {direction_accuracy:.2f}%')

    # 6. 回测交易策略
    performance = backtest_strategy(predictions, actuals)

    print(f'Cumulative Return: {performance["cumulative_return"]:.2f}%')
    print(f'Sharpe Ratio: {performance["sharpe_ratio"]:.2f}')
    print(f'Maximum Drawdown: {performance["max_drawdown"]:.2f}%')
    print(f'Win Rate: {performance["win_rate"]:.2f}%')

    # 7. 绘制结果
    plt.figure(figsize=(12, 6))
    plt.plot(actuals, label='Actual')
    plt.plot(predictions, label='Predicted')
    plt.legend()
    plt.title('TimeRAG - Price Prediction')
    plt.xlabel('Time')
    plt.ylabel('Price')
    plt.savefig('timerag_prediction.png')
    plt.close()

    # 绘制训练和验证损失
    plt.figure(figsize=(12, 6))
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.legend()
    plt.title('TimeRAG - Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.savefig('timerag_loss.png')
    plt.close()

if __name__ == '__main__':
    main()
