import inspect
import torch
import importlib
from torch.nn import functional as F
import torch.optim.lr_scheduler as lrs
from torch.optim.lr_scheduler import ReduceLROnPlateau
import pytorch_lightning as pl
from torch.optim import <PERSON>, AdamW
from torchmetrics.functional import accuracy

class PLGptModel(pl.LightningModule):
    def __init__(self, model_name, loss, lr, **kargs):
        super().__init__()
        self.kargs = kargs
        self.save_hyperparameters()
        self.load_model()
        self.configure_loss()

    def forward(self, *args, **kwargs):
        code, x, x_mark, y = args
        return self.model(code, x, x_mark, y)

    def training_step(self, batch, batch_idx):
        return self._shared_step(batch, batch_idx, 'loss')

    def validation_step(self, batch, batch_idx):
        return self._shared_step(batch, batch_idx, 'val_loss')

    def _shared_step(self, batch, batch_idx, log_name):
        code, x, x_mark, targets, y_mark = batch
        outputs, loss = self(code, x, x_mark, targets)
        self.log(log_name, loss, on_step=(log_name == 'loss'), on_epoch=True, prog_bar=True)
        return loss

    def test_step(self, batch, batch_idx):
        # Here we just reuse the validation_step for testing
        return self.validation_step(batch, batch_idx)
    
    # def on_train_epoch_end(self):
    #     # Make the Progress Bar leave there
    #     self.log('dummy', 0, on_step=False, on_epoch=True, prog_bar=True)

    # def on_validation_epoch_end(self):
    #     # Make the Progress Bar leave there
    #     self.log('dummy', 0, on_step=False, on_epoch=True, prog_bar=True)

    def configure_optimizers(self):
        weight_decay = getattr(self.hparams, 'weight_decay', 0)
        optimizer_class = AdamW if self.hparams.optimizer == 'adamw' else Adam
        optimizer = optimizer_class(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)

        if self.hparams.lr_scheduler is None:
            return optimizer

        schedulers = {
            'step': {
                'scheduler': lrs.StepLR(optimizer, step_size=self.hparams.lr_decay_steps, gamma=self.hparams.lr_decay_rate),
                'interval': 'epoch'
            },
            'cosine': {
                'scheduler': lrs.CosineAnnealingLR(optimizer, T_max=self.hparams.lr_decay_steps, eta_min=self.hparams.lr_decay_min_lr),
                'interval': 'epoch'
            },
            'plateau': {
                'scheduler': lrs.ReduceLROnPlateau(optimizer, mode='min', factor=self.hparams.lr_decay_rate, patience=self.hparams.lr_decay_steps, min_lr=self.hparams.lr_decay_min_lr, verbose=True),
                'interval': 'epoch',
                'monitor': 'val_loss'
            },
            'reduce_on_plateau': {
                'scheduler': ReduceLROnPlateau(optimizer, mode='min', factor=self.hparams.lr_decay_rate, patience=self.hparams.lr_decay_steps, min_lr=self.hparams.lr_decay_min_lr, verbose=True),
                'interval': 'epoch',
                'monitor': 'val_loss'
            }
        }

        if self.hparams.lr_scheduler not in schedulers:
            raise ValueError('Invalid lr_scheduler type!')

        return [optimizer], [schedulers[self.hparams.lr_scheduler]]

    def configure_loss(self):
        loss_functions = {
            'mse': (F.mse_loss, False),
            'l1': (F.l1_loss, False),
            'bce': (F.binary_cross_entropy, True),
            'ce': (F.cross_entropy, True)
        }

        loss = self.hparams.loss.lower()
        if loss not in loss_functions:
            raise ValueError("Invalid Loss Type!")

        self.loss_function, self.is_acc = loss_functions[loss]

    def configure_model(self):
        self = torch.compile(self, backend='inductor')
        
    def load_model(self):
        name = self.hparams.model_name
        # Change the `snake_case.py` file name to `CamelCase` class name.
        # Please always name your model file name as `snake_case.py` and
        # class name corresponding `CamelCase`.
        camel_name = ''.join([i.capitalize() for i in name.split('_')])
        mod_path = f"{self.hparams.model_path}.{name}"
        try:
            print(f"Model {mod_path}.{camel_name} Loaded!")
            Model = getattr(importlib.import_module(mod_path, package=__package__), camel_name)
        except (ImportError, AttributeError) as e:
            raise ValueError(f'Invalid Module File Name or Invalid Class Name {name}.{camel_name}!') from e
        self.model = self.instancialize(Model)


    def instancialize(self, Model, **other_args):
        """ Instancialize a model using the corresponding parameters
            from self.hparams dictionary. You can also input any args
            to overwrite the corresponding value in self.hparams.
        """
        class_args = inspect.getfullargspec (Model.__init__).args[1:]
        inkeys = self.hparams.keys()
        args1 = {}
        for arg in class_args:
            if arg in inkeys:
                args1[arg] = getattr(self.hparams, arg)
            if arg in self.kargs.keys():
                if arg == 'num_embeds':
                    args1[arg] = self.kargs[arg]
                elif arg == 'out_channels':
                    args1[arg] = self.kargs[arg]
                elif arg == 'ins_nums':
                    args1[arg] = self.kargs[arg]
                else:
                    args1[arg] = self.kargs[arg]
        args1.update(other_args)
        return Model(**args1)

