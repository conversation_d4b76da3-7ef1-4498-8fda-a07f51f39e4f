#!/usr/bin/env python3
"""
测试回测器修复的脚本
"""

import pandas as pd
import numpy as np
import torch
from pyqlab.models.gpt.backtest_bar_tokenized_model import (
    BarTokenizedModelBacktester, load_tokenizer, load_onnx_model
)

def create_test_data(n_samples=1000):
    """创建测试数据"""
    np.random.seed(42)

    # 生成模拟的OHLCV数据
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='1min')

    # 生成价格数据（随机游走）
    base_price = 100.0
    price_changes = np.random.normal(0, 0.01, n_samples)
    prices = [base_price]

    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 确保价格为正

    # 生成OHLCV数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成合理的OHLC数据
        volatility = abs(np.random.normal(0, 0.005))
        high = close * (1 + volatility)
        low = close * (1 - volatility)
        open_price = np.random.uniform(low, high)
        volume = np.random.randint(1000, 10000)

        data.append({
            'datetime': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })

    return pd.DataFrame(data)

def test_backtest_fix():
    """测试回测器修复"""
    print("=== 测试回测器修复 ===")

    # 1. 创建测试数据
    print("1. 创建测试数据...")
    df = create_test_data(1000)
    print(f"   测试数据形状: {df.shape}")
    print(f"   数据列: {df.columns.tolist()}")

    # 2. 加载tokenizer
    print("\n2. 加载tokenizer...")
    tokenizer_path = "lightning_logs_tokenized/tokenizer_quantile_100.pkl"
    try:
        tokenizer = load_tokenizer(tokenizer_path)
        print(f"   ✅ Tokenizer加载成功")
    except Exception as e:
        print(f"   ❌ Tokenizer加载失败: {e}")
        return False

    # 3. 加载ONNX模型（如果存在）
    print("\n3. 查找ONNX模型...")
    import os
    import glob

    # 查找ONNX模型文件
    onnx_files = glob.glob("lightning_logs_tokenized/**/*.onnx", recursive=True)
    if not onnx_files:
        onnx_files = glob.glob("**/*.onnx", recursive=True)

    if onnx_files:
        onnx_path = onnx_files[0]
        print(f"   找到ONNX模型: {onnx_path}")

        try:
            model, device = load_onnx_model(onnx_path)
            print(f"   ✅ ONNX模型加载成功")
        except Exception as e:
            print(f"   ❌ ONNX模型加载失败: {e}")
            return False
    else:
        print("   ⚠️  未找到ONNX模型文件，跳过测试")
        return True

    # 4. 创建回测器
    print("\n4. 创建回测器...")
    try:
        backtester = BarTokenizedModelBacktester(
            model=model,
            tokenizer=tokenizer,
            initial_capital=10000.0,
            device=device,
            signal_type='threshold',
            leverage=1.0
        )
        print(f"   ✅ 回测器创建成功")
    except Exception as e:
        print(f"   ❌ 回测器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 5. 运行小规模回测
    print("\n5. 运行小规模回测...")
    try:
        # 使用较小的数据集进行测试
        test_df = df.head(100)  # 只使用前100条数据

        results = backtester.backtest(
            df=test_df,
            seq_len=30,
            commission=0.001,
            threshold=0.6,
            print_interval=10
        )

        print(f"   ✅ 回测完成")
        print(f"   初始资金: {results['initial_capital']:,.2f}")
        print(f"   最终权益: {results['final_equity']:,.2f}")
        print(f"   总收益率: {results['total_return']:.2%}")
        print(f"   交易次数: {len(results['trades'])}")

        return True

    except Exception as e:
        print(f"   ❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_backtest_fix()
    if success:
        print("\n🎉 测试成功！回测器修复有效。")
    else:
        print("\n❌ 测试失败！需要进一步调试。")
