
import os
import random
# from tqdm.notebook import tqdm

import numpy as np
import pandas as pd
pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 500)
pd.set_option('display.width', 1000)

# import matplotlib.pyplot as plt
# import seaborn as sns

from pyqlab.rl.data.utils import calc_5min_tick_features

from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder

# import datetime
import warnings
warnings.filterwarnings("ignore")

class MarkDataDataset():

    def __init__(self, data_path, version="v1", mode='train', max_pnl=10000):

        self.data_path = data_path
        self.max_pnl = max_pnl
        self.version = version
        if version == "v1":
            self.col_names = [
                # RSI
                "FAST_RSI_ZSCORE", "FAST_RSI_STDDEV", "FAST_RSI_DIRECT",
                # "SLOW_RSI_ZSCORE", "SLOW_RSI_STDDEV", "SLOW_RSI_DIRECT",
                # MOM
                "FAST_MOM_ZSCORE", "FAST_MOM_STDDEV", "FAST_MOM_DIRECT",
                # "SLOW_MOM_ZSCORE", "SLOW_MOM_STDDEV", "SLOW_MOM_DIRECT",
                # FLRS
                "FAST_FLRS_ZSCORE", "FAST_FLRS_STDDEV", "FAST_FLRS_DIRECT",
                # "SLOW_FLRS_ZSCORE", "SLOW_FLRS_STDDEV", "SLOW_FLRS_DIRECT",
                # MLRS
                "FAST_MLRS_ZSCORE", "FAST_MLRS_STDDEV", "FAST_MLRS_DIRECT",
                # "SLOW_MLRS_ZSCORE", "SLOW_MLRS_STDDEV", "SLOW_MLRS_DIRECT",
                # NATR
                "FAST_NATR_ZSCORE", "FAST_NATR_STDDEV", "FAST_NATR_DIRECT",
                # "SLOW_NATR_ZSCORE", "SLOW_NATR_STDDEV", "SLOW_NATR_DIRECT",
            ]
        elif version == "v2":
            self.col_names = [
                # RSI
                "FAST_RSI_ZSCORE", "FAST_RSI_DIRECT",
                "SLOW_RSI_ZSCORE", "SLOW_RSI_DIRECT",
                # MOM
                "FAST_MOM_ZSCORE", "FAST_MOM_DIRECT",
                "SLOW_MOM_ZSCORE", "SLOW_MOM_DIRECT",
                # FLRS
                "FAST_FLRS_ZSCORE", "FAST_FLRS_DIRECT",
                "SLOW_FLRS_ZSCORE", "SLOW_FLRS_DIRECT",
                # MLRS
                "FAST_MLRS_ZSCORE", "FAST_MLRS_DIRECT",
                "SLOW_MLRS_ZSCORE", "SLOW_MLRS_DIRECT",
                # NATR
                "FAST_NATR_ZSCORE", "FAST_NATR_DIRECT",
                "SLOW_NATR_ZSCORE", "SLOW_NATR_DIRECT",
            ]
        elif version == "v3":
            self.col_names = [
                # RSI
                "FAST_RSI_ZSCORE", "FAST_RSI_STDDEV", "FAST_RSI_DIRECT",
                "SLOW_RSI_ZSCORE", "SLOW_RSI_STDDEV", "SLOW_RSI_DIRECT",
                # MOM
                "FAST_MOM_ZSCORE", "FAST_MOM_STDDEV", "FAST_MOM_DIRECT",
                "SLOW_MOM_ZSCORE", "SLOW_MOM_STDDEV", "SLOW_MOM_DIRECT",
                # FLRS
                "FAST_FLRS_ZSCORE", "FAST_FLRS_STDDEV", "FAST_FLRS_DIRECT",
                "SLOW_FLRS_ZSCORE", "SLOW_FLRS_STDDEV", "SLOW_FLRS_DIRECT",
                # MLRS
                "FAST_MLRS_ZSCORE", "FAST_MLRS_STDDEV", "FAST_MLRS_DIRECT",
                "SLOW_MLRS_ZSCORE", "SLOW_MLRS_STDDEV", "SLOW_MLRS_DIRECT",
                # NATR
                "FAST_NATR_ZSCORE", "FAST_NATR_STDDEV", "FAST_NATR_DIRECT",
                "SLOW_NATR_ZSCORE", "SLOW_NATR_STDDEV", "SLOW_NATR_DIRECT",
            ]
        else:
            print(f"不支持的版本：{version}")
        
        self.mode = mode
        self.df = None

    def load(self, split_percent=0.8):
        self.df = pd.read_csv(f"{self.data_path}")
        self.df = self.df[['instrument', 'datetime', 'direct', 'pnl'] + self.col_names]
        # sel        return self.len()
        rows = self.df.shape[0]
        self.df = self.df.loc[(self.df["pnl"] > -self.max_pnl) & (self.df["pnl"] < self.max_pnl)]
        # if self.version == "v1":
        #     self.df[self.col_names] = self.df[self.col_names].astype(np.int32)
        # elif self.version == "v2":
        for col in self.col_names:
            self.df = self.df.loc[(self.df[col] >= -20.0) & (self.df[col] <= 20.0)]
        self.df[self.col_names] = self.df[self.col_names].astype(np.float32)
        print(f"\n=== remain rows:{self.df.shape[0]} , filtering rows: {rows - self.df.shape[0]} ===\n")

    def len(self):
        return len(self.df) if self.df is not None else 0

    def getitem(self, idx):
        # assert idx < len(self.df), "Index out of range"
        assert self.df is not None, "Dataset not loaded"
        pos_side = self.df.iloc[idx]['direct']
        pnl = self.df.iloc[idx]['pnl']
        mk = self.df.iloc[idx][self.col_names]

        return pos_side, pnl, mk.values.astype(np.int32) if self.version == "v1" else mk.values.astype(np.float32)



