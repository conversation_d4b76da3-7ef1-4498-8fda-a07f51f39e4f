import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer

def create_test_data(n_samples=100):
    """创建测试数据"""
    dates = pd.date_range(start='2023-01-01', periods=n_samples, freq='D')
    
    # 创建一个简单的价格序列
    close = 100 + np.cumsum(np.random.normal(0, 1, n_samples))
    open_price = close * (1 + np.random.normal(0, 0.01, n_samples))
    high = np.maximum(close, open_price) * (1 + np.abs(np.random.normal(0, 0.01, n_samples)))
    low = np.minimum(close, open_price) * (1 - np.abs(np.random.normal(0, 0.01, n_samples)))
    volume = np.random.randint(1000, 10000, n_samples)
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': open_price,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    })
    
    return df

def test_tokenizer():
    """测试tokenizer"""
    # 创建测试数据
    df = create_test_data()
    
    # 创建tokenizer
    tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        atr_window=20,
        scale=10
    )
    
    # 打印tokenizer信息
    print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")
    
    # 测试tokenize方法
    print("\n测试tokenize方法:")
    tokens = tokenizer.tokenize(df)
    print(f"生成的token数量: {len(tokens)}")
    print(f"前10个token: {tokens[:10]}")
    
    # 测试tokens_to_candlesticks方法
    print("\n测试tokens_to_candlesticks方法:")
    atr = tokenizer._calculate_atr(df).iloc[-1]
    start_price = df['close'].iloc[-1]
    reconstructed_df = tokenizer.tokens_to_candlesticks(tokens[-10:], start_price, atr)
    
    # 打印重建的K线数据
    print("\n重建的K线数据:")
    print(reconstructed_df)
    
    # 可视化原始数据和重建的数据
    tokenizer.visualize_tokenization(df.iloc[-20:], tokens[-10:], reconstructed_df)
    
    return tokenizer, df, tokens, reconstructed_df

if __name__ == "__main__":
    test_tokenizer()
