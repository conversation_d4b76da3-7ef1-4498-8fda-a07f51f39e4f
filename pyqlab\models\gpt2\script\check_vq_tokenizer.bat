@REM 验证码本的有效性
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2

@REM python vq_tokenizer.py ^
@REM --data_file f:/hqdata/fut_top_min1.parquet ^
@REM --method atr_based ^
@REM --num_samples 200

python vq_tokenizer.py ^
--validate_onnx ^
--encoder_onnx_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250517\vqcb_atr_based_fut_top_min1_1024_0.0810_encoder.onnx ^
--decoder_onnx_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250517\vqcb_atr_based_fut_top_min1_1024_0.0810_decoder.onnx ^
--method atr_based ^
--num_samples 200