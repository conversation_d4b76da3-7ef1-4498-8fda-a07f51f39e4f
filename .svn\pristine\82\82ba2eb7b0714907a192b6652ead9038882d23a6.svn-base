import re
import glob
import os
import time
from pprint import pprint

from ray import train, tune
from ray.rllib.algorithms.algorithm_config import AlgorithmConfig
from ray.rllib.algorithms.callbacks import DefaultCallbacks
from ray.rllib.utils.metrics import (
    ENV_RUNNER_RESULTS,
    EPISODE_RETURN_MEAN,
)
from ray.rllib.utils.test_utils import (
    add_rllib_example_script_args,
    check_learning_achieved,
)
from ray.tune.registry import get_trainable_cls, register_env
from ray.air.integrations.wandb import WandbLoggerCallback
from argparse import ArgumentParser
from pyqlab.rl.env.env_market_timing_v2 import MarketTimingEnv

def export_onnx(block_name: str, version: str, checkpoint_path: str):
    import os
    from datetime import datetime
    from time import time
    import torch
    from ray.rllib.algorithms.ppo import PPO, PPOConfig

    # 加载训练好的模型
    print(f"加载模型 {checkpoint_path}")
    algo = PPO.from_checkpoint(checkpoint=checkpoint_path, )
    policy = algo.get_policy()

    # 获取模型的输入和输出
    if version == "v1":
        dummy_input = torch.zeros((1, 15), dtype=torch.float32)
    elif version == "v2":
        dummy_input = torch.zeros((1, 20), dtype=torch.float32)
    elif version == "v3":
        dummy_input = torch.zeros((1, 30), dtype=torch.float32)
    else:
        raise ValueError(f"version {version} is not supported")

    # 导出模型为 ONNX 格式
    # onnx_path = "E:/lab/RoboQuant/pylab/model_rl/markettiming_ppo_v1.onnx"
    result_pytorch, _ = policy.model({
        "obs": dummy_input,
    })

    # Evaluate tensor to fetch numpy array
    result_pytorch = result_pytorch.detach().numpy()

    # This line will export the model to ONNX.
    outdir = "E:/lab/RoboQuant/pylab/model"
    policy.export_model(outdir, onnx=18)

    tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
    model_name = f"{outdir}/mktm_ppo_{block_name}_{tm_str}_{version}.onnx"
    # 如果文件存在，则删除
    if os.path.exists(model_name):
        os.remove(model_name)
    os.rename(f"{outdir}/model.onnx", model_name)

    print(f"模型已成功导出为 {outdir}")

def export_onnx_v2(block_name: str, version: str, checkpoint_path: str):
    import torch
    from datetime import datetime
    from time import time
    # Restore the best checkpoint
    algo = config.algo_class(config=config)
    algo.restore(checkpoint_path)
    
    # Export the model to ONNX format
    outdir = "E:/lab/RoboQuant/pylab/model"
    tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
    model_name = f"mktm_ppo_{block_name}_{tm_str}_{version}.onnx"
    policy = algo.get_policy()
    input_dict = policy.model.get_input_dict_for_inference()
    torch.onnx.export(policy.model, 
                      (input_dict,), 
                      model_name, 
                      export_params=True, 
                      opset_version=18, 
                      do_constant_folding=True,
                      input_names=['obs'], 
                      output_names=['output'], 
                      dynamic_axes={'obs' : {0 : 'batch_size'}, 
                                    'output' : {0 : 'batch_size'}})
    
    print(f"Model exported to {model_name}")   

class CrashAfterNIters(DefaultCallbacks):
    """Callback that makes the algo crash after a certain avg. return is reached."""

    def __init__(self):
        super().__init__()
        # We have to delay crashing by one iteration just so the checkpoint still
        # gets created by Tune after(!) we have reached the trigger avg. return.
        self._should_crash = False

    def on_train_result(self, *, algorithm, metrics_logger, result, **kwargs):
        # We had already reached the mean-return to crash, the last checkpoint written
        # (the one from the previous iteration) should yield that exact avg. return.
        if self._should_crash:
            raise RuntimeError("Intended crash after reaching trigger return.")
        # Reached crashing criterion, crash on next iteration.
        elif result[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward:
            print(
                "Reached trigger return of "
                f"{result[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]}"
            )
            self._should_crash = True

class ValidationCallback(DefaultCallbacks):
    def __init__(self):
        super().__init__()
        self.best_val_return = -float("inf")
        self.no_improvement_count = 0
        self.patience = 50  # 早停耐心值
        
    def on_train_result(self, *, algorithm, result, **kwargs):
        # 每N个训练迭代执行一次验证
        if result["training_iteration"] % 10 == 0:
            # 切换到验证模式
            algorithm.workers.foreach_worker(
                lambda w: w.foreach_env(
                    lambda env: setattr(env, "mode", "validation")
                )
            )
            
            # 执行验证
            val_results = algorithm.evaluate()
            val_return = val_results["evaluation"]["episode_reward_mean"]
            
            # 检查是否有改善
            if val_return > self.best_val_return:
                self.best_val_return = val_return
                self.no_improvement_count = 0
            else:
                self.no_improvement_count += 1
                
            # 早停检查
            if self.no_improvement_count >= self.patience:
                print(f"Early stopping triggered! No improvement for {self.patience} validations")
                return True
                
            # 切回训练模式
            algorithm.workers.foreach_worker(
                lambda w: w.foreach_env(
                    lambda env: setattr(env, "mode", "train")
                )
            )
            
def trainer(config: AlgorithmConfig):
    # TODO: 设置自定义优化算法
    config.training(
        optimizer={
            "type": "SGD",
            "lr": 0.01,
            "momentum": 0.9
        }
    )
    
    # Tune config.
    # Need a WandB callback?
    tune_callbacks = []
    if args.wandb_key:
        project = args.wandb_project or (
            args.algo.lower() + "-" + re.sub("\\W+", "-", str(config.env).lower())
        )
        tune_callbacks.append(
            WandbLoggerCallback(
                api_key=args.wandb_key,
                project=args.wandb_project,
                upload_checkpoints=False,
                **({"name": args.wandb_run_name} if args.wandb_run_name else {}),
            )
        )

    # Define some stopping criterion
    stop = {
        f"{ENV_RUNNER_RESULTS}/{EPISODE_RETURN_MEAN}": args.stop_reward,
        "training_iteration": args.training_iteration,
    }

    # Run tune for some iterations and generate checkpoints.
    tuner = tune.Tuner(
        trainable=config.algo_class,
        param_space=config,
        run_config=train.RunConfig(
            storage_path="E:/lab/RoboQuant/pylab/ray_results",
            callbacks=tune_callbacks,
            checkpoint_config=train.CheckpointConfig(
                checkpoint_frequency=args.checkpoint_freq,
                checkpoint_at_end=args.checkpoint_at_end,
            ),
            stop=stop,
        ),
    )
    tuner_results = tuner.fit()
    
    
    # 如果有完成的trials，使用原来的逻辑
    metric = f"{ENV_RUNNER_RESULTS}/{EPISODE_RETURN_MEAN}"
    best_result = tuner_results.get_best_result(
        metric=metric,
        mode="max"
    )

    result = best_result.metrics[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]
    if result == 0.0:
        print("Warning: Could not find reward metric in results")
    
    if not best_result.checkpoint:
        print("Warning: Best result has no checkpoint")
        return None, result
        
    print(f"Best result: {result}")
    return best_result.checkpoint, result

def retrain_from_checkpoint(config: AlgorithmConfig, checkpoint_path: str):
    # - Change our config, such that the restored algo will have an env on the local
    # EnvRunner (to perform evaluation) and won't crash anymore (remove the crashing
    # callback).
    config.callbacks(None)
    # Rebuild the algorithm (just for testing purposes).
    test_algo = config.build()
    # Load algo's state from best checkpoint.
    test_algo.restore(checkpoint_path) # best_result.checkpoint
    # Perform some checks on the restored state.
    assert test_algo.training_iteration > 0
    # Evaluate on the restored algorithm.
    test_eval_results = test_algo.evaluate()
    # assert (test_eval_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward), test_eval_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]
    # Train one iteration to make sure, the performance does not collapse (e.g. due
    # to the optimizer weights not having been restored properly).
    test_results = test_algo.train()
    # assert (test_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward), test_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]
    # Stop the test algorithm again.
    test_algo.stop()

def continue_training(config: AlgorithmConfig, experiment_path: str):
    # Create a new Tuner from the existing experiment path (which contains the tuner's
    # own checkpoint file). Note that even the WandB logging will be continued without
    # creating a new WandB run name.
    restored_tuner = tune.Tuner.restore(
        path= experiment_path, # tuner_results.experiment_path,
        trainable=config.algo_class,
        # param_space=config,
        # Important to set this to True b/c the previous trial had failed (due to our
        # `CrashAfterNIters` callback).
        resume_errored=True,
    )
    # Continue the experiment exactly where we left off.
    tuner_results = restored_tuner.fit()
    
    metric = f"{ENV_RUNNER_RESULTS}/{EPISODE_RETURN_MEAN}"
    best_result = tuner_results.get_best_result(metric=metric, mode="max")
    # assert best_result.metrics[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward

    # Not sure, whether this is really necessary, but we have observed the WandB
    # logger sometimes not logging some of the last iterations. This sleep here might
    # give it enough time to do so.
    # time.sleep(20)

    if args.as_test:
        check_learning_achieved(tuner_results, args.stop_reward, metric=metric)
        
    return best_result.checkpoint, best_result.metrics[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]


if __name__ == "__main__":
    # parser = ArgumentParser()
    parser = add_rllib_example_script_args(
        default_reward=2000.0,
        default_timesteps=10000000,
        default_iters=2000
    )

    # parser.add_argument("--algo", type=str, default="PPO")
    # parser.add_argument("--enable-new-api-stack", action="store_true")
    # parser.add_argument("--stop-reward-crash", type=float, default=200.0)
    # parser.add_argument("--stop-reward", type=float, default=50000.0)
    # parser.add_argument("--checkpoint-freq", type=int, default=10)
    # parser.add_argument("--checkpoint-at-end", action="store_true")
    # parser.add_argument("--wandb-key", type=str, default="****************************************")
    # parser.add_argument("--wandb-project", type=str, default="market-timing")
    # parser.add_argument("--wandb-run-name", type=str, default="markdays")
    # parser.add_argument("--as-test", action="store_true")

    parser.add_argument("--stop_reward_crash", type=float, default=1000)

    parser.add_argument("--training_iteration", type=int, default=10000)
    parser.add_argument("--num_env_runners", type=int, default=8)

    parser.add_argument("--block_name", type=str, default="fut", help="market block name")
    parser.add_argument("--version", type=str, default="v2", help="model version")
    parser.add_argument("--data_file", type=str, default="", help="data file")
    parser.add_argument("--run_mode", type=str, default="train")
    parser.add_argument("--initial_amount", type=int, default=1e7, help="initial amount")

    parser.add_argument("--train_test_split", type=float, default=0.8, help="train test split")
    parser.add_argument("--validation_split", type=float, default=0.1, help="validation split")
    parser.add_argument("--sample_length", type=int, default=300, help="sample length")
    parser.add_argument("--random_episode", type=bool, default=True, help="random episode")

    parser.add_argument("--max_drawdown", type=float, default=0.5, help="max drawdown")
    parser.add_argument("--stop_loss", type=float, default=0.3, help="stop loss")
    parser.add_argument("--transaction_cost", type=float, default=0.0001, help="transaction cost")

    # By default, set `args.checkpoint_freq` to 1 and `args.checkpoint_at_end` to True.
    parser.set_defaults(
        checkpoint_freq=200,
        checkpoint_at_end=True,
        run_mode="train",
        num_env_runners=8,
    )

    args = parser.parse_args()

    print("========================Arguments========================")
    pprint(vars(args))
    print("========================Arguments========================")

    register_env("markettiming_env", lambda env_config: MarketTimingEnv(env_config))
    env_config = {
        "name": "MarketTimingEnv",
        "version": args.version,
        "initial_amount": args.initial_amount, # 1e7,
        "gamma": 0.98,
        "mode": "train",
        "split_percent": 0.9,
        "data_path": "E:/lab/RoboQuant/pylab/data",
        "data_file": args.data_file,
    }
    # Simple generic config.
    config = (
        get_trainable_cls(args.algo)
        .get_default_config()
        .api_stack(
            enable_rl_module_and_learner=False, # ONNX is not supported by RLModule API yet.
            enable_env_runner_and_connector_v2=args.enable_new_api_stack,
        )
        .environment("markettiming_env", env_config=env_config)
        .env_runners(
            create_env_on_local_worker=True,
            num_env_runners=args.num_env_runners,
        )
        .training(lr=0.0001)
        .callbacks(CrashAfterNIters)
    )
    
    experiment_path = "E:/lab/RoboQuant/pylab/ray_results/PPO_2025-01-02_23-07-59"
    best_checkpoint = R"E:\lab\RoboQuant\pylab\ray_results\PPO_2025-01-02_23-07-59\PPO_markettiming_env_5f110_00000_0_2025-01-02_23-08-06\checkpoint_000019"

    if args.run_mode == "train":
        best_checkpoint, best_avg_return = trainer(config)
        print(f"Best checkpoint: {best_checkpoint}")
        print(f"Best avg. return: {best_avg_return}")
        export_onnx(args.block_name, args.version, best_checkpoint)
    elif args.run_mode == "retrain_from_checkpoint":
        retrain_from_checkpoint(config, best_checkpoint)
    elif args.run_mode == "continue_training":
        # experiment_path = "C:/Users/<USER>/ray_results/PPO_2024-08-29_09-44-41"
        best_checkpoint, best_avg_return = continue_training(config, experiment_path)
        print(f"Best checkpoint: {best_checkpoint}")
        print(f"Best avg. return: {best_avg_return}")
        export_onnx(args.block_name, args.version, best_checkpoint)
    else:
        raise ValueError("Invalid run mode.")
