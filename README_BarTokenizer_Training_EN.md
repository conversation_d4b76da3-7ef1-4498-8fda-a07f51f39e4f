# Training BarGpt4 Model with High-Quality Tokens from BarTokenizer

## 🎯 Project Objective

This project aims to use high-quality tokens generated by BarToken<PERSON> to train the BarGpt4 model, solving the prediction concentration problem caused by sample imbalance. By testing with min1 data, we verify whether BarTokenizer can effectively improve model prediction diversity.

## 🏗️ Project Architecture

### Core Components

1. **BarTokenizer** - High-quality token generator
   - Supports multiple mapping strategies (linear, quantile, adaptive)
   - Built-in frequency balancing mechanism
   - Transparent and interpretable tokenization process

2. **BarTokenizedDataset** - Specialized dataset class
   - Integrated BarTokenizer processing pipeline
   - Automatic handling of token distribution imbalance
   - Detailed distribution analysis

3. **BarGpt4** - Improved GPT model
   - Supports balanced loss functions
   - Built-in prediction diversity analysis
   - Optimized training and inference modes

## 📁 File Structure

```
├── pyqlab/data/dataset/dataset_bar_tokenized.py  # Specialized dataset class
├── train_bar_gpt4_with_tokenizer_en.py           # Main training script (English)
├── prepare_min1_data.py                          # Data preprocessing script
├── quick_test_en.py                              # Quick integration test (English)
├── train_bar_gpt4_tokenizer_en.bat               # Batch training script (English)
└── README_BarTokenizer_Training_EN.md            # This document
```

## 🚀 Quick Start

### 1. Environment Setup

Ensure the following dependencies are installed:
```bash
pip install torch pytorch-lightning pandas numpy scikit-learn
```

### 2. Integration Test

First run the integration test to ensure all components work properly:
```bash
python quick_test_en.py --data_file your_data.parquet
```

Expected output:
```
🎉 All tests passed! Ready for formal training.
```

### 3. Data Preparation

Prepare min1 period candlestick data:
```bash
python prepare_min1_data.py
```

This will:
- Load raw data
- Clean and validate data quality
- Test BarTokenizer functionality
- Save in parquet format

### 4. Start Training

#### Method 1: Use batch script (Recommended)
```bash
train_bar_gpt4_tokenizer_en.bat
```

This will run three comparison experiments:
1. quantile mapping + frequency balancing
2. adaptive mapping + frequency balancing  
3. quantile mapping + no balancing

#### Method 2: Manual execution
```bash
python train_bar_gpt4_with_tokenizer_en.py \
  --data_file f:/hqdata/fut_top_min1.parquet \
  --mapping_strategy quantile \
  --balancing_strategy frequency \
  --n_bins 100 \
  --max_token_frequency 0.08 \
  --gini_threshold 0.6 \
  --block_size 30 \
  --n_layer 4 \
  --n_head 8 \
  --d_model 128 \
  --batch_size 64 \
  --lr 1e-4 \
  --max_epochs 8 \
  --k_folds 3 \
  --use_class_weights
```

## 📊 Key Parameters

### BarTokenizer Parameters

- `mapping_strategy`: Mapping strategy
  - `quantile`: Quantile mapping (recommended)
  - `adaptive`: Adaptive mapping
  - `linear`: Linear mapping

- `balancing_strategy`: Balancing strategy
  - `frequency`: Frequency balancing (recommended)
  - `none`: No balancing

- `n_bins`: Number of tokens, affects vocabulary size
- `max_token_frequency`: Max frequency per token (0.08 recommended)
- `gini_threshold`: Gini coefficient threshold (0.6 recommended)

### Model Parameters

- `block_size`: Sequence length (30 recommended)
- `n_layer`: Number of transformer layers (4 recommended)
- `n_head`: Number of attention heads (8 recommended)
- `d_model`: Model dimension (128 recommended)

### Training Parameters

- `batch_size`: Batch size (64 recommended)
- `lr`: Learning rate (1e-4 recommended)
- `k_folds`: Number of cross-validation folds (3 recommended)
- `use_class_weights`: Whether to use class weights

## 📈 Evaluation Metrics

### Primary Focus Metrics

1. **Prediction Diversity** (`val_diversity`)
   - Number of unique predicted tokens / Total vocabulary size
   - Target: > 10%

2. **Max Prediction Frequency**
   - Frequency of most common predicted token
   - Target: < 30%

3. **Validation Loss** (`val_loss`)
   - Model loss on validation set
   - Target: Continuous decrease

### Success Criteria

The prediction concentration problem is considered solved if:
- Average prediction diversity > 10%
- Average max prediction frequency < 30%
- Validation loss converges stably

## 🔍 Results Analysis

### View Training Logs

Use TensorBoard to view training process:
```bash
tensorboard --logdir lightning_logs_tokenized
```

### Key Output Information

During training, the following will be output:
```
Token Distribution Analysis:
  Gini coefficient: 0.3659
  Normalized entropy: 0.9522
  Coefficient of variation: 0.6508
  Number of unique tokens: 95

=== Prediction Analysis ===
Unique predicted tokens: 45 / 150
Unique target tokens: 95 / 150
Prediction diversity ratio: 30.00%
Max prediction frequency: 15.20%
✅ Prediction distribution is relatively uniform
```

## 🛠️ Troubleshooting

### Common Issues

1. **Out of Memory**
   - Reduce `batch_size`
   - Reduce `block_size`
   - Reduce `d_model`

2. **Slow Training**
   - Increase `num_workers`
   - Use GPU training
   - Reduce dataset size

3. **Predictions Still Concentrated**
   - Lower `max_token_frequency`
   - Try different `mapping_strategy`
   - Increase `n_bins`

4. **Loss Not Converging**
   - Adjust learning rate `lr`
   - Increase `early_stop` patience
   - Check data quality

### Debugging Tools

Use dataset debugging features:
```python
# View sample information
dataset.print_sample_info(0)

# Get distribution statistics
stats = dataset.get_distribution_stats()
print(stats)

# Get class weights
weights = dataset.get_class_weights()
```

## 📝 Experiment Log

Recommend recording the following information:

| Experiment | Mapping Strategy | Balancing Strategy | Gini Coefficient | Prediction Diversity | Max Frequency | Validation Loss |
|------------|------------------|-------------------|------------------|---------------------|---------------|-----------------|
| 1          | quantile         | frequency         | 0.36             | 30%                 | 15%           | 0.85            |
| 2          | adaptive         | frequency         | 0.42             | 25%                 | 20%           | 0.92            |
| 3          | quantile         | none              | 0.78             | 8%                  | 65%           | 1.15            |

## 🎯 Next Steps

1. **Extend to Other Periods**
   - Test min5, min15 data
   - Compare effects across different periods

2. **Model Optimization**
   - Try larger models
   - Experiment with different architectures

3. **Strategy Improvement**
   - Develop new balancing strategies
   - Optimize token combination methods

4. **Production Deployment**
   - Export ONNX models
   - Integrate into backtesting systems

## 📞 Support

If you encounter issues, please check:
1. Run `quick_test_en.py` to ensure environment is normal
2. Check error messages in training logs
3. Verify data file format and path
4. Confirm sufficient GPU/CPU resources

## 🌟 Key Innovations

1. **First integration of BarTokenizer into deep learning training pipeline**
2. **Innovative data balancing strategy specifically for financial time series data**
3. **Complete prediction diversity evaluation system**
4. **Transparent and interpretable tokenization process**

This solution provides a complete, reliable toolchain for solving sample imbalance problems in financial AI models, particularly suitable for processing and modeling high-frequency trading data.
