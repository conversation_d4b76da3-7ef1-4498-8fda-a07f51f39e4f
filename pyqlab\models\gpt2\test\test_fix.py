import sys
import os
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入回测器和相关模块
from pyqlab.models.gpt2.backtester import CandlestickLLMBacktester
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer


class MockModel(torch.nn.Module):
    """用于测试的模拟模型"""
    def __init__(self, vocab_size, trend='up'):
        super().__init__()
        self.vocab_size = vocab_size
        self.trend = trend  # 'up', 'down', 'random'

    def forward(self, input_tokens, code_ids, time_features=None, targets=None):
        batch_size, seq_len = input_tokens.size()

        # 创建logits
        logits = torch.zeros(batch_size, seq_len, self.vocab_size)

        # 根据趋势设置概率
        if self.trend == 'up':
            # 偏向上涨的token
            logits[:, :, :] = 1.0  # 基础概率
            # 增加上涨token的概率
            for i in range(min(100, self.vocab_size)):  # 只处理前100个token，避免处理太多
                logits[:, :, i] = 10.0 if i % 3 == 0 else 1.0  # 模拟上涨token
        elif self.trend == 'down':
            # 偏向下跌的token
            logits[:, :, :] = 1.0  # 基础概率
            # 增加下跌token的概率
            for i in range(min(100, self.vocab_size)):  # 只处理前100个token，避免处理太多
                logits[:, :, i] = 10.0 if i % 3 == 1 else 1.0  # 模拟下跌token
        else:
            # 随机趋势
            logits = torch.randn(batch_size, seq_len, self.vocab_size)

        # 返回logits和空损失
        return logits, None


def generate_mock_data(n_samples=100, trend='up', volatility=0.01, start_price=100.0):
    """生成模拟的K线数据"""
    # 生成日期序列
    dates = [datetime.now() - timedelta(days=n_samples-i) for i in range(n_samples)]

    # 生成价格序列
    prices = []
    price = start_price

    for i in range(n_samples):
        # 根据趋势生成价格变动
        if trend == 'up':
            change = np.random.normal(0.001, volatility)
        elif trend == 'down':
            change = np.random.normal(-0.001, volatility)
        else:
            change = np.random.normal(0, volatility)

        price *= (1 + change)
        prices.append(price)

    # 生成OHLC数据
    df = pd.DataFrame({
        'datetime': dates,
        'close': prices,
    })

    # 生成open, high, low
    df['open'] = df['close'].shift(1)
    df.loc[0, 'open'] = df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.01))

    for i in range(len(df)):
        high_range = np.random.uniform(0, 0.02)
        low_range = np.random.uniform(0, 0.02)
        df.loc[i, 'high'] = max(df.loc[i, 'open'], df.loc[i, 'close']) * (1 + high_range)
        df.loc[i, 'low'] = min(df.loc[i, 'open'], df.loc[i, 'close']) * (1 - low_range)

    # 生成交易量
    df['volume'] = np.random.randint(1000, 10000, size=len(df))

    return df


def main():
    """测试修复后的回测器"""
    print("创建 CandlestickTokenizer...")
    tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )

    print("生成模拟数据...")
    up_trend_data = generate_mock_data(n_samples=100, trend='up')
    down_trend_data = generate_mock_data(n_samples=100, trend='down')

    print("创建模拟模型...")
    up_model = MockModel(tokenizer.vocab_size, trend='up')
    down_model = MockModel(tokenizer.vocab_size, trend='down')

    # 不再需要设置tokenizer属性

    print("创建回测器...")
    up_backtester = CandlestickLLMBacktester(up_model, tokenizer)
    down_backtester = CandlestickLLMBacktester(down_model, tokenizer)

    print("执行上涨趋势回测...")
    up_results = up_backtester.backtest(
        up_trend_data,
        seq_len=10,
        take_profit=0.1,  # 10%止盈
        stop_loss=0.05    # 5%止损
    )

    print("\n上涨趋势回测结果:")
    print(f"初始资金: ${up_results['initial_capital']:.2f}")
    print(f"最终权益: ${up_results['final_equity']:.2f}")
    print(f"总收益率: {up_results['total_return']:.2%}")
    print(f"年化收益率: {up_results['annual_return']:.2%}")
    print(f"夏普比率: {up_results['sharpe_ratio']:.2f}")
    print(f"最大回撤: {up_results['max_drawdown']:.2%}")
    print(f"胜率: {up_results['win_rate']:.2%}")
    print(f"盈亏比: {up_results['profit_loss_ratio']:.2f}")
    print(f"交易次数: {len(up_results['trades'])}")

    print("\n执行下跌趋势回测...")
    down_results = down_backtester.backtest(
        down_trend_data,
        seq_len=10,
        take_profit=0.1,  # 10%止盈
        stop_loss=0.05    # 5%止损
    )

    print("\n下跌趋势回测结果:")
    print(f"初始资金: ${down_results['initial_capital']:.2f}")
    print(f"最终权益: ${down_results['final_equity']:.2f}")
    print(f"总收益率: {down_results['total_return']:.2%}")
    print(f"年化收益率: {down_results['annual_return']:.2%}")
    print(f"夏普比率: {down_results['sharpe_ratio']:.2f}")
    print(f"最大回撤: {down_results['max_drawdown']:.2%}")
    print(f"胜率: {down_results['win_rate']:.2%}")
    print(f"盈亏比: {down_results['profit_loss_ratio']:.2f}")
    print(f"交易次数: {len(down_results['trades'])}")

    print("\n比较策略...")
    up_backtester.compare_strategies(
        [up_results, down_results],
        names=['上涨策略', '下跌策略']
    )

    print("\n测试完成!")


if __name__ == '__main__':
    main()
