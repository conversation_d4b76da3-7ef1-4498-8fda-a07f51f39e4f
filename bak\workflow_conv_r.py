
# coding=utf-8
# CONV1D结果预测版

import qlib
from qlib.config import REG_CN
from qlib.utils import init_instance_by_config, flatten_dict
from qlib.workflow import R

from pyqlab.data.dataset.handler import DataHandlerAHF
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import ipywidgets as widgets
import copy
import json
from pprint import pprint
from time import time
from datetime import datetime
from argparse import ArgumentParser

from pyqlab.pl.data_api import get_dataset, get_model_name, get_win_size

import warnings
warnings.filterwarnings("ignore")

provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
qlib.init(provider_uri=provider_uri, region=REG_CN)

###################################
# train model
###################################
SEL_LONG_FACTOR_NAMES = [ # Slow period factor
    # "MACD", "MACD_DIFF", "MACD_DEA", "MOM", "RSI",
    # "NEW_CHANGE_PERCENT", 
    # "LR_SLOPE_FAST",
    # "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    # "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    # "SQUEEZE_ZERO_BARS", 
    # "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    # "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    # "BAND_POSITION", "BAND_WIDTH",
    # "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    # "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    # "TREND_LEVEL"
]

SEL_SHORT_FACTOR_NAMES = [ # Fast period factor
    # "VOLUME", # 在RangeBar下，Volume是Bar的时长seconds
    "NEW_CHANGE_PERCENT",

    # "AROON_UP", "AROON_DOWN",
    "MACD", "MACD_DIFF", "MACD_DEA", "RSI",
    "SQUEEZE_NARROW_BARS", "SQUEEZE_ZERO_BARS",

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]
SEL_MAIN_FACTOR_NAMES = SEL_SHORT_FACTOR_NAMES
SEL_CONTEXT_FACTOR_NAMES = [
  "STDDEV_RNG", "SHORT_RANGE",
  "FAST_QH_RSI", "FAST_QH_ZSCORE",
  "FAST_QH_NATR", "FAST_QH_NATR_ZSCORE",
  "FAST_QH_MOM", "FAST_QH_MOM_ZSCORE",
  # "FAST_QH_DIRECT", "FAST_QH_NATR_DIRECT", "FAST_QH_MOM_DIRECT",
  "DAYOFWEEK", "HOUR"
]

"""
根据当前测试的结果来看，主要有以下结论：
1.训练的数据越多越好，如何仅用2023年的数据来训练，那么测试的结果就不好
2.CONTEXT_FACTOR的因子对结果会造成噪点，最后不用
3.WIN_SIZE的大小对结果有影响，WIN_SIZE越大，结果越好 10 > 5
"""
IS_CLASS=False # 是否是分类问题
FACTOR_NUM=59
WIN_SIZE=5
# FUT_CODES=MAIN_FUT_CODES
FUT_CODES=MAIN_SEL_FUT_CODES
INS_NUMS=(0, 51, 51, 8)

# FUT_CODES=SF_FUT_CODES
# SEL_CONTEXT_FACTOR_NAMES = ["DAYOFWEEK", "HOUR"]
# INS_NUMS=(0, 51, 51, 0)

VERSION="2DR"
dsnames = {
    # ============================
    # FUT_CODES=MAIN_SEL_FUT_CODES
    # ============================
    '10HF': ['main.2023',],
    # -------------sf--------------
    # =============================
    # FUT_CODES=SF_FUT_CODES
    # =============================
    # '10HF_SF': ['sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],
}

filter_win = { #default=1
    '15HF': 5,
}

if VERSION == "1DR":
    SEL_MAIN_FACTOR_NAMES = []
    INS_NUMS=(0, 51, 0, 8)

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": ['2020',],
    "kwargs": {
        "win": WIN_SIZE,                # 采样窗口,与下面的num_channel通道数保持一致
        "step": 1,                      # 采样步长，通常为1
        "filter_win": 1,                # 是否过滤掉特征数据
        "is_class": IS_CLASS,           # 是否是分类问题
        "is_filter_extreme": False,     # 是否过滤极端值
        "is_normal": True,              # 是否归一化
        "verbose": False,               # 是否打印日志
        "direct": "ls",
        "sel_lf_names": SEL_LONG_FACTOR_NAMES,
        "sel_sf_names": SEL_SHORT_FACTOR_NAMES,
        "sel_mf_names": SEL_MAIN_FACTOR_NAMES,
        "sel_ct_names": SEL_CONTEXT_FACTOR_NAMES,
        "ins_nums": INS_NUMS,
    },
    "data_loader": {
        "class": "AHFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "data_path": "e:/featdata",
            "train_codes": FUT_CODES,   # 选期货交易标的
        },
    },
}

dataset_config = {
    "class": "AHFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAHF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    },
}

task = {
    "model": {
        "class": "Conv1dModelPytorch",
        "module_path": "pyqlab.contrib.model.pytorch_conv_r",
        "kwargs": {
            "version": VERSION,
            "loss": "mse",
            "num_code": [72],
            "num_channel": WIN_SIZE,    # 通道数,与上面的DataHander中的win保持一致
            "num_input": FACTOR_NUM,
            # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
            # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
            "out_channels": (16, 32, 800, 256), 
            # "out_channels": (24, 48, 1200, 256), 
            "ins_nums": INS_NUMS,
            "is_fcl": True,            # 是否使用全连接层
            "output_dim": 1,
            "dropout": 0.5,            # dropout >= 0.5
            "lr": 0.001,
            "lr_decay": 0.1,           # 学习率衰减典型的值可以在 0.1 到 0.5 之间
            "lr_decay_steps": 5,       # 典型的初始值可以在 10 到 100 之间
            "optimizer": "adam",
            "batch_size": 512,
            "GPU": 0,
            "n_epochs": 8,
            "early_stop": 4,           # 结束训练
            "best_cond": "loss",       # or loss
            # "best_model_path": "e:/lab/RoboQuant/pylab/model/CV2DR_10HF_800_11281632_ls.model",
        },
    },
}

def plt_show(df, title="", name=""):
    if df.empty:
        print("df is empty")
        return
    ylim = [df.min().min(), df.quantile(0.95).max()]
    ylim[0] -= (ylim[1] - ylim[0]) * 0.05
    df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)
    # plt.show()
    # 保存图形为文件
    plt.savefig(f'e:/lab/RoboQuant/pylab/mlab/plt/{name}.png')
    # 打开文件
    import os
    os.system(f'start e:/lab/RoboQuant/pylab/mlab/plt/{name}.png')

def display_result(evals_result, name: str):
    for key, val in evals_result.items():
        if not isinstance(val, dict):
            plt_show(pd.DataFrame(evals_result), key, name)
            break
        else:
            plt_show(pd.DataFrame(val), key, name)
            

def get_win_size(ds_name: str):
    if ds_name[:2] == "05":
        return 5
    elif ds_name[:2] == "10":
        return 10
    elif ds_name[:2] == "15":
        return 15
    else:
        return 0
    
def get_filter_win(ds_name: str):
    if ds_name in filter_win.keys():
        return filter_win[ds_name]
    else:
        return 0
    
def get_model_name(ds_name: str, direct: str):
    now = time()
    tm_str = datetime.fromtimestamp(now).strftime('%m%d%H%M')
    suffix = f"{ds_name}_{task['model']['kwargs']['out_channels'][2]}_{tm_str}"
    return f"CV{VERSION}_{suffix}_{direct}"
    
handler_class_config = {
            "class": "DataHandlerAHF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        }

def get_dataset(ds_name: str):
    if ds_name:
        if ds_name not in dsnames.keys():
            print(f"ds_name: {ds_name} not in pfs.keys() or pfs_win.keys()")
            return
        data_handler_config["instruments"] = dsnames[ds_name]

    hd: DataHandlerAHF = init_instance_by_config(handler_class_config)
    dataset_config["kwargs"]["handler"] = hd
    dataset = init_instance_by_config(dataset_config)
    dataset.setup_data(handler_kwargs=data_handler_config)
    return dataset, hd.ct_cat_num_embeds, len(hd.feat_names)


# 优化: datahandler可以重复使用，提高运行效率
# prepare processed data in memory.
def trainer(dataset, embeds, num_input, show=True, ds_name=None, reset_hp=True, train_result={}):
    dataset_size = []
    direct = 'ls'
    # if reset_hp:
    #     hparam_df=pd.read_csv("./data/HP_MLP.csv")
    #     if not hparam_df.empty:
    #         hparam_df.set_index("model_name", inplace=True)
    #         task["model"]["kwargs"]["dropout"]=hparam_df.loc[f"CONV1D_{ds_name}_{direct}", "dropout"]
    #         task["model"]["kwargs"]["lr"]=hparam_df.loc[f"MLP_{ds_name}_{direct}", "lr"]
    #         print(f'Set dropout: {task["model"]["kwargs"]["dropout"]}')
    #         print(f'Set lr: {task["model"]["kwargs"]["lr"]}')

    print("******************************")
    print(f"version: {VERSION}, ds_name: {ds_name}, num_input: {num_input}, win: {get_win_size(ds_name)}, filter_win: {get_filter_win(ds_name)}")
    print("******************************")

    # model initiaiton
    task["model"]["kwargs"]["num_embeds"] = embeds
    task["model"]["kwargs"]["num_input"] = num_input
    task["model"]["kwargs"]["num_channel"] = get_win_size(ds_name)
    model = init_instance_by_config(task["model"])

    # start exp to train model
    with R.start(experiment_name="train_model"):
        R.log_params(**flatten_dict(task))

        result={}
        x_data, y_data, encoded_data = dataset.prepare(
            ["train", "valid"],
            col_set=["feature", "label", "encoded"],
            data_key=None,  # DataHandlerLP.DK_L,
            direct=direct,
            win=get_win_size(ds_name),
            filter_win=get_filter_win(ds_name),
            is_class=IS_CLASS,
        )

        print(f"train data shape: {x_data.shape} {y_data.shape} {encoded_data.shape}")

        path="e:/lab/RoboQuant/pylab/model"
        model_name=get_model_name(ds_name, direct)
        model_path=f"{path}/{model_name}.model"

        best_epoch, best_loss = model.fit(
            x_data,
            y_data,
            encoded_data,
            evals_result=result,
            save_path=model_path,
        )
        train_result[model_name] = []
        train_result[model_name].append(best_epoch)
        train_result[model_name].append(round(best_loss, 4))

        dataset.save_model_inputs_config(f"{path}/{model_name}.json")

        # R.save_objects(trained_model=model)
        # rid = R.get_recorder().id
        # preds = np.array(model.predict(dataset, segment="valid"))
        # preds_stats = {}
        # preds_stats['mean'] = np.mean(preds)  # 计算均值
        # preds_stats['min_value'] = min(preds)  # 获取最小值
        # preds_stats['max_value'] = max(preds)  # 获取最大值
        # preds_stats['std_deviation'] = np.std(preds)  # 计算标准差
        # preds_stats['percentile_90'] = np.percentile(preds, 90)  # 计算90%分位数
        # preds_stats['percentile_95'] = np.percentile(preds, 95)  # 计算95%分位数
        # preds_stats['percentile_99'] = np.percentile(preds, 99)  # 计算99%分位数
        # pprint(preds_stats)
        # with open(f"{path}/preds/{model_name}.json", "w") as json_file:
        #     # Convert float32 values to float
        #     preds_stats = {key: float(value) for key, value in preds_stats.items()}
        #     # Write the dictionary to the JSON file
        #     json.dump(preds_stats, json_file, indent=4)
        # valid_auc = roc_auc_score(y_score=preds[:,1], y_true=y_valid)
        # print("valid accuracy: ", model.test(dataset, segment="valid"))
        if show:
            display_result(result, f"{get_model_name(ds_name, direct)}")
        dataset_size.append(len(x_data))
    return dataset_size

def main(args):
    result={}
    dataset_size = []
    dataset = None
    cur_dataset = None
    for ds in dsnames.keys():
        if dataset is None or cur_dataset != dsnames[ds]:
            dataset, embeds, num_input = get_dataset(ds)
            cur_dataset = dsnames[ds]
        dataset_size = trainer(dataset, embeds, num_input, show=True, ds_name=ds, reset_hp=False, train_result=result)
        print("============train dataset=============")
        print(f"dataset length: {dataset_size}")
    print("============train result=============")
    for key, item in result.items():
        print(key, item)
    print("============train result=============")

if __name__ == "__main__":
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', choices=['10HF', '15HF'], type=str)
    parser.add_argument('--ds_files', default='["main.2023"]', type=str)
    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)

    # Data module ===========================
    parser.add_argument('--batch_size', default=32, type=int)

    # Model Hyperparameters =================
    parser.add_argument('--model_name', default='time_series_model2d', type=str)
    parser.add_argument('--loss', default='mse', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)

    # model
    parser.add_argument('--num_embeds', default='[64, 5, 11]', type=str)
    parser.add_argument('--num_channel', default=10, type=int) # 通道数,与上面的DataHander中的win保持一致
    parser.add_argument('--num_input', default=51, type=int)
    # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
    # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
    # "out_channels": (16, 32, 800, 256), 
    # "out_channels": (24, 48, 1200, 256), 
    parser.add_argument('--out_channels', default='(24, 48, 1200, 256)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 8)', type=str)
    parser.add_argument('--dropout', default=0.5, type=float)

    # Others
    parser.add_argument('--model_dir', default='model', type=str)

    args = parser.parse_args()

    main(args)