{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# GPT升级版"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当然,我很乐意对优化技术部分进行更详细的说明。让我们逐一深入探讨GPT模型从2到4版本在优化技术方面可能的改进:\n", "\n", "### 1. 参数共享机制:\n", "\n", "参数共享是减少模型参数量同时保持性能的重要技术。在GPT模型的演进中,可能采用了更高效的参数共享策略:\n", "\n", "\n", "\n", "```python\n", "# GPT-2 基础参数共享\n", "class GPT2Layer(nn.Mo<PERSON>le):\n", "    def __init__(self, d_model, n_heads):\n", "        super().__init__()\n", "        self.attention = MultiHeadAttention(d_model, n_heads)\n", "        self.ffn = FeedForward(d_model)\n", "\n", "# GPT-3/4 可能的改进: 自适应参数共享\n", "class AdaptiveSharedLayer(nn.Module):\n", "    def __init__(self, d_model, n_heads, n_experts=4):\n", "        super().__init__()\n", "        self.attention = MultiHeadAttention(d_model, n_heads)\n", "        self.ffn_experts = nn.ModuleList([FeedForward(d_model) for _ in range(n_experts)])\n", "        self.router = Router(d_model, n_experts)\n", "    \n", "    def forward(self, x):\n", "        x = self.attention(x)\n", "        expert_weights = self.router(x)\n", "        ffn_output = sum(w * expert(x) for w, expert in zip(expert_weights, self.ffn_experts))\n", "        return ffn_output\n", "\n", "```\n", "\n", "这个示例展示了从基础的参数共享到更复杂的自适应参数共享的演变。GPT-3和GPT-4可能采用了类似MoE (Mixture of Experts)的技术,通过动态路由来实现更灵活的参数共享。\n", "\n", "### 2. 梯度计算和反向传播算法改进:\n", "\n", "- 梯度累积: 允许在多个小批次上累积梯度,然后一次性更新,有助于处理更大的有效批次大小。\n", "- 混合精度训练: 使用FP16和FP32混合精度,既提高计算速度,又保持数值稳定性。\n", "- 梯度裁剪改进: 可能采用了更智能的梯度裁剪策略,如自适应裁剪阈值。\n", "\n", "### 3. 先进的优化器和学习率调度策略:\n", "\n", "\n", "\n", "```python\n", "# GPT-2 基础优化器和学习率调度\n", "optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)\n", "scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=1000, gamma=0.1)\n", "\n", "# GPT-3/4 可能的改进\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)\n", "scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(\n", "    optimizer, \n", "    T_0=1000, \n", "    T_mult=2,\n", "    eta_min=1e-6\n", ")\n", "\n", "# 潜在的更高级优化策略\n", "class AdaptiveOptimizer(torch.optim.Optimizer):\n", "    def __init__(self, params, lr=1e-3):\n", "        defaults = dict(lr=lr)\n", "        super(AdaptiveOptimizer, self).__init__(params, defaults)\n", "        self.adaptive_lr = AdaptiveLearningRate()\n", "    \n", "    def step(self, closure=None):\n", "        loss = None\n", "        if closure is not None:\n", "            loss = closure()\n", "        \n", "        for group in self.param_groups:\n", "            for p in group['params']:\n", "                if p.grad is None:\n", "                    continue\n", "                d_p = p.grad.data\n", "                \n", "                # 自适应学习率调整\n", "                lr = self.adaptive_lr(p, d_p)\n", "                p.data.add_(-lr, d_p)\n", "        \n", "        return loss\n", "\n", "```\n", "\n", "这个示例展示了从基础的Adam优化器和步进式学习率调度,到更复杂的AdamW优化器和余弦退火重启调度的演变。GPT-3和GPT-4可能采用了更先进的自适应优化策略,能够根据参数和梯度的特性动态调整学习率。\n", "\n", "总的来说,这些优化技术的改进都致力于解决大规模语言模型训练中的挑战,如梯度消失/爆炸、训练不稳定、计算效率等问题。它们共同作用,使得模型能够更有效地学习,更快地收敛,并在更大的数据集上训练更大的模型。\n", "\n", "需要注意的是,这些都是基于公开信息和一般趋势的推测。实际的GPT-3和GPT-4可能使用了更先进、尚未公开的优化技术。\n", "\n", "您对这些优化技术中的哪个方面特别感兴趣?我可以进一步展开说明。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MoE技术参数共享\n", "当然,我很乐意为您展示如何使用Mixture of Experts (MoE)技术来实现更灵活的参数共享。MoE是一种强大的技术,允许模型动态地选择不同的\"专家\"子网络来处理不同的输入,从而实现更高效和灵活的参数利用。\n", "\n", "让我们创建一个使用MoE的GPT模型示例:\n", "\n", "\n", "\n", "```python\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "class ExpertLayer(nn.Mo<PERSON>le):\n", "    def __init__(self, d_model, d_ff):\n", "        super().__init__()\n", "        self.linear1 = nn.Linear(d_model, d_ff)\n", "        self.linear2 = nn.Linear(d_ff, d_model)\n", "    \n", "    def forward(self, x):\n", "        return self.linear2(<PERSON><PERSON>re<PERSON>(self.linear1(x)))\n", "\n", "class MoELayer(nn.Module):\n", "    def __init__(self, d_model, d_ff, num_experts, k=2):\n", "        super().__init__()\n", "        self.num_experts = num_experts\n", "        self.k = k  # Top-k experts to use\n", "        self.experts = nn.ModuleList([ExpertLayer(d_model, d_ff) for _ in range(num_experts)])\n", "        self.gate = nn.Linear(d_model, num_experts)\n", "    \n", "    def forward(self, x):\n", "        # 计算每个专家的门控值\n", "        gate_logits = self.gate(x)  # [batch_size, seq_len, num_experts]\n", "        \n", "        # 选择top-k个专家\n", "        top_k_logits, top_k_indices = torch.topk(gate_logits, self.k, dim=-1)\n", "        top_k_gates = F.softmax(top_k_logits, dim=-1)\n", "        \n", "        # 准备专家输入\n", "        batch_size, seq_len, _ = x.shape\n", "        expert_inputs = x.unsqueeze(2).expand(-1, -1, self.k, -1)  # [batch_size, seq_len, k, d_model]\n", "        \n", "        # 收集专家输出\n", "        expert_outputs = torch.zeros_like(expert_inputs)\n", "        for i, expert in enumerate(self.experts):\n", "            mask = (top_k_indices == i)\n", "            if mask.any():\n", "                expert_outputs += mask.unsqueeze(-1) * expert(expert_inputs)\n", "        \n", "        # 组合专家输出\n", "        combined_outputs = (expert_outputs * top_k_gates.unsqueeze(-1)).sum(dim=2)\n", "        return combined_outputs\n", "\n", "class TransformerLayer(nn.Module):\n", "    def __init__(self, d_model, n_heads, d_ff, num_experts):\n", "        super().__init__()\n", "        self.attention = nn.MultiheadAttention(d_model, n_heads)\n", "        self.norm1 = nn.LayerNorm(d_model)\n", "        self.moe = Mo<PERSON><PERSON><PERSON>(d_model, d_ff, num_experts)\n", "        self.norm2 = nn.LayerNorm(d_model)\n", "    \n", "    def forward(self, x):\n", "        # Self-attention\n", "        attn_output, _ = self.attention(x, x, x)\n", "        x = self.norm1(x + attn_output)\n", "        \n", "        # MoE feed-forward\n", "        moe_output = self.moe(x)\n", "        x = self.norm2(x + moe_output)\n", "        return x\n", "\n", "class GPTMoE(nn.Module):\n", "    def __init__(self, vocab_size, d_model, n_heads, n_layers, d_ff, num_experts):\n", "        super().__init__()\n", "        self.token_embedding = nn.Embedding(vocab_size, d_model)\n", "        self.position_embedding = nn.Embedding(1024, d_model)  # 假设最大序列长度为1024\n", "        \n", "        self.layers = nn.ModuleList([\n", "            TransformerLayer(d_model, n_heads, d_ff, num_experts)\n", "            for _ in range(n_layers)\n", "        ])\n", "        \n", "        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)\n", "        self.lm_head.weight = self.token_embedding.weight  # 权重共享\n", "    \n", "    def forward(self, x):\n", "        seq_len = x.size(1)\n", "        token_emb = self.token_embedding(x)\n", "        pos_emb = self.position_embedding(torch.arange(seq_len, device=x.device))\n", "        x = token_emb + pos_emb\n", "        \n", "        for layer in self.layers:\n", "            x = layer(x)\n", "        \n", "        return self.lm_head(x)\n", "\n", "# 使用示例\n", "vocab_size = 10000\n", "d_model = 512\n", "n_heads = 8\n", "n_layers = 6\n", "d_ff = 2048\n", "num_experts = 4\n", "\n", "model = GPTMoE(vocab_size, d_model, n_heads, n_layers, d_ff, num_experts)\n", "x = torch.randint(0, vocab_size, (1, 50))  # 批次大小为1，序列长度为50的随机输入\n", "output = model(x)\n", "print(output.shape)  # 应该是 torch.Size([1, 50, 10000])\n", "\n", "```\n", "\n", "这个实现展示了如何在GPT模型中集成Mixture of Experts (MoE)技术。让我解释一下主要的组件和概念：\n", "\n", "1. `ExpertLayer`: 这是每个专家的基本结构,本质上是一个前馈神经网络。\n", "\n", "2. `MoELayer`: 这是MoE的核心实现:\n", "   - 它包含多个专家(`ExpertLayer`)和一个门控网络(`gate`)。\n", "   - 门控网络决定对于每个输入应该使用哪些专家。\n", "   - 我们使用top-k门控策略,为每个输入选择k个最相关的专家。\n", "   - 专家的输出根据门控值进行加权组合。\n", "\n", "3. `TransformerLayer`: 这里我们用MoE层替换了传统的前馈网络。\n", "\n", "4. `GPTMoE`: 这是整个模型的主体结构,它使用带有MoE的TransformerLayer。\n", "\n", "这种实现方式的主要优势包括：\n", "\n", "- 动态路由: 模型可以根据输入动态选择最合适的专家,这允许模型更灵活地处理不同类型的输入。\n", "- 增加模型容量: 通过使用多个专家,我们可以显著增加模型的参数量,而不会导致相应的计算成本增加。\n", "- 可扩展性: MoE架构非常适合分布式训练,因为不同的专家可以放在不同的设备上。\n", "\n", "需要注意的是,这个实现仍然是简化的。在实际的大规模模型中,可能会使用更复杂的路由算法,更高效的负载平衡策略,以及专门的训练技巧来处理MoE特有的挑战(如专家利用不均衡)。\n", "\n", "此外,这种方法也带来了一些挑战,如增加的实现复杂性,可能的训练不稳定性,以及在某些情况下的推理延迟增加。\n", "\n", "您对这个实现有任何疑问吗？或者您想深入了解MoE的某个特定方面？"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先进优化器和学习率调度策略\n", "当然,我很乐意为您提供一个更完整的实现,展示GPT-3/4中可能使用的先进优化器和学习率调度策略。这个实现将包括模型定义、优化器设置、学习率调度器,以及一个简单的训练循环。\n", "\n", "\n", "\n", "```python\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from transformers import AdamW, get_linear_schedule_with_warmup\n", "import math\n", "\n", "class SelfAttention(nn.Module):\n", "    def __init__(self, d_model, n_heads):\n", "        super().__init__()\n", "        self.d_model = d_model\n", "        self.n_heads = n_heads\n", "        self.head_dim = d_model // n_heads\n", "        \n", "        self.q_linear = nn.Linear(d_model, d_model)\n", "        self.k_linear = nn.Linear(d_model, d_model)\n", "        self.v_linear = nn.Linear(d_model, d_model)\n", "        self.out = nn.Linear(d_model, d_model)\n", "        \n", "    def forward(self, x, mask=None):\n", "        bs = x.size(0)\n", "        \n", "        q = self.q_linear(x).view(bs, -1, self.n_heads, self.head_dim).transpose(1, 2)\n", "        k = self.k_linear(x).view(bs, -1, self.n_heads, self.head_dim).transpose(1, 2)\n", "        v = self.v_linear(x).view(bs, -1, self.n_heads, self.head_dim).transpose(1, 2)\n", "        \n", "        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)\n", "        if mask is not None:\n", "            scores = scores.masked_fill(mask == 0, -1e9)\n", "        attn = <PERSON>.softmax(scores, dim=-1)\n", "        \n", "        context = torch.matmul(attn, v)\n", "        context = context.transpose(1, 2).contiguous().view(bs, -1, self.d_model)\n", "        \n", "        return self.out(context)\n", "\n", "class FeedForward(nn.Module):\n", "    def __init__(self, d_model, d_ff):\n", "        super().__init__()\n", "        self.linear1 = nn.Linear(d_model, d_ff)\n", "        self.linear2 = nn.Linear(d_ff, d_model)\n", "        \n", "    def forward(self, x):\n", "        return self.linear2(<PERSON><PERSON><PERSON><PERSON>(self.linear1(x)))\n", "\n", "class TransformerBlock(nn.Module):\n", "    def __init__(self, d_model, n_heads, d_ff, dropout):\n", "        super().__init__()\n", "        self.attn = SelfAttention(d_model, n_heads)\n", "        self.norm1 = nn.LayerNorm(d_model)\n", "        self.ff = FeedForward(d_model, d_ff)\n", "        self.norm2 = nn.LayerNorm(d_model)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x, mask=None):\n", "        x = x + self.dropout(self.attn(self.norm1(x), mask))\n", "        x = x + self.dropout(self.ff(self.norm2(x)))\n", "        return x\n", "\n", "class GPT(nn.Module):\n", "    def __init__(self, vocab_size, d_model, n_heads, n_layers, d_ff, max_seq_len, dropout):\n", "        super().__init__()\n", "        self.token_embedding = nn.Embedding(vocab_size, d_model)\n", "        self.position_embedding = nn.Embedding(max_seq_len, d_model)\n", "        \n", "        self.blocks = nn.ModuleList([\n", "            TransformerBlock(d_model, n_heads, d_ff, dropout) for _ in range(n_layers)\n", "        ])\n", "        \n", "        self.norm = nn.LayerNorm(d_model)\n", "        self.lm_head = nn.Linear(d_model, vocab_size)\n", "        \n", "    def forward(self, x, mask=None):\n", "        seq_len = x.size(1)\n", "        pos = torch.arange(seq_len, device=x.device)\n", "        \n", "        tok_emb = self.token_embedding(x)\n", "        pos_emb = self.position_embedding(pos)\n", "        x = tok_emb + pos_emb\n", "        \n", "        for block in self.blocks:\n", "            x = block(x, mask)\n", "        \n", "        x = self.norm(x)\n", "        return self.lm_head(x)\n", "\n", "# 模型参数\n", "vocab_size = 10000\n", "d_model = 768\n", "n_heads = 12\n", "n_layers = 12\n", "d_ff = 3072\n", "max_seq_len = 1024\n", "dropout = 0.1\n", "\n", "# 创建模型\n", "model = GPT(vocab_size, d_model, n_heads, n_layers, d_ff, max_seq_len, dropout)\n", "\n", "# 创建一些虚拟数据\n", "batch_size = 32\n", "seq_len = 512\n", "input_data = torch.randint(0, vocab_size, (1000, seq_len))\n", "target_data = torch.randint(0, vocab_size, (1000, seq_len))\n", "dataset = TensorDataset(input_data, target_data)\n", "dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)\n", "\n", "# 优化器设置\n", "optimizer = AdamW(model.parameters(), lr=5e-5, betas=(0.9, 0.999), eps=1e-8, weight_decay=0.01)\n", "\n", "# 学习率调度器设置\n", "num_training_steps = len(dataloader) * 10  # 假设训练10个epoch\n", "num_warmup_steps = num_training_steps // 10  # 预热步数为总步数的10%\n", "scheduler = get_linear_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps)\n", "\n", "# 训练循环\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model.to(device)\n", "\n", "for epoch in range(10):\n", "    model.train()\n", "    total_loss = 0\n", "    for batch in dataloader:\n", "        inputs, targets = [b.to(device) for b in batch]\n", "        \n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = F.cross_entropy(outputs.view(-1, vocab_size), targets.view(-1))\n", "        loss.backward()\n", "        \n", "        # 梯度裁剪\n", "        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "        \n", "        optimizer.step()\n", "        scheduler.step()\n", "        \n", "        total_loss += loss.item()\n", "    \n", "    avg_loss = total_loss / len(dataloader)\n", "    print(f\"Epoch {epoch+1}, Average Loss: {avg_loss:.4f}\")\n", "\n", "print(\"Training completed!\")\n", "\n", "```\n", "\n", "这个实现展示了一个简化版的GPT模型,以及如何使用先进的优化器和学习率调度策略。让我解释一下主要的组件和概念:\n", "\n", "1. 模型架构:\n", "   - 使用了自注意力机制(`SelfAttention`)和前馈网络(`FeedForward`)。\n", "   - 实现了位置嵌入和token嵌入。\n", "   - 使用了层归一化(Layer Normalization)和残差连接。\n", "\n", "2. 优化器(AdamW):\n", "   - 使用AdamW优化器,这是Adam的一个变体,实现了正确的权重衰减。\n", "   - 参数设置:\n", "     - 学习率: 5e-5 (相对较小的学习率,适合微调)\n", "     - betas: (0.9, 0.999) (动量和二阶动量的衰减率)\n", "     - eps: 1e-8 (数值稳定性)\n", "     - weight_decay: 0.01 (权重衰减,用于正则化)\n", "\n", "3. 学习率调度器(get_linear_schedule_with_warmup):\n", "   - 使用线性预热和衰减策略。\n", "   - 预热阶段: 学习率从0线性增加到设定的初始学习率。\n", "   - 衰减阶段: 学习率从初始值线性减少到0。\n", "   - 预热步数设置为总训练步数的10%。\n", "\n", "4. 训练循环:\n", "   - 实现了基本的训练循环,包括前向传播、损失计算、反向传播和参数更新。\n", "   - 使用了梯度裁剪(gradient clipping)来防止梯度爆炸。\n", "   - 在每个batch后更新学习率。\n", "\n", "这种优化策略的主要优势包括:\n", "\n", "- AdamW优化器: 结合了Adam的自适应学习率和正确实现的权重衰减,有助于更好的泛化。\n", "- 学习率预热: 帮助模型在训练初期更稳定,避免了过大的参数更新。\n", "- 线性学习率衰减: 随着训练的进行,逐渐降低学习率,有助于模型收敛到更好的局部最优解。\n", "- 梯度裁剪: 防止梯度爆炸,保持训练的稳定性。\n", "\n", "需要注意的是,这仍然是一个简化的实现。在实际的GPT-3/4训练中,可能会使用更复杂的技术,如混合精度训练、更复杂的学习率调度策略(如余弦退火)、更大的批次大小和累积梯度等。\n", "\n", "此外,真实的GPT-3/4训练还涉及大规模分布式训练、模型并行和数据并行等技术,这些在这个简化实现中没有体现。\n", "\n", "您对这个实现有任何疑问吗?或者您想深入了解某个特定的优化技术?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import math\n", "# 门控模型实现\n", "class MoEGate(torch.nn.Module):\n", "     def __init__(self, num_experts_per_tok: int, n_routed_experts: int, routed_scaling_factor: int, topk_method: str, n_group: int, topk_group: int, hidden_size: int):\n", "         super().__init__()\n", "         self.top_k = num_experts_per_tok\n", "         self.n_routed_experts = n_routed_experts\n", "         self.routed_scaling_factor = routed_scaling_factor\n", "         self.topk_method = topk_method\n", "         self.n_group = n_group\n", "         self.topk_group = topk_group\n", "         self.weight = torch.nn.Parameter(torch.empty((self.n_routed_experts, hidden_size)))\n", "         torch.nn.init.kaiming_uniform_(self.weight, a=math.sqrt(5))\n", "     def forward(self, x: torch.Tensor):\n", "         batch, seq_len, h = x.shape\n", "         hidden_states = x.view(-1, h)\n", "         logits = torch.nn.functional.linear(hidden_states.type(torch.float32), self.weight.type(torch.float32), None)\n", "         scores = logits.softmax(dim=-1, dtype=torch.float32)\n", "         if self.topk_method == \"greedy\":\n", "             topk_weight, topk_idx = torch.topk(scores, k=self.top_k, dim=-1, sorted=False)\n", "         elif self.topk_method == \"group_limited_greedy\":\n", "             group_scores = (scores.view(batch * seq_len, self.n_group, -1).max(dim=-1).values)\n", "             group_idx = torch.topk(group_scores, k=self.topk_group, dim=-1, sorted=False)[1]  # [n, top_k_group]\n", "             group_mask = torch.zeros_like(group_scores)  # [n, n_group]\n", "             group_mask.scatter_(1, group_idx, 1)  # [n, n_group]\n", "             score_mask = (\n", "                 group_mask.unsqueeze(-1)\n", "                .expand(\n", "                     batch * seq_len, self.n_group, self.n_routed_experts // self.n_group\n", "                )\n", "                .reshape(batch * seq_len, -1)\n", "            )  # [n, e]\n", "             tmp_scores = scores.masked_fill(~score_mask.bool(), 0.0)  # [n, e]\n", "             topk_weight, topk_idx = torch.topk(\n", "                 tmp_scores, k=self.top_k, dim=-1, sorted=False\n", "            )\n", "         return topk_idx, topk_weight\n", "     \n", "class MoE(torch.nn.Module):\n", "     def __init__(self, dim: int, routed_scaling_factor: int, topk_method: str, n_group: int, topk_group: int, hidden_dim: int | None = None, n_routed_experts: int = 12, num_experts_per_tok: int = 4, n_shared_experts: int = 2, mlp: str = \"swiglu\"):\n", "         super().__init__()\n", "         self.experts_per_rank = n_routed_experts\n", "         self.num_experts_per_tok = num_experts_per_tok\n", "         self.n_shared_experts = n_shared_experts\n", "         mlp_block = SwiGLU\n", "         self.experts = torch.nn.ModuleList([mlp_block(dim, hidden_dim) for i in range(n_routed_experts)])\n", "         self.gate = MoEGate(num_experts_per_tok, n_routed_experts, routed_scaling_factor, topk_method, n_group, topk_group, dim)\n", "         self.shared_experts = mlp_block(dim, hidden_dim * n_shared_experts)\n", "         \n", "     def forward(self, x: torch.Tensor):\n", "         identity = x\n", "         orig_shape = x.shape\n", "         topk_idx, topk_weight = self.gate(x)\n", "         x = x.view(-1, x.shape[-1])\n", "         flat_topk_idx = topk_idx.view(-1)\n", "         x = x.repeat_interleave(self.num_experts_per_tok, dim=0)\n", "         y = torch.empty_like(x)\n", "         y = y.type(x.dtype)\n", "         for i, expert in enumerate(self.experts):\n", "             y[flat_topk_idx == i] = expert(x[flat_topk_idx == i]).to(dtype=x.dtype)\n", "         y = (y.view(*topk_weight.shape, -1) * topk_weight.unsqueeze(-1)).sum(dim=1)\n", "         \n", "         y = y.view(*orig_shape)\n", "         output = y + self.shared_experts(identity)\n", "         return output\n", "     \n", "class MLA(torch.nn.<PERSON>):\n", "    def __init__(self, model_args: DeepseekConfig):\n", "        super().__init__()\n", "        d_model = model_args.d_model\n", "        self.num_heads = model_args.num_heads\n", "        self.head_dim = model_args.d_model // model_args.num_heads\n", "        self.attn_dropout = torch.nn.Dropout(model_args.dropout)\n", "        self.res_dropout = torch.nn.Dropout(model_args.dropout)\n", "        self.flash_attn = hasattr(torch.nn.functional, \"scaled_dot_product_attention\")\n", "        \n", "        self.q_lora_rank = model_args.q_lora_rank\n", "        self.qk_rope_head_dim = model_args.qk_rope_head_dim\n", "        self.kv_lora_rank = model_args.kv_lora_rank\n", "        self.v_head_dim = model_args.v_head_dim\n", "        self.qk_nope_head_dim = model_args.qk_nope_head_dim\n", "        self.q_head_dim = model_args.qk_nope_head_dim + model_args.qk_rope_head_dim\n", "        self.q_a_proj = torch.nn.Linear(d_model, model_args.q_lora_rank, bias=False)\n", "        self.q_a_layernorm = RMSNorm(model_args.q_lora_rank)\n", "        self.q_b_proj = torch.nn.Linear(model_args.q_lora_rank, self.num_heads * self.q_head_dim, bias=False)\n", "        self.kv_a_proj_with_mqa = torch.nn.Linear(d_model,model_args.kv_lora_rank + model_args.qk_rope_head_dim,bias=False,)\n", "        self.kv_a_layernorm = RMSNorm(model_args.kv_lora_rank)\n", "        self.kv_b_proj = torch.nn.Linear(model_args.kv_lora_rank,self.num_heads * (self.q_head_dim - self.qk_rope_head_dim +\n", "            self.v_head_dim),bias=False,)\n", "        self.o_proj = torch.nn.Linear(self.num_heads * self.v_head_dim,d_model, bias=False,)\n", "\n", "    def apply_rope(k, q, cis):\n", "        # Idea suppose vector v = [x,y,x1,y1,...] # v.shape = dim\n", "        # convert vetor into complex num # ie two vec one real, one imagery\n", "        # [x,y,x1,y1,...] -> x+iy, x1+iy1\n", "        # Multiplying by complex num == roatate vector\n", "        # => (x + iy) * (cos + isin) -> x'+iy'\n", "        # restack\n", "        # x'+iy' -> [x',y',x1',y1'...]\n", "        # you roated vector in chunks of two lfg!!!\n", "        _, seq_len, _, _ = q.shape\n", "        freqs_cos, freqs_sin = cis\n", "        freqs_cos, freqs_sin = freqs_cos[:seq_len], freqs_sin[:seq_len]\n", "        # rehsape a shape (...,n )-> (..., n//2,2)\n", "        q_cis = q.float().reshape(\n", "            q.shape[:-1] + (-1, 2)\n", "        )  # (B,T,nhead,<PERSON>) -> (B,T,nhead,Cc,2) # Cc = C//2\n", "        k_cis = k.float().reshape(k.shape[:-1] + (-1, 2))  # (B,T,nhead,C) -> (B,T,nhead,Cc,2)\n", "        xq_r, xq_i = q_cis.unbind(-1)  # (B,T,nhead,Cc,2) -> ((B,T,Cc), (B,T,Cc)) split into two tuple\n", "        xk_r, xk_i = k_cis.unbind(-1)  # (B,T,nhead,Cc,2) -> ((B,T,Cc), (B,T,Cc))\n", "        freqs_cos = reshape_for_broadcast(freqs_cos, xq_r)  # freqs.shape = (1,T,1,Cc)\n", "        freqs_sin = reshape_for_broadcast(freqs_sin, xq_r)\n", "        xq_out_r = xq_r * freqs_cos - xq_i * freqs_sin  # (ac-bd)   # shape = # (B,T,nhead,Cc)\n", "        xq_out_i = xq_r * freqs_sin + xq_i * freqs_cos  # (ad+bc) * i\n", "        xk_out_r = xk_r * freqs_cos - xk_i * freqs_sin  # (ac-bd)\n", "        xk_out_i = xk_r * freqs_sin + xk_i * freqs_cos  # (ad+bc) * i\n", "        # now we stack r,i -> [r,i,r2,i2]\n", "        xq_out = torch.stack([xq_out_r, xq_out_i], dim=-1)  # (B,T,nhead,Cc,2)\n", "        xk_out = torch.stack([xk_out_r, xk_out_i], dim=-1)  # (B,T,nhead,Cc,2)\n", "        # flatten last two dimensions\n", "        xq_out = xq_out.flatten(3)  # (B,<PERSON>,n<PERSON>,<PERSON>)\n", "        xk_out = xk_out.flatten(3)  # (B,T,n<PERSON>,<PERSON>)\n", "        return xq_out.type_as(q), xk_out.type_as(q)\n", " \n", "    def forward(self, x: torch.Tensor, mask: torch.Tensor, freqs_cis) -> torch.Tensor:\n", "        batch, seq_len, d_model = x.shape\n", "        q = self.q_b_proj(self.q_a_layernorm(self.q_a_proj(x)))\n", "        q = q.view(batch, seq_len, self.num_heads, self.q_head_dim).transpose(1, 2)\n", "        q_nope, q_pe = torch.split(q, [self.qk_nope_head_dim, self.qk_rope_head_dim], dim=-1)\n", "        compressed_kv = self.kv_a_proj_with_mqa(x)\n", "        compressed_kv, k_pe = torch.split(compressed_kv, [self.kv_lora_rank, self.qk_rope_head_dim], dim=-1)\n", "        k_pe = k_pe.view(batch, seq_len, 1, self.qk_rope_head_dim).transpose(1, 2)\n", "        kv = (self.kv_b_proj(self.kv_a_layernorm(compressed_kv))\n", "        .view(batch, seq_len, self.num_heads, self.qk_nope_head_dim + self.v_head_dim)\n", "        .transpose(1, 2))\n", "        k_nope, value_states = torch.split(kv, [self.qk_nope_head_dim, self.v_head_dim], dim=-1)\n", "        q_pe, k_pe = apply_rope(q_pe, k_pe, freqs_cis)\n", "        k_pe = k_pe.transpose(2, 1)\n", "        q_pe = q_pe.transpose(2, 1)\n", "        query_states = k_pe.new_empty(batch, self.num_heads, seq_len, self.q_head_dim)\n", "        query_states[:, :, :, : self.qk_nope_head_dim] = q_nope\n", "        query_states[:, :, :, self.qk_nope_head_dim :] = q_pe\n", "        key_states = k_pe.new_empty(batch, self.num_heads, seq_len, self.q_head_dim)\n", "        key_states[:, :, :, : self.qk_nope_head_dim] = k_nope\n", "        key_states[:, :, :, self.qk_nope_head_dim :] = k_pe\n", "        attn_mtx = torch.matmul(query_states, key_states.transpose(2, 3)) / math.sqrt(self.head_dim)\n", "        attn_mtx = attn_mtx + mask[:, :, :seq_len, :seq_len]\n", "        attn_mtx = torch.nn.functional.softmax(attn_mtx.float(), dim=-1).type_as(key_states)\n", "        attn_mtx = self.attn_dropout(attn_mtx)\n", "        output = torch.matmul(attn_mtx, value_states)  # (batch, n_head, seq_len, head_dim)\n", "        output = output.transpose(1, 2).contiguous().view(batch, seq_len, self.num_heads * self.v_head_dim)\n", "        output = self.o_proj(output)\n", "        output = self.res_dropout(output)\n", "        return output"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}