{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\sklearn\\cross_validation.py:41: DeprecationWarning: This module was deprecated in version 0.18 in favor of the model_selection module into which all the refactored classes and functions are moved. Also note that the interface of the new CV iterators are different from that of this module. This module will be removed in 0.20.\n", "  \"This module will be removed in 0.20.\", DeprecationWarning)\n"]}], "source": ["import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sklearn.cross_validation import train_test_split"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                datetime          3      4       6       7        8         9  \\\n", "1294 2018-06-14 13:50:00  AP1910.Z<PERSON>   long  0.9871  0.9359  83.1898   82.6298   \n", "1436 2018-07-26 14:35:00  JD1909.DC   long  1.0783  1.0504  92.0790   41.4373   \n", "1435 2018-07-25 23:25:00  SC1907.SC   long  1.0267  1.0114  67.7692   85.5669   \n", "1433 2018-07-24 11:15:00  SM1909.Z<PERSON>   long  0.9051  0.8917  85.5713   69.2706   \n", "1432 2018-07-25 13:50:00  AP1910.ZC   long  0.9071  0.8600  80.0500  106.1462   \n", "\n", "          10      11      12 ...       28      29      30       31      32  \\\n", "1294  0.0222  0.0035  0.0197 ...  -3.8130  0.5225  0.6935  -2.3168 -2.4446   \n", "1436  0.0124  0.0022  0.0162 ...  -4.5070  0.7104 -0.8695   6.2192 -0.7171   \n", "1435  0.0000  0.0016  0.0000 ...   1.0605  0.3961  1.5411 -11.5744  0.6346   \n", "1433  0.0103  0.0015  0.0213 ...   3.4615  0.9147  1.2166   3.0917 -0.4660   \n", "1432  0.0204  0.0032  0.0218 ...  34.4409  0.6436  4.6150   3.0741 -0.0230   \n", "\n", "          33      34      35      36  37  \n", "1294 -0.0067 -0.0055 -0.0487 -0.0487   1  \n", "1436 -0.0032 -0.0103 -0.0053  0.0058   0  \n", "1435 -0.0024 -0.0021 -0.0201 -0.0147   1  \n", "1433 -0.0020 -0.0108 -0.0178 -0.0227   1  \n", "1432  0.0014 -0.0088 -0.0300 -0.0549   0  \n", "\n", "[5 rows x 35 columns]\n", "      0       1       2        3         4       5       6       7       8   \\\n", "1294   1  0.9871  0.9359  83.1898   82.6298  0.0222  0.0035  0.0197  0.0033   \n", "1436   0  1.0783  1.0504  92.0790   41.4373  0.0124  0.0022  0.0162  0.0044   \n", "1435   1  1.0267  1.0114  67.7692   85.5669  0.0000  0.0016  0.0000  0.0024   \n", "1433   1  0.9051  0.8917  85.5713   69.2706  0.0103  0.0015  0.0213  0.0030   \n", "1432   0  0.9071  0.8600  80.0500  106.1462  0.0204  0.0032  0.0218  0.0035   \n", "\n", "          9    ...         22       23      24      25       26      27  \\\n", "1294  0.0057   ...     7.4350  -3.8130  0.5225  0.6935  -2.3168 -2.4446   \n", "1436  0.0075   ...    -4.4950  -4.5070  0.7104 -0.8695   6.2192 -0.7171   \n", "1435  0.0032   ...     0.7685   1.0605  0.3961  1.5411 -11.5744  0.6346   \n", "1433  0.0082   ...     9.9750   3.4615  0.9147  1.2166   3.0917 -0.4660   \n", "1432  0.0090   ...    45.6450  34.4409  0.6436  4.6150   3.0741 -0.0230   \n", "\n", "          28      29      30      31  \n", "1294 -0.0067 -0.0055 -0.0487 -0.0487  \n", "1436 -0.0032 -0.0103 -0.0053  0.0058  \n", "1435 -0.0024 -0.0021 -0.0201 -0.0147  \n", "1433 -0.0020 -0.0108 -0.0178 -0.0227  \n", "1432  0.0014 -0.0088 -0.0300 -0.0549  \n", "\n", "[5 rows x 32 columns]\n", "      0       1       2        3        4       5       6       7       8   \\\n", "2076   1  0.8810  0.8679  10.6186  61.6751  0.0100  0.0015  0.0224  0.0016   \n", "2077   1  0.9703  0.9602  14.9958  11.6030  0.0100  0.0016  0.0230  0.0027   \n", "273    0  0.9050  0.8912  19.9093  27.5654  0.0106  0.0019  0.0239  0.0026   \n", "2073   1  0.9584  0.9484  22.5725  33.1512  0.0098  0.0015  0.0227  0.0022   \n", "2075   0  0.8194  0.7769  10.7078  45.9254  0.0184  0.0029  0.0248  0.0030   \n", "\n", "          9    ...        22       23      24      25      26      27      28  \\\n", "2076  0.0047   ...     5.520   3.6255 -0.5483  0.5373  3.3559 -0.4485  0.0038   \n", "2077  0.0056   ...    -4.285  -4.7242 -0.8775 -0.9088 -1.1479 -4.8334  0.0028   \n", "273   0.0074   ...    -2.160   2.9085 -1.0814 -0.0975 -0.3203 -3.4992  0.0029   \n", "2073  0.0050   ...    -3.395  -4.1839 -0.2558 -0.5462  2.1642 -8.2617  0.0000   \n", "2075  0.0067   ...   -19.170 -26.6755 -0.9025 -1.9373  4.5944 -0.7335  0.0051   \n", "\n", "          29      30      31  \n", "2076  0.0083  0.0137  0.0128  \n", "2077  0.0102  0.0127  0.0171  \n", "273   0.0115  0.0121  0.0049  \n", "2073  0.0047  0.0071  0.0081  \n", "2075  0.0124  0.0174  0.0171  \n", "\n", "[5 rows x 32 columns]\n"]}], "source": ["FD_PATH = \"d:/QuantLab/log/\"\n", "\n", "#用pandas将时间转为标准格式\n", "dateparse = lambda dates: pd.datetime.strptime(dates,' %Y-%m-%d %H:%M:%S')\n", "df_train = pd.read_csv(FD_PATH + 'featuresdata.log', header=None, sep=']|,', skipinitialspace=True,\n", "                       parse_dates={'datetime': [5]},date_parser=dateparse)\n", "# df_train = pd.read_csv(FD_PATH + 'featuresdata.201701_201905.log', header=None, sep=']|,', skipinitialspace=True)\n", "\n", "temp = pd.DatetimeIndex(df_train['datetime'])\n", "# df_train['date'] = temp.date\n", "# df_train['time'] = temp.time\n", "#由于时间的部分最小粒度为小时，所以把time变为hour更加简洁\n", "# df_train['hour']=pd.to_datetime(temp.time,format=\"%H:%M:%S\")#变换格式\n", "# df_train['hour']=pd.Index(df_train[\"hour\"]).hour\n", "# df_train['dayofweek']=pd.DatetimeIndex(temp.date).dayofweek #提取出星期几这个特征\n", "\n", "# df_train[3].apply(lambda x: x.str.strip())\n", "# df_obj = df_train.select_dtypes([3])\n", "df_train[3] = df_train[3].str.strip()\n", "df_train = df_train[df_train[3] != \"IF1905.SF\"]\n", "df_train = df_train[df_train[3] != \"IH1905.SF\"]\n", "df_train = df_train[df_train[3] != \"IC1905.SF\"]\n", "# df_train = df_train[df_train[3].str != \"IH1905.SF\"]\n", "# df_train = df_train[df_train[3].str != \"IC1905.SF\"]\n", "\n", "df_train = df_train.drop([0,1,2], axis=1)\n", "df_train = df_train.sort_values(4,ascending=1)\n", "print(df_train.head())\n", "\n", "df_train_long, df_train_short = [x for _, x in df_train.groupby(df_train[4])]\n", "train_long = df_train_long.drop([3,4,'datetime'], axis=1)\n", "train_long = train_long[[37] + [c for c in train_long if c not in [37]]]\n", "train_long.columns = [n for n in range(0, (train_long.shape[1]))]\n", "print(train_long.head())\n", "\n", "train_long, test_long = train_test_split(train_long, random_state=42)\n", "train_long.to_csv(FD_PATH + 'long3.train', header=False, index=False)\n", "test_long.to_csv(FD_PATH + 'long3.test', header=False, index=False)\n", "# print(train_long)\n", "# print(test_long)\n", "\n", "train_short = df_train_short.drop([3,4,'datetime'], axis=1)\n", "train_short = train_short[[37] + [c for c in train_short if c not in [37]]]\n", "train_short.columns = [n for n in range(0, (train_short.shape[1]))]\n", "print(train_short.head())\n", "\n", "train_short, test_short = train_test_split(train_short, random_state=42)\n", "train_short.to_csv(FD_PATH + 'short3.train', header=False, index=False)\n", "test_short.to_csv(FD_PATH + 'short3.test', header=False, index=False)\n", "# print(train_short)\n", "# print(test_short)\n", "\n", "# y_train = df_train[0]\n", "# y_test = df_test[0]\n", "# X_train = df_train.drop(0, axis=1)\n", "# X_test = df_test.drop(0, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}