{"trial_data": [["{\n  \"stub\": false,\n  \"trainable_name\": \"<PERSON><PERSON>\",\n  \"trial_id\": \"aaa74_00000\",\n  \"storage\": {\n    \"_type\": \"CLOUDPICKLE_FALLBACK\",\n    \"value\": \"800595e7020000000000008c1b7261792e747261696e2e5f696e7465726e616c2e73746f72616765948c0e53746f72616765436f6e746578749493942981947d94288c12637573746f6d5f66735f70726f766964656494898c136578706572696d656e745f6469725f6e616d65948c1750504f5f323032352d30312d30365f31342d30382d3330948c0e747269616c5f6469725f6e616d65948c3650504f5f6d61726b657474696d696e675f656e765f61616137345f30303030305f305f323032352d30312d30365f31342d30382d3336948c1863757272656e745f636865636b706f696e745f696e646578944b008c0b73796e635f636f6e666967948c097261792e747261696e948c0a53796e63436f6e6669679493942981947d94288c0b73796e635f706572696f64944d2c018c0c73796e635f74696d656f7574944d08078c0e73796e635f61727469666163747394898c1c73796e635f6172746966616374735f6f6e5f636865636b706f696e7494888c0a75706c6f61645f646972948c0a44455052454341544544948c0673796e6365729468168c1273796e635f6f6e5f636865636b706f696e7494681675628c1273746f726167655f66696c6573797374656d948c0b70796172726f772e5f6673948c1c4c6f63616c46696c6553797374656d2e5f7265636f6e7374727563749493947d948c087573655f6d6d6170948973859452948c0f73746f726167655f66735f70617468948c22453a2f6c61622f526f626f5175616e742f70796c61622f7261795f726573756c747394681768008c115f46696c6573797374656d53796e6365729493942981947d94286819682068114d2c0168124d08078c116c6173745f73796e635f75705f74696d659447fff00000000000008c136c6173745f73796e635f646f776e5f74696d659447fff00000000000008c0d5f73796e635f70726f63657373944e8c0c5f63757272656e745f636d64944e75628c0a5f74696d657374616d70948c13323032352d30312d30365f31342d30382d33369475622e\"\n  },\n  \"config\": {\n    \"extra_python_environs_for_driver\": {},\n    \"extra_python_environs_for_worker\": {},\n    \"placement_strategy\": \"PACK\",\n    \"num_gpus\": 0,\n    \"_fake_gpus\": false,\n    \"num_cpus_for_main_process\": 1,\n    \"eager_tracing\": true,\n    \"eager_max_retraces\": 20,\n    \"tf_session_args\": {\n      \"intra_op_parallelism_threads\": 2,\n      \"inter_op_parallelism_threads\": 2,\n      \"gpu_options\": {\n        \"allow_growth\": true\n      },\n      \"log_device_placement\": false,\n      \"device_count\": {\n        \"CPU\": 1\n      },\n      \"allow_soft_placement\": true\n    },\n    \"local_tf_session_args\": {\n      \"intra_op_parallelism_threads\": 8,\n      \"inter_op_parallelism_threads\": 8\n    },\n    \"torch_compile_learner\": false,\n    \"torch_compile_learner_what_to_compile\": \"forward_train\",\n    \"torch_compile_learner_dynamo_backend\": \"inductor\",\n    \"torch_compile_learner_dynamo_mode\": null,\n    \"torch_compile_worker\": false,\n    \"torch_compile_worker_dynamo_backend\": \"onnxrt\",\n    \"torch_compile_worker_dynamo_mode\": null,\n    \"torch_ddp_kwargs\": {},\n    \"torch_skip_nan_gradients\": false,\n    \"enable_rl_module_and_learner\": false,\n    \"enable_env_runner_and_connector_v2\": false,\n    \"env\": \"markettiming_env\",\n    \"env_config\": {\n      \"name\": \"MarketTimingEnv\",\n      \"version\": \"v1\",\n      \"initial_amount\": 10000000,\n      \"gamma\": 0.98,\n      \"mode\": \"train\",\n      \"split_percent\": 0.9,\n      \"data_path\": \"E:/lab/RoboQuant/pylab/data\",\n      \"data_file\": \"ft_all.all.00170516142453003.csv\"\n    },\n    \"observation_space\": null,\n    \"action_space\": null,\n    \"clip_rewards\": null,\n    \"normalize_actions\": true,\n    \"clip_actions\": false,\n    \"_is_atari\": null,\n    \"disable_env_checking\": false,\n    \"env_task_fn\": null,\n    \"render_env\": false,\n    \"action_mask_key\": \"action_mask\",\n    \"env_runner_cls\": null,\n    \"num_env_runners\": 8,\n    \"num_envs_per_env_runner\": 1,\n    \"num_cpus_per_env_runner\": 1,\n    \"num_gpus_per_env_runner\": 0,\n    \"custom_resources_per_env_runner\": {},\n    \"validate_env_runners_after_construction\": true,\n    \"max_requests_in_flight_per_env_runner\": 2,\n    \"sample_timeout_s\": 60.0,\n    \"_env_to_module_connector\": null,\n    \"add_default_connectors_to_env_to_module_pipeline\": true,\n    \"_module_to_env_connector\": null,\n    \"add_default_connectors_to_module_to_env_pipeline\": true,\n    \"episode_lookback_horizon\": 1,\n    \"rollout_fragment_length\": \"auto\",\n    \"batch_mode\": \"truncate_episodes\",\n    \"compress_observations\": false,\n    \"remote_worker_envs\": false,\n    \"remote_env_batch_wait_ms\": 0,\n    \"enable_tf1_exec_eagerly\": false,\n    \"sample_collector\": {\n      \"_type\": \"CLOUDPICKLE_FALLBACK\",\n      \"value\": \"80059551000000000000008c357261792e726c6c69622e6576616c756174696f6e2e636f6c6c6563746f72732e73696d706c655f6c6973745f636f6c6c6563746f72948c1353696d706c654c697374436f6c6c6563746f729493942e\"\n    },\n    \"preprocessor_pref\": \"deepmind\",\n    \"observation_filter\": \"NoFilter\",\n    \"update_worker_filter_stats\": true,\n    \"use_worker_filter_stats\": true,\n    \"sampler_perf_stats_ema_coef\": null,\n    \"num_learners\": 0,\n    \"num_gpus_per_learner\": 0,\n    \"num_cpus_per_learner\": 1,\n    \"local_gpu_idx\": 0,\n    \"gamma\": 0.99,\n    \"lr\": 0.0001,\n    \"grad_clip\": null,\n    \"grad_clip_by\": \"global_norm\",\n    \"train_batch_size_per_learner\": null,\n    \"train_batch_size\": 4000,\n    \"num_epochs\": 30,\n    \"minibatch_size\": 128,\n    \"shuffle_batch_per_epoch\": true,\n    \"model\": {\n      \"fcnet_hiddens\": [\n        256,\n        256\n      ],\n      \"fcnet_activation\": \"tanh\",\n      \"fcnet_weights_initializer\": null,\n      \"fcnet_weights_initializer_config\": null,\n      \"fcnet_bias_initializer\": null,\n      \"fcnet_bias_initializer_config\": null,\n      \"conv_filters\": null,\n      \"conv_activation\": \"relu\",\n      \"conv_kernel_initializer\": null,\n      \"conv_kernel_initializer_config\": null,\n      \"conv_bias_initializer\": null,\n      \"conv_bias_initializer_config\": null,\n      \"conv_transpose_kernel_initializer\": null,\n      \"conv_transpose_kernel_initializer_config\": null,\n      \"conv_transpose_bias_initializer\": null,\n      \"conv_transpose_bias_initializer_config\": null,\n      \"post_fcnet_hiddens\": [],\n      \"post_fcnet_activation\": \"relu\",\n      \"post_fcnet_weights_initializer\": null,\n      \"post_fcnet_weights_initializer_config\": null,\n      \"post_fcnet_bias_initializer\": null,\n      \"post_fcnet_bias_initializer_config\": null,\n      \"free_log_std\": false,\n      \"log_std_clip_param\": 20.0,\n      \"no_final_linear\": false,\n      \"vf_share_layers\": false,\n      \"use_lstm\": false,\n      \"max_seq_len\": 20,\n      \"lstm_cell_size\": 256,\n      \"lstm_use_prev_action\": false,\n      \"lstm_use_prev_reward\": false,\n      \"lstm_weights_initializer\": null,\n      \"lstm_weights_initializer_config\": null,\n      \"lstm_bias_initializer\": null,\n      \"lstm_bias_initializer_config\": null,\n      \"_time_major\": false,\n      \"use_attention\": false,\n      \"attention_num_transformer_units\": 1,\n      \"attention_dim\": 64,\n      \"attention_num_heads\": 1,\n      \"attention_head_dim\": 32,\n      \"attention_memory_inference\": 50,\n      \"attention_memory_training\": 50,\n      \"attention_position_wise_mlp_dim\": 32,\n      \"attention_init_gru_gate_bias\": 2.0,\n      \"attention_use_n_prev_actions\": 0,\n      \"attention_use_n_prev_rewards\": 0,\n      \"framestack\": true,\n      \"dim\": 84,\n      \"grayscale\": false,\n      \"zero_mean\": true,\n      \"custom_model\": null,\n      \"custom_model_config\": {},\n      \"custom_action_dist\": null,\n      \"custom_preprocessor\": null,\n      \"encoder_latent_dim\": null,\n      \"always_check_shapes\": false,\n      \"lstm_use_prev_action_reward\": -1,\n      \"_use_default_native_models\": -1,\n      \"_disable_preprocessor_api\": false,\n      \"_disable_action_flattening\": false\n    },\n    \"_learner_connector\": null,\n    \"add_default_connectors_to_learner_pipeline\": true,\n    \"learner_config_dict\": {},\n    \"optimizer\": {\n      \"type\": \"SGD\",\n      \"lr\": 0.01,\n      \"momentum\": 0.9\n    },\n    \"_learner_class\": null,\n    \"explore\": true,\n    \"exploration_config\": {\n      \"type\": \"StochasticSampling\"\n    },\n    \"count_steps_by\": \"env_steps\",\n    \"policy_map_capacity\": 100,\n    \"policy_mapping_fn\": {\n      \"_type\": \"CLOUDPICKLE_FALLBACK\",\n      \"value\": \"80059557000000000000008c257261792e726c6c69622e616c676f726974686d732e616c676f726974686d5f636f6e666967948c29416c676f726974686d436f6e6669672e44454641554c545f504f4c4943595f4d415050494e475f464e9493942e\"\n    },\n    \"policies_to_train\": null,\n    \"policy_states_are_swappable\": false,\n    \"observation_fn\": null,\n    \"input_read_method\": \"read_parquet\",\n    \"input_read_method_kwargs\": {},\n    \"input_read_schema\": {},\n    \"input_read_episodes\": false,\n    \"input_read_sample_batches\": false,\n    \"input_read_batch_size\": null,\n    \"input_filesystem\": null,\n    \"input_filesystem_kwargs\": {},\n    \"input_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"input_spaces_jsonable\": true,\n    \"materialize_data\": false,\n    \"materialize_mapped_data\": true,\n    \"map_batches_kwargs\": {},\n    \"iter_batches_kwargs\": {},\n    \"prelearner_class\": null,\n    \"prelearner_buffer_class\": null,\n    \"prelearner_buffer_kwargs\": {},\n    \"prelearner_module_synch_period\": 10,\n    \"dataset_num_iters_per_learner\": null,\n    \"input_config\": {},\n    \"actions_in_input_normalized\": false,\n    \"postprocess_inputs\": false,\n    \"shuffle_buffer_size\": 0,\n    \"output\": null,\n    \"output_config\": {},\n    \"output_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"output_max_file_size\": 67108864,\n    \"output_max_rows_per_file\": null,\n    \"output_write_method\": \"write_parquet\",\n    \"output_write_method_kwargs\": {},\n    \"output_filesystem\": null,\n    \"output_filesystem_kwargs\": {},\n    \"output_write_episodes\": true,\n    \"offline_sampling\": false,\n    \"evaluation_interval\": null,\n    \"evaluation_duration\": 10,\n    \"evaluation_duration_unit\": \"episodes\",\n    \"evaluation_sample_timeout_s\": 120.0,\n    \"evaluation_parallel_to_training\": false,\n    \"evaluation_force_reset_envs_before_iteration\": true,\n    \"evaluation_config\": null,\n    \"off_policy_estimation_methods\": {},\n    \"ope_split_batch_by_episode\": true,\n    \"evaluation_num_env_runners\": 0,\n    \"in_evaluation\": false,\n    \"sync_filters_on_rollout_workers_timeout_s\": 10.0,\n    \"keep_per_episode_custom_metrics\": false,\n    \"metrics_episode_collection_timeout_s\": 60.0,\n    \"metrics_num_episodes_for_smoothing\": 100,\n    \"min_time_s_per_iteration\": null,\n    \"min_train_timesteps_per_iteration\": 0,\n    \"min_sample_timesteps_per_iteration\": 0,\n    \"log_gradients\": true,\n    \"export_native_model_files\": false,\n    \"checkpoint_trainable_policies_only\": false,\n    \"logger_creator\": null,\n    \"logger_config\": null,\n    \"log_level\": \"WARN\",\n    \"log_sys_usage\": true,\n    \"fake_sampler\": false,\n    \"seed\": null,\n    \"_run_training_always_in_thread\": false,\n    \"_evaluation_parallel_to_training_wo_thread\": false,\n    \"restart_failed_env_runners\": true,\n    \"ignore_env_runner_failures\": false,\n    \"max_num_env_runner_restarts\": 1000,\n    \"delay_between_env_runner_restarts_s\": 60.0,\n    \"restart_failed_sub_environments\": false,\n    \"num_consecutive_env_runner_failures_tolerance\": 100,\n    \"env_runner_health_probe_timeout_s\": 30.0,\n    \"env_runner_restore_timeout_s\": 1800.0,\n    \"_model_config\": {},\n    \"_rl_module_spec\": null,\n    \"_AlgorithmConfig__prior_exploration_config\": null,\n    \"algorithm_config_overrides_per_module\": {},\n    \"_per_module_overrides\": {},\n    \"_torch_grad_scaler_class\": null,\n    \"_torch_lr_scheduler_classes\": null,\n    \"_tf_policy_handles_more_than_one_loss\": false,\n    \"_disable_preprocessor_api\": false,\n    \"_disable_action_flattening\": false,\n    \"_disable_initialize_loss_from_dummy_batch\": false,\n    \"_dont_auto_sync_env_runner_states\": false,\n    \"enable_connectors\": -1,\n    \"simple_optimizer\": -1,\n    \"policy_map_cache\": -1,\n    \"worker_cls\": -1,\n    \"synchronize_filters\": -1,\n    \"enable_async_evaluation\": -1,\n    \"custom_async_evaluation_function\": -1,\n    \"_enable_rl_module_api\": -1,\n    \"auto_wrap_old_gym_envs\": -1,\n    \"always_attach_evaluation_results\": -1,\n    \"replay_sequence_length\": null,\n    \"_disable_execution_plan_api\": -1,\n    \"lr_schedule\": null,\n    \"use_critic\": true,\n    \"use_gae\": true,\n    \"use_kl_loss\": true,\n    \"kl_coeff\": 0.2,\n    \"kl_target\": 0.01,\n    \"vf_loss_coeff\": 1.0,\n    \"entropy_coeff\": 0.0,\n    \"entropy_coeff_schedule\": null,\n    \"clip_param\": 0.3,\n    \"vf_clip_param\": 10.0,\n    \"sgd_minibatch_size\": -1,\n    \"vf_share_layers\": -1,\n    \"lambda\": 1.0,\n    \"input\": \"sampler\",\n    \"policies\": {\n      \"default_policy\": [\n        null,\n        null,\n        null,\n        null\n      ]\n    },\n    \"callbacks\": {\n      \"_type\": \"CLOUDPICKLE_FALLBACK\",\n      \"value\": \"800595e80a0000000000008c1b7261792e636c6f75647069636b6c652e636c6f75647069636b6c65948c145f6d616b655f736b656c65746f6e5f636c617373949394288c117261792e74756e652e63616c6c6261636b948c0d5f43616c6c6261636b4d6574619493948c10437261736841667465724e4974657273948c1e7261792e726c6c69622e616c676f726974686d732e63616c6c6261636b73948c1044656661756c7443616c6c6261636b7394939485947d948c0a5f5f6d6f64756c655f5f948c085f5f6d61696e5f5f94738c203034356465353639366535633435383462623536316437633161346664393434944e7494529468008c0f5f636c6173735f736574737461746594939468107d9428680c680d8c075f5f646f635f5f948c4a43616c6c6261636b2074686174206d616b65732074686520616c676f2063726173682061667465722061206365727461696e206176672e2072657475726e20697320726561636865642e948c085f5f696e69745f5f9468008c0e5f6d616b655f66756e6374696f6e9493942868008c0d5f6275696c74696e5f747970659493948c08436f6465547970659485945294284b014b004b004b014b024b03435695019700740100000000000000000000a6000000ab000000000000000000a0010000000000000000000000000000000000000000a6000000ab000000000000000000010064017c005f02000000000000000064005300944e8986948c0573757065729468168c0d5f73686f756c645f63726173689487948c0473656c669485948c36653a5c6c61625c526f626f5175616e745c70796c61625c7079716c61625c726c5c747261696e5f6d6b745f636f6e74696e75652e7079948c085f5f696e69745f5f948c19437261736841667465724e49746572732e5f5f696e69745f5f944b614329f88000dd080d89078c07d70818d20818d1081ad4081ad0081af006001e238804d4081ad0081ad0081a944300948c095f5f636c6173735f5f94859429749452947d94288c0b5f5f7061636b6167655f5f944e8c085f5f6e616d655f5f94680d8c085f5f66696c655f5f946825754e4e68008c105f6d616b655f656d7074795f63656c6c94939429529485947494529468008c125f66756e6374696f6e5f736574737461746594939468377d947d9428683068268c0c5f5f7175616c6e616d655f5f9468278c0f5f5f616e6e6f746174696f6e735f5f947d948c0e5f5f6b7764656661756c74735f5f944e8c0c5f5f64656661756c74735f5f944e680c680d68144e8c0b5f5f636c6f737572655f5f9468008c0a5f6d616b655f63656c6c94939468108594529485948c175f636c6f75647069636b6c655f7375626d6f64756c6573945d948c0b5f5f676c6f62616c735f5f947d947586948652308c0f6f6e5f747261696e5f726573756c7494681828681d284b014b004b034b054b054b0b43e097007c006a000000000000000000720f7403000000000000000000006401a6010000ab01000000000000000082017c0374040000000000000000000019000000000000000000740600000000000000000000190000000000000000007408000000000000000000006a0500000000000000006b05000000007231740d0000000000000000000064027c0374040000000000000000000019000000000000000000740600000000000000000000190000000000000000009b009d02a6010000ab010000000000000000010064037c005f000000000000000000640053006400530094284e8c2d496e74656e646564206372617368206166746572207265616368696e6720747269676765722072657475726e2e948c1a5265616368656420747269676765722072657475726e206f6620948874942868218c0c52756e74696d654572726f72948c12454e565f52554e4e45525f524553554c5453948c13455049534f44455f52455455524e5f4d45414e948c0461726773948c0b73746f705f726577617264948c057072696e749474942868238c09616c676f726974686d948c0e6d6574726963735f6c6f67676572948c06726573756c74948c066b776172677394749468258c0f6f6e5f747261696e5f726573756c74948c20437261736841667465724e49746572732e6f6e5f747261696e5f726573756c74944b6743848000f006000c10d40b1df000080926dd121ed01f4ed1124fd4124fd00c4fe00d13d51426d40d27d5283bd40d3cc504d44050d20d50d00d50dd0c11f00201114501d81319d51a2cd4132dd52e41d41342f00301114501f00001114501f103030d0ef400030d0ef000030d0ef0080022268844d40c1ed00c1ed00c1ef00b000e5101d00d50946829292974945294682e4e4e4e74945294683968637d947d94286830685d683c685e683d7d94683f4e68404e680c680d68144e68414e68475d9468497d942868528c0b656e765f72756e6e6572739468538c13657069736f64655f72657475726e5f6d65616e9468548c086172677061727365948c094e616d6573706163659493942981947d94288c04616c676f948c0350504f948c14656e61626c655f6e65775f6170695f737461636b94898c096672616d65776f726b948c05746f726368948c03656e76944e8c0f6e756d5f656e765f72756e6e657273944b088c176e756d5f656e76735f7065725f656e765f72756e6e6572944e8c0a6e756d5f6167656e7473944b008c1a6576616c756174696f6e5f6e756d5f656e765f72756e6e657273944b008c136576616c756174696f6e5f696e74657276616c944b008c136576616c756174696f6e5f6475726174696f6e944b0a8c186576616c756174696f6e5f6475726174696f6e5f756e6974948c08657069736f646573948c1f6576616c756174696f6e5f706172616c6c656c5f746f5f747261696e696e6794898c066f7574707574944e8c096c6f675f6c6576656c944e8c076e6f5f74756e6594898c0b6e756d5f73616d706c6573944b018c156d61785f636f6e63757272656e745f747269616c73944e8c07766572626f7365944b028c0f636865636b706f696e745f66726571944bc88c11636865636b706f696e745f61745f656e6494888c0977616e64625f6b6579944e8c0d77616e64625f70726f6a656374944e8c0e77616e64625f72756e5f6e616d65944e685547409f4000000000008c0a73746f705f6974657273944dd0078c0e73746f705f74696d657374657073944a809698008c0761735f7465737494898c0f61735f72656c656173655f7465737494898c086e756d5f67707573944b008c086e756d5f63707573944b008c0a6c6f63616c5f6d6f646594898c1173746f705f7265776172645f6372617368944de8038c12747261696e696e675f697465726174696f6e944b328c0a626c6f636b5f6e616d65948c03667574948c0776657273696f6e948c027631948c09646174615f66696c65948c2066745f616c6c2e616c6c2e30303137303531363134323435333030332e637376948c0872756e5f6d6f6465948c05747261696e948c0e696e697469616c5f616d6f756e74944a809698008c10747261696e5f746573745f73706c697494473fe999999999999a8c1076616c69646174696f6e5f73706c697494473fb999999999999a8c0d73616d706c655f6c656e677468944d2c018c0e72616e646f6d5f657069736f646594888c0c6d61785f64726177646f776e94473fe00000000000008c0973746f705f6c6f737394473fd33333333333338c107472616e73616374696f6e5f636f737494473f1a36e2eb1c432d7562757586948652308c135f5f61627374726163746d6574686f64735f5f942891948c095f6162635f696d706c945d94757d9486948652302e\"\n    },\n    \"create_env_on_driver\": true,\n    \"custom_eval_function\": null,\n    \"framework\": \"torch\"\n  },\n  \"_Trial__unresolved_config\": {\n    \"extra_python_environs_for_driver\": {},\n    \"extra_python_environs_for_worker\": {},\n    \"placement_strategy\": \"PACK\",\n    \"num_gpus\": 0,\n    \"_fake_gpus\": false,\n    \"num_cpus_for_main_process\": 1,\n    \"eager_tracing\": true,\n    \"eager_max_retraces\": 20,\n    \"tf_session_args\": {\n      \"intra_op_parallelism_threads\": 2,\n      \"inter_op_parallelism_threads\": 2,\n      \"gpu_options\": {\n        \"allow_growth\": true\n      },\n      \"log_device_placement\": false,\n      \"device_count\": {\n        \"CPU\": 1\n      },\n      \"allow_soft_placement\": true\n    },\n    \"local_tf_session_args\": {\n      \"intra_op_parallelism_threads\": 8,\n      \"inter_op_parallelism_threads\": 8\n    },\n    \"torch_compile_learner\": false,\n    \"torch_compile_learner_what_to_compile\": \"forward_train\",\n    \"torch_compile_learner_dynamo_backend\": \"inductor\",\n    \"torch_compile_learner_dynamo_mode\": null,\n    \"torch_compile_worker\": false,\n    \"torch_compile_worker_dynamo_backend\": \"onnxrt\",\n    \"torch_compile_worker_dynamo_mode\": null,\n    \"torch_ddp_kwargs\": {},\n    \"torch_skip_nan_gradients\": false,\n    \"enable_rl_module_and_learner\": false,\n    \"enable_env_runner_and_connector_v2\": false,\n    \"env\": \"markettiming_env\",\n    \"env_config\": {\n      \"name\": \"MarketTimingEnv\",\n      \"version\": \"v1\",\n      \"initial_amount\": 10000000,\n      \"gamma\": 0.98,\n      \"mode\": \"train\",\n      \"split_percent\": 0.9,\n      \"data_path\": \"E:/lab/RoboQuant/pylab/data\",\n      \"data_file\": \"ft_all.all.00170516142453003.csv\"\n    },\n    \"observation_space\": null,\n    \"action_space\": null,\n    \"clip_rewards\": null,\n    \"normalize_actions\": true,\n    \"clip_actions\": false,\n    \"_is_atari\": null,\n    \"disable_env_checking\": false,\n    \"env_task_fn\": null,\n    \"render_env\": false,\n    \"action_mask_key\": \"action_mask\",\n    \"env_runner_cls\": null,\n    \"num_env_runners\": 8,\n    \"num_envs_per_env_runner\": 1,\n    \"num_cpus_per_env_runner\": 1,\n    \"num_gpus_per_env_runner\": 0,\n    \"custom_resources_per_env_runner\": {},\n    \"validate_env_runners_after_construction\": true,\n    \"max_requests_in_flight_per_env_runner\": 2,\n    \"sample_timeout_s\": 60.0,\n    \"_env_to_module_connector\": null,\n    \"add_default_connectors_to_env_to_module_pipeline\": true,\n    \"_module_to_env_connector\": null,\n    \"add_default_connectors_to_module_to_env_pipeline\": true,\n    \"episode_lookback_horizon\": 1,\n    \"rollout_fragment_length\": \"auto\",\n    \"batch_mode\": \"truncate_episodes\",\n    \"compress_observations\": false,\n    \"remote_worker_envs\": false,\n    \"remote_env_batch_wait_ms\": 0,\n    \"enable_tf1_exec_eagerly\": false,\n    \"sample_collector\": [\n      \"__ref_ph\",\n      \"f176708f\"\n    ],\n    \"preprocessor_pref\": \"deepmind\",\n    \"observation_filter\": \"NoFilter\",\n    \"update_worker_filter_stats\": true,\n    \"use_worker_filter_stats\": true,\n    \"sampler_perf_stats_ema_coef\": null,\n    \"num_learners\": 0,\n    \"num_gpus_per_learner\": 0,\n    \"num_cpus_per_learner\": 1,\n    \"local_gpu_idx\": 0,\n    \"gamma\": 0.99,\n    \"lr\": 0.0001,\n    \"grad_clip\": null,\n    \"grad_clip_by\": \"global_norm\",\n    \"train_batch_size_per_learner\": null,\n    \"train_batch_size\": 4000,\n    \"num_epochs\": 30,\n    \"minibatch_size\": 128,\n    \"shuffle_batch_per_epoch\": true,\n    \"model\": {\n      \"fcnet_hiddens\": [\n        256,\n        256\n      ],\n      \"fcnet_activation\": \"tanh\",\n      \"fcnet_weights_initializer\": null,\n      \"fcnet_weights_initializer_config\": null,\n      \"fcnet_bias_initializer\": null,\n      \"fcnet_bias_initializer_config\": null,\n      \"conv_filters\": null,\n      \"conv_activation\": \"relu\",\n      \"conv_kernel_initializer\": null,\n      \"conv_kernel_initializer_config\": null,\n      \"conv_bias_initializer\": null,\n      \"conv_bias_initializer_config\": null,\n      \"conv_transpose_kernel_initializer\": null,\n      \"conv_transpose_kernel_initializer_config\": null,\n      \"conv_transpose_bias_initializer\": null,\n      \"conv_transpose_bias_initializer_config\": null,\n      \"post_fcnet_hiddens\": [],\n      \"post_fcnet_activation\": \"relu\",\n      \"post_fcnet_weights_initializer\": null,\n      \"post_fcnet_weights_initializer_config\": null,\n      \"post_fcnet_bias_initializer\": null,\n      \"post_fcnet_bias_initializer_config\": null,\n      \"free_log_std\": false,\n      \"log_std_clip_param\": 20.0,\n      \"no_final_linear\": false,\n      \"vf_share_layers\": false,\n      \"use_lstm\": false,\n      \"max_seq_len\": 20,\n      \"lstm_cell_size\": 256,\n      \"lstm_use_prev_action\": false,\n      \"lstm_use_prev_reward\": false,\n      \"lstm_weights_initializer\": null,\n      \"lstm_weights_initializer_config\": null,\n      \"lstm_bias_initializer\": null,\n      \"lstm_bias_initializer_config\": null,\n      \"_time_major\": false,\n      \"use_attention\": false,\n      \"attention_num_transformer_units\": 1,\n      \"attention_dim\": 64,\n      \"attention_num_heads\": 1,\n      \"attention_head_dim\": 32,\n      \"attention_memory_inference\": 50,\n      \"attention_memory_training\": 50,\n      \"attention_position_wise_mlp_dim\": 32,\n      \"attention_init_gru_gate_bias\": 2.0,\n      \"attention_use_n_prev_actions\": 0,\n      \"attention_use_n_prev_rewards\": 0,\n      \"framestack\": true,\n      \"dim\": 84,\n      \"grayscale\": false,\n      \"zero_mean\": true,\n      \"custom_model\": null,\n      \"custom_model_config\": {},\n      \"custom_action_dist\": null,\n      \"custom_preprocessor\": null,\n      \"encoder_latent_dim\": null,\n      \"always_check_shapes\": false,\n      \"lstm_use_prev_action_reward\": -1,\n      \"_use_default_native_models\": -1,\n      \"_disable_preprocessor_api\": false,\n      \"_disable_action_flattening\": false\n    },\n    \"_learner_connector\": null,\n    \"add_default_connectors_to_learner_pipeline\": true,\n    \"learner_config_dict\": {},\n    \"optimizer\": {\n      \"type\": \"SGD\",\n      \"lr\": 0.01,\n      \"momentum\": 0.9\n    },\n    \"_learner_class\": null,\n    \"explore\": true,\n    \"exploration_config\": {\n      \"type\": \"StochasticSampling\"\n    },\n    \"count_steps_by\": \"env_steps\",\n    \"policy_map_capacity\": 100,\n    \"policy_mapping_fn\": [\n      \"__ref_ph\",\n      \"cdf20c8b\"\n    ],\n    \"policies_to_train\": null,\n    \"policy_states_are_swappable\": false,\n    \"observation_fn\": null,\n    \"input_read_method\": \"read_parquet\",\n    \"input_read_method_kwargs\": {},\n    \"input_read_schema\": {},\n    \"input_read_episodes\": false,\n    \"input_read_sample_batches\": false,\n    \"input_read_batch_size\": null,\n    \"input_filesystem\": null,\n    \"input_filesystem_kwargs\": {},\n    \"input_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"input_spaces_jsonable\": true,\n    \"materialize_data\": false,\n    \"materialize_mapped_data\": true,\n    \"map_batches_kwargs\": {},\n    \"iter_batches_kwargs\": {},\n    \"prelearner_class\": null,\n    \"prelearner_buffer_class\": null,\n    \"prelearner_buffer_kwargs\": {},\n    \"prelearner_module_synch_period\": 10,\n    \"dataset_num_iters_per_learner\": null,\n    \"input_config\": {},\n    \"actions_in_input_normalized\": false,\n    \"postprocess_inputs\": false,\n    \"shuffle_buffer_size\": 0,\n    \"output\": null,\n    \"output_config\": {},\n    \"output_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"output_max_file_size\": 67108864,\n    \"output_max_rows_per_file\": null,\n    \"output_write_method\": \"write_parquet\",\n    \"output_write_method_kwargs\": {},\n    \"output_filesystem\": null,\n    \"output_filesystem_kwargs\": {},\n    \"output_write_episodes\": true,\n    \"offline_sampling\": false,\n    \"evaluation_interval\": null,\n    \"evaluation_duration\": 10,\n    \"evaluation_duration_unit\": \"episodes\",\n    \"evaluation_sample_timeout_s\": 120.0,\n    \"evaluation_parallel_to_training\": false,\n    \"evaluation_force_reset_envs_before_iteration\": true,\n    \"evaluation_config\": null,\n    \"off_policy_estimation_methods\": {},\n    \"ope_split_batch_by_episode\": true,\n    \"evaluation_num_env_runners\": 0,\n    \"in_evaluation\": false,\n    \"sync_filters_on_rollout_workers_timeout_s\": 10.0,\n    \"keep_per_episode_custom_metrics\": false,\n    \"metrics_episode_collection_timeout_s\": 60.0,\n    \"metrics_num_episodes_for_smoothing\": 100,\n    \"min_time_s_per_iteration\": null,\n    \"min_train_timesteps_per_iteration\": 0,\n    \"min_sample_timesteps_per_iteration\": 0,\n    \"log_gradients\": true,\n    \"export_native_model_files\": false,\n    \"checkpoint_trainable_policies_only\": false,\n    \"logger_creator\": null,\n    \"logger_config\": null,\n    \"log_level\": \"WARN\",\n    \"log_sys_usage\": true,\n    \"fake_sampler\": false,\n    \"seed\": null,\n    \"_run_training_always_in_thread\": false,\n    \"_evaluation_parallel_to_training_wo_thread\": false,\n    \"restart_failed_env_runners\": true,\n    \"ignore_env_runner_failures\": false,\n    \"max_num_env_runner_restarts\": 1000,\n    \"delay_between_env_runner_restarts_s\": 60.0,\n    \"restart_failed_sub_environments\": false,\n    \"num_consecutive_env_runner_failures_tolerance\": 100,\n    \"env_runner_health_probe_timeout_s\": 30.0,\n    \"env_runner_restore_timeout_s\": 1800.0,\n    \"_model_config\": {},\n    \"_rl_module_spec\": null,\n    \"_AlgorithmConfig__prior_exploration_config\": null,\n    \"algorithm_config_overrides_per_module\": {},\n    \"_per_module_overrides\": {},\n    \"_torch_grad_scaler_class\": null,\n    \"_torch_lr_scheduler_classes\": null,\n    \"_tf_policy_handles_more_than_one_loss\": false,\n    \"_disable_preprocessor_api\": false,\n    \"_disable_action_flattening\": false,\n    \"_disable_initialize_loss_from_dummy_batch\": false,\n    \"_dont_auto_sync_env_runner_states\": false,\n    \"enable_connectors\": -1,\n    \"simple_optimizer\": -1,\n    \"policy_map_cache\": -1,\n    \"worker_cls\": -1,\n    \"synchronize_filters\": -1,\n    \"enable_async_evaluation\": -1,\n    \"custom_async_evaluation_function\": -1,\n    \"_enable_rl_module_api\": -1,\n    \"auto_wrap_old_gym_envs\": -1,\n    \"always_attach_evaluation_results\": -1,\n    \"replay_sequence_length\": null,\n    \"_disable_execution_plan_api\": -1,\n    \"lr_schedule\": null,\n    \"use_critic\": true,\n    \"use_gae\": true,\n    \"use_kl_loss\": true,\n    \"kl_coeff\": 0.2,\n    \"kl_target\": 0.01,\n    \"vf_loss_coeff\": 1.0,\n    \"entropy_coeff\": 0.0,\n    \"entropy_coeff_schedule\": null,\n    \"clip_param\": 0.3,\n    \"vf_clip_param\": 10.0,\n    \"sgd_minibatch_size\": -1,\n    \"vf_share_layers\": -1,\n    \"lambda\": 1.0,\n    \"input\": \"sampler\",\n    \"policies\": {\n      \"default_policy\": [\n        null,\n        null,\n        null,\n        null\n      ]\n    },\n    \"callbacks\": [\n      \"__ref_ph\",\n      \"8913b504\"\n    ],\n    \"create_env_on_driver\": true,\n    \"custom_eval_function\": null,\n    \"framework\": \"torch\"\n  },\n  \"evaluated_params\": {},\n  \"experiment_tag\": \"0\",\n  \"stopping_criterion\": {\n    \"env_runners/episode_return_mean\": 2000.0,\n    \"training_iteration\": 50\n  },\n  \"_setup_default_resource\": true,\n  \"_default_placement_group_factory\": \"80054e2e\",\n  \"placement_group_factory\": \"8005951b010000000000008c237261792e74756e652e657865637574696f6e2e706c6163656d656e745f67726f757073948c15506c6163656d656e7447726f7570466163746f72799493942981947d94288c085f62756e646c6573945d94287d948c0343505594473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff000000000000073658c155f686561645f62756e646c655f69735f656d70747994898c095f7374726174656779948c045041434b948c055f6172677394298c075f6b7761726773947d9475622e\",\n  \"log_to_file\": [\n    null,\n    null\n  ],\n  \"max_failures\": 0,\n  \"_default_result_or_future\": null,\n  \"export_formats\": [],\n  \"status\": \"TERMINATED\",\n  \"relative_logdir\": \"PPO_markettiming_env_aaa74_00000_0_2025-01-06_14-08-36\",\n  \"trial_name_creator\": null,\n  \"trial_dirname_creator\": null,\n  \"custom_trial_name\": null,\n  \"custom_dirname\": null,\n  \"restore_path\": null,\n  \"_restore_checkpoint_result\": null,\n  \"_state_json\": null,\n  \"results\": \"80054e2e\",\n  \"extra_arg\": \"80054e2e\",\n  \"_resources\": \"80054e2e\"\n}", "{\n  \"start_time\": 1736143743.5980535,\n  \"num_failures\": 0,\n  \"num_failures_after_restore\": 0,\n  \"error_filename\": null,\n  \"pickled_error_filename\": null,\n  \"last_result\": {\n    \"custom_metrics\": {},\n    \"episode_media\": {},\n    \"info\": {\n      \"learner\": {\n        \"default_policy\": {\n          \"learner_stats\": {\n            \"allreduce_latency\": 0.0,\n            \"grad_gnorm\": 13.00508956764975,\n            \"cur_kl_coeff\": 0.20000000000000004,\n            \"cur_lr\": 0.00010000000000000003,\n            \"total_loss\": 0.7416076113630127,\n            \"policy_loss\": -0.013151719495253537,\n            \"vf_loss\": 0.7530719340176156,\n            \"vf_explained_var\": -0.20035952188635386,\n            \"kl\": 0.008436998933622465,\n            \"entropy\": 0.6046871279516528,\n            \"entropy_coeff\": 0.0\n          },\n          \"model\": {},\n          \"custom_metrics\": {},\n          \"num_agent_steps_trained\": 128.0,\n          \"num_grad_updates_lifetime\": 46035.5,\n          \"diff_num_grad_updates_vs_sampler_policy\": 464.5\n        }\n      },\n      \"num_env_steps_sampled\": 200000,\n      \"num_env_steps_trained\": 200000,\n      \"num_agent_steps_sampled\": 200000,\n      \"num_agent_steps_trained\": 200000\n    },\n    \"env_runners\": {\n      \"episode_reward_max\": NaN,\n      \"episode_reward_min\": NaN,\n      \"episode_reward_mean\": NaN,\n      \"episode_len_mean\": NaN,\n      \"episode_media\": {},\n      \"episodes_timesteps_total\": 0,\n      \"policy_reward_min\": {},\n      \"policy_reward_max\": {},\n      \"policy_reward_mean\": {},\n      \"custom_metrics\": {},\n      \"hist_stats\": {\n        \"episode_reward\": [],\n        \"episode_lengths\": []\n      },\n      \"sampler_perf\": {},\n      \"num_faulty_episodes\": 0,\n      \"connector_metrics\": {},\n      \"num_episodes\": 0,\n      \"episode_return_max\": NaN,\n      \"episode_return_min\": NaN,\n      \"episode_return_mean\": NaN,\n      \"episodes_this_iter\": 0\n    },\n    \"num_healthy_workers\": 8,\n    \"num_in_flight_async_sample_reqs\": 0,\n    \"num_remote_worker_restarts\": 0,\n    \"num_agent_steps_sampled\": 200000,\n    \"num_agent_steps_trained\": 200000,\n    \"num_env_steps_sampled\": 200000,\n    \"num_env_steps_trained\": 200000,\n    \"num_env_steps_sampled_this_iter\": 4000,\n    \"num_env_steps_trained_this_iter\": 4000,\n    \"num_env_steps_sampled_throughput_per_sec\": 414.10667563602397,\n    \"num_env_steps_trained_throughput_per_sec\": 414.10667563602397,\n    \"timesteps_total\": 200000,\n    \"num_env_steps_sampled_lifetime\": 200000,\n    \"num_agent_steps_sampled_lifetime\": 200000,\n    \"num_steps_trained_this_iter\": 4000,\n    \"agent_timesteps_total\": 200000,\n    \"timers\": {\n      \"training_iteration_time_ms\": 10719.097,\n      \"restore_workers_time_ms\": 0.0,\n      \"training_step_time_ms\": 10719.097,\n      \"sample_time_ms\": 3041.438,\n      \"load_time_ms\": 0.101,\n      \"load_throughput\": 39429414.806,\n      \"learn_time_ms\": 7660.081,\n      \"learn_throughput\": 522.188,\n      \"synch_weights_time_ms\": 16.63\n    },\n    \"counters\": {\n      \"num_env_steps_sampled\": 200000,\n      \"num_env_steps_trained\": 200000,\n      \"num_agent_steps_sampled\": 200000,\n      \"num_agent_steps_trained\": 200000\n    },\n    \"done\": true,\n    \"training_iteration\": 50,\n    \"trial_id\": \"aaa74_00000\",\n    \"date\": \"2025-01-06_14-17-40\",\n    \"timestamp\": 1736144260,\n    \"time_this_iter_s\": 9.668057441711426,\n    \"time_total_s\": 516.3769698143005,\n    \"pid\": 6784,\n    \"hostname\": \"Matebook-white\",\n    \"node_ip\": \"127.0.0.1\",\n    \"config\": {\n      \"extra_python_environs_for_driver\": {},\n      \"extra_python_environs_for_worker\": {},\n      \"placement_strategy\": \"PACK\",\n      \"num_gpus\": 0,\n      \"_fake_gpus\": false,\n      \"num_cpus_for_main_process\": 1,\n      \"eager_tracing\": true,\n      \"eager_max_retraces\": 20,\n      \"tf_session_args\": {\n        \"intra_op_parallelism_threads\": 2,\n        \"inter_op_parallelism_threads\": 2,\n        \"gpu_options\": {\n          \"allow_growth\": true\n        },\n        \"log_device_placement\": false,\n        \"device_count\": {\n          \"CPU\": 1\n        },\n        \"allow_soft_placement\": true\n      },\n      \"local_tf_session_args\": {\n        \"intra_op_parallelism_threads\": 8,\n        \"inter_op_parallelism_threads\": 8\n      },\n      \"torch_compile_learner\": false,\n      \"torch_compile_learner_what_to_compile\": \"forward_train\",\n      \"torch_compile_learner_dynamo_backend\": \"inductor\",\n      \"torch_compile_learner_dynamo_mode\": null,\n      \"torch_compile_worker\": false,\n      \"torch_compile_worker_dynamo_backend\": \"onnxrt\",\n      \"torch_compile_worker_dynamo_mode\": null,\n      \"torch_ddp_kwargs\": {},\n      \"torch_skip_nan_gradients\": false,\n      \"enable_rl_module_and_learner\": false,\n      \"enable_env_runner_and_connector_v2\": false,\n      \"env\": \"markettiming_env\",\n      \"env_config\": {\n        \"name\": \"MarketTimingEnv\",\n        \"version\": \"v1\",\n        \"initial_amount\": 10000000,\n        \"gamma\": 0.98,\n        \"mode\": \"train\",\n        \"split_percent\": 0.9,\n        \"data_path\": \"E:/lab/RoboQuant/pylab/data\",\n        \"data_file\": \"ft_all.all.00170516142453003.csv\"\n      },\n      \"observation_space\": null,\n      \"action_space\": null,\n      \"clip_rewards\": null,\n      \"normalize_actions\": true,\n      \"clip_actions\": false,\n      \"_is_atari\": null,\n      \"disable_env_checking\": false,\n      \"env_task_fn\": null,\n      \"render_env\": false,\n      \"action_mask_key\": \"action_mask\",\n      \"env_runner_cls\": null,\n      \"num_env_runners\": 8,\n      \"num_envs_per_env_runner\": 1,\n      \"num_cpus_per_env_runner\": 1,\n      \"num_gpus_per_env_runner\": 0,\n      \"custom_resources_per_env_runner\": {},\n      \"validate_env_runners_after_construction\": true,\n      \"max_requests_in_flight_per_env_runner\": 2,\n      \"sample_timeout_s\": 60.0,\n      \"_env_to_module_connector\": null,\n      \"add_default_connectors_to_env_to_module_pipeline\": true,\n      \"_module_to_env_connector\": null,\n      \"add_default_connectors_to_module_to_env_pipeline\": true,\n      \"episode_lookback_horizon\": 1,\n      \"rollout_fragment_length\": \"auto\",\n      \"batch_mode\": \"truncate_episodes\",\n      \"compress_observations\": false,\n      \"remote_worker_envs\": false,\n      \"remote_env_batch_wait_ms\": 0,\n      \"enable_tf1_exec_eagerly\": false,\n      \"sample_collector\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059551000000000000008c357261792e726c6c69622e6576616c756174696f6e2e636f6c6c6563746f72732e73696d706c655f6c6973745f636f6c6c6563746f72948c1353696d706c654c697374436f6c6c6563746f729493942e\"\n      },\n      \"preprocessor_pref\": \"deepmind\",\n      \"observation_filter\": \"NoFilter\",\n      \"update_worker_filter_stats\": true,\n      \"use_worker_filter_stats\": true,\n      \"sampler_perf_stats_ema_coef\": null,\n      \"num_learners\": 0,\n      \"num_gpus_per_learner\": 0,\n      \"num_cpus_per_learner\": 1,\n      \"local_gpu_idx\": 0,\n      \"gamma\": 0.99,\n      \"lr\": 0.0001,\n      \"grad_clip\": null,\n      \"grad_clip_by\": \"global_norm\",\n      \"train_batch_size_per_learner\": null,\n      \"train_batch_size\": 4000,\n      \"num_epochs\": 30,\n      \"minibatch_size\": 128,\n      \"shuffle_batch_per_epoch\": true,\n      \"model\": {\n        \"fcnet_hiddens\": [\n          256,\n          256\n        ],\n        \"fcnet_activation\": \"tanh\",\n        \"fcnet_weights_initializer\": null,\n        \"fcnet_weights_initializer_config\": null,\n        \"fcnet_bias_initializer\": null,\n        \"fcnet_bias_initializer_config\": null,\n        \"conv_filters\": null,\n        \"conv_activation\": \"relu\",\n        \"conv_kernel_initializer\": null,\n        \"conv_kernel_initializer_config\": null,\n        \"conv_bias_initializer\": null,\n        \"conv_bias_initializer_config\": null,\n        \"conv_transpose_kernel_initializer\": null,\n        \"conv_transpose_kernel_initializer_config\": null,\n        \"conv_transpose_bias_initializer\": null,\n        \"conv_transpose_bias_initializer_config\": null,\n        \"post_fcnet_hiddens\": [],\n        \"post_fcnet_activation\": \"relu\",\n        \"post_fcnet_weights_initializer\": null,\n        \"post_fcnet_weights_initializer_config\": null,\n        \"post_fcnet_bias_initializer\": null,\n        \"post_fcnet_bias_initializer_config\": null,\n        \"free_log_std\": false,\n        \"log_std_clip_param\": 20.0,\n        \"no_final_linear\": false,\n        \"vf_share_layers\": false,\n        \"use_lstm\": false,\n        \"max_seq_len\": 20,\n        \"lstm_cell_size\": 256,\n        \"lstm_use_prev_action\": false,\n        \"lstm_use_prev_reward\": false,\n        \"lstm_weights_initializer\": null,\n        \"lstm_weights_initializer_config\": null,\n        \"lstm_bias_initializer\": null,\n        \"lstm_bias_initializer_config\": null,\n        \"_time_major\": false,\n        \"use_attention\": false,\n        \"attention_num_transformer_units\": 1,\n        \"attention_dim\": 64,\n        \"attention_num_heads\": 1,\n        \"attention_head_dim\": 32,\n        \"attention_memory_inference\": 50,\n        \"attention_memory_training\": 50,\n        \"attention_position_wise_mlp_dim\": 32,\n        \"attention_init_gru_gate_bias\": 2.0,\n        \"attention_use_n_prev_actions\": 0,\n        \"attention_use_n_prev_rewards\": 0,\n        \"framestack\": true,\n        \"dim\": 84,\n        \"grayscale\": false,\n        \"zero_mean\": true,\n        \"custom_model\": null,\n        \"custom_model_config\": {},\n        \"custom_action_dist\": null,\n        \"custom_preprocessor\": null,\n        \"encoder_latent_dim\": null,\n        \"always_check_shapes\": false,\n        \"lstm_use_prev_action_reward\": -1,\n        \"_use_default_native_models\": -1,\n        \"_disable_preprocessor_api\": false,\n        \"_disable_action_flattening\": false\n      },\n      \"_learner_connector\": null,\n      \"add_default_connectors_to_learner_pipeline\": true,\n      \"learner_config_dict\": {},\n      \"optimizer\": {\n        \"type\": \"SGD\",\n        \"lr\": 0.01,\n        \"momentum\": 0.9\n      },\n      \"_learner_class\": null,\n      \"explore\": true,\n      \"exploration_config\": {\n        \"type\": \"StochasticSampling\"\n      },\n      \"count_steps_by\": \"env_steps\",\n      \"policy_map_capacity\": 100,\n      \"policy_mapping_fn\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059557000000000000008c257261792e726c6c69622e616c676f726974686d732e616c676f726974686d5f636f6e666967948c29416c676f726974686d436f6e6669672e44454641554c545f504f4c4943595f4d415050494e475f464e9493942e\"\n      },\n      \"policies_to_train\": null,\n      \"policy_states_are_swappable\": false,\n      \"observation_fn\": null,\n      \"input_read_method\": \"read_parquet\",\n      \"input_read_method_kwargs\": {},\n      \"input_read_schema\": {},\n      \"input_read_episodes\": false,\n      \"input_read_sample_batches\": false,\n      \"input_read_batch_size\": null,\n      \"input_filesystem\": null,\n      \"input_filesystem_kwargs\": {},\n      \"input_compress_columns\": [\n        \"obs\",\n        \"new_obs\"\n      ],\n      \"input_spaces_jsonable\": true,\n      \"materialize_data\": false,\n      \"materialize_mapped_data\": true,\n      \"map_batches_kwargs\": {},\n      \"iter_batches_kwargs\": {},\n      \"prelearner_class\": null,\n      \"prelearner_buffer_class\": null,\n      \"prelearner_buffer_kwargs\": {},\n      \"prelearner_module_synch_period\": 10,\n      \"dataset_num_iters_per_learner\": null,\n      \"input_config\": {},\n      \"actions_in_input_normalized\": false,\n      \"postprocess_inputs\": false,\n      \"shuffle_buffer_size\": 0,\n      \"output\": null,\n      \"output_config\": {},\n      \"output_compress_columns\": [\n        \"obs\",\n        \"new_obs\"\n      ],\n      \"output_max_file_size\": 67108864,\n      \"output_max_rows_per_file\": null,\n      \"output_write_method\": \"write_parquet\",\n      \"output_write_method_kwargs\": {},\n      \"output_filesystem\": null,\n      \"output_filesystem_kwargs\": {},\n      \"output_write_episodes\": true,\n      \"offline_sampling\": false,\n      \"evaluation_interval\": null,\n      \"evaluation_duration\": 10,\n      \"evaluation_duration_unit\": \"episodes\",\n      \"evaluation_sample_timeout_s\": 120.0,\n      \"evaluation_parallel_to_training\": false,\n      \"evaluation_force_reset_envs_before_iteration\": true,\n      \"evaluation_config\": null,\n      \"off_policy_estimation_methods\": {},\n      \"ope_split_batch_by_episode\": true,\n      \"evaluation_num_env_runners\": 0,\n      \"in_evaluation\": false,\n      \"sync_filters_on_rollout_workers_timeout_s\": 10.0,\n      \"keep_per_episode_custom_metrics\": false,\n      \"metrics_episode_collection_timeout_s\": 60.0,\n      \"metrics_num_episodes_for_smoothing\": 100,\n      \"min_time_s_per_iteration\": null,\n      \"min_train_timesteps_per_iteration\": 0,\n      \"min_sample_timesteps_per_iteration\": 0,\n      \"log_gradients\": true,\n      \"export_native_model_files\": false,\n      \"checkpoint_trainable_policies_only\": false,\n      \"logger_creator\": null,\n      \"logger_config\": null,\n      \"log_level\": \"WARN\",\n      \"log_sys_usage\": true,\n      \"fake_sampler\": false,\n      \"seed\": null,\n      \"_run_training_always_in_thread\": false,\n      \"_evaluation_parallel_to_training_wo_thread\": false,\n      \"restart_failed_env_runners\": true,\n      \"ignore_env_runner_failures\": false,\n      \"max_num_env_runner_restarts\": 1000,\n      \"delay_between_env_runner_restarts_s\": 60.0,\n      \"restart_failed_sub_environments\": false,\n      \"num_consecutive_env_runner_failures_tolerance\": 100,\n      \"env_runner_health_probe_timeout_s\": 30.0,\n      \"env_runner_restore_timeout_s\": 1800.0,\n      \"_model_config\": {},\n      \"_rl_module_spec\": null,\n      \"_AlgorithmConfig__prior_exploration_config\": null,\n      \"algorithm_config_overrides_per_module\": {},\n      \"_per_module_overrides\": {},\n      \"_torch_grad_scaler_class\": null,\n      \"_torch_lr_scheduler_classes\": null,\n      \"_tf_policy_handles_more_than_one_loss\": false,\n      \"_disable_preprocessor_api\": false,\n      \"_disable_action_flattening\": false,\n      \"_disable_initialize_loss_from_dummy_batch\": false,\n      \"_dont_auto_sync_env_runner_states\": false,\n      \"enable_connectors\": -1,\n      \"simple_optimizer\": false,\n      \"policy_map_cache\": -1,\n      \"worker_cls\": -1,\n      \"synchronize_filters\": -1,\n      \"enable_async_evaluation\": -1,\n      \"custom_async_evaluation_function\": -1,\n      \"_enable_rl_module_api\": -1,\n      \"auto_wrap_old_gym_envs\": -1,\n      \"always_attach_evaluation_results\": -1,\n      \"replay_sequence_length\": null,\n      \"_disable_execution_plan_api\": -1,\n      \"lr_schedule\": null,\n      \"use_critic\": true,\n      \"use_gae\": true,\n      \"use_kl_loss\": true,\n      \"kl_coeff\": 0.2,\n      \"kl_target\": 0.01,\n      \"vf_loss_coeff\": 1.0,\n      \"entropy_coeff\": 0.0,\n      \"entropy_coeff_schedule\": null,\n      \"clip_param\": 0.3,\n      \"vf_clip_param\": 10.0,\n      \"sgd_minibatch_size\": -1,\n      \"vf_share_layers\": -1,\n      \"__stdout_file__\": null,\n      \"__stderr_file__\": null,\n      \"lambda\": 1.0,\n      \"input\": \"sampler\",\n      \"policies\": {\n        \"default_policy\": [\n          null,\n          null,\n          null,\n          null\n        ]\n      },\n      \"callbacks\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595e80a0000000000008c1b7261792e636c6f75647069636b6c652e636c6f75647069636b6c65948c145f6d616b655f736b656c65746f6e5f636c617373949394288c117261792e74756e652e63616c6c6261636b948c0d5f43616c6c6261636b4d6574619493948c10437261736841667465724e4974657273948c1e7261792e726c6c69622e616c676f726974686d732e63616c6c6261636b73948c1044656661756c7443616c6c6261636b7394939485947d948c0a5f5f6d6f64756c655f5f948c085f5f6d61696e5f5f94738c203034356465353639366535633435383462623536316437633161346664393434944e7494529468008c0f5f636c6173735f736574737461746594939468107d9428680c680d8c075f5f646f635f5f948c4a43616c6c6261636b2074686174206d616b65732074686520616c676f2063726173682061667465722061206365727461696e206176672e2072657475726e20697320726561636865642e948c085f5f696e69745f5f9468008c0e5f6d616b655f66756e6374696f6e9493942868008c0d5f6275696c74696e5f747970659493948c08436f6465547970659485945294284b014b004b004b014b024b03435695019700740100000000000000000000a6000000ab000000000000000000a0010000000000000000000000000000000000000000a6000000ab000000000000000000010064017c005f02000000000000000064005300944e8986948c0573757065729468168c0d5f73686f756c645f63726173689487948c0473656c669485948c36653a5c6c61625c526f626f5175616e745c70796c61625c7079716c61625c726c5c747261696e5f6d6b745f636f6e74696e75652e7079948c085f5f696e69745f5f948c19437261736841667465724e49746572732e5f5f696e69745f5f944b614329f88000dd080d89078c07d70818d20818d1081ad4081ad0081af006001e238804d4081ad0081ad0081a944300948c095f5f636c6173735f5f94859429749452947d94288c0b5f5f7061636b6167655f5f944e8c085f5f6e616d655f5f94680d8c085f5f66696c655f5f946825754e4e68008c105f6d616b655f656d7074795f63656c6c94939429529485947494529468008c125f66756e6374696f6e5f736574737461746594939468377d947d9428683068268c0c5f5f7175616c6e616d655f5f9468278c0f5f5f616e6e6f746174696f6e735f5f947d948c0e5f5f6b7764656661756c74735f5f944e8c0c5f5f64656661756c74735f5f944e680c680d68144e8c0b5f5f636c6f737572655f5f9468008c0a5f6d616b655f63656c6c94939468108594529485948c175f636c6f75647069636b6c655f7375626d6f64756c6573945d948c0b5f5f676c6f62616c735f5f947d947586948652308c0f6f6e5f747261696e5f726573756c7494681828681d284b014b004b034b054b054b0b43e097007c006a000000000000000000720f7403000000000000000000006401a6010000ab01000000000000000082017c0374040000000000000000000019000000000000000000740600000000000000000000190000000000000000007408000000000000000000006a0500000000000000006b05000000007231740d0000000000000000000064027c0374040000000000000000000019000000000000000000740600000000000000000000190000000000000000009b009d02a6010000ab010000000000000000010064037c005f000000000000000000640053006400530094284e8c2d496e74656e646564206372617368206166746572207265616368696e6720747269676765722072657475726e2e948c1a5265616368656420747269676765722072657475726e206f6620948874942868218c0c52756e74696d654572726f72948c12454e565f52554e4e45525f524553554c5453948c13455049534f44455f52455455524e5f4d45414e948c0461726773948c0b73746f705f726577617264948c057072696e749474942868238c09616c676f726974686d948c0e6d6574726963735f6c6f67676572948c06726573756c74948c066b776172677394749468258c0f6f6e5f747261696e5f726573756c74948c20437261736841667465724e49746572732e6f6e5f747261696e5f726573756c74944b6743848000f006000c10d40b1df000080926dd121ed01f4ed1124fd4124fd00c4fe00d13d51426d40d27d5283bd40d3cc504d44050d20d50d00d50dd0c11f00201114501d81319d51a2cd4132dd52e41d41342f00301114501f00001114501f103030d0ef400030d0ef000030d0ef0080022268844d40c1ed00c1ed00c1ef00b000e5101d00d50946829292974945294682e4e4e4e74945294683968637d947d94286830685d683c685e683d7d94683f4e68404e680c680d68144e68414e68475d9468497d942868528c0b656e765f72756e6e6572739468538c13657069736f64655f72657475726e5f6d65616e9468548c086172677061727365948c094e616d6573706163659493942981947d94288c04616c676f948c0350504f948c14656e61626c655f6e65775f6170695f737461636b94898c096672616d65776f726b948c05746f726368948c03656e76944e8c0f6e756d5f656e765f72756e6e657273944b088c176e756d5f656e76735f7065725f656e765f72756e6e6572944e8c0a6e756d5f6167656e7473944b008c1a6576616c756174696f6e5f6e756d5f656e765f72756e6e657273944b008c136576616c756174696f6e5f696e74657276616c944b008c136576616c756174696f6e5f6475726174696f6e944b0a8c186576616c756174696f6e5f6475726174696f6e5f756e6974948c08657069736f646573948c1f6576616c756174696f6e5f706172616c6c656c5f746f5f747261696e696e6794898c066f7574707574944e8c096c6f675f6c6576656c944e8c076e6f5f74756e6594898c0b6e756d5f73616d706c6573944b018c156d61785f636f6e63757272656e745f747269616c73944e8c07766572626f7365944b028c0f636865636b706f696e745f66726571944bc88c11636865636b706f696e745f61745f656e6494888c0977616e64625f6b6579944e8c0d77616e64625f70726f6a656374944e8c0e77616e64625f72756e5f6e616d65944e685547409f4000000000008c0a73746f705f6974657273944dd0078c0e73746f705f74696d657374657073944a809698008c0761735f7465737494898c0f61735f72656c656173655f7465737494898c086e756d5f67707573944b008c086e756d5f63707573944b008c0a6c6f63616c5f6d6f646594898c1173746f705f7265776172645f6372617368944de8038c12747261696e696e675f697465726174696f6e944b328c0a626c6f636b5f6e616d65948c03667574948c0776657273696f6e948c027631948c09646174615f66696c65948c2066745f616c6c2e616c6c2e30303137303531363134323435333030332e637376948c0872756e5f6d6f6465948c05747261696e948c0e696e697469616c5f616d6f756e74944a809698008c10747261696e5f746573745f73706c697494473fe999999999999a8c1076616c69646174696f6e5f73706c697494473fb999999999999a8c0d73616d706c655f6c656e677468944d2c018c0e72616e646f6d5f657069736f646594888c0c6d61785f64726177646f776e94473fe00000000000008c0973746f705f6c6f737394473fd33333333333338c107472616e73616374696f6e5f636f737494473f1a36e2eb1c432d7562757586948652308c135f5f61627374726163746d6574686f64735f5f942891948c095f6162635f696d706c945d94757d9486948652302e\"\n      },\n      \"create_env_on_driver\": true,\n      \"custom_eval_function\": null,\n      \"framework\": \"torch\"\n    },\n    \"time_since_restore\": 516.3769698143005,\n    \"iterations_since_restore\": 50,\n    \"perf\": {\n      \"cpu_util_percent\": 29.807142857142853,\n      \"ram_util_percent\": 89.44285714285716\n    },\n    \"experiment_tag\": \"0\"\n  },\n  \"last_result_time\": **********.0183702,\n  \"metric_analysis\": {\n    \"num_healthy_workers\": {\n      \"max\": 8,\n      \"min\": 8,\n      \"avg\": 7.999999999999999,\n      \"last\": 8,\n      \"last-5-avg\": 8.0,\n      \"last-10-avg\": 8.0\n    },\n    \"num_in_flight_async_sample_reqs\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"num_remote_worker_restarts\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"num_agent_steps_sampled\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_agent_steps_trained\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_env_steps_sampled\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_env_steps_trained\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_env_steps_sampled_this_iter\": {\n      \"max\": 4000,\n      \"min\": 4000,\n      \"avg\": 3999.9999999999995,\n      \"last\": 4000,\n      \"last-5-avg\": 4000.0,\n      \"last-10-avg\": 4000.0\n    },\n    \"num_env_steps_trained_this_iter\": {\n      \"max\": 4000,\n      \"min\": 4000,\n      \"avg\": 3999.9999999999995,\n      \"last\": 4000,\n      \"last-5-avg\": 4000.0,\n      \"last-10-avg\": 4000.0\n    },\n    \"num_env_steps_sampled_throughput_per_sec\": {\n      \"max\": 499.0772529335396,\n      \"min\": 298.89167079254906,\n      \"avg\": 391.1466474434638,\n      \"last\": 414.10667563602397,\n      \"last-5-avg\": 360.56531526809795,\n      \"last-10-avg\": 376.715253331762\n    },\n    \"num_env_steps_trained_throughput_per_sec\": {\n      \"max\": 499.0772529335396,\n      \"min\": 298.89167079254906,\n      \"avg\": 391.1466474434638,\n      \"last\": 414.10667563602397,\n      \"last-5-avg\": 360.56531526809795,\n      \"last-10-avg\": 376.715253331762\n    },\n    \"timesteps_total\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_env_steps_sampled_lifetime\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_agent_steps_sampled_lifetime\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"num_steps_trained_this_iter\": {\n      \"max\": 4000,\n      \"min\": 4000,\n      \"avg\": 3999.9999999999995,\n      \"last\": 4000,\n      \"last-5-avg\": 4000.0,\n      \"last-10-avg\": 4000.0\n    },\n    \"agent_timesteps_total\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"done\": {\n      \"max\": true,\n      \"min\": false,\n      \"avg\": 0.02,\n      \"last\": true,\n      \"last-5-avg\": 0.2,\n      \"last-10-avg\": 0.1\n    },\n    \"training_iteration\": {\n      \"max\": 50,\n      \"min\": 1,\n      \"avg\": 25.499999999999996,\n      \"last\": 50,\n      \"last-5-avg\": 48.0,\n      \"last-10-avg\": 45.5\n    },\n    \"time_this_iter_s\": {\n      \"max\": 13.39140772819519,\n      \"min\": 8.022399425506592,\n      \"avg\": 10.327539396286012,\n      \"last\": 9.668057441711426,\n      \"last-5-avg\": 11.220180654525757,\n      \"last-10-avg\": 10.723295331001282\n    },\n    \"time_total_s\": {\n      \"max\": 516.3769698143005,\n      \"min\": 8.022399425506592,\n      \"avg\": 259.8335125064849,\n      \"last\": 516.3769698143005,\n      \"last-5-avg\": 494.54729027748107,\n      \"last-10-avg\": 467.42381472587584\n    },\n    \"time_since_restore\": {\n      \"max\": 516.3769698143005,\n      \"min\": 8.022399425506592,\n      \"avg\": 259.8335125064849,\n      \"last\": 516.3769698143005,\n      \"last-5-avg\": 494.54729027748107,\n      \"last-10-avg\": 467.42381472587584\n    },\n    \"iterations_since_restore\": {\n      \"max\": 50,\n      \"min\": 1,\n      \"avg\": 25.499999999999996,\n      \"last\": 50,\n      \"last-5-avg\": 48.0,\n      \"last-10-avg\": 45.5\n    },\n    \"info/num_env_steps_sampled\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"info/num_env_steps_trained\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"info/num_agent_steps_sampled\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"info/num_agent_steps_trained\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"env_runners/episode_reward_max\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_reward_min\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_reward_mean\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_len_mean\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episodes_timesteps_total\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"env_runners/num_faulty_episodes\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"env_runners/num_episodes\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"env_runners/episode_return_max\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_return_min\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_return_mean\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episodes_this_iter\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"timers/training_iteration_time_ms\": {\n      \"max\": 10884.072,\n      \"min\": 8014.791,\n      \"avg\": 10234.557039999994,\n      \"last\": 10719.097,\n      \"last-5-avg\": 10673.7062,\n      \"last-10-avg\": 10639.7606\n    },\n    \"timers/restore_workers_time_ms\": {\n      \"max\": 0.0,\n      \"min\": 0.0,\n      \"avg\": 0.0,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"timers/training_step_time_ms\": {\n      \"max\": 10884.072,\n      \"min\": 8014.791,\n      \"avg\": 10234.557039999994,\n      \"last\": 10719.097,\n      \"last-5-avg\": 10673.7062,\n      \"last-10-avg\": 10639.7606\n    },\n    \"timers/sample_time_ms\": {\n      \"max\": 3061.613,\n      \"min\": 2175.146,\n      \"avg\": 2781.0754199999983,\n      \"last\": 3041.438,\n      \"last-5-avg\": 2989.1569999999997,\n      \"last-10-avg\": 3002.1594000000005\n    },\n    \"timers/load_time_ms\": {\n      \"max\": 0.729,\n      \"min\": 0.0,\n      \"avg\": 0.3839399999999997,\n      \"last\": 0.101,\n      \"last-5-avg\": 0.0548,\n      \"last-10-avg\": 0.18960000000000002\n    },\n    \"timers/load_throughput\": {\n      \"max\": 39429414.806,\n      \"min\": 0.0,\n      \"avg\": 9284877.541999998,\n      \"last\": 39429414.806,\n      \"last-5-avg\": 12501983.1126,\n      \"last-10-avg\": 12561176.279199999\n    },\n    \"timers/learn_time_ms\": {\n      \"max\": 8236.78,\n      \"min\": 5815.003,\n      \"avg\": 7435.349859999997,\n      \"last\": 7660.081,\n      \"last-5-avg\": 7667.4446,\n      \"last-10-avg\": 7620.211400000002\n    },\n    \"timers/learn_throughput\": {\n      \"max\": 687.876,\n      \"min\": 485.627,\n      \"avg\": 539.2787399999997,\n      \"last\": 522.188,\n      \"last-5-avg\": 521.8224,\n      \"last-10-avg\": 525.1072\n    },\n    \"timers/synch_weights_time_ms\": {\n      \"max\": 24.642,\n      \"min\": 14.025,\n      \"avg\": 17.504199999999987,\n      \"last\": 16.63,\n      \"last-5-avg\": 16.627999999999997,\n      \"last-10-avg\": 16.9292\n    },\n    \"counters/num_env_steps_sampled\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"counters/num_env_steps_trained\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"counters/num_agent_steps_sampled\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"counters/num_agent_steps_trained\": {\n      \"max\": 200000,\n      \"min\": 4000,\n      \"avg\": 101999.99999999999,\n      \"last\": 200000,\n      \"last-5-avg\": 192000.0,\n      \"last-10-avg\": 182000.0\n    },\n    \"perf/cpu_util_percent\": {\n      \"max\": 41.25789473684211,\n      \"min\": 23.208333333333332,\n      \"avg\": 30.316827874267105,\n      \"last\": 29.807142857142853,\n      \"last-5-avg\": 31.589761904761907,\n      \"last-10-avg\": 30.66812454212454\n    },\n    \"perf/ram_util_percent\": {\n      \"max\": 90.02307692307691,\n      \"min\": 85.95,\n      \"avg\": 88.91386927509195,\n      \"last\": 89.44285714285716,\n      \"last-5-avg\": 89.23116129785248,\n      \"last-10-avg\": 89.1258430115636\n    },\n    \"info/learner/default_policy/num_agent_steps_trained\": {\n      \"max\": 128.0,\n      \"min\": 128.0,\n      \"avg\": 127.99999999999999,\n      \"last\": 128.0,\n      \"last-5-avg\": 128.0,\n      \"last-10-avg\": 128.0\n    },\n    \"info/learner/default_policy/num_grad_updates_lifetime\": {\n      \"max\": 46035.5,\n      \"min\": 465.5,\n      \"avg\": 23250.5,\n      \"last\": 46035.5,\n      \"last-5-avg\": 44175.5,\n      \"last-10-avg\": 41850.5\n    },\n    \"info/learner/default_policy/diff_num_grad_updates_vs_sampler_policy\": {\n      \"max\": 464.5,\n      \"min\": 464.5,\n      \"avg\": 464.49999999999994,\n      \"last\": 464.5,\n      \"last-5-avg\": 464.5,\n      \"last-10-avg\": 464.5\n    },\n    \"info/learner/default_policy/learner_stats/allreduce_latency\": {\n      \"max\": 0.0,\n      \"min\": 0.0,\n      \"avg\": 0.0,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"info/learner/default_policy/learner_stats/grad_gnorm\": {\n      \"max\": 13.8686088681221,\n      \"min\": 1.2323420040469657,\n      \"avg\": 8.67968293030191,\n      \"last\": 13.00508956764975,\n      \"last-5-avg\": 13.292779334407019,\n      \"last-10-avg\": 12.70425496351094\n    },\n    \"info/learner/default_policy/learner_stats/cur_kl_coeff\": {\n      \"max\": 0.20000000000000004,\n      \"min\": 0.20000000000000004,\n      \"avg\": 0.19999999999999993,\n      \"last\": 0.20000000000000004,\n      \"last-5-avg\": 0.20000000000000004,\n      \"last-10-avg\": 0.2\n    },\n    \"info/learner/default_policy/learner_stats/cur_lr\": {\n      \"max\": 0.00010000000000000003,\n      \"min\": 0.00010000000000000003,\n      \"avg\": 9.999999999999995e-05,\n      \"last\": 0.00010000000000000003,\n      \"last-5-avg\": 0.00010000000000000002,\n      \"last-10-avg\": 0.00010000000000000002\n    },\n    \"info/learner/default_policy/learner_stats/total_loss\": {\n      \"max\": 0.7760742186548649,\n      \"min\": 0.00929380807955499,\n      \"avg\": 0.4028365118824688,\n      \"last\": 0.7416076113630127,\n      \"last-5-avg\": 0.7466513273014777,\n      \"last-10-avg\": 0.6799743486588234\n    },\n    \"info/learner/default_policy/learner_stats/policy_loss\": {\n      \"max\": -0.006471030570326313,\n      \"min\": -0.018061318574973974,\n      \"avg\": -0.013189057294672391,\n      \"last\": -0.013151719495253537,\n      \"last-5-avg\": -0.01209778676550555,\n      \"last-10-avg\": -0.012622193924842342\n    },\n    \"info/learner/default_policy/learner_stats/vf_loss\": {\n      \"max\": 0.792103418232485,\n      \"min\": 0.016086466156728773,\n      \"avg\": 0.41411839741667367,\n      \"last\": 0.7530719340176156,\n      \"last-5-avg\": 0.7570961759891611,\n      \"last-10-avg\": 0.6908791458383567\n    },\n    \"info/learner/default_policy/learner_stats/vf_explained_var\": {\n      \"max\": -0.024081049298727383,\n      \"min\": -0.6114657448825016,\n      \"avg\": -0.3523317862108189,\n      \"last\": -0.20035952188635386,\n      \"last-5-avg\": -0.3289295176664988,\n      \"last-10-avg\": -0.3392071507182173\n    },\n    \"info/learner/default_policy/learner_stats/kl\": {\n      \"max\": 0.015485666958654937,\n      \"min\": 0.0069718697019772406,\n      \"avg\": 0.009535858397987516,\n      \"last\": 0.008436998933622465,\n      \"last-5-avg\": 0.008264684889317149,\n      \"last-10-avg\": 0.008586985516241064\n    },\n    \"info/learner/default_policy/learner_stats/entropy\": {\n      \"max\": 1.08550952775504,\n      \"min\": 0.5776352257497849,\n      \"avg\": 0.760791061007848,\n      \"last\": 0.6046871279516528,\n      \"last-5-avg\": 0.6219881298529204,\n      \"last-10-avg\": 0.6240590315800841\n    },\n    \"info/learner/default_policy/learner_stats/entropy_coeff\": {\n      \"max\": 0.0,\n      \"min\": 0.0,\n      \"avg\": 0.0,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    }\n  },\n  \"_n_steps\": [\n    5,\n    10\n  ],\n  \"metric_n_steps\": {\n    \"num_healthy_workers\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b084b084b084b084b08652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b084b084b084b084b084b084b084b084b084b08652e\"\n      }\n    },\n    \"num_in_flight_async_sample_reqs\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"num_remote_worker_restarts\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"num_agent_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_env_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_env_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_env_steps_sampled_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059531000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284da00f4da00f4da00f4da00f4da00f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059540000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f652e\"\n      }\n    },\n    \"num_env_steps_trained_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059531000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284da00f4da00f4da00f4da00f4da00f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059540000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f652e\"\n      }\n    },\n    \"num_env_steps_sampled_throughput_per_sec\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428474074ef22fa318f27474078b826cd1274e6474073a2c9ae5f9083474075817140f64c33474079e1b4f183000c652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847407606dd68d5a3434740787b543215459447407a5ece0f605bd7474079f86a4c963a7c474077ebcd27c84bbc474074ef22fa318f27474078b826cd1274e6474073a2c9ae5f9083474075817140f64c33474079e1b4f183000c652e\"\n      }\n    },\n    \"num_env_steps_trained_throughput_per_sec\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428474074ef22fa318f27474078b826cd1274e6474073a2c9ae5f9083474075817140f64c33474079e1b4f183000c652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847407606dd68d5a3434740787b543215459447407a5ece0f605bd7474079f86a4c963a7c474077ebcd27c84bbc474074ef22fa318f27474078b826cd1274e6474073a2c9ae5f9083474075817140f64c33474079e1b4f183000c652e\"\n      }\n    },\n    \"timesteps_total\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_env_steps_sampled_lifetime\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_agent_steps_sampled_lifetime\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"num_steps_trained_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059531000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284da00f4da00f4da00f4da00f4da00f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059540000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f652e\"\n      }\n    },\n    \"agent_timesteps_total\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"done\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059527000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288989898988652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942889898989898989898988652e\"\n      }\n    },\n    \"training_iteration\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b2e4b2f4b304b314b32652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b294b2a4b2b4b2c4b2d4b2e4b2f4b304b314b32652e\"\n      }\n    },\n    \"time_this_iter_s\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428474027e534d80000004740243cf3b00000004740297b87b80000004740273fedb8000000474023560ba0000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428474026b60fd00000004740246d1498000000474022f5f090000000474023413568000000474024e951b0000000474027e534d80000004740243cf3b00000004740297b87b80000004740273fedb8000000474023560ba0000000652e\"\n      }\n    },\n    \"time_total_s\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847407d83946b80000047407e257c0900000047407ef15846c0000047407fab57b4800000474080230408c00000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847407a47fe62c0000047407aeb670780000047407b83168c00000047407c1d203740000047407cc46ac4c0000047407d83946b80000047407e257c0900000047407ef15846c0000047407fab57b4800000474080230408c00000652e\"\n      }\n    },\n    \"time_since_restore\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847407d83946b80000047407e257c0900000047407ef15846c0000047407fab57b4800000474080230408c00000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847407a47fe62c0000047407aeb670780000047407b83168c00000047407c1d203740000047407cc46ac4c0000047407d83946b80000047407e257c0900000047407ef15846c0000047407fab57b4800000474080230408c00000652e\"\n      }\n    },\n    \"iterations_since_restore\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b2e4b2f4b304b314b32652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b294b2a4b2b4b2c4b2d4b2e4b2f4b304b314b32652e\"\n      }\n    },\n    \"info/num_env_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"info/num_env_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"info/num_agent_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"info/num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"env_runners/episode_reward_max\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_reward_min\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_reward_mean\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_len_mean\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episodes_timesteps_total\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"env_runners/num_faulty_episodes\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"env_runners/num_episodes\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"env_runners/episode_return_max\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_return_min\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_return_mean\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episodes_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"timers/training_iteration_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740c4a7310624dd2f4740c461083126e9794740c502751eb851ec4740c54209374bc6a84740c4ef8c6a7ef9db652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740c51aacac0831274740c51f2e76c8b4394740c4c1753f7ced914740c4520b851eb8524740c4452db22d0e564740c4a7310624dd2f4740c461083126e9794740c502751eb851ec4740c54209374bc6a84740c4ef8c6a7ef9db652e\"\n      }\n    },\n    \"timers/restore_workers_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      }\n    },\n    \"timers/training_step_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740c4a7310624dd2f4740c461083126e9794740c502751eb851ec4740c54209374bc6a84740c4ef8c6a7ef9db652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740c51aacac0831274740c51f2e76c8b4394740c4c1753f7ced914740c4520b851eb8524740c4452db22d0e564740c4a7310624dd2f4740c461083126e9794740c502751eb851ec4740c54209374bc6a84740c4ef8c6a7ef9db652e\"\n      }\n    },\n    \"timers/sample_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740a735753f7ced914740a6e3c51eb851ec4740a731deb851eb854740a7b5989374bc6a4740a7c2e04189374c652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740a7d8a76c8b43964740a7eb39db22d0e54740a7aac3958106254740a75f5374bc6a7f4740a6f9a5e353f7cf4740a735753f7ced914740a6e3c51eb851ec4740a731deb851eb854740a7b5989374bc6a4740a7c2e04189374c652e\"\n      }\n    },\n    \"timers/load_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428473fc624dd2f1a9fbe470000000000000000470000000000000000470000000000000000473fb9db22d0e56042652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428473fdb74bc6a7ef9db473fd50e5604189375473fd26e978d4fdf3b473fd26e978d4fdf3b473fd26e978d4fdf3b473fc624dd2f1a9fbe470000000000000000470000000000000000470000000000000000473fb9db22d0e56042652e\"\n      }\n    },\n    \"timers/load_throughput\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847417602e34c1cac08470000000000000000470000000000000000470000000000000000474182cd293672b021652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428474161c69b0147ae1447416730a97de353f847416a76ba22b851ec47416a76ba22b851ec47416a76ba22b851ec47417602e34c1cac08470000000000000000470000000000000000470000000000000000474182cd293672b021652e\"\n      }\n    },\n    \"timers/learn_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740bda2ba5e353f7d4740bd3fb0e56041894740be5b1c28f5c28f4740be979ced9168734740bdec14bc6a7efa652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740be3682d0e560424740be36c72b020c4a4740bd9ca04189374c4740bce3445a1cac084740bcfbb5810624dd4740bda2ba5e353f7d4740bd3fb0e56041894740be5b1c28f5c28f4740be979ced9168734740bdec14bc6a7efa652e\"\n      }\n    },\n    \"timers/learn_throughput\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847408079e560418937474080b1ae147ae14847408015d0e560418947407fec000000000047408051810624dd2f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428474080294dd2f1a9fc4740802928f5c28f5c4740807d49ba5e353f474080e716872b020c474080d8d70a3d70a447408079e560418937474080b1ae147ae14847408015d0e560418947407fec000000000047408051810624dd2f652e\"\n      }\n    },\n    \"timers/synch_weights_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740309d2f1a9fbe7747403059999999999a474030bb645a1cac08474030d0624dd2f1aa474030a147ae147ae1652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428474031fa1cac08312747403189ba5e353f7d47403083d70a3d70a4474030ba5e353f7cee47403164dd2f1a9fbe4740309d2f1a9fbe7747403059999999999a474030bb645a1cac08474030d0624dd2f1aa474030a147ae147ae1652e\"\n      }\n    },\n    \"counters/num_env_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"counters/num_env_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"counters/num_agent_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"counters/num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284aa08002004a409002004ae09f02004a80af02004a20bf02004ac0ce02004a60de02004a00ee02004aa0fd02004a400d0300652e\"\n      }\n    },\n    \"perf/cpu_util_percent\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080000000000803f4094869452946807680d43082322222222223c4094869452946807680d4308111111111151414094869452946807680d43080000000000f0404094869452946807680d4308e9a00eeaa0ce3d409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243083333333333d33e4094869452946807680d4308d2063a6da0533d4094869452946807680d4308197aa1177aa13e4094869452946807680d430839a8833aa8c33c4094869452946807680d4308f88aaff88a2f3d4094869452946807680d43080000000000803f4094869452946807680d43082322222222223c4094869452946807680d4308111111111151414094869452946807680d43080000000000f0404094869452946807680d4308e9a00eeaa0ce3d409486945294652e\"\n      }\n    },\n    \"perf/ram_util_percent\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308eaeaeaeaea4a564094869452946807680d4308666666666646564094869452946807680d4308a54ffaa44f4a564094869452946807680d4308000000000052564094869452946807680d4308c7577cc5575c56409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430800000000005a564094869452946807680d4308e27a14ae4761564094869452946807680d4308143bb1133b51564094869452946807680d43088aaff88aaf18564094869452946807680d4308165ff1155f21564094869452946807680d4308eaeaeaeaea4a564094869452946807680d4308666666666646564094869452946807680d4308a54ffaa44f4a564094869452946807680d4308000000000052564094869452946807680d4308c7577cc5575c56409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d430800000000000060409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d430800000000000060409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/num_grad_updates_lifetime\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080000000070a9e44094869452946807680d430800000000b01de54094869452946807680d430800000000f091e54094869452946807680d4308000000003006e64094869452946807680d430800000000707ae6409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000003064e24094869452946807680d43080000000070d8e24094869452946807680d430800000000b04ce34094869452946807680d430800000000f0c0e34094869452946807680d4308000000003035e44094869452946807680d43080000000070a9e44094869452946807680d430800000000b01de54094869452946807680d430800000000f091e54094869452946807680d4308000000003006e64094869452946807680d430800000000707ae6409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/diff_num_grad_updates_vs_sampler_policy\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/allreduce_latency\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/grad_gnorm\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308d8f8bcae01df2a4094869452946807680d43080fa4961adf172a4094869452946807680d430871c34d64ed442b4094869452946807680d430882a327961aaf2a4094869452946807680d430881368d199b022a409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430898fbc7852d79274094869452946807680d430862ee3fa54e70264094869452946807680d4308aa0cd9234b1f264094869452946807680d4308a11c6c97c362294094869452946807680d43083333334dbabc2b4094869452946807680d4308d8f8bcae01df2a4094869452946807680d43080fa4961adf172a4094869452946807680d430871c34d64ed442b4094869452946807680d430882a327961aaf2a4094869452946807680d430881368d199b022a409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/cur_kl_coeff\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/cur_lr\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/total_loss\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308864c55cf28abe83f94869452946807680d430812af22995a00e83f94869452946807680d4308fd257b7a77b5e73f94869452946807680d4308a93d90d99b5ae73f94869452946807680d4308279aa8e23fbbe73f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080fa4f658a0bfe13f94869452946807680d4308165820fa14bde13f94869452946807680d430832c618d9178de13f94869452946807680d4308387a1a424241e43f94869452946807680d430855868c9999d5e83f94869452946807680d4308864c55cf28abe83f94869452946807680d430812af22995a00e83f94869452946807680d4308fd257b7a77b5e73f94869452946807680d4308a93d90d99b5ae73f94869452946807680d4308279aa8e23fbbe73f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/policy_loss\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243084d65c854c0178dbf94869452946807680d4308d51e48edd72484bf94869452946807680d430887ea7628d7cf8bbf94869452946807680d43081073fff0e5e583bf94869452946807680d43088f3cf2e849ef8abf9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430812e0198109b185bf94869452946807680d4308f36635b746ea8cbf94869452946807680d43083b85aed3374388bf94869452946807680d4308ba7f987329c386bf94869452946807680d4308279a6892aa7e92bf94869452946807680d43084d65c854c0178dbf94869452946807680d4308d51e48edd72484bf94869452946807680d430887ea7628d7cf8bbf94869452946807680d43081073fff0e5e583bf94869452946807680d43088f3cf2e849ef8abf9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/vf_loss\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308b0c07a16f810e93f94869452946807680d4308026d6a2f8e42e83f94869452946807680d43087d8d37f93817e83f94869452946807680d430818c53e3dbf9ee73f94869452946807680d4308218404502a19e83f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080add01ccc808e23f94869452946807680d43081d0c56860a20e23f94869452946807680d4308fc563e0b78e0e13f94869452946807680d4308b3cb9264f48fe43f94869452946807680d430877778b44e958e93f94869452946807680d4308b0c07a16f810e93f94869452946807680d4308026d6a2f8e42e83f94869452946807680d43087d8d37f93817e83f94869452946807680d430818c53e3dbf9ee73f94869452946807680d4308218404502a19e83f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/vf_explained_var\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430842082144aca9d6bf94869452946807680d43084f70f4e4086edebf94869452946807680d4308df7beffd8c48d6bf94869452946807680d4308fdf4d30ff50ed1bf94869452946807680d4308d0d7f87c61a5c9bf9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308b0f1f91ac2ddc8bf94869452946807680d43089d42d7e988c3dbbf94869452946807680d4308697192d65456dcbf94869452946807680d4308481e7984e9bfd8bf94869452946807680d4308f97c8dcf228dd2bf94869452946807680d430842082144aca9d6bf94869452946807680d43084f70f4e4086edebf94869452946807680d4308df7beffd8c48d6bf94869452946807680d4308fdf4d30ff50ed1bf94869452946807680d4308d0d7f87c61a5c9bf9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/kl\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308a93db021aa33823f94869452946807680d4308252a16f1b9f7813f94869452946807680d4308a11c9fa952dd803f94869452946807680d43081cd82c287da27c3f94869452946807680d4308b469f8d36a47813f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308a2fcaf59a102813f94869452946807680d430851ddbead71e0843f94869452946807680d43083938a22ed517813f94869452946807680d430895448d8b55e27e3f94869452946807680d4308cf08c8bc15cf843f94869452946807680d4308a93db021aa33823f94869452946807680d4308252a16f1b9f7813f94869452946807680d4308a11c9fa952dd803f94869452946807680d43081cd82c287da27c3f94869452946807680d4308b469f8d36a47813f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/entropy\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308760a5d173777e33f94869452946807680d43086c4bc7863f0ce63f94869452946807680d430832952143a8dbe23f94869452946807680d43085860818deacbe33f94869452946807680d43081eaadbd19859e33f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308cdcccc0434f6e33f94869452946807680d4308111111010a6fe53f94869452946807680d4308279a68021d24e33f94869452946807680d430881360d485482e23f94869452946807680d430855864ce59822e53f94869452946807680d4308760a5d173777e33f94869452946807680d43086c4bc7863f0ce63f94869452946807680d430832952143a8dbe23f94869452946807680d43085860818deacbe33f94869452946807680d43081eaadbd19859e33f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/entropy_coeff\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      }\n    }\n  },\n  \"checkpoint_manager\": {\n    \"_type\": \"CLOUDPICKLE_FALLBACK\",\n    \"value\": \"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\"\n  }\n}"]], "runner_data": {"_earliest_stopping_actor": 1645271.906, "_actor_cleanup_timeout": 600, "_actor_force_cleanup_timeout": 10, "_reuse_actors": false, "_buffer_length": 1, "_buffer_min_time_s": 0.0, "_buffer_max_time_s": 100.0, "_max_pending_trials": 200, "_metric": null, "_total_time": 516.3769698143005, "_iteration": 5118, "_has_errored": false, "_fail_fast": false, "_print_trial_errors": true, "_cached_trial_decisions": {}, "_queued_trial_decisions": {}, "_should_stop_experiment": false, "_stopper": {"_type": "CLOUDPICKLE_FALLBACK", "value": "8005952c000000000000008c157261792e74756e652e73746f707065722e6e6f6f70948c0b4e6f6f7053746f707065729493942981942e"}, "_start_time": 1736143716.8248076, "_session_str": "2025-01-06_14-08-36", "_checkpoint_period": "auto", "_trial_checkpoint_config": {"_type": "CLOUDPICKLE_FALLBACK", "value": "800595f2000000000000008c097261792e747261696e948c10436865636b706f696e74436f6e6669679493942981947d94288c0b6e756d5f746f5f6b656570944e8c1a636865636b706f696e745f73636f72655f617474726962757465944e8c16636865636b706f696e745f73636f72655f6f72646572948c036d6178948c14636865636b706f696e745f6672657175656e6379944bc88c11636865636b706f696e745f61745f656e6494888c1a5f636865636b706f696e745f6b6565705f616c6c5f72616e6b73948c0a44455052454341544544948c1f5f636865636b706f696e745f75706c6f61645f66726f6d5f776f726b65727394680c75622e"}, "_resumed": false}, "stats": {"start_time": 1736143716.8248076}}