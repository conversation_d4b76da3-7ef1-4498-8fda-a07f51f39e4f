import pandas as pd
from datetime import datetime
import talib
from pyqlab.const import (
    MAIN_FUT_CODES,
    TOP_FUT_CODES,
    HOT_FUT_CODES,
    AAC_FUT_CODES,
    SF_FUT_CODES
)
from argparse import ArgumentParser
import json
import sys, os
sys.path.append("d:/QuantLab")
from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode
from pyqlab.data.dataset.pipeline import Pipeline
"""
数据的组织：
1.基础数据
  - 基本面数据
  - 财务数据
  - 行业数据
2.行情数据
  a.tick数据
  b.历史行情数据
    - min1
    - min5
    - min15
    - day

"""


# tsdb.py - 时序数据库操作的主模块
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime
import os
from typing import Dict, List, Optional, Union, Tuple
import logging

class TimeSeriesDB:
    """使用Parquet的证券时序数据库"""

    def __init__(self, db_path: str):
        """
        初始化时序数据库

        参数:
            db_path: 数据库根路径
        """
        self.db_path = db_path

        # 创建必要的目录结构
        self.market_path = os.path.join(db_path, "market")
        self.fundamental_path = os.path.join(db_path, "fundamental")
        self.metadata_path = os.path.join(db_path, "metadata")

        os.makedirs(self.market_path, exist_ok=True)
        os.makedirs(self.fundamental_path, exist_ok=True)
        os.makedirs(self.metadata_path, exist_ok=True)

        # 初始化子目录
        for data_type in ["tick", "bar"]:
            os.makedirs(os.path.join(self.market_path, data_type), exist_ok=True)

        if data_type == "bar":
            for freq in ["min1", "min5", "min15", "day"]:
                os.makedirs(os.path.join(self.market_path, data_type, freq), exist_ok=True)

        for data_type in ["basic", "financial", "industry"]:
            os.makedirs(os.path.join(self.fundamental_path, data_type), exist_ok=True)

        # 加载或创建证券元数据
        self.markets = self._load_markets()
        self.symbols = self._load_symbols()
        self.instruments = self._load_instruments()

    def _load_markets(self) -> pd.DataFrame:
        """加载市场元数据，如果不存在则创建"""
        markets_path = os.path.join(self.metadata_path, "markets.parquet")

        if os.path.exists(markets_path):
            return pd.read_parquet(markets_path)
        else:
            # 创建空的市场数据框
            markets = pd.DataFrame({
                'market_id': [],  # 市场代码
                'market_name': [],  # 市场名称
                'market_type': [],  # 市场类型
                'security_count': [],  # 证券数量
                'datetime': []  # 跟新时间
            })
            markets.to_parquet(markets_path, index=False)
            return markets

    def _load_symbols(self) -> pd.DataFrame:
        """加载证券元数据，如果不存在则创建"""
        symbols_path = os.path.join(self.metadata_path, "symbols.parquet")

        if os.path.exists(symbols_path):
            return pd.read_parquet(symbols_path)
        else:
            # 创建空的证券数据框
            symbols = pd.DataFrame({
                'symbol': [],  # 证券代码
                'name': [],    # 证券名称
                'market': [],  # 市场代码
                'industry': [], # 行业
                'sector': [],  # 板块
                'listing_date': [],  # 上市日期
                'is_active': []  # 是否活跃
            })
            symbols.to_parquet(symbols_path, index=False)
            return symbols
        
    def _load_instruments(self) -> pd.DataFrame:
        """加载特定市场的合约信息，如果不存在则创建"""
        instruments_path = os.path.join(self.metadata_path, "instruments.parquet")
        if os.path.exists(instruments_path):
            return pd.read_parquet(instruments_path)
        else:
            # 创建空的合约数据框
            instruments = pd.DataFrame({
                'category': [],  # 合约类型
                'market': [],  # 市场代码
                'code': [],  # 合约代码
                'name': [],    # 合约名称
                'desc': [],  # 合约描述
                'datetime': []  # 跟新时间
            })
            instruments.to_parquet(instruments_path, index=False)
            return instruments


    def _get_market_data_path(self,
                             symbol: str,
                             market: str,
                             data_type: str,
                             freq: str,
                             date: Union[str, datetime]) -> str:
        """获取行情数据文件的路径"""
        if isinstance(date, str):
            date = pd.Timestamp(date)

        year = date.year
        month = date.month
        day = date.day

        if data_type == 'tick':
            return os.path.join(
                self.market_path,
                data_type,
                str(year),
                f"{month:02d}",
                f"{day:02d}",
                f"{market}_{symbol}.parquet"
            )
        elif data_type == 'bar':
            if freq == 'min1':
                return os.path.join(
                    self.market_path,
                    data_type,
                    freq,
                    str(year),
                    f"{month:02d}",
                    f"{market}_{symbol}.parquet"
                )
            else:  # min5, min15, day
                return os.path.join(
                    self.market_path,
                    data_type,
                    freq,
                    str(year),
                    f"{market}_{symbol}.parquet"
                )
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

    def _get_fundamental_data_path(self,
                                  symbol: str,
                                  market: str,
                                  data_type: str,
                                  date: Union[str, datetime] = None) -> str:
        """获取基础数据文件的路径"""
        if data_type == 'basic':
            return os.path.join(
                self.fundamental_path,
                data_type,
                market,
                f"{symbol}.parquet"
            )
        elif data_type == 'financial':
            if date is None:
                raise ValueError("财务数据必须提供日期")

            if isinstance(date, str):
                date = pd.Timestamp(date)

            year = date.year
            # 确定季度
            quarter = (date.month - 1) // 3 + 1

            return os.path.join(
                self.fundamental_path,
                data_type,
                str(year),
                f"Q{quarter}",
                f"{market}_{symbol}.parquet"
            )
        elif data_type == 'industry':
            return os.path.join(
                self.fundamental_path,
                data_type,
                f"{symbol}.parquet"
            )
        elif data_type == 'block':
            return os.path.join(
                self.fundamental_path,
                data_type,
                f"{symbol}.parquet"
            )
        elif data_type == 'company_info':
            return os.path.join(
                self.fundamental_path,
                data_type,
                f"{symbol}.parquet"
            )
        elif data_type == 'xdxr':
            return os.path.join(
                self.fundamental_path,
                data_type,
                f"{symbol}.parquet"
            )
        elif data_type == 'company_category':
            return os.path.join(
                self.fundamental_path,
                data_type,
                f"{symbol}.parquet"
            )
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

    def write_market_data(self,
                         symbol: str,
                         data: pd.DataFrame,
                         market: str = 'CN',
                         data_type: str = 'bar',
                         freq: str = 'day',
                         date: Union[str, datetime] = None,
                         append: bool = True) -> bool:
        """
        写入行情数据

        参数:
            symbol: 证券代码
            data: 包含行情数据的DataFrame
            market: 市场代码(CN, US等)
            data_type: 数据类型(bar, tick)
            freq: 数据频率(day, min1, min5, min15) - 仅对bar类型有效
            date: 用于存储路径的参考日期
            append: 是否追加到现有数据

        返回:
            bool: 成功或失败
        """
        if date is None:
            if 'datetime' in data.columns:
                date = data['datetime'].max()
            else:
                date = datetime.now()

        # 确保数据有datetime列
        if 'datetime' not in data.columns:
            raise ValueError("数据必须包含'datetime'列")

        # 按datetime排序数据
        data = data.sort_values('datetime')

        # 获取本地路径
        local_path = self._get_market_data_path(symbol, market, data_type, freq, date)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        # 写入本地存储
        if append and os.path.exists(local_path):
            # 读取现有数据
            existing_data = pd.read_parquet(local_path)

            # 追加新数据
            combined_data = pd.concat([existing_data, data])

            # 基于datetime删除重复项
            combined_data = combined_data.drop_duplicates(subset=['datetime'], keep='last')

            # 按datetime排序
            combined_data = combined_data.sort_values('datetime')

            # 写入合并数据
            combined_data.to_parquet(local_path, index=False)
        else:
            # 写入新数据
            data.to_parquet(local_path, index=False)

        return True

    def write_fundamental_data(self,
                              symbol: str,
                              data: pd.DataFrame,
                              market: str = 'CN',
                              data_type: str = 'basic',
                              date: Union[str, datetime] = None) -> bool:
        """
        写入基础数据

        参数:
            symbol: 证券代码
            data: 包含基础数据的DataFrame
            market: 市场代码(CN, US等)
            data_type: 数据类型(basic, financial, industry)
            date: 用于财务数据的报告日期

        返回:
            bool: 成功或失败
        """
        # 获取本地路径
        local_path = self._get_fundamental_data_path(symbol, market, data_type, date)
        print(local_path)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        # 写入本地存储
        data.to_parquet(local_path, index=False)

        return True

    def read_market_data(self,
                        symbol: str,
                        start_date: Union[str, datetime],
                        end_date: Union[str, datetime],
                        market: str = 'CN',
                        data_type: str = 'bar',
                        freq: str = 'day') -> pd.DataFrame:
        """
        读取行情数据

        参数:
            symbol: 证券代码
            start_date: 数据检索的开始日期
            end_date: 数据检索的结束日期
            market: 市场代码(CN, US等)
            data_type: 数据类型(bar, tick)
            freq: 数据频率(day, min1, min5, min15) - 仅对bar类型有效

        返回:
            pd.DataFrame: 行情数据
        """
        if isinstance(start_date, str):
            start_date = pd.Timestamp(start_date)
        if isinstance(end_date, str):
            end_date = pd.Timestamp(end_date)

        dfs = []

        if data_type == 'tick':
            # 对于tick数据，我们需要每天的文件
            dates = pd.date_range(start=start_date.date(), end=end_date.date(), freq='D')

            for date in dates:
                local_path = self._get_market_data_path(symbol, market, data_type, freq, date)

                if os.path.exists(local_path):
                    df = pd.read_parquet(local_path)
                    dfs.append(df)

        elif data_type == 'bar':
            if freq == 'min1':
                # 对于1分钟数据，我们需要每月的文件
                current_date = pd.Timestamp(year=start_date.year, month=start_date.month, day=1)
                end_month = pd.Timestamp(year=end_date.year, month=end_date.month, day=1)

                while current_date <= end_month:
                    local_path = self._get_market_data_path(symbol, market, data_type, freq, current_date)

                    if os.path.exists(local_path):
                        df = pd.read_parquet(local_path)
                        dfs.append(df)

                    # 移至下一个月
                    year = current_date.year + (current_date.month // 12)
                    month = (current_date.month % 12) + 1
                    current_date = pd.Timestamp(year=year, month=month, day=1)
            else:
                # 对于其他bar数据，我们需要每年的文件
                years = range(start_date.year, end_date.year + 1)

                for year in years:
                    date = pd.Timestamp(year=year, month=1, day=1)
                    local_path = self._get_market_data_path(symbol, market, data_type, freq, date)

                    if os.path.exists(local_path):
                        df = pd.read_parquet(local_path)
                        dfs.append(df)

        if not dfs:
            return pd.DataFrame()

        # 合并所有数据框
        result = pd.concat(dfs)

        # 按日期范围过滤
        result = result[(result['datetime'] >= start_date) & (result['datetime'] <= end_date)]

        # 按datetime排序
        result = result.sort_values('datetime')

        return result

    def read_fundamental_data(self,
                             symbol: str,
                             market: str = 'CN',
                             data_type: str = 'basic',
                             date: Union[str, datetime] = None) -> pd.DataFrame:
        """
        读取基础数据

        参数:
            symbol: 证券代码
            market: 市场代码(CN, US等)
            data_type: 数据类型(basic, financial, industry)
            date: 用于财务数据的报告日期

        返回:
            pd.DataFrame: 基础数据
        """
        local_path = self._get_fundamental_data_path(symbol, market, data_type, date)

        if os.path.exists(local_path):
            return pd.read_parquet(local_path)
        else:
            return pd.DataFrame()

    def list_symbols(self,
                    market: str = None,
                    industry: str = None,
                    active_only: bool = True) -> pd.DataFrame:
        """
        列出可用的证券代码

        参数:
            market: 按市场过滤
            industry: 按行业过滤
            active_only: 仅返回活跃证券

        返回:
            pd.DataFrame: 过滤后的证券
        """
        symbols = self.symbols.copy()

        if market:
            symbols = symbols[symbols['market'] == market]

        if industry:
            symbols = symbols[symbols['industry'] == industry]

        if active_only:
            symbols = symbols[symbols['is_active']]

        return symbols

    def add_symbol(self,
                  symbol: str,
                  name: str,
                  market: str,
                  industry: str = None,
                  sector: str = None,
                  listing_date: Union[str, datetime] = None,
                  is_active: bool = True,
                  volunit: int = 100,
                  decimal_point: int = 2,
                  pre_close: float = 0.0,
                  **kwargs) -> bool:
        """
        向数据库添加新证券

        参数:
            symbol: 证券代码
            name: 证券名称
            market: 市场代码
            industry: 行业
            sector: 板块
            listing_date: 上市日期
            is_active: 证券是否活跃
            volunit: 交易单位，默认为100
            decimal_point: 小数点位数，默认为2
            pre_close: 前收盘价，默认为0.0
            **kwargs: 其他属性

        返回:
            bool: 成功或失败
        """
        # 将日期转换为时间戳
        if listing_date and isinstance(listing_date, str):
            listing_date = pd.Timestamp(listing_date)

        # 检查证券是否已存在
        if (self.symbols['symbol'] == symbol).any():
            # 更新现有证券
            idx = (self.symbols['symbol'] == symbol).idxmax()

            self.symbols.loc[idx, 'name'] = name
            self.symbols.loc[idx, 'market'] = market

            if industry:
                self.symbols.loc[idx, 'industry'] = industry
            if sector:
                self.symbols.loc[idx, 'sector'] = sector
            if listing_date:
                self.symbols.loc[idx, 'listing_date'] = listing_date

            self.symbols.loc[idx, 'is_active'] = is_active

            # 更新交易相关字段
            self.symbols.loc[idx, 'volunit'] = volunit
            self.symbols.loc[idx, 'decimal_point'] = decimal_point
            self.symbols.loc[idx, 'pre_close'] = pre_close

            # 更新其他属性
            for key, value in kwargs.items():
                self.symbols.loc[idx, key] = value
        else:
            # 准备新证券的数据
            symbol_data = {
                'symbol': [symbol],
                'name': [name],
                'market': [market],
                'industry': [industry if industry else ''],
                'sector': [sector if sector else ''],
                'listing_date': [listing_date if listing_date else pd.NaT],
                'is_active': [is_active],
                'volunit': [volunit],
                'decimal_point': [decimal_point],
                'pre_close': [pre_close]
            }

            # 添加其他属性
            for key, value in kwargs.items():
                symbol_data[key] = [value]

            # 添加新证券
            new_symbol = pd.DataFrame(symbol_data)

            # 确保所有必要的列都存在于self.symbols中
            for col in new_symbol.columns:
                if col not in self.symbols.columns:
                    self.symbols[col] = None

            self.symbols = pd.concat([self.symbols, new_symbol], ignore_index=True)

        # 保存更新后的证券元数据
        symbols_path = os.path.join(self.metadata_path, "symbols.parquet")
        self.symbols.to_parquet(symbols_path, index=False)

        return True

    def get_data(self, symbol: str, start_date: str, end_date: str, freq: str = 'day'):
        """
        获取证券数据的便捷方法

        参数:
            symbol: 证券代码
            start_date: 开始日期
            end_date: 结束日期
            freq: 数据频率(day, min1, min5, min15)

        返回:
            pd.DataFrame: 证券数据
        """
        # 确定市场
        market = 'CN'  # 默认市场
        if '.' in symbol:
            parts = symbol.split('.')
            symbol = parts[0]
            market = parts[1]

        return self.read_market_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            market=market,
            data_type='bar',
            freq=freq
        )

    def save_data(self, symbol: str, data: pd.DataFrame, freq: str = 'day'):
        """
        保存证券数据的便捷方法

        参数:
            symbol: 证券代码
            data: 要保存的数据
            freq: 数据频率(day, min1, min5, min15)

        返回:
            bool: 成功或失败
        """
        # 确定市场
        market = 'CN'  # 默认市场
        if '.' in symbol:
            parts = symbol.split('.')
            symbol = parts[0]
            market = parts[1]

        return self.write_market_data(
            symbol=symbol,
            data=data,
            market=market,
            data_type='bar',
            freq=freq
        )

    def batch_write_market_data(self,
                               data: pd.DataFrame,
                               market: str = 'CN',
                               data_type: str = 'bar',
                               freq: str = 'day',
                               symbol_col: str = 'symbol') -> Dict[str, bool]:
        """
        批量写入多个证券的行情数据

        参数:
            data: 包含多个证券数据的DataFrame
            market: 市场代码
            data_type: 数据类型(bar, tick)
            freq: 数据频率
            symbol_col: 证券代码列名

        返回:
            Dict[str, bool]: 每个证券的写入结果
        """
        if symbol_col not in data.columns:
            raise ValueError(f"数据必须包含'{symbol_col}'列")

        results = {}

        # 按证券代码分组
        grouped = data.groupby(symbol_col)

        for symbol, group in grouped:
            # 复制数据以避免修改原始数据
            symbol_data = group.copy()

            # 写入数据
            success = self.write_market_data(
                symbol=symbol,
                data=symbol_data,
                market=market,
                data_type=data_type,
                freq=freq
            )

            results[symbol] = success

        return results

    def export_to_csv(self,
                     symbol: str,
                     start_date: Union[str, datetime],
                     end_date: Union[str, datetime],
                     market: str = 'CN',
                     data_type: str = 'bar',
                     freq: str = 'day',
                     output_path: str = None) -> str:
        """
        将数据导出为CSV文件

        参数:
            symbol: 证券代码
            start_date: 开始日期
            end_date: 结束日期
            market: 市场代码
            data_type: 数据类型
            freq: 数据频率
            output_path: 输出路径，如果为None则自动生成

        返回:
            str: CSV文件路径
        """
        # 读取数据
        data = self.read_market_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            market=market,
            data_type=data_type,
            freq=freq
        )

        if data.empty:
            return None

        # 生成输出路径
        if output_path is None:
            if isinstance(start_date, str):
                start_str = start_date.replace('-', '')
            else:
                start_str = start_date.strftime('%Y%m%d')

            if isinstance(end_date, str):
                end_str = end_date.replace('-', '')
            else:
                end_str = end_date.strftime('%Y%m%d')

            output_path = f"{symbol}_{market}_{freq}_{start_str}_{end_str}.csv"

        # 导出为CSV
        data.to_csv(output_path, index=False)

        return output_path

    def import_from_csv(self,
                       file_path: str,
                       symbol: str = None,
                       market: str = 'CN',
                       data_type: str = 'bar',
                       freq: str = 'day',
                       date_col: str = 'datetime',
                       date_format: str = None) -> bool:
        """
        从CSV文件导入数据

        参数:
            file_path: CSV文件路径
            symbol: 证券代码，如果为None则从文件名推断
            market: 市场代码
            data_type: 数据类型
            freq: 数据频率
            date_col: 日期列名
            date_format: 日期格式，如果为None则自动推断

        返回:
            bool: 成功或失败
        """
        # 如果未提供证券代码，尝试从文件名推断
        if symbol is None:
            file_name = os.path.basename(file_path)
            symbol = file_name.split('_')[0]

        # 读取CSV文件
        data = pd.read_csv(file_path)

        # 确保日期列存在
        if date_col not in data.columns:
            raise ValueError(f"CSV文件必须包含'{date_col}'列")

        # 转换日期列
        if date_format:
            data[date_col] = pd.to_datetime(data[date_col], format=date_format)
        else:
            data[date_col] = pd.to_datetime(data[date_col])

        # 重命名日期列为datetime（如果需要）
        if date_col != 'datetime':
            data = data.rename(columns={date_col: 'datetime'})

        # 写入数据
        return self.write_market_data(
            symbol=symbol,
            data=data,
            market=market,
            data_type=data_type,
            freq=freq
        )

    def optimize_storage(self,
                        symbol: str = None,
                        market: str = None,
                        data_type: str = None,
                        freq: str = None) -> Dict[str, int]:
        """
        优化存储，重新压缩Parquet文件以减少存储空间

        参数:
            symbol: 要优化的证券代码，如果为None则优化所有
            market: 要优化的市场，如果为None则优化所有
            data_type: 要优化的数据类型，如果为None则优化所有
            freq: 要优化的数据频率，如果为None则优化所有

        返回:
            Dict[str, int]: 每个优化文件的节省字节数
        """
        savings = {}

        # 确定要优化的路径
        if data_type == 'bar' and freq:
            base_path = os.path.join(self.market_path, data_type, freq)
        elif data_type:
            base_path = os.path.join(self.market_path, data_type)
        else:
            base_path = self.market_path

        # 遍历所有Parquet文件
        for root, dirs, files in os.walk(base_path):
            for file in files:
                if file.endswith('.parquet'):
                    # 检查是否匹配筛选条件
                    file_path = os.path.join(root, file)
                    file_name = os.path.basename(file)

                    # 如果指定了证券代码和市场，检查文件名是否匹配
                    if symbol and market:
                        if not file_name.startswith(f"{market}_{symbol}"):
                            continue
                    elif symbol:
                        if symbol not in file_name:
                            continue
                    elif market:
                        if not file_name.startswith(f"{market}_"):
                            continue

                    # 获取文件大小
                    original_size = os.path.getsize(file_path)

                    # 读取数据
                    data = pd.read_parquet(file_path)

                    # 创建临时文件
                    temp_path = file_path + '.temp'

                    # 使用优化的压缩选项重新写入
                    data.to_parquet(
                        temp_path,
                        index=False,
                        compression='snappy',  # 使用snappy压缩算法
                        row_group_size=100000  # 优化行组大小
                    )

                    # 获取新文件大小
                    new_size = os.path.getsize(temp_path)

                    # 如果新文件更小，替换原文件
                    if new_size < original_size:
                        os.replace(temp_path, file_path)
                        savings[file_path] = original_size - new_size
                    else:
                        # 否则删除临时文件
                        os.remove(temp_path)

        return savings

    def get_storage_stats(self) -> Dict[str, Dict]:
        """
        获取存储统计信息

        返回:
            Dict: 存储统计信息
        """
        stats = {
            'total_size': 0,
            'file_count': 0,
            'market_data': {
                'size': 0,
                'file_count': 0,
                'bar': {
                    'size': 0,
                    'file_count': 0,
                    'day': {'size': 0, 'file_count': 0},
                    'min1': {'size': 0, 'file_count': 0},
                    'min5': {'size': 0, 'file_count': 0},
                    'min15': {'size': 0, 'file_count': 0}
                },
                'tick': {
                    'size': 0,
                    'file_count': 0
                }
            },
            'fundamental_data': {
                'size': 0,
                'file_count': 0,
                'basic': {'size': 0, 'file_count': 0},
                'financial': {'size': 0, 'file_count': 0},
                'industry': {'size': 0, 'file_count': 0}
            }
        }

        # 统计市场数据
        for data_type in ['bar', 'tick']:
            data_type_path = os.path.join(self.market_path, data_type)

            if os.path.exists(data_type_path):
                if data_type == 'bar':
                    for freq in ['day', 'min1', 'min5', 'min15']:
                        freq_path = os.path.join(data_type_path, freq)

                        if os.path.exists(freq_path):
                            size, count = self._get_dir_stats(freq_path)
                            stats['market_data'][data_type][freq]['size'] = size
                            stats['market_data'][data_type][freq]['file_count'] = count
                            stats['market_data'][data_type]['size'] += size
                            stats['market_data'][data_type]['file_count'] += count
                else:
                    size, count = self._get_dir_stats(data_type_path)
                    stats['market_data'][data_type]['size'] = size
                    stats['market_data'][data_type]['file_count'] = count

                stats['market_data']['size'] += stats['market_data'][data_type]['size']
                stats['market_data']['file_count'] += stats['market_data'][data_type]['file_count']

        # 统计基础数据
        for data_type in ['basic', 'financial', 'industry']:
            data_type_path = os.path.join(self.fundamental_path, data_type)

            if os.path.exists(data_type_path):
                size, count = self._get_dir_stats(data_type_path)
                stats['fundamental_data'][data_type]['size'] = size
                stats['fundamental_data'][data_type]['file_count'] = count
                stats['fundamental_data']['size'] += size
                stats['fundamental_data']['file_count'] += count

        # 计算总计
        stats['total_size'] = stats['market_data']['size'] + stats['fundamental_data']['size']
        stats['file_count'] = stats['market_data']['file_count'] + stats['fundamental_data']['file_count']

        return stats

    def _get_dir_stats(self, dir_path: str) -> Tuple[int, int]:
        """获取目录的大小和文件数量"""
        total_size = 0
        file_count = 0

        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if file.endswith('.parquet'):
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    file_count += 1

        return total_size, file_count

    def close(self):
        """关闭数据库连接（为了与现有代码兼容）"""
        # 在这个实现中不需要关闭任何连接
        pass

if __name__ == "__main__":
    # 示例1: 初始化数据库
    tsdb = TimeSeriesDB("f:/hqdata/tsdb")

    # 示例2: 添加证券
    tsdb.add_symbol(
        symbol="000001",
        name="平安银行",
        market="SZ",
        industry="银行",
        sector="金融"
    )

    # 示例3: 写入日线数据
    import pandas as pd
    from datetime import datetime, timedelta

    # 创建示例数据
    dates = pd.date_range(start='2023-01-01', end='2023-01-10')
    data = pd.DataFrame({
        'datetime': dates,
        'open': [10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0],
        'high': [10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1],
        'low': [10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9],
        'close': [10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1],
        'volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900]
    })

    # 写入数据
    tsdb.write_market_data(
        symbol="000001",
        data=data,
        market="SZ",
        data_type="bar",
        freq="day"
    )

    # 示例4: 读取数据
    result = tsdb.read_market_data(
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-01-10",
        market="SZ",
        data_type="bar",
        freq="day"
    )
    print(result)

    # 示例5: 使用便捷方法
    data = tsdb.get_data("000001.SZ", "2023-01-01", "2023-01-10", freq="day")
    print(data)

    # 示例6: 批量写入多个证券的数据
    multi_data = pd.DataFrame({
        'symbol': ['000001', '000002', '000001', '000002'],
        'datetime': [
            pd.Timestamp('2023-01-01'),
            pd.Timestamp('2023-01-01'),
            pd.Timestamp('2023-01-02'),
            pd.Timestamp('2023-01-02')
        ],
        'open': [10.1, 20.1, 10.2, 20.2],
        'high': [10.2, 20.2, 10.3, 20.3],
        'low': [10.0, 20.0, 10.1, 20.1],
        'close': [10.2, 20.2, 10.3, 20.3],
        'volume': [1000, 2000, 1100, 2100]
    })

    results = tsdb.batch_write_market_data(
        data=multi_data,
        market="SZ",
        data_type="bar",
        freq="day"
    )
    print(results)

    # 示例7: 获取存储统计信息
    stats = tsdb.get_storage_stats()
    print(f"总存储大小: {stats['total_size'] / (1024*1024):.2f} MB")
    print(f"总文件数: {stats['file_count']}")
