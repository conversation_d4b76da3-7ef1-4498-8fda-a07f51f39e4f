# -*- coding: utf-8 -*-

from inspect import signature
from typing import Callable, Union, Iterable
from functools import wraps, lru_cache as origin_lru_cache

cached_functions = []


def lru_cache(*args, **kwargs):
    def decorator(func):
        func = origin_lru_cache(*args, **kwargs)(func)
        cached_functions.append(func)
        return func

    return decorator


def clear_all_cached_functions():
    for func in cached_functions:
        func.cache_clear()


def instype_singledispatch(func):
    from pyqlab.model.instrument import Instrument
    from pyqlab.const import INSTRUMENT_TYPE
    from pyqlab.utils.exception import RQInvalidArgument, RQApiNotSupportedError
    from pyqlab.utils.i18n import gettext as _

    registry = {}
    data_proxy = None

    def rq_invalid_argument(arg):
        if registry:
            return RQInvalidArgument(_(
                u"function {}: invalid {} argument, "
                u"expected an order_book_id or instrument with types {}, got {} (type: {})"
            ).format(funcname, argname, [getattr(i, "name", str(i)) for i in registry], arg, type(arg)))
        else:
            return RQApiNotSupportedError(_(
                "function {} is not supported, please check your account or mod config"
            ).format(funcname))

    @lru_cache(1024)
    def dispatch(id_or_ins):
        nonlocal data_proxy
        if isinstance(id_or_ins, Instrument):
            instype = id_or_ins.type
        else:
            if not data_proxy:
                from pyqlab.environment import Environment
                data_proxy = Environment.get_instance().data_proxy
            ins = data_proxy.instrument(id_or_ins)
            if not ins:
                raise rq_invalid_argument(id_or_ins)
            instype = ins.type
        try:
            return registry[instype]
        except KeyError:
            raise rq_invalid_argument(id_or_ins)

    def register(instypes):
        # type: (Union[INSTRUMENT_TYPE, Iterable[INSTRUMENT_TYPE]]) -> Callable
        if isinstance(instypes, str):
            instypes = [instypes]

        def register_wrapper(f):
            for instype in instypes:
                registry[instype] = f
            return f

        return register_wrapper

    @wraps(func)
    def wrapper(*args, **kwargs):
        if not args:
            try:
                arg = kwargs[argname]
            except KeyError:
                raise TypeError('{}() missing 1 required positional argument: \'{}\''.format(
                    funcname, argname
                ))
        else:
            arg = args[0]
        try:
            impl = dispatch(arg)
        except TypeError:
            raise rq_invalid_argument(arg)
        return impl(*args, **kwargs)

    funcname = getattr(func, '__name__', 'instype_singledispatch function')
    argname = next(iter(signature(func).parameters))
    wrapper.register = register

    return wrapper
