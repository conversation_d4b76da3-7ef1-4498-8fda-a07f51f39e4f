#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试BarTokenizer是否可以正常工作
"""

import numpy as np
import pandas as pd
import sys
import os

def test_bar_tokenizer():
    """测试BarTokenizer基本功能"""
    print("🔍 测试BarTokenizer基本功能...")
    
    try:
        # 导入BarTokenizer
        from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
        print("✅ 成功导入BarTokenizer")
        
        # 生成测试数据
        np.random.seed(42)
        n = 1000
        
        base_price = 100
        returns = np.random.normal(0, 0.02, n)
        prices = base_price * np.exp(np.cumsum(returns))
        
        df = pd.DataFrame({
            'open': prices,
            'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),
            'close': prices * (1 + np.random.normal(0, 0.005, n)),
            'volume': np.random.lognormal(10, 1, n)
        })
        
        # 确保OHLC数据的合理性
        df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
        df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
        
        print(f"✅ 生成测试数据: {df.shape}")
        
        # 创建tokenizer
        tokenizer = BarTokenizer(
            mapping_strategy='quantile',
            balancing_strategy='frequency',
            n_bins=50,
            features=['change', 'body', 'upper_shadow', 'lower_shadow'],
            atr_period=14
        )
        print("✅ 创建BarTokenizer实例")
        
        # 拟合和转换
        tokens = tokenizer.fit_transform(df)
        print(f"✅ 成功生成tokens: {len(tokens)} 个")
        
        # 基本检查
        print(f"📊 词汇表大小: {tokenizer.get_vocab_size()}")
        print(f"📊 Token范围: {np.min(tokens)} - {np.max(tokens)}")
        print(f"📊 唯一tokens: {len(np.unique(tokens))}")
        
        # 分析分布
        balance_metrics = tokenizer.analyze_balance(tokens)
        print(f"📊 基尼系数: {balance_metrics['gini_coefficient']:.4f}")
        print(f"📊 标准化熵: {balance_metrics['normalized_entropy']:.4f}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_methods():
    """测试多种方法"""
    print("\n🔍 测试多种BarTokenizer方法...")
    
    try:
        from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
        
        # 生成测试数据
        np.random.seed(42)
        n = 500
        
        base_price = 100
        returns = np.random.normal(0, 0.02, n)
        prices = base_price * np.exp(np.cumsum(returns))
        
        df = pd.DataFrame({
            'open': prices,
            'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),
            'close': prices * (1 + np.random.normal(0, 0.005, n)),
            'volume': np.random.lognormal(10, 1, n)
        })
        
        df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
        df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
        
        methods = ['linear', 'quantile', 'adaptive']
        results = {}
        
        for method in methods:
            print(f"\n  测试方法: {method}")
            
            tokenizer = BarTokenizer(
                mapping_strategy=method,
                balancing_strategy='none',
                n_bins=30,
                features=['change', 'body'],
                atr_period=14
            )
            
            tokens = tokenizer.fit_transform(df)
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            results[method] = {
                'gini': balance_metrics['gini_coefficient'],
                'entropy': balance_metrics['normalized_entropy'],
                'vocab_size': tokenizer.get_vocab_size(),
                'unique_tokens': len(np.unique(tokens))
            }
            
            print(f"    基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"    标准化熵: {balance_metrics['normalized_entropy']:.4f}")
            print(f"    词汇表利用率: {len(np.unique(tokens)) / tokenizer.get_vocab_size():.2%}")
        
        # 对比结果
        print(f"\n📊 方法对比:")
        print(f"{'方法':<10} {'基尼系数':<10} {'标准化熵':<10} {'利用率':<10}")
        print("-" * 45)
        
        for method, result in results.items():
            utilization = result['unique_tokens'] / result['vocab_size']
            print(f"{method:<10} {result['gini']:<10.4f} {result['entropy']:<10.4f} {utilization:<10.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多方法测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 BarTokenizer简单测试")
    print("=" * 50)
    
    success_count = 0
    
    # 测试1: 基本功能
    if test_bar_tokenizer():
        success_count += 1
    
    # 测试2: 多种方法
    if test_multiple_methods():
        success_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"测试完成！成功 {success_count}/2 个测试")
    
    if success_count == 2:
        print("✅ 所有测试通过，BarTokenizer工作正常！")
        print("\n现在可以运行完整的可视化分析：")
        print("  python pyqlab/notebook/analyze_bar_tokenizer.py")
        print("  python pyqlab/notebook/demo_bar_tokenizer.py")
    else:
        print("❌ 部分测试失败，请检查BarTokenizer模块")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
