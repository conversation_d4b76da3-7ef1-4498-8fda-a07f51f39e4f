"""
测试修改后的CandlestickTokenizer
"""

import pandas as pd
import numpy as np
from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer

# 创建一个简单的K线数据
def create_sample_data(n=10):
    dates = pd.date_range(start='2023-01-01', periods=n)
    data = {
        'datetime': dates,
        'open': np.random.normal(100, 5, n),
        'high': np.random.normal(105, 5, n),
        'low': np.random.normal(95, 5, n),
        'close': np.random.normal(100, 5, n),
        'volume': np.random.normal(1000000, 200000, n)
    }
    
    # 确保high >= open, close和low <= open, close
    for i in range(n):
        data['high'][i] = max(data['high'][i], data['open'][i], data['close'][i])
        data['low'][i] = min(data['low'][i], data['open'][i], data['close'][i])
    
    return pd.DataFrame(data)

# 创建tokenizer
tokenizer = CandlestickTokenizer(
    change_range=(-12, 12),
    entity_range=(-12, 12),
    shadow_range=(0, 7),
    include_volume=True
)

# 打印特殊标记
print("特殊标记:")
print(f"PAD_TOKEN: {tokenizer.PAD_TOKEN}")
print(f"UNK_TOKEN: {tokenizer.UNK_TOKEN}")
print(f"ANOMALY_TOKEN: {tokenizer.ANOMALY_TOKEN}")
print(f"DAY_GAP_TOKEN: {tokenizer.DAY_GAP_TOKEN}")
print(f"HOLIDAY_TOKEN: {tokenizer.HOLIDAY_TOKEN}")

# 检查是否有BOS_TOKEN和EOS_TOKEN
print("\n检查是否有BOS_TOKEN和EOS_TOKEN:")
has_bos = hasattr(tokenizer, 'BOS_TOKEN')
has_eos = hasattr(tokenizer, 'EOS_TOKEN')
print(f"有BOS_TOKEN: {has_bos}")
print(f"有EOS_TOKEN: {has_eos}")

# 检查词汇表中是否有<BOS>和<EOS>
print("\n检查词汇表中是否有<BOS>和<EOS>:")
has_bos_in_vocab = '<BOS>' in tokenizer.vocab
has_eos_in_vocab = '<EOS>' in tokenizer.vocab
print(f"词汇表中有<BOS>: {has_bos_in_vocab}")
print(f"词汇表中有<EOS>: {has_eos_in_vocab}")

# 创建样本数据
df = create_sample_data(10)

# 对样本数据进行tokenize
tokens = tokenizer.tokenize(df)

# 打印tokens
print("\nTokens:")
print(tokens)

# 解码tokens
features = tokenizer.decode(tokens)

# 打印解码后的特征
print("\n解码后的特征:")
print(features[:3])  # 只打印前3个特征

# 可视化tokenization
tokenizer.visualize_tokenization(df, tokens)
