{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=====中信建投实盘CTP交易-XZY: ********=====\n", "{'交割手续费': 0.0,\n", " '保证金占用': 8869.4,\n", " '可用资金': 182822.46,\n", " '基础保证金': 10.0,\n", " '多头期权市值': 2.5,\n", " '客户权益': 191691.86,\n", " '市值权益': 191694.36,\n", " '平仓盈亏': 0.0,\n", " '手续费': 0.0,\n", " '持仓盯市盈亏': -200.0,\n", " '期初结存': 191891.86,\n", " '期权执行盈亏': 0.0,\n", " '权利金收入': 0.0,\n", " '空头期权市值': 0.0,\n", " '货币质入': 0.0,\n", " '货币质出': 0.0,\n", " '货币质押保证金占用': 0.0,\n", " '质押变化金额': 0.0,\n", " '质押金': 0.0,\n", " '风险度': 4.63}\n", "TransactionRecord-----------------------------------\n", "None\n", "PositionClosed-----------------------------------\n", "None\n", "Positions-----------------------------------\n", "        品种          合约 买持       买均价 卖持    卖均价       今结算   持仓盯市盈亏    保证金占用\n", "0       纯碱       SA301  1  2499.000  0  0.000  2601.000   180.00  6242.40\n", "1   精对苯二甲酸       TA301  1  5394.000  0  0.000  5254.000  -380.00  2627.00\n", "2  PTA看涨期权  TA301C7000  1     1.000  0  0.000     0.500     0.00     0.00\n", "PositionsDetail-----------------------------------\n", "   交易所       品种          合约      开仓日期 买/卖 持仓量       开仓价       结算价     浮动盈亏  \\\n", "0  郑商所       纯碱       SA301  ********   买   1  2499.000  2601.000  2040.00   \n", "1  郑商所   精对苯二甲酸       TA301  ********   买   1  5394.000  5254.000  -700.00   \n", "2  郑商所  PTA看涨期权  TA301C7000  ********   买   1     1.000     0.500     0.00   \n", "\n", "      盯市盈亏      保证金  期权市值  \n", "0   180.00  6242.40  0.00  \n", "1  -380.00  2627.00  0.00  \n", "2     0.00     0.00  2.50  \n", "\n", "=====南华期货实盘CTP交易-SE: ********=====\n", "{'交割保证金': 0.0,\n", " '交割手续费': 0.0,\n", " '保证金占用': 22134.9,\n", " '出入金': 0.0,\n", " '可用资金': 273907.57,\n", " '基础保证金': 1001.0,\n", " '多头期权市值': 0.0,\n", " '客户权益': 296042.47,\n", " '市值权益': 296042.47,\n", " '手续费': 3.87,\n", " '持仓盯市盈亏': 550.0,\n", " '期初结存': 295786.34,\n", " '期末结存': 296042.47,\n", " '期权执行盈亏': 0.0,\n", " '权利金收入': 0.0,\n", " '空头期权市值': 0.0,\n", " '行权手续费': 0.0,\n", " '货币质入': 0.0,\n", " '货币质出': 0.0,\n", " '货币质押保证金占用': 0.0,\n", " '质押变化金额': 0.0,\n", " '风险度': 7.48}\n", "TransactionRecord-----------------------------------\n", "       成交日期  交易所   品种      合约 买/卖       成交价 手数       成交额  开平   手续费     平仓盈亏\n", "0  ********  上期所  螺纹钢  rb2301   卖  3686.000  1  36860.00  平昨  3.87  -290.00\n", "PositionClosed-----------------------------------\n", "       平仓日期   品种      合约      开仓日期 买/卖 手数       开仓价       成交价     平仓盈亏\n", "0  ********  螺纹钢  rb2301  ********   卖  1  3658.000  3686.000  -290.00\n", "Positions-----------------------------------\n", "       品种      合约 买持       买均价 卖持       卖均价       今结算   持仓盯市盈亏     保证金占用\n", "0  精对苯二甲酸   TA301  1  5394.000  0     0.000  5254.000  -380.00   3940.50\n", "1    玉米淀粉  cs2301  1  3045.000  0     0.000  2976.000   160.00   4464.00\n", "2     聚丙烯  pp2301  0     0.000  2  7544.000  7628.000   770.00  13730.40\n", "PositionsDetail-----------------------------------\n", "   交易所      品种      合约      开仓日期 买/卖 持仓量       开仓价       结算价     浮动盈亏  \\\n", "0  郑商所  精对苯二甲酸   TA301  ********   买   1  5394.000  5254.000  -700.00   \n", "1  大商所    玉米淀粉  cs2301  ********   买   1  3045.000  2976.000  -690.00   \n", "2  大商所     聚丙烯  pp2301  ********   卖   2  7544.000  7628.000  -840.00   \n", "\n", "      盯市盈亏       保证金  期权市值  \n", "0  -380.00   3940.50  0.00  \n", "1   160.00   4464.00  0.00  \n", "2   770.00  13730.40  0.00  \n"]}], "source": ["from pyqlab.scripts.settlementdata import SettlementData\n", "sd=SettlementData()\n", "sd.DisplaySummary(\n", "    content=['account', 'trans', 'closed', 'position'],\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'                                        中信建投期货有限公司                                        \\r\\n                                                                    制表时间 Creation Date：********\\r\\n----------------------------------------------------------------------------------------------------\\r\\n                             交易结算单(盯市) Settlement Statement(MTM)                             \\r\\n客户号 Client ID：  ********          客户名称 Client Name：许贞勇\\r\\n日期 Date：********\\r\\n\\r\\n\\r\\n\\n尊敬的投资者：        根据交易所规定，上期所2212系列期权合约到期日为11月24日，期权买方如申请行权，请提前准备行权所需资金，期权卖方有履约义务，公司将于到期日当天提前至15:15关闭行权客户的银期转账通道，请您做好资金和持仓的管理，充分了解期权行权履约相关风险。进行期货交易风险较大，请您在入市交易前，全面了解《期货交易风险说明书》及期货交易法律法规、交易所及期货公司的业务规则 ，谨慎投资！为保障保证金安全，您可以登录中国期货市场监控中心www.cfmmc.com的查询系统，检查期货公司结算单信息与查询结果是否一致，\\n如有疑问，应向期货公司询问或向公司所在地证监局反映。您可以登录本公司网站www.cfc108.com，期货公司信息公示平台www.cfachina.org，查询期货公司公示信息.\\r\\n\\r\\n        资金状况   资金账号：********  币种：人民币  Account Summary AccountID：******** Currency：CNY    \\r\\n----------------------------------------------------------------------------------------------------\\r\\n期初结存 Balance b/f：                   191891.86  基础保证金 Initial Margin：                10.00\\r\\n出 入 金 Deposit/Withdrawal：        \\n         0.00  期末结存 Balance c/f：                 191691.86\\r\\n平仓盈亏 Realized P/L：                       0.00  质 押 金 Pledge Amount：                    0.00\\r\\n持仓盯市盈亏 MTM P/L：                     -200.00  客户权益 Client Equity：               191691.86\\r\\n期权执行盈亏 Exercise P/L：                   0.00  货币质押保证金占用 FX Pledge Occ.：         0.00\\r\\n手 续 费 Commission：                         0.00  保证金占用 Margin Occupied：             8869.40\\r\\n行权手续费 Exercise Fee：  \\n                   0.00  交割保证金 Delivery Margin：                0.00\\r\\n交割手续费 Delivery Fee：                     0.00  多头期权市值 Market value(long)：           2.50\\r\\n货币质入 New FX Pledge：                      0.00  空头期权市值 Market value(short)：          0.00\\r\\n货币质出 FX Redemption：                      0.00  市值权益 Market value(equity)：        191694.36\\r\\n质押变化金额 Chg in Pledge Amt：              0.00  可用资金 Fund Avail.：                 182822.46\\r\\n权利金收入 Premiu\\nm received：                 0.00  风 险 度 Risk Degree：                     4.63%\\r\\n权利金支出 Premium paid：                     0.00  应追加资金 Margin Call：                    0.00\\r\\n交割盈亏 Delivery P/L：                       0.00  货币质押变化金额 Chg in FX Pledge:          0.00\\r\\n\\r\\n                                                         持仓明细 Positions Detail\\r\\n-----------------------------------------------------------------------------------------------------------------------------\\n----------------------------------------------------------------------------------------------------------\\r\\n| 投资单元 | 交易所 | 交易编码  |       品种       |      合约      |开仓日期 |   投/保    |买/卖|持仓量 |    开仓价     |     昨结算     |     结算价     |  浮动盈亏  |  盯市盈亏 |  保证金   |       期权市值       |   资金账号   |\\r\\n|InvestUnit|Exchange|tradingcode|     Product      |   Instrument   |Open Date|    S/H     | B/S |Positon|Pos. Open Price|   Prev. Sttl   |Settlement Price| Ac\\ncum. P/L |  MTM P/L  |  Margin   | Market Value(Options)|   AccountID  |\\r\\n---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n|********  |郑商所  |********   |       纯碱       |     SA301      | ********|投机        |买   |      1|       2499.000|        2592.000|        2601.000|     2040.00|     180.00|    6242.40|\\n                  0.00|********      |\\r\\n|********  |郑商所  |********   |   精对苯二甲酸   |     TA301      | ********|投机        |买   |      1|       5394.000|        5330.000|        5254.000|     -700.00|    -380.00|    2627.00|                  0.00|********      |\\r\\n|********  |郑商所  |********   |   PTA看涨期权    |   TA301C7000   | ********|投机        |买   |      1|          1.000|           0.500|           0.500|        0.00|       0.00|       0.00|                  2.50|********   \\n   |\\r\\n---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n|共     3条|        |           |                  |                |         |            |     |      3|               |                |                |     1340.00|    -200.00|    8869.40|                  2.50|              |\\r\\n----------------------------\\n-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n能源中心---INE  上期所---SHFE   中金所---CFFEX  大商所---DCE   郑商所---CZCE   广期所---GFEX\\r\\n买---Buy   卖---Sell  \\r\\n投机---Speculation  套保---Hedge  套利---Arbitrage  一般---General  交易---Trade  做市商---Market Maker\\r\\n\\r\\n                                                               持仓汇\\n Positions\\r\\n--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n| 投资单元 | 交易编码  |       品种       |      合约      |    买持     |    买均价   |     卖持     |    卖均价    |  昨结算  |  今结算  |持仓盯市盈亏|  保证金占用   |  投/保     |   多头期权市值   |   空头期权市值    |   资金账号   |\\r\\n|InvestUnit\\n|tradingcode|      Product     |   Instrument   |  Long Pos.  |Avg Buy Price|  Short Pos.  |Avg Sell Price|Prev. Sttl|Sttl Today|  MTM P/L   |Margin Occupied|    S/H     |Market Value(Long)|Market Value(Short)|   AccountID  |\\r\\n--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n|********  |********   |       纯碱\\n       |     SA301      |            1|     2499.000|             0|         0.000|  2592.000|  2601.000|      180.00|        6242.40|投机        |              0.00|               0.00|********      |\\r\\n|********  |********   |   精对苯二甲酸   |     TA301      |            1|     5394.000|             0|         0.000|  5330.000|  5254.000|     -380.00|        2627.00|投机        |              0.00|               0.00|********      |\\r\\n|********  |********   |   PTA看涨期权    |   TA301C7000   \\n|            1|        1.000|             0|         0.000|     0.500|     0.500|        0.00|           0.00|投机        |              2.50|               0.00|********      |\\r\\n--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n|共     3条|           |                  |                |            3|         \\n    |             0|              |          |          |     -200.00|        8869.40|            |              2.50|               0.00|              |\\r\\n--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\\r\\n\\r\\n\\r\\n\\r\\n注：若有异议的，请在下一交易日开市前通过书面提出，如果您参与连续交易品种，请在下一个交易日连续交易开\\n星叭十分钟通过书面提出，否则视为对本账单所载事项确认。客户或被授权人签章：                               中信建投期货有限公司\\r\\n\\r\\n\\n'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["sd.report.iloc[0,2]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sd.SetData(sd.report.iloc[0,2])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         0    1         2        3           4         5   6  7  8         9   \\\n", "0  ********  郑商所  ********       纯碱       SA301  ********  投机  买  1  2499.000   \n", "1  ********  郑商所  ********   精对苯二甲酸       TA301  ********  投机  买  1  5394.000   \n", "2  ********  郑商所  ********  PTA看涨期权  TA301C7000  ********  投机  买  1     1.000   \n", "\n", "         10        11       12       13       14    15        16  \n", "0  2592.000  2601.000  2040.00   180.00  6242.40  0.00  ********  \n", "1  5330.000  5254.000  -700.00  -380.00  2627.00  0.00  ********  \n", "2     0.500     0.500     0.00     0.00     0.00  2.50  ********  \n"]}], "source": ["print(sd.PositionsDetail())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "import time\n", "from datetime import datetime\n", "import sqlite3\n", "import json\n", "import ast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs\n", "from qtunnel import DataSource, BarData, BarSize, DoRight, RunMode\n", "# ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["db_path = 'd:/RoboQuant'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_orders(portfolio_id=None):\n", "    \"\"\"\n", "    从orders数据库导出订单标签\n", "    \"\"\"\n", "    conn = sqlite3.connect(f'{db_path}/data/ot_store.db')\n", "    cur = conn.cursor()\n", "\n", "    # cur.execute(\"\"\"SELECT * FROM T_Filled_Order WHERE account_id = \"%s\" ORDER BY id DESC\"\"\"%('*****************'))\n", "    cur.execute(\"SELECT * FROM T_Filled_Order ORDER BY id ASC\")\n", "    orders = cur.fetchall()\n", "\n", "    data = []\n", "    for item in orders:\n", "        record = []\n", "        ord = json.loads(item[1])\n", "        if portfolio_id and ord['account_id'] != portfolio_id:\n", "            continue\n", "        record.append(np.int64(ord['order_id']))\n", "        record.append(ord['account_id'])\n", "        record.append(ord['order_book_id'])\n", "        record.append(time.strftime(\"%Y%m%d %H:%M:%S\", time.localtime(ord['trading_dt'])))\n", "        msg = ord['message'].split(',')\n", "        if len(msg) <= 1:\n", "            print(msg)\n", "            continue            \n", "        if len(msg) <= 2:\n", "            record.append(msg[0])\n", "            record.append(msg[1])\n", "            record.append(0)\n", "            record.append(0)\n", "        elif len(msg) > 7:\n", "            record.append(msg[0])\n", "            record.append(msg[1])\n", "            record.append(msg[3])\n", "            record.append(msg[7])\n", "        else:\n", "            record.append(msg[0])\n", "            record.append(msg[1])\n", "            record.append(0)\n", "            record.append(0)\n", "        data.append(record)\n", "\n", "    cur.close()\n", "    conn.close()\n", "\n", "    ord_df = pd.DataFrame(data, columns=['ord_id', 'account_id', 'label', 'datetime', 'oprater', 'direct', 'cost_atr', 'pnl'])\n", "    ord_df['CODE'] = ord_df.apply(lambda x: x['label'][0:-7], axis=1)\n", "\n", "    print(f'export order data: {len(ord_df)} \\\n", "    Today add count: {(ord_df[\"datetime\"] >= datetime.now().strftime(\"%Y%m%d 00:00:00\")).sum()}')\n", "    return ord_df\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def get_orders_label(ord_df):\n", "    data = []\n", "    for _, group in ord_df.groupby(['account_id', 'label']):\n", "        for i in range(len(group)):\n", "            if i + 1 >= len(group):\n", "                break\n", "            if group.iat[i, 4] != \"open\":\n", "                # print(group.iat[i, 4], group.iat[i, 2])\n", "                continue\n", "            item = []\n", "            item.append(group.iat[i, 1])\n", "            item.append(group.iat[i, 2])\n", "            i = i + 1\n", "            if group.iat[i, 4] != \"open\":\n", "                assert item[1] == group.iat[i, 2]\n", "                item.append(group.iat[i, 3])\n", "                item.append(group.iat[i, 5])\n", "                item.append(np.float32(group.iat[i, 6]))\n", "                item.append(np.float32(group.iat[i, 7]))\n", "                item.append(group.iat[i, 8])\n", "                if float(group.iat[i, 7]) > 0:\n", "                    item.append(1)\n", "                else:\n", "                    item.append(0)\n", "                data.append(item)\n", "    return pd.DataFrame(data, columns=['account_id', 'instrument', 'datetime', 'direct', 'cost_atr', 'pnl', 'code', 'label'])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["export order data: 49793     Today add count: 112\n"]}], "source": ["ords=load_orders()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df=get_orders_label(ords)\n", "df.set_index(['account_id', 'code'], inplace=True)\n", "df.sort_index(level=0, inplace=True)"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['*****************', '*****************', '*****************',\n", "       '*****************', '*****************', '*****************',\n", "       '*****************', '*****************', '*****************',\n", "       '*****************', '*****************', '*****************',\n", "       '*****************'], dtype=object)"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["df['account_id'].unique()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>cost_atr</th>\n", "      <th>pnl</th>\n", "      <th>label</th>\n", "    </tr>\n", "    <tr>\n", "      <th>account_id</th>\n", "      <th>code</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"41\" valign=\"top\">*****************</th>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 09:51:05</td>\n", "      <td>L</td>\n", "      <td>1.09</td>\n", "      <td>2840.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 10:05:39</td>\n", "      <td>L</td>\n", "      <td>1.20</td>\n", "      <td>3120.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 13:05:32</td>\n", "      <td>S</td>\n", "      <td>1.03</td>\n", "      <td>2680.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 14:02:44</td>\n", "      <td>S</td>\n", "      <td>2.62</td>\n", "      <td>6800.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 13:13:50</td>\n", "      <td>S</td>\n", "      <td>2.08</td>\n", "      <td>5400.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 09:30:00</td>\n", "      <td>L</td>\n", "      <td>-5.94</td>\n", "      <td>-15440.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 09:57:10</td>\n", "      <td>L</td>\n", "      <td>-5.28</td>\n", "      <td>-13720.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 10:39:08</td>\n", "      <td>S</td>\n", "      <td>1.06</td>\n", "      <td>2760.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 13:46:53</td>\n", "      <td>S</td>\n", "      <td>2.98</td>\n", "      <td>7760.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 09:49:08</td>\n", "      <td>L</td>\n", "      <td>1.02</td>\n", "      <td>2440.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 10:37:14</td>\n", "      <td>L</td>\n", "      <td>1.65</td>\n", "      <td>3960.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 11:16:13</td>\n", "      <td>L</td>\n", "      <td>1.65</td>\n", "      <td>3960.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 13:26:19</td>\n", "      <td>S</td>\n", "      <td>1.07</td>\n", "      <td>2560.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 14:34:07</td>\n", "      <td>S</td>\n", "      <td>1.18</td>\n", "      <td>2840.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 09:32:18</td>\n", "      <td>L</td>\n", "      <td>2.08</td>\n", "      <td>5400.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 09:50:47</td>\n", "      <td>S</td>\n", "      <td>2.05</td>\n", "      <td>5320.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 10:39:03</td>\n", "      <td>L</td>\n", "      <td>2.38</td>\n", "      <td>6200.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 11:05:28</td>\n", "      <td>S</td>\n", "      <td>1.28</td>\n", "      <td>3320.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 13:01:53</td>\n", "      <td>S</td>\n", "      <td>1.32</td>\n", "      <td>3440.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 13:54:48</td>\n", "      <td>L</td>\n", "      <td>1.05</td>\n", "      <td>2720.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 14:13:26</td>\n", "      <td>L</td>\n", "      <td>1.94</td>\n", "      <td>5040.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221101 14:42:16</td>\n", "      <td>L</td>\n", "      <td>1.40</td>\n", "      <td>3640.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221102 09:30:00</td>\n", "      <td>L</td>\n", "      <td>0.54</td>\n", "      <td>1400.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221103 09:47:25</td>\n", "      <td>S</td>\n", "      <td>-5.02</td>\n", "      <td>-13040.000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221104 09:58:08</td>\n", "      <td>L</td>\n", "      <td>1.38</td>\n", "      <td>3600.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221104 10:19:03</td>\n", "      <td>L</td>\n", "      <td>1.31</td>\n", "      <td>3400.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221104 10:51:55</td>\n", "      <td>L</td>\n", "      <td>1.03</td>\n", "      <td>2680.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221104 11:18:29</td>\n", "      <td>L</td>\n", "      <td>1.17</td>\n", "      <td>3040.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221104 13:02:05</td>\n", "      <td>L</td>\n", "      <td>1.09</td>\n", "      <td>2840.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221107 09:34:06</td>\n", "      <td>L</td>\n", "      <td>2.25</td>\n", "      <td>5840.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221107 09:55:00</td>\n", "      <td>X</td>\n", "      <td>0.78</td>\n", "      <td>2040.040039</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221107 10:46:01</td>\n", "      <td>L</td>\n", "      <td>1.05</td>\n", "      <td>2720.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221107 11:05:10</td>\n", "      <td>L</td>\n", "      <td>0.71</td>\n", "      <td>1840.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221107 13:55:01</td>\n", "      <td>X</td>\n", "      <td>0.49</td>\n", "      <td>1279.979980</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>20221108 11:20:48</td>\n", "      <td>S</td>\n", "      <td>1.03</td>\n", "      <td>2680.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 10:32:36</td>\n", "      <td>L</td>\n", "      <td>2.40</td>\n", "      <td>6240.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 10:49:20</td>\n", "      <td>S</td>\n", "      <td>0.09</td>\n", "      <td>240.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 13:08:05</td>\n", "      <td>S</td>\n", "      <td>1.02</td>\n", "      <td>2640.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 09:30:21</td>\n", "      <td>S</td>\n", "      <td>2.03</td>\n", "      <td>5280.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 10:19:40</td>\n", "      <td>S</td>\n", "      <td>2.94</td>\n", "      <td>7640.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM</th>\n", "      <td>IM2211.SF</td>\n", "      <td>******** 09:30:01</td>\n", "      <td>L</td>\n", "      <td>10.85</td>\n", "      <td>28200.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       instrument           datetime direct  cost_atr  \\\n", "account_id        code                                                  \n", "***************** IM    IM2211.SF  ******** 09:51:05      L      1.09   \n", "                  IM    IM2211.SF  ******** 10:05:39      L      1.20   \n", "                  IM    IM2211.SF  ******** 13:05:32      S      1.03   \n", "                  IM    IM2211.SF  ******** 14:02:44      S      2.62   \n", "                  IM    IM2211.SF  ******** 13:13:50      S      2.08   \n", "                  IM    IM2211.SF  ******** 09:30:00      L     -5.94   \n", "                  IM    IM2211.SF  ******** 09:57:10      L     -5.28   \n", "                  IM    IM2211.SF  ******** 10:39:08      S      1.06   \n", "                  IM    IM2211.SF  ******** 13:46:53      S      2.98   \n", "                  IM    IM2211.SF  ******** 09:49:08      L      1.02   \n", "                  IM    IM2211.SF  ******** 10:37:14      L      1.65   \n", "                  IM    IM2211.SF  ******** 11:16:13      L      1.65   \n", "                  IM    IM2211.SF  ******** 13:26:19      S      1.07   \n", "                  IM    IM2211.SF  ******** 14:34:07      S      1.18   \n", "                  IM    IM2211.SF  20221101 09:32:18      L      2.08   \n", "                  IM    IM2211.SF  20221101 09:50:47      S      2.05   \n", "                  IM    IM2211.SF  20221101 10:39:03      L      2.38   \n", "                  IM    IM2211.SF  20221101 11:05:28      S      1.28   \n", "                  IM    IM2211.SF  20221101 13:01:53      S      1.32   \n", "                  IM    IM2211.SF  20221101 13:54:48      L      1.05   \n", "                  IM    IM2211.SF  20221101 14:13:26      L      1.94   \n", "                  IM    IM2211.SF  20221101 14:42:16      L      1.40   \n", "                  IM    IM2211.SF  20221102 09:30:00      L      0.54   \n", "                  IM    IM2211.SF  20221103 09:47:25      S     -5.02   \n", "                  IM    IM2211.SF  20221104 09:58:08      L      1.38   \n", "                  IM    IM2211.SF  20221104 10:19:03      L      1.31   \n", "                  IM    IM2211.SF  20221104 10:51:55      L      1.03   \n", "                  IM    IM2211.SF  20221104 11:18:29      L      1.17   \n", "                  IM    IM2211.SF  20221104 13:02:05      L      1.09   \n", "                  IM    IM2211.SF  20221107 09:34:06      L      2.25   \n", "                  IM    IM2211.SF  20221107 09:55:00      X      0.78   \n", "                  IM    IM2211.SF  20221107 10:46:01      L      1.05   \n", "                  IM    IM2211.SF  20221107 11:05:10      L      0.71   \n", "                  IM    IM2211.SF  20221107 13:55:01      X      0.49   \n", "                  IM    IM2211.SF  20221108 11:20:48      S      1.03   \n", "                  IM    IM2211.SF  ******** 10:32:36      L      2.40   \n", "                  IM    IM2211.SF  ******** 10:49:20      S      0.09   \n", "                  IM    IM2211.SF  ******** 13:08:05      S      1.02   \n", "                  IM    IM2211.SF  ******** 09:30:21      S      2.03   \n", "                  IM    IM2211.SF  ******** 10:19:40      S      2.94   \n", "                  IM    IM2211.SF  ******** 09:30:01      L     10.85   \n", "\n", "                                 pnl  label  \n", "account_id        code                       \n", "***************** IM     2840.000000      1  \n", "                  IM     3120.000000      1  \n", "                  IM     2680.000000      1  \n", "                  IM     6800.000000      1  \n", "                  IM     5400.000000      1  \n", "                  IM   -15440.000000      0  \n", "                  IM   -13720.000000      0  \n", "                  IM     2760.000000      1  \n", "                  IM     7760.000000      1  \n", "                  IM     2440.000000      1  \n", "                  IM     3960.000000      1  \n", "                  IM     3960.000000      1  \n", "                  IM     2560.000000      1  \n", "                  IM     2840.000000      1  \n", "                  IM     5400.000000      1  \n", "                  IM     5320.000000      1  \n", "                  IM     6200.000000      1  \n", "                  IM     3320.000000      1  \n", "                  IM     3440.000000      1  \n", "                  IM     2720.000000      1  \n", "                  IM     5040.000000      1  \n", "                  IM     3640.000000      1  \n", "                  IM     1400.000000      1  \n", "                  IM   -13040.000000      0  \n", "                  IM     3600.000000      1  \n", "                  IM     3400.000000      1  \n", "                  IM     2680.000000      1  \n", "                  IM     3040.000000      1  \n", "                  IM     2840.000000      1  \n", "                  IM     5840.000000      1  \n", "                  IM     2040.040039      1  \n", "                  IM     2720.000000      1  \n", "                  IM     1840.000000      1  \n", "                  IM     1279.979980      1  \n", "                  IM     2680.000000      1  \n", "                  IM     6240.000000      1  \n", "                  IM      240.000000      1  \n", "                  IM     2640.000000      1  \n", "                  IM     5280.000000      1  \n", "                  IM     7640.000000      1  \n", "                  IM    28200.000000      1  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[('*****************', ),]\n", "df.loc[('*****************', 'IM'),]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["81520.04\n", "-44820.082\n", "123600.02\n"]}], "source": ["print(df.loc[('*****************', 'IC'),]['pnl'].sum())\n", "print(df.loc[('*****************', 'IF'),]['pnl'].sum())\n", "print(df.loc[('*****************', 'IM'),]['pnl'].sum())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["82.66666666666667\n", "92.6829268292683\n"]}], "source": ["print(df.loc[('*****************', 'IC'),]['label'].sum()/df.loc[('*****************', 'IC'),]['label'].count() *100)\n", "print(df.loc[('*****************', 'IM'),]['label'].sum()/df.loc[('*****************', 'IM'),]['label'].count() *100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}