import unittest
import pandas as pd
import numpy as np
import torch
from unittest.mock import patch, MagicMock
from pyqlab.data.dataset.dataset_bar import BarDataset
from pyqlab.data.dataset.pipeline import Pipeline

class TestBarDataset(unittest.TestCase):
    """测试BarDataset类的功能"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建配置对象
        self.config = MagicMock()
        self.config.block_size = 10
        self.config.is_sf = False
        self.config.data_path = 'test_data_path'
        self.config.market = 'fut'
        self.config.block_name = 'sf'
        self.config.period = 'day'
        
        # 创建测试数据
        # 模拟两个合约的数据，每个合约有15条记录
        code1 = [1] * 15
        code2 = [2] * 15
        codes = code1 + code2
        
        bars = list(range(15)) + list(range(15))
        
        # 创建时间特征列
        tf0 = [0.1] * 30
        tf1 = [0.2] * 30
        tf2 = [0.3] * 30
        tf3 = [0.4] * 30
        tf4 = [0.5] * 30
        
        # 创建DataFrame
        self.test_data = pd.DataFrame({
            'code_encoded': codes,
            'bar': bars,
            'tf0': tf0,
            'tf1': tf1,
            'tf2': tf2,
            'tf3': tf3,
            'tf4': tf4
        })
        
        # 创建BarDataset实例
        with patch.object(Pipeline, 'get_vocab') as mock_get_vocab:
            # 模拟词汇表
            mock_get_vocab.return_value = [f'{i}|{j}|{k}|{l}' for i in range(-2, 3) for j in range(-2, 3) for k in range(0, 2) for l in range(0, 2)]
            self.dataset = BarDataset(self.config, self.test_data)
    
    def test_init(self):
        """测试初始化功能"""
        # 验证属性是否正确设置
        self.assertEqual(self.dataset.block_size, self.config.block_size)
        self.assertEqual(self.dataset.vocab_size, len(Pipeline.get_vocab()))
        
        # 验证codecount计算是否正确
        # 每个合约有15条记录，减去block_size(10)和1，剩余4条可用作样本
        expected_codecount = np.array([4, 8])  # 第一个合约4个样本，两个合约共8个样本
        np.testing.assert_array_equal(self.dataset.codecount, expected_codecount)
    
    def test_get_vocab_size(self):
        """测试获取词汇表大小功能"""
        vocab_size = self.dataset.get_vocab_size()
        self.assertEqual(vocab_size, len(Pipeline.get_vocab()))
    
    def test_get_block_size(self):
        """测试获取块大小功能"""
        block_size = self.dataset.get_block_size()
        self.assertEqual(block_size, self.config.block_size)
    
    def test_i_to_idx(self):
        """测试索引转换功能"""
        # 测试第一个合约的第一个样本
        idx = self.dataset.i_to_idx(0)
        self.assertEqual(idx, 0)
        
        # 测试第一个合约的最后一个样本
        idx = self.dataset.i_to_idx(3)
        self.assertEqual(idx, 3)
        
        # 测试第二个合约的第一个样本
        idx = self.dataset.i_to_idx(4)
        self.assertEqual(idx, 15)  # 第二个合约的起始位置
    
    def test_len(self):
        """测试数据集长度功能"""
        length = len(self.dataset)
        # 两个合约共8个样本
        self.assertEqual(length, 8)
    
    def test_getitem(self):
        """测试获取样本功能"""
        # 获取第一个样本
        with patch.object(self.dataset.data, 'iloc') as mock_iloc:
            # 模拟iloc返回值
            mock_iloc.side_effect = lambda start, end, cols: pd.DataFrame({
                0: [1] * (end-start),  # code_encoded
                1: list(range(start, end)),  # bar
                -5: [0.1] * (end-start),  # tf0
                -4: [0.2] * (end-start),  # tf1
                -3: [0.3] * (end-start),  # tf2
                -2: [0.4] * (end-start),  # tf3
                -1: [0.5] * (end-start)   # tf4
            })
            
            code, x, x_mark, y, y_mark = self.dataset[0]
            
            # 验证返回值类型
            self.assertIsInstance(code, torch.Tensor)
            self.assertIsInstance(x, torch.Tensor)
            self.assertIsInstance(x_mark, torch.Tensor)
            self.assertIsInstance(y, torch.Tensor)
            self.assertIsInstance(y_mark, torch.Tensor)
            
            # 验证形状
            self.assertEqual(code.shape, (self.config.block_size,))
            self.assertEqual(x.shape, (self.config.block_size,))
            self.assertEqual(x_mark.shape, (self.config.block_size, 5))
            self.assertEqual(y.shape, (self.config.block_size,))
            self.assertEqual(y_mark.shape, (self.config.block_size, 5))

if __name__ == '__main__':
    unittest.main() 