#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BarTokenizer命令行分析工具
专门用于命令行环境的token分布分析，不显示图表
"""

import numpy as np
import pandas as pd
import sys
import os
from typing import Dict, Any, Tuple

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def generate_sample_data(n_samples: int = 5000) -> pd.DataFrame:
    """生成示例K线数据"""
    np.random.seed(42)
    
    # 生成价格序列
    base_price = 100
    returns = np.random.normal(0, 0.02, n_samples)
    prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLCV数据
    df = pd.DataFrame({
        'open': prices,
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n_samples))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n_samples))),
        'close': prices * (1 + np.random.normal(0, 0.005, n_samples)),
        'volume': np.random.lognormal(10, 1, n_samples)
    })
    
    # 确保OHLC数据的合理性
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    return df

def load_real_data(data_path: str = None, sample_size: int = 5000) -> pd.DataFrame:
    """加载真实数据"""
    if not data_path:
        # 尝试常见的数据路径
        data_paths = [
            'f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv',
            'e:/hqdata/tick/2024/SF202409.parquet',
        ]
        
        for path in data_paths:
            if os.path.exists(path):
                data_path = path
                break
    
    if data_path and os.path.exists(data_path):
        try:
            if data_path.endswith('.csv'):
                df = pd.read_csv(data_path)
            elif data_path.endswith('.parquet'):
                df = pd.read_parquet(data_path)
            else:
                raise ValueError("不支持的文件格式")
            
            print(f"✅ 成功加载真实数据: {data_path}")
            print(f"   数据形状: {df.shape}")
            
            # 如果数据太大，采样
            if len(df) > sample_size:
                df = df.sample(n=sample_size, random_state=42)
                print(f"   采样到: {len(df)} 行")
            
            # 检查是否包含必需的列
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if all(col in df.columns for col in required_cols):
                return df
            else:
                print(f"❌ 数据不包含必需的OHLCV列，使用模拟数据")
                
        except Exception as e:
            print(f"❌ 加载数据失败: {e}，使用模拟数据")
    
    print("使用模拟数据进行分析")
    return generate_sample_data(sample_size)

def analyze_tokenizer_methods(test_data: pd.DataFrame, n_bins: int = 100) -> Dict:
    """分析不同BarTokenizer方法"""
    
    try:
        from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
    except ImportError as e:
        print(f"❌ 导入BarTokenizer失败: {e}")
        return {}
    
    print(f"使用数据样本大小: {len(test_data)}")
    
    # 定义要测试的方法
    methods = {
        'Linear': {'mapping_strategy': 'linear', 'balancing_strategy': 'none'},
        'Linear+Balance': {'mapping_strategy': 'linear', 'balancing_strategy': 'frequency'},
        'Quantile': {'mapping_strategy': 'quantile', 'balancing_strategy': 'none'},
        'Quantile+Balance': {'mapping_strategy': 'quantile', 'balancing_strategy': 'frequency'},
        'Adaptive': {'mapping_strategy': 'adaptive', 'balancing_strategy': 'none'},
        'Adaptive+Balance': {'mapping_strategy': 'adaptive', 'balancing_strategy': 'frequency'}
    }
    
    results = {}
    
    # 测试每种方法
    for method_name, config in methods.items():
        print(f"\n🔍 测试方法: {method_name}")
        
        try:
            # 创建tokenizer
            tokenizer = BarTokenizer(
                mapping_strategy=config['mapping_strategy'],
                balancing_strategy=config['balancing_strategy'],
                n_bins=n_bins,
                features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
                atr_period=14
            )
            
            # 拟合和转换
            tokens = tokenizer.fit_transform(test_data)
            
            # 分析分布
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            # 计算额外的统计信息
            unique_tokens, counts = np.unique(tokens, return_counts=True)
            frequencies = counts / len(tokens)
            
            results[method_name] = {
                'tokenizer': tokenizer,
                'tokens': tokens,
                'unique_tokens': unique_tokens,
                'counts': counts,
                'frequencies': frequencies,
                'balance_metrics': balance_metrics,
                'vocab_size': tokenizer.get_vocab_size(),
                'vocab_utilization': len(unique_tokens) / tokenizer.get_vocab_size()
            }
            
            print(f"   ✅ 成功")
            print(f"   📊 词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"   📊 唯一token数: {len(unique_tokens)}")
            print(f"   📊 词汇表利用率: {len(unique_tokens) / tokenizer.get_vocab_size():.2%}")
            print(f"   📊 基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"   📊 标准化熵: {balance_metrics['normalized_entropy']:.4f}")
            print(f"   📊 变异系数: {balance_metrics['coefficient_of_variation']:.4f}")
            
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            results[method_name] = None
    
    return results

def print_detailed_analysis(results: Dict):
    """打印详细分析结果"""
    
    valid_results = {k: v for k, v in results.items() if v is not None}
    
    if len(valid_results) == 0:
        print("❌ 没有有效的分析结果")
        return
    
    print("\n" + "="*80)
    print("📈 BarTokenizer方法详细对比分析")
    print("="*80)
    
    # 创建汇总数据
    summary_data = []
    for method_name, result in valid_results.items():
        metrics = result['balance_metrics']
        summary_data.append({
            '方法': method_name,
            '词汇表大小': result['vocab_size'],
            '唯一Token数': len(result['unique_tokens']),
            '利用率(%)': f"{result['vocab_utilization']*100:.1f}",
            '基尼系数': f"{metrics['gini_coefficient']:.4f}",
            '标准化熵': f"{metrics['normalized_entropy']:.3f}",
            '变异系数': f"{metrics['coefficient_of_variation']:.2f}",
            '频率范围': f"{metrics['frequency_range']:.4f}"
        })
    
    # 转换为DataFrame并打印
    df_summary = pd.DataFrame(summary_data)
    print(df_summary.to_string(index=False))
    
    # 分析最佳方法
    print("\n" + "="*80)
    print("🏆 最佳方法推荐")
    print("="*80)
    
    # 计算综合评分
    scores = {}
    for method_name, result in valid_results.items():
        metrics = result['balance_metrics']
        
        # 标准化指标（越小越好的指标取倒数）
        gini_score = 1 / (1 + metrics['gini_coefficient'])  # 基尼系数越小越好
        entropy_score = metrics['normalized_entropy']  # 标准化熵越大越好
        utilization_score = result['vocab_utilization']  # 利用率越大越好
        cv_score = 1 / (1 + metrics['coefficient_of_variation'])  # 变异系数越小越好
        
        # 综合评分（可以调整权重）
        total_score = (gini_score * 0.3 + entropy_score * 0.3 + 
                      utilization_score * 0.2 + cv_score * 0.2)
        
        scores[method_name] = total_score
    
    # 排序并显示
    sorted_methods = sorted(scores.items(), key=lambda x: x[1], reverse=True)
    
    for i, (method, score) in enumerate(sorted_methods):
        rank_emoji = ["🥇", "🥈", "🥉"][i] if i < 3 else f"{i+1}."
        print(f"{rank_emoji} {method}: {score:.4f}")
        
        if i == 0:
            result = valid_results[method]
            metrics = result['balance_metrics']
            print(f"   推荐理由:")
            print(f"   - 基尼系数: {metrics['gini_coefficient']:.4f} (分布均匀)")
            print(f"   - 标准化熵: {metrics['normalized_entropy']:.3f} (随机性好)")
            print(f"   - 词汇表利用率: {result['vocab_utilization']:.2%} (利用充分)")
    
    # 高频token分析
    print("\n" + "="*80)
    print("🔍 高频Token分析")
    print("="*80)
    
    for method_name, result in valid_results.items():
        frequencies = result['frequencies']
        unique_tokens = result['unique_tokens']
        
        # 按频率排序
        sorted_indices = np.argsort(frequencies)[::-1]
        
        print(f"\n{method_name}:")
        print(f"  前5个最高频Token:")
        for i in range(min(5, len(sorted_indices))):
            idx = sorted_indices[i]
            token_id = unique_tokens[idx]
            freq = frequencies[idx]
            count = result['counts'][idx]
            print(f"    Token {token_id:4d}: {freq:.4f} ({count:5d}次)")
        
        # 计算前10%token的累计频率
        top_10_percent_count = max(1, len(frequencies) // 10)
        top_10_percent_freq = np.sum(frequencies[sorted_indices[:top_10_percent_count]])
        print(f"  前10%Token累计频率: {top_10_percent_freq:.4f}")

def main():
    """主函数"""
    print("🚀 BarTokenizer命令行分析工具")
    print("="*60)
    
    # 解析命令行参数
    data_path = None
    sample_size = 5000
    n_bins = 100
    
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
    if len(sys.argv) > 2:
        try:
            sample_size = int(sys.argv[2])
        except ValueError:
            print("⚠️  样本大小参数无效，使用默认值5000")
    if len(sys.argv) > 3:
        try:
            n_bins = int(sys.argv[3])
        except ValueError:
            print("⚠️  bins数量参数无效，使用默认值100")
    
    print(f"📋 分析参数:")
    print(f"   数据路径: {data_path or '自动检测'}")
    print(f"   样本大小: {sample_size}")
    print(f"   Token数量: {n_bins}")
    
    # 准备数据
    print(f"\n📂 准备测试数据...")
    test_data = load_real_data(data_path, sample_size)
    print(f"   最终数据形状: {test_data.shape}")
    print(f"   数据列: {test_data.columns.tolist()}")
    
    # 分析不同方法
    print(f"\n🔬 开始分析不同BarTokenizer方法...")
    analysis_results = analyze_tokenizer_methods(test_data, n_bins)
    
    # 检查结果
    valid_results = {k: v for k, v in analysis_results.items() if v is not None}
    if len(valid_results) == 0:
        print("❌ 没有成功的分析结果，请检查BarTokenizer模块是否正确安装")
        return
    
    print(f"\n✅ 成功分析了 {len(valid_results)}/{len(analysis_results)} 种方法")
    
    # 打印详细分析
    print_detailed_analysis(analysis_results)
    
    print(f"\n🎉 分析完成！")
    print("="*60)

if __name__ == "__main__":
    main()
