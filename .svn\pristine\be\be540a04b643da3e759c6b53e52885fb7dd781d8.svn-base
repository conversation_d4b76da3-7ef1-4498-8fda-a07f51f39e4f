from typing import Union, List, Tuple, Dict, Text, Optional
from .handler import DataHandler
import pandas as pd
import numpy as np
import torch
from inspect import getfullargspec
from qlib.log import get_module_logger
from qlib.utils import init_instance_by_config
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP

from torch.utils.data import Dataset

device = "cuda" if torch.cuda.is_available() else "cpu"

def _to_tensor(x):
    if not isinstance(x, torch.Tensor):
        return torch.tensor(x, dtype=torch.float, device=device)
    return x

class AFDatasetH(DatasetH):
    '''
    (A)icm (F)actor Dataset (H)andler


    '''
    def __init__(
        self,
        handler: Union[Dict, DataHandler],
        segments: Dict[Text, Tuple],
        batch_size=32,
        shuffle=True,
        drop_last=False,
        **kwargs
    ):
        self.batch_size=batch_size
        self.shuffle=shuffle
        self.drop_last=drop_last
        self.lb_df = pd.DataFrame()
        self.ft_df = pd.DataFrame()

        self.segments = segments.copy()
        self.fetch_kwargs = {}
        super().__init__(handler, segments, **kwargs)

    def config(self, handler_kwargs: dict = None, **kwargs):
        if handler_kwargs is not None:
            self.handler.config(**handler_kwargs)        
        super().config(**kwargs)

    def setup_data(self, handler_kwargs: dict = None, **kwargs):
        super().setup_data(**kwargs)
        if handler_kwargs is not None:
            self.handler.setup_data(**handler_kwargs)

    def _prepare_seg(self, slc: slice, **kwargs):
        """
        Give a slice, retrieve the according data

        Parameters
        ----------
        slc : slice
        """
        if hasattr(self, "fetch_kwargs"):
            return self.handler.fetch(slc, **kwargs, **self.fetch_kwargs)
        else:
            return self.handler.fetch(slc, **kwargs)

    def prepare(
        self,
        segments: Union[List[Text], Tuple[Text], Text, slice],
        col_set=DataHandler.CS_ALL,
        data_key=DataHandlerLP.DK_I,
        **kwargs
    ) -> Union[List[pd.DataFrame], pd.DataFrame]:
        logger = get_module_logger("DatasetH")
        fetch_kwargs = {"col_set": col_set}
        fetch_kwargs.update(kwargs)
        if "data_key" in getfullargspec(self.handler.fetch).args:
            fetch_kwargs["data_key"] = data_key
        else:
            logger.info(f"data_key[{data_key}] is ignored.")

        # Handle all kinds of segments format
        if isinstance(segments, (list, tuple)):
            return [self._prepare_seg(seg, **fetch_kwargs) for seg in segments]
        elif isinstance(segments, str):
            return self._prepare_seg(segments, **fetch_kwargs)
        # elif isinstance(segments, slice):
        #     return self._prepare_seg(segments, **fetch_kwargs)
        else:
            raise NotImplementedError(f"This type of input is not supported")
        # if isinstance(segments, str):
        #     self._lb_df, self._data = self.handler.fetch(segments, **kwargs)
        #     return self._data
        # if isinstance(segments, (list, tuple)):
        #     return
        # else:
        #     raise NotImplementedError(f"This type of input is not supported")
    
    def __len__(self):
        batch_size = self.batch_size
        if self.batch_size < 0:
            batch_size = -1*self.batch_size
        if self.drop_last:
            return len(self.ft_df) // batch_size
        return (len(self.ft_df) + batch_size - 1) // batch_size


    def __iter__(self):
        batch_size = abs(self.batch_size)
        indices = np.arange(len(self.ft_df))
        if self.shuffle:
            np.random.shuffle(indices)

        for i in range(len(indices))[::batch_size]:
            if self.drop_last and i+batch_size>len(indices):
                break

            code_encoded = []
            data = []
            label = []

            for j in indices[i: i + batch_size]:
                data.append(self.ft_df.iloc[j].values.tolist())
                code_encoded.append(self.lb_df.iloc[j]['code_encoded'])
                label.append(self.lb_df.iloc[j]['label'])

            code_encoded = _to_tensor(np.stack(code_encoded))
            data = _to_tensor(np.stack(data))
            label = _to_tensor(np.stack(label))
            yield {
                "code_encoded": code_encoded,
                "data": data,
                "label": label,
            }


class AFDatasetL(Dataset):

    def __init__(self, data):
        self.encodeds = data["encoded"]
        self.features = data["feature"]
        self.labels = data["label"]

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):

        features = torch.as_tensor(np.array(self.features.iloc[idx]), dtype=torch.float)
        labels = self.labels.iloc[idx]
        labels = torch.as_tensor(labels, dtype=torch.float)
        code_encodeds = torch.as_tensor(self.encodeds.iloc[idx], dtype=torch.long)
        return code_encodeds, features, labels