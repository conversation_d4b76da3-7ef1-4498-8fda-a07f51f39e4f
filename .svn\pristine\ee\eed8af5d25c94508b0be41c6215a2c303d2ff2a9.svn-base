#%%
from pyqlab.contrib.model.pytorch_mlp_pl import MLPModel
from pyqlab.data.dataset import AFDatasetL
from qlib.utils import init_instance_by_config
from qlib.data.dataset.handler import DataHandlerLP
from torch.utils.data import DataLoader

import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import Model<PERSON>heckpoint
from pytorch_lightning.callbacks.early_stopping import EarlyStopping

def trainer(early_stop=False, save_model=False):

    model_params = {
        'model_name': "MLP2",
        'model_path': 'e:/lab/RoboQuant/pylab/model',
        'model_parameters': {
            'batch_norm': True,
            'drop_out': True,
        }
    }

    training_params = {
        'amp': False,
        'learning_rate': 0.001,
        'weight_decay': 0,
        'epochs': 300,
        'batch_size': 128,
        'reduce_lr_patience': 5,
        'reduce_lr_factor': 0.25,
        'reduce_lr_min': 0.000001,
        'early_stopping_patience': 20,
        'random_state': 42,
        'deterministic_cudnn': False,
        'valid_fold': 1,
    }

    data_handler_config = {
        "start_time": "2008-01-01",
        "end_time": "2020-08-01",
        "instruments": "",
        "data_loader": {
            "class": "AFDataLoader",
            "module_path": "pyqlab.data.dataset.loader",
            "kwargs": {
                "direct": "long",
                "model_name": "MLP2",
                "model_path": "e:/lab/RoboQuant/pylab/model",
                "data_path": "e:/lab/RoboQuant/pylab/data",
                # 'portfolios': ['00211229152555000', '00171106132928000'], # 5R
                # 'portfolios': ['00200910081133001', '00170623114649000'], # 7R
                'portfolios': ['00200910081133001', '00171106132928000'],  
                # 'portfolios': ['00211229152555000', '00170623114649000'],  
            }
        },
    }

    dataset_config = {
        "class": "AFDatasetH",
        "module_path": "pyqlab.data.dataset",
        "kwargs": {
            "handler": {
                "class": "DataHandlerAF",
                "module_path": "pyqlab.data.dataset.handler",
                "kwargs": data_handler_config,
            },
            "segments": ["train", "valid"],
            "col_set": ["feature", "label", "encoded"],
        },
    }

    directs = ['long', 'short']
    for direct in directs:
        print(f'\n{"*" * 30}\n{direct}\n{"*" * 30}')

        dataset = init_instance_by_config(dataset_config)

        df_train, df_valid = dataset.prepare(
            ["train", "valid"], col_set=["feature", "label", "encoded"], data_key=DataHandlerLP.DK_L
        )
  
        train_ds = AFDatasetL(df_train)
        train_dl = DataLoader(
            train_ds,
            batch_size=training_params['batch_size'],
            # sampler=RandomSampler(train_ds),
            # pin_memory=True,
            # drop_last=False,
            # num_workers=training_params['num_workers'],
        )
        valid_ds = AFDatasetL(df_valid)
        valid_dl = DataLoader(
            valid_ds,
            batch_size=training_params['batch_size'],
            # sampler=SequentialSampler(valid_ds),
            # pin_memory=True,
            # drop_last=False,
            # num_workers=training_params['num_workers'],
        )
        input_num= df_train["feature"].shape[1]
        model = MLPModel(30, input_num, True, True)

        model.summarize(max_depth=1)
        cb = []
        # checkpoint_callback = ModelCheckpoint(monitor='valid/loss', mode='min')
        checkpoint_callback = ModelCheckpoint(monitor='valid/accuracy', mode='max')
        cb.append(checkpoint_callback)
        if early_stop:
            early_stop_callback = EarlyStopping(monitor="valid/accuracy", min_delta=0.00, patience=3, verbose=False, mode="max")
            cb.append(early_stop_callback)
        trainer = pl.Trainer(callbacks=cb, gpus=-1, auto_select_gpus=True, precision=16, max_epochs=training_params['epochs'], log_every_n_steps=5)
        trainer.fit(model, train_dl, valid_dl)

        # 训练完成之后，保存了多个模型，下面是获得最好的模型，也就是将原来保存的模型中最好的模型权重apply到当前的网络上
        print(f'Best loss epoch {model.loss_df["valid/loss"].argmin()}: {model.loss_df["valid/loss"].min()}')
        print(f'Best accuracy epoch {model.accuracy_df["valid/accuracy"].argmax()}: {model.accuracy_df["valid/accuracy"].max()}')

        print(checkpoint_callback.best_model_path)
        if save_model:
            # model = MLPModel.load_from_checkpoint(checkpoint_callback.best_model_path)
            checkpoint = torch.load(checkpoint_callback.best_model_path, map_location=lambda storage, loc: storage)
            model.load_state_dict(checkpoint['state_dict'])
            model.eval()
            sm = torch.jit.script(model)
            sm.save(f'{model_params["model_path"]}/{model_params["model_name"]}_{direct}.model')

import warnings
warnings.filterwarnings("ignore")

if __name__ == '__main__':
    trainer(save_model=True)



# %%
