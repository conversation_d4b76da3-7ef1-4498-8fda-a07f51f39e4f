{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# from pyecharts import online\n", "import datetime\n", "import talib as ta\n", "import pandas as pd\n", "# from pyecharts import Kline\n", "# online()\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "sys.path.append(\"d:/QuantTunnel\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"output_type": "stream", "name": "stdout", "text": "Mean ATR  ATR Percent      ATR  default ATR\nIC2006.SF     32.00     0.592088    32.00        32.00\nIF2006.SF     25.00     0.647501    25.00        25.00\nIH2006.SF     13.00     0.467727    13.00        13.00\nAP2010.ZC    182.00     2.025824   182.00       182.00\nCF2009.ZC    119.00     1.027634   119.00       119.00\nCJ2009.ZC     41.00     0.407960    41.00        41.00\nCY2009.ZC    305.00     1.671233   305.00       305.00\nFG2009.ZC     15.00     1.099707    15.00        15.00\nMA2009.ZC     24.00     1.426873    24.00        24.00\nOI2009.ZC     31.00     0.459123    31.00        31.00\nRM2009.ZC     17.00     0.737527    17.00        17.00\nSA2009.ZC      9.00     0.633357     9.00         9.00\nSF2009.ZC     43.00     0.731790    43.00        43.00\nSM2009.ZC    100.00     1.412828   100.00       100.00\nSR2009.ZC     26.00     0.522613    26.00        26.00\nTA2009.ZC     30.00     0.833797    30.00        30.00\nUR2009.ZC     12.00     0.783801    12.00        12.00\nA2009.DC      36.00     0.814480    36.00        36.00\nC2009.DC      14.00     0.678623    14.00        14.00\nCS2009.DC     18.00     0.747508    18.00        18.00\nEB2009.DC     65.00     1.159265    65.00        65.00\nEG2009.DC     38.00     1.025918    38.00        38.00\nI2009.DC       7.00     0.977654     7.00         7.00\nJ2009.DC      32.00     1.916168    32.00        32.00\nJD2006.DC     73.00     2.258663    73.00        73.00\nJM2009.DC     16.00     1.391304    16.00        16.00\nL2009.DC     149.00     2.540494   149.00       149.00\nM2009.DC      30.00     1.089325    30.00        30.00\nP2009.DC      35.00     0.781250    35.00        35.00\nPG2011.DC    115.00     3.444145   115.00       115.00\nPP2009.DC    162.00     2.452316   162.00       162.00\nRR2009.DC     33.00     0.970303    33.00        33.00\nV2009.DC      78.00     1.295681    78.00        78.00\nY2009.DC      70.00     1.298220    70.00        70.00\nAG2012.SC     57.00     1.373825    57.00        57.00\nAL2007.SC    159.00     1.184358   159.00       159.00\nAU2012.SC      3.15     0.864198     3.15         3.15\nBU2012.SC     30.00     1.255230    30.00        30.00\nCU2007.SC    625.00     1.361656   625.00       625.00\nHC2010.SC     25.00     0.732493    25.00        25.00\nNI2007.SC   1871.00     1.817740  1871.00      1871.00\nNR2007.SC     82.00     0.989740    82.00        82.00\nPB2007.SC    184.00     1.310075   184.00       184.00\nRB2010.SC     47.00     1.339795    47.00        47.00\nRU2009.SC    106.00     1.040746   106.00       106.00\nSC2007.SC      6.00     1.577287     6.00         6.00\nSN2007.SC   2343.00     1.726094  2343.00      2343.00\nSP2009.SC     21.00     0.479233    21.00        21.00\nSS2007.SC     81.00     0.617613    81.00        81.00\nZN2007.SC    243.00     1.484423   243.00       243.00\n"}], "source": ["def get_rangebar_atr(codelist, barsize):\n", "    atrs = {}\n", "    for symbol in codelist:\n", "        atrs[symbol] = ds.get_rangebar_atr(symbol, barsize)\n", "    return atrs\n", "\n", "def get_default_rangebar_atr(codelist, barsize, islast=True):\n", "    atrs = {}\n", "    for symbol in codelist:\n", "        atrs[symbol] = ds.get_default_rangebar_atr(symbol, barsize, islast)\n", "    return atrs\n", "\n", "def get_last_price_percent(codelist, barsize):\n", "    prices = {}\n", "    for symbol in codelist:\n", "        price = ds.price(symbol)\n", "        if price > 0:\n", "            prices[symbol] = ds.get_default_rangebar_atr(symbol, barsize, False)/ price * 100\n", "        else:\n", "            prices[symbol] = 0.0\n", "            print(\"%s price = 0\" % symbol)\n", "    return prices\n", "\n", "barsize = BarSize.day\n", "zlqh = ds.get_block_data(\"ZLQH\")\n", "mean_atr = get_default_rangebar_atr(zlqh, barsize, False)\n", "atr_percent = get_last_price_percent(zlqh, barsize)\n", "atr_range = get_rangebar_atr(zlqh, barsize)\n", "atr_dflt_range = get_default_rangebar_atr(zlqh, barsize)\n", "\n", "temp_table = {'Mean ATR': mean_atr, 'ATR Percent': atr_percent, 'ATR': atr_range, 'default ATR': atr_dflt_range}\n", "df = pd.DataFrame(temp_table)\n", "print(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 参数选择\n", "关于ATR，STDDEV等指标的period参数，在BarSize分别为day或min5的情况下，period通常取值20，min5如果取值20会显得太小，合约大部分交易时间波动性并不明显，特别是在期货夜盘时，更是这样。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"output_type": "stream", "name": "stdout", "text": "===================STK====================\n       ATR  CHG  PRICE\ncount  0.0  0.0    0.0\nmean   NaN  NaN    NaN\nstd    NaN  NaN    NaN\nmin    NaN  NaN    NaN\n25%    NaN  NaN    NaN\n50%    NaN  NaN    NaN\n75%    NaN  NaN    NaN\nmax    NaN  NaN    NaN\n"}, {"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\r\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\r\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\r\n<!-- Created with matplotlib (https://matplotlib.org/) -->\r\n<svg height=\"248.518125pt\" version=\"1.1\" viewBox=\"0 0 386.845313 248.518125\" width=\"386.845313pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\r\n <defs>\r\n  <style type=\"text/css\">\r\n*{stroke-linecap:butt;stroke-linejoin:round;}\r\n  </style>\r\n </defs>\r\n <g id=\"figure_1\">\r\n  <g id=\"patch_1\">\r\n   <path d=\"M 0 248.518125 \r\nL 386.845313 248.518125 \r\nL 386.845313 0 \r\nL 0 0 \r\nz\r\n\" style=\"fill:none;\"/>\r\n  </g>\r\n  <g id=\"axes_1\">\r\n   <g id=\"patch_2\">\r\n    <path d=\"M 44.845313 224.64 \r\nL 379.645313 224.64 \r\nL 379.645313 7.2 \r\nL 44.845313 7.2 \r\nz\r\n\" style=\"fill:#ffffff;\"/>\r\n   </g>\r\n   <g id=\"patch_3\">\r\n    <path clip-path=\"url(#pf8b8b1075b)\" d=\"M 60.063494 115.92 \r\nL 364.427131 115.92 \r\nL 364.427131 115.92 \r\nL 60.063494 115.92 \r\nz\r\n\" style=\"fill:#1f77b4;\"/>\r\n   </g>\r\n   <g id=\"matplotlib.axis_1\">\r\n    <g id=\"xtick_1\">\r\n     <g id=\"line2d_1\">\r\n      <defs>\r\n       <path d=\"M 0 0 \r\nL 0 3.5 \r\n\" id=\"m313791c4b7\" style=\"stroke:#000000;stroke-width:0.8;\"/>\r\n      </defs>\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"60.063494\" xlink:href=\"#m313791c4b7\" y=\"224.64\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_1\">\r\n      <!-- 0.0 -->\r\n      <defs>\r\n       <path d=\"M 31.78125 66.40625 \r\nQ 24.171875 66.40625 20.328125 58.90625 \r\nQ 16.5 51.421875 16.5 36.375 \r\nQ 16.5 21.390625 20.328125 13.890625 \r\nQ 24.171875 6.390625 31.78125 6.390625 \r\nQ 39.453125 6.390625 43.28125 13.890625 \r\nQ 47.125 21.390625 47.125 36.375 \r\nQ 47.125 51.421875 43.28125 58.90625 \r\nQ 39.453125 66.40625 31.78125 66.40625 \r\nz\r\nM 31.78125 74.21875 \r\nQ 44.046875 74.21875 50.515625 64.515625 \r\nQ 56.984375 54.828125 56.984375 36.375 \r\nQ 56.984375 17.96875 50.515625 8.265625 \r\nQ 44.046875 -1.421875 31.78125 -1.421875 \r\nQ 19.53125 -1.421875 13.0625 8.265625 \r\nQ 6.59375 17.96875 6.59375 36.375 \r\nQ 6.59375 54.828125 13.0625 64.515625 \r\nQ 19.53125 74.21875 31.78125 74.21875 \r\nz\r\n\" id=\"DejaVuSans-48\"/>\r\n       <path d=\"M 10.6875 12.40625 \r\nL 21 12.40625 \r\nL 21 0 \r\nL 10.6875 0 \r\nz\r\n\" id=\"DejaVuSans-46\"/>\r\n      </defs>\r\n      <g transform=\"translate(52.111932 239.238437)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_2\">\r\n     <g id=\"line2d_2\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"120.936222\" xlink:href=\"#m313791c4b7\" y=\"224.64\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_2\">\r\n      <!-- 0.2 -->\r\n      <defs>\r\n       <path d=\"M 19.1875 8.296875 \r\nL 53.609375 8.296875 \r\nL 53.609375 0 \r\nL 7.328125 0 \r\nL 7.328125 8.296875 \r\nQ 12.9375 14.109375 22.625 23.890625 \r\nQ 32.328125 33.6875 34.8125 36.53125 \r\nQ 39.546875 41.84375 41.421875 45.53125 \r\nQ 43.3125 49.21875 43.3125 52.78125 \r\nQ 43.3125 58.59375 39.234375 62.25 \r\nQ 35.15625 65.921875 28.609375 65.921875 \r\nQ 23.96875 65.921875 18.8125 64.3125 \r\nQ 13.671875 62.703125 7.8125 59.421875 \r\nL 7.8125 69.390625 \r\nQ 13.765625 71.78125 18.9375 73 \r\nQ 24.125 74.21875 28.421875 74.21875 \r\nQ 39.75 74.21875 46.484375 68.546875 \r\nQ 53.21875 62.890625 53.21875 53.421875 \r\nQ 53.21875 48.921875 51.53125 44.890625 \r\nQ 49.859375 40.875 45.40625 35.40625 \r\nQ 44.1875 33.984375 37.640625 27.21875 \r\nQ 31.109375 20.453125 19.1875 8.296875 \r\nz\r\n\" id=\"DejaVuSans-50\"/>\r\n      </defs>\r\n      <g transform=\"translate(112.984659 239.238437)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-50\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_3\">\r\n     <g id=\"line2d_3\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"181.808949\" xlink:href=\"#m313791c4b7\" y=\"224.64\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_3\">\r\n      <!-- 0.4 -->\r\n      <defs>\r\n       <path d=\"M 37.796875 64.3125 \r\nL 12.890625 25.390625 \r\nL 37.796875 25.390625 \r\nz\r\nM 35.203125 72.90625 \r\nL 47.609375 72.90625 \r\nL 47.609375 25.390625 \r\nL 58.015625 25.390625 \r\nL 58.015625 17.1875 \r\nL 47.609375 17.1875 \r\nL 47.609375 0 \r\nL 37.796875 0 \r\nL 37.796875 17.1875 \r\nL 4.890625 17.1875 \r\nL 4.890625 26.703125 \r\nz\r\n\" id=\"DejaVuSans-52\"/>\r\n      </defs>\r\n      <g transform=\"translate(173.857386 239.238437)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-52\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_4\">\r\n     <g id=\"line2d_4\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"242.681676\" xlink:href=\"#m313791c4b7\" y=\"224.64\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_4\">\r\n      <!-- 0.6 -->\r\n      <defs>\r\n       <path d=\"M 33.015625 40.375 \r\nQ 26.375 40.375 22.484375 35.828125 \r\nQ 18.609375 31.296875 18.609375 23.390625 \r\nQ 18.609375 15.53125 22.484375 10.953125 \r\nQ 26.375 6.390625 33.015625 6.390625 \r\nQ 39.65625 6.390625 43.53125 10.953125 \r\nQ 47.40625 15.53125 47.40625 23.390625 \r\nQ 47.40625 31.296875 43.53125 35.828125 \r\nQ 39.65625 40.375 33.015625 40.375 \r\nz\r\nM 52.59375 71.296875 \r\nL 52.59375 62.3125 \r\nQ 48.875 64.0625 45.09375 64.984375 \r\nQ 41.3125 65.921875 37.59375 65.921875 \r\nQ 27.828125 65.921875 22.671875 59.328125 \r\nQ 17.53125 52.734375 16.796875 39.40625 \r\nQ 19.671875 43.65625 24.015625 45.921875 \r\nQ 28.375 48.1875 33.59375 48.1875 \r\nQ 44.578125 48.1875 50.953125 41.515625 \r\nQ 57.328125 34.859375 57.328125 23.390625 \r\nQ 57.328125 12.15625 50.6875 5.359375 \r\nQ 44.046875 -1.421875 33.015625 -1.421875 \r\nQ 20.359375 -1.421875 13.671875 8.265625 \r\nQ 6.984375 17.96875 6.984375 36.375 \r\nQ 6.984375 53.65625 15.1875 63.9375 \r\nQ 23.390625 74.21875 37.203125 74.21875 \r\nQ 40.921875 74.21875 44.703125 73.484375 \r\nQ 48.484375 72.75 52.59375 71.296875 \r\nz\r\n\" id=\"DejaVuSans-54\"/>\r\n      </defs>\r\n      <g transform=\"translate(234.730114 239.238437)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-54\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_5\">\r\n     <g id=\"line2d_5\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"303.554403\" xlink:href=\"#m313791c4b7\" y=\"224.64\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_5\">\r\n      <!-- 0.8 -->\r\n      <defs>\r\n       <path d=\"M 31.78125 34.625 \r\nQ 24.75 34.625 20.71875 30.859375 \r\nQ 16.703125 27.09375 16.703125 20.515625 \r\nQ 16.703125 13.921875 20.71875 10.15625 \r\nQ 24.75 6.390625 31.78125 6.390625 \r\nQ 38.8125 6.390625 42.859375 10.171875 \r\nQ 46.921875 13.96875 46.921875 20.515625 \r\nQ 46.921875 27.09375 42.890625 30.859375 \r\nQ 38.875 34.625 31.78125 34.625 \r\nz\r\nM 21.921875 38.8125 \r\nQ 15.578125 40.375 12.03125 44.71875 \r\nQ 8.5 49.078125 8.5 55.328125 \r\nQ 8.5 64.0625 14.71875 69.140625 \r\nQ 20.953125 74.21875 31.78125 74.21875 \r\nQ 42.671875 74.21875 48.875 69.140625 \r\nQ 55.078125 64.0625 55.078125 55.328125 \r\nQ 55.078125 49.078125 51.53125 44.71875 \r\nQ 48 40.375 41.703125 38.8125 \r\nQ 48.828125 37.15625 52.796875 32.3125 \r\nQ 56.78125 27.484375 56.78125 20.515625 \r\nQ 56.78125 9.90625 50.3125 4.234375 \r\nQ 43.84375 -1.421875 31.78125 -1.421875 \r\nQ 19.734375 -1.421875 13.25 4.234375 \r\nQ 6.78125 9.90625 6.78125 20.515625 \r\nQ 6.78125 27.484375 10.78125 32.3125 \r\nQ 14.796875 37.15625 21.921875 38.8125 \r\nz\r\nM 18.3125 54.390625 \r\nQ 18.3125 48.734375 21.84375 45.5625 \r\nQ 25.390625 42.390625 31.78125 42.390625 \r\nQ 38.140625 42.390625 41.71875 45.5625 \r\nQ 45.3125 48.734375 45.3125 54.390625 \r\nQ 45.3125 60.0625 41.71875 63.234375 \r\nQ 38.140625 66.40625 31.78125 66.40625 \r\nQ 25.390625 66.40625 21.84375 63.234375 \r\nQ 18.3125 60.0625 18.3125 54.390625 \r\nz\r\n\" id=\"DejaVuSans-56\"/>\r\n      </defs>\r\n      <g transform=\"translate(295.602841 239.238437)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-56\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_6\">\r\n     <g id=\"line2d_6\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"364.427131\" xlink:href=\"#m313791c4b7\" y=\"224.64\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_6\">\r\n      <!-- 1.0 -->\r\n      <defs>\r\n       <path d=\"M 12.40625 8.296875 \r\nL 28.515625 8.296875 \r\nL 28.515625 63.921875 \r\nL 10.984375 60.40625 \r\nL 10.984375 69.390625 \r\nL 28.421875 72.90625 \r\nL 38.28125 72.90625 \r\nL 38.28125 8.296875 \r\nL 54.390625 8.296875 \r\nL 54.390625 0 \r\nL 12.40625 0 \r\nz\r\n\" id=\"DejaVuSans-49\"/>\r\n      </defs>\r\n      <g transform=\"translate(356.475568 239.238437)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-49\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n   </g>\r\n   <g id=\"matplotlib.axis_2\">\r\n    <g id=\"ytick_1\">\r\n     <g id=\"line2d_7\">\r\n      <defs>\r\n       <path d=\"M 0 0 \r\nL -3.5 0 \r\n\" id=\"m8cafb8ef39\" style=\"stroke:#000000;stroke-width:0.8;\"/>\r\n      </defs>\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"44.845313\" xlink:href=\"#m8cafb8ef39\" y=\"194.989091\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_7\">\r\n      <!-- −0.04 -->\r\n      <defs>\r\n       <path d=\"M 10.59375 35.5 \r\nL 73.1875 35.5 \r\nL 73.1875 27.203125 \r\nL 10.59375 27.203125 \r\nz\r\n\" id=\"DejaVuSans-8722\"/>\r\n      </defs>\r\n      <g transform=\"translate(7.2 198.78831)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-8722\"/>\r\n       <use x=\"83.789062\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"147.412109\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"179.199219\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"242.822266\" xlink:href=\"#DejaVuSans-52\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_2\">\r\n     <g id=\"line2d_8\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"44.845313\" xlink:href=\"#m8cafb8ef39\" y=\"155.454545\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_8\">\r\n      <!-- −0.02 -->\r\n      <g transform=\"translate(7.2 159.253764)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-8722\"/>\r\n       <use x=\"83.789062\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"147.412109\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"179.199219\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"242.822266\" xlink:href=\"#DejaVuSans-50\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_3\">\r\n     <g id=\"line2d_9\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"44.845313\" xlink:href=\"#m8cafb8ef39\" y=\"115.92\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_9\">\r\n      <!-- 0.00 -->\r\n      <g transform=\"translate(15.579688 119.719219)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_4\">\r\n     <g id=\"line2d_10\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"44.845313\" xlink:href=\"#m8cafb8ef39\" y=\"76.385455\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_10\">\r\n      <!-- 0.02 -->\r\n      <g transform=\"translate(15.579688 80.184673)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_5\">\r\n     <g id=\"line2d_11\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"44.845313\" xlink:href=\"#m8cafb8ef39\" y=\"36.850909\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_11\">\r\n      <!-- 0.04 -->\r\n      <g transform=\"translate(15.579688 40.650128)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\r\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-52\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n   </g>\r\n   <g id=\"patch_4\">\r\n    <path d=\"M 44.845313 224.64 \r\nL 44.845313 7.2 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"patch_5\">\r\n    <path d=\"M 379.645313 224.64 \r\nL 379.645313 7.2 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"patch_6\">\r\n    <path d=\"M 44.845313 224.64 \r\nL 379.645313 224.64 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"patch_7\">\r\n    <path d=\"M 44.845313 7.2 \r\nL 379.645313 7.2 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n  </g>\r\n </g>\r\n <defs>\r\n  <clipPath id=\"pf8b8b1075b\">\r\n   <rect height=\"217.44\" width=\"334.8\" x=\"44.845313\" y=\"7.2\"/>\r\n  </clipPath>\r\n </defs>\r\n</svg>\r\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["def fut_func_atr_stats(frequency=BarSize.day, atr_bar_count=20):\n", "    '''\n", "    fut_codes = [#\"IF8888.SF\", \"IH8888.SF\", \"IC8888.SF\",\n", "                 \"M8888.DC\", \"Y8888.DC\", \"A8888.DC\", \"P8888.DC\", \"J8888.DC\",\n", "                 \"JM8888.DC\", \"I8888.DC\", \"C8888.DC\", \"CS8888.DC\", \"JD8888.DC\", \"L8888.DC\", \"V8888.DC\", \"PP8888.DC\",\n", "                 \"SR8888.ZC\", \"CF8888.ZC\", \"ZC8888.ZC\", \"FG8888.ZC\", \"TA8888.ZC\", \"MA8888.ZC\", \"OI8888.ZC\", \"RM8888.ZC\",\n", "                 \"CU8888.SC\", \"AL8888.SC\", \"ZN8888.SC\", \"PB8888.SC\", \"NI8888.SC\", \"SN8888.SC\", \"AU8888.SC\", \"AG8888.SC\",\n", "                 \"RB8888.SC\", \"HC8888.SC\", \"BU8888.SC\", \"RU8888.SC\"]\n", "    '''\n", "    fut_codes = ds.get_block_data(\"ZLLX\") #ZLQH\n", "    col_atr = []\n", "    col_atr_mean = []\n", "    col_atr_std = []\n", "    col_chg = []\n", "    col_prc = []\n", "    col_mny = []\n", "    exist_codes = []\n", "    for symbol in fut_codes:\n", "        real_hist = ds.get_history_data(symbol, 260, [BarData.high, BarData.low, BarData.close], frequency)\n", "        if real_hist is None:\n", "            print(\"{} history bars is None.\".format(symbol))\n", "            continue\n", "        exist_codes.append(symbol)\n", "        real_atr = ta.ATR(real_hist[0], real_hist[1], real_hist[2], atr_bar_count)\n", "        col_atr.append(real_atr[-1])\n", "        col_atr_mean.append(real_atr[atr_bar_count+1:].mean())\n", "        col_atr_std.append(np.std(real_hist[2][-1*atr_bar_count:]))\n", "        \n", "        col_prc.append(real_hist[2][-1])\n", "        # col_chg.append(col_atr_mean[-1]/col_prc[-1]*100)\n", "        col_chg.append(col_atr_std[-1]/col_prc[-1]*100)\n", "\n", "        # vol_mult, margin = dh.get_future_margin(code)\n", "        # itm = instruments(book_id)\n", "        # margin = itm.margin_rate\n", "        # vol_mult = itm.contract_multiplier\n", "        # if vol_mult is None:\n", "        #     col_mny.append(col_chg[-1] * col_prc[-1] / 100)\n", "        #     print(\"{} volume multi is none.\".format(book_id))\n", "        # else:\n", "        #     col_mny.append(col_chg[-1] * col_prc[-1] * float(vol_mult) / 100)\n", "\n", "    df = pd.DataFrame(\n", "        {\"ATR\": col_atr, \"ATR mean\": col_atr_mean, \"stddev\": col_atr_std, \"CHG\": col_chg, \"PRICE\": col_prc},\n", "        index=exist_codes\n", "    )\n", "    df = df.sort_values(by=\"PRICE\", ascending=False)\n", "    # print(df.describe())\n", "    # df.to_excel(r\".\\store\\fut_atr_rank_{0}_{1}_{2}.xlsx\".format(frequency, atr_bar_count, datetime.datetime.now().strftime(\"%Y%m%d\")), \"Sheet1\")\n", "    return df, real_atr\n", "\n", "\n", "def stk_func_atr_stats(frequency=BarSize.day, period=20):\n", "    '''\n", "    基于全市场ATR统计\n", "    '''\n", "    # futs = all_instruments('Future')\n", "    stks = ds.get_block_data(\"A股\".encode('gbk'))\n", "    # print(stks[:10])\n", "    col_atr = []\n", "    col_chg = []\n", "    col_prc = []\n", "    col_mny = []\n", "    exist_codes = []\n", "    for stk in stks:\n", "        real_hist = ds.get_history_data(stk, 120, [BarData.high, BarData.low, BarData.close], frequency)\n", "        if real_hist is None or real_hist.shape[0] == 0 or real_hist.shape[1] < 100:\n", "            # print(\"{} history bars is None.\".format(stk))\n", "            continue\n", "        exist_codes.append(stk)\n", "        real_atr = ta.ATR(real_hist[0], real_hist[1], real_hist[2], period)\n", "        real_ma = ta.SMA(real_hist[2], period)\n", "        real_chg = real_atr[-1] / real_ma[-1] * 100\n", "        col_atr.append(real_atr[-1])\n", "        col_chg.append(real_chg)\n", "        col_prc.append(real_hist[2][-1])\n", "\n", "    df = pd.DataFrame(\n", "        {\"ATR\": col_atr, \"CHG\": col_chg, \"PRICE\": col_prc},\n", "        index=exist_codes\n", "    )\n", "    df = df.sort_values(by=\"CHG\", ascending=False)\n", "    # df.to_excel(r\".\\store\\stk_atr_rank_{0}_{1}_{2}.xlsx\".format(frequency, atr_bar_count, datetime.datetime.now().strftime(\"%Y%m%d\")), \"Sheet1\")\n", "    return df\n", "\n", "def fut_output():\n", "    print(\"===================FUT====================\")\n", "    df, atr = fut_func_atr_stats(frequency=BarSize.min5, atr_bar_count=20)\n", "    print(df)\n", "    print(df.describe())\n", "    #plt.hist(atr, bins='auto')\n", "    plt.hist(df['CHG'], bins='auto')\n", "\n", "    print(\"===================FUT====================\")\n", "    df, atr = fut_func_atr_stats(frequency=BarSize.min5, atr_bar_count=200)\n", "    print(df)\n", "    print(df.describe())\n", "    #plt.hist(atr, bins='auto')\n", "    plt.hist(df['CHG'], bins='auto')\n", "\n", "def stk_output():\n", "    print(\"===================STK====================\")\n", "    df = stk_func_atr_stats(frequency=BarSize.day, period=20)\n", "    print(df.describe())\n", "    plt.hist(df['CHG'], bins='auto')\n", "\n", "stk_output()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4-final"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}