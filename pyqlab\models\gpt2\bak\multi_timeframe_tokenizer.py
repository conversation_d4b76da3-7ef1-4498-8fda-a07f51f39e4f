"""
Multi-Timeframe Candlestick Tokenizer

实现多时间框架K线数据的tokenization
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Any
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer

class MultiTimeframeTokenizer:
    """多时间框架K线tokenizer"""
    
    def __init__(self, 
                 base_tokenizer: CandlestickTokenizer = None,
                 timeframes: List[str] = ['1m', '5m', '15m', '1h', '1d'],
                 tokenizer_params: Dict[str, Any] = None):
        """
        初始化多时间框架tokenizer
        
        Args:
            base_tokenizer: 基础tokenizer，如果为None则创建一个新的
            timeframes: 时间框架列表，如['1m', '5m', '15m', '1h', '1d']
            tokenizer_params: 创建基础tokenizer的参数
        """
        self.timeframes = timeframes
        
        # 创建或使用基础tokenizer
        if base_tokenizer is not None:
            self.base_tokenizer = base_tokenizer
        else:
            if tokenizer_params is None:
                tokenizer_params = {}
            self.base_tokenizer = CandlestickTokenizer(**tokenizer_params)
            
        # 为每个时间框架创建一个tokenizer
        self.tf_tokenizers = {}
        for tf in timeframes:
            self.tf_tokenizers[tf] = self.base_tokenizer
            
        print(f"MultiTimeframeTokenizer initialized with timeframes: {', '.join(timeframes)}")
        
    def resample_data(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        将数据重采样到指定时间框架
        
        Args:
            df: 包含OHLCV数据的DataFrame，必须包含datetime列
            timeframe: 目标时间框架，如'1m', '5m', '15m', '1h', '1d'
            
        Returns:
            重采样后的DataFrame
        """
        # 确保datetime列存在
        if 'datetime' not in df.columns:
            raise ValueError("DataFrame必须包含datetime列")
            
        # 确保datetime列是datetime类型
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 解析时间框架
        if 'm' in timeframe:
            minutes = int(timeframe.replace('m', ''))
            rule = f'{minutes}min'
        elif 'h' in timeframe:
            hours = int(timeframe.replace('h', ''))
            rule = f'{hours}H'
        elif 'd' in timeframe:
            days = int(timeframe.replace('d', ''))
            rule = f'{days}D'
        else:
            raise ValueError(f"不支持的时间框架: {timeframe}")
            
        # 设置datetime为索引
        df = df.set_index('datetime')
        
        # 重采样
        agg_dict = {
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last'
        }
        
        # 如果有交易量列，也进行重采样
        if 'volume' in df.columns:
            agg_dict['volume'] = 'sum'
            
        resampled = df.resample(rule).agg(agg_dict).dropna()
        
        # 重置索引
        resampled = resampled.reset_index()
        
        return resampled
        
    def tokenize_multi_timeframe(self, df: pd.DataFrame, add_special_tokens: bool = True) -> Dict[str, List[int]]:
        """
        对多个时间框架的数据进行tokenize
        
        Args:
            df: 包含OHLCV数据的DataFrame，必须包含datetime列
            add_special_tokens: 是否添加特殊token
            
        Returns:
            时间框架到token列表的映射
        """
        tokens_dict = {}
        
        for tf in self.timeframes:
            # 重采样数据
            try:
                resampled_df = self.resample_data(df, tf)
                
                # 如果重采样后的数据太少，跳过
                if len(resampled_df) < 10:
                    print(f"警告: 时间框架 {tf} 重采样后只有 {len(resampled_df)} 条数据，跳过")
                    continue
                    
                # tokenize
                tokenizer = self.tf_tokenizers[tf]
                tokens = tokenizer.tokenize(resampled_df, add_special_tokens)
                tokens_dict[tf] = tokens
                
            except Exception as e:
                print(f"处理时间框架 {tf} 时出错: {str(e)}")
                
        return tokens_dict
        
    def combine_tokens(self, tokens_dict: Dict[str, List[int]], method: str = 'concat') -> List[int]:
        """
        组合不同时间框架的tokens
        
        Args:
            tokens_dict: 时间框架到token列表的映射
            method: 组合方法，'concat'或'interleave'
            
        Returns:
            组合后的token列表
        """
        if not tokens_dict:
            return []
            
        if method == 'concat':
            # 简单拼接，按时间框架从小到大排序
            sorted_tfs = sorted(tokens_dict.keys(), key=self._timeframe_to_minutes)
            combined = []
            for tf in sorted_tfs:
                combined.extend(tokens_dict[tf])
            return combined
            
        elif method == 'interleave':
            # 交错排列
            sorted_tfs = sorted(tokens_dict.keys(), key=self._timeframe_to_minutes)
            max_len = max(len(tokens_dict[tf]) for tf in sorted_tfs)
            combined = []
            
            for i in range(max_len):
                for tf in sorted_tfs:
                    if i < len(tokens_dict[tf]):
                        combined.append(tokens_dict[tf][i])
                        
            return combined
            
        else:
            raise ValueError(f"不支持的组合方法: {method}")
            
    def _timeframe_to_minutes(self, timeframe: str) -> int:
        """将时间框架转换为分钟数，用于排序"""
        if 'm' in timeframe:
            return int(timeframe.replace('m', ''))
        elif 'h' in timeframe:
            return int(timeframe.replace('h', '')) * 60
        elif 'd' in timeframe:
            return int(timeframe.replace('d', '')) * 60 * 24
        else:
            return 0
            
    def tokenize(self, df: pd.DataFrame, add_special_tokens: bool = True, 
                combine_method: str = 'concat') -> List[int]:
        """
        将K线数据转换为多时间框架token序列并组合
        
        Args:
            df: 包含OHLCV数据的DataFrame，必须包含datetime列
            add_special_tokens: 是否添加特殊token
            combine_method: 组合方法，'concat'或'interleave'
            
        Returns:
            组合后的token索引列表
        """
        # 对多个时间框架进行tokenize
        tokens_dict = self.tokenize_multi_timeframe(df, add_special_tokens)
        
        # 组合tokens
        combined_tokens = self.combine_tokens(tokens_dict, combine_method)
        
        return combined_tokens
        
    def visualize_multi_timeframe(self, df: pd.DataFrame, tokens_dict: Dict[str, List[int]] = None):
        """
        可视化多时间框架的K线数据和token化结果
        
        Args:
            df: 原始K线数据
            tokens_dict: 时间框架到token列表的映射
        """
        if tokens_dict is None:
            tokens_dict = self.tokenize_multi_timeframe(df)
            
        # 计算子图数量
        n_timeframes = len(tokens_dict)
        if n_timeframes == 0:
            print("没有可视化的时间框架")
            return
            
        # 创建图表
        fig, axes = plt.subplots(n_timeframes, 1, figsize=(12, 5 * n_timeframes), sharex=False)
        
        # 确保axes是数组
        if n_timeframes == 1:
            axes = [axes]
            
        # 按时间框架从小到大排序
        sorted_tfs = sorted(tokens_dict.keys(), key=self._timeframe_to_minutes)
        
        # 为每个时间框架绘制K线
        for i, tf in enumerate(sorted_tfs):
            # 重采样数据
            resampled_df = self.resample_data(df, tf)
            
            # 获取对应的tokens
            tokens = tokens_dict[tf]
            
            # 计算ATR
            tokenizer = self.tf_tokenizers[tf]
            atr = tokenizer._calculate_atr(resampled_df).iloc[-1]
            
            # 重建K线数据
            start_price = resampled_df['close'].iloc[0]
            start_volume = resampled_df['volume'].iloc[0] if 'volume' in resampled_df.columns else None
            reconstructed_df = tokenizer.tokens_to_candlesticks(tokens, start_price, atr, start_volume)
            
            # 绘制原始K线和重建的K线
            ax = axes[i]
            ax.set_title(f'时间框架: {tf}')
            
            # 绘制原始K线
            for j in range(len(resampled_df)):
                # 计算位置和颜色
                x = j
                open_price = resampled_df['open'].iloc[j]
                close_price = resampled_df['close'].iloc[j]
                high_price = resampled_df['high'].iloc[j]
                low_price = resampled_df['low'].iloc[j]
                color = 'red' if close_price >= open_price else 'green'
                
                # 绘制实体
                ax.plot([x, x], [open_price, close_price], color=color, linewidth=4, alpha=0.7)
                # 绘制影线
                ax.plot([x, x], [low_price, high_price], color=color, linewidth=1, alpha=0.7)
                
            # 绘制重建的K线
            for j in range(len(reconstructed_df)):
                # 计算位置和颜色
                x = j + 0.3  # 稍微偏移，以便与原始K线区分
                open_price = reconstructed_df['open'].iloc[j]
                close_price = reconstructed_df['close'].iloc[j]
                high_price = reconstructed_df['high'].iloc[j]
                low_price = reconstructed_df['low'].iloc[j]
                color = 'blue' if close_price >= open_price else 'purple'
                
                # 绘制实体
                ax.plot([x, x], [open_price, close_price], color=color, linewidth=2, alpha=0.9)
                # 绘制影线
                ax.plot([x, x], [low_price, high_price], color=color, linewidth=1, alpha=0.9)
                
            # 添加图例
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], color='red', lw=4, label='原始上涨K线'),
                Line2D([0], [0], color='green', lw=4, label='原始下跌K线'),
                Line2D([0], [0], color='blue', lw=2, label='重建上涨K线'),
                Line2D([0], [0], color='purple', lw=2, label='重建下跌K线')
            ]
            ax.legend(handles=legend_elements, loc='upper right')
            
            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)
            
        plt.tight_layout()
        plt.show()
        
        return fig, axes
