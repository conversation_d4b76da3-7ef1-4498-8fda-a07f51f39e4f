@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\data

@REM 使用说明
@REM 安装依赖
@REM 首先需要安装必要的依赖库：
@REM pip install pytdx pandas numpy pyarrow schedule

@REM 基本用法
@REM 每日数据更新：
@REM python tdx_dc.py --task daily --db-path f:/hqdata/tsdb

@REM 每周数据更新：
@REM python tdx_dc.py --task weekly --db-path f:/hqdata/tsdb

@REM 下载完整历史数据：
@REM python tdx_dc.py --task full --db-path f:/hqdata/tsdb

@REM 获取市场代码表：
@REM python tdx_dc.py --task market_codes --db-path f:/hqdata/tsdb

@REM 获取股票列表：
@REM python tdx_dc.py --task stock --db-path f:/hqdata/tsdb

@REM 获取合约信息：
@REM python tdx_dc.py --task instruments --db-path f:/hqdata/tsdb
@REM python tdx_dc.py --task instruments --market-id 28 --db-path f:/hqdata/tsdb 
@REM 获取特定市场(如郑州商品期货)的合约信息

@REM 下载指定股票的数据：
@REM python tdx_dc.py --task stock --stocks 000001.SZ,600000.SH --freq day --days 365

@REM 下载指数数据：
@REM python tdx_dc.py --task index --freq day --days 365 --db-path f:/hqdata/tsdb

@REM 下载期货数据：
@REM python tdx_dc.py --task future --freq day --days 365 --db-path f:/hqdata/tsdb

@REM 定时任务
@REM 可以使用提供的调度脚本自动执行定时任务：
@REM python run_tdx_collector.py

@REM 这将在每个交易日（周一至周五）收盘后自动执行每日数据更新，并在每周五晚上执行每周数据更新。

@REM 数据存储结构
@REM 数据将按照TimeSeriesDB的存储结构进行组织，主要包括：

@REM 基础数据：
python tdx_dc.py --task basic --stocks 000001.SZ --db-path f:/hqdata/tsdb

@REM python tdx_dc.py --task block --db-path f:/hqdata/tsdb
