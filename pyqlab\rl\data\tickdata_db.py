import datetime
import pytz
import struct
import pandas as pd
import sys
import numpy as np

sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

# labels=['I8888.DC', 'M8888.DC', 'MA8888.ZC', 'RB8888.SC', 'SA8888.ZC','TA8888.ZC']

class TickDataDB():
    def __init__(self,
        dbfile="d:/QuantLab/store/tickdata.db",
        save_path="../data"
    ) -> None:
        self._dbfile=dbfile
        self._save_path = save_path
        self._db=None
        self._keys=[]
        self._tz=pytz.timezone('Asia/Shanghai')
        pass

    def open_db(self, mode):
        if self._db:
            self.close_db()
        
        try:
            self._db=create_db("leveldb", self._dbfile, mode)
        except:
            raise 'Fail to open db!'

    def close_db(self):
        if not self._db:
            raise "not db open."
        self._db.close()
        del self._db
        self._db=None

    def load_all_keys(self):
        if not self._db:
            raise "first open a db."
        self._keys.clear()
        cursor = self._db.new_cursor()
        while cursor.valid():
            self._keys.append(str(cursor.key()))
            cursor.next()
        del cursor

    def get_all_labels(self)->set:
        lbs = set()
        if len(self._keys) == 0:
            self.load_all_keys()
            
        for key in self._keys:
            s=str(key)
            pos0=s.find(':')
            pos1=s.find(':', pos0+1)
            lb=s[pos0+1:pos1]
            lbs.add(lb)
        return lbs

    def read_keys(self, key):
        if not self._db:
            raise "first open a db."

        cursor = self._db.new_cursor()
        while cursor.valid():
            if str(cursor.key()) == key:
                for i in range(len(cursor.value())//48):
                    tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                    print(datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)
                break
            cursor.next()
        del cursor

    def read_index(self, index):
        if not self._db:
            raise "first open a db."
        if index > len(self._keys):
            raise f"index < {len(self._keys)}"

        cursor = self._db.new_cursor()
        while cursor.valid():
            if cursor.key() == self._keys[index]:
                s=str(cursor.key())
                pos0=s.find(':')
                pos1=s.find(':', pos0+1)
                lb=s[pos0+1:pos1]
                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                data=[lb, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2]
                break
            cursor.next()
        del cursor
        return data

    def write(self, key, value):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.put(key, value)
        transaction.commit()
        del transaction
        
    def delete(self, key):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.delete(key)
        transaction.commit()
        del transaction

    def delete_label(self, label):
        if len(self._keys) == 0:
            self.load_all_keys()
        transaction = self._db.new_transaction()
        for key in self._keys:
            s=str(key)
            pos0=s.find(':')
            pos1=s.find(':', pos0+1)
            lb=s[pos0+1:pos1]
            if lb == label:
                transaction.delete(key)
                print(f"del key: {key}")
        transaction.commit()
        self.load_all_keys()

    def query(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        while cursor.valid():
            print(cursor.key())
            # print(cursor.key(), cursor.value())
            cursor.next()
        del cursor

    def read_label(self, label):
        if not self._db:
            raise "first open a db."

        cursor = self._db.new_cursor()
        data=[]
        while cursor.valid():
            for i in range(len(cursor.value())//48):
                s=str(cursor.key())
                pos0=s.find(':')
                pos1=s.find(':', pos0+1)
                lb=s[pos0+1:pos1]
                if lb == label:
                    tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                    # time_id=tt//300
                    data.append([lb, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])
            cursor.next()
        del cursor
        return pd.DataFrame(data, columns=['label','datetime','price','size','bid_price1','bid_price2','bid_size1','bid_size2','ask_price1','ask_price2','ask_size1','ask_size2'])

    def export_label(self, label):
        def trans_timestamp(dt):
            # return int(time.mktime(dt.timetuple()))//300
            return int(dt//300)

        def log_return(series):
            return np.log(series).diff()
            
        if not self._db:
            raise "first open a db."

        # self.open_db(Mode.read)
        df=self.read_label(label)
        # self.close_db()
        print(df.shape)

        try:
            cols=df.columns
            df = df.groupby(["label", "datetime"]).agg({"mean"}).reset_index()
            df.columns = cols
            df["time_id"]=df["datetime"].apply(trans_timestamp)

            df.to_parquet(f"{self._save_path}/tickdata.{label}.parquet", engine='fastparquet')
            df = df.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()
            df['return'] = log_return(df['price'])
            df=df.fillna(0)
            df['target'] = (df['return']>0).astype(int)
            df=df.drop(['price', 'return'], axis=1)
            df.to_parquet(f"{self._save_path}/tickdata_target.{label}.parquet", engine='fastparquet')
        except:
            raise 'Fail to export label data!'

    def read_all(self):
        if not self._db:
            raise "first open a db."

        cursor = self._db.new_cursor()
        data=[]
        while cursor.valid():
            for i in range(len(cursor.value())//48):
                s=str(cursor.key())
                pos0=s.find(':')
                pos1=s.find(':', pos0+1)
                lb=s[pos0+1:pos1]
                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", cursor.value()[i*48:(i+1)*48])
                # time_id=tt//300
                data.append([lb, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])
            cursor.next()
        del cursor
        return pd.DataFrame(data, columns=['label','datetime','price','size','bid_price1','bid_price2','bid_size1','bid_size2','ask_price1','ask_price2','ask_size1','ask_size2'])

    def export_all(self):
        def trans_timestamp(dt):
            # return int(time.mktime(dt.timetuple()))//300
            return int(dt//300)

        def log_return(series):
            return np.log(series).diff()
            
        if not self._db:
            raise "first open a db."
        # self.open_db(Mode.read)
        df=self.read_all()
        # self.close_db()
        print(df.shape)

        try:
            cols=df.columns
            df = df.groupby(["label", "datetime"]).agg({"mean"}).reset_index()
            df.columns = cols
            df["time_id"]=df["datetime"].apply(trans_timestamp)

            for lb in df["label"].unique():
                # df.to_parquet(f"../data/tickdata.parquet", engine='fastparquet')
                df2=df[df["label"]==lb]
                df2.to_parquet(f"{self._save_path}/tickdata.{lb}.parquet", engine='fastparquet')
                # df = pd.read_parquet(f"../data/tickdata.parquet")
                df2 = df2.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()
                df2['return'] = log_return(df2['price'])
                df2=df2.fillna(0)
                df2['target'] = (df2['return']>0).astype(int)
                df2=df2.drop(['price', 'return'], axis=1)
                df2.to_parquet(f"{self._save_path}/tickdata_target.{lb}.parquet", engine='fastparquet')
        except:
            raise 'Fail to export all data!'


if __name__ == '__main__':
    tddb = TickDataDB(
        dbfile="d:/QuantLab/store/tickdata.db",
        save_path="E:/lab/RoboQuant/pylab/data/tickdata"
    )
    tddb.open_db(Mode.read)
    # tddb.export_all()
    tddb.export_label("HC8888.SC")
    tddb.close_db()

