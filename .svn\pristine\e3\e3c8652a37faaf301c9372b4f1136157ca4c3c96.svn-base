"""
历史数据准备
"""
import talib
import pandas as pd
from datetime import datetime
from pyqlab.const import MAIN_FUT_CODES, SF_FUT_CODES
from argparse import ArgumentParser
import json
import sys
sys.path.append("d:/QuantLab")
from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode
from pyqlab.data.dataset.pipeline import Pipeline

class LocalHistoryDataToBar:
    """
    用于通过本地第三方(通达信)历史行情数据生成训练数据
    主要支持两个周期数据：1. 日数据   2. 5分钟数据
    支持两个市场：1. A股   2. 期货
    数据源来自通达信pytdx导出，保存数据格式为parquet
    将历史行情数据转换为Bar训练数据
    """
    def __init__(self, data_path: str, market: str, period: str, block_name: str):
        self.data_path = data_path # d:/RoboQuant2/data
        self.market = market
        self.period = period
        self.block_name = block_name
        self.symbols = dict()
        self.atr = dict()
        # 注意：要与系统中配置参数一致
        if self.period == 'day':
            self.window = 20
        else:
            self.window = 100
        self.scale = 10
        self.atr_mult = 0.88
        self._read_config()
        # RunMode.passive时，股票和期货的数据源为本地通达信数据
        self.ds = DataSource(RunMode.passive)
        self._update_symbols()
        assert self.symbols, f'symbols is empty'
        print(f'market: {self.market}, period: {self.period}, symbols: {len(self.symbols)}')
        print(f'window: {self.window}, atr_mult: {self.atr_mult}, scale: {self.scale}')

    def _update_symbols(self):
        """
        更新symbols
        """
        if self.market == 'fut':
            zllx = self.ds.get_block_data("ZLLX")
            self.symbols['sf'] = set()
            self.symbols['main'] = set()
            for code in zllx:
                if code[-2:] == 'SF':
                    self.symbols['sf'].add(code)
                else:
                    self.symbols['main'].add(code)
        elif self.market == 'stk':
            hs300 = self.ds.get_block_data("沪深300")
            self.symbols['hs300'] = set(hs300)
            zz500 = self.ds.get_block_data("中证500")
            self.symbols['zz500'] = set(zz500)
            zz1000 = self.ds.get_block_data("中证1000")
            self.symbols['zz1000'] = set(zz1000)
            # self.symbols = list(self.symbols)[:5]
            # self.symbols = set(self.symbols)
        else:
            raise ValueError(f'market {self.market} not supported')
        
    def _read_config(self):
        """
        读取配置文件
        """
        with open(f'{self.data_path}/alphaquant.json', 'r', encoding='gbk') as f:
            config = json.load(f)
            if self.market == 'fut':
                if self.period == 'day':
                    self.window = config['techindex']['fut_drange_atr_period']
                    self.atr_mult = config['techindex']['fut_drange_atr_mult']
                else:
                    self.window = config['techindex']['fut_mrange_atr_period']
                    self.atr_mult = config['techindex']['fut_mrange_atr_mult']
            elif self.market == 'stk':
                if self.period == 'day':
                    self.window = config['techindex']['stk_drange_atr_period']
                    self.atr_mult = config['techindex']['stk_drange_atr_mult']
                else:
                    self.window = config['techindex']['stk_mrange_atr_period']
                    self.atr_mult = config['techindex']['stk_mrange_atr_mult']


    def _calc_atr(self, df):
        """
        通过历史行情数据更新atr表
        """
        # 技术指标ATR
        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=self.window)
        return atr * self.atr_mult

    def get_hist_data(self, symbol: str):
        """
        加载指定symbol的历史行情数据
        """
        if self.period == 'day':
            df = self.ds.get_history_data(symbol, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close], BarSize.day, DoRight.none)
        else:
            df = self.ds.get_history_data(symbol, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close], BarSize.min5, DoRight.none)
        if len(df) == 0:
            return pd.DataFrame()
        return pd.DataFrame(df, columns=['datetime', 'open', 'high', 'low', 'close'])
    
    def save_data(self, block_name, bar_data):
        df = pd.DataFrame(bar_data, columns=['symbol', 'timestamp', 'change', 'entity', 'upline', 'downline'])

        if self.period == 'day':
            path = f'{self.data_path}/store/barenc/bar_{self.market}_{block_name}_{self.period}.csv'
            self.process_and_save_data(df, path)
        else:
            # 删除每日时间在00:00:00到06:00:00之间的数据
            print(df.shape)
            df = df[(pd.to_datetime(df['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 6]
            print(df.shape)
            years = self._get_years(df['timestamp'])
            print(f'years: {years}')
            for year in years:
                df_year = df[pd.to_datetime(df['timestamp'], unit='s').dt.year == year]
                path = f'{self.data_path}/store/barenc/bar_{self.market}_{block_name}_{self.period}_{year}.csv'
                self.process_and_save_data(df_year, path)

    def process_and_save_data(self, df, path):
        # Process and save data in batches of 10,000 records
        batch_size = 20000
        for i in range(0, len(df), batch_size):
            batch = df[i:i+batch_size]
            batch = Pipeline.to_bar(batch)
            
            if i == 0:
                # For the first batch, create a new file
                batch.to_csv(path, index=False, mode='w')
            else:
                # For subsequent batches, append to the existing file
                batch.to_csv(path, index=False, mode='a', header=False)
            
            print(f'Processed and saved batch {i//batch_size + 1}, records {i} to {min(i+batch_size, len(df))}')
        
        print(f'Save {path} success, total shape: {df.shape}')


    def _get_years(self, date: pd.Series):
        # 将timestamp转换为datetime格式
        date = pd.to_datetime(date, unit='s')
        return sorted(date.dt.year.unique())

    def encode_to_bar(self):
        """
        将历史行情数据转换为Bar训练数据
        """
        for block_name, symbols in self.symbols.items(): 
            if self.block_name != 'all' and block_name != self.block_name:
                continue
            bar_data = []
            cnt = 0
            print(f'block_name: {block_name}, symbols: {len(symbols)}')
            for symbol in symbols:
                cnt += 1
                df = self.get_hist_data(symbol)
                if df.empty:
                    print(f'{symbol} 数据为空')
                    continue
                open = df['open']
                high = df['high']
                low = df['low']
                close = df['close']
                date = df['datetime']
                if self.market == 'fut':
                    code = symbol[:len(symbol)-7]
                else:
                    code = symbol
                cur_date = pd.to_datetime(date.iloc[self.window + 1], unit='s').date()
                atrs = self._calc_atr(df) / self.scale
                assert len(atrs) == len(df), f'atrs length not equal df'
                atr = atrs[self.window + 1]
                for i in range(self.window + 1, len(df)):
                    barenc = []
                    # 股票有除权日期货有主力期货切换，行情数据不连续
                    # 切换日期时，前后两日有较大的跳空，需要跳过，否则，数据会失真
                    # TODO: 对于股票，有的涨跌幅为[-20, 20]，有的涨跌幅为[-10, 10]
                    chg = (close.iloc[i] - close.iloc[i-1])/close.iloc[i-1] * 100
                    if (self.market == 'stk' and abs(chg) > 10) or (self.market == 'fut' and abs(chg) > 7):
                        # print(f'{pd.to_datetime(date.iloc[i], unit="s").date()} {symbol} 跳空 {chg:.2f}%')
                        # 跳空数据用一个现实中不会出现的-12，12，7，7标记
                        barenc.append(code)
                        barenc.append(int(date.iloc[i]))
                        barenc.append(0)
                        barenc.append(0)
                        barenc.append(0)
                        barenc.append(0)
                        bar_data.append(barenc)
                        i += min(15, self.window + 1)
                        if i < len(df):
                            atr = atrs[i]
                        continue
                    if pd.to_datetime(date.iloc[i], unit='s').date() != cur_date:
                        cur_date = pd.to_datetime(date.iloc[i], unit='s').date()
                        atr = atrs[i]
                        # print(f'{symbol} {pd.to_datetime(date.iloc[i], unit="s").date()} atr is {atr}')
                    if atr == 0:
                        print(f'{symbol} {pd.to_datetime(date.iloc[i], unit="s").date()} atr is 0')
                        continue
                    barenc.append(code)
                    # 日期
                    barenc.append(int(date.iloc[i]))
                    # 涨跌
                    barenc.append(int((close.iloc[i] - close.iloc[i-1])/atr))
                    # 实体
                    barenc.append(int((close.iloc[i] - open.iloc[i])/atr))

                    if close.iloc[i] > open.iloc[i]: # 阳线
                        # 上影线
                        barenc.append(int((high.iloc[i] - close.iloc[i])/atr))
                        # 下影线
                        barenc.append(int((open.iloc[i] - low.iloc[i])/atr))
                    else: # 阴线
                        # 上影线
                        barenc.append(int((high.iloc[i] - open.iloc[i])/atr))
                        # 下影线
                        barenc.append(int((close.iloc[i] - low.iloc[i])/atr))

                    bar_data.append(barenc)
                print(f'{cnt}/{len(symbols)} {symbol} {len(df)}')
            self.save_data(block_name, bar_data)


if __name__ == '__main__':
    parser = ArgumentParser()
    parser.add_argument('--market', type=str, default='fut', choices=['fut', 'stk'], help='market')
    parser.add_argument('--period', type=str, default='day', choices=['day', 'min5'], help='period')
    parser.add_argument('--block_name', type=str, default='all', choices=['main', 'sf', 'hs300', 'zz500', 'zz1000'])
    args = parser.parse_args()
    hd = LocalHistoryDataToBar('d:/RoboQuant2', args.market, args.period, args.block_name)
    hd.encode_to_bar()

