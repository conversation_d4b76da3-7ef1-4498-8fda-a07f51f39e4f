"""
专门解决预测多样性问题的脚本
基于分析结果，实施激进的多样性修复策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from candlestick_vq_gpt import CandlestickVQGPT

class DiversityEnhancedCandlestickVQGPT(CandlestickVQGPT):
    """
    增强多样性的CandlestickVQGPT模型
    专门解决预测单一化问题
    """
    
    def __init__(self, *args, **kwargs):
        # 添加多样性相关参数
        self.diversity_weight = kwargs.pop('diversity_weight', 1.0)
        self.anti_collapse_weight = kwargs.pop('anti_collapse_weight', 2.0)
        self.temperature_annealing = kwargs.pop('temperature_annealing', True)
        
        super().__init__(*args, **kwargs)
        
        # 添加多样性监控
        self.register_buffer('token_usage_count', torch.zeros(self.vocab_size))
        self.register_buffer('prediction_history', torch.zeros(1000))  # 记录最近1000个预测
        self.history_idx = 0
        
    def forward(self, input_tokens, code_ids, time_features=None, targets=None, **kwargs):
        """
        增强的前向传播，包含强制多样性机制
        """
        batch_size, seq_len = input_tokens.size()
        
        # 获取token嵌入
        token_emb = self.token_embedding(input_tokens)
        
        # 获取证券代码嵌入并扩展到序列长度
        code_emb = self.code_embedding(code_ids).unsqueeze(1).expand(-1, seq_len, -1)
        
        # 组合嵌入 - 使用更小的系数
        x = token_emb + 0.05 * code_emb  # 进一步减小系数
        
        # 添加时间特征嵌入
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + 0.05 * time_emb
        
        # 添加位置噪声增强多样性
        if self.training:
            noise = torch.randn_like(x) * 0.01
            x = x + noise
        
        x = self.dropout(x)
        
        # 获取旋转位置编码
        cos, sin = self.rotary_emb(x, seq_len=seq_len)
        rotary_emb = (cos, sin)
        
        # 通过Transformer块
        for block in self.blocks:
            x = block(x, rotary_emb)
        
        # 最终层归一化和输出投影
        x = self.ln_f(x)
        
        if targets is not None:
            logits = self.head(x)
            
            # 检查logits是否包含NaN或无穷值
            if torch.isnan(logits).any() or torch.isinf(logits).any():
                print("警告: logits包含NaN或无穷值")
                return logits, torch.tensor(float('inf'), device=logits.device)
            
            # 应用反坍缩机制
            logits = self._apply_anti_collapse(logits)
            
            # 基础交叉熵损失
            loss = F.cross_entropy(
                logits.view(-1, logits.size(-1)),
                targets.view(-1),
                ignore_index=-1,
                label_smoothing=0.0  # 完全移除标签平滑
            )
            
            # 强制多样性损失
            if self.use_auxiliary_loss:
                diversity_loss = self._compute_diversity_loss(logits, targets)
                loss = loss + diversity_loss * self.diversity_weight
            
            return logits, loss
        else:
            logits = self.head(x[:, [-1], :])
            # 推理时也应用反坍缩
            logits = self._apply_anti_collapse(logits)
            return logits, None
    
    def _apply_anti_collapse(self, logits):
        """
        应用反坍缩机制，防止模型总是预测相同的token
        """
        if not self.training:
            # 推理时使用token使用历史来调整logits
            usage_penalty = torch.log(self.token_usage_count + 1.0) * 0.1
            logits = logits - usage_penalty.unsqueeze(0).unsqueeze(0)
        
        return logits
    
    def _compute_diversity_loss(self, logits, targets):
        """
        计算多样性损失，强制模型产生多样化的预测
        """
        logits_flat = logits.view(-1, logits.size(-1))
        valid_mask = targets.view(-1) != -1
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device)
        
        valid_logits = logits_flat[valid_mask]
        pred_tokens = valid_logits.argmax(dim=-1)
        
        total_loss = 0.0
        
        # 1. 极端反坍缩损失 - 严厉惩罚单一预测
        unique_preds = torch.unique(pred_tokens).numel()
        total_preds = pred_tokens.numel()
        if total_preds > 1:
            diversity_ratio = unique_preds / total_preds
            # 如果多样性低于50%，施加重罚
            if diversity_ratio < 0.5:
                collapse_penalty = (0.5 - diversity_ratio) * 10.0
                total_loss += collapse_penalty
        
        # 2. 最频繁token惩罚
        if total_preds > 5:
            pred_counts = torch.bincount(pred_tokens, minlength=self.vocab_size)
            max_count = pred_counts.max().float()
            max_ratio = max_count / total_preds
            # 如果某个token占比超过30%，重罚
            if max_ratio > 0.3:
                frequent_penalty = (max_ratio - 0.3) * 5.0
                total_loss += frequent_penalty
        
        # 3. 熵损失 - 强制高熵
        probs = F.softmax(valid_logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1).mean()
        target_entropy = torch.log(torch.tensor(self.vocab_size / 2.0))  # 目标更高的熵
        entropy_loss = F.relu(target_entropy - entropy) * 2.0  # 只在熵过低时惩罚
        total_loss += entropy_loss
        
        # 4. 均匀分布损失 - 鼓励预测分布接近均匀
        pred_dist = torch.bincount(pred_tokens, minlength=self.vocab_size).float()
        pred_dist = pred_dist / pred_dist.sum()
        uniform_dist = torch.ones_like(pred_dist) / self.vocab_size
        kl_loss = F.kl_div(torch.log(pred_dist + 1e-8), uniform_dist, reduction='sum') * 0.1
        total_loss += kl_loss
        
        return total_loss
    
    def update_token_usage(self, pred_tokens):
        """
        更新token使用统计
        """
        if isinstance(pred_tokens, torch.Tensor):
            pred_tokens = pred_tokens.cpu().numpy()
        
        for token in pred_tokens:
            if 0 <= token < self.vocab_size:
                self.token_usage_count[token] += 1
                
                # 更新预测历史
                self.prediction_history[self.history_idx] = token
                self.history_idx = (self.history_idx + 1) % 1000
    
    def get_diversity_stats(self):
        """
        获取多样性统计信息
        """
        recent_preds = self.prediction_history[self.prediction_history != 0]
        if len(recent_preds) == 0:
            return {}
        
        unique_count = len(torch.unique(recent_preds))
        total_count = len(recent_preds)
        
        return {
            'diversity_ratio': unique_count / total_count,
            'unique_tokens': unique_count,
            'total_predictions': total_count,
            'most_frequent_token': int(torch.mode(recent_preds)[0]),
            'most_frequent_ratio': (recent_preds == torch.mode(recent_preds)[0]).sum().item() / total_count
        }

def fix_existing_model(model_path, save_path):
    """
    修复现有模型的多样性问题
    """
    print(f"加载现有模型: {model_path}")
    
    # 加载原始模型
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    # 创建增强版模型
    enhanced_model = DiversityEnhancedCandlestickVQGPT(
        vocab_size=config.get('vocab_size', 518),
        code_size=config.get('code_size', 100),
        seq_len=config.get('seq_len', 30),
        n_layer=config.get('n_layer', 4),
        n_head=config.get('n_head', 8),
        d_model=config.get('d_model', 64),
        dropout=config.get('dropout', 0.1),
        use_time_features=config.get('use_time_features', True),
        n_time_features=config.get('n_time_features', 8),
        label_smoothing=0.0,  # 移除标签平滑
        use_auxiliary_loss=True,
        diversity_weight=2.0,  # 强多样性权重
        anti_collapse_weight=3.0
    )
    
    # 加载权重
    enhanced_model.load_state_dict(checkpoint['model_state_dict'], strict=False)
    
    # 重新初始化输出层以打破偏向
    print("重新初始化输出层...")
    with torch.no_grad():
        # 使用正交初始化
        nn.init.orthogonal_(enhanced_model.head.weight)
        enhanced_model.head.weight.data *= 0.01  # 缩小初始权重
        
        if hasattr(enhanced_model.head, 'bias') and enhanced_model.head.bias is not None:
            nn.init.zeros_(enhanced_model.head.bias)
    
    # 保存增强模型
    enhanced_checkpoint = {
        'model_state_dict': enhanced_model.state_dict(),
        'config': {
            **config,
            'label_smoothing': 0.0,
            'diversity_weight': 2.0,
            'anti_collapse_weight': 3.0
        }
    }
    
    torch.save(enhanced_checkpoint, save_path)
    print(f"增强模型已保存到: {save_path}")
    
    return enhanced_model

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='修复CandlestickVQGPT多样性问题')
    parser.add_argument('--model_path', type=str, required=True, help='原始模型路径')
    parser.add_argument('--save_path', type=str, required=True, help='修复后模型保存路径')
    
    args = parser.parse_args()
    
    # 修复模型
    enhanced_model = fix_existing_model(args.model_path, args.save_path)
    
    print("模型多样性修复完成！")
    print("建议使用更低的学习率重新训练几个epoch以稳定改进效果。")

if __name__ == "__main__":
    main()
