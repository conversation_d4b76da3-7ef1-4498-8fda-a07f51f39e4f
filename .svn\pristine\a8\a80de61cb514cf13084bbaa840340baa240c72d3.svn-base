{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["usage: ipykernel_launcher.py [-h] [--stop-iters STOP_ITERS]\n", "                             [--num-cpus NUM_CPUS]\n", "ipykernel_launcher.py: error: unrecognized arguments: --ip=127.0.0.1 --stdin=9013 --control=9011 --hb=9010 --Session.signature_scheme=\"hmac-sha256\" --Session.key=b\"024655f2-f1f3-480c-b6a2-36ef561a47ac\" --shell=9012 --transport=\"tcp\" --iopub=9014 --f=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp-19409LamnXgIc6T0.json\n"]}, {"ename": "SystemExit", "evalue": "2", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[1;31mSystemExit\u001b[0m\u001b[1;31m:\u001b[0m 2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Miniconda3\\lib\\site-packages\\IPython\\core\\interactiveshell.py:3369: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": ["import argparse\n", "import os\n", "\n", "import ray\n", "from ray import tune\n", "from ray.rllib.agents.trainer import Trainer\n", "from ray.rllib.policy.policy_template import build_policy_class\n", "from ray.rllib.policy.sample_batch import SampleBatch\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\"--stop-iters\", type=int, default=200)\n", "parser.add_argument(\"--num-cpus\", type=int, default=0)\n", "\n", "\n", "def policy_gradient_loss(policy, model, dist_class, train_batch):\n", "    logits, _ = model({SampleBatch.CUR_OBS: train_batch[SampleBatch.CUR_OBS]})\n", "    action_dist = dist_class(logits, model)\n", "    log_probs = action_dist.logp(train_batch[SampleBatch.ACTIONS])\n", "    return -train_batch[SampleBatch.REWARDS].dot(log_probs)\n", "\n", "\n", "# <class 'ray.rllib.policy.torch_policy_template.MyTorchPolicy'>\n", "MyTorchPolicy = build_policy_class(\n", "    name=\"MyTorchPolicy\", framework=\"torch\", loss_fn=policy_gradient_loss\n", ")\n", "\n", "\n", "# Create a new Trainer using the Policy defined above.\n", "class MyTrainer(Trainer):\n", "    def get_default_policy_class(self, config):\n", "        return MyTorchPolicy\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    args = parser.parse_args()\n", "    ray.init(num_cpus=args.num_cpus or None)\n", "    tune.run(\n", "        <PERSON><PERSON><PERSON><PERSON>,\n", "        stop={\"training_iteration\": args.stop_iters},\n", "        config={\n", "            \"env\": \"CartPole-v0\",\n", "            # Use GPUs iff `RLLIB_NUM_GPUS` env var set to > 0.\n", "            \"num_gpus\": int(os.environ.get(\"RLLIB_NUM_GPUS\", \"0\")),\n", "            \"num_workers\": 2,\n", "            \"framework\": \"torch\",\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.12.0\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}