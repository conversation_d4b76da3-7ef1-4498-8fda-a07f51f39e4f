import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import numpy as np

# --- 1. K线数据向量化 ---
# 假设你有一个函数可以将原始OHLCV数据转换为固定长度的向量
# 这个向量应该是VQ-VAE训练时使用的相同格式
def candlestick_to_vector(ohlcv_row, prev_close, ma_volume, atr_val, vector_dim=5):
    """
    将单根K线转换为数值向量。
    这里是一个示例实现，你需要根据你的VQ-VAE训练输入进行调整。
    例如：[ (O-PrevC)/ATR, (H-O)/ATR, (L-O)/ATR, (C-O)/ATR, Volume/MA_Volume ]
    或：  [ (O-PrevC)/PrevC, (H-PrevC)/PrevC, (L-PrevC)/PrevC, (C-PrevC)/PrevC, Volume/MA_Volume ]
    确保所有值都进行了合理的归一化。
    """
    o, h, l, c, v = ohlcv_row['Open'], ohlcv_row['High'], ohlcv_row['Low'], ohlcv_row['Close'], ohlcv_row['Volume']

    if prev_close is None or ma_volume is None or atr_val is None or atr_val == 0:
        # 返回一个零向量或者特定的“未知”向量，或者跳过这个K线
        # 这里的维度需要和你的 vector_dim 匹配
        return np.zeros(vector_dim)


    # 示例向量化： (变化量 / ATR) 或 (变化量 / 前收盘价)
    # 这里的归一化方式和特征选择对VQ-VAE效果至关重要
    # 确保与VQ-VAE训练时使用的方式一致

    # 方式一：基于ATR的归一化 (相对波动)
    vec = [
        (o - prev_close) / atr_val if atr_val != 0 else 0,
        (h - o) / atr_val if atr_val != 0 else 0,          # 最高价相对于开盘价的差
        (l - o) / atr_val if atr_val != 0 else 0,          # 最低价相对于开盘价的差
        (c - o) / atr_val if atr_val != 0 else 0,          # 收盘价相对于开盘价的差 (实体)
        v / ma_volume if ma_volume != 0 else 0,
    ]

    # 方式二：基于前收盘价的百分比变化 (相对价格水平)
    # vec = [
    #     (o - prev_close) / prev_close if prev_close != 0 else 0,
    #     (h - prev_close) / prev_close if prev_close != 0 else 0,
    #     (l - prev_close) / prev_close if prev_close != 0 else 0,
    #     (c - prev_close) / prev_close if prev_close != 0 else 0,
    #     v / ma_volume if ma_volume != 0 else 0,
    # ]

    # 确保向量维度与 vector_dim 匹配
    if len(vec) != vector_dim:
        # 可以填充或截断，但最好从源头保证维度一致
        # 例如，如果 vector_dim 更大，用0填充
        # 如果 vector_dim 更小，可能需要重新设计特征
        raise ValueError(f"Generated vector length {len(vec)} does not match vector_dim {vector_dim}")

    return np.array(vec, dtype=np.float32)


# --- 2. 模拟VQ-VAE的组件 (假设已训练好) ---
class VQEmbedding(nn.Module):
    """
    Vector Quantizer Layer (简化版，仅用于演示Tokenizer的核心逻辑)
    在实际的VQ-VAE中，这个模块通常在编码器之后，解码器之前。
    """
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super(VQEmbedding, self).__init__()
        self.embedding_dim = embedding_dim
        self.num_embeddings = num_embeddings
        self.commitment_cost = commitment_cost # 在tokenizer中通常不直接使用

        # 码本 (Codebook) - 这是VQ-VAE训练后得到的
        # 每一行是一个码向量 (code vector)
        self.embedding = nn.Embedding(self.num_embeddings, self.embedding_dim)
        # 通常，码本的权重是训练得到的，这里我们用随机初始化代替
        # 在实际应用中，你需要加载训练好的码本权重
        self.embedding.weight.data.uniform_(-1.0 / self.num_embeddings, 1.0 / self.num_embeddings)

    def forward(self, latents_e):
        """
        将编码器输出的连续向量映射到离散的码本索引。
        latents_e: (batch_size, embedding_dim) or (embedding_dim)
        """
        if latents_e.ndim == 1: # 单个向量
            latents_e = latents_e.unsqueeze(0) # 变成 (1, embedding_dim)

        # 计算输入向量与码本中所有码向量的L2距离的平方
        # latents_e_flat: (batch_size, embedding_dim)
        # self.embedding.weight: (num_embeddings, embedding_dim)
        distances = (torch.sum(latents_e**2, dim=1, keepdim=True)
                     - 2 * torch.matmul(latents_e, self.embedding.weight.t())
                     + torch.sum(self.embedding.weight**2, dim=1, keepdim=True).t())
        # distances: (batch_size, num_embeddings)

        # 找到最近的码向量的索引
        # encoding_indices: (batch_size, 1)
        encoding_indices = torch.argmin(distances, dim=1)

        return encoding_indices # 返回的是token ID (码本索引)

    def quantize(self, encoding_indices):
        """从索引获取量化后的向量 (码本中的向量)"""
        return self.embedding(encoding_indices)


# --- 3. CandlestickVQTokenizer ---
class CandlestickVQTokenizer:
    def __init__(self, codebook_weights_path=None, num_embeddings=512, embedding_dim=5,
                 atr_period=14, ma_volume_period=20):
        """
        初始化Tokenizer。
        :param codebook_weights_path: 预训练的VQ-VAE码本权重文件路径 (e.g., .pt or .pth)
        :param num_embeddings: 码本中的码向量数量 (词汇表大小)
        :param embedding_dim: K线向量的维度，也是码向量的维度
        :param atr_period: 计算ATR的周期
        :param ma_volume_period: 计算移动平均成交量的周期
        """
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.atr_period = atr_period
        self.ma_volume_period = ma_volume_period

        self.vq_layer = VQEmbedding(num_embeddings, embedding_dim)

        if codebook_weights_path:
            try:
                # 加载预训练的码本权重
                # 假设权重文件中只保存了 nn.Embedding.weight
                state_dict = torch.load(codebook_weights_path)
                if 'weight' in state_dict: # 如果保存的是整个embedding层的state_dict
                     self.vq_layer.embedding.load_state_dict(state_dict)
                else: # 如果直接保存的是权重张量
                    self.vq_layer.embedding.weight.data.copy_(state_dict)
                print(f"Loaded codebook weights from {codebook_weights_path}")
            except Exception as e:
                print(f"Warning: Could not load codebook weights from {codebook_weights_path}. Using random init. Error: {e}")
        else:
            print("Warning: No codebook_weights_path provided. Using randomly initialized codebook.")

        self.vq_layer.eval() # 设置为评估模式

        # 特殊tokens
        self.bos_token = "<BOS>"
        self.eos_token = "<EOS>"
        self.pad_token = "<PAD>"
        self.unk_token = "<UNK>" # 用于无法处理的K线或作为默认

        self.special_tokens = [self.pad_token, self.unk_token, self.bos_token, self.eos_token]
        self.vocab_size = self.num_embeddings + len(self.special_tokens)

        # 建立token到ID的映射
        self.token_to_id_map = {}
        self.id_to_token_map = {}

        # 码本中的token (ID从0到num_embeddings-1)
        for i in range(self.num_embeddings):
            token_name = f"CODE_{i}"
            self.token_to_id_map[token_name] = i
            self.id_to_token_map[i] = token_name

        # 特殊token的ID
        offset = self.num_embeddings
        for i, token in enumerate(self.special_tokens):
            self.token_to_id_map[token] = offset + i
            self.id_to_token_map[offset + i] = token

        # ID for unknown K-line token (can be one of the CODE_ tokens if VQ-VAE is robust, or a special UNK id)
        # For simplicity, let's map actual VQ indices as the primary tokens.
        # We'll handle UNK during the vectorization if data is bad.
        self.unk_token_id = self.token_to_id_map[self.unk_token]


    def _preprocess_df(self, df_ohlcv):
        """辅助函数，计算ATR, MA_Volume, Prev_Close"""
        df = df_ohlcv.copy()
        # ATR
        df['H-L'] = df['High'] - df['Low']
        df['H-PC'] = abs(df['High'] - df['Close'].shift(1))
        df['L-PC'] = abs(df['Low'] - df['Close'].shift(1))
        df['TR'] = df[['H-L', 'H-PC', 'L-PC']].max(axis=1)
        df['ATR'] = df['TR'].rolling(window=self.atr_period, min_periods=self.atr_period).mean() #确保有足够数据

        # MA Volume
        df['MA_Volume'] = df['Volume'].rolling(window=self.ma_volume_period, min_periods=self.ma_volume_period).mean()

        # Prev Close
        df['Prev_Close'] = df['Close'].shift(1)
        return df

    def tokenize_single_candlestick(self, ohlcv_row, prev_close, ma_volume, atr_val):
        """将单个K线数据点（已提取相关特征）转换为token ID"""
        vector = candlestick_to_vector(ohlcv_row, prev_close, ma_volume, atr_val, self.embedding_dim)
        if np.all(vector == 0): # 假设vectorizer在数据不足时返回0向量
             return self.unk_token_id # 或者可以抛出异常或返回None

        vector_tensor = torch.tensor(vector, dtype=torch.float32)
        with torch.no_grad():
            token_id_tensor = self.vq_layer(vector_tensor) # VQEmbedding的forward返回索引
        return token_id_tensor.item() # .item() 将单元素tensor转为python number

    def encode(self, df_ohlcv_history, add_bos_eos=True):
        """
        将K线历史数据 (Pandas DataFrame) 转换为token ID序列。
        df_ohlcv_history: DataFrame with columns ['Open', 'High', 'Low', 'Close', 'Volume', 'Timestamp']
        """
        if not all(col in df_ohlcv_history.columns for col in ['Open', 'High', 'Low', 'Close', 'Volume']):
            raise ValueError("Input DataFrame must contain Open, High, Low, Close, Volume columns.")

        processed_df = self._preprocess_df(df_ohlcv_history)
        token_ids = []

        if add_bos_eos:
            token_ids.append(self.token_to_id_map[self.bos_token])

        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            # 检查是否有足够的历史数据来计算ATR, MA_Volume, Prev_Close
            if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
                # 在序列开始部分，这些值可能是NaN，可以跳过或用unk_token
                # token_ids.append(self.unk_token_id)
                continue # 跳过无法计算完整特征的早期K线

            token_id = self.tokenize_single_candlestick(
                row,
                row['Prev_Close'],
                row['MA_Volume'],
                row['ATR']
            )
            token_ids.append(token_id)

        if add_bos_eos:
            token_ids.append(self.token_to_id_map[self.eos_token])

        return token_ids

    def decode_id(self, token_id):
        """将单个token ID转换回其符号表示 (e.g., "CODE_123" or "<BOS>")"""
        if token_id not in self.id_to_token_map:
            # raise ValueError(f"Unknown token ID: {token_id}")
            return self.unk_token # Fallback for unknown IDs
        return self.id_to_token_map[token_id]

    def decode_sequence(self, token_ids_sequence):
        """将token ID序列转换回符号表示序列"""
        return [self.decode_id(tid) for tid in token_ids_sequence]

    def get_quantized_vector(self, token_id):
        """
        (可选) 根据token ID (码本索引) 获取对应的码本向量。
        这对于理解token代表的“原型”K线或用于VQ-VAE解码器可能有用。
        """
        if token_id < 0 or token_id >= self.num_embeddings:
            # raise ValueError(f"Token ID {token_id} is out of codebook range [0, {self.num_embeddings-1}]")
            print(f"Warning: Token ID {token_id} is out of codebook range. Returning None.")
            return None
        with torch.no_grad():
            quantized_vec = self.vq_layer.quantize(torch.tensor([token_id]))
        return quantized_vec.squeeze().numpy()

# --- 示例用法 ---
if __name__ == '__main__':
    # 0. 准备一些伪K线数据
    data = {
        'Timestamp': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05',
                                      '2023-01-06', '2023-01-07', '2023-01-08', '2023-01-09', '2023-01-10',
                                      '2023-01-11', '2023-01-12', '2023-01-13', '2023-01-14', '2023-01-15',
                                      '2023-01-16', '2023-01-17', '2023-01-18', '2023-01-19', '2023-01-20']),
        'Open':  np.random.rand(20) * 10 + 100,
        'High':  np.random.rand(20) * 5 + 105, #确保 High >= Open, Close
        'Low':   np.random.rand(20) * -5 + 95, #确保 Low <= Open, Close
        'Close': np.random.rand(20) * 10 + 100,
        'Volume':np.random.randint(1000, 5000, size=20)
    }
    df_ohlcv = pd.DataFrame(data)
    df_ohlcv['High'] = df_ohlcv[['Open', 'Close', 'High']].max(axis=1)
    df_ohlcv['Low'] = df_ohlcv[['Open', 'Close', 'Low']].min(axis=1)


    # 1. 初始化Tokenizer
    # 假设我们有一个预训练的码本 (这里我们没有，所以会使用随机初始化的)
    # CODEBOOK_PATH = "path/to/your/vq_codebook_weights.pt"
    CODEBOOK_PATH = None # 使用随机码本进行演示
    EMBEDDING_DIM = 5 # K线向量的维度，与 candlestick_to_vector 一致
    NUM_EMBEDDINGS = 64 # 码本大小 (词汇表大小，不含特殊token)
    ATR_PERIOD = 7      # 缩短周期以便在少量数据上快速看到非NaN值
    MA_VOL_PERIOD = 7

    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=CODEBOOK_PATH,
        num_embeddings=NUM_EMBEDDINGS,
        embedding_dim=EMBEDDING_DIM,
        atr_period=ATR_PERIOD,
        ma_volume_period=MA_VOL_PERIOD
    )

    print(f"Tokenizer vocabulary size: {tokenizer.vocab_size}")
    print(f"Example token to ID: {tokenizer.token_to_id_map['CODE_0']}, {tokenizer.token_to_id_map['<BOS>']}")
    print(f"Example ID to token: {tokenizer.id_to_token_map[0]}, {tokenizer.id_to_token_map[NUM_EMBEDDINGS+2]}")


    # 2. 对K线历史数据进行编码
    # 由于ATR和MA需要一定周期的数据才能计算，前几条K线可能无法编码
    # 我们取一段数据，使得ATR和MA能够计算
    # min_data_points = max(ATR_PERIOD, MA_VOL_PERIOD) # 加1是因为shift(1) for Prev_Close
    # if len(df_ohlcv) > min_data_points :
    #     token_ids = tokenizer.encode(df_ohlcv.iloc[min_data_points-1:]) # 从有足够历史数据的地方开始
    # else:
    #     print("Not enough data to calculate initial ATR/MA_Volume for encoding.")
    #     token_ids = []

    token_ids = tokenizer.encode(df_ohlcv) # encode内部会处理NaN

    print(f"\nEncoded token IDs for a segment of data (length {len(token_ids)}):")
    print(token_ids)

    # 3. 解码Token ID序列
    if token_ids:
        decoded_tokens = tokenizer.decode_sequence(token_ids)
        print("\nDecoded tokens:")
        print(decoded_tokens)

        # 4. 获取某个token ID对应的量化向量 (码本中的向量)
        if len(token_ids) > 2 : # 确保至少有一个K线token（非BOS/EOS）
            example_kline_token_id = token_ids[1] # 第一个实际K线的token
            if example_kline_token_id < NUM_EMBEDDINGS: #确保是码本内的ID
                quantized_vector = tokenizer.get_quantized_vector(example_kline_token_id)
                print(f"\nQuantized vector for token ID {example_kline_token_id} ('{tokenizer.decode_id(example_kline_token_id)}'):")
                print(quantized_vector)
            else:
                print(f"\nToken ID {example_kline_token_id} ('{tokenizer.decode_id(example_kline_token_id)}') is a special token, no quantized vector.")
    else:
        print("\nNo tokens were generated, skipping decode and quantized vector steps.")