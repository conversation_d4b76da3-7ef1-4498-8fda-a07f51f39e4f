e:
cd e:\lab\RoboQuant\pylab
@REM python ./pyqlab/pl/train_conv_r.py --batch_size=512 --k_folds=5 --max_epochs=7 --activation=relu --ins_nums="(0, 51, 51, 0)" --num_embeds="[72]" --out_channels="(24, 48, 1296, 1296)" --version=CV1DR --model_name=time_series_model1dr

@REM python ./pyqlab/pl/train_conv_r.py --batch_size=512 --k_folds=4 --max_epochs=7 --activation=relu --out_channels="(24, 48, 1200, 1200)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --batch_size=512 --k_folds=4 --max_epochs=7 --activation=relu --out_channels="(24, 48, 1200, 1200)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --batch_size=512 --k_folds=4 --max_epochs=7 --activation=relu --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.4

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=10HF --num_channel=10 --batch_size=512 --k_folds=4 --max_epochs=7 --activation=relu --kernel_size=2 --out_channels="(32, 64, 3328, 3328)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=15HF --num_channel=15 --kernel_size=2 --batch_size=256 --k_folds=4 --max_epochs=10 --activation=relu --out_channels="(32, 64, 3328, 3328)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=10HF --num_channel=10 --start_time=2023-03-01 --end_time=2023-12-31 --batch_size=512 --k_folds=4 --max_epochs=8 --activation=relu --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=10HF --num_channel=10 --start_time=2023-03-01 --end_time=2023-12-31 --batch_size=512 --k_folds=4 --max_epochs=8 --activation=relu --out_channels="(48, 96, 2400, 2400)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=15HF --num_channel=15 --batch_size=256 --k_folds=4 --max_epochs=8 --min_delta=0.0 --activation=relu --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=15HF --num_channel=15 --start_time=2023-01-01 --end_time=2023-12-31 --batch_size=256 --k_folds=4 --max_epochs=8 --min_delta=0.0 --activation=relu --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=15HF --num_channel=15 --start_time=2023-03-01 --end_time=2024-01-31 --batch_size=256 --k_folds=4 --max_epochs=8 --activation=relu --out_channels="(48, 96, 2400, 2400)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_name=15HF --num_channel=15 --start_time=2023-06-01 --end_time=2024-01-31 --batch_size=512 --k_folds=4 --max_epochs=7 --activation=gelu --out_channels="(48, 96, 2400, 2400)" --model_name=time_series_model2dr --dropout=0.5

@REM AG ===========================
@REM python ./pyqlab/pl/train_conv_r.py --version="CV2DR-AG" --data_path="e:/featdata/ag" --ds_files="['ag.2018','ag.2019','ag.2020','ag.2021','ag.2022','ag.2023']" --ds_name=10HF --num_channel=10 --batch_size=256 --k_folds=4 --max_epochs=10 --min_delta=0.0 --early_stop=7 --activation=relu --ins_nums="(0, 51, 51, 2)" --num_embeds="[64,5,11]" --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --version="CV2DR-AG" --data_path="e:/featdata/ag" --ds_files="['ag.2015','ag.2016','ag.2017','ag.2018','ag.2019','ag.2020','ag.2021','ag.2022','ag.2023']" --ds_name=15HF --num_channel=15 --batch_size=256 --k_folds=4 --max_epochs=10 --activation=relu --ins_nums="(0, 51, 51, 2)" --num_embeds="[64,5,11]" --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM SF ===========================
@REM python ./pyqlab/pl/train_conv_r.py --version="CV2DR-SF" --fut_codes=SF_FUT_CODES --data_path="e:/featdata/sf" --ds_files="['sf.2020','sf.2021','sf.2022','sf.2023','sf.2024']" --ds_name=10HF --num_channel=10 --kernel_size=3 --batch_size=512 --k_folds=4 --max_epochs=10 --min_delta=0.0 --early_stop=7 --ins_nums="(0, 51, 51, 2)" --num_embeds="[64,5,11]" --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --version="CV2DR-SF" --fut_codes=SF_FUT_CODES --data_path="e:/featdata/sf" --ds_files="['sf.2020','sf.2021','sf.2022','sf.2023']" --ds_name=15HF --num_channel=15 --kernel_size=3 --batch_size=512 --k_folds=4 --max_epochs=10 --ins_nums="(0, 51, 51, 2)" --num_embeds="[64,5,11]" --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --ds_files="['main.2022']" --batch_size=512 --out_channels="(24, 48, 1200, 256)" --model_name=time_series_model2d --restart=True --sub_dir=TimeSeriesConv2d_1200

@REM 备选
@REM python ./pyqlab/pl/train_conv_r.py --batch_size=512 --k_folds=4 --max_epochs=8 --activation=relu --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5

python ./pyqlab/pl/train_conv_r.py ^
--version="CV2DR" ^
--data_path="f:/featdata/top" ^
--ds_files="['top.2025']" ^
--ds_name=15HF ^
--num_channel=15 ^
--batch_size=128 ^
--k_folds=5 ^
--max_epochs=1 ^
--activation=relu ^
--out_channels="(32, 64, 1600, 1600)" ^
--model_name=time_series_model2dr ^
--model_path=pyqlab.models.fintimeseries ^
--dropout=0.5 ^
--min_delta=0.001

@REM python ./pyqlab/pl/train_conv_r.py ^
@REM --data_path="f:/featdata/top" ^
@REM --ds_files="['top.2024', 'top.2025']" ^
@REM --ds_name=15HF ^
@REM --num_channel=15 ^
@REM --batch_size=128 ^
@REM --k_folds=3 ^
@REM --max_epochs=5 ^
@REM --activation=relu ^
@REM --out_channels="(32, 64, 1600, 1600)" ^
@REM --model_name=time_series_model2dr_v1 ^
@REM --model_path=pyqlab.models.fintimeseries ^
@REM --num_conv_layers=3 ^
@REM --conv_channels="(32, 64, 128)" ^
@REM --use_residual ^
@REM --num_outputs=1 ^
@REM --weight_decay=0.001 ^
@REM --dropout=0.5 ^
@REM --min_delta=0.01
@REM --save_as_to_onnx ^
@REM --best_score 0.1
@REM --use_attention ^
@REM --probabilistic ^
@REM  ^
@REM --resume

@REM python ./pyqlab/pl/train_conv_r.py ^
@REM --version="CV2DR2" ^
@REM --data_path="f:/featdata/top" ^
@REM --ds_files="['top.2025']" ^
@REM --ds_name=15HF ^
@REM --num_channel=15 ^
@REM --batch_size=64 ^
@REM --k_folds=5 ^
@REM --max_epochs=1 ^
@REM --activation=relu ^
@REM --out_channels="(32, 64, 1600, 1600)" ^
@REM --model_name=time_series_model2dr_v2 ^
@REM --model_path=pyqlab.models.fintimeseries ^
@REM --dropout=0.3 ^
@REM --min_delta=0.01

@REM --save_as_to_onnx ^
@REM --best_score 0.08

@REM python ./pyqlab/pl/train_conv_r.py 
@REM --ds_name=15HF ^
@REM --num_channel=15 ^
@REM --batch_size=480 ^
@REM --k_folds=4 ^
@REM --max_epochs=7 ^
@REM --activation=relu ^
@REM --out_channels="(48, 96, 2400, 2400)" ^
@REM --model_name=time_series_model2dr ^
@REM --dropout=0.5


@REM SLOW
@REM python ./pyqlab/pl/train_conv_r.py --version="CV1DR-L" --ds_name=15HF --num_channel=15 --kernel_size=2 --batch_size=512 --k_folds=3 --max_epochs=10 --activation=relu --ins_nums="(51,0,0,17)" --out_channels="(24, 48, 960, 960)" --model_name=time_series_model1dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --version="CV1DR-S" --ds_name=10HF --num_channel=10 --kernel_size=2 --batch_size=512 --k_folds=4 --max_epochs=10 --activation=relu --ins_nums="(51,0,0,17)" --out_channels="(32, 64, 1344, 1344)" --model_name=time_series_model1dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --version="CV1DR-L" --ds_name=15HF --num_channel=15 --kernel_size=2 --batch_size=512 --k_folds=4 --max_epochs=10 --activation=relu --ins_nums="(51,0,0,17)" --out_channels="(32, 64, 1344, 1344)" --model_name=time_series_model1dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --version="CV2DR-L" --ds_name=15HF --num_channel=15 --kernel_size=2 --batch_size=512 --k_folds=3 --max_epochs=10 --activation=relu --ins_nums="(51,0,0,17)" --out_channels="(24, 48, 1200, 1200)" --model_name=time_series_model2dr --dropout=0.5

@REM python ./pyqlab/pl/train_conv_r.py --version="CV2DR-L" --ds_name=15HF --num_channel=15 --kernel_size=2 --batch_size=512 --k_folds=3 --max_epochs=10 --activation=relu --ins_nums="(51,0,0,17)" --out_channels="(32, 64, 1600, 1600)" --model_name=time_series_model2dr --dropout=0.5


@REM PAUSE