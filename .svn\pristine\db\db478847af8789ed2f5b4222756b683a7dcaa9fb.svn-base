{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import pytz\n", "import struct\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class TickData():\n", "    def __init__(self, dbfile=\"d:/RoboQuant/store/tickdata.db\") -> None:\n", "        self._dbfile=dbfile\n", "        self._db=None\n", "        self._keys=[]\n", "        self._tz=pytz.timezone('Asia/Shanghai')\n", "        pass\n", "\n", "    def open_db(self, mode):\n", "        if self._db:\n", "            raise \"already open a db.\"\n", "        self._db=create_db(\"leveldb\", self._dbfile, mode)\n", "\n", "    def close_db(self):\n", "        if not self._db:\n", "            raise \"not db open.\"\n", "        self._db.close()\n", "        del self._db\n", "        self._db=None\n", "\n", "    def load_all_keys(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        self._keys.clear()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            self._keys.append(str(cursor.key()))\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def read_keys(self, key):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            if str(cursor.key()) == key:\n", "                for i in range(len(cursor.value())//48):\n", "                    tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                    print(datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)\n", "                break\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def read_index(self, index):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        if index > len(self._keys):\n", "            raise f\"index < {len(self._keys)}\"\n", "\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            if cursor.key() == self._keys[index]:\n", "                s=str(cursor.key(), encoding='utf-8')\n", "                pos0=s.find(':')\n", "                pos1=s.find(':', pos0+1)\n", "                lb=s[pos0+1:pos1]\n", "                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                data=[lb, datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2]\n", "                break\n", "            cursor.next()\n", "        del cursor\n", "        return data\n", "\n", "    def write(self, key, value):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.put(key, value)\n", "        del transaction\n", "        \n", "    def delete(self, key):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.delete(key)\n", "        del transaction\n", "        \n", "    def query(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            print(cursor.key())\n", "            # print(cursor.key(), cursor.value())\n", "            cursor.next()\n", "        del cursor\n", "        \n", "    def read_all(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        data=[]\n", "        while cursor.valid():\n", "            for i in range(len(cursor.value())//48):\n", "                s=str(cursor.key(), encoding='utf-8')\n", "                pos0=s.find(':')\n", "                pos1=s.find(':', pos0+1)\n", "                lb=s[pos0+1:pos1]\n", "                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                data.append([lb, datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])\n", "            cursor.next()\n", "        del cursor\n", "        return pd.DataFrame(data, columns=['label','datetime','price','volume','bid_price1','bid_price2','bid_volume1','bid_volume2','ask_price1','ask_price2','ask_volume1','ask_volume2'])\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["td=TickData()\n", "td.open_db(Mode.read)\n", "df=td.read_all()\n", "td.close_db()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"../data/tick.csv\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           label            datetime   price  volume  bid_price1  bid_price2  \\\n", "0       I8888.DC 2022-01-19 21:05:04   739.5      42       739.0         0.0   \n", "1       I8888.DC 2022-01-19 21:05:05   739.5       9       739.0         0.0   \n", "2       I8888.DC 2022-01-19 21:05:06   739.5      20       739.0         0.0   \n", "3       I8888.DC 2022-01-19 21:05:06   739.5      34       739.0         0.0   \n", "4       I8888.DC 2022-01-19 21:05:07   739.5      55       739.0         0.0   \n", "...          ...                 ...     ...     ...         ...         ...   \n", "31455  RB8888.SC 2022-01-20 21:34:57  4718.0       9      4717.0      4716.0   \n", "31456  RB8888.SC 2022-01-20 21:34:58  4718.0       5      4717.0      4716.0   \n", "31457  RB8888.SC 2022-01-20 21:34:58  4718.0      10      4717.0      4716.0   \n", "31458  RB8888.SC 2022-01-20 21:34:59  4718.0       1      4717.0      4716.0   \n", "31459  RB8888.SC 2022-01-20 21:34:59  4718.0      58      4718.0      4717.0   \n", "\n", "       bid_volume1  bid_volume2  ask_price1  ask_price2  ask_volume1  \\\n", "0              804            0       739.5         0.0          317   \n", "1              802            0       739.5         0.0          309   \n", "2              788            0       739.5         0.0          313   \n", "3              785            0       739.5         0.0          292   \n", "4              787            0       739.5         0.0          249   \n", "...            ...          ...         ...         ...          ...   \n", "31455          260          120      4718.0      4719.0           11   \n", "31456          261          121      4718.0      4719.0           19   \n", "31457          260          121      4718.0      4719.0           12   \n", "31458          260          121      4718.0      4719.0           18   \n", "31459           17          260      4719.0      4720.0          395   \n", "\n", "       ask_volume2  \n", "0                0  \n", "1                0  \n", "2                0  \n", "3                0  \n", "4                0  \n", "...            ...  \n", "31455          408  \n", "31456          408  \n", "31457          410  \n", "31458          411  \n", "31459          884  \n", "\n", "[31460 rows x 12 columns]\n"]}], "source": ["print(df)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["56\n"]}], "source": ["td.open_db(Mode.read)\n", "td.load_all_keys()\n", "td.close_db()\n", "print(len(td._keys))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\"b'tick:I8888.DC:5475325'\", \"b'tick:I8888.DC:5475326'\", \"b'tick:I8888.DC:5475327'\", \"b'tick:I8888.DC:5475328'\", \"b'tick:I8888.DC:5475329'\", \"b'tick:I8888.DC:5475330'\", \"b'tick:I8888.DC:5475331'\", \"b'tick:I8888.DC:5475332'\", \"b'tick:I8888.DC:5475333'\", \"b'tick:I8888.DC:5475334'\", \"b'tick:I8888.DC:5475335'\", \"b'tick:I8888.DC:5475336'\", \"b'tick:I8888.DC:5475337'\", \"b'tick:I8888.DC:5475338'\", \"b'tick:I8888.DC:5475339'\", \"b'tick:I8888.DC:5475340'\", \"b'tick:I8888.DC:5475341'\", \"b'tick:I8888.DC:5475342'\", \"b'tick:I8888.DC:5475343'\", \"b'tick:I8888.DC:5475344'\", \"b'tick:I8888.DC:5475345'\", \"b'tick:I8888.DC:5475346'\", \"b'tick:I8888.DC:5475615'\", \"b'tick:I8888.DC:5475616'\", \"b'tick:I8888.DC:5475617'\", \"b'tick:I8888.DC:5475618'\", \"b'tick:M8888.DC:5475615'\", \"b'tick:M8888.DC:5475616'\", \"b'tick:M8888.DC:5475617'\", \"b'tick:M8888.DC:5475618'\", \"b'tick:RB8888.SC:5475325'\", \"b'tick:RB8888.SC:5475326'\", \"b'tick:RB8888.SC:5475327'\", \"b'tick:RB8888.SC:5475328'\", \"b'tick:RB8888.SC:5475329'\", \"b'tick:RB8888.SC:5475330'\", \"b'tick:RB8888.SC:5475331'\", \"b'tick:RB8888.SC:5475332'\", \"b'tick:RB8888.SC:5475333'\", \"b'tick:RB8888.SC:5475334'\", \"b'tick:RB8888.SC:5475335'\", \"b'tick:RB8888.SC:5475336'\", \"b'tick:RB8888.SC:5475337'\", \"b'tick:RB8888.SC:5475338'\", \"b'tick:RB8888.SC:5475339'\", \"b'tick:RB8888.SC:5475340'\", \"b'tick:RB8888.SC:5475341'\", \"b'tick:RB8888.SC:5475342'\", \"b'tick:RB8888.SC:5475343'\", \"b'tick:RB8888.SC:5475344'\", \"b'tick:RB8888.SC:5475345'\", \"b'tick:RB8888.SC:5475346'\", \"b'tick:RB8888.SC:5475615'\", \"b'tick:RB8888.SC:5475616'\", \"b'tick:RB8888.SC:5475617'\", \"b'tick:RB8888.SC:5475618'\"]\n"]}], "source": ["print(td._keys)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}