# 平衡训练BarGpt4模型

## 概述

`train_balanced_bar_gpt4.py` 是一个完善的训练脚本，参考了原来的 `train_bar_gpt.py` 实现，主要用于解决token分布不均衡问题。

## 主要特性

### 1. 完整的数据集支持
- 参考原版实现了完整的数据加载流程
- 支持Pipeline数据处理
- 支持BarDataset数据集
- 自动处理数据配置和时间编码

### 2. 交叉验证训练
- 支持K折交叉验证 (默认4折)
- 自动数据集划分
- 每个fold独立训练

### 3. 平衡训练功能
- 支持类别权重平衡 (`--use_class_weights`)
- 支持多种平衡方法：
  - `focal_loss`: Focal Loss
  - `weighted_loss`: 加权损失
  - `none`: 不使用平衡
- 支持数据级别平衡：
  - `weighted_sampling`: 加权采样
  - `oversampling`: 过采样
  - `none`: 不使用数据平衡

### 4. 完整的训练流程
- 自动模型保存和恢复
- TensorBoard日志记录
- 早停和学习率监控
- 自动ONNX模型导出
- 模型摘要显示

### 5. 丰富的配置选项
- 数据路径和市场配置
- 模型超参数配置
- 训练参数配置
- 平衡参数配置

## 使用方法

### 1. 基本训练
```bash
python train_balanced_bar_gpt4.py
```

### 2. 使用批处理文件
```bash
train_balanced_bar_gpt4.bat
```

### 3. 自定义参数训练
```bash
python train_balanced_bar_gpt4.py \
    --data_path "f:/featdata/barenc/db2" \
    --market "fut" \
    --block_name "sf" \
    --period "min5" \
    --start_year 2024 \
    --end_year 2024 \
    --block_size 20 \
    --batch_size 64 \
    --n_head 32 \
    --n_layer 4 \
    --d_model 96 \
    --lr 5e-4 \
    --max_epochs 10 \
    --k_folds 4 \
    --use_class_weights \
    --balance_method "weighted_loss"
```

### 4. 导出ONNX模型
```bash
python train_balanced_bar_gpt4.py --export_onnx
```

### 5. 恢复训练
```bash
python train_balanced_bar_gpt4.py --resume
```

## 主要参数说明

### 数据参数
- `--data_path`: 数据路径 (默认: f:/featdata/barenc/db2)
- `--market`: 市场类型 (fut/stk, 默认: fut)
- `--block_name`: 块名称 (默认: sf)
- `--period`: 时间周期 (day/min5/min1, 默认: min5)
- `--start_year/--end_year`: 数据年份范围
- `--block_size`: 序列长度 (默认: 20)
- `--batch_size`: 批次大小 (默认: 64)

### 模型参数
- `--n_head`: 注意力头数 (默认: 32)
- `--n_layer`: Transformer层数 (默认: 4)
- `--d_model`: 模型维度 (默认: 96)
- `--vocab_size`: 词汇表大小 (默认: 40002)
- `--lr`: 学习率 (默认: 5e-4)

### 平衡参数
- `--use_class_weights`: 启用类别权重
- `--balance_method`: 平衡方法 (focal_loss/weighted_loss/none)
- `--data_balance_method`: 数据平衡方法 (weighted_sampling/oversampling/none)

### 训练参数
- `--max_epochs`: 最大训练轮数 (默认: 10)
- `--k_folds`: 交叉验证折数 (默认: 4)
- `--early_stop`: 早停耐心值 (默认: 5)
- `--resume`: 恢复训练

## 输出文件

### 训练日志
- `lightning_logs/`: TensorBoard日志文件
- 包含训练损失、验证损失、学习率等信息

### 模型文件
- `model/`: ONNX模型文件
- 文件名格式: `{trainer_name}_{timestamp}_{score}_ls.onnx`

### 检查点文件
- `lightning_logs/{trainer_name}/`: PyTorch Lightning检查点
- 包含最佳模型和最后一个模型

## 注意事项

1. **数据路径**: 确保数据路径正确，数据文件存在
2. **内存使用**: 大数据集可能需要调整batch_size和num_workers
3. **GPU使用**: 脚本会自动检测和使用可用的GPU
4. **平衡方法**: 根据数据分布选择合适的平衡方法
5. **交叉验证**: K折交叉验证会增加训练时间，但提高模型稳定性

## 与原版的主要改进

1. **完整的数据集实现**: 不再是空框架，而是完整的数据加载逻辑
2. **交叉验证支持**: 完整实现K折交叉验证
3. **配置系统**: 完善的配置管理系统
4. **模型导出**: 自动ONNX模型导出功能
5. **日志系统**: 完整的TensorBoard日志记录
6. **错误处理**: 更好的错误处理和提示信息
7. **参数完整性**: 包含所有必要的训练参数
8. **代码结构**: 更清晰的代码组织和注释

这个完善后的训练脚本现在可以直接用于生产环境的模型训练。
