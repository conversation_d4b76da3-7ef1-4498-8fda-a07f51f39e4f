import sys
import os
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from pyqlab.models.gpt2.signal_generator import (
    SignalGenerator, 
    ThresholdDirectionStrategy, 
    TopKStrategy, 
    StatisticalMomentumStrategy, 
    WeightedEnsembleStrategy
)

# 模拟模型和tokenizer
class DummyModel:
    def __init__(self):
        pass
        
    def to(self, device):
        return self
        
    def eval(self):
        pass
        
    def __call__(self, input_tokens, code_tensor, time_features=None):
        # 返回随机logits
        batch_size = input_tokens.shape[0]
        seq_len = input_tokens.shape[1]
        vocab_size = 100
        logits = torch.randn(batch_size, seq_len, vocab_size)
        return logits, None

class DummyTokenizer:
    def __init__(self):
        # 创建一个模拟的token词汇表，包含不同的变化幅度
        self.idx2token = {}
        for i in range(100):
            # 创建一些上涨、下跌和持平的token
            if i < 40:  # 上涨
                change = np.random.randint(1, 10)
            elif i < 80:  # 下跌
                change = -np.random.randint(1, 10)
            else:  # 持平
                change = 0
                
            entity = np.random.randint(1, 10)
            upline = np.random.randint(1, 5)
            downline = np.random.randint(1, 5)
            
            self.idx2token[i] = f"{change}|{entity}|{upline}|{downline}"
            
        # 添加一些特殊token
        self.idx2token[95] = "<PAD>"
        self.idx2token[96] = "<UNK>"
        self.idx2token[97] = "<BOS>"
        self.idx2token[98] = "<EOS>"
        self.idx2token[99] = "<MASK>"
        
    def tokenize(self, df):
        # 返回一些模拟的token
        return [np.random.randint(0, 95) for _ in range(30)]

def generate_dummy_logits(bias="neutral"):
    """生成带有偏向性的模拟logits"""
    batch_size = 1
    seq_len = 1
    vocab_size = 100
    
    # 创建基础logits
    logits = torch.randn(batch_size, seq_len, vocab_size)
    
    # 根据偏向性调整logits
    if bias == "bullish":
        # 增加上涨token的logits值
        for i in range(40):
            logits[0, 0, i] += 2.0
    elif bias == "bearish":
        # 增加下跌token的logits值
        for i in range(40, 80):
            logits[0, 0, i] += 2.0
    elif bias == "neutral":
        # 增加持平token的logits值
        for i in range(80, 95):
            logits[0, 0, i] += 1.0
    
    return logits

def compare_strategies():
    """比较不同的信号生成策略"""
    # 创建tokenizer
    tokenizer = DummyTokenizer()
    
    # 创建不同的策略
    threshold_strategy = ThresholdDirectionStrategy(threshold=0.6)
    topk_strategy = TopKStrategy(k=5, min_prob=0.3)
    momentum_strategy = StatisticalMomentumStrategy(momentum_threshold=0.2)
    
    # 创建集成策略
    ensemble_strategy = WeightedEnsembleStrategy([
        (threshold_strategy, 0.5),
        (topk_strategy, 0.3),
        (momentum_strategy, 0.2)
    ])
    
    # 创建信号生成器
    threshold_generator = SignalGenerator(threshold_strategy)
    topk_generator = SignalGenerator(topk_strategy)
    momentum_generator = SignalGenerator(momentum_strategy)
    ensemble_generator = SignalGenerator(ensemble_strategy)
    
    # 测试不同的市场情景
    scenarios = ["bullish", "bearish", "neutral"]
    
    results = []
    
    for scenario in scenarios:
        print(f"\n===== {scenario.upper()} 市场情景 =====")
        
        # 生成带有偏向性的logits
        logits = generate_dummy_logits(scenario)
        
        # 获取各策略的信号
        threshold_signal = threshold_generator.generate_signal(logits, tokenizer)
        topk_signal = topk_generator.generate_signal(logits, tokenizer)
        momentum_signal = momentum_generator.generate_signal(logits, tokenizer)
        ensemble_signal = ensemble_generator.generate_signal(logits, tokenizer)
        
        # 打印结果
        print(f"阈值方向策略: {threshold_signal['signal']} (置信度: {threshold_signal['confidence']:.4f})")
        if 'direction_probs' in threshold_signal:
            print(f"  方向概率: 上涨={threshold_signal['direction_probs']['up']:.4f}, "
                  f"持平={threshold_signal['direction_probs']['flat']:.4f}, "
                  f"下跌={threshold_signal['direction_probs']['down']:.4f}")
        
        print(f"TopK策略: {topk_signal['signal']} (置信度: {topk_signal['confidence']:.4f})")
        if 'top_predictions' in topk_signal and topk_signal['top_predictions']:
            top_pred = topk_signal['top_predictions'][0]
            if 'change' in top_pred:
                print(f"  Top预测: 变化={top_pred['change']}, 实体={top_pred['entity']}, "
                      f"上影线={top_pred['upline']}, 下影线={top_pred['downline']}, "
                      f"概率={top_pred['probability']:.4f}")
        
        print(f"统计动量策略: {momentum_signal['signal']} (置信度: {momentum_signal['confidence']:.4f})")
        if 'up_momentum' in momentum_signal:
            print(f"  动量: 上涨={momentum_signal['up_momentum']:.4f}, "
                  f"下跌={momentum_signal['down_momentum']:.4f}")
        
        print(f"集成策略: {ensemble_signal['signal']} (置信度: {ensemble_signal['confidence']:.4f})")
        
        # 收集结果用于可视化
        results.append({
            'scenario': scenario,
            'threshold': {'signal': threshold_signal['signal'], 'confidence': threshold_signal['confidence']},
            'topk': {'signal': topk_signal['signal'], 'confidence': topk_signal['confidence']},
            'momentum': {'signal': momentum_signal['signal'], 'confidence': momentum_signal['confidence']},
            'ensemble': {'signal': ensemble_signal['signal'], 'confidence': ensemble_signal['confidence']}
        })
    
    # 可视化比较结果
    visualize_comparison(results)

def visualize_comparison(results):
    """可视化不同策略的比较结果"""
    scenarios = [r['scenario'] for r in results]
    
    # 提取置信度数据
    threshold_conf = [r['threshold']['confidence'] for r in results]
    topk_conf = [r['topk']['confidence'] for r in results]
    momentum_conf = [r['momentum']['confidence'] for r in results]
    ensemble_conf = [r['ensemble']['confidence'] for r in results]
    
    # 创建柱状图
    fig, ax = plt.subplots(figsize=(12, 8))
    
    x = np.arange(len(scenarios))
    width = 0.2
    
    ax.bar(x - 1.5*width, threshold_conf, width, label='阈值方向策略')
    ax.bar(x - 0.5*width, topk_conf, width, label='TopK策略')
    ax.bar(x + 0.5*width, momentum_conf, width, label='统计动量策略')
    ax.bar(x + 1.5*width, ensemble_conf, width, label='集成策略')
    
    ax.set_ylabel('置信度')
    ax.set_title('不同市场情景下各策略的置信度比较')
    ax.set_xticks(x)
    ax.set_xticklabels([s.capitalize() for s in scenarios])
    ax.legend()
    
    # 添加信号标签
    for i, r in enumerate(results):
        signals = [
            r['threshold']['signal'],
            r['topk']['signal'],
            r['momentum']['signal'],
            r['ensemble']['signal']
        ]
        positions = [
            x[i] - 1.5*width,
            x[i] - 0.5*width,
            x[i] + 0.5*width,
            x[i] + 1.5*width
        ]
        confs = [threshold_conf[i], topk_conf[i], momentum_conf[i], ensemble_conf[i]]
        
        for pos, conf, sig in zip(positions, confs, signals):
            color = 'green' if sig == 'BUY' else ('red' if sig == 'SELL' else 'blue')
            ax.text(pos, conf + 0.02, sig, ha='center', color=color, fontweight='bold')
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    compare_strategies()
