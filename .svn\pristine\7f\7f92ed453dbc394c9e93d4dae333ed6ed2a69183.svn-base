{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# import sys\n", "# sys.path.append('../')\n", "\n", "from pyqlab.data import get_dataset\n", "from pyqlab.data import FTSDataset\n", "import random\n", "from time import time\n", "from argparse import ArgumentParser\n", "import numpy as np\n", "from sklearn.model_selection import KFold\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch import optim\n", "from torch.utils.data import TensorDataset, Subset, DataLoader\n", "from pyqlab.models.transformer.Nonstationary_Transformer import Model\n", "from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def trainer(args, dataset):\n", "    model = Model(args).float()\n", "    train_loader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True, num_workers=args.num_workers)\n", "    train_steps = len(train_loader)\n", "    # early_stopping = EarlyStopping(patience=args.patience, verbose=True)\n", "\n", "    model_optim = optim.Adam(model.parameters(), lr=args.learning_rate)\n", "    criterion = nn.MS<PERSON><PERSON>()\n", "    if args.use_amp:\n", "        scaler = torch.cuda.amp.GradScaler()\n", "\n", "    for epoch in range(args.train_epochs):\n", "        iter_count = 0\n", "        train_loss = []\n", "\n", "        model.train()\n", "        epoch_time = time()\n", "        for i, (batch_emb, batch_x, batch_x_mark, batch_y, batch_y_mark) in enumerate(train_loader):\n", "            print(i, batch_emb.shape, batch_x.shape, batch_x_mark.shape, batch_y.shape, batch_y_mark.shape)\n", "            iter_count += 1\n", "            model_optim.zero_grad()\n", "            batch_x = batch_x.float()\n", "            batch_y = batch_y.float()\n", "            batch_x_mark = batch_x_mark.float()\n", "            batch_y_mark = batch_y_mark.float()\n", "\n", "            # decoder input\n", "            dec_inp = torch.zeros_like(batch_y[:, -args.pred_len:, :]).float()\n", "            dec_inp = torch.cat([batch_y[:, :args.label_len, :], dec_inp], dim=1).float()\n", "\n", "            # encoder - decoder\n", "            if args.use_amp:\n", "                with torch.cuda.amp.autocast():\n", "                    if args.output_attention:\n", "                        outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]\n", "                    else:\n", "                        outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)\n", "\n", "                    f_dim = -1 if args.features == 'MS' else 0\n", "                    outputs = outputs[:, -args.pred_len:, f_dim:]\n", "                    batch_y = batch_y[:, -args.pred_len:, f_dim:]\n", "                    loss = criterion(outputs, batch_y)\n", "                    train_loss.append(loss.item())\n", "            else:\n", "                if args.output_attention:\n", "                    outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]\n", "                else:\n", "                    outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)\n", "\n", "                f_dim = -1 if args.features == 'MS' else 0\n", "                outputs = outputs[:, -args.pred_len:, f_dim:]\n", "                batch_y = batch_y[:, -args.pred_len:, f_dim:]\n", "                loss = criterion(outputs, batch_y)\n", "                train_loss.append(loss.item())\n", "\n", "            if (i + 1) % 100 == 0:\n", "                print(\"\\titers: {0}, epoch: {1} | loss: {2:.7f}\".format(i + 1, epoch + 1, loss.item()))\n", "                speed = (time() - time_now) / iter_count\n", "                left_time = speed * ((args.train_epochs - epoch) * train_steps - i)\n", "                print('\\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))\n", "                iter_count = 0\n", "                time_now = time()\n", "\n", "            if args.use_amp:\n", "                scaler.scale(loss).backward()\n", "                scaler.step(model_optim)\n", "                scaler.update()\n", "            else:\n", "                loss.backward()\n", "                model_optim.step()\n", "            break\n", "\n", "        print(\"Epoch: {} cost time: {}\".format(epoch + 1, time() - epoch_time))\n", "        train_loss = np.average(train_loss)\n", "        # vali_loss = vali(vali_data, vali_loader, criterion)\n", "        # test_loss = vali(test_data, test_loader, criterion)\n", "\n", "        # print(\"Epoch: {0}, Steps: {1} | Train Loss: {2:.7f} Vali Loss: {3:.7f} Test Loss: {4:.7f}\".format(\n", "        #     epoch + 1, train_steps, train_loss, vali_loss, test_loss))\n", "        # early_stopping(vali_loss, model, './model/')\n", "        # if early_stopping.early_stop:\n", "        #     print(\"Early stopping\")\n", "        #     break\n", "\n", "        # adjust_learning_rate(model_optim, epoch + 1, args)\n", "\n", "    # best_model_path = path + '/' + 'checkpoint.pth'\n", "    # model.load_state_dict(torch.load(best_model_path))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_ds(args):\n", "    args.out_channels = eval(args.out_channels)\n", "    args.ins_nums = eval(args.ins_nums)\n", "    args.num_embeds = eval(args.num_embeds)\n", "    args.ds_files = eval(args.ds_files)\n", "    args.fut_codes = eval(args.fut_codes)\n", "    args.seed = random.randint(0, 10000)\n", "    print(args)\n", "    dataset = get_dataset(ds_files=args.ds_files,\n", "                          ins_nums=args.ins_nums,\n", "                          is_normal=args.is_normal,\n", "                          verbose=args.verbose,\n", "                          fut_codes=args.fut_codes,\n", "                          data_path=args.data_path,\n", "                          start_time=args.start_time,\n", "                          end_time=args.end_time,\n", "                          timeenc=1 if args.embed == 'timeF' else 0,\n", "                          model_type=args.model_type,\n", "                          seq_len=args.seq_len,\n", "                          label_len=args.label_len,\n", "                          pred_len=args.pred_len,\n", "                          )\n", "    return dataset\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def main():\n", "    parser = ArgumentParser()\n", "\n", "    # Data API ==============================\n", "    parser.add_argument('--ds_name', default='10HF', type=str)\n", "    parser.add_argument('--ds_files', default='[\"sf.2024\"]', type=str)\n", "    parser.add_argument('--start_time', default='', type=str)\n", "    parser.add_argument('--end_time', default='', type=str)\n", "    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)\n", "    parser.add_argument('--filter_win', default=0, type=int)\n", "    parser.add_argument('--is_normal', default=True, action='store_true')\n", "    parser.add_argument('--verbose', default=False, action='store_true')\n", "    parser.add_argument('--fut_codes', default='SF_FUT_CODES', choices=['MAIN_FUT_CODES', 'MAIN_SEL_FUT_CODES', 'SF_FUT_CODES'], type=str)\n", "    parser.add_argument('--data_path', default='e:/featdata/sf2024', type=str)\n", "    parser.add_argument('--model_type', default=0, type=int)\n", "\n", "    # data loader\n", "    parser.add_argument('--data', type=str, default='ETTm1', help='dataset type')\n", "    parser.add_argument('--root_path', type=str, default='./data/ETT/', help='root path of the data file')\n", "    parser.add_argument('--features', type=str, default='M', help='forecasting task, options:[M, S, MS]; M:multivariate predict multivariate, S:univariate predict univariate, MS:multivariate predict univariate')\n", "    parser.add_argument('--target', type=str, default='OT', help='target feature in S or MS task')\n", "    parser.add_argument('--freq', type=str, default='h', help='freq for time features encoding, options:[s:secondly, t:minutely, h:hourly, d:daily, b:business days, w:weekly, m:monthly], you can also use more detailed freq like 15min or 3h')\n", "    parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='location of model checkpoints')\n", "\n", "    # model\n", "    parser.add_argument('--num_embeds', default='[64, 5, 11]', type=str)\n", "    parser.add_argument('--num_channel', default=15, type=int) # 通道数,与上面的DataHander中的win保持一致\n", "    parser.add_argument('--num_input', default=51, type=int)\n", "    parser.add_argument('--out_channels', default='(24, 48, 1200, 1200)', type=str)\n", "    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)\n", "    parser.add_argument('--kernel_size', default=3, type=int)\n", "    parser.add_argument('--pooling', default='max', choices=['max', 'avg'], type=str)\n", "\n", "    # basic config\n", "    parser.add_argument('--task_name', type=str, default='long_term_forecast', help='task name, options:[long_term_forecast, short_term_forecast, imputation, classification, anomaly_detection]')\n", "    parser.add_argument('--is_training', type=int, default=1, help='status')\n", "    parser.add_argument('--model_id', type=str, default='test', help='model id')\n", "    parser.add_argument('--model', type=str, default='Autoformer', help='model name, options: [Autoformer, Transformer, TimesNet]')\n", "\n", "    # forecasting task\n", "    parser.add_argument('--seq_len', type=int, default=15, help='input sequence length')\n", "    parser.add_argument('--label_len', type=int, default=15, help='start token length')\n", "    parser.add_argument('--pred_len', type=int, default=3, help='prediction sequence length')\n", "    parser.add_argument('--seasonal_patterns', type=str, default='Monthly', help='subset for M4')\n", "    parser.add_argument('--inverse', action='store_true', help='inverse output data')\n", "\n", "    # model define\n", "    parser.add_argument('--expand', type=int, default=2, help='expansion factor for Mamba')\n", "    parser.add_argument('--d_conv', type=int, default=4, help='conv kernel size for Mamba')\n", "    parser.add_argument('--top_k', type=int, default=5, help='for TimesBlock')\n", "    parser.add_argument('--num_kernels', type=int, default=6, help='for Inception')\n", "    parser.add_argument('--enc_in', type=int, default=120, help='encoder input size')\n", "    parser.add_argument('--dec_in', type=int, default=120, help='decoder input size')\n", "    parser.add_argument('--c_out', type=int, default=120, help='output size')\n", "    parser.add_argument('--d_model', type=int, default=512, help='dimension of model')\n", "    parser.add_argument('--n_heads', type=int, default=8, help='num of heads')\n", "    parser.add_argument('--e_layers', type=int, default=2, help='num of encoder layers')\n", "    parser.add_argument('--d_layers', type=int, default=1, help='num of decoder layers')\n", "    parser.add_argument('--d_ff', type=int, default=2048, help='dimension of fcn')\n", "    parser.add_argument('--moving_avg', type=int, default=25, help='window size of moving average')\n", "    parser.add_argument('--factor', type=int, default=1, help='attn factor')\n", "    parser.add_argument('--distil', action='store_false', help='whether to use distilling in encoder, using this argument means not using distilling')\n", "    parser.add_argument('--dropout', type=float, default=0.1, help='dropout')\n", "    parser.add_argument('--embed', type=str, default='fixed', help='time features encoding, options:[timeF, fixed, learned]')\n", "    parser.add_argument('--activation', type=str, default='gelu', help='activation')\n", "    parser.add_argument('--output_attention', action='store_true', help='whether to output attention in ecoder')\n", "    parser.add_argument('--channel_independence', type=int, default=1, help='0: channel dependence 1: channel independence for FreTS model')\n", "    parser.add_argument('--decomp_method', type=str, default='moving_avg', help='method of series decompsition, only support moving_avg or dft_decomp')\n", "    parser.add_argument('--use_norm', type=int, default=1, help='whether to use normalize; True 1 False 0')\n", "    parser.add_argument('--down_sampling_layers', type=int, default=0, help='num of down sampling layers')\n", "    parser.add_argument('--down_sampling_window', type=int, default=1, help='down sampling window size')\n", "    parser.add_argument('--down_sampling_method', type=str, default=None, help='down sampling method, only support avg, max, conv')\n", "    parser.add_argument('--seg_len', type=int, default=48, help='the length of segmen-wise iteration of SegRNN')\n", "\n", "    # optimization\n", "    parser.add_argument('--num_workers', type=int, default=0, help='data loader num workers')\n", "    parser.add_argument('--itr', type=int, default=1, help='experiments times')\n", "    parser.add_argument('--train_epochs', type=int, default=10, help='train epochs')\n", "    parser.add_argument('--batch_size', type=int, default=32, help='batch size of train input data')\n", "    parser.add_argument('--patience', type=int, default=3, help='early stopping patience')\n", "    parser.add_argument('--learning_rate', type=float, default=0.0001, help='optimizer learning rate')\n", "    parser.add_argument('--des', type=str, default='test', help='exp description')\n", "    parser.add_argument('--loss', type=str, default='MSE', help='loss function')\n", "    parser.add_argument('--lradj', type=str, default='type1', help='adjust learning rate')\n", "    parser.add_argument('--use_amp', action='store_true', help='use automatic mixed precision training')\n", "\n", "    # de-stationary projector params\n", "    parser.add_argument('--p_hidden_dims', type=int, nargs='+', default=[16, 16, 16, 16], help='hidden layer dimensions of projector (List)')\n", "    parser.add_argument('--p_hidden_layers', type=int, default=4, help='number of hidden layers in projector')\n", "\n", "    args = parser.parse_args(args=[])\n", "    return args"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Namespace(ds_name='10HF', ds_files=['sf.2024'], start_time='', end_time='', direct='ls', filter_win=0, is_normal=True, verbose=False, fut_codes=['IH', 'IF', 'IC', 'IM'], data_path='e:/featdata/sf2024', model_type=0, data='ETTm1', root_path='./data/ETT/', features='M', target='OT', freq='h', checkpoints='./checkpoints/', num_embeds=[64, 5, 11], num_channel=15, num_input=51, out_channels=(24, 48, 1200, 1200), ins_nums=(0, 51, 51, 17), kernel_size=3, pooling='max', task_name='long_term_forecast', is_training=1, model_id='test', model='Autoformer', seq_len=15, label_len=15, pred_len=3, seasonal_patterns='Monthly', inverse=False, expand=2, d_conv=4, top_k=5, num_kernels=6, enc_in=120, dec_in=120, c_out=120, d_model=512, n_heads=8, e_layers=2, d_layers=1, d_ff=2048, moving_avg=25, factor=1, distil=True, dropout=0.1, embed='fixed', activation='gelu', output_attention=False, channel_independence=1, decomp_method='moving_avg', use_norm=1, down_sampling_layers=0, down_sampling_window=1, down_sampling_method=None, seg_len=48, num_workers=0, itr=1, train_epochs=10, batch_size=32, patience=3, learning_rate=0.0001, des='test', loss='MSE', lradj='type1', use_amp=False, p_hidden_dims=[16, 16, 16, 16], p_hidden_layers=4, seed=9987)\n", "DataHandlerAHF init: ['sf.2024'], , \n", "DataHandler init: {'class': 'AHFDataLoader', 'module_path': 'pyqlab.data', 'kwargs': {'data_path': 'e:/featdata/sf2024', 'train_codes': ['IH', 'IF', 'IC', 'IM']}}\n", "FTSDataset model_type: 0, seq_len: 15, label_len: 15, pred_len: 0\n", "Loading sf.2024 data...\n", "Clear before: (29907, 237) (29907, 237) (29907, 237) (29907, 66)\n", "Clear after:  (29711, 237) (29711, 237) (29711, 237) (29711, 66)\n", "count    29711.000000\n", "mean         0.000051\n", "std          0.732475\n", "min         -1.000025\n", "25%         -0.727273\n", "50%         -0.049988\n", "75%          0.739990\n", "max          1.000025\n", "Name: bar_length, dtype: float64\n", "Dataset size: 29651\n", "      code                date  change  long_label  short_label  label  \\\n", "0       IC 2024-01-04 11:24:26    0.21           1            0      1   \n", "1       IC 2024-01-04 11:28:03   -0.41           0            1      0   \n", "2       IC 2024-01-04 13:06:30   -0.51           0            1      0   \n", "3       IC 2024-01-04 13:20:48   -0.33           0            1      0   \n", "4       IC 2024-01-04 13:29:38    0.36           1            0      2   \n", "...    ...                 ...     ...         ...          ...    ...   \n", "29706   IM 2024-05-30 14:03:03    0.20           1            0      1   \n", "29707   IM 2024-05-30 14:07:07   -0.26           0            1      0   \n", "29708   IM 2024-05-30 14:17:02    0.25           1            0      1   \n", "29709   IM 2024-05-30 14:23:00   -0.37           0            1      0   \n", "29710   IM 2024-05-30 14:26:35   -0.33           0            1      0   \n", "\n", "       bar_length  code_encoded  tf0  tf1  tf2  tf3  tf4  \n", "0       -0.350016            23    1    4    3   11    4  \n", "1        0.466675            23    1    4    3   11    5  \n", "2        0.616659            23    1    4    3   13    1  \n", "3        0.100016            23    1    4    3   13    4  \n", "4       -0.766683            23    1    4    3   13    5  \n", "...           ...           ...  ...  ...  ...  ...  ...  \n", "29706   -0.618164            26    5   30    3   14    0  \n", "29707   -0.600009            26    5   30    3   14    1  \n", "29708    0.399991            26    5   30    3   14    3  \n", "29709   -0.636364            26    5   30    3   14    4  \n", "29710    0.363636            26    5   30    3   14    5  \n", "\n", "[29711 rows x 13 columns]\n"]}], "source": ["args = main()\n", "dataset = get_ds(args)\n", "dataset.load_data()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["emb: [[0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]\n", " [0 1 3]]\n", "torch.<PERSON><PERSON>([15, 3]) torch.<PERSON><PERSON>([15, 119]) torch.<PERSON><PERSON>([])\n"]}], "source": ["embds, x, y = dataset[0]\n", "print(embds.shape, x.shape, y.shape)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.0900],\n", "        [ 0.1400],\n", "        [-0.1200],\n", "        [-0.1800],\n", "        [-0.1200],\n", "        [ 0.1600],\n", "        [ 0.1400],\n", "        [-0.2000],\n", "        [-0.2400],\n", "        [-0.2600],\n", "        [-0.1800],\n", "        [-0.1300],\n", "        [-0.1000],\n", "        [-0.1200],\n", "        [ 0.0600]])"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([30, 3]) torch.<PERSON><PERSON>([30, 120]) torch.<PERSON><PERSON>([30, 5]) torch.<PERSON><PERSON>([18, 120]) torch.<PERSON><PERSON>([18, 5])\n"]}], "source": ["emb, x, x_mark, y, y_mark = next(iter(dataset))\n", "print(emb.shape, x.shape, x_mark.shape, y.shape, y_mark.shape)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 0],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 1],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 2],\n", "        [1, 2, 1, 9, 3],\n", "        [1, 2, 1, 9, 3],\n", "        [1, 2, 1, 9, 3]], dtype=torch.int32)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["x_mark"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["seq_len: 30 label_len: 15 pred_len: 3\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 1 cost time: 0.3561899662017822\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 2 cost time: 0.3119194507598877\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 3 cost time: 0.31101131439208984\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 4 cost time: 0.2891104221343994\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 5 cost time: 0.2999837398529053\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 6 cost time: 0.2998321056365967\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 7 cost time: 0.2999119758605957\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 8 cost time: 0.3002207279205322\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 9 cost time: 0.3000810146331787\n", "0 torch.<PERSON><PERSON>([32, 30, 3]) torch.<PERSON><PERSON>([32, 30, 120]) torch.<PERSON><PERSON>([32, 30, 5]) torch.<PERSON><PERSON>([32, 18, 120]) torch.<PERSON><PERSON>([32, 18, 5])\n", "Epoch: 10 cost time: 0.30164098739624023\n"]}], "source": ["trainer(args, dataset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}