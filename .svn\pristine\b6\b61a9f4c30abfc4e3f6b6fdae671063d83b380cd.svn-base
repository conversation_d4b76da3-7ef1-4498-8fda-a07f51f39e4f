{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 盘口数据特征\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- tick数据量太大，如果保存所有数据，没有足够的存储空间\n", "    - 只保存主连合约\n", "    - 保存文件格式除考虑支持压缩\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import date, datetime\n", "from pyqlab.const import MAIN_FUT_MARKET_CODES, SF_FUT_CODES\n", "data_path = 'f:/hqdata'"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["def is_exist_folder(path: str, sub_folder: str):\n", "    return sub_folder in os.listdir(path) \n", "    \n", "def is_exist_file(filename: str):\n", "    return os.path.isfile(filename)\n", "\n", "def get_dir_list(dir_path):\n", "    # Get a list of the content inside the directory\n", "    files = os.listdir(dir_path)\n", "\n", "    # Print out the list of files\n", "    dir_list = []\n", "    for file in files:\n", "        dir_list.append(file)\n", "    return dir_list    \n", "\n", "def year_month_segment(year: int):\n", "    seg = []\n", "    for month in range(1, 13):\n", "        first_day = datetime.date(year, month, 1)\n", "        if month == 12:\n", "            last_day = datetime.date(year+1, 1, 1) - datetime.timedelta(days=1)\n", "        else:\n", "            last_day = datetime.date(year, month+1, 1) - datetime.timedelta(days=1)\n", "        seg.append((first_day.strftime('%Y%m%d'), last_day.strftime('%Y%m%d')))\n", "        # print(f\"Month {month}: First day: {first_day.strftime('%Y%m%d')}, Last day: {last_day.strftime('%Y%m%d')}\")\n", "    return seg\n", "\n", "def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def read_csv_file(fname, code, market):\n", "    # columns=[\n", "    #     \"交易日\",\"合约代码\",\"交易所代码\",\"合约在交易所的代码\",\n", "    #     \"最新价\",\"上次结算价\",\"昨收盘\",\"昨持仓量\",\"今开盘\",\"最高价\",\n", "    #     \"最低价\",\"数量\",\"成交金额\",\"持仓量\",\"今收盘\",\"本次结算价\",\"涨停板价\",\n", "    #     \"跌停板价\",\"昨虚实度\",\"今虚实度\",\"最后修改时间\",\"最后修改毫秒\",\n", "    #     \"申买价一\",\"申买量一\",\"申卖价一\",\"申卖量一\",\"申买价二\",\"申买量二\",\"申卖价二\",\"申卖量二\",\n", "    #     \"申买价三\",\"申买量三\",\"申卖价三\",\"申卖量三\",\"申买价四\",\"申买量四\",\"申卖价四\",\"申卖量四\",\n", "    #     \"申买价五\",\"申买量五\",\"申卖价五\",\"申卖量五\",\"当日均价\",\"业务日期\"\n", "    # ]\n", "    #read csv file \n", "    df = pd.read_csv(fname, encoding='gbk', header='infer')\n", "    df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]\n", "    df = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'}).reset_index()\n", "    df['交易时间'] = df.apply(combine_datetime, axis=1)\n", "    df = df[['合约代码', '交易时间', '最新价', '数量']]\n", "    df = df.rename(columns={'合约代码': 'code', '交易时间': 'datetime', '最新价': 'price', '数量':'volume'})\n", "    df = df.loc[df['volume']>0]\n", "    df['code'] = f'{code}9999.{market}'\n", "    return df\n", "\n", "def test_signle_code():\n", "    # 加载主力日期切换表\n", "    # print(maintb)\n", "    # 起止日期间所有日期列表\n", "    start = '20230101'\n", "    end = '20230131'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td < td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = 'f:/hqdata/raw/2023' \n", "    # 有交易数据的日期列表\n", "    market = 'ZC' # 'SC', 'DC'\n", "    code='SA' # SA\n", "    dfs = pd.DataFrame()\n", "    for day in all_day:\n", "        # ag主力连续_20230213\n", "        fname = f'{directory}/{code}主力连续_{day}.csv'\n", "        if not is_exist_file(fname):\n", "            continue\n", "        print(f'read csv file: {fname}')\n", "        df = read_csv_file(fname)\n", "        dfs = pd.concat([dfs, df])\n", "        print(dfs.shape)\n", "    # 将按年汇总的tick数据写入文件\n", "    # print(f'{code}: {dfs.shape}')\n", "    # dfs.to_parquet(f'e:/hqdata/tick/{code}2020.parquet')\n", "    print(dfs.tail())"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["def etl_fut_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):\n", "    # 起止日期间所有日期列表\n", "    # start = '20230201'\n", "    # end = '20230231'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td <= td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = f'{data_path}/raw/{start[0:4]}' \n", "    cnt = 0\n", "    markets=['SC', 'ZC', 'DC'] # \n", "    dfs = pd.DataFrame()\n", "    for mk, codes in MAIN_FUT_MARKET_CODES.items():\n", "        for code in codes:\n", "            for day in all_day:\n", "                # 获取当日主力期货的代码名即文件名\n", "                if is_sec:\n", "                    fname = f'{directory}/{code}次主力连续_{day}.csv'\n", "                else:\n", "                    fname = f'{directory}/{code}主力连续_{day}.csv'\n", "                if not is_exist_file(fname):\n", "                    continue\n", "                df = read_csv_file(fname, code, mk)\n", "                dfs = pd.concat([dfs, df])\n", "            # 将按年汇总的tick数据写入文件\n", "            print(f'{cnt} {code}: {dfs.shape}')\n", "            cnt = cnt + 1\n", "    dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    if is_sec:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.sec.parquet')\n", "    else:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.parquet')\n", "    return dfs\n", "\n", "def etl_sf_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td <= td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = f'{data_path}/raw/{start[0:4]}sf' \n", "    cnt = 0\n", "    dfs = pd.DataFrame()\n", "    for code in SF_FUT_CODES:\n", "        for day in all_day:\n", "            # 获取当日主力期货的代码名即文件名\n", "            if is_sec:\n", "                fname = f'{directory}/{code}次主力连续_{day}.csv'\n", "            else:\n", "                fname = f'{directory}/{code}主力连续_{day}.csv'\n", "            if not is_exist_file(fname):\n", "                continue\n", "            df = read_csv_file(fname, code, 'SF')\n", "            dfs = pd.concat([dfs, df])\n", "        # 将按年汇总的tick数据写入文件\n", "        print(f'{cnt} {code}: {dfs.shape}')\n", "        cnt = cnt + 1\n", "    dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    if is_sec:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.sec.parquet')\n", "    else:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.parquet')\n", "    return dfs\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all day: 31\n", "0 CU: (310719, 4)\n", "1 AL: (609664, 4)\n", "2 ZN: (904768, 4)\n", "3 PB: (1034485, 4)\n", "4 NI: (1475959, 4)\n", "5 SN: (1916372, 4)\n", "6 AU: (2298530, 4)\n", "7 AG: (2786204, 4)\n", "8 RB: (3116007, 4)\n", "9 HC: (3425972, 4)\n", "10 BU: (3753856, 4)\n", "11 RU: (3977878, 4)\n", "12 FU: (4206830, 4)\n", "13 SP: (4521278, 4)\n", "14 WR: (4522303, 4)\n", "15 SC: (5013656, 4)\n", "16 NR: (5291116, 4)\n", "17 SS: (5624865, 4)\n", "18 SR: (5858586, 4)\n", "19 CF: (6073460, 4)\n", "20 ZC: (6073460, 4)\n", "21 FG: (6233630, 4)\n", "22 TA: (6510231, 4)\n", "23 MA: (6727844, 4)\n", "24 OI: (6990402, 4)\n", "25 RM: (7165136, 4)\n", "26 CY: (7191425, 4)\n", "27 WH: (7191425, 4)\n", "28 PM: (7191425, 4)\n", "29 RI: (7191425, 4)\n", "30 LR: (7191425, 4)\n", "31 JR: (7191425, 4)\n", "32 RS: (7191818, 4)\n", "33 SF: (7370041, 4)\n", "34 SM: (7530716, 4)\n", "35 AP: (7623082, 4)\n", "36 CJ: (7657952, 4)\n", "37 UR: (7741059, 4)\n", "38 SA: (8025744, 4)\n", "39 PF: (8255363, 4)\n", "40 PK: (8325081, 4)\n", "41 M: (8531969, 4)\n", "42 Y: (8725068, 4)\n", "43 A: (8830875, 4)\n", "44 B: (8996615, 4)\n", "45 P: (9247334, 4)\n", "46 J: (9348255, 4)\n", "47 JM: (9462020, 4)\n", "48 I: (9693857, 4)\n", "49 RR: (9725718, 4)\n", "50 C: (9910664, 4)\n", "51 CS: (10087028, 4)\n", "52 JD: (10168567, 4)\n", "53 BB: (10168567, 4)\n", "54 FB: (10173690, 4)\n", "55 L: (10427143, 4)\n", "56 V: (10712442, 4)\n", "57 PP: (10984696, 4)\n", "58 EG: (11206455, 4)\n", "59 EB: (11468395, 4)\n", "60 PG: (11694488, 4)\n", "61 LH: (11784014, 4)\n", "(11784014, 4)\n", "all day: 31\n", "0 IH: (203075, 4)\n", "1 IF: (423806, 4)\n", "2 IC: (635450, 4)\n", "3 IM: (832641, 4)\n", "(832641, 4)\n", "all day: 28\n", "0 CU: (397594, 4)\n", "1 AL: (746086, 4)\n", "2 ZN: (1110909, 4)\n", "3 PB: (1268358, 4)\n", "4 NI: (1809097, 4)\n", "5 SN: (2357153, 4)\n", "6 AU: (2831862, 4)\n", "7 AG: (3380102, 4)\n", "8 RB: (3789159, 4)\n", "9 HC: (4193829, 4)\n", "10 BU: (4606202, 4)\n", "11 RU: (4894284, 4)\n", "12 FU: (5216686, 4)\n", "13 SP: (5628681, 4)\n", "14 WR: (5630123, 4)\n", "15 SC: (6254495, 4)\n", "16 NR: (6492897, 4)\n", "17 SS: (6794991, 4)\n", "18 SR: (7065699, 4)\n", "19 CF: (7352258, 4)\n", "20 ZC: (7352258, 4)\n", "21 FG: (7592815, 4)\n", "22 TA: (7922300, 4)\n", "23 MA: (8154053, 4)\n", "24 OI: (8429887, 4)\n", "25 RM: (8665179, 4)\n", "26 CY: (8718372, 4)\n", "27 WH: (8718372, 4)\n", "28 PM: (8718372, 4)\n", "29 RI: (8718372, 4)\n", "30 LR: (8718372, 4)\n", "31 JR: (8718372, 4)\n", "32 RS: (8719313, 4)\n", "33 SF: (8911037, 4)\n", "34 SM: (9081881, 4)\n", "35 AP: (9192204, 4)\n", "36 CJ: (9236796, 4)\n", "37 UR: (9338371, 4)\n", "38 SA: (9719624, 4)\n", "39 PF: (10015238, 4)\n", "40 PK: (10145597, 4)\n", "41 M: (10431891, 4)\n", "42 Y: (10700169, 4)\n", "43 A: (10827982, 4)\n", "44 B: (11010294, 4)\n", "45 P: (11285280, 4)\n", "46 J: (11398906, 4)\n", "47 JM: (11543233, 4)\n", "48 I: (11842675, 4)\n", "49 RR: (11883421, 4)\n", "50 C: (12096120, 4)\n", "51 CS: (12280763, 4)\n", "52 JD: (12373831, 4)\n", "53 BB: (12373831, 4)\n", "54 FB: (12381006, 4)\n", "55 L: (12711628, 4)\n", "56 V: (13046343, 4)\n", "57 PP: (13396081, 4)\n", "58 EG: (13700567, 4)\n", "59 EB: (14003631, 4)\n", "60 PG: (14284411, 4)\n", "61 LH: (14382013, 4)\n", "(14382013, 4)\n", "all day: 28\n", "0 IH: (246524, 4)\n", "1 IF: (508860, 4)\n", "2 IC: (769510, 4)\n", "3 IM: (1022941, 4)\n", "(1022941, 4)\n", "all day: 31\n", "0 CU: (439824, 4)\n", "1 AL: (808786, 4)\n", "2 ZN: (1201468, 4)\n", "3 PB: (1333873, 4)\n", "4 NI: (1874110, 4)\n", "5 SN: (2437594, 4)\n", "6 AU: (3093740, 4)\n", "7 AG: (3741756, 4)\n", "8 RB: (4191841, 4)\n", "9 HC: (4576370, 4)\n", "10 BU: (5029511, 4)\n", "11 RU: (5383168, 4)\n", "12 FU: (5802357, 4)\n", "13 SP: (6224790, 4)\n", "14 WR: (6225889, 4)\n", "15 SC: (6918409, 4)\n", "16 NR: (7303185, 4)\n", "17 SS: (7847620, 4)\n", "18 SR: (8203719, 4)\n", "19 CF: (8556451, 4)\n", "20 ZC: (8556451, 4)\n", "21 FG: (8913534, 4)\n", "22 TA: (9272901, 4)\n", "23 MA: (9549392, 4)\n", "24 OI: (9923469, 4)\n", "25 RM: (10188854, 4)\n", "26 CY: (10252829, 4)\n", "27 WH: (10252829, 4)\n", "28 PM: (10252829, 4)\n", "29 RI: (10252829, 4)\n", "30 LR: (10252829, 4)\n", "31 JR: (10252829, 4)\n", "32 RS: (10253930, 4)\n", "33 SF: (10446723, 4)\n", "34 SM: (10623394, 4)\n", "35 AP: (10775421, 4)\n", "36 CJ: (10828999, 4)\n", "37 UR: (10981069, 4)\n", "38 SA: (11395844, 4)\n", "39 PF: (11734093, 4)\n", "40 PK: (11877562, 4)\n", "41 M: (12241709, 4)\n", "42 Y: (12603639, 4)\n", "43 A: (12756754, 4)\n", "44 B: (12971973, 4)\n", "45 P: (13334419, 4)\n", "46 J: (13508900, 4)\n", "47 JM: (13698930, 4)\n", "48 I: (14078162, 4)\n", "49 RR: (14124340, 4)\n", "50 C: (14367418, 4)\n", "51 CS: (14580714, 4)\n", "52 JD: (14699959, 4)\n", "53 BB: (14699959, 4)\n", "54 FB: (14701924, 4)\n", "55 L: (15036209, 4)\n", "56 V: (15418476, 4)\n", "57 PP: (15776048, 4)\n", "58 EG: (16089254, 4)\n", "59 EB: (16413534, 4)\n", "60 PG: (16681004, 4)\n", "61 LH: (16790792, 4)\n", "(16790792, 4)\n", "all day: 31\n", "0 IH: (257303, 4)\n", "1 IF: (539749, 4)\n", "2 IC: (815978, 4)\n", "3 IM: (1086236, 4)\n", "(1086236, 4)\n"]}], "source": ["dt_seg=[('20230101', '20230131'), ('20230201', '20230228'), ('20230301', '20230331')]\n", "for seg in dt_seg:\n", "    second = True\n", "    df = etl_fut_tick_data(start=seg[0], end=seg[1], data_path=data_path, is_sec=second)\n", "    print(df.shape)\n", "    df = etl_sf_tick_data(start=seg[0], end=seg[1], data_path=data_path, is_sec=second)\n", "    print(df.shape)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["del df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Clearn"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import datetime\n", "codes = {\n", "    'SC': ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG', 'RB', 'HC', 'BU', 'RU', 'FU', 'SP', 'WR', 'SC', 'NR', 'SS'],\n", "    'ZC': ['SR', '<PERSON>', 'Z<PERSON>', 'F<PERSON>', 'T<PERSON>', 'MA', 'O<PERSON>', 'R<PERSON>', 'C<PERSON>', 'WH', 'PM', 'R<PERSON>', 'L<PERSON>', '<PERSON>', 'RS', 'SF', 'SM', 'AP', 'CJ', 'UR', 'SA', 'PF', 'PK'], \n", "    'DC': ['M', 'Y', 'A', 'B', 'P', 'J', 'J<PERSON>', 'I', 'RR', 'C', 'CS', 'JD', 'BB', 'FB', 'L', 'V', 'PP', 'EG', 'EB', 'PG', 'LH']\n", "}\n", "sfcodes = ['IH', 'IF', 'IC', 'IM']\n", "data_path = 'f:/hqdata/tick'"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}\", \"%Y%m%d %H:%M:%S %f\")\n", "    # return datetime.strptime(f\"{row['TradingDay']} {row['UpdateTime']} {row['UpdateMillisec']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def load_clearn_tick_data(code, start, end):\n", "    # 读取tick数据parquet文件\n", "    # filename = f'{self.data_path}/{start//10000}/sf/{code}{start//10000}.parquet'\n", "    filename = f'{data_path}/{start//10000}/{code}{start//10000}.parquet'\n", "    dt0 = datetime.strptime(str(start), '%Y%m%d')\n", "    dt1 = datetime.strptime(str(end), '%Y%m%d')\n", "    if os.path.isfile(filename):\n", "        df = pd.read_parquet(filename)\n", "        if start > 20230000:\n", "            # df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]\n", "            # df[['最新价', '数量']] = df[['最新价', '数量']].astype(float)\n", "            grouped = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'})\n", "            df = grouped.reset_index()\n", "            df['datetime'] = df.apply(combine_datetime, axis=1)\n", "            df = df[['合约代码', 'datetime', '最新价', '数量']]\n", "            df.to_parquet(filename, engine='fastparquet')\n", "            df = df.loc[(df['datetime'] >= dt0) & (df['datetime'] <= dt1)]\n", "        return df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "def load_all_combine_tick_data(start, end):\n", "    dfs = pd.DataFrame()\n", "    for mkt, codess in codes.items():\n", "        for code in codess:\n", "            df = load_clearn_tick_data(code, start, end)\n", "            if df.shape[0] > 0:\n", "                print(code, df.shape)\n", "                # df['label'] = f'{code}9999.{mkt}'\n", "                # dfs = pd.concat([dfs, df])\n", "    # if dfs.shape[0] > 0:\n", "    #     # dfs['datetime'] = dfs.apply(self.combine_datetime, axis=1)\n", "    #     dfs = dfs.sort_values(by='datetime', ascending=True)\n", "    return dfs"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CU (680899, 4)\n", "AL (679759, 4)\n", "ZN (672462, 4)\n", "PB (411998, 4)\n", "NI (822463, 4)\n", "SN (826244, 4)\n", "AU (818763, 4)\n", "AG (819969, 4)\n", "RB (709174, 4)\n", "HC (690167, 4)\n", "BU (698748, 4)\n", "RU (693135, 4)\n", "FU (709034, 4)\n", "SP (646093, 4)\n", "WR (72531, 4)\n", "SC (775300, 4)\n", "NR (608981, 4)\n", "SS (615511, 4)\n", "SR (583868, 4)\n", "CF (664177, 4)\n", "ZC (1, 4)\n", "FG (701675, 4)\n", "TA (704306, 4)\n", "MA (705622, 4)\n", "OI (692186, 4)\n", "RM (681946, 4)\n", "CY (149830, 4)\n", "WH (1066, 4)\n", "JR (1, 4)\n", "RS (6557, 4)\n", "SF (417644, 4)\n", "SM (390364, 4)\n", "AP (417980, 4)\n", "CJ (201773, 4)\n", "UR (417344, 4)\n", "SA (704999, 4)\n", "PF (588404, 4)\n", "PK (351991, 4)\n", "M (680033, 4)\n", "Y (687724, 4)\n", "B (431890, 4)\n", "P (702260, 4)\n", "J (426905, 4)\n", "JM (532149, 4)\n", "RR (89656, 4)\n", "C (562226, 4)\n", "CS (502043, 4)\n", "JD (326201, 4)\n", "FB (41106, 4)\n", "L (662545, 4)\n", "V (705831, 4)\n", "PP (674742, 4)\n", "EG (665628, 4)\n", "EB (613396, 4)\n", "PG (586192, 4)\n", "LH (281257, 4)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["load_all_combine_tick_data(start=20230101, end=20230228)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_parquet(f'{data_path}/2023/A2023.parquet')\n", "df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Test"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyqlab.scripts.SimulationServer import SimulationSvr"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ss = SimulationSvr(ip=\"\", port=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = ss.load_tick_data('A', 20230101, 20230228)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        合约代码       交易日    最后修改时间 最后修改毫秒        最新价    数量       申买价一 申买量一  \\\n", "index                                                                      \n", "2      a2305  20230103  09:00:00    428  5181.0000  1054  5181.0000   18   \n", "3      a2305  20230103  09:00:00    943  5183.0000  1166  5183.0000    6   \n", "4      a2305  20230103  09:00:01    431  5190.0000  1216  5186.0000   10   \n", "5      a2305  20230103  09:00:01    921  5191.0000  1341  5192.0000    6   \n", "6      a2305  20230103  09:00:02    439  5199.0000  1495  5194.0000    2   \n", "\n", "            申卖价一 申卖量一  \n", "index                  \n", "2      5183.0000   39  \n", "3      5186.0000    1  \n", "4      5192.0000   60  \n", "5      5193.0000    4  \n", "6      5198.0000    1  \n"]}], "source": ["print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}