e:
cd e:\lab\RoboQuant\pylab;
 python -m pyqlab.models.gpt2.validate_codebook ^
 --data_file e:\lab\RoboQuant\pylab\data\futures\IC9999.SF_min5.csv ^
 --encoder_onnx_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250519\vqcb_atr_based_fut_top_min1_1024_0.0378_encoder.onnx ^
 --decoder_onnx_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250519\vqcb_atr_based_fut_top_min1_1024_0.0378_decoder.onnx ^
 --method atr_based ^
 --num_samples 20