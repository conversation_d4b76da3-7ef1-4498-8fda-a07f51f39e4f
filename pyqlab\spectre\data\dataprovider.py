# import numpy as np
import pandas as pd
import sys
# import glob
import pytz
from datetime import datetime
# import warnings
from .dataloader import DataLoader
# sys.path.append("d:/QuantLab")
# from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs
from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode

class ProviderLoader(DataLoader):
    def __init__(self):
        super().__init__("",
                         ohlcv=('open', 'high', 'low', 'close', 'volume'),
                         adjustments=None)        
        self.ds = DataSource(RunMode.passive)

    def _load(self) -> pd.DataFrame:
        length=260
        barsize=BarSize.day
        blkname='主选期货'
        blocks=self.ds.get_block_data(blkname)
        blocks=[self.ds.get_fut_lx_label(x) for x in blocks]
        dfs={}
        for symbol in blocks:
            hist=self.ds.get_history_data(symbol, length, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close,BarData.volume], barsize)
            if hist.shape[0] < length:
                print(f"{symbol} len {hist.shape[0]}")
                continue
            df=pd.DataFrame(hist, columns=["date", "open", "high", "low", "close", "volume"])
            df["date"] -= df["date"] % 8640
            df["date"] = df["date"].apply(datetime.fromtimestamp, args=(pytz.timezone('Asia/Shanghai'),)) #.apply(lambda x: x.strftime("%Y-%m-%d"))
            df.set_index(["date"], inplace=True, drop=True)
            dfs[symbol]=df

        df=pd.concat(dfs, sort=False)
        df=df.rename_axis(['asset', 'date'])
        df=df.swaplevel(0,1).sort_index(level=0)
        print(df.shape)
        df=self._format(df)
        return df