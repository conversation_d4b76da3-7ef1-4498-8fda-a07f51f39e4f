{"cells": [{"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["import sys, site\n", "# sys.path.append(\"e:/github/qlib\")\n", "from matplotlib import pyplot as plt\n", "import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config, flatten_dict\n", "from qlib.workflow import R\n", "from qlib.tests.data import GetData\n", "from qlib.data.dataset import TSDatasetH\n", "import numpy as np\n", "import pandas as pd\n", "import time\n", "from qlib.data.dataset.handler import DataHandlerLP"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["#reload\n", "site.main()\n", "# sys.path.append(\"e:/lab/RoboQuant/pylab\")\n", "import pyqlab\n", "from pyqlab.data.dataset import AFDatasetH\n"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature total: 1762\n", "Today add long count: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\pandas\\core\\frame.py:5582: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  return super().sort_index(\n"]}], "source": ["dataset = AFDatasetH(\n", "    handler={\n", "        \"class\": \"DataHandlerAF\",\n", "        \"module_path\": \"pyqlab.data.dataset.handler\",\n", "        \"kwargs\": {\n", "            \"start_time\": \"2020-01-01\",\n", "            \"end_time\": \"2020-08-01\",\n", "            \"instruments\": \"csi300\",\n", "            \"is_normal\": <PERSON>als<PERSON>,\n", "            \"data_loader\": {\n", "                \"class\": \"AFDataLoader\",\n", "                \"module_path\": \"pyqlab.data.dataset.loader\",\n", "                \"kwargs\": {\n", "                    \"direct\": \"long\",\n", "                    \"model_name\": \"MLP2\",\n", "                    \"model_path\": \"e:/lab/RoboQuant/pylab/model\",\n", "                    \"data_path\": \"e:/lab/RoboQuant/pylab/data\",\n", "                    \"portfolios\": ['00200910081133001', '00171106132928000'],\n", "                }\n", "            },\n", "        },\n", "    },\n", "    segments=[\"train\", \"valid\"],\n", ")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1409, 87) (1409, 1) (1409, 1)\n", "(353, 87) (353, 1) (353, 1)\n", "(1409, 88) (1409, 1) (1409, 1)\n", "(353, 88) (353, 1) (353, 1)\n"]}], "source": ["train_df, valid_df = dataset.prepare([\"train\", \"valid\"], col_set=[\"feature\", \"label\", \"encoded\"])\n", "\n", "print(train_df[\"feature\"].shape, train_df[\"label\"].shape, train_df[\"encoded\"].shape)\n", "print(valid_df[\"feature\"].shape, valid_df[\"label\"].shape, valid_df[\"encoded\"].shape)\n", "# print(train_df[\"encoded\"].tail())\n", "train_df1 = pd.concat([train_df[\"feature\"], train_df[\"encoded\"]], axis=1)\n", "valid_df1 = pd.concat([valid_df[\"feature\"], valid_df[\"encoded\"]], axis=1)\n", "print(train_df1.shape, train_df[\"label\"].shape, train_df[\"encoded\"].shape)\n", "print(valid_df1.shape, valid_df[\"label\"].shape, valid_df[\"encoded\"].shape)\n", "# print(train_df1.tail())\n", "# print(train_df)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["X_train = train_df1.values\n", "X_valid = valid_df1.values\n", "y_train = np.squeeze(train_df[\"label\"].values)\n", "y_valid = np.squeeze(valid_df[\"label\"].values)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["import torch\n", "# site.main()\n", "sys.path.append(\"e:/github/tabnet\")\n", "from pytorch_tabnet.tab_model import TabNetClassifier, TabNetRegressor"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["cat_idxs=[87]\n", "cat_dims=[30]\n", "tabnet_params = {\"cat_idxs\":cat_idxs,\n", "                 \"cat_dims\":cat_dims,\n", "                 \"cat_emb_dim\":1,\n", "                 \"optimizer_fn\":torch.optim.Adam,\n", "                 \"optimizer_params\":dict(lr=2e-2),\n", "                 \"scheduler_params\":{\"step_size\":30, # how to use learning rate scheduler\n", "                                 \"gamma\":0.9},\n", "                 \"scheduler_fn\":torch.optim.lr_scheduler.StepLR,\n", "                 \"mask_type\":'entmax' # \"sparsemax\"\n", "                }"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:/github/tabnet\\pytorch_tabnet\\abstract_model.py:75: UserWarning: Device used : cpu\n", "  warnings.warn(f\"Device used : {self.device}\")\n"]}], "source": ["clf = TabNetClassifier(**tabnet_params)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 0  | loss: 0.75804 | train_auc: 0.51041 | valid_auc: 0.49809 |  0:00:00s\n", "epoch 1  | loss: 0.70968 | train_auc: 0.4991  | valid_auc: 0.47412 |  0:00:01s\n", "epoch 2  | loss: 0.69915 | train_auc: 0.49708 | valid_auc: 0.51215 |  0:00:01s\n", "epoch 3  | loss: 0.69051 | train_auc: 0.52135 | valid_auc: 0.51662 |  0:00:02s\n", "epoch 4  | loss: 0.69201 | train_auc: 0.53146 | valid_auc: 0.46356 |  0:00:03s\n", "epoch 5  | loss: 0.68559 | train_auc: 0.55082 | valid_auc: 0.49686 |  0:00:03s\n", "epoch 6  | loss: 0.68646 | train_auc: 0.55362 | valid_auc: 0.56861 |  0:00:04s\n", "epoch 7  | loss: 0.68496 | train_auc: 0.58538 | valid_auc: 0.59017 |  0:00:05s\n", "epoch 8  | loss: 0.68026 | train_auc: 0.60168 | valid_auc: 0.56108 |  0:00:05s\n", "epoch 9  | loss: 0.67837 | train_auc: 0.59761 | valid_auc: 0.54712 |  0:00:06s\n", "epoch 10 | loss: 0.69274 | train_auc: 0.60666 | valid_auc: 0.52994 |  0:00:07s\n", "epoch 11 | loss: 0.67968 | train_auc: 0.60598 | valid_auc: 0.59303 |  0:00:07s\n", "epoch 12 | loss: 0.67691 | train_auc: 0.64468 | valid_auc: 0.57958 |  0:00:08s\n", "epoch 13 | loss: 0.66857 | train_auc: 0.6478  | valid_auc: 0.54416 |  0:00:09s\n", "epoch 14 | loss: 0.6658  | train_auc: 0.67429 | valid_auc: 0.57106 |  0:00:09s\n", "epoch 15 | loss: 0.67575 | train_auc: 0.65263 | valid_auc: 0.53419 |  0:00:10s\n", "epoch 16 | loss: 0.66766 | train_auc: 0.66545 | valid_auc: 0.54982 |  0:00:11s\n", "epoch 17 | loss: 0.66459 | train_auc: 0.65377 | valid_auc: 0.5449  |  0:00:11s\n", "epoch 18 | loss: 0.67104 | train_auc: 0.67295 | valid_auc: 0.59587 |  0:00:12s\n", "epoch 19 | loss: 0.65822 | train_auc: 0.68396 | valid_auc: 0.58052 |  0:00:13s\n", "epoch 20 | loss: 0.65735 | train_auc: 0.68651 | valid_auc: 0.58966 |  0:00:14s\n", "epoch 21 | loss: 0.6502  | train_auc: 0.66714 | valid_auc: 0.63174 |  0:00:14s\n", "epoch 22 | loss: 0.65985 | train_auc: 0.69949 | valid_auc: 0.61597 |  0:00:15s\n", "epoch 23 | loss: 0.65686 | train_auc: 0.67971 | valid_auc: 0.61079 |  0:00:15s\n", "epoch 24 | loss: 0.64542 | train_auc: 0.68778 | valid_auc: 0.66195 |  0:00:16s\n", "epoch 25 | loss: 0.64738 | train_auc: 0.69927 | valid_auc: 0.61266 |  0:00:17s\n", "epoch 26 | loss: 0.65975 | train_auc: 0.67826 | valid_auc: 0.62067 |  0:00:18s\n", "epoch 27 | loss: 0.6485  | train_auc: 0.71452 | valid_auc: 0.64847 |  0:00:18s\n", "epoch 28 | loss: 0.65397 | train_auc: 0.68101 | valid_auc: 0.60043 |  0:00:19s\n", "epoch 29 | loss: 0.65467 | train_auc: 0.69549 | valid_auc: 0.607   |  0:00:19s\n", "epoch 30 | loss: 0.63777 | train_auc: 0.72924 | valid_auc: 0.61128 |  0:00:20s\n", "epoch 31 | loss: 0.63833 | train_auc: 0.74399 | valid_auc: 0.61945 |  0:00:21s\n", "epoch 32 | loss: 0.62898 | train_auc: 0.7335  | valid_auc: 0.61961 |  0:00:21s\n", "epoch 33 | loss: 0.62453 | train_auc: 0.72978 | valid_auc: 0.57798 |  0:00:22s\n", "epoch 34 | loss: 0.60837 | train_auc: 0.76231 | valid_auc: 0.60613 |  0:00:22s\n", "\n", "Early stopping occurred at epoch 34 with best_epoch = 24 and best_valid_auc = 0.66195\n"]}, {"name": "stderr", "output_type": "stream", "text": ["e:/github/tabnet\\pytorch_tabnet\\callbacks.py:172: UserWarning: Best weights from best epoch are automatically used!\n", "  warnings.warn(wrn_msg)\n"]}], "source": ["clf.fit(\n", "    X_train=X_train, y_train=y_train,\n", "    eval_set=[(X_train, y_train), (X_valid, y_valid)],\n", "        eval_name=['train', 'valid'],\n", "        eval_metric=['auc'],\n", "        max_epochs=100, #patience=20,\n", "        batch_size=64, #virtual_batch_size=64,\n", "        # num_workers=0,\n", "        # weights=1,\n", "        # drop_last=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# save tabnet model\n", "saving_path_name = \"./tabnet_model_test_1\"\n", "# saved_filepath = clf.save_model(saving_path_name)\n", "torch.jit.script(clf.network).save(saving_path_name)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x18c94afa1c0>]"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAD4CAYAAADiry33AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAtd0lEQVR4nO3deXxU9bnH8c+TjZBAyMqWhIQlYZU1bKKAUAW1iktRUMHaVqUtitbaalf1Xr29dbdSKeK+gLj0ihYFq2wiW1glhCWsCUtISFiSQNbn/pHBxpBlEibMZOZ5v168yJzzm3OeOS/45szv/M7viKpijDHGe/m5uwBjjDFNy4LeGGO8nAW9McZ4OQt6Y4zxchb0xhjj5QLcXUBNoqOjNTEx0d1lGGNMs7F+/fpcVY2paZ1HBn1iYiKpqanuLsMYY5oNEdlf2zrrujHGGC9nQW+MMV7OqaAXkfEiskNEMkTkoRrWPygimxx/topIuYhEOtaFi8gHIrJdRNJFZLirP4Qxxpja1Rv0IuIPzASuBHoBk0WkV9U2qvqkqvZX1f7Aw8AyVc1zrH4e+FxVewD9gHQX1m+MMaYezpzRDwEyVHWPqpYA84AJdbSfDMwFEJEwYCTwCoCqlqjq8fOq2BhjTIM4E/SxQGaV11mOZecQkRBgPPChY1EXIAd4TUQ2isgcEQk9j3qNMcY0kDNBLzUsq23Ky2uAlVW6bQKAgcBLqjoAKATO6eMHEJG7RCRVRFJzcnKcKMsYY4wznAn6LCC+yus44FAtbSfh6Lap8t4sVV3jeP0BlcF/DlWdraopqpoSE1PjmP86lVcoM5dksHyn/ZIwxpiqnAn6dUCSiHQWkSAqw3xB9UYi0gYYBXx8dpmqHgEyRaS7Y9FYYNt5V10Dfz9h9vI9LN52pCk2b4wxzVa9d8aqapmITAcWAf7Aq6qaJiLTHOtnOZpeDyxW1cJqm7gHeMfxS2IPcIfLqq8mISqE/ceKmmrzxhjTLDk1BYKqLgQWVls2q9rr14HXa3jvJiClsQU2REJUKJsy8y/ErowxptnwqjtjE6NCOJh/mpKyCneXYowxHsOrgj4hKpQKhax8674xxpizvCroE6NCANifZ0FvjDFneVXQJ0RV3ou1P7f69WBjjPFdXhX00a2CCA3yZ5+NvDHGmO94VdCLCAlRoew/Zmf0xhhzllcFPUBitI2lN8aYqrwu6DtFhpKZX0RZuQ2xNMYY8MKgT4wKobRcOXzijLtLMcYYj+B1QX925M0+66c3xhjAC4M+Mdoxlt766Y0xBvDCoG/XOpgWAX428sYYYxy8Luj9/ISEqBAbS2+MMQ5eF/SAjaU3xpgqvDLoEx3z0ldU1PbEQ2OM8R1eGfQJUaEUl1WQfcqGWBpjjFcGfeLZIZa51k9vjDFeGfQJZ6crtn56Y4xxLuhFZLyI7BCRDBF5qIb1D4rIJsefrSJSLiKRVdb7i8hGEfnUlcXXpkObYAL9xealN8YYnAh6EfEHZgJXAr2AySLSq2obVX1SVfuran/gYWCZquZVaTIDSHdZ1fUI8PcjPiLEzuiNMQbnzuiHABmqukdVS4B5wIQ62k8G5p59ISJxwNXAnPMptKESokKsj94YY3Au6GOBzCqvsxzLziEiIcB44MMqi58DfgPUOZ2kiNwlIqkikpqTk+NEWXU7O5Ze1YZYGmN8mzNBLzUsqy09rwFWnu22EZEfAkdVdX19O1HV2aqaoqopMTExTpRVt8SoEApLysktKDnvbRljTHPmTNBnAfFVXscBh2ppO4kq3TbACOBaEdlHZZfPGBF5uxF1NlhCtOP5sdZPb4zxcc4E/TogSUQ6i0gQlWG+oHojEWkDjAI+PrtMVR9W1ThVTXS87ytVvc0lldfju7H0NueNMcbHBdTXQFXLRGQ6sAjwB15V1TQRmeZYP8vR9Hpgsap6xCl0bHhL/P3EzuiNMT6v3qAHUNWFwMJqy2ZVe/068Hod21gKLG1gfY0WFOBHx/Bgm5feGOPzvPLO2LMSbRZLY4zx7qC3eemNMcbLgz4xKpQTp0s5XmRDLI0xvsurgz7BRt4YY4x3B32izWJpjDHeHfTxkSGI2Lz0xhjf5tVBHxzoT4ewYDujN8b4NK8OenBMbmbz0htjfJgPBL3NS2+M8W0+EPSh5BaUcOpMqbtLMcYYt/D6oP/PyBvrvjHG+CavD/qzY+kt6I0xvsoHgr7yjH6f9dMbY3yU1wd9aIsAYlq3sAuyxhif5fVBD5X99DYNgjHGV/lE0CdEhXLAgt4Y46N8IugTo0I4cvIMp0vK3V2KMcZccE4FvYiMF5EdIpIhIg/VsP5BEdnk+LNVRMpFJFJE4kVkiYiki0iaiMxw/Ueo39mRNwfsDlljjA+qN+hFxB+YCVwJ9AImi0ivqm1U9UlV7a+q/YGHgWWqmgeUAQ+oak9gGPDL6u+9EGzkjTHGlzlzRj8EyFDVPapaAswDJtTRfjIwF0BVD6vqBsfPp4B0IPb8Sm64hMizY+kt6I0xvseZoI8FMqu8zqKWsBaREGA88GEN6xKBAcCaWt57l4ikikhqTk6OE2U5r01IIBEhgTbyxhjjk5wJeqlhmdbS9hpgpaPb5j8bEGlFZfjfp6ona3qjqs5W1RRVTYmJiXGirIZJsAeFG2N8lDNBnwXEV3kdBxyqpe0kHN02Z4lIIJUh/46qftSYIl0hMSrEHkBijPFJzgT9OiBJRDqLSBCVYb6geiMRaQOMAj6uskyAV4B0VX3GNSU3TkJUKIdPnKa4zIZYGmN8S71Br6plwHRgEZUXU+erapqITBORaVWaXg8sVtWq/SMjgCnAmCrDL69yYf1OS4wOoUIhK/+0O3ZvjDFuE+BMI1VdCCystmxWtdevA69XW/Y1NffxX3D/mcWykK4xrdxcjTHGXDg+cWcsQKIj6K2f3hjja3wm6CNCAmndIsBG3hhjfI7PBL2IkBBts1gaY3yPzwQ92Fh6Y4xv8qmgT4wKISv/NGXlFe4uxRhjLhifCvqEqFDKKpRDx8+4uxRjjLlgfCrovxt5Y903xhgf4mNBXzld8d5cC3pjjO/wqaCPad2CxKgQXlu5lzOlNhWCMcY3+FTQiwiPX38R+44V8dy/d7m7HGOMuSB8KugBRnSL5qaUOF5esYetB0+4uxxjjGlyPhf0AL+/qheRoUH89sMtNtTSGOP1fDLo24QE8ti1vUk7dJI5X+91dznGGNOkfDLoAa68qAPjerfj2S922igcY4xX89mgB3hsQh+CAvx4+KMtqNb2dERjjGnefDro24UF87urerJ6Tx7z1mXW/wZjjGmGfDroASYNjmdYl0ieWJhO9kmbGsEY4318PuhFhL/c0JeSsgr+9PFWd5djjDEu51TQi8h4EdkhIhki8lAN6x+s8kzYrSJSLiKRzrzXEyRGh3L/5cksSsvms28Pu7scY4xxqXqDXkT8gZnAlUAvYLKI9KraRlWfVNX+qtofeBhYpqp5zrzXU/zsks707hjGnxakcaKo1N3lGGOMyzhzRj8EyFDVPapaAswDJtTRfjIwt5HvdZsAfz/+98a+5BWW8PjCbe4uxxhjXMaZoI8Fqg5JyXIsO4eIhADjgQ8b8d67RCRVRFJzcnKcKMv1+sS24c5LuzA/NYvlO91TgzHGuJozQS81LKtt0Pk1wEpVzWvoe1V1tqqmqGpKTEyME2U1jft+kERS21bc994mDh0/7bY6jDHGVZwJ+iwgvsrrOOBQLW0n8Z9um4a+1yMEB/oza8ogSsoq+Pk7Gygus+mMjTHNmzNBvw5IEpHOIhJEZZgvqN5IRNoAo4CPG/peT9M1phVPTezH5szjPLLA+uuNMc1bvUGvqmXAdGARkA7MV9U0EZkmItOqNL0eWKyqhfW915UfoKmM79OeaaO6MnftAebbXbPGmGZMPHGOl5SUFE1NTXV3GZSVV3D7a2tZty+fD6ddzEVxbdxdkjHG1EhE1qtqSk3rfP7O2LoE+PvxwqQBRIcGMe3t9eQXlri7JGOMaTAL+npEtWrBS7cNIudUMffO20h5hed9AzLGmLpY0DuhX3w4j07ozYpduTz7xU53l+NWpeUV7M4pcHcZxpgGsKB30uQhnbg5JZ4Xl2SwOO1Ive3zC0vYlHmcUi97VOGzX+zk8meWsf3ISXeXYoxxUoC7C2hOHp3Qm22HT/LA/M18PL0VXWJaAZUXbXdkn2LjgeNsOJDPpgPH2eN4atWYHm35+60DCQ70d2fpLnHidClvrtpPhcIzi3cye2qN132MMR7Ggr4BggP9eem2gVzzt6+Z9vZ6ftCzHRsPHGdz1nGKSipvrIpuFcSAThFMTImnQpWnFu/gx6+tZc7tg2nVonkf7rdX76eguIyrL+rAv749zObM4/SLD3d3WcaYejTv5HGDuIgQXpg8gNtfXcuenEJ6dQxj4qA4BiZEMLBTBHERLRGRKu1b8qv5m7l1zhreuGMw4SFBbqy+8c6UlvPayr2MTI7hLzdexDe7c3lq8Q7e+ulQd5dmjKmHBX0jXJoUw8qHxhDeMoiWQXV3yUzoH0vLQH+mv7uRSbNX89ZPhxLTusUFqtR15qdmkltQwi9Gd6V1cCA/H92VJxZuZ82eYwztEuXu8owxdbCLsY3UoU3LekP+rCt6t+fVHw9m/7EibvrHKg42s8nSSssr+MeyPQzsFM7QzpEATB2eSNvWLXh68U57sLoxHs6C/gK5JCmat382hNyCYm6atYq9uYX1v8lDfLrlEAePn+YXo7t91y0VHOjPPWO6sXZfHst35bq5QmNMXSzoL6BBCZHMvXMYp0vLmThrVbMYolhRoby0dDfJ7Voxpkfb7627eXAn4iJa8vTiHXZWb4wHsz76C6xPbBvm3z2MW+esYdLs1bxxxxD6xrUhv6iUzLwisvJPk5lf9L2fjxWUkNS2FQMTIhgQH87AhAjahQVfkHq/2n6UndkFPHtzP/z8vv94gaAAP2aMTeLBD7aweFs243q3vyA1GWMaxiY1c5PMvCJumbOa7JPFBPoJhSXfn/c+PCSQ+IgQ4iNbEh4SxPbDJ9l68CQljhuwYsNb0r9TOAM7RTCgUzi9O4bRIsC1Y/VVlRtf+oajp4pZ+uvRBPif+wWwrLyCK55bTqCfHwtnXIq/X03PmjHGNLW6JjWzM3o3iY8M4f27L+b5L3cRHOhHfEQIcREtiY+s/Lt1cOA57ykuK2fboZNsqHJj1r+2HAYgJMif34zrztThieeceTfWmr15bDhwnP+a0LvGkIfKid/u/0Ey98zdyKdbDjGhf41PijTGuJGd0Tdz2SfPsPFAPnPXZrJsZw6XJkXz5I/60b7N+XftTH11LdsOneDr346p887eigrl6r99zemSMr741SgCa/mlYIxpOjZNsRdrFxbM+D4deP2OwTx+fR9S9+VzxbPLWLD5/J7YuPXgCZbvzOGOEZ3rnb7Bz0944PJk9h0r4sP1Wee1X2OM61nQewkR4dahCSyccSld27bi3rkbuWfuRk4UlTZqey8t203rFgFMGZ7gVPuxPdvSPz6cF77cZc/ZNcbDOBX0IjJeRHaISIaIPFRLm9EisklE0kRkWZXl9zuWbRWRuSJyYYaL+KjO0aG8f/dwHrg8mc++Pcy455azYldOg7axN7eQz749zG3DEwir4VpBTUSEX1/RnUMnzjB3zYHGlG6MaSL1Br2I+AMzgSuBXsBkEelVrU048HfgWlXtDUx0LI8F7gVSVLUP4E/lA8JNEwrw9+OesUn88xcjCG3hz5RX1vLIgjROlzh3pj17+W4C/P24Y0Rig/Y7olsUw7pE8uKS3RSVlDWicmNMU3DmjH4IkKGqe1S1BJgHTKjW5hbgI1U9AKCqR6usCwBaikgAEAKcX+excdpFcW34172XcseIRF7/Zh9XPr+cZ77Yyfr9eZTVMk9+9skzfLj+IDelxNG2dcO+fIkID47rTm5BMW98s98VH8EY4wLODK+MBTKrvM4Cqk9ZmAwEishSoDXwvKq+qaoHReQp4ABwGlisqotr2omI3AXcBdCpU6cGfQhTu+BAf/58TW/G9mjHM1/s4MWvdvHCl7sICw5gRLdoRiXHMDI5ho7hLQGYs2IP5arcPbJro/Y3KCGSy7rH8MKXu8g+eYZbh3YiqV1rV34kY0wDORP0NQ3Krj4mMwAYBIwFWgKrRGQ1kEPl2X9n4Djwvojcpqpvn7NB1dnAbKgcXunsBzDOuSQpmkuSojleVMLKjGMs35nDsp05fLa18mlZ3dq24tKkaOavy+SHfTsQHxnS6H09ccNF/O9n23l3zQFe/2YfQzpHctuwBMb1bufym7qMMfVzJuizgPgqr+M4t/slC8hV1UKgUESWA/0c6/aqag6AiHwEXAycE/TmwggPCeLqvh24um8HVJVdRwu+C/131hygvEL5+ejGnc2f1aFNS56bNIA//rCY99dn8e6aA9w7dyNRoUHcNDieW4Z0Oq9fJMaYhqn3hilH3/pOKs/WDwLrgFtUNa1Km57Ai8A4IAhYS+VF11DgVWAwlV03rwOpqvq3uvZpN0y5x5nScnILiomLcG0IV1QoKzJyeXv1fr5Mz0aBUckx3HlpF0Z0i3bpvozxVec1BYKqlonIdGARlaNmXlXVNBGZ5lg/S1XTReRzYAtQAcxR1a2OnX8AbADKgI04umeM5wkO9Hd5yEPlDVWjkmMYlRzD4ROnmbs2k3lrDzDllTW8+uPBjO7etv6NGGMazaZAMG5RVFLGjS+tIiu/iI9/OeK7B603hVe+3ktmXhF/+mEvl80DZIynsSkQjMcJCQpg9pRBBPr7ceebqZw607g7eOuzdm8e//2vbbz+zT6eWryjSfZhjKezoDduEx8Zwt9vHcj+Y0XcN28TFRWu/XZZWFzGr9/fTHxECBMHxfH3pbttLh7jkyzojVsN6xLFn6/pxZfbj/LMFztduu0nFqaTmV/EUxP78cQNF3Fx1yge/uhbUvfluXQ/xng6C3rjdrcNS2DykHheXJLBp1tcc+P02eGiP7ukM0M6RxLo78ffbx1IbERL7n5rPZl5RS7ZjzHNgQW9cTsR4dFr+5CSEMGv399M2qET57W9E0Wl/PaDLXRr24oHruj+3fLwkCBeuT2F0vIKfvrGuia7LmCMp7GgNx4hKMCPl24bRERIEHe9uZ5jBcWN3tYjn6SRU1DMMzf1O2cu/S4xrXjptkHszink3rkbKXfhdQFVZX5qJt9k5Lpsm8a4ggW98RgxrVvwjymDyC0o5ufvbKC0lonX6vL51sP8c+NBpl/Wjb5x4TW2GdEtmscm9GbJjhyeWJh+nlVXUlX+8tl2fvPBFqbP3UhBsc3eaTyHBb3xKH3jwvnfG/uydm8ej32yrUHvzS0o5vf/3Eqf2DCmj+lWZ9tbhybw44sTeeXrvcxde37z51dUKH/8eCv/WL6Hy3u1I6+whFdW7D2vbRrjShb0xuNcNyCWu0d24a3V+3n1671ODbtUVX730becOlPGMzf1d+q5tX+4uiejkmP44/9t5ZvdjetuKSuv4NcfbObt1Qe4e1QXZk8ZxLje7Xh5xR7yCksatU1jXM2C3nik34zvwejuMTz26TbGPL2U2ct3k19HcP5z40EWb8vmgSuSSXZyWuQAfz/+dssAOkeH8vO3N7A3t7BBNZaUVXDvvI18tOEgD1yezEPje3z3pK2ikjJeWprRoO0Z01Qs6I1H8vcTZk9J4flJ/Ylp3YInFm5n6P98yf3vbWL9/jyqTt1x+MRp/rwgjZSECH52aZcG7ScsOJBXbh+Mn8C1f/uaJxamc+j46Xrfd6a0nLvfSmXht0f4w9U9uWdsEiKV0ysktWvNDQPjeGPVfqe2ZUxTs7luTLOw48gp3lmzn482HKSguIwe7Vtz67AEruvfkV+8s4HUffl8NuNSEqNDG7X9jKOneP7LDBZ+exgBrunXkZ9d2pneHduc07aguIw730hl9d5jPHH9RUwecu6DcrLyixjz1DJuGBjLX27s26iajGmIuua6saA3zUphcRkLNh/i7dX7STt0kiB/P0rKK/iv6/owZVjCeW8/M6+I11buY966AxSVlHNJt2juHNmFkUnRiAgnikr58etr2ZJ1gqcn9uO6AbG1buvRT9J4c9V+Ft8/kq5NOGmbMWBBb7yQqrI56wTvrN5PYIAfj1/X57uuE1c4UVTKu2sP8NrKvRw9VUyP9q25/eJE3lq1n4yjBbwweQDj+7Svcxu5BcWM/OsSLuvelpm3DnRZbcbUxILemEYqKatgweZDvLx8DzuyTxEc6Mc/pqQwKjnGqfc/s3gHL3yVwaf3XEKf2HO7gYxxFQt6Y86TqvLN7mOEhwTW2G9fm5NnShn51yX0jQvnzZ8MacIKja+z+eiNOU8iwohu0Q0Keagc1fPL0d1YvjOHVbuPNVF1xtTNgt6YJjZleALtw4L566LteOI3aOP9nAp6ERkvIjtEJENEHqqlzWgR2SQiaSKyrMrycBH5QES2i0i6iAx3VfHGNAfBgf7M+EESGw8c59/pR91djvFB9Qa9iPgDM4ErgV7AZBHpVa1NOPB34FpV7Q1MrLL6eeBzVe0B9ANcM4uUMc3IxEFxdIkO5clF2106Y6YxznDmjH4IkKGqe1S1BJgHTKjW5hbgI1U9AKCqRwFEJAwYCbziWF6iqsddVLsxzUaAvx+/uiKZndkFfLzp4AXZ5+mSyrt3//vTbR7xoJUTRaU8sTCd7JNn3F2Kz3Em6GOBzCqvsxzLqkoGIkRkqYisF5GpjuVdgBzgNRHZKCJzRKTGWxdF5C4RSRWR1JycnAZ+DGM831V9OtC7YxjPfLGTkrKGT8HcUE8sTGdRWjavf7OPUU8u4RfvrCd1X57brhO88NUuZi/fw58+3uqW/fsyZ4K+prtQqv9LCQAGAVcD44A/ikiyY/lA4CVVHQAUAjX28avqbFVNUdWUmBjnxigb05z4+QkPjutOVv5pfvHOBmYuyWDB5kNsPJBPbkGxSwN4yfajvLV6Pz+7pDMrfnsZd43sysqMY/xo1iqum7mSjzcdbNR8/4114FgRb67aR7uwFixKy2bJDrtWcSEFONEmC4iv8joOqP5gzywgV1ULgUIRWU5lf/wKIEtV1zjafUAtQW+MLxiVHMOtQzvx2dYj/Ds9+3vrQoL8iYtoSXxECPGRIdw8OJ6eHcIavI/cgmIe/GAzPdq35tfjuhMc6M9DV/bg3rHd+HB9Fq+t3MeMeZv4y2fbmTo8kclD4gkPCXLVR6zRk4t34O8nfDDtYm5/bS2PLEhj+H1R5zwBzDSNem+YEpEAYCcwFjgIrANuUdW0Km16Ai9SeTYfBKwFJqnqVhFZAfxMVXeIyCNAqKo+WNc+7YYp4wsKisvIyi8iM+80mXlFZDp+zsovYt+xQoL8/Zh717AGjd1XVe58cz3Ld+aw4J4R9Gh/7i+Kigpl6c6jvPL1XlZmHKNloD8zbx3AmB7tXPnxvrM58zgTZq5k+mXd+PW47qzYlcOUV9bywOXJ3DM2qUn26YvqumGq3jN6VS0TkenAIsAfeFVV00RkmmP9LFVNF5HPgS1ABTBHVc92xN0DvCMiQcAe4I7z/0jGNH+tWgTQo31YjWGclV/ExFmrmPrKWuZPG+70pGjz1mXy7/Rs/nB1zxq3C5VdSGN6tGNMj3akHz7JffM28ft/buXLB6IICXLmS77zVJUnFqYTFRrE3aMqp5C+NCmGqy/qwItLMrhuQCzxkSEu3ac5l1Pj6FV1oaomq2pXVX3csWyWqs6q0uZJVe2lqn1U9bkqyzc5+t77qup1qprv8k9hjJeJiwjhnZ8NRQRum7PGqVEze3MLeeyTbYzoFsVPRnR2aj89O4Tx+PV9OHziDDOXuP5BKV9tP8qavXnM+EESrYMDv1v+hx/2xN9PeLSBj4s0jePaX9/GGJfpEtOKt346lEmzV3PrnDW8P2047cKCa2xbWl7Bfe9tIijAj6cm9sPPz/mZPFMSI7lhQCwvL9/LxEHxjZ7Tv7qy8gr+8tl2OkeHnjNnf4c2LZkxNon/+Ww7X6ZnM7Znw7qNTp0p5fCJM2SfPMMRx9/ZJ4s5cvIMR0+e4cjJM6jCgumX0L5NzcfMl9gUCMZ4sJ4dwnj9jsEcKyjmtjlran0O7d++ymBz5nGeuP4iOrRp2eD9PHRlD4IC/HjsU9edYb+/PotdRwv47fjuNT7D944RnenWthWPfJLGmdJyp7ZZUaE888VO+j26mCueXc6UV9by4AdbeGrxTj7ZcogDx4poExLEiG7RHD1VzLx15/fgd29hZ/TGeLgBnSKYc/tgfvzaWqa+uoZ37xxGWJVukPX785m5JIMbBsZydd8OjdpH27BgZoxN4vGF6Y06w66uqKSMZ77YyaCECMb1rnne/qAAPx67tje3zFnDS0t3c//lyXVu89SZUu5/bxP/Tj/Kdf07MqZnO9qHBdM+LJi2YS3OGcGTc6qY99ZlMv2ybgQ48bB4b+bbn96YZmJ41yhm3TaIHUdO8ZPX1lFUUgZUjtz51fxNdGgTzKPX9j6vffx4RCLd2rbi0U+2OX2GXZs5K/aSc6qY313Vo84HwlzcLZpr+nXkpWW72X+s9oez78kp4LqZK1myI4dHr+3Nszf359p+HRnSOZJOUSE1DtO8dWgnDp84w9IddgOmBb0xzcRlPdry/KQBbDiQz91vredMaTn/9Unl9AbP3NT/exc7GyPQ349HrunNgbwiXl6+p9HbyTlVzD+W7WZ87/YMSoist/0fru5JoJ/wyIK0Gm8aW7L9KBNeXEl+USlv/3Qot1+c6NTTxMb2bEdM6xbMXWvdNxb0xjQjV13Ugb/+qB8rduVy40vf8F5qJj8f3ZUhnesPVGdckhTNVRe1Z+bSDA4eP92obTz/5U6Kyyr4zfjuTrVvFxbM/Zcns2RHzvdm91RVZi7J4CdvrKNTVAgLpo9geNcop+sI9PfjppQ4luw4yqFGfhZvYUFvTDPzo0FxPDahN2mHTnJRbBtmjK27b7uhfn915eS0j/+r4Rdmd+cUMHdtJrcM7USXBjwQ/faLE0lu14pHFqRxuqScopIypr+7kScX7eCavh35YNrFxEU0fLz9pMGdUCrvL/BldjHWmGZo6vBEusa0Irlda4ICXHu+Fhvekl+O7sbTX+zk6125XJIU7fR7//ez7bQM9OfeBt7xGujvx2MT+jBp9moe+zSNjQeOszP7FA9f2YO7RnZp9IPf4yNDGJkUw/x1mdw7xncvyvrmpzbGC4zoFk1M6xZNsu07R3ahU2QIj3yS5vTkZ+v25bF4WzbTRnUhulXD6xrWJYrrB8Qyd20mh46f5rU7hnD3qK6NDvmzJg/pxJGTZ1jiwxdlLeiNMecIDvTnz9f0IuNoAW98s6/e9rkFxTz+r3TahbXgp5d0afR+f391T+4YkciC6ZcwKtk1s9iO7dmWtq1b8O6a/S7ZXnNkXTfGmBqN7dmOy7rH8Ny/d3Ftv460rXJXbkWF8u3BE3y1/ShLdxxly8ETqMLTE/vRMqjxM1JGt2rBn685v2Gi1QX6+3Hz4HheXFJ5gTk2vOE3lDV3dkZvjKnVn67pTUlZ5VQGJ4pK+WTzIX41fxODH/83E2au5IWvduHvJ/zqB8l8es8l3Dgozt0l1+jmwZUzrb/no0Mt7YzeGFOrztGh3DmyMzOX7Ob/Nh2kQiEiJJBRyTFc1qMtlybFEBnatHPZu0JcRAijkmN4LzWTe8cm+dxFWQt6Y0ydfnlZN46cKCY2PJjRPdrSLy4c/wZMmuYpbhnSibveWs+X24/WOi2Dt7KgN8bUKSQogKdv6ufuMs7bmB5taRdWeaesrwW9b31/Mcb4rAB/P25OiWfZzhyn5vf3Jhb0xhifcfOQTgjwno/dKetU0IvIeBHZISIZIlLjw71FZLSIbBKRNBFZVm2dv4hsFJFPXVG0McY0Rmx4S0Z3b8v81EynbwTzBvUGvYj4AzOBK4FewGQR6VWtTTjwd+BaVe0NTKy2mRlAuisKNsaY8zF5SCeOnirmyyoTqHk7Z87ohwAZqrpHVUuAecCEam1uAT5S1QMAqvrdERSROOBqYI5rSjbGmMa7rHsM7cOCedeHxtQ7E/SxQNUOrSzHsqqSgQgRWSoi60VkapV1zwG/Aer8niQid4lIqoik5uT47pwUxpimFeC4U3bFLt+5KOtM0Nc0YLb60wECgEFUnrmPA/4oIski8kPgqKqur28nqjpbVVNUNSUmxjVzXBhjTE1uHhyPwDnPlC0rr+DoqTOkHz7JyoxcFmw+xNe7ct1TpAs5M44+C4iv8joOOFRDm1xVLQQKRWQ50A8YCFwrIlcBwUCYiLytqredf+nGGNM4HcNbcln3try5aj9r9+ZxrLCEYwUlnDhdWmP7v00ewDX9Ol7gKl3HmaBfBySJSGfgIDCJyj75qj4GXhSRACAIGAo8q6rvAw9D5agc4NcW8sYYT/CLy7qSveAM/n5Cz/ZhRLUKIjI0iKjQICJDW3z3+rcfbuF3H31L//hw4iMb/vATT1Bv0KtqmYhMBxYB/sCrqpomItMc62eparqIfA5sobIvfo6qbm3Kwo0x5nwMSojk03surbfdC5MGcNXzK5gxbyPz7x7eLOfJkZoexutuKSkpmpqa6u4yjDEGgAWbD3Hv3I3cM6YbD1zh3LNwLzQRWa+qKTWta36/mowx5gK7tl9HJg6K48UlGazafczd5TSYBb0xxjjhkWt70zkqlPvf20R+YYm7y2kQC3pjjHFCaIsAXpg8gGOFxfzmwy14Yrd3bSzojTHGSX1i2/Db8T34Yls2b69pPnfWWtAbY0wD/GREZ0Ymx/Dfn25jx5FT7i7HKRb0xhjTAH5+wtMT+9E6OIB75m7gTGm5u0uqlwW9McY0UEzrFjx9U392Zhfw3//a5u5y6mVBb4wxjTAqOYY7L+3M26sPsCjtiLvLqZMFvTHGNNKD43rQJzaMB+ZvZv66TI8diWNBb4wxjRQU4Mc/pqTQq2MYv/lwC1NfXUtWvudNfWxBb4wx5yE2vCXz7hzGf03ozfr9+Yx7djlvrdpHRYXnnN1b0BtjzHny8xOmDE9k0X0jGZgQwR8/TmPSy6vZl1vo7tIAC3pjjHGZ+MgQ3vzJEP76o76kHz7J+OeX8/LyPZS7+ezegt4YY1xIRLgpJZ5//2oUl3SL5vGF6dz40jfsynbfzVUW9MYY0wTahQXz8tQUnp/Un/3HCvnRrFUUFJe5pRYLemOMaSIiwoT+scy5fTAnTpfyz40H3VKHBb0xxjSxgZ3C6RMbxlur9rllrL1TQS8i40Vkh4hkiMhDtbQZLSKbRCRNRJY5lsWLyBIRSXcsn+HK4o0xpjkQEaYOS2RndgFr9uZd8P3XG/Qi4g/MBK4EegGTRaRXtTbhwN+Ba1W1NzDRsaoMeEBVewLDgF9Wf68xxviCa/p1pE3LQN5cte+C79uZM/ohQIaq7lHVEmAeMKFam1uAj1T1AICqHnX8fVhVNzh+PgWkA7GuKt4YY5qLlkH+3Dw4nkVp2Rw5ceaC7tuZoI8FMqu8zuLcsE4GIkRkqYisF5Gp1TciIonAAGBNI2s1xphm7bahCVSo8u7aC/vQEmeCXmpYVv1qQgAwCLgaGAf8UUSSv9uASCvgQ+A+VT1Z405E7hKRVBFJzcnJcap4Y4xpTjpFhTA6OYZ31xygpKzigu3XmaDPAuKrvI4DDtXQ5nNVLVTVXGA50A9ARAKpDPl3VPWj2naiqrNVNUVVU2JiYhryGYwxptmYenEiuQXFfH4BpzZ2JujXAUki0llEgoBJwIJqbT4GLhWRABEJAYYC6SIiwCtAuqo+48rCjTGmORqVFENCVAhvXcCLsvUGvaqWAdOBRVReTJ2vqmkiMk1EpjnapAOfA1uAtcAcVd0KjACmAGMcQy83ichVTfRZjDHG4/n5CbcNTWDdvny2HaqxJ9vlxBMnyk9JSdHU1FR3l2GMMU3ieFEJw/7nS64fEMv/3NDXJdsUkfWqmlLTOrsz1hhjLrDwkCAm9Ivl/zYe4kRRaZPvz4LeGGPcYMrwBE6XlvP++sz6G58nC3pjjHGDPrFtGJQQwdur9zf506gs6I0xxk2mDk9g37EiVmTkNul+LOiNMcZNxvdpT3SrIN78Zl+T7seC3hhj3KRFgD+Th3Tiqx1HycwrarL9WNAbY4wb3TK0E34ivL1mf5Ptw4LeGGPcqEObllzesx3vrcvkTGl5k+zDgt4YY9xs6sUJHC8q5ZPN1acRcw0LemOMcbPhXaJIatuKN1ftb5JHDVrQG2OMm4kId47sQv/4cErKXT99cYDLt2iMMabBbkqJ56aU+PobNoKd0RtjjJezoDfGGC9nQW+MMV7Ogt4YY7ycBb0xxng5C3pjjPFyFvTGGOPlLOiNMcbLeeTDwUUkB2jsVG7RQNPO4u96za3m5lYvWM0XSnOrubnVC7XXnKCqMTW9wSOD/nyISGptT0L3VM2t5uZWL1jNF0pzq7m51QuNq9m6bowxxstZ0BtjjJfzxqCf7e4CGqG51dzc6gWr+UJpbjU3t3qhETV7XR+9McaY7/PGM3pjjDFVWNAbY4yX85qgF5HxIrJDRDJE5CF31+MMEdknIt+KyCYRSXV3PTURkVdF5KiIbK2yLFJEvhCRXY6/I9xZY3W11PyIiBx0HOtNInKVO2usSkTiRWSJiKSLSJqIzHAs99jjXEfNnnycg0VkrYhsdtT8qGO5Rx7nOupt8DH2ij56EfEHdgKXA1nAOmCyqm5za2H1EJF9QIqqeuwNGyIyEigA3lTVPo5lfwXyVPUvjl+qEar6W3fWWVUtNT8CFKjqU+6srSYi0gHooKobRKQ1sB64DvgxHnqc66j5Jjz3OAsQqqoFIhIIfA3MAG7AA49zHfWOp4HH2FvO6IcAGaq6R1VLgHnABDfX5BVUdTmQV23xBOANx89vUPkf3GPUUrPHUtXDqrrB8fMpIB2IxYOPcx01eyytVOB4Gej4o3joca6j3gbzlqCPBTKrvM7Cw//ROSiwWETWi8hd7i6mAdqp6mGo/A8PtHVzPc6aLiJbHF07HvH1vDoRSQQGAGtoJse5Ws3gwcdZRPxFZBNwFPhCVT36ONdSLzTwGHtL0EsNy5pDn9QIVR0IXAn80tHlYJrGS0BXoD9wGHjardXUQERaAR8C96nqSXfX44waavbo46yq5araH4gDhohIHzeXVKda6m3wMfaWoM8Cqj4+PQ445KZanKaqhxx/HwX+SWUXVHOQ7eijPdtXe9TN9dRLVbMd/2kqgJfxsGPt6IP9EHhHVT9yLPbo41xTzZ5+nM9S1ePAUir7uz36OMP3623MMfaWoF8HJIlIZxEJAiYBC9xcU51EJNRxEQsRCQWuALbW/S6PsQC43fHz7cDHbqzFKWf/Iztcjwcda8dFt1eAdFV9psoqjz3OtdXs4cc5RkTCHT+3BH4AbMdDj3Nt9TbmGHvFqBsAxxCj5wB/4FVVfdy9FdVNRLpQeRYPEAC864k1i8hcYDSVU6NmA38G/g+YD3QCDgATVdVjLn7WUvNoKr/qKrAPuPtsv6y7icglwArgW6DCsfh3VPZ5e+RxrqPmyXjuce5L5cVWfypPcuer6mMiEoUHHuc66n2LBh5jrwl6Y4wxNfOWrhtjjDG1sKA3xhgvZ0FvjDFezoLeGGO8nAW9McZ4OQt6Y4zxchb0xhjj5f4fMaChdu34RUkAAAAASUVORK5CYII=", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot losses\n", "plt.plot(clf.history['loss'])"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x18c95279280>]"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************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*****************************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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot auc\n", "plt.plot(clf.history['train_auc'])\n", "plt.plot(clf.history['valid_auc'])"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x18c952e3040>]"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAY0AAAD4CAYAAAAQP7oXAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAfk0lEQVR4nO3df4yV133n8feHO3PHc6chpGVcsfwoZEusJfGGmBFGSR17vY0Dbhq8azkFeY3XiUJwQBu7q2xxlR9NlUhtNq1qJAeEY+wQxSBnba/Jlix1q7o0ESQMMcUQm3hM3HhsNpBadWzAwMB3/3jOxQ+XO3OfucDMwP28pKt57jnnOc/3Pmn5+pzz3HsUEZiZmRUxbrQDMDOzi4eThpmZFeakYWZmhTlpmJlZYU4aZmZWWNtoB3ChTZw4MaZPnz7aYZiZXVR27tz5y4jori2/5JPG9OnT6e3tHe0wzMwuKpL+uV65p6fMzKwwJw0zMyvMScPMzApz0jAzs8KcNMzMrLBCSUPSfEn7JPVJWlmnXpJWpfrdkq5K5VMl/b2kZyXtlfSZ3Dm/LulJSc+nv+/I1d2T+ton6cO58jmSnkl1qyTp3D6+mZkNR8OkIakE3AcsAGYBiyXNqmm2AJiZXkuB1al8APjvEfHvgHnA8ty5K4G/i4iZwN+l96T6RcC7gfnA11MMpH6X5q41f7gf2MzMmlfkexpzgb6I2A8gaSOwEPhJrs1CYH1kv7O+XdIESZMi4gBwACAiXpf0LDA5nbsQuC6d/03gKeCPUvnGiDgG/ExSHzBX0ovA+IjYluJYD9wEfK+5jz60h37wM149fPxCdG1mF5nOcht3fGA6l7WXGje+xBVJGpOBl3Lv+4GrC7SZTEoYAJKmA+8DfpiKfjMlFSLigKTLc31tr9PXiXRcW34WSUvJRiRMmzZtyA83mId/9HOeP/hGU+ea2aWjuuXQrH8znmvfddYXpFtOkaRRb92gduemIdtI+jXgUeCuiPhVk9crEkdWGLEWWAvQ09PT1C5Tf3P3tc2cZmaXmOf+36+Y/1f/yBtvDox2KGNCkYXwfmBq7v0U4JWibSS1kyWMb0fEY7k2v5A0KbWZBBxs0Fd/Oh4qDjOz86qrnP239ZHjThpQLGnsAGZKmiGpTLZIvammzSZgSXqKah7wWppyEvAA8GxE/GWdc25Px7cDT+TKF0nqkDSDbMH7R2kq63VJ81K/S3LnmJldEJ3lbB3jyPGToxzJ2NBweioiBiStALYAJWBdROyVtCzVrwE2AzcCfcAR4I50+geA24BnJO1KZX8cEZuBPwMekfQJ4OfALam/vZIeIVssHwCWR0T1f607gYeATrIF8AuyCG5mVvXWSMNJAwr+ym36R35zTdma3HEAy+uc933qr0UQEf8C/MdB6r4CfKVOeS/wniIxm5mdD5e1j0Py9FSVvxFuZjYESVTaSxw+5pEGOGmYmTVU6Wjj6AmPNMBJw8ysoUrZI40qJw0zswYq5TYvhCdOGmZmDXSVS14IT5w0zMwa6CyXOOyRBuCkYWbWUFe5jaMeaQBOGmZmDXkh/C1OGmZmDVQ6Shw94aQBThpmZg11lds4fMzTU+CkYWbWUGe5xLGBU5w81dROC5cUJw0zswb88+hvcdIwM2vAP4/+FicNM7MGujqcNKqcNMzMGqik6SkvhjtpmJk1VEnTU37stmDSkDRf0j5JfZJW1qmXpFWpfrekq3J16yQdlLSn5pz3Stom6RlJ35U0PpXfKmlX7nVK0uxU91SKo1p3+Tl9ejOzAjzSeEvDpCGpBNwHLABmAYslzapptoBsL++ZwFJgda7uIWB+na6/AayMiCuBx4HPAkTEtyNidkTMJtsq9sWI2JU779ZqfUQcbPgJzczOUcUL4acVGWnMBfoiYn9EHAc2Agtr2iwE1kdmOzBB0iSAiNgKvFqn3yuAren4SeDmOm0WAxsKxGhmdsF4n/C3FEkak4GXcu/7U9lw29TaA3w0Hd8CTK3T5g84O2k8mKamPi+p7v7jkpZK6pXUe+jQoQZhmJkNrXL66SlPTxVJGvX+Ya79WmSRNrU+DiyXtBN4G3D8jA6lq4EjEZFfC7k1TWddk1631es4ItZGRE9E9HR3dzcIw8xsaJ6eekuRpNHPmaOAKcArTbQ5Q0Q8FxE3RMQcstHECzVNFlEzyoiIl9Pf14GHyabOzMwuqMvaSkhwxAvhhZLGDmCmpBmSymT/mG+qabMJWJKeopoHvBYRB4bqtPrkk6RxwOeANbm6cWRTVhtzZW2SJqbjduAjZFNcZmYX1LhxorPdGzFBgaQREQPACmAL8CzwSETslbRM0rLUbDOwH+gD7gc+XT1f0gZgG3CFpH5Jn0hViyX9FHiObFTyYO6yHwT6I2J/rqwD2CJpN7ALeDldy8zsgvM+4Zm2Io0iYjNZYsiXrckdB7B8kHMXD1J+L3DvIHVPAfNqyg4Dc4rEa2Z2vlW8Tzjgb4SbmRWSJQ2PNJw0zMwK6Opo80gDJw0zs0K8T3jGScPMrIBKucRRT085aZiZFVEpt3HY01NOGmZmRXikkXHSMDMroKvDIw1w0jAzK6SzvcSbJ05x8lSjn9W7tDlpmJkVUN0nvNV373PSMDMroLO6p0aL/2ihk4aZWQFd/nl0wEnDzKyQ0/uEt/hiuJOGmVkB1Y2YWv2xWycNM7MCqgvhrb6nhpOGmVkBne1eCAcnDTOzQqojDS+EFyBpvqR9kvokraxTL0mrUv1uSVfl6tZJOihpT80575W0TdIzkr4raXwqny7pqKRd6ZXfBnZOat+XrqfmP7qZWXHVhfBW/3n0hklDUgm4D1gAzCLbpnVWTbMFwMz0WgqsztU9BMyv0/U3gJURcSXwOPDZXN0LETE7vZblylen/qvXqtevmdl5V/Ejt0CxkcZcoC8i9kfEcWAjsLCmzUJgfWS2AxMkTQKIiK3Aq3X6vQLYmo6fBG4eKojU3/iI2Ja2l10P3FQgfjOzc9bZ7oVwKJY0JgMv5d73p7Lhtqm1B/hoOr4FmJqrmyHpaUn/IOma3DX6i1xD0lJJvZJ6Dx061CAMM7PGxo0Tne0lL4QXaFNv3aD2F7uKtKn1cWC5pJ3A24DjqfwAMC0i3gf8IfBwWu8ofI2IWBsRPRHR093d3SAMM7NiujpKHGnx355qK9CmnzNHAVOAV5poc4aIeA64AUDSu4DfS+XHgGPpeKekF4B3pWtMGc41zMzOp0q5zSONAm12ADMlzZBUBhYBm2rabAKWpKeo5gGvRcSBoTqVdHn6Ow74HLAmve9Oi+9IeifZgvf+1N/rkualp6aWAE8U/aBmZueqUi55IbxRg4gYAFYAW4BngUciYq+kZZKqTzZtBvYDfcD9wKer50vaAGwDrpDUL+kTqWqxpJ8Cz5GNGB5M5R8Edkv6J+B/AcsiorqQfifZU1d9wAvA95r72GZmw+ekAcoeRLp09fT0RG9v72iHYWaXgP/yjR9y+PgAj3/6A6MdygUnaWdE9NSW+xvhZmYFeZ9wJw0zs8K8T7iThplZYZ0eaThpmJkV1VUucfiYk4aZmRXQWW7j6ImTnDp1aT9ANBQnDTOzgqr7hB9t4W+FO2mYmRVU6fA+4U4aZmYFVdq9T7iThplZQaf3CW/hxXAnDTOzgjrT7n1HT3h6yszMGqguhHukYWZmDXmfcCcNM7PCvE+4k4aZWWGVDu8T7qRhZlZQdXrqqKenhiZpvqR9kvokraxTL0mrUv1uSVfl6tZJOihpT80575W0TdIzkr6b9gFH0ock7UzlOyVdnzvnqRTHrvS6vPmPbmY2PJ3tXghvmDTS1qv3AQuAWWQ77s2qabaAbFvWmcBSYHWu7iFgfp2uvwGsjIgrgceBz6byXwK/n8pvB75Vc96tETE7vQ42it/M7HwpjROd7SUvhDcwF+iLiP0RcRzYCCysabMQWB+Z7cAESZMAImIr8CpnuwLYmo6fBG5O7Z+OiFdS+V7gMkkdw/lQZmYXSqtv+VokaUwGXsq9709lw21Taw/w0XR8CzC1Tpubgacj4liu7ME0NfV5SarXsaSlknol9R46dKhBGGZmxVU6nDQaqfcPc+3vAhdpU+vjwHJJO4G3AcfP6FB6N/DnwKdyxbemaatr0uu2eh1HxNqI6ImInu7u7gZhmJkVV2lv8/RUA/2cOQqYArzSRJszRMRzEXFDRMwBNgAvVOskTSFb51gSES/kznk5/X0deJhs6szMbMR4pNHYDmCmpBmSysAiYFNNm03AkvQU1TzgtYg4MFSn1SefJI0DPgesSe8nAH8N3BMRP8i1b5M0MR23Ax8hm+IyMxsxXeU2Dh/zSGNQETEArAC2AM8Cj0TEXknLJC1LzTYD+4E+4H7g09XzJW0AtgFXSOqX9IlUtVjST4HnyEYlD6byFcBvA5+vebS2A9giaTewC3g5XcvMbMR0tvhCuCIu7W0Le3p6ore3d7TDMLNLxF0bn+bHP/9Xtv6P/zDaoVxQknZGRE9tub8RbmY2DJ3ltpYeaThpmJkNQ1fZX+4zM7OCql/uO3Xq0p7aH4yThpnZMFQ6sh8tfHOgNaeonDTMzIah1Xfvc9IwMxuG0/uEt+hiuJOGmdkwnB5ptOhiuJOGmdkwdJ7e8tVJw8zMGuhKC+Gt+l0NJw0zs2GoeCHczMyKOr1P+AlPT5mZWQN+5NbMzAqrLoT7kVszM2uoOj3lR27NzKyh0jhxWfs4Pz1lZmbFVMqtu094oaQhab6kfZL6JK2sUy9Jq1L9bklX5erWSTooaU/NOe+VtE3SM5K+K2l8ru6e1Nc+SR/Olc9J7fvS9dTcxzYza16lXOKIF8Lrk1QC7gMWALPItmmdVdNsATAzvZYCq3N1DwHz63T9DWBlRFwJPA58Nl1vFtk+5O9O5309xUDqd2nuWvX6NTO7oCotvOVrkZHGXKAvIvZHxHFgI7Cwps1CYH1ktgMTJE0CiIitwKt1+r0C2JqOnwRuzvW1MSKORcTPyPYdn5v6Gx8R2yLbo3Y9cFPRD2pmdr5Uym1eCB/CZOCl3Pv+VDbcNrX2AB9Nx7cAUxv0NTkdN7yGpKWSeiX1Hjp0qEEYZmbD09XhkcZQ6q0b1G5ZVaRNrY8DyyXtBN4GHG/QV+FrRMTaiOiJiJ7u7u4GYZiZDU9ne+vuE95WoE0/b40CAKYArzTR5gwR8RxwA4CkdwG/16Cv/nRc+BpmZhdCNtLw9NRgdgAzJc2QVCZbpN5U02YTsCQ9RTUPeC0iDgzVqaTL099xwOeANbm+FknqkDSDbMH7R6m/1yXNS09NLQGeKPYxzczOHy+EDyEiBoAVwBbgWeCRiNgraZmkZanZZmA/2aL1/cCnq+dL2gBsA66Q1C/pE6lqsaSfAs+RjRgeTNfbCzwC/AT4v8DyiKj+r3Mn2VNXfcALwPea/eBmZs2qlNs4cqw1RxpFpqeIiM1kiSFftiZ3HMDyQc5dPEj5vcC9g9R9BfhKnfJe4D1FYjYzu1C6yiWOnDhJRNBqXxfzN8LNzIaps9xGBLx54tRohzLinDTMzIapq6N19wl30jAzG6bO9tb9eXQnDTOzYaruE+6RhpmZNdTK+4Q7aZiZDdPpfcI9PWVmZo2cHml4esrMzBqptPA+4U4aZmbD5IVwMzMrzCMNMzMrrLoQ7qenzMysodI40dE2riV/Ht1Jw8ysCa368+hOGmZmTWjVfcKdNMzMmtDVUfJCuJmZFdNZbuOwk0Z9kuZL2iepT9LKOvWStCrV75Z0Va5unaSDkvbUnDNb0nZJuyT1Spqbym9NZdXXKUmzU91TKY5q3eXn9OnNzJrUVS615O59DZOGpBJwH7AAmEW2TeusmmYLyPbyngksBVbn6h4C5tfp+qvAlyJiNvCF9J6I+HZEzE7ltwEvRsSu3Hm3Vusj4mCj+M3MLgQvhA9uLtAXEfsj4jiwEVhY02YhsD4y24EJkiYBRMRW4NU6/QYwPh2/nWyf8FqLgQ0FYjQzG1GVcltLPnJbZI/wycBLuff9wNUF2kwGDgzR713AFklfI0te76/T5g84O0E9KOkk8Cjw5bQ/+RkkLSUb8TBt2rQhQjAza05Xh0cag6m3a3rtP9RF2tS6E7g7IqYCdwMPnNGhdDVwJCLyayG3RsSVwDXpdVu9jiNibUT0RERPd3d3gzDMzIavs73NSWMQ/cDU3PspnD2VVKRNrduBx9Lxd8imwfIWUTM1FREvp7+vAw/XOcfMbER0dZQ4fHyAOpMdl7QiSWMHMFPSDEllsn/MN9W02QQsSU9RzQNei4ihpqYgSyrXpuPrgeerFZLGAbeQrZ9Uy9okTUzH7cBHgDOeyDIzGymd5RIRcGzg1GiHMqIarmlExICkFcAWoASsi4i9kpal+jXAZuBGoA84AtxRPV/SBuA6YKKkfuCLEfEA8EngXkltwJukNYjkg0B/ROzPlXWQrYG0pzj+Fri/qU9tZnaOuk7/aOEAl7WXRjmakVNkIZyI2EyWGPJla3LHASwf5NzFg5R/H5gzSN1TwLyassODtTczG2nVn0c/cvwkvzHKsYwkfyPczKwJ1Z9Hb7XFcCcNM7MmVDpac59wJw0zsyZU2ltz9z4nDTOzJpzeJ7zFfn/KScPMrAmn9wk/4ZGGmZk10Kr7hDtpmJk1oboQ3mo/WuikYWbWhOpCuB+5NTOzhtpK4yi3jfMjt2ZmVkyl3Hr7hDtpmJk1qavc5oVwMzMrplIucfSEp6fMzKyASrnkkYaZmRXTivuEO2mYmTWpUm69fcILJQ1J8yXtk9QnaWWdeklalep3S7oqV7dO0kFJe2rOmS1pu6RdknolzU3l0yUdTeW7JK3JnTNH0jPpOqsk1dub3MxsRFQ6Wm+f8IZJQ1IJuA9YAMwCFkuaVdNsATAzvZYCq3N1DwHz63T9VeBLETEb+EJ6X/VCRMxOr2W58tWp/+q16vVrZjYiusolT0/VMRfoi4j9EXGcbN/uhTVtFgLrI7MdmCBpEkBEbAVerdNvAOPT8dvJ9gwfVOpvfERsSzsFrgduKhC/mdkF0VkuccQL4WeZDLyUe9+fyobbptZdwP+U9BLwNeCeXN0MSU9L+gdJ1+Su0V/kGpKWpimv3kOHDjUIw8ysOV3lNg4fHyD779jWUCRp1Fs3qL1DRdrUuhO4OyKmAncDD6TyA8C0iHgf8IfAw5LGD+caEbE2Inoioqe7u7tBGGZmzekslzgVcGzg1GiHMmKKJI1+YGru/RTOnkoq0qbW7cBj6fg7ZNNgRMSxiPiXdLwTeAF4V7rGlGFew8zsgukqt96PFhZJGjuAmZJmSCoDi4BNNW02AUvSU1TzgNci4kCDfl8Brk3H1wPPA0jqTovvSHon2YL3/tTf65LmpaemlgBPFIjfzOyCqKTd+1ppMbytUYOIGJC0AtgClIB1EbFX0rJUvwbYDNwI9AFHgDuq50vaAFwHTJTUD3wxIh4APgncK6kNeJPsqSiADwJ/KmkAOAksi4jqQvqdZE9jdQLfSy8zs1FRacGRRsOkARARm8kSQ75sTe44gOWDnLt4kPLvA3PqlD8KPDrIOb3Ae4rEbGZ2oXWVW2+fcH8j3MysSZ3VfcJbaKThpGFm1qTTIw0nDTMza6QV9wl30jAza1IrLoQ7aZiZNalSrj5y66RhZmYNnB5p+OkpMzNrpL00jnJpnBfCzcysmEpHiaNeCDczsyIq7SWPNMzMrJhKR5u/3GdmZsVUyiUOe3rKzMyKqLTY7n1OGmZm56Cr3MaREx5pmJlZAa22T7iThpnZOegqt/kb4WZmVkynF8LPJmm+pH2S+iStrFMvSatS/W5JV+Xq1kk6KGlPzTmzJW2XtEtSr6S5qfxDknZKeib9vT53zlMpjl3pdXnzH93M7Nx1dZQ4cvwk2V50l76GSSPt130fsACYBSyWNKum2QKyvbxnkm3bujpX9xAwv07XXwW+FBGzgS+k9wC/BH4/Iq4Ebge+VXPerRExO70ONorfzOxCqpTbOHkqOH7y1GiHMiKKjDTmAn0RsT8ijgMbgYU1bRYC6yOzHZggaRJARGwFXuVsAYxPx28HXkntn46IV1L5XuAySR3D+VBmZiPlrR8tbI11jSJ7hE8GXsq97weuLtBmMnBgiH7vArZI+hpZ8np/nTY3A09HxLFc2YOSTpLtI/7lqDMmlLSUbMTDtGnThgjBzOzcVHfvO3LiJO8Y5VhGQpGRhuqU1f5DXaRNrTuBuyNiKnA38MAZHUrvBv4c+FSu+NY0bXVNet1Wr+OIWBsRPRHR093d3SAMM7PmdbbYz6MXSRr9wNTc+ymkqaRhtql1O/BYOv4O2TQYAJKmAI8DSyLihWp5RLyc/r4OPJw/x8xsNHSlLV9b5UcLiySNHcBMSTMklYFFwKaaNpuAJekpqnnAaxEx1NQUZEnl2nR8PfA8gKQJwF8D90TED6qNJbVJmpiO24GPAGc8kWVmNtLe2r2vNUYaDdc0ImJA0gpgC1AC1kXEXknLUv0aYDNwI9AHHAHuqJ4vaQNwHTBRUj/wxYh4APgkcK+kNuBN0hoEsAL4beDzkj6fym4ADpOtgbSnOP4WuP8cPruZ2TnzQngdEbGZLDHky9bkjgNYPsi5iwcp/z4wp075l4EvDxLKWe3NzEZTJbcQ3gr8jXAzs3PQavuEO2mYmZ2D04/ceiHczMwaOf3IbYsshDtpmJmdg3LbONpL8iO3ZmZWTKXcOvuEO2mYmZ2jSrnEYS+Em5lZEZVyyY/cmplZMV0dbX7k1szMiulsL3kh3MzMiunq8EK4mZkV1Er7hDtpmJmdo65yySMNMzMrplJu8yO3ZmZWTKVc8m9PmZlZMV0dbQycCo4PnBrtUC44Jw0zs3PU2d46P1pYKGlImi9pn6Q+SSvr1EvSqlS/W9JVubp1kg5K2lNzzmxJ2yXtktQrKb9H+D2pr32SPpwrnyPpmVS3SpKa+9hmZudPdZ/wVpiiapg0JJWA+4AFwCxgsaRZNc0WADPTaymwOlf3EDC/TtdfBb4UEbOBL6T3pL4XAe9O5309xUDqd2nuWvX6NTMbUZ0ttE94ke1e5wJ9EbEfQNJGYCHwk1ybhcD6tO3rdkkTJE2KiAMRsVXS9Dr9BjA+Hb8deCXX18aIOAb8TFIfMFfSi8D4iNiW4lgP3AR8r/CnNTO7ALrSnhp3PLSDy9pKDVqPnP/z336HjvMcT5GkMRl4Kfe+H7i6QJvJwIEh+r0L2CLpa2Qjnvfn+tpep68T6bi2/CySlpKNSJg2bdoQIZiZnbs5v/UObr5qCkdPjK2Rhjj/M/hFkka9q0YTbWrdCdwdEY9K+hjwAPC7Q/RV+BoRsRZYC9DT09MoDjOzczKhUuYvPvbe0Q5jRBRZCO8HpubeT+GtqaThtKl1O/BYOv4O2TTYUH31p+PhXMPMzM6jIkljBzBT0gxJZbJF6k01bTYBS9JTVPOA1yJiqKkpyP7BvzYdXw88n+trkaQOSTPIFrx/lPp7XdK89NTUEuCJAvGbmdl50nB6KiIGJK0AtgAlYF1E7JW0LNWvATYDNwJ9wBHgjur5kjYA1wETJfUDX4yIB4BPAvdKagPeJK1BpL4fIVtoHwCWR0T1ObY7yZ7G6iRbAPciuJnZCFL2wNOlq6enJ3p7e0c7DDOzi4qknRHRU1vub4SbmVlhThpmZlaYk4aZmRXmpGFmZoVd8gvhkg4B/9zk6ROBX57HcEbCxRbzxRYvOOaRcrHFfLHFC0PH/FsR0V1beMknjXMhqbfe0wNj2cUW88UWLzjmkXKxxXyxxQvNxezpKTMzK8xJw8zMCnPSGNra0Q6gCRdbzBdbvOCYR8rFFvPFFi80EbPXNMzMrDCPNMzMrDAnDTMzK8xJow5J8yXtk9QnaeVox1OEpBclPSNpl6Qx+QuNktZJOihpT67s1yU9Ken59PcdoxljrUFi/hNJL6d7vUvSjaMZY56kqZL+XtKzkvZK+kwqH7P3eYiYx/J9vkzSjyT9U4r5S6l8LN/nwWIe1n32mkYNSSXgp8CHyDZ+2gEsjoifDHniKEt7qPdExJj9cpGkDwJvkO0n/55U9lXg1Yj4s5Sg3xERfzSaceYNEvOfAG9ExNdGM7Z6JE0CJkXEjyW9DdgJ3AT8V8bofR4i5o8xdu+zgK6IeENSO/B94DPAf2bs3ufBYp7PMO6zRxpnmwv0RcT+iDgObAQWjnJMl4SI2Aq8WlO8EPhmOv4m2T8WY8YgMY9ZEXEgIn6cjl8HngUmM4bv8xAxj1mReSO9bU+vYGzf58FiHhYnjbNNBl7Kve9njP8fcBLA30jaKWnpaAczDL9Z3eUx/b18lOMpaoWk3Wn6asxMQeRJmg68D/ghF8l9rokZxvB9llSStAs4CDwZEWP+Pg8SMwzjPjtpnE11yi6GObwPRMRVwAJgeZpWsQtjNfBvgdnAAeAvRjWaOiT9GvAocFdE/Gq04ymiTsxj+j5HxMmImA1MAeZKes8oh9TQIDEP6z47aZytH5iaez+FbD/zMS0iXkl/DwKPk02zXQx+kea0q3PbB0c5noYi4hfp//lOAfczxu51mq9+FPh2RDyWisf0fa4X81i/z1UR8a/AU2RrA2P6PlflYx7ufXbSONsOYKakGZLKwCJg0yjHNCRJXWkBEUldwA3AnqHPGjM2Aben49uBJ0YxlkKq/ygk/4kxdK/TYucDwLMR8Ze5qjF7nweLeYzf525JE9JxJ/C7wHOM7ftcN+bh3mc/PVVHeuTsr4ASsC4ivjK6EQ1N0jvJRhcAbcDDYzFmSRuA68h+jvkXwBeB/w08AkwDfg7cEhFjZuF5kJivIxvKB/Ai8KnqPPZok/Q7wD8CzwCnUvEfk60RjMn7PETMixm79/nfky10l8j+4/uRiPhTSb/B2L3Pg8X8LYZxn500zMysME9PmZlZYU4aZmZWmJOGmZkV5qRhZmaFOWmYmVlhThpmZlaYk4aZmRX2/wFiFLlMDImP7gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot learning rates\n", "plt.plot(clf.history['lr'])"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([7.08967224e-03, 7.74585949e-02, 3.04804387e-05, 1.37812444e-05,\n", "       7.01331635e-06, 2.58244503e-02, 9.56429775e-07, 8.06138553e-05,\n", "       2.18523484e-04, 3.81505082e-02, 6.14442957e-05, 3.03465752e-05,\n", "       6.59701365e-06, 9.80640813e-02, 1.21455478e-02, 2.70627701e-04,\n", "       2.10645656e-06, 5.93892002e-02, 0.00000000e+00, 5.57655857e-05,\n", "       4.27236820e-06, 0.00000000e+00, 1.93714369e-04, 2.26229910e-08,\n", "       1.03399981e-06, 1.25027302e-03, 7.13133184e-06, 1.88411956e-04,\n", "       7.33143550e-04, 1.29646912e-04, 2.75760538e-02, 2.34957101e-04,\n", "       3.47452974e-02, 1.78591244e-03, 2.73058034e-05, 9.48548474e-07,\n", "       3.95493759e-02, 2.76857620e-03, 2.87751779e-04, 5.60878076e-06,\n", "       7.63905360e-02, 7.23070234e-02, 9.52197486e-02, 0.00000000e+00,\n", "       4.15781395e-03, 3.53815645e-02, 1.24892673e-03, 5.04081641e-06,\n", "       4.00722857e-04, 1.18054836e-04, 4.21350252e-03, 3.64553287e-03,\n", "       2.36693030e-04, 4.38305591e-05, 1.71959555e-04, 2.28751558e-03,\n", "       2.32060167e-02, 4.79748111e-02, 1.95627176e-07, 7.84100502e-04,\n", "       2.41496791e-02, 2.63881814e-04, 7.20692498e-04, 0.00000000e+00,\n", "       1.14257031e-05, 2.96445413e-06, 2.88829947e-04, 1.29369244e-03,\n", "       8.00768284e-04, 2.46443335e-02, 6.85497230e-05, 5.40559606e-05,\n", "       2.42066704e-07, 1.89005040e-05, 1.13286498e-06, 1.44046856e-05,\n", "       9.08565034e-03, 9.26153068e-04, 9.73327721e-06, 9.16110218e-04,\n", "       0.00000000e+00, 4.73357185e-02, 3.48353920e-04, 5.64827368e-05,\n", "       9.92222564e-06, 1.36507339e-02, 4.31421273e-02, 3.60021221e-02])"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["clf.feature_importances_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## XGB"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-logloss:0.69110\n", "[10]\tvalidation_0-logloss:0.64733\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\xgboost\\sklearn.py:1224: UserWarning: The use of label encoder in XGBClassifier is deprecated and will be removed in a future release. To remove this warning, do the following: 1) Pass option use_label_encoder=False when constructing XGBClassifier object; and 2) Encode your labels (y) as integers starting with 0, i.e. 0, 1, 2, ..., [num_class - 1].\n", "  warnings.warn(label_encoder_deprecation_msg, UserWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[20]\tvalidation_0-logloss:0.63319\n", "[30]\tvalidation_0-logloss:0.63726\n", "[40]\tvalidation_0-logloss:0.64243\n", "[50]\tvalidation_0-logloss:0.64874\n", "[60]\tvalidation_0-logloss:0.65600\n"]}, {"data": {"text/plain": ["XGBClassifier(base_score=0.5, booster='gbtree', colsample_bylevel=1,\n", "              colsample_bynode=1, colsample_bytree=1, enable_categorical=False,\n", "              gamma=0, gpu_id=-1, importance_type=None,\n", "              interaction_constraints='', learning_rate=0.1, max_delta_step=0,\n", "              max_depth=8, min_child_weight=1, missing=nan,\n", "              monotone_constraints='()', n_estimators=1000, n_jobs=-1,\n", "              nthread=8, num_parallel_tree=1, predictor='auto', random_state=0,\n", "              reg_alpha=0, reg_lambda=1, scale_pos_weight=1, seed=0,\n", "              silent=None, subsample=0.7, tree_method='exact',\n", "              validate_parameters=1, ...)"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["from xgboost import XGBClassifier\n", "\n", "clf_xgb = XGBClassifier(max_depth=8,\n", "    learning_rate=0.1,\n", "    n_estimators=1000,\n", "    verbosity=0,\n", "    silent=None,\n", "    objective='binary:logistic',\n", "    booster='gbtree',\n", "    n_jobs=-1,\n", "    nthread=None,\n", "    gamma=0,\n", "    min_child_weight=1,\n", "    max_delta_step=0,\n", "    subsample=0.7,\n", "    colsample_bytree=1,\n", "    colsample_bylevel=1,\n", "    colsample_bynode=1,\n", "    reg_alpha=0,\n", "    reg_lambda=1,\n", "    scale_pos_weight=1,\n", "    base_score=0.5,\n", "    random_state=0,\n", "    seed=None,)\n", "\n", "clf_xgb.fit(X_train, y_train,\n", "        eval_set=[(X_valid, y_valid)],\n", "        early_stopping_rounds=40,\n", "        verbose=10)"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "from sklearn.metrics import roc_auc_score"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.7042310167310168\n"]}], "source": ["preds = np.array(clf_xgb.predict_proba(X_valid))\n", "valid_auc = roc_auc_score(y_score=preds[:,1], y_true=y_valid)\n", "print(valid_auc)\n", "\n", "# preds = np.array(clf_xgb.predict_proba(X_test))\n", "# test_auc = roc_auc_score(y_score=preds[:,1], y_true=y_test)\n", "# print(test_auc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LightGBM"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from qlib.config import REG_CN\n", "from qlib.contrib.model.gbdt import LGBModel\n", "# from qlib.contrib.data.handler import Alpha158\n", "from qlib.contrib.evaluate import (\n", "    backtest as normal_backtest,\n", "    risk_analysis,\n", ")\n", "# from qlib.utils import exists_qlib_data, init_instance_by_config\n", "from qlib.workflow import R\n", "from qlib.workflow.record_temp import SignalRecord, PortAnaRecord\n", "from qlib.utils import flatten_dict\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[3120:MainThread](2021-12-23 20:42:53,772) INFO - qlib.Initialization - [config.py:393] - default_conf: client.\n", "[3120:MainThread](2021-12-23 20:42:55,800) WARNING - qlib.Initialization - [config.py:418] - redis connection failed(host=127.0.0.1 port=6379), DiskExpressionCache and DiskDatasetCache will not be used!\n", "[3120:MainThread](2021-12-23 20:42:55,816) INFO - qlib.Initialization - [__init__.py:57] - qlib successfully initialized based on client settings.\n", "[3120:MainThread](2021-12-23 20:42:55,821) INFO - qlib.Initialization - [__init__.py:59] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[3120:MainThread](2021-12-23 20:42:59,226) INFO - qlib.workflow - [expm.py:270] - No tracking URI is provided. Use the default tracking URI.\n", "[3120:MainThread](2021-12-23 20:42:59,230) INFO - qlib.workflow - [expm.py:306] - <mlflow.tracking.client.MlflowClient object at 0x000001E4194F0BB0>\n", "[3120:MainThread](2021-12-23 20:42:59,292) INFO - qlib.workflow - [exp.py:249] - Experiment 1 starts running ...\n", "[3120:MainThread](2021-12-23 20:42:59,500) INFO - qlib.workflow - [recorder.py:290] - Recorder 1fce705ad7454736945e9e4738512b4d starts running under Experiment 1 ...\n", "[3120:MainThread](2021-12-23 20:42:59,502) INFO - qlib.DatasetH - [__init__.py:80] - data_key[learn] is ignored.\n", "d:\\Anaconda3\\lib\\site-packages\\lightgbm\\engine.py:181: User<PERSON>arning: 'early_stopping_rounds' argument is deprecated and will be removed in a future release of LightGBM. Pass 'early_stopping()' callback via 'callbacks' argument instead.\n", "  _log_warning(\"'early_stopping_rounds' argument is deprecated and will be removed in a future release of LightGBM. \"\n", "d:\\Anaconda3\\lib\\site-packages\\lightgbm\\engine.py:239: UserWarning: 'verbose_eval' argument is deprecated and will be removed in a future release of LightGBM. Pass 'log_evaluation()' callback via 'callbacks' argument instead.\n", "  _log_warning(\"'verbose_eval' argument is deprecated and will be removed in a future release of LightGBM. \"\n", "d:\\Anaconda3\\lib\\site-packages\\lightgbm\\engine.py:260: UserWarning: 'evals_result' argument is deprecated and will be removed in a future release of LightGBM. Pass 'record_evaluation()' callback via 'callbacks' argument instead.\n", "  _log_warning(\"'evals_result' argument is deprecated and will be removed in a future release of LightGBM. \"\n", "[3120:MainThread](2021-12-23 20:42:59,664) INFO - qlib.timer - [log.py:113] - Time cost: 0.000s | waiting `async_log` Done\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Training until validation scores don't improve for 50 rounds\n", "[20]\ttrain's l2: 0.489116\tvalid's l2: 0.460868\n", "[40]\ttrain's l2: 0.489116\tvalid's l2: 0.460868\n", "Early stopping, best iteration is:\n", "[1]\ttrain's l2: 0.489116\tvalid's l2: 0.460868\n"]}], "source": ["###################################\n", "# train model\n", "###################################\n", "\n", "task = {\n", "    \"model\": {\n", "        \"class\": \"LGBModel\",\n", "        \"module_path\": \"qlib.contrib.model.gbdt\",\n", "        \"kwargs\": {\n", "            \"loss\": \"mse\",\n", "            \"colsample_bytree\": 0.8879,\n", "            \"learning_rate\": 0.0421,\n", "            \"subsample\": 0.8789,\n", "            \"lambda_l1\": 205.6999,\n", "            \"lambda_l2\": 580.9768,\n", "            \"max_depth\": 8,\n", "            \"num_leaves\": 210,\n", "            \"num_threads\": 20,\n", "        },\n", "    },\n", "}\n", "\n", "# model initiaiton\n", "model = init_instance_by_config(task[\"model\"])\n", "# dataset = init_instance_by_config(task[\"dataset\"])\n", "\n", "# start exp to train model\n", "with <PERSON>.start(experiment_name=\"train_model\"):\n", "    <PERSON>.log_params(**flatten_dict(task))\n", "    model.fit(dataset)\n", "    R.save_objects(trained_model=model)\n", "    rid = R.get_recorder().id\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}