import pandas as pd
import numpy as np
from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES
from pyqlab.utils.timefeatures import time_features


class Pipeline:

    def __init__(self, data_path, start_year, end_year, block_size=15, timeenc=0, is_bar=True):
        self.data_path = data_path  # d:/RoboQuant2/store/barenc/sf
        self.start_year = start_year
        self.end_year = end_year
        self.block_size = block_size
        # self.step_size = step_size
        self.code_size = len(SF_FUT_CODES + MAIN_FUT_CODES)
        self.is_sf = False
        self.is_bar = is_bar
        self.fut_codes_dict = {c: i for i, c in enumerate(sorted(SF_FUT_CODES + MAIN_FUT_CODES))}
        self.timeenc = timeenc
        
    @staticmethod
    def get_vocab():
        bar_set = []
        for i in range(-12, 13):
            for j in range(-12, 13):
                for k in range(0, 8):
                    for l in range(0, 8):
                        bar_set.append(f'{i}|{j}|{k}|{l}')
        bar_set = sorted(bar_set)
        return bar_set
    
    def _load_data(self):
        df = pd.DataFrame()
        if self.is_bar:
            for y in range(self.start_year, self.end_year + 1):
                df2 = pd.read_csv(f'{self.data_path}/bar_{y}.csv', header=0)
                df = pd.concat([df, df2], axis=0, ignore_index=True)
            df.columns = ['symbol', 'date', 'bar']
        else:
            for y in range(self.start_year, self.end_year + 1):
                df2 = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
                df = pd.concat([df, df2], axis=0, ignore_index=True)
            df.columns = ['symbol', 'date', 'change', 'entity', 'upline', 'downline']
        if "IF" in df['symbol'].values:
            self.is_sf = True

        return df
    
    def _to_bar(self, df):
        # 将列change的值限定在-10到10之间
        df.loc[df['change'] < -12, 'change'] = -12
        df.loc[df['change'] > 12, 'change'] = 12
        df.loc[df['entity'] < -12, 'entity'] = -12
        df.loc[df['entity'] > 12, 'entity'] = 12
        df.loc[df['upline'] > 7, 'upline'] = 7
        df.loc[df['downline'] > 7, 'downline'] = 7

        # 将3,4,5,6列的值转换为bar_set中的索引
        bar_set = self.get_vocab()
        df['bar'] = df['change'].astype(str) + '|' + df['entity'].astype(str) + '|' + df['upline'].astype(str) + '|' + df['downline'].astype(str)
        df['bar'] = df['bar'].apply(lambda x: bar_set.index(x))
        df['bar'] = df['bar'].astype(int)
        df.drop(columns=['change', 'entity', 'upline', 'downline'], inplace=True)
        return df
    
    def _to_time_tf(self, df):
        # 将date列由timestamp转换为东8区日期时间
        df['date'] = pd.to_datetime(df['date'], unit='s') + pd.Timedelta(hours=8)
        if self.timeenc == 0:
            df['Month'] = df.date.apply(lambda row: row.month, 1)
            df['Day'] = df.date.apply(lambda row: row.day, 1)
            df['DayOfWeek'] = df.date.apply(lambda row: row.weekday(), 1)
            df['Hour'] = df.date.apply(lambda row: row.hour, 1)
            df['Minute'] = df.date.apply(lambda row: row.minute, 1)
            df['Minute'] = df.Minute.map(lambda x: x // 5)
            # df = df.drop(['date'], axis=1).values
        elif self.timeenc == 1:
            df_stamp= time_features(pd.to_datetime(df['date'].values), freq='t').transpose(1, 0)
            df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])
            df = pd.concat([df, df_tf], axis=1)
        else:
            raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
        return df

    def _prepare_data(self):
        # 将列change的值限定在-10到10之间
        df=self._load_data()

        df['code_encoded'] = df['symbol'].apply(lambda x: self.fut_codes_dict[x])
        df.drop(columns=['symbol'], inplace=True)
        # move code_encoded to the first column
        cols = df.columns.tolist()
        cols = cols[-1:] + cols[:-1]
        df = df[cols]
        # TODO: remove this line
        # print(f"{df.shape}=============")
        # df = df.sort_values(by=['date']).reset_index(drop=True)
        # df = df.iloc[-6000:]
        # print(f"{df.shape}=============")
        df = df.sort_values(by=['code_encoded', 'date']).reset_index(drop=True)

        if not self.is_bar:
            df=self._to_bar(df)

        df=self._to_time_tf(df)
        if self.timeenc == 0:
            df = df[['code_encoded', 'bar', 'Month', 'Day', 'DayOfWeek', 'Hour', 'Minute']]
        else:
            df = df[['code_encoded', 'bar', 'MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear']]

        return df
    
    def _convert_to_bar_file(self):
        for y in range(self.start_year, self.end_year + 1):
            df = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
            df.columns = ['symbol', 'date', 'change', 'entity', 'upline', 'downline']
            df = self._to_bar(df)
            df.to_csv(f'{self.data_path}/bar_{y}.csv', index=False, header=True)
            print(f'bar_{y}.csv saved.')
    
    def get_data(self):
        df = self._prepare_data()
        print('============================')
        print(f'code_size: {self.code_size}, bar_size: {len(self.get_vocab())}')
        print(f'df shape: {df.shape}')
        print(f'df head: {df.head(3)}')
        print('============================')
        return df
        """
        # dataframe group by symbol to ndarray
        df = df.groupby('code_encoded')
        data = []
        data_mark = []
        for _, group in df:
            for i in range(0, len(group) - self.block_size, self.step_size):
                data.append(group.iloc[i:i+self.block_size+1, :2].values)
                data_mark.append(group.iloc[i:i+self.block_size+1, 2:].values)
        data = np.array(data)
        data_mark = np.array(data_mark)
        return data, data_mark
        """

if __name__ == '__main__':
    data_path = 'd:/RoboQuant2/store/barenc/fut'
    start_year = 2023
    end_year = 2024
    pipeline = Pipeline(data_path, start_year, end_year)
    pipeline._convert_to_bar_file()