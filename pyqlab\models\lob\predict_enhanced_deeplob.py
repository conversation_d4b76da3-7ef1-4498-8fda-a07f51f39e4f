"""
使用增强版DeepLOB模型进行预测
"""
import os
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import logging
import argparse
import json
import sys
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入自定义模块
from pyqlab.models.lob.enhanced_deeplob import EnhancedDeepLOBRegression
from pyqlab.data.orderbook_processor import OrderBookProcessor
from pyqlab.data.orderbook_collector import OrderBookCollector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("predict_enhanced_deeplob.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("PredictEnhancedDeepLOB")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用增强版DeepLOB模型进行预测')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, default='./data/orderbook',
                        help='原始数据路径')
    parser.add_argument('--processed_data_path', type=str, default='./data/processed_orderbook',
                        help='处理后的数据路径')
    parser.add_argument('--code', type=str, required=True,
                        help='要预测的证券代码')
    parser.add_argument('--market', type=int, required=True,
                        help='市场代码，0-深圳，1-上海，对于期货则是市场ID')
    parser.add_argument('--data_type', type=str, default='stock',
                        choices=['stock', 'future'],
                        help='数据类型，stock或future')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True,
                        help='模型文件路径')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu',
                        help='预测设备')
    
    # 预测参数
    parser.add_argument('--collect_data', action='store_true',
                        help='是否收集新数据')
    parser.add_argument('--interval', type=int, default=1,
                        help='数据收集间隔，单位为秒')
    parser.add_argument('--duration', type=int, default=60,
                        help='数据收集时长，单位为秒')
    parser.add_argument('--save_dir', type=str, default='./predictions',
                        help='预测结果保存目录')
    
    return parser.parse_args()

def load_model(model_path, device):
    """
    加载模型
    
    参数:
        model_path: 模型文件路径
        device: 设备
        
    返回:
        tuple: (model, args, code_to_id)
    """
    try:
        checkpoint = torch.load(model_path, map_location=device)
        args = checkpoint['args']
        code_to_id = checkpoint.get('code_to_id', {})
        
        # 创建模型
        model = EnhancedDeepLOBRegression(
            input_channels=args['input_channels'],
            time_steps=args['time_steps'],
            num_features=args['num_features'],
            output_size=args['output_size'],
            horizon=args['horizon'],
            dropout=args['dropout'],
            use_transformer=args['use_transformer'],
            num_transformer_layers=args['num_transformer_layers'],
            num_heads=args['num_heads'],
            head_dim=args['head_dim'],
            ff_dim=args['ff_dim'],
            lstm_hidden_size=args['lstm_hidden_size'],
            lstm_layers=args['lstm_layers'],
            use_gru=args['use_gru'],
            use_code_embedding=args['use_code_embedding'],
            num_codes=args['num_codes'],
            code_embedding_dim=args['code_embedding_dim'],
            probabilistic=args['probabilistic']
        ).to(device)
        
        # 加载模型参数
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        logger.info(f"模型已加载: {model_path}")
        
        return model, args, code_to_id
    except Exception as e:
        logger.error(f"加载模型失败: {str(e)}")
        return None, None, None

def collect_and_process_data(args):
    """
    收集和处理数据
    
    参数:
        args: 命令行参数
        
    返回:
        tuple: (X, code_id)
    """
    # 创建保存目录
    os.makedirs(args.processed_data_path, exist_ok=True)
    os.makedirs(os.path.join(args.processed_data_path, args.data_type), exist_ok=True)
    
    # 收集数据
    if args.collect_data:
        collector = OrderBookCollector(save_path=args.data_path)
        
        if args.data_type == 'stock':
            collector.collect_stock_orderbook(args.market, args.code, args.interval, args.duration)
        else:  # future
            collector.collect_future_orderbook(args.market, args.code, args.interval, args.duration)
        
        collector.disconnect()
    
    # 处理数据
    processor = OrderBookProcessor(
        data_path=args.data_path,
        save_path=args.processed_data_path,
        n_levels=5,
        time_steps=100,
        normalize=True,
        diff_features=True
    )
    
    # 获取最新的数据文件
    if args.data_type == 'stock':
        data_dir = os.path.join(args.data_path, 'stock')
    else:  # future
        data_dir = os.path.join(args.data_path, 'future')
    
    # 查找包含指定代码的最新文件
    files = [f for f in os.listdir(data_dir) if args.code in f and f.endswith('.parquet')]
    if not files:
        logger.error(f"没有找到代码为 {args.code} 的数据文件")
        return None, None
    
    # 按时间排序，选择最新的文件
    latest_file = sorted(files)[-1]
    file_path = os.path.join(data_dir, latest_file)
    
    # 处理文件
    X, _ = processor.process_file(file_path)
    
    if X is None:
        logger.error(f"处理数据失败")
        return None, None
    
    # 获取代码ID
    code_id = 0  # 默认代码ID
    
    return X, code_id

def predict(model, X, code_id, device, probabilistic=False):
    """
    使用模型进行预测
    
    参数:
        model: 模型
        X: 特征数据
        code_id: 证券代码ID
        device: 设备
        probabilistic: 是否是概率预测模型
        
    返回:
        np.ndarray: 预测结果
    """
    model.eval()
    
    # 转换为PyTorch张量
    X_tensor = torch.FloatTensor(X).to(device)
    code_id_tensor = torch.LongTensor([code_id] * len(X)).to(device)
    
    with torch.no_grad():
        # 前向传播
        if probabilistic:
            mean, log_var = model(X_tensor, code_id_tensor)
            predictions = mean.cpu().numpy()
            uncertainty = np.exp(log_var.cpu().numpy())
            return predictions, uncertainty
        else:
            outputs = model(X_tensor, code_id_tensor)
            predictions = outputs.cpu().numpy()
            return predictions, None

def plot_predictions(predictions, uncertainty, code, save_dir, horizon):
    """
    绘制预测结果
    
    参数:
        predictions: 预测值
        uncertainty: 不确定性（方差），如果为None则不绘制
        code: 证券代码
        save_dir: 保存目录
        horizon: 预测时间范围
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 绘制预测结果
    plt.figure(figsize=(12, 8))
    
    # 绘制每个时间步的预测
    for i in range(horizon):
        plt.subplot(horizon, 1, i+1)
        
        # 绘制预测值
        plt.plot(predictions[:, i, 0], label=f'Step {i+1}')
        
        # 如果有不确定性，绘制置信区间
        if uncertainty is not None:
            plt.fill_between(
                range(len(predictions)),
                predictions[:, i, 0] - 2 * np.sqrt(uncertainty[:, i, 0]),
                predictions[:, i, 0] + 2 * np.sqrt(uncertainty[:, i, 0]),
                alpha=0.2
            )
        
        plt.title(f'Prediction for Step {i+1}')
        plt.xlabel('Sample')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'{code}_predictions.png'))
    plt.close()
    
    # 绘制所有时间步的平均预测
    plt.figure(figsize=(10, 6))
    
    avg_predictions = np.mean(predictions[:, :, 0], axis=1)
    plt.plot(avg_predictions, label='Average Prediction')
    
    if uncertainty is not None:
        avg_uncertainty = np.mean(uncertainty[:, :, 0], axis=1)
        plt.fill_between(
            range(len(avg_predictions)),
            avg_predictions - 2 * np.sqrt(avg_uncertainty),
            avg_predictions + 2 * np.sqrt(avg_uncertainty),
            alpha=0.2
        )
    
    plt.title(f'Average Prediction for All Steps')
    plt.xlabel('Sample')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    plt.savefig(os.path.join(save_dir, f'{code}_average_predictions.png'))
    plt.close()

def save_predictions(predictions, uncertainty, code, save_dir):
    """
    保存预测结果
    
    参数:
        predictions: 预测值
        uncertainty: 不确定性（方差），如果为None则不保存
        code: 证券代码
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建DataFrame
    df_list = []
    
    for i in range(predictions.shape[1]):
        step_df = pd.DataFrame({
            'sample': range(len(predictions)),
            'step': i + 1,
            'prediction': predictions[:, i, 0]
        })
        
        if uncertainty is not None:
            step_df['uncertainty'] = uncertainty[:, i, 0]
        
        df_list.append(step_df)
    
    # 合并所有步骤的DataFrame
    df = pd.concat(df_list, ignore_index=True)
    
    # 保存为CSV
    df.to_csv(os.path.join(save_dir, f'{code}_predictions.csv'), index=False)
    
    logger.info(f"预测结果已保存到 {os.path.join(save_dir, f'{code}_predictions.csv')}")

def main(args):
    """
    主函数
    
    参数:
        args: 命令行参数
    """
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 加载模型
    model, model_args, code_to_id = load_model(args.model_path, args.device)
    if model is None:
        return
    
    # 收集和处理数据
    X, code_id = collect_and_process_data(args)
    if X is None:
        return
    
    # 如果使用代码嵌入，尝试从code_to_id中获取代码ID
    if model_args['use_code_embedding'] and args.code in code_to_id:
        code_id = code_to_id[args.code]
    
    # 进行预测
    predictions, uncertainty = predict(model, X, code_id, args.device, model_args['probabilistic'])
    
    # 绘制预测结果
    plot_predictions(predictions, uncertainty, args.code, args.save_dir, model_args['horizon'])
    
    # 保存预测结果
    save_predictions(predictions, uncertainty, args.code, args.save_dir)
    
    logger.info(f"预测完成")

if __name__ == "__main__":
    args = parse_args()
    main(args)
