"""
TimeSeriesModel2drV2回测系统测试脚本

该脚本用于测试回测系统的基本功能，包括模型加载、数据处理和回测执行。
"""

import os
import sys
import numpy as np
import torch
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.fintimeseries.time_series_model2dr_v2 import TimeSeriesModel2drV2
from pyqlab.models.fintimeseries.backtest_time_series_model2dr_v2 import TimeSeriesBacktester

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockDataset:
    """模拟数据集，用于测试"""
    
    def __init__(self, size=1000, seq_len=30):
        self.size = size
        self.seq_len = seq_len
        self.data = self._generate_mock_data()
    
    def _generate_mock_data(self):
        """生成模拟数据"""
        data = []
        
        # 生成模拟的时间序列数据
        np.random.seed(42)  # 确保可重复性
        
        for i in range(self.size):
            # 生成嵌入特征 (embds)
            embds = np.random.randint(0, 72, size=(self.seq_len, 3))
            
            # 生成时间序列特征 (x)
            # 模拟价格走势
            base_price = 100.0
            price_changes = np.random.normal(0, 0.02, self.seq_len)  # 2%的标准差
            prices = [base_price]
            for change in price_changes[:-1]:
                prices.append(prices[-1] * (1 + change))
            
            # 构造OHLCV数据
            x = np.zeros((self.seq_len, 51))  # 假设有51个特征
            for j in range(self.seq_len):
                # 模拟OHLCV特征
                close = prices[j]
                open_price = close * (1 + np.random.normal(0, 0.005))
                high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
                low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
                volume = np.random.lognormal(10, 1)
                
                # 填充特征向量
                x[j, 0] = open_price
                x[j, 1] = high
                x[j, 2] = low
                x[j, 3] = close
                x[j, 4] = volume
                
                # 填充其他技术指标特征
                for k in range(5, 51):
                    x[j, k] = np.random.normal(0, 1)
            
            # 生成目标值（下一期的价格变化）
            target = np.random.normal(0, 0.01)  # 1%的价格变化
            
            data.append((embds, x, target))
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]


class MockModel:
    """模拟模型，用于测试"""
    
    def __init__(self):
        self.device = 'cpu'
    
    def eval(self):
        """设置为评估模式"""
        pass
    
    def to(self, device):
        """移动到指定设备"""
        self.device = device
        return self
    
    def __call__(self, embds, x):
        """模拟前向传播"""
        batch_size = embds.shape[0]

        # 生成更合理的模拟预测
        # 限制预测值在合理范围内（-0.05到0.05，即-5%到5%）
        prediction = np.random.normal(0, 0.01)  # 1%的标准差
        prediction = np.clip(prediction, -0.05, 0.05)  # 限制在±5%范围内

        return torch.tensor([[prediction]], dtype=torch.float32)


def test_backtester_basic():
    """测试回测器基本功能"""
    logger.info("开始测试回测器基本功能...")
    
    # 创建模拟模型和数据
    model = MockModel()
    dataset = MockDataset(size=100, seq_len=30)
    
    # 创建回测器
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=10000.0,
        leverage=1.0
    )
    
    # 执行回测
    results = backtester.backtest(
        dataset=dataset,
        seq_len=30,
        commission=0.001,
        threshold=0.005,  # 较小的阈值以产生更多交易
        print_interval=20
    )
    
    # 验证结果
    assert 'total_return' in results, "缺少总收益率"
    assert 'max_drawdown' in results, "缺少最大回撤"
    assert 'sharpe_ratio' in results, "缺少夏普比率"
    assert 'total_trades' in results, "缺少交易次数"
    assert 'win_rate' in results, "缺少胜率"
    assert 'equity_curve' in results, "缺少权益曲线"
    
    logger.info("基本功能测试通过！")
    return results


def test_signal_generation():
    """测试信号生成功能"""
    logger.info("开始测试信号生成功能...")
    
    model = MockModel()
    backtester = TimeSeriesBacktester(model=model, initial_capital=10000.0)
    
    # 测试不同的预测值
    test_cases = [
        (0.8, 'BUY'),   # 高于阈值
        (-0.8, 'SELL'), # 低于负阈值
        (0.3, 'HOLD'),  # 在阈值范围内
        (0.0, 'HOLD'),  # 零值
    ]
    
    for prediction, expected_signal in test_cases:
        signal = backtester._generate_signal(prediction, threshold=0.5)
        assert signal == expected_signal, f"预测值 {prediction} 应该生成 {expected_signal} 信号，但得到 {signal}"
    
    logger.info("信号生成测试通过！")


def test_position_sizing():
    """测试仓位计算功能"""
    logger.info("开始测试仓位计算功能...")
    
    model = MockModel()
    backtester = TimeSeriesBacktester(model=model, initial_capital=10000.0, leverage=2.0)
    
    # 测试仓位计算
    available_capital = 10000.0
    price = 100.0
    
    position_size = backtester._calculate_position_size(available_capital, price, leverage=2.0)
    
    # 验证仓位大小合理性
    max_position = (available_capital * 2.0) / price  # 最大可买入数量
    expected_position = max_position * 0.9  # 90%的资金使用率
    
    assert abs(position_size - expected_position) < 1e-6, f"仓位计算错误: 期望 {expected_position}, 得到 {position_size}"
    
    logger.info("仓位计算测试通过！")


def test_metrics_calculation():
    """测试指标计算功能"""
    logger.info("开始测试指标计算功能...")
    
    model = MockModel()
    backtester = TimeSeriesBacktester(model=model, initial_capital=10000.0)
    
    # 创建模拟的权益曲线和交易记录
    equity_curve = [10000, 10100, 10050, 10200, 10150, 10300]
    daily_returns = [0.01, -0.005, 0.015, -0.0025, 0.01]
    trades = [
        {'profit': 100, 'datetime': datetime.now()},
        {'profit': -50, 'datetime': datetime.now()},
        {'profit': 150, 'datetime': datetime.now()},
    ]
    
    metrics = backtester._calculate_metrics(equity_curve, daily_returns, trades)
    
    # 验证指标
    assert 'total_return' in metrics, "缺少总收益率"
    assert 'max_drawdown' in metrics, "缺少最大回撤"
    assert 'sharpe_ratio' in metrics, "缺少夏普比率"
    assert 'win_rate' in metrics, "缺少胜率"
    
    # 验证胜率计算
    expected_win_rate = 2 / 3  # 3笔交易中2笔盈利
    assert abs(metrics['win_rate'] - expected_win_rate) < 1e-6, f"胜率计算错误: 期望 {expected_win_rate}, 得到 {metrics['win_rate']}"
    
    logger.info("指标计算测试通过！")


def run_all_tests():
    """运行所有测试"""
    logger.info("=" * 50)
    logger.info("开始运行TimeSeriesModel2drV2回测系统测试")
    logger.info("=" * 50)
    
    try:
        # 运行各项测试
        test_signal_generation()
        test_position_sizing()
        test_metrics_calculation()
        results = test_backtester_basic()
        
        # 打印测试结果摘要
        logger.info("\n" + "=" * 50)
        logger.info("测试完成！回测结果摘要:")
        logger.info(f"总收益率: {results['total_return']:.4f} ({results['total_return']*100:.2f}%)")
        logger.info(f"最大回撤: {results['max_drawdown']:.4f} ({results['max_drawdown']*100:.2f}%)")
        logger.info(f"夏普比率: {results['sharpe_ratio']:.4f}")
        logger.info(f"总交易次数: {results['total_trades']}")
        logger.info(f"胜率: {results['win_rate']:.4f} ({results['win_rate']*100:.2f}%)")
        logger.info(f"最终资金: {results['final_capital']:.2f}")
        logger.info("=" * 50)
        
        logger.info("✅ 所有测试通过！回测系统工作正常。")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    run_all_tests()
