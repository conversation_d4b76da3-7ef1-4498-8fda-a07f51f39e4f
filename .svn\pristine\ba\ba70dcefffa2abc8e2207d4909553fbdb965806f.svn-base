{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 投资日报\n", "---\n", "- 报告市场统计结果（主要合约的波动幅度、趋势）\n", "- 比较当日模拟盘与实盘交易结果\n", "- 分析是否抓住了所有交易机会"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:Anaconda3]", "language": "python", "name": "conda-env-Anaconda3-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}