{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BarTokenizer可视化分析\n", "\n", "本notebook用于比较不同BarTokenizer方法的token分布情况和质量"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成\n"]}], "source": ["# 导入必要的库\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import warnings\n", "import sys\n", "import os\n", "from typing import Dict, Any, Tuple\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体和图表样式\n", "# plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "# plt.rcParams['axes.unicode_minus'] = False\n", "# sns.set_style(\"whitegrid\")\n", "\n", "print(\"库导入完成\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功导入BarTokenizer模块\n"]}], "source": ["# 导入BarTokenizer相关模块\n", "sys.path.append(os.path.join(os.getcwd(), '..'))\n", "\n", "try:\n", "    from pyqlab.data.tokenizers.bar_tokenizer import (\n", "        <PERSON><PERSON><PERSON><PERSON>,\n", "        LinearMapping,\n", "        Quantile<PERSON><PERSON><PERSON>, \n", "        AdaptiveMapping\n", "    )\n", "    print(\"成功导入BarTokenizer模块\")\n", "except ImportError as e:\n", "    print(f\"导入BarTokenizer模块失败: {e}\")\n", "    print(\"请确保项目路径正确\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准备测试数据...\n", "加载真实数据成功，数据量: 624454\n", "数据采样到: 10000\n", "测试数据准备完成，形状: (10000, 8)\n", "数据列: ['code', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>15303</th>\n", "      <td>P9999.DC</td>\n", "      <td>2024-03-21 09:29:00</td>\n", "      <td>8324.000000</td>\n", "      <td>8328.000000</td>\n", "      <td>8322.000000</td>\n", "      <td>8324.000000</td>\n", "      <td>2304</td>\n", "      <td>7.081714e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18875</th>\n", "      <td>MA9999.ZC</td>\n", "      <td>2024-04-10 10:36:00</td>\n", "      <td>2487.000000</td>\n", "      <td>2487.000000</td>\n", "      <td>2485.000000</td>\n", "      <td>2486.000000</td>\n", "      <td>438</td>\n", "      <td>6.494388e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9211</th>\n", "      <td>RB9999.SC</td>\n", "      <td>2024-02-21 21:57:00</td>\n", "      <td>3799.000000</td>\n", "      <td>3803.000000</td>\n", "      <td>3799.000000</td>\n", "      <td>3803.000000</td>\n", "      <td>3283</td>\n", "      <td>2.512996e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82362</th>\n", "      <td>P9999.DC</td>\n", "      <td>2025-02-26 21:24:00</td>\n", "      <td>9202.000977</td>\n", "      <td>9206.000977</td>\n", "      <td>9200.000977</td>\n", "      <td>9200.000977</td>\n", "      <td>3155</td>\n", "      <td>7.821488e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104901</th>\n", "      <td>AG9999.SC</td>\n", "      <td>2024-11-29 21:13:00</td>\n", "      <td>7741.000000</td>\n", "      <td>7744.000000</td>\n", "      <td>7737.000000</td>\n", "      <td>7737.000000</td>\n", "      <td>2402</td>\n", "      <td>5.275370e-40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             code            datetime         open         high          low  \\\n", "index                                                                          \n", "15303    P9999.DC 2024-03-21 09:29:00  8324.000000  8328.000000  8322.000000   \n", "18875   MA9999.ZC 2024-04-10 10:36:00  2487.000000  2487.000000  2485.000000   \n", "9211    RB9999.SC 2024-02-21 21:57:00  3799.000000  3803.000000  3799.000000   \n", "82362    P9999.DC 2025-02-26 21:24:00  9202.000977  9206.000977  9200.000977   \n", "104901  AG9999.SC 2024-11-29 21:13:00  7741.000000  7744.000000  7737.000000   \n", "\n", "              close  volume        amount  \n", "index                                      \n", "15303   8324.000000    2304  7.081714e-40  \n", "18875   2486.000000     438  6.494388e-40  \n", "9211    3803.000000    3283  2.512996e-39  \n", "82362   9200.000977    3155  7.821488e-40  \n", "104901  7737.000000    2402  5.275370e-40  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 数据准备函数\n", "def generate_mock_ohlcv_data(n: int = 10000) -> pd.DataFrame:\n", "    \"\"\"生成模拟的OHLCV数据\"\"\"\n", "    np.random.seed(42)\n", "    \n", "    # 生成价格序列\n", "    base_price = 100\n", "    returns = np.random.normal(0, 0.02, n)\n", "    prices = base_price * np.exp(np.cumsum(returns))\n", "    \n", "    # 生成OHLCV数据\n", "    df = pd.DataFrame({\n", "        'open': prices,\n", "        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),\n", "        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),\n", "        'close': prices * (1 + np.random.normal(0, 0.005, n)),\n", "        'volume': np.random.lognormal(10, 1, n)\n", "    })\n", "    \n", "    # 确保OHLC数据的合理性\n", "    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))\n", "    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))\n", "    \n", "    return df\n", "\n", "def prepare_test_data(data_path: str = None, sample_size: int = 5000) -> pd.DataFrame:\n", "    \"\"\"准备用于测试的K线数据\"\"\"\n", "    try:\n", "        if data_path:\n", "            # 尝试加载真实数据\n", "            if data_path.endswith('.csv'):\n", "                df = pd.read_csv(data_path)\n", "            elif data_path.endswith('.parquet'):\n", "                df = pd.read_parquet(data_path)\n", "            else:\n", "                raise ValueError(\"不支持的文件格式\")\n", "            \n", "            print(f\"加载真实数据成功，数据量: {len(df)}\")\n", "            # print(df.head())\n", "            \n", "            # 如果数据量太大，随机采样\n", "            if len(df) > sample_size:\n", "                df = df.sample(n=sample_size, random_state=42)\n", "                print(f\"数据采样到: {len(df)}\")\n", "            \n", "            # 转换为标准OHLCV格式（如果需要）\n", "            if not all(col in df.columns for col in ['open', 'high', 'low', 'close', 'volume']):\n", "                print(\"数据不包含标准OHLCV列，生成模拟数据...\")\n", "                df = generate_mock_ohlcv_data(len(df))\n", "            \n", "        else:\n", "            print(\"未提供数据路径，生成模拟数据\")\n", "            df = generate_mock_ohlcv_data(sample_size)\n", "            \n", "    except Exception as e:\n", "        print(f\"加载数据失败: {e}，使用模拟数据\")\n", "        df = generate_mock_ohlcv_data(sample_size)\n", "    \n", "    return df\n", "\n", "# 准备测试数据\n", "print(\"准备测试数据...\")\n", "# 可以在这里指定真实数据路径\n", "data_path = 'f:/hqdata/fut_top_min1.parquet'\n", "test_data = prepare_test_data(data_path, sample_size=10000)\n", "\n", "print(f\"测试数据准备完成，形状: {test_data.shape}\")\n", "print(f\"数据列: {test_data.columns.tolist()}\")\n", "test_data.head()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始分析不同BarTokenizer方法...\n", "使用数据样本大小: 10000\n", "\n", "测试方法: Linear\n", "  词汇表大小: 400\n", "  唯一token数: 223\n", "  词汇表利用率: 55.75%\n", "  基尼系数: 0.5762\n", "  标准化熵: 0.8931\n", "\n", "测试方法: Linear+Balance\n", "  词汇表大小: 400\n", "  唯一token数: 223\n", "  词汇表利用率: 55.75%\n", "  基尼系数: 0.5762\n", "  标准化熵: 0.8931\n", "\n", "测试方法: Quantile\n", "  词汇表大小: 400\n", "  唯一token数: 372\n", "  词汇表利用率: 93.00%\n", "  基尼系数: 0.4030\n", "  标准化熵: 0.9532\n", "\n", "测试方法: Quantile+Balance\n", "  词汇表大小: 400\n", "  唯一token数: 372\n", "  词汇表利用率: 93.00%\n", "  基尼系数: 0.4030\n", "  标准化熵: 0.9532\n", "\n", "测试方法: Adaptive\n", "  词汇表大小: 400\n", "  唯一token数: 200\n", "  词汇表利用率: 50.00%\n", "  基尼系数: 0.5559\n", "  标准化熵: 0.8977\n", "\n", "测试方法: Adaptive+Balance\n", "  词汇表大小: 400\n", "  唯一token数: 200\n", "  词汇表利用率: 50.00%\n", "  基尼系数: 0.5559\n", "  标准化熵: 0.8977\n"]}], "source": ["# 分析不同BarTokenizer方法\n", "def analyze_tokenizer_methods(test_data: pd.DataFrame, n_bins: int = 100) -> Tuple[Dict, Dict]:\n", "    \"\"\"分析不同BarTokenizer方法的token分布情况和质量\"\"\"\n", "    \n", "    print(f\"使用数据样本大小: {len(test_data)}\")\n", "    \n", "    # 定义要测试的方法\n", "    methods = {\n", "        'Linear': {'mapping_strategy': 'linear', 'balancing_strategy': 'none'},\n", "        'Linear+Balance': {'mapping_strategy': 'linear', 'balancing_strategy': 'frequency'},\n", "        'Quantile': {'mapping_strategy': 'quantile', 'balancing_strategy': 'none'},\n", "        'Quantile+Balance': {'mapping_strategy': 'quantile', 'balancing_strategy': 'frequency'},\n", "        'Adaptive': {'mapping_strategy': 'adaptive', 'balancing_strategy': 'none'},\n", "        'Adaptive+Balance': {'mapping_strategy': 'adaptive', 'balancing_strategy': 'frequency'}\n", "    }\n", "    \n", "    results = {}\n", "    tokenizers = {}\n", "    \n", "    # 测试每种方法\n", "    for method_name, config in methods.items():\n", "        print(f\"\\n测试方法: {method_name}\")\n", "        \n", "        try:\n", "            # 创建tokenizer\n", "            tokenizer = BarTokenizer(\n", "                mapping_strategy=config['mapping_strategy'],\n", "                balancing_strategy=config['balancing_strategy'],\n", "                n_bins=n_bins,\n", "                # features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],\n", "                features=['body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],\n", "                atr_period=14\n", "            )\n", "            \n", "            # 拟合和转换\n", "            tokens = tokenizer.fit_transform(test_data)\n", "            \n", "            # 分析分布\n", "            balance_metrics = tokenizer.analyze_balance(tokens)\n", "            \n", "            # 计算额外的统计信息\n", "            unique_tokens, counts = np.unique(tokens, return_counts=True)\n", "            frequencies = counts / len(tokens)\n", "            \n", "            results[method_name] = {\n", "                'tokenizer': tokenizer,\n", "                'tokens': tokens,\n", "                'unique_tokens': unique_tokens,\n", "                'counts': counts,\n", "                'frequencies': frequencies,\n", "                'balance_metrics': balance_metrics,\n", "                'vocab_size': tokenizer.get_vocab_size(),\n", "                'vocab_utilization': len(unique_tokens) / tokenizer.get_vocab_size()\n", "            }\n", "            \n", "            tokenizers[method_name] = tokenizer\n", "            \n", "            print(f\"  词汇表大小: {tokenizer.get_vocab_size()}\")\n", "            print(f\"  唯一token数: {len(unique_tokens)}\")\n", "            print(f\"  词汇表利用率: {len(unique_tokens) / tokenizer.get_vocab_size():.2%}\")\n", "            print(f\"  基尼系数: {balance_metrics['gini_coefficient']:.4f}\")\n", "            print(f\"  标准化熵: {balance_metrics['normalized_entropy']:.4f}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"  错误: {e}\")\n", "            results[method_name] = None\n", "    \n", "    return results, tokenizers\n", "\n", "# 运行分析\n", "print(\"开始分析不同BarTokenizer方法...\")\n", "analysis_results, tokenizers = analyze_tokenizer_methods(test_data, n_bins=100)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化token分布对比\n", "def plot_token_distributions(results: Dict, max_tokens_to_show: int = 50):\n", "    \"\"\"绘制不同方法的token分布对比图\"\"\"\n", "    \n", "    valid_results = {k: v for k, v in results.items() if v is not None}\n", "    n_methods = len(valid_results)\n", "    \n", "    if n_methods == 0:\n", "        print(\"没有有效的结果可以绘制\")\n", "        return\n", "    \n", "    # 创建子图\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    axes = axes.flatten()\n", "    \n", "    for idx, (method_name, result) in enumerate(valid_results.items()):\n", "        if idx >= len(axes):\n", "            break\n", "            \n", "        ax = axes[idx]\n", "        \n", "        # 获取频率数据\n", "        frequencies = result['frequencies']\n", "        unique_tokens = result['unique_tokens']\n", "        \n", "        # 只显示前N个最频繁的tokens\n", "        if len(frequencies) > max_tokens_to_show:\n", "            # 按频率排序\n", "            sorted_indices = np.argsort(frequencies)[::-1]\n", "            top_indices = sorted_indices[:max_tokens_to_show]\n", "            plot_frequencies = frequencies[top_indices]\n", "        else:\n", "            plot_frequencies = frequencies\n", "        \n", "        # 绘制条形图\n", "        bars = ax.bar(range(len(plot_frequencies)), plot_frequencies, alpha=0.7)\n", "        \n", "        # 设置标题和标签\n", "        gini = result[\"balance_metrics\"][\"gini_coefficient\"]\n", "        entropy = result[\"balance_metrics\"][\"normalized_entropy\"]\n", "        ax.set_title(f'{method_name}\\n基尼系数: {gini:.4f}, 标准化熵: {entropy:.3f}', \n", "                    fontsize=11, fontweight='bold')\n", "        ax.set_xlabel('Token排名')\n", "        ax.set_ylabel('频率')\n", "        \n", "        # 添加网格\n", "        ax.grid(True, alpha=0.3)\n", "        \n", "        # 高亮显示高频token\n", "        max_freq = np.max(plot_frequencies)\n", "        for i, (bar, freq) in enumerate(zip(bars, plot_frequencies)):\n", "            if freq > max_freq * 0.1:  # 超过最大频率10%的token用红色标记\n", "                bar.set_color('red')\n", "                bar.set_alpha(0.8)\n", "    \n", "    # 隐藏多余的子图\n", "    for idx in range(len(valid_results), len(axes)):\n", "        axes[idx].set_visible(False)\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('不同BarTokenizer方法的Token分布对比', fontsize=16, fontweight='bold', y=1.02)\n", "    plt.show()\n", "\n", "# 绘制token分布对比\n", "plot_token_distributions(analysis_results, max_tokens_to_show=50)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAQBCAYAAADFHMORAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdeXwT9dbH8W+a7kARKFSRTVFZStHSqqi94gVBQBFEQVDZF0XBHRFQKQhiAX0UcQMsO6IoohcRZBUXFC1LWSyCsoNYpKWFrknm+aO3udQSaEvSJM3n7QtpJpOZkxwmPTn5zW9MhmEYAgAAAAAAAAAAxfi5OwAAAAAAAAAAADwVTXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOCAv7sDAAAAQNksWbJEI0eOPOd9ZrNZQUFBqlGjhq6//noNGTJE9erVc0ssjsydO1c33nhjqffVunVrHTly5KK24QqHDx9WmzZt7Ld3797txmgAAAAAOAsj0QEAACogq9WqrKwsHTp0SEuWLNG9996rlJQUd4cFAAAAAF6HkegAAAAVQOXKldWzZ09JkmEYslqt+vvvv7V69WplZWUpIyNDY8eO1YcffuiS/V9zzTUaNGhQkWXffPONfvvtN/v9rVq1KnJ/7dq1XRKLu1SpUqXYa+BJVq9erQ0bNpRo3UqVKmnEiBHFls+YMUOHDh0q0TaaNWum7t27F1s+evRomc3mCz4+Pz9f999/v6677roiy1NTUzV+/HhVrVr1gttITU3Vu+++W2y5p7wWAAAA8A400QEAACqAqlWr6tlnny22fOPGjerbt68kafPmzTp9+rQqV67s9P03a9ZMzZo1K7LsxIkT9iZ6ZGTkOeOrSBzlwFOcOXNG48aNK9G6n3/++TmX16pVq8RfFDjaxg033KDOnTtf8PF//vmnDhw4UGy51WrVAw88UKJpfBzF4CmvBQAAALwDTXQAAIAKLDo6ushtm81m/zkzM1PTp0/XmjVrdOTIEeXn56tKlSpq0qSJ+vXrV2Tk+NnzfV977bUaPXq0XnzxRf3xxx8KDw/X66+/rhYtWpQpxpMnT2rhwoX6+uuvdfjwYRmGoSuuuEIdO3bUQw89pODg4BJt56WXXtJHH30kqWD08Lx58xQZGWm/f+PGjfrggw+0bds25ebmqnbt2mrTpo0GDRqkSy65pMi2evXqpU2bNkmSli9frrS0NE2bNk3Jycny8/PT9ddfr6eeekrXXHPNOV8j6X9zoj///PP67LPPzhv75ZdfrrVr19pvZ2ZmaubMmVqxYoWOHj2q0NBQRUVFqXfv3rr11luLPPbs+egHDRqkBg0a6K233tLJkydVv359ffzxxwoNDS3RawgAAACgOJroAAAAFdi3335r/7lx48YKCwuTJOXk5GjQoEHasmVLkfXT09O1ceNGbdy4Ua+88oruvffeYttMTU3VoEGDdOrUKUlSWlpakWZyaezYsUOPPvqojh8/XmT5zp07tXPnTi1dulQzZ87UpZdeet7tzJo1y95ADwgI0LRp04o00GfOnKnJkycXecy+ffs0c+ZMffXVV5ozZ47q1q17zm0vW7ZM7733XpEvINauXauff/5ZS5cuVZ06dUr1nC/k+PHj6t27t/bv329flpeXp2+//VbffvutHnvsMT3++OPnfOyGDRs0Y8YM++0qVarQQAcAAAAuEk10AACACuDUqVOaMmWK/bbVatWRI0fso5sDAgL0wgsv2O9fvHixvYFeo0YN3XHHHTKZTPruu+/sU2jMnj37nE30o0ePys/PT3feeacCAwNls9nKNEVMRkaGhg0bZm+gV6tWTf/+97+Vl5entWvXKisrS3v27NGQIUO0ePFi+fufu3Rdu3atJk2aJEkymUx69dVXdfPNN9vv37RpU5HX5qabbtKVV16p77//Xvv379eRI0c0fPhwLVq06Jzbf+edd1SzZk21bdtWR44c0TfffCOpYLT44sWL9dRTT533ebZq1Urh4eFFlv3222/27RTGVOi5556zN9CrV6+u22+/XRkZGVq1apWsVqvefvtttWjRQnFxccX2tXv3blWpUkV33nmnjh07VmweegAAAAClRxMdAACgAjh9+nSREcj/NGbMGF1//fX22zVq1FCXLl3022+/adKkSbr66qslSceOHdNtt90mSee9aGLv3r3tU4iU1cKFC3X06FFJBdOZLFq0SLVq1ZIk/f777+revbtOnz6tXbt26YsvvlDXrl2LbePXX3/Vm2++aR8l/vzzz+uuu+4qss7MmTNlGIYkafDgwXrmmWckFYzuvv/++7Vr1y5t2bJFSUlJiomJKbaPyy67TEuWLFH16tUlSUOHDtWqVaskSXv27Lng8+zQoYM6dOhgv3306NEiF5mMiorSSy+9JElKTk7Wjz/+KEm65JJLtHTpUkVEREiSvvrqKz355JOSpA8++OCcTXRJSkhIKDKtDAAAAICL4+fuAAAAAOB6L7zwgkaOHCmLxSJJ6tixoxISEvTZZ5/p6quv1unTp7Vx40YtWLDA/picnByH27vzzjsvOqavvvrK/vOQIUPsDXRJatiwoXr16mW/Xdi0/qcpU6YoKytLktSkSRP7RVQLWa1W+9zmkvTAAw/Yfw4MDCzScP/hhx/OuY/OnTvbG+iSinwZcebMmXM+xpGsrCw98sgjSk1NlVTwZca0adMUFBQkSfYGuiS1adPG3kCXCprxhVOz/Pzzz8rLyyu2/UqVKunf//53qWICAAAAcH6MRAcAAKgA/nlhyry8PKWnp2vDhg16+eWXlZOToyVLluiKK67Q4MGDJRWM9v7oo4+0ceNG7d27t8ic35Lso7fPxRnzgB88eND+89nzl59r2dnrni0/P9/+86+//qoff/xRLVu2tC9LT09Xdna2/XbhKPtz+f3338+5/J/zsVeqVMn+8z9fs/MxDEMjRoywX3A0ICBAU6dOLbL9wpH5kvTpp5/q008/Pee28vPzdejQITVs2LDI8ssuu0x+foyTAQAAAJyJJjoAAEAFFBgYqFq1aum+++7TgQMHNH36dEnSJ598osGDB2vFihV69tlnlZ+fr4CAAN18882KjY1VdHS0+vTpc8Htl2UO9H9yNMd5obOb+CaTyeF6l156qf78809JBVOZLFmyxL6+1Wotsu4/5yY/m6Pmc+Eo8QutdyHTpk3T119/bb89cuRIxcbGFlnn7KZ8aGjoeS8KevaXA4WckRcAAAAARdFEBwAAqODOHun8559/ymazafz48fZR3PPmzVN0dLSkczdmzyUgIOCi46pdu7YyMjIkSbt27VLTpk2L3L9r1y77z/Xr1z/nNiIjIzVr1iz17NlTv//+u3bt2qXPP/9cXbp0kVRwsdKAgAD7c/3ss8+KTBtjtVplNpsv+rlcyNdff623337bfrtr16568MEHi613dmydOnXSuHHjitxvs9nO28R3Rl4AAAAAFMW5ngAAABWYzWYrMs1LvXr19Pfff9vn5JYKLmBZ6PPPPy/2+HM538jwkmrdurX953fffbdITPv27dP8+fPtt9u2bXvObQwfPlxVq1bV008/bV/2f//3f/b53AMCAtSiRQv7fbNnz7b/bLVa1aNHD7Vq1UoDBgzQxo0bL/o5ncvu3bs1YsQI+8j6G264oVhzvNCNN95o/3nlypX2EfaStGbNGkVHR+uee+7RmDFjzjndjjPyAgAAAKAoRqIDAABUAKdOndKUKVPstw3DUG5urn755Rf9+uuv9uVdunRRWFiYgoKClJubK0nq27evbr/9du3fv1/fffddke3m5OScd0qRi9GzZ08tWrRIJ0+e1OHDh3X33XfrtttuU35+vtasWWO/YGizZs3UqVOnc26jcFT27bffrujoaG3ZskV//vmnZs2apSFDhkiS+vXrp59++kmS9MEHH2jr1q1q2rSptm3bpuTkZElSRkaGGjVq5PTnmJWVpUcffdT+XCTpiiuu0Jtvvlls3UGDBik2NlbNmjXTjh07lJ6erk6dOun222+XyWTSV199pZycHO3atUs333wzDXMAAACgnNBEBwAAqABOnz6tGTNmnHed2NhY9e7dW4GBgXrooYf0wQcfSCqY4qVw1LfJZFKlSpV0+vRpSdKBAwfUpEkTl8Rcq1YtvfXWW3riiSd04sQJnTx5UkuWLCmyzjXXXKN33nmnRFOuPPvss/YpUmbMmKFu3bopPDxc//73vzVo0CD765OUlKSkpCT74wICAvTaa6+pevXqTnx2BQq/IDjbRx99dM51e/TooapVq+r//u//9OCDD+qvv/5SRkZGsdekZcuWGjZsmNNjBQAAAHBuNNEBAAAqIJPJJH9/fwUHB6tBgwa644477A10qaDhXLduXX344Yc6cOCAAgIC1LRpUw0cOFCbN2/Wu+++K6lgShFXNdGlgsb+f/7zHy1YsECrV6/WwYMHJRWM1r7zzjv14IMPKjg4uMTbat26tdauXaszZ85o6tSp9mlTnn32WV1//fVauHChkpOTlZmZqVq1aik6OlqDBg1S48aNXfYcS6tevXr6z3/+o5kzZ2rdunU6cuSI/P39Va9ePXXt2lXdu3e357G0XnrppRKtZzKZ1Llz52LL9+/fX+JtXHnlledcvn79+iJfYjiSnZ2t++6775z3zZo1S19++eUFt3HgwIFzPg/JM14LAAAAeAeTca7JFAEAAAAAAAAAABcWBQAAAAAAAADAEZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAFBi69evV6NGjbR06VJ3h+I0x44dU9OmTTVx4kR3hwIAAACUGLU5AJQfk2EYhruDAICKJCEh4bz3x8XF6ZZbbrHf/v777/Xdd9+d9zEjRowo9338U15entq3b6/c3FytW7dOgYGBkqT33ntPp06dcvi4qKgodezY0X57+/btWr58ucP1T58+rfj4eJnNZvsyV+9j2LBhWrNmjT7++GM1a9bM4eMAAADgXajNi6I2B4Cy8Xd3AABQ0fzrX//SzTff7PD+H374ochtk8l03iL5n+uX1z7+6YsvvtCRI0fUv39/e5EuSc2bNy9VLJmZmeeN5aefftI/v9919T66deumr7/+Wm+//bbeffddh48DAACAd6E2P/+2qc0BoGSYzgUAUCIffvihJKl9+/ZujsT5WrZsqcqVK2vdunU6dOiQu8MBAAAAzovaHADKF010AMAF/f3339q5c6dCQ0PVvHlzd4fjdIGBgYqJiZFhGFq3bp27wwEAAAAcojYHgPJHEx0AcEFbtmyRYRhq1KiRTCaTu8NxiUaNGkmSNm3a5OZIAAAAAMeozQGg/NFEBwBc0PHjxyVJNWrUcHMkrlP43I4dO+bmSAAAAADHqM0BoPzRRAcAXFBmZqYkKTQ01M2RuE6lSpUkSadPn3ZzJAAAAIBj1OYAUP5oogMALqhKlSqSpNzcXDdH4jqFBXrVqlXdHAkAAADgGLU5AJQ/mugAgAu6/PLLJf3v1NGKKDU1VZJUv359N0cCAAAAOEZtDgDljyY6AOCCoqOj5efnpz179shisbg7HJfYvXu3pILnCgAAAHgqanMAKH/+7g4AACqa5cuX69tvv3V4f/PmzYvcPnXqlBISEhyun5mZqZtvvrnc93G2qlWr6rrrrtPmzZuVnJysFi1a2O/77rvvzhtL/fr1i237fLEcO3ZMMTExRZa5eh8Wi0Xbt2+Xv7+/2rZt6/BxAAAA8C7U5kVRmwNA2ZgMwzDcHQQAwPN9/vnneu655zRs2DANHTrU3eE41aZNm9SrVy+1bdtW06ZNc3c4AAAAwHlRmwNA+WI6FwBAiXTo0EH16tXT559/LpvN5u5wnOqzzz6T2WyucB9AAAAAUDFRmwNA+aKJDgAokcDAQI0YMUIHDx7UmjVr3B2O06SmpmrZsmXq0aOHGjdu7O5wAAAAgAuiNgeA8sV0LgAAAADs8vLy1LVrV7344ou68cYbz7nOrl27NGbMGP3222+66qqrNHbsWDVr1qycIwUAAADKByPRAQAAAEiScnNz9fTTT2vPnj0O18nKytLgwYMVGxurJUuWKDo6Wg8//LCysrLKMVIAAACg/NBEBwAAAKC9e/eqe/fuOnjw4HnXW758uYKCgvTcc8+pYcOGGj16tCpVqqQVK1aUU6QAAABA+XJrEz03N1ejRo1SbGys4uLilJiY6HDd3bt3q2fPnmrevLk6deqkH3/8sRwjBQAAACq2TZs26cYbb9RHH3103vW2bdummJgYmUwmSZLJZFKLFi20devWcogSAAAAKH/+7tz5pEmTtGPHDs2ZM0dHjx7ViBEjVLt2bbVv377IepmZmerfv79at26tV199VZ9//rmGDh2qlStXqkaNGiXal81mk8VikZ+fn73gBwAAANzBMAzZbDb5+/vLz88zTg594IEHSrReamqqrrrqqiLLatSocd4pYM5GXQ4AAABPUdK63G1N9KysLC1evFgzZsxQZGSkIiMjtWfPHi1YsKBYE/2zzz5TaGio4uPjZTab9fjjj+ubb77Rjh071KpVqxLtz2KxaPv27a54KgAAAECZREVFKTAw0N1hlEp2dnaxmAMDA5WXl1eix1OXAwAAwNNcqC53WxM9JSVFFotF0dHR9mUxMTF67733ZLPZinT+N23apDZt2shsNtuXffrpp6XaX+H2oqKiimwH/2O1WrV9+3ZeIw9HnrwHufIO5Ml7kCvvQJ5KpvB18pRR6KURFBRUrGGel5en4ODgEj3eG58zAAAAKrYL1ahua6KnpqaqWrVqRTr84eHhys3NVXp6uqpXr25ffujQITVv3lwvvvii1q5dq8svv1wjRoxQTExMiffHqaIAAADwNN5Yo0ZEROjEiRNFlp04cUK1atUq0eMLnzNftJwfX0h5B/LkHciT9yBX3oE8eQ9ydWGFr9GF6nK3NdEdnQYqqdjIlqysLE2fPl29e/fWjBkz9OWXX2rAgAH66quvdNlll5Vqv5w6emG8Rt6BPHkPcuUdyJP3IFfegTxVXNdee61mzJghwzBkMplkGIY2b96sRx55pFTbMZvNfJgrAV4n70CevAN58h7kyjuQJ+9Bri6e25rojk4DlVTsVFCz2awmTZro8ccflyQ1bdpU33//vT7//PNSF+t88+IY3055B/LkPciVdyBP3oNceQfyVDKFr5O3SE1NVZUqVRQcHKz27dvrtdde04QJE9SjRw8tWrRI2dnZ6tChg7vDBAAAAFzCbU30iIgIpaWlyWKxyN+/IIzU1FQFBwcrLCysyLo1a9bUlVdeWWRZgwYNdOzYsVLvl29eLozXyDuQJ+9BrrwDefIe5Mo7kKeKJS4uThMnTlTXrl1VuXJlvf/++xozZow+/vhjNWrUSNOnT1doaKi7wwQAAABcwm1N9CZNmsjf319bt25VbGysJCkpKUlRUVHFJnK/7rrr9PPPPxdZ9scff+iuu+4qt3gBAAAAX7F79+7z3m7evLk+++yz8gwJAAAAcJvzX3bUhUJCQtSlSxfFx8crOTlZq1evVmJionr37i2pYFR6Tk6OJKlHjx7avXu33nrrLR04cEBvvvmmDh06pM6dO7srfAAAAAAAAACAD3BbE12SRo4cqcjISPXp00djx47VsGHD1K5dO0kFp4wuX75cknT55Zdr5syZWrdune666y6tW7dO06dPV0REhDvDBwAAAAAAAABUcG6bzkUqGI2ekJCghISEYvf985TRmJgYLVmypLxCAwAAAAAAAADAvSPRAQAAAAAAAADwZDTRAQAAAAAAAABwgCY6AAAAAAAAAAAO0EQHAAAAAAAAAMABmugAAAAAAAAAADhAEx0AAAAAAAAAAAdooqOIwMBAd4eAEiBPAAAAAFByfIbyHuQKgCfyd3cAviLHalO+1XB3GOdlSKpVt4HOWCWT1erucC4owGxSsNm53wORJ+dzRZ4AAAAAeIZcI1f5Rr67wzgvQ4YirohQlrJkspncHc4FBZgCFGQKcuo2vSFPknflyhV5AuC5aKKXk3yroaTUbGVZPLhBa0iZpzNVpbIkz/5dpVB/k2JqhijY7NztkifnclWeAAAAAHiGfCNf23K3KduW7e5QHDIMQ5mZmaqiKjKZPPtDVIhfiK4NutbpzVlvyJPkPblyVZ4AeC6a6OUoy2LojMXm7jAcM6TMPIv8LDaPb866ciYi8uRMjEAHAAAAKrpsW7ayjCx3h+GQYRg6bT0ts2GWydM/RLnwo6in50nyolx5cMsAgGvQ4QIAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAF4jMDDQ3SGgBMgTAAAAgIrE390BAAAA98qx2pRvNdwdxgUZkmrVbaAzVslktbo7nPMKMJsUbHb+WAVvyJU35UlyXa4AAAAAVBw00QEA8HH5VkNJqdnKsnh2c1aGlHk6U1UqSzK5OxjHQv1NiqkZomCz87ftFbnykjxJrs0VAAAAgIqDJjoAAFCWxdAZi83dYZyfIWXmWeRnsXl4c9a1o5o9PldekyeJmQ0BAAAAlASfHAAAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAQIkFBga6OwSUELlyDn93BwAAAAAAAABAyjVylW/kuzuM8zJkKOKKCGUpSyabyd3hXFCAKUBBpiCnbtMb8iR5V65ckSdnookOAAAAAB6IkWPegTwBcKZ8I1/bcrcp25bt7lAcMgxDmZmZqqIqMpk8uzEb4heia4OudXpz1hvyJHlPrlyVJ2eiiQ4AAADAZzByzPkY4ecdeZI8f5QfgALZtmxlGVnuDsMhwzB02npaZsMskzz8vc/muk17ep4kL8qVC/PkLDTRAQAAAPgMRo45FyP8vCNPkneM8gMAwFPRRAcAAADgUxg55kSM8POOPEleMcoPAABP5efuAAAAAAAAAAAA8FQ00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOCAW5voubm5GjVqlGJjYxUXF6fExESH6w4ZMkSNGjUq8mfdunXlGC0AAAAAAAAAwNf4u3PnkyZN0o4dOzRnzhwdPXpUI0aMUO3atdW+ffti6/7++++aPHmybrrpJvuyqlWrlme4AAAAAAAAAAAf47YmelZWlhYvXqwZM2YoMjJSkZGR2rNnjxYsWFCsiZ6Xl6fDhw8rKipKNWvWdFPEAAAAAAAAAABf47bpXFJSUmSxWBQdHW1fFhMTo23btslmsxVZ948//pDJZFLdunXLO0wAAAAAAAAAgA9zWxM9NTVV1apVU2BgoH1ZeHi4cnNzlZ6eXmTdP/74Q5UrV9Zzzz2nuLg43Xffffrmm2/KOWIAAAAAAAAAgK9x23Qu2dnZRRrokuy38/Lyiiz/448/lJOTo7i4OA0ePFirVq3SkCFD9NFHHykqKqpU+7VarRcXeBkZhf8z3LL7EjEMw/63SSY3R3MBRsFL6ex8kicnc1GevEXh8/bV5+8tyJN3vPdJXvT+58L3Pm/IldfkSXLr7ylffs8BAAAAvI3bmuhBQUHFmuWFt4ODg4ssf/TRR9WrVy/7hUQbN26snTt36uOPPy51E3379u0XEXXZBAYGqlbdBso8nanMPEu577+0MjIz3B3CBdkC/ZWdJR04tL/Yv6OyIk/O54o8eSN3vO+g9Hw1T9723id5/vufq977vC1Xnp4nid9TAAAAAErGbU30iIgIpaWlyWKxyN+/IIzU1FQFBwcrLCysyLp+fn72BnqhK6+8Unv37i31fqOiomQ2m8seeBmdsUpVKkt+FtuFV3YTwzCUkZmhsCphMpk8e+RYJX8/hYSGqmnTpk7dLnlyLlflyVtYrVZt377dbe87KBny5B3vfZL3vP+58r3PG3LlLXmS3Pt7qvC9BwAAAIDnc1sTvUmTJvL399fWrVsVGxsrSUpKSlJUVJT8/IpO1f7888/LZDJp4sSJ9mUpKSm65pprSr1fs9nsliaJyWqVTJInn9VceMq1yWTy6DglSf8N0dm5JE9O5qI8eRt3ve+gdHw5T97w3id50fufC9/7vCFXXpMnid9TAAAAAErEbRcWDQkJUZcuXRQfH6/k5GStXr1aiYmJ6t27t6SCUek5OTmSpNatW+s///mPli5dqgMHDmjatGlKSkrSQw895K7wAQAAAAAAAAA+wG1NdEkaOXKkIiMj1adPH40dO1bDhg1Tu3btJElxcXFavny5JKldu3YaM2aM3n33Xd11111au3atZs6cqTp16rgzfAAAAKDCyM3N1ahRoxQbG6u4uDglJiY6XHfVqlXq0KGDoqOj1bNnT+3cubMcIwUAAADKl9umc5EKRqMnJCQoISGh2H27d+8ucrtbt27q1q1beYUGAAAA+JRJkyZpx44dmjNnjo4ePaoRI0aodu3aat++fZH19uzZo2eeeUbjxo1TixYtNHv2bD388MNatWqVQkJC3BQ9AAAA4DpuHYkOAAAAwP2ysrK0ePFijR49WpGRkWrbtq0GDhyoBQsWFFv3+++/11VXXaUuXbqoXr16evrpp5Wamqq9e/e6IXIAAADA9WiiAwAAAD4uJSVFFotF0dHR9mUxMTHatm2bbDZbkXUvueQS7d27V0lJSbLZbFqyZIkqV66sevXqlXfYAAAAQLlw63QuAAAAANwvNTVV1apVU2BgoH1ZeHi4cnNzlZ6erurVq9uXd+zYUWvXrtUDDzwgs9ksPz8/vf/++6patWqp9mm1Wp0Wf2kYMmQYBX88WWF8Hh/nf19PZ+eTPDmfL+eKPHlHniTvyZWr8nT2tj35NfCWPEkcU96SK1ceUxdS0n3SRAcAAAB8XHZ2dpEGuiT77by8vCLL09LSlJqaqpdeeknXXnutPvzwQ40cOVKfffaZatSoUeJ9bt++/eIDL6XAwEBFXBGhzMxMnbaeLvf9l0VGRoa7Qzgvq9mqbGVr/779xf6tlBV5cg1yRZ68JU+S5+fKFXmSvC9Xnp4niWOqkKfnylXHlDPRRAcAAAB8XFBQULEPLIW3g4ODiyyfMmWKrrnmGj344IOSpJdfflkdOnTQp59+qsGDB5d4n1FRUTKbzRcZeellKUtVVEVmo/z3XRqGYSgjI0NhYWEymUzuDsehUFOoQkJC1LRpU6dulzw5ny/nijx5R54k78mVq/IkeUeuvCVPEseUt+TKlcfUhVit1hIN7qCJDgAAAPi4iIgIpaWlyWKxyN+/4CNCamqqgoODFRYWVmTdnTt3qlevXvbbfn5+aty4sY4ePVqqfZrNZrc00U02k0wmk0zy3A+SZzOZTB79obcwPrOfc3NJnpyPXJEnb8mT5Pm5clWeJO/KlafnSeKYKuTpuXLlMeUsXFgUAAAA8HFNmjSRv7+/tm7dal+WlJSkqKgo+fkV/chQq1Yt/f7770WW7du3T3Xq1CmPUAEAAIByRxMdAAAA8HEhISHq0qWL4uPjlZycrNWrVysxMVG9e/eWVDAqPScnR5LUvXt3ffzxx1q6dKkOHDigKVOm6OjRo7rnnnvc+RQAAAAAl2E6FwAAAAAaOXKk4uPj1adPH1WuXFnDhg1Tu3btJElxcXGaOHGiunbtqo4dO+rMmTN6//339eeff6pJkyaaM2dOqS4qCgAAAHgTmugAAAAAFBISooSEBCUkJBS7b/fu3UVud+vWTd26dSuv0AAAAAC3YjoXAAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAG3NtFzc3M1atQoxcbGKi4uTomJiRd8zOHDhxUdHa2ffvqpHCIEAAAAAAAAAPgyf3fufNKkSdqxY4fmzJmjo0ePasSIEapdu7bat2/v8DHx8fHKysoqxygBAAAAAAAAAL7KbU30rKwsLV68WDNmzFBkZKQiIyO1Z88eLViwwGET/YsvvtCZM2fKOVIAAAAAAAAAgK9y23QuKSkpslgsio6Oti+LiYnRtm3bZLPZiq2flpamyZMna9y4ceUZJgBclMDAQHeHgBIgTwAAAAAAwBG3jURPTU1VtWrVijQuwsPDlZubq/T0dFWvXr3I+q+++qruueceXX311eUdKgAPk2O1Kd9quDuMCzIk1arbQGeskslqdXc45xVgNinY7PzvVb0hV96UJ8l1uQIAAAAAAOfmtiZ6dnZ2sZF/hbfz8vKKLP/hhx+UlJSkZcuWXfR+rW5qkBiF//PgXpJhGPa/TTK5OZoLMApeSmfnkzw5mYvylGeVNqdmKyvfgxMlyZCh06dPq3Jlz85VaIBJLWqGKEDOf3/0hlx5S54k1+XKG977JC96/3PRe99/N+3xufKaPEkuzdWFuKsmBQAAAFB6bmuiBwUFFWuWF94ODg62L8vJydFLL72kMWPGFFleVtu3b7/obZRWYGCgatVtoMzTmcrMs5T7/ksrIzPD3SFckC3QX9lZ0oFD+4v9Oyor8uR8rszT8TTvyJMkZZ5Md3cI51Ul0F/ZlQyn5knyvlx5ep4k1+TK2977JM9//3PFe5/kfbny9DxJrssVAAAAgIrFbU30iIgIpaWlyWKxyN+/IIzU1FQFBwcrLCzMvl5ycrIOHTqkxx9/vMjjBw0apC5dupR6jvSoqCiZzeaLfwKldMYqVaks+VmKz/fuKQzDUEZmhsKqhMlk8uyRY5X8/RQSGqqmTZs6dbvkybl8OU+S9+TKVXmSvCNX3pIniWPKW3LFMeUdeZJcm6sLsVqtbhncAQAAAKD03NZEb9Kkifz9/bV161bFxsZKkpKSkhQVFSU/v//N9dq8eXN9/fXXRR7brl07jR8/Xrfcckup92s2m93SRDdZrZJJ8uSzmgtPuTaZTB4dpyTpvyE6O5fkycl8OE+SF+XKRXmSvCNXXpMniWPKW3LFMVXwt6fnSXJprgAAAABUHG5rooeEhKhLly6Kj4/XK6+8or/++kuJiYmaOHGipIJR6VWqVFFwcLDq169f7PERERGqUaNGeYcNAAAAAAAAAPAhfhdexXVGjhypyMhI9enTR2PHjtWwYcPUrl07SVJcXJyWL1/uzvAAAAAAAAAAAD7ObSPRpYLR6AkJCUpISCh23+7dux0+7nz3AQAAAAAAAADgLG4diQ4AAAAAAAAAgCejiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAoNzdXo0aNUmxsrOLi4pSYmOhw3d27d6tnz55q3ry5OnXqpB9//LEcIwUAAADKF010AAAAAJo0aZJ27NihOXPmaMyYMZo2bZpWrFhRbL3MzEz1799fV111lf7zn/+obdu2Gjp0qP7++283RA0AAAC4Hk10AAAAwMdlZWVp8eLFGj16tCIjI9W2bVsNHDhQCxYsKLbuZ599ptDQUMXHx6t+/fp6/PHHVb9+fe3YscMNkQMAAACu5+/uAAAAAAC4V0pKiiwWi6Kjo+3LYmJi9N5778lms8nP739jbzZt2qQ2bdrIbDbbl3366aflGi8AAABQnhiJDgAAAPi41NRUVatWTYGBgfZl4eHhys3NVXp6epF1Dx06pOrVq+vFF1/ULbfcou7duyspKamcIwYAAADKDyPRAQAAAB+XnZ1dpIEuyX47Ly+vyPKsrCxNnz5dvXv31owZM/Tll19qwIAB+uqrr3TZZZeVeJ9Wq/XiAy8DQ4YMo+CPJyuMz+Pj/O/r6ex8kifn8+VckSfvyJPkPblyVZ7O3rYnvwbekieJY8pbcuXKY+pCSrpPmugAAACAjwsKCirWLC+8HRwcXGS52WxWkyZN9Pjjj0uSmjZtqu+//16ff/65HnnkkRLvc/v27RcZdekFBgYq4ooIZWZm6rT1dLnvvywyMjLcHcJ5Wc1WZStb+/ftL/ZvqKzIk2uQK/LkLXmSPD9XrsiT5H258vQ8SRxThTw9V646ppyJJjoAAADg4yIiIpSWliaLxSJ//4KPCKmpqQoODlZYWFiRdWvWrKkrr7yyyLIGDRro2LFjpdpnVFRUkXnVy0uWslRFVWQ2yn/fpWEYhjIyMhQWFiaTyeTucBwKNYUqJCRETZs2dep2yZPz+XKuyJN35Enynly5Kk+Sd+TKW/IkcUx5S65ceUxdiNVqLdHgDproAAAAgI9r0qSJ/P39tXXrVsXGxkqSkpKSFBUVVeSiopJ03XXX6eeffy6y7I8//tBdd91Vqn2azWa3NNFNNpNMJpNM8twPkmczmUwe/aG3MD6zn3NzSZ6cj1yRJ2/Jk+T5uXJVniTvypWn50nimCrk6bly5THlLFxYFAAAAPBxISEh6tKli+Lj45WcnKzVq1crMTFRvXv3llQwKj0nJ0eS1KNHD+3evVtvvfWWDhw4oDfffFOHDh1S586d3fkUAAAAAJehiQ4AAABAI0eOVGRkpPr06aOxY8dq2LBhateunSQpLi5Oy5cvlyRdfvnlmjlzptatW6e77rpL69at0/Tp0xUREeHO8AEAAACXYToXAAAAAAoJCVFCQoISEhKK3bd79+4it2NiYrRkyZLyCg0AAABwK0aiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA74l+VBkydPVps2bWQYhkwmkyTZfzYMQytXrtSoUaOcGigAAACAoqjLAQAAANcrUxPdz89PLVq0cHj/+vXryxoPAAAAgBKiLgcAAABcr0zTuRSOcinr/QAAAAAuHnU5AAAA4HrMiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADpTpwqJZWVk6fvy4w/tPnz5d5oAAAAAAlAx1OQAAAOB6ZWqiDxw4UBaLxeH9AwYMKHNAAAAAAEqGuhwAAABwvTI10S+99FJnxwEAAACglKjLAQAAANdjTnQAAAAAAAAAABwo00j05cuX69JLL5VhGDKZTJJk/9kwDB0+fFidO3d2aqAAAAAAiqIuBwAAAFyvTE30lJQUdezY0eH933zzTZkDAgAAAFAy1OUAAACA65VpOhfDMC7q/kK5ubkaNWqUYmNjFRcXp8TERIfrfvHFF7rjjjvUvHlz9ejRQ8nJyaWKGQAAAKhonFWXAwAAAHCsTE30wlNFy3p/oUmTJmnHjh2aM2eOxowZo2nTpmnFihXF1vvll180evRoPfroo/ryyy8VHR2tQYMG6cyZM2UJHwAAAKgQnFWXAwAAAHDMbRcWzcrK0uLFizV69GhFRkaqbdu2GjhwoBYsWFBs3dTUVD366KPq3Lmz6tatq8cee0zp6en6/fff3RA5AAAAAAAAAMBXlGlOdGdISUmRxWJRdHS0fVlMTIzee+892Ww2+fn9r7/foUMH+885OTmaPXu2atSooYYNG5ZrzAAAAAAAAAAA31KmJroz5l5MTU1VtWrVFBgYaF8WHh6u3Nxcpaenq3r16sUes3HjRvXv31+GYWjKlCmqVKlS6YMHAAAAKgjmRAcAAABcr0xN9EaNGmnz5s0yDKPYPIuGYejKK6+84Days7OLNNAl2W/n5eWd8zFXX321lixZonXr1un5559XnTp1dN1115UqdqvVWqr1ncUo/J8Hf44p/JBlGIZM8vD5M42Cl9LZ+SRPTubDeZK8KFcuytN/N+3xufKaPEkcU96SK44p+98enSfJpbm6EGft0xl1OQAAAIDzK1MT/a677jrv/TExMRfcRlBQULFmeeHt4ODgcz4mPDxc4eHhatKkibZt26ZFixaVuom+ffv2Uq3vDIGBgapVt4EyT2cqM89S7vsvrYzMDHeHcEG2QH9lZ0kHDu13+KVLaZEn5yNPBTw9V67Ik+R9ufL0PEkcU4U8PVccUwU8PU+S63JVnpxRlwMAAAA4P7fNiR4REaG0tDRZLBb5+xeEkZqaquDgYIWFhRVZNzk5WWazWZGRkfZlDRs2LNOFRaOiomQ2my8u+DI4Y5WqVJb8LLZy33dJGYahjMwMhVUJKzaSydNU8vdTSGiomjZt6tTtkifn8uU8Sd6TK1flSfKOXHlLniSOKW/JFceUd+RJcm2uLsRqtbplcAcAAACA0itTE/3EiRMXPAU1IiLivPc3adJE/v7+2rp1q2JjYyVJSUlJioqKKnJRUUn65JNPdOTIEX3wwQf2ZTt37izTBx6z2eyWJrrJapVMkief1Vx4yrXJZPLoOCVJ/w3R2bkkT07mw3mSvChXLsqT5B258po8SRxT3pIrjqmCvz09T5JLc1VenFGXAwAAADg/vwuvUtx7770nq9Uqi8VS5E/hsvfee++C2wgJCVGXLl0UHx+v5ORkrV69WomJierdu7ekglHpOTk5kqT7779fP/74o+bMmaP9+/dr6tSpSk5OVt++fcsSPgAAAFAhOKMuBwAAAHB+ZRqJHhISotq1azu8v3LlyiXazsiRIxUfH68+ffqocuXKGjZsmNq1aydJiouL08SJE9W1a1dFRkZq2rRpev311/Xaa6/p6quv1gcffMCoGgAAAPg0Z9XlAAAAABwrUxP9QvNblnT+y5CQECUkJCghIaHYfbt37y5y+9///rf+/e9/lzxIAAAAoIJzVl0OAAAAwLEyTecCAAAAAAAAAIAvoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCgTHOi5+fna/PmzTIMwz7PYuHPhmEoKyvLqUECAAAAKI66HAAAAHC9MjXRR4wYcd77Y2JiyhQMAAAAgJKjLgcAAABcj+lcAAAAAAAAAABwoEwj0adPn67Y2FgZhlFkeeFpoxs3btTQoUOdEiAAAACAc6MuBwAAAFyvTE30zMxMtWjRwuH969evL2s8AAAAAEqIuhwAAABwvTJN51J40aKy3g8AAADg4lGXAwAAAK7HnOgAAAAAAAAAADhAEx0AAAAAAAAAAAdoogMAAAAAAAAA4ECZmuiGYVzU/QAAAAAuHnU5AAAA4Hr+ZXnQ7bffrs2bN5/zPsMw1KpVq4sKCgAAAMCFUZcDAAAArlemJvq1117r7DgAAAAAlBJ1OQAAAOB6zIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAQLm5uRo1apRiY2MVFxenxMTECz7m8OHDio6O1k8//VQOEQIAAADu4e/uAAAAAAC436RJk7Rjxw7NmTNHR48e1YgRI1S7dm21b9/e4WPi4+OVlZVVjlECAAAA5Y8mOgAAAODjsrKytHjxYs2YMUORkZGKjIzUnj17tGDBAodN9C+++EJnzpwp50gBAACA8sd0LgAAAICPS0lJkcViUXR0tH1ZTEyMtm3bJpvNVmz9tLQ0TZ48WePGjSvPMAEAAAC3YCQ6AAAA4ONSU1NVrVo1BQYG2peFh4crNzdX6enpql69epH1X331Vd1zzz26+uqry7xPq9Va5sdeDEOGDKPgjycrjM/j4/zv6+nsfJIn5/PlXJEn78iT5D25clWezt62J78G3pIniWPKW3LlymPqQkq6T5roAAAAgI/Lzs4u0kCXZL+dl5dXZPkPP/ygpKQkLVu27KL2uX379ot6fFkEBgYq4ooIZWZm6rT1dLnvvywyMjLcHcJ5Wc1WZStb+/ftL/ZvpazIk2uQK/LkLXmSPD9XrsiT5H258vQ8SRxThTw9V646ppyJJjoAAADg44KCgop9YCm8HRwcbF+Wk5Ojl156SWPGjCmyvCyioqJkNpsvahtlkaUsVVEVmY3y33dpGIahjIwMhYWFyWQyuTsch0JNoQoJCVHTpk2dul3y5Hy+nCvy5B15krwnV67Kk+QdufKWPEkcU96SK1ceUxditVpLNLiDJjoAAADg4yIiIpSWliaLxSJ//4KPCKmpqQoODlZYWJh9veTkZB06dEiPP/54kccPGjRIXbp0KdUc6Waz2S1NdJPNJJPJJJM894Pk2Uwmk0d/6C2Mz+zn3FySJ+cjV+TJW/IkeX6uXJUnybty5el5kjimCnl6rlx5TDkLTXQAAADAxzVp0kT+/v7aunWrYmNjJUlJSUmKioqSn5+ffb3mzZvr66+/LvLYdu3aafz48brlllvKNWYAAACgvNBEBwAAAHxcSEiIunTpovj4eL3yyiv666+/lJiYqIkTJ0oqGJVepUoVBQcHq379+sUeHxERoRo1apR32AAAAEC58LvwKgAAAAAqupEjRyoyMlJ9+vTR2LFjNWzYMLVr106SFBcXp+XLl7s5QgAAAMA9GIkOAAAAQCEhIUpISFBCQkKx+3bv3u3wcee7DwAAAKgIGIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOODWJnpubq5GjRql2NhYxcXFKTEx0eG669evV+fOnRUdHa1OnTppzZo15RgpAAAAAAAAAMAXubWJPmnSJO3YsUNz5szRmDFjNG3aNK1YsaLYeikpKRo6dKjuvfdeLV26VD169NATTzyhlJQUN0QNAAAAAAAAAPAV/u7acVZWlhYvXqwZM2YoMjJSkZGR2rNnjxYsWKD27dsXWXfZsmVq2bKlevfuLUmqX7++1q5dq6+++kqNGzd2R/gAAAAAAAAAAB/gtiZ6SkqKLBaLoqOj7ctiYmL03nvvyWazyc/vf4Pk77nnHuXn5xfbRmZmZrnECgAAAAAAAADwTW5roqempqpatWoKDAy0LwsPD1dubq7S09NVvXp1+/KGDRsWeeyePXu0ceNG9ejRo9T7tVqtZQ/6IhiF/zPcsvsSMQzD/rdJJjdHcwFGwUvp7HySJyfz4TxJXpQrF+Xpv5v2+Fx5TZ4kjilvyRXHlP1vj86T5NJcXYi7alIAAAAApee2Jnp2dnaRBrok++28vDyHjzt58qSGDRumFi1aqE2bNqXe7/bt20v9mIsVGBioWnUbKPN0pjLzLOW+/9LKyMxwdwgXZAv0V3aWdODQ/vP+eykN8uR85KmAp+fKFXmSvC9Xnp4niWOqkKfnimOqgKfnSXJdrgAAAABULG5rogcFBRX7sFJ4Ozg4+JyPOXHihPr16yfDMDR16tQiU76UVFRUlMxmc+kDvkhnrFKVypKfxVbu+y4pwzCUkZmhsCphMpk8e+RYJX8/hYSGqmnTpk7dLnlyLl/Ok+Q9uXJVniTvyJW35EnimPKWXHFMeUeeJNfm6kKsVqtbBncAAAAAKD23NdEjIiKUlpYmi8Uif/+CMFJTUxUcHKywsLBi6x8/ftx+YdG5c+cWme6lNMxms1ua6CarVTJJnnxWc+Ep1yaTyaPjlCT9N0Rn55I8OZkP50nyoly5KE+Sd+TKa/IkcUx5S644pgr+9vQ8SS7NFQAAAICKo/RDuZ2kSZMm8vf319atW+3LkpKSFBUVVWyEeVZWlgYOHCg/Pz/Nnz9fERER5RwtAAAAAAAAAMAXua2JHhISoi5duig+Pl7JyclavXq1EhMT7aPNU1NTlZOTI0l6//33dfDgQSUkJNjvS01NVWZmprvCBwAAAAAAAAD4ALdN5yJJI0eOVHx8vPr06aPKlStr2LBhateunSQpLi5OEydOVNeuXbVy5Url5OSoW7duRR5/zz336NVXX3VH6AAAAAAAAAAAH+DWJnpISIgSEhLsI8zPtnv3bvvPK1asKM+wAAAAAAAAAACQ5MbpXAAAAAAAAAAA8HQ00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroAAAAAAAAAAA4QBMdAAAAAAAAAAAHaKIDAAAAAAAAAOAATXQAAAAAAAAAABygiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAADl5uZq1KhRio2NVVxcnBITEx2uu379enXu3FnR0dHq1KmT1qxZU46RAgAAAOWLJjoAAAAATZo0STt27NCcOXM0ZswYTZs2TStWrCi2XkpKioYOHap7771XS5cuVY8ePfTEE08oJSXFDVEDAAAArufv7gAAAAAAuFdWVpYWL16sGTNmKDIyUpGRkdqzZ48WLFig9u3bF1l32bJlatmypXr37i1Jql+/vtauXauvvvpKjRs3dkf4AAAAgEvRRAcAAAB8XEpKiiwWi6Kjo+3LYmJi9N5778lms8nP738nsN5zzz3Kz88vto3MzMxyiRUAAAAob0znAgAAAPi41NRUVatWTYGBgfZl4eHhys3NVXp6epF1GzZsWGTE+Z49e7Rx40bddNNN5RUuAAAAUK4YiQ4AAAD4uOzs7CINdEn223l5eQ4fd/LkSQ0bNkwtWrRQmzZtSrVPq9Va+kCdwJAhwyj448kK4/P4OP/7ejo7n+TJ+Xw5V+TJO/IkeU+uXJWns7ftya+Bt+RJ4pjylly58pi6kJLukyY6AAAA4OOCgoKKNcsLbwcHB5/zMSdOnFC/fv1kGIamTp1aZMqXkti+fXvZgr0IgYGBirgiQpmZmTptPV3u+y+LjIwMd4dwXlazVdnK1v59+8/7hUtpkCfXIFfkyVvyJHl+rlyRJ8n7cuXpeZI4pgp5eq5cdUw5E010AAAAwMdFREQoLS1NFotF/v4FHxFSU1MVHByssLCwYusfP37cfmHRuXPnqnr16qXeZ1RUlMxm88UFXgZZylIVVZHZKP99l4ZhGMrIyFBYWJhMJpO7w3Eo1BSqkJAQNW3a1KnbJU/O58u5Ik/ekSfJe3LlqjxJ3pErb8mTxDHlLbly5TF1IVartUSDO2iiAwAAAD6uSZMm8vf319atWxUbGytJSkpKUlRUVLER5llZWRo4cKD8/Pw0d+5c1axZs0z7NJvNbmmim2wmmUwmmeS5HyTPZjKZPPpDb2F8Zj/n5pI8OR+5Ik/ekifJ83PlqjxJ3pUrT8+TxDFVyNNz5cpjylm4sCgAAADg40JCQtSlSxfFx8crOTlZq1evVmJion20eWpqqnJyciRJ77//vg4ePKiEhAT7fampqcrMzHRb/AAAAIArMRIdAAAAgEaOHKn4+Hj16dNHlStX1rBhw9SuXTtJUlxcnCZOnKiuXbtq5cqVysnJUbdu3Yo8/p577tGrr77qjtABAAAAl6KJDgAAAEAhISFKSEiwjzA/2+7du+0/r1ixojzDAgAAANyO6VwAAAAAAAAAAHDArU303NxcjRo1SrGxsYqLi1NiYuIFH/PLL7+oTZs25RAdAAAAAAAAAMDXuXU6l0mTJmnHjh2aM2eOjh49qhEjRqh27dpq3779OdffvXu3nnjiCQUFBZVzpAAAAAAAAAAAX+S2kehZWVlavHixRo8ercjISLVt21YDBw7UggULzrn+okWL1KNHD9WoUaOcIwUAAAAAAAAA+Cq3NdFTUlJksVgUHR1tXxYTE6Nt27bJZrMVW3/Dhg1KSEhQ3759yzFKAAAAAAAAAIAvc9t0LqmpqapWrZoCAwPty8LDw5Wbm6v09HRVr169yPrvvPOOJGnJkiUXtV+r1XpRjy8ro/B/hlt2XyKGYdj/Nsnk5mguwCh4KZ2dT/LkZD6cJ8mLcuWiPP130x6fK6/Jk8Qx5S254piy/+3ReZJcmqsLcVdNCgAAAKD03NZEz87OLtJAl2S/nZeX57L9bt++3WXbdiQwMFC16jZQ5ulMZeZZyn3/pZWRmeHuEC7IFuiv7CzpwKH9Tvv3Qp6cjzwV8PRcuSJPkvflytPzJHFMFfL0XHFMFfD0PEmuyxUAAACAisVtTfSgoKBiH1YKbwcHB7tsv1FRUTKbzS7bviNnrFKVypKfpfhUNZ7CMAxlZGYorEqYTCbPHjlWyd9PIaGhatq0qVO3S56cy5fzJHlPrlyVJ8k7cuUteZI4prwlVxxT3pEnybW5uhCr1eqWwR0AAAAASs9tTfSIiAilpaXJYrHI378gjNTUVAUHByssLMxl+zWbzW5popusVskkefJZzYWnXJtMJo+OU5L03xCdnUvy5GQ+nCfJi3LlojxJ3pErr8mTxDHlLbnimCr429PzJLk0VwAAAAAqDrddWLRJkyby9/fX1q1b7cuSkpIUFRUlPz+3hQUAAAAAAAAAgJ3butUhISHq0qWL4uPjlZycrNWrVysxMVG9e/eWVDAqPScnx13hAQAAAAAAAADgvia6JI0cOVKRkZHq06ePxo4dq2HDhqldu3aSpLi4OC1fvtyd4QEAAAAAAAAAfJzb5kSXCkajJyQkKCEhodh9u3fvPudjunbtqq5du7o6NAAAAAAAAAAA3DsSHQAAAAAAAAAAT0YTHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA7QRAcAAAAAAAAAwAGa6AAAAAAAAAAAOEATHQAAAAAAAAAAB2iiAwAAAAAAAADgAE10AAAAAAAAAAAcoIkOAAAAAAAAAIADNNEBAAAAAAAAAHCAJjoAAAAAAAAAAA64tYmem5urUaNGKTY2VnFxcUpMTHS47q5du9StWzdde+21uvfee7Vjx45yjBQAAACo2KjNAQAAgHNzaxN90qRJ2rFjh+bMmaMxY8Zo2rRpWrFiRbH1srKyNHjwYMXGxmrJkiWKjo7Www8/rKysLDdEDQAAAFQ81OYAAADAubmtiZ6VlaXFixdr9OjRioyMVNu2bTVw4EAtWLCg2LrLly9XUFCQnnvuOTVs2FCjR49WpUqVzlnUAwAAACgdanMAAADAMbc10VNSUmSxWBQdHW1fFhMTo23btslmsxVZd9u2bYqJiZHJZJIkmUwmtWjRQlu3bi3PkAEAAIAKidocAAAAcMzfXTtOTU1VtWrVFBgYaF8WHh6u3Nxcpaenq3r16kXWveqqq4o8vkaNGtqzZ0+J92cYhiQpLy9PZrP5IqMvPYtVCvWzyWQyyn3fJWWYDBkBfqrkZ5NJJneHc14hfjZZrFbl5Vmdul3y5Fy+nCfJe3LlqjxJ3pErb8mTxDHlLbnimPKOPEmuzdWFWK0F+yysUd2tPGtzt9flsijYFizD5hmvvUOGZDPZFGoNlScfSsF+wbJYLMpTnlO3S56cz6dzRZ68I0+S1+TKVXmSvCRXXpIniWPKW3LlymPqQkpal7utiZ6dnV2kSJdkv52Xl1eidf+53vkUjqDZtWtXWcJ1iir//ePJIiS54d9rmfzxl2u2S56cy5fzJHlPrlyVJ8k7cuUteZI4prwlVxxT8oo8Sa7NVUn8c5S3u5Rnbe4JdXnIf//zdNVV/cIreYDf9btLtkuenM+Xc0WevCNPkvfkylV5krwjV96SJ4ljylty5cpjqiQuVJe7rYkeFBRUrNAuvB0cHFyidf+53vn4+/srKipKfn5+9lNPAQAAAHcwDEM2m03+/m4rx4soz9qcuhwAAACeoqR1uduq9oiICKWlpclisdiDTE1NVXBwsMLCwoqte+LEiSLLTpw4oVq1apV4f35+fsVGzAAAAAAo39qcuhwAAADexm0XFm3SpIn8/f2LXIAoKSnJPirlbNdee622bNlin5vGMAxt3rxZ1157bXmGDAAAAFRI1OYAAACAY25rooeEhKhLly6Kj49XcnKyVq9ercTERPXu3VtSwciXnJwcSVL79u2VkZGhCRMmaO/evZowYYKys7PVoUMHd4UPAAAAVBjU5gAAAIBjJuNClx51oezsbMXHx+vrr79W5cqVNWDAAPXt21eS1KhRI02cOFFdu3aVJCUnJ2vMmDH6/fff1ahRI40dO1ZNmzZ1V+gAAABAhUJtDgAAAJybW5voAAAAAAAAAAB4MrdN5wIAAAAAAAAAgKejiQ4AAAAAAAAAgAM00QEAAAAAAAAAcIAmOgAAAAAAAAAADtBEBwAAAAAAAADAAZroKBXDMCRJ2dnZbo4EAIDzK/yd9c+fAaCioDYHAHgLanN4O5roKBWTyaTVq1crISFBaWlp7g4HbmSz2dwdQoVGUQFcHJvNJpPJJEnKz8/XmTNn3BwRyhPvofAV1OYoRG3uWvxeAS4Otblvqyjvof7uDgDe5ciRI/r444911113qXLlyu4OB+XAMAyZTCbt3btXJ06cUG5urm666SYFBga6O7QKp/C1lmT/G77h7NxbrVaZzWY3R+TdDMOQn1/BOIHp06dr06ZNOnz4sK6//nrdfffdio2N5RirwGw2mz3/J06c0OnTp9WgQYMixxlQUVCb+x5q8/JDbe67qM2di9rct1Wk2pwmOkrs999/11133aVGjRopMjJSAQEB7g4J5cBkMmnlypVKSEhQlSpVFBwcrNGjR2vatGlq3ry5/c0QF6fwF8gvv/yipKQkpaWl6frrr1erVq3k789bdUVWmPsNGzZo1apV2rNnj9q0aaPo6GjFxsa6OzyvVFiMvfPOO5o/f76eeuopNW7cWAMHDtThw4f16quvKiIiws1RwlUKfy/93//9n3744Qft27dPLVu21JVXXqmhQ4fSaEKFQW3um6jNywe1ue+iNnc+anPfVpFqc37DosQaNmyonj17KiUlRbt27XJ3OCgne/fu1csvv6xhw4bp888/V0JCgk6cOKH9+/fLYrFIqjin5rhT4enYQ4YM0c6dO2WxWPTYY48pISFBp06dcnd4cCGTyaR169bp8ccfV+XKldW5c2f95z//UUJCgvbt2+fu8LxK4XuRYRhKT0/Xzz//rBdffFHdunVTfn6+cnJy1LNnT/311186fvy4m6OFK82ZM0cfffSRnnjiCa1atUpWq1VLly7VwYMH3R0a4DTU5r6J2rx8UJv7Lmpz56E2R6GKUpvTRIdDhW94J06c0J9//ilJeumll9S9e3e9+OKL2rhxozvDQznJyspSRESE7rnnHh08eFD9+vVTjx49dOONNyohIUHZ2dledwqOJ9i/f7997krDMPT3339r+vTpevLJJzV16lSNGDFCgYGBuuyyy5Sfn888lxVYXl6eli1bpiFDhmjEiBG69957dfz4cbVu3VqBgYFeV1i409mnXF9yySXKy8vTJZdcojVr1mjAgAF67rnn1K5dO7355ptasGCBm6OFqxiGoT/++EOPPPKI4uLitGvXLv34448aMWKEcnJytH79eneHCJQJtTkkanNXoTZHIWpz56E2h1SxanOa6DinwlOYVq9erYceekgDBw7U4MGDJUnjxo3TnXfeqUcffVSbNm1yc6Rwtfz8fGVnZ+vIkSPq27evbrnlFsXHxyswMFDr1q3TunXr3B2iVzEMQ99++63at2+vtWvX2o+14OBg2Ww23XjjjTp8+LDatGmjTp06qUePHho3bhyvcwUWEBCg48eP65JLLlFqaqpuv/12tWnTRoMGDdKMGTP0xRdfuDtErzJ79myNGjVKkhQeHq5p06Zp5MiRGj58uB588EH78hMnTrgzTDjRPxsZJpNJaWlpOnHihNavX69hw4bp2Wef1Z133qkvvvhCH374oaxWq5uiBcqG2hyFqM2di9oc/0Rt7lzU5r6nItfmNNFxTiaTSd98842eeuoptW/fXo888oj27Nmjhx9+WJI0YcIEdezYUUOHDtUPP/zg5mjhLIUjnH799VetXr1a69atU1RUlGrUqKE2bdooLi5O48ePl1TwxhgUFKTq1au7M2SvY7FY9K9//Uvdu3fXyJEjtXbtWlksFlksFhmGoW+++Ub9+vVTq1at9PLLLys0NFSZmZnasGGDu0OHkxQeZzabTbm5uTKZTIqOjlZycrLuu+8+3XbbbZowYYL8/f0VEBCgn376yWuKCk9QtWpV7du3TydPntSTTz6pffv2qUGDBrrvvvtks9lkGIb279+vmjVrujtUOMHZFyo6fPiwfUqLW2+9Vb/88osef/xxPfPMM/YPafXq1VNWVhbHFLwOtblvojZ3PWpzUJu7FrW5b6notTlXxEAxhmEoNzdX8+bN0yOPPKLHHntMf/31l0wmk7777jvdf//9+uijjzRhwgTl5OTo+eef19dff63g4GB3h46LVHihovj4eNWsWVPh4eGyWq169tlnNWHCBP3xxx86fvy4MjIytHLlSuXm5qpBgwbuDttrvPfee/rhhx80d+5cjRs3TmazWc8++6wmT56s22+/Xd27d9eYMWN0yy236OWXX7Y/zmQyqXbt2m6MHM5SOLpp/fr1WrJkiY4cOaIBAwaoZcuWevrpp3XppZdq6NCh9lMfT506pfr163Na9gWcfWX35s2ba9asWVq2bJl69+6tt99+WwMGDNDAgQNlNpuVl5enM2fOaNiwYW6OGs5QWKS/9tprWrt2rf766y/dd9996tq1q1avXq0rr7xSderUUU5OjiwWi1avXq3LL7/cqy5gBFCb+y5qc9eiNge1uWtQm/uuil6bmwyuOoJzyMvLU58+fdSjRw+1adNGCQkJ8vf3V8+ePdWnTx+1aNFCY8eOVXh4uFJTU/nWsII4dOiQBg4cqEceeUTt27fXqVOnVKNGDQUEBGjz5s2aPn26tmzZoksuuUQ2m01vvPGGIiMj3R221/jrr7906tQpXX311fZl8fHx+uyzz/Taa6/ptttu0/Tp0zV16lQ9/fTTqlSpkg4fPqwlS5Zo0aJFuuKKK9wYPZzl+++/16OPPqo+ffqocuXKCggIUL9+/bRhwwYNHz5ccXFxCggIkNls1sqVK7Vw4UJdc8017g7bo0yfPl0dOnRQ3bp1JUk///yzrr/+envBvmDBAr399tuaN2+eGjZsqMOHD2v58uU6efKkqlatqkGDBsnf319Wq1Vms9nNzwYXa8mSJZoyZYpef/111apVS8ePH9dNN92kjIwMPfXUU8rOztb+/ftVu3Zt5eXl6dNPP1VAQECRD3iAp6M2903U5q5FbQ6J2twZqM1xtopcmzMSHecUGBioG264QZdccol++eUXZWRkqE+fPmrQoIHq16+vNWvW6NixY1q4cCFFegVitVpls9nUsGFDhYSEKCQkRJJ05MgRzZgxQ2+88Yb27NmjwMBAVa9endyX0PHjx3XmzBldeeWVqlWrlpKSkjR+/Hh9+OGHio+PlyQNHz5cr732mh599FHVrFlTH3/8saxWq8LCwjRnzhyK9ArCZrNp3bp1uv/++/X0009L+t8ppC1bttS4ceO0a9cu7dy5U7Vr16ZIP4eUlBT98ssv6t+/v6SCU9x79eqlm2++WR06dNDdd9+tBx98UJs3b9Z3332nevXqqU6dOva5gwtRpFccBw4c0O23366WLVtKkq688kpJ0r59+9SzZ0/VqVNHSUlJqlmzptq0aSOz2SyLxSJ/f8pgeA9qc99Ebe4a1OYoRG1+8ajN8U8VuTb3/AjhcoXf9vz6669KT0/X8ePH1bFjRz311FOSpIkTJyowMFAtWrSQJF1xxRV67LHHdNVVV3GaaAWTkZGhM2fOKD8/X1LBhYsCAgJ05swZ7dmzR3v37lVUVJSbo/Q+v/32mzZs2KAhQ4YoIyNDl112mU6dOqX+/ftr1qxZio+Pl2EYeuaZZzR58mR169ZNHTp0UGBgoPLz81WpUiV3PwU4iZ+fnw4dOmQ/zU0qGF3o5+ene++9V/fee6/9vddbConylJubq8aNG+vtt9+Wv7+/1q9fr4iICH3zzTeaNm2avvjiC82dO1cvvfSSKleurG+++Ua9evWSVLwwp0j3TucaoZKenq4tW7bYbxf+7nrjjTdUpUoVTZ06VY0bN7bfb7VaObbg0ajNUYja3DWozVGI2vziUJvD12pzLiwK+1x7/fv318yZMzV16lT1799f8+bNkySlpaXp8OHDOnTokCZPnqxvv/1WV111lS677DI3R46LUfgN+7Fjx/Tbb79JKpiv7KabbtLTTz+tkydPKiAgQJJUs2ZNhYSEKCcnx23xerOcnBwlJSVp+PDhuv/++5Wfn68PP/xQJ0+eVO/evZWbm6uxY8eqc+fOGjFihL766isFBwcrMDCQIt2LGYahc82YFhcXp+zsbG3btk2SFBQUpICAAMXFxWnr1q32q5l7SyFRXv766y/NmDFDp06dUkBAgDIyMjR8+HAlJiYqICBA8fHxSkhI0HXXXaepU6cqLy9PP/zwg9577z1JFOYVgc1mKzInaWFT6c4771RQUJDmz5+vnJwc+++uO+64Q5KKXaiIfwvwdNTmvonavPxQm/smanPnojaHL9bmNNGhQ4cOKSEhQU8++aQ++OADTZ8+Xb/88ouCg4OVl5enxx57TH///bf69++vFStWaPr06RTpFYDJZNKKFSt0//33q2/fvurRo4cOHDig0aNHq2HDhrr77ru1ceNG/fLLL1qwYIHOnDljn+MMpdO2bVvddttt+v7779WoUSP5+fkpIiJCc+bM0alTp+zFenx8vNq0aaOJEycqNzfX3WHDCUwmk3788Ue98sorWrRokQ4cOKA77rhDGRkZmj9/fpFv6E+ePKmwsLAiI2HwP/n5+frll1/08ssva/jw4dq2bZvef/99JScna/LkyTp48KBq166tl19+WY8++qiio6Pl7++vP//8092hw0kKj41p06apV69eGjp0qBYvXqwbbrhBsbGxWrlypebOnauTJ0/q1KlTWrlypSpVquRVhTkgUZv7Kmrz8kNt7ruozZ2H2hy+WJtzYVFo+/btGj58uFasWKGjR4/a56969NFHtWjRIvXv31/BwcHat2+fatWqperVq7s7ZDjBb7/9pscff1zdunXTVVddpTfffFP5+fn6v//7P1122WUaP368kpKSFBAQIJPJpISEBC5UVAY2m01+fn6aO3eujh07pi1btqhJkybq0aOHGjVqpOPHj6tPnz6qXr26EhMTFRwczAXBKpD169frkUceUYsWLXTo0CFFRUXpqaeeUlBQkJ577jn7afcRERFas2YN8yxewNq1azV27FgdP35cM2fOVFxcnH7++WeNHDlSN954o/r06VPk9Tt27Jhq1arl1YUa/vc+KkkfffSRJk+erMGDB2vHjh06evSoOnXqpD59+mjatGn67rvv9Ouvv+qaa65Rfn6+Fi9e7DUXKgIKUZv7Jmrz8kFt7tuozZ2L2tw3+XJtThMdOnLkiEaPHq2HHnpI48ePV6tWrTR27FidPn1a//rXvzR69Gjdd9997g4TTrR371598sknOnHihKZMmWJf3rVrV+Xl5emtt97SFVdcoUOHDslsNis4OJgPaKVU+Evhzz//lMlkUkREhCTpyy+/1OzZs9WsWbMixXr37t1Vr149zZs3z2t/oaCoQ4cO6csvv1RYWJgeeOAB/fTTT5o/f77y8/P19NNPKzw8XD/88IM2bNigyy67TJ06ddJVV13l7rA9UuExsWXLFo0cOVJVqlRR3bp1NWLECEVERNiL9ZYtW6pfv35q2LBhkcdzoaKKYcWKFfr7779VrVo1dezYUSdPntTixYv19ddfq1OnTurbt69ycnK0fv16Va9eXTExMV51oSKgELW576E2dz1qc1CbOw+1OSTfrM1povuYs4sHs9ms8PBw5ebm6uGHH9ZPP/2kzp07KyEhQZJ0+vRp9e/fX8OGDdO//vUvN0cOZyj8xnDq1KlaunSpJGnZsmUKDQ21r3PvvfcqLy9PU6ZM0TXXXEPBeBFWrlypCRMmyGw2KyIiQu+8846qV6+u5cuXa/bs2YqKilK3bt10+vRp1a1bV/n5+apTp467w4YTHD9+XA888IBOnz6t+Ph4dejQQZK0adMmzZ07VxaLRYMHD7ZfFO7sb/PxP/98XXJzc5WVlaXk5GR98MEHuuSSS/TCCy+oVq1a+vnnn/XCCy/ommuu0fPPP6/LL7/cjZHD2TIzM3X77bfr1KlTev7559W3b19J0t9//61PPvlEq1atUuvWrfXoo48WeRwf0uDpqM19G7V5+aI2913U5s5BbY5Cvlqb867gYwrn2uvTp486d+6s8ePH68SJE3rttddUt25dnTlzRmvWrNHu3bs1Y8YMHT16tNi3hvA+hd+VWSwWSdLQoUM1cOBAhYaG6o033lBGRoZ93U8//VS5ubkaM2aM/cIQKL2UlBRNmjRJvXv31lNPPSWr1aoHH3xQJ06cUMeOHdW3b19t3bpVTzzxhAYOHCibzUaRXoGEhYVp4MCBslqtSklJsS+/4YYb1KdPHwUFBemNN97QTz/9JEkU6edgGEaR0wTHjx+v119/Xdu3b1erVq00YMAApaena8KECTp58qSuv/56TZgwQX5+fswNXAEUXsSrUJUqVfTJJ5+oUaNGWrlypf33Vo0aNXTfffepXbt2WrJkiT755JMij/PmIh2+gdrcN1Gblz9qc99GbX7xqM19G7V5AUai+4DCFJtMJu3fv1+9e/e2f0u0bNkyXXnllXryySdlNpv1wgsv6MiRI7JarQoICNCUKVPUtGlTN0aPi1U4wumHH37Q559/rvDwcHXq1EmNGzfWrFmztHbtWl133XV6+OGHVblyZfvjDh06xMWKyiglJUXr1q3ToUOH9Morr0gqGD02cOBAnTp1SnPnzlXNmjW1ZcsWHTx4UNdee60aNGjg3qBxUQqPs0OHDkmSKleurGrVqmn+/PkaP358kW/nJWnjxo367LPP9PTTT+vSSy91U9Se6+zTpmfMmKG3335bnTt31q+//iqLxaJrr71WY8aM0fr16zVr1izZbDadOXNGQ4YMUdu2bSUxgsibnZ27wqK8Vq1aatWqlQ4dOqQ+ffqofv36evvtt+2jNVNTU/X999+rU6dOXl+co+KjNvdt1Oblj9rc91CbOxe1uW+jNv8fmugV2Pz583XdddepWbNmkqRdu3Zp/vz5MpvNevnllyVJP//8s15//XXVrl1bTzzxhC6//HKdOHFC6enpqlmzJnPtebF/vtE988wzuu222/Tbb7+pXr16evjhh3X99dcrMTFRa9euVUxMjAYOHKgqVaq4OXLvVVhcTJgwQZ999plq1KihRYsWqVq1apKkjIwMDR48WGfOnNHMmTPtczHCuxXm/euvv9b48eMVGhqqsLAwjR8/Xtdcc43mzZunCRMmFCvWc3Jy7Bcvwrl98803+uqrr3T77bfr9ttvV3Z2tlauXKlZs2bpX//6l5599lmtWbNGa9as0eHDh/XBBx8oICDA3WHjIpz9IS0hIUGLFi1S3bp19ccff+j555/XQw89ZC/WGzRooLffflshISFFtuHtp4mi4qI2923U5uWP2tw3UZu7DrW576E2/wcDFdKBAweM559/3ti/f7992axZs4ybbrrJ6NatW5F1f/zxR6NHjx7GiBEjjJ9//rm8Q4WT7d27t8jtAwcOGK1btzYWLVpkGIZhbN682YiMjDQGDhxobNy40TCMgn8bnTp1Mt566y3DZrOVe8zervA1y83NtS+bPn260b59e+Pdd981Tp48aV9+6tQpo0OHDkb37t0Ni8VS7rHCNTZs2GBERUUZiYmJxpo1a4xHHnnEuPPOO409e/YYhmEYc+fONSIjI4333nvPzZF6jz179hjdu3c3mjRpYnzzzTf25ZmZmcbcuXONBx54wDhx4kSxx+Xn55dnmHCSf74fLly40LjllluMX3/91cjPzzdefPFFo1GjRsa8efMMwzCMgwcPGm3atDE6d+5s5OTkuCNkoFSozX0XtXn5ozYHtbnzUZv7Fmrzc+NcigqqXr16evHFF1W/fn1t27ZNKSkp6tOnjwYMGKCdO3dqxowZ9nVvvPFGPfXUU9qxY4eWLVumnJwcN0aOi/Htt9/q0Ucf1fLly+3Ljh07Jn9/f913331KT09XYmKiOnToIMMw9MYbb2jLli3q27evHnjgAd1zzz1crKiUjP9+M7tx40a98soreu2115SamqpBgwapffv2Wrt2rT777DOdOnVKUsF8fB999JFee+21ivNtrI9ZuHChDh48KKkg/4Zh6IsvvtCDDz6ofv366brrrtO+fftksVj02GOPac+ePerVq5eefPJJJSYmKj093X4qP/7nn/PsXXrpperVq5fCw8P1xRdf2JdXrlxZt956q7Zv366dO3cWeYxhGF57pXdf9vjjj2v27NlF5vr97bffdOedd6px48bauXOnNm/erHvuuUcTJkzQggULVLduXX3wwQdq0KABI5zgFajNfRO1efmjNvc91OauQW3uu6jNHeNfcwVUWDiEhobq1KlTSkxM1OHDhzVhwgQNGDBANpvNfupo//79JRVcUCM+Pl6XXnoppzB5sfDwcLVs2VJz586VJHXs2FHh4eGqXbu2/vjjD33//feqVKmSXnrpJR05ckSdOnXSCy+8oG7duhU5lQ0lZzKZ7Kfk/utf/9JXX32lHTt2aMSIEXriiSdkGIZWrFghPz8/de7cWdWqVVOVKlU4NddLWSwWffLJJ5oxY4bmzZunOnXqKC8vTwcOHFCzZs1ksVg0bdo0tWzZUvfff79efvllPfPMM3rppZc0cOBAdevWTVWrVnX30/A4Z5/i/t1338lsNqtWrVq666675OfnpxkzZighIUEjRoyQVHDBmkaNGhU7jmg0eKdGjRrp9ddfV2hoqLp27arAwED9/fffuuqqq/T3339r8eLFat++vQYMGKC8vDy9/PLL+uOPPzR8+HC98cYbkirYaaKocKjNfRe1efmjNvct1OauQW3u26jNHaOJXgGd/UZVtWpV9ejRQ59++qkmTJig0aNHa9CgQTKZTJozZ478/PzsBVpsbKybIoazNGnSRA899JA+/PBDzZ49W35+fmrfvr1efPFF1apVS99++61at26t0NBQGYahyMhI3XrrrfaLfaD0/vjjDyUkJGjs2LG69957lZKSoi5duuj111/X008/bb8w2IcffqiAgAD17NmTC6p4MX9/f82fP1/Dhg1T3759NXv2bNWpU0c9e/ZUUFCQfvnlFx09elR9+/ZVkyZNVKNGDSUnJ2vYsGFatWoVRfo5GIZhPyYSEhL06aefKiQkRJdddpkGDx6sjh07SpLeeust7d69W9dee612796tzMxMNW/e3J2h4yIVNhYfe+wxVapUSePGjZPNZtODDz6o4cOH6+TJk/r999+1b98+denSRSEhIapVq5ZatGihffv2KTAw0L6tiliko+KgNvdd1Oblj9rct1CbOx+1ue+iNr8wmugVxL59+1S/fv0iBUDhAXDTTTfZC4XCYn3gwIHy8/PTG2+8oYCAAD344INujB7OUJjvq6++Wt27d5fNZtPMmTNlNpvVtm1bnTx5UgcOHFBYWJikggsahYeHq1evXlyk6iIcO3ZMAQEB6tKli9LS0vTWW2+pa9eu2r17t1566SVNmDBBw4YNU2hoqG699VaK9AogNDRU06ZN0yOPPKLevXtr7ty5uueeeyRJU6dOlSS1bNlSUsHpjRMnTlRcXJwqV67stpg91dkjFGbOnKmlS5dqxowZCg0N1WuvvabJkyfLMAx17NhRJpNJkydP1p49e/T444/rnXfeKbYNeJ/C3119+/aVzWazX1zxwQcfVN26dTVt2jSZzWbFxsbKarVq79696tGjh+6++25JRUdKAZ6E2hzU5u5Bbe57qM2dh9oc1ObnV3GfmQ85ePCgOnTooNWrVxeZt8pkMtnn9rrhhhvUs2dPhYeHa8KECUpJSVH//v31zDPP6JZbbnFX6HCSwje6v/76Szk5OWrcuLEGDx6sZs2aafr06frqq69UvXp1XXfddZo+fbq6du2quXPn6qmnnqJIL6MTJ05IkqpUqaIrr7xS+/fv19KlSxUWFqYxY8ZoypQpSk5O1qhRo7R48WINGDBAdevWdXPUuBhnz5UYEhKiGTNm6PLLL1ffvn11+PBhSVJwcLB27typH374QQkJCfr222/VvHlzVatWzV1he6Qnn3xSP/30k8xms6xWq6xWq3bu3Klhw4bp2muvVU5Ojn799VfVr19f7777rtavX68OHTro2WefVXh4uLZu3erup4CLZLPZZDKZZDKZdObMGUlS//79NXz4cL388stauHChJCkmJkabNm3SM888o549eyo1NdU+AurskVKAJ6E2B7V5+aM29z3U5s5DbQ5q85Kp2M/OR9SrV0/vv/++Ro4cqTVr1lywWI+IiNDzzz+v3377Tb169VKDBg3cFDmcobBIX716tQYMGKDu3btr8uTJCgoK0sMPP6zIyEjNnDlTGzdu1AsvvKA+ffrojjvu0EcffaTGjRu7O3yvlJKSoueff16rVq1S8+bN9cwzzyg8PFwbNmxQs2bNFBQUpDNnzqhJkyZq1qwZH4YrgMLjbNu2bVqyZImWLVumwMBAzZ49W5deeqn69Omjw4cPq2vXroqOjtYLL7yg7777TtOnT1f9+vXdHb5Hyc7Olp+fnwYNGqTNmzfLbDYrOztbu3btksViUXp6uubNm6e+fftq+PDhkqQJEyZo9uzZ6tixowYNGqTk5GQ9+eSTkiruqYIV2dkjVGbNmqUXXnhBQ4cO1cmTJzVgwACNGDFC48aN04IFC3TDDTfo9ddfl2EYioqK0uLFi+Xv7y+r1co8m/BY1Oa+jdq8/FGb+x5qc+ehNge1eckxnUsF0apVK73++ut64oknNHnyZLVp0+ac3wDdcMMNstls+vzzz1WpUiU3RApnM5lMWrNmjZ566ik98sgjslqt2rhxo06dOqWnn35aQ4YM0TvvvKP/+7//04ABA3Tfffe5O2SvZ7PZFBQUpE8//VQBAQG67bbblJqaqqNHj6pOnTqSpHXr1qlu3bp68sknmWvPyxUW6StWrNDzzz+vOnXq6OjRo/rxxx81fvx4zZ49W3379tWAAQOUmJioqVOn6vDhw6pcubIuueQSd4fvcYKDgzVu3DhVrVpVvXv31rx58xQdHa1XXnlFfn5+2rJlizIyMhQTE6OGDRuqdu3aMgxDGzZsUN++fXXbbbcpNzdXixYt0l9//aVatWq5+ymhlM6eZ/Pjjz9Wx44dtXPnTj3wwAOaOXOm+vXrJ8MwNGHCBPn5+alnz55q3769/XEWi0X+/pSw8GzU5r6L2rz8UZv7Fmpz56I2B7V5KRioUNavX29ce+21xtdff21YrdYi9x09etQYP368sWPHDiMnJ8dNEcKZbDabkZWVZQwYMMD44IMPDMMwjIyMDKNly5ZGx44djVGjRhl///23cezYMePZZ581evfubWRmZho2m83NkXuHwmPon8eSYRjGr7/+ajz99NPGoEGDjFWrVhk2m83o37+/0alTJ+O+++4zYmNjjV27dpV3yHCSfx4jW7duNW644QZj6dKlRm5urrFkyRKjUaNGxtixYw3DMAyLxWL06tXLiI2NNY4cOeKOkL2CxWKx/7xp0ybjoYceMq677jpj27ZthmEUHGvPPvusMWTIEMMwDCMnJ8cYMGCAsWTJkiI5yc7ONjIzM8s3eFyUwvwVvp9u2LDBaN26tbF3717DMAxj1apVRqNGjYw77rjDOHjwoGEYhjFr1iyjUaNGxsqVK90TNOAE1Oa+hdrctajNfRe1uWtQm/suavOyYTqXCqZVq1Z68803NXz4cK1evdq+/K+//tLrr7+uefPmKTAwUEFBQW6MEmX1+++/Kzk5WRkZGfZv4LOzs7V7927Vq1dPeXl5evXVV3XnnXdqwIAB2rRpk958803l5+frhRde0JQpU1S5cmWfOM3GGQq/WfXz89NPP/2kiRMn2u9r3LixBgwYoCpVqmjhwoVKTk7WlClT1K1bN7Vu3VqLFi1SkyZN3BU6LlLhMWL895T73377Tc2aNVPnzp2VnZ2tpUuXqm3btlq2bJlefPFFmc1mzZgxQzfeeKPy8/PdGbpHKzy9c9KkSRo9erQqVaqkSpUq6cEHH1RSUpL8/PwUExOjPXv26I033tCQIUN06tQp3X333TKZTPYpEYKDg7kYlBc5ffq00tLSJP3vffXMmTOqXr266tatq19//VVz5szRuHHjVKdOHQ0ePFiHDh1S37599dZbb6l169buDB+4KNTmFRu1efmiNvdd1OauQW3um6jNy85Hxtv7lsJi/YknnpCfn5+ioqI0efJkrV69Wp999pmuvvpqd4eIMnjttde0fv16HT16VE2bNlWrVq3Ur18/Va9eXb1791ZAQIA2bNigM2fO6P7771fz5s01b948rVixQjt37tTChQs5dbGEvvnmG/3xxx/asWOHrrnmGjVq1EiGYWju3LkKCAjQs88+K0lq2rSpBg0apBdeeEETJ07UkCFD1KtXLzdHj4uxePFi7d27V35+frrtttt04403SpL+/PNPhYSE6MyZM5ozZ47q16+vxx9/XF999ZUmTJigtLQ0vfjii3rrrbf4IHwB27Zt07JlyzRt2jRFRkbq5MmTmjlzpvr06aP58+erS5cuOnjwoH766SfVqlVL77//vv0iR8yx6H2GDx+uw4cP6/Tp07r55ps1cuRISVJeXp6aNGmi9PR0LV++XM2bN1eHDh0UERGhhx9+WPfff78mTpyotm3bSvKx00RR4VCbV0zU5uWH2tx3UZu7HrW5b6E2vzi+94x9RGGx/uSTT9qvTr5w4UK+ffdSr7zyipYuXapp06apatWq+vzzz7Vx40bFxcWpcePG6tGjh0JDQ/XCCy8oKChIzZs3l8ViUe3atdWlSxfdeeedCgwMdPfT8ApTpkzRqlWrVKdOHVWvXl2ffvqpTCaTGjVqpNdff12jR4+W1WrViBEjJBWMeomJidGqVau0cuVKxcTE8C28l5o4caK+/PJL3XLLLUpPT9f8+fP1xhtvqE2bNurXr582bdqk9PR0/fLLL+ratavCw8MVFhamJk2a6ODBg8rPz6dI/4esrCyFhoYWWRYYGKjAwECFhobKbDarZs2aGjFihKxWq/r166fFixfrueeeU05OjoKCgmQymXy2SPN2PXv2lNVqVd++fXX69Gm98sorslqteuGFF3T33Xfruuuuk8lk0vr16zVkyBBVqVJFZrNZHTt21JVXXqm4uDj7tsg/vB21ecVCbV5+qM19F7W581Gb+zZq84vnm8/aR7Rq1UpvvPGGHn74YS1dupSrvXupV199VcuWLdPcuXPtORwyZIjuv/9+/fLLL2rcuLH9NNDAwECdPHlSe/bs0cqVK/X7779r9OjRCg8Pd/Oz8A5z587VF198oalTp6pRo0YKCQnRsWPH9NNPP2ny5MlKT0/XhAkTNGbMGPn5+dmvTu7v768uXbqoT58+FOle6tVXX9Wnn36quXPnqmnTpsrOztY777yjhQsXqmXLlqpcubJat26t77//XgcOHFCXLl0kSSkpKWrZsqWeeuopPgz/Q+Fr0rZtW91+++325fn5+crMzNShQ4d01VVX2Yvwjh07av78+brrrruK/M4yDMNnizRv9tBDD8lisWj27Nn2iyVWr15d77zzjk6ePKnq1aurXr162rFjh9LS0tSsWTNlZGRo/vz5qlWrloYOHSpJjHJChUJtXjFQm5cfanPfRW3ufNTmvo3a3Dn4l1/BtWrVSlu2bFFISIi7Q0EZJCUlafbs2XrkkUfUuHFjWSwWSVKVKlV01VVXadmyZfryyy8VGhqqV199VW3bttVbb72lQYMGyWQy6e2331bt2rXd/Cw8n2EYys/P188//6whQ4bouuuusy+/7LLLdNdddykiIkLPPPOM1qxZo0mTJunpp5/WTz/9pOrVqyspKUlLlizhlFwvNWnSJC1cuFAff/yxGjduLKvVqpCQEEVHR2vTpk36/ffflZmZqejoaDVu3Fg5OTnq16+fLrnkEn3//ff2+WzxP0ePHtXOnTvl7++vr7/+WjfddJNat26t++67T82bN9cDDzygJ598UvPmzVPz5s0lSbVq1VLHjh3VtGlTXXXVVfZtMYLI+zzzzDPauXOnPv/8c1WqVEn5+fkKCAhQnTp1lJaWpvnz5+uvv/5SmzZtdOutt+rqq69Wp06dVLt2bQUEBOitt96SVPAe7MtFOiomanPvRm1ePqjNfRu1ufNRm/s2anPn4cKiPoAi3XtddtllevTRR/Wf//xHH3/8sfz9/eXv76/p06dr7dq1uvXWW3XLLbcoLS1Nffv21Q033KCpU6fq7bff1qJFi9S0aVN3PwWvYDKZlJGRoS1btuiKK64oslwqGM0SExOjxx57TNu3b1dYWJjmzZun+vXr69JLL9XChQtVv359d4WPi3TgwAF7MZCfn2//edeuXdq2bZtefPFFDRgwQI899phSU1P17rvvKjQ0VMHBwZo3b54aNWrkzvA9Uu3atXXrrbfqkksu0YIFC5Sdna0333xTnTp10rJly3THHXeoV69e6tmzp959913NmjVLY8aM0V9//aUBAwbI39/f3piA94mJidEll1yir7/+WkePHlVAQIAk6Z133lFWVpZOnDihbdu2acKECVq5cqVmzZql+Ph4DRs2TEuWLFFAQIAsFgsf0lBhUZt7L2rz8kFt7tuozZ2P2ty3UZs7kQHAo/3555/GW2+9ZbRq1cpYtWqVsWDBAuOGG24w1q9fb19ny5YtRnR0tLFq1So3RurdcnNzjbvuusv44osvDMMwDJvNZr/ParUahmEY+/fvN2688UZj/fr1xpkzZ4xTp065JVY4x9k5HjJkiHHLLbcYmzdvNgzDMKZPn27ExsYaK1euNNLS0oyUlBQjJibGeO211wzDKPg3kZeX55a4PV3h65qWlmZcf/31xrx58wyr1WqsWrXKeOyxx4zIyEjj3//+t7F48WJj3LhxxqBBg4wBAwYYw4YNs7+mZ+cG3uPsvH344YfGrbfeasyYMcM4c+aM8cQTTxh33nmnsX//fvs6PXr0MPr06VNsOxaLpTzCBYAyoTYvH9Tmvuf/2bvv+KYK/f/j7yRt2kIpq1CGAhfQglgQiuNqFb8yLuAAFBBEQDYiIA5EQKSAiAUcIPJjaGVelohyEUGR4QCVvbQIgjKFMkvpTHJ+f2AjFcNsepLm9Xw8+oCcnDSf5tOTvvvpGWRz7yCbBy6yed7jdC6Aj4uKilKrVq0kSUOGDNGpU6f00Ucf6dZbb3UfhlOyZEmVKVNGERERJlfrnwzDkNPpVEREhNauXauHH35YFotFhmHIYrHIarXql19+0S+//KLQ0FANGjRIhQsXVqdOndS2bVuzy8c1slgs7nO6TZw4UT179tQLL7yge++9V8uXL9e4ceN09913y+VyqVixYmrQoIG2bt2qrKws2e12Wa0czPVPLBaL+zV74okn9Omnn+rOO+9UgwYN1KBBA8XGxspqtWro0KG67bbbVKhQIU2ePNm9lxEXKvJfOb23Wq1q06aNXC6Xpk6dqo8//liSNG3aNJUuXdq9DdWrV0/ff/+90tPTc+2ZG+iHiQLwbWRz7yObByayuXeQzQMX2Tzv8S4D+BDDMNz/d7lc7v9HRUWpQ4cOateunSIjI/Xrr79KkvswnE8++UQOh4PDFq+RxWJRWFiYnnnmGS1atEizZ892LzcMQ6mpqVq2bJkWL16s9PR0tWzZUu+++64efPBBkyvHtbhw27LZbMrIyJAkTZo0SbVq1dK8efPUs2dP3X333ZL+OnQ4MzNTVatWJURegZxfYh544AHt27dPW7dulSQ1a9ZM0dHRmjNnjqZNm6aKFSsqMzPT/RobXKjIL124TVmtVqWnp0uSnnjiCT333HM6duyY6tWr5/6ZlXOe0nXr1qlChQqc2gKAzyKbm4NsHljI5t5HNg8sZHMvMmsXeACevfnmm8Znn31mZGdnG4ZhGPPnzzf2799vnDhxwnj33XeN++67z1iwYIFhGIYxbtw4o2bNmsaOHTvMLLlAcDqdxttvv21ER0cbkyZNMtLT0933ZWZmGqNHjzYefPBB48SJEyZWibyybt26XLczMzMNwzCMPn36GPfee6+xfv16933jxo0z6tSpY+zZsydfaywI3nrrLaNOnTrG//3f/xnt2rXLtf1kZGS4/59zaDb8U2ZmpjF27Nhc/c3ZhubNm2fUq1fPeO+999z3d+vWzWjSpIn75xyHCQPwZWRzc5DNAwvZPH+QzQMD2dw7+JMS4IPS09P17rvvqnz58lq3bp0mT56sxMRE3Xjjje7DRydNmqRPP/1U27dv1+zZs1WjRg2Tq/Z/VqtV3bp1U0hIiN555x2tW7dO1apVU3h4uA4cOKDVq1dr2rRpKlGihNml4jqtWbNGU6ZM0b59+9wX0Nm3b59Gjx6t8ePHq1evXurXr5+mTJmiFStWaOrUqZozZ46qVKlidul+5/7779fChQsVGRmpt99+O9f2ExISIun8Xi4cguvfdu3ape3bt2v06NF644031KtXL2VnZ+vWW29V69at5XQ6NWXKFFmtVn333Xc6duyYlixZ4r5QFXs5AfBlZHNzkM0DB9k8/5DNAwPZ3DsshnHBMWoATJVzvipJ+vDDDzVlyhRlZmbq7bffVr169dzrHT16VDNnztSSJUv03nvvEdK9YOvWrZo7d65+//13hYWFKTo6Wi1btlTlypXNLg154OjRo/r000/1yy+/KCMjQz/88IPGjRunu+66y70N9u7dWytWrFBYWJhmzZrFdnYdBg0apK+//lorV66U3W53n9MUBUdWVpZ+/vlnzZo1Sxs2bFDhwoU1a9YsFStWzL3O/Pnz9eqrr+rmm2/WwoULFRwcTEgH4NPI5r6DbF6wkc3zF9m84CObewdDdMAH5PzQSktLU6FChSRJM2fO1FtvvaWiRYtq5MiRuuOOO9znrJLOBw273a7ixYubVTbgly4MBl27dtX333+vNm3a6JVXXpEk94VVJOm1117TY489purVq5tWrz/LeW87ePCg2rVrp8cee0x9+/Y1uyzksQu3qfbt2+vnn39W3bp1NW7cOIWEhLgvtCdJa9eu1R133MFeLgB8GtkcyD9k8/xDNg8MZHPv4fgMwAdYLBatWLFCo0ePVmZmphYsWKBx48Zp2rRpevzxxzVw4EB98803uS5uFBUVRUj3sgtfb/7eWDAYF1wcZ/Lkydq+fbseeeQRnThxQtOnT5d0/sIqDodDkvTKK68Q0q9Dzh4tJUuWVIUKFXTixAmTK0Je+Pt7Y8421alTJ50+fVrDhg1TkSJF1Lt3b6Wmpio4ONj9mLvvvpuQDsDnkc19E9m84CGb5y+yecFENs8/vEKADzh06JDmz5+vBx98UDabTTabzX018lq1aikjI0Ph4eEcYpXPLny9ee3914WHJ+b8++uvv+rTTz/VO++8o5iYGE2bNk3r1q1Tw4YNVa5cOQJEHgsLC9Mbb7yhMmXKmF0KrtOFpzbIzs5WZmamwsPDlZqaqkqVKum1115T+fLlFRERoRUrVig7O1vSxe+hbGMAfBnZ3DeRzQsGsrn5yOYFB9k8f3E6F8Bkv/76qx566CFFR0dr7Nixqlq1qvs+p9Mpm83mvs25yoCrk7PNbNiwQRs3btSpU6d0xx136I477lBWVpb7QjonTpzQuXPnVKFCBZMrLvj+/r4G/3Hhz6ApU6boxx9/1MGDB3X77berRYsWqlmzpjuAu1wuORwO2e32XOEeAHwd2RzwHrK57yGb+y+yef7jVQNMVqVKFbVt21ZJSUn6+eefc9339x9mhHTg6uQcjv30009r586dcjgc6tWrl8aNG5dr+8o5pBHeR0j3Xzk/gyZOnKhp06bpP//5j8aMGaMvvvhC7777bq5Dgq1Wq/v8pYR0AP6EbA54D9nc95DN/RfZPP+xvz6Qz3L+Wnj8+HE5HA6VKVNGr776qhwOh4YMGaLIyEj9+9//NrtMwC/99ttvqlChgqxWqwzD0MmTJzVlyhT169dP7dq1U3Z2tubNm6eyZcsqOztbLpdLFouFX4KBS8j5uWUYhs6cOaP169dryJAhatKkiTZt2qSMjAy1bdtWx44dk3T+vMAA4C/I5oD3kM2BvEc2Nw9/fgDyUc6b3YoVK/Tkk0+qa9eu6t69uyRp+PDhevDBB9WrVy/9+OOPJlcK+BfDMPTNN9+ocePGWrlypXtbCw0Nlcvl0p133qmDBw+qfv36euSRR9SmTRsNHz5cq1atIqQDl3HheUuLFSumrKwsFStWTF999ZW6dOmil156SY0aNdK4ceM0e/Zsk6sFgCtHNge8g2wOeA/Z3DwM0YF8ZLFYtGbNGj333HNq3Lixevbsqd27d6tHjx6SpJEjR6pp06bq3bu31q5da3K1gP9wOBy699571bp1aw0cOFArV66Uw+GQw+GQYRhas2aNOnXqpHr16mnEiBEqVKiQzp49q6+//trs0gG/MG3aNA0aNEiSFBkZqQkTJmjgwIHq37+/2rVr515+/PhxM8sEgKtCNge8g2wOeBfZ3BwM0YF8YhiGMjIyNHPmTPXs2VP9+vXTHXfcIYvFom+//VaPP/64pPNh/d5779XLL7+sjIwMk6sGfN+kSZPUpUsXSef3GnvooYf04osvavXq1SpatKhat26tMWPGqEKFChoxYoT7HHAWi0XlypUzs3TAbxQtWlT79u3TyZMn1a9fP+3bt0+VKlVSy5Yt5XK5ZBiGfvvtN5UqVcrsUgHgipDNAe8gmwPeRzY3h8UwDMPsIoBAkZWVpY4dO6pNmzaqX7++EhISFBQUpLZt26pjx46qU6eOhg0bpsjISCUnJ/OGB1yBY8eO6cyZM7rpppvcy+Lj47Vo0SK9+eabuv/++zVlyhSNHz9ezz//vAoXLqyDBw/q448/1ty5c/Wvf/3LxOoB35Vz6LUk/frrr3ruuefUsmVLdejQQZs3b1aXLl106623ymazKSsrSykpKVq0aJGCgrjkDgD/QDYH8h7ZHPAOsrn52BMdyEd2u1133HGHihUrpg0bNiglJUUPP/ywKlWqpIoVK+qrr75S9+7dlZGRQUgHLuPo0aPau3evSpcurZtuukkbN25UixYtlJGRofj4eLVo0UL9+/fX119/rV69emnEiBH68ssvtXDhQv3888+aPn06IR24wJQpU3TgwAH37Q0bNkg6H9irVKmixx9/XJMmTdKvv/6q2rVra/HixYqLi1N0dLTi4uLcId3pdJr1JQDAVSGbA3mHbA7kLbK572FPdMBLcv5K+PPPP+v06dM6evSomjZtKrvdLkkaNWqUTp48qTFjxkiSBg4cqKZNm6pq1aoqW7asmaUDfuGbb77R119/raefflopKSmy2+168sknVaZMGX344YcKCQnR0KFDtXjxYo0ZM0YNGjRQamqq7Ha7srOzVbhwYbO/BMBnJCUl6a233tLEiRMVFBSkn3/+WS1atNDdd9+tJk2a6JFHHlFISIheeOEF1axZU0888YSCg4Mv+jxOp1M2m82ErwAALo1sDngX2RzIO2Rz38Se6ICXWCwWLV++XJ07d9b777+v8ePHq3Pnzpo5c6Yk6dSpUzp48KAOHDigMWPG6JtvviGkA1chIyNDGzduVP/+/fX4448rOztbc+bM0cmTJ9WhQwdlZmZq2LBhatasmQYMGKDPP/9coaGhstvthHTgApmZmapWrZree+89BQUFafXq1ZKkNWvWqHz58lq8eLFatmyp9evXKzw8XGvWrHGH8b/v2UJIB+CryOaAd5HNgbxBNvddDNEBLzlw4IASEhLUr18/ffDBB5oyZYo2bNig0NBQZWVl6ZlnntGJEyfUuXNnLVu2TFOmTCGkA1ehYcOGuv/++/Xdd98pOjpaVqtVUVFRmj59us6cOeMO6/Hx8apfv75GjRqlzMxMs8sGfMqxY8c0depUnTlzRsHBwUpJSVH//v2VmJio4OBgxcfHKyEhQbfddpvGjx+vrKwsrV27VpMmTZJEMAfgP8jmgHeRzYHrRzb3bZzOBfCS7du3q3///lq2bJkOHz6s9u3b6+6771avXr00d+5cde7cWaGhodq3b59Kly6tEiVKmF0y4DdcLpesVqtmzJihI0eOaPPmzapevbratGmj6OhoHT16VB07dlSJEiWUmJio0NBQLggG/INDhw5p8ODBioyMlMVi0SOPPKLChQtr4MCBqlOnjrp37+4+P+m6det04MABDR8+XI8++qiGDx9ucvUAcOXI5oD3kM2BvEE2920M0QEvyXnze/LJJ/Xaa6+pXr16GjZsmFJTU3Xvvfdq8ODBatmypdllAn4l53ymf/zxhywWi6KioiRJn332maZNm6Zbb701V1hv3bq1KlSooJkzZ+a6mjmAv6xcuVLDhg3T0aNH9f777ysuLk7r16/XwIEDdeedd6pjx466+eab3esfOXJEpUuXZk8XAH6FbA7kPbI5kPfI5r4ryOwCgILgwvBgs9kUGRmpkiVLyjAM9e7dW82aNdOwYcPc6990003ugAHgyuWcz3TkyJGy2WyKiorSxIkT9eCDD8pisWjatGmaP3++WrVqpdTUVM2fP1/Z2dnuxwL4S87PruLFiyssLEwxMTH6+OOPddNNN+n222/XqFGjNHDgQFksFnXq1ElVqlSRJPfpDbhQEQBfRTYH8gfZHMg7ZHPfx57oQB5ZtmyZ3n77bZ09e1ZNmjRRp06dFBoaqrZt2yo6OlotWrTQDTfcoKVLl2rhwoWaP3++ypUrZ3bZgF9JSkrSM888o7Zt26p06dKaOXOmUlNTNXPmTEVGRmrp0qX64IMPlJqaqqNHj+rzzz/nfKbA3+Qccp0jMzNTaWlp2rZtmz744AMVK1ZMr7zyikqXLq3169frlVde0c0336yXX35Z5cuXN7FyALhyZHPA+8jmwPUjm/sPhujANcrZdCwWi3777Td16NBBTz31lCRpyZIlqly5svr16yebzaZXXnlFhw4dktPpVHBwsMaOHatbbrnFxOoB/5OUlKRVq1bpwIEDev311yVJqamp6tq1q86cOaMZM2aoVKlS2rx5s/bv369atWqpUqVK5hYN+JgLD52eN2+edu/eLZvNpnvuuUf33Xef1qxZow8++EDFixfX0KFDVaJECW3YsEEzZ87U22+/nSvgA4AvIZsD+YtsDlw/srl/YYgOXKVZs2bptttu06233ipJ+umnnzRr1izZbDaNGDFCkrR+/Xq99dZbKleunJ599lmVL19ex48f1+nTp1WqVCkuVARchZxgMXLkSC1atEglS5bU3LlzVbx4cUlSSkqKunfvrnPnzun999/ncGzAgwtD+tSpU/Xee++pWbNm+vnnn+VwOFSrVi0NHTpUq1ev1ocffiiXy6Vz587p6aefVsOGDSVdvKcMAJiNbA7kL7I5kDfI5v6HVxq4Cvv379fOnTtVpEgR97Iff/xRq1ev1q5du9zLbr/9dvXr10+HDx/WxIkTtXnzZkVFRSk6OpqQDlyhnL/x5pw3cfDgwerRo4esVqvmzZunU6dOSZIiIiI0ZcoUOZ1O9e3bV06n07SaAV+WE9LXrFmjX3/9VWPHjtWwYcM0ffp0dejQQZs2bdLYsWN1//33q0OHDrrxxhsVHh6u+++/3/05COkAfAnZHMg/ZHMgb5HN/Q8XFgWuQoUKFTRkyBAVKlRIW7duVUhIiDp27Cin06m33npLU6dOVbdu3SRJd955p5577jkNHz5coaGhuvXWWxUaGmryVwD4h5y/yq9bt07Lly9XkSJF1KFDB3Xr1k1paWlauXKl7Ha7HnvsMRUtWlQRERGaN2+ezpw5w8VUgEvYs2ePJk6cqO3bt6tp06aSpLCwMDVo0EBnz57VsmXLdOLECdWvX1/169d3P87hcCgoiNgIwLeQzYH8QTYHvINs7l/4kwVwhXL+8l6oUCGdOXNGiYmJGjx4sHbt2qUuXbqoX79+mjVrlhITE92PueOOOxQfH6+uXbsS0oGrYLFYtHz5cnXr1k1Hjx7V/Pnz9dJLLykpKUnPPvus7r77bi1btkyLFi1y7/VSpEgR3XDDDSZXDvgWl8uV63aZMmXUvn17RUZGavHixe7l4eHhuu+++7R9+3bt3Lkz12MMwyCkA/A5ZHMg/5DNgbxBNvdvDNGBK5RzqI0kFS1aVG3atNG//vUvjRw5UklJSerWrZvat2+v6dOna9q0ae5169atS3gArtLevXuVkJCgYcOG6f/9v/+n6dOna926dXrrrbeUlJSkfv366d5779WcOXO0dOnSi8IIgNznSPz222+1bt06HT16VA899JBefvll/frrr0pISHCvX7JkSUVHR+c6LYKU++cfAPgKsjmQf8jmwPUjm/s//nQBXMK+fftUsWLFXOeZyjmU7d///rdsNpvmzJmjkSNHavDgweratausVqveeecdBQcHq127diZWD/ivI0eOKDg4WM2bN9epU6f07rvv6tFHH9WuXbv06quvauTIkerTp48KFSqk++67j3PBAX9jGIZ7u0hISNDChQsVFhamsmXLqnv37u7DRd99913t2rVLtWrV0q5du3T27FnVrFnTzNIBwCOyOWAOsjlwfcjmBQNDdMCD/fv3q0mTJho/frwaNGjgfsOzWCzusH7HHXdIUq6w3rlzZwUHB+uee+4xs3zALx0/flyRkZEqUqSIKleurN9++01ff/21IiIiNHToUB0+fFhNmjTRoEGD1Lp1a3Xp0sXskgGf43Q63ecfff/99/XJJ59o6tSpKlSokN58802NGTNGhmGoadOmslgsGjNmjHbv3q2+fftq4sSJF30OAPAFZHMg/5HNgetHNi84+PMg4EGFChU0efJkDRw4UF999VWuQ9Jywrp0/tyKbdu2VVRUlF5++WX98ssvat++vSpVqmRS5YB/SkpK0ssvv6wvv/xSNWvW1AsvvKDIyEh9/fXXuvXWWxUSEqJz586pevXquvXWW/llGPibfv366YcffpDNZpPT6ZTT6dTOnTvVp08f1apVSxkZGfr5559VsWJF/b//9/+0evVqNWnSRC+++KIiIyO1ZcsWs78EAPCIbA7kL7I5cH3I5gUPQ3TgEurVq6e33npL/fv3vyisX+iOO+5Qy5YtVb16dRUuXDifqwQKBpfLpZCQEC1cuFCrV69W1apVlZWVpcOHD7vPXbpq1SrdeOON6tevn8qVK2dyxYDvSE9Pl9VqVbdu3bRp0ybZbDalp6frp59+ksPh0OnTpzVz5kw99dRT6t+/vyRp5MiRmjZtmpo2bapu3bpp27Zt6tevnySxpwsAn0Q2B/IP2Ry4dmTzgsli5PzJHoBHa9as0bPPPqsxY8aofv36uc7xduTIESUmJqp58+aqWrWqQkJCTKwU8H05F1S58MIqOZKSkjR16lSdPXtWrVu3Vv369dW1a1clJycrJCREv/32m2bMmKHq1aubVD3gmwzD0Llz5/Tmm29qwYIFmjlzpmrXrq2NGzfKarXq9OnTmjdvnnr16qWaNWuqb9++OnTokIoWLarExESlpaVp+fLlmjt3rt59912VLl3a7C8JADwimwN5h2wO5D2yecHEEB24QjlhffTo0WrUqJEk6dixYxozZoz+97//6X//+59uuukmk6sE/MsPP/yglStXauDAge5lP/30kz744AOdOnVKzz77rCpUqKAlS5YoNTVVjRo1UpUqVUysGPA9F54jcf369Ro/frx27Nih6dOnq2bNmnK5XBowYIDOnTuniRMnKjMzU88884wefPBBNW/eXBaLRZKUkZEhh8Oh8PBwM78cALgiZHMg75HNgetHNi+4GKIDVyEnrI8dO1YxMTEaM2aMVqxYoTlz5vDXd+Ay1qxZo71792rHjh26+eabFR0dLcMw1KtXL3Xp0kUvvviie92kpCS98sorCgoK0tNPP6169eqZWDngH0aPHq0VK1aocuXK2rFjh86cOaNp06YpNjZWc+fO1QcffKAHH3xQ27Zt09mzZzV37lzZbLZ/3PMMAPwB2Ry4dmRzwLvI5gVPkNkFAP6kXr16GjdunPr16+e+Ovl///tfQjpwGWPHjtWXX36pG264QSVKlNDChQtlsVgUHR2tt956S4MHD5bT6dSAAQMkSdWqVVNsbKy+/PJLLV++XLGxsfwFHriErVu3asmSJZowYYJq1KihkydP6v3331fHjh01a9YsNW/eXPv379cPP/yg0qVLa/Lkye6LHHGORQD+imwOXBuyOeBdZPOCiSE6cJXq1aund955Rz169NAnn3yiatWqmV0S4NNmzJihxYsXa/z48YqOjlZYWJiOHDmiH374QWPGjNHp06c1cuRIDR06VFar1X1hlaCgIDVv3lwdO3YkpAMXSEtLU6FChXIts9vtstvtKlSokGw2m0qVKqUBAwbI6XSqU6dOWrBggV566SVlZGQoJCREFotFDodDQUFEQQD+jWwOXB2yOZC3yOaBg9O5ANcoPT1dYWFhZpcB+CzDMJSdna0XXnhBd999t9q2betenhMS1q9f777/oYce0vPPP6/KlSurRIkS2rhxoz7++GNVrFjR5K8E8B3PPfec7Ha7GjZsqAYNGriXb9u2Td26ddMbb7yh//u//3OH8E2bNumJJ56QpFzDpZztEAAKCrI5cGlkcyDvkc0DCyfZAa4RIR24NIvFopSUFG3evFn/+te/ci2Xzu/NEhsbq2eeeUbbt29XRESEZs6cqYoVK6pMmTL673//S0gHLnD48GHt3LlT27dvV//+/dWrVy999NFHkqSaNWvqiSeeUL9+/bRt2zb3XiylS5dW06ZN9eKLL6pq1aruz0VIB1DQkM2BSyObA3mLbB54OE4AAOA1ERERKl68uJKTkyXl/gu7y+WS3W5XXFyc3n33XZ09e1bVqlXT0KFDFRERYWbZgE8qV66c7rvvPv30008aO3asxowZo3Hjxmn69Onq0aOH/vOf/yg7O1tt27ZV7969FRoaqm+//VaZmZl68803OUwUAIAARzYH8g7ZPPCwJzoAwCsMw5DT6VRERITWrl0r6fxf2HPOIma1WvXLL79o+/btCg0N1aBBg9S8eXN99tlnZpYN+KSc7aZ3797as2ePNm3apA8++EBDhw5VxYoV9fLLL6tXr16qVKmS2rRpo82bN+u7775T4cKF9eGHH7q3PUI6AACBiWwO5B2yeWDinOgAAK9au3atOnfurCFDhqhdu3aSzoeOc+fOKTExUTt27NDWrVvVpk0bNW3aVGXLlmVvF+AfuFwuWa1WvfPOO/ruu+/0+uuv66abbpIkxcbGqnjx4jpy5Ihuu+02FSpUSJMmTZLNZpMk9nIBAACSyOZAXiGbBx72RAcAeNVdd92lnj17asSIEZo8ebIyMjJksVgUHh6unj176qabblKpUqXUsWNHRUdHE9IBD6zW87HtgQce0L59+7R161ZJUrNmzRQdHa05c+Zo2rRpqlixojIzM92HZ7OXCwAAyEE2B/IG2TzwsCc6AMDrzp07pxkzZmj8+PG68847Va1aNYWHh+vAgQNavXq1pk2bpurVq5tdJuA33n77bc2aNUtFixZVuXLlNH78eJUoUUKSlJmZqZCQEEl/7SEDAACQg2wO5C2yeWBgiA4AyDdbt27V3Llz9fvvvyssLEzR0dFq2bKlKleubHZpgF/ZvHmz+vTpo3Llyum9995TqVKlLlrnwouFAQAA/B3ZHMgbZPPAwBAdAADADw0aNEhff/21Vq5cKbvdTjAHAAAATEI2L/g4hgAAkK8u/Nstf8cFrl7OdtOrVy/ZbDZNmjRJkgjpAADgqpHNgetDNg8cDNEBAPnqwjBBsACuXs52U7JkSVWoUEEnTpwwuSIAAOCvyObA9SGbBw5O5wIAAOCnDh06pDJlyshms5ldCgAAABDQyOYFG0N0AAAAP+d0OgnrAAAAgA8gmxdMDNEBAAAAAAAAAPCAc6IDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAACvWr16taKjo/XJJ5+YXUqeOXLkiG655RaNGjXK7FIAAAAQoMjZAJB/LIZhGGYXAQC+LCEh4ZL3x8XF6Z577nHf/u677/Ttt99e8jEDBgwokM/xd1lZWWrcuLEyMzO1atUq2e12SdKkSZN05swZj4+LiYlR06ZN3be3b9+upUuXelw/NTVV8fHxstls7mXefo4+ffroq6++0vz583Xrrbd6fBwAAADMQ87OjZwNANcmyOwCAMDX3Xvvvbr77rs93r927dpcty0WyyVD79/XL0jP8XeLFy/WoUOH1LlzZ3ewl6SaNWteVS1nz569ZC0//PCD/v43YW8/R6tWrfTFF1/ovffe0//7f//P4+MAAABgHnL2pT83ORsArgyncwEAeM2cOXMkSY0bNza5krx31113KTw8XKtWrdKBAwfMLgcAAAABhJwNAPmLIToAwCtOnDihnTt3qlChQqpZs6bZ5eQ5u92u2NhYGYahVatWmV0OAAAAAgQ5GwDyH0N0AIBXbN68WYZhKDo6WhaLxexyvCI6OlqS9OOPP5pcCQAAAAIFORsA8h9DdACAVxw9elSSVLJkSZMr8Z6cr+3IkSMmVwIAAIBAQc4GgPzHEB0A4BVnz56VJBUqVMjkSryncOHCkqTU1FSTKwEAAECgIGcDQP5jiA4A8IoiRYpIkjIzM02uxHtyQn3RokVNrgQAAACBgpwNAPmPIToAwCvKly8v6a/DTQui5ORkSVLFihVNrgQAAACBgpwNAPmPIToAwCtq164tq9Wq3bt3y+FwmF2OV+zatUvS+a8VAAAAyA/kbADIf0FmFwAAvm7p0qX65ptvPN5fs2bNXLfPnDmjhIQEj+ufPXtWd999d4F8jgsVLVpUt912mzZt2qRt27apTp067vu+/fbbS9ZSsWLFiz73pWo5cuSIYmNjcy3z9nM4HA5t375dQUFBatiwocfHAQAAwDzk7NzI2QBwbSyGYRhmFwEAKJg+/fRTvfTSS+rTp4969+5tdjl56scff1T79u3VsGFDTZgwwexyAAAAEEDI2QCQvzidCwDAa5o0aaIKFSro008/lcvlMrucPLVo0SLZbLYC90sLAAAAfB85GwDyF0N0AIDX2O12DRgwQPv379dXX31ldjl5Jjk5WUuWLFGbNm1UrVo1s8sBAABAgCFnA0D+4nQuAAAAAAAAAAB4wJ7oAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADwIMruA/OJyueRwOGS1WmWxWMwuBwAAAAHMMAy5XC4FBQXJag2s/VrI5QAAAPAVV5rLA2aI7nA4tH37drPLAAAAANxiYmJkt9vNLiNfkcsBAADgay6XywNmiJ7zl4SYmBjZbDaTq/FNTqdT27dv5zXycfTJf9Ar/0Cf/Ae98g/06crkvE6Bthe6RC6/UmxL/oE++Qf65D/olX+gT/6DXl3elebygBmi5xwqarPZ+Ka5DF4j/0Cf/Ae98g/0yX/QK/9An65MIJ7OhFx+dXid/AN98g/0yX/QK/9An/wHvbq8y+XywNv1BQAAAAAAAACAK8QQHQAAAAAAAAAADxiiAwAAAAAAAADgAUN0AAAAAAAAAAA8YIgOAAAAAAAAAIAHDNEBAAAAAAAAAPCAIToAAAAAAAAAAB4wRAcAAAAAAAAAwAOG6AAAAAAAAAAAeMAQHQAAAAAAAAAADxiiAwAAAAAAAADgAUN0AAAAAAAAAAA8YIgOAAAAAAAAAIAHDNEBwIvsdrvZJeAK0CcAgC/i55N/oE8AABR8QWYXAABXLeuM5Eg1u4rLshqGKpcNlTXjiGSxmF3OpQWFS/aief95/aBXftUnyXu9AoBA4XBITpfZVVyW1TBU+YYbZc12SA6n2eVcms0qBeXxr5b0yTu80SsAAAIAPz0B+B9HqvTLBCnjuNmVXIYh59lUqUi4JB8ezoZGSjf39s5g1i965Sd9krzbKwAIFE6XdOiYlO0wu5JLMww5U1Ol8HDf/iNvcJBUvnTe/2ZJn/Ket3oFAEAA4McnAP+UcVzK+MPsKi7DkCstRQqOkM8PZ73J53tFnwAg4GQ7pKxss6u4NMOQKyNTsof4/nDWW+gTAADwEZwTHQAAAAAAAAAADxiiAwAAAAAAAADgAUN0AAAAAAAAAAA8YIgOAAAAAAAAAIAHDNEBAAAAAAAAAPCAIToAAAAAAAAAAB4wRAcAAAAAAAAAwAOG6AAAAAAAAAAAeMAQHQAAAAAAAAAADxiiAwAAAAAAAADgAUN0AAAAAAAAAAA8YIgOAAAAAAAAAIAHDNEBAAAAAAAAAPCAIToAAAAAAAAAAB4wRAcAAAAAAAAAwAOG6AAAAAAAAACumN1uN7sEXCF6lTeCzC4AAAAAAAAAgCSHQ3K6zK7ikqyGoco33ChrtkNyOM0u5/JsVikoj0egftAnyc965Y0+5SHfrQwAAABAvsvKytKjjz6qIUOG6M477/zHdX766ScNHTpUv/zyi6pWraphw4bp1ltvzedKAQAogJwu6dAxKdthdiWeGYacqalSeLhksZhdzaUFB0nlS+f9BNQf+iT5T6+81ac85MOlAQAAAMhPmZmZeuGFF7R7926P66Slpal79+56+OGH9cYbb2jOnDnq0aOHvvzySxUqVCgfqwUAoIDKdkhZ2WZX4ZlhyJWRKdlDfHsw622+3ieJXuUhzokOAAAAQHv27FHr1q21f//+S663dOlShYSE6KWXXlKVKlU0ePBgFS5cWMuWLcunSgEAAID8xRAdAAAAgH788Ufdeeedmjdv3iXX27p1q2JjY2X5c28mi8WiOnXqaMuWLflQJQAAAJD/OJ0LAAAAAD3xxBNXtF5ycrKqVq2aa1nJkiUveQqYf+J0mnNxK6thSDkfPsyQ8de/vlzqn6+lK4/7SZ+8wEu98gc57zdmve/gytEr/3j/473PP/ok+VGvTPwZdaXvNwzRAQAAAFyx9PR02e32XMvsdruysrKu6vNs3749L8u6Ina7XZVvuFHO1NTz5wf1AykpZ80u4ZKsoSGypadr7+4DV/094Al98g5v9MrfmPG+g2sTqL3yt/e/QH3v87c+Sb7fK3/4GcUQHQAAAMAVCwkJueiXm6ysLIWGhl7V54mJiZHNZsvL0q6INdshhYefv8CWDzNkKCXlrCIiisgiH74QmD1YCgvTLbfckqeflj55gZd65Q+cTqe2b99u2vsOrhy98o/3P977/KNPkh/1ysSfUTnvO5fDEB0AAADAFYuKitLx48dzLTt+/LhKly59VZ/HZrOZMyBxOCWL5fyHL/vzkGuLLO7zz/ukP1/LPO8lfcp73uqVHzHtfQdXLaB75Q/vf7z3+UefJP/plR/8jOLCogAAAACuWK1atbR582YZf54D1DAMbdq0SbVq1TK5MgAAAMA7GKIDAAAAuKTk5GRlZGRIkho3bqyUlBSNHDlSe/bs0ciRI5Wenq4mTZqYXCUAAADgHQzRAQAAAFxSXFycli5dKkkKDw/X5MmTtXHjRj366KPaunWrpkyZokKFCplcJQAAAOAdnBMdAAAAQC67du265O2aNWtq0aJF+VkSAAAAYBr2RAcAAAAAAAAAwAOG6AAAAAAAAAAAeMAQHQAAAAAAAAAADxiiAwAAAAAAAADgAUN0AAAAAAAAAAA8YIgOAAAAAAAAAIAHDNEBAAAAAAAAAPCAIToAAAAAAAAAAB4wRAcAAAAAAAAAwAOG6AAAAAAAAAAAeGDqED0zM1ODBg1S3bp1FRcXp8TERI/rfvnll2rSpIlq166ttm3baufOnflYKQAAAAAAAAAgEJk6RB89erR27Nih6dOna+jQoZowYYKWLVt20Xq7d+/WCy+8oB49eujTTz9V9erV1aNHD6Wnp5tQNQAAAAAAAAAgUJg2RE9LS9OCBQs0ePBg1ahRQw0bNlTXrl01e/bsi9b97rvvVLVqVTVv3lwVKlTQ888/r+TkZO3Zs8eEygEAAAAAAAAAgcK0IXpSUpIcDodq167tXhYbG6utW7fK5XLlWrdYsWLas2ePNm7cKJfLpY8//ljh4eGqUKFCfpcNAAAAAAAAAAggQWY9cXJysooXLy673e5eFhkZqczMTJ0+fVolSpRwL2/atKlWrlypJ554QjabTVarVZMnT1bRokXNKB0AAAAAAAAAECBMG6Knp6fnGqBLct/OysrKtfzUqVNKTk7Wq6++qlq1amnOnDkaOHCgFi1apJIlS17V8zqdzusrvADLeW14jXwbfZKshiEp58N3GYbx178Wk4u5JEMyDLm88D3lD73ynz5J3uyVP+D9zz/QpyvD6wMAAAD4D9OG6CEhIRcNy3Nuh4aG5lo+duxY3XzzzWrXrp0kacSIEWrSpIkWLlyo7t27X9Xzbt++/TqqDgy8Rv4hUPtkt9tVuWyonGdT5UpLMbucK5KSctbsEi7Jmh0uW3q69u796aL35evhb73y9T5J3uuVvwnU9z9/Q58AAAAAFBSmDdGjoqJ06tQpORwOBQWdLyM5OVmhoaGKiIjIte7OnTvVvn17922r1apq1arp8OHDV/28MTExstls11d8AeV0OrV9+3ZeIx9HnyRrxhGpSLgUHHH5lU1kGIZSUs4qIqKILBYf3sU5NFwKC9Mtt1TO80/tD73ymz5JXu2VP+D9zz/QpyuT8zoBAAAA8H2mDdGrV6+uoKAgbdmyRXXr1pUkbdy4UTExMbJac1/vtHTp0vr1119zLdu3b59iYmKu+nltNhu/0F0Gr5F/COg+WSw6f94NHx94/lmexWKRxadrtUgWi3e+n/yhV37TJ8mrvfIjAf3+50foEwAAAICCwnr5VbwjLCxMzZs3V3x8vLZt26YVK1YoMTFRHTp0kHR+r/SMjAxJUuvWrTV//nx98skn+v333zV27FgdPnxYLVq0MKt8AAAAAAAAAEAAMG1PdEkaOHCg4uPj1bFjR4WHh6tPnz5q1KiRJCkuLk6jRo3So48+qqZNm+rcuXOaPHmy/vjjD1WvXl3Tp0+/6ouKAgAAAAAAAABwNUwdooeFhSkhIUEJCQkX3bdr165ct1u1aqVWrVrlV2kAAAAAAAAAAJh3OhcAAAAAAAAAAHwdQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAKDMzEwNGjRIdevWVVxcnBITEz2u++WXX6pJkyaqXbu22rZtq507d+ZjpQAAAED+YogOAAAAQKNHj9aOHTs0ffp0DR06VBMmTNCyZcsuWm/37t164YUX1KNHD3366aeqXr26evToofT0dBOqBgAAALyPIToAAAAQ4NLS0rRgwQINHjxYNWrUUMOGDdW1a1fNnj37onW/++47Va1aVc2bN1eFChX0/PPPKzk5WXv27DGhcgAAAMD7GKIDAAAAAS4pKUkOh0O1a9d2L4uNjdXWrVvlcrlyrVusWDHt2bNHGzdulMvl0scff6zw8HBVqFAhv8sGAAAA8kWQ2QUAAAAAMFdycrKKFy8uu93uXhYZGanMzEydPn1aJUqUcC9v2rSpVq5cqSeeeEI2m01Wq1WTJ09W0aJFr+o5nU5nntV/NayGIeV8+DBDxl//+nKpf76WrjzuJ33yAi/1yh/kvN+Y9b6DK0ev/OP9j/c+/+iT5Ee9MvFn1JW+3zBEBwAAAAJcenp6rgG6JPftrKysXMtPnTql5ORkvfrqq6pVq5bmzJmjgQMHatGiRSpZsuQVP+f27duvv/CrZLfbVfmGG+VMTZUrIzPfn/9apKScNbuES7KGhsiWnq69uw9c9L1yreiTd3ijV/7GjPcdXJtA7ZW/vf8F6nufv/VJ8v1e+cPPKIboAAAAQIALCQm56BeWnNuhoaG5lo8dO1Y333yz2rVrJ0kaMWKEmjRpooULF6p79+5X/JwxMTGy2WzXWfnVs2Y7pPBwyR6S7899NQwZSkk5q4iIIrLIYnY5ntmDpbAw3XLLLXn6aemTF3ipV/7A6XRq+/btpr3v4MrRK/94/+O9zz/6JPlRr0z8GZXzvnM5DNEBAACAABcVFaVTp07J4XAoKOj8rwjJyckKDQ1VRERErnV37typ9u3bu29brVZVq1ZNhw8fvqrntNls5gxIHE7JYjn/4cv+POTaIossvlzrn69lnveSPuU9b/XKj5j2voOrFtC98of3P977/KNPkv/0yg9+RnFhUQAAACDAVa9eXUFBQdqyZYt72caNGxUTEyOrNfevDKVLl9avv/6aa9m+fft0ww035EepAAAAQL5jiA4AAAAEuLCwMDVv3lzx8fHatm2bVqxYocTERHXo0EHS+b3SMzIyJEmtW7fW/Pnz9cknn+j333/X2LFjdfjwYbVo0cLMLwEAAADwGk7nAgAAAEADBw5UfHy8OnbsqPDwcPXp00eNGjWSJMXFxWnUqFF69NFH1bRpU507d06TJ0/WH3/8oerVq2v69OlXdVFRAAAAwJ8wRAcAAACgsLAwJSQkKCEh4aL7du3alet2q1at1KpVq/wqDQAAADAVp3MBAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4IGpQ/TMzEwNGjRIdevWVVxcnBITEz2uu2vXLrVt21Y1a9bUww8/rO+//z4fKwUAAAAAAAAABCJTh+ijR4/Wjh07NH36dA0dOlQTJkzQsmXLLlrv7Nmz6ty5s6pWrar//e9/atiwoXr37q0TJ06YUDUAAAAAAAAAIFCYNkRPS0vTggULNHjwYNWoUUMNGzZU165dNXv27IvWXbRokQoVKqT4+HhVrFhRffv2VcWKFbVjxw4TKgcAAAAAAAAABIogs544KSlJDodDtWvXdi+LjY3VpEmT5HK5ZLX+Nd//8ccfVb9+fdlsNveyhQsX5mu9AAAAAADAP9ntdrNLwBWiVwB8kWlD9OTkZBUvXjzXm2NkZKQyMzN1+vRplShRwr38wIEDqlmzpoYMGaKVK1eqfPnyGjBggGJjY80oHQAAAAAASJLDITldZldxSVbDUOUbbpQ12yE5nGaXc3k2qxSUx+MaP+iT5Ge98kafAPgs07b29PT0i/66mHM7Kysr1/K0tDRNmTJFHTp00NSpU/XZZ5+pS5cu+vzzz1W2bNmrel6n08ffhE2U89rwGvk2+nQ+WEk5H77LMIy//rWYXMwlGZJhyOWF7yl/6JX/9EnyZq/8Ae9//oE+XRleHwAFhtMlHTomZTvMrsQzw5AzNVUKD5csPh74goOk8qXzflrjD32S/KdX3uoTAJ9l2uYeEhJy0bA853ZoaGiu5TabTdWrV1ffvn0lSbfccou+++47ffrpp+rZs+dVPe/27duvo+rAwGvkHwK1T3a7XZXLhsp5NlWutBSzy7kiKSlnzS7hkqzZ4bKlp2vv3p8uel++Hv7WK1/vk+S9XvmbQH3/8zf0CQACSLZDyso2uwrPDEOujEzJHuLbg1lv8/U+SfQKgM8ybYgeFRWlU6dOyeFwKOjPw1+Sk5MVGhqqiIiIXOuWKlVKlStXzrWsUqVKOnLkyFU/b0xMTK5zq+MvTqdT27dv5zXycfRJsmYckYqES8ERl1/ZRIZhKCXlrCIiisjiywEwNFwKC9Mtt1S+/LpXyR965Td9krzaK3/A+59/oE9XJud1AgAAAOD7TBuiV69eXUFBQdqyZYvq1q0rSdq4caNiYmJyXVRUkm677TatX78+17K9e/fqoYceuurntdls/EJ3GbxG/iGg+2Sx6Px5N3x84PlneRaLRRafrtUiWSze+X7yh175TZ8kr/bKjwT0+58foU8AAAAACgrr5VfxjrCwMDVv3lzx8fHatm2bVqxYocTERHXo0EHS+b3SMzIyJElt2rTRrl279O677+r333/XuHHjdODAATVr1sys8gEAAAAAAAAAAcC0IbokDRw4UDVq1FDHjh01bNgw9enTR40aNZIkxcXFaenSpZKk8uXL6/3339eqVav00EMPadWqVZoyZYqioqLMLB8AAAAAAAAAUMCZeh3hsLAwJSQkKCEh4aL7du3alet2bGysPv744/wqDQAAAAAAAAAAc/dEBwAAAAAAAADAlzFEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOnKx2+1ml4ArQJ8AAAAAAACA/BFkdgEBI+uM5Eg1u4pLshqGKpcNlTXjiGSxmF3O5QWFS/aiefs56VPe80afAAAAAAAAgHzCED2/OFKlXyZIGcfNruQSDDnPpkpFwiX5+HA2NFK6uXfeD2fpU97yVp8AAAAAAACAfMIQPT9lHJcy/jC7iksw5EpLkYIj5PPDWW+iTwAAAAAAAAD+xDnRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPAgyuwAAAAAA12bMmDGqX7++DMOQxWKRJPf/DcPQ8uXLNWjQoCv6XJmZmRo2bJi++OILhYaGqnPnzurcufM/rrtr1y7Fx8dr586dqlixogYPHqy77rorz74uAAAAwJcwRAcAAAD8lNVqVZ06dTzev3r16iv+XKNHj9aOHTs0ffp0HT58WAMGDFC5cuXUuHHjXOudPXtWnTt31gMPPKA33nhDn376qXr37q3ly5erZMmS1/qlAAAAAD6L07kAAAAAfipn7/NrvT9HWlqaFixYoMGDB6tGjRpq2LChunbtqtmzZ1+07qJFi1SoUCHFx8erYsWK6tu3rypWrKgdO3Zc09cAAAAA+Dr2RAcAAAACXFJSkhwOh2rXru1eFhsbq0mTJsnlcslq/Wvfmx9//FH169eXzWZzL1u4cGG+1gsAAADkp2saoi9dulRlypTJde7FHIZh6ODBg2rWrFmeFAgAAADAu5KTk1W8eHHZ7Xb3ssjISGVmZur06dMqUaKEe/mBAwdUs2ZNDRkyRCtXrlT58uU1YMAAxcbGmlE6AAAA4HXXNERPSkpS06ZNPd6/Zs2aay4IAAAAQP5KT0/PNUCX5L6dlZWVa3laWpqmTJmiDh06aOrUqfrss8/UpUsXff755ypbtuwVP6fT6bz+wq+B1TCknA8fZsj4619fLvXP19KVx/2kT14QwL2iT/7RJ8mPeuWlPkn+0Su/6ZPENuUvvfLiNnU5V5pJr2mIblzmG+Ry9wMAAAC4fmlpaTp69KjH+1NTU6/o84SEhFw0LM+5HRoammu5zWZT9erV1bdvX0nSLbfcou+++06ffvqpevbsecW1b9++/YrXzSt2u12Vb7hRztRUuTIy8/35r0VKylmzS7gka2iIbOnp2rv7wEXfQ9eKPnkHvaJP/tInyfd75Y0+Sf7XK1/vk8Q2lcPXe+WtbSovXdMQPa8uYAQAAADg2nXt2lUOh8Pj/V26dLmizxMVFaVTp07J4XAoKOj8rwjJyckKDQ1VRERErnVLlSqlypUr51pWqVIlHTly5Kpqj4mJyXVe9fxizXZI4eGSPSTfn/tqGDKUknJWERFFZJEP/35lD5bCwnTLLbfk6aelT14QwL2iT/7RJ8mPeuWlPkn+0Su/6ZPENuUvvfLiNnU5Tqfzinbu4MKiAAAAgJ8qU6ZMnnye6tWrKygoSFu2bFHdunUlSRs3blRMTEyui4pK0m233ab169fnWrZ371499NBDV/WcNpvNlCG6HE7JYjn/4cv+PLjXIotv76T052uZ572kT3kvkHtFn/yjT5L/9MpbfZL8o1f+0ieJbcpfeuXNbSqPWC+/CgAAAICCLCwsTM2bN1d8fLy2bdumFStWKDExUR06dJB0fq/0jIwMSVKbNm20a9cuvfvuu/r99981btw4HThwQM2aNTPzSwAAAAC8hnOiAwAAAH5q6dKlKlOmjAzDcO9dlPN/wzB08ODBKx5uDxw4UPHx8erYsaPCw8PVp08fNWrUSJIUFxenUaNG6dFHH1X58uX1/vvva+TIkZoyZYqqVKmiKVOmKCoqymtfJwAAAGCmaxqiR0dHa9OmTbnCeg7DMC46RyIAAEBesNvtZpeAK0Cf8k9SUpKaNm3q8f41a9Zc8ecKCwtTQkKCEhISLrpv165duW7Hxsbq448/vvJCAQAAAD92TUP0y53vMDY29pqKAQAAJsg6IzlSza7isqyGocplQ2XNOOL75x4MCpfsRfP+8/pBr/yqT5L3epVPOEIUAAAA8D4uLAoAQKBzpEq/TJAyjptdyWUYcp5NlYqES758ZfnQSOnm3t4ZzPpFr/ykT5J3e5VPLneBKJ++gBQAAADgJ65piH78+HE5nc5LrsM5EQEA8CMZx6WMP8yu4jIMudJSpOAI+fxw1pt8vlf0CQAAAEDBYr2WB02aNElOp1MOhyPXR86ySZMm5XWdAAAAAAAAAADku2vaEz0sLEzlypXzeH94ePg1FwQAAADgynBOdAAAAMD7rmmIzrkXAQAAAPNFR0dr06ZNMgzjogxuGIYqV65sUmUAAABAwcGFRQEAAAA/9dBDD13y/tjY2HyqBAAAACi4rumc6AAAAAAAAAAABAL2RAcAAAD81PHjx+V0Oi+5TlRUVD5VAwAAABRM1zREz87Ovujcizn/NwxDaWlpeVokAAAAgItNmjRJnTt3vugCojm5/P3339fQoUNNqg4AAAAoGK5piD5gwIBL3s+5FwEAAADvCwsLU7ly5TzeHx4eno/VAAAAAAUT50QHAAAA/FTOUaHXej8AAACAy7umPdGnTJmiunXrejxsdN26derdu3eeFAgAAAAAAAAAgFmuaYh+9uxZ1alTx+P9q1evvtZ6AAAAAAAAAADwGdd0OhcOGwUAAAAAAAAABIJr2hMdAAAAgPmys7O1adMmGYbh3pEl5/+GYSgtLc3kCgEAAAD/xxAdAAAA8FMDBgy45P2xsbH5VAkAAABQcF3T6VwAAAAAAAAAAAgE17QnumEY13U/AAAAgOs3ZcoU1a1b96L8nXM6l3Xr1ql3794mVQcAAAAUDNc0RG/QoIE2bdr0j/cZhqF69epdV1EAAAAALu/s2bOqU6eOx/tXr16df8UAAAAABdQ1DdFr1aqV13UAAAAAuEo5FxO91vsBAAAAXB7nRAcAAAAAAAAAwAOG6AAAAAAAAAAAeMAQHQAAAAAAAAAADxiiAwAAAH7KMIzruh8AAADA5V3ThUUBAAAAmK9BgwbatGnTP95nGIbq1auXzxUBAAAABQ9DdAAAAMBP1apVy+wSAAAAgAKP07kAAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8MDUIXpmZqYGDRqkunXrKi4uTomJiZd9zMGDB1W7dm398MMP+VAhAAAAAAAAACCQBZn55KNHj9aOHTs0ffp0HT58WAMGDFC5cuXUuHFjj4+Jj49XWlpaPlYJAAAAAAAAAAhUpg3R09LStGDBAk2dOlU1atRQjRo1tHv3bs2ePdvjEH3x4sU6d+5cPlcKAAAAAAAAAAhUpp3OJSkpSQ6HQ7Vr13Yvi42N1datW+VyuS5a/9SpUxozZoyGDx+en2UCAAAAAAAAAAKYaXuiJycnq3jx4rLb7e5lkZGRyszM1OnTp1WiRIlc67/xxhtq0aKFbrrpput6XqfTeV2Pv1ZWw5CU8+GbDMP461+LycVcliEZhlx53E/6lNcCt0+SP/XKO32S/KNX/tMniW3KX3rFNuX+16f7JHmzV5djViYFAAAAcPVMG6Knp6fnGqBLct/OysrKtXzt2rXauHGjlixZct3Pu3379uv+HFfLbrerctlQOc+mypWWku/Pf7VSUs6aXcJlWbPDZUtP1969P130/XKt6FPeo0/n+XqvvNEnyf965et9ktimcvh6r9imzvP1Pkne6xUAAACAgsW0IXpISMhFv6zk3A4NDXUvy8jI0KuvvqqhQ4fmWn6tYmJiZLPZrvvzXC1rxhGpSLgUHJHvz32lDMNQSspZRUQUkcXi47uOhYZLYWG65ZbKefpp6VMeC+A+SX7UKy/1SfKPXvlNnyS2KX/pFduUf/RJ8mqvLsfpdJqycwcAAACAq2faED0qKkqnTp2Sw+FQUND5MpKTkxUaGqqIiL9+Mdy2bZsOHDigvn375np8t27d1Lx586s+R7rNZjNliC6LReePafbhXyb/LM1iscjiy3VKkiySxZL3vaRPeSyA+yT5Ua+81CfJP3rlN32S2Kb+/Mfne8U2JflDnySv9goAAABAgWHaEL169eoKCgrSli1bVLduXUnSxo0bFRMTI6v1r+ud1qxZU1988UWuxzZq1Eivvfaa7rnnnnytGQAAAAAAAAAQWEwbooeFhal58+aKj4/X66+/rmPHjikxMVGjRo2SdH6v9CJFiig0NFQVK1a86PFRUVEqWbJkfpcNAAAAAAAAAAgg1suv4j0DBw5UjRo11LFjRw0bNkx9+vRRo0aNJElxcXFaunSpmeUBAAAAAAAAAAKcaXuiS+f3Rk9ISFBCQsJF9+3atcvj4y51HwAAAAAAAAAAecXUPdEBAAAAAAAAAPBlDNEBAAAAAAAAAPCAIToAAAAAAAAAAB4wRAcAAAAAAAAAwAOG6AAAAAAAAAAAeMAQHQAAAAAAAAAADxiiAwAAAAAAAADgAUN0AAAAAAAAAAA8YIgOAAAAQJmZmRo0aJDq1q2ruLg4JSYmXvYxBw8eVO3atfXDDz/kQ4UAAACAOYLMLgAAAACA+UaPHq0dO3Zo+vTpOnz4sAYMGKBy5cqpcePGHh8THx+vtLS0fKwSAAAAyH8M0QEAAIAAl5aWpgULFmjq1KmqUaOGatSood27d2v27Nkeh+iLFy/WuXPn8rlSAAAAIP9xOhcAAAAgwCUlJcnhcKh27druZbGxsdq6datcLtdF6586dUpjxozR8OHD87NMAAAAwBQM0QEAAIAAl5ycrOLFi8tut7uXRUZGKjMzU6dPn75o/TfeeEMtWrTQTTfdlI9VAgAAAObgdC4AAABAgEtPT881QJfkvp2VlZVr+dq1a7Vx40YtWbLkup7T6XRe1+OvldUwpJwPH2bI+OtfXy71z9fSlcf9pE9eEMC9ok/+0SfJj3rlpT5J/tErv+mTxDblL73y4jZ1OVeaSRmiAwAAAAEuJCTkomF5zu3Q0FD3soyMDL366qsaOnRoruXXYvv27df1+Gtht9tV+YYb5UxNlSsjM9+f/1qkpJw1u4RLsoaGyJaerr27D1z0PXSt6JN30Cv65C99kny/V97ok+R/vfL1PklsUzl8vVfe2qbyEkN0AAAAIMBFRUXp1KlTcjgcCgo6/ytCcnKyQkNDFRER4V5v27ZtOnDggPr27Zvr8d26dVPz5s2v6hzpMTExstlsefMFXAVrtkMKD5fsIfn+3FfDkKGUlLOKiCgiiyxml+OZPVgKC9Mtt9ySp5+WPnlBAPeKPvlHnyQ/6pWX+iT5R6/8pk8S25S/9MqL29TlOJ3OK9q5gyE6AAAAEOCqV6+uoKAgbdmyRXXr1pUkbdy4UTExMbJa/7qMUs2aNfXFF1/kemyjRo302muv6Z577rmq57TZbKYM0eVwShbL+Q9f9uch1xZZZPHlWv98LfO8l/Qp7wVyr+iTf/RJ8p9eeatPkn/0yl/6JLFN+UuvvLlN5RGG6AAAAECACwsLU/PmzRUfH6/XX39dx44dU2JiokaNGiXp/F7pRYoUUWhoqCpWrHjR46OiolSyZMn8LhsAAADIF9bLrwIAAACgoBs4cKBq1Kihjh07atiwYerTp48aNWokSYqLi9PSpUtNrhAAAAAwB3uiAwAAAFBYWJgSEhKUkJBw0X27du3y+LhL3QcAAAAUBOyJDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMEQHAAAAAAAAAMADhugAAAAAAAAAAHjAEB0AAAAAAAAAAA8YogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACAB6YO0TMzMzVo0CDVrVtXcXFxSkxM9Lju6tWr1axZM9WuXVsPP/ywvvrqq3ysFAAAAAAAAAAQiEwdoo8ePVo7duzQ9OnTNXToUE2YMEHLli27aL2kpCT17t1bjz32mD755BO1adNGzz77rJKSkkyoGgAAAAAAAAAQKILMeuK0tDQtWLBAU6dOVY0aNVSjRg3t3r1bs2fPVuPGjXOtu2TJEt11113q0KGDJKlixYpauXKlPv/8c1WrVs2M8gEAAAAAAAAAAcC0IXpSUpIcDodq167tXhYbG6tJkybJ5XLJav1rJ/kWLVooOzv7os9x9uzZfKkVAAAAAAAAABCYTDudS3JysooXLy673e5eFhkZqczMTJ0+fTrXulWqVMm1x/nu3bu1bt06/fvf/86vcgEAAAAAAAAAAci0PdHT09NzDdAluW9nZWV5fNzJkyfVp08f1alTR/Xr17/q53U6nVf9mLxgNQxJOR++yTCMv/61mFzMZRmSYciVx/2kT3ktcPsk+VOvvNMnyT965T99ktim/KVXbFPuf326T5I3e3U5ZmVSAAAAAFfPtCF6SEjIRcPynNuhoaH/+Jjjx4+rU6dOMgxD48ePz3XKlyu1ffv2qy/2OtntdlUuGyrn2VS50lLy/fmvVkqK758mx5odLlt6uvbu/emSf3S5GvQp79Gn83y9V97ok+R/vfL1PklsUzl8vVdsU+f5ep8k7/UKAAAAQMFi2hA9KipKp06dksPhUFDQ+TKSk5MVGhqqiIiIi9Y/evSo+8KiM2bMUIkSJa7peWNiYmSz2a698GtkzTgiFQmXgi/+2nyFYRhKSTmriIgislh8fNex0HApLEy33FI5Tz8tfcpjAdwnyY965aU+Sf7RK7/pk8Q25S+9Ypvyjz5JXu3V5TidTlN27gAAAABw9UwbolevXl1BQUHasmWL6tatK0nauHGjYmJiLtrDPC0tTV27dpXVatWMGTNUqlSpa35em81myhBdFovOH9Psw79M/lmaxWKRxZfrlCRZJIsl73tJn/JYAPdJ8qNeealPkn/0ym/6JLFN/fmPz/eKbUryhz5JXu0VAAAAgALDtAuLhoWFqXnz5oqPj9e2bdu0YsUKJSYmuvc2T05OVkZGhiRp8uTJ2r9/vxISEtz3JScn6+xZ3z9MGAAAAAAAAADgv0wbokvSwIEDVaNGDXXs2FHDhg1Tnz591KhRI0lSXFycli5dKklavny5MjIy1KpVK8XFxbk/Ro4caWb5AAAAQIGRmZmpQYMGqW7duoqLi1NiYqLHdVevXq1mzZqpdu3aevjhh/XVV1/lY6UAAABA/jLtdC7S+b3RExIS3HuYX2jXrl3u/y9btiw/ywIAAAACzujRo7Vjxw5Nnz5dhw8f1oABA1SuXDk1btw413pJSUnq3bu3XnrpJdWrV0/ffvutnn32WX300UeqVq2aSdUDAAAA3mPqEB0AAACA+dLS0rRgwQJNnTpVNWrUUI0aNbR7927Nnj37oiH6kiVLdNddd7lPw1ixYkWtXLlSn3/+OUN0AAAAFEgM0QEAAIAAl5SUJIfDodq1a7uXxcbGatKkSXK5XLJa/zoLZIsWLZSdnX3R5+B6RQAAACioTD0nOgAAAADzJScnq3jx4rLb7e5lkZGRyszM1OnTp3OtW6VKlVx7nO/evVvr1q3Tv//97/wqFwAAAMhX7IkOAAAABLj09PRcA3RJ7ttZWVkeH3fy5En16dNHderUUf369a/qOZ1O59UXmgeshiHlfPgwQ8Zf//pyqX++lq487id98oIA7hV98o8+SX7UKy/1SfKPXvlNnyS2KX/plRe3qcu50kzKEB0AAAAIcCEhIRcNy3Nuh4aG/uNjjh8/rk6dOskwDI0fPz7XKV+uxPbt26+t2Otgt9tV+YYb5UxNlSsjM9+f/1qkpPj2aXKsoSGypadr7+4Dl/yDy9WgT95Br+iTv/RJ8v1eeaNPkv/1ytf7JLFN5fD1Xnlrm8pLDNEBAACAABcVFaVTp07J4XAoKOj8rwjJyckKDQ1VRETEResfPXrUfWHRGTNmqESJElf9nDExMbLZbNdX+DWwZjuk8HDJHpLvz301DBlKSTmriIgisshidjme2YOlsDDdcsstefpp6ZMXBHCv6JN/9Enyo155qU+Sf/TKb/oksU35S6+8uE1djtPpvKKdOxiiAwAAAAGuevXqCgoK0pYtW1S3bl1J0saNGxUTE3PRHuZpaWnq2rWrrFarZsyYoVKlSl3Tc9psNlOG6HI4JYvl/Icv+/OQa4sssvhyrX++lnneS/qU9wK5V/TJP/ok+U+vvNUnyT965S99ktim/KVX3tym8ggXFgUAAAACXFhYmJo3b674+Hht27ZNK1asUGJiontv8+TkZGVkZEiSJk+erP379yshIcF9X3Jyss6e9e3DhAEAAIBrxZ7oAAAAADRw4EDFx8erY8eOCg8PV58+fdSoUSNJUlxcnEaNGqVHH31Uy5cvV0ZGhlq1apXr8S1atNAbb7xhRukAAACAVzFEBwAAAKCwsDAlJCS49zC/0K5du9z/X7ZsWX6WBQAAAJiO07kAAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAcM0QEAAAAAAAAA8IAhOgAAAAAAAAAAHjBEBwAAAAAAAADAA4boAAAAAAAAAAB4wBAdAAAAAAAAAAAPGKIDAAAAAAAAAOABQ3QAAAAAAAAAADxgiA4AAAAAAAAAgAemDtEzMzM1aNAg1a1bV3FxcUpMTPS47k8//aRWrVqpVq1aeuyxx7Rjx458rBQAAAAo2MjmAAAAwD8zdYg+evRo7dixQ9OnT9fQoUM1YcIELVu27KL10tLS1L17d9WtW1cff/yxateurR49eigtLc2EqgEAAICCh2wOAAAA/DPThuhpaWlasGCBBg8erBo1aqhhw4bq2rWrZs+efdG6S5cuVUhIiF566SVVqVJFgwcPVuHChf8x1AMAAAC4OmRzAAAAwDPThuhJSUlyOByqXbu2e1lsbKy2bt0ql8uVa92tW7cqNjZWFotFkmSxWFSnTh1t2bIlP0sGAAAACiSyOQAAAOCZaUP05ORkFS9eXHa73b0sMjJSmZmZOn369EXrli5dOteykiVL6o8//siPUgEAAIACjWwOAAAAeBZk1hOnp6fnCumS3LezsrKuaN2/r3cphmG4P7fNZruWkq+L1eGUQsrIxJf8sgzDkFEoTY6QQu49i3xWSKTkcMp1Fd8DV4I+5bEA7pPkR73yUp8k/+iV3/RJYpvyl16xTflHnySv9upynE6npL8yqtnyM5ubn8sdks0iBeX/c18NQ4aMULscwTZZ5MPbks0iORxy5XGJ9MkLArhX9Mk/+iT5Ua+81CfJP3rlN32S2Kb8pVde3KYu50pzuWm/gYWEhFwUtHNuh4aGXtG6f1/vUnIOQ/3pp5+updw80lwKNvHpr0QJswu4Qi5Ju5MlJXvhkzenT3kl0Psk+UevvNonyS965Q99ktimJP/oFduUf/RJyodeXUEJfztVilnyM5v7Ri73ExZJaafNruLyzhw3uwJz+UufpMDuFX3yH/7SK/rkH32S6JW/9MrkPl0ul5s2RI+KitKpU6fkcDgUFHS+jOTkZIWGhioiIuKidY8fz/1CHj9+/KLDSC8lKChIMTExslqtvr9XFAAAAAo0wzDkcrncOdhs+ZnNyeUAAADwFVeay01L7dWrV1dQUJC2bNmiunXrSpI2btzoDtQXqlWrlqZOnSrDMGSxWGQYhjZt2qSePXte8fNZrdaLDjsFAAAAkL/ZnFwOAAAAf2PahUXDwsLUvHlzxcfHa9u2bVqxYoUSExPVoUMHSef3fMnIyJAkNW7cWCkpKRo5cqT27NmjkSNHKj09XU2aNDGrfAAAAKDAIJsDAAAAnlkME69mlJ6ervj4eH3xxRcKDw9Xly5d9NRTT0mSoqOjNWrUKD366KOSpG3btmno0KH69ddfFR0drWHDhumWW24xq3QAAACgQCGbAwAAAP/M1CE6AAAAAAAAAAC+zLTTuQAAAAAAAAAA4OsYogMAAAAAAAAA4AFDdAAAAAAAAAAAPGCIDgAAAAAAAACABwzRAQAAAAAAAADwgCE6AAAAAAAAAAAeMETHVTEMQ5KUnp5uciUAAFxazs+sv/8fBR/9RqAgmwMA/AXZPHAVlH4zRMdVsVgsWrFihRISEnTq1Cmzy4GJXC6X2SUUaAXlhwxgFpfLJYvFIknKzs7WuXPnTK4I+eXC3h8/fly//fabJN5XUTCRzZGDbO5d/AwBrg/ZPHAVpGweZHYB8C+HDh3S/Pnz9dBDDyk8PNzscpAPDMOQxWLRnj17dPz4cWVmZurf//637Ha72aUVODmvtST3vwgMF/be6XTKZrOZXJF/MwxDVuv5/QSmTJmiH3/8UQcPHtTtt9+uRx55RHXr1mUbK8Byev/2229r7dq12rdvn+666y5VrlxZvXv35ucXChSyeeAhm+cfsnngIpvnLbJ5YCtI2Zw90XHFfv31VzVo0EDHjh1TjRo1FBwcbHZJyAcWi0XLly9X9+7dNWrUKE2cOFEPPPCAtmzZwh4veSgnqG3YsEGTJ0/WG2+8oa+++koOh8Ps0uBlOb3/+uuvNWTIELVr105Tp07Vhg0bzC7Nb+WE8IkTJ2ratGn6z3/+ozFjxuiLL77QxIkTdezYMZMrhLdNnz5d8+bN07PPPqsvv/xSTqdTn3zyifbv3292aUCeIZsHJrJ5/iCbBy6yed4jm6OgZHOG6LhiVapUUdu2bZWUlKSffvrJ7HKQT/bs2aMRI0aoT58++vTTT5WQkOA+BCcnRPrjYTi+Judw7Kefflo7d+6Uw+HQM888o4SEBJ05c8bs8uBFFotFq1atUt++fRUeHq5mzZrpf//7nxISErRv3z6zy/MrOe9FhmHo9OnTWr9+vYYMGaJWrVopOztbGRkZatu2rY4dO6ajR4+aXC28xTAM7d27Vz179lRcXJx++uknff/99xowYIAyMjK0evVqs0sE8gTZPDCRzfMH2Txwkc3zDtkcUsHK5gzR4VHOG97x48f1xx9/SJJeffVVtW7dWkOGDNG6devMLA/5JC0tTVFRUWrRooX279+vTp06qU2bNrrzzjuVkJCg9PR0Dr26Br/99pt7byHDMHTixAlNmTJF/fr10/jx4zVgwADZ7XaVLVtW2dnZ7FlUgGVlZWnJkiV6+umnNWDAAD322GM6evSoHnjgAdntdr/767yZLjzkulixYsrKylKxYsX01VdfqUuXLnrppZfUqFEjjRs3TrNnzza5WuSVv78/WiwWnTp1SsePH9fq1avVp08fvfjii3rwwQe1ePFizZkzR06n06RqgWtHNodENvcWsjlykM3zDtk8MBXkbM4QHf8o5xCmFStW6Mknn1TXrl3VvXt3SdLw4cP14IMPqlevXvrxxx9NrhTelp2drfT0dB06dEhPPfWU7rnnHsXHx8tut2vVqlVatWqV2SX6FcMw9M0336hx48ZauXKle1sLDQ2Vy+XSnXfeqYMHD6p+/fp6+OGH1aZNGw0fPpzXuQALDg7W0aNHVaxYMSUnJ6tBgwaqX7++unXrpqlTp2rx4sVml+hXpk2bpkGDBkmSIiMjNWHCBA0cOFD9+/dXu3bt3MuPHz9uZpnIIy6Xy32exYMHD7r3xr3vvvu0YcMG9e3bVy+88IK79xUqVFBaWprfBHUgB9kcOcjmeYtsjr8jm+ctsnlgKejZnCE6/pHFYtGaNWv03HPPqXHjxurZs6d2796tHj16SJJGjhyppk2bqnfv3lq7dq3J1SKv5Ozh9PPPP2vFihVatWqVYmJiVLJkSdWvX19xcXF67bXXJJ1/cwwJCVGJEiXMLNnvOBwO3XvvvWrdurUGDhyolStXyuFwyOFwyDAMrVmzRp06dVK9evU0YsQIFSpUSGfPntXXX39tdunIIznbmcvlUmZmpiwWi2rXrq1t27apZcuWuv/++zVy5EgFBQUpODhYP/zwg9+ECl9QtGhR7du3TydPnlS/fv20b98+VapUSS1btpTL5ZJhGPrtt99UqlQps0tFHsgJ6W+++aZ69Oihjh07KiEhQbVq1VKxYsVUuXJl3XDDDcrIyFBqaqpWrFih8uXL+9UFjACJbB6oyObeRzYH2dy7yOaBpaBn8yCzC4DvMQxDmZmZmjlzpnr27KlnnnlGx44dk8Vi0bfffqvHH39c8+bN08iRI5WRkaGXX35ZX3zxhUJDQ80uHdcp50JF8fHxKlWqlCIjI+V0OvXiiy9q5MiR2rt3r44ePaqUlBQtX75cmZmZqlSpktll+41JkyZp7dq1mjFjhoYPHy6bzaYXX3xRY8aMUYMGDdS6dWsNHTpU99xzj0aMGOF+nMViUbly5UysHHklZ++m1atX6+OPP9ahQ4fUpUsX3XXXXXr++edVpkwZ9e7d233o45kzZ1SxYkUOy76MnNdVkmrWrKkPP/xQS5YsUYcOHfTee++pS5cu6tq1q2w2m7KysnTu3Dn16dPH5KqRVz7++GMtXLhQb731lkqXLq2jR4/qpptu0ujRo/Xcc89p8uTJGjhwoMqVK6esrCxNnTpVUu7vG8CXkc0DF9ncu8jmIJt7B9k8sBXkbG4xuOoI/kFWVpY6duyoNm3aqH79+kpISFBQUJDatm2rjh07qk6dOho2bJgiIyOVnJzMXw0LiAMHDqhr167q2bOnGjdurDNnzqhkyZIKDg7Wpk2bNGXKFG3evFnFihWTy+XSO++8oxo1aphdtt84duyYzpw5o5tuusm9LD4+XosWLdKbb76p+++/X1OmTNH48eP1/PPPq3Dhwjp48KA+/vhjzZ07V//6179MrB555bvvvlOvXr3UsWNHhYeHKzg4WJ06ddLXX3+t/v37Ky4uTsHBwbLZbFq+fLn++9//6uabbza7bJ8yZcoUNWnSRDfeeKMkaf369br99tvdwWv27Nl67733NHPmTFWpUkUHDx7U0qVLdfLkSRUtWlTdunVTUFCQnE6nbDabyV8Nrtfbb7+tU6dOafjw4bmWb926VcnJybrhhhu0ceNGlSpVSvXr15fNZpPD4VBQEPuSwH+QzQMT2dy7yOaQyOZ5gWyOCxXkbO77FcIUdrtdd9xxh4oVK6YNGzYoJSVFHTt2VKVKlVSxYkV99dVXOnLkiP773/8S0gsQp9Mpl8ulKlWqKCwsTGFhYZKkQ4cOaerUqXrnnXe0e/du2e12lShRgt5foaNHj+rcuXOqXLmySpcurY0bN+q1117TnDlzFB8fL0nq37+/3nzzTfXq1UulSpXS/Pnz5XQ6FRERoenTpxPSCwiXy6VVq1bp8ccf1/PPPy/pr0NI77rrLg0fPlw//fSTdu7cqXLlyhHS/0FSUpI2bNigzp07Szp/iHv79u119913q0mTJnrkkUfUrl07bdq0Sd9++60qVKigG264wX3u4ByEdP/0T3uonD59Wps3b3bfzs7OVnBwsN555x0VKVJE48ePV7Vq1dz3O51OvwjpwIXI5oGJbO4dZHPkIJtfP7J5YAu0bO4fVcKrcr7pf/75Z50+fVpHjx5V06ZN9dxzz0mSRo0aJbvdrjp16kiS/vWvf+mZZ55R1apVOUy0gElJSdG5c+eUnZ0t6a83u3Pnzmn37t3as2ePYmJiTK7S//zyyy/6+uuv9fTTTyslJUVly5bVmTNn1LlzZ3344YeKj4+XYRh64YUXNGbMGLVq1UpNmjSR3W5Xdna2ChcubPaXgDxitVp14MAB97nipPN7F1qtVj322GN67LHH3O+9/vLX+PyUmZmpatWq6b333lNQUJBWr16tqKgorVmzRhMmTNDixYs1Y8YMvfrqqwoPD9eaNWvUvn17SRcHc0K6/7nwQkVnzpxRoUKFFBwcrAcffFA7d+7UrFmz1LJlS3c2+c9//qO1a9fSe/gdsjlykM29g2yOHGTz60M2D2yBmM25sCjc59rr3Lmz3n//fY0fP16dO3fWzJkzJUmnTp3SwYMHdeDAAY0ZM0bffPONqlatqrJly5pcOa5Hzl/Yjxw5ol9++UXS+fOV/fvf/9bzzz+vkydPKjg4WJJUqlQphYWFKSMjw7R6/VlGRoY2btyo/v376/HHH1d2drbmzJmjkydPqkOHDsrMzNSwYcPUrFkzDRgwQJ9//rlCQ0Nlt9sJ6X7MMAz90xnT4uLilJ6erq1bt0qSQkJCFBwcrLi4OG3ZskUul0uSCOl/c+zYMU2dOlVnzpxRcHCwUlJS1L9/fyUmJio4OFjx8fFKSEjQbbfdpvHjxysrK0tr167VpEmTJPlXOMM/ywnpEyZMUPv27dW7d28tWLBAd9xxh+rWravly5drxowZOnnypM6cOaPly5ercOHC9B5+h2wemMjm+YdsHpjI5nmLbI5AzOYM0aEDBw4oISFB/fr10wcffKApU6Zow4YNCg0NVVZWlp555hmdOHFCnTt31rJlyzRlyhRCegFgsVi0bNkyPf7443rqqafUpk0b/f777xo8eLCqVKmiRx55ROvWrdOGDRs0e/ZsnTt3zn2OM1ydhg0b6v7779d3332n6OhoWa1WRUVFafr06Tpz5ow7rMfHx6t+/foaNWqUMjMzzS4becBisej777/X66+/rrlz5+r333/Xf/7zH6WkpGjWrFm5DnM7efKkIiIicu0Jg79kZ2drw4YNGjFihPr376+tW7dq8uTJ2rZtm8aMGaP9+/erXLlyGjFihHr16qXatWsrKChIf/zxh9ml4zrl/PIqSfPmzdO0adP00EMPKSQkRPPmzdP06dP18ssv684779TKlSv1f//3f+ratWuuczFyCSD4E7J5YCKb5x+yeeAim+cdsnngCuRszoVFoe3bt6t///5atmyZDh8+7D5/Va9evTR37lx17txZoaGh2rdvn0qXLq0SJUqYXTLywC+//KK+ffuqVatWqlq1qsaNG6fs7Gy9/fbbKlu2rF577TVt3LhRwcHBslgsSkhI4EJF1yDnEKcZM2boyJEj2rx5s6pXr642bdooOjpaR48eVceOHVWiRAklJiYqNDSUC4IVIKtXr1bPnj1Vp04dHThwQDExMXruuecUEhKil156yX1oW1RUlL766ivOs3gZK1eu1LBhw3T06FG9//77iouL0/r16zVw4EDdeeed6tixY67X78iRIypdurRf7+2AvyxbtkwnTpxQ8eLF1bRpU508eVILFizQF198oYcfflhPPfWUMjIytHr1apUoUUKxsbF+daEiIAfZPDCRzfMH2Tywkc3zFtk8sAViNmeIDh06dEiDBw/Wk08+qddee0316tXTsGHDlJqaqnvvvVeDBw9Wy5YtzS4TeWjPnj366KOPdPz4cY0dO9a9/NFHH1VWVpbeffdd/etf/9KBAwdks9kUGhrKL2hXKed8pn/88YcsFouioqIkSZ999pmmTZumW2+9NVdYb926tSpUqKCZM2f+48U54H8OHDigzz77TBEREXriiSf0ww8/aNasWcrOztbzzz+vyMhIrV27Vl9//bXKli2rhx9+WFWrVjW7bJ+Us01s3rxZAwcOVJEiRXTjjTdqwIABioqKcof1u+66S506dVKVKlVyPZ4LFfm/s2fPqkGDBjpz5oxefvllPfXUU5KkEydO6KOPPtKXX36pBx54QL169cr1OHoPf0Q2Dzxkc+8jm4NsnnfI5gjUbM4QPcBcGB5sNpsiIyOVmZmpHj166IcfflCzZs2UkJAgSUpNTVXnzp3Vp08f3XvvvSZXjryQs+fF+PHj9cknn0iSlixZokKFCrnXeeyxx5SVlaWxY8fq5ptvJjBeh+XLl2vkyJGy2WyKiorSxIkTVaJECS1dulTTpk1TTEyMWrVqpdTUVN14443Kzs7WDTfcYHbZyANHjx7VE088odTUVMXHx6tJkyaSpB9//FEzZsyQw+FQ9+7d3ReFu/CiLPjL31+XzMxMpaWladu2bfrggw9UrFgxvfLKKypdurTWr1+vV155RTfffLNefvlllS9f3sTKcb3+aZs4cOCAevfurUKFCmny5MmKiIiQdD6sL1y4UPPnz1fPnj0ZLsKvkM0DG9k8f5HNAxfZPG+QzQMX2fw8hugBaNmyZXr77bd19uxZNWnSRJ06dVJoaKjatm2r6OhotWjRQjfccIOWLl3q/sYvV66c2WXjOuT8gpaVlSW73S6Xy6W5c+fqv//9r+6++2717t3b/YYnSY0aNVKJEiU0Y8YM2e12Eyv3X0lJSXrmmWfUtm1blS5dWjNnzlRqaqpmzpypyMhILV26VB988IFSU1N19OhRff7555zPtABJT0/XJ598ojfffFPt2rXTc889575v/fr1mjVrlk6dOqVnnnlGd955p4mV+q4L9/qaN2+edu/eLZvNpnvuuUf33Xef1qxZow8++EDFixfX0KFDVaJECW3YsEEzZ87U22+/zS8+fuzCkL58+XKlpKSodOnSqlevng4cOKCOHTuqYsWKeu+999yDpuTkZH333Xd6+OGH/XrvFgQmsnngIZvnP7J5YCObXz+yeeAim/+FIXoAyGmxxWLRb7/9pg4dOrgPtViyZIkqV66sfv36yWaz6ZVXXtGhQ4fkdDoVHByssWPH6pZbbjGxelyvnB92a9eu1aeffqrIyEg9/PDDqlatmj788EOtXLlSt912m3r06KHw8HD34w4cOMDFiq5RUlKSVq1apQMHDuj111+XdH7vsa5du+rMmTOaMWOGSpUqpc2bN2v//v2qVauWKlWqZG7RuC4529mBAwckSeHh4SpevLhmzZql1157LdchbpK0bt06LVq0SM8//7zKlCljUtW+68KQPnXqVL333ntq1qyZfv75ZzkcDtWqVUtDhw7V6tWr9eGHH8rlcuncuXN6+umn1bBhQ0nsQeSvLux9QkKC5s6dqxtvvFF79+7Vyy+/rCeffNId1itVqqT33ntPYWFhuT6Hvx8mioKPbB7YyOb5j2weeMjmeYtsHrjI5rkxRC/AZs2apdtuu0233nqrJOmnn37SrFmzZLPZNGLECEnn/+r61ltvqVy5cnr22WdVvnx5HT9+XKdPn1apUqU4154f+/tfC1944QXdf//9+uWXX1ShQgX16NFDt99+uxITE7Vy5UrFxsaqa9euKlKkiMmV+6+cHzAjR47UokWLVLJkSc2dO1fFixeXJKWkpKh79+46d+6c3n//ffe5GOHfcvr+xRdf6LXXXlOhQoUUERGh1157TTfffLNmzpypkSNHXhTWMzIy3Bcvwj9bs2aNPv/8czVo0EANGjRQenq6li9frg8//FD33nuvXnzxRX311Vf66quvdPDgQX3wwQcKDg42u2xcg7+H6zlz5ui9997T+++/r6pVq2r48OGaP3++XnnlFXdY79Spk8LDwzVv3jyFhISYWD1wZcjmgY1snv/I5oGJbO49ZPPAQTb/Z/wZqIDav3+/du7cmSt0/fjjj1q9erV27drlXnb77berX79+Onz4sCZOnKjNmzcrKipK0dHRhHQ/9euvv0qSO6Tv379fo0eP1pAhQzRhwgQlJCTo+++/15QpU/T999+rc+fOatCggVatWqXp06eLv6tdvZzXLDs7W5I0ePBg9ejRQ1arVfPmzdOpU6ckSREREZoyZYqcTqf69u0rp9NpWs3IOxaLRd98841efPFFderUSS+99JJKliyp559/Xnv27FH79u01ePBgjR07VpMnT3Y/jpB+aXv27NHEiRO1ePFi96HrYWFhatCggVq2bKnNmzfrxIkTql+/vl5//XXNmDFDwcHBcjgcJleOq9W3b19NmzbN/R4qSb/88osefPBBVatWTTt37tSmTZvUokULjRw5UrNnz9aNN96oDz74QJUqVeKXM/gFsnngIpvnP7J5YCObewfZPHCQzT1jiF5AVahQQUOGDFHFihW1detWJSUlqWPHjurSpYt27typqVOnute988479dxzz2nHjh1asmSJMjIyTKwc1+Obb75Rr169tHTpUveyI0eOKCgoSC1bttTp06eVmJioJk2ayDAMvfPOO9q8ebOeeuopPfHEE2rRogUXK7pKOXs6rFu3Tq+//rrefPNNJScnq1u3bmrcuLFWrlypRYsW6cyZM5LOh/V58+bpzTffLDCHNAWa//73v9q/f7+k8/03DEOLFy9Wu3bt1KlTJ912223at2+fHA6HnnnmGe3evVvt27dXv379lJiYqNOnT/ML8T9wuVy5bpcpU0bt27dXZGSkFi9e7F4eHh6u++67T9u3b9fOnTtzPcYwDAUFBeVLvcg70dHReuutt/TRRx8pMzNThmHoxIkTKly4sE6cOKEFCxaocePGevXVV9W0aVONGDFCI0aMUFRUlN555x1ZrVYGH/B5ZPPARDbPf2TzwEM29w6yeeAim3vGd3MBlBMcChUqpDNnzigxMVEHDx7UyJEj1aVLF7lcLveho507d5Yk3XHHHYqPj1eZMmX4C6wfi4yM1F133aUZM2ZIkpo2barIyEiVK1dOe/fu1XfffafChQvr1Vdf1aFDh/Twww/rlVdeUatWrXIdyoYrZ7FY3Ifk3nvvvfr888+1Y8cODRgwQM8++6wMw9CyZctktVrVrFkzFS9eXEWKFOHQXD/lcDj00UcfaerUqZo5c6ZuuOEGZWVl6ffff9ett94qh8OhCRMm6K677tLjjz+uESNG6IUXXtCrr76qrl27qlWrVipatKjZX4bPufAQ92+//VY2m02lS5fWQw89JKvVqqlTpyohIUEDBgyQJJUsWVLR0dEXbUcMGvxLTl555plnVLhwYQ0fPlwul0vt2rVT//79dfLkSf3666/at2+fmjdvrrCwMJUuXVp16tTRvn37cl1cj8EHfBnZPHCRzfMf2TywkM29g2wemMjml8cQvQC68I2qaNGiatOmjRYuXKiRI0dq8ODB6tatmywWi6ZPny6r1eoOaHXr1jWpYuSV6tWr68knn9ScOXM0bdo0Wa1WNW7cWEOGDFHp0qX1zTff6IEHHlChQoVkGIZq1Kih++67z32xD1y9vXv3KiEhQcOGDdNjjz2mpKQkNW/eXG+99Zaef/5594XB5syZo+DgYLVt25YLqvixoKAgzZo1S3369NFTTz2ladOm6YYbblDbtm0VEhKiDRs26PDhw3rqqadUvXp1lSxZUtu2bVOfPn305ZdfEtL/gWEY7m0iISFBCxcuVFhYmMqWLavu3buradOmkqR3331Xu3btUq1atbRr1y6dPXtWNWvWNLN05IGcsP7UU0/J5XK5zwvdrl073XjjjZowYYJsNpvq1q0rp9OpPXv2qE2bNnrkkUckcZEq+AeyeeAim+c/snlgIZvnPbJ5YCObXxpD9AJi3759qlixYq5v1pxv/n//+9/uoJAT1rt27Sqr1ap33nlHwcHBateunYnVIy/k9Pumm25S69at5XK59P7778tms6lhw4Y6efKkfv/9d0VEREg6f0GjyMhItW/fnnNsXocjR44oODhYzZs316lTp/Tuu+/q0Ucf1a5du/Tqq69q5MiR6tOnjwoVKqT77ruvQP9ACRSFChXShAkT1LNnT3Xo0EEzZsxQixYtJEnjx4+XJN11112Szh/eOGrUqP/f3p3HVVnn/R9/ncOOiEqIypiUWaCjaEOhlam32ySaWlmKpihouSZuuUdpuOGKSoobAi7pzyUjl1xCK80tckvNTFPTEXdxYTtcvz8azq0zdzOpwGF5P/9JD8jjcw5d8DrX9b2ui/r16+Pm5mazmQure29YM3/+fNauXcu8efNwdXVlypQpREVFYRgGQUFBmEwmoqKiOHHiBO+99x4xMTH/9jWk6Lg3sG/fvk2pUqUIDQ3FMAzGjh2LyWSiY8eOBAQEMGvWLAYNGsTZs2fJzMy0vnm7902eSGGjNhe1uW2ozUsetXneUZuXXGrzP6d4P7sS4syZM7Ro0YItW7bcd90qk8lkvbZXYGAgwcHBeHp6EhkZybFjxwgNDWXQoEG89NJLthpd8khupKemppKeno6fnx/vvPMONWvWJDY2lg0bNuDh4UGdOnWIjY3l9ddfJz4+ngEDBijSH9Lly5cBKF26NFWrVuX06dOsXbsWd3d3IiIimDx5MgcPHmTEiBGsXLmSsLAwHn/8cRtPLY/i3msluri4MG/ePP7yl7/QtWtXzp07B/x+Q6IjR46wc+dOJk6cyNdff42/vz/lypWz1diFUnh4OLt378bOzg6LxYLFYuHIkSP069eP2rVrk56eztGjR/Hx8eGTTz4hOTmZFi1aMHjwYDw9Pfnhhx9s/RTkEdwb6YsWLWLUqFH07duXq1evEhYWxtChQxkzZgxLliwhMDCQqVOnYhgGtWrVYuXKldjb22OxWHSKsBRaanNRmxc8tXnJozbPO2rzkk1t/gAMKRaSk5ONv/3tb8aXX35pWCyW+z6Wk5Nj/fPu3buNQYMGGW3atDGOHz9e0GNKPsj9/m7evNlo1aqV8eqrrxqTJk0yLl++bJw/f96IiIgwXn/9dWPnzp3GtWvXjJUrVxpz5swxTp48aePJi66jR48aYWFhxpdffmkYhmGcOHHCuH79utG1a1cjMTHRMAzDOHTokNG2bVvjww8/NH777Tdbjit5IHc7++GHH4xVq1YZn3/+uWEYhpGdnW106tTJaNy4sXH27Fnj0qVLRr9+/Yz/+Z//MVq1amUcOXLElmMXSnfu3DEGDBhg1KpVy9i/f79hGIaRlpZmNG/e3Fi8eLFx7do1Y8iQIcbChQuNn3/+2XjjjTeMpk2bGosWLTIMwzC++OILo1WrVkb//v1t9yQkT0yYMMH429/+ZowaNcp47bXXjL///e/G2bNnDcMwjAULFhjVq1c3li5dahiGcV/bZGVl2WRekQehNi+51OYFT21e8qjN847aXHKpzf87Xc6lmGjYsCFTp06lf//+REVF0aRJk//zNIrAwEBycnL47LPPKFWqlA0mlbxmMpnYunUrAwYMoGfPnlgsFnbt2sWNGzcYOHAgvXr1IiYmhmnTphEWFka7du1sPXKRl5OTg5OTE6tWrcLBwYFGjRpx6dIlzp8/T+XKlQH46quvePzxxwkPD9e19oo445+ryTZu3MiwYcOoXLky58+f57vvvuPjjz8mLi6Orl27EhYWxsKFC4mOjubcuXO4ublRtmxZW49f6Dg7OzNmzBjKlClDly5dSEhI4Nlnn2XcuHGYzWZSUlK4efMmAQEBPPXUU3h7e2MYBjt27KBr1640atSIjIwMli9fTmpqKl5eXrZ+SvIn5G5HuStdvv76a7788ktWrFjBU089xZYtW+jbty/du3dn3rx5hIaGYjab+eijj3jsscdo3ry59WvZ2ytfpfBTm5dcavOCpzYvWdTmeUttXjKpzR+SLffgS95LTk42ateu/X+uejl//rzx8ccfG4cPHzbS09NtNKHkpZycHOPOnTtGWFiYsWDBAsMwDOPmzZtGvXr1jKCgIGPEiBHGlStXjAsXLhiDBw82unTpYqSlpd23Akr+WO429K/bkmH8vuJl4MCBRo8ePYzNmzcbOTk5RmhoqPHqq68a7dq1M5577jnjxx9/LOiRJY/86zbyww8/GIGBgcbatWuNjIwMY/Xq1Yavr6/x0UcfGYbx+6qXzp07G88995xWN/0H2dnZ1j/v2bPHePvtt406deoYBw4cMAzj921t8ODBRq9evQzDMIz09HQjLCzMWL169X3fk7t37xppaWkFO7w8tLS0NOPKlSv3PbZhwwajXbt2RkZGhvHjjz8ab7/9tvHpp58aYWFhxiuvvGKcOXPGMAzD+PLLL0vU6hYpftTmJYvaPH+pzUsutXn+UJuXTGrzh6drohczDRs2ZMaMGQwZMoQtW7ZYH09NTWXq1KkkJCTg6OiIk5OTDaeUh3Xy5EkOHjzIzZs3rUcO7969y/Hjx6lSpQqZmZlMmDCBli1bEhYWxp49e5gxYwZZWVmMGjWKyZMn4+bmVjKuVZUHcleMmc1mdu/ezfjx460f8/PzIywsjNKlS7N06VIOHjzI5MmTefPNN2ncuDHLly+nevXqthpdHlHuNmL881qLP/30EzVr1qRNmzbcvXuXtWvX0qxZM5KSkhg9ejR2dnbMmzePunXrkpWVZcvRC7XcmwxNmjSJkSNHUqpUKUqVKkWnTp3Yv38/ZrOZgIAATpw4wfTp0+nVqxc3btygdevW1pUS8PuKGd0MqmgYMmQIPXr0ICQk5L6foZmZmVSvXp3r16+zfv16/P39adGiBZ07d+bUqVO0b9+e7du306xZM+zt7cnOzrbhsxB5eGrz4k1tXrDU5iWX2jx/qM1LHrX5oylBa+5LjtxY79+/P2azmVq1ahEVFcWWLVtYs2YNTz/9tK1HlIcwZcoUkpOTOX/+PDVq1KBhw4Z069YNDw8PunTpgoODAzt27OD27du0b98ef39/EhIS2LhxI0eOHGHp0qU6dfFP2r59O7/88guHDx/mmWeewdfXF8MwiI+Px8HBgcGDBwNQo0YNevTowahRoxg/fjy9evWic+fONp5eHsXKlSv5+eefMZvNNGrUiLp16wLwj3/8AxcXF27fvs3ixYvx8fHhvffeY8OGDURGRnLt2jVGjx7NzJkz9Ub4vzhw4ABJSUnMmjWLv/71r1y9epX58+cTEhJCYmIibdu25cyZM+zevRsvLy/mzp1rvclRbuhL0RAcHIzFYqFr167cunWLcePGYbFYGDVqFK1bt6ZOnTqYTCaSk5Pp1asXpUuXxs7OjqCgIKpWrUr9+vWtX6tEnSYqxY7avHhSmxcctXnJpTbPf2rzkkNt/uhK5rMuAXJjPTw83Hp38qVLl+roexE1btw41q5dy6xZsyhTpgyfffYZu3bton79+vj5+dGhQwdcXV0ZNWoUTk5O+Pv7k52djbe3N23btqVly5Y4Ojra+mkUCZMnT2bz5s1UrlwZDw8PVq1ahclkwtfXl6lTpzJy5EgsFgtDhw4Ffl/1EhAQwObNm9m0aRMBAQE6Cl9EjR8/ni+++IKXXnqJ69evk5iYyPTp02nSpAndunVjz549XL9+nX379vH666/j6emJu7s71atX58yZM2RlZSnS/8WdO3dwdXW97zFHR0ccHR1xdXXFzs6O8uXLM3ToUCwWC926dWPlypW8//77pKen4+TkhMlkIjs7u8SGWlH19ttvk52dTVxcnPU6zx4eHsTExHD16lU8PDyoUqUKhw8f5tq1a9SsWZObN2+SmJiIl5cXffv2BdAbNCk21ObFi9q84KjNSy61ed5Tm5dcavO8of/ri7GGDRsyffp03n33XdauXYufn5+tR5KHMGHCBJKSkoiPj7d+D3v16kX79u3Zt28ffn5+1tNAHR0duXr1KidOnGDTpk2cPHmSkSNH4unpaeNnUTTEx8ezbt06oqOj8fX1xcXFhQsXLrB7926ioqK4fv06kZGRREREYDabGTJkCPD7Udi2bdsSEhKiSC+iJkyYwKpVq4iPj6dGjRrcvXuXmJgYli5dSr169XBzc6Nx48Z8++23/Prrr7Rt2xaAY8eOUa9ePQYMGKA3w/8i9zVp1qwZTZs2tT6elZVFWloaZ8+epVq1atYIDwoKIjExkVatWt33O8swDEV6ETNo0CCOHDlivVFiVlYWDg4OVK5cmWvXrpGYmEhqaipNmjShQYMGPP3007z66qt4e3vj4ODAzJkzgd+/9yU50qX4UZsXD2rzgqM2L7nU5nlPbV5yqc3zjv7PL+YaNmxISkoKLi4uth5FHsL+/fuJi4ujZ8+e+Pn5Wa87Vbp0aapVq0ZSUhJffPEFrq6uTJgwgWbNmjFz5kx69OiByWRi9uzZeHt72/hZFH6GYZCVlcXevXvp1asXderUsT5eqVIlWrVqRYUKFRg0aBBbt25l0qRJDBw4kN27d+Ph4cH+/ftZvXq1TsktoiZNmsTSpUtZsWIFfn5+WCwWXFxcePbZZ9mzZw8nT54kLS2NZ599Fj8/P9LT0+nWrRtly5bl22+/tV7PVv7X+fPnOXLkCPb29nz55Ze88MILNG7cmHbt2uHv70/Hjh0JDw8nISEBf39/ALy8vAgKCqJGjRpUq1bN+rW0gqjoCQgI4Pvvv+fLL78kKCjI+nsoJiaGO3fucPnyZQ4cOMB3333H3bt3WbRoEWvWrMHJyYnmzZtbr7OoN2hSHKnNiza1ecFQm5dsavO8pzYv2dTmeUc3Fi0BFOlFV6VKlejduzeff/45K1aswN7eHnt7e2JjY9m2bRsNGjTgpZde4tq1a3Tt2pXAwECio6OZPXs2y5cvp0aNGrZ+CkWCyWTi5s2bpKSk8OSTT973OPy+miUgIIA+ffpw6NAh3N3dSUhIwMfHh4oVK7J06VJ8fHxsNb48ol9//dV6RD0rK8v65x9//JEDBw4wevRowsLC6NOnD5cuXeKTTz7B1dUVZ2dnEhIS8PX1teX4hZK3tzcNGjSgbNmyLFmyhLt37zJjxgxeffVVkpKS+Pvf/07nzp0JDg7mk08+YdGiRURERJCamkpYWFiJvllNUZZ7s6+OHTvy7rvvkpCQwPr167lz5w7h4eH88ssvrFixgjFjxvD5559Tvnx5VqxYAcBrr71GUFAQ9vb2WCwWRboUa2rzokttXjDU5iWb2jzvqc1LJrV53tOrIFKIeXt70759e8xmMzExMXh4eJCamsqCBQuYPXs2DRs2BKB+/fqEhoayY8cOmjZtSoUKFWw8edHj7u5OuXLluHTpEvD7L5zcUM/JycHR0ZH69eszc+ZM0tLS8PPzIyIiAnd3d1uOLY8g93s8e/ZsevfuTffu3Zk5cybPPvss8+bNY/HixURHRxMYGMjFixfp1KkT69evZ+DAgdSpUweLxYKDg4Otn0ahk/u69u3bl+bNm/P999+zYMECtm3bxtq1axk2bBheXl707t2bDh06kJKSQk5ODqVKlWLOnDmYTCadJlpEmUwmcnJyMJvNdOjQgZycHObNm8fq1asBiIuLw8vLi8zMTBwdHWnYsKF1xcu9OxVL+mmiIlJ4qc0Ljtq85FGb5w+1ecmlNs972gpECrkKFSrw5ptvAjB69GiuXbvG//t//4+aNWtar2X12GOPUbFiRUXjQzIMA4vFgru7Ozt37uTVV1+1xoLJZMJsNvPTTz/x008/4ezszIgRIyhVqhTdunUjODjY1uPLQzKZTNYbo8TExNCzZ08GDRrEyy+/zKZNm5gxYwYvvvgiOTk5lC1blqZNm3LgwAFrZJjNOpnr/5Iba2XLlqVjx4589tln1K1bl6ZNm9K0aVMCAgIwm81ERERQp04dXF1dmTt3rjXOdKpg0ZMb5wBms9ka3h07dsTNzY0xY8bw5ptvWt/Y5p5ivWvXLnx8fLQqV0SKFLV5/lObl0xq8/yhNi951Ob5Rz9lRAqR3NNt4PcffLkqVKhAly5d6NSpE56enpw8eRLA+kNv7dq1ZGdn67TFh2QymXBxcaFPnz6sWbOGJUuWWB83DINbt26xceNG1q1bx927d2nXrh0zZ86kZcuWNp5cHsa925adnR3p6ekAzJkzh9q1a/Ppp5/Ss2dPXnzxReB/Tx3OyMigWrVqisg/ITfaGjduzKlTpzhw4AAAbdq0wdfXl2XLTicv8AAAHZtJREFUlhEXF4ePjw8ZGRnW11irXIoms9lMZmYmU6ZM4erVq9bw3rdvH61bt+b9999nw4YNLFu2jKtXrwLwzjvvcOnSJT744APg/t9/IiKFhdrcNtTmJYvaPP+pzUsWtXn+0dYgUojk/rKaOnUqfn5+1ps4rFy5knr16tGxY0frx7OysmjXrh3R0dEsWLCApUuX6lTRR1SvXj169uzJ2LFjuXXrFiEhITg7O+Pm5kbPnj2ZMWMG58+fJyQkBA8PD1uPKw8pNyK/++476tWrh7OzMwCZmZlMmzYNi8XCwoULqVmzJs899xwmk4no6Gh27NjBihUrtMrlAfj7+9OpUyfGjx9PTEwM3t7eREdH4+HhQfny5fH398fJyQm4f8WEFD3Hjx/n0KFDTJo0iQkTJtC7d2+ysrKoWbMmb731FhaLhdjYWMxmM99++y2pqakkJSXpRkUiUqipzW1LbV4yqM0Ljtq85FCb5w+TocMLIoVOZGQk33zzDRMmTGDXrl3MnTuXhQsX8uyzz3Lx4kVWrlzJ2rVrqVSpEocOHSIxMZGaNWvaeuxi4fbt28THxxMdHU3dunXx8/PDzc2Ns2fPkpycTFxcHNWrV7f1mPKItm/fTmxsLK1atbLeQOfUqVNMmjQJgN69e3Pw4EFiY2PZsmUL8+bNY9myZdrOHkJKSgr9+vXD29ub2bNnU758+X/7nHuvcypFU2ZmJkePHiUxMZF9+/ZRqlQpEhMTKVu2rPVzVqxYwQcffMAzzzzDqlWrcHBwUKSLSJGgNrcdtXnJoDYvOGrzkkFtnj+0E12kELn3aO+iRYuIjY0lIyODadOmWW9UBHDx4kUSEhJISkpi9uzZ/PWvf7XVyMXWgQMHWL58Ob/++isuLi74+vrSrl07qlatauvRJA9cvHiRzz77jJ9++on09HR2797NjBkzqFevnnUb7Nu3L1u2bMHFxYXExERtZ49gxIgR7Nixg23btuHo6KgwL2buje3OnTtz9OhRnnvuOWbMmIGTk5P1GsEAO3fuJDAwUKtcRKRIUJsXHmrz4k1tXrDU5sWb2jz/aCe6SCGQ+0vrzp07uLq6ApCQkMDUqVMpU6YMkZGRBAYG3ne38YsXL+Lo6Ei5cuVsNbZIkXRvHHTv3p3vvvuODh06MGrUKADrzYkAPv74Y9544w2tcHpIuT/bzp07R6dOnXjjjTd47733bD2WPKJ732jd++du3bpx+fJlevbsSXJyMtevX2fatGm4ubn925szRbqIFGZqc5GCozYvOGrz4kltXnB0gSORQsBkMrFlyxYmTZpERkYGK1euZMaMGcTFxdG+fXuGDx/O119/fd/NHSpUqKBIz2f3vt463lg83HtznLlz53Lo0CFat27NlStXWLx4MfD73cmzs7MBGDVqlCL9EeSG2WOPPUaVKlW4cuWKjSeSR5WTk2P9vmZlZXH79m0Abt26xRNPPMGcOXNo2bIlrVu3xtvbm6ysLIB/W92kSBeRwkxtXjipzYsftXnBUpsXP2rzgqVXSaQQ+O2331ixYgUtW7bEzs4OOzs7693Ia9euTXp6Om5ubjrFqoDd+3rrtS+67j3KnvvfkydP8tlnnzF9+nRq1apFXFwcu3btolmzZnh7eysi8piLiwsTJkygYsWKth5FHoFhGNZTqmNjY9mzZw/nzp3j+eef57XXXmPkyJHWbeell16ibt26ODo66sZUIlLkqM0LJ7V58aA2tz21efGgNi94upyLiI2dPHmSVq1a4evry+TJk6lWrZr1YxaLBTs7O+vfda0ykQeTu83s27eP/fv3c+3aNQIDAwkMDCQzMxMPDw8Arly5wu3bt6lSpYqNJy7+/vXnmhQ9MTExJCYmMmDAAPz8/OjevTs1atRgwoQJVKhQwdbjiYg8ErW5SP5Rmxc+avOiT21ecHToQcTGnnrqKYKDgzl27BhHjx6972P/+stMkS7yYHJPx+7VqxdHjhwhOzub3r17M2PGjPu2r9xTGiX/KdKLntz1FoZhcP36dfbu3cvo0aN58803ycrKIj09neDgYFJTU7l48aKNpxUReTRqc5H8ozYvfNTmRY/a3HZ0ToxIAcs9+n758mWys7OpWLEiH3zwAdnZ2YwePRpPT09eeOEFW48pUiSdPn2aKlWqYDabMQyDq1evEhsbS3h4OJ06dSIrK4tPP/2USpUqkZWVZb2GnN4Ei/yxe0+5Llu2LJmZmZQtW5atW7cyePBg3n//fZo3b25d9TJw4EAbTywi8uepzUXyj9pcJO+pzW1HK9FFClBupG/ZsoW3336b7t2788477wAwZswYWrZsSe/evdmzZ4+NJxUpWgzD4Ouvv+aVV15h27Zt1m3N2dmZnJwc6taty7lz52jSpAmtW7emQ4cOjBkzhq+++kqRLvInxMXFMWLECAA8PT2ZNWsWw4cPZ8iQIXTq1Mn6+OXLl205pojIA1Gbi+QPtblI/lKb24Z2oosUIJPJxPbt2xkwYACvvPIKPXv25MSJE7z77rsAREZGEhQURN++fdm5c6eNpxUpOrKzs3n55Zd56623GD58ONu2bSM7O5vs7GwMw2D79u1069aNhg0bMnbsWFxdXUlLS2PHjh22Hl2kSChTpgynTp3i6tWrhIeHc+rUKZ544gnatWtHTk4OhmFw+vRpypcvb+tRRUT+NLW5SP5Qm4vkL7W5bWgnukgBMQyD9PR0EhIS6NmzJ+Hh4QQGBmIymfjmm29o37498Husv/zyywwbNoz09HQbTy1S+M2ZM4ewsDDg91VjrVq1YvDgwSQnJ1OmTBneeustoqKiqFKlCmPHjrXeidxkMuHt7W3L0UUKtXvvPe/v78/t27dJSkriySefZPbs2fz88890796dsLAw3n77bW7fvk2/fv1sOLGIyJ+nNhfJH2pzkfyhNrc9k3Hvd0FE8lVmZiYhISF06NCBJk2aMHHiROzt7QkODiYkJIS//e1vfPTRR3h6enLp0iUdNRT5E1JTU7lx4wZPP/209bEPP/yQNWvWMGXKFBo1akRsbCzR0dEMHDiQUqVKce7cOVavXs3y5ct58sknbTi9SOERGxtLixYtePzxxwHYu3cvzz//vPUU7CVLljB79mwSEhJ46qmnOHfuHOvXr+fq1auUKVOGHj16YG9vj8Vi0U2qRKRIUJuL5D21uUjeUJsXPrqxqEgBcnR0JDAwkLJly7Jv3z5u3rxJSEgITzzxBD4+PmzdupULFy6wdOlSRbrIf3Hx4kVu375N1apV8fLyYv/+/Xz88ccsW7aMDz/8EIAhQ4YwZcoUevfuTfny5VmxYgUWiwV3d3cWL16sSBf5p2PHjrFv3z5CQ0MBOHr0KJ07d+bFF1+kRYsWtG7dmk6dOvH999/zzTffUKVKFSpXrmy9dnAuRbqIFCVqc5G8ozYXyTtq88JJK9FF8knu0cGjR49y/fp1Ll68SFBQEI6OjgCMHz+eq1evEhUVBcDw4cMJCgqiWrVqVKpUyZajixQJX3/9NTt27KBXr17cvHkTR0dH3n77bSpWrMiiRYtwcnIiIiKCdevWERUVRdOmTbl16xaOjo5kZWVRqlQpWz8FkUIhIyMDJycnsrKycHBwIDk5mQoVKuDh4cGsWbM4ffo0169f54MPPiApKYmzZ88yf/58zGazwlxEigy1uUj+UpuL5A21eeGla6KL5BOTycSmTZsIDQ1l/vz5REdHExoaSkJCAgDXrl3j3LlznD17lqioKL7++mtFusgDSE9PZ//+/QwZMoT27duTlZXFsmXLuHr1Kl26dCEjI4OPPvqINm3aMHToUDZs2ICzszOOjo6KdJF/Sk1NZd68edy4cQMHBwdu3rzJkCFDWLhwIQ4ODnz44YdMnDiROnXqEB0dTWZmJjt37mTOnDkAinQRKTLU5iL5S20u8ujU5oWbdqKL5JOzZ88yceJEwsPDWbBgAbGxsezbtw9nZ2cyMzPp06cPV65cITQ0lI0bNxIbG6tIF3kAzZo1o1GjRnz77bf4+vpiNpupUKECixcv5saNG9ZY//DDD2nSpAnjx48nIyPD1mOLFCpZWVns27ePsWPHMmTIEA4cOMDcuXM5ePAgUVFRnDlzBm9vb8aOHUvv3r159tlnsbe35x//+IetRxcReSBqc5H8pTYXeXRq88JNl3MRySeHDh1iyJAhbNy4kfPnz1uvX9W7d2+WL19OaGgozs7OnDp1Ci8vLzw8PGw9skiRkZOTg9lsJj4+ngsXLpCSkkL16tXp0KEDvr6+XLx4kZCQEDw8PFi4cCHOzs66IZjIH9i2bRsfffQRFy9eZP78+dSvX5+9e/cyfPhw6tatS0hICM8884z18y9cuICXl5dWuohIkaI2F8k/anORvKM2L7y0El0kn3h4eFCxYkW2bNlCx44dqV+/PmPHjqV06dLEx8ezefNmnJyc8PPzU6SL/Em5x31TU1O5ePEiXbp0YejQoXTu3JnDhw+zfPlyjh8/bl318ttvv9GjRw8APD09bTm6SKGTuz2VK1cOFxcXatWqxerVq7l48SLPP/8848ePZ/fu3cTHx3Py5Enrv6tUqRJ2dnZYLBZbjS4i8sDU5iJ5T20uknfU5oWfVqKL5IHcGxX94x//wM7ODk9PTzIyMnj33XfZvXs3bdq0YeLEiQDcunWL0NBQ+vXrx8svv2zjyUWKnk2bNhEZGYmdnR0VKlQgJiYGDw8P1q9fT1xcHLVq1eLNN9/k1q1bPP7442RlZVG5cmVbjy1SaOSuFsuVkZHBnTt3OHjwIAsWLKBs2bKMGjUKLy8v9u7dy6hRo3jmmWcYNmwYf/nLX2w4uYjIn6M2Fyk4anORR6M2Lzq0E10kj2zcuJFp06aRlpZGixYt6NatG87OzgQHB+Pr68trr71G5cqVWb9+PatWrWLFihV4e3vbemyRIuXYsWP06dOH4OBgvLy8SEhI4NatWyQkJODp6cn69etZsGABt27d4uLFi2zYsEHXMxW5R+6OJYBPP/2UEydOYGdnx0svvUSDBg3Yvn07CxYsoFy5ckRERODh4cG+fftISEhg2rRp9wW+iEhhpjYXyX9qc5FHozYvWrQTXeQh5W46JpOJ06dP06VLF7p27QpAUlISVatWJTw8HDs7O0aNGsVvv/2GxWLBwcGByZMnU6NGDRtOL1L0HDt2jK+++oqzZ88ybtw44PfVY927d+fGjRvEx8dTvnx5UlJSOHPmDLVr1+aJJ56w7dAihci9kT5v3jxmz55NmzZtOHr0KNnZ2dSuXZuIiAiSk5NZtGgROTk53L59m169etGsWTPg31fKiIgUFmpzkYKlNhd5NGrzokc70UUeUGJiInXq1KFmzZoA/PjjjyQmJmJnZ8fYsWMB2Lt3L1OnTsXb25v+/fvzl7/8hcuXL3P9+nXKly+v6yyKPIDcuIiMjGTNmjU89thjLF++nHLlygFw8+ZN3nnnHW7fvs38+fOpUKGCjScWKdy2b9/Ohg0baNq0KU2bNuXu3bts2rSJRYsW8fLLLzN48GC2bt3K1q1bOXfuHAsWLMDBwcHWY4uI/J/U5iIFS20ukrfU5kWHDleIPIAzZ85w5MgRSpcubX1sz549JCcnc/z4cetjzz//POHh4Zw/f56YmBhSUlKoUKECvr6+inSRPyn3GG9WVhYAI0eO5N1338VsNvPpp59y7do1ANzd3YmNjcVisfDee+/phioi/8HPP/9MTEwM69atw9HREQAXFxeaNm1Ku3btSElJ4cqVKzRp0oRx48YRHx+Pg4MD2dnZNp5cROTfqc1FCo7aXCTvqc2LFu1EF3kAVapUYfTo0fj4+HDgwAGOHTtGSEgIYWFhHDlyhHnz5lk/t27dugwYMIDDhw+TlJREenq6DScXKVpyV7js2rWLcePGMWXKFC5dukSPHj145ZVX2LZtG2vWrOHGjRvA77H+6aefMmXKFOzs7Gw8vUjhkZOTc9/fK1asSOfOnfH09GTdunXWx93c3GjQoAGHDh3iyJEj9/0bwzCwt7cvkHlFRB6E2lykYKjNRfKG2rxo06su8iflhoOrqys3btxg4cKFnDt3jsjISMLCwsjJybGeOhoaGgpAYGAgH374IRUrVsTZ2dnGz0Ck6DCZTGzatIlBgwbx8ssvs2HDBg4fPszQoUPp378/hmGwceNGzGYzbdq0oVy5cpQuXfq+lWgiJd2910j85ptvsLOzw8vLi1atWmE2m5k3bx4TJ05k6NChADz22GP4+vr+23aUe61GEZHCRG0uUnDU5iKPTm1e9GknusifdO8PqjJlytChQwdWrVpFZGQkI0eOpEePHphMJhYvXozZbLbeyOi5556z0cQiRdcvv/zCxIkT+eijj3jjjTc4duwYbdu2ZerUqQwcONB6Y7Bly5bh4OBAcHCwbqgicg/DMKzbxMSJE1m1ahUuLi5UqlSJd955h6CgIABmzpzJ8ePHqV27NsePHyctLQ1/f39bji4i8qeozUUKjtpc5NGozYsH7UQX+Q9OnTqFj4/PfQGQu+rlhRdesIZCbqx3794ds9nM9OnTcXBwoFOnTjacXqTounDhAg4ODrRt25Zr164xc+ZMXn/9dY4fP84HH3xAZGQk/fr1w9XVlQYNGijSRe5hsVisp07Pnz+ftWvXMm/ePFxdXZkyZQpRUVEYhkFQUBAmk4moqChOnDjBe++9R0xMzL99DRGRwkJtLmIbanORh6c2Lz70k03kD5w5c4YWLVqwZcuW+65bZTKZrDdVCQwMJDg4GE9PTyIjIzl27BihoaEMGjSIl156yVajixRZly9fBqB06dJUrVqV06dPs3btWtzd3YmIiGDy5MkcPHiQESNGsHLlSsLCwnj88cdtPLVI4RAeHs7u3buxs7PDYrFgsVg4cuQI/fr1o3bt2qSnp3P06FF8fHz45JNPSE5OpkWLFgwePBhPT09++OEHWz8FEZE/pDYXKXhqc5GHpzYvfrQTXeQPVKlShblz5zJ8+HC2bt36X2O9QoUKDBs2jJ9++onOnTvzxBNP2GhykaLp2LFjDBs2jM2bN+Pv78+gQYPw9PRkx44d1KxZEycnJ27fvk316tWpWbOm3gyL3OPu3buYzWZ69OjB999/j52dHXfv3uXHH38kOzub69evk5CQQNeuXRkyZAgAkZGRxMXFERQURI8ePTh48CDh4eEAWukiIoWO2lykYKnNRR6e2rx40k50kf+gYcOGTJ06lSFDhvxbrN8rMDCQdu3aUb16dUqVKlXAU4oUDzk5OTg5ObFq1SqSk5OpVq0amZmZnD9/nsqVKwPw1Vdf8fjjjxMeHo63t7eNJxYpPJydnRkzZgxvvPEGXbp0ISUlBTc3N8aNG0etWrVISUnh5s2bBAQE8NRTT+Ht7Y27uzs7duwAoFGjRoSGhnLhwgVSU1Nt/GxERP5vanORgqM2F3l4avPiyWTkHrIXkT+0fft2+vfvT1RUFE2aNLnvGm8XLlxg4cKFtG3blmrVquHk5GTDSUUKv9y7kt97d/Jcx44dY968eaSlpfHWW2/RpEkTunfvzqVLl3BycuL06dPEx8dTvXp1G00vUvjce43EvXv3Eh0dzeHDh1m8eDH+/v7k5OQwdOhQbt++TUxMDBkZGfTp04eWLVvStm1b68350tPTyc7Oxs3NzZZPR0Tkv1Kbi+QdtblI3lKbF1/aiS7yJ+XG+qRJk2jevDkAqampREVF8fnnn/P555/z9NNP23hKkaJl9+7dbNu2jeHDh1sf+/HHH1mwYAHXrl2jf//+VKlShaSkJG7dukXz5s156qmnbDixSOE1adIktmzZQtWqVTl8+DA3btwgLi6OgIAAli9fzoIFC2jZsiUHDx4kLS2N5cuXY2dn93++aRYRKezU5iJ5T20uknfU5sWPdqKLPIDcWJ88eTK1atUiKiqKLVu2sGzZMh19F/kvtm/fzi+//MLhw4d55pln8PX1xTAMevfuTVhYGIMHD7Z+7rFjxxg1ahT29vb06tWLhg0b2nBykcLvwIED9OvXj1mzZvHXv/6Vq1evMn/+fJYsWUJiYiJ+fn5ER0eTkpKCl5cXkydPxsHB4b6VMiIiRY3aXOThqc1F8o/avHiyt/UAIkVJw4YNmTFjBuHh4da7ky9dulSRLvJfTJ48mc2bN1O5cmU8PDxYtWoVJpMJX19fpk6dysiRI7FYLAwdOhQAPz8/AgIC2Lx5M5s2bSIgIECnsYn80507d3B1db3vMUdHRxwdHXF1dcXOzo7y5cszdOhQLBYL3bp1Y+XKlbz//vukp6fj5OSEyWQiOzsbe3uloIgUXWpzkYejNhfJO2rzkkPfHZEH1LBhQ6ZPn867777L2rVr8fPzs/VIIoVafHw869atIzo6Gl9fX1xcXLhw4QK7d+8mKiqK69evExkZSUREBGaz2Xp3cnt7e9q2bUtISIgiXeSfBgwYgKOjI82aNaNp06bWx7OyskhLS+Ps2bNUq1bNGuFBQUEkJibSqlWr+35nGYahSBeRYkFtLvJg1OYieUdtXrLoci4iD+nu3bu4uLjYegyRQsswDLKyshg0aBAvvvgiwcHB1sdzj7Tv3bvX+vFWrVoxcOBAqlatioeHB/v372f16tX4+PjY+JmIFA7nz5+na9eu2Nvbc+HCBV544QUaN25Mu3btAJgxYwYLFy4kISEBf39/AM6dO8fUqVOpUaOG9d+KiBRHanOR/0xtLpK31OYlj65UL/KQFOki/5nJZOLmzZukpKTw5JNP3vc4/L6aJSAggD59+nDo0CHc3d1JSEjAx8eHihUrsnTpUkW6yD28vb1p0KABZcuWZcmSJdy9e5cZM2bw6quvkpSUxN///nc6d+5McHAwn3zyCYsWLSIiIoLU1FTCwsKwt7cnOzvb1k9DRCRfqM1F/jO1uUjeUpuXPDrkISIi+cbd3Z1y5cpx6dIl4H9XugDk5OTg6OhI/fr1mTlzJmlpafj5+REREYG7u7stxxYpdHK3nb59+9K8eXO+//57FixYwLZt21i7di3Dhg3Dy8uL3r1706FDB1JSUsjJyaFUqVLMmTMHk8mk00RFRERKOLW5SN5Qm5dMWokuIiL5wjAMLBYL7u7u7Ny5E8AaCwBms5mffvqJQ4cO4ezszIgRI2jbti1ffPGFLccWKZRMJhM5OTmULVuWjh078tlnn3Hy5EmaNm3KrFmzcHJywmw2ExERwbFjxzAMg7lz5xIdHY2DgwPZ2dnWN8kiIiJS8qjNRfKO2rxk0k50ERHJFyaTCRcXF/r06cOaNWtYsmSJ9XHDMLh16xYbN25k3bp13L17l3bt2jFz5kxatmxp48lFCiez+fdsa9y4MadOneLAgQMAtGnTBl9fX5YtW0ZcXBw+Pj5kZGRYw1yrXERERERtLpK31OYlj24sKiIi+SonJ4fo6GjmzJnDgAEDCAkJwdnZGYDMzExmzJjB9u3biY+Px8PDw8bTihQN06ZNIzExkTJlyuDt7U10dLR1+8nIyMDJyQn4ffvLDXwRERERtblI3lOblww69CEiIvnKbDbTo0cPnJycmD59Ort27cLPzw83NzfOnj1LcnIycXFxinSRB9CoUSNWrVqFp6cn06ZNu2/7yY10wzAU6SIiInIftblI3lOblwxaiS4iIgXmwIEDLF++nF9//RUXFxd8fX1p164dVatWtfVoIkXOiBEj2LFjB9u2bcPR0fG+m4OJiIiI/Ddqc5G8ozYv/rQTXURERKQIyQ3yc+fO0alTJ9544w3ee+89W48lIiIiIlLiqM1LDp1HICIiBereY7c6jivy4HJXtDz22GNUqVKFK1eu2HgiERERKarU5iKPRm1ecmgluoiIiEgR9dtvv1GxYkXs7OxsPYqIiIiISImmNi/etBNdREREpIizWCyKdRERERGRQkBtXjxpJ7qIiIiIiIiIiIiIyB/QNdFFRERERERERERERP6AdqKLiIiIiIiIiIiIiPwB7UQXEREREREREREREfkD2okuIiIiIiIiIiIiIvIHtBNdREREREREREREROQPaCe6iIiIiIiIiIiIiMgf0E50EREREREREREREZE/oJ3oIiIiIiIiIiIiIiJ/QDvRRURERERERERERET+gHaii4iIiIiIiIiIiIj8gf8P6NntIWf9nT8AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制质量指标对比图\n", "def plot_quality_metrics_comparison(results: Dict):\n", "    \"\"\"绘制质量指标对比图\"\"\"\n", "    \n", "    valid_results = {k: v for k, v in results.items() if v is not None}\n", "    \n", "    if len(valid_results) == 0:\n", "        print(\"没有有效的结果可以绘制\")\n", "        return\n", "    \n", "    # 提取指标数据\n", "    methods = list(valid_results.keys())\n", "    gini_coeffs = [valid_results[m]['balance_metrics']['gini_coefficient'] for m in methods]\n", "    normalized_entropies = [valid_results[m]['balance_metrics']['normalized_entropy'] for m in methods]\n", "    vocab_utilizations = [valid_results[m]['vocab_utilization'] for m in methods]\n", "    cv_values = [valid_results[m]['balance_metrics']['coefficient_of_variation'] for m in methods]\n", "    \n", "    # 创建子图\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 基尼系数\n", "    axes[0, 0].bar(methods, gini_coeffs, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('基尼系数 (越小越好)', fontweight='bold')\n", "    axes[0, 0].set_ylabel('基尼系数')\n", "    axes[0, 0].tick_params(axis='x', rotation=45)\n", "    \n", "    # 标准化熵\n", "    axes[0, 1].bar(methods, normalized_entropies, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].set_title('标准化熵 (越大越好)', fontweight='bold')\n", "    axes[0, 1].set_ylabel('标准化熵')\n", "    axes[0, 1].tick_params(axis='x', rotation=45)\n", "    \n", "    # 词汇表利用率\n", "    axes[1, 0].bar(methods, vocab_utilizations, color='orange', alpha=0.7)\n", "    axes[1, 0].set_title('词汇表利用率 (越大越好)', fontweight='bold')\n", "    axes[1, 0].set_ylabel('利用率')\n", "    axes[1, 0].tick_params(axis='x', rotation=45)\n", "    \n", "    # 变异系数\n", "    axes[1, 1].bar(methods, cv_values, color='pink', alpha=0.7)\n", "    axes[1, 1].set_title('变异系数 (越小越好)', fontweight='bold')\n", "    axes[1, 1].set_ylabel('变异系数')\n", "    axes[1, 1].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('BarTokenizer质量指标对比', fontsize=16, fontweight='bold', y=1.02)\n", "    plt.show()\n", "\n", "# 绘制质量指标对比\n", "plot_quality_metrics_comparison(analysis_results)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "BarTokenizer方法对比汇总表\n", "================================================================================\n", "              方法  词汇表大小  唯一Token数 利用率(%)   基尼系数  标准化熵 变异系数   频率范围\n", "          Linear    400       223   55.8 0.5762 0.893 1.08 0.0163\n", "  Linear+Balance    400       223   55.8 0.5762 0.893 1.08 0.0163\n", "        Quantile    400       372   93.0 0.4030 0.953 0.70 0.0065\n", "Quantile+Balance    400       372   93.0 0.4030 0.953 0.70 0.0065\n", "        Adaptive    400       200   50.0 0.5559 0.898 1.02 0.0159\n", "Adaptive+Balance    400       200   50.0 0.5559 0.898 1.02 0.0159\n", "================================================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>方法</th>\n", "      <th>词汇表大小</th>\n", "      <th>唯一Token数</th>\n", "      <th>利用率(%)</th>\n", "      <th>基尼系数</th>\n", "      <th>标准化熵</th>\n", "      <th>变异系数</th>\n", "      <th>频率范围</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Linear</td>\n", "      <td>400</td>\n", "      <td>223</td>\n", "      <td>55.8</td>\n", "      <td>0.5762</td>\n", "      <td>0.893</td>\n", "      <td>1.08</td>\n", "      <td>0.0163</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Linear+Balance</td>\n", "      <td>400</td>\n", "      <td>223</td>\n", "      <td>55.8</td>\n", "      <td>0.5762</td>\n", "      <td>0.893</td>\n", "      <td>1.08</td>\n", "      <td>0.0163</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Quantile</td>\n", "      <td>400</td>\n", "      <td>372</td>\n", "      <td>93.0</td>\n", "      <td>0.4030</td>\n", "      <td>0.953</td>\n", "      <td>0.70</td>\n", "      <td>0.0065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Quantile+Balance</td>\n", "      <td>400</td>\n", "      <td>372</td>\n", "      <td>93.0</td>\n", "      <td>0.4030</td>\n", "      <td>0.953</td>\n", "      <td>0.70</td>\n", "      <td>0.0065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Adaptive</td>\n", "      <td>400</td>\n", "      <td>200</td>\n", "      <td>50.0</td>\n", "      <td>0.5559</td>\n", "      <td>0.898</td>\n", "      <td>1.02</td>\n", "      <td>0.0159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Adaptive+Balance</td>\n", "      <td>400</td>\n", "      <td>200</td>\n", "      <td>50.0</td>\n", "      <td>0.5559</td>\n", "      <td>0.898</td>\n", "      <td>1.02</td>\n", "      <td>0.0159</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 方法  词汇表大小  唯一Token数 利用率(%)    基尼系数   标准化熵  变异系数    频率范围\n", "0            Linear    400       223   55.8  0.5762  0.893  1.08  0.0163\n", "1    Linear+Balance    400       223   55.8  0.5762  0.893  1.08  0.0163\n", "2          Quantile    400       372   93.0  0.4030  0.953  0.70  0.0065\n", "3  Quantile+Balance    400       372   93.0  0.4030  0.953  0.70  0.0065\n", "4          Adaptive    400       200   50.0  0.5559  0.898  1.02  0.0159\n", "5  Adaptive+Balance    400       200   50.0  0.5559  0.898  1.02  0.0159"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# 打印汇总表格\n", "def print_summary_table(results: Dict):\n", "    \"\"\"打印汇总表格\"\"\"\n", "    \n", "    valid_results = {k: v for k, v in results.items() if v is not None}\n", "    \n", "    if len(valid_results) == 0:\n", "        print(\"没有有效的结果\")\n", "        return\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"BarTokenizer方法对比汇总表\")\n", "    print(\"=\"*80)\n", "    \n", "    # 创建汇总数据\n", "    summary_data = []\n", "    for method_name, result in valid_results.items():\n", "        metrics = result['balance_metrics']\n", "        summary_data.append({\n", "            '方法': method_name,\n", "            '词汇表大小': result['vocab_size'],\n", "            '唯一Token数': len(result['unique_tokens']),\n", "            '利用率(%)': f\"{result['vocab_utilization']*100:.1f}\",\n", "            '基尼系数': f\"{metrics['gini_coefficient']:.4f}\",\n", "            '标准化熵': f\"{metrics['normalized_entropy']:.3f}\",\n", "            '变异系数': f\"{metrics['coefficient_of_variation']:.2f}\",\n", "            '频率范围': f\"{metrics['frequency_range']:.4f}\"\n", "        })\n", "    \n", "    # 转换为DataFrame并打印\n", "    df_summary = pd.DataFrame(summary_data)\n", "    print(df_summary.to_string(index=False))\n", "    print(\"=\"*80)\n", "    \n", "    return df_summary\n", "\n", "# 打印汇总表格\n", "summary_df = print_summary_table(analysis_results)\n", "summary_df"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "各方法前10个最高频Token分析\n", "============================================================\n", "\n", "Linear:\n", "----------------------------------------\n", "  Token  772: 0.0164 (  164次)\n", "  Token  789: 0.0151 (  151次)\n", "  Token  793: 0.0150 (  150次)\n", "  Token  771: 0.0150 (  150次)\n", "  Token  779: 0.0146 (  146次)\n", "  Token  784: 0.0146 (  146次)\n", "  Token  786: 0.0146 (  146次)\n", "  Token  778: 0.0145 (  145次)\n", "  Token  776: 0.0145 (  145次)\n", "  Token  775: 0.0145 (  145次)\n", "  前10%Token累计频率: 0.3112\n", "\n", "Linear+Balance:\n", "----------------------------------------\n", "  Token  772: 0.0164 (  164次)\n", "  Token  789: 0.0151 (  151次)\n", "  Token  793: 0.0150 (  150次)\n", "  Token  771: 0.0150 (  150次)\n", "  Token  779: 0.0146 (  146次)\n", "  Token  784: 0.0146 (  146次)\n", "  Token  786: 0.0146 (  146次)\n", "  Token  778: 0.0145 (  145次)\n", "  Token  776: 0.0145 (  145次)\n", "  Token  775: 0.0145 (  145次)\n", "  前10%Token累计频率: 0.3112\n", "\n", "Quantile:\n", "----------------------------------------\n", "  Token  768: 0.0066 (   66次)\n", "  Token  797: 0.0065 (   65次)\n", "  Token  751: 0.0065 (   65次)\n", "  Token  774: 0.0065 (   65次)\n", "  Token  771: 0.0063 (   63次)\n", "  Token  747: 0.0063 (   63次)\n", "  Token  784: 0.0062 (   62次)\n", "  Token  755: 0.0061 (   61次)\n", "  Token  736: 0.0060 (   60次)\n", "  Token  740: 0.0060 (   60次)\n", "  前10%Token累计频率: 0.2155\n", "\n", "Quantile+Balance:\n", "----------------------------------------\n", "  Token  768: 0.0066 (   66次)\n", "  Token  797: 0.0065 (   65次)\n", "  Token  751: 0.0065 (   65次)\n", "  Token  774: 0.0065 (   65次)\n", "  Token  771: 0.0063 (   63次)\n", "  Token  747: 0.0063 (   63次)\n", "  Token  784: 0.0062 (   62次)\n", "  Token  755: 0.0061 (   61次)\n", "  Token  736: 0.0060 (   60次)\n", "  Token  740: 0.0060 (   60次)\n", "  前10%Token累计频率: 0.2155\n", "\n", "Adaptive:\n", "----------------------------------------\n", "  Token  681: 0.0160 (  160次)\n", "  Token  695: 0.0158 (  158次)\n", "  Token  692: 0.0157 (  157次)\n", "  Token  680: 0.0154 (  154次)\n", "  Token  690: 0.0152 (  152次)\n", "  Token  696: 0.0148 (  148次)\n", "  Token  682: 0.0148 (  148次)\n", "  Token  687: 0.0147 (  147次)\n", "  Token  689: 0.0146 (  146次)\n", "  Token  694: 0.0146 (  146次)\n", "  前10%Token累计频率: 0.2910\n", "\n", "Adaptive+Balance:\n", "----------------------------------------\n", "  Token  681: 0.0160 (  160次)\n", "  Token  695: 0.0158 (  158次)\n", "  Token  692: 0.0157 (  157次)\n", "  Token  680: 0.0154 (  154次)\n", "  Token  690: 0.0152 (  152次)\n", "  Token  696: 0.0148 (  148次)\n", "  Token  682: 0.0148 (  148次)\n", "  Token  687: 0.0147 (  147次)\n", "  Token  689: 0.0146 (  146次)\n", "  Token  694: 0.0146 (  146次)\n", "  前10%Token累计频率: 0.2910\n"]}], "source": ["# 分析最高频token的详细信息\n", "def analyze_top_tokens(results: Dict, top_n: int = 10):\n", "    \"\"\"分析最高频token的详细信息\"\"\"\n", "    \n", "    valid_results = {k: v for k, v in results.items() if v is not None}\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "    print(f\"各方法前{top_n}个最高频Token分析\")\n", "    print(\"=\"*60)\n", "    \n", "    for method_name, result in valid_results.items():\n", "        print(f\"\\n{method_name}:\")\n", "        print(\"-\" * 40)\n", "        \n", "        frequencies = result['frequencies']\n", "        unique_tokens = result['unique_tokens']\n", "        \n", "        # 按频率排序\n", "        sorted_indices = np.argsort(frequencies)[::-1]\n", "        \n", "        for i in range(min(top_n, len(sorted_indices))):\n", "            idx = sorted_indices[i]\n", "            token_id = unique_tokens[idx]\n", "            freq = frequencies[idx]\n", "            count = result['counts'][idx]\n", "            \n", "            print(f\"  Token {token_id:4d}: {freq:.4f} ({count:5d}次)\")\n", "        \n", "        # 计算前10%token的累计频率\n", "        top_10_percent_count = max(1, len(frequencies) // 10)\n", "        top_10_percent_freq = np.sum(frequencies[sorted_indices[:top_10_percent_count]])\n", "        print(f\"  前10%Token累计频率: {top_10_percent_freq:.4f}\")\n", "\n", "# 分析最高频token\n", "analyze_top_tokens(analysis_results, top_n=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 分析结论\n", "\n", "### 指标说明\n", "\n", "1. **基尼系数**: 衡量token分布的不平衡程度，越小越好（0表示完全平衡）\n", "2. **标准化熵**: 衡量token分布的随机性，越大越好（1表示完全随机）\n", "3. **词汇表利用率**: 实际使用的token数量占总词汇表的比例，越大越好\n", "4. **变异系数**: 频率分布的变异程度，越小越好\n", "\n", "### 方法对比\n", "\n", "- **Linear方法**: 简单直接，但对异常值敏感\n", "- **Quantile方法**: 对数据分布适应性强，通常表现最好\n", "- **Adaptive方法**: 平衡了等频和等宽的优势\n", "- **+Balance后缀**: 添加了频率平衡策略，可以改善token分布\n", "\n", "### 使用建议\n", "\n", "1. **推荐使用Quantile+Balance方法**作为默认选择\n", "2. 对于计算效率要求高的场景，可考虑Linear方法\n", "3. 对于数据质量较高的场景，Adaptive方法也是不错的选择"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d:\\miniconda3\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc\n"]}], "source": ["import matplotlib\n", "print(matplotlib.matplotlib_fname())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}