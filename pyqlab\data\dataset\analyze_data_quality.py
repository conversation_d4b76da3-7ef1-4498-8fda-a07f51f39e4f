"""
数据质量分析工具
用于分析K线数据的质量问题和token分布不平衡问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import os
from argparse import ArgumentParser
from pyqlab.data.dataset.utils import get_vocab

class DataQualityAnalyzer:
    """数据质量分析器"""
    
    def __init__(self, data_path, market='fut', period='min1', block_name='top'):
        self.data_path = data_path
        self.market = market
        self.period = period
        self.block_name = block_name
        self.analysis_results = {}
        
    def load_data(self, year=None):
        """加载数据"""
        print(f"加载数据: {self.market}_{self.block_name}_{self.period}")
        
        if year is None:
            # 加载所有年份的数据
            if self.period == 'day' or self.block_name == 'sf':
                file_path = f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}.csv'
            else:
                # 尝试加载多个年份的数据
                dfs = []
                for y in range(2020, 2026):
                    file_path = f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}_{y}.csv'
                    if os.path.exists(file_path):
                        df_year = pd.read_csv(file_path)
                        dfs.append(df_year)
                        print(f"  加载 {y} 年数据: {len(df_year):,} 条")
                
                if dfs:
                    df = pd.concat(dfs, ignore_index=True)
                else:
                    raise FileNotFoundError(f"未找到数据文件")
        else:
            file_path = f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}_{year}.csv'
            df = pd.read_csv(file_path)
            
        print(f"总数据量: {len(df):,} 条")
        return df
    
    def analyze_token_distribution(self, df, save_plots=True):
        """分析token分布"""
        print("\n分析token分布...")
        
        if 'bar' not in df.columns:
            print("数据中没有bar列，跳过token分布分析")
            return {}
            
        # 统计token分布
        token_counter = Counter(df['bar'])
        total_tokens = len(df)
        unique_tokens = len(token_counter)
        
        # 获取词汇表大小
        vocab = get_vocab(field_num=3 if self.period in ['min5', 'min1'] else 4)
        vocab_size = len(vocab)
        
        print(f"  总token数: {total_tokens:,}")
        print(f"  唯一token数: {unique_tokens}")
        print(f"  词汇表大小: {vocab_size}")
        print(f"  词汇表利用率: {unique_tokens/vocab_size:.2%}")
        
        # 分析高频token
        print(f"\n高频token分析:")
        high_freq_tokens = []
        for token_id, count in token_counter.most_common(20):
            frequency = count / total_tokens
            high_freq_tokens.append((token_id, count, frequency))
            
            status = ""
            if frequency > 0.3:
                status = "🚨 严重不平衡"
            elif frequency > 0.2:
                status = "⚠️ 中度不平衡"
            elif frequency > 0.1:
                status = "⚡ 轻度不平衡"
                
            print(f"  Token {token_id}: {count:,}次 ({frequency:.2%}) {status}")
        
        # 计算基尼系数
        frequencies = [count/total_tokens for count in token_counter.values()]
        frequencies.sort()
        n = len(frequencies)
        gini = (2 * sum((i+1) * freq for i, freq in enumerate(frequencies))) / (n * sum(frequencies)) - (n+1) / n
        
        print(f"\n分布不平衡指标:")
        print(f"  基尼系数: {gini:.4f} (0=完全平衡, 1=完全不平衡)")
        print(f"  Top-10 token占比: {sum(count for _, count in token_counter.most_common(10))/total_tokens:.2%}")
        print(f"  Top-5 token占比: {sum(count for _, count in token_counter.most_common(5))/total_tokens:.2%}")
        
        # 保存分析结果
        results = {
            'total_tokens': total_tokens,
            'unique_tokens': unique_tokens,
            'vocab_size': vocab_size,
            'vocab_utilization': unique_tokens / vocab_size,
            'gini_coefficient': gini,
            'high_freq_tokens': high_freq_tokens,
            'token_counter': token_counter
        }
        
        # 绘制分布图
        if save_plots:
            self.plot_token_distribution(token_counter, results)
            
        return results
    
    def analyze_data_quality(self, df):
        """分析数据质量"""
        print("\n分析数据质量...")
        
        # 基本统计
        print(f"数据基本信息:")
        print(f"  数据条数: {len(df):,}")
        print(f"  时间范围: {pd.to_datetime(df['datetime'].min(), unit='s')} - {pd.to_datetime(df['datetime'].max(), unit='s')}")
        print(f"  证券数量: {df['symbol'].nunique() if 'symbol' in df.columns else 'N/A'}")
        
        # 检查缺失值
        missing_data = df.isnull().sum()
        if missing_data.sum() > 0:
            print(f"\n缺失值检查:")
            for col, missing_count in missing_data.items():
                if missing_count > 0:
                    print(f"  {col}: {missing_count} ({missing_count/len(df):.2%})")
        else:
            print(f"\n✅ 无缺失值")
            
        # 检查重复数据
        if 'symbol' in df.columns:
            duplicates = df.duplicated(subset=['symbol', 'datetime']).sum()
            print(f"重复数据: {duplicates} ({duplicates/len(df):.2%})")
        
        # 时间连续性检查
        if 'symbol' in df.columns:
            print(f"\n时间连续性检查:")
            gap_stats = []
            for symbol in df['symbol'].unique()[:5]:  # 检查前5个证券
                symbol_df = df[df['symbol'] == symbol].sort_values('datetime')
                if len(symbol_df) > 1:
                    time_diffs = pd.to_datetime(symbol_df['datetime'], unit='s').diff()
                    if self.period == 'min1':
                        expected_diff = pd.Timedelta(minutes=1)
                    elif self.period == 'min5':
                        expected_diff = pd.Timedelta(minutes=5)
                    else:
                        expected_diff = pd.Timedelta(days=1)
                    
                    large_gaps = (time_diffs > expected_diff * 2).sum()
                    gap_stats.append((symbol, large_gaps, len(symbol_df)))
                    
            for symbol, gaps, total in gap_stats:
                print(f"  {symbol}: {gaps} 个大间隔 / {total} 总数据点")
        
        return {
            'total_records': len(df),
            'missing_data': missing_data.to_dict(),
            'duplicates': duplicates if 'symbol' in df.columns else 0,
            'time_range': (df['datetime'].min(), df['datetime'].max())
        }
    
    def plot_token_distribution(self, token_counter, results):
        """绘制token分布图"""
        print("绘制token分布图...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Token分布分析 - {self.market}_{self.block_name}_{self.period}', fontsize=16)
        
        # 1. Top 50 token分布
        top_tokens = token_counter.most_common(50)
        tokens, counts = zip(*top_tokens)
        
        ax1 = axes[0, 0]
        bars = ax1.bar(range(len(tokens)), counts, color='skyblue')
        ax1.set_xlabel('Token排名')
        ax1.set_ylabel('出现次数')
        ax1.set_title('Top 50 Token分布')
        ax1.set_xticks(range(0, len(tokens), 5))
        ax1.set_xticklabels([f'T{tokens[i]}' for i in range(0, len(tokens), 5)], rotation=45)
        
        # 标记高频token
        total_tokens = sum(token_counter.values())
        for i, (token_id, count) in enumerate(top_tokens[:10]):
            percentage = count / total_tokens
            if percentage > 0.1:
                bars[i].set_color('red')
                ax1.text(i, count, f'{percentage:.1%}', ha='center', va='bottom', fontsize=8)
        
        # 2. 频率分布直方图
        ax2 = axes[0, 1]
        frequencies = [count/total_tokens for count in token_counter.values()]
        ax2.hist(frequencies, bins=50, color='lightgreen', alpha=0.7)
        ax2.set_xlabel('Token频率')
        ax2.set_ylabel('Token数量')
        ax2.set_title('Token频率分布直方图')
        ax2.axvline(x=0.1, color='orange', linestyle='--', label='10%阈值')
        ax2.axvline(x=0.2, color='red', linestyle='--', label='20%阈值')
        ax2.legend()
        
        # 3. 累积分布
        ax3 = axes[1, 0]
        sorted_counts = sorted(token_counter.values(), reverse=True)
        cumulative_pct = np.cumsum(sorted_counts) / total_tokens * 100
        ax3.plot(range(len(sorted_counts)), cumulative_pct, color='purple')
        ax3.set_xlabel('Token排名')
        ax3.set_ylabel('累积占比 (%)')
        ax3.set_title('Token累积分布')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=80, color='red', linestyle='--', label='80%线')
        ax3.legend()
        
        # 4. 统计信息
        ax4 = axes[1, 1]
        ax4.axis('off')
        stats_text = f"""
统计信息:
总token数: {results['total_tokens']:,}
唯一token数: {results['unique_tokens']:,}
词汇表大小: {results['vocab_size']:,}
词汇表利用率: {results['vocab_utilization']:.2%}
基尼系数: {results['gini_coefficient']:.4f}

Top-5占比: {sum(count for _, count in token_counter.most_common(5))/total_tokens:.2%}
Top-10占比: {sum(count for _, count in token_counter.most_common(10))/total_tokens:.2%}

建议:
{'🚨 严重不平衡，需要平衡处理' if results['gini_coefficient'] > 0.7 else '⚠️ 中度不平衡，建议平衡处理' if results['gini_coefficient'] > 0.5 else '✅ 分布相对平衡'}
        """
        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=12, 
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # 保存图表
        save_path = f'token_distribution_{self.market}_{self.block_name}_{self.period}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存: {save_path}")
        plt.show()
    
    def generate_recommendations(self, token_results, quality_results):
        """生成优化建议"""
        print("\n" + "="*60)
        print("数据优化建议")
        print("="*60)
        
        recommendations = []
        
        # Token分布建议
        if token_results:
            gini = token_results['gini_coefficient']
            vocab_util = token_results['vocab_utilization']
            
            if gini > 0.7:
                recommendations.append("🚨 Token分布严重不平衡:")
                recommendations.append("  1. 启用自适应平衡处理 (--balance_method adaptive)")
                recommendations.append("  2. 设置最大token频率为10% (--max_token_frequency 0.1)")
                recommendations.append("  3. 使用Focal Loss训练")
            elif gini > 0.5:
                recommendations.append("⚠️ Token分布中度不平衡:")
                recommendations.append("  1. 启用温和平衡处理 (--balance_method undersample)")
                recommendations.append("  2. 设置最大token频率为15% (--max_token_frequency 0.15)")
            
            if vocab_util < 0.3:
                recommendations.append(f"📊 词汇表利用率过低 ({vocab_util:.1%}):")
                recommendations.append("  1. 考虑减小词汇表大小")
                recommendations.append("  2. 检查tokenizer训练是否充分")
        
        # 数据质量建议
        if self.period in ['min1', 'min5']:
            recommendations.append("⚡ 短周期数据优化:")
            recommendations.append("  1. 启用数据质量检查 (--enable_quality_check True)")
            recommendations.append("  2. 设置跳空阈值 (--max_gap_threshold 5.0)")
            recommendations.append("  3. 设置最小成交量阈值 (--min_volume_threshold 100)")
        
        # 打印建议
        for rec in recommendations:
            print(rec)
        
        # 生成命令行示例
        print(f"\n推荐的命令行参数:")
        cmd = f"python hdl_bar2.py \\\n"
        cmd += f"  --market {self.market} \\\n"
        cmd += f"  --period {self.period} \\\n"
        cmd += f"  --block_name {self.block_name} \\\n"
        cmd += f"  --enable_quality_check True \\\n"
        cmd += f"  --enable_balance True \\\n"
        
        if token_results and token_results['gini_coefficient'] > 0.7:
            cmd += f"  --balance_method adaptive \\\n"
            cmd += f"  --max_token_frequency 0.1 \\\n"
        elif token_results and token_results['gini_coefficient'] > 0.5:
            cmd += f"  --balance_method undersample \\\n"
            cmd += f"  --max_token_frequency 0.15 \\\n"
        
        if self.period in ['min1', 'min5']:
            cmd += f"  --max_gap_threshold 5.0 \\\n"
            cmd += f"  --min_volume_threshold 100"
        
        print(cmd)
        print("="*60)

def main():
    parser = ArgumentParser(description='数据质量分析工具')
    parser.add_argument('--data_path', type=str, required=True, help='数据路径')
    parser.add_argument('--market', type=str, default='fut', choices=['fut', 'stk'], help='市场类型')
    parser.add_argument('--period', type=str, default='min1', choices=['day', 'min5', 'min1'], help='周期')
    parser.add_argument('--block_name', type=str, default='top', help='数据块名称')
    parser.add_argument('--year', type=int, default=None, help='指定年份(可选)')
    parser.add_argument('--save_plots', type=bool, default=True, help='保存图表')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = DataQualityAnalyzer(args.data_path, args.market, args.period, args.block_name)
    
    try:
        # 加载数据
        df = analyzer.load_data(args.year)
        
        # 分析数据质量
        quality_results = analyzer.analyze_data_quality(df)
        
        # 分析token分布
        token_results = analyzer.analyze_token_distribution(df, args.save_plots)
        
        # 生成优化建议
        analyzer.generate_recommendations(token_results, quality_results)
        
    except Exception as e:
        print(f"分析失败: {e}")
        print("请检查数据路径和文件是否存在")

if __name__ == '__main__':
    main()
