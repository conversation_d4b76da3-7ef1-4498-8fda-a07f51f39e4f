import logging
from pprint import pprint
import random
import numpy as np
import gymnasium as gym
from gymnasium.spaces import Box, Discrete, Tuple

# from pyqlab.rl.data.tickdata_fut import PreprocessingPipeline
from pyqlab.rl.data.markdata_fut import MarkDataDataset

logger = logging.getLogger(__name__)

"""
- state的含义、设置,如account,price,technical indicator,
  具体看代码 https://github.com/AI4Finance-Foundation/FinRL/blob/master/finrl/finrl_meta/env_stock_trading/env_stocktrading.py
- action的含义,买入卖出,以及小额交易忽略
- reward function的设置(直接影响 能否训练出更加谨慎,回撤更小的智能体)交易滑点,
- turbulence达到阈值就强制卖出,等
- env reset 里的随机性对于强化学习训练的益处

金融强化学习算法与其他自动交易算法的区别如下：
1. 强化学习算法中的无模型算法 不需要对环境进行建模（也就是它不预测市场）。
   这与部分深度学习的交易算法对市场进行一定程度的预测不同。市场的预测一直是一个难题,而深度强化学习
   有机会在不对市场进行预测的情况下,直接学习交易策略。
2. 数据导向,增量学习。
   深度强化学习算法和其他深度学习算法一样,都是数据导向的。这与一些基于基本逻辑语句写就的交易算法明显不同。
   市场每时每刻都在产出大量数据,依靠人类经验总结出来的交易策略大家都在使用。而深度学习可以使用这些数据,
   提高交易策略的自动化程度。

"""

class MarketTimingEnv(gym.Env):
    """
    def __init__(self, 
        initial_amount=1e7,  # 初始本金
        max_stock=1e2,  # 最大交易额度,买入或卖出100个单位
        buy_cost_pct=1e-3,  # 交易损耗率设为 0.001
        sell_cost_pct=1e-3,  # 交易损耗率设为 0.001
        gamma=0.99,  # 强化学习的折扣比率,给人为设置的终止状态的reward进行补偿的时候会用到
        beg_idx=0,   # 使用数据集的这个位置之后的数据
        end_idx=1113  # 使用数据集的这个位置之前的数据
     ):

    """
    def __init__(self, env_config):
        self.md = MarkDataDataset(
            data_path=f"{env_config['data_path']}/{env_config['data_file']}",
            version=env_config["version"]
        )
        self.md.load()
        _, _, mk_data = self.md.getitem(0)

        self.initial_amount = env_config["initial_amount"]
        # self.gamma = env_config["gamma"]

        # Environment information
        self.action_space = Discrete(3)
        self.state_dim = len(mk_data)
        self.observation_space = Box(low=-10, high=10, shape=(self.state_dim,), dtype=np.float32)
        self.max_step = self.md.len()


        # 添加交易限制参数
        self.max_position = env_config.get("max_position", 1)  # 最大持仓倍数
        self.min_trade_interval = env_config.get("min_trade_interval", 5)  # 最小交易间隔
        self.transaction_cost = env_config.get("transaction_cost", 0.0001)  # 交易成本
        
        # 添加风控参数
        self.max_drawdown = env_config.get("max_drawdown", 0.5)  # 最大回撤限制
        self.stop_loss = env_config.get("stop_loss", 0.3)  # 止损线
        
        # 记录交易状态
        self.last_trade_step = 0
        self.current_position = 0  # 当前持仓状态: 0-空仓, 1-多头, -1-空头
        self.position_history = []
        self.nav_history = []  # 净值历史

        # 添加数据集划分参数
        self.train_test_split = env_config.get("train_test_split", 0.8)
        self.validation_split = env_config.get("validation_split", 0.1)
        self.mode = env_config.get("mode", "train")  # train/validation/test
        
        # 划分数据集
        self._split_datasets()
        
        # 添加随机采样参数
        self.sample_length = env_config.get("sample_length", 1000)  # 每个episode的样本长度
        self.random_episode = env_config.get("random_episode", True)  # 是否随机采样起点

        # Initialize variables
        self.reset()

    def _split_datasets(self):
        total_len = self.md.len()
        train_end = int(total_len * self.train_test_split)
        val_end = int(total_len * (self.train_test_split + self.validation_split))
        
        self.train_indices = list(range(0, train_end))
        self.val_indices = list(range(train_end, val_end))
        self.test_indices = list(range(val_end, total_len))
        
        if self.mode == "train":
            self.available_indices = self.train_indices
        elif self.mode == "validation":
            self.available_indices = self.val_indices
        else:  # test
            self.available_indices = self.test_indices
        
    def reset(self, *, seed=None, options=None):
        super().reset(seed=seed)
        
        # 随机选择起始点
        if self.random_episode:
            start_idx = random.choice(self.available_indices[:-self.sample_length])
            self.episode_indices = list(range(start_idx, start_idx + self.sample_length))
        else:
            self.episode_indices = self.available_indices
        
        # 重置所有状态变量
        self.cur_steps = 0
        self.amount = self.initial_amount
        # self.rewards = []
        self.current_position = 0
        self.last_trade_step = 0
        self.position_history = []
        self.nav_history = [1.0]
        
        # 获取初始观察
        _, _, mk_data = self.md.getitem(0)
        new_state = self.get_state(mk_data)
        
        return new_state, self._get_info()

    def get_state(self, data):
        state = np.array(data, dtype=np.float32)
        
        # 训练时添加噪声
        if self.mode == "train":
            noise = np.random.normal(0, 0.01, state.shape)
            state = state + noise
        
        return state

    def step(self, action):
        self.cur_steps += 1
        
        # 获取当前市场数据
        pos_side, pnl, mk_data = self.md.getitem(self.cur_steps)
        
        # 检查交易间隔限制
        # if self.cur_steps - self.last_trade_step < self.min_trade_interval:
        #     action = 0  # 强制不交易
            
        # 计算新的持仓状态
        new_position = 0
        if action == 1:  # 做多
            new_position = 1
        elif action == 2:  # 做空
            new_position = -1
            
        # 计算交易成本
        trading_cost = 0
        if new_position != self.current_position:
            trading_cost = abs(new_position - self.current_position) * self.transaction_cost
            # print(f"交易成本: {trading_cost:.6f}")
            # self.last_trade_step = self.cur_steps
            
        # 更新资金
        if (action == 1 and pos_side == "L") or (action == 2 and pos_side == "S"):
            self.amount += pnl - trading_cost * self.amount
            # print(f"资金: {self.amount:.0f}, 收益: {pnl:.0f}, 交易成本: {trading_cost * self.amount:.3f}")
            
        # 计算回报和奖励
        returns = (self.amount - self.initial_amount) / self.initial_amount
        
        # 多因子奖励函数
        reward = self._calculate_reward(returns, action, trading_cost)
        # self.rewards.append(reward)
        
        # 更新状态
        state = self.get_state(mk_data)
        self.current_position = new_position
        self.position_history.append(new_position)
        self.nav_history.append(self.amount / self.initial_amount)
        
        # 检查是否触发止损或最大回撤
        done = self._check_risk_limits()
        if not done:
            done = self.cur_steps == self.max_step - 1
            
        if done:
            # self._handle_episode_end(reward)
            pprint(self._get_info())
        elif self.cur_steps % 200 == 0:
            self._log_progress(reward)

        return state, reward, done, False, self._get_info()
        
    def _calculate_reward(self, returns, action, trading_cost):
        # 使用Huber损失来平滑极端收益
        def huber_loss(x, delta=1.0):
            abs_x = abs(x)
            if abs_x <= delta:
                return 0.5 * x * x
            else:
                return delta * abs_x - 0.5 * delta * delta
            
        # 基础收益奖励使用Huber损失
        reward = huber_loss(returns) * 100
        
        # 添加持仓平衡惩罚
        position_penalty = abs(sum(self.position_history[-20:]) / 20)  # 最近20步的平均持仓
        reward -= position_penalty * 0.1
        
        # 交易成本惩罚
        reward -= trading_cost * 200
        
        # 频繁交易惩罚
        if action != 0 and self.cur_steps - self.last_trade_step < self.min_trade_interval:
            reward -= 0.1
            
        # 持仓时间奖励
        if self.current_position != 0:
            holding_time = self.cur_steps - self.last_trade_step
            reward += np.log1p(holding_time) * 0.01
            
        return reward
        
    def _check_risk_limits(self):
        if len(self.nav_history) < 2:
            return False
            
        # 计算当前回撤
        peak = max(self.nav_history)
        current_drawdown = (peak - self.nav_history[-1]) / peak
        
        # 检查止损
        if self.nav_history[-1] < (1 - self.stop_loss):
            print(f"触发止损: NAV={self.nav_history[-1]:.4f}")
            return True
            
        # 检查最大回撤
        if current_drawdown > self.max_drawdown:
            print(f"触发最大回撤: DD={current_drawdown:.4f}")
            return True
            
        return False
        
    def _get_info(self):
        return {
            "total_asset": self.amount,
            "returns": self.amount / self.initial_amount - 1,
            "position": self.current_position,
            "nav": self.nav_history[-1] if self.nav_history else 1.0
        }
        
    def _handle_episode_end(self, reward):
        mean_reward = np.mean(self.rewards)
        final_reward = reward + 1 / (1 - self.gamma) * mean_reward
        self.returns = self.amount / self.initial_amount - 1
        # print(f"=== reward: {reward:.3f}, returns: {self.returns:.3f} ===")

    def _log_progress(self, reward):
        print(f"=== Step: {self.cur_steps}/{self.max_step}, reward: {reward:.3f}, total_asset: {self.amount:.0f} ===")



