"""
BarTokenizer 演示脚本

展示 BarTokenizer 的主要功能，包括：
1. 基本使用方法
2. 不同映射策略的比较
3. 分布平衡性分析
4. 多周期tokenizer使用
5. 模型保存和加载
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from bar_tokenizer import BarTokenizer, MultiPeriodBarTokenizer


def create_sample_data(n_samples=2000, freq='5min'):
    """创建示例OHLCV数据"""
    np.random.seed(42)
    
    dates = pd.date_range('2024-01-01', periods=n_samples, freq=freq)
    base_price = 100
    
    # 创建带趋势的价格序列
    trend = np.linspace(0, 20, n_samples)  # 上升趋势
    noise = np.cumsum(np.random.randn(n_samples) * 0.3)  # 随机游走
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': base_price + trend + noise,
        'high': 0.0,
        'low': 0.0,
        'close': 0.0,
        'volume': np.random.lognormal(8, 0.5, n_samples).astype(int)
    })
    
    # 生成合理的OHLC数据
    for i in range(n_samples):
        open_price = df.loc[i, 'open']
        
        # 模拟日内价格变化
        intraday_change = np.random.randn() * 0.8
        close_price = open_price + intraday_change
        
        # 计算高低价
        high_extra = abs(np.random.randn() * 0.4)
        low_extra = abs(np.random.randn() * 0.4)
        
        high_price = max(open_price, close_price) + high_extra
        low_price = min(open_price, close_price) - low_extra
        
        df.loc[i, 'close'] = close_price
        df.loc[i, 'high'] = high_price
        df.loc[i, 'low'] = low_price
    
    return df


def demo_basic_usage():
    """演示基本使用方法"""
    print("=== BarTokenizer 基本使用演示 ===\n")
    
    # 创建示例数据
    df = create_sample_data(1000)
    print(f"创建了 {len(df)} 条K线数据")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    print(f"成交量范围: {df['volume'].min()} - {df['volume'].max()}\n")
    
    # 创建tokenizer
    tokenizer = BarTokenizer(
        mapping_strategy='adaptive',
        balancing_strategy='frequency',
        n_bins=100,
        features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
    )
    
    # 拟合和转换
    print("正在进行token化...")
    tokens = tokenizer.fit_transform(df)
    
    print(f"生成了 {len(tokens)} 个tokens")
    print(f"词汇表大小: {tokenizer.get_vocab_size()}")
    print(f"唯一token数: {len(np.unique(tokens))}")
    
    # 分析分布
    balance_info = tokenizer.analyze_balance(tokens)
    print(f"\n分布平衡性分析:")
    print(f"  基尼系数: {balance_info['gini_coefficient']:.4f}")
    print(f"  标准化熵: {balance_info['normalized_entropy']:.4f}")
    print(f"  变异系数: {balance_info['coefficient_of_variation']:.4f}")
    print(f"  频率范围: {balance_info['frequency_range']:.4f}")
    
    # 测试逆变换
    sample_tokens = tokens[:5]
    reconstructed = tokenizer.inverse_transform(sample_tokens)
    print(f"\n逆变换测试 (前5个tokens):")
    for feature, values in reconstructed.items():
        print(f"  {feature}: {values}")
    
    return tokenizer, tokens


def demo_mapping_strategies():
    """演示不同映射策略的比较"""
    print("\n=== 映射策略比较演示 ===\n")
    
    df = create_sample_data(1500)
    strategies = ['linear', 'quantile', 'adaptive']
    results = {}
    
    for strategy in strategies:
        print(f"测试 {strategy} 映射策略...")
        
        tokenizer = BarTokenizer(
            mapping_strategy=strategy,
            n_bins=80,
            features=['change', 'body', 'upper_shadow', 'lower_shadow']
        )
        
        tokens = tokenizer.fit_transform(df)
        balance_metrics = tokenizer.analyze_balance(tokens)
        
        results[strategy] = {
            'gini': balance_metrics['gini_coefficient'],
            'entropy': balance_metrics['normalized_entropy'],
            'cv': balance_metrics['coefficient_of_variation'],
            'unique_tokens': len(np.unique(tokens))
        }
        
        print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
        print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
        print(f"  唯一tokens: {len(np.unique(tokens))}")
        print()
    
    # 比较结果
    print("策略比较总结:")
    print("策略\t\t基尼系数\t标准化熵\t变异系数\t唯一tokens")
    print("-" * 60)
    for strategy, metrics in results.items():
        print(f"{strategy:10s}\t{metrics['gini']:.4f}\t\t{metrics['entropy']:.4f}\t\t{metrics['cv']:.4f}\t\t{metrics['unique_tokens']}")
    
    return results


def demo_multi_period():
    """演示多周期tokenizer"""
    print("\n=== 多周期Tokenizer演示 ===\n")
    
    # 创建不同周期的数据
    base_data = create_sample_data(1200, '5min')
    
    data_dict = {
        '5min': base_data,
        '15min': base_data[::3].reset_index(drop=True),  # 15分钟数据
        '1h': base_data[::12].reset_index(drop=True),    # 1小时数据
        '4h': base_data[::48].reset_index(drop=True)     # 4小时数据
    }
    
    print("多周期数据准备:")
    for period, data in data_dict.items():
        print(f"  {period}: {len(data)} 条记录")
    
    # 创建多周期tokenizer
    multi_tokenizer = MultiPeriodBarTokenizer(
        periods=['5min', '15min', '1h'],
        base_tokenizer_config={
            'mapping_strategy': 'adaptive',
            'n_bins': 50,
            'features': ['change', 'body', 'volume_ratio']
        },
        combination_method='concatenate'
    )
    
    print("\n正在拟合多周期tokenizer...")
    multi_tokenizer.fit(data_dict)
    
    print("正在转换多周期数据...")
    multi_tokens = multi_tokenizer.transform(data_dict)
    
    print(f"多周期tokens形状: {multi_tokens.shape}")
    print(f"多周期词汇表大小: {multi_tokenizer.vocab_size}")
    
    return multi_tokenizer, multi_tokens


def demo_model_persistence():
    """演示模型保存和加载"""
    print("\n=== 模型持久化演示 ===\n")
    
    df = create_sample_data(800)
    
    # 创建并训练tokenizer
    original_tokenizer = BarTokenizer(
        mapping_strategy='quantile',
        n_bins=60,
        features=['change', 'body', 'upper_shadow']
    )
    
    original_tokens = original_tokenizer.fit_transform(df)
    print("原始tokenizer训练完成")
    print(f"生成tokens数量: {len(original_tokens)}")
    
    # 保存模型
    model_path = 'demo_tokenizer.pkl'
    original_tokenizer.save_model(model_path)
    print(f"模型已保存到: {model_path}")
    
    # 加载模型
    loaded_tokenizer = BarTokenizer.load_model(model_path)
    print("模型加载完成")
    
    # 验证加载的模型
    loaded_tokens = loaded_tokenizer.transform(df)
    
    # 检查一致性
    tokens_match = np.array_equal(original_tokens, loaded_tokens)
    print(f"tokens一致性检查: {'通过' if tokens_match else '失败'}")
    
    if tokens_match:
        print("模型保存和加载功能正常！")
    
    # 清理临时文件
    import os
    if os.path.exists(model_path):
        os.remove(model_path)
        print("临时文件已清理")
    
    return loaded_tokenizer


def plot_token_distribution(tokens, title="Token分布"):
    """绘制token分布图"""
    try:
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        frequencies = counts / len(tokens)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 频率分布直方图
        ax1.hist(frequencies, bins=30, alpha=0.7, edgecolor='black')
        ax1.set_xlabel('Token频率')
        ax1.set_ylabel('数量')
        ax1.set_title(f'{title} - 频率分布')
        ax1.grid(True, alpha=0.3)
        
        # 累积分布
        sorted_freq = np.sort(frequencies)[::-1]
        cumulative = np.cumsum(sorted_freq)
        ax2.plot(range(len(cumulative)), cumulative)
        ax2.set_xlabel('Token排名')
        ax2.set_ylabel('累积频率')
        ax2.set_title(f'{title} - 累积分布')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("Matplotlib不可用，跳过绘图")


def main():
    """主演示函数"""
    print("BarTokenizer 功能演示")
    print("=" * 50)
    
    # 基本使用演示
    tokenizer, tokens = demo_basic_usage()
    
    # 映射策略比较
    strategy_results = demo_mapping_strategies()
    
    # 多周期演示
    multi_tokenizer, multi_tokens = demo_multi_period()
    
    # 模型持久化演示
    loaded_tokenizer = demo_model_persistence()
    
    print("\n=== 演示完成 ===")
    print("BarTokenizer 提供了以下主要功能:")
    print("1. ✅ ATR标准化的K线特征提取")
    print("2. ✅ 多种映射策略 (linear, quantile, adaptive)")
    print("3. ✅ 分布平衡性分析和优化")
    print("4. ✅ 多周期数据处理")
    print("5. ✅ 模型保存和加载")
    print("6. ✅ 完整的逆变换支持")
    
    # 可选：绘制分布图
    try:
        plot_token_distribution(tokens, "基本Tokenizer")
    except:
        print("绘图功能不可用")


if __name__ == "__main__":
    main()
