# QuantTimeRAG: 基于TimeRAG的量化交易多Agent系统
# 作者：AIChemist
# 日期：2025-03-23

import os
import json
import pandas as pd
import numpy as np
import requests
from typing import List, Dict, Any, Tuple, Optional, Union
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from PIL import Image
import io
import base64
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from tslearn.metrics import dtw
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging
from metagpt.roles import Role
from metagpt.actions import Action
from metagpt.memory import Memory
from metagpt.tools import SearchEngineType, SearchEngine
from metagpt.logs import logger
from metagpt.tools import WebBrowserEngine
from metagpt.utils.common import extract_urls

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("QuantTimeRAG")

# 配置环境变量
os.environ["QWEN_API_KEY"] = "your_qwen_api_key_here"
os.environ["QWEN_API_BASE"] = "http://localhost:8000/v1"

# TimeRAG相关实现
class TimeSeriesEntry:
    """时间序列知识库条目"""
    def __init__(self, 
                 ts_id: str,
                 ts_data: np.ndarray,
                 ts_metadata: Dict[str, Any] = None,
                 ts_features: np.ndarray = None,
                 source_type: str = "numeric",
                 related_text: str = None,
                 related_image_url: str = None):
        """
        初始化时间序列条目

        Args:
            ts_id: 时间序列唯一标识符
            ts_data: 时间序列数据，形状为(n_timestamps, n_features)
            ts_metadata: 时间序列元数据
            ts_features: 预计算的时间序列特征
            source_type: 数据源类型，例如"numeric", "text", "image"
            related_text: 与时间序列相关的文本
            related_image_url: 与时间序列相关的图像URL
        """
        self.ts_id = ts_id
        self.ts_data = ts_data
        self.ts_metadata = ts_metadata or {}
        self.ts_features = ts_features
        self.source_type = source_type
        self.related_text = related_text
        self.related_image_url = related_image_url
        self.creation_timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """将条目转换为字典表示"""
        return {
            "ts_id": self.ts_id,
            "ts_data": self.ts_data.tolist() if isinstance(self.ts_data, np.ndarray) else self.ts_data,
            "ts_metadata": self.ts_metadata,
            "ts_features": self.ts_features.tolist() if isinstance(self.ts_features, np.ndarray) else self.ts_features,
            "source_type": self.source_type,
            "related_text": self.related_text,
            "related_image_url": self.related_image_url,
            "creation_timestamp": self.creation_timestamp.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TimeSeriesEntry':
        """从字典创建条目"""
        instance = cls(
            ts_id=data["ts_id"],
            ts_data=np.array(data["ts_data"]) if isinstance(data["ts_data"], list) else data["ts_data"],
            ts_metadata=data.get("ts_metadata", {}),
            ts_features=np.array(data["ts_features"]) if data.get("ts_features") and isinstance(data["ts_features"], list) else data.get("ts_features"),
            source_type=data.get("source_type", "numeric"),
            related_text=data.get("related_text"),
            related_image_url=data.get("related_image_url")
        )
        if "creation_timestamp" in data:
            instance.creation_timestamp = datetime.fromisoformat(data["creation_timestamp"])
        return instance


class TimeSeriesKnowledgeBase:
    """时间序列知识库"""
    def __init__(self, 
                 db_path: str = "timeseries_kb.json",
                 max_entries: int = 10000,
                 feature_extractor = None):
        """
        初始化时间序列知识库

        Args:
            db_path: 知识库存储路径
            max_entries: 最大条目数
            feature_extractor: 特征提取器
        """
        self.db_path = db_path
        self.max_entries = max_entries
        self.feature_extractor = feature_extractor
        self.entries: Dict[str, TimeSeriesEntry] = {}
        self._load_db()

    def _load_db(self) -> None:
        """从磁盘加载知识库"""
        try:
            if os.path.exists(self.db_path):
                with open(self.db_path, 'r') as f:
                    data = json.load(f)
                    for entry_data in data:
                        entry = TimeSeriesEntry.from_dict(entry_data)
                        self.entries[entry.ts_id] = entry
                logger.info(f"Loaded {len(self.entries)} entries from knowledge base.")
            else:
                logger.info("Knowledge base file not found. Creating new database.")
        except Exception as e:
            logger.error(f"Error loading knowledge base: {str(e)}")

    def _save_db(self) -> None:
        """保存知识库到磁盘"""
        try:
            entries_data = [entry.to_dict() for entry in self.entries.values()]
            with open(self.db_path, 'w') as f:
                json.dump(entries_data, f)
            logger.info(f"Saved {len(self.entries)} entries to knowledge base.")
        except Exception as e:
            logger.error(f"Error saving knowledge base: {str(e)}")

    def add_entry(self, entry: TimeSeriesEntry) -> bool:
        """
        添加时间序列条目到知识库

        Args:
            entry: 要添加的时间序列条目

        Returns:
            bool: 是否成功添加
        """
        if len(self.entries) >= self.max_entries:
            # 如果达到最大条目数，移除最旧的条目
            oldest_entry_id = min(self.entries.values(), key=lambda x: x.creation_timestamp).ts_id
            del self.entries[oldest_entry_id]

        # 如果提供了特征提取器并且条目没有预计算特征，则提取特征
        if self.feature_extractor and entry.ts_features is None:
            try:
                entry.ts_features = self.feature_extractor.extract_features(entry.ts_data)
            except Exception as e:
                logger.error(f"Error extracting features: {str(e)}")

        self.entries[entry.ts_id] = entry
        self._save_db()
        return True

    def retrieve_by_id(self, ts_id: str) -> Optional[TimeSeriesEntry]:
        """通过ID检索时间序列条目"""
        return self.entries.get(ts_id)

    def retrieve_similar(self, 
                        query_ts: np.ndarray, 
                        top_k: int = 5, 
                        metadata_filters: Dict[str, Any] = None,
                        source_type_filter: str = None) -> List[Tuple[TimeSeriesEntry, float]]:
        """
        检索与查询时间序列最相似的条目

        Args:
            query_ts: 查询时间序列
            top_k: 返回的最相似条目数量
            metadata_filters: 元数据过滤条件
            source_type_filter: 数据源类型过滤

        Returns:
            List[Tuple[TimeSeriesEntry, float]]: 相似条目和相似度分数列表
        """
        candidates = []

        # 应用过滤器
        filtered_entries = self.entries.values()
        if metadata_filters:
            filtered_entries = [e for e in filtered_entries if all(
                e.ts_metadata.get(k) == v for k, v in metadata_filters.items()
            )]

        if source_type_filter:
            filtered_entries = [e for e in filtered_entries if e.source_type == source_type_filter]

        # 计算DTW相似度
        for entry in filtered_entries:
            try:
                if query_ts.shape[1] != entry.ts_data.shape[1]:
                    # 特征维度不匹配，跳过
                    continue

                # 使用DTW计算相似度
                distance = dtw(query_ts, entry.ts_data)
                # 将距离转换为相似度分数（距离越小，相似度越高）
                similarity = 1.0 / (1.0 + distance)
                candidates.append((entry, similarity))
            except Exception as e:
                logger.warning(f"Error calculating similarity for entry {entry.ts_id}: {str(e)}")

        # 按相似度降序排序并返回top_k个结果
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:top_k]

    def clear(self) -> None:
        """清空知识库"""
        self.entries.clear()
        self._save_db()


class TimeRAG:
    """TimeRAG主类：时间序列检索增强生成系统"""
    def __init__(self, 
                 knowledge_base: TimeSeriesKnowledgeBase,
                 llm_client: 'QwenClient',
                 prompt_template: str = None):
        """
        初始化TimeRAG系统

        Args:
            knowledge_base: 时间序列知识库
            llm_client: LLM客户端
            prompt_template: 提示模板
        """
        self.knowledge_base = knowledge_base
        self.llm_client = llm_client
        self.prompt_template = prompt_template or self._default_prompt_template()

    def _default_prompt_template(self) -> str:
        """默认提示模板"""
        return """
        我需要预测时间序列的未来趋势。

        查询序列:
        {query_sequence}

        以下是与查询序列相似的历史序列:
        {reference_sequences}

        相关上下文信息:
        {context_info}

        根据提供的查询序列和参考序列，预测未来{forecast_horizon}个时间步的趋势。
        给出详细的分析和推理过程，并解释为什么这些参考序列与当前查询相关。
        """

    def format_time_series(self, ts_data: np.ndarray, timestamps=None) -> str:
        """格式化时间序列用于提示"""
        if timestamps is None:
            timestamps = [f"t{i}" for i in range(len(ts_data))]

        formatted = []
        for i, values in enumerate(ts_data):
            if len(values) == 1:
                formatted.append(f"{timestamps[i]}: {values[0]:.4f}")
            else:
                formatted_values = ", ".join([f"{v:.4f}" for v in values])
                formatted.append(f"{timestamps[i]}: [{formatted_values}]")

        return "\n".join(formatted)

    def format_reference_sequence(self, entry: TimeSeriesEntry, similarity: float) -> str:
        """格式化参考序列用于提示"""
        ts_str = self.format_time_series(entry.ts_data)
        metadata_str = ", ".join([f"{k}: {v}" for k, v in entry.ts_metadata.items()])

        result = f"""
        序列ID: {entry.ts_id}
        相似度: {similarity:.4f}
        元数据: {metadata_str}
        数据:
        {ts_str}
        """

        if entry.related_text:
            result += f"\n相关文本:\n{entry.related_text[:500]}..."

        if entry.related_image_url:
            result += f"\n相关图像: {entry.related_image_url}"

        return result

    async def generate_forecast(self, 
                         query_ts: np.ndarray,
                         forecast_horizon: int = 10,
                         metadata_filters: Dict[str, Any] = None,
                         top_k: int = 5,
                         additional_context: str = None,
                         include_images: bool = False) -> Dict[str, Any]:
        """
        生成时间序列预测

        Args:
            query_ts: 查询时间序列
            forecast_horizon: 预测时长
            metadata_filters: 元数据过滤条件
            top_k: 使用的相似序列数量
            additional_context: 额外上下文信息
            include_images: 是否在提示中包含图像引用

        Returns:
            Dict: 预测结果
        """
        # 检索相似序列
        similar_sequences = self.knowledge_base.retrieve_similar(
            query_ts=query_ts,
            top_k=top_k,
            metadata_filters=metadata_filters
        )

        if not similar_sequences:
            return {
                "status": "error",
                "message": "没有找到相似的参考序列"
            }

        # 格式化查询序列
        query_sequence_str = self.format_time_series(query_ts)

        # 格式化参考序列
        reference_sequences_str = "\n\n".join([
            self.format_reference_sequence(entry, similarity)
            for entry, similarity in similar_sequences
        ])

        # 准备上下文信息
        context_info = additional_context or "没有其他上下文信息。"

        # 准备提示
        prompt = self.prompt_template.format(
            query_sequence=query_sequence_str,
            reference_sequences=reference_sequences_str,
            context_info=context_info,
            forecast_horizon=forecast_horizon
        )

        # 收集相关图像URL（如果需要）
        image_urls = []
        if include_images:
            for entry, _ in similar_sequences:
                if entry.related_image_url:
                    image_urls.append(entry.related_image_url)

        # 调用LLM生成预测
        response = await self.llm_client.generate_with_images(prompt, image_urls)

        # 解析和处理LLM响应
        # 在实际应用中，可能需要更复杂的解析逻辑
        forecast_result = {
            "status": "success",
            "forecast_text": response,
            "reference_sequences": [entry.ts_id for entry, _ in similar_sequences],
            "similarities": [float(similarity) for _, similarity in similar_sequences],
            "timestamp": datetime.now().isoformat()
        }

        return forecast_result


# 多模态处理客户端
class QwenClient:
    """Qwen模型客户端，实现与Qwen2.5-VL-72b-instruct的交互"""
    def __init__(self, 
                 api_key: str = None, 
                 api_base: str = None,
                 model: str = "qwen2.5-vl-72b-instruct"):
        """
        初始化Qwen客户端

        Args:
            api_key: API密钥
            api_base: API基础URL
            model: 模型名称
        """
        self.api_key = api_key or os.environ.get("QWEN_API_KEY")
        self.api_base = api_base or os.environ.get("QWEN_API_BASE", "http://localhost:8000/v1")
        self.model = model

        if not self.api_key:
            logger.warning("No Qwen API key provided. Using mock responses.")

    async def generate(self, prompt: str) -> str:
        """生成文本响应"""
        if not self.api_key:
            # 模拟响应用于测试
            return f"Mock response for: {prompt[:50]}..."

        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }

                payload = {
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 2048
                }

                async with session.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error from Qwen API: {error_text}")
                        return f"Error: Failed to generate response. Status code: {response.status}"

                    data = await response.json()
                    return data["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return f"Error: {str(e)}"

    async def generate_with_images(self, prompt: str, image_urls: List[str] = None) -> str:
        """生成包含图像的响应"""
        if not image_urls:
            return await self.generate(prompt)

        if not self.api_key:
            # 模拟响应用于测试
            return f"Mock image+text response for: {prompt[:50]}... with {len(image_urls)} images"

        try:
            images_content = []
            for img_url in image_urls[:5]:  # 限制图像数量
                if img_url.startswith("http"):
                    # 远程图像，需要下载
                    async with aiohttp.ClientSession() as session:
                        async with session.get(img_url) as response:
                            if response.status == 200:
                                img_data = await response.read()
                                encoded_img = base64.b64encode(img_data).decode('utf-8')
                                images_content.append({
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{encoded_img}"
                                    }
                                })
                elif os.path.exists(img_url):
                    # 本地图像
                    with open(img_url, "rb") as img_file:
                        encoded_img = base64.b64encode(img_file.read()).decode('utf-8')
                        images_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{encoded_img}"
                            }
                        })

            # 构建多模态消息
            content = [{"type": "text", "text": prompt}]
            content.extend(images_content)

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }

                payload = {
                    "model": self.model,
                    "messages": [{"role": "user", "content": content}],
                    "max_tokens": 2048
                }

                async with session.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error from Qwen API: {error_text}")
                        return f"Error: Failed to generate response. Status code: {response.status}"

                    data = await response.json()
                    return data["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"Error generating multimodal response: {str(e)}")
            return f"Error: {str(e)}"


# 数据处理工具
class DataProcessor:
    """数据处理工具类"""
    @staticmethod
    def load_csv(file_path: str) -> pd.DataFrame:
        """加载CSV文件"""
        try:
            return pd.read_csv(file_path)
        except Exception as e:
            logger.error(f"Error loading CSV file {file_path}: {str(e)}")
            raise

    @staticmethod
    def extract_time_series_from_df(
        df: pd.DataFrame, 
        timestamp_col: str, 
        value_cols: List[str],
        sort_by_time: bool = True
    ) -> Tuple[np.ndarray, List[str]]:
        """从DataFrame提取时间序列数据"""
        if sort_by_time and timestamp_col in df.columns:
            df = df.sort_values(by=timestamp_col)

        # 确保所有值列都存在
        missing_cols = [col for col in value_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Columns not found in DataFrame: {missing_cols}")

        # 提取时间戳和值
        timestamps = df[timestamp_col].tolist() if timestamp_col in df.columns else None
        values = df[value_cols].values

        return values, timestamps

    @staticmethod
    def normalize_time_series(ts_data: np.ndarray, method: str = 'minmax') -> np.ndarray:
        """归一化时间序列数据"""
        if method == 'minmax':
            # Min-Max归一化
            min_vals = np.min(ts_data, axis=0, keepdims=True)
            max_vals = np.max(ts_data, axis=0, keepdims=True)
            range_vals = max_vals - min_vals
            # 避免除以零
            range_vals[range_vals == 0] = 1.0
            return (ts_data - min_vals) / range_vals

        elif method == 'zscore':
            # Z-score标准化
            mean_vals = np.mean(ts_data, axis=0, keepdims=True)
            std_vals = np.std(ts_data, axis=0, keepdims=True)
            # 避免除以零
            std_vals[std_vals == 0] = 1.0
            return (ts_data - mean_vals) / std_vals

        else:
            raise ValueError(f"Unsupported normalization method: {method}")

    @staticmethod
    def extract_text_from_pdf(file_path: str) -> str:
        """从PDF提取文本（简化实现）"""
        try:
            # 在实际实现中，应使用如PyPDF2、pdfplumber等库
            return f"Extracted text from {file_path}"
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {str(e)}")
            return ""

    @staticmethod
    def extract_tables_from_pdf(file_path: str) -> List[pd.DataFrame]:
        """从PDF提取表格（简化实现）"""
        try:
            # 在实际实现中，应使用如tabula-py、camelot等库
            return [pd.DataFrame({'mock': [1, 2, 3]})]
        except Exception as e:
            logger.error(f"Error extracting tables from PDF {file_path}: {str(e)}")
            return []

    @staticmethod
    def extract_images_from_pdf(file_path: str, output_dir: str) -> List[str]:
        """从PDF提取图像（简化实现）"""
        try:
            # 在实际实现中，应使用如PyMuPDF等库
            os.makedirs(output_dir, exist_ok=True)
            image_paths = [f"{output_dir}/mock_image_{i}.jpg" for i in range(3)]
            return image_paths
        except Exception as e:
            logger.error(f"Error extracting images from PDF {file_path}: {str(e)}")
            return []


# 多Agent系统实现
# 使用MetaGPT框架实现多Agent系统

# 基本Action定义
class AnalyzeMarketData(Action):
    """分析市场数据的动作"""
    def __init__(self):
        super().__init__(name="analyze_market_data", description="分析市场数据和趋势")

    async def run(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行市场数据分析"""
        prompt = f"""
        请分析以下市场数据并提供见解：

        时间范围: {market_data.get('time_range', 'N/A')}
        股票代码: {market_data.get('symbol', 'N/A')}

        价格数据:
        {market_data.get('price_summary', 'N/A')}

        成交量数据:
        {market_data.get('volume_summary', 'N/A')}

        市场指标:
        {market_data.get('indicators', 'N/A')}

        最近新闻:
        {market_data.get('recent_news', 'N/A')}

        请提供详细的市场分析，包括趋势识别、支撑/阻力位、技术指标解读以及可能的交易机会。
        """

        qwen_client = QwenClient()
        analysis = await qwen_client.generate(prompt)

        return {
            "analysis": analysis,
            "timestamp": datetime.now().isoformat()
        }


class GenerateInvestmentIdea(Action):
    """生成投资想法的动作"""
    def __init__(self):
        super().__init__(name="generate_investment_idea", description="根据分析生成投资想法")

    async def run(self, market_analysis: Dict[str, Any], investment_style: str = "value") -> Dict[str, Any]:
        """执行投资想法生成"""
        prompt = f"""
        基于以下市场分析，生成符合{investment_style}投资风格的投资想法：

        市场分析:
        {market_analysis.get('analysis', 'N/A')}

        请提供具体的投资想法，包括：
        1. 建议的操作（买入、卖出、持有）
        2. 目标价格区间
        3. 仓位比例建议
        4. 预期持有时间
        5. 风险评估
        6. 决策理由

        以{investment_style}投资风格为基础进行分析和推荐。
        """

        qwen_client = QwenClient()
        idea = await qwen_client.generate(prompt)

        return {
            "investment_idea": idea,
            "investment_style": investment_style,
            "timestamp": datetime.now().isoformat()
        }


class EvaluateRisk(Action):
    """评估风险的动作"""
    def __init__(self):
        super().__init__(name="evaluate_risk", description="评估投资想法的风险")

    async def run(self, investment_idea: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行风险评估"""
        prompt = f"""
        请对以下投资想法进行全面的风险评估：

        投资想法:
        {investment_idea.get('investment_idea', 'N/A')}

        投资风格:
        {investment_idea.get('investment_style', 'N/A')}

        当前市场数据:
        价格: {market_data.get('price_summary', 'N/A')}
        波动率: {market_data.get('volatility', 'N/A')}
        市场情绪: {market_data.get('market_sentiment', 'N/A')}

        请评估以下风险维度:
        1. 市场风险：系统性风险评估
        2. 流动性风险：能否容易地进出仓位
        3. 价格风险：不利价格变动的风险
        4. 时机风险：进场/出场时机不当的风险
        5. 情绪风险：市场情绪对投资的影响

        对每个风险维度给出具体的风险等级（低/中/高）和详细解释。
        """

        qwen_client = QwenClient()
        risk_assessment = await qwen_client.generate(prompt)

        return {
            "risk_assessment": risk_assessment,
            "investment_idea": investment_idea.get('investment_idea'),
            "timestamp": datetime.now().isoformat()
        }


class RetrieveSimilarPatterns(Action):
    """使用TimeRAG检索相似模式的动作"""
    def __init__(self, time_rag: TimeRAG):
        super().__init__(name="retrieve_similar_patterns", description="检索与当前市场状况相似的历史模式")
        self.time_rag = time_rag

    async def run(self, 
                current_data: np.ndarray, 
                additional_context: str = None, 
                top_k: int = 5) -> Dict[str, Any]:
        """执行相似模式检索"""
        try:
            forecast_result = await self.time_rag.generate_forecast(
                query_ts=current_data,
                forecast_horizon=10,
                top_k=top_k,
                additional_context=additional_context
            )

            return {
                "forecast_result": forecast_result,
                "status": "success",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error retrieving similar patterns: {str(e)}")
            return {
                "status": "error",
                "error_message": str(e),
                "timestamp": datetime.now().isoformat()
            }


class GenerateTradingDecision(Action):
    """生成交易决策的动作"""
    def __init__(self):
        super().__init__(name="generate_trading_decision", description="整合多个输入生成最终交易决策")

    async def run(self, 
                market_analysis: Dict[str, Any],
                investment_ideas: List[Dict[str, Any]],
                risk_assessment: Dict[str, Any],
                historical_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易决策生成"""

        # 格式化投资想法
        formatted_ideas = "\n\n".join([
            f"投资风格: {idea.get('investment_style', 'N/A')}\n"
            f"投资想法: {idea.get('investment_idea', 'N/A')}"
            for idea in investment_ideas
        ])

        prompt = f"""
        请基于以下综合信息生成最终交易决策：

        市场分析:
        {market_analysis.get('analysis', 'N/A')}

        投资想法（多种风格）:
        {formatted_ideas}

        风险评估:
        {risk_assessment.get('risk_assessment', 'N/A')}

        历史相似模式分析:
        {historical_patterns.get('forecast_result', {}).get('forecast_text', 'N/A')}

        请提供一个明确的交易决策，包括：
        1. 建议的具体操作（买入/卖出/持有）
        2. 入场价格范围
        3. 止损水平
        4. 目标价格
        5. 仓位大小（资产百分比）
        6. 预计持有时间
        7. 交易类型（日内/摇摆/中期/长期）
        8. 综合决策的详细理由
        9. 执行计划和条件

        决策应该平衡不同投资风格的见解，考虑风险评估，并借鉴历史相似模式的表现。
        """

        qwen_client = QwenClient()
        decision = await qwen_client.generate(prompt)

        return {
            "trading_decision": decision,
            "confidence_level": self._calculate_confidence(investment_ideas, risk_assessment),
            "timestamp": datetime.now().isoformat()
        }

    def _calculate_confidence(self, investment_ideas: List[Dict[str, Any]], risk_assessment: Dict[str, Any]) -> float:
        """计算决策置信度（简化实现）"""
        # 在实际应用中，应该基于更复杂的逻辑计算置信度
        # 这里仅提供一个示例实现
        ideas_count = len(investment_ideas)
        if ideas_count == 0:
            return 0.0

        # 假设风险评估中包含了风险等级
        risk_text = risk_assessment.get('risk_assessment', '').lower()
        risk_factor = 1.0
        if '高风险' in risk_text or 'high risk' in risk_text:
            risk_factor = 0.7
        elif '中风险' in risk_text or 'medium risk' in risk_text:
            risk_factor = 0.85

        # 简单计算置信度
        return risk_factor * min(0.95, 0.5 + (ideas_count / 10))


# Agent定义
class MarketAnalysisAgent(Role):
    """市场分析Agent"""
    def __init__(self, name="市场分析专家", profile="专注于分析市场数据和趋势的专家"):
        super().__init__(name=name, profile=profile)
        self.actions = [AnalyzeMarketData()]

    async def run(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """运行市场分析"""
        analyze_action = self.actions[0]
        result = await analyze_action.run(market_data)
        return result


class ValueInvestorAgent(Role):
    """价值投资Agent - 本杰明·格雷厄姆风格"""
    def __init__(self, name="价值投资专家 (本杰明·格雷厄姆)", 
                 profile="遵循本杰明·格雷厄姆价值投资原则，专注于安全边际和内在价值"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行价值投资分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="价值投资 (本杰明·格雷厄姆)")
        return result


class ActivistInvestorAgent(Role):
    """激进投资者Agent - 比尔·阿克曼风格"""
    def __init__(self, name="激进投资专家 (比尔·阿克曼)", 
                 profile="遵循比尔·阿克曼的投资风格，积极寻找具有转型潜力的公司"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行激进投资分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="激进投资 (比尔·阿克曼)")
        return result


class GrowthInvestorAgent(Role):
    """成长投资Agent - 凯瑟琳·伍德风格"""
    def __init__(self, name="成长投资专家 (凯瑟琳·伍德)", 
                 profile="遵循凯瑟琳·伍德的投资风格，专注于创新和破坏性技术"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行成长投资分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="成长投资 (凯瑟琳·伍德)")
        return result


class CommonSenseInvestorAgent(Role):
    """常识投资Agent - 查理·芒格风格"""
    def __init__(self, name="常识投资专家 (查理·芒格)", 
                 profile="遵循查理·芒格的投资风格，专注于多学科思维和高质量企业"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行常识投资分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="常识投资 (查理·芒格)")
        return result


class GrowthValueInvestorAgent(Role):
    """成长价值投资Agent - 菲利普·费舍尔风格"""
    def __init__(self, name="成长价值投资专家 (菲利普·费舍尔)", 
                 profile="遵循菲利普·费舍尔的投资风格，寻找有增长潜力的优质企业"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行成长价值投资分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="成长价值投资 (菲利普·费舍尔)")
        return result


class MacroTradingAgent(Role):
    """宏观交易Agent - 斯坦利·德鲁肯米勒风格"""
    def __init__(self, name="宏观交易专家 (斯坦利·德鲁肯米勒)", 
                 profile="遵循斯坦利·德鲁肯米勒的投资风格，关注宏观经济趋势和灵活仓位管理"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行宏观交易分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="宏观交易 (斯坦利·德鲁肯米勒)")
        return result


class ConcentratedInvestorAgent(Role):
    """集中投资Agent - 沃伦·巴菲特风格"""
    def __init__(self, name="集中投资专家 (沃伦·巴菲特)", 
                 profile="遵循沃伦·巴菲特的投资风格，专注于集中持有优质企业"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateInvestmentIdea()]

    async def run(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """运行集中投资分析"""
        generate_action = self.actions[0]
        result = await generate_action.run(market_analysis, investment_style="集中投资 (沃伦·巴菲特)")
        return result


class RiskAnalysisAgent(Role):
    """风险分析Agent"""
    def __init__(self, name="风险分析专家", profile="专注于全面评估投资风险"):
        super().__init__(name=name, profile=profile)
        self.actions = [EvaluateRisk()]

    async def run(self, investment_idea: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """运行风险分析"""
        evaluate_action = self.actions[0]
        result = await evaluate_action.run(investment_idea, market_data)
        return result


class HistoricalPatternAnalysisAgent(Role):
    """历史模式分析Agent，使用TimeRAG"""
    def __init__(self, name="历史模式分析专家", profile="专注于寻找历史相似模式", time_rag: TimeRAG = None):
        super().__init__(name=name, profile=profile)
        self.time_rag = time_rag
        self.actions = [RetrieveSimilarPatterns(time_rag)]

    async def run(self, current_data: np.ndarray, context: str = None) -> Dict[str, Any]:
        """运行历史模式分析"""
        retrieve_action = self.actions[0]
        result = await retrieve_action.run(current_data, context)
        return result


class DecisionMakingAgent(Role):
    """决策制定Agent"""
    def __init__(self, name="首席决策专家", profile="整合所有分析，制定最终交易决策"):
        super().__init__(name=name, profile=profile)
        self.actions = [GenerateTradingDecision()]

    async def run(self, 
                market_analysis: Dict[str, Any],
                investment_ideas: List[Dict[str, Any]],
                risk_assessment: Dict[str, Any],
                historical_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """运行决策制定"""
        decision_action = self.actions[0]
        result = await decision_action.run(
            market_analysis, 
            investment_ideas, 
            risk_assessment, 
            historical_patterns
        )
        return result


class ExecutionAgent(Role):
    """交易执行Agent"""
    def __init__(self, name="交易执行专家", profile="负责将决策转化为可执行的交易指令"):
        super().__init__(name=name, profile=profile)

    async def run(self, trading_decision: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易执行计划"""
        decision_text = trading_decision.get('trading_decision', '')
        confidence = trading_decision.get('confidence_level', 0.0)

        prompt = f"""
        请基于以下交易决策生成详细的执行计划：

        交易决策:
        {decision_text}

        决策置信度: {confidence:.2f}

        请提供：
        1. 明确的交易指令格式（适合执行系统处理）
        2. 执行时间安排（考虑最佳执行时机）
        3. 订单类型建议（市价单、限价单、止损单等）
        4. 分批执行计划（如适用）
        5. 执行风险控制措施
        6. 执行后监控计划

        确保执行计划具体、明确，并考虑到市场流动性和执行风险。
        """

        qwen_client = QwenClient()
        execution_plan = await qwen_client.generate(prompt)

        return {
            "execution_plan": execution_plan,
            "original_decision": decision_text,
            "confidence_level": confidence,
            "timestamp": datetime.now().isoformat()
        }


class DataCollectionAgent(Role):
    """数据收集Agent"""
    def __init__(self, name="数据收集专家", profile="负责收集和处理各种格式的金融数据"):
        super().__init__(name=name, profile=profile)
        self.tools = {
            "search_engine": SearchEngine(SearchEngineType.SERPAPI),
            "web_browser": WebBrowserEngine()
        }

    async def process_csv_data(self, file_path: str) -> Dict[str, Any]:
        """处理CSV数据"""
        try:
            processor = DataProcessor()
            df = processor.load_csv(file_path)

            # 基本描述性统计
            stats = {}
            for col in df.columns:
                if pd.api.types.is_numeric_dtype(df[col]):
                    stats[col] = {
                        "mean": df[col].mean(),
                        "median": df[col].median(),
                        "std": df[col].std(),
                        "min": df[col].min(),
                        "max": df[col].max()
                    }

            return {
                "status": "success",
                "file_path": file_path,
                "rows": len(df),
                "columns": list(df.columns),
                "stats": stats,
                "sample": df.head(5).to_dict(),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error processing CSV file {file_path}: {str(e)}")
            return {
                "status": "error",
                "file_path": file_path,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def process_pdf_report(self, file_path: str, output_dir: str = "./extracted") -> Dict[str, Any]:
        """处理PDF研究报告"""
        try:
            processor = DataProcessor()
            text = processor.extract_text_from_pdf(file_path)
            tables = processor.extract_tables_from_pdf(file_path)
            images = processor.extract_images_from_pdf(file_path, output_dir)

            # 使用Qwen分析报告内容
            if text:
                prompt = f"""
                请分析以下金融研究报告的主要内容并提取关键信息：

                报告内容:
                {text[:5000]}...

                请提供：
                1. 报告的主要主题和目标
                2. 主要研究发现和结论
                3. 市场预测和建议
                4. 提到的主要股票或资产
                5. 主要风险因素

                以结构化格式提供这些信息。
                """

                qwen_client = QwenClient()
                analysis = await qwen_client.generate(prompt)
            else:
                analysis = "无法提取报告文本内容"

            return {
                "status": "success",
                "file_path": file_path,
                "text_length": len(text),
                "table_count": len(tables),
                "image_count": len(images),
                "analysis": analysis,
                "image_paths": images,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error processing PDF file {file_path}: {str(e)}")
            return {
                "status": "error",
                "file_path": file_path,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def collect_web_data(self, keywords: str, max_results: int = 5) -> Dict[str, Any]:
        """从网络收集数据"""
        try:
            search_results = await self.tools["search_engine"].search(keywords, max_results=max_results)

            urls = extract_urls(search_results)
            web_contents = []

            for url in urls[:3]:  # 限制处理前3个URL
                try:
                    content = await self.tools["web_browser"].browse(url)
                    web_contents.append({
                        "url": url,
                        "content": content[:5000]  # 限制内容长度
                    })
                except Exception as e:
                    logger.warning(f"Error browsing URL {url}: {str(e)}")

            # 使用Qwen分析收集到的内容
            if web_contents:
                prompt = f"""
                请分析以下从网络收集的关于"{keywords}"的信息，并提取关键见解：

                {web_contents[0]['url']}:
                {web_contents[0]['content'][:2000]}...

                {web_contents[1]['url'] if len(web_contents) > 1 else ''}:
                {web_contents[1]['content'][:2000] if len(web_contents) > 1 else ''}...

                {web_contents[2]['url'] if len(web_contents) > 2 else ''}:
                {web_contents[2]['content'][:2000] if len(web_contents) > 2 else ''}...

                请提供：
                1. 主要市场趋势和新闻
                2. 专家观点和分析
                3. 潜在的投资机会
                4. 主要风险因素
                5. 市场情绪总结

                以结构化格式提供这些信息。
                """

                qwen_client = QwenClient()
                analysis = await qwen_client.generate(prompt)
            else:
                analysis = "未能收集到相关网络内容"

            return {
                "status": "success",
                "keywords": keywords,
                "urls": [item["url"] for item in web_contents],
                "analysis": analysis,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error collecting web data for {keywords}: {str(e)}")
            return {
                "status": "error",
                "keywords": keywords,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


# QuantTimeRAG系统主类
class QuantTimeRAG:
    """QuantTimeRAG系统：集成TimeRAG和多Agent系统的量化交易框架"""
    def __init__(self, kb_path: str = "quant_timeseries_kb.json"):
        """初始化QuantTimeRAG系统"""
        # 初始化组件
        self.qwen_client = QwenClient()
        self.knowledge_base = TimeSeriesKnowledgeBase(db_path=kb_path)
        self.time_rag = TimeRAG(knowledge_base=self.knowledge_base, llm_client=self.qwen_client)

        # 初始化Agent
        self.data_collection_agent = DataCollectionAgent()
        self.market_analysis_agent = MarketAnalysisAgent()
        self.value_investor_agent = ValueInvestorAgent()
        self.activist_investor_agent = ActivistInvestorAgent()
        self.growth_investor_agent = GrowthInvestorAgent()
        self.common_sense_investor_agent = CommonSenseInvestorAgent()
        self.growth_value_investor_agent = GrowthValueInvestorAgent()
        self.macro_trading_agent = MacroTradingAgent()
        self.concentrated_investor_agent = ConcentratedInvestorAgent()
        self.risk_analysis_agent = RiskAnalysisAgent()
        self.historical_pattern_agent = HistoricalPatternAnalysisAgent(time_rag=self.time_rag)
        self.decision_making_agent = DecisionMakingAgent()
        self.execution_agent = ExecutionAgent()

    async def add_to_knowledge_base(self, file_path: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """添加数据到知识库"""
        try:
            processor = DataProcessor()

            if file_path.endswith('.csv'):
                df = processor.load_csv(file_path)

                # 确定时间列和值列
                time_col = metadata.get('time_column', df.columns[0])
                if 'value_columns' in metadata:
                    value_cols = metadata['value_columns']
                else:
                    # 自动选择数值列
                    value_cols = [col for col in df.columns if col != time_col and pd.api.types.is_numeric_dtype(df[col])]

                # 提取时间序列
                ts_data, timestamps = processor.extract_time_series_from_df(df, time_col, value_cols)

                # 创建并添加时间序列条目
                entry = TimeSeriesEntry(
                    ts_id=f"ts_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    ts_data=ts_data,
                    ts_metadata=metadata or {"source": file_path},
                    source_type="numeric"
                )

                self.knowledge_base.add_entry(entry)

                return {
                    "status": "success",
                    "message": f"Added time series with ID {entry.ts_id} to knowledge base",
                    "ts_id": entry.ts_id
                }

            else:
                return {
                    "status": "error",
                    "message": f"Unsupported file format: {file_path}"
                }

        except Exception as e:
            logger.error(f"Error adding to knowledge base: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to add to knowledge base: {str(e)}"
            }

    async def full_analysis_pipeline(self, 
                                   price_data_path: str,
                                   symbol: str,
                                   additional_data: Dict[str, Any] = None,
                                   web_keywords: str = None) -> Dict[str, Any]:
        """执行完整的分析流程"""
        results = {}

        try:
            # 1. 添加价格数据到知识库
            kb_result = await self.add_to_knowledge_base(
                price_data_path, 
                metadata={"symbol": symbol, "type": "price_data"}
            )
            results["knowledge_base"] = kb_result

            # 2. 准备市场数据
            price_data = DataProcessor.load_csv(price_data_path)
            market_data = {
                "symbol": symbol,
                "time_range": f"{price_data.iloc[0, 0]} to {price_data.iloc[-1, 0]}",
                "price_summary": price_data.describe().to_string(),
                "volume_summary": "N/A"  # 实际应用中需要提供
            }

            # 添加额外数据
            if additional_data:
                market_data.update(additional_data)

            # 3. 如有需要，收集网络数据
            if web_keywords:
                web_data = await self.data_collection_agent.collect_web_data(web_keywords)
                market_data["recent_news"] = web_data.get("analysis", "No web data available")
                results["web_data"] = web_data

            # 4. 市场分析
            market_analysis = await self.market_analysis_agent.run(market_data)
            results["market_analysis"] = market_analysis

            # 5. 多视角投资分析
            investment_ideas = []

            value_idea = await self.value_investor_agent.run(market_analysis)
            investment_ideas.append(value_idea)

            activist_idea = await self.activist_investor_agent.run(market_analysis)
            investment_ideas.append(activist_idea)

            growth_idea = await self.growth_investor_agent.run(market_analysis)
            investment_ideas.append(growth_idea)

            common_sense_idea = await self.common_sense_investor_agent.run(market_analysis)
            investment_ideas.append(common_sense_idea)

            growth_value_idea = await self.growth_value_investor_agent.run(market_analysis)
            investment_ideas.append(growth_value_idea)

            macro_idea = await self.macro_trading_agent.run(market_analysis)
            investment_ideas.append(macro_idea)

            concentrated_idea = await self.concentrated_investor_agent.run(market_analysis)
            investment_ideas.append(concentrated_idea)

            results["investment_ideas"] = investment_ideas

            # 6. 风险分析（使用第一个投资想法作为示例）
            risk_assessment = await self.risk_analysis_agent.run(investment_ideas[0], market_data)
            results["risk_assessment"] = risk_assessment

            # 7. 历史模式分析
            # 准备查询数据
            time_col = price_data.columns[0]
            value_cols = [col for col in price_data.columns if col != time_col and pd.api.types.is_numeric_dtype(price_data[col])]
            query_ts, _ = DataProcessor.extract_time_series_from_df(price_data, time_col, value_cols)

            historical_patterns = await self.historical_pattern_agent.run(
                query_ts, 
                context=f"分析 {symbol} 的历史价格模式，找出类似当前情况的历史案例。"
            )
            results["historical_patterns"] = historical_patterns

            # 8. 决策制定
            trading_decision = await self.decision_making_agent.run(
                market_analysis,
                investment_ideas,
                risk_assessment,
                historical_patterns
            )
            results["trading_decision"] = trading_decision

            # 9. 执行计划
            execution_plan = await self.execution_agent.run(trading_decision)
            results["execution_plan"] = execution_plan

            return {
                "status": "success",
                "results": results,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in analysis pipeline: {str(e)}")
            return {
                "status": "error",
                "message": f"Analysis pipeline failed: {str(e)}",
                "partial_results": results,
                "timestamp": datetime.now().isoformat()
            }


# API服务
app = FastAPI(title="QuantTimeRAG API", description="量化交易TimeRAG系统API")

class FilePathInput(BaseModel):
    file_path: str
    symbol: str
    web_keywords: str = None
    metadata: Dict[str, Any] = None

@app.post("/analyze")
async def analyze_data(input_data: FilePathInput):
    """分析数据API端点"""
    system = QuantTimeRAG()
    result = await system.full_analysis_pipeline(
        price_data_path=input_data.file_path,
        symbol=input_data.symbol,
        web_keywords=input_data.web_keywords,
        additional_data=input_data.metadata
    )
    return result

@app.post("/add_to_kb")
async def add_to_knowledge_base(input_data: FilePathInput):
    """添加到知识库API端点"""
    system = QuantTimeRAG()
    result = await system.add_to_knowledge_base(
        file_path=input_data.file_path,
        metadata=input_data.metadata
    )
    return result


# 使用示例
async def main():
    """QuantTimeRAG系统使用示例"""
    # 创建系统实例
    system = QuantTimeRAG()

    # 示例1：添加示例数据到知识库
    print("示例1：添加数据到知识库")
    # 这里假设有一个sample_data.csv文件
    result = await system.add_to_knowledge_base(
        "sample_data.csv",
        metadata={"symbol": "AAPL", "type": "price_data"}
    )
    print(result)

    # 示例2：运行完整分析流程
    print("\n示例2：运行完整分析流程")
    # 这里假设有一个apple_prices.csv文件
    result = await system.full_analysis_pipeline(
        price_data_path="apple_prices.csv",
        symbol="AAPL",
        web_keywords="Apple stock latest news earnings"
    )
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
