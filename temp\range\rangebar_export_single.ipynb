{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              name       stategy       pnl  times  long_atr_mult  \\\n", "label                                                              \n", "A1909.DC    豆一1909  FUT-GAP-b-RL  -3600.00      1            0.8   \n", "AP1910.ZC   苹果1910  FUT-GAP-b-RL    760.00      2            0.6   \n", "BU1912.SC   沥青1912  FUT-GAP-b-RL  14300.00      1            1.0   \n", "CF1909.ZC   郑棉1909  FUT-GAP-b-RL  29140.00      4            0.6   \n", "CU1908.SC   沪铜1908  FUT-GAP-b-RL   1885.00      2            0.8   \n", "CY2001.ZC   棉纱2001  FUT-GAP-b-RL   2625.00      1            1.0   \n", "FG1909.ZC   玻璃1909  FUT-GAP-b-RL   8800.00      2            1.0   \n", "HC1910.SC   热卷1910  FUT-GAP-b-RL  24400.00      2            0.7   \n", "I1909.DC    铁矿1909  FUT-GAP-b-RL  29800.00      3            0.6   \n", "J1909.DC    焦炭1909  FUT-GAP-b-RL   2650.00      2            0.8   \n", "JD1909.DC   鸡蛋1909  FUT-GAP-b-RL   8460.00      1            0.6   \n", "JM1909.DC   焦煤1909  FUT-GAP-b-RL   1800.00      1            0.8   \n", "L1909.DC    乙烯1909  FUT-GAP-b-RL  16875.00      1            0.9   \n", "M1909.DC    豆粕1909  FUT-GAP-b-RL  11000.00      2            0.6   \n", "MA1909.ZC   甲醇1909  FUT-GAP-b-RL  15880.00      2            1.0   \n", "NI1908.SC   沪镍1908  FUT-GAP-b-RL   2658.00      1            0.6   \n", "OI1909.ZC   菜油1909  FUT-GAP-b-RL   7440.00      1            0.6   \n", "P1909.DC    棕榈1909  FUT-GAP-b-RL   4400.00      1            0.9   \n", "PB1908.SC   沪铅1908  FUT-GAP-b-RL    450.00      1            0.7   \n", "RB1910.SC   螺纹1910  FUT-GAP-b-RL  23760.00      1            1.0   \n", "RM1909.ZC   菜粕1909  FUT-GAP-b-RL  14770.00      3            0.6   \n", "RU1909.SC   橡胶1909  FUT-GAP-b-RL  12600.00      2            1.0   \n", "SF1909.ZC   硅铁1909  FUT-GAP-b-RL   5810.00      2            0.6   \n", "SM1909.ZC   锰硅1909  FUT-GAP-b-RL  15950.00      2            0.8   \n", "SR1909.Z<PERSON>   白糖1909  FUT-GAP-b-RL  12980.00      2            0.8   \n", "TA1909.ZC  PTA1909  FUT-GAP-b-RL  40290.00      4            0.6   \n", "V1909.DC   PVC1909  FUT-GAP-b-RL   5100.00      2            0.6   \n", "Y1909.DC    豆油1909  FUT-GAP-b-RL   6050.00      1            0.8   \n", "ZC1909.ZC   动煤1909  FUT-GAP-b-RL   4160.01      3            0.8   \n", "ZN1908.SC   沪锌1908  FUT-GAP-b-RL   7800.00      2            0.9   \n", "\n", "           short_atr_mult  long_atr  short_atr  \n", "label                                           \n", "A1909.DC              0.8        36         12  \n", "AP1910.ZC             0.8       133         65  \n", "BU1912.SC             0.8        75         16  \n", "CF1909.ZC             1.0       131         43  \n", "CU1908.SC             1.2       355         50  \n", "CY2001.ZC             1.2       356         66  \n", "FG1909.ZC             0.8        16          4  \n", "HC1910.SC             0.8        37         11  \n", "I1909.DC              1.6        10          8  \n", "J1909.DC              0.8        34         10  \n", "JD1909.DC             1.0        34         24  \n", "JM1909.DC             1.2        16          7  \n", "L1909.DC              1.0        86         28  \n", "M1909.DC              1.0        23         13  \n", "MA1909.ZC             0.8        43         10  \n", "NI1908.SC             1.0      1060        155  \n", "OI1909.ZC             1.6        43         34  \n", "P1909.DC              1.2        44         17  \n", "PB1908.SC             1.2       154         26  \n", "RB1910.SC             1.2        51         17  \n", "RM1909.ZC             1.0        25         14  \n", "RU1909.SC             0.8       223         51  \n", "SF1909.ZC             0.8        36         15  \n", "SM1909.ZC             1.2        67         30  \n", "SR1909.ZC             1.0        50         13  \n", "TA1909.ZC             1.0        58         20  \n", "V1909.DC              1.0        50         26  \n", "Y1909.DC              0.8        42         14  \n", "ZC1909.ZC             0.8         5          1  \n", "ZN1908.SC             1.4       308         50  \n"]}], "source": ["CSV_PATH = \"d:/QuantLab/rpt/\"\n", "JSON_PATH = \"d:/QuantLab/\"\n", "\n", "def read_backtest_range(csv_file):\n", "    df = pd.read_csv(CSV_PATH + csv_file, encoding=\"gbk\")\n", "    df.set_index('label',inplace=True)\n", "    return df\n", "\n", "csv_file = \"FUT-GAP-b.225906.ord2.csv\"\n", "# csv_file = \"FUT-STR-BK-a.231344.ord2.csv\"\n", "df = pd.read_csv(CSV_PATH + csv_file, encoding=\"gbk\")\n", "df.set_index('label',inplace=True)\n", "print(df)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["with open(JSO<PERSON>_PATH + \"RangeBar.json\",'r') as json_f:\n", "    range_dict = json.load(json_f)\n", "\n", "for index,row in df.iterrows():\n", "    range_dict['fut']['long_range_bar'][index[:-7]] = row['long_atr']\n", "    range_dict['fut']['short_range_bar'][index[:-7]] = row['short_atr']\n", "    if index in range_dict['fut']['long_range_bar']:\n", "        del range_dict['fut']['long_range_bar'][index]\n", "    if index in range_dict['fut']['short_range_bar']:\n", "        del range_dict['fut']['short_range_bar'][index]\n", "\n", "with open(J<PERSON><PERSON>_PATH + \"RangeBar.\"+ df.iloc[1, 1][:-2] + csv_file[-15:-9] + \".json\",\"w\") as json_f:\n", "    json.dump(range_dict,json_f,indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}