
import os
from pathlib2 import Path

def load_model_path(root=None, version=None, v_num=None, best=False):
    """ When best = True, return the best model's path in a directory 
        by selecting the best model with largest epoch. If not, return
        the last model saved. You must provide at least one of the 
        first three args.
    Args: 
        root: The root directory of checkpoints. It can also be a
            model ckpt file. Then the function will return it.
        version: The name of the version you are going to load.
        v_num: The version's number that you are going to load.
        best: Whether return the best model.
    """
    def sort_by_epoch(path):
        name = path.stem
        epoch=int(name.split('-')[1].split('=')[1])
        return epoch
    
    def generate_root():
        if root is not None:
            return root
        elif version is not None:
            return str(Path('lightning_logs', version, 'checkpoints'))
        else:
            return str(Path('lightning_logs', f'version_{v_num}', 'checkpoints'))

    if root==version==v_num==None:
        return None

    root = generate_root()
    if Path(root).is_file():
        return root
    if best:
        files=[i for i in list(Path(root).iterdir()) if i.stem.startswith('best')]
        files.sort(key=sort_by_epoch, reverse=True)
        res = str(files[0])
    else:
        res = str(Path(root) / 'last.ckpt')
    return res

def load_model_path_by_args(load_dir=None, load_ver=None, load_v_num=None, load_best=False):
    return load_model_path(root=load_dir, version=load_ver, v_num=load_v_num, best=load_best)

def get_best_saved_model_filename(log_dir, sub_dir, only_best=False):
    model_files = {}
    log_dir = os.path.join(log_dir, sub_dir)
    for root, dirs, files in os.walk(log_dir):
        for file in files:
            if file.startswith("best-"):
                model_files[file] = root
    models_scores = {}
    if model_files:
        # 获取score最小的模型
        best_file=None
        best_score=None
        for file in model_files.keys():
            file_score = float(file.split('=')[-1][:-5])
            print(f"Found model file: {file}  {file_score}")
            if not only_best:
                models_scores[f"{model_files[file]}\\{file}"] = file_score
            if best_file is None or file_score < best_score:
                best_file = file
                best_score = file_score
        print(f"{model_files[best_file]}\\{best_file}", best_score)
        if only_best:
            return f"{model_files[best_file]}\\{best_file}", best_score
        # model_scores dict values sorted by score ascending
        # models_scores = dict(sorted(models_scores.items(), key=lambda x: x[1]))
    return models_scores
  