{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pylab as plt\n", "import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data loading"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["data_raw = pd.read_csv('IVE_tickbidask.txt', header=None, names=['Date','Time','Price','Bid','Ask','Size'])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["data = data_raw#.iloc[:100000]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": true}, "outputs": [], "source": ["data['DateTime'] = pd.to_datetime(data['Date'] + ' ' + data['Time'])\n", "data = data.drop(columns = ['Date', 'Time'])\n", "data = data.set_index('DateTime')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["from scipy import stats\n", "from bars import *"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                        open   high      low  close  volume\n", "DateTime                                                   \n", "2009-09-28 09:40:00  50.7900  50.85  50.7100  50.81    9197\n", "2009-09-28 09:50:00  50.8100  51.12  50.7833  51.07   26447\n", "2009-09-28 10:00:00  51.0988  51.15  51.0800  51.12   53716\n", "2009-09-28 10:10:00  51.1500  51.29  51.1500  51.27   47673\n", "2009-09-28 10:20:00  51.2800  51.29  51.2100  51.21   30631\n"]}], "source": ["bars = BarSeries(data)\n", "time_bars = bars.process_ticks(frequency='10Min')\n", "print time_bars.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     close   high    low   open  volume\n", "DateTime                                               \n", "2009-09-28 09:47:22  51.06  51.07  50.71  50.79   30044\n", "2009-09-28 09:54:38  51.13  51.15  51.06  51.06   28975\n", "2009-09-28 10:00:15  51.21  51.21  51.08  51.13   32841\n", "2009-09-28 10:05:50  51.21  51.28  51.20  51.21   33764\n", "2009-09-28 10:15:13  51.25  51.29  51.19  51.22   37104\n"]}], "source": ["bars = TickBarSeries(data)\n", "tick_bars = bars.process_ticks(frequency = 100)\n", "print tick_bars.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fixed horizon"]}, {"cell_type": "code", "execution_count": 96, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["WINDOW = 180\n", "HORIZON = 60\n", "T = 0.025\n", "\n", "labels = []\n", "for i in range(WINDOW, 10000, 1):\n", "    window = tick_bars.iloc[i-WINDOW:i]\n", "    now = tick_bars.close[i]\n", "    future = tick_bars.close[i+HORIZON]\n", "    ret = (future - now) / now\n", "    \n", "    if ret > T:\n", "        labels.append(1)\n", "    elif ret < -T:\n", "        labels.append(-1)\n", "    else:\n", "        labels.append(0)"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nR3IAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(labels, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Volatility horizon"]}, {"cell_type": "code", "execution_count": 98, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["labels = []\n", "for i in range(WINDOW, 10000, 1):\n", "    window = tick_bars.iloc[i-WINDOW:i]\n", "    now = tick_bars.close[i]\n", "    future = tick_bars.close[i+HORIZON]\n", "    ret = (future - now) / now\n", "    \n", "    window_abs_returns = np.abs(window.close.pct_change())\n", "    Ti = np.std(window_abs_returns) + np.mean(window_abs_returns)\n", "    \n", "    if ret > Ti:\n", "        labels.append(1)\n", "    elif ret < -Ti:\n", "        labels.append(-1)\n", "    else:\n", "        labels.append(0)"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nrkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(labels, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Meta labeling"]}, {"cell_type": "code", "execution_count": 167, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_meta_barier(future_window, last_close, min_ret, tp, sl, vertical_zero = False):\n", "    '''\n", "        XXX\n", "    '''\n", "    if vertical_zero:\n", "        min_ret_situation = [0, 0, 0]\n", "    else:\n", "        min_ret_situation = [0, 0]\n", "        \n", "        \n", "    differences = np.array([(fc - last_close) / last_close for fc in future_window])\n", "    \n", "    # Are there gonna be fluctuations within min_ret???\n", "    min_ret_ups = np.where((differences >= min_ret) == True)[0]\n", "    min_ret_downs = np.where((differences < -min_ret) == True)[0]\n", "  \n", "    if (len(min_ret_ups) == 0) and (len(min_ret_downs) == 0):\n", "        if vertical_zero:\n", "            min_ret_situation[2] = 1\n", "        else:\n", "            if differences[-1] > 0:\n", "                min_ret_situation[0] = 1\n", "            else:\n", "                min_ret_situation[1] = 1            \n", "    else:\n", "        if len(min_ret_ups) == 0: min_ret_ups = [np.inf]\n", "        if len(min_ret_downs) == 0: min_ret_downs = [np.inf]\n", "        if min_ret_ups[0] > min_ret_downs[0]:\n", "            min_ret_situation[0] = 1\n", "        else:\n", "            min_ret_situation[1] = 1\n", "        \n", "    #  Take profit and stop losses indices\n", "    take_profit = np.where((differences >= tp) == True)[0]\n", "    stop_loss = np.where((differences < sl) == True)[0]\n", "    \n", "    # Fluctuation directions coincide with take profit / stop loss actions?\n", "    if min_ret_situation[0] == 1 and len(take_profit) != 0:\n", "        take_action = 1\n", "    elif min_ret_situation[1] == 1 and len(stop_loss) != 0:\n", "        take_action = 1\n", "    else:\n", "        take_action = 0.\n", "    \n", "    return min_ret_situation, take_action"]}, {"cell_type": "code", "execution_count": 202, "metadata": {"collapsed": true}, "outputs": [], "source": ["WINDOW = 180\n", "HORIZON = 60\n", "\n", "TIs = []\n", "# MIN_RET = 0.01\n", "TP = 0.015\n", "SL = -0.015\n", "\n", "X, Y, Y2 = [], [], []\n", "for i in range(WINDOW, 10000, 1):\n", "    window = tick_bars.iloc[i-WINDOW:i]\n", "    now = tick_bars.close[i]\n", "    future_window = tick_bars.close[i:i+HORIZON]\n", "    \n", "    window_abs_returns = np.abs(window.close.pct_change())\n", "    Ti = np.std(window_abs_returns) + np.mean(window_abs_returns)\n", "    \n", "    min_ret_situation, take_action = get_meta_barier(future_window, now, Ti, TP, SL, True)\n", "    X.append(window.close)\n", "    Y.append(min_ret_situation)\n", "    Y2.append(take_action)\n", "    TIs.append(Ti)"]}, {"cell_type": "code", "execution_count": 203, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nrkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(Y2, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 204, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist([np.argmax(y) for y in Y], bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.14"}}, "nbformat": 4, "nbformat_minor": 2}