"""
优化的K线数据集

参照dataset_bar.py实现的高性能K线数据集
"""

import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Tuple, Optional, Union, Callable
import logging

from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES

# 获取logger
logger = logging.getLogger('OptimizedCandlestickDataset')

class OptimizedCandlestickDataset(Dataset):
    """
    优化的K线数据集，参照dataset_bar.py实现
    """
    @staticmethod
    def get_default_config():
        """获取默认配置"""
        from pyqlab.utils.config import CfgNode as CN
        C = CN()
        C.block_size = 30  # 序列长度
        C.pred_len = 1     # 预测长度
        C.stride = 1       # 滑动窗口步长
        C.market = 'fut'   # 市场类型
        C.block_name = 'sf'  # 板块名称
        C.period = 'min5'  # 周期
        C.start_year = None  # 开始年份
        C.end_year = None    # 结束年份
        C.start_date = ""    # 开始日期
        C.end_date = ""      # 结束日期
        C.sel_codes = None   # 选择的代码
        C.use_time_features = True  # 是否使用时间特征
        return C
    
    def __init__(self, 
                 data: Union[pd.DataFrame, List[pd.DataFrame]],
                 tokenizer: CandlestickTokenizer,
                 seq_len: int = 30,
                 pred_len: int = 1,
                 stride: int = 1,
                 add_special_tokens: bool = True,
                 transform: Optional[Callable] = None,
                 code_ids: Optional[List[int]] = None,
                 use_time_features: bool = True):
        """
        初始化优化的K线数据集
        
        Args:
            data: 单个DataFrame或DataFrame列表，每个DataFrame必须包含datetime, open, high, low, close列
            tokenizer: 用于将K线数据转换为token的tokenizer
            seq_len: 输入序列长度
            pred_len: 预测序列长度
            stride: 滑动窗口的步长
            add_special_tokens: 是否添加特殊token
            transform: 数据转换函数
            code_ids: 证券代码ID列表，与data列表一一对应
            use_time_features: 是否使用时间特征
        """
        self.tokenizer = tokenizer
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.stride = stride
        self.add_special_tokens = add_special_tokens
        self.transform = transform
        self.use_time_features = use_time_features
        
        # 处理输入数据
        if isinstance(data, pd.DataFrame):
            self.data_list = [data]
        else:
            self.data_list = data
            
        # 处理代码ID
        if code_ids is None:
            self.code_ids = list(range(len(self.data_list)))
        else:
            if len(code_ids) != len(self.data_list):
                raise ValueError("code_ids的长度必须与data列表长度相同")
            self.code_ids = code_ids
            
        # 预处理数据
        self._preprocess_data()
        
    def _preprocess_data(self):
        """预处理数据，计算每个证券的样本数量和累积和"""
        # 对每个DataFrame进行tokenize并计算可用样本数
        self.tokenized_data = []
        self.time_features = []
        sample_counts = []
        
        for i, df in enumerate(self.data_list):
            # 确保数据按时间排序
            if 'datetime' in df.columns:
                df = df.sort_values('datetime')
                
            # 对整个数据集进行tokenize
            tokens = self.tokenizer.tokenize(df)
            
            # 计算可用样本数
            valid_length = len(tokens) - self.seq_len - self.pred_len + 1
            if valid_length <= 0:
                # 如果数据不足，跳过
                continue
                
            # 计算实际样本数（考虑stride）
            sample_count = (valid_length + self.stride - 1) // self.stride
            sample_counts.append(sample_count)
            
            # 保存tokenized数据
            self.tokenized_data.append({
                'tokens': tokens,
                'df': df,
                'code_id': self.code_ids[i]
            })
            
            # 提取时间特征
            if self.use_time_features and 'datetime' in df.columns:
                time_feats = self._extract_time_features(df)
                self.time_features.append(time_feats)
            else:
                self.time_features.append(None)
                
        # 计算累积和，用于快速定位
        if sample_counts:
            self.sample_counts = np.array(sample_counts)
            self.cum_counts = np.cumsum(self.sample_counts)
            self.total_samples = self.cum_counts[-1]
        else:
            self.sample_counts = np.array([0])
            self.cum_counts = np.array([0])
            self.total_samples = 0
            
        logger.info(f"总共 {len(self.tokenized_data)} 个证券，{self.total_samples} 个样本")
        
    def _extract_time_features(self, df):
        """提取时间特征"""
        if 'datetime' in df.columns:
            # 转换为pandas datetime
            dt = pd.to_datetime(df['datetime'])
            
            # 创建时间特征
            time_features = np.zeros((len(df), 5))
            
            # 小时 (0-23) -> (0-1)
            time_features[:, 0] = dt.dt.hour / 23.0
            
            # 星期几 (0-6) -> (0-1)
            time_features[:, 1] = dt.dt.dayofweek / 6.0
            
            # 月份 (1-12) -> (0-1)
            time_features[:, 2] = (dt.dt.month - 1) / 11.0
            
            # 月内日 (1-31) -> (0-1)
            time_features[:, 3] = (dt.dt.day - 1) / 30.0
            
            # 是否为交易日 (0 or 1)
            time_features[:, 4] = (dt.dt.dayofweek < 5).astype(float)
            
            return time_features
            
        # 如果没有datetime列，返回零矩阵
        return np.zeros((len(df), 5))
        
    def _get_data_index(self, idx):
        """根据全局索引获取数据索引和局部索引"""
        if idx >= self.total_samples:
            raise IndexError(f"索引 {idx} 超出范围 (0-{self.total_samples-1})")
            
        # 找到对应的数据集
        data_idx = np.searchsorted(self.cum_counts, idx, side='right')
        
        # 计算局部索引
        if data_idx == 0:
            local_idx = idx
        else:
            local_idx = idx - self.cum_counts[data_idx - 1]
            
        # 转换为原始数据的起始索引
        start_idx = local_idx * self.stride
        
        return data_idx, start_idx
        
    def __len__(self):
        """获取数据集长度"""
        return self.total_samples
        
    def __getitem__(self, idx):
        """获取一个样本"""
        if self.total_samples == 0:
            raise IndexError("数据集为空")
            
        # 获取数据索引和局部索引
        data_idx, start_idx = self._get_data_index(idx)
        
        # 获取对应的数据
        data = self.tokenized_data[data_idx]
        tokens = data['tokens']
        df = data['df']
        code_id = data['code_id']
        
        # 计算结束索引
        end_idx = start_idx + self.seq_len
        target_end_idx = end_idx + self.pred_len
        
        # 获取输入和目标序列
        input_tokens = tokens[start_idx:end_idx]
        target_tokens = tokens[start_idx+self.pred_len:target_end_idx]
        
        # 确保长度一致
        if len(input_tokens) < self.seq_len:
            input_tokens = input_tokens + [0] * (self.seq_len - len(input_tokens))
        
        if len(target_tokens) < self.seq_len:
            target_tokens = target_tokens + [0] * (self.seq_len - len(target_tokens))
            
        # 获取时间特征
        time_feats = self.time_features[data_idx]
        if time_feats is not None:
            input_time_feats = time_feats[start_idx:end_idx]
            target_time_feats = time_feats[start_idx+self.pred_len:target_end_idx]
            
            # 确保长度一致
            if len(input_time_feats) < self.seq_len:
                padding = np.zeros((self.seq_len - len(input_time_feats), input_time_feats.shape[1]))
                input_time_feats = np.vstack([input_time_feats, padding])
                
            if len(target_time_feats) < self.seq_len:
                padding = np.zeros((self.seq_len - len(target_time_feats), target_time_feats.shape[1]))
                target_time_feats = np.vstack([target_time_feats, padding])
        else:
            # 创建全零时间特征
            input_time_feats = np.zeros((self.seq_len, 5))
            target_time_feats = np.zeros((self.seq_len, 5))
            
        # 转换为PyTorch张量
        input_tokens = torch.tensor(input_tokens, dtype=torch.long)
        target_tokens = torch.tensor(target_tokens, dtype=torch.long)
        code_id = torch.tensor(code_id, dtype=torch.long)
        input_time_feats = torch.tensor(input_time_feats, dtype=torch.float)
        target_time_feats = torch.tensor(target_time_feats, dtype=torch.float)
        
        # 创建结果字典
        result = {
            'input_tokens': input_tokens,
            'target_tokens': target_tokens,
            'code_id': code_id,
            'input_time_features': input_time_feats,
            'target_time_features': target_time_feats
        }
        
        # 应用转换
        if self.transform is not None:
            result = self.transform(result)
            
        return result
