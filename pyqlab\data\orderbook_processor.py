"""
OrderBook数据处理器

用于预处理orderbook数据，将其转换为深度学习模型可用的格式
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
import logging
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("orderbook_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("OrderBookProcessor")

class OrderBookProcessor:
    """OrderBook数据处理器，用于预处理orderbook数据"""
    
    def __init__(self, data_path, save_path=None, n_levels=5, time_steps=100, normalize=True, diff_features=True):
        """
        初始化OrderBook数据处理器
        
        参数:
            data_path: 原始数据路径
            save_path: 处理后数据保存路径，如果为None则不保存
            n_levels: orderbook的档位数量
            time_steps: 时间步长，即每个样本包含多少个时间点
            normalize: 是否对数据进行归一化
            diff_features: 是否计算差分特征
        """
        self.data_path = data_path
        self.save_path = save_path
        self.n_levels = n_levels
        self.time_steps = time_steps
        self.normalize = normalize
        self.diff_features = diff_features
        
        # 创建保存目录
        if save_path:
            os.makedirs(save_path, exist_ok=True)
            os.makedirs(os.path.join(save_path, 'stock'), exist_ok=True)
            os.makedirs(os.path.join(save_path, 'future'), exist_ok=True)
        
        # 初始化标准化器
        self.price_scaler = StandardScaler()
        self.volume_scaler = StandardScaler()
        self.scalers = {}
    
    def load_orderbook_data(self, file_path):
        """
        加载orderbook数据
        
        参数:
            file_path: 数据文件路径
            
        返回:
            pd.DataFrame: orderbook数据
        """
        try:
            df = pd.read_parquet(file_path)
            logger.info(f"加载数据文件 {file_path} 成功，共 {len(df)} 条记录")
            return df
        except Exception as e:
            logger.error(f"加载数据文件 {file_path} 失败: {str(e)}")
            return None
    
    def extract_features(self, df):
        """
        从orderbook数据中提取特征
        
        参数:
            df: orderbook数据DataFrame
            
        返回:
            pd.DataFrame: 特征DataFrame
        """
        if df is None or len(df) == 0:
            logger.warning("输入数据为空，无法提取特征")
            return None
        
        # 确保时间戳列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 按时间戳排序
        df = df.sort_values('timestamp')
        
        # 提取基本特征
        features = []
        
        # 价格特征
        for i in range(1, self.n_levels + 1):
            features.extend([
                f'bid_price_{i}', f'bid_volume_{i}',
                f'ask_price_{i}', f'ask_volume_{i}'
            ])
        
        # 中间价格
        df['mid_price'] = (df['bid_price_1'] + df['ask_price_1']) / 2
        features.append('mid_price')
        
        # 价差
        df['spread'] = df['ask_price_1'] - df['bid_price_1']
        features.append('spread')
        
        # 累积量
        for i in range(1, self.n_levels + 1):
            df[f'cum_bid_volume_{i}'] = sum(df[f'bid_volume_{j}'] for j in range(1, i + 1))
            df[f'cum_ask_volume_{i}'] = sum(df[f'ask_volume_{j}'] for j in range(1, i + 1))
            features.extend([f'cum_bid_volume_{i}', f'cum_ask_volume_{i}'])
        
        # 买卖不平衡
        for i in range(1, self.n_levels + 1):
            df[f'imbalance_{i}'] = (df[f'cum_bid_volume_{i}'] - df[f'cum_ask_volume_{i}']) / (df[f'cum_bid_volume_{i}'] + df[f'cum_ask_volume_{i}'])
            features.append(f'imbalance_{i}')
        
        # 价格差异
        for i in range(1, self.n_levels):
            df[f'bid_price_diff_{i}'] = df[f'bid_price_{i}'] - df[f'bid_price_{i+1}']
            df[f'ask_price_diff_{i}'] = df[f'ask_price_{i+1}'] - df[f'ask_price_{i}']
            features.extend([f'bid_price_diff_{i}', f'ask_price_diff_{i}'])
        
        # 如果需要计算差分特征
        if self.diff_features:
            # 计算价格和量的变化
            for feature in features:
                df[f'{feature}_diff'] = df[feature].diff()
                features.append(f'{feature}_diff')
        
        # 选择特征列
        feature_df = df[['timestamp'] + features].copy()
        
        # 处理缺失值
        feature_df = feature_df.dropna()
        
        return feature_df
    
    def normalize_features(self, feature_df, code):
        """
        对特征进行归一化
        
        参数:
            feature_df: 特征DataFrame
            code: 证券代码，用于区分不同的标准化器
            
        返回:
            pd.DataFrame: 归一化后的特征DataFrame
        """
        if feature_df is None or len(feature_df) == 0:
            logger.warning("输入特征为空，无法进行归一化")
            return None
        
        # 复制数据，避免修改原始数据
        normalized_df = feature_df.copy()
        
        # 分离时间戳列
        timestamp = normalized_df['timestamp']
        features = normalized_df.drop('timestamp', axis=1)
        
        # 为每个代码创建单独的标准化器
        if code not in self.scalers:
            self.scalers[code] = {
                'price': StandardScaler(),
                'volume': StandardScaler(),
                'other': StandardScaler()
            }
        
        # 分类特征
        price_cols = [col for col in features.columns if 'price' in col]
        volume_cols = [col for col in features.columns if 'volume' in col]
        other_cols = [col for col in features.columns if col not in price_cols + volume_cols]
        
        # 标准化价格特征
        if price_cols:
            normalized_df[price_cols] = self.scalers[code]['price'].fit_transform(features[price_cols])
        
        # 标准化量特征
        if volume_cols:
            normalized_df[volume_cols] = self.scalers[code]['volume'].fit_transform(features[volume_cols])
        
        # 标准化其他特征
        if other_cols:
            normalized_df[other_cols] = self.scalers[code]['other'].fit_transform(features[other_cols])
        
        # 恢复时间戳列
        normalized_df['timestamp'] = timestamp
        
        return normalized_df
    
    def create_time_series_samples(self, feature_df, target_horizon=10):
        """
        创建时间序列样本
        
        参数:
            feature_df: 特征DataFrame
            target_horizon: 目标预测时间范围，即预测未来多少个时间点
            
        返回:
            tuple: (X, y) 其中X是特征张量，y是目标张量
        """
        if feature_df is None or len(feature_df) < self.time_steps + target_horizon:
            logger.warning("输入特征不足以创建时间序列样本")
            return None, None
        
        # 分离时间戳列
        features = feature_df.drop('timestamp', axis=1).values
        
        # 创建样本
        X = []
        y = []
        
        for i in range(len(features) - self.time_steps - target_horizon + 1):
            X.append(features[i:i+self.time_steps])
            # 使用中间价格作为预测目标
            mid_price_idx = feature_df.columns.get_loc('mid_price') - 1  # -1是因为我们去掉了timestamp列
            y.append(features[i+self.time_steps:i+self.time_steps+target_horizon, mid_price_idx])
        
        return np.array(X), np.array(y)
    
    def process_file(self, file_path, target_horizon=10):
        """
        处理单个文件
        
        参数:
            file_path: 数据文件路径
            target_horizon: 目标预测时间范围
            
        返回:
            tuple: (X, y) 其中X是特征张量，y是目标张量
        """
        # 加载数据
        df = self.load_orderbook_data(file_path)
        if df is None:
            return None, None
        
        # 提取代码
        code = df['code'].iloc[0] if 'code' in df.columns else os.path.basename(file_path).split('_')[1]
        
        # 提取特征
        feature_df = self.extract_features(df)
        if feature_df is None:
            return None, None
        
        # 归一化特征
        if self.normalize:
            feature_df = self.normalize_features(feature_df, code)
        
        # 创建时间序列样本
        X, y = self.create_time_series_samples(feature_df, target_horizon)
        
        return X, y
    
    def process_directory(self, directory, target_horizon=10, save=True):
        """
        处理目录中的所有文件
        
        参数:
            directory: 数据目录路径
            target_horizon: 目标预测时间范围
            save: 是否保存处理后的数据
            
        返回:
            dict: 包含所有处理后数据的字典
        """
        # 获取目录中的所有parquet文件
        file_paths = glob.glob(os.path.join(directory, '*.parquet'))
        
        if not file_paths:
            logger.warning(f"目录 {directory} 中没有找到parquet文件")
            return {}
        
        # 处理每个文件
        results = {}
        for file_path in file_paths:
            logger.info(f"处理文件 {file_path}")
            
            # 提取代码
            file_name = os.path.basename(file_path)
            code = file_name.split('_')[1]
            
            # 处理文件
            X, y = self.process_file(file_path, target_horizon)
            
            if X is not None and y is not None:
                results[code] = (X, y)
                
                # 保存处理后的数据
                if save and self.save_path:
                    save_dir = os.path.join(self.save_path, 'stock' if 'stock' in file_path else 'future')
                    os.makedirs(save_dir, exist_ok=True)
                    
                    np.save(os.path.join(save_dir, f'{code}_X.npy'), X)
                    np.save(os.path.join(save_dir, f'{code}_y.npy'), y)
                    
                    logger.info(f"保存处理后的数据到 {save_dir}/{code}_X.npy 和 {save_dir}/{code}_y.npy")
        
        return results
    
    def process_all_data(self, target_horizon=10, save=True):
        """
        处理所有数据
        
        参数:
            target_horizon: 目标预测时间范围
            save: 是否保存处理后的数据
            
        返回:
            dict: 包含所有处理后数据的字典
        """
        # 处理股票数据
        stock_dir = os.path.join(self.data_path, 'stock')
        stock_results = self.process_directory(stock_dir, target_horizon, save) if os.path.exists(stock_dir) else {}
        
        # 处理期货数据
        future_dir = os.path.join(self.data_path, 'future')
        future_results = self.process_directory(future_dir, target_horizon, save) if os.path.exists(future_dir) else {}
        
        # 合并结果
        results = {**stock_results, **future_results}
        
        return results
    
    def load_processed_data(self, code, data_type='stock'):
        """
        加载处理后的数据
        
        参数:
            code: 证券代码
            data_type: 数据类型，'stock'或'future'
            
        返回:
            tuple: (X, y) 其中X是特征张量，y是目标张量
        """
        if not self.save_path:
            logger.warning("未设置保存路径，无法加载处理后的数据")
            return None, None
        
        try:
            X_path = os.path.join(self.save_path, data_type, f'{code}_X.npy')
            y_path = os.path.join(self.save_path, data_type, f'{code}_y.npy')
            
            X = np.load(X_path)
            y = np.load(y_path)
            
            logger.info(f"加载处理后的数据 {X_path} 和 {y_path} 成功")
            
            return X, y
        except Exception as e:
            logger.error(f"加载处理后的数据失败: {str(e)}")
            return None, None

if __name__ == "__main__":
    # 示例用法
    processor = OrderBookProcessor(
        data_path="./data/orderbook",
        save_path="./data/processed_orderbook",
        n_levels=5,
        time_steps=100,
        normalize=True,
        diff_features=True
    )
    
    # 处理所有数据
    results = processor.process_all_data(target_horizon=10, save=True)
    
    # 输出处理结果
    for code, (X, y) in results.items():
        logger.info(f"代码 {code}: X.shape={X.shape}, y.shape={y.shape}")
