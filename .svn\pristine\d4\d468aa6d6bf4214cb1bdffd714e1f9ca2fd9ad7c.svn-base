
import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary

from pyqlab.models import PLData
from pyqlab.models import PLGptModel

from pyqlab.data.data_api import get_dataset
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import TimeSeriesSplit, KFold
import os
import random
import numpy as np
from datetime import datetime
from time import time

from pyqlab.data.dataset.pipeline import Pipeline
from pyqlab.data.dataset.dataset_bar import BarDataset
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
from pyqlab.pl.utils import get_best_saved_model_filename

def get_trainer_name(args):
    time_encoding = 'T' if args.time_encoding == 'timeF' else 'F'
    market = args.market.upper()
    block_name = args.block_name.upper()
    period = args.period.upper()
    trainer_name = f'{market}_{args.version}_{block_name}_{period}_{args.block_size}_{args.n_head}_{args.n_layer}'
    return trainer_name

def save_model_as_to_onnx(args):
    trainer_name=get_trainer_name(args)
    model_files = get_best_saved_model_filename(
        log_dir=args.log_dir,
        sub_dir=trainer_name
    )
    if len(model_files) == 0:
        raise Exception("No saved model found!")
    print(f"=== Export best model files ===")
    for model_file, best_score in model_files.items():
        if best_score > args.best_score:
            print(f"Skip model file: {model_file}  {best_score}")
            continue
        else:
            print(f"Export model file: {model_file}  {best_score}")
        try:
            model = PLGptModel.load_from_checkpoint(checkpoint_path=model_file)
            model.freeze()
            code = torch.zeros(1, args.block_size).to(torch.int32)
            x = torch.zeros(1, args.block_size).to(torch.int32)
            x_mark = torch.zeros(1, args.block_size * 5).to(torch.float32)
            x_mark = x_mark.reshape(1, args.block_size, 5)
            tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
            model_name = f"{trainer_name}_{tm_str}_{best_score}_ls"
            model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (code, x, x_mark, None), export_params=True)
            print(f"Model saved to： {args.model_dir}/{model_name}.onnx")
        except Exception as e:
            print(f"Error: {e}")


def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    return callbacks

def get_config():

    C = CN()

    # system
    # C.system = CN()
    # C.system.seed = 3407
    # C.system.model_name = 'BARGPT'
    # C.system.resave_model_onnx = False
    # C.system.init_from = 'scratch'

    # data
    C.data = BarDataset.get_default_config()

    # model
    # C.model = BarGpt.get_default_config()

    return C

def main(args):
    args.seed = random.randint(0, 10000)
    if args.period == 'min5' or args.period == 'min1':
        args.freq = 't'
    elif args.period == 'day':
        args.freq = 'b'
    else:
        raise ValueError(f"Invalid period: {args.period}")
    if args.market == 'fut' and args.block_name == 'sf':
        args.sel_codes = SF_FUT_CODES
    print(args)

    config = get_config()
    # config.system.update_from_dict(vars(args))
    config.data.update_from_dict(vars(args))
    # config.model.update_from_dict(vars(args))
    # config.trainer.update_from_dict(vars(args))
    # set_seed(config.system.seed)

    if args.export_onnx:
        save_model_as_to_onnx(args)
        return

    if args.time_encoding == 'timeF':
        config.data.timeenc = 1
    else:
        config.data.timeenc = 0

    # construct the training dataset
    pipe = Pipeline(
        config.data.data_path,
        config.data.market,
        config.data.block_name,
        config.data.period,
        config.data.start_year,
        config.data.end_year,
        config.data.start_date,
        config.data.end_date,
        config.data.block_size,
        config.data.timeenc,
        config.data.sel_codes
    )
    data = pipe.get_data()

    assert len(data) > 0 and len(data.shape) == 2, 'No data or wrong shape'
    config.data.is_sf = pipe.is_sf
    args.is_sf = pipe.is_sf
    print(f"args.is_sf: {args.is_sf}")

    dataset = BarDataset(config.data, data)

    # construct the model
    # config.model.vocab_size = dataset.get_vocab_size()
    # config.model.block_size = dataset.get_block_size()
    # config.model.code_size = pipe.code_size

    print(config)

    pl.seed_everything(args.seed)

    trainer_name = get_trainer_name(args)
    print(f"Trainer Name: {trainer_name}")

    model=None
    # 定义交叉验证
    # 使用 KFold 分割数据集
    kfold = KFold(n_splits=args.k_folds, shuffle=True, random_state=args.seed)
    # 创建一个数据模块列表，每个数据模块对应一个 fold
    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
    # tscv = TimeSeriesSplit(n_splits=args.k_folds)
    # for fold, (train_idx, val_idx) in enumerate(tscv.split(np.arange(len(dataset)))):
        print(f"=== Training fold {fold} ===")
        train_data = Subset(dataset, train_idx)
        val_data = Subset(dataset, val_idx)
        print(f"Train data: {len(train_data)}, Val data: {len(val_data)}, Total data: {len(dataset)}")
        data_module = PLData(
            train_data,
            val_data,
            batch_size=args.batch_size,
            num_workers=args.num_workers,
            seed=args.seed
        )

        # if fold > 0 and callbacks[0].stopped_epoch is not None:
        #     # 加载之前训练的模型
        #     print(f"Fold: {fold} Loading model from {callbacks[1].best_model_path}")
        #     model = PLGptModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        if model is None:
            if args.resume:
                model_path, _= get_best_saved_model_filename(
                    log_dir=args.log_dir,
                    sub_dir=trainer_name,
                    only_best=True
                )
                if model_path is None:
                    raise Exception("No saved model found!")
                print(f"=== Resume Training ===")
                print(f"Loading model from {model_path}")
                try:
                    model = PLGptModel.load_from_checkpoint(checkpoint_path=model_path)
                except Exception as e:
                    print(f"Error: {e}")
            else:
                print(f"=== New Training ===")
                model = PLGptModel(**vars(args))

        # 创建训练器
        logger = TensorBoardLogger(save_dir=args.log_dir, name=trainer_name)
        # 创建回调函数
        callbacks = load_callbacks(args)
        trainer = Trainer(
            accelerator='auto',
            devices='auto',
            # precision='16-true',
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
        )
        trainer.fit(model, data_module)
        # 在每个数据集的训练之后，执行其他操作（如保存模型、记录日志等）
        # log_metrics(logger, trainer)

    # 训练完成后，保存编译最后一个模型
    if callbacks[0].stopped_epoch is not None:
        # 加载之前训练的模型
        print(f"Best model to save {callbacks[1].best_model_path}")
        best_score=callbacks[1].best_model_score.cpu().numpy()
        model = PLGptModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        # 设置为推理模式
        if hasattr(model.model, 'inference_mode'):
            model.model.inference_mode()
            print("已将模型设置为推理模式")
        else:
            print("模型没有推理模式")
            model.eval()
        code = torch.zeros(1, config.data.block_size).to(torch.int32)
        x = torch.zeros(1, config.data.block_size).to(torch.int32)
        x_mark = torch.zeros(1, config.data.block_size * 5).to(torch.float32)
        x_mark = x_mark.reshape(1, config.data.block_size, 5)
        tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
        model_name = f"{trainer_name}_{tm_str}_%.3f_ls" % best_score
        model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (code, x, x_mark, None), export_params=True)

        #查看模型大小
        model.example_input_array = (code, x, x_mark, None)
        summary = ModelSummary(model, max_depth=-1)
        print(summary) 

        print(f"Model saved to： {model_name}.onnx")
        print("=== Training Finished ===\n\n")

if __name__ == '__main__':
    parser = ArgumentParser()

    # Data module ===========================
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)
    parser.add_argument('--data_path', default='d:/RoboQuant2/store/barenc', type=str, help='path to the data')  
    parser.add_argument('--market', default='fut', choices=['fut', 'stk'], type=str, help='market')
    parser.add_argument('--block_name', default='sf', type=str, help='block name')
    parser.add_argument('--period', default='min5', choices=['day', 'min5', 'min1'], type=str, help='period')
    parser.add_argument('--start_year', default=2024, type=int, help='start year of the data')  
    parser.add_argument('--end_year', default=2024, type=int, help='end year of the data')
    parser.add_argument('--start_date', default='', type=str)
    parser.add_argument('--end_date', default='', type=str)
    parser.add_argument('--block_size', default=20, type=int, help='block size')
    parser.add_argument('--step_size', default=1, type=int, help='step size')
    parser.add_argument('--sel_codes', default=MAIN_SEL_FUT_CODES, type=list, choices=[MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES], help='selected codes')

    # Model Hyperparameters =================
    parser.add_argument('--version', default='GPT', type=str)
    parser.add_argument('--model_name', default='bar_gpt', type=str)
    parser.add_argument('--model_path', default='', type=str)
    parser.add_argument('--loss', default='ce', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)
    parser.add_argument('--n_head', default=20, type=int)
    parser.add_argument('--n_layer', default=3, type=int)
    parser.add_argument('--vocab_size', default=40002, type=int)
    parser.add_argument('--code_size', default=96, type=int)
    parser.add_argument('--d_model', default=256, type=int)
    parser.add_argument('--bias', action='store_false')
    parser.add_argument('--n_experts', default=8, type=int, help='Number of expert networks')
    parser.add_argument('--top_k', default=2, type=int, help='Number of experts to use for each token')

    # model
    parser.add_argument('--dropout', default=0.1, type=float)
    parser.add_argument('--time_encoding', type=str, default='timeF', help='time features encoding, options:[timeF, fixed, learned]')
    parser.add_argument('--freq', type=str, default='t', help='time features encoding, options:[t, b]')
    parser.add_argument('--time_embed_type', type=str, default='time_feature', help='BarGpt4: time embedding type, options:[time_feature, periodic, continuous, adaptive, multi_scale]')
    parser.add_argument('--pos_embed_type', type=str, default='rope', help='BarGpt4: positional embedding type, options:[rope, alibi]')

    # LR SchedulertimeF
    # lr_scheduler 可以有以下选项：
    #     'step'：使用步进式学习率调度器
    #     'cosine'：使用余弦退火学习率调度器
    #     'plateau'：使用学习率衰减调度器
    #     'exponential'：使用指数衰减学习率调度器
    #     'reduce_on_plateau'：使用学习率衰减调度器，并在验证集上监测指标不再改善时降低学习率
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)
    parser.add_argument('--weight_decay', default=0.0, type=float)
    parser.add_argument('--optimizer', default='adamw', choices=['adam', 'adamw'], type=str)

    # Restart Control
    parser.add_argument('--resume', action='store_true', help='resume training from the last checkpoint')

    # Training Info
    parser.add_argument('--max_epochs', default=10, type=int)
    parser.add_argument('--early_stop', default=5, type=int)
    parser.add_argument('--min_delta', default=1e-2, type=float) 
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    
    # Others
    parser.add_argument('--model_dir', default='model', type=str)

    parser.add_argument('--export_onnx', action='store_true')
    parser.add_argument('--best_score', default=0.6, type=float)
    parser.add_argument('--is_sf', action='store_true')

    args = parser.parse_args()

    main(args)


