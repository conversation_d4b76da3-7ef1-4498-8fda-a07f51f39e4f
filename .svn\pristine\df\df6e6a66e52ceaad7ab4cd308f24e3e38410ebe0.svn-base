from pprint import pprint
# from config import DQN_PARAMS, RLlib_PARAMS

# construct environment
# from rllab.env.env_futtrading import FutureTradingEnv
from rllab.env.env_futures_discrete import FuturesDiscEnv

import ray
from ray import tune

import ray
import ray.rllib.agents.ppo as ppo
from ray.tune.logger import pretty_print

ray.init()
config = ppo.DEFAULT_CONFIG.copy()
config["num_gpus"] = 0
config["num_workers"] = 1
trainer = ppo.PPOTrainer(config=config, env="CartPole-v0")

# Can optionally call trainer.restore(path) to load a checkpoint.

for i in range(1000):
   # Perform one iteration of training the policy with PPO
   result = trainer.train()
   print(pretty_print(result))

   if i % 100 == 0:
       checkpoint = trainer.save()
       print("checkpoint saved at", checkpoint)

# Also, in case you have trained a model outside of ray/RLlib and have created
# an h5-file with weight values in it, e.g.
# my_keras_model_trained_outside_rllib.save_weights("model.h5")
# (see: https://keras.io/models/about-keras-models/)

# ... you can load the h5-weights into your Trainer's Policy's ModelV2
# (tf or torch) by doing:
trainer.import_model("my_weights.h5")
# NOTE: In order for this to work, your (custom) model needs to implement
# the `import_from_h5` method.
# See https://github.com/ray-project/ray/blob/master/rllib/tests/test_model_imports.py
# for detailed examples for tf- and torch trainers/models.


ray.init()
tune.run(
    "PPO",
    stop={"episode_reward_mean": 200},
    config={
        "env": "CartPole-v0",
        "num_gpus": 0,
        "num_workers": 1,
        "lr": tune.grid_search([0.01, 0.001, 0.0001]),
    },
)


# tune.run() allows setting a custom log directory (other than ``~/ray-results``)
# and automatically saving the trained agent
log_dir='./log_dir'
analysis = ray.tune.run(
    ppo.PPOTrainer,
    config=config,
    local_dir=log_dir,
    stop=stop_criteria,
    checkpoint_at_end=True)

# list of lists: one list per checkpoint; each checkpoint list contains
# 1st the path, 2nd the metric value
checkpoints = analysis.get_trial_checkpoints_paths(
    trial=analysis.get_best_trial("episode_reward_mean"),
    metric="episode_reward_mean")

# or simply get the last checkpoint (with highest "training_iteration")
last_checkpoint = analysis.get_last_checkpoint()
# if there are multiple trials, select a specific trial or automatically
# choose the best one according to a given metric
last_checkpoint = analysis.get_last_checkpoint(
    metric="episode_reward_mean", mode="max"
)

agent = ppo.PPOTrainer(config=config, env=env_class)
agent.restore(checkpoint_path)


#=================================================================
def train(
        drl_lib,
        env,
        model_name,
        **kwargs
):
    # load train data
    # env init
    # env_config = {
    #     "mode": "train",
    # }
    # env_instance = env()

    # env_args = get_gym_env_args(env_instance, True)

    # read parameters
    agent_path = kwargs.get("agent_path", "./" + str(model_name))

    if drl_lib == "rllib":
        total_episodes = kwargs.get("total_episodes", 50)
        rllib_params = kwargs.get("rllib_params")
        from rllab.agents.rllib.models import DRLAgent as DRLAgent_rllib
        agent_rllib = DRLAgent_rllib(env=env)

        model, model_config = agent_rllib.get_model(model_name)

        model_config["lr"] = rllib_params["lr"]
        model_config["train_batch_size"] = rllib_params["train_batch_size"]
        model_config["gamma"] = rllib_params["gamma"]
        model_config["env_config"] = rllib_params["env_config"]
        # pprint(model_config)

        # ray.shutdown()
        trained_model = agent_rllib.train_model(
            model=model,
            model_name=model_name,
            model_config=model_config,
            total_episodes=total_episodes,
            agent_path=agent_path,
            reload=True # 是否继续之前的训练
        )

    elif drl_lib == "stable_baselines3":
        total_timesteps = kwargs.get("total_timesteps", 1e6)
        agent_params = kwargs.get("agent_params")
        from rllab.agents.stablebaselines3.models import DRLAgent as DRLAgent_sb3
        agent = DRLAgent_sb3(env=env_instance)

        model = agent.get_model(model_name, model_kwargs=agent_params)
        trained_model = agent.train_model(
            model=model, tb_log_name=model_name, total_timesteps=total_timesteps
        )
        print("Training finished!")
        trained_model.save(cwd)
        print("Trained model saved in " + str(cwd))
    else:
        raise ValueError("DRL library input is NOT supported. Please check.")


if __name__ == "__main__":

    Env_PARAMS = {
        "name": "FuturesDiscEnv_v1",
        "initial_amount": 1e5,
        "gamma": 0.95,
        "direct": "short",
        "mode": "train",
    }
    RLlib_PARAMS = {
        "lr": 5e-5,
        "train_batch_size": 128,
        "gamma": 0.99,
        "env_config": Env_PARAMS
    }

    env = FuturesDiscEnv

    # demo for rllib
    import ray
    ray.shutdown()  # always shutdown previous session if any
    train(
        drl_lib="rllib",
        env=env,
        model_name="ppo",
        agent_path="./model_rl/FuturesDiscEnv_opp_" + Env_PARAMS["direct"],
        rllib_params=RLlib_PARAMS,
        total_episodes=50,
    )

    # demo for stable-baselines3
    # train(
    #     drl_lib="stable_baselines3",
    #     env=env,
    #     model_name="dqn",
    #     cwd="./test_dqn",
    #     agent_params=DQN_PARAMS,
    #     total_timesteps=1e5,
    # )
