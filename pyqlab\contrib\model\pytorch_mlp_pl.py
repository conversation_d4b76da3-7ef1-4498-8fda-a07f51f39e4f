# %%
from copy import copy
import pandas as pd
import numpy as np
# import json

# import random
from torch._C import device
from tqdm import tqdm

import matplotlib.pyplot as plt

import torch
import torch.nn as nn
import torch.nn.functional as F
# import torch.optim as optim
# from torch.optim import lr_scheduler
# from fastai.layers import SigmoidRange
from torch.optim import Adam
from torch.optim.lr_scheduler import ExponentialLR
import pytorch_lightning as pl
import ipywidgets as widgets


# 创建模型
class AICMBaseModel(pl.LightningModule):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.save_hyperparameters()
        self.loss_df = pd.DataFrame()
        self.accuracy_df = pd.DataFrame()

    # 定义计算正确率函数
    def _accuracy(self, out, yb):
        preds = (out>0.5).type(torch.IntTensor).cuda()
        return (preds == yb).float().mean()

    def _common_step(self, batch, stage):
        code_encoded, x, y = batch
        y_pred = self(code_encoded, x)
        # loss = F.binary_cross_entropy(y_pred, y)
        loss = F.binary_cross_entropy_with_logits(y_pred, y)
        acc = self._accuracy(y_pred, y)
        self.log(f'{stage}/loss', loss.item(), prog_bar=True, on_step=True, on_epoch=False)
        self.log(f'{stage}/accuracy', acc.item(), prog_bar=True, on_step=True, on_epoch=False)
        return {
            'loss': loss,
            'accuracy': acc
        }

    def training_step(self, batch, batch_idx):
        return self._common_step(batch, 'train')

    def validation_step(self, batch, batch_idx):
        return self._common_step(batch, 'valid')

    def _common_epoch_end(self, outputs, stage):
        loss = np.mean([x['loss'].item() for x in outputs])
        acc = np.mean([x['accuracy'].item() for x in outputs])
        # print(loss, acc)
        self.log(f'{stage}/loss', loss.item(), prog_bar=True, on_step=False, on_epoch=True)
        self.log(f'{stage}/accuracy', acc.item(), prog_bar=True, on_step=False, on_epoch=True)
        self.loss_df.loc[self.trainer.current_epoch, f'{stage}/loss'] = loss
        self.accuracy_df.loc[self.trainer.current_epoch, f'{stage}/accuracy'] = acc
        
    def training_epoch_end(self, outputs):
        self._common_epoch_end(outputs, 'train')

        self.loss_widget.clear_output(wait=True)
        with self.loss_widget:
            ylim = [self.loss_df.min().min(), self.loss_df.quantile(0.95).max()]
            ylim[0] -= (ylim[1] - ylim[0]) * 0.05
            self.loss_df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim)
            plt.show()            

        self.accuracy_widget.clear_output(wait=True)
        with self.accuracy_widget:
            ylim = [self.accuracy_df.min().min(), self.accuracy_df.quantile(0.95).max()]
            ylim[0] -= (ylim[1] - ylim[0]) * 0.05
            self.accuracy_df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim)
            plt.show()            

    def validation_epoch_end(self, outputs):
        self._common_epoch_end(outputs, 'valid')

    def on_fit_start(self):
        self.loss_widget = widgets.Output()
        display(self.loss_widget)
        self.accuracy_widget = widgets.Output()
        display(self.accuracy_widget)

    def configure_optimizers(self):
        opt = Adam(self.parameters(), lr=0.0005)
        sched = {
            'scheduler': ExponentialLR(opt, 0.95), 
            'interval': 'epoch'
        }
        return [opt], [sched]
        

class MLPModel(AICMBaseModel): # nn.Module
    '''多层感知机模型'''
    def __init__(self, code_num, input_num, bn=True, dropout=True):

        super(MLPModel, self).__init__()
        
        self.code_embeddings = nn.Embedding(num_embeddings=code_num, embedding_dim=1)
        self.bn = bn
        self.dropout = dropout
        self.lin_1 = nn.Linear(input_num + 1, 96)
        self.bn_1 = nn.BatchNorm1d(96)
        self.lin_2 = nn.Linear(96, 96)
        self.bn_2 = nn.BatchNorm1d(96)
        self.lin_3 = nn.Linear(96, 96)
        self.bn_3 = nn.BatchNorm1d(96)
        self.lin_4 = nn.Linear(96, 1, bias=True)
        self.drop = nn.Dropout(0.05)
        self.activate = nn.ReLU()
        self.sigmoid = nn.Sigmoid()


    def forward(self, code_ids, input):
        '''
        注意:
        模型不能这样写:self.bn_1(F.dropout(F.relu(self.lin_1(input))))
        模型层嵌套写法的问题，dropout在模型的train时执行，在eval时不执行
        Dropout:放在全连接层防止过拟合，一般放在激活函数层之后
        BatchNorm:归一化放在激活层前后好像都有，最初放在了
        激活层池化层后面，而现在普遍放在激活层前。
        '''
        # input layer
        # 加入code向量
        if len(code_ids.shape) == 2:
            code_ids = code_ids.squeeze(1)
        embedded_code_ids = self.code_embeddings(code_ids)
        input = torch.cat([input, embedded_code_ids], dim=1)
        x = self.lin_1(input)
        # print(x.shape)
        if self.bn:
            x = self.bn_1(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer1
        x = self.lin_2(x)
        if self.bn:
            x = self.bn_2(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer2
        x = self.lin_3(x)
        if self.bn:
            x = self.bn_3(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # out layer
        x = self.lin_4(x)
        x = self.sigmoid(x)
        return x


# %%
