@echo off
REM 使用交叉验证训练K线LLM模型

REM 设置Python路径
set PYTHONPATH=%PYTHONPATH%;%~dp0

REM 设置数据路径
set DATA_PATH=data\klines

REM 设置模型参数
set MODEL_TYPE=advanced
set VOCAB_SIZE=512
set N_LAYER=6
set N_HEAD=8
set N_EMBD=256
set DROPOUT=0.1
set TOKENIZER_TYPE=nonlinear

REM 设置交叉验证参数
set N_SPLITS=5
set CV_TYPE=kfold
set SEQ_LEN=30
set BATCH_SIZE=32
set LEARNING_RATE=5e-4
set MAX_EPOCHS=10
set CHECKPOINT_DIR=checkpoints\candlestick_llm_cv

REM 创建检查点目录
if not exist %CHECKPOINT_DIR% mkdir %CHECKPOINT_DIR%

REM 运行训练脚本
python examples\train_candlestick_llm_cv.py ^
    --data_path %DATA_PATH% ^
    --model_type %MODEL_TYPE% ^
    --vocab_size %VOCAB_SIZE% ^
    --n_layer %N_LAYER% ^
    --n_head %N_HEAD% ^
    --n_embd %N_EMBD% ^
    --dropout %DROPOUT% ^
    --tokenizer_type %TOKENIZER_TYPE% ^
    --n_splits %N_SPLITS% ^
    --cv_type %CV_TYPE% ^
    --seq_len %SEQ_LEN% ^
    --batch_size %BATCH_SIZE% ^
    --learning_rate %LEARNING_RATE% ^
    --max_epochs %MAX_EPOCHS% ^
    --checkpoint_dir %CHECKPOINT_DIR% ^
    --save_best_models ^
    --ensemble_prediction

echo 训练完成
pause
