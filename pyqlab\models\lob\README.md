# 增强版DeepLOB模型

这是一个基于股票期货orderbook数据的深度学习预测模型，结合了原始DeepLOB的CNN架构、Transformer的自注意力机制和LSTM的时序建模能力，用于预测未来价格走势。

## 模型架构

增强版DeepLOB模型包含以下组件：

1. **卷积层**：用于提取orderbook数据的空间特征
2. **Inception模块**：用于提取多尺度特征
3. **Transformer层**：使用自注意力机制捕捉不同价格档位之间的关系
4. **LSTM/GRU层**：捕捉时序特征
5. **证券代码嵌入**：可选功能，用于区分不同证券的特性
6. **概率预测**：可选功能，输出预测的均值和方差，提供不确定性估计

## 文件结构

- `enhanced_deeplob.py`: 模型定义
- `train_enhanced_deeplob.py`: 训练脚本
- `predict_enhanced_deeplob.py`: 预测脚本
- `../../data/orderbook_collector.py`: 数据收集器，用于从pytdx获取orderbook数据
- `../../data/orderbook_processor.py`: 数据处理器，用于预处理orderbook数据

## 数据收集

使用`orderbook_collector.py`从pytdx获取orderbook数据：

```bash
# 收集股票orderbook数据
python -m pyqlab.data.orderbook_collector --market 0 --code 000001 --interval 1 --duration 3600 --save_path ./data/orderbook

# 收集期货orderbook数据
python -m pyqlab.data.orderbook_collector --market_id 47 --code IF2306 --interval 1 --duration 3600 --save_path ./data/orderbook --data_type future
```

## 数据处理

使用`orderbook_processor.py`处理orderbook数据：

```bash
python -m pyqlab.data.orderbook_processor --data_path ./data/orderbook --save_path ./data/processed_orderbook --n_levels 5 --time_steps 100 --normalize --diff_features
```

## 模型训练

使用`train_enhanced_deeplob.py`训练模型：

```bash
python -m pyqlab.models.lob.train_enhanced_deeplob \
    --data_path ./data/processed_orderbook \
    --code 000001 \
    --data_type stock \
    --input_channels 1 \
    --time_steps 100 \
    --num_features 40 \
    --output_size 1 \
    --horizon 10 \
    --dropout 0.1 \
    --use_transformer \
    --num_transformer_layers 2 \
    --num_heads 4 \
    --head_dim 16 \
    --ff_dim 256 \
    --lstm_hidden_size 64 \
    --lstm_layers 1 \
    --use_code_embedding \
    --num_codes 100 \
    --code_embedding_dim 16 \
    --batch_size 32 \
    --epochs 50 \
    --lr 0.001 \
    --weight_decay 1e-5 \
    --patience 10 \
    --save_dir ./models/saved
```

## 模型预测

使用`predict_enhanced_deeplob.py`进行预测：

```bash
python -m pyqlab.models.lob.predict_enhanced_deeplob \
    --data_path ./data/orderbook \
    --processed_data_path ./data/processed_orderbook \
    --code 000001 \
    --market 0 \
    --data_type stock \
    --model_path ./models/saved/000001_model.pth \
    --collect_data \
    --interval 1 \
    --duration 60 \
    --save_dir ./predictions
```

## 概率预测

要启用概率预测，在训练和预测时添加`--probabilistic`参数：

```bash
# 训练
python -m pyqlab.models.lob.train_enhanced_deeplob --probabilistic ...

# 预测
python -m pyqlab.models.lob.predict_enhanced_deeplob --model_path ./models/saved/000001_model_prob.pth ...
```

## 使用GRU代替LSTM

要使用GRU代替LSTM，在训练时添加`--use_gru`参数：

```bash
python -m pyqlab.models.lob.train_enhanced_deeplob --use_gru ...
```

## 模型参数说明

- `input_channels`: 输入通道数，默认为1
- `time_steps`: 时间步长，即每个样本包含多少个时间点，默认为100
- `num_features`: 每个时间点的特征数，默认为40
- `output_size`: 输出维度，默认为1（中间价格）
- `horizon`: 预测时间范围，即预测未来多少个时间点，默认为10
- `dropout`: Dropout比率，默认为0.1
- `use_transformer`: 是否使用Transformer
- `num_transformer_layers`: Transformer层数，默认为2
- `num_heads`: 注意力头数，默认为4
- `head_dim`: 每个头的维度，默认为16
- `ff_dim`: 前馈网络维度，默认为256
- `lstm_hidden_size`: LSTM隐藏层大小，默认为64
- `lstm_layers`: LSTM层数，默认为1
- `use_gru`: 是否使用GRU代替LSTM
- `use_code_embedding`: 是否使用证券代码嵌入
- `num_codes`: 证券代码数量，默认为100
- `code_embedding_dim`: 证券代码嵌入维度，默认为16
- `probabilistic`: 是否进行概率预测

## 依赖库

- PyTorch
- NumPy
- Pandas
- Matplotlib
- scikit-learn
- tqdm
- pytdx

## 注意事项

1. 确保已安装pytdx库，并且可以连接到行情服务器
2. 对于股票市场，market参数为0（深圳）或1（上海）
3. 对于期货市场，market_id参数根据pytdx的市场ID设置，如47表示股指期货
4. 模型训练可能需要较长时间，建议使用GPU加速
5. 预测结果包括未来多个时间点的预测值，以及可选的不确定性估计
