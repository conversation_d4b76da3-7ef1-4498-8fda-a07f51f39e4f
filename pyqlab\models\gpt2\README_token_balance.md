# CandlestickVQGPT Token平衡解决方案

## 问题描述

在CandlestickVQGPT模型训练中，经常出现token分布不均衡的问题，特别是某些token（如795）出现频率过高，导致：

1. **模型总是预测高频token**：模型倾向于预测训练数据中最常见的token
2. **预测多样性差**：生成的预测缺乏多样性，大部分预测都是相同的token
3. **收敛困难**：损失函数难以收敛，或收敛到局部最优
4. **泛化能力差**：模型在测试集上表现不佳

## 解决方案概述

本解决方案提供了一套完整的工具来分析和解决token不平衡问题：

### 1. 核心改进

- **平衡损失函数**：结合Focal Loss和加权交叉熵
- **多样性正则化**：增强预测多样性的辅助损失
- **类别权重**：自动计算并应用类别权重
- **预测监控**：实时监控预测多样性

### 2. 工具文件

- `candlestick_vq_gpt.py`：改进的模型，支持平衡训练
- `analyze_candlestick_token_balance.py`：token分布分析工具
- `train_balanced_candlestick_vq_gpt.py`：平衡训练脚本
- `example_fix_token_imbalance.py`：使用示例

## 使用指南

### 步骤1: 分析token分布

首先分析您的训练数据中的token分布：

```bash
python pyqlab/models/gpt2/analyze_candlestick_token_balance.py \
    --data_path /path/to/your/kline/data \
    --codebook_path /path/to/your/codebook.pt \
    --output_dir ./token_analysis
```

这将生成：
- `token_distribution_analysis.png`：详细的分布图表
- `balance_solutions.txt`：针对性的解决方案建议
- `class_weights.pt`：计算好的类别权重

### 步骤2: 查看分析结果

检查生成的分析报告：

```
=== Token分布分析 ===
总token数: 1,000,000
唯一token数: 856
词汇表利用率: 83.59%
高频token数量: 5

最常见的tokens:
  Token 795: 350,000次 (35.00%) 🚨 严重不平衡
  Token 100: 120,000次 (12.00%) ⚡ 轻度不平衡
  Token 200: 80,000次 (8.00%)
```

### 步骤3: 应用解决方案

根据分析结果，使用平衡训练：

```python
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt.token_balance_utils import TokenBalancer

# 1. 加载类别权重
class_weights = torch.load("./token_analysis/class_weights.pt")

# 2. 创建模型（已包含平衡损失）
model = CandlestickVQGPT(
    vocab_size=1024,
    code_size=100,
    seq_len=30,
    n_layer=4,
    n_head=8,
    d_model=128,
    dropout=0.15,  # 增加dropout
    label_smoothing=0.05,  # 减少标签平滑
    use_auxiliary_loss=True  # 启用多样性损失
)

# 3. 训练时传入类别权重
for batch in dataloader:
    input_tokens, code_ids, targets = batch
    
    logits, loss = model(
        input_tokens=input_tokens,
        code_ids=code_ids,
        targets=targets,
        class_weights=class_weights  # 关键：传入类别权重
    )
    
    loss.backward()
    optimizer.step()
```

### 步骤4: 监控训练效果

在训练过程中监控预测多样性：

```python
# 每100步检查一次预测多样性
if step % 100 == 0:
    with torch.no_grad():
        preds = logits.argmax(dim=-1)
        unique_preds = len(set(preds[:, -1].tolist()))
        total_preds = len(preds)
        diversity = unique_preds / total_preds
        print(f"预测多样性: {diversity:.2%}")
        
        # 检查是否还有高频预测
        pred_counter = Counter(preds[:, -1].tolist())
        most_common = pred_counter.most_common(1)[0]
        max_freq = most_common[1] / total_preds
        if max_freq > 0.3:
            print(f"⚠️ Token {most_common[0]} 预测频率过高: {max_freq:.2%}")
```

## 核心技术细节

### 1. 平衡损失函数

```python
def _compute_balanced_loss(self, logits, targets, class_weights=None):
    # 1. 加权交叉熵损失
    if class_weights is not None:
        loss = F.cross_entropy(
            valid_logits, 
            valid_targets, 
            weight=class_weights.to(logits.device),
            label_smoothing=self.label_smoothing * 0.3
        )
    
    # 2. Focal Loss处理类别不平衡
    alpha = 0.25
    gamma = 2.0
    ce_loss = F.cross_entropy(valid_logits, valid_targets, reduction='none')
    pt = torch.exp(-ce_loss)
    focal_loss = alpha * (1 - pt) ** gamma * ce_loss
    
    # 3. 组合损失
    combined_loss = 0.7 * loss + 0.3 * focal_loss.mean()
    return combined_loss
```

### 2. 多样性正则化

```python
def _compute_auxiliary_loss(self, logits, targets):
    # 1. 预测多样性损失
    pred_tokens = valid_logits.argmax(dim=-1)
    unique_preds = torch.unique(pred_tokens).numel()
    diversity_ratio = unique_preds / pred_tokens.numel()
    
    if diversity_ratio < 0.4:  # 期望至少40%的预测是不同的
        diversity_penalty = (0.4 - diversity_ratio) * 3.0
        aux_loss += diversity_penalty
    
    # 2. 熵正则化
    probs = F.softmax(valid_logits, dim=-1)
    entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1).mean()
    target_entropy = torch.log(torch.tensor(self.vocab_size / 3.0))
    entropy_loss = F.mse_loss(entropy, target_entropy) * 0.15
    
    # 3. 基尼系数惩罚
    pred_freqs = torch.bincount(pred_tokens, minlength=self.vocab_size).float()
    pred_freqs = pred_freqs / pred_freqs.sum()
    # 计算基尼系数并惩罚不均匀分布
    
    return aux_loss
```

### 3. 类别权重计算

```python
# 平方根逆频率权重（推荐）
weights[token_id] = np.sqrt(total_samples / (count + smooth_factor))

# 其他选项：
# 逆频率权重：weights[token_id] = total_samples / (count + smooth_factor)
# 对数逆频率权重：weights[token_id] = np.log(total_samples / (count + smooth_factor) + 1)
```

## 配置建议

根据token分布的不平衡程度，推荐以下配置：

### 严重不平衡（基尼系数 > 0.8）
```python
TRAINING_CONFIG = {
    'learning_rate': 3e-5,
    'dropout': 0.15,
    'label_smoothing': 0.05,
    'focal_gamma': 2.0,
    'diversity_weight': 0.2,
    'use_class_weights': True,
    'use_balanced_sampling': True
}
```

### 中度不平衡（基尼系数 0.6-0.8）
```python
TRAINING_CONFIG = {
    'learning_rate': 5e-5,
    'dropout': 0.12,
    'label_smoothing': 0.03,
    'focal_gamma': 1.5,
    'diversity_weight': 0.1,
    'use_class_weights': True
}
```

### 轻度不平衡（基尼系数 < 0.6）
```python
TRAINING_CONFIG = {
    'learning_rate': 1e-4,
    'dropout': 0.1,
    'label_smoothing': 0.02,
    'diversity_weight': 0.05
}
```

## 效果验证

训练完成后，验证解决效果：

1. **预测多样性**：应该 > 40%
2. **最高频token**：预测频率应该 < 30%
3. **基尼系数**：预测分布的基尼系数应该 < 0.6
4. **验证损失**：应该稳定下降并收敛

## 常见问题

### Q: 为什么使用平方根逆频率权重？
A: 平方根逆频率权重比完全逆频率权重更温和，避免过度惩罚高频token，同时有效提升低频token的重要性。

### Q: 多样性正则化会影响模型性能吗？
A: 适度的多样性正则化（权重0.1-0.2）通常会提升模型的泛化能力，但过高的权重可能影响收敛。

### Q: 如何判断token不平衡是否已解决？
A: 主要看三个指标：预测多样性>40%，最高频预测<30%，验证集上的性能提升。

## 运行示例

查看完整的使用示例：

```bash
python pyqlab/models/gpt2/example_fix_token_imbalance.py
```

这将演示完整的问题识别、分析和解决流程。
