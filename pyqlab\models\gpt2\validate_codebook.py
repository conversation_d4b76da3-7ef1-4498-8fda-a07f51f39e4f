"""
验证码本的有效性，检查通过token还原的K线数据是否准确保留了原始数据的关键特性。
"""

import os
import argparse
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import onnxruntime as ort
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.utils import load_single_data


def validate_codebook(tokenizer, df_data, num_samples=20, save_dir=None, prefix=""):
    """
    验证码本的有效性，检查通过token还原的K线数据是否准确保留了原始数据的关键特性。

    Args:
        tokenizer: CandlestickVQTokenizer实例
        df_data: 原始K线数据
        num_samples: 要验证的样本数量
        save_dir: 保存结果的目录
        prefix: 文件名前缀

    Returns:
        dict: 验证结果
    """
    # 确保数据足够
    if len(df_data) < num_samples + tokenizer.atr_period:
        print(f"Warning: Not enough data for validation. Need at least {num_samples + tokenizer.atr_period + 1} candles.")
        num_samples = max(5, len(df_data) - tokenizer.atr_period)

    # 选择要验证的数据
    df_subset = df_data.iloc[:num_samples + tokenizer.atr_period].copy()

    # 预处理数据
    processed_df = tokenizer._preprocess_df(df_subset)

    # 收集向量化和量化数据
    original_vectors = []
    quantized_vectors = []
    token_ids = []
    valid_indices = []

    # 从预处理后的数据中提取有效的K线
    valid_data = []

    for i in range(len(processed_df)):
        row = processed_df.iloc[i]
        if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
            continue

        # 检测跳空
        gap_token_id = tokenizer._detect_gap(row)
        if gap_token_id is not None:
            token_ids.append(gap_token_id)

        # 向量化
        from pyqlab.models.gpt2.vq_tokenizer import candlestick_to_vector
        vector = candlestick_to_vector(
            row,
            row['Prev_Close'],
            row['MA_Volume'],
            row['ATR'],
            tokenizer.embedding_dim,
            tokenizer.vectorization_method
        )

        if np.all(vector == 0):
            token_ids.append(tokenizer.unk_token_id)
            continue

        original_vectors.append(vector)

        # 量化
        vector_tensor = torch.tensor(vector, dtype=torch.float32)
        with torch.no_grad():
            token_id = tokenizer.vq_layer(vector_tensor).item()

        token_ids.append(token_id)

        # 获取量化后的向量
        if token_id < tokenizer.num_embeddings:
            quantized_vector = tokenizer.get_quantized_vector(token_id)
            quantized_vectors.append(quantized_vector)
            valid_indices.append(i)
            valid_data.append(row)

    # 解码
    df_reconstructed = tokenizer.tokens_to_candlesticks(token_ids, df_subset)

    # 计算原始数据和重建数据的差异
    results = {}

    if len(valid_data) > 0 and len(df_reconstructed) > 0:
        # 确保两个DataFrame有相同的索引
        min_len = min(len(df_reconstructed), len(valid_data))

        # 提取价格数据
        orig_prices = pd.DataFrame([{
            'open': row['open'],
            'high': row['high'],
            'low': row['low'],
            'close': row['close']
        } for row in valid_data[:min_len]])

        recon_prices = df_reconstructed[['open', 'high', 'low', 'close']].iloc[:min_len]

        # 计算价格差异
        price_diff = np.abs(orig_prices.values - recon_prices.values)
        mean_price_diff = np.mean(price_diff)

        # 计算相对差异百分比
        orig_mean_price = np.mean(orig_prices.values)
        relative_diff_pct = mean_price_diff / orig_mean_price * 100

        # 检查最高价和最低价的正确性
        high_errors = 0
        low_errors = 0

        for i in range(min_len):
            orig_row = orig_prices.iloc[i]
            recon_row = recon_prices.iloc[i]

            # 检查最高价是否是最高点
            if not (recon_row['high'] >= recon_row['open'] and
                    recon_row['high'] >= recon_row['close']):
                high_errors += 1

            # 检查最低价是否是最低点
            if not (recon_row['low'] <= recon_row['open'] and
                    recon_row['low'] <= recon_row['close']):
                low_errors += 1

        # 计算向量差异
        if len(original_vectors) == len(quantized_vectors):
            original_vectors_array = np.array(original_vectors)
            quantized_vectors_array = np.array(quantized_vectors)

            vector_diff = np.abs(original_vectors_array - quantized_vectors_array)
            mean_vector_diff = np.mean(vector_diff)

            results['vector_diff'] = mean_vector_diff

        # 保存结果
        results['price_diff'] = mean_price_diff
        results['relative_diff_pct'] = relative_diff_pct
        results['high_errors'] = high_errors
        results['low_errors'] = low_errors
        results['sample_count'] = min_len

        # 保存验证结果
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)

            # 保存文本结果
            result_path = os.path.join(save_dir, f"{prefix}codebook_validation_{tokenizer.vectorization_method}.txt")
            with open(result_path, "w") as f:
                f.write(f"验证结果 ({tokenizer.vectorization_method}):\n")
                f.write(f"样本数量: {min_len}\n")
                f.write(f"价格平均差异: {mean_price_diff:.6f}\n")
                f.write(f"相对差异百分比: {relative_diff_pct:.2f}%\n")
                f.write(f"最高价错误数: {high_errors}/{min_len} ({high_errors/min_len*100:.2f}%)\n")
                f.write(f"最低价错误数: {low_errors}/{min_len} ({low_errors/min_len*100:.2f}%)\n")

                if 'vector_diff' in results:
                    f.write(f"向量平均差异: {results['vector_diff']:.6f}\n")

                # 添加详细的样本比较
                f.write("\n详细样本比较:\n")
                for i in range(min_len):
                    orig_row = orig_prices.iloc[i]
                    recon_row = recon_prices.iloc[i]

                    f.write(f"\n样本 {i}:\n")
                    f.write(f"原始: O={orig_row['open']:.2f}, H={orig_row['high']:.2f}, L={orig_row['low']:.2f}, C={orig_row['close']:.2f}\n")
                    f.write(f"重建: O={recon_row['open']:.2f}, H={recon_row['high']:.2f}, L={recon_row['low']:.2f}, C={recon_row['close']:.2f}\n")

                    diff = np.abs(orig_row.values - recon_row.values)
                    f.write(f"差异: {diff}\n")
                    f.write(f"平均差异: {np.mean(diff):.6f}\n")

            print(f"验证结果已保存到 {result_path}")

            # 可视化结果
            fig = plt.figure(figsize=(20, 15))
            gs = fig.add_gridspec(3, 1, height_ratios=[3, 3, 2])

            # 绘制原始K线
            ax_orig = fig.add_subplot(gs[0, :])
            tokenizer._plot_candlesticks(ax_orig, pd.DataFrame(valid_data[:min_len]),
                                       title=f"Original Candlesticks ({tokenizer.vectorization_method})")

            # 绘制重建的K线
            ax_recon = fig.add_subplot(gs[1, :], sharex=ax_orig)
            tokenizer._plot_candlesticks(ax_recon, df_reconstructed.iloc[:min_len],
                                       title="Reconstructed Candlesticks")

            # 确保两个K线图的Y轴范围一致
            orig_ymin, orig_ymax = ax_orig.get_ylim()
            recon_ymin, recon_ymax = ax_recon.get_ylim()

            # 使用共同的Y轴范围
            ymin = min(orig_ymin, recon_ymin)
            ymax = max(orig_ymax, recon_ymax)

            # 设置相同的Y轴范围
            ax_orig.set_ylim(ymin, ymax)
            ax_recon.set_ylim(ymin, ymax)

            # 绘制差异
            ax_diff = fig.add_subplot(gs[2, :])
            diff_values = np.mean(price_diff, axis=1)
            ax_diff.bar(range(len(diff_values)), diff_values, color='skyblue')
            ax_diff.set_title("Mean Absolute Error Between Original and Reconstructed Prices")
            ax_diff.set_xlabel("Candle Index")
            ax_diff.set_ylabel("Mean Absolute Error")
            ax_diff.grid(True, alpha=0.3)

            # 保存图形
            fig_path = os.path.join(save_dir, f"{prefix}codebook_validation_{tokenizer.vectorization_method}.png")
            plt.tight_layout()
            plt.savefig(fig_path)
            plt.close()

            print(f"验证图形已保存到 {fig_path}")

    return results


def validate_onnx_codebook(encoder_onnx_path, decoder_onnx_path, df_data, vectorization_method,
                          num_samples=20, save_dir=None, prefix="onnx_"):
    """
    验证ONNX格式的码本模型。

    Args:
        encoder_onnx_path: 编码器ONNX模型路径
        decoder_onnx_path: 解码器ONNX模型路径
        df_data: 原始K线数据
        vectorization_method: 向量化方法
        num_samples: 要验证的样本数量
        save_dir: 保存结果的目录
        prefix: 文件名前缀

    Returns:
        dict: 验证结果
    """
    # 创建一个临时的tokenizer用于数据预处理
    tokenizer = CandlestickVQTokenizer(
        vectorization_method=vectorization_method
    )

    # 确保数据足够
    if len(df_data) < num_samples + tokenizer.atr_period:
        print(f"Warning: Not enough data for validation. Need at least {num_samples + tokenizer.atr_period + 1} candles.")
        num_samples = max(5, len(df_data) - tokenizer.atr_period)

    # 选择要验证的数据
    df_subset = df_data.iloc[:num_samples + tokenizer.atr_period].copy()

    # 预处理数据
    processed_df = tokenizer._preprocess_df(df_subset)

    # 加载ONNX模型
    try:
        # 创建ONNX运行时会话
        encoder_session = ort.InferenceSession(encoder_onnx_path)
        decoder_session = ort.InferenceSession(decoder_onnx_path)

        # 获取输入名称
        encoder_input_name = encoder_session.get_inputs()[0].name
        decoder_input_name = decoder_session.get_inputs()[0].name

        # 收集向量化和量化数据
        original_vectors = []
        quantized_vectors = []
        token_ids = []
        valid_indices = []

        # 从预处理后的数据中提取有效的K线
        valid_data = []

        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
                continue

            # 向量化
            from pyqlab.models.gpt2.vq_tokenizer import candlestick_to_vector
            vector = candlestick_to_vector(
                row,
                row['Prev_Close'],
                row['MA_Volume'],
                row['ATR'],
                tokenizer.embedding_dim,
                tokenizer.vectorization_method
            )

            if np.all(vector == 0):
                continue

            original_vectors.append(vector)

            # 使用ONNX编码器进行编码
            try:
                # 确保向量是二维的 [1, embedding_dim]
                vector_reshaped = vector.reshape(1, tokenizer.embedding_dim).astype(np.float32)
                onnx_input = {encoder_input_name: vector_reshaped}

                # 打印调试信息
                if i < 3:  # 只打印前几个样本的调试信息
                    print(f"ONNX编码输入 {i}: 形状={vector_reshaped.shape}, 类型={vector_reshaped.dtype}")
                    print(f"ONNX编码输入值: {vector_reshaped}")

                # 运行推理
                onnx_output = encoder_session.run(None, onnx_input)
                token_id = onnx_output[0][0]

                if i < 3:
                    print(f"ONNX编码: 向量 -> Token ID {token_id}")
            except Exception as e:
                print(f"ONNX编码错误: {e}")
                # 使用随机token_id作为后备
                token_id = np.random.randint(0, tokenizer.num_embeddings)
                print(f"使用随机Token ID: {token_id}")

            token_ids.append(token_id)

            # 使用ONNX解码器进行解码
            try:
                # 确保token_id是一维的 [1]
                token_np = np.array([token_id]).astype(np.int64)
                onnx_input = {decoder_input_name: token_np}

                # 打印调试信息
                if i < 3:
                    print(f"ONNX解码输入 {i}: 值={token_np}, 类型={token_np.dtype}")

                # 运行推理
                onnx_output = decoder_session.run(None, onnx_input)
                quantized_vector = onnx_output[0][0]

                if i < 3:
                    print(f"ONNX解码: Token ID {token_id} -> 向量 {quantized_vector}")
            except Exception as e:
                print(f"ONNX解码错误: {e}")
                # 使用随机向量作为后备
                quantized_vector = np.random.randn(tokenizer.embedding_dim).astype(np.float32)
                print(f"使用随机向量: {quantized_vector}")

            quantized_vectors.append(quantized_vector)
            valid_indices.append(i)
            valid_data.append(row)

        # 重建K线数据
        reconstructed_data = []

        prev_close = valid_data[0]['Prev_Close']
        ma_volume = valid_data[0]['MA_Volume']
        atr_val = valid_data[0]['ATR']

        for i, vector in enumerate(quantized_vectors):
            # 规范化向量值
            if vectorization_method == VectorizationMethod.ATR_BASED:
                vector = np.clip(vector, -10.0, 10.0)
            elif vectorization_method == VectorizationMethod.PERCENT_CHANGE:
                vector = np.clip(vector, -20.0, 20.0)

            # 转换为K线数据
            candlestick = tokenizer.vector_to_candlestick(vector, prev_close, ma_volume, atr_val)

            # 添加时间戳
            if i < len(valid_data):
                candlestick['datetime'] = valid_data[i]['datetime']

            reconstructed_data.append(candlestick)

            # 更新基准值
            prev_close = candlestick['close']

            # 更新ATR和MA_Volume
            if i + 1 < len(valid_data):
                ma_volume = valid_data[i + 1]['MA_Volume']
                atr_val = valid_data[i + 1]['ATR']

        df_reconstructed = pd.DataFrame(reconstructed_data)

        # 计算原始数据和重建数据的差异
        results = {}

        if len(valid_data) > 0 and len(df_reconstructed) > 0:
            # 确保两个DataFrame有相同的索引
            min_len = min(len(df_reconstructed), len(valid_data))

            # 提取价格数据
            orig_prices = pd.DataFrame([{
                'open': row['open'],
                'high': row['high'],
                'low': row['low'],
                'close': row['close']
            } for row in valid_data[:min_len]])

            recon_prices = df_reconstructed[['open', 'high', 'low', 'close']].iloc[:min_len]

            # 计算价格差异
            price_diff = np.abs(orig_prices.values - recon_prices.values)
            mean_price_diff = np.mean(price_diff)

            # 计算相对差异百分比
            orig_mean_price = np.mean(orig_prices.values)
            relative_diff_pct = mean_price_diff / orig_mean_price * 100

            # 检查最高价和最低价的正确性
            high_errors = 0
            low_errors = 0

            for i in range(min_len):
                orig_row = orig_prices.iloc[i]
                recon_row = recon_prices.iloc[i]

                # 检查最高价是否是最高点
                if not (recon_row['high'] >= recon_row['open'] and
                        recon_row['high'] >= recon_row['close']):
                    high_errors += 1

                # 检查最低价是否是最低点
                if not (recon_row['low'] <= recon_row['open'] and
                        recon_row['low'] <= recon_row['close']):
                    low_errors += 1

            # 计算向量差异
            if len(original_vectors) == len(quantized_vectors):
                original_vectors_array = np.array(original_vectors)
                quantized_vectors_array = np.array(quantized_vectors)

                vector_diff = np.abs(original_vectors_array - quantized_vectors_array)
                mean_vector_diff = np.mean(vector_diff)

                results['vector_diff'] = mean_vector_diff

            # 保存结果
            results['price_diff'] = mean_price_diff
            results['relative_diff_pct'] = relative_diff_pct
            results['high_errors'] = high_errors
            results['low_errors'] = low_errors
            results['sample_count'] = min_len

            # 保存验证结果
            if save_dir:
                os.makedirs(save_dir, exist_ok=True)

                # 保存文本结果
                result_path = os.path.join(save_dir, f"{prefix}codebook_validation_{vectorization_method}.txt")
                with open(result_path, "w") as f:
                    f.write(f"ONNX验证结果 ({vectorization_method}):\n")
                    f.write(f"编码器: {encoder_onnx_path}\n")
                    f.write(f"解码器: {decoder_onnx_path}\n")
                    f.write(f"样本数量: {min_len}\n")
                    f.write(f"价格平均差异: {mean_price_diff:.6f}\n")
                    f.write(f"相对差异百分比: {relative_diff_pct:.2f}%\n")
                    f.write(f"最高价错误数: {high_errors}/{min_len} ({high_errors/min_len*100:.2f}%)\n")
                    f.write(f"最低价错误数: {low_errors}/{min_len} ({low_errors/min_len*100:.2f}%)\n")

                    if 'vector_diff' in results:
                        f.write(f"向量平均差异: {results['vector_diff']:.6f}\n")

                    # 添加详细的样本比较
                    f.write("\n详细样本比较:\n")
                    for i in range(min_len):
                        orig_row = orig_prices.iloc[i]
                        recon_row = recon_prices.iloc[i]

                        f.write(f"\n样本 {i}:\n")
                        f.write(f"原始: O={orig_row['open']:.2f}, H={orig_row['high']:.2f}, L={orig_row['low']:.2f}, C={orig_row['close']:.2f}\n")
                        f.write(f"重建: O={recon_row['open']:.2f}, H={recon_row['high']:.2f}, L={recon_row['low']:.2f}, C={recon_row['close']:.2f}\n")

                        diff = np.abs(orig_row.values - recon_row.values)
                        f.write(f"差异: {diff}\n")
                        f.write(f"平均差异: {np.mean(diff):.6f}\n")

                        if i < len(original_vectors) and i < len(quantized_vectors):
                            f.write(f"原始向量: {original_vectors[i]}\n")
                            f.write(f"量化向量: {quantized_vectors[i]}\n")

                print(f"ONNX验证结果已保存到 {result_path}")

                # 可视化结果
                fig = plt.figure(figsize=(20, 15))
                gs = fig.add_gridspec(3, 1, height_ratios=[3, 3, 2])

                # 绘制原始K线
                ax_orig = fig.add_subplot(gs[0, :])
                tokenizer._plot_candlesticks(ax_orig, pd.DataFrame(valid_data[:min_len]),
                                           title=f"Original Candlesticks ({vectorization_method})")

                # 绘制重建的K线
                ax_recon = fig.add_subplot(gs[1, :], sharex=ax_orig)
                tokenizer._plot_candlesticks(ax_recon, df_reconstructed.iloc[:min_len],
                                           title="ONNX Reconstructed Candlesticks")

                # 确保两个K线图的Y轴范围一致
                orig_ymin, orig_ymax = ax_orig.get_ylim()
                recon_ymin, recon_ymax = ax_recon.get_ylim()

                # 使用共同的Y轴范围
                ymin = min(orig_ymin, recon_ymin)
                ymax = max(orig_ymax, recon_ymax)

                # 设置相同的Y轴范围
                ax_orig.set_ylim(ymin, ymax)
                ax_recon.set_ylim(ymin, ymax)

                # 绘制差异
                ax_diff = fig.add_subplot(gs[2, :])
                diff_values = np.mean(price_diff, axis=1)
                ax_diff.bar(range(len(diff_values)), diff_values, color='skyblue')
                ax_diff.set_title("Mean Absolute Error Between Original and ONNX Reconstructed Prices")
                ax_diff.set_xlabel("Candle Index")
                ax_diff.set_ylabel("Mean Absolute Error")
                ax_diff.grid(True, alpha=0.3)

                # 保存图形
                fig_path = os.path.join(save_dir, f"{prefix}codebook_validation_{vectorization_method}.png")
                plt.tight_layout()
                plt.savefig(fig_path)
                plt.close()

                print(f"ONNX验证图形已保存到 {fig_path}")

        return results

    except Exception as e:
        print(f"验证ONNX模型时出错: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description="验证码本的有效性")
    parser.add_argument('--data_file', type=str, required=True, help='K线数据文件路径')
    parser.add_argument('--codebook_path', type=str, help='码本权重文件路径')
    parser.add_argument('--encoder_onnx_path', type=str, help='编码器ONNX模型路径')
    parser.add_argument('--decoder_onnx_path', type=str, help='解码器ONNX模型路径')
    parser.add_argument('--method', type=str, default='atr_based', choices=['atr_based', 'percent_change', 'zscore', 'log_return', 'minmax'], help='向量化方法')
    parser.add_argument('--num_samples', type=int, default=20, help='要验证的样本数量')
    parser.add_argument('--save_dir', type=str, default='.', help='保存结果的目录')
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=5, help='嵌入维度')

    args = parser.parse_args()

    # 加载数据
    result = load_single_data(args.data_file)
    if result is None:
        print(f"错误: 无法加载数据文件 {args.data_file}")
        return

    df_data_list, _ = result
    if len(df_data_list) == 0:
        print(f"错误: 数据文件 {args.data_file} 中没有有效的数据")
        return

    # 使用第一个DataFrame
    df_data = df_data_list[0]

    print(f"加载了 {len(df_data)} 条K线数据")

    # 验证PyTorch码本
    if args.codebook_path:
        print(f"\n验证PyTorch码本: {args.codebook_path}")
        tokenizer = CandlestickVQTokenizer(
            codebook_weights_path=args.codebook_path,
            num_embeddings=args.num_embeddings,
            embedding_dim=args.embedding_dim,
            vectorization_method=args.method,
            encoder_onnx_path=args.encoder_onnx_path,
            decoder_onnx_path=args.decoder_onnx_path
        )

        results = validate_codebook(
            tokenizer=tokenizer,
            df_data=df_data,
            num_samples=args.num_samples,
            save_dir=args.save_dir
        )

        print("\nPyTorch码本验证结果:")
        print(f"价格平均差异: {results['price_diff']:.6f}")
        print(f"相对差异百分比: {results['relative_diff_pct']:.2f}%")
        print(f"最高价错误数: {results['high_errors']}/{results['sample_count']} ({results['high_errors']/results['sample_count']*100:.2f}%)")
        print(f"最低价错误数: {results['low_errors']}/{results['sample_count']} ({results['low_errors']/results['sample_count']*100:.2f}%)")

        if 'vector_diff' in results:
            print(f"向量平均差异: {results['vector_diff']:.6f}")

    # 验证ONNX码本
    if args.encoder_onnx_path and args.decoder_onnx_path:
        print(f"\n验证ONNX码本:")
        print(f"编码器: {args.encoder_onnx_path}")
        print(f"解码器: {args.decoder_onnx_path}")

        results = validate_onnx_codebook(
            encoder_onnx_path=args.encoder_onnx_path,
            decoder_onnx_path=args.decoder_onnx_path,
            df_data=df_data,
            vectorization_method=args.method,
            num_samples=args.num_samples,
            save_dir=args.save_dir
        )

        if results:
            print("\nONNX码本验证结果:")
            print(f"价格平均差异: {results['price_diff']:.6f}")
            print(f"相对差异百分比: {results['relative_diff_pct']:.2f}%")
            print(f"最高价错误数: {results['high_errors']}/{results['sample_count']} ({results['high_errors']/results['sample_count']*100:.2f}%)")
            print(f"最低价错误数: {results['low_errors']}/{results['sample_count']} ({results['low_errors']/results['sample_count']*100:.2f}%)")

            if 'vector_diff' in results:
                print(f"向量平均差异: {results['vector_diff']:.6f}")


if __name__ == "__main__":
    main()
