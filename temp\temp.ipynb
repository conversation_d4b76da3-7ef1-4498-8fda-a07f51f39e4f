{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from copy import copy\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import copy\n", "import json"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import random\n", "from tqdm import tqdm\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集\n", "from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler\n", "import torch.optim as optim\n", "from torch.optim import lr_scheduler\n", "# from fastai.layers import SigmoidRange"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [], "source": ["class PreprocessingPipeline:\n", "    \n", "    def __init__(self, n_splits, shuffle, random_state, data_path, portfolios):\n", "        \n", "        self.n_splits = n_splits\n", "        self.shuffle = shuffle\n", "        self.random_state = random_state\n", "        self.data_path = data_path\n", "        self.portfolios = portfolios\n", "        self.lb_df = None\n", "        self.lf_df = None\n", "        self.sf_df = None\n", "        self.ct_df = None\n", "\n", "    def _load_data(self, direct):\n", "\n", "        self.lb_df = pd.DataFrame()\n", "        self.lf_df = pd.DataFrame()\n", "        self.sf_df = pd.DataFrame()\n", "        self.ct_df = pd.DataFrame()\n", "\n", "        for pf in self.portfolios:\n", "            if os.path.isfile('%s/factors_%s_lf.%s.csv'%(self.data_path, direct, pf)):\n", "                self.lf_df = self.lf_df.append(pd.read_csv('%s/factors_%s_lf.%s.csv'%(self.data_path, direct, pf)))\n", "            if os.path.isfile('%s/factors_%s_sf.%s.csv'%(self.data_path, direct, pf)):\n", "                self.sf_df = self.sf_df.append(pd.read_csv('%s/factors_%s_sf.%s.csv'%(self.data_path, direct, pf)))\n", "            if os.path.isfile('%s/factors_%s_ct.%s.csv'%(self.data_path, direct, pf)):\n", "                self.ct_df = self.ct_df.append(pd.read_csv('%s/factors_%s_ct.%s.csv'%(self.data_path, direct, pf)))\n", "            if os.path.isfile('%s/orders_%s_label.%s.csv'%(self.data_path, direct, pf)):\n", "                self.lb_df = self.lb_df.append(pd.read_csv('%s/orders_%s_label.%s.csv'%(self.data_path, direct, pf)), ignore_index=True)\n", "\n", "        self.lf_df.to_csv(\"%s/factors_%s_lf.csv\"%(self.data_path, direct), index=0)\n", "        self.sf_df.to_csv(\"%s/factors_%s_sf.csv\"%(self.data_path, direct), index=0)\n", "        self.ct_df.to_csv(\"%s/factors_%s_ct.csv\"%(self.data_path, direct), index=0)\n", "        self.lb_df.to_csv(\"%s/orders_%s_label.csv\"%(self.data_path, direct), index=0)\n", "\n", "        self.lf_df.set_index(\"ord_id\", inplace=True)\n", "        self.sf_df.set_index(\"ord_id\", inplace=True)\n", "        self.ct_df.set_index(\"ord_id\", inplace=True)\n", "        # self.lb_df.set_index(\"ord_id\", inplace=True)\n", "        print(len(self.lf_df), len(self.sf_df), len(self.ct_df), len(self.lb_df))\n", "        print(\"\\nToday add %s count: \" % direct, (self.lb_df['datetime'] >= datetime.datetime.now().strftime(\"%Y%m%d 00:00:00\")).sum())\n", "        \n", "    def _label_encode(self):\n", "\n", "        # Encoding instrument_id for embeddings\n", "        le = LabelEncoder()\n", "        self.lb_df['code_encoded'] = le.fit_transform(self.lb_df['CODE'].values)\n", "    \n", "    def _get_folds(self):\n", "        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)\n", "        for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):\n", "            self.lb_df.loc[val_idx, 'fold'] = fold\n", "        self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)\n", "\n", "    def _dump_input_param_json(self, direct, model_name, model_path):\n", "        f_sel = {}\n", "        f_sel['codes'] = self.lb_df.CODE.unique().tolist()\n", "\n", "        f_sel['lf_mean'] = self.lf_df.values.mean(axis=0).tolist()\n", "        f_sel['lf_std'] = self.lf_df.values.std(axis=0).tolist()\n", "        f_sel['sf_mean'] = self.sf_df.values.mean(axis=0).tolist()\n", "        f_sel['sf_std'] = self.sf_df.values.std(axis=0).tolist()\n", "        f_sel['ct_mean'] = self.ct_df.values.mean(axis=0).tolist()\n", "        f_sel['ct_std'] = self.ct_df.values.std(axis=0).tolist()\n", "\n", "        with open(f'{self.data_path}/using_factor.json', 'r') as using_file:\n", "            using_factor = json.load(using_file)\n", "            using_factor.update(f_sel)\n", "\n", "        with open(f'{model_path}/{model_name}_{direct}.json', 'w') as factor_sel_file:\n", "            json.dump(using_factor, factor_sel_file)\n", "                        \n", "    def transform(self, direct, model_name, model_path):\n", "        self._load_data(direct)\n", "        self._label_encode()\n", "        self._get_folds()\n", "        self._dump_input_param_json(direct, model_name, model_path)\n", "        \n", "        return self.lb_df, self.lf_df, self.sf_df, self.ct_df\n", "\n", "    def get_num_embeddings(self):\n", "        return len(self.lb_df.CODE.unique())"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [], "source": ["\n", "preprocessing_parameters = {\n", "    'n_splits': 5,\n", "    'shuffle': True,\n", "    'random_state': 42,\n", "    'data_path': 'e:/lab/RoboQuant/pylab/data',\n", "    'portfolios': ['00200910081133001', '00171106132928000', '00170623114649000'],\n", "}\n", "\n", "ppp = PreprocessingPipeline(**preprocessing_parameters)"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1616 1616 1616 1616\n", "\n", "Today add long count:  10\n"]}], "source": ["lb_df, lf_df, sf_df, ct_df = ppp.transform('long', 'test', 'e:/lab/RoboQuant/pylab/data')"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "      <th>CODE</th>\n", "      <th>code_encoded</th>\n", "      <th>fold</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>210922090831255</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 09:08:31</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>210922133807043</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 13:38:07</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>210922140751125</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 14:07:51</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>210923093149123</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210923 09:31:49</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>210923111322363</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210923 11:13:22</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1611</th>\n", "      <td>210927210007017</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210927 21:00:07</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1612</th>\n", "      <td>210927213705105</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210927 21:37:05</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1613</th>\n", "      <td>210928093756285</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210928 09:37:56</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1614</th>\n", "      <td>210928111845457</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210928 11:18:45</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1615</th>\n", "      <td>210930140924235</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210930 14:09:24</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1616 rows × 8 columns</p>\n", "</div>"], "text/plain": ["               ord_id instrument           datetime direct  label CODE  \\\n", "0     210922090831255   A2111.DC  20210922 09:08:31      L      1    A   \n", "1     210922133807043   A2111.DC  20210922 13:38:07      L      0    A   \n", "2     210922140751125   A2111.DC  20210922 14:07:51      L      0    A   \n", "3     210923093149123   A2111.DC  20210923 09:31:49      L      1    A   \n", "4     210923111322363   A2111.DC  20210923 11:13:22      L      1    A   \n", "...               ...        ...                ...    ...    ...  ...   \n", "1611  210927210007017  ZN2111.SC  20210927 21:00:07      L      0   ZN   \n", "1612  210927213705105  ZN2111.SC  20210927 21:37:05      L      0   ZN   \n", "1613  210928093756285  ZN2111.SC  20210928 09:37:56      L      0   ZN   \n", "1614  210928111845457  ZN2111.SC  20210928 11:18:45      L      0   ZN   \n", "1615  210930140924235  ZN2111.SC  20210930 14:09:24      L      1   ZN   \n", "\n", "      code_encoded  fold  \n", "0                0     4  \n", "1                0     4  \n", "2                0     4  \n", "3                0     3  \n", "4                0     3  \n", "...            ...   ...  \n", "1611            37     1  \n", "1612            37     4  \n", "1613            37     1  \n", "1614            37     2  \n", "1615            37     1  \n", "\n", "[1616 rows x 8 columns]"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["lb_df"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"data": {"text/plain": ["41"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["lf_df.shape[1]"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["class Factor1DDataset(Dataset):\n", "\n", "    def __init__(self, lb_df, lf_df, sf_df, ct_df):\n", "\n", "        self.lb_df = lb_df\n", "        self.lb_df.sort_values(by='ord_id', inplace=True)\n", "        self.lb_df.reset_index(drop=True, inplace=True)\n", "\n", "        df = pd.merge(lf_df, sf_df, how='inner', on='ord_id')\n", "        self.data = pd.merge(df, ct_df, how='inner', on='ord_id')\n", "        self.data = self.data[self.data.index.isin(self.lb_df['ord_id'])]\n", "        self.data.sort_index(inplace=True)\n", "        self.data = self.data.values\n", "        \n", "        self.data = (self.data - self.data.mean(axis=0)) /self.data.std(axis=0)\n", "\n", "    def __len__(self):\n", "        return len(self.lb_df)\n", "\n", "    def __getitem__(self, idx):\n", "\n", "        sequences = torch.as_tensor(np.array(self.data[idx]), dtype=torch.float)\n", "        code_encoded = torch.as_tensor(self.lb_df.iloc[idx]['code_encoded'], dtype=torch.long)\n", "        target = self.lb_df.iloc[idx]['label']\n", "        target = torch.as_tensor(target, dtype=torch.float)\n", "        return code_encoded, sequences, target\n"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               ord_id instrument           datetime direct  label CODE  \\\n", "0     210922090007097  CS2111.DC  20210922 09:00:07      L      1   CS   \n", "1     210922090015109  BU2112.SC  20210922 09:00:15      L      0   BU   \n", "2     210922090502169  UR2201.ZC  20210922 09:05:02      L      1   UR   \n", "3     210922090503177  PP2201.DC  20210922 09:05:03      L      1   PP   \n", "4     210922090504179   M2201.DC  20210922 09:05:04      L      0    M   \n", "...               ...        ...                ...    ...    ...  ...   \n", "1287  211020090749129  CJ2201.ZC  20211020 09:07:49      L      1   CJ   \n", "1288  211020105650379  BU2112.SC  20211020 10:56:50      L      0   BU   \n", "1289  211020105816315  BU2112.SC  20211020 10:58:16      L      0   BU   \n", "1290  211020141637455   Y2201.DC  20211020 14:16:37      L      1    Y   \n", "1291  211020142059469   Y2201.DC  20211020 14:20:59      L      1    Y   \n", "\n", "      code_encoded  fold  \n", "0                9     5  \n", "1                5     4  \n", "2               34     3  \n", "3               24     4  \n", "4               19     5  \n", "...            ...   ...  \n", "1287             8     3  \n", "1288             5     2  \n", "1289             5     3  \n", "1290            36     3  \n", "1291            36     2  \n", "\n", "[1292 rows x 8 columns]\n", "                  lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  lf_LR_SLOPE_FAST_2  \\\n", "ord_id                                                                          \n", "210922090007097  42.198003  43.185837                -6.7                 6.1   \n", "210922090015109  49.171182  49.047868                16.6               -12.6   \n", "210922090502169  69.478145  67.232473                67.0                46.6   \n", "210922090503177  54.342023  57.438281               -68.7               -88.4   \n", "210922090504179  51.957238  51.602900                16.4                20.7   \n", "...                    ...        ...                 ...                 ...   \n", "211020090749129  56.924021  57.016164              -206.5              -288.6   \n", "211020105650379  45.331777  45.079594               -60.4               -44.8   \n", "211020105816315  45.259309  42.611519               -76.8               -74.9   \n", "211020141637455  63.865318  64.443116                73.0                44.0   \n", "211020142059469  64.344766  64.391469               101.8               131.4   \n", "\n", "                 lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  lf_LR_SLOPE_SLOW_1  \\\n", "ord_id                                                                        \n", "210922090007097           -2.096970            0.400000           -5.757692   \n", "210922090015109           12.618182            6.933333           -1.192308   \n", "210922090502169           27.812121           31.630303            9.746154   \n", "210922090503177           55.327273           46.290909           27.682308   \n", "210922090504179            4.424242            8.957576           -5.265385   \n", "...                             ...                 ...                 ...   \n", "211020090749129          196.284848          153.406061           57.827692   \n", "211020105650379          -39.272727          -46.436364            6.013846   \n", "211020105816315           -9.103030          -23.363636           15.693846   \n", "211020141637455           25.660606           46.654545           45.863077   \n", "211020142059469           27.939394           29.781818           41.090000   \n", "\n", "                 lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  \\\n", "ord_id                                                            \n", "210922090007097           -6.750769                   17.134648   \n", "210922090015109           -0.221538                   32.259578   \n", "210922090502169           12.932308                   29.977054   \n", "210922090503177           30.622308                   63.619696   \n", "210922090504179           -4.150769                   22.420616   \n", "...                             ...                         ...   \n", "211020090749129           59.270000                  296.874538   \n", "211020105650379            2.738462                   31.125792   \n", "211020105816315           13.513077                   37.120451   \n", "211020141637455           48.052308                   76.518893   \n", "211020142059469           44.556923                   90.212744   \n", "\n", "                 lf_LR_SLOPE_SLOW_THRESHOLD  ...  sf_TREND_INBARS_2  \\\n", "ord_id                                       ...                      \n", "210922090007097                    6.741661  ...                2.0   \n", "210922090015109                   12.499770  ...                2.0   \n", "210922090502169                    7.870420  ...                3.0   \n", "210922090503177                   29.322697  ...                0.0   \n", "210922090504179                    9.507421  ...                2.0   \n", "...                                     ...  ...                ...   \n", "211020090749129                   93.091122  ...                0.0   \n", "211020105650379                   13.947795  ...                2.0   \n", "211020105816315                   13.526001  ...                2.0   \n", "211020141637455                   31.188722  ...                0.0   \n", "211020142059469                   31.874267  ...                0.0   \n", "\n", "                 sf_TREND_INPOSR_1  sf_TREND_INPOSR_2  sf_TREND_HLR  \\\n", "ord_id                                                                \n", "210922090007097           1.545462           2.406417      5.347594   \n", "210922090015109           7.000000           1.500000     13.500000   \n", "210922090502169           3.500000           1.900000      7.000000   \n", "210922090503177           2.181818           0.545455      6.545455   \n", "210922090504179           3.415005           2.207506      6.181015   \n", "...                            ...                ...           ...   \n", "211020090749129           1.100917           0.000000      6.192661   \n", "211020105650379           4.444444           1.777778      8.444444   \n", "211020105816315           4.444444           2.222222      8.444444   \n", "211020141637455           1.739130           0.000000      6.869565   \n", "211020142059469           1.833333           0.333333      6.750000   \n", "\n", "                 sf_TREND_LEVEL  STDDEV_RNG  SHORT_RANGE  LONG_RANGE  \\\n", "ord_id                                                                 \n", "210922090007097             0.0    1.232618         3.74        41.0   \n", "210922090015109             0.0    1.095635         8.00        80.0   \n", "210922090502169             0.0    1.428389        10.00        76.0   \n", "210922090503177             2.0    1.449321        22.00       170.0   \n", "210922090504179             0.0    1.092932         4.53        50.0   \n", "...                         ...         ...          ...         ...   \n", "211020090749129             3.0    1.361508       109.00       661.0   \n", "211020105650379             0.0    1.283300         9.00        74.0   \n", "211020105816315             0.0    1.172480         9.00        93.0   \n", "211020141637455             3.0    1.295273        23.00       178.0   \n", "211020142059469             3.0    1.119000        24.00       225.0   \n", "\n", "                 FAST_QH_RSI  SLOW_QH_RSI  \n", "ord_id                                     \n", "210922090007097    -1.606082     0.766325  \n", "210922090015109    -1.606082     0.766325  \n", "210922090502169     0.920621     1.517871  \n", "210922090503177     0.920621     1.517871  \n", "210922090504179     0.920621     1.517871  \n", "...                      ...          ...  \n", "211020090749129    -5.173262     1.157163  \n", "211020105650379    -5.656015     1.220513  \n", "211020105816315    -6.231024     1.045753  \n", "211020141637455    -2.336185     1.798001  \n", "211020142059469    -3.036250     1.760265  \n", "\n", "[1292 rows x 87 columns]\n"]}], "source": ["fold = 1\n", "trn_idx, val_idx = lb_df.loc[lb_df['fold'] != fold].index, lb_df.loc[lb_df['fold'] == fold].index\n", "train_ds = Factor1DDataset(lb_df=lb_df.loc[trn_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df)\n", "# train_dl = DataLoader(train_ds, batch_size=self.training_params['batch_size'], shuffle=True)"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "      <th>CODE</th>\n", "      <th>code_encoded</th>\n", "      <th>fold</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6</td>\n", "      <td>210924112908597</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210924 11:29:08</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>15</td>\n", "      <td>210929222656175</td>\n", "      <td>AG2112.SC</td>\n", "      <td>20210929 22:26:56</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>AG</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17</td>\n", "      <td>211013104201683</td>\n", "      <td>AG2112.SC</td>\n", "      <td>20211013 10:42:01</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>AG</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>210922090500167</td>\n", "      <td>AP2201.ZC</td>\n", "      <td>20210922 09:05:00</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>AP</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>28</td>\n", "      <td>210927100908831</td>\n", "      <td>AP2201.ZC</td>\n", "      <td>20210927 10:09:08</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>AP</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>1601</td>\n", "      <td>210930090001103</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20210930 09:00:01</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "      <td>36</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>320</th>\n", "      <td>1607</td>\n", "      <td>211014090001059</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20211014 09:00:01</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "      <td>36</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>321</th>\n", "      <td>1611</td>\n", "      <td>210927210007017</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210927 21:00:07</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>1613</td>\n", "      <td>210928093756285</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210928 09:37:56</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>323</th>\n", "      <td>1615</td>\n", "      <td>210930140924235</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210930 14:09:24</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>324 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     index           ord_id instrument           datetime direct  label CODE  \\\n", "0        6  210924112908597   A2111.DC  20210924 11:29:08      L      1    A   \n", "1       15  210929222656175  AG2112.SC  20210929 22:26:56      L      1   AG   \n", "2       17  211013104201683  AG2112.SC  20211013 10:42:01      L      1   AG   \n", "3       19  210922090500167  AP2201.ZC  20210922 09:05:00      L      0   AP   \n", "4       28  210927100908831  AP2201.ZC  20210927 10:09:08      L      0   AP   \n", "..     ...              ...        ...                ...    ...    ...  ...   \n", "319   1601  210930090001103   Y2201.DC  20210930 09:00:01      L      1    Y   \n", "320   1607  211014090001059   Y2201.DC  20211014 09:00:01      L      1    Y   \n", "321   1611  210927210007017  ZN2111.SC  20210927 21:00:07      L      0   ZN   \n", "322   1613  210928093756285  ZN2111.SC  20210928 09:37:56      L      0   ZN   \n", "323   1615  210930140924235  ZN2111.SC  20210930 14:09:24      L      1   ZN   \n", "\n", "     code_encoded  fold  \n", "0               0     1  \n", "1               1     1  \n", "2               1     1  \n", "3               3     1  \n", "4               3     1  \n", "..            ...   ...  \n", "319            36     1  \n", "320            36     1  \n", "321            37     1  \n", "322            37     1  \n", "323            37     1  \n", "\n", "[324 rows x 9 columns]"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["lb1_df"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["train_dataset = Factor1DDataset(lb_df=lb_df, lf_df=lf_df, sf_df=sf_df, ct_df=ct_df)\n", "train_loader = DataLoader(\n", "    train_dataset,\n", "    batch_size=32,\n", ")"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["td0, td1, td2 = next(iter(train_loader))"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3,\n", "        3, 3, 3, 3, 3, 3, 3, 3])"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["td0"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import akshare as ak\n", "DATA_PATH = 'e:/lab/RoboQuant/pylab/data'"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          代码    名称  送转股份-送转总比例 送转股份-送转比例 送转股份-转股比例 现金分红-现金分红比例  现金分红-股息率  \\\n", "0     688601   力芯微         0.0         -         -         3.5  0.002155   \n", "1     688509  正元地信         0.0         -         -      0.1595  0.002363   \n", "2     002218  拓日新能         0.0         -         -        0.35  0.007991   \n", "3     001212  中旗新材         0.0         -         -         3.0  0.009706   \n", "4     301028  东亚机械         0.0         -         -         1.6  0.010499   \n", "...      ...   ...         ...       ...       ...         ...       ...   \n", "3060  600315  上海家化         0.0         -         -      1.9951  0.003872   \n", "3061  000001  平安银行         0.0         -         -         1.8   0.00766   \n", "3062  300617  安靠智电         0.0         -         -         5.0  0.011939   \n", "3063  002107  沃华医药         0.0         -         -         2.6  0.033248   \n", "3064  002886  沃特股份         7.0         -       7.0         1.0  0.004257   \n", "\n", "       每股收益      每股净资产     每股公积金   每股未分配利润   净利润同比增长           总股本  \\\n", "0     1.390   6.730000  1.966734  3.263822   64.1098  6.400000e+07   \n", "1     0.110   1.970000  0.387546  0.546276  -22.1747  7.700000e+08   \n", "2     0.134   2.502365  1.064237  0.418843  112.6522  1.413021e+09   \n", "3     2.010   8.990000  4.891477  2.609863   25.5720  9.067000e+07   \n", "4     0.510   1.890000  0.025916  0.676682   69.9882  3.789120e+08   \n", "...     ...        ...       ...       ...       ...           ...   \n", "3060  0.640   9.586308  1.443091  6.752966  -22.7772  6.796345e+08   \n", "3061  1.400  15.160000  4.164485  6.760074    2.5998  1.940592e+10   \n", "3062  1.040   7.147896  3.004728  3.053751  108.0783  1.293436e+08   \n", "3063  0.310   1.398797  0.004065  0.323593   86.7198  5.772096e+08   \n", "3064  0.529   8.310441  4.710395  2.415723   36.7885  1.332785e+08   \n", "\n", "                    预案公告日                股权登记日                除权除息日      方案进度  \\\n", "0     2021-11-20T00:00:00                    -                    -   董事会决议通过   \n", "1     2021-11-06T00:00:00                    -                    -   董事会决议通过   \n", "2     2021-10-26T00:00:00                    -                    -  股东大会决议通过   \n", "3     2021-09-27T00:00:00  2021-11-01T00:00:00  2021-11-02T00:00:00      实施分配   \n", "4     2021-08-27T00:00:00                    -                    -      取消分配   \n", "...                   ...                  ...                  ...       ...   \n", "3060  2021-02-03T00:00:00  2021-08-04T00:00:00  2021-08-05T00:00:00      实施分配   \n", "3061  2021-02-02T00:00:00  2021-05-13T00:00:00  2021-05-14T00:00:00      实施分配   \n", "3062  2021-01-27T00:00:00  2021-03-01T00:00:00  2021-03-02T00:00:00      实施分配   \n", "3063  2021-01-21T00:00:00  2021-04-08T00:00:00  2021-04-09T00:00:00      实施分配   \n", "3064  2021-01-12T00:00:00  2021-05-12T00:00:00  2021-05-13T00:00:00      实施分配   \n", "\n", "                   最新公告日期                        配送方案              分红配送报告期  \n", "0     2021-11-20T00:00:00                10派3.50元(含税)  2020-12-31T00:00:00  \n", "1     2021-11-06T00:00:00              10派0.1595元(含税)  2020-12-31T00:00:00  \n", "2     2021-10-26T00:00:00                10派0.35元(含税)  2020-12-31T00:00:00  \n", "3     2021-10-27T00:00:00       10派3.00元(含税,扣税后2.70元)  2020-12-31T00:00:00  \n", "4     2021-08-27T00:00:00                10派1.60元(含税)  2020-12-31T00:00:00  \n", "...                   ...                         ...                  ...  \n", "3060  2021-07-30T00:00:00   10派1.9951元(含税,扣税后1.7956元)  2020-12-31T00:00:00  \n", "3061  2021-05-07T00:00:00       10派1.80元(含税,扣税后1.62元)  2020-12-31T00:00:00  \n", "3062  2021-02-23T00:00:00       10派5.00元(含税,扣税后4.50元)  2020-12-31T00:00:00  \n", "3063  2021-03-31T00:00:00       10派2.60元(含税,扣税后2.34元)  2020-12-31T00:00:00  \n", "3064  2021-04-30T00:00:00  10转7.00派1.00元(含税,扣税后0.90元)  2020-12-31T00:00:00  \n", "\n", "[3065 rows x 20 columns]\n"]}], "source": ["stock_em_fhps_df = ak.stock_em_fhps(date=\"20201231\")\n", "stock_em_fhps_df.to_csv(f'{DATA_PATH}/fhps.csv')\n", "print(stock_em_fhps_df)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               datetime                                            content\n", "0   2021-11-21 11:23:24  【外交部：将中立两国外交关系降为代办级】2021年11月18日，立陶宛不顾中方严正抗议和反复...\n", "1   2021-11-21 11:18:18  【巴西日均新增新冠肺炎死亡病例降至200例以下】根据巴西卫生部当地时间11月20日公布的数据...\n", "2   2021-11-21 11:07:07  【华泰期货：关注美联储主席提名对明年货币政策的影响】近期需要重点关注两大要点，一是拜登表示将...\n", "3   2021-11-21 11:00:52  【10月以来超600家公司接受私募机构调研】Choice数据显示，截至11月20日，10月以...\n", "4   2021-11-21 10:57:14  <a href=\"https://qihuo.jin10.com/app/index.htm...\n", "5   2021-11-21 10:48:31  <b>【14亿人每人每天3斤菜 我国“菜篮子”总量充足供应有保障】</b>农业农村部市场与信...\n", "6   2021-11-21 10:39:34                  【金十图示】2021年11月21日（周日）全网加密货币实时爆仓数据\n", "7   2021-11-21 10:38:47                    【行情】比特币跌至59000美元/枚下方，日内跌幅1.30%。\n", "8   2021-11-21 10:33:20  德国疾控机构罗伯特·科赫研究所：德国新增42727例新冠肺炎确诊病例；新增75例死亡病例，累...\n", "9   2021-11-21 10:25:20  【报道】根据美国一项最新研究估计，由于新冠肺炎患者基数庞大，美国有70万到160万新冠患者康...\n", "10  2021-11-21 10:15:19  【韩国首尔公寓市场供需指数时隔7个月首次跌破100】据韩国KBS报道，韩国不动产研究院数据显...\n", "11  2021-11-21 10:04:17  【辽宁大连市将金普新区三地划定为中风险地区】辽宁大连市将大连市金普新区先进街道响泉社区万科城...\n", "12  2021-11-21 09:59:58  11月20日至21日，北京冬奥村、冬残奥村开展全流程、全要素的运行模拟测试，全面检验北京冬奥...\n", "13  2021-11-21 09:56:49  据美国运输安全管理局（TSA）消息，亚特兰大机场此前发生枪支意外走火事件，至少造成3人受伤。...\n", "14  2021-11-21 09:49:30  【成都市郫都区全域降为低风险区】据成都市郫都区新型冠状病毒肺炎疫情防控指挥部，11月21日解...\n", "15  2021-11-21 09:40:11  【世卫组织：若不采取紧急措施 至明年3月欧洲或有50万新冠死亡病例】世卫组织欧洲区域办事处主...\n", "16  2021-11-21 09:33:21  <a href=\"https://app.jin10.com/\" target=\"_blan...\n", "17  2021-11-21 09:26:50  【超百亿资金流入香港中国国债和政策性金融债ETF】金十数据11月21日讯，香港市场中资债券E...\n", "18  2021-11-21 09:20:19  <b>【金十出品】欢迎下载2021年11月22日-2021年11月28日财经数据与事件精美周...\n", "19  2021-11-21 09:13:15  <b>【今年底我国汽车保有量将超过3亿辆，新能源汽车产销量刷新纪录】</b>11月20日，据...\n", "20  2021-11-21 09:03:34   法国新增22678例新冠肺炎确诊病例；新增24例新冠肺炎死亡病例，累计死亡病例达118446例。\n", "21  2021-11-21 08:54:05  【也门胡塞武装使用无人机袭击沙特境内多个目标】据马西拉电视台报道，胡塞武装发言人叶海亚·萨里...\n", "22  2021-11-21 08:44:51  国家卫健委：昨日报告新增无症状感染者8例（均为境外输入）；当日转为确诊病例1例（为境外输入）...\n", "23  2021-11-21 08:43:50  国家卫健委：昨日新增本土确诊病例4例（辽宁3例，均在大连市；云南1例，在德宏傣族景颇族自治州...\n", "24  2021-11-21 08:43:31                       泰国新增7006例新冠肺炎确诊病例，新增29例死亡病例。\n", "25  2021-11-21 08:39:24  <b>【经济日报：直播带货要过品质关】</b>近日，有关调查显示，2020年中国直播电商市场...\n", "26  2021-11-21 08:31:58     韩国疾病控制和预防机构（KDCA）：韩国新增3120例新冠肺炎确诊病例，新增30例死亡病例。\n", "27  2021-11-21 08:28:33  【报道】黑龙江省应急管理厅微信公众号11月21日消息，黑龙江省启动重大气象灾害（暴雪）Ⅱ级应...\n", "28  2021-11-21 08:21:13                     英国新增40941例新冠肺炎确诊病例，新增150例死亡病例。\n", "29  2021-11-21 08:16:14  <b>【特斯拉目前正遭受大范围内的应用服务器中断问题】</b>据Electrek消息，特斯拉...\n", "30  2021-11-21 08:15:50  【四川成都市新都区一地调整为低风险地区】四川省成都市新都区三合嘉苑小区由中风险区降为低风险区...\n", "31  2021-11-21 08:14:26  美国疾控中心：美国已有超过2.298亿人至少接种了一剂新冠疫苗，超过已有1.961亿人完全接...\n", "32  2021-11-21 08:12:44  美国疾控中心：美国累计新冠肺炎确诊病例升至47587441例，累计新冠肺炎死亡病例升至770...\n", "33  2021-11-21 08:07:14                   辽宁昨日新增3例本土新冠肺炎确诊病例，为大连市报告。（央视新闻）\n", "34  2021-11-21 08:04:59              市场消息：美国纽约州新增7097例新冠肺炎确诊病例，新增176例死亡病例。\n", "35  2021-11-21 08:02:20         云南省昨日新增本土确诊病例1例，系陇川县集中隔离点密接人员核酸检测发现。（人民日报）\n", "36  2021-11-20 22:59:51  <b>【金十整理：周六重要消息大汇总】www.jin10.com</b><br/><b>国内...\n", "37  2021-11-20 22:50:20  【北京疾控：快递和外卖配送人员未全程接种新冠疫苗不得上岗】11月20日，北京市疾控中心发布新...\n", "38  2021-11-20 22:40:11  【江西上饶铅山县21日零时起有序恢复正常生产生活秩序】根据江西省上饶市铅山县当前疫情防控形势...\n", "39  2021-11-20 22:30:04                  爱尔兰新增5959例新冠肺炎确诊病例，单日新增病例为1月以来最多。\n", "40  2021-11-20 22:29:09    据荷兰媒体BNO Newsroom：荷兰新增21873例新冠肺炎确诊病例，新增55例死亡病例。\n", "41  2021-11-20 22:26:45     据沙特国营媒体：沙特领导的联军表示，在也门的行动击中了在萨那、萨达、马里卜的13个军事目标。\n", "42  2021-11-20 22:23:40                   据沙特国营媒体：沙特领导的联盟称，在也门对胡塞发动了重大的行动。\n", "43  2021-11-20 22:21:52  <b>OANDA资深市场分析师<PERSON>：预计黄金进入盘整</b><br/>下周...\n", "44  2021-11-20 22:10:32  【联发科发布首款支持8K 120Hz显示的7nm电视芯片】11月20日，联发科发布用于下一代...\n", "45  2021-11-20 21:57:10                        欧洲地中海地震中心：洛亚蒂群岛东南方发生5.2级地震。\n", "46  2021-11-20 21:55:41  【沙特为首的多国联军击落4架无人机并拦截2枚弹道导弹】沙特为首的多国联军当地时间20日下午发...\n", "47  2021-11-20 21:50:52  【花了1.18亿！B站拥抱支付牌照，力拼电商、金融业务】日前，知名视频平台哔哩哔哩bilib...\n", "48  2021-11-20 21:44:19               也门胡塞武装方面表示，用武装无人机袭击了位于吉达的阿卜杜勒国王国际机场。\n", "49  2021-11-20 21:38:42            <b>据俄罗斯卫星网：也门胡塞武装声称袭击了沙特的哈立德国王空军基地。</b>\n"]}], "source": ["js_news_df = ak.js_news(timestamp=\"2021-11-21 11:27:18\")\n", "print(js_news_df)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                               "]}, {"name": "stdout", "output_type": "stream", "text": ["        date                                              title  \\\n", "0   20211120                             “一带一路”建设高质量推进 中国方案惠及世界   \n", "1   20211120  高标准 可持续 惠民生 推动共建“一带一路”高质量发展——习近平总书记在第三次“一带一路”建...   \n", "2   20211120                        央视快评：推动共建“一带一路”高质量发展不断取得新成效   \n", "3   20211120              党的十九届六中全会在中管企业 中管金融企业 中管高校干部职工中引起热烈反响   \n", "4   20211120      人民日报评论员文章：锚定既定奋斗目标 意气风发走向未来——论学习贯彻党的十九届六中全会精神   \n", "5   20211120                                中央军委表彰全军备战标兵单位和标兵个人   \n", "6   20211120                                  北京冬奥村场馆进行全流程全要素测试   \n", "7   20211120                           国务院联防联控机制发布会：做好冬春季疫情防控工作   \n", "8   20211120                                      国家卫生健康委通报最新疫情   \n", "9   20211120                                冷空气将影响全国大部 局地降温超14℃   \n", "10  20211120                                             国内联播快讯   \n", "11  20211120                                 全球新冠肺炎确诊病例超2亿5532万   \n", "12  20211120                                             国际联播快讯   \n", "\n", "                                              content  \n", "0   2013年秋天，习近平主席访问哈萨克斯坦、印度尼西亚期间，先后提出共同建设“丝绸之路经济带”...  \n", "1   习近平总书记在第三次“一带一路”建设座谈会上发表的重要讲话，在与会代表中引起热烈反响。与会代...  \n", "2         本台今天（11月20日）播发央视快评《推动共建“一带一路”高质量发展不断取得新成效》。  \n", "3   党的十九届六中全会全面总结党的百年奋斗重大成就和历史经验，汇聚起坚定的历史自信和创造历史伟业...  \n", "4   今天（11月20日）出版的人民日报发表评论员文章，题目是《锚定既定奋斗目标 意气风发走向未来...  \n", "5   中央军委日前印发通报，对10个全军备战标兵单位和20名全军备战标兵个人予以表彰。 通报指出，...  \n", "6   11月20日至21日，北京冬奥村开展全流程、全要素的运行模拟测试，全面检验赛前运行状态。北京...  \n", "7   国务院联防联控机制今天（11月20日）下午举行新闻发布会，介绍进一步做好冬春季疫情防控工作有...  \n", "8   国家卫生健康委今天（11月20日）通报，11月19日0—24时，31个省（自治区、直辖市）和...  \n", "9   受较强冷空气影响，从昨天（11月19日）开始，新疆、甘肃、宁夏等地部分地区出现大风降温和雨雪...  \n", "10  前十个月我国制造业贷款增量超去年全年银保监会最新统计显示，今年前10个月，银行业新增制造业贷...  \n", "11  根据世界卫生组织的统计数据，全球累计新冠肺炎确诊病例已达255324963例，累计死亡病例5...  \n", "12  北约和欧盟公然挑衅 俄不会视而不见19日，俄罗斯外长拉夫罗夫表示，北约和欧盟在乌克兰鼓动军国...  \n"]}, {"name": "stderr", "output_type": "stream", "text": []}], "source": ["news_cctv_df = ak.news_cctv(date=\"20211120\")\n", "print(news_cctv_df)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    name   rate\n", "0    比亚迪   0.48\n", "1    士兰微  -1.38\n", "2    格林美   0.38\n", "3   立讯精密   5.51\n", "4   通威股份  -0.97\n", "5   深圳能源   0.25\n", "6   长电科技   0.80\n", "7   洛阳钼业   2.89\n", "8   节能风电  -0.91\n", "9   包钢股份   0.80\n", "10  三一重工   0.83\n", "11  中国平安   0.91\n", "12  隆基股份   2.42\n", "13  郑州煤电  -0.61\n", "14  东方财富   1.53\n", "15  三安光电   1.74\n", "16  紫金矿业   0.68\n", "17  长安汽车   0.83\n", "18   大北农   2.49\n", "19   欧菲光   0.57\n", "20   惠博普   1.35\n", "21  华银电力   0.17\n", "22  焦作万方   2.44\n", "23   酒鬼酒   0.56\n", "24  伊利股份  -0.85\n", "25  贵州茅台   2.03\n", "26  牧原股份  -1.24\n", "27  华天科技   2.03\n", "28  露笑科技  -2.18\n", "29  格力电器   0.45\n", "30  福田汽车   1.41\n", "31  长城汽车   0.24\n", "32  陕西黑猫   3.16\n", "33  金风科技   1.17\n", "34  兴化股份   1.94\n", "35  彩虹股份   2.57\n", "36  丹化科技   0.59\n", "37  章源钨业  -0.94\n", "38  洋河股份   0.27\n", "39  天齐锂业   0.22\n", "40  达安基因   0.37\n", "41  豫能控股  -1.37\n", "42   云天化   0.48\n", "43  亿纬锂能   1.04\n", "44  通富微电   0.82\n", "45  华兰生物   0.14\n", "46  赣锋锂业  -2.35\n", "47  中信证券   0.91\n", "48  智飞生物  -1.20\n", "49  天赐材料  -3.55\n"]}], "source": ["stock_js_weibo_report_df = ak.stock_js_weibo_report(time_period=\"CNHOUR12\")\n", "print(stock_js_weibo_report_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(1000)[:: 64]:\n", "\n", "    if 3000 - i < 64:\n", "        break\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["range(0, 1000, 64)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["range(1000)[:: 64]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}