"""
测试tokens_to_candlesticks函数

测试K线token转换回K线数据的功能
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入tokenizer
from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.base.nonlinear_tokenizer import NonlinearCandlestickTokenizer

def test_linear_tokenizer():
    """测试线性tokenizer的tokens_to_candlesticks函数"""
    print("\n=== 测试线性tokenizer的tokens_to_candlesticks函数 ===")

    # 创建tokenizer
    tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        atr_window=100,
        atr_mult=0.88,
        scale=10,
        include_volume=True
    )

    # 创建一些测试token
    tokens = [100, 101, 102, 103, 104]  # 随机选择的token

    # 测试正常情况
    print("\n测试正常情况:")
    start_price = 100.0
    atr = 1.0
    start_volume = 10000

    result = tokenizer.tokens_to_candlesticks(tokens, start_price, atr, start_volume)
    print("\n结果:")
    print(result)

    # 测试边界情况
    print("\n测试边界情况 - 无效的ATR:")
    result = tokenizer.tokens_to_candlesticks(tokens, start_price, 0, start_volume)
    print("\n结果:")
    print(result)

    print("\n测试边界情况 - 无效的起始价格:")
    result = tokenizer.tokens_to_candlesticks(tokens, -1, atr, start_volume)
    print("\n结果:")
    print(result)

    print("\n测试边界情况 - 空token列表:")
    result = tokenizer.tokens_to_candlesticks([], start_price, atr, start_volume)
    print("\n结果:")
    print(result)

    print("\n测试边界情况 - 无效token:")
    result = tokenizer.tokens_to_candlesticks([-1, 999999], start_price, atr, start_volume)
    print("\n结果:")
    print(result)

    return tokenizer

def test_nonlinear_tokenizer():
    """测试非线性tokenizer的tokens_to_candlesticks函数"""
    print("\n=== 测试非线性tokenizer的tokens_to_candlesticks函数 ===")

    # 创建tokenizer
    tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        atr_window=100,
        atr_mult=0.88,
        scale=10,
        include_volume=True
    )

    # 创建一些测试token
    tokens = [100, 101, 102, 103, 104]  # 随机选择的token

    # 测试正常情况
    print("\n测试正常情况:")
    start_price = 100.0
    atr = 1.0
    start_volume = 10000

    result = tokenizer.tokens_to_candlesticks(tokens, start_price, atr, start_volume)
    print("\n结果:")
    print(result)

    # 测试边界情况
    print("\n测试边界情况 - 无效的ATR:")
    result = tokenizer.tokens_to_candlesticks(tokens, start_price, 0, start_volume)
    print("\n结果:")
    print(result)

    print("\n测试边界情况 - 无效的起始价格:")
    result = tokenizer.tokens_to_candlesticks(tokens, -1, atr, start_volume)
    print("\n结果:")
    print(result)

    print("\n测试边界情况 - 空token列表:")
    result = tokenizer.tokens_to_candlesticks([], start_price, atr, start_volume)
    print("\n结果:")
    print(result)

    print("\n测试边界情况 - 无效token:")
    result = tokenizer.tokens_to_candlesticks([-1, 999999], start_price, atr, start_volume)
    print("\n结果:")
    print(result)

    return tokenizer

def test_with_real_tokens(tokenizer, tokens=None):
    """使用真实token测试"""
    print("\n=== 使用真实token测试 ===")

    if tokens is None:
        # 使用一些常见的token
        # 获取一些有效的token
        valid_tokens = []
        for i in range(100, 110):  # 尝试一些可能有效的token ID
            if i < tokenizer.vocab_size:
                valid_tokens.append(i)

        if not valid_tokens:
            valid_tokens = [0] * 5  # 如果没有找到有效token，使用0

        tokens = valid_tokens[:5]  # 取前5个

    print(f"测试tokens: {tokens}")

    # 测试正常情况
    start_price = 5850.6  # 使用与错误输出中相同的起始价格
    atr = 10.0  # 使用较大的ATR值

    result = tokenizer.tokens_to_candlesticks(tokens, start_price, atr)
    print("\n结果:")
    print(result)

    # 测试边界值
    print("\n测试边界值 - 全部使用下限值:")
    boundary_tokens = []
    for i in range(5):
        # 找到一个表示下限值的token
        for token_idx, token_str in tokenizer.idx2token.items():
            if token_str.startswith(f"{tokenizer.change_range[0]}|"):
                boundary_tokens.append(token_idx)
                break

    if not boundary_tokens:
        boundary_tokens = [0] * 5

    boundary_result = tokenizer.tokens_to_candlesticks(boundary_tokens, start_price, atr)
    print("\n结果:")
    print(boundary_result)

    return result

def main():
    """主函数"""
    try:
        # 测试线性tokenizer
        print("\n========== 测试线性tokenizer ==========")
        linear_tokenizer = test_linear_tokenizer()

        # 使用真实token测试线性tokenizer
        print("\n========== 使用真实token测试线性tokenizer ==========")
        test_with_real_tokens(linear_tokenizer)

        # 测试特定的问题token
        print("\n========== 测试特定问题token (线性tokenizer) ==========")
        problem_tokens = [0, 0, 0, 0, 0]  # 使用有效的token
        test_with_real_tokens(linear_tokenizer, problem_tokens)

        try:
            # 测试非线性tokenizer
            print("\n========== 测试非线性tokenizer ==========")
            nonlinear_tokenizer = test_nonlinear_tokenizer()

            # 使用真实token测试非线性tokenizer
            print("\n========== 使用真实token测试非线性tokenizer ==========")
            test_with_real_tokens(nonlinear_tokenizer)

            # 测试特定的问题token
            print("\n========== 测试特定问题token (非线性tokenizer) ==========")
            test_with_real_tokens(nonlinear_tokenizer, problem_tokens)
        except Exception as e:
            print(f"\n非线性tokenizer测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n所有测试完成")

if __name__ == "__main__":
    main()
