{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "openai.api_key = '***************************************************'\n", "openai.Model.list()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import openai\n", "openai.api_key = '***************************************************'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "APIConnectionError", "evalue": "Error communicating with OpenAI: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1131)')))", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mSSLEOFError\u001b[0m                               Traceback (most recent call last)", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connectionpool.py:700\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    699\u001b[0m \u001b[39mif\u001b[39;00m is_new_proxy_conn \u001b[39mand\u001b[39;00m http_tunnel_required:\n\u001b[1;32m--> 700\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_prepare_proxy(conn)\n\u001b[0;32m    702\u001b[0m \u001b[39m# Make the request on the httplib connection object.\u001b[39;00m\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connectionpool.py:996\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._prepare_proxy\u001b[1;34m(self, conn)\u001b[0m\n\u001b[0;32m    994\u001b[0m     conn\u001b[39m.\u001b[39mtls_in_tls_required \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n\u001b[1;32m--> 996\u001b[0m conn\u001b[39m.\u001b[39;49mconnect()\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connection.py:364\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    363\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mtls_in_tls_required:\n\u001b[1;32m--> 364\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39msock \u001b[39m=\u001b[39m conn \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_connect_tls_proxy(hostname, conn)\n\u001b[0;32m    365\u001b[0m     tls_in_tls \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connection.py:499\u001b[0m, in \u001b[0;36mHTTPSConnection._connect_tls_proxy\u001b[1;34m(self, hostname, conn)\u001b[0m\n\u001b[0;32m    497\u001b[0m \u001b[39m# If no cert was provided, use only the default options for server\u001b[39;00m\n\u001b[0;32m    498\u001b[0m \u001b[39m# certificate validation\u001b[39;00m\n\u001b[1;32m--> 499\u001b[0m socket \u001b[39m=\u001b[39m ssl_wrap_socket(\n\u001b[0;32m    500\u001b[0m     sock\u001b[39m=\u001b[39;49mconn,\n\u001b[0;32m    501\u001b[0m     ca_certs\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mca_certs,\n\u001b[0;32m    502\u001b[0m     ca_cert_dir\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mca_cert_dir,\n\u001b[0;32m    503\u001b[0m     ca_cert_data\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mca_cert_data,\n\u001b[0;32m    504\u001b[0m     server_hostname\u001b[39m=\u001b[39;49mhostname,\n\u001b[0;32m    505\u001b[0m     ssl_context\u001b[39m=\u001b[39;49mssl_context,\n\u001b[0;32m    506\u001b[0m )\n\u001b[0;32m    508\u001b[0m \u001b[39mif\u001b[39;00m ssl_context\u001b[39m.\u001b[39mverify_mode \u001b[39m!=\u001b[39m ssl\u001b[39m.\u001b[39mCERT_NONE \u001b[39mand\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mgetattr\u001b[39m(\n\u001b[0;32m    509\u001b[0m     ssl_context, \u001b[39m\"\u001b[39m\u001b[39mcheck_hostname\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39mFalse\u001b[39;00m\n\u001b[0;32m    510\u001b[0m ):\n\u001b[0;32m    511\u001b[0m     \u001b[39m# While urllib3 attempts to always turn off hostname matching from\u001b[39;00m\n\u001b[0;32m    512\u001b[0m     \u001b[39m# the TLS library, this cannot always be done. So we check whether\u001b[39;00m\n\u001b[0;32m    513\u001b[0m     \u001b[39m# the TLS Library still thinks it's matching hostnames.\u001b[39;00m\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\util\\ssl_.py:453\u001b[0m, in \u001b[0;36mssl_wrap_socket\u001b[1;34m(sock, keyfile, certfile, cert_reqs, ca_certs, server_hostname, ssl_version, ciphers, ssl_context, ca_cert_dir, key_password, ca_cert_data, tls_in_tls)\u001b[0m\n\u001b[0;32m    452\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m--> 453\u001b[0m     ssl_sock \u001b[39m=\u001b[39m _ssl_wrap_socket_impl(sock, context, tls_in_tls)\n\u001b[0;32m    454\u001b[0m \u001b[39mreturn\u001b[39;00m ssl_sock\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\util\\ssl_.py:495\u001b[0m, in \u001b[0;36m_ssl_wrap_socket_impl\u001b[1;34m(sock, ssl_context, tls_in_tls, server_hostname)\u001b[0m\n\u001b[0;32m    494\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m--> 495\u001b[0m     \u001b[39mreturn\u001b[39;00m ssl_context\u001b[39m.\u001b[39;49mwrap_socket(sock)\n", "File \u001b[1;32md:\\Miniconda3\\lib\\ssl.py:500\u001b[0m, in \u001b[0;36mSSLContext.wrap_socket\u001b[1;34m(self, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, session)\u001b[0m\n\u001b[0;32m    494\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mwrap_socket\u001b[39m(\u001b[39mself\u001b[39m, sock, server_side\u001b[39m=\u001b[39m\u001b[39mFalse\u001b[39;00m,\n\u001b[0;32m    495\u001b[0m                 do_handshake_on_connect\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m,\n\u001b[0;32m    496\u001b[0m                 suppress_ragged_eofs\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m,\n\u001b[0;32m    497\u001b[0m                 server_hostname\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, session\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m):\n\u001b[0;32m    498\u001b[0m     \u001b[39m# SSLSocket class handles server_hostname encoding before it calls\u001b[39;00m\n\u001b[0;32m    499\u001b[0m     \u001b[39m# ctx._wrap_socket()\u001b[39;00m\n\u001b[1;32m--> 500\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49msslsocket_class\u001b[39m.\u001b[39;49m_create(\n\u001b[0;32m    501\u001b[0m         sock\u001b[39m=\u001b[39;49msock,\n\u001b[0;32m    502\u001b[0m         server_side\u001b[39m=\u001b[39;49mserver_side,\n\u001b[0;32m    503\u001b[0m         do_handshake_on_connect\u001b[39m=\u001b[39;49mdo_handshake_on_connect,\n\u001b[0;32m    504\u001b[0m         suppress_ragged_eofs\u001b[39m=\u001b[39;49msuppress_ragged_eofs,\n\u001b[0;32m    505\u001b[0m         server_hostname\u001b[39m=\u001b[39;49mserver_hostname,\n\u001b[0;32m    506\u001b[0m         context\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m,\n\u001b[0;32m    507\u001b[0m         session\u001b[39m=\u001b[39;49msession\n\u001b[0;32m    508\u001b[0m     )\n", "File \u001b[1;32md:\\Miniconda3\\lib\\ssl.py:1040\u001b[0m, in \u001b[0;36mSSLSocket._create\u001b[1;34m(cls, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, context, session)\u001b[0m\n\u001b[0;32m   1039\u001b[0m             \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\u001b[39m\"\u001b[39m\u001b[39mdo_handshake_on_connect should not be specified for non-blocking sockets\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m-> 1040\u001b[0m         \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mdo_handshake()\n\u001b[0;32m   1041\u001b[0m \u001b[39mexcept\u001b[39;00m (\u001b[39mOSError\u001b[39;00m, \u001b[39mValueError\u001b[39;00m):\n", "File \u001b[1;32md:\\Miniconda3\\lib\\ssl.py:1309\u001b[0m, in \u001b[0;36mSSLSocket.do_handshake\u001b[1;34m(self, block)\u001b[0m\n\u001b[0;32m   1308\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39msettimeout(\u001b[39mNone\u001b[39;00m)\n\u001b[1;32m-> 1309\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_sslobj\u001b[39m.\u001b[39;49mdo_handshake()\n\u001b[0;32m   1310\u001b[0m \u001b[39mfinally\u001b[39;00m:\n", "\u001b[1;31mSSLEOFError\u001b[0m: EOF occurred in violation of protocol (_ssl.c:1131)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\requests\\adapters.py:489\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    488\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m chunked:\n\u001b[1;32m--> 489\u001b[0m     resp \u001b[39m=\u001b[39m conn\u001b[39m.\u001b[39;49murlopen(\n\u001b[0;32m    490\u001b[0m         method\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mmethod,\n\u001b[0;32m    491\u001b[0m         url\u001b[39m=\u001b[39;49murl,\n\u001b[0;32m    492\u001b[0m         body\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mbody,\n\u001b[0;32m    493\u001b[0m         headers\u001b[39m=\u001b[39;49mrequest\u001b[39m.\u001b[39;49mheaders,\n\u001b[0;32m    494\u001b[0m         redirect\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[0;32m    495\u001b[0m         assert_same_host\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[0;32m    496\u001b[0m         preload_content\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[0;32m    497\u001b[0m         decode_content\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m,\n\u001b[0;32m    498\u001b[0m         retries\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mmax_retries,\n\u001b[0;32m    499\u001b[0m         timeout\u001b[39m=\u001b[39;49mtimeout,\n\u001b[0;32m    500\u001b[0m     )\n\u001b[0;32m    502\u001b[0m \u001b[39m# Send the request.\u001b[39;00m\n\u001b[0;32m    503\u001b[0m \u001b[39melse\u001b[39;00m:\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connectionpool.py:815\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    812\u001b[0m     log\u001b[39m.\u001b[39mwarning(\n\u001b[0;32m    813\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mRetrying (\u001b[39m\u001b[39m%r\u001b[39;00m\u001b[39m) after connection broken by \u001b[39m\u001b[39m'\u001b[39m\u001b[39m%r\u001b[39;00m\u001b[39m'\u001b[39m\u001b[39m: \u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m\"\u001b[39m, retries, err, url\n\u001b[0;32m    814\u001b[0m     )\n\u001b[1;32m--> 815\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49murlopen(\n\u001b[0;32m    816\u001b[0m         method,\n\u001b[0;32m    817\u001b[0m         url,\n\u001b[0;32m    818\u001b[0m         body,\n\u001b[0;32m    819\u001b[0m         headers,\n\u001b[0;32m    820\u001b[0m         retries,\n\u001b[0;32m    821\u001b[0m         redirect,\n\u001b[0;32m    822\u001b[0m         assert_same_host,\n\u001b[0;32m    823\u001b[0m         timeout\u001b[39m=\u001b[39;49mtimeout,\n\u001b[0;32m    824\u001b[0m         pool_timeout\u001b[39m=\u001b[39;49mpool_timeout,\n\u001b[0;32m    825\u001b[0m         release_conn\u001b[39m=\u001b[39;49mrelease_conn,\n\u001b[0;32m    826\u001b[0m         chunked\u001b[39m=\u001b[39;49mchunked,\n\u001b[0;32m    827\u001b[0m         body_pos\u001b[39m=\u001b[39;49mbody_pos,\n\u001b[0;32m    828\u001b[0m         \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mresponse_kw\n\u001b[0;32m    829\u001b[0m     )\n\u001b[0;32m    831\u001b[0m \u001b[39m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connectionpool.py:815\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    812\u001b[0m     log\u001b[39m.\u001b[39mwarning(\n\u001b[0;32m    813\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mRetrying (\u001b[39m\u001b[39m%r\u001b[39;00m\u001b[39m) after connection broken by \u001b[39m\u001b[39m'\u001b[39m\u001b[39m%r\u001b[39;00m\u001b[39m'\u001b[39m\u001b[39m: \u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m\"\u001b[39m, retries, err, url\n\u001b[0;32m    814\u001b[0m     )\n\u001b[1;32m--> 815\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49murlopen(\n\u001b[0;32m    816\u001b[0m         method,\n\u001b[0;32m    817\u001b[0m         url,\n\u001b[0;32m    818\u001b[0m         body,\n\u001b[0;32m    819\u001b[0m         headers,\n\u001b[0;32m    820\u001b[0m         retries,\n\u001b[0;32m    821\u001b[0m         redirect,\n\u001b[0;32m    822\u001b[0m         assert_same_host,\n\u001b[0;32m    823\u001b[0m         timeout\u001b[39m=\u001b[39;49mtimeout,\n\u001b[0;32m    824\u001b[0m         pool_timeout\u001b[39m=\u001b[39;49mpool_timeout,\n\u001b[0;32m    825\u001b[0m         release_conn\u001b[39m=\u001b[39;49mrelease_conn,\n\u001b[0;32m    826\u001b[0m         chunked\u001b[39m=\u001b[39;49mchunked,\n\u001b[0;32m    827\u001b[0m         body_pos\u001b[39m=\u001b[39;49mbody_pos,\n\u001b[0;32m    828\u001b[0m         \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mresponse_kw\n\u001b[0;32m    829\u001b[0m     )\n\u001b[0;32m    831\u001b[0m \u001b[39m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\connectionpool.py:787\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    785\u001b[0m     e \u001b[39m=\u001b[39m ProtocolError(\u001b[39m\"\u001b[39m\u001b[39mConnection aborted.\u001b[39m\u001b[39m\"\u001b[39m, e)\n\u001b[1;32m--> 787\u001b[0m retries \u001b[39m=\u001b[39m retries\u001b[39m.\u001b[39;49mincrement(\n\u001b[0;32m    788\u001b[0m     method, url, error\u001b[39m=\u001b[39;49me, _pool\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m, _stacktrace\u001b[39m=\u001b[39;49msys\u001b[39m.\u001b[39;49mexc_info()[\u001b[39m2\u001b[39;49m]\n\u001b[0;32m    789\u001b[0m )\n\u001b[0;32m    790\u001b[0m retries\u001b[39m.\u001b[39msleep()\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\urllib3\\util\\retry.py:592\u001b[0m, in \u001b[0;36mRetry.increment\u001b[1;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[0;32m    591\u001b[0m \u001b[39mif\u001b[39;00m new_retry\u001b[39m.\u001b[39mis_exhausted():\n\u001b[1;32m--> 592\u001b[0m     \u001b[39mraise\u001b[39;00m MaxRetryError(_pool, url, error \u001b[39mor\u001b[39;00m ResponseError(cause))\n\u001b[0;32m    594\u001b[0m log\u001b[39m.\u001b[39mdebug(\u001b[39m\"\u001b[39m\u001b[39mIncremented Retry for (url=\u001b[39m\u001b[39m'\u001b[39m\u001b[39m%s\u001b[39;00m\u001b[39m'\u001b[39m\u001b[39m): \u001b[39m\u001b[39m%r\u001b[39;00m\u001b[39m\"\u001b[39m, url, new_retry)\n", "\u001b[1;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1131)')))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\openai\\api_requestor.py:516\u001b[0m, in \u001b[0;36mAPIRequestor.request_raw\u001b[1;34m(self, method, url, params, supplied_headers, files, stream, request_id, request_timeout)\u001b[0m\n\u001b[0;32m    515\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m--> 516\u001b[0m     result \u001b[39m=\u001b[39m _thread_context\u001b[39m.\u001b[39;49msession\u001b[39m.\u001b[39;49mrequest(\n\u001b[0;32m    517\u001b[0m         method,\n\u001b[0;32m    518\u001b[0m         abs_url,\n\u001b[0;32m    519\u001b[0m         headers\u001b[39m=\u001b[39;49mheaders,\n\u001b[0;32m    520\u001b[0m         data\u001b[39m=\u001b[39;49mdata,\n\u001b[0;32m    521\u001b[0m         files\u001b[39m=\u001b[39;49mfiles,\n\u001b[0;32m    522\u001b[0m         stream\u001b[39m=\u001b[39;49mstream,\n\u001b[0;32m    523\u001b[0m         timeout\u001b[39m=\u001b[39;49mrequest_timeout \u001b[39mif\u001b[39;49;00m request_timeout \u001b[39melse\u001b[39;49;00m TIMEOUT_SECS,\n\u001b[0;32m    524\u001b[0m     )\n\u001b[0;32m    525\u001b[0m \u001b[39mexcept\u001b[39;00m requests\u001b[39m.\u001b[39mexceptions\u001b[39m.\u001b[39mTimeout \u001b[39mas\u001b[39;00m e:\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\requests\\sessions.py:587\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    586\u001b[0m send_kwargs\u001b[39m.\u001b[39mupdate(settings)\n\u001b[1;32m--> 587\u001b[0m resp \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49msend(prep, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49msend_kwargs)\n\u001b[0;32m    589\u001b[0m \u001b[39mreturn\u001b[39;00m resp\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\requests\\sessions.py:701\u001b[0m, in \u001b[0;36mSession.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    700\u001b[0m \u001b[39m# Send the request\u001b[39;00m\n\u001b[1;32m--> 701\u001b[0m r \u001b[39m=\u001b[39m adapter\u001b[39m.\u001b[39;49msend(request, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[0;32m    703\u001b[0m \u001b[39m# Total elapsed time of the request (approximately)\u001b[39;00m\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\requests\\adapters.py:563\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    561\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39misinstance\u001b[39m(e\u001b[39m.\u001b[39mreason, _SSLError):\n\u001b[0;32m    562\u001b[0m     \u001b[39m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[1;32m--> 563\u001b[0m     \u001b[39mraise\u001b[39;00m SSLError(e, request\u001b[39m=\u001b[39mrequest)\n\u001b[0;32m    565\u001b[0m \u001b[39mraise\u001b[39;00m \u001b[39mConnectionError\u001b[39;00m(e, request\u001b[39m=\u001b[39mrequest)\n", "\u001b[1;31mSSLError\u001b[0m: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1131)')))", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mAPIConnectionError\u001b[0m                        Traceback (most recent call last)", "\u001b[1;32me:\\lab\\RoboQuant\\pylab\\tests\\chatgpt_api.ipynb 单元格 3\u001b[0m in \u001b[0;36m1\n\u001b[1;32m----> <a href='vscode-notebook-cell:/e%3A/lab/RoboQuant/pylab/tests/chatgpt_api.ipynb#W2sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m completion \u001b[39m=\u001b[39m openai\u001b[39m.\u001b[39;49mChatCompletion\u001b[39m.\u001b[39;49mcreate (\n\u001b[0;32m      <a href='vscode-notebook-cell:/e%3A/lab/RoboQuant/pylab/tests/chatgpt_api.ipynb#W2sZmlsZQ%3D%3D?line=1'>2</a>\u001b[0m  model\u001b[39m=\u001b[39;49m\u001b[39m\"\u001b[39;49m\u001b[39mgpt-3.5-turbo\u001b[39;49m\u001b[39m\"\u001b[39;49m, \n\u001b[0;32m      <a href='vscode-notebook-cell:/e%3A/lab/RoboQuant/pylab/tests/chatgpt_api.ipynb#W2sZmlsZQ%3D%3D?line=2'>3</a>\u001b[0m  messages\u001b[39m=\u001b[39;49m[{\u001b[39m\"\u001b[39;49m\u001b[39mrole\u001b[39;49m\u001b[39m\"\u001b[39;49m: \u001b[39m\"\u001b[39;49m\u001b[39muser\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39m\"\u001b[39;49m\u001b[39mcontent\u001b[39;49m\u001b[39m\"\u001b[39;49m: \u001b[39m\"\u001b[39;49m\u001b[39mTell the world about the ChatGPT API in the style of a pirate.\u001b[39;49m\u001b[39m\"\u001b[39;49m}]\n\u001b[0;32m      <a href='vscode-notebook-cell:/e%3A/lab/RoboQuant/pylab/tests/chatgpt_api.ipynb#W2sZmlsZQ%3D%3D?line=3'>4</a>\u001b[0m )\n\u001b[0;32m      <a href='vscode-notebook-cell:/e%3A/lab/RoboQuant/pylab/tests/chatgpt_api.ipynb#W2sZmlsZQ%3D%3D?line=5'>6</a>\u001b[0m \u001b[39mprint\u001b[39m (completion\u001b[39m.\u001b[39mchoices[\u001b[39m0\u001b[39m]\u001b[39m.\u001b[39mtext)\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\openai\\api_resources\\chat_completion.py:25\u001b[0m, in \u001b[0;36mChatCompletion.create\u001b[1;34m(cls, *args, **kwargs)\u001b[0m\n\u001b[0;32m     23\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[0;32m     24\u001b[0m     \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m---> 25\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49mcreate(\u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[0;32m     26\u001b[0m     \u001b[39mexcept\u001b[39;00m <PERSON>gain \u001b[39mas\u001b[39;00m e:\n\u001b[0;32m     27\u001b[0m         \u001b[39mif\u001b[39;00m timeout \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m time\u001b[39m.\u001b[39mtime() \u001b[39m>\u001b[39m start \u001b[39m+\u001b[39m timeout:\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\openai\\api_resources\\abstract\\engine_api_resource.py:153\u001b[0m, in \u001b[0;36mEngineAPIResource.create\u001b[1;34m(cls, api_key, api_base, api_type, request_id, api_version, organization, **params)\u001b[0m\n\u001b[0;32m    127\u001b[0m \u001b[39m@classmethod\u001b[39m\n\u001b[0;32m    128\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mcreate\u001b[39m(\n\u001b[0;32m    129\u001b[0m     \u001b[39mcls\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    136\u001b[0m     \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mparams,\n\u001b[0;32m    137\u001b[0m ):\n\u001b[0;32m    138\u001b[0m     (\n\u001b[0;32m    139\u001b[0m         deployment_id,\n\u001b[0;32m    140\u001b[0m         engine,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    150\u001b[0m         api_key, api_base, api_type, api_version, organization, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mparams\n\u001b[0;32m    151\u001b[0m     )\n\u001b[1;32m--> 153\u001b[0m     response, _, api_key \u001b[39m=\u001b[39m requestor\u001b[39m.\u001b[39;49mrequest(\n\u001b[0;32m    154\u001b[0m         \u001b[39m\"\u001b[39;49m\u001b[39mpost\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[0;32m    155\u001b[0m         url,\n\u001b[0;32m    156\u001b[0m         params\u001b[39m=\u001b[39;49mparams,\n\u001b[0;32m    157\u001b[0m         headers\u001b[39m=\u001b[39;49mheaders,\n\u001b[0;32m    158\u001b[0m         stream\u001b[39m=\u001b[39;49mstream,\n\u001b[0;32m    159\u001b[0m         request_id\u001b[39m=\u001b[39;49mrequest_id,\n\u001b[0;32m    160\u001b[0m         request_timeout\u001b[39m=\u001b[39;49mrequest_timeout,\n\u001b[0;32m    161\u001b[0m     )\n\u001b[0;32m    163\u001b[0m     \u001b[39mif\u001b[39;00m stream:\n\u001b[0;32m    164\u001b[0m         \u001b[39m# must be an iterator\u001b[39;00m\n\u001b[0;32m    165\u001b[0m         \u001b[39massert\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39misinstance\u001b[39m(response, OpenAIResponse)\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\openai\\api_requestor.py:216\u001b[0m, in \u001b[0;36mAPIRequestor.request\u001b[1;34m(self, method, url, params, headers, files, stream, request_id, request_timeout)\u001b[0m\n\u001b[0;32m    205\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mrequest\u001b[39m(\n\u001b[0;32m    206\u001b[0m     \u001b[39mself\u001b[39m,\n\u001b[0;32m    207\u001b[0m     method,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    214\u001b[0m     request_timeout: Optional[Union[\u001b[39mfloat\u001b[39m, Tuple[\u001b[39mfloat\u001b[39m, \u001b[39mfloat\u001b[39m]]] \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m,\n\u001b[0;32m    215\u001b[0m ) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m Tuple[Union[OpenAIResponse, Iterator[OpenAIResponse]], \u001b[39mbool\u001b[39m, \u001b[39mstr\u001b[39m]:\n\u001b[1;32m--> 216\u001b[0m     result \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mrequest_raw(\n\u001b[0;32m    217\u001b[0m         method\u001b[39m.\u001b[39;49mlower(),\n\u001b[0;32m    218\u001b[0m         url,\n\u001b[0;32m    219\u001b[0m         params\u001b[39m=\u001b[39;49mparams,\n\u001b[0;32m    220\u001b[0m         supplied_headers\u001b[39m=\u001b[39;49mheaders,\n\u001b[0;32m    221\u001b[0m         files\u001b[39m=\u001b[39;49mfiles,\n\u001b[0;32m    222\u001b[0m         stream\u001b[39m=\u001b[39;49mstream,\n\u001b[0;32m    223\u001b[0m         request_id\u001b[39m=\u001b[39;49mrequest_id,\n\u001b[0;32m    224\u001b[0m         request_timeout\u001b[39m=\u001b[39;49mrequest_timeout,\n\u001b[0;32m    225\u001b[0m     )\n\u001b[0;32m    226\u001b[0m     resp, got_stream \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_interpret_response(result, stream)\n\u001b[0;32m    227\u001b[0m     \u001b[39mreturn\u001b[39;00m resp, got_stream, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mapi_key\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\openai\\api_requestor.py:528\u001b[0m, in \u001b[0;36mAPIRequestor.request_raw\u001b[1;34m(self, method, url, params, supplied_headers, files, stream, request_id, request_timeout)\u001b[0m\n\u001b[0;32m    526\u001b[0m     \u001b[39mraise\u001b[39;00m error\u001b[39m.\u001b[39mTimeout(\u001b[39m\"\u001b[39m\u001b[39mRequest timed out: \u001b[39m\u001b[39m{}\u001b[39;00m\u001b[39m\"\u001b[39m\u001b[39m.\u001b[39mformat(e)) \u001b[39mfrom\u001b[39;00m \u001b[39me\u001b[39;00m\n\u001b[0;32m    527\u001b[0m \u001b[39mexcept\u001b[39;00m requests\u001b[39m.\u001b[39mexceptions\u001b[39m.\u001b[39mRequestException \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m--> 528\u001b[0m     \u001b[39mraise\u001b[39;00m error\u001b[39m.\u001b[39mAPIConnectionError(\n\u001b[0;32m    529\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mError communicating with OpenAI: \u001b[39m\u001b[39m{}\u001b[39;00m\u001b[39m\"\u001b[39m\u001b[39m.\u001b[39mformat(e)\n\u001b[0;32m    530\u001b[0m     ) \u001b[39mfrom\u001b[39;00m \u001b[39me\u001b[39;00m\n\u001b[0;32m    531\u001b[0m util\u001b[39m.\u001b[39mlog_debug(\n\u001b[0;32m    532\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39mOpenAI API response\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[0;32m    533\u001b[0m     path\u001b[39m=\u001b[39mabs_url,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    536\u001b[0m     request_id\u001b[39m=\u001b[39mresult\u001b[39m.\u001b[39mheaders\u001b[39m.\u001b[39mget(\u001b[39m\"\u001b[39m\u001b[39mX-Request-Id\u001b[39m\u001b[39m\"\u001b[39m),\n\u001b[0;32m    537\u001b[0m )\n\u001b[0;32m    538\u001b[0m \u001b[39m# Don't read the whole stream for debug logging unless necessary.\u001b[39;00m\n", "\u001b[1;31mAPIConnectionError\u001b[0m: <PERSON><PERSON><PERSON> communicating with OpenAI: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1131)')))"]}], "source": ["\n", "completion = openai.ChatCompletion.create (\n", " model=\"gpt-3.5-turbo\", \n", " messages=[{\"role\": \"user\", \"content\": \"Tell the world about the ChatGPT API in the style of a pirate.\"}]\n", ")\n", "\n", "print (completion.choices[0].text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}