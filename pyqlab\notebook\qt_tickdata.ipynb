{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 盘口数据特征\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- tick数据量太大，如果保存所有数据，没有足够的存储空间\n", "    - 只保存主连合约\n", "    - 保存文件格式除考虑支持压缩\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import date, datetime\n", "from pyqlab.const import MAIN_FUT_MARKET_CODES, SF_FUT_CODES\n", "data_path = 'f:/hqdata'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_exist_folder(path: str, sub_folder: str):\n", "    return sub_folder in os.listdir(path) \n", "    \n", "def is_exist_file(filename: str):\n", "    return os.path.isfile(filename)\n", "\n", "def get_dir_list(dir_path):\n", "    # Get a list of the content inside the directory\n", "    files = os.listdir(dir_path)\n", "\n", "    # Print out the list of files\n", "    dir_list = []\n", "    for file in files:\n", "        dir_list.append(file)\n", "    return dir_list    \n", "\n", "def year_month_segment(year: int):\n", "    seg = []\n", "    for month in range(1, 13):\n", "        first_day = datetime.date(year, month, 1)\n", "        if month == 12:\n", "            last_day = datetime.date(year+1, 1, 1) - datetime.timedelta(days=1)\n", "        else:\n", "            last_day = datetime.date(year, month+1, 1) - datetime.timedelta(days=1)\n", "        seg.append((first_day.strftime('%Y%m%d'), last_day.strftime('%Y%m%d')))\n", "        # print(f\"Month {month}: First day: {first_day.strftime('%Y%m%d')}, Last day: {last_day.strftime('%Y%m%d')}\")\n", "    return seg\n", "\n", "def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def restore_order_of_night_trading_time(df: pd.DataFrame):\n", "    \"\"\"恢复夜盘时间顺序\"\"\"\n", "    if \"datetime\" not in df.columns:\n", "        raise Exception(\"datetime column not exists\")\n", "    # 分成两个dataframe\n", "    df1 = df.loc[df['datetime'].dt.hour < 20]\n", "    df2 = df.loc[df['datetime'].dt.hour >= 20] # 夜盘时间\n", "    # 进一步分批处理\n", "    df2_1 = df2.loc[(df2['datetime'].dt.weekday <= 4) & (df2['datetime'].dt.weekday > 0)]\n", "    df2_2 = df2.loc[df2['datetime'].dt.weekday == 0]\n", "    del df2\n", "    #如果datetime是星期二到星期五，且时间在21:00到24:00之间，那么datetime减一天\n", "    df2_1['datetime'] = df2_1['datetime'] - pd.<PERSON><PERSON><PERSON>(days=1)\n", "    #如果datetime是星期一，且时间在21:00到24:00之间，那么datetime减三天\n", "    df2_2['datetime'] = df2_2['datetime'] - pd.<PERSON><PERSON><PERSON>(days=3)\n", "    dfs = pd.concat([df1, df2_1, df2_2])\n", "    dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "    dfs.reset_index(drop=True, inplace=True)\n", "    return dfs\n", "\n", "def read_csv_file(fname, code, market):\n", "    # columns=[\n", "    #     \"交易日\",\"合约代码\",\"交易所代码\",\"合约在交易所的代码\",\n", "    #     \"最新价\",\"上次结算价\",\"昨收盘\",\"昨持仓量\",\"今开盘\",\"最高价\",\n", "    #     \"最低价\",\"数量\",\"成交金额\",\"持仓量\",\"今收盘\",\"本次结算价\",\"涨停板价\",\n", "    #     \"跌停板价\",\"昨虚实度\",\"今虚实度\",\"最后修改时间\",\"最后修改毫秒\",\n", "    #     \"申买价一\",\"申买量一\",\"申卖价一\",\"申卖量一\",\"申买价二\",\"申买量二\",\"申卖价二\",\"申卖量二\",\n", "    #     \"申买价三\",\"申买量三\",\"申卖价三\",\"申卖量三\",\"申买价四\",\"申买量四\",\"申卖价四\",\"申卖量四\",\n", "    #     \"申买价五\",\"申买量五\",\"申卖价五\",\"申卖量五\",\"当日均价\",\"业务日期\"\n", "    # ]\n", "    #read csv file \n", "    df = pd.read_csv(fname, encoding='gbk', header='infer')\n", "    df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]\n", "    # TODO: 是否要合并秒内的tick数据\n", "    # df = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'}).reset_index()\n", "    df['交易时间'] = df.apply(combine_datetime, axis=1)\n", "    df = df[['合约代码', '交易时间', '最新价', '数量']]\n", "    df = df.rename(columns={'合约代码': 'code', '交易时间': 'datetime', '最新价': 'price', '数量':'volume'})\n", "    df = df.loc[df['volume']>0]\n", "    df['code'] = f'{code}9999.{market}'\n", "    return df\n", "\n", "def read_csv_file2(fname, code, market):\n", "    # 对于上海期货交易所,有盘口数据\n", "    # columns=[\n", "    #     \"交易日\",\"合约代码\",\"交易所代码\",\"合约在交易所的代码\",\n", "    #     \"最新价\",\"上次结算价\",\"昨收盘\",\"昨持仓量\",\"今开盘\",\"最高价\",\n", "    #     \"最低价\",\"数量\",\"成交金额\",\"持仓量\",\"今收盘\",\"本次结算价\",\"涨停板价\",\n", "    #     \"跌停板价\",\"昨虚实度\",\"今虚实度\",\"最后修改时间\",\"最后修改毫秒\",\n", "    #     \"申买价一\",\"申买量一\",\"申卖价一\",\"申卖量一\",\"申买价二\",\"申买量二\",\"申卖价二\",\"申卖量二\",\n", "    #     \"申买价三\",\"申买量三\",\"申卖价三\",\"申卖量三\",\"申买价四\",\"申买量四\",\"申卖价四\",\"申卖量四\",\n", "    #     \"申买价五\",\"申买量五\",\"申卖价五\",\"申卖量五\",\"当日均价\",\"业务日期\"\n", "    # ]\n", "    #read csv file \n", "    df = pd.read_csv(fname, encoding='gbk', header='infer')\n", "    df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量',\n", "        '申买价一','申买量一','申卖价一','申卖量一','申买价二','申买量二','申卖价二','申卖量二',\n", "        '申买价三','申买量三','申卖价三','申卖量三','申买价四','申买量四','申卖价四','申卖量四',\n", "        '申买价五','申买量五','申卖价五','申卖量五']]\n", "    df['交易时间'] = df.apply(combine_datetime, axis=1)\n", "    df.drop(['交易日', '最后修改时间', '最后修改毫秒'], axis=1, inplace=True)\n", "    df = df.rename(columns={'合约代码': 'code', '交易时间': 'datetime', '最新价': 'price', '数量':'volume',\n", "        '申买价一':'bid_price1','申买量一':'bid_volume1','申卖价一':'ask_price1','申卖量一':'ask_volume1',\n", "        '申买价二':'bid_price2','申买量二':'bid_volume2','申卖价二':'ask_price2','申卖量二':'ask_volume2',\n", "        '申买价三':'bid_price3','申买量三':'bid_volume3','申卖价三':'ask_price3','申卖量三':'ask_volume3',\n", "        '申买价四':'bid_price4','申买量四':'bid_volume4','申卖价四':'ask_price4','申卖量四':'ask_volume4',\n", "        '申买价五':'bid_price5','申买量五':'bid_volume5','申卖价五':'ask_price5','申卖量五':'ask_volume5',})\n", "    df = df.loc[df['volume']>0]\n", "    df['code'] = f'{code}9999.{market}'\n", "    return df\n", "\n", "def test_signle_code():\n", "    # 加载主力日期切换表\n", "    # print(maintb)\n", "    # 起止日期间所有日期列表\n", "    start = '20230101'\n", "    end = '20230131'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td < td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = 'f:/hqdata/raw/2023' \n", "    # 有交易数据的日期列表\n", "    market = 'ZC' # 'SC', 'DC'\n", "    code='SA' # SA\n", "    dfs = pd.DataFrame()\n", "    for day in all_day:\n", "        # ag主力连续_20230213\n", "        fname = f'{directory}/{code}主力连续_{day}.csv'\n", "        if not is_exist_file(fname):\n", "            continue\n", "        print(f'read csv file: {fname}')\n", "        df = read_csv_file(fname)\n", "        dfs = pd.concat([dfs, df])\n", "        print(dfs.shape)\n", "    # 将按年汇总的tick数据写入文件\n", "    # print(f'{code}: {dfs.shape}')\n", "    # dfs.to_parquet(f'e:/hqdata/tick/{code}2020.parquet')\n", "    print(dfs.tail())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def etl_fut_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):\n", "    # 起止日期间所有日期列表\n", "    # start = '20230201'\n", "    # end = '20230231'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td <= td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = f'{data_path}/raw/{start[0:4]}' \n", "    cnt = 0\n", "    markets=['SC', 'ZC', 'DC'] # \n", "    dfs = pd.DataFrame()\n", "    for mk, codes in MAIN_FUT_MARKET_CODES.items():\n", "        for code in codes:\n", "            for day in all_day:\n", "                # 获取当日主力期货的代码名即文件名\n", "                if is_sec:\n", "                    fname = f'{directory}/{code}次主力连续_{day}.csv'\n", "                else:\n", "                    fname = f'{directory}/{code}主力连续_{day}.csv'\n", "                if not is_exist_file(fname):\n", "                    continue\n", "                df = read_csv_file(fname, code, mk)\n", "                dfs = pd.concat([dfs, df])\n", "            # 将按年汇总的tick数据写入文件\n", "            print(f'{cnt} {code}: {dfs.shape}')\n", "            cnt = cnt + 1\n", "    # dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    dfs = restore_order_of_night_trading_time(dfs) # 恢复夜盘时间顺序\n", "    if is_sec:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.sec.parquet')\n", "    else:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.parquet')\n", "    return dfs\n", "\n", "def etl_sf_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td <= td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = f'{data_path}/raw/{start[0:4]}sf' \n", "    cnt = 0\n", "    dfs = pd.DataFrame()\n", "    for code in SF_FUT_CODES:\n", "        for day in all_day:\n", "            # 获取当日主力期货的代码名即文件名\n", "            if is_sec:\n", "                fname = f'{directory}/{code}次主力连续_{day}.csv'\n", "            else:\n", "                fname = f'{directory}/{code}主力连续_{day}.csv'\n", "            if not is_exist_file(fname):\n", "                continue\n", "            df = read_csv_file(fname, code, 'SF')\n", "            dfs = pd.concat([dfs, df])\n", "        # 将按年汇总的tick数据写入文件\n", "        print(f'{cnt} {code}: {dfs.shape}')\n", "        cnt = cnt + 1\n", "    dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    if is_sec:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.sec.parquet')\n", "    else:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.parquet')\n", "    return dfs\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dt_seg=[('20230101', '20230131'), ('20230201', '20230228'), ('20230301', '20230331')]\n", "dt_seg=[('20230901', '20230930')] # 按月份分段导出\n", "for seg in dt_seg:\n", "    second = True # 是否导出次主力\n", "    df = etl_fut_tick_data(start=seg[0], end=seg[1], data_path=data_path, is_sec=second)\n", "    print(df.shape)\n", "    df = etl_sf_tick_data(start=seg[0], end=seg[1], data_path=data_path, is_sec=second)\n", "    print(df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Clearn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import datetime\n", "codes = {\n", "    'SC': ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG', 'RB', 'HC', 'BU', 'RU', 'FU', 'SP', 'WR', 'SC', 'NR', 'SS'],\n", "    'ZC': ['SR', '<PERSON>', 'Z<PERSON>', 'F<PERSON>', 'T<PERSON>', 'MA', 'O<PERSON>', 'R<PERSON>', 'C<PERSON>', 'WH', 'PM', 'R<PERSON>', 'L<PERSON>', '<PERSON>', 'RS', 'SF', 'SM', 'AP', 'CJ', 'UR', 'SA', 'PF', 'PK'], \n", "    'DC': ['M', 'Y', 'A', 'B', 'P', 'J', 'J<PERSON>', 'I', 'RR', 'C', 'CS', 'JD', 'BB', 'FB', 'L', 'V', 'PP', 'EG', 'EB', 'PG', 'LH']\n", "}\n", "sfcodes = ['IH', 'IF', 'IC', 'IM']\n", "data_path = 'f:/hqdata/tick'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}\", \"%Y%m%d %H:%M:%S %f\")\n", "    # return datetime.strptime(f\"{row['TradingDay']} {row['UpdateTime']} {row['UpdateMillisec']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def load_clearn_tick_data(code, start, end):\n", "    # 读取tick数据parquet文件\n", "    # filename = f'{self.data_path}/{start//10000}/sf/{code}{start//10000}.parquet'\n", "    filename = f'{data_path}/{start//10000}/{code}{start//10000}.parquet'\n", "    dt0 = datetime.strptime(str(start), '%Y%m%d')\n", "    dt1 = datetime.strptime(str(end), '%Y%m%d')\n", "    if os.path.isfile(filename):\n", "        df = pd.read_parquet(filename)\n", "        if start > 20230000:\n", "            # df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]\n", "            # df[['最新价', '数量']] = df[['最新价', '数量']].astype(float)\n", "            grouped = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'})\n", "            df = grouped.reset_index()\n", "            df['datetime'] = df.apply(combine_datetime, axis=1)\n", "            df = df[['合约代码', 'datetime', '最新价', '数量']]\n", "            df.to_parquet(filename, engine='fastparquet')\n", "            df = df.loc[(df['datetime'] >= dt0) & (df['datetime'] <= dt1)]\n", "        return df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "def load_all_combine_tick_data(start, end):\n", "    dfs = pd.DataFrame()\n", "    for mkt, codess in codes.items():\n", "        for code in codess:\n", "            df = load_clearn_tick_data(code, start, end)\n", "            if df.shape[0] > 0:\n", "                print(code, df.shape)\n", "                # df['label'] = f'{code}9999.{mkt}'\n", "                # dfs = pd.concat([dfs, df])\n", "    # if dfs.shape[0] > 0:\n", "    #     # dfs['datetime'] = dfs.apply(self.combine_datetime, axis=1)\n", "    #     dfs = dfs.sort_values(by='datetime', ascending=True)\n", "    return dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_all_combine_tick_data(start=20230101, end=20230228)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_parquet(f'{data_path}/tick/2023/SF202301.parquet')\n", "df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyqlab.scripts.SimulationServer import SimulationSvr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ss = SimulationSvr(ip=\"\", port=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = ss.load_tick_data('A', 20230101, 20230228)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}