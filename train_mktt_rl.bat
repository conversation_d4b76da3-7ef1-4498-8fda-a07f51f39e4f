e:
cd e:\lab\RoboQuant\pylab

@REM python ./pyqlab/rl/train_mkt_continue.py ^
@REM --data_file=ft_all.all.00170516142453003.csv ^
@REM --run_mode=train ^
@REM --block_name=fut ^
@REM --version=v1 ^
@REM --training_iteration=5000 ^
@REM --initial_amount=10000000 ^
@REM --max_drawdown=0.5 ^
@REM --stop_loss=0.3

python ./pyqlab/rl/train_mkt_continue.py ^
--data_file=ft_all.all.00170607085233003.csv ^
--run_mode=train ^
--block_name=sf ^
--version=v1 ^
--training_iteration=3500 ^
--initial_amount=1000000 ^
--max_drawdown=0.5 ^
--stop_loss=0.3

@REM 新版本API目前无法导出ONNX模型
@REM python ./pyqlab/rl/train_mkt_continue2.py ^
@REM --data_file=ft_all.all.00170607085233003.csv ^
@REM --run_mode=train ^
@REM --block_name=sf ^
@REM --version=v2 ^
@REM --train_iteration=2000 ^
@REM --initial_amount=1000000 ^
@REM --max_drawdown=0.5 ^
@REM --stop_loss=0.3 ^
@REM --transaction_cost=0.0001

@REM python ./pyqlab/rl/train_mkt_continue.py ^
@REM --data_file=ft_all.all.00170516142453003.csv ^
@REM --run_mode=train ^
@REM --block_name=fut ^
@REM --version=v2 ^
@REM --training_iteration=10000 ^
@REM --initial_amount=5000000

PAUSE