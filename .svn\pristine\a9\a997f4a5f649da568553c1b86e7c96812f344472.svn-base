# include the required libraries  
import csv
import redis

# connect to Redis
r = redis.Redis(host='localhost', port=6379, db=0)

# open the csv file
with open('data.csv', 'r') as csv_file:
    # parse csv records into dictionaries
    reader = csv.DictReader(csv_file)

    # iterate over each record 
    for row in reader:
        # set the data in redis using "localtime" as key
        r.set(row['localtime'], 
               {
               	'InstrumentID': row['InstrumentID'],
               	'TradingDay': row['TradingDay'],
               	'ActionDay': row['ActionDay'],
               	'UpdateTime': row['UpdateTime'],
               	'UpdateMillisec': row['UpdateMillisec'],
               	'LastPrice': row['LastPrice'],
               	'Volume': row['Volume'],
               	'HighestPrice': row['HighestPrice'],
               	'LowestPrice': row['LowestPrice'],
               	'OpenPrice': row['OpenPrice'],
               	'ClosePrice': row['ClosePrice'],
               	'AveragePrice': row['AveragePrice'],
               	'AskPrice1': row['AskPrice1'],
               	'AskVolume1': row['AskVolume1'],
               	'BidPrice1': row['BidPrice1'],
               	'BidVolume1': row['BidVolume1'],
               	'UpperLimitPrice': row['UpperLimitPrice'],
               	'LowerLimitPrice': row['LowerLimitPrice'],
               	'OpenInterest': row['OpenInterest'],
               	'Turnover': row['Turnover'],
               	'PreClosePrice': row['PreClosePrice'],
               	'PreOpenInterest': row['PreOpenInterest'],
               	'PreSettlementPrice': row['PreSettlementPrice']
               })

if __name__ == '__main__':
	pass