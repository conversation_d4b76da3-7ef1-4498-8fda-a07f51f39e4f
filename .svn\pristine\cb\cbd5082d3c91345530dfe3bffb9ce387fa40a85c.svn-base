# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.

import torch
import torch.nn as nn

class DropChannelLayyer(nn.Module):
    """
    在深层卷积之前先采用1*1的卷积进行降channel，然后再使用想要的卷积核进行卷积，
    在达到相同效果的前提下显著降低网络参数数量。
    """
    def __init__(self, in_channels, out_channels):
        super().__init__()

        self.conv = nn.Sequential(
            nn.Conv1d(in_channels=in_channels, out_channels=out_channels, kernel_size=1),
            nn.BatchNorm1d(out_channels),
            nn.GELU(),
        )

    def forward(self, x):
        return self.conv(x)


class AdaptiveLayyer(nn.Module):

    def __init__(self, out_channels=(32, 64)):
        super().__init__()
        self.prepare_output_cnn1 = nn.Sequential(
            nn.Conv1d(out_channels[0], 2, 1),
            nn.BatchNorm1d(2),
            nn.<PERSON><PERSON><PERSON>(),
        )
        
        self.prepare_output_cnn2 = nn.Sequential(
            nn.Conv1d(out_channels[1], out_channels[0], 1),
            nn.BatchNorm1d(out_channels[0]),
            nn.ReLU(),
            nn.Conv1d(out_channels[0], 2, 1), 
        )
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)            

    def forward(self, x1, x2):
        x1 = self.prepare_output_cnn1(x1)
        x2 = self.prepare_output_cnn2(x2)
        
        x1 = self.global_avg_pool(x1)
        x2 = self.global_avg_pool(x2)
        
        return torch.squeeze(x1 + x2, -1)    
    
def count_parameters(models_or_parameters, unit="m"):
    """
    This function is to obtain the storage size unit of a (or multiple) models.

    Parameters
    ----------
    models_or_parameters : PyTorch model(s) or a list of parameters.
    unit : the storage size unit.

    Returns
    -------
    The number of parameters of the given model(s) or parameters.
    """
    if isinstance(models_or_parameters, nn.Module):
        counts = sum(v.numel() for v in models_or_parameters.parameters())
    elif isinstance(models_or_parameters, nn.Parameter):
        counts = models_or_parameters.numel()
    elif isinstance(models_or_parameters, (list, tuple)):
        return sum(count_parameters(x, unit) for x in models_or_parameters)
    else:
        counts = sum(v.numel() for v in models_or_parameters)
    unit = unit.lower()
    if unit == "kb" or unit == "k":
        counts /= 2 ** 10
    elif unit == "mb" or unit == "m":
        counts /= 2 ** 20
    elif unit == "gb" or unit == "g":
        counts /= 2 ** 30
    elif unit is not None:
        raise ValueError("Unknow unit: {:}".format(unit))
    return counts
