import unittest
import sys
import os
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入回测器和相关模块
from pyqlab.models.gpt2.backtester import CandlestickLLMBacktester
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM


class MockModel(torch.nn.Module):
    """用于测试的模拟模型"""
    def __init__(self, vocab_size, trend='up'):
        super().__init__()
        self.vocab_size = vocab_size
        self.trend = trend  # 'up', 'down', 'random'
        
    def forward(self, input_tokens, code_ids, time_features=None, targets=None):
        batch_size, seq_len = input_tokens.size()
        
        # 创建logits
        logits = torch.zeros(batch_size, seq_len, self.vocab_size)
        
        # 根据趋势设置概率
        if self.trend == 'up':
            # 偏向上涨的token
            for i in range(self.vocab_size):
                token_str = str(i)
                if '|' in token_str and int(token_str.split('|')[0]) > 0:
                    logits[:, :, i] = 10.0
        elif self.trend == 'down':
            # 偏向下跌的token
            for i in range(self.vocab_size):
                token_str = str(i)
                if '|' in token_str and int(token_str.split('|')[0]) < 0:
                    logits[:, :, i] = 10.0
        else:
            # 随机趋势
            logits = torch.randn(batch_size, seq_len, self.vocab_size)
            
        # 返回logits和空损失
        return logits, None


def generate_mock_data(n_samples=100, trend='up', volatility=0.01, start_price=100.0):
    """生成模拟的K线数据"""
    # 生成日期序列
    dates = [datetime.now() - timedelta(days=n_samples-i) for i in range(n_samples)]
    
    # 生成价格序列
    prices = []
    price = start_price
    
    for i in range(n_samples):
        # 根据趋势生成价格变动
        if trend == 'up':
            change = np.random.normal(0.001, volatility)
        elif trend == 'down':
            change = np.random.normal(-0.001, volatility)
        else:
            change = np.random.normal(0, volatility)
            
        price *= (1 + change)
        prices.append(price)
    
    # 生成OHLC数据
    df = pd.DataFrame({
        'datetime': dates,
        'close': prices,
    })
    
    # 生成open, high, low
    df['open'] = df['close'].shift(1)
    df.loc[0, 'open'] = df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.01))
    
    for i in range(len(df)):
        high_range = np.random.uniform(0, 0.02)
        low_range = np.random.uniform(0, 0.02)
        df.loc[i, 'high'] = max(df.loc[i, 'open'], df.loc[i, 'close']) * (1 + high_range)
        df.loc[i, 'low'] = min(df.loc[i, 'open'], df.loc[i, 'close']) * (1 - low_range)
    
    # 生成交易量
    df['volume'] = np.random.randint(1000, 10000, size=len(df))
    
    return df


class TestCandlestickLLMBacktester(unittest.TestCase):
    """测试CandlestickLLMBacktester类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建tokenizer
        self.tokenizer = CandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            include_volume=True
        )
        
        # 创建模拟数据
        self.up_trend_data = generate_mock_data(n_samples=100, trend='up')
        self.down_trend_data = generate_mock_data(n_samples=100, trend='down')
        self.random_trend_data = generate_mock_data(n_samples=100, trend='random')
        
        # 创建模拟模型
        self.up_model = MockModel(self.tokenizer.vocab_size, trend='up')
        self.down_model = MockModel(self.tokenizer.vocab_size, trend='down')
        self.random_model = MockModel(self.tokenizer.vocab_size, trend='random')
        
        # 创建回测器
        self.up_backtester = CandlestickLLMBacktester(self.up_model, self.tokenizer)
        self.down_backtester = CandlestickLLMBacktester(self.down_model, self.tokenizer)
        self.random_backtester = CandlestickLLMBacktester(self.random_model, self.tokenizer)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.up_backtester.initial_capital, 10000.0)
        self.assertEqual(self.up_backtester.model, self.up_model)
        self.assertEqual(self.up_backtester.tokenizer, self.tokenizer)
    
    def test_backtest_up_trend(self):
        """测试上涨趋势回测"""
        # 使用上涨趋势模型回测上涨趋势数据
        results = self.up_backtester.backtest(self.up_trend_data, seq_len=10)
        
        # 验证结果
        self.assertIn('trades', results)
        self.assertIn('equity_curve', results)
        self.assertIn('total_return', results)
        
        # 上涨趋势模型在上涨趋势数据上应该有正收益
        self.assertGreater(results['total_return'], 0)
    
    def test_backtest_down_trend(self):
        """测试下跌趋势回测"""
        # 使用下跌趋势模型回测下跌趋势数据
        results = self.down_backtester.backtest(self.down_trend_data, seq_len=10)
        
        # 验证结果
        self.assertIn('trades', results)
        self.assertIn('equity_curve', results)
        self.assertIn('total_return', results)
    
    def test_backtest_with_stop_loss(self):
        """测试带止损的回测"""
        # 使用随机模型回测随机数据，带止损
        results = self.random_backtester.backtest(
            self.random_trend_data, 
            seq_len=10,
            stop_loss=0.05  # 5%止损
        )
        
        # 验证结果
        self.assertIn('trades', results)
        
        # 检查是否有止损交易
        stop_loss_trades = [t for t in results['trades'] if t['action'] == 'STOP_LOSS']
        print(f"止损交易数量: {len(stop_loss_trades)}")
    
    def test_backtest_with_take_profit(self):
        """测试带止盈的回测"""
        # 使用上涨模型回测上涨数据，带止盈
        results = self.up_backtester.backtest(
            self.up_trend_data, 
            seq_len=10,
            take_profit=0.1  # 10%止盈
        )
        
        # 验证结果
        self.assertIn('trades', results)
        
        # 检查是否有止盈交易
        take_profit_trades = [t for t in results['trades'] if t['action'] == 'TAKE_PROFIT']
        print(f"止盈交易数量: {len(take_profit_trades)}")
    
    def test_save_and_load_results(self):
        """测试保存和加载回测结果"""
        # 执行回测
        results = self.up_backtester.backtest(self.up_trend_data, seq_len=10)
        
        # 保存结果
        save_path = 'test_results.json'
        self.up_backtester.save_results(results, save_path)
        
        # 加载结果
        loaded_results = self.up_backtester.load_results(save_path)
        
        # 验证加载的结果
        self.assertEqual(results['initial_capital'], loaded_results['initial_capital'])
        self.assertEqual(results['total_return'], loaded_results['total_return'])
        self.assertEqual(len(results['trades']), len(loaded_results['trades']))
        
        # 清理
        if os.path.exists(save_path):
            os.remove(save_path)
    
    def test_compare_strategies(self):
        """测试比较策略"""
        # 执行不同策略的回测
        up_results = self.up_backtester.backtest(self.up_trend_data, seq_len=10)
        down_results = self.down_backtester.backtest(self.down_trend_data, seq_len=10)
        
        # 比较策略
        fig = self.up_backtester.compare_strategies(
            [up_results, down_results],
            names=['上涨策略', '下跌策略']
        )
        
        # 验证图表
        self.assertIsNotNone(fig)
        
        # 关闭图表
        plt.close(fig)


def run_tests():
    """运行测试"""
    unittest.main()


if __name__ == '__main__':
    run_tests()
