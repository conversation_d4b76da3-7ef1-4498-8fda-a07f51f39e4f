"""
平衡训练脚本 - 解决token分布不均衡问题
参考原来的 train_bar_gpt.py 完善数据集部分和训练流程
"""

import sys
import torch
import argparse
import numpy as np
import random
from pathlib import Path
from collections import Counter
from datetime import datetime
from time import time
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor, RichProgressBar
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary
from torch.utils.data import Subset
from sklearn.model_selection import KFold

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt.bar_gpt4 import BarGpt4
from pyqlab.models.gpt.token_balance_utils import TokenBalancer, create_balanced_dataloader
from pyqlab.models import PLGptModel
from pyqlab.data.dataset.dataset_bar import BarDataset
from pyqlab.data.dataset.pipeline import Pipeline
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES
from pyqlab.utils.config import CfgNode as CN
from pyqlab.pl.utils import get_best_saved_model_filename


def get_trainer_name(args):
    """生成训练器名称"""
    market = args.market.upper()
    block_name = args.block_name.upper()
    period = args.period.upper()
    trainer_name = f'{market}_{args.version}_{block_name}_{period}_{args.block_size}_{args.n_head}_{args.n_layer}'
    return trainer_name


def get_config():
    """获取默认配置"""
    C = CN()
    # data
    C.data = BarDataset.get_default_config()
    return C


def load_callbacks(args):
    """加载回调函数"""
    callbacks = []
    callbacks.append(EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(LearningRateMonitor(
        logging_interval='epoch'
    ))

    callbacks.append(RichProgressBar())

    return callbacks


def save_model_as_to_onnx(args):
    """导出模型为ONNX格式"""
    trainer_name = get_trainer_name(args)
    model_files = get_best_saved_model_filename(
        log_dir=args.log_dir,
        sub_dir=trainer_name
    )
    if len(model_files) == 0:
        raise Exception("No saved model found!")
    print(f"=== Export best model files ===")
    for model_file, best_score in model_files.items():
        if best_score > args.best_score:
            print(f"Skip model file: {model_file}  {best_score}")
            continue
        else:
            print(f"Export model file: {model_file}  {best_score}")
        try:
            model = PLGptModel.load_from_checkpoint(checkpoint_path=model_file)
            model.freeze()
            code = torch.zeros(1, args.block_size).to(torch.int32)
            x = torch.zeros(1, args.block_size).to(torch.int32)
            x_mark = torch.zeros(1, args.block_size * 5).to(torch.float32)
            x_mark = x_mark.reshape(1, args.block_size, 5)
            tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
            model_name = f"{trainer_name}_{tm_str}_{best_score}_ls"
            model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (code, x, x_mark, None), export_params=True)
            print(f"Model saved to： {args.model_dir}/{model_name}.onnx")
        except Exception as e:
            print(f"Error: {e}")


class BalancedBarGpt4Trainer(pl.LightningModule):
    """平衡训练的BarGpt4模型"""

    def __init__(self, model_config, training_config):
        super().__init__()
        self.save_hyperparameters()

        # 创建模型
        self.model = BarGpt4(**model_config)

        # 训练配置
        self.learning_rate = training_config['learning_rate']
        self.weight_decay = training_config['weight_decay']
        self.use_class_weights = training_config.get('use_class_weights', True)
        self.balance_method = training_config.get('balance_method', 'focal_loss')

        # 类别权重（将在setup中设置）
        self.class_weights = None

        # 记录训练统计
        self.train_predictions = []
        self.val_predictions = []

    def setup(self, stage=None):
        """设置阶段 - 计算类别权重"""
        if stage == 'fit' and self.use_class_weights:
            print("开始计算类别权重...")
            # 从训练数据计算类别权重 - 使用采样方式加速
            train_dataloader = self.trainer.datamodule.train_dataloader()

            # 收集训练token（采样方式，只处理前1000个批次）
            all_tokens = []
            max_batches = min(1000, len(train_dataloader))  # 最多处理1000个批次
            print(f"采样 {max_batches} 个批次来计算类别权重（总共 {len(train_dataloader)} 个批次）")

            for batch_idx, batch in enumerate(train_dataloader):
                if batch_idx >= max_batches:
                    break

                if batch_idx % 100 == 0:
                    print(f"处理批次 {batch_idx}/{max_batches}")

                if isinstance(batch, dict):
                    targets = batch['targets']
                else:
                    # BarDataset返回: (code, x, x_mark, y, y_mark)
                    # targets是第4个元素（索引3）
                    _, _, _, targets, _ = batch

                # 过滤有效token（非-1）
                valid_tokens = targets[targets != -1]
                all_tokens.extend(valid_tokens.tolist())

            print(f"收集了 {len(all_tokens)} 个token用于计算类别权重")

            # 计算类别权重
            balancer = TokenBalancer(self.model.vocab_size)
            token_sequences = [all_tokens]  # 包装成序列格式
            balancer.analyze_token_distribution(token_sequences, save_plot=True,
                                              plot_path="train_token_distribution.png")

            if self.balance_method == 'weighted_loss':
                # 使用更温和的平衡策略
                self.class_weights = balancer.compute_class_weights(
                    method='sqrt_inverse_freq', smooth_factor=10.0  # 增加平滑因子
                ).to(self.device)

                # 限制权重范围，避免过度调整
                max_weight = self.class_weights.max()
                min_weight = self.class_weights.min()
                if max_weight / min_weight > 10:  # 如果权重差距超过10倍
                    # 压缩权重范围
                    self.class_weights = torch.clamp(self.class_weights, min_weight, min_weight * 10)
                    print(f"权重范围被限制到: [{self.class_weights.min():.4f}, {self.class_weights.max():.4f}]")

                print(f"已计算类别权重，权重范围: [{self.class_weights.min():.4f}, {self.class_weights.max():.4f}]")

    def training_step(self, batch, batch_idx):
        """训练步骤"""
        if isinstance(batch, dict):
            code = batch['code']
            x = batch['x']
            x_mark = batch['x_mark']
            targets = batch['targets']
        else:
            # BarDataset返回: (code, x, x_mark, y, y_mark)
            code, x, x_mark, targets, _ = batch

        # 前向传播
        if self.balance_method == 'focal_loss':
            # 使用Focal Loss - 先获取logits，然后计算自定义损失
            logits, _ = self.model(code, x, x_mark, targets,
                                 class_weights=None)  # 不使用内置权重
            loss = self._focal_loss(logits, targets)
        else:
            # 使用标准损失或加权损失
            logits, loss = self.model(code, x, x_mark, targets,
                                    class_weights=self.class_weights if self.use_class_weights else None)

        # 记录预测用于分析
        if batch_idx % 100 == 0:  # 每100个batch记录一次
            with torch.no_grad():
                predictions = logits.argmax(dim=-1)
                self.train_predictions.extend(predictions[:, -1].cpu().tolist())

        # 记录损失
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)

        return loss

    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        if isinstance(batch, dict):
            code = batch['code']
            x = batch['x']
            x_mark = batch['x_mark']
            targets = batch['targets']
        else:
            # BarDataset返回: (code, x, x_mark, y, y_mark)
            code, x, x_mark, targets, _ = batch

        # 前向传播
        if self.balance_method == 'focal_loss':
            # 使用Focal Loss
            logits, _ = self.model(code, x, x_mark, targets, class_weights=None)
            loss = self._focal_loss(logits, targets)
        else:
            # 使用标准损失或加权损失
            logits, loss = self.model(code, x, x_mark, targets,
                                    class_weights=self.class_weights if self.use_class_weights else None)

        # 记录预测
        with torch.no_grad():
            predictions = logits.argmax(dim=-1)
            self.val_predictions.extend(predictions[:, -1].cpu().tolist())

        # 记录损失
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)

        return loss

    def on_validation_epoch_end(self):
        """验证轮次结束时分析预测多样性"""
        if len(self.val_predictions) > 0:
            pred_counter = Counter(self.val_predictions)
            unique_preds = len(pred_counter)
            total_preds = len(self.val_predictions)
            diversity = unique_preds / min(total_preds, self.model.vocab_size)

            self.log('val_prediction_diversity', diversity, on_epoch=True)

            # 检查是否存在过度集中的预测
            most_common = pred_counter.most_common(1)[0]
            max_freq = most_common[1] / total_preds

            if max_freq > 0.5:  # 如果某个token占比超过50%
                print(f"⚠️  警告: Token {most_common[0]} 在验证集中占比 {max_freq:.2%}")

            # 清空记录
            self.val_predictions = []

    def configure_optimizers(self):
        """配置优化器"""
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
            betas=(0.9, 0.95)
        )

        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=self.trainer.max_epochs,
            eta_min=self.learning_rate * 0.1
        )

        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "interval": "epoch",
            }
        }

    def _focal_loss(self, logits, targets, alpha=1.0, gamma=1.5):
        """
        改进的Focal Loss实现，用于处理类别不平衡
        Args:
            logits: 模型输出 [batch_size, seq_len, vocab_size]
            targets: 目标标签 [batch_size, seq_len]
            alpha: 平衡因子
            gamma: 聚焦参数（降低到1.5以提高稳定性）
        """
        # 重塑张量
        logits = logits.view(-1, logits.size(-1))  # [batch_size * seq_len, vocab_size]
        targets = targets.view(-1)  # [batch_size * seq_len]

        # 过滤有效目标（非-1）
        valid_mask = targets != -1
        if not valid_mask.any():
            return torch.tensor(0.0, device=logits.device, requires_grad=True)

        valid_logits = logits[valid_mask]
        valid_targets = targets[valid_mask]

        # 添加数值稳定性：裁剪logits
        valid_logits = torch.clamp(valid_logits, min=-100, max=100)

        # 计算log softmax以提高数值稳定性
        log_pt = torch.nn.functional.log_softmax(valid_logits, dim=-1)
        log_pt = log_pt.gather(1, valid_targets.unsqueeze(1)).squeeze(1)

        # 计算概率
        pt = torch.exp(log_pt)

        # 添加小的epsilon以避免log(0)
        pt = torch.clamp(pt, min=1e-8, max=1.0)

        # 计算Focal Loss
        focal_loss = -alpha * (1 - pt) ** gamma * log_pt

        # 检查是否有NaN或Inf
        if torch.isnan(focal_loss).any() or torch.isinf(focal_loss).any():
            print("警告：Focal Loss中出现NaN或Inf，回退到标准交叉熵")
            ce_loss = torch.nn.functional.cross_entropy(valid_logits, valid_targets, reduction='mean')
            return ce_loss

        return focal_loss.mean()


class BalancedBarDataModule(pl.LightningDataModule):
    """平衡的数据模块 - 参考train_bar_gpt.py实现"""

    def __init__(self, args, balance_config=None):
        super().__init__()
        self.args = args
        self.balance_config = balance_config or {'method': 'none'}

        # 初始化数据相关属性
        self.dataset = None
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None

        # 设置数据加载参数
        self.batch_size = args.batch_size
        self.num_workers = getattr(args, 'num_workers', 0)

        # 准备数据
        self._prepare_data()

    def _prepare_data(self):
        """准备数据 - 参考train_bar_gpt.py的数据加载逻辑"""
        # 设置随机种子
        if hasattr(self.args, 'seed'):
            random.seed(self.args.seed)
            np.random.seed(self.args.seed)

        # 设置频率参数
        if self.args.period == 'min5' or self.args.period == 'min1':
            self.args.freq = 't'
        elif self.args.period == 'day':
            self.args.freq = 'b'
        else:
            raise ValueError(f"Invalid period: {self.args.period}")

        # 设置选择的代码
        if self.args.market == 'fut' and self.args.block_name == 'sf':
            self.args.sel_codes = SF_FUT_CODES

        # 获取配置
        config = get_config()
        config.data.update_from_dict(vars(self.args))

        # 设置时间编码
        if self.args.time_encoding == 'timeF':
            config.data.timeenc = 1
        else:
            config.data.timeenc = 0

        # 构建训练数据集
        pipe = Pipeline(
            config.data.data_path,
            config.data.market,
            config.data.block_name,
            config.data.period,
            config.data.start_year,
            config.data.end_year,
            config.data.start_date,
            config.data.end_date,
            config.data.block_size,
            config.data.timeenc,
            config.data.sel_codes
        )

        # 修复词汇表大小不匹配问题
        # 检查数据中的bar值范围，如果超过3字段版本的范围，则强制使用4字段版本
        data = pipe.get_data()
        max_bar_value = data['bar'].max()
        if max_bar_value >= 1602:  # 3字段版本的最大索引是1601
            print(f"检测到数据使用4字段版本 (max bar value: {max_bar_value})，强制使用4字段词汇表")
            # 清除已有的词汇表
            from pyqlab.data.dataset.utils import bar_set
            bar_set.clear()  # 清空全局词汇表
            Pipeline.bar_set.clear()  # 清空Pipeline类词汇表

            # 重新初始化Pipeline使用4字段版本
            pipe.field_num = 4
            pipe.get_vocab()  # 重新生成词汇表
            new_vocab_size = len(pipe.get_vocab())
            print(f"重新生成词汇表，大小: {new_vocab_size}")

            # 更新args中的vocab_size参数
            self.args.vocab_size = new_vocab_size
            print(f"更新模型vocab_size参数为: {new_vocab_size}")

        assert len(data) > 0 and len(data.shape) == 2, 'No data or wrong shape'
        config.data.is_sf = pipe.is_sf
        self.args.is_sf = pipe.is_sf
        print(f"args.is_sf: {self.args.is_sf}")

        # 创建数据集
        self.dataset = BarDataset(config.data, data)
        print(f"Dataset created with {len(self.dataset)} samples")

    def setup(self, stage=None):
        """设置数据集 - 不需要额外操作，数据已在初始化时准备好"""
        # stage参数用于区分不同阶段，这里暂时不需要特殊处理
        pass

    def get_dataset(self):
        """获取完整数据集，用于交叉验证"""
        return self.dataset

    def train_dataloader(self):
        """训练数据加载器"""
        if self.train_dataset is None:
            raise ValueError("训练数据集未设置，请先调用set_fold_data")

        # 根据平衡配置创建数据加载器
        balance_method = self.balance_config.get('method', 'none')

        if balance_method == 'weighted_sampling':
            return create_balanced_dataloader(
                self.train_dataset,
                tokenizer=None,  # 需要传入实际的tokenizer
                batch_size=self.batch_size,
                balance_method='weighted_sampling'
            )
        else:
            return torch.utils.data.DataLoader(
                self.train_dataset,
                batch_size=self.batch_size,
                shuffle=True,
                num_workers=self.num_workers,
                pin_memory=True,
                persistent_workers=True if self.num_workers > 0 else False
            )

    def val_dataloader(self):
        """验证数据加载器"""
        if self.val_dataset is None:
            raise ValueError("验证数据集未设置，请先调用set_fold_data")

        return torch.utils.data.DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True,
            persistent_workers=True if self.num_workers > 0 else False
        )

    def set_fold_data(self, train_dataset, val_dataset):
        """设置当前fold的训练和验证数据集"""
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset


def main():
    """主训练函数 - 参考train_bar_gpt.py实现完整的训练流程"""
    parser = argparse.ArgumentParser(description='平衡训练BarGpt4模型')

    # Data module ===========================
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)
    parser.add_argument('--data_path', default='f:/featdata/barenc/db2', type=str, help='path to the data')
    parser.add_argument('--market', default='fut', choices=['fut', 'stk'], type=str, help='market')
    parser.add_argument('--block_name', default='sf', type=str, help='block name')
    parser.add_argument('--period', default='min5', choices=['day', 'min5', 'min1'], type=str, help='period')
    parser.add_argument('--start_year', default=2024, type=int, help='start year of the data')
    parser.add_argument('--end_year', default=2024, type=int, help='end year of the data')
    parser.add_argument('--start_date', default='', type=str)
    parser.add_argument('--end_date', default='', type=str)
    parser.add_argument('--block_size', default=20, type=int, help='block size')
    parser.add_argument('--step_size', default=1, type=int, help='step size')
    parser.add_argument('--sel_codes', default=MAIN_SEL_FUT_CODES, type=list,
                       choices=[MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES], help='selected codes')

    # Model Hyperparameters =================
    parser.add_argument('--version', default='GPT', type=str)
    parser.add_argument('--model_name', default='bar_gpt4', type=str)
    parser.add_argument('--model_path', default='pyqlab.models.gpt', type=str)
    parser.add_argument('--loss', default='ce', type=str)
    parser.add_argument('--lr', default=5e-4, type=float)
    parser.add_argument('--n_head', default=32, type=int)
    parser.add_argument('--n_layer', default=4, type=int)
    parser.add_argument('--vocab_size', default=40002, type=int)
    parser.add_argument('--code_size', default=96, type=int)
    parser.add_argument('--d_model', default=96, type=int)
    parser.add_argument('--bias', action='store_false')
    parser.add_argument('--n_experts', default=8, type=int, help='Number of expert networks')
    parser.add_argument('--top_k', default=2, type=int, help='Number of experts to use for each token')

    # model
    parser.add_argument('--dropout', default=0.1, type=float)
    parser.add_argument('--time_encoding', type=str, default='timeF',
                       help='time features encoding, options:[timeF, fixed, learned]')
    parser.add_argument('--freq', type=str, default='t', help='time features encoding, options:[t, b]')
    parser.add_argument('--time_embed_type', type=str, default='time_feature',
                       help='BarGpt4: time embedding type, options:[time_feature, periodic, continuous, adaptive, multi_scale]')
    parser.add_argument('--pos_embed_type', type=str, default='rope',
                       help='BarGpt4: positional embedding type, options:[rope, alibi]')

    # LR Scheduler
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau',
                       choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)
    parser.add_argument('--weight_decay', default=0.0, type=float)
    parser.add_argument('--optimizer', default='adamw', choices=['adam', 'adamw'], type=str)

    # Restart Control
    parser.add_argument('--resume', action='store_true', help='resume training from the last checkpoint')

    # Training Info
    parser.add_argument('--max_epochs', default=10, type=int)
    parser.add_argument('--early_stop', default=5, type=int)
    parser.add_argument('--min_delta', default=1e-2, type=float)
    parser.add_argument('--k_folds', default=4, type=int)
    parser.add_argument('--log_dir', default='lightning_logs', type=str)

    # 平衡参数
    parser.add_argument('--use_class_weights', action='store_true', help='使用类别权重')
    parser.add_argument('--balance_method', type=str, default='focal_loss',
                       choices=['focal_loss', 'weighted_loss', 'none'], help='平衡方法')
    parser.add_argument('--data_balance_method', type=str, default='none',
                       choices=['weighted_sampling', 'oversampling', 'none'], help='数据平衡方法')

    # Others
    parser.add_argument('--model_dir', default='model', type=str)
    parser.add_argument('--export_onnx', action='store_true')
    parser.add_argument('--best_score', default=0.6, type=float)
    parser.add_argument('--is_sf', action='store_true')

    args = parser.parse_args()

    # 设置随机种子
    args.seed = random.randint(0, 10000)
    print(args)

    # 如果只是导出ONNX，直接调用导出函数
    if args.export_onnx:
        save_model_as_to_onnx(args)
        return

    # 创建数据模块
    balance_config = {'method': args.data_balance_method}
    datamodule = BalancedBarDataModule(args, balance_config)
    dataset = datamodule.get_dataset()

    # 设置PyTorch Lightning随机种子
    pl.seed_everything(args.seed)

    trainer_name = get_trainer_name(args)
    print(f"Trainer Name: {trainer_name}")

    model = None
    # 定义交叉验证
    kfold = KFold(n_splits=args.k_folds, shuffle=True, random_state=args.seed)

    # 创建一个数据模块列表，每个数据模块对应一个 fold
    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
        print(f"=== Training fold {fold} ===")
        train_data = Subset(dataset, train_idx)
        val_data = Subset(dataset, val_idx)
        print(f"Train data: {len(train_data)}, Val data: {len(val_data)}, Total data: {len(dataset)}")

        # 设置当前fold的数据
        datamodule.set_fold_data(train_data, val_data)

        # 创建或加载模型
        if model is None:
            if args.resume:
                model_path, _ = get_best_saved_model_filename(
                    log_dir=args.log_dir,
                    sub_dir=trainer_name,
                    only_best=True
                )
                if model_path is None:
                    raise Exception("No saved model found!")
                print(f"=== Resume Training ===")
                print(f"Loading model from {model_path}")
                try:
                    model = BalancedBarGpt4Trainer.load_from_checkpoint(checkpoint_path=model_path)
                except Exception as e:
                    print(f"Error: {e}")
            else:
                print(f"=== New Training ===")
                # 创建模型配置
                model_config = {
                    'block_size': args.block_size,
                    'code_size': args.code_size,
                    'vocab_size': args.vocab_size,
                    'n_layer': args.n_layer,
                    'n_head': args.n_head,
                    'd_model': args.d_model,
                    'time_encoding': args.time_encoding,
                    'time_embed_type': args.time_embed_type,
                    'freq': args.freq,
                    'pos_embed_type': args.pos_embed_type,
                    'dropout': args.dropout
                }

                # 训练配置
                training_config = {
                    'learning_rate': args.lr,
                    'weight_decay': args.weight_decay,
                    'use_class_weights': args.use_class_weights,
                    'balance_method': args.balance_method
                }

                model = BalancedBarGpt4Trainer(model_config, training_config)

        # 创建训练器
        logger = TensorBoardLogger(save_dir=args.log_dir, name=trainer_name)
        # 创建回调函数
        callbacks = load_callbacks(args)
        trainer = pl.Trainer(
            accelerator='auto',
            devices='auto',
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
        )
        trainer.fit(model, datamodule)

    # 训练完成后，保存编译最后一个模型
    if callbacks[0].stopped_epoch is not None:
        # 加载之前训练的模型
        print(f"Best model to save {callbacks[1].best_model_path}")
        best_score = callbacks[1].best_model_score.cpu().numpy()
        best_model = BalancedBarGpt4Trainer.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)

        # 设置为推理模式
        if hasattr(best_model.model, 'inference_mode'):
            best_model.model.inference_mode()
            print("已将模型设置为推理模式")
        else:
            print("模型没有推理模式")
            best_model.eval()

        # 导出ONNX模型 - 注意：BalancedBarGpt4Trainer可能没有to_onnx方法
        # 我们需要使用内部的model来导出
        config = get_config()
        config.data.update_from_dict(vars(args))
        code = torch.zeros(1, config.data.block_size).to(torch.int32)
        x = torch.zeros(1, config.data.block_size).to(torch.int32)
        x_mark = torch.zeros(1, config.data.block_size * 5).to(torch.float32)
        x_mark = x_mark.reshape(1, config.data.block_size, 5)
        tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
        model_name = f"{trainer_name}_{tm_str}_%.3f_ls" % best_score

        # 尝试导出ONNX模型
        try:
            if hasattr(best_model, 'to_onnx'):
                best_model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (code, x, x_mark, None), export_params=True)
            else:
                print("模型没有to_onnx方法，跳过ONNX导出")
        except Exception as e:
            print(f"ONNX导出失败: {e}")

        # 查看模型大小
        best_model.example_input_array = (code, x, x_mark, None)
        summary = ModelSummary(best_model, max_depth=-1)
        print(summary)

        print(f"Model training completed. Best score: {best_score}")
        print("=== Training Finished ===\n\n")


if __name__ == "__main__":
    main()
