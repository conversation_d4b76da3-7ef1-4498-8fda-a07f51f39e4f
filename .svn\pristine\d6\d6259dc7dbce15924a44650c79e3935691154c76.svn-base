import struct

class Message():

    def __init__(self):
        self.command = 2    # 0-heartbeat 1-regist 2-normal 3-normal_compress
        self.business_type = 0
        self.app_id = 0
        self.body = b''

    def package_msg(self):
        '''注意跨语言编程时大小端的顺序,首字符用@,=,<,>,!表示'''
        st = 0
        msg = b''
        msg += 'XXH'.encode('ascii')
        msg += struct.pack('B', st)
        msg += struct.pack('<i', len(self.body) + 6)
        msg += struct.pack('<H', self.command)
        msg += struct.pack('<H', self.business_type)
        msg += struct.pack('<H', self.app_id)
        msg += self.body.encode('ascii')

        return msg
        # return struct.pack('<2sbLhhh%ds' % len(self.body), 'NX', st, len(self.body) + 6, self.command, self.business_type, self.app_id, self.body.encode())

    def package_msg_bytes(self):
        '''
        H: unsign short max:65535
        '''
        st = 0
        msg = b''
        msg += 'XXH'.encode('ascii')
        msg += struct.pack('B', st)
        msg += struct.pack('<i', len(self.body) + 6)
        msg += struct.pack('<H', self.command)
        msg += struct.pack('<H', self.business_type)
        msg += struct.pack('<H', self.app_id)
        msg += self.body
        return msg
    
    def unpackage_msg(self, data, length):
        self.command, self.business_type, self.app_id, self.body = struct.unpack('<hhh%ds' % (length - 6), data)
        # print(tag, st, len)
