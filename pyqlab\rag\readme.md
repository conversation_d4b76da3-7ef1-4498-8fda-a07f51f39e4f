Okay, 这是一个非常有趣的 RAG 应用场景，将时序数据嵌入用于检索。这里的核心挑战在于如何将包含分类（symbol）、时序（datetime）和高维分类序列（token）的信息有效地编码到一个固定维度的向量中，以便进行相似性搜索。

以下是一些方法和步骤，你可以考虑：

**核心思想：** 使用深度学习序列模型来捕捉时序`token`序列的动态，并结合`symbol`和`datetime`的上下文信息，生成一个能代表该时间段（长度30）核心特征的 embedding。

**数据预处理与特征工程:**

1.  **`symbol` (证券代码):**
    *   这是一个分类特征。如果 `symbol` 数量不多，可以考虑 One-Hot 编码，但通常股票数量很多，这样做维度会爆炸。
    *   **推荐:** 使用 **Embedding Layer**。为每个 `symbol` 分配一个可学习的 embedding 向量。你需要维护一个 `symbol` 到整数索引的映射。
    *   `symbol_embedding = nn.Embedding(num_symbols, symbol_embedding_dim)`

2.  **`datetime` (时间戳):**
    *   原始时间戳信息量大但直接使用效果不佳。需要提取有意义的特征。
    *   **绝对时间特征:**
        *   年份、月份、星期几、月中第几天、一年中第几天、小时、分钟等。
        *   对于周期性特征（如小时、星期几），进行 **Cyclical Encoding** (使用 sin/cos 变换):
            *   `hour_sin = sin(2 * pi * hour / 24)`
            *   `hour_cos = cos(2 * pi * hour / 24)`
            *   类似地处理星期几、月份等。
    *   **相对时间特征 (在长度为30的序列内):**
        *   **位置编码 (Positional Encoding):** 类似 Transformer 中的做法，为序列中的每个位置（0到29）添加一个固定的或可学习的位置向量，表示其在序列中的相对位置。
        *   **时间差:** 计算相邻时间点之间的时间差（`delta_t`），可能需要归一化。这可以捕捉数据点的时间间隔。
    *   **事件相关特征:** (如果可能) 距离某个重要市场事件（如财报发布、开盘/收盘）的时间。
    *   **处理方式:** 可以将这些提取出的时间特征拼接成一个向量，或者使用一个小型 MLP 将其映射到一个时间 embedding 向量。

3.  **`token` (词汇索引):**
    *   这是一个高维分类序列（词汇表大小 40002）。
    *   **推荐:** 使用 **Embedding Layer**。为每个 `token` 索引分配一个可学习的 embedding 向量。
    *   `token_embedding = nn.Embedding(40002, token_embedding_dim)`

**Embedding 模型架构:**

选择一个能处理序列数据的模型架构：

**方案一：基于 RNN (LSTM/GRU)**

1.  **输入准备:**
    *   获取 `symbol` 的 embedding (`symbol_vec`)。这个向量在整个序列中是共享的，或者只在最后拼接。
    *   对于序列中的每个时间步 `t` (0 到 29):
        *   获取 `token[t]` 的 embedding (`token_vec[t]`).
        *   获取 `datetime[t]` 的相关特征/embedding (`time_vec[t]`).
        *   将 `token_vec[t]` 和 `time_vec[t]` **拼接**起来，形成时间步 `t` 的输入向量 `step_input[t]`.
        *   (可选) 也可以将 `symbol_vec` 拼接到每个 `step_input[t]` 中，让模型在每一步都知道 `symbol` 上下文。

2.  **序列建模:**
    *   将 `step_input` 序列（长度 30）输入到一个 LSTM 或 GRU 层。
    *   `outputs, hidden_state = lstm(step_input_sequence)`

3.  **获取最终 Embedding:**
    *   **使用最后的隐藏状态:** `sequence_embedding = hidden_state[-1]` (对于多层 LSTM/GRU，是最后一层的最后一个时间步的隐藏状态)。这代表了整个序列的压缩信息。
    *   **(可选) 结合 Symbol:** 如果 `symbol_vec` 没有在每一步输入，可以在这里将其与 `sequence_embedding` **拼接**：`final_embedding = concat(symbol_vec, sequence_embedding)`。
    *   **(可选) MLP 投影:** 可以添加一个或多个全连接层（MLP）对 `final_embedding` 进行变换和降维，得到最终用于 RAG 的 embedding。

**方案二：基于 Transformer Encoder**

1.  **输入准备:**
    *   获取 `symbol` 的 embedding (`symbol_vec`)。
    *   对于序列中的每个时间步 `t` (0 到 29):
        *   获取 `token[t]` 的 embedding (`token_vec[t]`).
        *   获取 `datetime[t]` 的相关特征/embedding (`time_vec[t]`).
        *   将 `token_vec[t]` 和 `time_vec[t]` **相加或拼接** (如果拼接，可能需要一个线性层映射回目标维度)。
        *   添加 **Positional Encoding** 到上述结果中，得到 `input_repr[t]`。

2.  **序列建模:**
    *   将 `input_repr` 序列（长度 30）输入到一个 Transformer Encoder 模型中。Encoder 内部的自注意力机制可以捕捉序列内任意两个时间步之间的关系。
    *   `encoded_sequence = transformer_encoder(input_repr_sequence)`

3.  **获取最终 Embedding:**
    *   **[CLS] Token 方式:** 在输入序列的最前面添加一个特殊的 `[CLS]` token，然后使用 Transformer Encoder 输出对应于 `[CLS]` token 的向量作为整个序列的表示。
    *   **平均池化 (Mean Pooling):** 计算 `encoded_sequence` 在时间维度上的平均值。`sequence_embedding = mean(encoded_sequence, dim=1)`。
    *   **结合 Symbol:** 将 `symbol_vec` 与 `sequence_embedding` **拼接**：`final_embedding = concat(symbol_vec, sequence_embedding)`。
    *   **MLP 投影:** 同上，可选添加 MLP 进行变换和降维。

**方案三：基于 CNN (1D Convolution)**

1.  **输入准备:** 类似于 RNN/Transformer 的步骤 1，准备好每个时间步的融合了 `token` 和 `datetime` 信息的输入向量序列 `input_repr_sequence` (形状如 `Batch x 30 x FeatureDim`)。
2.  **卷积层:** 将 `input_repr_sequence` 视为 1D 信号，应用多个 1D 卷积层。可以使用不同的卷积核大小（如 3, 5, 7）来捕捉不同长度的局部模式。
3.  **池化层:** 在卷积层后使用池化层（如 MaxPooling 或 AveragePooling）来降低维度并提取最显著的特征。通常使用全局池化（GlobalMaxPooling 或 GlobalAveragePooling）将时间维度压缩掉，得到固定大小的向量。
4.  **获取最终 Embedding:**
    *   将池化层的输出（可能来自不同卷积核大小的分支）拼接起来。
    *   将 `symbol_vec` 与上述结果拼接。
    *   可选 MLP 投影。

**如何训练 Embedding 模型 (关键步骤):**

由于目标是 RAG 检索，你需要让相似的时序片段具有相似的 embedding。这通常通过 **对比学习 (Contrastive Learning)** 或 **代理任务 (Proxy Tasks)** 来实现：

1.  **对比学习 (推荐):**
    *   **思想:** 将一个时序片段（Anchor）与其相似的片段（Positive）在 embedding 空间中拉近，同时与不相似的片段（Negative）推远。
    *   **构造正样本:**
        *   同一 `symbol` 下，时间上稍微重叠或相邻的片段。
        *   对原始片段进行数据增强（如添加噪声、时间扭曲、部分掩码）得到的片段。
        *   具有相似模式但来自不同时间段（甚至不同 symbol，如果适用）的片段（需要领域知识或自动发现）。
    *   **构造负样本:**
        *   来自不同 `symbol` 的随机片段。
        *   来自同一 `symbol` 但时间距离很远的片段。
        *   与 Anchor 模式显著不同的片段。
    *   **损失函数:** Triplet Loss, NT-Xent Loss (SimCLR), InfoNCE Loss 等。
    *   **优点:** 直接优化 embedding 的相似性度量，非常适合检索任务。

2.  **代理任务:**
    *   **序列预测:** 训练模型预测序列中的下一个 `token` 或未来几个 `token`。使用 Encoder 部分作为 embedding 生成器。
    *   **掩码预测 (Masked Language Model):** 类似 BERT，随机 mask 掉序列中的一些 `token`，让模型去预测它们。
    *   **分类/回归任务:** 如果有时序片段的标签（例如，这段时间代表上涨趋势、震荡、特定事件等），可以训练模型进行分类。或者预测这段时间之后的某个指标（如未来收益率）。
    *   **优点:** 实现相对简单，可以利用已有的监督信息。
    *   **缺点:** 优化目标与检索相似性不完全一致，效果可能不如对比学习。

**RAG 流程:**

1.  **离线建立索引:**
    *   将你的历史时序数据切分成长度为 30 的片段（可以有重叠）。
    *   对于每个片段，使用你训练好的 Embedding 模型（如上面描述的 LSTM, Transformer 或 CNN）计算其 `final_embedding`。
    *   将这些 embedding 存储到一个向量数据库（如 FAISS, Milvus, Pinecone, ChromaDB）中，同时存储对应的元数据（symbol, start_datetime, end_datetime, 原始数据片段等）。
2.  **在线检索:**
    *   当有一个查询（Query）时，你需要将查询也转换成一个 embedding。
        *   如果查询本身就是一个长度 30 的时序片段，直接使用相同的 Embedding 模型计算其 embedding。
        *   如果查询是自然语言（例如 "查找 AAPL 在 2023 年 3 月初股价剧烈波动的时期"），你需要一个额外的机制：
            *   **方法 A:** 训练一个能将文本查询映射到与时序 embedding 相同空间的模型（可能需要跨模态训练）。
            *   **方法 B:** 使用文本查询先进行元数据过滤（如 `symbol=AAPL`, `datetime` 在 2023 年 3 月），然后可能需要一个更复杂的模型或人工定义的规则来匹配 "剧烈波动" 这个概念对应的 `token` 序列模式，或者直接检索所有过滤后的片段，让后续的 LLM 去判断。对于 RAG，通常假设查询和文档在同一模态或可以映射到同一空间。最直接的是用时序片段作为查询。
    *   使用查询 embedding 在向量数据库中执行 K 近邻 (KNN) 搜索，找到最相似的 N 个历史时序片段的 embedding。
3.  **生成:**
    *   获取检索到的 N 个最相似片段的原始数据或元数据。
    *   将这些检索到的信息（作为上下文）连同原始查询一起输入到一个大型语言模型 (LLM) 中，让 LLM 基于这些上下文生成最终的答案或分析。

**选择建议:**

*   **起点:** LSTM/GRU 相对简单，是序列建模的良好起点。
*   **性能:** Transformer Encoder 通常能捕捉更复杂的序列依赖关系，但计算量更大，需要更多数据和调优。
*   **训练:** 对比学习（如 SimCLR 或 Triplet Loss 变体）是为检索任务生成 embedding 的 SOTA 方法。
*   **时间特征:** 不要忽略 `datetime` 特征工程，Cyclical Encoding 和 Positional Encoding 很重要。
*   **`symbol`:** 必须包含 `symbol` 信息，拼接 Embedding 是常用方法。

开始时，可以先尝试 LSTM 结构，结合基本的 `token` embedding、`symbol` embedding 拼接以及简单的 `datetime` 特征（如星期几、小时的 cyclical encoding 和 基础 positional encoding），使用对比学习进行训练。然后根据效果逐步迭代，尝试 Transformer 或更复杂的时间特征。

好的，下面我将分别使用 PyTorch 来实现基于 LSTM, Transformer Encoder, 和 1D CNN 的时序数据 Embedding 模型。

**前提:**

1.  **安装 PyTorch:** 如果你还没有安装，请先安装：`pip install torch`
2.  **数据准备:** 下面的代码假设你已经将数据处理成如下格式的 PyTorch Tensors:
    *   `symbol_indices`: `(batch_size,)` - 每个序列对应的 `symbol` 索引 (LongTensor)。
    *   `token_sequences`: `(batch_size, seq_len)` - `token` 索引序列 (LongTensor)。
    *   `time_feature_vectors`: `(batch_size, seq_len, time_feature_dim)` - 从 `datetime` 提取并处理好的时间特征向量序列 (FloatTensor)。你需要预先完成这部分特征工程。

**1. 时间特征工程 (辅助函数)**

我们先定义一些处理时间特征的辅助函数。

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from datetime import datetime # 仅用于示例数据生成

# --- 时间特征辅助函数 ---
def cyclical_encoding(value, max_val):
    """对周期性特征进行 sin/cos 编码"""
    sin = np.sin(2 * np.pi * value / max_val)
    cos = np.cos(2 * np.pi * value / max_val)
    return sin, cos

def get_time_features(dt_obj):
    """
    从 datetime 对象提取特征向量。
    你可以根据需要添加/修改特征。
    """
    # 示例特征：小时, 星期几, 月份, 年份中的第几天
    hour = dt_obj.hour
    day_of_week = dt_obj.weekday() # Monday=0, Sunday=6
    month = dt_obj.month
    day_of_year = dt_obj.timetuple().tm_yday

    # 进行周期编码
    hour_sin, hour_cos = cyclical_encoding(hour, 24)
    dow_sin, dow_cos = cyclical_encoding(day_of_week, 7)
    month_sin, month_cos = cyclical_encoding(month, 12)
    doy_sin, doy_cos = cyclical_encoding(day_of_year, 366) # 考虑闰年

    # 组合特征 (可以添加其他非周期特征，如年份，并进行归一化)
    # 这里只用了周期特征作为示例
    features = [
        hour_sin, hour_cos,
        dow_sin, dow_cos,
        month_sin, month_cos,
        doy_sin, doy_cos
    ]
    return np.array(features, dtype=np.float32)

# --- Transformer Positional Encoding ---
class PositionalEncoding(nn.Module):
    """标准的 Transformer 位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0) # shape (1, max_len, d_model)
        self.register_buffer('pe', pe) # 注册为 buffer 而不是 parameter

    def forward(self, x):
        """
        Args:
            x: Tensor, shape [batch_size, seq_len, embedding_dim]
        """
        # x 只需要前 seq_len 个位置编码
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)

# --- 定义模型超参数 ---
# 这些需要根据你的具体数据和任务调整
SEQ_LEN = 30
VOCAB_SIZE = 40002
NUM_SYMBOLS = 1000 # 假设有 1000 个不同的证券代码
SYMBOL_EMB_DIM = 32
TOKEN_EMB_DIM = 128
# 假设 get_time_features 返回 8 个特征
TIME_FEATURE_DIM = 8
HIDDEN_DIM = 256 # RNN/Transformer 内部维度
NUM_RNN_LAYERS = 2
NUM_TRANSFORMER_HEADS = 8
NUM_TRANSFORMER_LAYERS = 3
CNN_NUM_FILTERS = 128
CNN_KERNEL_SIZES = [3, 5, 7]
DROPOUT = 0.1
FINAL_EMB_DIM = 512 # RAG 检索向量的最终维度
```

**2. 基于 LSTM 的 Embedding 模型**

```python
class TimeSeriesRNNEmbedder(nn.Module):
    def __init__(self, num_symbols, vocab_size, seq_len,
                 symbol_emb_dim, token_emb_dim, time_feature_dim,
                 hidden_dim, num_layers, final_emb_dim, dropout=0.1):
        super().__init__()
        self.seq_len = seq_len
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # Embedding 层
        self.symbol_embedding = nn.Embedding(num_symbols, symbol_emb_dim)
        self.token_embedding = nn.Embedding(vocab_size, token_emb_dim)

        # LSTM 输入维度 = token embedding 维度 + 时间特征维度
        rnn_input_dim = token_emb_dim + time_feature_dim
        self.rnn = nn.LSTM(rnn_input_dim, hidden_dim, num_layers,
                           batch_first=True, dropout=dropout if num_layers > 1 else 0,
                           bidirectional=False) # 可以改为 True 实现双向

        # 输出层前的维度 = LSTM 隐藏层维度 + symbol embedding 维度
        # 如果是双向LSTM，hidden_dim * 2
        output_dim_before_proj = hidden_dim + symbol_emb_dim

        # 最终投影层
        self.projection = nn.Sequential(
            nn.Linear(output_dim_before_proj, output_dim_before_proj // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim_before_proj // 2, final_emb_dim)
        )

    def forward(self, symbol_indices, token_sequences, time_feature_vectors):
        """
        Args:
            symbol_indices: (batch_size,)
            token_sequences: (batch_size, seq_len)
            time_feature_vectors: (batch_size, seq_len, time_feature_dim)
        Returns:
            final_embedding: (batch_size, final_emb_dim)
        """
        batch_size = token_sequences.size(0)

        # 1. 获取 Embeddings
        sym_emb = self.symbol_embedding(symbol_indices) # (batch_size, symbol_emb_dim)
        tok_emb = self.token_embedding(token_sequences) # (batch_size, seq_len, token_emb_dim)

        # 2. 准备 RNN 输入
        # (batch_size, seq_len, token_emb_dim + time_feature_dim)
        rnn_input = torch.cat([tok_emb, time_feature_vectors], dim=-1)

        # 3. 通过 RNN
        # outputs: (batch_size, seq_len, hidden_dim * num_directions)
        # h_n: (num_layers * num_directions, batch_size, hidden_dim)
        # c_n: (num_layers * num_directions, batch_size, hidden_dim)
        outputs, (h_n, c_n) = self.rnn(rnn_input)

        # 4. 获取序列表示 (使用最后一个时间步的输出)
        # 如果是单向，取 outputs[:, -1, :]
        # h_n 的最后一个 layer 的 hidden state 也可以用：h_n[-1] 或 h_n.view(...) 处理
        sequence_repr = outputs[:, -1, :] # (batch_size, hidden_dim)

        # 5. 结合 Symbol Embedding
        # (batch_size, hidden_dim + symbol_emb_dim)
        combined_repr = torch.cat([sequence_repr, sym_emb], dim=-1)

        # 6. 最终投影
        final_embedding = self.projection(combined_repr) # (batch_size, final_emb_dim)

        return final_embedding

```

**3. 基于 Transformer Encoder 的 Embedding 模型**

```python
class TimeSeriesTransformerEmbedder(nn.Module):
    def __init__(self, num_symbols, vocab_size, seq_len,
                 symbol_emb_dim, token_emb_dim, time_feature_dim,
                 d_model, nhead, num_encoder_layers, dim_feedforward,
                 final_emb_dim, dropout=0.1):
        super().__init__()
        self.seq_len = seq_len
        self.d_model = d_model # Transformer 内部维度

        # Embedding 层
        self.symbol_embedding = nn.Embedding(num_symbols, symbol_emb_dim)
        self.token_embedding = nn.Embedding(vocab_size, token_emb_dim)

        # 输入投影层，将 token_emb + time_feat 映射到 d_model
        input_dim = token_emb_dim + time_feature_dim
        self.input_projection = nn.Linear(input_dim, d_model)

        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout, max_len=seq_len + 1) # +1 以防万一

        # Transformer Encoder
        encoder_layers = nn.TransformerEncoderLayer(d_model, nhead, dim_feedforward, dropout, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_encoder_layers)

        # 输出层前的维度 = d_model (来自序列表示) + symbol_emb_dim
        output_dim_before_proj = d_model + symbol_emb_dim

        # 最终投影层
        self.projection = nn.Sequential(
            nn.Linear(output_dim_before_proj, output_dim_before_proj // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim_before_proj // 2, final_emb_dim)
        )

    def forward(self, symbol_indices, token_sequences, time_feature_vectors):
        """
        Args:
            symbol_indices: (batch_size,)
            token_sequences: (batch_size, seq_len)
            time_feature_vectors: (batch_size, seq_len, time_feature_dim)
        Returns:
            final_embedding: (batch_size, final_emb_dim)
        """
        batch_size = token_sequences.size(0)

        # 1. 获取 Embeddings
        sym_emb = self.symbol_embedding(symbol_indices) # (batch_size, symbol_emb_dim)
        tok_emb = self.token_embedding(token_sequences) # (batch_size, seq_len, token_emb_dim)

        # 2. 准备 Transformer 输入
        # (batch_size, seq_len, token_emb_dim + time_feature_dim)
        step_input = torch.cat([tok_emb, time_feature_vectors], dim=-1)
        # (batch_size, seq_len, d_model)
        projected_input = self.input_projection(step_input) * math.sqrt(self.d_model) # 缩放

        # 3. 添加位置编码
        # (batch_size, seq_len, d_model)
        pos_encoded_input = self.pos_encoder(projected_input)

        # 4. 通过 Transformer Encoder
        # (batch_size, seq_len, d_model)
        # 注意：可以添加 src_key_padding_mask 如果有 padding 的话
        encoded_sequence = self.transformer_encoder(pos_encoded_input)

        # 5. 获取序列表示 (例如，使用平均池化)
        sequence_repr = encoded_sequence.mean(dim=1) # (batch_size, d_model)
        # 或者使用 CLS Token (需要修改输入和模型结构)

        # 6. 结合 Symbol Embedding
        # (batch_size, d_model + symbol_emb_dim)
        combined_repr = torch.cat([sequence_repr, sym_emb], dim=-1)

        # 7. 最终投影
        final_embedding = self.projection(combined_repr) # (batch_size, final_emb_dim)

        return final_embedding
```

**4. 基于 1D CNN 的 Embedding 模型**

```python
class TimeSeriesCNNEmbedder(nn.Module):
    def __init__(self, num_symbols, vocab_size, seq_len,
                 symbol_emb_dim, token_emb_dim, time_feature_dim,
                 num_filters, kernel_sizes, final_emb_dim, dropout=0.1):
        super().__init__()
        self.seq_len = seq_len

        # Embedding 层
        self.symbol_embedding = nn.Embedding(num_symbols, symbol_emb_dim)
        self.token_embedding = nn.Embedding(vocab_size, token_emb_dim)

        # 输入维度
        input_dim = token_emb_dim + time_feature_dim

        # 1D 卷积层
        self.conv_layers = nn.ModuleList([
            nn.Conv1d(in_channels=input_dim,
                      out_channels=num_filters,
                      kernel_size=k,
                      padding=(k - 1) // 2) # 'same' padding
            for k in kernel_sizes
        ])

        # 输出层前的维度 = 卷积核数量 * 每个卷积核的滤波器数量 + symbol embedding 维度
        output_dim_before_proj = len(kernel_sizes) * num_filters + symbol_emb_dim

        self.dropout_layer = nn.Dropout(dropout)

        # 最终投影层
        self.projection = nn.Sequential(
            nn.Linear(output_dim_before_proj, output_dim_before_proj // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim_before_proj // 2, final_emb_dim)
        )

    def forward(self, symbol_indices, token_sequences, time_feature_vectors):
        """
        Args:
            symbol_indices: (batch_size,)
            token_sequences: (batch_size, seq_len)
            time_feature_vectors: (batch_size, seq_len, time_feature_dim)
        Returns:
            final_embedding: (batch_size, final_emb_dim)
        """
        batch_size = token_sequences.size(0)

        # 1. 获取 Embeddings
        sym_emb = self.symbol_embedding(symbol_indices) # (batch_size, symbol_emb_dim)
        tok_emb = self.token_embedding(token_sequences) # (batch_size, seq_len, token_emb_dim)

        # 2. 准备 CNN 输入
        # (batch_size, seq_len, token_emb_dim + time_feature_dim)
        cnn_input = torch.cat([tok_emb, time_feature_vectors], dim=-1)

        # 3. 调整维度以适应 Conv1d: (N, C_in, L_in)
        # (batch_size, input_dim, seq_len)
        cnn_input = cnn_input.permute(0, 2, 1)

        # 4. 通过卷积层 + 激活 + 池化
        pooled_outputs = []
        for conv in self.conv_layers:
            conv_out = conv(cnn_input) # (batch_size, num_filters, seq_len)
            activated_out = F.relu(conv_out)
            # 全局最大池化 Global Max Pooling
            # (batch_size, num_filters, 1) -> (batch_size, num_filters)
            pooled = F.max_pool1d(activated_out, kernel_size=activated_out.size(2)).squeeze(2)
            pooled_outputs.append(pooled)

        # 5. 拼接所有卷积核的池化输出
        # (batch_size, len(kernel_sizes) * num_filters)
        cnn_output = torch.cat(pooled_outputs, dim=1)
        cnn_output = self.dropout_layer(cnn_output) # 应用 Dropout

        # 6. 结合 Symbol Embedding
        # (batch_size, len(Ks)*num_filters + symbol_emb_dim)
        combined_repr = torch.cat([cnn_output, sym_emb], dim=-1)

        # 7. 最终投影
        final_embedding = self.projection(combined_repr) # (batch_size, final_emb_dim)

        return final_embedding
```

**5. 示例用法**

```python
# --- 准备伪数据 ---
BATCH_SIZE = 4 # 示例 Batch Size

# (B,)
dummy_symbol_indices = torch.randint(0, NUM_SYMBOLS, (BATCH_SIZE,), dtype=torch.long)
# (B, SeqLen)
dummy_token_sequences = torch.randint(0, VOCAB_SIZE, (BATCH_SIZE, SEQ_LEN), dtype=torch.long)

# (B, SeqLen, TimeFeatDim) - 实际中你需要根据 datetime 数据生成这个
# 这里用随机数代替
dummy_time_features = torch.randn(BATCH_SIZE, SEQ_LEN, TIME_FEATURE_DIM, dtype=torch.float)

# --- 实例化并测试模型 ---

# RNN
print("--- RNN Embedder ---")
rnn_embedder = TimeSeriesRNNEmbedder(
    num_symbols=NUM_SYMBOLS, vocab_size=VOCAB_SIZE, seq_len=SEQ_LEN,
    symbol_emb_dim=SYMBOL_EMB_DIM, token_emb_dim=TOKEN_EMB_DIM,
    time_feature_dim=TIME_FEATURE_DIM, hidden_dim=HIDDEN_DIM,
    num_layers=NUM_RNN_LAYERS, final_emb_dim=FINAL_EMB_DIM, dropout=DROPOUT
)
rnn_output = rnn_embedder(dummy_symbol_indices, dummy_token_sequences, dummy_time_features)
print("RNN Output Shape:", rnn_output.shape) # 应为 (BATCH_SIZE, FINAL_EMB_DIM)

# Transformer
print("\n--- Transformer Embedder ---")
transformer_embedder = TimeSeriesTransformerEmbedder(
    num_symbols=NUM_SYMBOLS, vocab_size=VOCAB_SIZE, seq_len=SEQ_LEN,
    symbol_emb_dim=SYMBOL_EMB_DIM, token_emb_dim=TOKEN_EMB_DIM,
    time_feature_dim=TIME_FEATURE_DIM, d_model=HIDDEN_DIM, # 使用 HIDDEN_DIM 作为 d_model
    nhead=NUM_TRANSFORMER_HEADS, num_encoder_layers=NUM_TRANSFORMER_LAYERS,
    dim_feedforward=HIDDEN_DIM * 4, # 通常设为 d_model 的 4 倍
    final_emb_dim=FINAL_EMB_DIM, dropout=DROPOUT
)
transformer_output = transformer_embedder(dummy_symbol_indices, dummy_token_sequences, dummy_time_features)
print("Transformer Output Shape:", transformer_output.shape) # 应为 (BATCH_SIZE, FINAL_EMB_DIM)

# CNN
print("\n--- CNN Embedder ---")
cnn_embedder = TimeSeriesCNNEmbedder(
    num_symbols=NUM_SYMBOLS, vocab_size=VOCAB_SIZE, seq_len=SEQ_LEN,
    symbol_emb_dim=SYMBOL_EMB_DIM, token_emb_dim=TOKEN_EMB_DIM,
    time_feature_dim=TIME_FEATURE_DIM, num_filters=CNN_NUM_FILTERS,
    kernel_sizes=CNN_KERNEL_SIZES, final_emb_dim=FINAL_EMB_DIM, dropout=DROPOUT
)
cnn_output = cnn_embedder(dummy_symbol_indices, dummy_token_sequences, dummy_time_features)
print("CNN Output Shape:", cnn_output.shape) # 应为 (BATCH_SIZE, FINAL_EMB_DIM)
```

**重要说明:**

1.  **时间特征工程 (`get_time_features` 和 `dummy_time_features`)**: 这部分非常关键。示例中的 `get_time_features` 只是一个起点。你需要仔细考虑哪些时间特征对你的任务最重要，并进行适当的编码（周期性、归一化等）。实际使用时，你需要批量处理你的 `datetime` 数据，生成 `time_feature_vectors` Tensor。
2.  **训练:** 这些模型定义了前向传播。你需要根据你的目标（RAG检索）选择合适的训练方法，如**对比学习 (Triplet Loss, SimCLR等)**。你需要构造正负样本对，并定义相应的损失函数来训练模型参数。这部分代码未包含在内，但至关重要。
3.  **超参数调整:** 代码中定义的超参数（Embedding 维度、隐藏层大小、层数、滤波器数量、核大小、Dropout率等）需要通过实验仔细调整以获得最佳性能。
4.  **模型选择:** 没有绝对最好的模型，你需要根据你的数据特性、计算资源和实验结果来选择 LSTM, Transformer 还是 CNN（或它们的组合）。
5.  **Padding:** 如果你的序列长度不固定（小于 `SEQ_LEN`），你需要进行 padding，并在 Transformer 中使用 `src_key_padding_mask`，在 RNN 中可能需要 `pack_padded_sequence` 来提高效率和准确性。CNN 对 padding 相对不敏感，但仍需注意。
6.  **Symbol Embedding 处理:** 代码中将 Symbol Embedding 在最后与序列表示拼接。也可以尝试在每个时间步将 Symbol Embedding 加入输入，但这会增加输入维度。

选择其中一个模型，并结合对比学习等训练方法进行训练，得到的 `final_embedding` 就可以用于构建 RAG 的向量索引库了。


Okay, 让我们将 `delta_t` (相邻时间点的时间差) 特征整合到模型中。

核心步骤：

1.  **计算 `delta_t`**: 在数据预处理阶段，你需要计算每个时间点与其前一个时间点的时间差。
2.  **数值化和缩放**: 将时间差转换为数值（例如，总秒数、分钟数或小时数），然后进行缩放（如标准化或归一化），使其数值范围适合神经网络处理。
3.  **整合到输入特征**: 将缩放后的 `delta_t` 作为每个时间步的一个额外特征，拼接到其他时间特征（如周期编码特征）中。

**1. 数据预处理阶段 - 计算和缩放 `delta_t`**

假设你有一个包含 `datetime` 对象的列表或数组，对应一个长度为 `SEQ_LEN` 的序列。

```python
import numpy as np
import torch
from datetime import timedelta

# --- 假设你有一个 datetime 对象列表/数组 ---
# 示例: datetimes_list = [dt_obj_0, dt_obj_1, ..., dt_obj_29]
# 例如:
from datetime import datetime, timedelta
base_time = datetime(2023, 1, 1, 9, 30, 0)
datetimes_list = [base_time + timedelta(minutes=i*np.random.randint(1, 6)) for i in range(SEQ_LEN)] # 随机分钟间隔

# --- 计算 delta_t (例如，以秒为单位) ---
delta_t_values = np.zeros(SEQ_LEN, dtype=np.float32)
for i in range(1, SEQ_LEN):
    time_diff = datetimes_list[i] - datetimes_list[i-1]
    delta_t_values[i] = time_diff.total_seconds() # 使用总秒数

# --- 缩放 delta_t ---
# 方法1: 标准化 (需要训练集的 mean 和 std)
# 假设你从训练集计算得到了 mean_delta_t 和 std_delta_t
# mean_delta_t = np.mean(all_training_delta_t_values_excluding_first)
# std_delta_t = np.std(all_training_delta_t_values_excluding_first)
#
# # 防止除以零，并处理第一个元素 (其 delta_t 为 0)
# epsilon = 1e-8
# scaled_delta_t = np.zeros_like(delta_t_values)
# if std_delta_t > epsilon:
#     scaled_delta_t[1:] = (delta_t_values[1:] - mean_delta_t) / std_delta_t
# else: # 如果标准差为0或非常小，只做中心化或保持为0
#      scaled_delta_t[1:] = (delta_t_values[1:] - mean_delta_t)

# 方法2: Log + 标准化 (对于变化范围大的 delta_t 可能更好)
log_delta_t = np.log1p(delta_t_values) # log(1 + x) 处理 0 值
# 假设你从训练集计算得到了 mean_log_delta_t 和 std_log_delta_t (基于 log1p 变换后的值)
# mean_log_delta_t = np.mean(np.log1p(all_training_delta_t_values_excluding_first))
# std_log_delta_t = np.std(np.log1p(all_training_delta_t_values_excluding_first))
# epsilon = 1e-8
# scaled_delta_t = np.zeros_like(log_delta_t)
# if std_log_delta_t > epsilon:
#     scaled_delta_t[1:] = (log_delta_t[1:] - mean_log_delta_t) / std_log_delta_t
# else:
#     scaled_delta_t[1:] = (log_delta_t[1:] - mean_log_delta_t)

# !!! 重要: 在实际应用中，mean 和 std 必须从整个训练数据集计算，
# !!! 并且在处理验证集和测试集时使用相同的 mean 和 std。
# !!! 下面为了代码能运行，我们先用当前序列的值做标准化（这是不正确的做法，仅为演示）
current_mean = np.mean(delta_t_values[1:])
current_std = np.std(delta_t_values[1:])
epsilon = 1e-8
scaled_delta_t = np.zeros_like(delta_t_values)
if current_std > epsilon:
    scaled_delta_t[1:] = (delta_t_values[1:] - current_mean) / current_std
else:
     scaled_delta_t[1:] = (delta_t_values[1:] - current_mean)


# --- 将 scaled_delta_t 添加到其他时间特征 ---
# 假设 other_time_features 是 (SEQ_LEN, original_time_feature_dim) 的 NumPy 数组
# 例如，包含周期编码特征
other_time_features = np.array([get_time_features(dt) for dt in datetimes_list])

# 将 scaled_delta_t 增加一个维度，使其成为 (SEQ_LEN, 1)
scaled_delta_t_reshaped = scaled_delta_t[:, np.newaxis]

# 拼接特征
# new_time_features 的形状将是 (SEQ_LEN, original_time_feature_dim + 1)
new_time_features = np.concatenate([other_time_features, scaled_delta_t_reshaped], axis=-1)

# --- 更新 TIME_FEATURE_DIM ---
# 原来的 TIME_FEATURE_DIM 是 8 (来自 get_time_features)
# 现在需要加 1
NEW_TIME_FEATURE_DIM = TIME_FEATURE_DIM + 1

print(f"Original time feature shape per step: {other_time_features.shape[1]}")
print(f"Delta_t shape per step: {scaled_delta_t_reshaped.shape[1]}")
print(f"New combined time feature shape per step: {new_time_features.shape[1]}")
print(f"Updated TIME_FEATURE_DIM: {NEW_TIME_FEATURE_DIM}")

# 在构建 batch 时，确保每个序列都进行了这样的处理
# 最终得到 time_feature_vectors: (batch_size, seq_len, NEW_TIME_FEATURE_DIM)
```

**2. 修改模型定义**

只需要在实例化模型时，将更新后的 `time_feature_dim` 传递给构造函数即可。模型内部的维度计算会自动调整。

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
# 假设之前的 PositionalEncoding, TimeSeriesRNNEmbedder,
# TimeSeriesTransformerEmbedder, TimeSeriesCNNEmbedder 类定义不变

# --- 更新的超参数 ---
TIME_FEATURE_DIM_WITH_DELTA_T = TIME_FEATURE_DIM + 1 # 例如 8 + 1 = 9

# --- 实例化模型 (以 RNN 为例) ---
rnn_embedder_with_delta_t = TimeSeriesRNNEmbedder(
    num_symbols=NUM_SYMBOLS,
    vocab_size=VOCAB_SIZE,
    seq_len=SEQ_LEN,
    symbol_emb_dim=SYMBOL_EMB_DIM,
    token_emb_dim=TOKEN_EMB_DIM,
    # 使用更新后的维度
    time_feature_dim=TIME_FEATURE_DIM_WITH_DELTA_T,
    hidden_dim=HIDDEN_DIM,
    num_layers=NUM_RNN_LAYERS,
    final_emb_dim=FINAL_EMB_DIM,
    dropout=DROPOUT
)

# --- 实例化模型 (以 Transformer 为例) ---
transformer_embedder_with_delta_t = TimeSeriesTransformerEmbedder(
    num_symbols=NUM_SYMBOLS,
    vocab_size=VOCAB_SIZE,
    seq_len=SEQ_LEN,
    symbol_emb_dim=SYMBOL_EMB_DIM,
    token_emb_dim=TOKEN_EMB_DIM,
    # 使用更新后的维度
    time_feature_dim=TIME_FEATURE_DIM_WITH_DELTA_T,
    d_model=HIDDEN_DIM,
    nhead=NUM_TRANSFORMER_HEADS,
    num_encoder_layers=NUM_TRANSFORMER_LAYERS,
    dim_feedforward=HIDDEN_DIM * 4,
    final_emb_dim=FINAL_EMB_DIM,
    dropout=DROPOUT
)

# --- 实例化模型 (以 CNN 为例) ---
cnn_embedder_with_delta_t = TimeSeriesCNNEmbedder(
    num_symbols=NUM_SYMBOLS,
    vocab_size=VOCAB_SIZE,
    seq_len=SEQ_LEN,
    symbol_emb_dim=SYMBOL_EMB_DIM,
    token_emb_dim=TOKEN_EMB_DIM,
    # 使用更新后的维度
    time_feature_dim=TIME_FEATURE_DIM_WITH_DELTA_T,
    num_filters=CNN_NUM_FILTERS,
    kernel_sizes=CNN_KERNEL_SIZES,
    final_emb_dim=FINAL_EMB_DIM,
    dropout=DROPOUT
)


# --- 准备包含 delta_t 的伪数据 ---
BATCH_SIZE = 4

dummy_symbol_indices = torch.randint(0, NUM_SYMBOLS, (BATCH_SIZE,), dtype=torch.long)
dummy_token_sequences = torch.randint(0, VOCAB_SIZE, (BATCH_SIZE, SEQ_LEN), dtype=torch.long)

# (B, SeqLen, TimeFeatDim + 1) - 现在时间特征维度增加了
dummy_time_features_with_delta_t = torch.randn(BATCH_SIZE, SEQ_LEN, TIME_FEATURE_DIM_WITH_DELTA_T, dtype=torch.float)

# --- 测试模型 ---
print("\n--- RNN with Delta_t ---")
rnn_output_dt = rnn_embedder_with_delta_t(dummy_symbol_indices, dummy_token_sequences, dummy_time_features_with_delta_t)
print("RNN Output Shape:", rnn_output_dt.shape)

print("\n--- Transformer with Delta_t ---")
transformer_output_dt = transformer_embedder_with_delta_t(dummy_symbol_indices, dummy_token_sequences, dummy_time_features_with_delta_t)
print("Transformer Output Shape:", transformer_output_dt.shape)

print("\n--- CNN with Delta_t ---")
cnn_output_dt = cnn_embedder_with_delta_t(dummy_symbol_indices, dummy_token_sequences, dummy_time_features_with_delta_t)
print("CNN Output Shape:", cnn_output_dt.shape)

```

**总结:**

1.  **预处理**: 修改你的数据加载和预处理流程，计算每个序列中相邻时间点的 `delta_t`，将其转换为合适的数值单位（如秒），然后进行缩放（标准化或 log+标准化，使用训练集的统计数据）。
2.  **特征合并**: 将缩放后的 `delta_t` 作为一个新特征，添加到原来的时间特征向量中（例如，使用 `np.concatenate` 或 `torch.cat`）。
3.  **更新维度**: 记录下新的时间特征总维度 (`NEW_TIME_FEATURE_DIM`)。
4.  **模型实例化**: 在创建模型实例时，传入这个新的 `time_feature_dim`。模型内部结构（如 `nn.Linear`, `nn.LSTM`, `nn.Conv1d` 的输入维度）会根据这个参数自动调整。

这样，模型就能在处理序列时，同时考虑到 `token` 信息、绝对时间信息（来自周期编码等）以及相对时间间隔信息（来自 `delta_t`）。