{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1580,) (1580, 51)\n", "(527,) (527, 51)\n", "Fitting 3 folds for each of 2916 candidates, totalling 8748 fits\n"]}], "source": ["import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import lightgbm as lgb\n", "from sklearn.grid_search import GridSearchCV  # Perforing grid search\n", "from sklearn.model_selection import train_test_split\n", "\n", "# train_data = pd.read_csv('train.csv')   # 读取数据\n", "# y = train_data.pop('30').values   # 用pop方式将训练数据中的标签值y取出来，作为训练目标，这里的‘30’是标签的列名\n", "# col = train_data.columns   \n", "# x = train_data[col].values  # 剩下的列作为训练数据\n", "# y = pd.read_csv('d:/QuantLab/log/y_long.train', header = None).values\n", "# x = pd.read_csv('d:/QuantLab/log/X_long.train', header = None).values\n", "# train_x, valid_x, train_y, valid_y = train_test_split(x, y, test_size=0.333, random_state=0)   # 分训练集和验证集\n", "\n", "FD_PATH = \"d:/QuantLab/log/\"\n", "train_long = pd.read_csv(FD_PATH + 'long.train', header = None)\n", "test_long = pd.read_csv(FD_PATH + 'long.test', header = None)\n", "\n", "train_y = train_long[0]\n", "train_x = train_long.drop(0, axis=1)\n", "print(train_y.shape, train_x.shape)\n", "valid_y = test_long[0]\n", "valid_x = test_long.drop(0, axis=1)\n", "print(valid_y.shape, valid_x.shape)\n", "\n", "train = lgb.Dataset(train_x, train_y)\n", "valid = lgb.Dataset(valid_x, valid_y, reference=train)\n", "\n", "# print(train_x,train_y)\n", "# print(valid_x,valid_y)\n", "\n", "# parameters = {\n", "#               'max_depth': [15, 20, 25, 30, 35],\n", "#               'learning_rate': [0.01, 0.02, 0.05, 0.1, 0.15],\n", "#               'feature_fraction': [0.6, 0.7, 0.8, 0.9, 0.95],\n", "#               'bagging_fraction': [0.6, 0.7, 0.8, 0.9, 0.95],\n", "#               'bagging_freq': [2, 4, 5, 6, 8],\n", "#               'lambda_l1': [0, 0.1, 0.4, 0.5, 0.6],\n", "#               'lambda_l2': [0, 10, 15, 35, 40],\n", "#               'cat_smooth': [1, 10, 15, 20, 35]\n", "# }\n", "parameters = {\n", "    'learning_rate': [0.01],  # 0.1\n", "    'feature_fraction': [0.6, 0.8, 0.95],  # 1.0 [0.0,1.0]\n", "    'bagging_fraction': [0.6, 0.8, 0.95],  # 1.0 [0.0,1.0]\n", "    'bagging_freq': [2, 5, 8],\n", "    'num_leaves': [30, 40, 50],  # 31\n", "    'max_depth': [8, 12],  # -1\n", "    'max_bin': [10, 30],  # 255\n", "    'lambda_l1': [0, 0.1, 0.6],\n", "    'lambda_l2': [0, 10, 40],\n", "}\n", "\n", "gbm = lgb.LGBMClassifier(boosting_type='gbdt',\n", "                         objective = 'binary',\n", "                         metric = 'auc',\n", "                         verbose = 1,\n", "                         learning_rate = 0.01,\n", "                         num_leaves = 35,\n", "                         feature_fraction=0.8,\n", "                         bagging_fraction= 0.9,\n", "                         bagging_freq= 8,\n", "                         lambda_l1= 0.6,\n", "                         lambda_l2= 0)\n", "# 有了gridsearch我们便不需要fit函数\n", "gsearch = GridSearchCV(gbm, param_grid=parameters, scoring='accuracy', cv=3, verbose=2)\n", "# print(train_y.shape)\n", "# c, r = train_y.shape\n", "# train_y = train_y.reshape(c,)\n", "gsearch.fit(train_x, train_y)\n", "\n", "print(\"Best score: %0.3f\" % gsearch.best_score_)\n", "print(\"Best parameters set:\")\n", "best_parameters = gsearch.best_estimator_.get_params()\n", "for param_name in sorted(parameters.keys()):\n", "    print(\"\\t%s: %r\" % (param_name, best_parameters[param_name]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}