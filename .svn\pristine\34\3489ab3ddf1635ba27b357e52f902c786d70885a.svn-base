import pandas as pd
import numpy as np
import sys
# sys.path.append("d:/QuantLab")
# from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

sys.path.append("e:/github/qlib")
sys.path.append("e:/lab/RoboQuant/pylab")
import pyqlab
from pyqlab.data.dataset import AFDatasetH

dataset = AFDatasetH(
    handler={
        "class": "DataHandlerAF",
        "module_path": "pyqlab.data.dataset.handler",
        "kwargs": {
            "start_time": "2020-01-01",
            "end_time": "2020-08-01",
            "instruments": "csi300",
            "data_loader": {
                "class": "AFDataLoader",
                "module_path": "pyqlab.data.dataset.loader",
                "kwargs": {
                    "direct": "long",
                    "model_name": "MLP2",
                    "model_path": "e:/lab/RoboQuant/pylab/model",
                    "data_path": "e:/lab/RoboQuant/pylab/data",
                    "portfolios": ['00200910081133001', '00171106132928000', '00170623114649000'],
                }
            },
        },
    },
    segments={
        "train": ("2020-01-01", "2020-03-01"),
        "valid": ("2020-03-01", "2020-06-01"),
        "test": ("2020-06-01", "2020-08-01"),
    },
)

train_df = dataset.prepare("train")
print(train_df.shape)
#print(train_df)

#valid_df = dataset.prepare("valid")
#print(valid_df.shape)
#print(valid_df)

for ds in dataset:
    print(ds)
    break