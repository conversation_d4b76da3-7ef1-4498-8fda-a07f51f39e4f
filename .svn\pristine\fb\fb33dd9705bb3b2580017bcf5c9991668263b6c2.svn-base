{"cells": [{"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config\n", "from qlib.data.dataset import DatasetH\n", "# from qlib.model.base import Model\n", "from qlib.data.dataset.handler import DataHandlerLP\n", "\n", "import tensorflow as tf\n", "\n", "from pyqlab.data.dataset.handler import DataHandlerAF\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[3476:MainThread](2022-10-16 23:59:40,091) INFO - qlib.Initialization - [config.py:413] - default_conf: client.\n", "[3476:MainThread](2022-10-16 23:59:40,094) INFO - qlib.Initialization - [__init__.py:74] - qlib successfully initialized based on client settings.\n", "[3476:MainThread](2022-10-16 23:59:40,096) INFO - qlib.Initialization - [__init__.py:76] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["\n", "provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\", \"LONG_RANGE\", \n", "  \"FAST_QH_RSI\", \"FAST_QH_STDDEV\", \"SLOW_QH_RSI\", \"SLOW_QH_STDDEV\",\n", "#   \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\", \"SLOW_QH_ZSCORE\", \"SLOW_QH_DIRECT\", \n", "]\n", "\n", "\n", "data_handler_config = {\n", "    \"start_time\": \"\",\n", "    \"end_time\": \"\",\n", "    \"instruments\": ['06220831232331000', '01220901173143000'],\n", "    \"data_loader\": {\n", "        \"class\": \"AFDataLoader\",\n", "        \"module_path\": \"pyqlab.data.dataset.loader\",\n", "        \"kwargs\": {\n", "            \"direct\": \"L\",\n", "            \"model_name\": \"MLP_HLR\",\n", "            \"model_name_suff\": \"\",\n", "            \"model_path\": \"e:/lab/RoboQuant/pylab/model\",\n", "            \"data_path\": \"e:/lab/RoboQuant/pylab/data\",\n", "            \"sel_lf_names\": SEL_LONG_FACTOR_NAMES,\n", "            \"sel_sf_names\": SEL_SHORT_FACTOR_NAMES,\n", "            \"sel_ct_names\": SEL_CONTEXT_FACTOR_NAMES,\n", "        }\n", "    },\n", "}\n", "\n", "dataset_config = {\n", "    \"class\": \"AFDatasetH\",\n", "    \"module_path\": \"pyqlab.data.dataset\",\n", "    \"kwargs\": {\n", "        \"handler\": {\n", "            \"class\": \"DataHandlerAF\",\n", "            \"module_path\": \"pyqlab.data.dataset.handler\",\n", "            \"kwargs\": data_handler_config,\n", "        },\n", "        \"segments\": [\"train\", \"valid\"],\n", "        \"col_set\": [\"feature\", \"label\", \"encoded\"],\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[3476:MainThr<PERSON>](2022-10-17 00:00:22,778) INFO - qlib.DataHandlerAF - [handler.py:83] - \n", "============================\n", "\n", "lb(478, 6) sf(478, 45) lf(478, 45) ct(478, 7)\n", "\n", "============================\n", "[3476:MainThread](2022-10-17 00:00:22,788) INFO - qlib.timer - [log.py:117] - Time cost: 0.062s | Loading data Done\n", "[3476:MainThread](2022-10-17 00:00:22,789) INFO - qlib.DataHandlerAF - [handler.py:130] - train shape(382, 97) valid shape(96, 97)\n", "[3476:MainThread](2022-10-17 00:00:22,790) INFO - qlib.timer - [log.py:117] - Time cost: 0.065s | Init data Done\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Feature total: 478\n", "Today add long count: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[3476:MainThr<PERSON>](2022-10-17 00:00:23,384) INFO - qlib.DataHandlerAF - [handler.py:83] - \n", "============================\n", "\n", "lb(8848, 6) sf(8848, 45) lf(8848, 45) ct(8848, 7)\n", "\n", "============================\n", "[3476:MainThread](2022-10-17 00:00:23,442) INFO - qlib.timer - [log.py:117] - Time cost: 0.650s | Loading data Done\n", "[3476:MainThread](2022-10-17 00:00:23,444) INFO - qlib.DataHandlerAF - [handler.py:130] - train shape(7078, 97) valid shape(1770, 97)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Feature total: 8848\n", "Today add long count: 0\n"]}], "source": ["data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n", "hd_long: DataHandlerAF = init_instance_by_config(dataset_config[\"kwargs\"][\"handler\"])\n", "\n", "data_handler_config[\"instruments\"] = ['00211229152555000', '00171106132928000']\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"model_name_suff\"] = \"5R\"\n", "\n", "dataset_config[\"kwargs\"][\"handler\"] = hd_long\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n", "\n", "dataset = init_instance_by_config(dataset_config)\n", "dataset.setup_data(handler_kwargs=data_handler_config)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[3476:MainThread](2022-10-17 00:00:58,772) INFO - qlib.DatasetH - [__init__.py:83] - data_key[learn] is ignored.\n"]}], "source": ["df_train, df_valid = dataset.prepare(\n", "    [\"train\", \"valid\"],\n", "    col_set=[\"feature\", \"label\", \"encoded\"],\n", "    data_key=DataHandlerLP.DK_L,\n", ")\n", "code_train, x_train, y_train = df_train[\"encoded\"], df_train[\"feature\"], df_train[\"label\"]\n", "code_valid, x_valid, y_valid = df_valid[\"encoded\"], df_valid[\"feature\"], df_valid[\"label\"]"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["(7078, 99)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train.shape"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["code_train_values = np.squeeze(code_train.values)\n", "x_train_values = x_train.values\n", "y_train_values = np.squeeze(y_train.values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["embedd = tf.keras.layers.Embedding(input_dim=60, output_dim=2)(code_train_values)\n", "x = tf.keras.backend.concatenate([x_train_values, embedd], axis=1)\n", "x = tf.keras.layers.Concatenate(axis=1)([x_train_values, embedd])"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [], "source": ["# onehot独热编码\n", "# oh = tf.keras.utils.to_categorical()\n", "class MLP(tf.keras.Model):\n", "    def __init__(self, num_code, num_input, dropout=0.5, output_dim=1):\n", "        super().__init__()\n", "        self.embedding = tf.keras.layers.Embedding(input_dim=num_code, output_dim=4)\n", "        self.concat = tf.keras.layers.Concatenate(axis=1)\n", "        self.flatten = tf.keras.layers.Flatten()    # Flatten层将除第一维（batch_size）以外的维度展平\n", "        self.dense1 = tf.keras.layers.Dense(units=128, activation='relu')\n", "        self.dense2 = tf.keras.layers.Dense(units=96, activation='relu')\n", "        self.dense3 = tf.keras.layers.Dense(units=1)\n", "        self.dropout = tf.keras.layers.Dropout(rate=0.5)\n", "        self.bn = tf.keras.layers.BatchNormalization(axis=1)\n", "\n", "    def call(self, inputs):         # [128, 28, 28]\n", "        # x = self.flatten(inputs)    # [batch_size, 784]\n", "        embedding = self.embedding(inputs[0])\n", "        x = self.concat([inputs[1], embedding])\n", "        x = self.dense1(x)          # [batch_size, 100]\n", "        # x = self.dropout(x)\n", "        x = self.dense2(x)      # [batch_size, 10]\n", "        # x = self.dropout(x)\n", "        x = self.dense3(x)      # [batch_size, 10]\n", "        output = tf.nn.sigmoid(x)\n", "        return output"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["model = MLP(60, 99, 0.03)"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["model.compile(\n", "    optimizer=tf.keras.optimizers.<PERSON>(learning_rate=0.001),\n", "    loss = tf.keras.losses.binary_crossentropy,\n", "    metrics=['acc']\n", ")"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/50\n", "25/25 - 1s - loss: 0.7051 - acc: 0.5168 - val_loss: 0.6945 - val_acc: 0.5240 - 564ms/epoch - 23ms/step\n", "Epoch 2/50\n", "25/25 - 0s - loss: 0.6797 - acc: 0.5659 - val_loss: 0.6929 - val_acc: 0.5480 - 71ms/epoch - 3ms/step\n", "Epoch 3/50\n", "25/25 - 0s - loss: 0.6695 - acc: 0.5954 - val_loss: 0.6934 - val_acc: 0.5367 - 66ms/epoch - 3ms/step\n", "Epoch 4/50\n", "25/25 - 0s - loss: 0.6590 - acc: 0.6193 - val_loss: 0.6998 - val_acc: 0.5268 - 71ms/epoch - 3ms/step\n", "Epoch 5/50\n", "25/25 - 0s - loss: 0.6509 - acc: 0.6358 - val_loss: 0.6985 - val_acc: 0.5240 - 69ms/epoch - 3ms/step\n", "Epoch 6/50\n", "25/25 - 0s - loss: 0.6415 - acc: 0.6484 - val_loss: 0.7007 - val_acc: 0.5282 - 74ms/epoch - 3ms/step\n", "Epoch 7/50\n", "25/25 - 0s - loss: 0.6329 - acc: 0.6600 - val_loss: 0.7030 - val_acc: 0.5268 - 72ms/epoch - 3ms/step\n", "Epoch 8/50\n", "25/25 - 0s - loss: 0.6245 - acc: 0.6728 - val_loss: 0.7068 - val_acc: 0.5268 - 69ms/epoch - 3ms/step\n", "Epoch 9/50\n", "25/25 - 0s - loss: 0.6171 - acc: 0.6738 - val_loss: 0.7108 - val_acc: 0.5226 - 72ms/epoch - 3ms/step\n", "Epoch 10/50\n", "25/25 - 0s - loss: 0.6079 - acc: 0.6802 - val_loss: 0.7087 - val_acc: 0.5184 - 72ms/epoch - 3ms/step\n", "Epoch 11/50\n", "25/25 - 0s - loss: 0.5920 - acc: 0.7093 - val_loss: 0.7178 - val_acc: 0.5240 - 70ms/epoch - 3ms/step\n", "Epoch 12/50\n", "25/25 - 0s - loss: 0.5813 - acc: 0.7168 - val_loss: 0.7166 - val_acc: 0.5226 - 77ms/epoch - 3ms/step\n", "Epoch 13/50\n", "25/25 - 0s - loss: 0.5686 - acc: 0.7278 - val_loss: 0.7225 - val_acc: 0.5169 - 77ms/epoch - 3ms/step\n", "Epoch 14/50\n", "25/25 - 0s - loss: 0.5603 - acc: 0.7301 - val_loss: 0.7261 - val_acc: 0.5438 - 73ms/epoch - 3ms/step\n", "Epoch 15/50\n", "25/25 - 0s - loss: 0.5503 - acc: 0.7367 - val_loss: 0.7328 - val_acc: 0.5381 - 63ms/epoch - 3ms/step\n", "Epoch 16/50\n", "25/25 - 0s - loss: 0.5358 - acc: 0.7524 - val_loss: 0.7447 - val_acc: 0.5268 - 63ms/epoch - 3ms/step\n", "Epoch 17/50\n", "25/25 - 0s - loss: 0.5224 - acc: 0.7625 - val_loss: 0.7509 - val_acc: 0.5325 - 69ms/epoch - 3ms/step\n", "Epoch 18/50\n", "25/25 - 0s - loss: 0.5098 - acc: 0.7722 - val_loss: 0.7573 - val_acc: 0.5297 - 69ms/epoch - 3ms/step\n", "Epoch 19/50\n", "25/25 - 0s - loss: 0.4997 - acc: 0.7747 - val_loss: 0.7638 - val_acc: 0.5311 - 70ms/epoch - 3ms/step\n", "Epoch 20/50\n", "25/25 - 0s - loss: 0.4839 - acc: 0.7893 - val_loss: 0.7622 - val_acc: 0.5395 - 68ms/epoch - 3ms/step\n", "Epoch 21/50\n", "25/25 - 0s - loss: 0.4762 - acc: 0.7947 - val_loss: 0.7759 - val_acc: 0.5282 - 65ms/epoch - 3ms/step\n", "Epoch 22/50\n", "25/25 - 0s - loss: 0.4638 - acc: 0.8006 - val_loss: 0.7736 - val_acc: 0.5381 - 64ms/epoch - 3ms/step\n", "Epoch 23/50\n", "25/25 - 0s - loss: 0.4507 - acc: 0.8108 - val_loss: 0.7856 - val_acc: 0.5410 - 69ms/epoch - 3ms/step\n", "Epoch 24/50\n", "25/25 - 0s - loss: 0.4424 - acc: 0.8162 - val_loss: 0.7934 - val_acc: 0.5424 - 69ms/epoch - 3ms/step\n", "Epoch 25/50\n", "25/25 - 0s - loss: 0.4240 - acc: 0.8267 - val_loss: 0.7995 - val_acc: 0.5325 - 66ms/epoch - 3ms/step\n", "Epoch 26/50\n", "25/25 - 0s - loss: 0.4134 - acc: 0.8312 - val_loss: 0.8213 - val_acc: 0.5494 - 68ms/epoch - 3ms/step\n", "Epoch 27/50\n", "25/25 - 0s - loss: 0.4013 - acc: 0.8425 - val_loss: 0.8388 - val_acc: 0.5226 - 67ms/epoch - 3ms/step\n", "Epoch 28/50\n", "25/25 - 0s - loss: 0.3930 - acc: 0.8458 - val_loss: 0.8307 - val_acc: 0.5297 - 85ms/epoch - 3ms/step\n", "Epoch 29/50\n", "25/25 - 0s - loss: 0.3821 - acc: 0.8540 - val_loss: 0.8370 - val_acc: 0.5353 - 66ms/epoch - 3ms/step\n", "Epoch 30/50\n", "25/25 - 0s - loss: 0.3726 - acc: 0.8579 - val_loss: 0.8464 - val_acc: 0.5410 - 63ms/epoch - 3ms/step\n", "Epoch 31/50\n", "25/25 - 0s - loss: 0.3573 - acc: 0.8705 - val_loss: 0.8589 - val_acc: 0.5438 - 68ms/epoch - 3ms/step\n", "Epoch 32/50\n", "25/25 - 0s - loss: 0.3440 - acc: 0.8747 - val_loss: 0.8905 - val_acc: 0.5438 - 64ms/epoch - 3ms/step\n", "Epoch 33/50\n", "25/25 - 0s - loss: 0.3360 - acc: 0.8757 - val_loss: 0.8856 - val_acc: 0.5424 - 75ms/epoch - 3ms/step\n", "Epoch 34/50\n", "25/25 - 0s - loss: 0.3268 - acc: 0.8835 - val_loss: 0.8871 - val_acc: 0.5254 - 67ms/epoch - 3ms/step\n", "Epoch 35/50\n", "25/25 - 0s - loss: 0.3156 - acc: 0.8889 - val_loss: 0.9712 - val_acc: 0.5551 - 63ms/epoch - 3ms/step\n", "Epoch 36/50\n", "25/25 - 0s - loss: 0.3112 - acc: 0.8860 - val_loss: 0.8998 - val_acc: 0.5452 - 63ms/epoch - 3ms/step\n", "Epoch 37/50\n", "25/25 - 0s - loss: 0.2949 - acc: 0.9005 - val_loss: 0.9211 - val_acc: 0.5424 - 64ms/epoch - 3ms/step\n", "Epoch 38/50\n", "25/25 - 0s - loss: 0.2857 - acc: 0.9047 - val_loss: 0.9511 - val_acc: 0.5212 - 65ms/epoch - 3ms/step\n", "Epoch 39/50\n", "25/25 - 0s - loss: 0.2759 - acc: 0.9115 - val_loss: 0.9623 - val_acc: 0.5480 - 67ms/epoch - 3ms/step\n", "Epoch 40/50\n", "25/25 - 0s - loss: 0.2685 - acc: 0.9154 - val_loss: 0.9708 - val_acc: 0.5607 - 64ms/epoch - 3ms/step\n", "Epoch 41/50\n", "25/25 - 0s - loss: 0.2586 - acc: 0.9223 - val_loss: 0.9886 - val_acc: 0.5466 - 65ms/epoch - 3ms/step\n", "Epoch 42/50\n", "25/25 - 0s - loss: 0.2450 - acc: 0.9279 - val_loss: 0.9950 - val_acc: 0.5494 - 66ms/epoch - 3ms/step\n", "Epoch 43/50\n", "25/25 - 0s - loss: 0.2394 - acc: 0.9294 - val_loss: 1.0179 - val_acc: 0.5537 - 63ms/epoch - 3ms/step\n", "Epoch 44/50\n", "25/25 - 0s - loss: 0.2295 - acc: 0.9352 - val_loss: 1.0336 - val_acc: 0.5466 - 69ms/epoch - 3ms/step\n", "Epoch 45/50\n", "25/25 - 0s - loss: 0.2249 - acc: 0.9339 - val_loss: 1.0446 - val_acc: 0.5410 - 66ms/epoch - 3ms/step\n", "Epoch 46/50\n", "25/25 - 0s - loss: 0.2200 - acc: 0.9385 - val_loss: 1.0484 - val_acc: 0.5410 - 63ms/epoch - 3ms/step\n", "Epoch 47/50\n", "25/25 - 0s - loss: 0.2038 - acc: 0.9480 - val_loss: 1.0754 - val_acc: 0.5395 - 64ms/epoch - 3ms/step\n", "Epoch 48/50\n", "25/25 - 0s - loss: 0.2023 - acc: 0.9487 - val_loss: 1.0753 - val_acc: 0.5410 - 63ms/epoch - 3ms/step\n", "Epoch 49/50\n", "25/25 - 0s - loss: 0.1944 - acc: 0.9491 - val_loss: 1.0920 - val_acc: 0.5367 - 61ms/epoch - 2ms/step\n", "Epoch 50/50\n", "25/25 - 0s - loss: 0.1859 - acc: 0.9538 - val_loss: 1.1013 - val_acc: 0.5367 - 65ms/epoch - 3ms/step\n"]}], "source": ["hist = model.fit(\n", "    x=[code_train_values, x_train_values],\n", "    y=y_train_values,\n", "    epochs=50,\n", "    batch_size=256,\n", "    verbose=2,\n", "    validation_split=0.1\n", ")"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x2a2407d5310>"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(hist.epoch, hist.history.get('acc'), label='acc')\n", "plt.plot(hist.epoch, hist.history.get('val_acc'), label='val_acc')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.save()"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["code_valid_values = np.squeeze(code_valid.values)\n", "x_valid_values = x_valid.values\n", "y_valid_values = np.squeeze(y_valid.values)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["56/56 - 0s - loss: 0.7081 - acc: 0.5254 - 60ms/epoch - 1ms/step\n"]}, {"data": {"text/plain": ["[0.7081162333488464, 0.5254237055778503]"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["model.evaluate([code_valid_values,x_valid_values],y_valid_values,verbose=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}