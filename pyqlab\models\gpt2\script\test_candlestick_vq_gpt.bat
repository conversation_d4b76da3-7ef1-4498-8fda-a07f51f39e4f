@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python test_candlestick_vq_gpt.py ^
--data_path f:/hqdata/fut_top_min1.parquet ^
--model_path e:/lab/RoboQuant/pylab/checkpoints/candlestick_vq_gpt\best_model.pt ^
--codebook_path e:/lab/RoboQuant/pylab/models/vqvae/vqvae_20250508_235443/vqcb_percent_change_fut_top_min1.pt ^
--vectorization_method percent_change ^
--use_time_features ^
--seq_len 30 ^
--pred_len 10 ^
--temperature 0.8 ^
--top_k 50 ^
--output_file prediction_result.png
