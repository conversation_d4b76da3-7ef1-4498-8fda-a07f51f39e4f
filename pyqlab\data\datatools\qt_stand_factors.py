# ### 标准化（Standardization）特征数据
# 
# 特征数据的标准化和归一化是在量化投资中常用的数据预处理技术，其主要目的是消除不同特征之间的尺度差异，使其具有相似的尺度范围，以便更好地适应模型的训练和优化过程。以下是标准化和归一化的作用和常见方法：
# 
# 作用：
# - 帮助优化算法更快地收敛：标准化和归一化可以使不同特征之间的尺度范围相似，使优化算法能够更快地找到最优解，避免因不同尺度带来的优化困难。
# 
# - 防止某些特征对模型的主导影响：如果某个特征的数值范围较大，可能会对模型的训练产生过大的影响，而忽略了其他特征的作用。标准化和归一化可以平衡不同特征之间的影响，确保模型能够全面考虑各个特征。
# 
# - 提高模型的稳定性和鲁棒性：标准化和归一化可以减少异常值和噪声对模型的干扰，提高模型的稳定性和鲁棒性。

# #### 1.Factors
# - lf: long period factors
# - sf: short period factors
# - ct: market and portfolio context factors

import pandas as pd
import numpy as np
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES
from argparse import ArgumentParser

# feat_path = 'e:/featdata'

class FactorMeanStd:

    def __init__(self, feat_path: str, save_path: str, fut_years: list):
        self.feat_path = feat_path
        self.save_path = save_path
        self.fut_years = fut_years
        self.fd_set = set({(1,0),(1,1),(2,0),(2,1),(6,0),(6,1)})

    def get_factor_cols(self):
        """
        因子列名称
        """
        col_names = []
        for name in ALL_FACTOR_NAMES:
            if name in TWO_VAL_FACTOR_NAMES:
                col_names.append(f"{name}_1")
                col_names.append(f"{name}_2")
            else:
                col_names.append(f"{name}_2")

        return col_names

    def check_data_quality(self, df, stage="原始数据", year=None, fd_name=None, suffix=None):
        """
        数据质量检查

        Args:
            df: 待检查的DataFrame
            stage: 检查阶段标识
            year: 年份
            fd_name: 因子数据名称
            suffix: 后缀

        Returns:
            dict: 数据质量报告
        """
        if df is None or df.empty:
            print(f"⚠️  [{stage}] 数据为空")
            return {"status": "empty", "total_rows": 0}

        total_rows, total_cols = df.shape

        # 基本信息
        quality_report = {
            "stage": stage,
            "year": year,
            "fd_name": fd_name,
            "suffix": suffix,
            "total_rows": total_rows,
            "total_cols": total_cols,
            "memory_usage_mb": df.memory_usage(deep=True).sum() / 1024 / 1024
        }

        # 检查缺失值
        missing_info = {}
        total_missing = 0
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                missing_ratio = missing_count / total_rows * 100
                missing_info[col] = {
                    "count": int(missing_count),
                    "ratio": round(missing_ratio, 2)
                }
                total_missing += missing_count

        quality_report["missing_values"] = {
            "total_missing": int(total_missing),
            "missing_ratio": round(total_missing / (total_rows * total_cols) * 100, 2),
            "columns_with_missing": len(missing_info),
            "details": missing_info
        }

        # 检查无穷值
        inf_info = {}
        total_inf = 0
        for col in df.select_dtypes(include=[np.number]).columns:
            inf_count = np.isinf(df[col]).sum()
            if inf_count > 0:
                inf_ratio = inf_count / total_rows * 100
                inf_info[col] = {
                    "count": int(inf_count),
                    "ratio": round(inf_ratio, 2)
                }
                total_inf += inf_count

        quality_report["infinite_values"] = {
            "total_inf": int(total_inf),
            "columns_with_inf": len(inf_info),
            "details": inf_info
        }

        # 检查零值（对于某些关键列）
        zero_info = {}
        key_columns = ['RSI_2', 'FAST_QH_NATR_ZSCORE', 'BAND_EXPAND_2']
        for col in key_columns:
            if col in df.columns:
                zero_count = (df[col] == 0.0).sum()
                if zero_count > 0:
                    zero_ratio = zero_count / total_rows * 100
                    zero_info[col] = {
                        "count": int(zero_count),
                        "ratio": round(zero_ratio, 2)
                    }

        quality_report["zero_values"] = {
            "key_columns_checked": key_columns,
            "details": zero_info
        }

        # 检查数据类型
        dtype_info = {}
        for col in df.columns:
            dtype_info[col] = str(df[col].dtype)
        quality_report["data_types"] = dtype_info

        # 检查重复行
        duplicate_count = df.duplicated().sum()
        quality_report["duplicates"] = {
            "count": int(duplicate_count),
            "ratio": round(duplicate_count / total_rows * 100, 2)
        }

        # 输出质量报告
        self._print_quality_report(quality_report)

        return quality_report

    def _print_quality_report(self, report):
        """
        打印数据质量报告
        """
        stage = report.get("stage", "未知阶段")
        year = report.get("year", "")
        fd_name = report.get("fd_name", "")
        suffix = report.get("suffix", "")

        print(f"\n📊 数据质量检查报告 - {stage}")
        if year and fd_name and suffix:
            print(f"   文件: {fd_name}.{suffix}.{year}")

        print(f"   数据规模: {report['total_rows']:,} 行 × {report['total_cols']} 列")
        print(f"   内存占用: {report['memory_usage_mb']:.2f} MB")

        # 缺失值报告
        missing = report["missing_values"]
        if missing["total_missing"] > 0:
            print(f"   ❌ 缺失值: {missing['total_missing']:,} 个 ({missing['missing_ratio']:.2f}%)")
            print(f"      涉及列数: {missing['columns_with_missing']}")
            if missing["details"]:
                print("      详细信息:")
                for col, info in list(missing["details"].items())[:5]:  # 只显示前5个
                    print(f"        - {col}: {info['count']:,} 个 ({info['ratio']:.2f}%)")
                if len(missing["details"]) > 5:
                    print(f"        ... 还有 {len(missing['details']) - 5} 个列有缺失值")
        else:
            print(f"   ✅ 缺失值: 无")

        # 无穷值报告
        inf = report["infinite_values"]
        if inf["total_inf"] > 0:
            print(f"   ❌ 无穷值: {inf['total_inf']:,} 个")
            print(f"      涉及列数: {inf['columns_with_inf']}")
        else:
            print(f"   ✅ 无穷值: 无")

        # 零值报告（关键列）
        zero = report["zero_values"]
        if zero["details"]:
            print(f"   ⚠️  关键列零值:")
            for col, info in zero["details"].items():
                print(f"      - {col}: {info['count']:,} 个 ({info['ratio']:.2f}%)")

        # 重复行报告
        dup = report["duplicates"]
        if dup["count"] > 0:
            print(f"   ⚠️  重复行: {dup['count']:,} 行 ({dup['ratio']:.2f}%)")
        else:
            print(f"   ✅ 重复行: 无")

        print("-" * 60)

    def save_quality_reports(self, reports, fd_name, suffix):
        """
        保存数据质量报告到文件

        Args:
            reports: 质量报告列表
            fd_name: 因子数据名称
            suffix: 后缀
        """
        if not reports:
            return

        # 创建汇总报告
        summary_data = []
        for report in reports:
            if report.get("status") == "empty":
                continue

            summary_data.append({
                "stage": report.get("stage", ""),
                "year": report.get("year", ""),
                "fd_name": report.get("fd_name", ""),
                "suffix": report.get("suffix", ""),
                "total_rows": report.get("total_rows", 0),
                "total_cols": report.get("total_cols", 0),
                "memory_usage_mb": report.get("memory_usage_mb", 0),
                "missing_count": report.get("missing_values", {}).get("total_missing", 0),
                "missing_ratio": report.get("missing_values", {}).get("missing_ratio", 0),
                "inf_count": report.get("infinite_values", {}).get("total_inf", 0),
                "duplicate_count": report.get("duplicates", {}).get("count", 0),
                "duplicate_ratio": report.get("duplicates", {}).get("ratio", 0)
            })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            report_file = f'{self.save_path}/{fd_name}_{suffix}_quality_report.csv'
            summary_df.to_csv(report_file, index=False, encoding='utf-8-sig')
            print(f"📄 质量报告已保存: {report_file}")

        return summary_data

    def get_factor_df(self, year, fd_name, suffix="main"):
        # 加载数据
        file_path = f'{self.feat_path}/ffs_{fd_name}.{suffix}.{year}.parquet'
        try:
            df = pd.read_parquet(file_path)
        except Exception as e:
            print(f"❌ 数据加载失败: {file_path}")
            print(f"   错误信息: {e}")
            return None

        # 数据加载后的质量检查
        print(f"\n🔍 开始数据质量检查: {fd_name}.{suffix}.{year}")
        original_report = self.check_data_quality(df, "数据加载后", year, fd_name, suffix)

        # 存储质量报告
        if not hasattr(self, '_quality_reports'):
            self._quality_reports = []
        self._quality_reports.append(original_report)

        # 数据清洗前的备份信息
        original_shape = df.shape

        # 开始数据清洗
        print(f"🧹 开始数据清洗...")

        # 填充缺失值
        missing_before = df.isnull().sum().sum()
        df.fillna(0.0, inplace=True)
        if missing_before > 0:
            print(f"   ✅ 缺失值填充: {missing_before:,} 个缺失值已填充为 0.0")

        # 过滤特定列的零值
        rows_filtered = 0
        if 'RSI_2' in df.columns:
            before_count = len(df)
            df = df[df['RSI_2'] != 0.0]
            filtered = before_count - len(df)
            rows_filtered += filtered
            if filtered > 0:
                print(f"   🔧 RSI_2 零值过滤: 移除 {filtered:,} 行")

        # 处理 change 列
        if 'change' in df.columns:
            before_count = len(df)
            df['change'] = df['change'].astype(float)
            df = df[(df['change'] <= 0.01) & (df['change'] >= -0.01)]
            filtered = before_count - len(df)
            rows_filtered += filtered
            if filtered > 0:
                print(f"   🔧 change 列极值过滤: 移除 {filtered:,} 行 (保留 [-0.01, 0.01] 范围)")

        # 剔除BAND_EXPAND_2大于15的极端数据
        if 'BAND_EXPAND_2' in df.columns:
            before_count = len(df)
            df = df[df['BAND_EXPAND_2'] < 15.0]
            filtered = before_count - len(df)
            rows_filtered += filtered
            if filtered > 0:
                print(f"   🔧 BAND_EXPAND_2 极值过滤: 移除 {filtered:,} 行 (保留 < 15.0)")

        # 注意：不删除列以保持数据位置匹配
        # 但可以将不需要的列设置为特殊值或保持原样
        if 'change' in df.columns:
            print(f"   ℹ️  保留 change 列以维持数据结构")
        if 'date' in df.columns:
            print(f"   ℹ️  保留 date 列以维持数据结构")

        # 数据清洗后的质量检查
        cleaned_report = self.check_data_quality(df, "数据清洗后", year, fd_name, suffix)
        self._quality_reports.append(cleaned_report)

        # 清洗效果总结
        print(f"📈 数据清洗总结:")
        print(f"   原始数据: {original_shape[0]:,} 行 × {original_shape[1]} 列")
        print(f"   清洗后: {df.shape[0]:,} 行 × {df.shape[1]} 列")
        print(f"   移除行数: {original_shape[0] - df.shape[0]:,} 行 ({(original_shape[0] - df.shape[0])/original_shape[0]*100:.2f}%)")
        print(f"   保持列数: {df.shape[1]} 列 (维持数据结构完整性)")

        return df

    def calc_factor_mean_std(self, year, fd_name, suffix="main"):
        df = self.get_factor_df(year, fd_name, suffix)
        # 将因子数据按CODE分组求均值和标准差
        df_mean = df.groupby('code').mean()
        df_std = df.groupby('code').std()
        return df_mean, df_std

    def get_factors_mean_std(self, fd_name, suffix="main"):
        # 计算商品期货因子数据的均值和标准差
        df_mean = {}
        df_std = {}

        for year in self.fut_years:
            print(f"\n🔄 处理年份: {year}")
            df_mean[year], df_std[year] = self.calc_factor_mean_std(year, fd_name, suffix)

        # 将所有因子数据的mean和std合并求均值
        df_mean_all = pd.concat(df_mean.values()).groupby('code').mean()
        df_std_all = pd.concat(df_std.values()).groupby('code').mean()
        df_mean_all = df_mean_all[self.get_factor_cols()] # 确保列的顺序一致
        df_std_all = df_std_all[self.get_factor_cols()] # 确保列的顺序一致

        return df_mean_all, df_std_all

    def get_all_factors_mean_std(self, suffixs=["main", "sf"]):
        for fd in self.fd_set:
            fd_name = f'fd_{fd[0]}_{fd[1]}'
            df_mean = pd.DataFrame()
            df_std = pd.DataFrame()

            # 初始化质量报告收集
            self._quality_reports = []

            for suffix in suffixs:
                print(f"\n🚀 开始处理: {fd_name} - {suffix}")
                fut_mean, fut_std = self.get_factors_mean_std(fd_name, suffix=suffix)
                print(f"   结果维度: {suffix} {fd_name} {fut_mean.shape}")
                df_mean = pd.concat([df_mean, fut_mean])
                df_std = pd.concat([df_std, fut_std])

                # 保存当前suffix的质量报告
                # if hasattr(self, '_quality_reports') and self._quality_reports:
                #     self.save_quality_reports(self._quality_reports, fd_name, suffix)
                #     self._quality_reports = []  # 清空报告列表

            print(f"📊 汇总结果: {fd_name} {df_mean.shape}")
            df_mean.to_csv(f'{self.save_path}/{fd_name}_mean.csv')
            df_std.to_csv(f'{self.save_path}/{fd_name}_std.csv')
            print(f"💾 已保存: {fd_name}_mean.csv, {fd_name}_std.csv")


    # 两个dataframe合并，不要覆盖第一个dataframe已有code字段的数据
    def mergers_mean_std_files(self, data_path1, data_path2):
        for fd in self.fd_set:
            fd_name = f'fd_{fd[0]}_{fd[1]}'
            df_mean1 = pd.read_csv(f'{data_path1}/{fd_name}_mean.csv')
            df_mean2 = pd.read_csv(f'{data_path2}/{fd_name}_mean.csv')
            df_mean = pd.concat([df_mean1, df_mean2], ignore_index=True)
            df_mean.drop_duplicates(subset=['code'], keep='last', inplace=True)
            df_mean.to_csv(f'{data_path1}/{fd_name}_mean.csv', index=False)
            print(f"{fd_name}_mean: ", fd_name, df_mean.shape)
            df_std1 = pd.read_csv(f'{data_path1}/{fd_name}_std.csv')
            df_std2 = pd.read_csv(f'{data_path2}/{fd_name}_std.csv')
            df_std = pd.concat([df_std1, df_std2], ignore_index=True)
            df_std.drop_duplicates(subset=['code'], keep='last', inplace=True)
            df_std.to_csv(f'{data_path1}/{fd_name}_std.csv', index=False)
            print(f"{fd_name}_std: ", df_std.shape)
        

def main(args):
    fms = FactorMeanStd(args.feat_path, args.save_path, args.fut_years)
    if args.mergers:
        fms.mergers_mean_std_files(data_path1='f:/featdata/main',
                                data_path2='f:/featdata/sf')
    else:
        fms.get_all_factors_mean_std(args.suffixs)

if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument('--mergers', default=False, type=bool)

    parser.add_argument('--suffixs', default=["top"], type=list)
    parser.add_argument('--feat_path', default='f:/featdata/tmp', type=str)
    parser.add_argument('--save_path', default='f:/featdata/tmp', type=str)
    parser.add_argument('--fut_years', default=['2025'], type=list)

    # parser.add_argument('--suffixs', default=["sf"], type=list)
    # parser.add_argument('--feat_path', default='f:/featdata/sf', type=str)
    # parser.add_argument('--save_path', default='f:/featdata/sf', type=str)
    # parser.add_argument('--fut_years', default=['2024','2025'], type=list)

    args = parser.parse_args()

    main(args)

    




