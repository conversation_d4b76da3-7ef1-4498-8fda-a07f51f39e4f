{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 1.232760720051104, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.012732180408252183, "policy_loss": -0.007333906619779525, "vf_loss": 0.01809490356548634, "vf_explained_var": -0.2367588633491147, "kl": 0.009855916448585597, "entropy": 1.089018451911147, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 465.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 4000, "num_env_steps_trained": 4000, "num_agent_steps_sampled": 4000, "num_agent_steps_trained": 4000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 4000, "num_agent_steps_trained": 4000, "num_env_steps_sampled": 4000, "num_env_steps_trained": 4000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 558.5597606969958, "num_env_steps_trained_throughput_per_sec": 558.5597606969958, "timesteps_total": 4000, "num_env_steps_sampled_lifetime": 4000, "num_agent_steps_sampled_lifetime": 4000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 4000, "timers": {"training_iteration_time_ms": 7161.275, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7161.275, "sample_time_ms": 1608.509, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5532.376, "learn_throughput": 723.017, "synch_weights_time_ms": 20.39}, "counters": {"num_env_steps_sampled": 4000, "num_env_steps_trained": 4000, "num_agent_steps_sampled": 4000, "num_agent_steps_trained": 4000}, "done": false, "training_iteration": 1, "trial_id": "e4287_00000", "date": "2025-01-06_14-03-29", "timestamp": 1736143409, "time_this_iter_s": 7.1612749099731445, "time_total_s": 7.1612749099731445, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 7.1612749099731445, "iterations_since_restore": 1, "perf": {"cpu_util_percent": 21.245454545454546, "ram_util_percent": 85.84545454545453}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 1.7420021475963694, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.021488866959047573, "policy_loss": -0.018030159207441474, "vf_loss": 0.03715272326515587, "vf_explained_var": -0.47597006386326207, "kl": 0.011831530183433095, "entropy": 1.0761438618424117, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 1395.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 8000, "num_env_steps_trained": 8000, "num_agent_steps_sampled": 8000, "num_agent_steps_trained": 8000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 8000, "num_agent_steps_trained": 8000, "num_env_steps_sampled": 8000, "num_env_steps_trained": 8000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 547.1302431063683, "num_env_steps_trained_throughput_per_sec": 547.1302431063683, "timesteps_total": 8000, "num_env_steps_sampled_lifetime": 8000, "num_agent_steps_sampled_lifetime": 8000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 8000, "timers": {"training_iteration_time_ms": 7236.074, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7236.074, "sample_time_ms": 1594.801, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5620.865, "learn_throughput": 711.634, "synch_weights_time_ms": 20.408}, "counters": {"num_env_steps_sampled": 8000, "num_env_steps_trained": 8000, "num_agent_steps_sampled": 8000, "num_agent_steps_trained": 8000}, "done": false, "training_iteration": 2, "trial_id": "e4287_00000", "date": "2025-01-06_14-03-36", "timestamp": 1736143416, "time_this_iter_s": 7.310873508453369, "time_total_s": 14.472148418426514, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 14.472148418426514, "iterations_since_restore": 2, "perf": {"cpu_util_percent": 22.910000000000004, "ram_util_percent": 85.88}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 1.6345710435061045, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.02825176951585598, "policy_loss": -0.012486771932773052, "vf_loss": 0.03843821071517674, "vf_explained_var": -0.41070303327293806, "kl": 0.011501637959714954, "entropy": 1.0595376983765632, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 2325.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 12000, "num_env_steps_trained": 12000, "num_agent_steps_sampled": 12000, "num_agent_steps_trained": 12000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 12000, "num_agent_steps_trained": 12000, "num_env_steps_sampled": 12000, "num_env_steps_trained": 12000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 549.8727904998203, "num_env_steps_trained_throughput_per_sec": 549.8727904998203, "timesteps_total": 12000, "num_env_steps_sampled_lifetime": 12000, "num_agent_steps_sampled_lifetime": 12000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 12000, "timers": {"training_iteration_time_ms": 7248.853, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7248.853, "sample_time_ms": 1581.895, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5647.968, "learn_throughput": 708.219, "synch_weights_time_ms": 18.321}, "counters": {"num_env_steps_sampled": 12000, "num_env_steps_trained": 12000, "num_agent_steps_sampled": 12000, "num_agent_steps_trained": 12000}, "done": false, "training_iteration": 3, "trial_id": "e4287_00000", "date": "2025-01-06_14-03-44", "timestamp": 1736143424, "time_this_iter_s": 7.274409770965576, "time_total_s": 21.74655818939209, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 21.74655818939209, "iterations_since_restore": 3, "perf": {"cpu_util_percent": 21.430000000000003, "ram_util_percent": 85.88}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 1.9218770498069384, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.04320715858891446, "policy_loss": -0.008608437754133696, "vf_loss": 0.04946162951573472, "vf_explained_var": -0.11077490429724417, "kl": 0.011769821051769899, "entropy": 1.0192308688035576, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 3255.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 16000, "num_env_steps_trained": 16000, "num_agent_steps_sampled": 16000, "num_agent_steps_trained": 16000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 16000, "num_agent_steps_trained": 16000, "num_env_steps_sampled": 16000, "num_env_steps_trained": 16000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 554.1699521603927, "num_env_steps_trained_throughput_per_sec": 554.1699521603927, "timesteps_total": 16000, "num_env_steps_sampled_lifetime": 16000, "num_agent_steps_sampled_lifetime": 16000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 16000, "timers": {"training_iteration_time_ms": 7241.14, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7241.14, "sample_time_ms": 1575.077, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5648.2, "learn_throughput": 708.19, "synch_weights_time_ms": 17.362}, "counters": {"num_env_steps_sampled": 16000, "num_env_steps_trained": 16000, "num_agent_steps_sampled": 16000, "num_agent_steps_trained": 16000}, "done": false, "training_iteration": 4, "trial_id": "e4287_00000", "date": "2025-01-06_14-03-51", "timestamp": 1736143431, "time_this_iter_s": 7.2180023193359375, "time_total_s": 28.964560508728027, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 28.964560508728027, "iterations_since_restore": 4, "perf": {"cpu_util_percent": 21.509090909090908, "ram_util_percent": 85.88181818181818}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 2.9446939168838404, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.0981415733414632, "policy_loss": -0.00991591584858715, "vf_loss": 0.10547771169031064, "vf_explained_var": -0.32435355282598927, "kl": 0.0128988990823282, "entropy": 1.0149325525247923, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 4185.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 20000, "num_env_steps_trained": 20000, "num_agent_steps_sampled": 20000, "num_agent_steps_trained": 20000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 20000, "num_agent_steps_trained": 20000, "num_env_steps_sampled": 20000, "num_env_steps_trained": 20000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 532.494759309088, "num_env_steps_trained_throughput_per_sec": 532.494759309088, "timesteps_total": 20000, "num_env_steps_sampled_lifetime": 20000, "num_agent_steps_sampled_lifetime": 20000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 20000, "timers": {"training_iteration_time_ms": 7295.274, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7295.274, "sample_time_ms": 1579.713, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5697.177, "learn_throughput": 702.102, "synch_weights_time_ms": 17.983}, "counters": {"num_env_steps_sampled": 20000, "num_env_steps_trained": 20000, "num_agent_steps_sampled": 20000, "num_agent_steps_trained": 20000}, "done": false, "training_iteration": 5, "trial_id": "e4287_00000", "date": "2025-01-06_14-03-59", "timestamp": 1736143439, "time_this_iter_s": 7.525683641433716, "time_total_s": 36.49024415016174, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 36.49024415016174, "iterations_since_restore": 5, "perf": {"cpu_util_percent": 22.11, "ram_util_percent": 85.78000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 2.3749186882408715, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.13903362365659847, "policy_loss": -0.014277606020851801, "vf_loss": 0.15071699716230874, "vf_explained_var": -0.25146058637608765, "kl": 0.012971171139968112, "entropy": 0.9808696373175549, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 5115.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 24000, "num_env_steps_trained": 24000, "num_agent_steps_sampled": 24000, "num_agent_steps_trained": 24000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 24000, "num_agent_steps_trained": 24000, "num_env_steps_sampled": 24000, "num_env_steps_trained": 24000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 552.9531063109439, "num_env_steps_trained_throughput_per_sec": 552.9531063109439, "timesteps_total": 24000, "num_env_steps_sampled_lifetime": 24000, "num_agent_steps_sampled_lifetime": 24000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 24000, "timers": {"training_iteration_time_ms": 7285.043, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7285.043, "sample_time_ms": 1602.583, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5663.311, "learn_throughput": 706.301, "synch_weights_time_ms": 18.814}, "counters": {"num_env_steps_sampled": 24000, "num_env_steps_trained": 24000, "num_agent_steps_sampled": 24000, "num_agent_steps_trained": 24000}, "done": false, "training_iteration": 6, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-06", "timestamp": 1736143446, "time_this_iter_s": 7.241939306259155, "time_total_s": 43.7321834564209, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 43.7321834564209, "iterations_since_restore": 6, "perf": {"cpu_util_percent": 21.763636363636365, "ram_util_percent": 85.70000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 2.219432065323476, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.22606218802211905, "policy_loss": -0.014353370563858138, "vf_loss": 0.23795091499653032, "vf_explained_var": -0.23372548414814856, "kl": 0.012323208289259343, "entropy": 0.9531376334287787, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 6045.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 28000, "num_env_steps_trained": 28000, "num_agent_steps_sampled": 28000, "num_agent_steps_trained": 28000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 28000, "num_agent_steps_trained": 28000, "num_env_steps_sampled": 28000, "num_env_steps_trained": 28000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 535.7078687885453, "num_env_steps_trained_throughput_per_sec": 535.7078687885453, "timesteps_total": 28000, "num_env_steps_sampled_lifetime": 28000, "num_agent_steps_sampled_lifetime": 28000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 28000, "timers": {"training_iteration_time_ms": 7311.002, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7311.002, "sample_time_ms": 1624.709, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5666.956, "learn_throughput": 705.846, "synch_weights_time_ms": 19.05}, "counters": {"num_env_steps_sampled": 28000, "num_env_steps_trained": 28000, "num_agent_steps_sampled": 28000, "num_agent_steps_trained": 28000}, "done": false, "training_iteration": 7, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-13", "timestamp": 1736143453, "time_this_iter_s": 7.466756105422974, "time_total_s": 51.19893956184387, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 51.19893956184387, "iterations_since_restore": 7, "perf": {"cpu_util_percent": 23.87, "ram_util_percent": 85.70000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 2.3747369998244827, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.17303292723673006, "policy_loss": -0.016354282865280748, "vf_loss": 0.18728087620729247, "vf_explained_var": -0.3321333541665026, "kl": 0.010531666346014705, "entropy": 0.9995263616243998, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 6975.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 32000, "num_env_steps_trained": 32000, "num_agent_steps_sampled": 32000, "num_agent_steps_trained": 32000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 32000, "num_agent_steps_trained": 32000, "num_env_steps_sampled": 32000, "num_env_steps_trained": 32000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 530.4835337684926, "num_env_steps_trained_throughput_per_sec": 530.4835337684926, "timesteps_total": 32000, "num_env_steps_sampled_lifetime": 32000, "num_agent_steps_sampled_lifetime": 32000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 32000, "timers": {"training_iteration_time_ms": 7339.663, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7339.663, "sample_time_ms": 1634.725, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5686.341, "learn_throughput": 703.44, "synch_weights_time_ms": 18.221}, "counters": {"num_env_steps_sampled": 32000, "num_env_steps_trained": 32000, "num_agent_steps_sampled": 32000, "num_agent_steps_trained": 32000}, "done": false, "training_iteration": 8, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-21", "timestamp": 1736143461, "time_this_iter_s": 7.540290594100952, "time_total_s": 58.739230155944824, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 58.739230155944824, "iterations_since_restore": 8, "perf": {"cpu_util_percent": 23.181818181818183, "ram_util_percent": 85.70000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 2.8604590259252056, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.2114196131525371, "policy_loss": -0.01684188744433785, "vf_loss": 0.22582534252433106, "vf_explained_var": -0.4230381830405163, "kl": 0.012180775055572246, "entropy": 0.9494166108869737, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 7905.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 36000, "num_env_steps_trained": 36000, "num_agent_steps_sampled": 36000, "num_agent_steps_trained": 36000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 36000, "num_agent_steps_trained": 36000, "num_env_steps_sampled": 36000, "num_env_steps_trained": 36000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 531.0484290497002, "num_env_steps_trained_throughput_per_sec": 531.0484290497002, "timesteps_total": 36000, "num_env_steps_sampled_lifetime": 36000, "num_agent_steps_sampled_lifetime": 36000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 36000, "timers": {"training_iteration_time_ms": 7361.064, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7361.064, "sample_time_ms": 1639.963, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5702.876, "learn_throughput": 701.4, "synch_weights_time_ms": 17.331}, "counters": {"num_env_steps_sampled": 36000, "num_env_steps_trained": 36000, "num_agent_steps_sampled": 36000, "num_agent_steps_trained": 36000}, "done": false, "training_iteration": 9, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-28", "timestamp": 1736143468, "time_this_iter_s": 7.5421717166900635, "time_total_s": 66.28140187263489, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 66.28140187263489, "iterations_since_restore": 9, "perf": {"cpu_util_percent": 22.69090909090909, "ram_util_percent": 85.70000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 2.6045479830795077, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.20251010495328134, "policy_loss": -0.015088294666781221, "vf_loss": 0.21526848068404492, "vf_explained_var": -0.42976920418841863, "kl": 0.011649599030458072, "entropy": 0.9678326273477206, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 8835.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 40000, "num_env_steps_trained": 40000, "num_agent_steps_sampled": 40000, "num_agent_steps_trained": 40000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 40000, "num_agent_steps_trained": 40000, "num_env_steps_sampled": 40000, "num_env_steps_trained": 40000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 532.2715915013911, "num_env_steps_trained_throughput_per_sec": 532.2715915013911, "timesteps_total": 40000, "num_env_steps_sampled_lifetime": 40000, "num_agent_steps_sampled_lifetime": 40000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 40000, "timers": {"training_iteration_time_ms": 7376.453, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7376.453, "sample_time_ms": 1642.176, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5716.831, "learn_throughput": 699.688, "synch_weights_time_ms": 16.643}, "counters": {"num_env_steps_sampled": 40000, "num_env_steps_trained": 40000, "num_agent_steps_sampled": 40000, "num_agent_steps_trained": 40000}, "done": false, "training_iteration": 10, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-36", "timestamp": 1736143476, "time_this_iter_s": 7.524990558624268, "time_total_s": 73.80639243125916, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 73.80639243125916, "iterations_since_restore": 10, "perf": {"cpu_util_percent": 23.310000000000002, "ram_util_percent": 85.71000000000001}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 3.684334835962903, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.19380760818284246, "policy_loss": -0.015142766013741493, "vf_loss": 0.20671594161918933, "vf_explained_var": -0.6786010124991017, "kl": 0.011172158046062109, "entropy": 0.929903652142453, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 9765.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 44000, "num_env_steps_trained": 44000, "num_agent_steps_sampled": 44000, "num_agent_steps_trained": 44000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 44000, "num_agent_steps_trained": 44000, "num_env_steps_sampled": 44000, "num_env_steps_trained": 44000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 533.5241533994642, "num_env_steps_trained_throughput_per_sec": 533.5241533994642, "timesteps_total": 44000, "num_env_steps_sampled_lifetime": 44000, "num_agent_steps_sampled_lifetime": 44000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 44000, "timers": {"training_iteration_time_ms": 7410.058, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7410.058, "sample_time_ms": 1653.129, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5739.607, "learn_throughput": 696.912, "synch_weights_time_ms": 16.517}, "counters": {"num_env_steps_sampled": 44000, "num_env_steps_trained": 44000, "num_agent_steps_sampled": 44000, "num_agent_steps_trained": 44000}, "done": false, "training_iteration": 11, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-43", "timestamp": 1736143483, "time_this_iter_s": 7.497317552566528, "time_total_s": 81.30370998382568, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 81.30370998382568, "iterations_since_restore": 11, "perf": {"cpu_util_percent": 22.036363636363635, "ram_util_percent": 85.70909090909093}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 5.18062733871642, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.34219454974443053, "policy_loss": -0.01685082538714332, "vf_loss": 0.3566932098359816, "vf_explained_var": -0.5077626787206178, "kl": 0.01176082759738903, "entropy": 0.8895634348674487, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 10695.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 48000, "num_env_steps_trained": 48000, "num_agent_steps_sampled": 48000, "num_agent_steps_trained": 48000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 48000, "num_agent_steps_trained": 48000, "num_env_steps_sampled": 48000, "num_env_steps_trained": 48000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 535.944302888296, "num_env_steps_trained_throughput_per_sec": 535.944302888296, "timesteps_total": 48000, "num_env_steps_sampled_lifetime": 48000, "num_agent_steps_sampled_lifetime": 48000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 48000, "timers": {"training_iteration_time_ms": 7425.317, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7425.317, "sample_time_ms": 1669.538, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5738.438, "learn_throughput": 697.054, "synch_weights_time_ms": 16.536}, "counters": {"num_env_steps_sampled": 48000, "num_env_steps_trained": 48000, "num_agent_steps_sampled": 48000, "num_agent_steps_trained": 48000}, "done": false, "training_iteration": 12, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-51", "timestamp": 1736143491, "time_this_iter_s": 7.473523855209351, "time_total_s": 88.77723383903503, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 88.77723383903503, "iterations_since_restore": 12, "perf": {"cpu_util_percent": 21.99090909090909, "ram_util_percent": 85.68181818181819}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 6.020282447466286, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.36640889452812914, "policy_loss": -0.016946295885148868, "vf_loss": 0.38114951581748013, "vf_explained_var": -0.5756303635976647, "kl": 0.011028353253057318, "entropy": 0.8946532066150378, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 11625.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 52000, "num_env_steps_trained": 52000, "num_agent_steps_sampled": 52000, "num_agent_steps_trained": 52000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 52000, "num_agent_steps_trained": 52000, "num_env_steps_sampled": 52000, "num_env_steps_trained": 52000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 547.2971771709978, "num_env_steps_trained_throughput_per_sec": 547.2971771709978, "timesteps_total": 52000, "num_env_steps_sampled_lifetime": 52000, "num_agent_steps_sampled_lifetime": 52000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 52000, "timers": {"training_iteration_time_ms": 7428.74, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7428.74, "sample_time_ms": 1690.774, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5720.435, "learn_throughput": 699.248, "synch_weights_time_ms": 16.928}, "counters": {"num_env_steps_sampled": 52000, "num_env_steps_trained": 52000, "num_agent_steps_sampled": 52000, "num_agent_steps_trained": 52000}, "done": false, "training_iteration": 13, "trial_id": "e4287_00000", "date": "2025-01-06_14-04-58", "timestamp": 1736143498, "time_this_iter_s": 7.308643579483032, "time_total_s": 96.08587741851807, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 96.08587741851807, "iterations_since_restore": 13, "perf": {"cpu_util_percent": 22.740000000000002, "ram_util_percent": 85.70000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 5.548517572014562, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.29237555689916955, "policy_loss": -0.015281563809001317, "vf_loss": 0.3053578447051076, "vf_explained_var": -0.38907060257850157, "kl": 0.011496373312036642, "entropy": 0.9184061447138427, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 12555.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 56000, "num_env_steps_trained": 56000, "num_agent_steps_sampled": 56000, "num_agent_steps_trained": 56000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 56000, "num_agent_steps_trained": 56000, "num_env_steps_sampled": 56000, "num_env_steps_trained": 56000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 532.8115529156012, "num_env_steps_trained_throughput_per_sec": 532.8115529156012, "timesteps_total": 56000, "num_env_steps_sampled_lifetime": 56000, "num_agent_steps_sampled_lifetime": 56000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 56000, "timers": {"training_iteration_time_ms": 7457.674, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7457.674, "sample_time_ms": 1716.061, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5722.947, "learn_throughput": 698.941, "synch_weights_time_ms": 17.517}, "counters": {"num_env_steps_sampled": 56000, "num_env_steps_trained": 56000, "num_agent_steps_sampled": 56000, "num_agent_steps_trained": 56000}, "done": false, "training_iteration": 14, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-06", "timestamp": 1736143506, "time_this_iter_s": 7.507344722747803, "time_total_s": 103.59322214126587, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 103.59322214126587, "iterations_since_restore": 14, "perf": {"cpu_util_percent": 22.927272727272726, "ram_util_percent": 85.63636363636364}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 5.86640359044716, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.37095204816069655, "policy_loss": -0.015525279302270182, "vf_loss": 0.38436642917588487, "vf_explained_var": -0.22515845157766856, "kl": 0.010554514516800275, "entropy": 0.8829271092850675, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 13485.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 60000, "num_env_steps_trained": 60000, "num_agent_steps_sampled": 60000, "num_agent_steps_trained": 60000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 60000, "num_agent_steps_trained": 60000, "num_env_steps_sampled": 60000, "num_env_steps_trained": 60000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 517.1745070806558, "num_env_steps_trained_throughput_per_sec": 517.1745070806558, "timesteps_total": 60000, "num_env_steps_sampled_lifetime": 60000, "num_agent_steps_sampled_lifetime": 60000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 60000, "timers": {"training_iteration_time_ms": 7479.926, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7479.926, "sample_time_ms": 1729.043, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5732.245, "learn_throughput": 697.807, "synch_weights_time_ms": 17.488}, "counters": {"num_env_steps_sampled": 60000, "num_env_steps_trained": 60000, "num_agent_steps_sampled": 60000, "num_agent_steps_trained": 60000}, "done": false, "training_iteration": 15, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-14", "timestamp": 1736143514, "time_this_iter_s": 7.744398355484009, "time_total_s": 111.33762049674988, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 111.33762049674988, "iterations_since_restore": 15, "perf": {"cpu_util_percent": 22.30909090909091, "ram_util_percent": 85.6909090909091}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 6.9202596467028386, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5159296101980633, "policy_loss": -0.01574913635048815, "vf_loss": 0.5294465148065399, "vf_explained_var": -0.41348177271504555, "kl": 0.011161158965631095, "entropy": 0.8261920520054397, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 14415.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 64000, "num_env_steps_trained": 64000, "num_agent_steps_sampled": 64000, "num_agent_steps_trained": 64000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 64000, "num_agent_steps_trained": 64000, "num_env_steps_sampled": 64000, "num_env_steps_trained": 64000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 508.15458422559095, "num_env_steps_trained_throughput_per_sec": 508.15458422559095, "timesteps_total": 64000, "num_env_steps_sampled_lifetime": 64000, "num_agent_steps_sampled_lifetime": 64000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 64000, "timers": {"training_iteration_time_ms": 7543.7, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7543.7, "sample_time_ms": 1733.062, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5792.242, "learn_throughput": 690.579, "synch_weights_time_ms": 17.246}, "counters": {"num_env_steps_sampled": 64000, "num_env_steps_trained": 64000, "num_agent_steps_sampled": 64000, "num_agent_steps_trained": 64000}, "done": false, "training_iteration": 16, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-21", "timestamp": 1736143521, "time_this_iter_s": 7.871620416641235, "time_total_s": 119.20924091339111, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 119.20924091339111, "iterations_since_restore": 16, "perf": {"cpu_util_percent": 23.854545454545452, "ram_util_percent": 85.86363636363636}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 6.755163150961681, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.4969829740383292, "policy_loss": -0.013992026127794738, "vf_loss": 0.5089428154235927, "vf_explained_var": -0.28224199574480774, "kl": 0.01016090625627222, "entropy": 0.9018759287172748, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 15345.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 68000, "num_env_steps_trained": 68000, "num_agent_steps_sampled": 68000, "num_agent_steps_trained": 68000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 68000, "num_agent_steps_trained": 68000, "num_env_steps_sampled": 68000, "num_env_steps_trained": 68000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 507.48433721499254, "num_env_steps_trained_throughput_per_sec": 507.48433721499254, "timesteps_total": 68000, "num_env_steps_sampled_lifetime": 68000, "num_agent_steps_sampled_lifetime": 68000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 68000, "timers": {"training_iteration_time_ms": 7585.226, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7585.226, "sample_time_ms": 1733.792, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5833.579, "learn_throughput": 685.685, "synch_weights_time_ms": 16.53}, "counters": {"num_env_steps_sampled": 68000, "num_env_steps_trained": 68000, "num_agent_steps_sampled": 68000, "num_agent_steps_trained": 68000}, "done": false, "training_iteration": 17, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-29", "timestamp": 1736143529, "time_this_iter_s": 7.882016658782959, "time_total_s": 127.09125757217407, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 127.09125757217407, "iterations_since_restore": 17, "perf": {"cpu_util_percent": 24.245454545454546, "ram_util_percent": 86.0}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 7.0149579149420545, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.4758745718687292, "policy_loss": -0.016639545596935736, "vf_loss": 0.49051574562320743, "vf_explained_var": -0.48457250005455427, "kl": 0.009991844118921093, "entropy": 0.8411492871340885, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 16275.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 72000, "num_env_steps_trained": 72000, "num_agent_steps_sampled": 72000, "num_agent_steps_trained": 72000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 72000, "num_agent_steps_trained": 72000, "num_env_steps_sampled": 72000, "num_env_steps_trained": 72000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 520.9071012940915, "num_env_steps_trained_throughput_per_sec": 520.9071012940915, "timesteps_total": 72000, "num_env_steps_sampled_lifetime": 72000, "num_agent_steps_sampled_lifetime": 72000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 72000, "timers": {"training_iteration_time_ms": 7599.088, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7599.088, "sample_time_ms": 1742.258, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5839.465, "learn_throughput": 684.994, "synch_weights_time_ms": 16.141}, "counters": {"num_env_steps_sampled": 72000, "num_env_steps_trained": 72000, "num_agent_steps_sampled": 72000, "num_agent_steps_trained": 72000}, "done": false, "training_iteration": 18, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-37", "timestamp": 1736143537, "time_this_iter_s": 7.689068555831909, "time_total_s": 134.78032612800598, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 134.78032612800598, "iterations_since_restore": 18, "perf": {"cpu_util_percent": 23.22727272727273, "ram_util_percent": 86.03636363636363}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 7.9299763799034135, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.49573158032951814, "policy_loss": -0.015982136109052726, "vf_loss": 0.5097424978290194, "vf_explained_var": -0.25540143347555594, "kl": 0.009856078924517484, "entropy": 0.8415445823182341, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 17205.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 76000, "num_env_steps_trained": 76000, "num_agent_steps_sampled": 76000, "num_agent_steps_trained": 76000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 76000, "num_agent_steps_trained": 76000, "num_env_steps_sampled": 76000, "num_env_steps_trained": 76000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 523.0667372435496, "num_env_steps_trained_throughput_per_sec": 523.0667372435496, "timesteps_total": 76000, "num_env_steps_sampled_lifetime": 76000, "num_agent_steps_sampled_lifetime": 76000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 76000, "timers": {"training_iteration_time_ms": 7610.582, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7610.582, "sample_time_ms": 1758.353, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5835.177, "learn_throughput": 685.498, "synch_weights_time_ms": 16.13}, "counters": {"num_env_steps_sampled": 76000, "num_env_steps_trained": 76000, "num_agent_steps_sampled": 76000, "num_agent_steps_trained": 76000}, "done": false, "training_iteration": 19, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-45", "timestamp": 1736143545, "time_this_iter_s": 7.657677173614502, "time_total_s": 142.43800330162048, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 142.43800330162048, "iterations_since_restore": 19, "perf": {"cpu_util_percent": 22.11818181818182, "ram_util_percent": 86.00909090909092}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 7.683058810971117, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5359118831574275, "policy_loss": -0.015116516348495278, "vf_loss": 0.5492945586231046, "vf_explained_var": -0.3751542590638643, "kl": 0.008669191332230547, "entropy": 0.8119061539250035, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 18135.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 80000, "num_env_steps_trained": 80000, "num_agent_steps_sampled": 80000, "num_agent_steps_trained": 80000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 80000, "num_agent_steps_trained": 80000, "num_env_steps_sampled": 80000, "num_env_steps_trained": 80000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 536.8860478914621, "num_env_steps_trained_throughput_per_sec": 536.8860478914621, "timesteps_total": 80000, "num_env_steps_sampled_lifetime": 80000, "num_agent_steps_sampled_lifetime": 80000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 80000, "timers": {"training_iteration_time_ms": 7604.123, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7604.123, "sample_time_ms": 1778.637, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5807.429, "learn_throughput": 688.773, "synch_weights_time_ms": 17.135}, "counters": {"num_env_steps_sampled": 80000, "num_env_steps_trained": 80000, "num_agent_steps_sampled": 80000, "num_agent_steps_trained": 80000}, "done": false, "training_iteration": 20, "trial_id": "e4287_00000", "date": "2025-01-06_14-05-52", "timestamp": 1736143552, "time_this_iter_s": 7.45037055015564, "time_total_s": 149.88837385177612, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 149.88837385177612, "iterations_since_restore": 20, "perf": {"cpu_util_percent": 22.99, "ram_util_percent": 86.03999999999999}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 6.450447400459038, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5112401626203009, "policy_loss": -0.01091031023571568, "vf_loss": 0.5204404476225146, "vf_explained_var": -0.4802583747012641, "kl": 0.00855012842764407, "entropy": 0.8565182878766009, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 19065.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 84000, "num_env_steps_trained": 84000, "num_agent_steps_sampled": 84000, "num_agent_steps_trained": 84000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 84000, "num_agent_steps_trained": 84000, "num_env_steps_sampled": 84000, "num_env_steps_trained": 84000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 537.3807444579741, "num_env_steps_trained_throughput_per_sec": 537.3807444579741, "timesteps_total": 84000, "num_env_steps_sampled_lifetime": 84000, "num_agent_steps_sampled_lifetime": 84000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 84000, "timers": {"training_iteration_time_ms": 7598.742, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7598.742, "sample_time_ms": 1797.803, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5783.135, "learn_throughput": 691.666, "synch_weights_time_ms": 16.882}, "counters": {"num_env_steps_sampled": 84000, "num_env_steps_trained": 84000, "num_agent_steps_sampled": 84000, "num_agent_steps_trained": 84000}, "done": false, "training_iteration": 21, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-00", "timestamp": 1736143560, "time_this_iter_s": 7.452832221984863, "time_total_s": 157.341206073761, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 157.341206073761, "iterations_since_restore": 21, "perf": {"cpu_util_percent": 21.99090909090909, "ram_util_percent": 86.02727272727273}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 6.7655341186190165, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.4412352555241155, "policy_loss": -0.01648683491092856, "vf_loss": 0.45531796627047083, "vf_explained_var": -0.2452198738051999, "kl": 0.012020609698315289, "entropy": 0.8348502535973826, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 19995.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 88000, "num_env_steps_trained": 88000, "num_agent_steps_sampled": 88000, "num_agent_steps_trained": 88000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 88000, "num_agent_steps_trained": 88000, "num_env_steps_sampled": 88000, "num_env_steps_trained": 88000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 521.0370220656354, "num_env_steps_trained_throughput_per_sec": 521.0370220656354, "timesteps_total": 88000, "num_env_steps_sampled_lifetime": 88000, "num_agent_steps_sampled_lifetime": 88000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 88000, "timers": {"training_iteration_time_ms": 7620.096, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7620.096, "sample_time_ms": 1813.515, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5787.819, "learn_throughput": 691.107, "synch_weights_time_ms": 17.085}, "counters": {"num_env_steps_sampled": 88000, "num_env_steps_trained": 88000, "num_agent_steps_sampled": 88000, "num_agent_steps_trained": 88000}, "done": false, "training_iteration": 22, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-07", "timestamp": 1736143567, "time_this_iter_s": 7.676997661590576, "time_total_s": 165.01820373535156, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 165.01820373535156, "iterations_since_restore": 22, "perf": {"cpu_util_percent": 23.981818181818184, "ram_util_percent": 86.00909090909092}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 7.752490236674265, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.48460133601635974, "policy_loss": -0.013652383788458762, "vf_loss": 0.49610064348058214, "vf_explained_var": -0.11372297579242337, "kl": 0.010765368167404291, "entropy": 0.7681761355810268, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 20925.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 92000, "num_env_steps_trained": 92000, "num_agent_steps_sampled": 92000, "num_agent_steps_trained": 92000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 92000, "num_agent_steps_trained": 92000, "num_env_steps_sampled": 92000, "num_env_steps_trained": 92000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 518.14103384498, "num_env_steps_trained_throughput_per_sec": 518.14103384498, "timesteps_total": 92000, "num_env_steps_sampled_lifetime": 92000, "num_agent_steps_sampled_lifetime": 92000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 92000, "timers": {"training_iteration_time_ms": 7661.222, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7661.222, "sample_time_ms": 1820.679, "load_time_ms": 0.547, "load_throughput": 7317029.09, "learn_time_ms": 5821.566, "learn_throughput": 687.1, "synch_weights_time_ms": 17.301}, "counters": {"num_env_steps_sampled": 92000, "num_env_steps_trained": 92000, "num_agent_steps_sampled": 92000, "num_agent_steps_trained": 92000}, "done": false, "training_iteration": 23, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-15", "timestamp": 1736143575, "time_this_iter_s": 7.719905853271484, "time_total_s": 172.73810958862305, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 172.73810958862305, "iterations_since_restore": 23, "perf": {"cpu_util_percent": 23.263636363636362, "ram_util_percent": 86.0}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 8.063436018899884, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5171211144268533, "policy_loss": -0.013122556262439296, "vf_loss": 0.5284050673082714, "vf_explained_var": -0.3422953128173787, "kl": 0.009193043740915606, "entropy": 0.7644946483514642, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 21855.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 96000, "num_env_steps_trained": 96000, "num_agent_steps_sampled": 96000, "num_agent_steps_trained": 96000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 96000, "num_agent_steps_trained": 96000, "num_env_steps_sampled": 96000, "num_env_steps_trained": 96000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 514.3858316229006, "num_env_steps_trained_throughput_per_sec": 514.3858316229006, "timesteps_total": 96000, "num_env_steps_sampled_lifetime": 96000, "num_agent_steps_sampled_lifetime": 96000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 96000, "timers": {"training_iteration_time_ms": 7688.114, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7688.114, "sample_time_ms": 1827.913, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5842.092, "learn_throughput": 684.686, "synch_weights_time_ms": 16.979}, "counters": {"num_env_steps_sampled": 96000, "num_env_steps_trained": 96000, "num_agent_steps_sampled": 96000, "num_agent_steps_trained": 96000}, "done": false, "training_iteration": 24, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-23", "timestamp": 1736143583, "time_this_iter_s": 7.779742240905762, "time_total_s": 180.5178518295288, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 180.5178518295288, "iterations_since_restore": 24, "perf": {"cpu_util_percent": 23.61818181818182, "ram_util_percent": 86.0}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 7.125686622675388, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5285396081405461, "policy_loss": -0.017521815298385517, "vf_loss": 0.5438429162360012, "vf_explained_var": -0.4123239682566735, "kl": 0.011092566724540032, "entropy": 0.7496766250620606, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 22785.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 100000, "num_env_steps_trained": 100000, "num_agent_steps_sampled": 100000, "num_agent_steps_trained": 100000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 100000, "num_agent_steps_trained": 100000, "num_env_steps_sampled": 100000, "num_env_steps_trained": 100000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 505.6053148117275, "num_env_steps_trained_throughput_per_sec": 505.6053148117275, "timesteps_total": 100000, "num_env_steps_sampled_lifetime": 100000, "num_agent_steps_sampled_lifetime": 100000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 100000, "timers": {"training_iteration_time_ms": 7705.812, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7705.812, "sample_time_ms": 1839.975, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5847.874, "learn_throughput": 684.009, "synch_weights_time_ms": 16.833}, "counters": {"num_env_steps_sampled": 100000, "num_env_steps_trained": 100000, "num_agent_steps_sampled": 100000, "num_agent_steps_trained": 100000}, "done": false, "training_iteration": 25, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-31", "timestamp": 1736143591, "time_this_iter_s": 7.911309242248535, "time_total_s": 188.42916107177734, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 188.42916107177734, "iterations_since_restore": 25, "perf": {"cpu_util_percent": 23.427272727272726, "ram_util_percent": 85.91818181818182}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 9.810824249957198, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6138946850643924, "policy_loss": -0.012128716939559548, "vf_loss": 0.6240283395928551, "vf_explained_var": -0.36567103683307606, "kl": 0.00997533069170967, "entropy": 0.748875113712844, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 23715.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 104000, "num_env_steps_trained": 104000, "num_agent_steps_sampled": 104000, "num_agent_steps_trained": 104000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 104000, "num_agent_steps_trained": 104000, "num_env_steps_sampled": 104000, "num_env_steps_trained": 104000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 512.5895687333982, "num_env_steps_trained_throughput_per_sec": 512.5895687333982, "timesteps_total": 104000, "num_env_steps_sampled_lifetime": 104000, "num_agent_steps_sampled_lifetime": 104000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 104000, "timers": {"training_iteration_time_ms": 7699.001, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7699.001, "sample_time_ms": 1851.425, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5830.772, "learn_throughput": 686.015, "synch_weights_time_ms": 15.674}, "counters": {"num_env_steps_sampled": 104000, "num_env_steps_trained": 104000, "num_agent_steps_sampled": 104000, "num_agent_steps_trained": 104000}, "done": false, "training_iteration": 26, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-39", "timestamp": 1736143599, "time_this_iter_s": 7.813574552536011, "time_total_s": 196.24273562431335, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 196.24273562431335, "iterations_since_restore": 26, "perf": {"cpu_util_percent": 23.654545454545453, "ram_util_percent": 85.92727272727272}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 9.856198130275613, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6128068432377111, "policy_loss": -0.016789470348627336, "vf_loss": 0.6272653700467149, "vf_explained_var": -0.5621718078531245, "kl": 0.011654725659090374, "entropy": 0.7875966782851885, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 24645.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 108000, "num_env_steps_trained": 108000, "num_agent_steps_sampled": 108000, "num_agent_steps_trained": 108000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 108000, "num_agent_steps_trained": 108000, "num_env_steps_sampled": 108000, "num_env_steps_trained": 108000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 494.3542915010344, "num_env_steps_trained_throughput_per_sec": 494.3542915010344, "timesteps_total": 108000, "num_env_steps_sampled_lifetime": 108000, "num_agent_steps_sampled_lifetime": 108000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 108000, "timers": {"training_iteration_time_ms": 7719.936, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7719.936, "sample_time_ms": 1888.747, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5814.884, "learn_throughput": 687.89, "synch_weights_time_ms": 15.349}, "counters": {"num_env_steps_sampled": 108000, "num_env_steps_trained": 108000, "num_agent_steps_sampled": 108000, "num_agent_steps_trained": 108000}, "done": false, "training_iteration": 27, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-47", "timestamp": 1736143607, "time_this_iter_s": 8.102252006530762, "time_total_s": 204.34498763084412, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 204.34498763084412, "iterations_since_restore": 27, "perf": {"cpu_util_percent": 23.033333333333335, "ram_util_percent": 85.96666666666665}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 10.485675578091735, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6042991482667746, "policy_loss": -0.018017572124478638, "vf_loss": 0.6203259574229358, "vf_explained_var": -0.5447322709586031, "kl": 0.009953820093878426, "entropy": 0.756317529691163, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 25575.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 112000, "num_env_steps_trained": 112000, "num_agent_steps_sampled": 112000, "num_agent_steps_trained": 112000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 112000, "num_agent_steps_trained": 112000, "num_env_steps_sampled": 112000, "num_env_steps_trained": 112000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 449.3221755462025, "num_env_steps_trained_throughput_per_sec": 449.3221755462025, "timesteps_total": 112000, "num_env_steps_sampled_lifetime": 112000, "num_agent_steps_sampled_lifetime": 112000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 112000, "timers": {"training_iteration_time_ms": 7842.274, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7842.274, "sample_time_ms": 1956.705, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5869.298, "learn_throughput": 681.512, "synch_weights_time_ms": 15.316}, "counters": {"num_env_steps_sampled": 112000, "num_env_steps_trained": 112000, "num_agent_steps_sampled": 112000, "num_agent_steps_trained": 112000}, "done": false, "training_iteration": 28, "trial_id": "e4287_00000", "date": "2025-01-06_14-06-56", "timestamp": 1736143616, "time_this_iter_s": 8.912665843963623, "time_total_s": 213.25765347480774, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 213.25765347480774, "iterations_since_restore": 28, "perf": {"cpu_util_percent": 27.150000000000006, "ram_util_percent": 85.81666666666666}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 10.082525685397528, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6081182269349454, "policy_loss": -0.017538263760907676, "vf_loss": 0.6237384639279817, "vf_explained_var": -0.2961467348119264, "kl": 0.009590146960586172, "entropy": 0.707458258059717, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 26505.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 116000, "num_env_steps_trained": 116000, "num_agent_steps_sampled": 116000, "num_agent_steps_trained": 116000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 116000, "num_agent_steps_trained": 116000, "num_env_steps_sampled": 116000, "num_env_steps_trained": 116000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 464.6553904690455, "num_env_steps_trained_throughput_per_sec": 464.6553904690455, "timesteps_total": 116000, "num_env_steps_sampled_lifetime": 116000, "num_agent_steps_sampled_lifetime": 116000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 116000, "timers": {"training_iteration_time_ms": 7938.406, "restore_workers_time_ms": 0.0, "training_step_time_ms": 7938.406, "sample_time_ms": 2024.294, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5897.829, "learn_throughput": 678.216, "synch_weights_time_ms": 15.528}, "counters": {"num_env_steps_sampled": 116000, "num_env_steps_trained": 116000, "num_agent_steps_sampled": 116000, "num_agent_steps_trained": 116000}, "done": false, "training_iteration": 29, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-04", "timestamp": 1736143624, "time_this_iter_s": 8.608530282974243, "time_total_s": 221.86618375778198, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 221.86618375778198, "iterations_since_restore": 29, "perf": {"cpu_util_percent": 25.966666666666665, "ram_util_percent": 85.925}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 9.763846765923244, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6602525447819492, "policy_loss": -0.014539009853396364, "vf_loss": 0.6728638142137037, "vf_explained_var": -0.361080410275408, "kl": 0.009638699161488032, "entropy": 0.7699939802769692, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 27435.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 120000, "num_env_steps_trained": 120000, "num_agent_steps_sampled": 120000, "num_agent_steps_trained": 120000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 120000, "num_agent_steps_trained": 120000, "num_env_steps_sampled": 120000, "num_env_steps_trained": 120000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 461.8235886883663, "num_env_steps_trained_throughput_per_sec": 461.8235886883663, "timesteps_total": 120000, "num_env_steps_sampled_lifetime": 120000, "num_agent_steps_sampled_lifetime": 120000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 120000, "timers": {"training_iteration_time_ms": 8059.501, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8059.501, "sample_time_ms": 2093.688, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5949.553, "learn_throughput": 672.319, "synch_weights_time_ms": 15.505}, "counters": {"num_env_steps_sampled": 120000, "num_env_steps_trained": 120000, "num_agent_steps_sampled": 120000, "num_agent_steps_trained": 120000}, "done": false, "training_iteration": 30, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-13", "timestamp": 1736143633, "time_this_iter_s": 8.66131591796875, "time_total_s": 230.52749967575073, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 230.52749967575073, "iterations_since_restore": 30, "perf": {"cpu_util_percent": 25.200000000000003, "ram_util_percent": 85.93846153846154}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 10.464631977388935, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6353408778026219, "policy_loss": -0.012637091884689946, "vf_loss": 0.6459744034119711, "vf_explained_var": -0.29667423085499833, "kl": 0.01001783899173212, "entropy": 0.8007720459532994, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 28365.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 124000, "num_env_steps_trained": 124000, "num_agent_steps_sampled": 124000, "num_agent_steps_trained": 124000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 124000, "num_agent_steps_trained": 124000, "num_env_steps_sampled": 124000, "num_env_steps_trained": 124000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 461.12050041910385, "num_env_steps_trained_throughput_per_sec": 461.12050041910385, "timesteps_total": 124000, "num_env_steps_sampled_lifetime": 124000, "num_agent_steps_sampled_lifetime": 124000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 124000, "timers": {"training_iteration_time_ms": 8182.602, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8182.602, "sample_time_ms": 2162.848, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 6003.143, "learn_throughput": 666.318, "synch_weights_time_ms": 15.857}, "counters": {"num_env_steps_sampled": 124000, "num_env_steps_trained": 124000, "num_agent_steps_sampled": 124000, "num_agent_steps_trained": 124000}, "done": false, "training_iteration": 31, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-22", "timestamp": 1736143642, "time_this_iter_s": 8.674522161483765, "time_total_s": 239.2020218372345, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 239.2020218372345, "iterations_since_restore": 31, "perf": {"cpu_util_percent": 26.28333333333333, "ram_util_percent": 85.93333333333334}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 10.446903902099978, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.6468950018314245, "policy_loss": -0.01187477840351001, "vf_loss": 0.6566404772064303, "vf_explained_var": -0.4917933463409383, "kl": 0.01064649788214621, "entropy": 0.7871140724869184, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 29295.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 128000, "num_env_steps_trained": 128000, "num_agent_steps_sampled": 128000, "num_agent_steps_trained": 128000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 128000, "num_agent_steps_trained": 128000, "num_env_steps_sampled": 128000, "num_env_steps_trained": 128000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 457.6113393849883, "num_env_steps_trained_throughput_per_sec": 457.6113393849883, "timesteps_total": 128000, "num_env_steps_sampled_lifetime": 128000, "num_agent_steps_sampled_lifetime": 128000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 128000, "timers": {"training_iteration_time_ms": 8289.006, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8289.006, "sample_time_ms": 2232.94, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 6040.495, "learn_throughput": 662.197, "synch_weights_time_ms": 15.37}, "counters": {"num_env_steps_sampled": 128000, "num_env_steps_trained": 128000, "num_agent_steps_sampled": 128000, "num_agent_steps_trained": 128000}, "done": false, "training_iteration": 32, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-30", "timestamp": 1736143650, "time_this_iter_s": 8.745145797729492, "time_total_s": 247.947167634964, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 247.947167634964, "iterations_since_restore": 32, "perf": {"cpu_util_percent": 26.683333333333337, "ram_util_percent": 85.83333333333331}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 9.587770612778202, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5904561602139986, "policy_loss": -0.01410878428448272, "vf_loss": 0.6028643254831594, "vf_explained_var": -0.37473509785949544, "kl": 0.008503099148536773, "entropy": 0.6628890771058298, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 30225.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 132000, "num_env_steps_trained": 132000, "num_agent_steps_sampled": 132000, "num_agent_steps_trained": 132000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 132000, "num_agent_steps_trained": 132000, "num_env_steps_sampled": 132000, "num_env_steps_trained": 132000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 466.34237984764195, "num_env_steps_trained_throughput_per_sec": 466.34237984764195, "timesteps_total": 132000, "num_env_steps_sampled_lifetime": 132000, "num_agent_steps_sampled_lifetime": 132000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 132000, "timers": {"training_iteration_time_ms": 8374.755, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8374.755, "sample_time_ms": 2309.79, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 6049.37, "learn_throughput": 661.226, "synch_weights_time_ms": 15.393}, "counters": {"num_env_steps_sampled": 132000, "num_env_steps_trained": 132000, "num_agent_steps_sampled": 132000, "num_agent_steps_trained": 132000}, "done": false, "training_iteration": 33, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-39", "timestamp": 1736143659, "time_this_iter_s": 8.577389001846313, "time_total_s": 256.5245566368103, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 256.5245566368103, "iterations_since_restore": 33, "perf": {"cpu_util_percent": 25.06153846153846, "ram_util_percent": 85.81538461538459}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 11.605336008661537, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.5784026077257529, "policy_loss": -0.013089533466645466, "vf_loss": 0.5896243097005232, "vf_explained_var": -0.11577509372465072, "kl": 0.00933916152267772, "entropy": 0.5845601645849084, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 31155.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 136000, "num_env_steps_trained": 136000, "num_agent_steps_sampled": 136000, "num_agent_steps_trained": 136000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 136000, "num_agent_steps_trained": 136000, "num_env_steps_sampled": 136000, "num_env_steps_trained": 136000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 467.796160830694, "num_env_steps_trained_throughput_per_sec": 467.796160830694, "timesteps_total": 136000, "num_env_steps_sampled_lifetime": 136000, "num_agent_steps_sampled_lifetime": 136000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 136000, "timers": {"training_iteration_time_ms": 8452.202, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8452.202, "sample_time_ms": 2388.104, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 6049.175, "learn_throughput": 661.247, "synch_weights_time_ms": 14.723}, "counters": {"num_env_steps_sampled": 136000, "num_env_steps_trained": 136000, "num_agent_steps_sampled": 136000, "num_agent_steps_trained": 136000}, "done": false, "training_iteration": 34, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-48", "timestamp": 1736143668, "time_this_iter_s": 8.563373327255249, "time_total_s": 265.08792996406555, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 265.08792996406555, "iterations_since_restore": 34, "perf": {"cpu_util_percent": 27.383333333333336, "ram_util_percent": 85.78333333333332}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 10.050650530829225, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.4366872190535369, "policy_loss": -0.010669274447906402, "vf_loss": 0.44519513358777607, "vf_explained_var": -0.6139007148563221, "kl": 0.010806770797491042, "entropy": 0.7044626908276671, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 32085.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 140000, "num_env_steps_trained": 140000, "num_agent_steps_sampled": 140000, "num_agent_steps_trained": 140000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 140000, "num_agent_steps_trained": 140000, "num_env_steps_sampled": 140000, "num_env_steps_trained": 140000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 466.93409155825924, "num_env_steps_trained_throughput_per_sec": 466.93409155825924, "timesteps_total": 140000, "num_env_steps_sampled_lifetime": 140000, "num_agent_steps_sampled_lifetime": 140000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 140000, "timers": {"training_iteration_time_ms": 8517.723, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8517.723, "sample_time_ms": 2479.114, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 6024.554, "learn_throughput": 663.95, "synch_weights_time_ms": 13.854}, "counters": {"num_env_steps_sampled": 140000, "num_env_steps_trained": 140000, "num_agent_steps_sampled": 140000, "num_agent_steps_trained": 140000}, "done": false, "training_iteration": 35, "trial_id": "e4287_00000", "date": "2025-01-06_14-07-56", "timestamp": 1736143676, "time_this_iter_s": 8.576640844345093, "time_total_s": 273.66457080841064, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 273.66457080841064, "iterations_since_restore": 35, "perf": {"cpu_util_percent": 26.466666666666665, "ram_util_percent": 85.7}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 10.101509887300512, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.4260380117808499, "policy_loss": -0.015005901119401378, "vf_loss": 0.4388261238633785, "vf_explained_var": -0.5089550865593777, "kl": 0.011088958450787487, "entropy": 0.6941911176968646, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 33015.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 144000, "num_env_steps_trained": 144000, "num_agent_steps_sampled": 144000, "num_agent_steps_trained": 144000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 144000, "num_agent_steps_trained": 144000, "num_env_steps_sampled": 144000, "num_env_steps_trained": 144000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 475.4893861263636, "num_env_steps_trained_throughput_per_sec": 475.4893861263636, "timesteps_total": 144000, "num_env_steps_sampled_lifetime": 144000, "num_agent_steps_sampled_lifetime": 144000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 144000, "timers": {"training_iteration_time_ms": 8578.61, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8578.61, "sample_time_ms": 2571.361, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5992.615, "learn_throughput": 667.488, "synch_weights_time_ms": 14.433}, "counters": {"num_env_steps_sampled": 144000, "num_env_steps_trained": 144000, "num_agent_steps_sampled": 144000, "num_agent_steps_trained": 144000}, "done": false, "training_iteration": 36, "trial_id": "e4287_00000", "date": "2025-01-06_14-08-05", "timestamp": 1736143685, "time_this_iter_s": 8.41580843925476, "time_total_s": 282.0803792476654, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 282.0803792476654, "iterations_since_restore": 36, "perf": {"cpu_util_percent": 27.10833333333333, "ram_util_percent": 85.65000000000002}}
{"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": 11.176471208628788, "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.00010000000000000003, "total_loss": 0.4203817323611308, "policy_loss": -0.006460704181783942, "vf_loss": 0.4253196742565882, "vf_explained_var": -0.3829774095807024, "kl": 0.007613794697424241, "entropy": 0.6914780310084743, "entropy_coeff": 0.0}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 33945.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 148000, "num_env_steps_trained": 148000, "num_agent_steps_sampled": 148000, "num_agent_steps_trained": 148000}, "env_runners": {"episode_reward_max": NaN, "episode_reward_min": NaN, "episode_reward_mean": NaN, "episode_len_mean": NaN, "episode_media": {}, "episodes_timesteps_total": 0, "policy_reward_min": {}, "policy_reward_max": {}, "policy_reward_mean": {}, "custom_metrics": {}, "hist_stats": {"episode_reward": [], "episode_lengths": []}, "sampler_perf": {}, "num_faulty_episodes": 0, "connector_metrics": {}, "num_episodes": 0, "episode_return_max": NaN, "episode_return_min": NaN, "episode_return_mean": NaN, "episodes_this_iter": 0}, "num_healthy_workers": 8, "num_in_flight_async_sample_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 148000, "num_agent_steps_trained": 148000, "num_env_steps_sampled": 148000, "num_env_steps_trained": 148000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 427.71819931146337, "num_env_steps_trained_throughput_per_sec": 427.71819931146337, "timesteps_total": 148000, "num_env_steps_sampled_lifetime": 148000, "num_agent_steps_sampled_lifetime": 148000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 148000, "timers": {"training_iteration_time_ms": 8704.669, "restore_workers_time_ms": 0.0, "training_step_time_ms": 8704.669, "sample_time_ms": 2638.36, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 6051.835, "learn_throughput": 660.957, "synch_weights_time_ms": 14.273}, "counters": {"num_env_steps_sampled": 148000, "num_env_steps_trained": 148000, "num_agent_steps_sampled": 148000, "num_agent_steps_trained": 148000}, "done": false, "training_iteration": 37, "trial_id": "e4287_00000", "date": "2025-01-06_14-08-14", "timestamp": 1736143694, "time_this_iter_s": 9.362024784088135, "time_total_s": 291.44240403175354, "pid": 32464, "hostname": "Matebook-white", "node_ip": "127.0.0.1", "config": {"extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "env": "markettiming_env", "env_config": {"name": "MarketTimingEnv", "version": "v1", "initial_amount": 10000000, "gamma": 0.98, "mode": "train", "split_percent": 0.9, "data_path": "E:/lab/RoboQuant/pylab/data", "data_file": "ft_all.all.00170516142453003.csv"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "env_task_fn": null, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "max_requests_in_flight_per_env_runner": 2, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "episode_lookback_horizon": 1, "rollout_fragment_length": "auto", "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": 1, "local_gpu_idx": 0, "gamma": 0.99, "lr": 0.0001, "grad_clip": null, "grad_clip_by": "global_norm", "train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": null, "custom_model_config": {}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {"type": "SGD", "lr": 0.01, "momentum": 0.9}, "_learner_class": null, "explore": true, "exploration_config": {"type": "StochasticSampling"}, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x000001533C8F8EA0>", "policies_to_train": null, "policy_states_are_swappable": false, "observation_fn": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": null, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "WARN", "log_sys_usage": true, "fake_sampler": false, "seed": null, "_run_training_always_in_thread": false, "_evaluation_parallel_to_training_wo_thread": false, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "_AlgorithmConfig__prior_exploration_config": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": -1, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "lr_schedule": null, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 1.0, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "clip_param": 0.3, "vf_clip_param": 10.0, "sgd_minibatch_size": -1, "vf_share_layers": -1, "__stdout_file__": null, "__stderr_file__": null, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": "<class '__main__.CrashAfterNIters'>", "create_env_on_driver": true, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 291.44240403175354, "iterations_since_restore": 37, "perf": {"cpu_util_percent": 29.507692307692306, "ram_util_percent": 85.84615384615384}}
