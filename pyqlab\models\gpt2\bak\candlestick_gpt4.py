"""
CandlestickGPT4 模型

基于GPT-4架构的K线预测模型，针对金融时间序列数据优化。
主要特点：
1. 使用RMSNorm替代LayerNorm，提高训练稳定性
2. 实现旋转位置编码(RoPE)，更好地处理序列位置信息
3. 支持多种时间特征编码方法
4. 使用现代化残差连接
5. 优化的权重初始化
6. 支持标签平滑和辅助损失函数
7. 实现自适应学习率调整
"""

import math
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Any, Callable

class RMSNorm(nn.Module):
    """
    RMSNorm层
    使用均方根归一化进行层归一化，比传统LayerNorm更稳定。
    """
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        return self._norm(x) * self.weight

class RotaryEmbedding(nn.Module):
    """
    RoPE（Rotary Position Embedding）
    使用旋转位置嵌入进行位置编码，更好地处理序列位置信息。
    """
    def __init__(self, dim, max_position_embeddings=2048, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(self.max_seq_len_cached, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x, seq_len=None):
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len
            t = torch.arange(self.max_seq_len_cached, device=x.device).type_as(self.inv_freq)
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)
            self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
            self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

def rotate_half(x):
    """将输入向量的后半部分旋转到前半部分"""
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def apply_rotary_pos_emb(q, k, cos, sin, offset=0):
    """应用旋转位置嵌入到查询和键向量"""
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed

class MLP(nn.Module):
    """
    MLP（Multi-Layer Perceptron）
    包含两个全连接层和GELU激活函数。
    """
    def __init__(self, dim, hidden_dim, dropout=0.1):
        super().__init__()
        self.c_fc = nn.Linear(dim, hidden_dim, bias=False)
        self.c_proj = nn.Linear(hidden_dim, dim, bias=False)
        self.act = nn.GELU()
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        return self.dropout(self.c_proj(self.act(self.c_fc(x))))

class Attention(nn.Module):
    """
    注意力机制
    支持RoPE位置嵌入。
    """
    def __init__(self, dim, n_heads, dropout=0.1):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        assert self.head_dim * n_heads == dim, "维度必须能被头数整除"

        self.wq = nn.Linear(dim, dim, bias=False)
        self.wk = nn.Linear(dim, dim, bias=False)
        self.wv = nn.Linear(dim, dim, bias=False)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.proj = nn.Linear(dim, dim, bias=False)

        # 检查是否支持Flash Attention
        self.flash = hasattr(torch.nn.functional, 'scaled_dot_product_attention')
        if not self.flash:
            print("警告: 使用较慢的注意力机制。Flash Attention需要PyTorch >= 2.0")

    def forward(self, x, rotary_emb=None):
        B, T, C = x.size() # 批大小，序列长度，嵌入维度

        # 计算query, key, values
        q, k, v = self.wq(x), self.wk(x), self.wv(x)
        q = q.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2) # (B, nh, T, hs)
        k = k.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2) # (B, nh, T, hs)
        v = v.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2) # (B, nh, T, hs)

        # 应用旋转位置嵌入
        if rotary_emb is not None:
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)

        # 注意力计算
        if self.flash:
            # 使用Flash Attention
            y = torch.nn.functional.scaled_dot_product_attention(
                q, k, v, attn_mask=None, dropout_p=self.attn_dropout.p if self.training else 0, is_causal=True
            )
        else:
            # 手动实现注意力
            att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
            att = att.masked_fill(torch.triu(torch.ones(T, T, device=x.device), diagonal=1).bool().unsqueeze(0).unsqueeze(0), float('-inf'))
            att = F.softmax(att, dim=-1)
            att = self.attn_dropout(att)
            y = att @ v # (B, nh, T, T) x (B, nh, T, hs) -> (B, nh, T, hs)

        y = y.transpose(1, 2).contiguous().view(B, T, C) # 重组所有头的输出
        y = self.resid_dropout(self.proj(y))
        return y

class Block(nn.Module):
    """
    Transformer Block
    包含RMSNorm、注意力机制和MLP。
    """
    def __init__(self, dim, n_heads, dropout=0.1):
        super().__init__()
        self.ln_1 = RMSNorm(dim)
        self.attn = Attention(dim, n_heads, dropout)
        self.ln_2 = RMSNorm(dim)
        self.mlp = MLP(dim, 4 * dim, dropout)

    def forward(self, x, rotary_emb=None):
        x = x + self.attn(self.ln_1(x), rotary_emb)
        x = x + self.mlp(self.ln_2(x))
        return x

class TimeFeatureEmbedding(nn.Module):
    """
    时间特征嵌入
    将时间特征转换为嵌入向量。
    """
    def __init__(self, d_model, n_time_features=5):
        super().__init__()
        self.time_proj = nn.Linear(n_time_features, d_model)

    def forward(self, time_features):
        """
        Args:
            time_features: [batch_size, seq_len, n_time_features]
        """
        return self.time_proj(time_features)

class CandlestickGPT4(nn.Module):
    """
    CandlestickGPT4模型
    基于GPT-4架构的K线预测模型，针对金融时间序列数据优化。
    """
    def __init__(self,
                 vocab_size: int,
                 code_size: int,
                 block_size: int = 64,
                 n_layer: int = 8,
                 n_head: int = 8,
                 d_model: int = 128,
                 dropout: float = 0.1,
                 bias: bool = False,
                 use_time_features: bool = True,
                 n_time_features: int = 5,
                 label_smoothing: float = 0.1,
                 use_auxiliary_loss: bool = True):
        """
        初始化CandlestickGPT4模型

        Args:
            vocab_size: 词汇表大小
            code_size: 证券代码数量
            block_size: 最大序列长度
            n_layer: Transformer层数
            n_head: 注意力头数
            d_model: 模型维度
            dropout: Dropout比例
            bias: 是否使用偏置
            use_time_features: 是否使用时间特征
            n_time_features: 时间特征数量
            label_smoothing: 标签平滑系数
            use_auxiliary_loss: 是否使用辅助损失
        """
        super().__init__()
        self.block_size = block_size
        self.vocab_size = vocab_size
        self.code_size = code_size
        self.use_time_features = use_time_features
        self.d_model = d_model
        self.n_head = n_head
        self.n_layer = n_layer
        self.label_smoothing = label_smoothing
        self.use_auxiliary_loss = use_auxiliary_loss

        # 各种嵌入
        self.token_embedding = nn.Embedding(vocab_size, d_model)
        self.code_embedding = nn.Embedding(code_size, d_model)
        self.rotary_emb = RotaryEmbedding(d_model // n_head)

        if use_time_features:
            self.time_embedding = TimeFeatureEmbedding(d_model, n_time_features)

        self.dropout = nn.Dropout(dropout)

        # Transformer块
        self.blocks = nn.ModuleList([
            Block(d_model, n_head, dropout)
            for _ in range(n_layer)
        ])

        # 最终层归一化
        self.ln_f = RMSNorm(d_model)

        # 语言模型头
        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)

        # 权重绑定
        self.token_embedding.weight = self.lm_head.weight

        # 初始化权重
        self.apply(self._init_weights)

        # 对残差投影应用特殊的缩放初始化
        for pn, p in self.named_parameters():
            if pn.endswith('proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        print(f"参数数量: {self.get_num_params()/1e6:.2f}M")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)

    def get_num_params(self, non_embedding=False):
        """获取参数数量"""
        n_params = sum(p.numel() for p in self.parameters())
        if non_embedding:
            n_params -= self.token_embedding.weight.numel()
            n_params -= self.code_embedding.weight.numel()
        return n_params

    def forward(self, input_tokens, code_ids, time_features=None, targets=None, **kwargs):
        """
        前向传播

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            targets: 目标token序列 [batch_size, seq_len]
            **kwargs: 其他参数

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size]
            loss: 损失值（如果提供了targets）
        """
        batch_size, seq_len = input_tokens.size()
        assert seq_len <= self.block_size, f"输入序列长度{seq_len}超过了最大长度{self.block_size}"

        # 获取各种嵌入
        token_emb = self.token_embedding(input_tokens)  # [batch_size, seq_len, d_model]

        # 扩展code_ids并获取嵌入
        code_ids = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
        code_emb = self.code_embedding(code_ids)  # [batch_size, seq_len, d_model]

        # 组合嵌入
        x = token_emb + code_emb

        # 添加时间特征
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + time_emb

        x = self.dropout(x)

        # 获取旋转位置嵌入
        rotary_emb = self.rotary_emb(x, seq_len=seq_len)

        # 通过Transformer块
        for block in self.blocks:
            x = block(x, rotary_emb)

        x = self.ln_f(x)

        # 计算logits
        logits = self.lm_head(x)  # [batch_size, seq_len, vocab_size]

        # 计算损失
        loss = None
        if targets is not None:
            # 处理填充值
            mask = targets != -1
            valid_targets = targets.clone()
            valid_targets[~mask] = 0  # 将-1替换为0，避免索引越界

            # 使用标签平滑
            if self.label_smoothing > 0:
                # 创建平滑标签
                n_class = logits.size(-1)
                one_hot = torch.zeros_like(logits).scatter_(
                    dim=-1, index=valid_targets.unsqueeze(-1), value=1.0
                )

                # 只对有效位置应用标签平滑
                mask_expanded = mask.unsqueeze(-1).expand_as(one_hot)
                one_hot = torch.where(
                    mask_expanded,
                    one_hot * (1 - self.label_smoothing) + self.label_smoothing / n_class,
                    torch.zeros_like(one_hot)
                )

                # 计算KL散度损失
                log_prob = F.log_softmax(logits, dim=-1)
                loss = -(one_hot * log_prob).sum(dim=-1)
                loss = loss.masked_select(mask).mean()  # 只计算有效位置的损失
            else:
                # 标准交叉熵损失
                loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)

            # 添加辅助损失以帮助收敛
            if self.use_auxiliary_loss:
                # 添加预测准确性损失
                pred = logits.argmax(dim=-1)
                acc = (pred == targets).float()
                acc = acc.masked_select(mask).mean()  # 只计算有效位置的准确率
                aux_loss = (1.0 - acc) * 0.1  # 小权重的辅助损失
                loss = loss + aux_loss

        return logits, loss

    @torch.no_grad()
    def generate(self,
                input_tokens,
                code_ids,
                time_features=None,
                max_new_tokens=10,
                temperature=1.0,
                top_k=None,
                **kwargs):
        """
        生成新的token序列

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            max_new_tokens: 生成的最大新token数量
            temperature: 温度参数，控制采样的随机性
            top_k: 只考虑概率最高的前k个token
            **kwargs: 其他参数

        Returns:
            生成的token序列 [batch_size, seq_len + max_new_tokens]
        """
        try:
            # 打印输入参数
            print(f"\n生成参数:")
            print(f"input_tokens shape: {input_tokens.shape}")
            print(f"code_ids shape: {code_ids.shape}")
            if time_features is not None:
                print(f"time_features shape: {time_features.shape}")
            print(f"max_new_tokens: {max_new_tokens}")
            print(f"temperature: {temperature}")
            print(f"top_k: {top_k}")

            # 打印输入token的统计信息
            print(f"\n输入token统计:")
            unique_tokens = torch.unique(input_tokens)
            print(f"唯一token数量: {len(unique_tokens)}")
            print(f"最小token值: {input_tokens.min().item()}")
            print(f"最大token值: {input_tokens.max().item()}")
            print(f"前10个token: {input_tokens[0, :10].tolist()}")

            batch_size, seq_len = input_tokens.size()

            # 检查输入有效性
            if seq_len == 0:
                print("警告: 输入序列长度为0，无法生成预测")
                # 返回一个全零的序列
                return torch.zeros((batch_size, max_new_tokens), dtype=torch.int32, device=input_tokens.device)

            if batch_size == 0:
                print("警告: 批次大小为0，无法生成预测")
                # 返回一个空张量
                return torch.zeros((0, seq_len + max_new_tokens), dtype=torch.int32, device=input_tokens.device)

            # 复制输入，以便我们可以追加生成的token
            tokens = input_tokens.clone()

            # 如果提供了时间特征，我们需要扩展它以容纳新生成的token
            if self.use_time_features and time_features is not None:
                # 假设时间特征的最后一个时间步可以重复
                last_time_feature = time_features[:, -1:, :]
                extended_time_features = time_features
            else:
                extended_time_features = None

            # 打印生成过程
            print(f"\n开始生成 {max_new_tokens} 个新token:")

            # 存储生成的token和概率
            generated_tokens = []
            token_probs = []

            for i in range(max_new_tokens):
                # 如果序列太长，截断它
                if tokens.size(1) > self.block_size:
                    tokens = tokens[:, -self.block_size:]
                    if extended_time_features is not None:
                        extended_time_features = extended_time_features[:, -self.block_size:, :]

                # 前向传播
                try:
                    if extended_time_features is not None:
                        logits, _ = self(tokens, code_ids, extended_time_features)
                    else:
                        logits, _ = self(tokens, code_ids)
                except Exception as e:
                    print(f"前向传播出错: {str(e)}")
                    # 返回已生成的token
                    return tokens

                # 只关注最后一个时间步的logits
                logits = logits[:, -1, :] / max(temperature, 1e-6)  # 确保温度不为零

                # 打印logits统计信息
                if i == 0:  # 只打印第一次迭代的信息，避免输出过多
                    print(f"\nLogits统计 (第1次迭代):")
                    print(f"Logits形状: {logits.shape}")
                    print(f"Logits最小值: {logits.min().item()}")
                    print(f"Logits最大值: {logits.max().item()}")
                    print(f"Logits平均值: {logits.mean().item()}")
                    print(f"Logits是否包含NaN: {torch.isnan(logits).any().item()}")
                    print(f"Logits是否包含Inf: {torch.isinf(logits).any().item()}")

                # 可选地只保留top-k个logits
                if top_k is not None and top_k > 0:
                    v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                    logits[logits < v[:, [-1]]] = -float('Inf')

                # 应用softmax得到概率
                probs = F.softmax(logits, dim=-1)

                # 检查概率分布
                if torch.isnan(probs).any() or torch.isinf(probs).any():
                    print(f"警告: 概率分布包含NaN或Inf值，使用均匀分布")
                    probs = torch.ones_like(probs) / probs.size(-1)

                # 打印概率统计信息
                if i == 0:  # 只打印第一次迭代的信息
                    print(f"\n概率统计 (第1次迭代):")
                    print(f"概率和: {probs.sum().item()}")
                    print(f"最大概率: {probs.max().item()}")
                    print(f"最大概率对应的token: {probs.argmax().item()}")

                    # 打印前5个最高概率的token
                    top_probs, top_indices = torch.topk(probs, min(5, probs.size(-1)))
                    print(f"前5个最高概率的token:")
                    for j in range(len(top_indices[0])):
                        print(f"  Token {top_indices[0][j].item()}: 概率 {top_probs[0][j].item():.4f}")

                # 采样下一个token
                try:
                    next_token = torch.multinomial(probs, num_samples=1)
                except Exception as e:
                    print(f"采样出错: {str(e)}")
                    # 使用最高概率的token
                    next_token = torch.argmax(probs, dim=-1, keepdim=True)

                # 获取采样token的概率
                token_prob = probs[0, next_token[0, 0]].item()
                token_probs.append(token_prob)
                generated_tokens.append(next_token.item())

                # 打印生成的token
                if i < 5 or i >= max_new_tokens - 5:  # 只打印前5个和后5个
                    print(f"生成第{i+1}个token: {next_token.item()} (概率: {token_prob:.4f})")

                # 追加到序列
                tokens = torch.cat((tokens, next_token), dim=1)

                # 如果有时间特征，也扩展它
                if extended_time_features is not None:
                    extended_time_features = torch.cat((extended_time_features, last_time_feature), dim=1)

            # 打印生成的token统计信息
            print(f"\n生成的token统计:")
            print(f"生成的token: {generated_tokens}")
            print(f"token概率: {[f'{p:.4f}' for p in token_probs]}")
            print(f"平均概率: {sum(token_probs)/len(token_probs):.4f}")

            print(f"生成完成，最终序列长度: {tokens.size(1)}")
            return tokens

        except Exception as e:
            import traceback
            print(f"生成过程出错: {str(e)}")
            traceback.print_exc()
            # 返回输入序列
            return input_tokens

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        """
        配置优化器

        Args:
            weight_decay: 权重衰减系数
            learning_rate: 学习率
            betas: Adam优化器的beta参数
            device_type: 设备类型

        Returns:
            优化器
        """
        import inspect
        # 获取所有参数
        param_dict = {pn: p for pn, p in self.named_parameters() if p.requires_grad}

        # 将参数分为需要权重衰减的和不需要的
        decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
        nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]

        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]

        # 打印参数信息
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"需要权重衰减的参数: {len(decay_params)}个，共{num_decay_params:,}个参数")
        print(f"不需要权重衰减的参数: {len(nodecay_params)}个，共{num_nodecay_params:,}个参数")

        # 创建AdamW优化器
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"使用fused AdamW: {use_fused}")

        return optimizer
