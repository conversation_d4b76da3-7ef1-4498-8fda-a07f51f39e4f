# ## 数据预处理
# 特征向量的构建是机器学习的关键步骤
#%%
import sys
import time
import datetime
import json
import sqlite3
import pandas as pd
import numpy as np

sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

# ### AICM系统中的因子数据作为机器学习的主要特征向量数据
# - 市场数据
# - 技术指标数据
# - 财务数据
# - 订单上下文数据

# AICM中的一些常量定义
FACOTR_NUM = 112
ALL_FACTOR_NAMES = [
    # 价量因子
    "OPEN", "HIGH", "LOW", "CLOSE", "VOLUME", "TYPICAL_PRICE", "NEW",
    "NEW_CHANGE_PERCENT", "SHORT_TERM_HIGH", "LONG_TERM_HIGH", "SHORT_TERM_LOW",
    "LONG_TERM_LOW",

    # 技术指标类因子
    "AD", "DX", "ADX", "ADXR", "APO", "AROON_UP", "AROON_DOWN", "ATR",
    "BOLL_UP", "BOLL_MID", "BOLL_DOWN", "CCI", "CMO",

    "MA_FAST", "MA_SLOW", "EMA_FAST", "EMA_SLOW", "DEMA_FAST", "DEMA_SLOW",
    "KAMA_FAST", "KAMA_SLOW", "MAMA_FAST", "MAMA_SLOW", "T3_FAST", "T3_SLOW",
    "TEMA_FAST", "TEMA_SLOW", "TRIMA_FAST", "TRIMA_SLOW", "TRIX_FAST",
    "TRIX_SLOW",

    "MACD", "MACD_DIFF", "MACD_DEA", "MFI", "MOM", "NATR", "OBV", "ROC", "RSI",
    "SAR", "TRANGE", "TSF", "ULTOSC", "WILLR",
    "KDJ_K", "KDJ_D",

    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE", "SQUEEZE_SIGNAL", "SQUEEZE_ZERO_BARS", "SQUEEZE_BAND_UPL",
    "SQUEEZE_BAND_DWL", "SQUEEZE_MDL", "SQUEEZE_KC_UPL", "SQUEEZE_KC_DWL",
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_UPL", "BAND_MDL", "BAND_DWL", "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",
    "BAND_BK_BARS", "BAR_STICK_LENGTH",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HIGHEST", "TREND_LOWEST", "TREND_HLR",
    "TREND_LEVEL",

    "HYO_TENKAN_SEN", "HYO_KIJUN_SEN", "HYO_CROSS_BARS", "TATR",
    "TATR_THRESHOLD"    
]

# 使用两个值的因子
TWO_VAL_FACTOR_NAMES = [
    "RSI", "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "BAND_GRADIENT",
    "TL_FAST", "TL_SLOW",
    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR"
]

SELECT_FACTOR_NAMES = [[ # Slow period factor
    "RSI",
    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
],
[ # Fast period factor
    "RSI",
    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]]

# SNAPSHOT_CONTEXT = [
#   "COST_RNG", "DRAWDOWN_RNG", "STDDEV_RNG", "PNL", "POS_DAYS",
#   "POS_SHORT_BARS", "POS_LONG_BARS", "SHORT_RANGE", "LONG_RANGE",
#   "PF_YIELD_TREND", "PF_YIELD_HL", "AD_PS_RATIO", "PF_PS_RATIO",
#   "FAST_AG_RSI", "FAST_AG_RSI_PREV", "SLOW_AG_RSI", "SLOW_AG_RSI_PREV",
#   "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV",
#   "CURRENT_TIME"
# ]

SNAPSHOT_CONTEXT = [
  "COST_RNG", "DRAWDOWN_RNG", "STDDEV_RNG", "PNL", "POS_DAYS",
  "POS_SHORT_BARS", "POS_LONG_BARS", "SHORT_RANGE", "LONG_RANGE",
  "PF_YIELD_TREND", "PF_YIELD_HL", "AD_PS_RATIO", "PF_PS_RATIO",
  "FAST_AG_RSI", "FAST_AG_STDDEV", "SLOW_AG_RSI", "SLOW_AG_STDDEV",
  "FAST_QH_RSI", "FAST_QH_STDDEV", "SLOW_QH_RSI", "SLOW_QH_STDDEV",
  "FAST_AG_LR_SLOPE_PREV", "FAST_AG_LR_SLOPE",
  "SLOW_AG_LR_SLOPE_PREV", "SLOW_AG_LR_SLOPE",
  "FAST_QH_LR_SLOPE_PREV", "FAST_QH_LR_SLOPE",
  "SLOW_QH_LR_SLOPE_PREV", "SLOW_QH_LR_SLOPE", 
  "CURRENT_TIME"
]

SELECT_SNAPSHOT_CONTEXT = [
  "STDDEV_RNG", "SHORT_RANGE", "LONG_RANGE", "FAST_QH_RSI", "SLOW_QH_RSI"
]


def factor_select(n, i):
    if ALL_FACTOR_NAMES[n] in SELECT_FACTOR_NAMES[i]:
        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
            return 2
        else:
            return 1
    return 0

def context_select(n):
    if SNAPSHOT_CONTEXT[n] in SELECT_SNAPSHOT_CONTEXT:
        return 1
    return 0


class AicmFactorEda():

    def __init__(self, db_path, data_path) -> None:
        self.db_path = db_path
        self.data_path = data_path

    # 从数据库加载因子数据
    def load_factor_from_db(self):
        db=create_db("leveldb", f"{self.db_path}/store/kv.db" , Mode.read)
        cursor = db.new_cursor()
        # cursor.seek_to_first()
        data = {}
        while cursor.valid():
            # print(cursor.key())
            # print(cursor.key(), cursor.value())
            # fss:lf:
            if cursor.key()[0:4] == b'fss:':
                key = cursor.key().decode()
                ordid = key[7:]
                if ordid not in data:
                    data[ordid] = {}
                item = data[ordid]
                if key[0:7] == 'fss:lf:':
                    item['lf'] = cursor.value().decode()
                if key[0:7] == 'fss:sf:':
                    item['sf'] = cursor.value().decode()
                if key[0:7] == 'fss:ct:':
                    item['ct'] = cursor.value().decode()
                # data.append(cursor.value())
            cursor.next()
        del cursor
        db.close()
        del db
        return data

    def dump_using_factor_json(self):
        f_sel = {}
        f_sel['slow'] = [factor_select(n, 0) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [factor_select(n, 1) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [context_select(n) for n in range(len(SNAPSHOT_CONTEXT))]

        with open(f'{self.data_path}/using_factor.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)

    def get_factor_names(self, ds, factor_type):

        col_names = ['ord_id']
        if factor_type == "lf":
            for name in SELECT_FACTOR_NAMES[0]:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append("lf_{}_1".format(name))
                    col_names.append("lf_{}_2".format(name))
                else:
                    col_names.append("lf_%s" % name)

        if factor_type == "sf":
            for name in SELECT_FACTOR_NAMES[1]:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append("sf_{}_1".format(name))
                    col_names.append("sf_{}_2".format(name))
                else:
                    col_names.append("sf_%s" % name)

        if factor_type == "ct":
            col_names.extend(SELECT_SNAPSHOT_CONTEXT)

        return col_names

    def get_order_factors(self, ds, factor_type):

        factor_data = []
        for key, value in ds.items():
            if factor_type not in value:
                continue

            item = []
            item.append(key)

            if factor_type == "lf":
                data = json.loads(value['lf'])
                for name in SELECT_FACTOR_NAMES[0]:
                    if name in TWO_VAL_FACTOR_NAMES:
                        item.extend(data[name][1:])
                    else:
                        item.append(data[name][2])

            if factor_type == "sf":
                data = json.loads(value['sf'])
                for name in SELECT_FACTOR_NAMES[1]:
                    if name in TWO_VAL_FACTOR_NAMES:
                        item.extend(data[name][1:])
                    else:
                        item.append(data[name][2])

            if factor_type == "ct":
                data = json.loads(value['ct'])
                if 'PNL' not in data.keys():
                    continue
                for key in SELECT_SNAPSHOT_CONTEXT:
                    if key in data.keys():
                        item.append(data[key])
                    else:
                        item.append(0.0)

            factor_data.append(item)
        ft_name = self.get_factor_names(ds, factor_type)
        return pd.DataFrame(factor_data, columns=ft_name)


    """
    从orders数据库导出订单标签
    """
    def load_orders(self, portfolio_id):
        conn = sqlite3.connect(f'{self.db_path}/data/ot_store.db')
        cur = conn.cursor()

        # cur.execute("""SELECT * FROM T_Filled_Order WHERE account_id = "%s" ORDER BY id DESC"""%('*****************'))
        cur.execute("SELECT * FROM T_Filled_Order ORDER BY id ASC")
        orders = cur.fetchall()

        data = []
        for item in orders:
            record = []
            ord = json.loads(item[1])
            if ord['account_id'] != portfolio_id:
                continue
            record.append(ord['order_id'])
            record.append(ord['account_id'])
            record.append(ord['order_book_id'])
            record.append(time.strftime("%Y%m%d %H:%M:%S", time.localtime(ord['trading_dt'])))
            msg = ord['message'].split(',')
            if len(msg) <= 2:
                record.append(msg[0])
                record.append(msg[1])
                record.append(0)
                record.append(0)
            elif len(msg) > 7:
                record.append(msg[0])
                record.append(msg[1])
                record.append(msg[3])
                record.append(msg[7])
            else:
                record.append(msg[0])
                record.append(msg[1])
                record.append(0)
                record.append(0)
            data.append(record)

        cur.close()
        conn.close()

        return pd.DataFrame(data, columns=['ord_id', 'account_id', 'label', 'datetime', 'oprater', 'direct', 'cost_atr', 'pnl'])

    def get_orders_label(self, ord_df):
        data = []
        for _, group in ord_df.groupby(['account_id', 'label']):
            for i in range(len(group)):
                if i + 1 >= len(group):
                    break
                if group.iat[i, 4] != "open":
                    continue
                item = []
                item.append(group.iat[i, 0])
                item.append(group.iat[i, 2])
                i = i + 1
                if group.iat[i, 4] != "open":
                    item.append(group.iat[i, 3])
                    item.append(group.iat[i, 5])
                    if float(group.iat[i, 7]) > 0:
                        item.append(1)
                    else:
                        item.append(0)
                    data.append(item)
                # else:
                #     print("warning:", group.iat[i, 2])
        return pd.DataFrame(data, columns=['ord_id', 'instrument', 'datetime', 'direct', 'label'])

    def export_factors_data(self, portfolio_id):
        ord_df = self.load_orders(portfolio_id)
        if len(ord_df) == 0:
            print("portfolio: %s not find." % portfolio_id)
            return
        ord_lb_df = self.get_orders_label(ord_df)
        ord_lb_df['CODE'] = ord_lb_df.apply(lambda x: x['instrument'][0:-7], axis=1)

        print(f'Portfolio: {portfolio_id} export order data: {len(ord_lb_df)} \
        Today add count: {(ord_lb_df["datetime"] >= datetime.datetime.now().strftime("%Y%m%d 00:00:00")).sum()}')

        ft_ds = self.load_factor_from_db()
        lf_df = self.get_order_factors(ft_ds, factor_type="lf")
        sf_df = self.get_order_factors(ft_ds, factor_type="sf")
        ct_df = self.get_order_factors(ft_ds, factor_type="ct")

        ord_lb_long_df = ord_lb_df[ord_lb_df['direct'] == 'L']
        ord_lb_short_df = ord_lb_df[ord_lb_df['direct'] == 'S']

        print(f"long Y:\n{ord_lb_long_df.label.value_counts()}")
        print(f"short Y:\n{ord_lb_short_df.label.value_counts()}")

        '''
        ft_lf_long_df = pd.merge(lf_df, ord_lb_long_df['ord_id'], how='inner', on='ord_id')
        ft_sf_long_df = pd.merge(sf_df, ord_lb_long_df['ord_id'], how='inner', on='ord_id')
        ft_ct_long_df = pd.merge(ct_df, ord_lb_long_df['ord_id'], how='inner', on='ord_id')
        ord_lb_long_df = pd.merge(ord_lb_long_df, ft_lf_long_df['ord_id'], how='inner', on='ord_id')

        ft_lf_short_df = pd.merge(lf_df, ord_lb_short_df['ord_id'], how='inner', on='ord_id')
        ft_sf_short_df = pd.merge(sf_df, ord_lb_short_df['ord_id'], how='inner', on='ord_id')
        ft_ct_short_df = pd.merge(ct_df, ord_lb_short_df['ord_id'], how='inner', on='ord_id')
        ord_lb_short_df = pd.merge(ord_lb_short_df, ft_lf_short_df['ord_id'], how='inner', on='ord_id')
        '''

        ft_ct_long_df = ct_df[ct_df['ord_id'].isin(ord_lb_long_df['ord_id'])]
        ord_lb_long_df = ord_lb_long_df[ord_lb_long_df['ord_id'].isin(ft_ct_long_df['ord_id'])]
        ft_lf_long_df = lf_df[lf_df['ord_id'].isin(ord_lb_long_df['ord_id'])]
        ft_sf_long_df = sf_df[sf_df['ord_id'].isin(ord_lb_long_df['ord_id'])]

        ft_ct_short_df = ct_df[ct_df['ord_id'].isin(ord_lb_short_df['ord_id'])]
        ord_lb_short_df = ord_lb_short_df[ord_lb_short_df['ord_id'].isin(ft_ct_short_df['ord_id'])]
        ft_lf_short_df = lf_df[lf_df['ord_id'].isin(ord_lb_short_df['ord_id'])]
        ft_sf_short_df = sf_df[sf_df['ord_id'].isin(ord_lb_short_df['ord_id'])]

        ft_lf_long_df.to_csv("%s/factors_long_lf.%s.csv"%(self.data_path, portfolio_id), index=0)
        ft_sf_long_df.to_csv("%s/factors_long_sf.%s.csv"%(self.data_path, portfolio_id), index=0)
        ft_ct_long_df.to_csv("%s/factors_long_ct.%s.csv"%(self.data_path, portfolio_id), index=0)
        ord_lb_long_df.to_csv("%s/orders_long_label.%s.csv"%(self.data_path, portfolio_id), index=0)

        ft_lf_short_df.to_csv("%s/factors_short_lf.%s.csv"%(self.data_path, portfolio_id), index=0)
        ft_sf_short_df.to_csv("%s/factors_short_sf.%s.csv"%(self.data_path, portfolio_id), index=0)
        ft_ct_short_df.to_csv("%s/factors_short_ct.%s.csv"%(self.data_path, portfolio_id), index=0)
        ord_lb_short_df.to_csv("%s/orders_short_label.%s.csv"%(self.data_path, portfolio_id), index=0)

        print(f"portfolio: {portfolio_id} export long: {len(ord_lb_long_df)} short: {len(ord_lb_short_df)}\
             total: {len(ord_lb_long_df) + len(ord_lb_short_df)}")


if __name__ == '__main__':
    # 配置
    config_param = {
        "db_path": "d:/RoboQuant",
        "data_path": 'e:/lab/RoboQuant/pylab/data',
    }

    portfolios = ['*****************', '00171106132928000']
    eda = AicmFactorEda(**config_param)
    eda.dump_using_factor_json()
    for pf in portfolios:
        eda.export_factors_data(pf)

