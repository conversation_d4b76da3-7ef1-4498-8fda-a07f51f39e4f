"""
使用PyTorch Lightning框架的CandlestickLLM模型
"""

import os
import math
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from typing import Dict, List, Tuple, Union, Optional, Any
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau, OneCycleLR
from torch.utils.data import DataLoader, Dataset

from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer


class PLCandlestickLLM(pl.LightningModule):
    """
    使用PyTorch Lightning框架的CandlestickLLM模型
    """
    def __init__(
        self,
        vocab_size: int,
        code_size: int,
        block_size: int = 30,
        n_layer: int = 12,
        n_head: int = 12,
        d_model: int = 128,
        dropout: float = 0.1,
        bias: bool = True,
        use_time_features: bool = True,
        n_time_features: int = 5,
        learning_rate: float = 3e-4,
        weight_decay: float = 0.01,
        betas: Tuple[float, float] = (0.9, 0.999),
        lr_scheduler: str = 'cosine',
        warmup_ratio: float = 0.1,
        max_epochs: int = 10,
        grad_clip: float = 1.0,
        label_smoothing: float = 0.0,
    ):
        """
        初始化PyTorch Lightning版本的CandlestickLLM模型

        Args:
            vocab_size: 词汇表大小
            code_size: 证券代码数量
            block_size: 最大序列长度
            n_layer: Transformer层数
            n_head: 注意力头数
            d_model: 模型维度
            dropout: Dropout比例
            bias: 是否使用偏置
            use_time_features: 是否使用时间特征
            n_time_features: 时间特征数量
            learning_rate: 学习率
            weight_decay: 权重衰减
            betas: Adam优化器的beta参数
            lr_scheduler: 学习率调度器类型 ('cosine', 'reduce_on_plateau', 'one_cycle')
            warmup_ratio: 预热步数比例
            max_epochs: 最大训练轮数
            grad_clip: 梯度裁剪值
            label_smoothing: 标签平滑系数
        """
        super().__init__()
        self.save_hyperparameters()

        # 创建基础CandlestickLLM模型
        self.model = CandlestickLLM(
            vocab_size=vocab_size,
            code_size=code_size,
            block_size=block_size,
            n_layer=n_layer,
            n_head=n_head,
            d_model=d_model,
            dropout=dropout,
            bias=bias,
            use_time_features=use_time_features,
            n_time_features=n_time_features
        )

        # 记录训练参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.betas = betas
        self.lr_scheduler = lr_scheduler
        self.warmup_ratio = warmup_ratio
        self.max_epochs = max_epochs
        self.grad_clip = grad_clip
        self.label_smoothing = label_smoothing

        # 记录训练状态
        self.training_step_outputs = []
        self.validation_step_outputs = []
        self.test_step_outputs = []

    def forward(self, input_tokens, code_ids, time_features=None, targets=None, **kwargs):
        """
        前向传播

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            targets: 目标token序列 [batch_size, seq_len]
            **kwargs: 其他参数

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size]
            loss: 损失值（如果提供了targets）
        """
        return self.model(input_tokens, code_ids, time_features, targets, **kwargs)

    def training_step(self, batch, batch_idx):
        """
        训练步骤

        Args:
            batch: 批次数据
            batch_idx: 批次索引

        Returns:
            loss: 损失值
        """
        # 解包批次数据
        input_tokens = batch['input_tokens']
        target_tokens = batch['target_tokens']
        code_ids = batch['code_id']
        time_features = batch.get('time_features')

        # 前向传播
        logits, loss = self(input_tokens, code_ids, time_features, target_tokens)

        # 记录损失
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)

        # 保存输出以便在epoch结束时计算指标
        self.training_step_outputs.append({'loss': loss, 'logits': logits, 'targets': target_tokens})
        # print(f"训练步骤 {batch_idx}, 损失: {loss.item()}")
        return loss

    def validation_step(self, batch, batch_idx):
        """
        验证步骤

        Args:
            batch: 批次数据
            batch_idx: 批次索引

        Returns:
            loss: 损失值
        """
        # 解包批次数据
        input_tokens = batch['input_tokens']
        target_tokens = batch['target_tokens']
        code_ids = batch['code_id']
        time_features = batch.get('time_features')

        # 前向传播
        logits, loss = self(input_tokens, code_ids, time_features, target_tokens)

        # 记录损失
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True, logger=True)

        # 保存输出以便在epoch结束时计算指标
        self.validation_step_outputs.append({'loss': loss, 'logits': logits, 'targets': target_tokens})

        return loss

    def test_step(self, batch, batch_idx):
        """
        测试步骤

        Args:
            batch: 批次数据
            batch_idx: 批次索引

        Returns:
            loss: 损失值
        """
        # 解包批次数据
        input_tokens = batch['input_tokens']
        target_tokens = batch['target_tokens']
        code_ids = batch['code_id']
        time_features = batch.get('time_features')

        # 前向传播
        logits, loss = self(input_tokens, code_ids, time_features, target_tokens)

        # 记录损失
        self.log('test_loss', loss, on_step=False, on_epoch=True, prog_bar=True, logger=True)

        # 保存输出以便在epoch结束时计算指标
        self.test_step_outputs.append({'loss': loss, 'logits': logits, 'targets': target_tokens})

        return loss

    def on_train_epoch_end(self):
        """训练轮结束时的回调"""
        # 计算平均损失
        avg_loss = torch.stack([x['loss'] for x in self.training_step_outputs]).mean()
        self.log('train_loss_epoch', avg_loss, prog_bar=True, logger=True)

        # 计算准确率
        all_preds = []
        all_targets = []
        for output in self.training_step_outputs:
            logits = output['logits']
            targets = output['targets']
            preds = torch.argmax(logits, dim=-1)
            all_preds.append(preds)
            all_targets.append(targets)

        all_preds = torch.cat(all_preds, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        # 计算准确率（忽略填充token）
        mask = (all_targets != -1)
        correct = (all_preds[mask] == all_targets[mask]).float().sum()
        total = mask.sum().float()
        accuracy = correct / total if total > 0 else torch.tensor(0.0)

        self.log('train_accuracy', accuracy, prog_bar=True, logger=True)

        # 清空输出列表
        self.training_step_outputs.clear()

    def on_validation_epoch_end(self):
        """验证轮结束时的回调"""
        # 计算平均损失
        avg_loss = torch.stack([x['loss'] for x in self.validation_step_outputs]).mean()
        self.log('val_loss_epoch', avg_loss, prog_bar=True, logger=True)

        # 计算准确率
        all_preds = []
        all_targets = []
        for output in self.validation_step_outputs:
            logits = output['logits']
            targets = output['targets']
            preds = torch.argmax(logits, dim=-1)
            all_preds.append(preds)
            all_targets.append(targets)

        all_preds = torch.cat(all_preds, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        # 计算准确率（忽略填充token）
        mask = (all_targets != -1)
        correct = (all_preds[mask] == all_targets[mask]).float().sum()
        total = mask.sum().float()
        accuracy = correct / total if total > 0 else torch.tensor(0.0)

        self.log('val_accuracy', accuracy, prog_bar=True, logger=True)

        # 清空输出列表
        self.validation_step_outputs.clear()

    def on_test_epoch_end(self):
        """测试轮结束时的回调"""
        # 计算平均损失
        avg_loss = torch.stack([x['loss'] for x in self.test_step_outputs]).mean()
        self.log('test_loss_epoch', avg_loss, prog_bar=True, logger=True)

        # 计算准确率
        all_preds = []
        all_targets = []
        for output in self.test_step_outputs:
            logits = output['logits']
            targets = output['targets']
            preds = torch.argmax(logits, dim=-1)
            all_preds.append(preds)
            all_targets.append(targets)

        all_preds = torch.cat(all_preds, dim=0)
        all_targets = torch.cat(all_targets, dim=0)

        # 计算准确率（忽略填充token）
        mask = (all_targets != -1)
        correct = (all_preds[mask] == all_targets[mask]).float().sum()
        total = mask.sum().float()
        accuracy = correct / total if total > 0 else torch.tensor(0.0)

        self.log('test_accuracy', accuracy, prog_bar=True, logger=True)

        # 清空输出列表
        self.test_step_outputs.clear()

    def configure_optimizers(self):
        """
        配置优化器和学习率调度器

        Returns:
            优化器和学习率调度器
        """
        # 将参数分为权重衰减组和非权重衰减组
        decay_params = []
        no_decay_params = []

        for name, param in self.named_parameters():
            if 'bias' in name or 'ln' in name or 'layernorm' in name:
                no_decay_params.append(param)
            else:
                decay_params.append(param)

        optimizer_grouped_parameters = [
            {'params': decay_params, 'weight_decay': self.weight_decay},
            {'params': no_decay_params, 'weight_decay': 0.0}
        ]

        # 创建优化器
        optimizer = torch.optim.AdamW(
            optimizer_grouped_parameters,
            lr=self.learning_rate,
            betas=self.betas
        )

        # 创建学习率调度器
        if self.lr_scheduler == 'cosine':
            # 计算总步数和预热步数
            total_steps = self.trainer.estimated_stepping_batches
            warmup_steps = int(total_steps * self.warmup_ratio)

            # 创建余弦退火学习率调度器
            scheduler = {
                'scheduler': CosineAnnealingLR(optimizer, T_max=total_steps - warmup_steps),
                'interval': 'step',
                'frequency': 1
            }

            # 如果有预热步数，创建预热调度器
            if warmup_steps > 0:
                def lr_lambda(current_step):
                    if current_step < warmup_steps:
                        return float(current_step) / float(max(1, warmup_steps))
                    return 1.0

                scheduler = {
                    'scheduler': torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda),
                    'interval': 'step',
                    'frequency': 1
                }

        elif self.lr_scheduler == 'reduce_on_plateau':
            scheduler = {
                'scheduler': ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True),
                'monitor': 'val_loss',
                'interval': 'epoch',
                'frequency': 1
            }

        elif self.lr_scheduler == 'one_cycle':
            total_steps = self.trainer.estimated_stepping_batches
            scheduler = {
                'scheduler': OneCycleLR(
                    optimizer,
                    max_lr=self.learning_rate,
                    total_steps=total_steps,
                    pct_start=self.warmup_ratio,
                    anneal_strategy='cos',
                    div_factor=25.0,
                    final_div_factor=10000.0
                ),
                'interval': 'step',
                'frequency': 1
            }

        else:
            return optimizer

        return [optimizer], [scheduler]

    def get_num_params(self, non_embedding=False):
        """
        获取模型参数数量

        Args:
            non_embedding: 是否排除嵌入层参数

        Returns:
            参数数量
        """
        return self.model.get_num_params(non_embedding)

    @torch.no_grad()
    def generate(self, input_tokens, code_ids, time_features=None, max_new_tokens=10, temperature=1.0, top_k=None):
        """
        生成新的token序列

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            max_new_tokens: 最大生成token数
            temperature: 温度参数
            top_k: 只考虑概率最高的k个token

        Returns:
            生成的token序列
        """
        # 确保模型处于评估模式
        self.eval()

        # 保存原始输入
        original_input = input_tokens.clone()

        # 生成新token
        for _ in range(max_new_tokens):
            # 如果输入长度超过block_size，截断
            if input_tokens.size(1) > self.model.block_size:
                input_tokens = input_tokens[:, -self.model.block_size:]
                if time_features is not None:
                    time_features = time_features[:, -self.model.block_size:]

            # 前向传播
            logits, _ = self(input_tokens, code_ids, time_features)

            # 获取最后一个时间步的logits
            logits = logits[:, -1, :] / temperature

            # 如果指定了top_k，只保留概率最高的k个token
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')

            # 计算概率分布
            probs = F.softmax(logits, dim=-1)

            # 采样下一个token
            next_token = torch.multinomial(probs, num_samples=1)

            # 将新token添加到输入序列
            input_tokens = torch.cat([input_tokens, next_token], dim=1)

            # 如果有时间特征，也需要扩展
            if time_features is not None:
                # 简单复制最后一个时间步的特征
                next_time_feature = time_features[:, -1:, :]
                time_features = torch.cat([time_features, next_time_feature], dim=1)

        # 返回生成的序列（包括原始输入）
        return input_tokens


class PLCandlestickLLMDataModule(pl.LightningDataModule):
    """
    CandlestickLLM数据模块
    """
    def __init__(
        self,
        train_set,
        val_set,
        test_set,
        batch_size: int = 32,
        num_workers: int = 0
    ):
        """
        初始化CandlestickLLM数据模块

        Args:
            train_set: 训练数据列表
            val_set: 验证数据列表
            test_set: 测试数据列表
            batch_size: 批大小
            num_workers: 数据加载器的工作进程数
        """
        super().__init__()
        self.train_set = train_set
        self.val_set = val_set
        self.test_set = test_set
        self.batch_size = batch_size
        self.num_workers = num_workers

    def setup(self, stage=None):
        """
        准备数据集

        Args:
            stage: 当前阶段 ('fit', 'validate', 'test', 'predict')
        """
        pass

    def train_dataloader(self):
        """
        获取训练数据加载器

        Returns:
            训练数据加载器
        """
        # 只有当num_workers > 0时才启用persistent_workers
        persistent_workers = self.num_workers > 0

        return DataLoader(
            self.train_set,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            # pin_memory=True,
            persistent_workers=persistent_workers  # 启用持久化工作进程以加速数据加载
        )

    def val_dataloader(self):
        """
        获取验证数据加载器

        Returns:
            验证数据加载器
        """
        if self.val_set is None:
            return None

        # 只有当num_workers > 0时才启用persistent_workers
        persistent_workers = self.num_workers > 0

        return DataLoader(
            self.val_set,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            # pin_memory=True,
            persistent_workers=persistent_workers  # 启用持久化工作进程以加速数据加载
        )

    def test_dataloader(self):
        """
        获取测试数据加载器

        Returns:
            测试数据加载器
        """
        if self.test_set is None:
            return None

        # 只有当num_workers > 0时才启用persistent_workers
        persistent_workers = self.num_workers > 0

        return DataLoader(
            self.test_set,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            # pin_memory=True,
            persistent_workers=persistent_workers  # 启用持久化工作进程以加速数据加载
        )
