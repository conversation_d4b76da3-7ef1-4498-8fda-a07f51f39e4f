#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用新的CANDLESTICK_FEATURES向量化方法的示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from pyqlab.models.gpt2.vq_tokenizer import VectorizationMethod, CandlestickVQTokenizer

def create_sample_data():
    """创建示例K线数据"""
    # 创建一些模拟的K线数据
    np.random.seed(42)  # 为了结果可重现
    
    dates = pd.date_range('2023-01-01', periods=50, freq='D')
    base_price = 100.0
    
    data = []
    for i, date in enumerate(dates):
        # 模拟价格波动
        price_change = np.random.normal(0, 0.02)  # 2%的日波动
        
        open_price = base_price * (1 + price_change)
        
        # 模拟日内波动
        high_change = abs(np.random.normal(0, 0.01))  # 向上波动
        low_change = -abs(np.random.normal(0, 0.01))  # 向下波动
        close_change = np.random.normal(0, 0.01)
        
        high_price = open_price * (1 + high_change)
        low_price = open_price * (1 + low_change)
        close_price = open_price * (1 + close_change)
        
        # 确保价格关系正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 模拟成交量
        volume = np.random.randint(800, 1200)
        
        data.append({
            'datetime': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
        
        base_price = close_price  # 下一天的基准价格
    
    return pd.DataFrame(data)

def demonstrate_candlestick_features():
    """演示CANDLESTICK_FEATURES向量化方法"""
    print("=== CANDLESTICK_FEATURES向量化方法演示 ===\n")
    
    # 创建示例数据
    df = create_sample_data()
    print("创建的示例K线数据（前5行）:")
    print(df.head())
    print(f"\n数据形状: {df.shape}")
    
    # 创建使用新向量化方法的tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=None,  # 使用随机初始化的码本
        num_embeddings=512,
        embedding_dim=5,
        atr_period=14,
        ma_volume_period=20,
        vectorization_method=VectorizationMethod.CANDLESTICK_FEATURES,
        gap_threshold=3.0,
        detect_gaps=True
    )
    
    print(f"\n创建的Tokenizer:")
    print(f"  向量化方法: {tokenizer.vectorization_method}")
    print(f"  向量维度: {tokenizer.embedding_dim}")
    print(f"  词汇表大小: {tokenizer.vocab_size}")
    
    # 编码K线数据
    print("\n编码K线数据...")
    token_ids = tokenizer.encode(df, add_bos_eos=True)
    print(f"编码后的token序列长度: {len(token_ids)}")
    print(f"前10个token IDs: {token_ids[:10]}")
    
    # 解码token序列
    print("\n解码token序列...")
    decoded_tokens = tokenizer.decode_sequence(token_ids)
    print(f"前10个解码后的tokens: {decoded_tokens[:10]}")
    
    # 重建K线数据
    print("\n重建K线数据...")
    reconstructed_df = tokenizer.tokens_to_candlesticks(token_ids, df)
    print(f"重建数据形状: {reconstructed_df.shape}")
    print("\n重建的K线数据（前5行）:")
    print(reconstructed_df.head())
    
    # 比较原始数据和重建数据
    print("\n=== 数据质量分析 ===")
    
    # 找到有效的数据范围（跳过前面的NaN值）
    valid_start = 0
    for i in range(len(df)):
        if not pd.isna(df.iloc[i]['open']):
            valid_start = i
            break
    
    # 计算重建误差
    if len(reconstructed_df) > 0:
        # 取相同长度的数据进行比较
        min_len = min(len(reconstructed_df), len(df) - valid_start - 20)  # 减去ATR计算需要的历史数据
        
        if min_len > 0:
            orig_subset = df.iloc[valid_start + 20:valid_start + 20 + min_len]
            recon_subset = reconstructed_df.iloc[:min_len]
            
            # 计算各价格的平均误差百分比
            open_error = np.mean(np.abs(recon_subset['open'].values - orig_subset['open'].values) / orig_subset['open'].values) * 100
            high_error = np.mean(np.abs(recon_subset['high'].values - orig_subset['high'].values) / orig_subset['high'].values) * 100
            low_error = np.mean(np.abs(recon_subset['low'].values - orig_subset['low'].values) / orig_subset['low'].values) * 100
            close_error = np.mean(np.abs(recon_subset['close'].values - orig_subset['close'].values) / orig_subset['close'].values) * 100
            
            print(f"平均重建误差（百分比）:")
            print(f"  开盘价: {open_error:.2f}%")
            print(f"  最高价: {high_error:.2f}%")
            print(f"  最低价: {low_error:.2f}%")
            print(f"  收盘价: {close_error:.2f}%")
            print(f"  总体平均: {(open_error + high_error + low_error + close_error) / 4:.2f}%")
    
    # 分析K线特征
    print("\n=== K线特征分析 ===")
    
    # 手动计算一些K线的特征向量
    processed_df = tokenizer._preprocess_df(df)
    
    print("分析前5个有效K线的特征:")
    for i in range(len(processed_df)):
        row = processed_df.iloc[i]
        if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
            continue
        
        # 计算K线特征
        o, h, l, c, v = row['open'], row['high'], row['low'], row['close'], row['volume']
        prev_close = row['Prev_Close']
        ma_volume = row['MA_Volume']
        
        if o != 0:  # 避免除零
            body = (c - o) / o
            upper_shadow = (h - max(o, c)) / o
            lower_shadow = (min(o, c) - l) / o
            volume_ratio = v / ma_volume
            price_change = (c - prev_close) / prev_close
            
            print(f"\nK线 {i}:")
            print(f"  OHLC: {o:.2f}, {h:.2f}, {l:.2f}, {c:.2f}")
            print(f"  实体: {body:.4f} ({'阳线' if body > 0 else '阴线' if body < 0 else '十字'})")
            print(f"  上影线: {upper_shadow:.4f}")
            print(f"  下影线: {lower_shadow:.4f}")
            print(f"  成交量比率: {volume_ratio:.4f}")
            print(f"  价格变化: {price_change:.4f}")
            
            if i >= 4:  # 只显示前5个
                break
    
    print("\n=== 演示完成 ===")
    print("CANDLESTICK_FEATURES方法成功提取了K线的关键特征：")
    print("- 实体：反映开盘价和收盘价的关系")
    print("- 上影线：反映向上的价格压力")
    print("- 下影线：反映向下的价格支撑")
    print("- 成交量比率：反映成交活跃程度")
    print("- 价格变化：反映相对于前一交易日的变化")

if __name__ == "__main__":
    demonstrate_candlestick_features()
