{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import time\n", "import pytz\n", "import struct\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class TickDataDB():\n", "    def __init__(self,\n", "        dbfile=\"d:/QuantLab/store/tickdata.db\",\n", "        save_path=\"../data\"\n", "    ) -> None:\n", "        self._dbfile=dbfile\n", "        self._save_path = save_path\n", "        self._db=None\n", "        self._keys=[]\n", "        self._tz=pytz.timezone('Asia/Shanghai')\n", "        pass\n", "\n", "    def open_db(self, mode):\n", "        if self._db:\n", "            self.close_db()\n", "        \n", "        try:\n", "            self._db=create_db(\"leveldb\", self._dbfile, mode)\n", "        except:\n", "            raise 'Fail to open db!'\n", "\n", "    def close_db(self):\n", "        if not self._db:\n", "            raise \"not db open.\"\n", "        self._db.close()\n", "        del self._db\n", "        self._db=None\n", "\n", "    def load_all_keys(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        self._keys.clear()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            self._keys.append(str(cursor.key()))\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def get_all_labels(self)->set:\n", "        lbs = set()\n", "        if len(self._keys) == 0:\n", "            self.load_all_keys()\n", "            \n", "        for key in self._keys:\n", "            s=str(key)\n", "            pos0=s.find(':')\n", "            pos1=s.find(':', pos0+1)\n", "            lb=s[pos0+1:pos1]\n", "            lbs.add(lb)\n", "        return lbs\n", "\n", "    def read_keys(self, key):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            if str(cursor.key()) == key:\n", "                for i in range(len(cursor.value())//48):\n", "                    tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                    print(datetime.datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)\n", "                break\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def read_index(self, index):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        if index > len(self._keys):\n", "            raise f\"index < {len(self._keys)}\"\n", "\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            if cursor.key() == self._keys[index]:\n", "                s=str(cursor.key())\n", "                pos0=s.find(':')\n", "                pos1=s.find(':', pos0+1)\n", "                lb=s[pos0+1:pos1]\n", "                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                data=[lb, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2]\n", "                break\n", "            cursor.next()\n", "        del cursor\n", "        return data\n", "\n", "    def write(self, key, value):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.put(key, value)\n", "        transaction.commit()\n", "        del transaction\n", "        \n", "    def delete(self, key):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.delete(key)\n", "        transaction.commit()\n", "        del transaction\n", "\n", "    def delete_label(self, label):\n", "        if len(self._keys) == 0:\n", "            self.load_all_keys()\n", "        transaction = self._db.new_transaction()\n", "        for key in self._keys:\n", "            s=str(key)\n", "            pos0=s.find(':')\n", "            pos1=s.find(':', pos0+1)\n", "            lb=s[pos0+1:pos1]\n", "            if lb == label:\n", "                transaction.delete(key)\n", "                print(f\"del key: {key}\")\n", "        transaction.commit()\n", "        self.load_all_keys()\n", "\n", "    def query(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            print(cursor.key())\n", "            # print(cursor.key(), cursor.value())\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def read_label(self, label):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "\n", "        cursor = self._db.new_cursor()\n", "        data=[]\n", "        while cursor.valid():\n", "            for i in range(len(cursor.value())//48):\n", "                s=str(cursor.key())\n", "                pos0=s.find(':')\n", "                pos1=s.find(':', pos0+1)\n", "                lb=s[pos0+1:pos1]\n", "                if lb != label:\n", "                    cursor.next()\n", "                    continue\n", "                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                # time_id=tt//300\n", "                data.append([lb, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])\n", "            cursor.next()\n", "        del cursor\n", "        return pd.DataFrame(data, columns=['label','datetime','price','size','bid_price1','bid_price2','bid_size1','bid_size2','ask_price1','ask_price2','ask_size1','ask_size2'])\n", "\n", "    def export_label(self, label):\n", "        def trans_timestamp(dt):\n", "            # return int(time.mktime(dt.timetuple()))//300\n", "            return int(dt//300)\n", "\n", "        def log_return(series):\n", "            return np.log(series).diff()\n", "            \n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "\n", "        # self.open_db(Mode.read)\n", "        df=self.read_label(label)\n", "        # self.close_db()\n", "        print(df.shape)\n", "\n", "        try:\n", "            cols=df.columns\n", "            df = df.groupby([\"label\", \"datetime\"]).agg({\"mean\"}).reset_index()\n", "            df.columns = cols\n", "            df[\"time_id\"]=df[\"datetime\"].apply(trans_timestamp)\n", "\n", "            df.to_parquet(f\"{self._save_path}/tickdata.{label}.parquet\", engine='fastparquet')\n", "            df = df.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()\n", "            df['return'] = log_return(df['price'])\n", "            df=df.fillna(0)\n", "            df['target'] = (df['return']>0).astype(int)\n", "            df=df.drop(['price', 'return'], axis=1)\n", "            df.to_parquet(f\"{self._save_path}/tickdata_target.{label}.parquet\", engine='fastparquet')\n", "        except:\n", "            raise 'Fail to export label data!'\n", "\n", "    def read_all(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "\n", "        cursor = self._db.new_cursor()\n", "        data=[]\n", "        while cursor.valid():\n", "            for i in range(len(cursor.value())//48):\n", "                s=str(cursor.key())\n", "                pos0=s.find(':')\n", "                pos1=s.find(':', pos0+1)\n", "                lb=s[pos0+1:pos1]\n", "                tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "                # time_id=tt//300\n", "                data.append([lb, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])\n", "            cursor.next()\n", "        del cursor\n", "        return pd.DataFrame(data, columns=['label','datetime','price','size','bid_price1','bid_price2','bid_size1','bid_size2','ask_price1','ask_price2','ask_size1','ask_size2'])\n", "\n", "    def export_all(self):\n", "        def trans_timestamp(dt):\n", "            # return int(time.mktime(dt.timetuple()))//300\n", "            return int(dt//300)\n", "\n", "        def log_return(series):\n", "            return np.log(series).diff()\n", "            \n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        # self.open_db(Mode.read)\n", "        df=self.read_all()\n", "        # self.close_db()\n", "        print(df.shape)\n", "\n", "        try:\n", "            cols=df.columns\n", "            df = df.groupby([\"label\", \"datetime\"]).agg({\"mean\"}).reset_index()\n", "            df.columns = cols\n", "            df[\"time_id\"]=df[\"datetime\"].apply(trans_timestamp)\n", "\n", "            for lb in df[\"label\"].unique():\n", "                # df.to_parquet(f\"../data/tickdata.parquet\", engine='fastparquet')\n", "                df2=df[df[\"label\"]==lb]\n", "                df2.to_parquet(f\"{self._save_path}/tickdata.{lb}.parquet\", engine='fastparquet')\n", "                # df = pd.read_parquet(f\"../data/tickdata.parquet\")\n", "                df2 = df2.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()\n", "                df2['return'] = log_return(df2['price'])\n", "                df2=df2.fillna(0)\n", "                df2['target'] = (df2['return']>0).astype(int)\n", "                df2=df2.drop(['price', 'return'], axis=1)\n", "                df2.to_parquet(f\"{self._save_path}/tickdata_target.{lb}.parquet\", engine='fastparquet')\n", "        except:\n", "            raise 'Fail to export all data!'\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["td=TickDataDB(save_path='e:/lab/RoboQuant/pylab/data/tickdata')\n", "td.open_db(Mode.write)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'MA8888.ZC', 'TA8888.ZC', 'RB8888.SC', 'M8888.DC', 'SA8888.ZC', 'I8888.DC', 'HC8888.SC'}\n"]}], "source": ["print(td.get_all_labels())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lbs = ['M8888.DC', 'MA8888.ZC', 'SA8888.ZC', 'I8888.DC', 'TA8888.ZC']\n", "for lb in lbs:\n", "    td.delete_label(lb)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(8387120, 12)\n"]}], "source": ["td=TickDataDB()\n", "td.open_db(Mode.read)\n", "df=td.read_all()\n", "td.close_db()\n", "print(df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["labels=['RB8888.SC', 'HC8888.SC', 'RU8888.SC', 'BU8888.SC', 'SS8888.SC']"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       label  time_id  target\n", "0  BU8888.SC  5511494       0\n", "1  BU8888.SC  5511495       0\n", "2  BU8888.SC  5511496       0\n", "3  BU8888.SC  5511497       0\n", "4  BU8888.SC  5511522       1\n"]}], "source": ["LABEL='BU8888.SC'\n", "lbs=pd.read_parquet(f\"../data/tickdata/tickdata_target.{LABEL}.parquet\")\n", "print(lbs.head())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["df = pd.read_parquet(f\"../data/tickdata/tickdata.{LABEL}.parquet\")\n", "df.set_index(['label', 'time_id'], inplace=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(300, 11)\n", "                     datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511495  1653448500  4487.5   7.5      4487.0      4486.0   \n", "          5511495  1653448501  4487.0   4.0      4487.0      4486.0   \n", "          5511495  1653448502  4488.0  68.0      4488.0      4487.0   \n", "          5511495  1653448503  4488.0  29.5      4488.0      4487.0   \n", "          5511495  1653448504  4488.0   4.5      4488.0      4487.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511495       30.5      146.0      4488.0      4489.0      129.5   \n", "          5511495       26.0      143.0      4488.0      4489.0      127.0   \n", "          5511495       70.0       30.5      4489.0      4490.0       22.0   \n", "          5511495       60.0       30.0      4489.5      4490.5       44.0   \n", "          5511495       62.5       31.0      4489.0      4490.0       30.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511495       33.5  \n", "          5511495       33.0  \n", "          5511495       81.0  \n", "          5511495       82.5  \n", "          5511495       88.0  \n"]}], "source": ["idx=5511495\n", "print(df.loc[(LABEL, idx)].shape)\n", "print(df.loc[(LABEL, idx)].head())"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(('BU8888.SC', 5511494),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511494  1653448407  4491.0    0.0      4490.0      4489.0   \n", "          5511494  1653448408  4491.0    9.0      4490.0      4489.0   \n", "          5511494  1653448409  4491.0   55.0      4490.5      4489.5   \n", "          5511494  1653448410  4493.0  120.5      4491.5      4490.5   \n", "          5511494  1653448411  4492.5   69.5      4492.0      4490.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511494  1653448495  4488.5   34.0      4487.5      4486.5   \n", "          5511494  1653448496  4488.0   50.5      4486.5      4485.5   \n", "          5511494  1653448497  4487.0   13.0      4486.5      4485.5   \n", "          5511494  1653448498  4486.0   18.5      4486.5      4485.5   \n", "          5511494  1653448499  4487.5    8.5      4486.5      4485.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511494       74.0       65.0      4491.0      4492.0       73.0   \n", "          5511494       65.5       65.0      4491.0      4492.0       61.5   \n", "          5511494       36.0       69.0      4492.0      4493.0      128.0   \n", "          5511494       27.0       58.0      4492.5      4493.5       16.5   \n", "          5511494       18.0       50.5      4493.0      4494.0       46.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511494       49.0      143.5      4488.5      4489.5       77.0   \n", "          5511494      101.5      130.5      4488.0      4489.0      128.5   \n", "          5511494       76.5      121.0      4487.5      4488.5       68.0   \n", "          5511494       69.5      109.5      4487.5      4488.5       65.0   \n", "          5511494       72.5      122.0      4487.5      4488.5       64.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511494       43.0  \n", "          5511494       43.0  \n", "          5511494       47.5  \n", "          5511494       85.0  \n", "          5511494       48.0  \n", "...                      ...  \n", "          5511494       51.0  \n", "          5511494       35.0  \n", "          5511494       81.0  \n", "          5511494       82.0  \n", "          5511494       80.5  \n", "\n", "[93 rows x 11 columns])\n", "(('BU8888.SC', 5511495),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511495  1653448500  4487.5   7.5      4487.0      4486.0   \n", "          5511495  1653448501  4487.0   4.0      4487.0      4486.0   \n", "          5511495  1653448502  4488.0  68.0      4488.0      4487.0   \n", "          5511495  1653448503  4488.0  29.5      4488.0      4487.0   \n", "          5511495  1653448504  4488.0   4.5      4488.0      4487.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511495  1653448795  4486.0   2.5      4484.5      4483.5   \n", "          5511495  1653448796  4485.5   0.5      4485.0      4484.0   \n", "          5511495  1653448797  4486.0   1.0      4485.0      4484.0   \n", "          5511495  1653448798  4486.0   0.0      4485.0      4484.0   \n", "          5511495  1653448799  4485.5  14.0      4485.5      4484.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511495       30.5      146.0      4488.0      4489.0      129.5   \n", "          5511495       26.0      143.0      4488.0      4489.0      127.0   \n", "          5511495       70.0       30.5      4489.0      4490.0       22.0   \n", "          5511495       60.0       30.0      4489.5      4490.5       44.0   \n", "          5511495       62.5       31.0      4489.0      4490.0       30.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511495       27.5       91.5      4486.0      4487.0       19.5   \n", "          5511495       17.0       44.0      4486.0      4487.0       17.5   \n", "          5511495       13.0       44.0      4486.0      4487.0       17.5   \n", "          5511495       14.0       44.0      4486.0      4487.0       18.0   \n", "          5511495       15.5       37.5      4486.5      4487.5       35.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511495       33.5  \n", "          5511495       33.0  \n", "          5511495       81.0  \n", "          5511495       82.5  \n", "          5511495       88.0  \n", "...                      ...  \n", "          5511495       51.5  \n", "          5511495       52.0  \n", "          5511495       52.0  \n", "          5511495       53.0  \n", "          5511495       56.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511496),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511496  1653448800  4486.0   3.5      4486.0      4485.0   \n", "          5511496  1653448801  4486.0   3.0      4486.0      4485.0   \n", "          5511496  1653448802  4486.0   2.5      4486.0      4485.0   \n", "          5511496  1653448803  4485.5   5.5      4485.5      4484.5   \n", "          5511496  1653448804  4486.0   1.5      4485.0      4484.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511496  1653449095  4485.5   2.0      4485.0      4484.0   \n", "          5511496  1653449096  4485.0   7.5      4485.0      4484.0   \n", "          5511496  1653449097  4485.0   1.0      4485.0      4484.0   \n", "          5511496  1653449098  4485.5   1.0      4485.0      4484.0   \n", "          5511496  1653449099  4486.0   1.0      4485.0      4484.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511496       10.5       28.0      4487.0      4488.0       47.5   \n", "          5511496        6.0       29.5      4487.0      4488.0       46.0   \n", "          5511496        3.5       30.0      4487.0      4488.0       48.5   \n", "          5511496       16.0       39.5      4487.0      4488.0       79.5   \n", "          5511496       31.0       49.0      4486.5      4487.5       44.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511496       23.5       41.0      4486.0      4487.0       45.0   \n", "          5511496       15.5       41.0      4486.0      4487.0       45.0   \n", "          5511496        7.5       41.5      4486.0      4487.0       44.0   \n", "          5511496        7.5       42.0      4486.0      4487.0       43.0   \n", "          5511496        6.0       42.0      4486.0      4487.0       42.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511496       62.0  \n", "          5511496       62.0  \n", "          5511496       62.0  \n", "          5511496       62.0  \n", "          5511496       77.0  \n", "...                      ...  \n", "          5511496       41.0  \n", "          5511496       40.5  \n", "          5511496       40.0  \n", "          5511496       41.5  \n", "          5511496       43.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511497),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511497  1653449100  4486.0   0.0      4485.0      4484.0   \n", "          5511497  1653449101  4486.5  62.5      4485.5      4484.5   \n", "          5511497  1653449102  4486.0   4.5      4486.0      4485.0   \n", "          5511497  1653449103  4486.0   0.0      4486.0      4485.0   \n", "          5511497  1653449104  4486.0   0.0      4486.0      4485.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511497  1653449395  4484.5  43.5      4483.5      4482.5   \n", "          5511497  1653449396  4484.0  10.5      4483.0      4482.0   \n", "          5511497  1653449397  4484.5  29.5      4483.5      4482.5   \n", "          5511497  1653449398  4485.0   5.5      4485.0      4484.0   \n", "          5511497  1653449399  4486.0  13.5      4485.0      4484.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511497        6.5       43.0      4486.0      4487.0       46.0   \n", "          5511497       15.5       40.5      4486.5      4487.5        1.0   \n", "          5511497       37.0       37.0      4487.0      4488.0       15.5   \n", "          5511497       37.0       37.0      4487.0      4488.0       34.0   \n", "          5511497       33.5       43.0      4487.0      4488.0       41.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511497       36.5      289.0      4484.5      4487.0       36.0   \n", "          5511497       51.0      529.0      4484.0      4485.0       49.0   \n", "          5511497       26.0      289.0      4485.0      4487.0        5.5   \n", "          5511497       14.5       15.5      4487.0      4488.0        3.5   \n", "          5511497        5.5       19.0      4487.0      4488.0        3.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511497       43.0  \n", "          5511497       24.0  \n", "          5511497       38.0  \n", "          5511497       38.0  \n", "          5511497       37.5  \n", "...                      ...  \n", "          5511497       13.5  \n", "          5511497       11.5  \n", "          5511497        2.0  \n", "          5511497       17.0  \n", "          5511497       17.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511522),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511522  1653456601  4485.0  47.0      4485.0      4484.0   \n", "          5511522  1653456602  4484.0  93.0      4482.5      4481.5   \n", "          5511522  1653456603  4483.5  55.0      4483.0      4482.0   \n", "          5511522  1653456604  4484.5   9.5      4484.0      4483.0   \n", "          5511522  1653456605  4484.0   6.5      4484.0      4483.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511522  1653456895  4491.0  18.0      4490.0      4489.0   \n", "          5511522  1653456896  4491.0   1.5      4490.0      4489.0   \n", "          5511522  1653456897  4491.0  10.0      4490.0      4489.0   \n", "          5511522  1653456898  4491.0   4.5      4490.0      4489.0   \n", "          5511522  1653456899  4491.0   0.5      4490.0      4489.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511522       24.0       46.0      4486.5      4487.5        5.0   \n", "          5511522      264.5      285.5      4484.0      4485.0       16.0   \n", "          5511522       19.0      509.0      4484.5      4485.5       10.0   \n", "          5511522       20.0       24.0      4485.0      4486.0       45.5   \n", "          5511522        7.0       24.0      4485.0      4486.0       46.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511522      233.5       51.5      4491.0      4492.0        9.0   \n", "          5511522      232.0       51.0      4491.0      4492.0       43.0   \n", "          5511522      231.5       51.5      4491.0      4492.0       35.0   \n", "          5511522      230.0       52.0      4491.0      4492.0       47.0   \n", "          5511522      211.0       52.0      4491.0      4492.0       43.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511522       25.5  \n", "          5511522        5.5  \n", "          5511522        5.5  \n", "          5511522        7.0  \n", "          5511522        5.5  \n", "...                      ...  \n", "          5511522       20.0  \n", "          5511522       21.5  \n", "          5511522       22.5  \n", "          5511522       23.5  \n", "          5511522       24.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511523),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511523  1653456900  4490.0  121.0      4489.5      4488.5   \n", "          5511523  1653456901  4489.5   31.5      4488.0      4487.0   \n", "          5511523  1653456902  4489.0    6.0      4488.0      4487.0   \n", "          5511523  1653456903  4487.5   27.5      4487.0      4486.0   \n", "          5511523  1653456904  4487.0    5.0      4487.0      4486.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511523  1653457195  4488.0    0.0      4487.0      4486.0   \n", "          5511523  1653457196  4487.0    5.0      4487.0      4486.0   \n", "          5511523  1653457197  4487.5    0.5      4487.0      4486.0   \n", "          5511523  1653457198  4487.0    1.5      4487.0      4486.0   \n", "          5511523  1653457199  4488.0   30.5      4487.5      4486.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511523      128.5       47.5      4490.5      4491.5       34.0   \n", "          5511523       38.5       19.5      4489.5      4490.5       37.0   \n", "          5511523       40.0       22.0      4489.0      4490.0       36.0   \n", "          5511523       24.0       51.5      4488.0      4489.0       17.5   \n", "          5511523       17.5       50.0      4488.0      4489.0       34.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511523       39.0      115.0      4488.0      4489.0       43.0   \n", "          5511523       35.0      115.0      4488.0      4489.0       44.0   \n", "          5511523       37.5      115.0      4488.0      4489.0       50.0   \n", "          5511523       36.5      112.5      4488.0      4489.0       49.5   \n", "          5511523       21.5       73.5      4489.0      4490.0       26.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511523       17.0  \n", "          5511523       53.5  \n", "          5511523       34.0  \n", "          5511523       33.0  \n", "          5511523       21.0  \n", "...                      ...  \n", "          5511523       36.0  \n", "          5511523       36.0  \n", "          5511523       36.0  \n", "          5511523       36.0  \n", "          5511523      100.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511524),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511524  1653457200  4488.0   7.5      4488.0      4487.0   \n", "          5511524  1653457201  4488.0  55.5      4488.0      4487.0   \n", "          5511524  1653457202  4488.0  10.5      4488.0      4487.0   \n", "          5511524  1653457203  4488.0   2.5      4488.0      4487.0   \n", "          5511524  1653457204  4488.0   1.0      4488.0      4487.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511524  1653457495  4484.5   7.0      4484.0      4483.0   \n", "          5511524  1653457496  4483.0  18.0      4483.0      4482.0   \n", "          5511524  1653457497  4483.0  13.5      4483.0      4482.0   \n", "          5511524  1653457498  4483.0   8.0      4483.0      4482.0   \n", "          5511524  1653457499  4483.0   8.0      4482.5      4481.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511524        7.5       37.0      4489.0      4490.0       20.0   \n", "          5511524       11.5       38.0      4489.0      4490.0       17.5   \n", "          5511524        7.0       38.0      4489.0      4490.0       32.5   \n", "          5511524       29.0       38.0      4489.0      4490.0       41.0   \n", "          5511524       29.0       38.0      4489.0      4490.0       36.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511524       24.5       36.0      4485.0      4486.0       32.5   \n", "          5511524       23.0       35.5      4484.5      4485.5       25.5   \n", "          5511524       13.0       34.0      4484.0      4485.0       11.0   \n", "          5511524        7.0       34.5      4484.0      4485.0       10.0   \n", "          5511524       19.0       54.0      4483.5      4484.5       11.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511524      100.0  \n", "          5511524       56.5  \n", "          5511524       38.0  \n", "          5511524       38.0  \n", "          5511524       38.0  \n", "...                      ...  \n", "          5511524       74.0  \n", "          5511524       61.5  \n", "          5511524       48.0  \n", "          5511524       48.0  \n", "          5511524       29.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511525),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511525  1653457500  4483.0  20.5      4483.0      4482.0   \n", "          5511525  1653457501  4483.0  13.0      4483.0      4482.0   \n", "          5511525  1653457502  4483.0   6.0      4482.5      4481.5   \n", "          5511525  1653457503  4483.0  31.5      4482.0      4481.0   \n", "          5511525  1653457504  4482.5  15.5      4482.0      4481.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511525  1653457795  4476.5  11.5      4476.5      4475.5   \n", "          5511525  1653457796  4476.0  11.5      4476.0      4475.0   \n", "          5511525  1653457797  4476.0  17.0      4475.5      4474.5   \n", "          5511525  1653457798  4477.0   7.0      4476.0      4475.0   \n", "          5511525  1653457799  4476.0  10.0      4475.0      4474.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511525       22.5       33.5      4484.0      4485.0       11.5   \n", "          5511525       13.5       37.5      4484.0      4485.0       21.0   \n", "          5511525       26.5       54.5      4483.5      4484.5       28.0   \n", "          5511525       36.0       68.0      4483.0      4484.0       16.5   \n", "          5511525       18.5       73.0      4483.0      4484.0       18.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511525       23.0       32.0      4478.0      4479.0       78.5   \n", "          5511525       32.5       28.0      4477.5      4478.5       41.5   \n", "          5511525       23.0       29.0      4476.5      4477.5        8.5   \n", "          5511525        3.0       26.0      4477.0      4478.0       18.5   \n", "          5511525       28.0       30.5      4476.0      4477.0        8.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511525       40.0  \n", "          5511525       41.0  \n", "          5511525       38.5  \n", "          5511525       32.5  \n", "          5511525       34.0  \n", "...                      ...  \n", "          5511525       39.0  \n", "          5511525       56.5  \n", "          5511525       41.0  \n", "          5511525       75.0  \n", "          5511525       30.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511526),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511526  1653457800  4475.5   9.0      4475.0      4474.0   \n", "          5511526  1653457801  4476.0   7.0      4475.0      4474.0   \n", "          5511526  1653457802  4475.5   2.5      4475.0      4474.0   \n", "          5511526  1653457803  4475.0   5.5      4475.0      4474.0   \n", "          5511526  1653457804  4475.0   8.5      4475.0      4474.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511526  1653458095  4472.5   2.5      4472.0      4471.0   \n", "          5511526  1653458096  4473.0   4.5      4472.0      4471.0   \n", "          5511526  1653458097  4473.0  13.0      4473.0      4472.0   \n", "          5511526  1653458098  4473.0   3.0      4473.0      4472.0   \n", "          5511526  1653458099  4474.0   3.5      4473.0      4472.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511526       29.0       31.5      4476.0      4477.0       11.0   \n", "          5511526       22.0       32.0      4476.0      4477.0       12.0   \n", "          5511526       29.0       32.0      4476.0      4477.0       20.0   \n", "          5511526       27.0       33.5      4476.0      4477.0       21.0   \n", "          5511526       14.0       35.0      4476.0      4477.0       39.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511526       38.0      118.0      4473.0      4474.0       32.0   \n", "          5511526       57.0      118.0      4473.0      4474.0       25.5   \n", "          5511526       13.5       75.0      4474.0      4475.0       66.0   \n", "          5511526       28.5       75.5      4474.0      4475.0       66.0   \n", "          5511526       30.5       77.0      4474.0      4475.0       61.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511526       34.5  \n", "          5511526       35.0  \n", "          5511526       35.0  \n", "          5511526       35.5  \n", "          5511526       36.0  \n", "...                      ...  \n", "          5511526       65.5  \n", "          5511526       66.0  \n", "          5511526       55.0  \n", "          5511526       55.0  \n", "          5511526       55.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511527),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511527  1653458100  4473.5   4.0      4473.0      4472.0   \n", "          5511527  1653458101  4474.0   0.5      4473.0      4472.0   \n", "          5511527  1653458102  4473.5   0.5      4473.0      4472.0   \n", "          5511527  1653458103  4473.0   3.0      4473.0      4472.0   \n", "          5511527  1653458104  4474.0   5.0      4473.0      4472.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511527  1653458395  4467.5  33.5      4467.0      4466.0   \n", "          5511527  1653458396  4467.0  14.0      4466.5      4465.5   \n", "          5511527  1653458397  4468.0   9.5      4466.5      4465.5   \n", "          5511527  1653458398  4466.5   7.5      4466.0      4465.0   \n", "          5511527  1653458399  4466.5  12.0      4466.0      4465.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511527       30.0       78.5      4474.0      4475.0       72.0   \n", "          5511527       29.0       78.0      4474.0      4475.0       71.0   \n", "          5511527       28.5       78.0      4474.0      4475.0       73.5   \n", "          5511527       24.5       79.0      4474.0      4475.0       75.0   \n", "          5511527       15.5       79.0      4474.0      4475.0       66.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511527       38.0      133.0      4468.0      4469.0       50.5   \n", "          5511527       71.0      296.5      4468.0      4469.0       67.5   \n", "          5511527       66.5      297.0      4467.5      4468.5       31.0   \n", "          5511527      128.5      460.0      4467.0      4468.0        4.0   \n", "          5511527      126.0      460.0      4467.0      4468.0       12.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511527       55.0  \n", "          5511527       55.0  \n", "          5511527       55.0  \n", "          5511527       55.0  \n", "          5511527       56.0  \n", "...                      ...  \n", "          5511527       69.0  \n", "          5511527       69.0  \n", "          5511527       64.0  \n", "          5511527       60.0  \n", "          5511527       63.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511528),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511528  1653458400  4467.0   23.5      4467.0      4466.0   \n", "          5511528  1653458401  4466.0   20.5      4466.0      4465.0   \n", "          5511528  1653458402  4466.0    6.5      4466.0      4465.0   \n", "          5511528  1653458403  4466.0   77.5      4465.0      4464.0   \n", "          5511528  1653458404  4465.0  200.5      4465.0      4464.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511528  1653458695  4454.5   22.5      4453.0      4452.0   \n", "          5511528  1653458696  4453.0    4.0      4452.5      4451.5   \n", "          5511528  1653458697  4453.5   15.5      4453.0      4452.0   \n", "          5511528  1653458698  4454.0    6.0      4453.0      4452.0   \n", "          5511528  1653458699  4454.0    3.5      4453.0      4452.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511528       10.0      127.5      4468.0      4469.0       65.0   \n", "          5511528      125.5      465.0      4467.0      4468.0       21.5   \n", "          5511528      129.5      464.5      4467.0      4468.0       33.5   \n", "          5511528      464.0      121.5      4466.0      4467.0       14.5   \n", "          5511528      301.5      118.0      4467.0      4468.0       27.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511528       24.0       43.0      4455.0      4456.0       37.5   \n", "          5511528       28.0       92.5      4453.5      4454.5       17.0   \n", "          5511528        9.5       45.0      4454.0      4455.0       17.0   \n", "          5511528       13.5       48.5      4454.0      4455.0       11.0   \n", "          5511528       20.5       55.0      4454.0      4455.0       16.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511528       68.5  \n", "          5511528       65.0  \n", "          5511528       66.5  \n", "          5511528       33.5  \n", "          5511528       81.0  \n", "...                      ...  \n", "          5511528       50.0  \n", "          5511528       37.5  \n", "          5511528       59.5  \n", "          5511528       66.5  \n", "          5511528       60.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511529),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511529  1653458700  4453.5  25.0      4453.0      4452.0   \n", "          5511529  1653458701  4453.5  42.0      4453.5      4452.5   \n", "          5511529  1653458702  4454.0  13.5      4453.0      4452.0   \n", "          5511529  1653458703  4453.5   9.0      4453.0      4452.0   \n", "          5511529  1653458704  4454.0   2.5      4453.0      4452.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511529  1653458995  4442.5  42.0      4442.0      4441.0   \n", "          5511529  1653458996  4442.0   6.5      4442.0      4441.0   \n", "          5511529  1653458997  4442.5  15.5      4442.0      4441.0   \n", "          5511529  1653458998  4442.5  13.0      4442.0      4441.0   \n", "          5511529  1653458999  4443.0  37.0      4442.0      4441.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511529        1.5       55.5      4454.0      4455.0       27.0   \n", "          5511529        1.0       36.5      4454.5      4455.5       36.0   \n", "          5511529       18.0       50.5      4454.0      4455.0       32.0   \n", "          5511529       29.0       50.0      4454.0      4455.0       21.0   \n", "          5511529       48.0       51.5      4454.0      4455.0       29.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511529       82.0       84.5      4443.0      4444.0       27.5   \n", "          5511529       64.5       88.0      4443.0      4444.0       24.5   \n", "          5511529       64.0       92.0      4443.0      4444.0        6.0   \n", "          5511529       57.5       95.0      4444.0      4445.0       86.0   \n", "          5511529       52.5      309.5      4443.0      4444.0       43.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511529       60.5  \n", "          5511529       65.5  \n", "          5511529       63.5  \n", "          5511529       64.5  \n", "          5511529       66.0  \n", "...                      ...  \n", "          5511529       83.0  \n", "          5511529       82.0  \n", "          5511529       84.0  \n", "          5511529       49.5  \n", "          5511529       26.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511530),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511530  1653459000  4442.5  13.5      4442.5      4441.5   \n", "          5511530  1653459001  4443.5  24.5      4443.0      4442.0   \n", "          5511530  1653459002  4443.5  45.5      4443.0      4442.0   \n", "          5511530  1653459003  4444.0  11.5      4443.0      4442.0   \n", "          5511530  1653459004  4443.5   7.5      4443.0      4442.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511530  1653459295  4441.0   8.0      4440.0      4439.0   \n", "          5511530  1653459296  4440.5   6.5      4440.0      4439.0   \n", "          5511530  1653459297  4440.0  12.0      4440.0      4439.0   \n", "          5511530  1653459298  4441.0  12.5      4440.0      4439.0   \n", "          5511530  1653459299  4440.5   1.0      4440.0      4439.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511530       17.0       69.0      4443.5      4444.5       55.5   \n", "          5511530       22.0       70.0      4444.0      4445.0       82.0   \n", "          5511530       45.5       78.0      4444.0      4445.0       66.5   \n", "          5511530       35.5       80.5      4444.0      4445.0       48.0   \n", "          5511530       46.5       81.0      4444.0      4445.0       45.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511530      113.5       79.0      4441.0      4442.0        9.0   \n", "          5511530      102.5       79.0      4441.0      4442.0       26.5   \n", "          5511530       97.0       79.0      4441.0      4442.0       23.0   \n", "          5511530       86.0       79.0      4441.0      4442.0       19.5   \n", "          5511530       78.5       79.0      4441.0      4442.0       27.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511530       69.5  \n", "          5511530       50.0  \n", "          5511530       78.0  \n", "          5511530      105.0  \n", "          5511530      107.0  \n", "...                      ...  \n", "          5511530      169.5  \n", "          5511530      173.5  \n", "          5511530      178.5  \n", "          5511530      181.5  \n", "          5511530      177.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511531),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511531  1653459300  4440.0  34.0      4440.0      4439.0   \n", "          5511531  1653459301  4440.5  43.5      4439.5      4438.5   \n", "          5511531  1653459302  4440.0  31.0      4440.0      4439.0   \n", "          5511531  1653459303  4441.5  29.0      4440.0      4439.0   \n", "          5511531  1653459304  4441.5   2.0      4441.0      4440.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511531  1653459595  4424.0  20.0      4424.0      4423.0   \n", "          5511531  1653459596  4424.5  44.5      4424.0      4423.0   \n", "          5511531  1653459597  4424.5  58.5      4423.0      4422.0   \n", "          5511531  1653459598  4424.0  24.5      4423.0      4422.0   \n", "          5511531  1653459599  4423.0  71.5      4422.5      4421.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511531       27.0       79.0      4441.0      4442.0       50.5   \n", "          5511531       44.5      160.5      4440.5      4441.5       21.5   \n", "          5511531       25.0       79.0      4441.5      4442.5      106.5   \n", "          5511531       64.0       80.0      4441.5      4442.5       86.5   \n", "          5511531       13.0       83.5      4442.0      4443.0      166.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511531      119.5      131.0      4425.0      4426.0       28.5   \n", "          5511531       55.5      132.0      4425.0      4426.0       32.0   \n", "          5511531      115.5      310.0      4424.0      4425.0       56.0   \n", "          5511531      112.0      310.0      4424.0      4425.0       97.5   \n", "          5511531      162.5      308.5      4423.5      4424.5       44.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511531      178.0  \n", "          5511531      116.5  \n", "          5511531      135.0  \n", "          5511531      133.5  \n", "          5511531       98.5  \n", "...                      ...  \n", "          5511531       44.0  \n", "          5511531       44.5  \n", "          5511531       28.5  \n", "          5511531       31.5  \n", "          5511531       62.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511532),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511532  1653459600  4422.0  305.0      4422.0      4421.0   \n", "          5511532  1653459601  4421.5   51.0      4421.0      4420.0   \n", "          5511532  1653459602  4422.0  165.5      4422.0      4421.0   \n", "          5511532  1653459603  4423.0   38.5      4423.0      4422.0   \n", "          5511532  1653459604  4423.5  121.0      4423.5      4422.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511532  1653459895  4437.5    6.5      4437.5      4436.5   \n", "          5511532  1653459896  4437.5    5.5      4437.0      4436.0   \n", "          5511532  1653459897  4437.5    3.0      4437.0      4436.0   \n", "          5511532  1653459898  4437.5   17.0      4436.5      4435.5   \n", "          5511532  1653459899  4438.0    0.5      4436.0      4435.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511532      152.0      203.5      4423.0      4424.0       16.5   \n", "          5511532       54.0     1514.0      4422.0      4423.0       64.0   \n", "          5511532        4.5      743.5      4423.5      4424.5       81.5   \n", "          5511532       62.5       37.0      4424.0      4425.0      100.0   \n", "          5511532       51.5       76.5      4424.5      4425.5       68.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511532       20.5       49.0      4439.0      4440.0       74.0   \n", "          5511532       37.0       56.0      4438.0      4439.0        5.5   \n", "          5511532       31.5       56.0      4438.0      4439.0        6.0   \n", "          5511532       40.5       90.0      4438.0      4439.0       11.5   \n", "          5511532       52.0      124.0      4437.0      4438.0        7.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511532      102.5  \n", "          5511532       42.0  \n", "          5511532       77.0  \n", "          5511532       44.0  \n", "          5511532       35.0  \n", "...                      ...  \n", "          5511532      157.5  \n", "          5511532       76.0  \n", "          5511532       78.5  \n", "          5511532       75.0  \n", "          5511532       31.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511533),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511533  1653459900  4436.5  34.5      4435.5      4434.5   \n", "          5511533  1653459901  4436.5  17.5      4436.0      4435.0   \n", "          5511533  1653459902  4436.5   4.0      4435.0      4434.0   \n", "          5511533  1653459903  4435.0  17.0      4435.0      4434.0   \n", "          5511533  1653459904  4435.0  12.0      4435.0      4434.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511533  1653460195  4445.0  53.5      4444.5      4443.5   \n", "          5511533  1653460196  4444.0  74.5      4442.5      4441.5   \n", "          5511533  1653460197  4444.5   8.0      4444.0      4443.0   \n", "          5511533  1653460198  4444.5  27.0      4444.0      4443.0   \n", "          5511533  1653460199  4445.0  18.5      4444.0      4443.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511533       83.0       84.5      4437.0      4438.0       10.0   \n", "          5511533        9.5      112.0      4437.0      4438.0       21.0   \n", "          5511533      111.5       47.0      4436.5      4437.5       14.5   \n", "          5511533       81.5       47.0      4436.0      4437.0       21.5   \n", "          5511533       65.5       47.0      4436.0      4437.0       31.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511533        8.0       32.0      4445.5      4446.5       30.5   \n", "          5511533      230.5      237.0      4444.0      4445.0        4.5   \n", "          5511533        9.0       24.0      4445.0      4446.0       16.5   \n", "          5511533       11.0       33.0      4445.0      4446.0        7.5   \n", "          5511533       18.5       35.0      4445.0      4446.0        3.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511533       37.5  \n", "          5511533       42.5  \n", "          5511533       31.0  \n", "          5511533       19.0  \n", "          5511533       19.0  \n", "...                      ...  \n", "          5511533      105.5  \n", "          5511533       14.5  \n", "          5511533       64.0  \n", "          5511533       69.5  \n", "          5511533       80.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511534),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511534  1653460200  4444.5  33.5      4444.5      4443.5   \n", "          5511534  1653460201  4445.0  32.5      4444.5      4443.5   \n", "          5511534  1653460202  4445.0  36.0      4445.0      4444.0   \n", "          5511534  1653460203  4445.5  49.0      4444.5      4443.5   \n", "          5511534  1653460204  4445.5  21.0      4445.0      4444.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511534  1653460495  4464.0  37.0      4464.0      4463.0   \n", "          5511534  1653460496  4464.5  46.5      4463.0      4462.0   \n", "          5511534  1653460497  4464.5  48.0      4464.0      4463.0   \n", "          5511534  1653460498  4464.0  12.0      4463.5      4462.5   \n", "          5511534  1653460499  4464.0   7.5      4463.0      4462.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511534       18.0       24.5      4445.5      4446.5       47.0   \n", "          5511534        2.0       25.0      4445.5      4446.5       45.5   \n", "          5511534       13.5       16.0      4446.0      4447.0       52.0   \n", "          5511534       10.5       31.5      4445.5      4446.5       30.5   \n", "          5511534       13.0       23.5      4446.0      4447.0       22.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511534       60.0       15.0      4465.0      4466.0      136.0   \n", "          5511534       14.5       59.5      4464.0      4465.0        4.5   \n", "          5511534       14.5       27.0      4465.0      4466.0       63.0   \n", "          5511534       30.0       33.5      4464.5      4465.5       73.5   \n", "          5511534       33.0       37.0      4464.0      4465.0       84.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511534      117.0  \n", "          5511534      123.5  \n", "          5511534      159.0  \n", "          5511534       87.5  \n", "          5511534      156.0  \n", "...                      ...  \n", "          5511534      129.0  \n", "          5511534      128.5  \n", "          5511534      130.5  \n", "          5511534      102.0  \n", "          5511534       85.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511535),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511535  1653460500  4463.0  12.0      4463.0      4462.0   \n", "          5511535  1653460501  4463.5  33.5      4463.0      4462.0   \n", "          5511535  1653460502  4463.0  12.5      4463.0      4462.0   \n", "          5511535  1653460503  4463.5  17.0      4463.0      4462.0   \n", "          5511535  1653460504  4463.5   2.0      4463.0      4462.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511535  1653460795  4471.0   8.5      4470.0      4469.0   \n", "          5511535  1653460796  4470.5   6.0      4470.0      4469.0   \n", "          5511535  1653460797  4470.5  14.0      4470.5      4469.5   \n", "          5511535  1653460798  4470.5  24.0      4470.5      4469.5   \n", "          5511535  1653460799  4470.5  12.0      4470.0      4469.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511535       28.5       37.0      4464.0      4465.0       81.5   \n", "          5511535       26.5       37.5      4464.0      4465.0       55.0   \n", "          5511535       25.5       38.0      4464.0      4465.0       24.0   \n", "          5511535       19.5       38.0      4464.0      4465.0        3.5   \n", "          5511535       25.0       38.0      4464.0      4465.0       16.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511535       49.0       92.0      4471.0      4472.0       29.0   \n", "          5511535       47.5       87.0      4471.0      4472.0       18.5   \n", "          5511535       26.0       61.5      4471.5      4472.5        7.5   \n", "          5511535       27.5       68.0      4472.0      4473.0       32.0   \n", "          5511535       48.0       82.0      4471.0      4472.0       32.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511535       86.5  \n", "          5511535       87.5  \n", "          5511535       92.5  \n", "          5511535       96.0  \n", "          5511535       96.0  \n", "...                      ...  \n", "          5511535        2.0  \n", "          5511535        6.5  \n", "          5511535       37.0  \n", "          5511535       64.0  \n", "          5511535       34.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511536),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511536  1653460800  4469.5  36.5      4469.0      4468.0   \n", "          5511536  1653460801  4469.5  56.0      4468.5      4467.5   \n", "          5511536  1653460802  4468.5  24.5      4468.0      4467.0   \n", "          5511536  1653460803  4468.0  20.5      4467.5      4466.5   \n", "          5511536  1653460804  4468.0  15.0      4468.0      4467.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511536  1653461095  4466.0   1.0      4465.0      4464.0   \n", "          5511536  1653461096  4465.5   1.5      4465.0      4464.0   \n", "          5511536  1653461097  4465.5  17.5      4464.5      4463.5   \n", "          5511536  1653461098  4464.5   8.5      4464.0      4463.0   \n", "          5511536  1653461099  4464.0  12.5      4464.0      4463.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511536       74.0       50.5      4470.5      4471.5       23.0   \n", "          5511536       57.5       42.0      4470.0      4471.0       28.5   \n", "          5511536       21.5       32.5      4469.0      4470.0       19.5   \n", "          5511536       27.5       37.0      4468.5      4469.5       16.0   \n", "          5511536        5.0       28.5      4469.0      4470.0       15.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511536       35.0       61.0      4466.0      4467.0       21.0   \n", "          5511536       23.5       61.0      4466.0      4467.0       28.5   \n", "          5511536       36.0       40.0      4465.5      4466.5       47.0   \n", "          5511536       54.5       20.0      4465.0      4466.0       83.5   \n", "          5511536       37.0       20.0      4465.0      4466.0       93.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511536       49.0  \n", "          5511536       56.5  \n", "          5511536       42.0  \n", "          5511536       32.5  \n", "          5511536       41.0  \n", "...                      ...  \n", "          5511536       33.5  \n", "          5511536       34.0  \n", "          5511536       28.5  \n", "          5511536       26.0  \n", "          5511536       27.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511537),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511537  1653461100  4463.5  26.5      4462.5      4461.5   \n", "          5511537  1653461101  4464.0  13.0      4463.0      4462.0   \n", "          5511537  1653461102  4464.0   3.5      4463.0      4462.0   \n", "          5511537  1653461103  4463.5   2.0      4463.0      4462.0   \n", "          5511537  1653461104  4463.0  35.0      4463.0      4462.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511537  1653461395  4477.5  59.5      4477.0      4476.0   \n", "          5511537  1653461396  4477.5  20.0      4477.0      4476.0   \n", "          5511537  1653461397  4477.0  20.0      4476.0      4475.0   \n", "          5511537  1653461398  4476.5   8.5      4476.0      4475.0   \n", "          5511537  1653461399  4477.5  60.0      4477.0      4476.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511537       47.0       60.0      4464.0      4465.0       48.0   \n", "          5511537        9.5       79.5      4464.0      4465.0       23.0   \n", "          5511537       24.0       78.0      4464.0      4465.0       19.5   \n", "          5511537       23.5       77.0      4464.0      4465.0       19.5   \n", "          5511537       12.5       84.0      4464.0      4465.0       21.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511537       25.0       59.0      4478.0      4479.0       22.0   \n", "          5511537        2.0       53.0      4478.0      4479.0       38.5   \n", "          5511537       48.0       58.0      4477.0      4478.0        7.0   \n", "          5511537       46.5       58.0      4477.0      4478.0        9.5   \n", "          5511537       12.0       45.0      4478.0      4479.0       18.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511537       27.0  \n", "          5511537      103.0  \n", "          5511537      103.0  \n", "          5511537      104.0  \n", "          5511537      105.0  \n", "...                      ...  \n", "          5511537       19.5  \n", "          5511537       33.0  \n", "          5511537       41.5  \n", "          5511537       44.5  \n", "          5511537       43.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511538),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511538  1653461400  4476.5  25.5      4476.5      4475.5   \n", "          5511538  1653461401  4476.5  25.0      4476.0      4475.0   \n", "          5511538  1653461402  4476.0  16.5      4476.0      4475.0   \n", "          5511538  1653461403  4477.5  67.0      4477.5      4476.5   \n", "          5511538  1653461404  4478.5  15.5      4479.0      4478.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511538  1653461695  4477.5   3.0      4477.0      4476.0   \n", "          5511538  1653461696  4477.0   6.5      4477.0      4476.0   \n", "          5511538  1653461697  4477.5   7.0      4477.0      4476.0   \n", "          5511538  1653461698  4477.0   7.0      4477.0      4476.0   \n", "          5511538  1653461699  4477.5  23.0      4476.5      4475.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511538       13.0       43.0      4477.5      4478.5       40.5   \n", "          5511538       14.5       61.5      4477.0      4478.0       38.5   \n", "          5511538        9.5       64.0      4477.0      4478.0       36.0   \n", "          5511538      241.5      229.5      4479.0      4480.0       75.0   \n", "          5511538        7.0       59.0      4480.0      4481.0       97.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511538       25.5       52.0      4478.0      4479.0       26.0   \n", "          5511538       28.5       54.0      4478.0      4479.0       13.0   \n", "          5511538       21.5       56.0      4478.0      4479.0       13.0   \n", "          5511538       34.0       56.0      4478.0      4479.0       16.5   \n", "          5511538       44.0      248.0      4478.0      4479.0       29.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511538       39.0  \n", "          5511538       27.5  \n", "          5511538       21.5  \n", "          5511538       51.5  \n", "          5511538       52.0  \n", "...                      ...  \n", "          5511538       71.0  \n", "          5511538       71.0  \n", "          5511538       71.0  \n", "          5511538       78.0  \n", "          5511538       78.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511539),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511539  1653461700  4477.0  10.0      4476.0      4475.0   \n", "          5511539  1653461701  4477.0  10.5      4476.0      4475.0   \n", "          5511539  1653461702  4476.5  15.5      4476.0      4475.0   \n", "          5511539  1653461703  4476.5  11.5      4476.0      4475.0   \n", "          5511539  1653461704  4476.0  17.0      4476.0      4475.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511539  1653461995  4474.5  50.5      4474.0      4473.0   \n", "          5511539  1653461996  4475.0  30.0      4474.0      4473.0   \n", "          5511539  1653461997  4475.0  36.0      4473.5      4472.5   \n", "          5511539  1653461998  4473.5  21.5      4472.0      4471.0   \n", "          5511539  1653461999  4475.0   4.0      4474.0      4473.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511539       51.5      442.5      4477.0      4478.0       32.0   \n", "          5511539       40.5      445.0      4477.0      4478.0       49.0   \n", "          5511539       51.5      446.5      4477.0      4478.0       30.5   \n", "          5511539       50.5      448.0      4477.0      4478.0        5.0   \n", "          5511539       34.0       42.0      4477.0      4478.0       13.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511539       22.5       16.5      4475.5      4476.5       21.5   \n", "          5511539       12.0       16.0      4476.0      4477.0       30.0   \n", "          5511539       18.5       21.0      4475.5      4477.5       48.0   \n", "          5511539       22.0       52.0      4474.0      4475.0        1.5   \n", "          5511539        8.0        2.0      4476.0      4477.0        1.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511539       34.5  \n", "          5511539       36.0  \n", "          5511539       36.5  \n", "          5511539       37.0  \n", "          5511539       44.5  \n", "...                      ...  \n", "          5511539       63.0  \n", "          5511539       95.0  \n", "          5511539      147.5  \n", "          5511539        1.0  \n", "          5511539       73.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511612),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511612  1653483601  4480.5  129.0      4479.0      4478.0   \n", "          5511612  1653483602  4480.0   95.5      4478.0      4477.0   \n", "          5511612  1653483603  4480.0   77.0      4477.5      4476.5   \n", "          5511612  1653483604  4478.5   46.5      4478.5      4477.0   \n", "          5511612  1653483605  4480.5  122.5      4479.5      4478.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511612  1653483895  4436.0   48.0      4435.0      4434.0   \n", "          5511612  1653483896  4436.0   40.5      4435.5      4434.5   \n", "          5511612  1653483897  4436.0  203.0      4435.5      4434.5   \n", "          5511612  1653483898  4435.5   42.0      4435.0      4434.0   \n", "          5511612  1653483899  4435.0   49.5      4434.5      4433.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511612       28.5       12.0      4480.5      4481.5      113.5   \n", "          5511612       41.5       22.0      4480.0      4481.0       67.0   \n", "          5511612       23.0       20.0      4480.0      4481.0      103.0   \n", "          5511612       10.0       34.5      4479.5      4480.5       49.5   \n", "          5511612       43.0       12.5      4480.5      4481.5       50.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511612       85.5       29.0      4436.5      4437.5      300.5   \n", "          5511612       47.5       60.5      4437.0      4438.0      578.5   \n", "          5511612       38.5       55.0      4436.5      4437.5      201.0   \n", "          5511612       57.0       38.0      4436.0      4437.0        5.0   \n", "          5511612       45.0       70.5      4435.5      4436.5        9.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511612      109.5  \n", "          5511612      119.0  \n", "          5511612      123.5  \n", "          5511612      112.5  \n", "          5511612      127.0  \n", "...                      ...  \n", "          5511612      306.0  \n", "          5511612       15.0  \n", "          5511612      174.5  \n", "          5511612      335.5  \n", "          5511612      177.5  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511613),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511613  1653483900  4436.5   94.5      4436.0      4435.0   \n", "          5511613  1653483901  4436.0  223.5      4436.0      4435.0   \n", "          5511613  1653483902  4437.5   19.5      4437.5      4436.5   \n", "          5511613  1653483903  4439.5  176.0      4439.5      4438.5   \n", "          5511613  1653483904  4439.5   80.5      4438.0      4437.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511613  1653484195  4447.0   46.0      4446.5      4445.5   \n", "          5511613  1653484196  4447.5    8.5      4447.0      4446.0   \n", "          5511613  1653484197  4447.0    5.5      4446.5      4445.5   \n", "          5511613  1653484198  4447.0   11.5      4446.0      4445.0   \n", "          5511613  1653484199  4447.0    2.0      4446.0      4445.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511613       11.0       14.5      4437.0      4438.0      322.5   \n", "          5511613       49.5       82.0      4437.0      4438.0      121.5   \n", "          5511613      127.0      141.0      4439.0      4440.0       15.5   \n", "          5511613       22.0       45.0      4440.5      4441.5       35.5   \n", "          5511613       21.5       51.5      4439.5      4440.5       73.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511613       24.5      123.5      4448.0      4449.0       59.5   \n", "          5511613       13.0       54.0      4448.0      4449.0       61.0   \n", "          5511613       28.5      125.0      4447.5      4448.5       32.0   \n", "          5511613       42.0      192.5      4447.0      4448.0        3.5   \n", "          5511613       41.0      192.0      4447.0      4448.0        9.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511613       16.0  \n", "          5511613       12.0  \n", "          5511613       26.5  \n", "          5511613       80.5  \n", "          5511613       83.5  \n", "...                      ...  \n", "          5511613       69.0  \n", "          5511613       69.0  \n", "          5511613       67.0  \n", "          5511613       64.0  \n", "          5511613       63.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511614),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511614  1653484200  4447.0  15.0      4446.5      4445.5   \n", "          5511614  1653484201  4447.0  14.0      4446.5      4445.5   \n", "          5511614  1653484202  4446.5   7.0      4446.0      4445.0   \n", "          5511614  1653484203  4446.0  38.5      4445.5      4444.5   \n", "          5511614  1653484204  4446.0  18.5      4445.0      4444.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511614  1653484495  4435.0   7.5      4435.0      4434.0   \n", "          5511614  1653484496  4436.0  18.5      4435.0      4434.0   \n", "          5511614  1653484497  4435.0   2.0      4434.0      4433.0   \n", "          5511614  1653484498  4435.0   6.5      4434.0      4433.0   \n", "          5511614  1653484499  4435.0   9.5      4434.0      4433.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511614       33.0      121.5      4447.5      4448.5       36.0   \n", "          5511614       38.0      122.5      4448.0      4449.0       65.5   \n", "          5511614       54.0      190.0      4447.0      4448.0        9.0   \n", "          5511614      117.0      128.0      4446.5      4447.5       28.5   \n", "          5511614      172.5       68.0      4446.0      4447.0      137.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511614       25.5       31.0      4436.0      4437.0       25.0   \n", "          5511614       14.5       32.0      4436.0      4437.0      167.5   \n", "          5511614       17.0       63.0      4435.0      4436.0       36.5   \n", "          5511614       10.5       62.0      4435.0      4436.0       45.0   \n", "          5511614       39.5       62.0      4435.0      4436.0       24.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511614       66.0  \n", "          5511614       65.0  \n", "          5511614       72.0  \n", "          5511614       59.0  \n", "          5511614       77.0  \n", "...                      ...  \n", "          5511614       43.0  \n", "          5511614       44.0  \n", "          5511614      315.0  \n", "          5511614      319.0  \n", "          5511614      320.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511615),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511615  1653484500  4434.5  34.5      4434.0      4433.0   \n", "          5511615  1653484501  4434.0  13.0      4433.0      4432.0   \n", "          5511615  1653484502  4434.0  23.5      4433.0      4432.0   \n", "          5511615  1653484503  4434.0  39.0      4433.0      4432.0   \n", "          5511615  1653484504  4433.5  14.0      4432.5      4431.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511615  1653484795  4441.0  29.0      4439.5      4438.5   \n", "          5511615  1653484796  4441.0   0.5      4439.0      4438.0   \n", "          5511615  1653484797  4439.0   6.5      4439.0      4438.0   \n", "          5511615  1653484798  4439.5   9.5      4439.5      4438.5   \n", "          5511615  1653484799  4440.0   9.0      4440.0      4439.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511615       24.0       62.0      4435.0      4436.0       16.0   \n", "          5511615       57.0      132.0      4434.5      4435.5       45.5   \n", "          5511615       59.0      131.0      4434.0      4435.0      199.0   \n", "          5511615       11.0      115.0      4434.0      4435.0      333.5   \n", "          5511615       50.5      111.0      4433.5      4434.5      261.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511615       46.5       53.5      4441.0      4442.0       22.0   \n", "          5511615       41.0       77.0      4440.0      4441.0       17.0   \n", "          5511615       36.5       77.0      4440.0      4441.0       15.0   \n", "          5511615       30.5       69.0      4440.5      4441.5       28.5   \n", "          5511615       45.0       61.5      4441.0      4442.0       33.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511615      320.0  \n", "          5511615      201.0  \n", "          5511615       78.0  \n", "          5511615       79.0  \n", "          5511615      275.0  \n", "...                      ...  \n", "          5511615       33.5  \n", "          5511615       42.5  \n", "          5511615       42.0  \n", "          5511615       38.5  \n", "          5511615       33.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511616),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511616  1653484800  4440.0  19.5      4440.0      4439.0   \n", "          5511616  1653484801  4441.0   4.0      4441.0      4440.0   \n", "          5511616  1653484802  4441.5  23.0      4441.5      4440.5   \n", "          5511616  1653484803  4442.0  26.5      4442.0      4441.0   \n", "          5511616  1653484804  4443.5  32.0      4443.0      4442.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511616  1653485095  4434.0   2.5      4433.0      4432.0   \n", "          5511616  1653485096  4434.0   2.5      4433.0      4432.0   \n", "          5511616  1653485097  4433.5   3.5      4433.0      4432.0   \n", "          5511616  1653485098  4433.5   3.0      4433.0      4432.0   \n", "          5511616  1653485099  4432.5  31.5      4432.5      4431.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511616       39.5       62.5      4441.5      4442.5       18.5   \n", "          5511616       25.0       41.0      4442.0      4443.0       30.0   \n", "          5511616       42.5       55.5      4443.0      4444.0       56.5   \n", "          5511616       38.0       47.5      4443.0      4444.0       58.5   \n", "          5511616       37.5       49.0      4444.0      4445.0       36.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511616       46.0       97.0      4434.0      4435.0       64.5   \n", "          5511616       49.5       98.0      4434.0      4435.0       57.0   \n", "          5511616       50.0       98.0      4434.0      4435.0       56.0   \n", "          5511616       43.0      104.5      4434.0      4435.0       61.5   \n", "          5511616       49.5      103.0      4433.5      4434.5       36.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511616       44.5  \n", "          5511616       56.0  \n", "          5511616       36.5  \n", "          5511616       35.5  \n", "          5511616       27.0  \n", "...                      ...  \n", "          5511616       84.0  \n", "          5511616       83.0  \n", "          5511616       83.0  \n", "          5511616       83.0  \n", "          5511616       79.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511617),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511617  1653485100  4433.5  63.5      4433.5      4432.0   \n", "          5511617  1653485101  4433.0  11.5      4433.0      4432.0   \n", "          5511617  1653485102  4433.0   2.0      4433.0      4432.0   \n", "          5511617  1653485103  4433.5  29.0      4433.5      4432.5   \n", "          5511617  1653485104  4434.0  16.5      4434.0      4433.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511617  1653485395  4440.5   6.5      4440.5      4439.5   \n", "          5511617  1653485396  4441.5   6.0      4441.5      4440.5   \n", "          5511617  1653485397  4442.5  10.0      4442.0      4441.0   \n", "          5511617  1653485398  4442.5  17.5      4441.5      4440.5   \n", "          5511617  1653485399  4442.0   8.5      4441.0      4440.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511617       12.0       87.0      4434.5      4435.5       76.5   \n", "          5511617        8.5       89.0      4434.0      4435.0       45.0   \n", "          5511617       24.5       89.0      4434.0      4435.0       69.5   \n", "          5511617       21.0       61.5      4434.5      4435.5       59.5   \n", "          5511617       22.5       34.0      4435.0      4436.0      115.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511617       22.0       47.0      4441.5      4442.5        8.5   \n", "          5511617       23.0       49.0      4442.5      4443.5       41.0   \n", "          5511617       10.0       41.5      4443.0      4444.0       71.0   \n", "          5511617       27.0       49.0      4442.5      4443.5       40.5   \n", "          5511617       43.0       57.0      4442.5      4443.5       25.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511617       72.0  \n", "          5511617      101.5  \n", "          5511617       87.0  \n", "          5511617       74.0  \n", "          5511617       62.0  \n", "...                      ...  \n", "          5511617      165.0  \n", "          5511617       68.5  \n", "          5511617       65.0  \n", "          5511617       57.0  \n", "          5511617       59.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511618),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511618  1653485400  4440.0  62.5      4440.0      4439.0   \n", "          5511618  1653485401  4441.0  21.0      4440.0      4439.0   \n", "          5511618  1653485402  4440.0   2.0      4440.0      4439.0   \n", "          5511618  1653485403  4441.0  36.0      4440.0      4439.0   \n", "          5511618  1653485404  4441.0  25.0      4440.5      4439.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511618  1653485695  4446.5   0.5      4446.0      4445.0   \n", "          5511618  1653485696  4446.0   4.0      4446.0      4445.0   \n", "          5511618  1653485697  4446.5  10.0      4445.5      4444.5   \n", "          5511618  1653485698  4445.0   5.0      4445.0      4444.0   \n", "          5511618  1653485699  4445.5  21.5      4444.0      4443.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511618       15.5       64.5      4441.0      4442.0       17.5   \n", "          5511618        7.5       38.0      4441.5      4442.5       36.5   \n", "          5511618       31.5       36.5      4441.0      4442.0       60.0   \n", "          5511618       28.5       47.0      4441.0      4442.0       35.5   \n", "          5511618       59.0       67.0      4441.5      4442.5       18.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511618       12.0       78.0      4447.0      4448.0       25.5   \n", "          5511618       12.5       78.0      4447.0      4448.0       34.5   \n", "          5511618       42.5       58.5      4446.5      4447.5       67.5   \n", "          5511618       36.5       37.5      4446.0      4447.0      101.0   \n", "          5511618       45.0       41.0      4445.5      4446.5       50.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511618       31.5  \n", "          5511618       58.0  \n", "          5511618       50.5  \n", "          5511618       51.0  \n", "          5511618       61.0  \n", "...                      ...  \n", "          5511618       45.0  \n", "          5511618       45.0  \n", "          5511618       44.0  \n", "          5511618       44.0  \n", "          5511618       75.5  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511619),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511619  1653485700  4445.0   3.0      4444.0      4443.0   \n", "          5511619  1653485701  4444.5   5.0      4444.5      4443.5   \n", "          5511619  1653485702  4444.0   0.0      4445.0      4444.0   \n", "          5511619  1653485703  4445.0  12.5      4444.0      4443.0   \n", "          5511619  1653485704  4445.0   0.0      4444.0      4443.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511619  1653485995  4452.0   3.0      4452.0      4451.0   \n", "          5511619  1653485996  4452.0   4.5      4451.0      4450.0   \n", "          5511619  1653485997  4452.0  11.5      4451.0      4450.0   \n", "          5511619  1653485998  4451.0   0.0      4451.5      4450.5   \n", "          5511619  1653485999  4451.0   0.0      4452.0      4451.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511619       52.0       41.0      4445.5      4446.5       55.0   \n", "          5511619       25.5       46.0      4446.0      4447.0      107.0   \n", "          5511619        1.0       51.0      4446.0      4447.0      108.0   \n", "          5511619       45.5       41.0      4445.0      4446.0       12.5   \n", "          5511619       52.5       40.0      4445.0      4446.0       21.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511619        8.0       40.5      4453.0      4454.0       45.5   \n", "          5511619       40.5      129.0      4452.0      4453.0        1.0   \n", "          5511619       69.5      128.5      4453.0      4454.0       42.5   \n", "          5511619       43.0      106.0      4453.0      4454.0       42.0   \n", "          5511619       17.0       71.5      4453.0      4454.0       45.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511619       77.0  \n", "          5511619       47.0  \n", "          5511619       50.0  \n", "          5511619      107.5  \n", "          5511619      107.0  \n", "...                      ...  \n", "          5511619       96.0  \n", "          5511619       46.0  \n", "          5511619       99.0  \n", "          5511619      102.0  \n", "          5511619      102.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511620),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511620  1653486000  4452.0  32.5      4452.5      4451.5   \n", "          5511620  1653486001  4453.5  61.5      4454.0      4453.0   \n", "          5511620  1653486002  4455.0  42.5      4454.0      4453.0   \n", "          5511620  1653486003  4455.5  29.5      4454.5      4453.5   \n", "          5511620  1653486004  4456.0   9.0      4455.0      4454.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511620  1653486295  4465.5  21.5      4464.5      4463.5   \n", "          5511620  1653486296  4465.0   7.5      4464.0      4463.0   \n", "          5511620  1653486297  4465.0  12.5      4464.5      4463.5   \n", "          5511620  1653486298  4465.5   7.5      4465.0      4464.0   \n", "          5511620  1653486299  4465.0   9.0      4464.5      4463.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511620       88.0       56.5      4453.5      4454.5       65.0   \n", "          5511620       35.0      149.5      4455.0      4456.0      104.0   \n", "          5511620       59.0      151.5      4455.5      4456.5       68.0   \n", "          5511620       43.0      109.0      4456.0      4457.0       29.0   \n", "          5511620       61.0       59.0      4456.5      4457.5       38.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511620       32.0       33.0      4465.5      4466.5       52.0   \n", "          5511620       29.0       33.0      4465.0      4466.0       16.0   \n", "          5511620       21.0       34.5      4465.5      4466.5       55.5   \n", "          5511620       18.5       36.0      4466.0      4467.0       79.5   \n", "          5511620       24.0       34.0      4465.5      4466.5       41.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511620      115.0  \n", "          5511620       69.0  \n", "          5511620       67.5  \n", "          5511620       66.0  \n", "          5511620      147.5  \n", "...                      ...  \n", "          5511620       82.0  \n", "          5511620       87.0  \n", "          5511620       82.0  \n", "          5511620       77.0  \n", "          5511620       79.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511621),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511621  1653486300  4464.5   5.5      4464.0      4463.0   \n", "          5511621  1653486301  4465.0   4.5      4464.0      4463.0   \n", "          5511621  1653486302  4465.0   8.0      4465.0      4464.0   \n", "          5511621  1653486303  4464.0   7.5      4464.5      4463.5   \n", "          5511621  1653486304  4465.0   7.0      4464.0      4463.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511621  1653486595  4452.5   1.5      4451.5      4450.5   \n", "          5511621  1653486596  4452.0  18.5      4452.0      4450.5   \n", "          5511621  1653486597  4453.0   3.5      4452.0      4451.0   \n", "          5511621  1653486598  4452.5   2.0      4452.0      4451.0   \n", "          5511621  1653486599  4452.0   2.5      4452.0      4451.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511621       26.0       33.0      4465.0      4466.0        7.5   \n", "          5511621       25.0       33.0      4465.0      4466.0        9.5   \n", "          5511621       12.0       22.5      4466.0      4467.0       93.0   \n", "          5511621       12.0       25.5      4466.0      4467.0       97.0   \n", "          5511621       16.0       34.0      4465.5      4466.5       55.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511621       57.5      178.5      4453.0      4454.0       39.0   \n", "          5511621       58.5      176.5      4453.5      4454.5       46.0   \n", "          5511621        4.5      117.0      4453.0      4454.0        7.5   \n", "          5511621        6.5      117.5      4453.0      4454.0        8.0   \n", "          5511621       25.0      113.5      4453.0      4454.0       12.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511621       87.5  \n", "          5511621       94.0  \n", "          5511621       77.0  \n", "          5511621       77.0  \n", "          5511621       89.5  \n", "...                      ...  \n", "          5511621       48.0  \n", "          5511621       80.5  \n", "          5511621       48.0  \n", "          5511621       48.0  \n", "          5511621       48.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511622),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511622  1653486600  4453.0  18.5      4452.0      4451.0   \n", "          5511622  1653486601  4453.0   5.5      4453.0      4452.0   \n", "          5511622  1653486602  4453.0   5.5      4452.0      4451.0   \n", "          5511622  1653486603  4453.5   3.5      4453.0      4452.0   \n", "          5511622  1653486604  4454.0  29.5      4453.5      4452.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511622  1653486895  4458.0   3.0      4457.0      4456.0   \n", "          5511622  1653486896  4458.0  76.0      4458.0      4457.0   \n", "          5511622  1653486897  4458.0   3.5      4458.0      4457.0   \n", "          5511622  1653486898  4458.5   8.5      4458.0      4457.0   \n", "          5511622  1653486899  4458.0  18.0      4457.0      4456.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511622       40.0      109.0      4453.5      4454.5       24.5   \n", "          5511622        5.5       49.0      4454.0      4455.0       53.5   \n", "          5511622       64.5      109.0      4453.5      4454.5       28.0   \n", "          5511622       11.0       88.0      4454.0      4455.0       55.0   \n", "          5511622      124.5      103.0      4455.0      4456.0       88.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511622       74.0        5.0      4458.0      4459.0       26.0   \n", "          5511622       15.5       73.0      4459.0      4460.0       38.5   \n", "          5511622       22.0       73.0      4459.0      4460.0       48.0   \n", "          5511622       19.0       73.0      4459.0      4460.0       36.0   \n", "          5511622       73.0       13.0      4459.0      4460.0       35.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511622       79.0  \n", "          5511622      109.5  \n", "          5511622       82.0  \n", "          5511622      109.0  \n", "          5511622       59.0  \n", "...                      ...  \n", "          5511622       29.0  \n", "          5511622       64.0  \n", "          5511622       74.5  \n", "          5511622       75.0  \n", "          5511622       75.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511623),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511623  1653486900  4459.0   5.0      4457.0      4456.0   \n", "          5511623  1653486901  4457.0   2.5      4457.0      4456.0   \n", "          5511623  1653486902  4459.0   3.5      4457.0      4456.0   \n", "          5511623  1653486903  4458.0   1.0      4457.0      4456.0   \n", "          5511623  1653486904  4459.0  33.0      4457.0      4456.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511623  1653487195  4465.5  20.5      4465.5      4464.5   \n", "          5511623  1653487196  4466.5   2.5      4466.0      4465.0   \n", "          5511623  1653487197  4466.5   1.0      4466.0      4465.0   \n", "          5511623  1653487198  4466.5   1.0      4466.0      4465.0   \n", "          5511623  1653487199  4466.0  10.5      4465.5      4464.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511623       74.0       13.0      4458.5      4459.5       22.5   \n", "          5511623       71.0       13.0      4459.0      4460.0       45.0   \n", "          5511623       71.0       13.0      4459.0      4460.0       46.0   \n", "          5511623       71.5       14.0      4459.0      4460.0       58.5   \n", "          5511623       70.0       14.5      4458.5      4459.5        9.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511623       35.0       67.5      4466.5      4467.5       24.0   \n", "          5511623       18.0       57.0      4467.0      4468.0       41.5   \n", "          5511623       20.0       57.0      4467.0      4468.0       40.0   \n", "          5511623       19.5       57.0      4467.0      4468.0       42.0   \n", "          5511623       38.5       68.5      4467.0      4468.0       36.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511623       60.0  \n", "          5511623       75.0  \n", "          5511623       75.0  \n", "          5511623       75.0  \n", "          5511623       54.0  \n", "...                      ...  \n", "          5511623       91.0  \n", "          5511623      150.0  \n", "          5511623      150.0  \n", "          5511623      150.0  \n", "          5511623      150.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511624),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511624  1653487200  4466.5  19.0      4466.5      4465.5   \n", "          5511624  1653487201  4467.0  82.0      4467.0      4466.0   \n", "          5511624  1653487202  4468.0  13.5      4468.0      4467.0   \n", "          5511624  1653487203  4468.5   5.5      4468.0      4467.0   \n", "          5511624  1653487204  4468.0  13.5      4467.0      4466.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511624  1653487495  4469.0   0.0      4468.0      4467.0   \n", "          5511624  1653487496  4469.0   5.5      4468.0      4467.0   \n", "          5511624  1653487497  4468.5   6.0      4468.0      4467.0   \n", "          5511624  1653487498  4469.0   2.0      4468.0      4467.0   \n", "          5511624  1653487499  4468.0  12.0      4468.0      4467.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511624        8.5       55.5      4467.5      4468.5       90.5   \n", "          5511624       53.0       55.5      4468.0      4469.0       81.5   \n", "          5511624       16.0       49.0      4469.0      4470.0       70.5   \n", "          5511624       26.0       50.0      4469.0      4470.0       77.5   \n", "          5511624       89.0       66.0      4468.0      4469.0       25.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511624       92.5       38.5      4469.5      4470.5       39.0   \n", "          5511624       93.5       39.0      4469.0      4470.0       28.0   \n", "          5511624       92.0       39.0      4469.0      4470.0       56.0   \n", "          5511624      101.5       37.5      4469.0      4470.0       28.0   \n", "          5511624       93.0       36.0      4469.0      4470.0        6.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511624      110.5  \n", "          5511624       72.0  \n", "          5511624      249.0  \n", "          5511624      249.0  \n", "          5511624       79.0  \n", "...                      ...  \n", "          5511624       68.5  \n", "          5511624       72.0  \n", "          5511624       71.0  \n", "          5511624       72.0  \n", "          5511624       73.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511625),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511625  1653487500  4468.0   9.5      4468.0      4467.0   \n", "          5511625  1653487501  4468.0   2.5      4468.0      4467.0   \n", "          5511625  1653487502  4469.0   2.5      4468.0      4467.0   \n", "          5511625  1653487503  4468.0  10.5      4468.0      4467.0   \n", "          5511625  1653487504  4469.0  10.0      4469.0      4468.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511625  1653487795  4457.5   1.5      4456.0      4455.0   \n", "          5511625  1653487796  4457.0   0.0      4456.0      4455.0   \n", "          5511625  1653487797  4457.0   2.0      4456.0      4455.0   \n", "          5511625  1653487798  4456.5  13.0      4456.0      4455.0   \n", "          5511625  1653487799  4457.0   0.5      4456.0      4455.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511625       86.5       36.5      4469.0      4470.0        4.0   \n", "          5511625       80.0       37.0      4469.0      4470.0       15.5   \n", "          5511625       97.0       37.0      4469.0      4470.0       11.0   \n", "          5511625      101.5       37.5      4469.0      4470.0        5.5   \n", "          5511625       13.0       97.0      4470.0      4471.0       65.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511625       47.0       49.0      4457.0      4458.0        1.0   \n", "          5511625       46.0       49.0      4457.0      4458.0        9.0   \n", "          5511625       30.0       49.0      4457.0      4458.0       16.0   \n", "          5511625       38.0       49.0      4458.0      4459.0      226.5   \n", "          5511625       38.5       52.5      4457.0      4458.0        4.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511625       74.0  \n", "          5511625       75.0  \n", "          5511625       72.5  \n", "          5511625       73.0  \n", "          5511625       71.0  \n", "...                      ...  \n", "          5511625      226.5  \n", "          5511625      227.5  \n", "          5511625      227.0  \n", "          5511625       63.0  \n", "          5511625      227.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511626),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511626  1653487800  4457.0  10.5      4456.0      4455.0   \n", "          5511626  1653487801  4457.0   2.0      4456.0      4455.0   \n", "          5511626  1653487802  4456.5  16.5      4455.0      4454.0   \n", "          5511626  1653487803  4456.0   0.0      4455.0      4454.0   \n", "          5511626  1653487804  4455.5   7.5      4455.0      4454.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511626  1653488095  4462.0   1.5      4462.0      4461.0   \n", "          5511626  1653488096  4463.0  12.0      4462.5      4461.5   \n", "          5511626  1653488097  4463.0   4.0      4462.0      4461.0   \n", "          5511626  1653488098  4462.5   1.0      4462.0      4461.0   \n", "          5511626  1653488099  4462.0   0.5      4463.0      4462.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511626       26.0       56.0      4457.0      4458.0        6.0   \n", "          5511626       25.0       56.0      4457.0      4458.0       19.5   \n", "          5511626       57.0       44.0      4456.5      4457.5       16.5   \n", "          5511626       56.0       45.5      4456.0      4457.0        8.0   \n", "          5511626       84.5       46.0      4456.5      4457.5       24.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511626      113.0       39.0      4463.0      4464.0       24.0   \n", "          5511626       57.0       76.0      4464.0      4465.0       48.5   \n", "          5511626      113.0       39.0      4463.5      4464.5       22.0   \n", "          5511626      122.5       39.0      4464.0      4465.0       46.0   \n", "          5511626       31.0      133.0      4464.0      4465.0       43.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511626      227.0  \n", "          5511626      227.5  \n", "          5511626      132.0  \n", "          5511626       37.5  \n", "          5511626      131.5  \n", "...                      ...  \n", "          5511626       55.0  \n", "          5511626       54.0  \n", "          5511626       49.0  \n", "          5511626       58.0  \n", "          5511626       58.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511627),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511627  1653488100  4463.5  25.5      4463.0      4462.0   \n", "          5511627  1653488101  4464.0   0.5      4464.0      4463.0   \n", "          5511627  1653488102  4464.5   2.5      4464.0      4463.0   \n", "          5511627  1653488103  4465.0   2.5      4464.0      4463.0   \n", "          5511627  1653488104  4465.0   4.0      4464.0      4463.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511627  1653488395  4466.0  20.0      4466.0      4465.0   \n", "          5511627  1653488396  4466.0   1.0      4466.0      4465.0   \n", "          5511627  1653488397  4466.0   2.0      4466.0      4465.0   \n", "          5511627  1653488398  4466.0   1.0      4466.0      4465.0   \n", "          5511627  1653488399  4466.0   0.0      4466.0      4465.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511627       36.0      142.0      4464.5      4465.5       38.5   \n", "          5511627       13.0       45.0      4465.0      4466.0       20.0   \n", "          5511627       17.0       46.0      4465.0      4466.0       18.0   \n", "          5511627       16.0       46.0      4465.0      4466.0       33.0   \n", "          5511627       71.5       47.5      4465.0      4466.0       45.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511627       89.0       30.0      4467.0      4468.0        3.0   \n", "          5511627       88.0       25.0      4467.0      4468.0       18.5   \n", "          5511627       92.0       26.0      4467.0      4468.0       20.0   \n", "          5511627       90.5       26.0      4467.0      4468.0        7.0   \n", "          5511627       91.0       26.0      4467.0      4468.0       15.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511627       51.0  \n", "          5511627       50.0  \n", "          5511627       47.0  \n", "          5511627       47.0  \n", "          5511627       46.5  \n", "...                      ...  \n", "          5511627       92.0  \n", "          5511627       92.0  \n", "          5511627       92.0  \n", "          5511627       92.5  \n", "          5511627       93.0  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5511628),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511628  1653488400  4466.5  30.0      4466.0      4465.0   \n", "          5511628  1653488401  4467.0   1.5      4466.0      4465.0   \n", "          5511628  1653488402  4467.0   2.0      4466.5      4465.5   \n", "          5511628  1653488403  4467.0   4.5      4466.5      4465.5   \n", "          5511628  1653488404  4467.0   3.0      4466.5      4465.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511628  1653488695  4465.0   3.5      4464.0      4463.0   \n", "          5511628  1653488696  4465.0   1.5      4464.0      4463.0   \n", "          5511628  1653488697  4465.0   1.5      4464.0      4463.0   \n", "          5511628  1653488698  4464.5   6.0      4464.0      4463.0   \n", "          5511628  1653488699  4464.5  25.5      4464.0      4463.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511628       38.5       26.5      4467.0      4468.0       13.5   \n", "          5511628       37.5       27.0      4467.0      4468.0        5.5   \n", "          5511628       21.0       34.0      4467.5      4468.5       47.5   \n", "          5511628       24.0       36.0      4467.5      4468.5       49.0   \n", "          5511628       67.0       80.5      4467.5      4468.5       45.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511628       87.0      102.0      4465.0      4466.0       26.5   \n", "          5511628       86.0      102.0      4465.0      4466.0       41.5   \n", "          5511628       86.0      102.0      4465.0      4466.0       38.5   \n", "          5511628       81.0      102.0      4465.0      4466.0       21.0   \n", "          5511628       30.5      102.0      4465.0      4466.0       37.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511628       93.0  \n", "          5511628       93.0  \n", "          5511628       88.0  \n", "          5511628       84.0  \n", "          5511628       84.5  \n", "...                      ...  \n", "          5511628       65.0  \n", "          5511628       65.0  \n", "          5511628       65.0  \n", "          5511628       65.0  \n", "          5511628       65.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511629),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511629  1653488700  4464.0  14.5      4463.0      4462.0   \n", "          5511629  1653488701  4464.0   1.5      4463.0      4462.0   \n", "          5511629  1653488702  4463.0   4.5      4463.5      4462.5   \n", "          5511629  1653488703  4463.5  10.0      4464.0      4463.0   \n", "          5511629  1653488704  4464.0   8.5      4464.0      4463.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511629  1653488995  4464.0   1.0      4463.0      4462.0   \n", "          5511629  1653488996  4464.0   2.0      4463.0      4462.0   \n", "          5511629  1653488997  4464.0   1.0      4463.0      4462.0   \n", "          5511629  1653488998  4464.0   0.0      4463.0      4462.0   \n", "          5511629  1653488999  4464.0  17.0      4464.0      4463.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511629      102.5       78.0      4465.0      4466.0       73.0   \n", "          5511629      103.5       78.0      4465.0      4466.0       56.0   \n", "          5511629       49.5       86.0      4465.0      4466.0       54.0   \n", "          5511629       13.5       83.0      4465.0      4466.0       65.5   \n", "          5511629       54.0       85.0      4465.0      4466.0       48.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511629       31.5       22.0      4464.0      4465.0       17.0   \n", "          5511629       28.5       22.0      4464.0      4465.0       36.5   \n", "          5511629       27.0       22.0      4464.0      4465.0       34.5   \n", "          5511629       27.0       22.0      4464.0      4465.0       35.0   \n", "          5511629       12.5       25.0      4465.0      4466.0       57.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511629       63.0  \n", "          5511629       62.0  \n", "          5511629       62.5  \n", "          5511629       63.0  \n", "          5511629       62.5  \n", "...                      ...  \n", "          5511629       57.0  \n", "          5511629       57.0  \n", "          5511629       57.0  \n", "          5511629       57.0  \n", "          5511629       43.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511630),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511630  1653489000  4461.0  91.5      4460.0      4459.0   \n", "          5511630  1653489001  4461.0  25.5      4460.0      4459.0   \n", "          5511630  1653489002  4460.0  46.5      4460.0      4459.0   \n", "          5511630  1653489003  4460.0   7.5      4459.0      4458.0   \n", "          5511630  1653489004  4459.0  24.0      4459.0      4458.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511630  1653489295  4457.5   6.0      4457.0      4456.0   \n", "          5511630  1653489296  4458.0  13.0      4457.5      4456.5   \n", "          5511630  1653489297  4458.0   6.0      4457.0      4456.0   \n", "          5511630  1653489298  4458.0   6.0      4457.5      4456.5   \n", "          5511630  1653489299  4458.5   9.5      4458.0      4457.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511630       94.5       26.0      4462.5      4463.5       46.0   \n", "          5511630       67.0       26.0      4461.0      4462.0       20.0   \n", "          5511630        1.5       24.0      4461.0      4462.0       31.0   \n", "          5511630       24.5      100.0      4460.0      4461.0       10.5   \n", "          5511630       30.0       97.5      4460.0      4461.0       28.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511630       94.5       59.5      4458.5      4459.5       17.0   \n", "          5511630       38.5       68.5      4458.5      4459.5       14.0   \n", "          5511630       97.0       62.5      4458.5      4459.5       12.5   \n", "          5511630       49.0       82.0      4459.0      4460.0       17.0   \n", "          5511630        3.5       95.5      4459.0      4460.0       58.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511630       62.0  \n", "          5511630       94.5  \n", "          5511630       95.0  \n", "          5511630       41.5  \n", "          5511630       58.0  \n", "...                      ...  \n", "          5511630       55.5  \n", "          5511630       55.0  \n", "          5511630       52.5  \n", "          5511630       87.0  \n", "          5511630       67.0  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5511631),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511631  1653489300  4457.5  28.0      4457.0      4456.0   \n", "          5511631  1653489301  4457.5   2.0      4457.0      4456.0   \n", "          5511631  1653489302  4458.5   8.0      4458.0      4457.0   \n", "          5511631  1653489303  4458.0  12.0      4458.0      4457.0   \n", "          5511631  1653489304  4458.0  27.0      4457.5      4456.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511631  1653489595  4439.5  38.5      4438.0      4437.0   \n", "          5511631  1653489596  4438.0  29.0      4437.5      4436.5   \n", "          5511631  1653489597  4438.0  17.0      4437.5      4436.5   \n", "          5511631  1653489598  4438.5   7.5      4438.0      4437.0   \n", "          5511631  1653489599  4437.5  12.0      4437.0      4436.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511631       81.0       69.0      4458.0      4459.0        7.5   \n", "          5511631       62.5       69.0      4458.0      4459.0       19.0   \n", "          5511631       13.0       62.0      4459.0      4460.0       66.5   \n", "          5511631       10.0       62.5      4459.0      4460.0       73.0   \n", "          5511631       41.0       66.5      4459.0      4460.0       57.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511631       21.0       20.5      4439.5      4440.5       20.0   \n", "          5511631       17.5       32.5      4438.5      4439.5        9.0   \n", "          5511631       10.5       48.5      4439.0      4440.0       45.5   \n", "          5511631       12.0       55.0      4439.0      4440.0       38.0   \n", "          5511631       49.0       47.5      4438.0      4439.0       19.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511631       64.0  \n", "          5511631       67.0  \n", "          5511631       66.5  \n", "          5511631       69.0  \n", "          5511631       70.0  \n", "...                      ...  \n", "          5511631       30.5  \n", "          5511631       38.5  \n", "          5511631       58.0  \n", "          5511631       58.0  \n", "          5511631       38.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511632),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511632  1653489600  4438.5  65.0      4438.0      4437.0   \n", "          5511632  1653489601  4439.5  35.0      4438.5      4437.5   \n", "          5511632  1653489602  4439.5  42.5      4439.0      4438.0   \n", "          5511632  1653489603  4440.0  23.0      4439.5      4438.5   \n", "          5511632  1653489604  4441.0  12.5      4440.0      4439.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511632  1653489895  4447.0  10.5      4446.5      4445.5   \n", "          5511632  1653489896  4447.0  10.5      4446.0      4445.0   \n", "          5511632  1653489897  4446.0   0.5      4446.0      4445.0   \n", "          5511632  1653489898  4446.5   4.5      4446.0      4445.0   \n", "          5511632  1653489899  4446.5   1.5      4446.0      4445.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511632       26.0       61.5      4439.0      4440.0       35.0   \n", "          5511632       38.0       67.0      4440.0      4441.0       63.5   \n", "          5511632       30.0       65.5      4440.0      4441.0       33.0   \n", "          5511632        8.5       44.0      4440.5      4441.5       46.0   \n", "          5511632       26.0       13.0      4441.0      4442.0       67.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511632       11.0       52.5      4447.5      4448.5       37.0   \n", "          5511632       18.5       70.0      4447.0      4448.0        8.5   \n", "          5511632       12.5       69.0      4447.0      4448.0       30.0   \n", "          5511632        8.5       68.5      4447.0      4448.0       25.5   \n", "          5511632        9.0       70.0      4447.0      4448.0       24.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511632       64.0  \n", "          5511632       25.0  \n", "          5511632       25.0  \n", "          5511632       53.5  \n", "          5511632       78.0  \n", "...                      ...  \n", "          5511632       62.0  \n", "          5511632       55.0  \n", "          5511632       56.0  \n", "          5511632       57.0  \n", "          5511632       58.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511633),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511633  1653489900  4447.0  20.5      4445.5      4444.5   \n", "          5511633  1653489901  4446.0   3.0      4446.0      4445.0   \n", "          5511633  1653489902  4446.5  10.0      4446.0      4445.0   \n", "          5511633  1653489903  4446.5   5.5      4446.0      4445.0   \n", "          5511633  1653489904  4447.0   5.5      4446.0      4445.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511633  1653490195  4442.0   3.0      4441.0      4440.0   \n", "          5511633  1653490196  4442.0   0.5      4441.0      4440.0   \n", "          5511633  1653490197  4441.5   1.5      4441.0      4440.0   \n", "          5511633  1653490198  4441.0   1.5      4441.0      4440.0   \n", "          5511633  1653490199  4442.0   1.5      4441.0      4440.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511633       47.0       89.0      4447.0      4448.0        8.5   \n", "          5511633       24.5       69.0      4447.0      4448.0       20.0   \n", "          5511633        4.5       68.0      4447.0      4448.0       26.5   \n", "          5511633       15.0       68.0      4447.0      4448.0       22.0   \n", "          5511633       16.5       71.5      4447.0      4448.0       19.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511633       31.5       45.5      4442.0      4443.0       79.0   \n", "          5511633       31.5       45.0      4442.0      4443.0       88.5   \n", "          5511633       30.0       44.0      4442.0      4443.0       91.0   \n", "          5511633       29.0       44.0      4442.0      4443.0       90.0   \n", "          5511633       30.0       44.0      4442.0      4443.0       87.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511633       59.0  \n", "          5511633       59.0  \n", "          5511633       59.0  \n", "          5511633       59.0  \n", "          5511633       60.5  \n", "...                      ...  \n", "          5511633      103.0  \n", "          5511633      103.0  \n", "          5511633      103.5  \n", "          5511633      106.5  \n", "          5511633      108.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511634),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511634  1653490200  4441.0   1.0      4441.0      4440.0   \n", "          5511634  1653490201  4442.0   5.5      4441.0      4440.0   \n", "          5511634  1653490202  4442.0  24.5      4441.0      4440.0   \n", "          5511634  1653490203  4441.0   3.5      4440.0      4439.0   \n", "          5511634  1653490204  4441.0   3.0      4440.0      4439.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511634  1653490495  4444.0   0.5      4443.0      4442.0   \n", "          5511634  1653490496  4444.0   0.0      4443.0      4442.0   \n", "          5511634  1653490497  4444.0   0.0      4443.0      4442.0   \n", "          5511634  1653490498  4444.0   1.0      4443.0      4442.0   \n", "          5511634  1653490499  4444.0   2.5      4443.0      4442.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511634       48.0       44.0      4442.0      4443.0       87.0   \n", "          5511634       48.0       44.5      4442.0      4443.0       81.0   \n", "          5511634       24.0       47.5      4442.0      4443.0       78.5   \n", "          5511634       48.5       22.0      4441.5      4442.5       47.0   \n", "          5511634       65.5       22.0      4441.0      4442.0       17.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511634       53.0       56.0      4444.0      4445.0       50.5   \n", "          5511634       53.0       55.0      4444.0      4445.0       51.0   \n", "          5511634       53.0       62.0      4444.0      4445.0       51.0   \n", "          5511634       53.0       62.0      4444.0      4445.0       51.0   \n", "          5511634       51.0       62.0      4444.0      4445.0       48.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511634      107.0  \n", "          5511634      107.0  \n", "          5511634      103.0  \n", "          5511634       90.5  \n", "          5511634       79.5  \n", "...                      ...  \n", "          5511634       62.0  \n", "          5511634       62.0  \n", "          5511634       62.0  \n", "          5511634       62.0  \n", "          5511634       62.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511635),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511635  1653490500  4443.5  29.0      4442.5      4441.5   \n", "          5511635  1653490501  4444.0   9.0      4443.0      4442.0   \n", "          5511635  1653490502  4443.0  12.0      4442.0      4441.0   \n", "          5511635  1653490503  4443.0   1.5      4442.0      4441.0   \n", "          5511635  1653490504  4443.0   7.0      4442.0      4441.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511635  1653490795  4445.0  67.0      4444.5      4443.5   \n", "          5511635  1653490796  4445.5   5.0      4445.0      4444.0   \n", "          5511635  1653490797  4445.5   5.5      4445.0      4444.0   \n", "          5511635  1653490798  4445.5   9.0      4445.0      4444.0   \n", "          5511635  1653490799  4444.5  16.0      4443.5      4442.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511635       55.0       63.5      4444.0      4445.0       48.0   \n", "          5511635       32.0       62.0      4444.0      4445.0       33.0   \n", "          5511635       62.0       65.0      4444.0      4445.0       33.0   \n", "          5511635       62.0       65.0      4443.0      4444.0       16.5   \n", "          5511635       94.0       66.0      4443.0      4444.0       10.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511635       13.0       36.0      4445.5      4446.5       69.5   \n", "          5511635       12.5        8.0      4446.0      4447.0       71.5   \n", "          5511635       20.0        9.0      4446.0      4447.0       67.5   \n", "          5511635       14.0        9.0      4446.0      4447.0       62.5   \n", "          5511635       21.0       31.0      4444.5      4445.5        2.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511635       62.5  \n", "          5511635       63.0  \n", "          5511635       62.0  \n", "          5511635       31.5  \n", "          5511635       33.5  \n", "...                      ...  \n", "          5511635       37.5  \n", "          5511635       23.0  \n", "          5511635       23.0  \n", "          5511635       23.0  \n", "          5511635       34.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511756),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511756  1653526845  4455.0   7.0      4455.0      4454.0   \n", "          5511756  1653526846  4455.5  11.0      4455.0      4454.0   \n", "          5511756  1653526847  4456.0   7.0      4455.0      4454.0   \n", "          5511756  1653526848  4456.0  13.5      4455.0      4454.0   \n", "          5511756  1653526849  4455.5   9.0      4455.0      4454.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511756  1653527095  4443.5   1.0      4443.0      4442.0   \n", "          5511756  1653527096  4443.5  11.0      4443.0      4442.0   \n", "          5511756  1653527097  4444.0   0.0      4443.0      4442.0   \n", "          5511756  1653527098  4444.0   3.5      4443.0      4442.0   \n", "          5511756  1653527099  4444.0   4.0      4443.0      4442.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511756        6.0      104.0      4456.0      4457.0        3.0   \n", "          5511756        5.5      104.0      4456.0      4457.0        4.5   \n", "          5511756       16.5      104.0      4456.0      4457.0       10.0   \n", "          5511756       13.5      109.0      4456.0      4457.0       16.5   \n", "          5511756       18.5      109.0      4456.0      4457.0       16.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511756        8.5      114.0      4444.0      4445.0       24.5   \n", "          5511756       14.5      115.0      4444.0      4445.0       14.0   \n", "          5511756       20.0      115.5      4444.0      4445.0        4.5   \n", "          5511756       20.5      116.5      4445.0      4446.0       62.5   \n", "          5511756       23.0      118.0      4445.0      4446.0       62.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511756       83.0  \n", "          5511756       83.5  \n", "          5511756       86.0  \n", "          5511756       87.5  \n", "          5511756       85.5  \n", "...                      ...  \n", "          5511756       62.0  \n", "          5511756       62.0  \n", "          5511756       62.0  \n", "          5511756       46.5  \n", "          5511756       46.0  \n", "\n", "[255 rows x 11 columns])\n", "(('BU8888.SC', 5511757),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511757  1653527100  4444.0   1.0      4443.5      4442.5   \n", "          5511757  1653527101  4443.5  22.0      4442.5      4441.5   \n", "          5511757  1653527102  4442.5   9.0      4442.0      4441.0   \n", "          5511757  1653527103  4444.0   9.0      4442.5      4441.5   \n", "          5511757  1653527104  4443.0  11.5      4442.5      4441.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511757  1653527395  4457.0  16.5      4456.5      4455.5   \n", "          5511757  1653527396  4458.0  12.5      4457.0      4456.0   \n", "          5511757  1653527397  4458.0  12.5      4457.5      4456.5   \n", "          5511757  1653527398  4458.0   2.5      4457.5      4456.5   \n", "          5511757  1653527399  4458.5   2.0      4458.0      4457.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511757       15.0       73.0      4444.5      4445.5       32.0   \n", "          5511757       65.0      124.0      4443.5      4444.5        9.0   \n", "          5511757      118.5      130.0      4443.0      4444.0        1.0   \n", "          5511757       70.5      128.0      4444.0      4445.0       25.0   \n", "          5511757       65.0      128.0      4444.0      4445.0       50.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511757      193.5      211.5      4458.5      4459.5       23.0   \n", "          5511757       17.5      380.5      4458.0      4459.0        8.5   \n", "          5511757       15.0      198.0      4458.5      4459.5       26.5   \n", "          5511757       15.0      197.5      4458.5      4459.5       20.0   \n", "          5511757       19.0       15.0      4459.0      4460.0       37.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511757       54.0  \n", "          5511757       36.5  \n", "          5511757       11.0  \n", "          5511757       69.5  \n", "          5511757       74.0  \n", "...                      ...  \n", "          5511757       42.5  \n", "          5511757       47.5  \n", "          5511757       50.0  \n", "          5511757       45.0  \n", "          5511757       52.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511758),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511758  1653527400  4458.5   17.5      4457.0      4456.0   \n", "          5511758  1653527401  4457.0   12.0      4457.0      4456.0   \n", "          5511758  1653527402  4458.0    5.0      4456.5      4455.5   \n", "          5511758  1653527403  4458.0    9.0      4456.0      4455.0   \n", "          5511758  1653527404  4456.0  207.0      4455.0      4454.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511758  1653527695  4445.5    1.0      4445.0      4444.0   \n", "          5511758  1653527696  4446.0    1.0      4445.0      4444.0   \n", "          5511758  1653527697  4445.5    7.5      4445.0      4444.0   \n", "          5511758  1653527698  4446.0    5.0      4445.0      4444.0   \n", "          5511758  1653527699  4445.5    1.0      4445.0      4444.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511758       17.5      382.0      4459.0      4460.0       35.5   \n", "          5511758        4.5      382.5      4458.0      4459.0       15.0   \n", "          5511758      194.5      214.5      4458.0      4459.0       35.0   \n", "          5511758      371.5       48.0      4457.5      4458.5       35.0   \n", "          5511758      105.0       82.5      4456.5      4457.5       79.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511758       13.5      151.0      4446.0      4447.0       72.0   \n", "          5511758       20.0      151.0      4446.0      4447.0       70.5   \n", "          5511758       13.5      151.0      4446.0      4447.0       64.5   \n", "          5511758       15.5      151.0      4446.0      4447.0       57.5   \n", "          5511758       15.0      152.0      4446.0      4447.0       57.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511758       52.5  \n", "          5511758       36.0  \n", "          5511758       36.5  \n", "          5511758       50.0  \n", "          5511758       50.5  \n", "...                      ...  \n", "          5511758       67.0  \n", "          5511758       66.5  \n", "          5511758       66.0  \n", "          5511758       64.5  \n", "          5511758       64.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511759),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511759  1653527700  4446.0  12.5      4445.0      4444.0   \n", "          5511759  1653527701  4447.0  59.0      4446.5      4445.5   \n", "          5511759  1653527702  4447.0   0.0      4448.0      4447.0   \n", "          5511759  1653527703  4448.5  18.0      4448.0      4447.0   \n", "          5511759  1653527704  4447.5  28.5      4447.5      4446.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511759  1653527995  4446.0   0.0      4445.0      4444.0   \n", "          5511759  1653527996  4445.5   5.5      4445.0      4444.0   \n", "          5511759  1653527997  4445.5  11.5      4445.0      4444.0   \n", "          5511759  1653527998  4446.0   2.0      4446.0      4445.0   \n", "          5511759  1653527999  4446.0   3.0      4446.0      4445.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511759       31.0      152.0      4446.0      4447.0       45.5   \n", "          5511759       46.0       53.0      4448.0      4449.0       31.0   \n", "          5511759       25.5       42.5      4449.0      4450.0       34.0   \n", "          5511759       43.0       43.5      4449.5      4450.5       42.0   \n", "          5511759       38.0       55.0      4449.0      4450.0        3.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511759       16.5       23.5      4446.0      4447.0       28.0   \n", "          5511759       17.5       22.5      4446.0      4447.0       20.0   \n", "          5511759       19.0       22.0      4446.5      4447.5       22.5   \n", "          5511759       22.5       38.5      4447.0      4448.0       29.0   \n", "          5511759       23.0       37.5      4447.0      4448.0       28.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511759       64.0  \n", "          5511759       30.5  \n", "          5511759       46.0  \n", "          5511759       43.0  \n", "          5511759       51.0  \n", "...                      ...  \n", "          5511759       27.0  \n", "          5511759       27.0  \n", "          5511759       38.0  \n", "          5511759       49.0  \n", "          5511759       49.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511760),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511760  1653528000  4447.0   1.0      4446.0      4445.0   \n", "          5511760  1653528001  4446.5  10.5      4446.0      4445.0   \n", "          5511760  1653528002  4446.0   7.0      4445.0      4444.0   \n", "          5511760  1653528003  4445.5   4.5      4445.0      4444.0   \n", "          5511760  1653528004  4445.5   5.0      4445.5      4444.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511760  1653528295  4446.0   3.0      4446.0      4445.0   \n", "          5511760  1653528296  4446.0   0.0      4446.0      4445.0   \n", "          5511760  1653528297  4446.0   0.5      4446.0      4445.0   \n", "          5511760  1653528298  4446.5   3.5      4446.0      4445.0   \n", "          5511760  1653528299  4446.5   8.5      4445.0      4444.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511760       25.0       35.0      4447.0      4448.0       27.0   \n", "          5511760       15.0       35.0      4447.0      4448.0       31.0   \n", "          5511760       31.0       22.0      4446.0      4447.0        2.0   \n", "          5511760       26.5       22.0      4446.5      4447.5       12.5   \n", "          5511760        8.0       19.5      4447.0      4448.0       24.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511760       24.0       35.5      4447.0      4448.0       28.0   \n", "          5511760       24.0       37.0      4447.0      4448.0       28.5   \n", "          5511760       16.5       37.0      4447.0      4448.0       33.0   \n", "          5511760        7.0       37.5      4447.0      4448.0       30.5   \n", "          5511760       38.0       29.5      4447.0      4448.0       21.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511760       55.0  \n", "          5511760       55.0  \n", "          5511760       25.0  \n", "          5511760       40.5  \n", "          5511760       59.0  \n", "...                      ...  \n", "          5511760       47.0  \n", "          5511760       47.0  \n", "          5511760       47.0  \n", "          5511760       47.0  \n", "          5511760       47.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511761),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511761  1653528300  4445.0   6.0      4445.0      4444.0   \n", "          5511761  1653528301  4445.0   1.5      4445.5      4444.5   \n", "          5511761  1653528302  4447.0   2.5      4445.0      4444.0   \n", "          5511761  1653528303  4446.0   3.5      4445.0      4444.0   \n", "          5511761  1653528304  4446.0   1.0      4445.0      4444.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511761  1653528595  4427.5  38.5      4427.0      4426.0   \n", "          5511761  1653528596  4428.0  13.5      4427.0      4426.0   \n", "          5511761  1653528597  4427.5   4.0      4427.0      4426.0   \n", "          5511761  1653528598  4428.0  51.5      4428.0      4427.0   \n", "          5511761  1653528599  4429.0  12.0      4429.0      4428.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511761       24.5       30.0      4447.0      4448.0       22.5   \n", "          5511761       10.5       25.5      4447.0      4448.0       27.5   \n", "          5511761       20.0       30.0      4446.5      4447.5       43.0   \n", "          5511761       16.0       14.0      4446.0      4447.0       57.5   \n", "          5511761       16.0       14.0      4446.0      4447.0       57.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511761      134.5       53.0      4428.5      4429.5       38.5   \n", "          5511761      125.5       53.0      4428.5      4429.5       36.5   \n", "          5511761      121.5       53.5      4428.0      4429.0       33.5   \n", "          5511761      383.5      118.0      4429.0      4430.0       43.5   \n", "          5511761        8.0      402.0      4430.0      4431.0      124.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511761       47.0  \n", "          5511761       48.5  \n", "          5511761       43.0  \n", "          5511761       25.0  \n", "          5511761       25.0  \n", "...                      ...  \n", "          5511761      100.0  \n", "          5511761      100.0  \n", "          5511761       77.5  \n", "          5511761      126.0  \n", "          5511761       98.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511762),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511762  1653528600  4429.0  18.0      4428.5      4427.5   \n", "          5511762  1653528601  4428.0  40.0      4428.0      4427.0   \n", "          5511762  1653528602  4428.5   3.5      4428.0      4427.0   \n", "          5511762  1653528603  4429.5  20.5      4428.5      4427.5   \n", "          5511762  1653528604  4429.5  53.0      4429.0      4428.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511762  1653528895  4443.5   1.0      4443.0      4442.0   \n", "          5511762  1653528896  4443.5   5.0      4442.0      4441.0   \n", "          5511762  1653528897  4443.0  22.5      4442.5      4441.5   \n", "          5511762  1653528898  4444.0   2.0      4443.0      4442.0   \n", "          5511762  1653528899  4443.0  11.0      4442.5      4441.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511762      213.0      262.0      4429.5      4430.5       70.5   \n", "          5511762      359.5      124.5      4429.0      4430.0       29.0   \n", "          5511762      365.5      126.0      4429.0      4430.0       42.5   \n", "          5511762      194.5      249.5      4429.5      4430.5       77.0   \n", "          5511762       72.0      361.5      4430.0      4431.0       49.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511762        4.5       66.0      4444.0      4445.0       24.5   \n", "          5511762       67.5      114.0      4443.0      4444.0        9.0   \n", "          5511762       33.0       88.5      4443.5      4444.5       22.5   \n", "          5511762       14.0       64.0      4444.0      4445.0       26.5   \n", "          5511762       33.5       89.0      4443.5      4444.5       17.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511762      110.0  \n", "          5511762      121.0  \n", "          5511762      121.0  \n", "          5511762      113.0  \n", "          5511762      105.0  \n", "...                      ...  \n", "          5511762      140.0  \n", "          5511762       46.5  \n", "          5511762       99.5  \n", "          5511762      143.0  \n", "          5511762       89.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511763),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511763  1653528900  4443.5   1.5      4442.5      4441.5   \n", "          5511763  1653528901  4442.5   7.0      4442.0      4441.0   \n", "          5511763  1653528902  4443.0   3.0      4442.0      4441.0   \n", "          5511763  1653528903  4443.0   0.5      4442.0      4441.0   \n", "          5511763  1653528904  4442.5  14.0      4442.0      4441.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511763  1653529195  4434.0  18.5      4434.0      4433.0   \n", "          5511763  1653529196  4434.0   0.0      4434.0      4433.0   \n", "          5511763  1653529197  4435.0  10.0      4434.0      4433.0   \n", "          5511763  1653529198  4434.0   4.5      4434.0      4433.0   \n", "          5511763  1653529199  4434.0   2.0      4434.0      4433.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511763       37.5       89.0      4444.0      4445.0       26.5   \n", "          5511763       65.5      114.0      4443.5      4444.5       14.5   \n", "          5511763       69.5      114.0      4443.0      4444.0       12.0   \n", "          5511763       76.0      114.0      4443.0      4444.0       14.0   \n", "          5511763       53.0      114.0      4443.0      4444.0       14.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511763        1.5       12.0      4435.0      4436.0       66.0   \n", "          5511763        7.5       19.0      4435.0      4436.0       65.0   \n", "          5511763       12.0       20.0      4435.0      4436.0       55.0   \n", "          5511763        4.0       22.5      4435.0      4436.0       54.0   \n", "          5511763        4.5       26.0      4435.0      4436.0       55.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511763      155.0  \n", "          5511763       97.5  \n", "          5511763       30.0  \n", "          5511763       63.5  \n", "          5511763       98.5  \n", "...                      ...  \n", "          5511763       51.0  \n", "          5511763       51.0  \n", "          5511763       51.0  \n", "          5511763       51.0  \n", "          5511763       51.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511764),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511764  1653529200  4434.0   5.5      4433.0      4432.0   \n", "          5511764  1653529201  4434.0   6.0      4433.0      4432.0   \n", "          5511764  1653529202  4434.0  12.0      4433.0      4432.0   \n", "          5511764  1653529203  4434.0   0.0      4433.0      4432.0   \n", "          5511764  1653529204  4434.0   1.0      4433.0      4432.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511764  1653529495  4431.5  33.5      4431.5      4430.5   \n", "          5511764  1653529496  4431.5  12.0      4431.5      4430.5   \n", "          5511764  1653529497  4431.5   2.0      4431.0      4430.0   \n", "          5511764  1653529498  4431.5   9.5      4431.0      4430.0   \n", "          5511764  1653529499  4431.5   1.5      4431.0      4430.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511764       25.0       62.0      4434.0      4435.0        2.5   \n", "          5511764       25.0       62.0      4434.5      4435.5       27.0   \n", "          5511764       40.0       62.0      4434.0      4435.0       11.0   \n", "          5511764       41.0       62.5      4434.0      4435.0       21.5   \n", "          5511764       42.0       63.0      4434.0      4435.0       29.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511764       10.5      228.0      4432.5      4433.5       44.5   \n", "          5511764       10.0      231.0      4433.0      4434.0       72.0   \n", "          5511764       26.0      445.0      4432.0      4433.0       14.5   \n", "          5511764       29.0      445.0      4433.0      4434.0       72.0   \n", "          5511764       34.0      445.0      4432.5      4433.5       36.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511764       54.0  \n", "          5511764       47.0  \n", "          5511764       45.0  \n", "          5511764       45.0  \n", "          5511764       45.0  \n", "...                      ...  \n", "          5511764       48.0  \n", "          5511764       24.0  \n", "          5511764       72.0  \n", "          5511764       24.0  \n", "          5511764       48.0  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5511765),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511765  1653529500  4431.0    6.5      4431.0      4430.0   \n", "          5511765  1653529501  4432.0    7.0      4431.0      4430.0   \n", "          5511765  1653529502  4431.5    2.0      4431.0      4430.0   \n", "          5511765  1653529503  4430.5   72.0      4430.0      4429.0   \n", "          5511765  1653529504  4430.0  162.5      4430.0      4429.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511765  1653529795  4433.0    5.0      4432.0      4431.0   \n", "          5511765  1653529796  4433.0    0.5      4432.0      4431.0   \n", "          5511765  1653529797  4433.0    0.0      4432.0      4431.0   \n", "          5511765  1653529798  4433.0    0.0      4432.0      4431.0   \n", "          5511765  1653529799  4433.0    0.0      4432.0      4431.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511765       22.5      445.0      4432.0      4433.0       47.5   \n", "          5511765        9.0      444.5      4432.0      4433.0       53.5   \n", "          5511765       16.0      444.0      4432.0      4433.0       50.5   \n", "          5511765      387.0       72.5      4431.0      4432.0      337.5   \n", "          5511765      153.5       71.0      4431.0      4432.0      297.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511765       55.0       56.0      4434.0      4435.0       55.0   \n", "          5511765       38.0       56.0      4434.0      4435.0       56.0   \n", "          5511765       38.0       56.5      4433.0      4434.0        1.0   \n", "          5511765       38.0       57.0      4433.0      4434.0        1.0   \n", "          5511765       38.0       56.0      4433.0      4434.0        1.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511765       72.0  \n", "          5511765       71.0  \n", "          5511765       71.0  \n", "          5511765       46.5  \n", "          5511765       44.0  \n", "...                      ...  \n", "          5511765       47.0  \n", "          5511765       49.5  \n", "          5511765       56.5  \n", "          5511765       57.0  \n", "          5511765       59.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511766),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511766  1653529800  4432.5   0.5      4432.0      4431.0   \n", "          5511766  1653529801  4432.5   2.5      4432.0      4431.0   \n", "          5511766  1653529802  4432.5   2.5      4432.0      4431.0   \n", "          5511766  1653529803  4433.0   0.0      4432.0      4431.0   \n", "          5511766  1653529804  4432.0   0.5      4432.0      4431.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511766  1653530095  4433.0   6.0      4432.0      4431.0   \n", "          5511766  1653530096  4433.0   6.0      4432.5      4431.5   \n", "          5511766  1653530097  4433.5   1.5      4432.5      4431.5   \n", "          5511766  1653530098  4433.0   3.0      4432.5      4431.5   \n", "          5511766  1653530099  4433.0   0.5      4433.0      4432.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511766       37.5       56.5      4433.0      4434.0        7.5   \n", "          5511766       35.0       57.0      4433.0      4434.0       13.0   \n", "          5511766       34.0       57.0      4433.0      4434.0       11.5   \n", "          5511766       34.0       59.0      4433.0      4434.0        6.5   \n", "          5511766       35.0       62.0      4433.0      4434.0        7.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511766       22.0       75.0      4433.0      4434.0        1.0   \n", "          5511766       21.5       51.0      4434.0      4435.0       54.5   \n", "          5511766       17.5       58.0      4434.0      4435.0       53.0   \n", "          5511766       18.5       58.0      4433.5      4434.5       29.0   \n", "          5511766        1.5       37.0      4434.0      4435.0       53.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511766       62.0  \n", "          5511766       62.0  \n", "          5511766       62.0  \n", "          5511766       63.0  \n", "          5511766       63.0  \n", "...                      ...  \n", "          5511766       54.0  \n", "          5511766      213.0  \n", "          5511766      213.0  \n", "          5511766      133.0  \n", "          5511766      213.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511767),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511767  1653530100  4432.5   4.0      4432.5      4431.5   \n", "          5511767  1653530101  4432.0   0.0      4432.0      4431.0   \n", "          5511767  1653530102  4432.0  22.5      4431.5      4430.5   \n", "          5511767  1653530103  4432.0   0.5      4431.0      4430.0   \n", "          5511767  1653530104  4431.0   2.5      4431.0      4430.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511767  1653530395  4433.0   4.5      4432.0      4431.0   \n", "          5511767  1653530396  4432.0   6.5      4432.0      4431.0   \n", "          5511767  1653530397  4432.0   3.0      4431.0      4430.0   \n", "          5511767  1653530398  4432.0   1.5      4432.0      4431.0   \n", "          5511767  1653530399  4432.0  10.0      4431.0      4430.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511767       17.0       58.5      4434.0      4435.0       55.0   \n", "          5511767       34.5       80.0      4433.0      4434.0       15.5   \n", "          5511767       57.5       85.0      4433.0      4434.0       26.0   \n", "          5511767       84.5       90.0      4433.0      4434.0       46.0   \n", "          5511767       83.0       90.0      4432.0      4433.0        5.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511767       11.5       27.0      4433.0      4434.0       11.5   \n", "          5511767        7.5       40.5      4433.0      4434.0       11.5   \n", "          5511767       55.5      151.0      4432.5      4433.5        8.0   \n", "          5511767        1.0       59.5      4433.0      4434.0       18.0   \n", "          5511767       61.5      152.0      4432.5      4433.5        9.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511767      203.0  \n", "          5511767       54.0  \n", "          5511767       54.0  \n", "          5511767       54.0  \n", "          5511767       66.5  \n", "...                      ...  \n", "          5511767       31.0  \n", "          5511767       31.0  \n", "          5511767       23.5  \n", "          5511767       32.0  \n", "          5511767       25.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511768),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511768  1653530400  4431.5   8.0      4431.0      4430.0   \n", "          5511768  1653530401  4431.0  10.5      4431.0      4430.0   \n", "          5511768  1653530402  4431.0   3.5      4431.0      4430.0   \n", "          5511768  1653530403  4431.5  27.5      4431.5      4430.5   \n", "          5511768  1653530404  4433.5  18.0      4433.0      4432.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511768  1653530695  4440.0   2.5      4439.0      4438.0   \n", "          5511768  1653530696  4440.0   1.0      4439.5      4438.5   \n", "          5511768  1653530697  4440.0   0.5      4439.0      4438.0   \n", "          5511768  1653530698  4440.0   0.0      4439.0      4438.0   \n", "          5511768  1653530699  4439.5  30.5      4438.5      4437.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511768       61.0      154.5      4432.0      4433.0        5.0   \n", "          5511768       38.5      156.0      4432.0      4433.0       28.5   \n", "          5511768       45.5      156.0      4432.0      4433.0       31.5   \n", "          5511768       38.0      103.5      4432.5      4433.5       19.5   \n", "          5511768       47.5       48.5      4434.5      4435.5       24.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511768       63.0       49.0      4440.5      4441.5       17.5   \n", "          5511768       31.0       55.0      4441.0      4442.0       34.5   \n", "          5511768       61.0       49.0      4440.5      4441.5       24.5   \n", "          5511768       61.0       50.0      4440.0      4441.0       15.5   \n", "          5511768       55.5       35.0      4440.0      4441.0       26.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511768       21.0  \n", "          5511768       24.0  \n", "          5511768       25.0  \n", "          5511768       28.0  \n", "          5511768       35.0  \n", "...                      ...  \n", "          5511768       30.5  \n", "          5511768       29.0  \n", "          5511768       33.5  \n", "          5511768       38.0  \n", "          5511768       38.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511769),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511769  1653530700  4438.5   1.5      4438.0      4437.0   \n", "          5511769  1653530701  4438.0  24.0      4437.0      4436.0   \n", "          5511769  1653530702  4438.0   1.0      4437.0      4436.0   \n", "          5511769  1653530703  4438.5   9.5      4437.5      4436.5   \n", "          5511769  1653530704  4438.0   8.0      4438.0      4437.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511769  1653530995  4443.0   1.0      4442.0      4441.0   \n", "          5511769  1653530996  4443.0   5.5      4443.0      4442.0   \n", "          5511769  1653530997  4442.5   6.5      4442.0      4441.0   \n", "          5511769  1653530998  4442.5   5.5      4442.0      4441.0   \n", "          5511769  1653530999  4443.0   0.0      4443.0      4442.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511769       45.5       20.0      4439.0      4440.0       45.5   \n", "          5511769       17.0       57.5      4438.5      4439.5       34.0   \n", "          5511769       23.5       56.0      4438.0      4439.0       20.0   \n", "          5511769       16.0       43.0      4439.0      4440.0       34.5   \n", "          5511769        5.0       26.0      4439.0      4440.0       43.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511769       24.5       41.0      4443.0      4444.0        8.0   \n", "          5511769        2.0       25.0      4444.0      4445.0       21.5   \n", "          5511769       24.0       42.0      4443.5      4444.5       15.5   \n", "          5511769       26.5       42.0      4443.5      4444.5       10.0   \n", "          5511769        2.0       27.0      4444.0      4445.0       19.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511769       51.5  \n", "          5511769       46.5  \n", "          5511769       41.0  \n", "          5511769       53.0  \n", "          5511769       53.0  \n", "...                      ...  \n", "          5511769       21.0  \n", "          5511769       32.0  \n", "          5511769       21.5  \n", "          5511769       21.5  \n", "          5511769       32.0  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5511770),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511770  1653531000  4443.5   3.0      4443.0      4442.0   \n", "          5511770  1653531001  4443.0   0.5      4443.0      4442.0   \n", "          5511770  1653531002  4443.0   2.0      4442.5      4441.5   \n", "          5511770  1653531003  4443.0   0.0      4442.0      4441.0   \n", "          5511770  1653531004  4442.5   2.0      4442.0      4441.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511770  1653531295  4456.5  24.0      4456.0      4455.0   \n", "          5511770  1653531296  4456.0   9.5      4456.0      4455.0   \n", "          5511770  1653531297  4456.0  13.0      4456.0      4455.0   \n", "          5511770  1653531298  4455.5   3.0      4455.0      4454.0   \n", "          5511770  1653531299  4456.5   2.5      4455.5      4454.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511770        3.5       27.0      4444.0      4445.0       16.0   \n", "          5511770        2.5       27.0      4444.0      4445.0       25.0   \n", "          5511770       15.5       35.0      4443.5      4444.5       46.5   \n", "          5511770       24.0       43.0      4443.0      4444.0       64.5   \n", "          5511770       19.0       43.0      4443.0      4444.0       65.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511770       37.0       25.0      4457.0      4458.0       43.0   \n", "          5511770       18.5       25.0      4457.0      4458.0       30.5   \n", "          5511770        4.5       20.5      4457.0      4458.0       21.5   \n", "          5511770       20.5       45.0      4456.5      4457.5        9.0   \n", "          5511770       10.0       31.5      4457.0      4458.0       20.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511770       32.0  \n", "          5511770       32.0  \n", "          5511770       34.0  \n", "          5511770       30.5  \n", "          5511770       32.0  \n", "...                      ...  \n", "          5511770      139.0  \n", "          5511770      137.0  \n", "          5511770      131.0  \n", "          5511770       74.0  \n", "          5511770      131.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511774),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511774  1653532201  4462.0  82.5      4461.0      4460.0   \n", "          5511774  1653532202  4463.5  40.0      4463.0      4462.0   \n", "          5511774  1653532203  4463.5  46.5      4463.0      4462.0   \n", "          5511774  1653532204  4463.5   8.5      4463.5      4462.5   \n", "          5511774  1653532205  4462.5  78.0      4462.0      4461.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511774  1653532495  4468.0   2.5      4468.0      4467.0   \n", "          5511774  1653532496  4468.5   4.5      4468.0      4467.0   \n", "          5511774  1653532497  4468.5  81.0      4468.0      4467.0   \n", "          5511774  1653532498  4469.0   4.5      4469.0      4468.0   \n", "          5511774  1653532499  4469.0  40.0      4469.0      4468.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511774      166.0       67.5      4462.5      4463.5       10.5   \n", "          5511774       68.5      102.5      4464.5      4465.5       42.0   \n", "          5511774       54.0      146.5      4464.0      4465.0        5.0   \n", "          5511774       24.0      114.5      4464.5      4465.5       44.5   \n", "          5511774      119.0       83.0      4463.0      4464.0        4.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511774       43.5       83.5      4469.0      4470.0      126.0   \n", "          5511774       39.5       85.0      4469.0      4470.0      124.5   \n", "          5511774       32.5       86.0      4469.5      4470.5      212.5   \n", "          5511774        8.5       60.0      4470.0      4471.0      422.5   \n", "          5511774       19.0       58.5      4470.0      4471.0      390.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511774       27.5  \n", "          5511774       97.0  \n", "          5511774       76.5  \n", "          5511774       94.5  \n", "          5511774       36.0  \n", "...                      ...  \n", "          5511774      421.0  \n", "          5511774      422.5  \n", "          5511774      247.5  \n", "          5511774       69.0  \n", "          5511774       68.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511775),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511775  1653532500  4469.0  101.0      4469.0      4468.0   \n", "          5511775  1653532501  4470.0   93.0      4469.5      4468.5   \n", "          5511775  1653532502  4470.5   37.0      4470.0      4469.0   \n", "          5511775  1653532503  4471.0   30.0      4470.0      4469.0   \n", "          5511775  1653532504  4470.5   19.0      4470.0      4469.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511775  1653532795  4466.0    5.5      4465.0      4464.0   \n", "          5511775  1653532796  4466.0   13.5      4465.0      4464.0   \n", "          5511775  1653532797  4465.5    1.0      4465.0      4464.0   \n", "          5511775  1653532798  4466.0   10.0      4465.0      4464.0   \n", "          5511775  1653532799  4465.5    1.5      4465.0      4464.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511775       33.0       58.0      4470.0      4471.0      210.5   \n", "          5511775      169.5       38.5      4470.5      4471.5       54.0   \n", "          5511775      280.5       16.5      4471.0      4472.0       54.5   \n", "          5511775      238.0       15.0      4471.0      4472.0       43.5   \n", "          5511775      238.5       15.0      4471.0      4472.0       14.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511775       25.5       34.0      4466.0      4467.0       23.5   \n", "          5511775       16.0       34.0      4466.0      4467.0       13.0   \n", "          5511775        8.5       34.0      4466.0      4467.0       17.0   \n", "          5511775        4.0       34.0      4466.0      4467.0       12.0   \n", "          5511775        3.5       34.0      4466.0      4467.0       14.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511775       68.5  \n", "          5511775      112.5  \n", "          5511775      152.0  \n", "          5511775      152.0  \n", "          5511775      152.0  \n", "...                      ...  \n", "          5511775       25.0  \n", "          5511775       25.0  \n", "          5511775       25.0  \n", "          5511775       25.0  \n", "          5511775       25.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511776),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511776  1653532800  4465.0   2.5      4464.5      4463.5   \n", "          5511776  1653532801  4465.0   1.5      4465.0      4464.0   \n", "          5511776  1653532802  4465.0   3.5      4465.0      4464.0   \n", "          5511776  1653532803  4465.0   1.0      4465.0      4464.0   \n", "          5511776  1653532804  4465.5   7.5      4465.0      4464.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511776  1653533095  4466.0   5.5      4465.0      4464.0   \n", "          5511776  1653533096  4465.0   5.0      4465.0      4464.0   \n", "          5511776  1653533097  4465.0   2.5      4465.0      4464.0   \n", "          5511776  1653533098  4465.5   0.5      4465.0      4464.0   \n", "          5511776  1653533099  4466.0  27.5      4466.0      4465.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511776       18.0       31.0      4465.5      4466.5       11.5   \n", "          5511776        9.5       34.0      4466.0      4467.0       27.0   \n", "          5511776       13.0       34.0      4466.0      4467.0       23.0   \n", "          5511776       11.5       34.0      4466.0      4467.0       24.0   \n", "          5511776        8.0       34.0      4466.0      4467.0       31.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511776       11.5       26.0      4466.0      4467.0       47.5   \n", "          5511776        3.5       26.0      4466.0      4467.0       46.5   \n", "          5511776       14.0       26.0      4466.0      4467.0       47.0   \n", "          5511776       16.0       27.0      4466.0      4467.0       45.5   \n", "          5511776       21.0       11.0      4467.0      4468.0       15.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511776       23.5  \n", "          5511776       26.0  \n", "          5511776       26.0  \n", "          5511776       28.5  \n", "          5511776       29.0  \n", "...                      ...  \n", "          5511776       15.0  \n", "          5511776       15.0  \n", "          5511776       15.0  \n", "          5511776       15.0  \n", "          5511776       48.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511777),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511777  1653533100  4466.5   1.0      4466.0      4465.0   \n", "          5511777  1653533101  4468.0  30.5      4467.0      4466.0   \n", "          5511777  1653533102  4467.5   2.5      4467.0      4466.0   \n", "          5511777  1653533103  4467.5  11.5      4467.5      4466.5   \n", "          5511777  1653533104  4468.0  14.0      4468.0      4467.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511777  1653533395  4471.5   2.5      4471.0      4470.0   \n", "          5511777  1653533396  4471.5   1.5      4471.0      4470.0   \n", "          5511777  1653533397  4472.0   2.0      4471.0      4470.0   \n", "          5511777  1653533398  4471.5   8.5      4471.0      4470.0   \n", "          5511777  1653533399  4471.5   7.5      4471.0      4470.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511777       23.5       11.0      4467.0      4468.0       15.0   \n", "          5511777       12.5       33.5      4468.0      4469.0        8.0   \n", "          5511777       22.0       34.0      4468.0      4469.0       11.0   \n", "          5511777       60.0       26.0      4468.5      4469.5       22.5   \n", "          5511777       87.5       18.0      4469.0      4470.0       28.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511777       37.0      438.0      4472.0      4473.0       21.0   \n", "          5511777       29.5      438.0      4472.0      4473.0       19.5   \n", "          5511777       36.0      438.0      4472.0      4473.0       17.5   \n", "          5511777       21.0      438.0      4472.0      4473.0       17.0   \n", "          5511777       23.0      438.0      4472.0      4473.0       15.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511777       49.0  \n", "          5511777       31.0  \n", "          5511777       30.0  \n", "          5511777       63.5  \n", "          5511777       92.0  \n", "...                      ...  \n", "          5511777      327.0  \n", "          5511777      327.0  \n", "          5511777      327.0  \n", "          5511777      327.0  \n", "          5511777      327.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511778),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511778  1653533400  4472.5   13.5      4472.0      4471.0   \n", "          5511778  1653533401  4472.5    6.5      4472.0      4471.0   \n", "          5511778  1653533402  4473.0  174.0      4472.5      4471.5   \n", "          5511778  1653533403  4473.0    2.0      4473.0      4472.0   \n", "          5511778  1653533404  4473.5    1.0      4473.0      4472.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511778  1653533695  4475.0    3.5      4474.0      4473.0   \n", "          5511778  1653533696  4475.0    3.0      4475.0      4474.0   \n", "          5511778  1653533697  4474.5   26.5      4474.5      4473.5   \n", "          5511778  1653533698  4475.0   24.0      4474.0      4473.0   \n", "          5511778  1653533699  4475.5    5.5      4474.0      4473.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511778       24.5       42.5      4473.0      4474.0      326.0   \n", "          5511778       51.5       43.5      4473.0      4474.0      327.0   \n", "          5511778       29.5       49.5      4473.5      4474.5        9.5   \n", "          5511778       31.5       50.0      4474.0      4475.0       56.5   \n", "          5511778       31.0       51.0      4474.0      4475.0       53.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511778       39.5       33.0      4475.5      4476.5       15.0   \n", "          5511778        3.5       38.5      4476.0      4477.0       36.0   \n", "          5511778       10.0       17.0      4475.5      4476.5       16.5   \n", "          5511778       15.5       33.0      4475.5      4476.5       20.5   \n", "          5511778       29.5       33.0      4475.0      4476.0        1.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511778       28.0  \n", "          5511778       28.0  \n", "          5511778      237.5  \n", "          5511778      444.5  \n", "          5511778      444.0  \n", "...                      ...  \n", "          5511778       50.5  \n", "          5511778       77.0  \n", "          5511778       59.0  \n", "          5511778       53.5  \n", "          5511778       30.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511779),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511779  1653533700  4474.5   1.0      4474.5      4473.5   \n", "          5511779  1653533701  4475.5   7.5      4474.5      4473.5   \n", "          5511779  1653533702  4476.0   3.5      4474.0      4473.0   \n", "          5511779  1653533703  4475.0   1.0      4474.0      4473.0   \n", "          5511779  1653533704  4474.5   1.0      4474.0      4473.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511779  1653533995  4490.5   7.0      4490.0      4489.0   \n", "          5511779  1653533996  4490.0   3.5      4490.0      4489.0   \n", "          5511779  1653533997  4491.0  14.0      4490.5      4489.5   \n", "          5511779  1653533998  4491.5  13.5      4491.0      4490.0   \n", "          5511779  1653533999  4491.0   5.0      4491.0      4490.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511779       15.0       31.0      4475.5      4476.5       16.0   \n", "          5511779       11.0       25.0      4475.5      4476.5       19.5   \n", "          5511779       19.5       34.0      4475.5      4476.5       25.5   \n", "          5511779       22.0       34.0      4475.0      4476.0        4.0   \n", "          5511779       21.0       34.0      4475.0      4476.0       13.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511779       10.0       47.5      4491.0      4492.0       23.0   \n", "          5511779       13.5       50.0      4491.0      4492.0       19.0   \n", "          5511779       17.0       26.5      4491.5      4492.5       21.0   \n", "          5511779       12.0       10.0      4492.0      4493.0       12.5   \n", "          5511779        5.5       10.0      4492.0      4493.0        8.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511779       55.0  \n", "          5511779       59.5  \n", "          5511779       58.5  \n", "          5511779       38.0  \n", "          5511779       41.0  \n", "...                      ...  \n", "          5511779       26.0  \n", "          5511779       25.0  \n", "          5511779       25.0  \n", "          5511779       25.0  \n", "          5511779       25.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511780),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511780  1653534000  4491.5   8.5      4491.0      4490.0   \n", "          5511780  1653534001  4492.5   5.0      4492.0      4491.0   \n", "          5511780  1653534002  4492.5  33.0      4492.0      4491.0   \n", "          5511780  1653534003  4493.5  27.5      4492.5      4491.5   \n", "          5511780  1653534004  4492.0  26.5      4490.5      4489.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511780  1653534295  4485.0   4.0      4484.5      4483.5   \n", "          5511780  1653534296  4484.0  21.0      4483.0      4482.0   \n", "          5511780  1653534297  4484.0   7.5      4483.0      4482.0   \n", "          5511780  1653534298  4483.5   1.5      4483.0      4482.0   \n", "          5511780  1653534299  4483.5   4.0      4483.0      4482.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511780        6.0       10.0      4492.0      4493.0        9.5   \n", "          5511780        8.0       27.5      4493.0      4494.0       40.5   \n", "          5511780       30.5       29.0      4493.0      4494.0       24.0   \n", "          5511780       24.0       36.0      4494.0      4495.0       51.0   \n", "          5511780       26.5       43.5      4492.0      4493.0       14.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511780       18.0       64.5      4485.5      4486.5      123.0   \n", "          5511780       95.5       43.0      4484.5      4485.5       96.0   \n", "          5511780       95.0       43.0      4484.0      4485.0       27.0   \n", "          5511780       95.5       43.0      4484.0      4485.0       18.0   \n", "          5511780       88.5       43.0      4484.0      4485.0       22.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511780       29.0  \n", "          5511780       57.0  \n", "          5511780       55.0  \n", "          5511780       71.0  \n", "          5511780       34.5  \n", "...                      ...  \n", "          5511780       58.0  \n", "          5511780      120.0  \n", "          5511780      190.5  \n", "          5511780      193.0  \n", "          5511780      194.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511781),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511781  1653534300  4483.5  12.0      4483.0      4482.0   \n", "          5511781  1653534301  4484.0   5.5      4483.0      4482.0   \n", "          5511781  1653534302  4483.0   0.5      4483.0      4482.0   \n", "          5511781  1653534303  4483.5   5.0      4483.0      4482.0   \n", "          5511781  1653534304  4483.5  11.5      4483.0      4482.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511781  1653534595  4478.0   0.5      4478.0      4477.0   \n", "          5511781  1653534596  4478.5   6.5      4478.0      4477.0   \n", "          5511781  1653534597  4478.0   1.0      4477.0      4476.0   \n", "          5511781  1653534598  4478.0   0.5      4477.0      4476.0   \n", "          5511781  1653534599  4478.0   2.5      4477.5      4476.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511781       88.0       43.0      4484.0      4485.0       16.5   \n", "          5511781       85.5       51.0      4484.0      4485.0       16.5   \n", "          5511781       90.0       52.0      4484.0      4485.0       24.5   \n", "          5511781       92.5       52.5      4484.0      4485.0       27.5   \n", "          5511781       84.5       53.0      4484.0      4485.0       26.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511781        1.5       38.0      4479.0      4480.0       41.5   \n", "          5511781        1.5       39.0      4479.0      4480.0       35.5   \n", "          5511781       41.5       92.0      4478.0      4479.0        2.5   \n", "          5511781       44.5       92.0      4478.0      4479.0        4.5   \n", "          5511781       23.0       69.0      4478.5      4479.5       26.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511781      198.0  \n", "          5511781      202.0  \n", "          5511781      202.0  \n", "          5511781      202.0  \n", "          5511781      202.5  \n", "...                      ...  \n", "          5511781       47.0  \n", "          5511781       47.0  \n", "          5511781       40.5  \n", "          5511781       40.0  \n", "          5511781       44.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511782),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511782  1653534600  4477.5  11.0      4477.0      4476.0   \n", "          5511782  1653534601  4477.0   0.5      4477.0      4476.0   \n", "          5511782  1653534602  4478.0   0.5      4477.0      4476.0   \n", "          5511782  1653534603  4478.0   0.0      4477.0      4476.0   \n", "          5511782  1653534604  4478.0   0.0      4477.0      4476.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511782  1653534895  4480.0   5.5      4479.0      4478.0   \n", "          5511782  1653534896  4479.5   3.5      4479.0      4478.0   \n", "          5511782  1653534897  4480.0  15.0      4479.0      4478.0   \n", "          5511782  1653534898  4480.0   1.5      4479.0      4478.0   \n", "          5511782  1653534899  4480.0   0.0      4479.0      4478.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511782       38.0       92.0      4478.0      4479.0        3.0   \n", "          5511782       29.0       92.0      4478.0      4479.0       41.5   \n", "          5511782       34.5       92.0      4478.0      4479.0       46.0   \n", "          5511782       35.0       92.0      4478.0      4479.0       28.0   \n", "          5511782       35.0       93.0      4478.0      4479.0       29.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511782       49.0       36.0      4480.0      4481.0       73.0   \n", "          5511782       49.0       36.5      4480.0      4481.0       70.0   \n", "          5511782       49.5       37.0      4480.0      4481.0       39.5   \n", "          5511782       50.5       37.0      4480.0      4481.0       36.0   \n", "          5511782       52.5       37.0      4480.0      4481.0       41.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511782       49.5  \n", "          5511782       49.0  \n", "          5511782       49.0  \n", "          5511782       49.0  \n", "          5511782       49.0  \n", "...                      ...  \n", "          5511782       55.0  \n", "          5511782       54.0  \n", "          5511782       54.0  \n", "          5511782       54.0  \n", "          5511782       48.5  \n", "\n", "[297 rows x 11 columns])\n", "(('BU8888.SC', 5511783),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511783  1653534900  4479.0  27.0      4479.0      4478.0   \n", "          5511783  1653534901  4480.0  14.0      4479.5      4478.5   \n", "          5511783  1653534902  4481.0  12.5      4480.0      4479.0   \n", "          5511783  1653534903  4481.0   5.0      4480.0      4479.0   \n", "          5511783  1653534904  4481.0   4.0      4480.0      4479.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511783  1653535195  4479.5   2.0      4479.0      4478.0   \n", "          5511783  1653535196  4479.0   1.0      4479.0      4478.0   \n", "          5511783  1653535197  4479.0   0.0      4479.0      4478.0   \n", "          5511783  1653535198  4479.0   5.5      4478.5      4477.5   \n", "          5511783  1653535199  4478.0   6.0      4478.0      4477.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511783       45.0       37.0      4480.0      4481.0       31.0   \n", "          5511783       28.5       37.0      4480.5      4481.5       26.5   \n", "          5511783       31.5       32.0      4481.0      4482.0       10.5   \n", "          5511783       45.0       29.5      4481.0      4482.0       15.5   \n", "          5511783       48.5       28.0      4481.0      4482.0        7.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511783        8.0       75.0      4480.0      4481.0       32.0   \n", "          5511783       21.0       75.0      4480.0      4481.0       35.0   \n", "          5511783       18.5       75.0      4480.0      4481.0       35.0   \n", "          5511783       43.0       56.0      4479.5      4480.5       23.0   \n", "          5511783       44.5       37.0      4479.0      4480.0       19.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511783       43.0  \n", "          5511783       42.5  \n", "          5511783       42.0  \n", "          5511783       42.0  \n", "          5511783       42.0  \n", "...                      ...  \n", "          5511783       17.5  \n", "          5511783       18.0  \n", "          5511783       18.0  \n", "          5511783       28.0  \n", "          5511783       38.5  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5511784),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511784  1653535200  4478.0  13.5      4477.5      4476.5   \n", "          5511784  1653535201  4478.0   2.0      4478.0      4477.0   \n", "          5511784  1653535202  4478.0   8.0      4477.0      4476.0   \n", "          5511784  1653535203  4478.0   0.0      4477.0      4476.0   \n", "          5511784  1653535204  4477.5   0.5      4477.0      4476.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511784  1653535495  4483.5   1.5      4483.0      4482.0   \n", "          5511784  1653535496  4483.5   6.5      4483.0      4482.0   \n", "          5511784  1653535497  4484.0   1.5      4483.0      4482.0   \n", "          5511784  1653535498  4484.0   0.0      4483.0      4482.0   \n", "          5511784  1653535499  4484.0   1.0      4483.0      4482.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511784       31.5       37.5      4479.0      4480.0       41.0   \n", "          5511784       11.0       38.0      4479.0      4480.0       45.0   \n", "          5511784       39.0       36.0      4479.0      4480.0       55.0   \n", "          5511784       39.0       36.0      4478.0      4479.0        3.0   \n", "          5511784       46.5       36.0      4478.0      4479.0        3.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511784       35.0       20.0      4484.0      4485.0       11.5   \n", "          5511784       32.0       21.5      4484.0      4485.0       11.0   \n", "          5511784       33.0       22.0      4484.0      4485.0        3.5   \n", "          5511784       33.0       22.0      4484.0      4485.0        4.0   \n", "          5511784       33.0       22.0      4484.0      4485.0        3.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511784       40.5  \n", "          5511784       43.0  \n", "          5511784       43.0  \n", "          5511784       58.0  \n", "          5511784       72.5  \n", "...                      ...  \n", "          5511784       31.0  \n", "          5511784       31.0  \n", "          5511784       31.0  \n", "          5511784       32.5  \n", "          5511784       33.0  \n", "\n", "[296 rows x 11 columns])\n", "(('BU8888.SC', 5511785),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511785  1653535500  4484.0   1.5      4483.0      4482.0   \n", "          5511785  1653535501  4484.5   2.5      4483.0      4482.0   \n", "          5511785  1653535502  4483.5   1.5      4483.0      4482.0   \n", "          5511785  1653535503  4484.0   7.0      4482.0      4481.0   \n", "          5511785  1653535504  4484.0   0.0      4482.0      4481.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511785  1653535795  4489.0   2.5      4488.0      4487.0   \n", "          5511785  1653535796  4488.5   4.5      4488.0      4487.0   \n", "          5511785  1653535797  4488.0   6.5      4487.5      4486.5   \n", "          5511785  1653535798  4489.0   3.5      4488.0      4487.0   \n", "          5511785  1653535799  4488.0   3.5      4487.0      4486.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511785       33.0       16.5      4484.5      4485.5       18.0   \n", "          5511785       13.0       18.0      4484.5      4485.5       15.0   \n", "          5511785       14.0       18.0      4484.0      4485.0       19.5   \n", "          5511785       17.5       37.0      4484.0      4485.0       30.0   \n", "          5511785       18.5       37.0      4483.0      4484.0       24.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511785       10.0        9.0      4489.0      4490.0        4.0   \n", "          5511785        6.0        9.0      4489.0      4490.0        3.0   \n", "          5511785        2.0       17.0      4489.0      4490.0       13.0   \n", "          5511785        5.5        1.5      4489.0      4490.0       13.0   \n", "          5511785        3.0       26.0      4488.0      4489.0        2.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511785       46.5  \n", "          5511785       49.5  \n", "          5511785       31.0  \n", "          5511785       32.0  \n", "          5511785       42.0  \n", "...                      ...  \n", "          5511785       91.0  \n", "          5511785       90.0  \n", "          5511785       89.0  \n", "          5511785       91.0  \n", "          5511785       12.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511810),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511810  1653543001  4489.0   45.5      4486.5      4485.5   \n", "          5511810  1653543002  4488.5   16.5      4488.0      4487.0   \n", "          5511810  1653543003  4489.0   10.0      4487.0      4486.0   \n", "          5511810  1653543004  4488.5   25.0      4487.5      4486.0   \n", "          5511810  1653543005  4488.0   18.0      4487.0      4485.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511810  1653543295  4491.5   35.0      4491.0      4490.0   \n", "          5511810  1653543296  4492.0   16.5      4492.0      4491.0   \n", "          5511810  1653543297  4492.0    6.5      4492.0      4491.0   \n", "          5511810  1653543298  4492.0    4.5      4492.0      4491.0   \n", "          5511810  1653543299  4493.0  106.0      4492.5      4491.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511810       15.0       38.5      4489.0      4490.0       13.5   \n", "          5511810        8.0       17.5      4489.5      4490.5       45.5   \n", "          5511810       14.0       25.0      4489.0      4490.0       14.0   \n", "          5511810       11.0       25.0      4488.5      4489.5       11.5   \n", "          5511810       14.0       34.5      4488.5      4489.5       22.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511810       49.0       91.0      4492.0      4493.0       43.0   \n", "          5511810        5.5       58.5      4493.0      4494.0      196.0   \n", "          5511810       27.5       54.0      4493.0      4494.0      196.0   \n", "          5511810       28.0       54.5      4493.0      4494.0      194.0   \n", "          5511810       14.5       35.5      4493.5      4494.5      111.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511810       88.5  \n", "          5511810       60.5  \n", "          5511810       86.0  \n", "          5511810       55.0  \n", "          5511810       54.5  \n", "...                      ...  \n", "          5511810      196.0  \n", "          5511810       69.0  \n", "          5511810       68.0  \n", "          5511810       68.0  \n", "          5511810      105.5  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511811),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511811  1653543300  4493.5   6.0      4493.0      4492.0   \n", "          5511811  1653543301  4493.0   4.5      4493.0      4492.0   \n", "          5511811  1653543302  4493.0   5.5      4493.0      4492.0   \n", "          5511811  1653543303  4493.5  27.0      4493.0      4492.0   \n", "          5511811  1653543304  4493.0  10.5      4493.0      4492.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511811  1653543595  4495.0   0.0      4495.0      4494.0   \n", "          5511811  1653543596  4495.5   4.5      4495.0      4494.0   \n", "          5511811  1653543597  4495.0   4.0      4495.0      4494.0   \n", "          5511811  1653543598  4495.0   0.5      4495.0      4494.0   \n", "          5511811  1653543599  4495.0   0.5      4495.0      4494.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511811       21.5       15.5      4494.0      4495.0       63.5   \n", "          5511811       38.0       15.0      4494.0      4495.0       60.0   \n", "          5511811       37.5       15.0      4494.0      4495.0       55.0   \n", "          5511811       46.5       15.0      4494.0      4495.0       13.0   \n", "          5511811       49.0       15.5      4494.5      4495.5       80.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511811       28.0       26.0      4496.0      4497.0       21.5   \n", "          5511811       29.5       26.0      4496.0      4497.0       30.0   \n", "          5511811       26.5       26.0      4496.0      4497.0       36.0   \n", "          5511811       20.5       26.0      4496.0      4497.0       38.5   \n", "          5511811       20.0       26.0      4496.0      4497.0       39.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511811      141.0  \n", "          5511811      142.5  \n", "          5511811      142.0  \n", "          5511811      142.0  \n", "          5511811       92.0  \n", "...                      ...  \n", "          5511811       36.0  \n", "          5511811       34.0  \n", "          5511811       34.5  \n", "          5511811       36.5  \n", "          5511811       37.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511812),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511812  1653543600  4495.0   0.5      4495.0      4494.0   \n", "          5511812  1653543601  4495.0   3.5      4495.0      4494.0   \n", "          5511812  1653543602  4495.0   9.0      4494.0      4493.0   \n", "          5511812  1653543603  4495.0   1.0      4494.0      4493.0   \n", "          5511812  1653543604  4494.0   8.0      4494.0      4493.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511812  1653543895  4481.0   0.0      4481.0      4480.0   \n", "          5511812  1653543896  4482.0   4.0      4482.0      4481.0   \n", "          5511812  1653543897  4482.5   1.0      4482.0      4481.0   \n", "          5511812  1653543898  4482.0   3.5      4482.0      4481.0   \n", "          5511812  1653543899  4482.5   2.5      4481.0      4480.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511812       19.0       26.0      4496.0      4497.0       40.0   \n", "          5511812       19.0       26.0      4496.0      4497.0       40.0   \n", "          5511812       23.0       33.0      4495.0      4496.0       12.5   \n", "          5511812       18.0       32.0      4495.0      4496.0       21.0   \n", "          5511812        7.0       32.0      4495.0      4496.0       28.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511812       75.5      159.0      4482.0      4483.0        3.5   \n", "          5511812        2.5       90.5      4483.0      4484.0       44.0   \n", "          5511812        7.0       91.5      4483.0      4484.0       35.0   \n", "          5511812        9.5       92.0      4483.0      4484.0       38.0   \n", "          5511812       92.5      160.0      4483.0      4484.0       37.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511812       44.5  \n", "          5511812       46.0  \n", "          5511812       56.5  \n", "          5511812       57.5  \n", "          5511812       58.5  \n", "...                      ...  \n", "          5511812       44.0  \n", "          5511812       18.0  \n", "          5511812       18.0  \n", "          5511812       18.0  \n", "          5511812       18.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511813),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511813  1653543900  4482.0   3.5      4481.0      4480.0   \n", "          5511813  1653543901  4482.0   3.0      4481.0      4480.0   \n", "          5511813  1653543902  4482.0  45.0      4481.5      4480.5   \n", "          5511813  1653543903  4482.0   4.5      4482.0      4481.0   \n", "          5511813  1653543904  4482.5  11.0      4482.0      4481.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511813  1653544195  4482.0   1.5      4481.0      4480.0   \n", "          5511813  1653544196  4481.5  15.0      4481.0      4480.0   \n", "          5511813  1653544197  4481.0   0.0      4481.0      4480.0   \n", "          5511813  1653544198  4481.0   3.0      4481.0      4480.0   \n", "          5511813  1653544199  4481.5   7.5      4481.0      4480.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511813       92.0      160.0      4482.0      4483.0       78.5   \n", "          5511813      101.0      160.0      4482.0      4483.0       73.0   \n", "          5511813       52.0      130.5      4482.5      4483.5       62.0   \n", "          5511813       26.5       93.0      4483.0      4484.0       37.5   \n", "          5511813       15.0       90.0      4483.0      4484.0       35.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511813       56.0      246.5      4482.0      4483.0       19.0   \n", "          5511813       42.0      249.0      4482.0      4483.0       17.0   \n", "          5511813       28.0      249.0      4482.0      4483.0       18.5   \n", "          5511813       24.0      249.0      4482.0      4483.0       20.0   \n", "          5511813       31.5      249.0      4482.0      4483.0       13.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511813       37.0  \n", "          5511813       37.0  \n", "          5511813       27.5  \n", "          5511813       18.0  \n", "          5511813       18.0  \n", "...                      ...  \n", "          5511813       75.0  \n", "          5511813       75.0  \n", "          5511813       76.5  \n", "          5511813       76.5  \n", "          5511813       77.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511814),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511814  1653544200  4482.5  46.5      4482.0      4481.0   \n", "          5511814  1653544201  4483.0  23.0      4482.0      4481.0   \n", "          5511814  1653544202  4483.0   0.0      4482.0      4481.0   \n", "          5511814  1653544203  4481.5  41.5      4481.0      4480.0   \n", "          5511814  1653544204  4481.0  14.0      4481.0      4480.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511814  1653544495  4477.0   3.5      4477.0      4476.0   \n", "          5511814  1653544496  4477.0   0.0      4477.5      4476.5   \n", "          5511814  1653544497  4477.0   0.0      4478.0      4477.0   \n", "          5511814  1653544498  4479.0   1.0      4478.0      4477.0   \n", "          5511814  1653544499  4478.0   3.0      4478.0      4477.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511814       50.5       39.5      4483.0      4484.0       46.0   \n", "          5511814       55.0       39.0      4483.0      4484.0        4.0   \n", "          5511814       60.0       41.5      4483.0      4484.0       46.0   \n", "          5511814       25.0      249.0      4482.0      4483.0       41.0   \n", "          5511814        8.5      249.0      4482.0      4483.0       47.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511814       32.0       27.0      4479.0      4480.0       38.0   \n", "          5511814       17.0       30.5      4479.0      4480.0       38.0   \n", "          5511814        4.5       34.5      4479.0      4480.0       38.0   \n", "          5511814        8.0       37.0      4479.0      4480.0       36.5   \n", "          5511814       16.0       37.0      4479.0      4480.0       37.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511814       19.0  \n", "          5511814       16.5  \n", "          5511814       17.0  \n", "          5511814       63.0  \n", "          5511814       61.5  \n", "...                      ...  \n", "          5511814       72.0  \n", "          5511814       72.0  \n", "          5511814       72.0  \n", "          5511814       72.0  \n", "          5511814       72.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511815),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511815  1653544500  4478.5   2.0      4478.0      4477.0   \n", "          5511815  1653544501  4479.0  22.0      4478.0      4477.0   \n", "          5511815  1653544502  4479.0   7.0      4478.5      4477.5   \n", "          5511815  1653544503  4478.5   5.0      4478.0      4477.0   \n", "          5511815  1653544504  4478.0   0.5      4478.0      4477.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511815  1653544795  4485.0   0.0      4484.0      4483.0   \n", "          5511815  1653544796  4485.0   0.0      4484.0      4483.0   \n", "          5511815  1653544797  4484.0   3.0      4484.0      4483.0   \n", "          5511815  1653544798  4485.0  15.5      4484.5      4483.5   \n", "          5511815  1653544799  4486.0   3.5      4485.0      4484.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511815       15.5       37.0      4479.0      4480.0       32.5   \n", "          5511815       19.5       37.5      4479.0      4480.0       23.5   \n", "          5511815       15.0       38.0      4480.0      4481.0       71.0   \n", "          5511815       33.5       38.0      4479.5      4480.5       49.0   \n", "          5511815       26.5       38.0      4479.0      4480.0        4.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511815       75.0       48.0      4485.0      4486.0       14.0   \n", "          5511815       76.0       48.0      4485.0      4486.0       16.5   \n", "          5511815       71.0       48.0      4485.0      4486.0       16.5   \n", "          5511815       36.0       59.5      4485.5      4486.5       45.0   \n", "          5511815       16.5       70.0      4486.0      4487.0       68.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511815       62.0  \n", "          5511815       52.0  \n", "          5511815       55.5  \n", "          5511815       74.5  \n", "          5511815       98.0  \n", "...                      ...  \n", "          5511815       94.0  \n", "          5511815       94.0  \n", "          5511815       91.5  \n", "          5511815       89.5  \n", "          5511815       90.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511816),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511816  1653544800  4485.0   7.0      4485.0      4484.0   \n", "          5511816  1653544801  4485.0   1.0      4485.0      4484.0   \n", "          5511816  1653544802  4485.0  10.0      4484.0      4483.0   \n", "          5511816  1653544803  4484.0  11.0      4484.0      4483.0   \n", "          5511816  1653544804  4484.0  11.5      4484.0      4483.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511816  1653545095  4472.0   2.0      4471.0      4470.0   \n", "          5511816  1653545096  4471.0   5.5      4471.0      4470.0   \n", "          5511816  1653545097  4471.0   1.0      4471.0      4470.0   \n", "          5511816  1653545098  4471.5   6.0      4471.0      4470.0   \n", "          5511816  1653545099  4471.0   3.0      4471.0      4470.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511816       12.0       70.0      4486.0      4487.0       63.5   \n", "          5511816       14.0       70.0      4486.0      4487.0       79.5   \n", "          5511816       70.5       49.5      4485.0      4486.0       35.5   \n", "          5511816       58.0       51.0      4485.0      4486.0       63.0   \n", "          5511816       39.5       43.0      4485.0      4486.0       69.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511816       13.5       70.5      4472.0      4473.0       36.5   \n", "          5511816        9.5       74.5      4472.0      4473.0       30.0   \n", "          5511816        9.0       75.0      4472.0      4473.0       27.0   \n", "          5511816       12.5       73.0      4472.0      4473.0       16.5   \n", "          5511816       17.5       74.0      4472.0      4473.0       14.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511816       90.0  \n", "          5511816       90.0  \n", "          5511816       74.0  \n", "          5511816       65.0  \n", "          5511816       65.5  \n", "...                      ...  \n", "          5511816       38.0  \n", "          5511816       38.0  \n", "          5511816       38.0  \n", "          5511816       38.0  \n", "          5511816       38.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511817),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511817  1653545100  4471.0  11.0      4471.5      4470.5   \n", "          5511817  1653545101  4471.0   3.5      4472.0      4471.0   \n", "          5511817  1653545102  4471.5   3.0      4472.0      4471.0   \n", "          5511817  1653545103  4473.0  11.0      4472.0      4471.0   \n", "          5511817  1653545104  4472.0   5.0      4472.0      4471.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511817  1653545395  4477.0   1.0      4476.0      4475.0   \n", "          5511817  1653545396  4477.0   2.0      4476.0      4475.0   \n", "          5511817  1653545397  4477.0   0.0      4476.0      4475.0   \n", "          5511817  1653545398  4477.0   0.0      4476.0      4475.0   \n", "          5511817  1653545399  4477.0   5.0      4476.0      4475.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511817       16.5       54.5      4472.5      4473.5       20.5   \n", "          5511817       23.5       25.0      4473.0      4474.0       39.0   \n", "          5511817       24.0       29.0      4473.0      4474.0       69.5   \n", "          5511817       12.5       32.0      4473.0      4474.0      101.0   \n", "          5511817        5.0       33.0      4473.0      4474.0      117.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511817       16.0       46.0      4477.0      4478.0       11.0   \n", "          5511817       15.0       46.0      4477.0      4478.0       10.0   \n", "          5511817       15.0       46.0      4477.0      4478.0       10.5   \n", "          5511817        6.0       46.0      4477.0      4478.0       11.0   \n", "          5511817        8.0       44.0      4477.0      4478.0        1.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511817       58.5  \n", "          5511817       79.0  \n", "          5511817       79.0  \n", "          5511817       79.0  \n", "          5511817       79.0  \n", "...                      ...  \n", "          5511817       49.0  \n", "          5511817       49.0  \n", "          5511817       50.5  \n", "          5511817       51.0  \n", "          5511817       51.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511818),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511818  1653545400  4475.5  12.0      4475.0      4474.0   \n", "          5511818  1653545401  4475.5   9.5      4475.0      4474.0   \n", "          5511818  1653545402  4476.0  11.0      4475.0      4474.0   \n", "          5511818  1653545403  4476.0  35.5      4475.0      4474.0   \n", "          5511818  1653545404  4476.0   1.5      4476.0      4475.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511818  1653545694  4476.0   0.0      4475.0      4474.0   \n", "          5511818  1653545695  4476.0   0.0      4475.0      4474.0   \n", "          5511818  1653545696  4476.0   0.5      4475.0      4474.0   \n", "          5511818  1653545698  4476.0   1.5      4475.0      4474.0   \n", "          5511818  1653545699  4476.0   1.5      4475.0      4474.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511818       39.0       42.0      4477.0      4478.0       24.5   \n", "          5511818       15.5       43.0      4476.0      4477.0       21.0   \n", "          5511818       12.0       44.0      4476.0      4477.0       36.5   \n", "          5511818       23.5       44.0      4476.0      4477.0       10.5   \n", "          5511818       13.0       30.0      4477.0      4478.0       18.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511818       37.0       57.0      4476.0      4477.0       19.0   \n", "          5511818       37.5       57.0      4476.0      4477.0       19.0   \n", "          5511818       38.5       57.0      4476.0      4477.0       18.0   \n", "          5511818       39.5       57.0      4476.0      4477.0       17.0   \n", "          5511818       40.5       57.0      4476.0      4477.0       16.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511818       51.0  \n", "          5511818       51.0  \n", "          5511818       45.5  \n", "          5511818       38.5  \n", "          5511818       51.0  \n", "...                      ...  \n", "          5511818       40.0  \n", "          5511818       40.0  \n", "          5511818       40.0  \n", "          5511818       40.0  \n", "          5511818       40.0  \n", "\n", "[291 rows x 11 columns])\n", "(('BU8888.SC', 5511819),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511819  1653545700  4476.0   5.5      4475.5      4474.5   \n", "          5511819  1653545701  4475.5   8.0      4475.0      4474.0   \n", "          5511819  1653545702  4476.0  20.0      4475.5      4474.5   \n", "          5511819  1653545703  4476.0   0.5      4476.0      4475.0   \n", "          5511819  1653545704  4476.5  20.0      4476.0      4475.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511819  1653545995  4471.0   8.5      4470.0      4469.0   \n", "          5511819  1653545996  4470.5  33.5      4470.0      4469.0   \n", "          5511819  1653545997  4471.0   7.0      4470.0      4469.0   \n", "          5511819  1653545998  4470.5   2.5      4470.0      4469.0   \n", "          5511819  1653545999  4471.0   4.5      4470.0      4469.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511819       23.0       52.0      4476.5      4477.5       24.5   \n", "          5511819       35.0       57.0      4476.5      4477.5       21.0   \n", "          5511819       25.5       46.5      4476.5      4477.5       20.0   \n", "          5511819       15.0       35.0      4477.0      4478.0       36.0   \n", "          5511819       14.5       34.0      4477.0      4478.0       20.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511819      174.0      108.0      4471.0      4472.0       30.0   \n", "          5511819      165.0      106.0      4471.0      4472.0        4.0   \n", "          5511819      163.0      106.0      4471.0      4472.0        7.0   \n", "          5511819      161.5      107.5      4471.0      4472.0        3.0   \n", "          5511819      159.0      109.0      4471.5      4472.5       10.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511819       33.5  \n", "          5511819       34.0  \n", "          5511819       33.5  \n", "          5511819       28.0  \n", "          5511819       28.0  \n", "...                      ...  \n", "          5511819        8.0  \n", "          5511819       12.5  \n", "          5511819       18.5  \n", "          5511819       19.0  \n", "          5511819       50.0  \n", "\n", "[292 rows x 11 columns])\n", "(('BU8888.SC', 5511820),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511820  1653546000  4470.5   7.0      4471.0      4470.0   \n", "          5511820  1653546001  4471.0   0.5      4470.5      4469.5   \n", "          5511820  1653546002  4472.0   5.5      4471.0      4470.0   \n", "          5511820  1653546003  4471.5   3.5      4471.0      4470.0   \n", "          5511820  1653546004  4472.0   0.0      4471.0      4470.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511820  1653546295  4474.0  15.5      4473.0      4472.0   \n", "          5511820  1653546296  4474.0   5.0      4473.5      4472.5   \n", "          5511820  1653546297  4474.0   0.0      4474.0      4473.0   \n", "          5511820  1653546298  4474.5   0.5      4474.0      4473.0   \n", "          5511820  1653546299  4475.0   0.0      4474.0      4473.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511820        1.0      155.5      4472.0      4473.0       42.0   \n", "          5511820       80.5      132.0      4472.0      4473.0       38.5   \n", "          5511820       17.0      153.0      4472.0      4473.0       35.5   \n", "          5511820       12.5      154.0      4472.0      4473.0       48.5   \n", "          5511820       17.0      154.0      4472.0      4473.0       51.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511820       55.5       36.0      4474.0      4475.0       10.5   \n", "          5511820       69.0       32.5      4474.5      4475.5      159.0   \n", "          5511820       72.0      251.5      4475.0      4476.0      325.0   \n", "          5511820       73.0      258.0      4475.0      4476.0      345.0   \n", "          5511820       73.0      260.0      4475.0      4476.0      345.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511820       60.0  \n", "          5511820       60.0  \n", "          5511820       60.0  \n", "          5511820       60.0  \n", "          5511820       60.0  \n", "...                      ...  \n", "          5511820      293.0  \n", "          5511820      174.5  \n", "          5511820       35.0  \n", "          5511820       35.0  \n", "          5511820       35.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511821),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511821  1653546300  4475.0    0.0      4474.0      4473.0   \n", "          5511821  1653546301  4475.0   55.0      4474.0      4473.0   \n", "          5511821  1653546302  4475.0    0.0      4474.0      4473.0   \n", "          5511821  1653546303  4474.0   39.5      4474.0      4473.0   \n", "          5511821  1653546304  4474.0    6.0      4474.0      4473.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511821  1653546595  4476.0    2.5      4475.5      4474.5   \n", "          5511821  1653546596  4478.0  101.0      4476.0      4475.0   \n", "          5511821  1653546597  4478.5   12.5      4478.0      4477.0   \n", "          5511821  1653546598  4479.0    1.0      4478.0      4477.0   \n", "          5511821  1653546599  4478.5    9.0      4477.0      4476.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511821       74.5      262.5      4475.0      4476.0      348.0   \n", "          5511821      225.5      269.0      4475.0      4476.0      327.5   \n", "          5511821       81.0      269.5      4475.0      4476.0      313.5   \n", "          5511821      330.5      270.0      4475.0      4476.0      343.0   \n", "          5511821      291.5      160.0      4475.0      4476.0      342.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511821       29.0       56.0      4477.0      4478.0        9.5   \n", "          5511821       33.0       32.5      4478.0      4479.0       19.0   \n", "          5511821        8.5       32.0      4479.0      4480.0        3.5   \n", "          5511821        7.0       39.0      4479.0      4480.0        6.5   \n", "          5511821       40.0       16.0      4478.5      4479.5        6.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511821       35.0  \n", "          5511821       35.0  \n", "          5511821       35.0  \n", "          5511821       35.0  \n", "          5511821       35.5  \n", "...                      ...  \n", "          5511821       31.0  \n", "          5511821       25.5  \n", "          5511821       47.5  \n", "          5511821       42.5  \n", "          5511821       31.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511822),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511822  1653546600  4478.0   7.5      4477.0      4476.0   \n", "          5511822  1653546601  4477.5  55.5      4476.5      4475.5   \n", "          5511822  1653546602  4476.0  30.5      4476.0      4475.0   \n", "          5511822  1653546603  4476.5   3.0      4476.0      4475.0   \n", "          5511822  1653546604  4477.0   1.0      4476.0      4475.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511822  1653546895  4472.0   2.0      4470.0      4469.0   \n", "          5511822  1653546896  4472.0   4.0      4470.0      4469.0   \n", "          5511822  1653546897  4471.0   1.0      4470.0      4469.0   \n", "          5511822  1653546898  4471.0   1.0      4470.0      4469.0   \n", "          5511822  1653546899  4471.0   5.0      4470.0      4469.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511822       40.0       16.0      4478.0      4479.0       10.5   \n", "          5511822       29.5       38.5      4477.5      4478.5       10.5   \n", "          5511822       32.0       59.5      4477.0      4478.0        9.5   \n", "          5511822       33.5       58.0      4477.0      4478.0        9.0   \n", "          5511822       35.5       58.0      4477.0      4478.0        9.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511822       24.0       42.0      4471.5      4472.5       13.0   \n", "          5511822       25.5       42.0      4471.0      4472.0        2.5   \n", "          5511822       26.0       42.0      4471.0      4472.0       11.5   \n", "          5511822       27.5       42.0      4471.0      4472.0       11.5   \n", "          5511822       28.5       42.0      4471.0      4472.0        9.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511822       19.5  \n", "          5511822       14.5  \n", "          5511822        7.0  \n", "          5511822       11.5  \n", "          5511822       16.0  \n", "...                      ...  \n", "          5511822       31.5  \n", "          5511822       22.0  \n", "          5511822       21.0  \n", "          5511822       25.5  \n", "          5511822       26.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511823),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511823  1653546900  4471.0  15.5      4470.0      4469.0   \n", "          5511823  1653546901  4471.5   6.0      4471.0      4470.0   \n", "          5511823  1653546902  4471.0   3.5      4471.0      4470.0   \n", "          5511823  1653546903  4471.5   2.0      4471.0      4470.0   \n", "          5511823  1653546904  4471.0   3.0      4471.0      4470.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511823  1653547195  4483.0   8.0      4483.0      4482.0   \n", "          5511823  1653547196  4483.0   0.5      4484.0      4483.0   \n", "          5511823  1653547197  4483.5  11.5      4483.0      4482.0   \n", "          5511823  1653547198  4483.0   3.0      4483.0      4482.0   \n", "          5511823  1653547199  4484.0   0.5      4483.0      4482.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511823       31.5       44.0      4471.0      4472.0        4.0   \n", "          5511823       18.0       30.5      4472.0      4473.0       27.0   \n", "          5511823       20.0       27.5      4472.0      4473.0       21.0   \n", "          5511823       17.5       28.0      4472.0      4473.0       36.0   \n", "          5511823       17.0       27.5      4472.0      4473.0       41.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511823      148.5       27.5      4484.5      4485.5       32.0   \n", "          5511823        3.0      148.0      4485.0      4486.0       52.0   \n", "          5511823      151.0       28.0      4484.0      4485.0       22.5   \n", "          5511823      147.5       29.0      4484.0      4485.0       21.0   \n", "          5511823      147.5       29.5      4484.0      4485.0       20.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511823       26.5  \n", "          5511823       40.0  \n", "          5511823       40.0  \n", "          5511823       40.0  \n", "          5511823       40.0  \n", "...                      ...  \n", "          5511823       60.0  \n", "          5511823       69.0  \n", "          5511823       53.5  \n", "          5511823       54.0  \n", "          5511823       54.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511824),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511824  1653547200  4484.0   5.0      4483.0      4482.0   \n", "          5511824  1653547201  4484.0   0.5      4483.0      4482.0   \n", "          5511824  1653547202  4484.0   0.0      4483.0      4482.0   \n", "          5511824  1653547203  4483.0   2.0      4483.0      4482.0   \n", "          5511824  1653547204  4483.5   1.0      4483.0      4482.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511824  1653547495  4482.0   1.0      4481.0      4480.0   \n", "          5511824  1653547496  4482.0   2.0      4482.0      4481.0   \n", "          5511824  1653547497  4482.5   1.0      4482.0      4481.0   \n", "          5511824  1653547498  4483.0   0.0      4482.0      4481.0   \n", "          5511824  1653547499  4482.0   4.5      4481.5      4480.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511824      149.5       30.0      4484.0      4485.0       20.5   \n", "          5511824      151.0       30.0      4484.0      4485.0       24.5   \n", "          5511824      151.0       30.0      4484.0      4485.0       32.5   \n", "          5511824      147.0       30.0      4484.0      4485.0       38.5   \n", "          5511824      146.5       30.0      4484.0      4485.0       40.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511824       22.0       67.5      4482.0      4483.0        3.0   \n", "          5511824        7.0       22.0      4483.0      4484.0       66.0   \n", "          5511824       10.0       23.5      4483.0      4484.0       65.0   \n", "          5511824       10.5       24.0      4483.0      4484.0       65.0   \n", "          5511824       13.5       46.5      4482.5      4483.5       36.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511824       54.0  \n", "          5511824       54.0  \n", "          5511824       53.0  \n", "          5511824       52.0  \n", "          5511824       52.0  \n", "...                      ...  \n", "          5511824       65.0  \n", "          5511824       39.0  \n", "          5511824       39.0  \n", "          5511824       38.0  \n", "          5511824       51.5  \n", "\n", "[292 rows x 11 columns])\n", "(('BU8888.SC', 5511825),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511825  1653547500  4482.0   0.5      4481.0      4480.0   \n", "          5511825  1653547501  4481.5   1.0      4481.0      4480.0   \n", "          5511825  1653547502  4481.0   0.5      4481.0      4480.0   \n", "          5511825  1653547503  4481.5   5.5      4481.0      4480.0   \n", "          5511825  1653547504  4482.0   0.5      4481.0      4480.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511825  1653547795  4484.0   1.0      4483.0      4482.0   \n", "          5511825  1653547796  4484.0   0.0      4483.0      4482.0   \n", "          5511825  1653547797  4483.0   1.0      4483.0      4482.0   \n", "          5511825  1653547798  4483.0   3.0      4483.0      4482.0   \n", "          5511825  1653547799  4483.5   6.5      4483.0      4482.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511825       25.0       69.0      4482.0      4483.0       11.5   \n", "          5511825       34.5       65.5      4482.0      4483.0       29.5   \n", "          5511825       36.0       66.0      4482.0      4483.0       63.5   \n", "          5511825       33.5       66.0      4482.0      4483.0       67.0   \n", "          5511825       30.5       66.0      4482.0      4483.0       72.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511825       44.0       13.0      4484.0      4485.0       24.5   \n", "          5511825       45.0       13.0      4484.0      4485.0       24.0   \n", "          5511825       43.0       17.0      4484.0      4485.0       23.0   \n", "          5511825       39.0       17.0      4484.0      4485.0       23.0   \n", "          5511825       35.5       17.0      4484.0      4485.0       28.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511825       68.0  \n", "          5511825       73.0  \n", "          5511825       75.5  \n", "          5511825       78.5  \n", "          5511825       80.0  \n", "...                      ...  \n", "          5511825       28.0  \n", "          5511825       30.0  \n", "          5511825       31.0  \n", "          5511825       32.0  \n", "          5511825       33.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511826),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511826  1653547800  4483.5  35.5      4483.5      4482.5   \n", "          5511826  1653547801  4484.0  29.0      4483.5      4482.5   \n", "          5511826  1653547802  4484.0   4.0      4483.0      4482.0   \n", "          5511826  1653547803  4484.0  66.0      4483.0      4482.0   \n", "          5511826  1653547804  4484.0   2.5      4483.0      4482.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511826  1653548095  4492.0   6.5      4490.0      4489.0   \n", "          5511826  1653548096  4490.5  12.5      4490.5      4489.5   \n", "          5511826  1653548097  4492.0  12.5      4491.0      4490.0   \n", "          5511826  1653548098  4492.0  16.5      4491.0      4490.0   \n", "          5511826  1653548099  4491.0  15.0      4491.0      4490.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511826       42.0       24.0      4484.5      4485.5       32.5   \n", "          5511826       66.5       52.5      4484.5      4485.5       22.0   \n", "          5511826      129.0       19.0      4484.0      4485.0       28.5   \n", "          5511826       83.0       19.0      4484.0      4485.0       15.0   \n", "          5511826       55.0       19.0      4484.0      4485.0       45.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511826       31.5       44.0      4491.5      4492.5       24.5   \n", "          5511826       20.5       43.5      4491.5      4492.5       23.5   \n", "          5511826       10.0       43.0      4492.0      4493.0       35.5   \n", "          5511826       13.5       43.0      4492.0      4493.0       14.5   \n", "          5511826       17.5       44.0      4492.0      4493.0        7.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511826       52.5  \n", "          5511826       55.5  \n", "          5511826       38.0  \n", "          5511826       40.0  \n", "          5511826       41.0  \n", "...                      ...  \n", "          5511826       45.0  \n", "          5511826       49.5  \n", "          5511826       58.0  \n", "          5511826       58.0  \n", "          5511826       58.5  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5511827),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511827  1653548100  4492.0  13.5      4491.0      4490.0   \n", "          5511827  1653548101  4492.0  24.0      4491.0      4490.0   \n", "          5511827  1653548102  4492.0  18.5      4490.5      4489.5   \n", "          5511827  1653548103  4492.0  10.5      4490.5      4489.5   \n", "          5511827  1653548104  4491.5  28.0      4491.0      4490.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511827  1653548395  4492.5  55.0      4490.5      4489.5   \n", "          5511827  1653548396  4490.5   8.5      4490.5      4489.5   \n", "          5511827  1653548397  4491.0   6.5      4491.0      4490.0   \n", "          5511827  1653548398  4491.0  11.5      4491.0      4490.0   \n", "          5511827  1653548399  4491.0   9.0      4490.0      4489.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511827       33.5       48.5      4492.5      4493.5       32.5   \n", "          5511827       24.5       53.5      4492.0      4493.0       27.5   \n", "          5511827       27.0       48.5      4492.0      4493.0       76.0   \n", "          5511827       27.0       56.0      4492.0      4493.0       67.0   \n", "          5511827        4.5       68.0      4492.0      4493.0       38.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511827       87.5      103.0      4492.5      4493.5       75.5   \n", "          5511827       73.5       90.5      4492.0      4493.0       68.0   \n", "          5511827        9.5      149.0      4492.0      4493.0       57.0   \n", "          5511827        6.0      149.0      4492.0      4493.0       51.5   \n", "          5511827      148.0       34.0      4491.0      4492.0        3.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511827       48.0  \n", "          5511827       59.0  \n", "          5511827       60.5  \n", "          5511827       61.5  \n", "          5511827       62.5  \n", "...                      ...  \n", "          5511827       78.0  \n", "          5511827       18.5  \n", "          5511827      127.5  \n", "          5511827      129.0  \n", "          5511827       44.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511900),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511900  1653570001  4517.0  466.5      4512.5      4511.5   \n", "          5511900  1653570002  4514.0  218.0      4515.0      4513.5   \n", "          5511900  1653570003  4515.5  151.5      4513.5      4512.5   \n", "          5511900  1653570004  4512.5   90.0      4511.5      4510.5   \n", "          5511900  1653570005  4510.5  211.5      4509.5      4508.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511900  1653570295  4524.0   30.5      4524.0      4523.0   \n", "          5511900  1653570296  4524.0   13.5      4524.0      4523.0   \n", "          5511900  1653570297  4524.5  182.0      4524.5      4523.0   \n", "          5511900  1653570298  4525.0  105.5      4524.5      4523.5   \n", "          5511900  1653570299  4525.0  165.0      4525.0      4524.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511900       11.5       78.0      4517.0      4518.0       78.0   \n", "          5511900       41.0       51.0      4516.5      4517.5        7.5   \n", "          5511900        4.0       20.0      4515.5      4516.5       12.5   \n", "          5511900       14.5      127.0      4512.5      4513.5      127.0   \n", "          5511900       94.0       11.5      4510.5      4511.5      294.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511900       23.0      441.0      4525.0      4526.0      286.5   \n", "          5511900        3.0      440.5      4525.0      4526.0      282.5   \n", "          5511900       96.0      439.5      4525.5      4526.5      178.5   \n", "          5511900       32.0      224.5      4525.5      4526.5       34.0   \n", "          5511900      236.5      221.5      4526.0      4527.0       97.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511900       93.5  \n", "          5511900       14.0  \n", "          5511900        2.5  \n", "          5511900       53.0  \n", "          5511900      185.0  \n", "...                      ...  \n", "          5511900       63.5  \n", "          5511900       63.5  \n", "          5511900       62.5  \n", "          5511900       67.5  \n", "          5511900      172.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511901),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511901  1653570300  4526.5  176.5      4526.0      4525.0   \n", "          5511901  1653570301  4526.0   70.5      4526.0      4525.0   \n", "          5511901  1653570302  4526.5  151.5      4525.5      4524.5   \n", "          5511901  1653570303  4526.0   50.5      4525.0      4524.0   \n", "          5511901  1653570304  4525.5  108.5      4524.5      4523.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511901  1653570595  4546.5   47.0      4545.5      4544.5   \n", "          5511901  1653570596  4546.0   25.5      4545.0      4544.0   \n", "          5511901  1653570597  4546.0   24.0      4546.0      4545.0   \n", "          5511901  1653570598  4544.5   38.5      4544.5      4543.5   \n", "          5511901  1653570599  4544.5   38.0      4543.5      4542.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511901      274.5       38.0      4527.0      4528.0       28.5   \n", "          5511901      220.0       44.5      4527.0      4528.0        8.0   \n", "          5511901      135.0       40.5      4526.5      4527.5        3.5   \n", "          5511901       94.0       24.0      4526.0      4527.0       33.5   \n", "          5511901        7.5       42.5      4525.5      4526.5       53.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511901       20.0       57.0      4546.5      4547.5       27.0   \n", "          5511901       71.5       53.0      4546.5      4547.5       18.5   \n", "          5511901        9.5       72.0      4547.0      4548.0       29.0   \n", "          5511901       56.5       50.0      4546.0      4547.0       26.0   \n", "          5511901       46.0       41.5      4545.0      4546.0       55.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511901      263.0  \n", "          5511901      255.0  \n", "          5511901      128.5  \n", "          5511901       25.0  \n", "          5511901       42.0  \n", "...                      ...  \n", "          5511901       71.5  \n", "          5511901       59.5  \n", "          5511901       97.0  \n", "          5511901       35.0  \n", "          5511901       75.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511902),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511902  1653570600  4543.5   35.0      4543.0      4542.0   \n", "          5511902  1653570601  4543.0   38.5      4542.5      4541.5   \n", "          5511902  1653570602  4543.5   29.5      4544.0      4543.0   \n", "          5511902  1653570603  4545.0   55.5      4545.0      4544.0   \n", "          5511902  1653570604  4544.5   63.5      4544.0      4543.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511902  1653570895  4569.5   47.5      4569.0      4568.0   \n", "          5511902  1653570896  4570.5   38.5      4570.0      4569.0   \n", "          5511902  1653570897  4571.0   30.0      4570.5      4569.5   \n", "          5511902  1653570898  4571.0  119.0      4571.0      4570.0   \n", "          5511902  1653570899  4571.5   33.5      4571.0      4570.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511902       33.5       43.0      4544.0      4545.0       24.5   \n", "          5511902       24.5       52.0      4543.5      4544.5        6.0   \n", "          5511902       56.0       47.0      4545.0      4546.0       47.5   \n", "          5511902       26.5      127.5      4546.0      4547.0       36.0   \n", "          5511902      243.0       46.0      4545.0      4546.0       11.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511902       49.0       78.0      4570.0      4571.0       36.0   \n", "          5511902      137.5       85.5      4571.0      4572.0       43.0   \n", "          5511902       87.0      113.5      4571.5      4572.5       96.5   \n", "          5511902       56.5       66.0      4572.0      4573.0       77.5   \n", "          5511902       28.5       66.0      4572.0      4573.0       66.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511902       74.5  \n", "          5511902       42.0  \n", "          5511902       80.0  \n", "          5511902       43.5  \n", "          5511902       10.0  \n", "...                      ...  \n", "          5511902      119.0  \n", "          5511902      229.0  \n", "          5511902      139.0  \n", "          5511902       90.0  \n", "          5511902       90.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511903),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511903  1653570900  4572.0   75.0      4571.5      4570.5   \n", "          5511903  1653570901  4573.0  175.5      4572.5      4571.5   \n", "          5511903  1653570902  4574.0  222.0      4574.0      4573.0   \n", "          5511903  1653570903  4574.0   68.0      4573.5      4572.5   \n", "          5511903  1653570904  4573.5   78.5      4573.0      4572.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511903  1653571195  4581.0    9.0      4580.5      4579.5   \n", "          5511903  1653571196  4580.5   23.0      4580.5      4579.5   \n", "          5511903  1653571197  4581.5   12.5      4581.0      4580.0   \n", "          5511903  1653571198  4582.0   51.5      4580.5      4579.5   \n", "          5511903  1653571199  4581.5   14.5      4581.0      4580.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511903      101.0       74.5      4572.5      4573.5       41.5   \n", "          5511903      138.5      142.0      4573.5      4574.5       52.0   \n", "          5511903       17.5      152.5      4575.5      4576.5       59.0   \n", "          5511903       60.5      203.5      4574.5      4575.5       12.5   \n", "          5511903      106.0      272.5      4574.0      4575.0       26.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511903       81.0      110.0      4581.5      4582.5       47.0   \n", "          5511903       79.5      111.0      4582.0      4583.0       89.0   \n", "          5511903        6.5      162.0      4582.0      4583.0       90.0   \n", "          5511903       94.0      110.5      4582.0      4583.0       38.0   \n", "          5511903       57.5      164.0      4582.0      4583.0       26.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511903       87.5  \n", "          5511903       73.0  \n", "          5511903      207.5  \n", "          5511903       68.0  \n", "          5511903       29.5  \n", "...                      ...  \n", "          5511903       88.5  \n", "          5511903      104.0  \n", "          5511903      109.0  \n", "          5511903      109.0  \n", "          5511903      109.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511904),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511904  1653571200  4582.0   79.5      4581.0      4580.0   \n", "          5511904  1653571201  4581.5   38.0      4581.0      4580.0   \n", "          5511904  1653571202  4581.5   41.5      4581.5      4580.5   \n", "          5511904  1653571203  4583.0  100.5      4582.5      4581.5   \n", "          5511904  1653571204  4584.0  114.5      4584.0      4583.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511904  1653571495  4571.5   12.0      4571.5      4570.5   \n", "          5511904  1653571496  4572.0   13.0      4572.0      4571.0   \n", "          5511904  1653571497  4572.0    1.0      4572.0      4571.0   \n", "          5511904  1653571498  4572.0   47.0      4571.5      4570.5   \n", "          5511904  1653571499  4573.0    2.5      4572.0      4571.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511904       15.0      170.0      4582.0      4583.0       45.5   \n", "          5511904       34.0      171.5      4582.0      4583.0       35.5   \n", "          5511904       73.5      135.5      4582.5      4583.5       68.5   \n", "          5511904       73.5      149.0      4584.0      4585.0       67.5   \n", "          5511904       38.0       51.0      4585.0      4586.0      241.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511904       85.5       76.0      4572.5      4573.5       60.5   \n", "          5511904       66.5       88.5      4573.0      4574.0       86.0   \n", "          5511904       63.0       89.0      4573.0      4574.0       95.0   \n", "          5511904       77.0       77.5      4572.5      4573.5       46.0   \n", "          5511904       64.5       91.0      4573.0      4574.0       90.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511904      110.0  \n", "          5511904      111.0  \n", "          5511904      108.0  \n", "          5511904      263.5  \n", "          5511904      150.5  \n", "...                      ...  \n", "          5511904      114.0  \n", "          5511904      122.0  \n", "          5511904      122.0  \n", "          5511904      105.0  \n", "          5511904      122.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511905),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511905  1653571500  4572.5  17.0      4572.0      4571.0   \n", "          5511905  1653571501  4572.5   5.0      4572.0      4571.0   \n", "          5511905  1653571502  4572.5  34.0      4572.5      4571.5   \n", "          5511905  1653571503  4573.5   9.0      4573.0      4572.0   \n", "          5511905  1653571504  4573.5   3.5      4573.0      4572.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511905  1653571795  4572.0   3.0      4571.5      4570.5   \n", "          5511905  1653571796  4572.0  19.0      4572.0      4571.0   \n", "          5511905  1653571797  4573.0  11.0      4573.0      4572.0   \n", "          5511905  1653571798  4573.5   2.0      4573.0      4572.0   \n", "          5511905  1653571799  4573.5   9.0      4573.0      4572.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511905       59.0       91.0      4573.0      4574.0       76.5   \n", "          5511905       52.5       91.0      4573.0      4574.0       58.5   \n", "          5511905       47.5       92.5      4573.5      4574.5       68.5   \n", "          5511905       46.5       65.0      4574.0      4575.0      109.0   \n", "          5511905       76.0       71.5      4574.0      4575.0      106.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511905       71.5      152.0      4572.5      4573.5       18.5   \n", "          5511905       56.0      113.5      4573.0      4574.0       22.5   \n", "          5511905       24.0       56.0      4574.0      4575.0       60.5   \n", "          5511905       83.5       58.0      4574.0      4575.0       51.5   \n", "          5511905       83.0       58.5      4574.0      4575.0       41.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511905      122.5  \n", "          5511905      123.0  \n", "          5511905      139.5  \n", "          5511905      156.5  \n", "          5511905      160.0  \n", "...                      ...  \n", "          5511905       52.5  \n", "          5511905       68.5  \n", "          5511905      229.0  \n", "          5511905      228.0  \n", "          5511905      228.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511906),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511906  1653571800  4574.5  44.0      4573.5      4572.5   \n", "          5511906  1653571801  4575.5  93.0      4574.5      4573.5   \n", "          5511906  1653571802  4576.0  57.5      4575.0      4574.0   \n", "          5511906  1653571803  4576.5  49.0      4576.0      4575.0   \n", "          5511906  1653571804  4576.0   9.0      4576.0      4575.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511906  1653572095  4583.0  13.5      4583.0      4582.0   \n", "          5511906  1653572096  4584.5   9.5      4583.5      4582.5   \n", "          5511906  1653572097  4583.0   8.0      4583.0      4582.0   \n", "          5511906  1653572098  4584.0   6.0      4583.5      4582.5   \n", "          5511906  1653572099  4583.0   9.0      4583.0      4582.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511906       60.5       70.5      4575.0      4576.0      204.5   \n", "          5511906       71.5       77.0      4575.5      4576.5      106.0   \n", "          5511906       92.5       80.0      4576.0      4577.0       66.5   \n", "          5511906       20.5       44.0      4577.0      4578.0       91.5   \n", "          5511906       58.5       31.5      4577.0      4578.0       80.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511906       33.0       78.5      4584.5      4585.5       76.0   \n", "          5511906       22.0       60.0      4585.0      4586.0      134.0   \n", "          5511906       40.5       78.0      4584.5      4585.5       65.0   \n", "          5511906       25.5       59.5      4585.0      4586.0      128.5   \n", "          5511906       52.5       79.0      4584.5      4585.5       65.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511906      116.0  \n", "          5511906      108.5  \n", "          5511906       98.0  \n", "          5511906      174.5  \n", "          5511906      175.0  \n", "...                      ...  \n", "          5511906      123.5  \n", "          5511906       92.0  \n", "          5511906      109.0  \n", "          5511906       92.0  \n", "          5511906      112.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511907),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511907  1653572100  4584.0  77.5      4583.5      4582.5   \n", "          5511907  1653572101  4585.5   7.0      4585.0      4584.0   \n", "          5511907  1653572102  4585.0  15.5      4585.0      4584.0   \n", "          5511907  1653572103  4586.0  34.0      4585.5      4584.5   \n", "          5511907  1653572104  4586.0  11.0      4586.0      4585.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511907  1653572395  4590.0  12.0      4590.0      4589.0   \n", "          5511907  1653572396  4590.5  31.5      4589.5      4588.5   \n", "          5511907  1653572397  4590.0  28.5      4589.0      4588.0   \n", "          5511907  1653572398  4590.5  21.0      4589.5      4588.5   \n", "          5511907  1653572399  4590.5  12.5      4589.5      4588.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511907       47.5       76.5      4585.5      4586.5      104.5   \n", "          5511907       24.5       54.5      4586.0      4587.0       86.5   \n", "          5511907       88.5       49.0      4586.0      4587.0       61.0   \n", "          5511907       51.5       70.5      4586.5      4587.5       24.5   \n", "          5511907       77.0       22.0      4587.0      4588.0       56.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511907       30.5       85.0      4591.0      4592.0      173.0   \n", "          5511907       66.0      100.5      4591.0      4592.0      166.5   \n", "          5511907       62.5      117.0      4590.5      4591.5       83.0   \n", "          5511907       20.0       79.5      4591.0      4592.0      163.0   \n", "          5511907       46.0      107.0      4591.0      4592.0      126.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511907       74.0  \n", "          5511907       54.0  \n", "          5511907       54.0  \n", "          5511907       67.5  \n", "          5511907       77.0  \n", "...                      ...  \n", "          5511907       48.0  \n", "          5511907       42.0  \n", "          5511907      102.0  \n", "          5511907       43.0  \n", "          5511907       46.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511908),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511908  1653572400  4590.0  36.0      4589.5      4588.5   \n", "          5511908  1653572401  4590.5   4.5      4590.0      4589.0   \n", "          5511908  1653572402  4591.0   3.5      4590.0      4589.0   \n", "          5511908  1653572403  4590.0  32.5      4590.0      4589.0   \n", "          5511908  1653572404  4590.0   8.5      4590.0      4589.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511908  1653572695  4587.5  34.5      4587.0      4586.0   \n", "          5511908  1653572696  4587.5  12.5      4587.0      4586.0   \n", "          5511908  1653572697  4587.5  16.5      4587.0      4586.0   \n", "          5511908  1653572698  4587.0   1.5      4587.0      4586.0   \n", "          5511908  1653572699  4588.0   4.0      4587.0      4586.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511908       46.5      105.0      4590.5      4591.5       45.0   \n", "          5511908       22.5       90.5      4591.0      4592.0       73.0   \n", "          5511908       82.0       88.0      4591.0      4592.0       70.0   \n", "          5511908      133.0       87.0      4591.0      4592.0       72.0   \n", "          5511908      114.5       87.0      4591.0      4592.0       75.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511908       16.5       57.0      4588.0      4589.0        5.5   \n", "          5511908       35.5       57.0      4588.5      4589.5       34.0   \n", "          5511908       35.5       58.0      4588.0      4589.0       15.0   \n", "          5511908       35.0       58.0      4588.0      4589.0       12.0   \n", "          5511908       28.5       58.0      4588.0      4589.0       19.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511908       79.0  \n", "          5511908       59.5  \n", "          5511908      139.5  \n", "          5511908      135.0  \n", "          5511908      140.0  \n", "...                      ...  \n", "          5511908       59.5  \n", "          5511908       59.0  \n", "          5511908       46.0  \n", "          5511908       46.0  \n", "          5511908       47.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511909),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511909  1653572700  4588.0  11.5      4587.5      4586.5   \n", "          5511909  1653572701  4589.0   3.0      4588.0      4587.0   \n", "          5511909  1653572702  4589.0  41.0      4588.5      4587.5   \n", "          5511909  1653572703  4589.5   9.0      4589.0      4588.0   \n", "          5511909  1653572704  4589.0   8.5      4589.0      4588.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511909  1653572995  4588.5  18.5      4588.5      4587.5   \n", "          5511909  1653572996  4588.5  20.5      4588.0      4587.0   \n", "          5511909  1653572997  4588.5  38.0      4588.0      4587.0   \n", "          5511909  1653572998  4588.0  14.0      4588.5      4587.5   \n", "          5511909  1653572999  4588.0   0.5      4588.0      4587.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511909       30.0       46.0      4588.5      4589.5       30.5   \n", "          5511909       51.0       29.5      4589.0      4590.0       26.0   \n", "          5511909       19.5       40.0      4590.0      4591.0       62.0   \n", "          5511909       78.0       28.0      4590.0      4591.0       51.5   \n", "          5511909       64.5       28.0      4590.0      4591.0       50.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511909       34.5       81.0      4589.5      4590.5       34.0   \n", "          5511909       59.0       98.0      4589.0      4590.0       10.5   \n", "          5511909       50.0       63.0      4589.0      4590.0       33.5   \n", "          5511909       13.0       60.0      4589.5      4590.5       43.0   \n", "          5511909       29.5       95.0      4589.0      4590.0       20.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511909       61.5  \n", "          5511909       72.5  \n", "          5511909       42.0  \n", "          5511909       42.0  \n", "          5511909       42.0  \n", "...                      ...  \n", "          5511909      138.5  \n", "          5511909       64.0  \n", "          5511909      109.5  \n", "          5511909      144.5  \n", "          5511909       72.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511910),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511910  1653573000  4588.5   7.5      4588.0      4587.0   \n", "          5511910  1653573001  4589.0  17.0      4588.5      4587.5   \n", "          5511910  1653573002  4589.0  39.5      4589.5      4588.5   \n", "          5511910  1653573003  4590.0   6.5      4590.0      4589.0   \n", "          5511910  1653573004  4590.5   6.0      4590.0      4589.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511910  1653573295  4584.0  54.5      4583.0      4582.0   \n", "          5511910  1653573296  4584.0   4.5      4583.0      4582.0   \n", "          5511910  1653573297  4583.5   4.0      4583.0      4582.0   \n", "          5511910  1653573298  4583.0   2.5      4583.0      4582.0   \n", "          5511910  1653573299  4583.0  10.0      4582.5      4581.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511910       43.5       95.0      4589.0      4590.0       20.5   \n", "          5511910       34.5       90.5      4589.5      4590.5       46.0   \n", "          5511910       36.0       77.0      4590.5      4591.5      153.5   \n", "          5511910       82.5       37.0      4591.0      4592.0      240.0   \n", "          5511910       71.5       38.0      4591.0      4592.0      230.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511910       39.5       99.0      4584.0      4585.0       36.0   \n", "          5511910       38.5      105.0      4584.0      4585.0       76.5   \n", "          5511910       41.0      115.0      4584.0      4585.0       70.0   \n", "          5511910       33.0      117.0      4584.0      4585.0       67.5   \n", "          5511910       68.0      230.5      4583.5      4584.5       47.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511910       73.0  \n", "          5511910      157.5  \n", "          5511910      152.5  \n", "          5511910       72.0  \n", "          5511910       72.0  \n", "...                      ...  \n", "          5511910      116.0  \n", "          5511910      119.0  \n", "          5511910      119.0  \n", "          5511910      119.0  \n", "          5511910      243.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511911),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5511911  1653573300  4582.5   15.0      4582.0      4581.0   \n", "          5511911  1653573301  4581.5  233.5      4580.5      4579.5   \n", "          5511911  1653573302  4580.0   94.0      4579.5      4578.5   \n", "          5511911  1653573303  4580.0   16.0      4579.5      4578.5   \n", "          5511911  1653573304  4580.5   22.0      4580.0      4579.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5511911  1653573595  4582.0    0.0      4582.0      4581.0   \n", "          5511911  1653573596  4582.0    0.5      4582.0      4581.0   \n", "          5511911  1653573597  4582.5   37.0      4582.0      4581.0   \n", "          5511911  1653573598  4582.5    1.0      4582.0      4581.0   \n", "          5511911  1653573599  4582.0    9.5      4582.0      4581.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511911      119.0      343.0      4583.0      4584.0       16.5   \n", "          5511911      221.0      129.0      4581.5      4582.5       97.0   \n", "          5511911       96.0       77.0      4580.5      4581.5       55.5   \n", "          5511911       43.0       79.5      4580.5      4581.5       56.0   \n", "          5511911       15.0       80.0      4581.0      4582.0       95.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511911       48.0       78.0      4583.0      4584.0       31.5   \n", "          5511911       94.0       78.5      4583.0      4584.0       68.0   \n", "          5511911      106.5       78.0      4583.0      4584.0       39.5   \n", "          5511911       88.0       78.0      4583.0      4584.0       35.5   \n", "          5511911       88.0       78.0      4583.0      4584.0       33.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511911       72.0  \n", "          5511911      402.0  \n", "          5511911      428.5  \n", "          5511911       71.0  \n", "          5511911       48.0  \n", "...                      ...  \n", "          5511911       65.0  \n", "          5511911       64.5  \n", "          5511911       64.5  \n", "          5511911       65.0  \n", "          5511911       67.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511912),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511912  1653573600  4582.5   6.5      4582.0      4581.0   \n", "          5511912  1653573601  4583.0  57.0      4582.0      4581.0   \n", "          5511912  1653573602  4583.0   5.5      4582.0      4581.0   \n", "          5511912  1653573603  4584.5  40.5      4583.0      4582.0   \n", "          5511912  1653573604  4585.5  60.0      4585.5      4584.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511912  1653573895  4592.0   0.5      4591.0      4590.0   \n", "          5511912  1653573896  4591.5   1.5      4591.0      4590.0   \n", "          5511912  1653573897  4592.5  26.5      4592.0      4591.0   \n", "          5511912  1653573898  4592.0  27.5      4592.0      4591.0   \n", "          5511912  1653573899  4592.0   2.0      4592.0      4591.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511912      102.5       78.0      4583.0      4584.0        7.0   \n", "          5511912       28.0       78.0      4583.5      4584.5       47.0   \n", "          5511912       78.5       78.0      4583.0      4584.0       42.0   \n", "          5511912       64.0       69.0      4584.5      4585.5       66.0   \n", "          5511912       30.5       85.5      4586.5      4587.5       56.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511912       56.5       73.0      4592.0      4593.0       13.0   \n", "          5511912       53.0       73.0      4592.0      4593.0       15.5   \n", "          5511912       21.5       47.0      4593.0      4594.0       46.5   \n", "          5511912        5.0       47.0      4593.0      4594.0       13.5   \n", "          5511912       14.5       48.0      4593.0      4594.0       27.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511912       67.5  \n", "          5511912       74.0  \n", "          5511912       66.0  \n", "          5511912       66.5  \n", "          5511912      119.5  \n", "...                      ...  \n", "          5511912       80.5  \n", "          5511912       81.5  \n", "          5511912       60.0  \n", "          5511912       60.0  \n", "          5511912       60.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511913),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511913  1653573900  4592.5  25.5      4592.5      4591.5   \n", "          5511913  1653573901  4594.0  31.0      4593.5      4592.5   \n", "          5511913  1653573902  4594.0   8.0      4594.0      4593.0   \n", "          5511913  1653573903  4594.0   5.5      4593.5      4592.5   \n", "          5511913  1653573904  4594.0  10.5      4593.0      4592.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511913  1653574195  4587.5  17.0      4587.0      4586.0   \n", "          5511913  1653574196  4587.0   0.0      4588.0      4587.0   \n", "          5511913  1653574197  4588.5   5.5      4588.0      4587.0   \n", "          5511913  1653574198  4589.0   0.5      4588.0      4587.0   \n", "          5511913  1653574199  4589.0   0.5      4588.0      4587.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511913      156.5       62.0      4593.5      4594.5       48.0   \n", "          5511913      145.0      184.0      4594.5      4595.5       73.5   \n", "          5511913        7.0      289.5      4595.0      4596.0      144.5   \n", "          5511913      155.5      181.0      4594.5      4595.5       72.5   \n", "          5511913      319.5       71.5      4594.0      4595.0       38.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511913       58.5       35.0      4588.5      4589.5       38.5   \n", "          5511913       27.5       54.0      4589.0      4590.0       45.0   \n", "          5511913       24.5       50.0      4589.0      4590.0       42.5   \n", "          5511913       24.5       50.0      4589.0      4590.0       60.5   \n", "          5511913       30.0       50.0      4589.0      4590.0       61.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511913       98.5  \n", "          5511913      185.5  \n", "          5511913      234.0  \n", "          5511913      189.5  \n", "          5511913      125.5  \n", "...                      ...  \n", "          5511913       95.5  \n", "          5511913      126.0  \n", "          5511913      126.5  \n", "          5511913      127.0  \n", "          5511913      127.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511914),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511914  1653574200  4588.0  27.0      4587.0      4586.0   \n", "          5511914  1653574201  4587.5   2.5      4587.0      4586.0   \n", "          5511914  1653574202  4588.0   0.5      4587.0      4586.0   \n", "          5511914  1653574203  4588.0   2.5      4587.0      4586.0   \n", "          5511914  1653574204  4588.0   0.5      4587.0      4586.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511914  1653574495  4589.0   8.5      4588.5      4587.5   \n", "          5511914  1653574496  4590.0   8.5      4588.0      4587.0   \n", "          5511914  1653574497  4590.0   4.0      4588.0      4587.0   \n", "          5511914  1653574498  4588.5   1.5      4588.0      4587.0   \n", "          5511914  1653574499  4588.5   1.0      4588.0      4587.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511914       52.5       37.0      4588.0      4589.0       22.5   \n", "          5511914       74.0       47.0      4588.0      4589.0       26.0   \n", "          5511914       75.0       47.5      4588.0      4589.0       33.5   \n", "          5511914       75.0       48.0      4588.0      4589.0       38.0   \n", "          5511914       75.5       48.0      4588.0      4589.0       38.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511914       42.0       62.5      4589.5      4590.5       36.0   \n", "          5511914       72.0       56.0      4589.5      4590.5       32.0   \n", "          5511914       72.0       56.0      4590.0      4591.0       60.0   \n", "          5511914       70.0       56.0      4589.5      4590.5       30.5   \n", "          5511914       69.0       56.0      4590.0      4591.0       60.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511914       61.5  \n", "          5511914       62.0  \n", "          5511914       67.0  \n", "          5511914       68.0  \n", "          5511914       68.0  \n", "...                      ...  \n", "          5511914       56.0  \n", "          5511914       52.0  \n", "          5511914       44.0  \n", "          5511914       52.5  \n", "          5511914       54.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511915),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511915  1653574500  4590.0   4.5      4588.0      4587.0   \n", "          5511915  1653574501  4590.0  11.0      4588.5      4587.5   \n", "          5511915  1653574502  4590.0  23.5      4589.5      4588.5   \n", "          5511915  1653574503  4591.0  12.5      4590.0      4589.0   \n", "          5511915  1653574504  4591.0  24.5      4591.0      4590.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511915  1653574795  4588.5   4.0      4588.0      4587.0   \n", "          5511915  1653574796  4588.0   2.5      4588.0      4587.0   \n", "          5511915  1653574797  4588.0   1.5      4588.0      4587.0   \n", "          5511915  1653574798  4588.5   6.0      4587.5      4586.5   \n", "          5511915  1653574799  4588.5   1.5      4587.5      4586.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511915       68.5       56.0      4590.0      4591.0       57.0   \n", "          5511915       36.0       69.5      4589.5      4590.5       20.5   \n", "          5511915       52.0       86.0      4590.5      4591.5       41.0   \n", "          5511915       65.0       78.0      4591.0      4592.0       42.0   \n", "          5511915       20.5       76.0      4592.0      4593.0      121.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511915       14.5       47.0      4589.5      4590.5       33.0   \n", "          5511915        7.5       47.0      4589.0      4590.0       11.0   \n", "          5511915        7.5       47.0      4589.0      4590.0       14.0   \n", "          5511915       23.0       66.0      4589.0      4590.0       14.0   \n", "          5511915       25.0       67.0      4589.0      4590.0       23.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511915       54.0  \n", "          5511915       57.0  \n", "          5511915       91.0  \n", "          5511915      128.0  \n", "          5511915      182.0  \n", "...                      ...  \n", "          5511915       66.0  \n", "          5511915       55.0  \n", "          5511915       55.5  \n", "          5511915       56.5  \n", "          5511915       59.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511916),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511916  1653574800  4588.0   4.5      4587.0      4586.0   \n", "          5511916  1653574801  4588.0   4.0      4587.0      4586.0   \n", "          5511916  1653574802  4588.0   3.0      4587.5      4586.5   \n", "          5511916  1653574803  4588.5   8.0      4588.0      4587.0   \n", "          5511916  1653574804  4589.0   1.5      4588.0      4587.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511916  1653575095  4588.0   5.0      4587.0      4586.0   \n", "          5511916  1653575096  4588.0   0.0      4587.0      4586.0   \n", "          5511916  1653575097  4588.0   1.5      4587.0      4586.0   \n", "          5511916  1653575098  4587.5   1.0      4587.0      4586.0   \n", "          5511916  1653575099  4587.5   9.0      4587.0      4586.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511916       50.5       88.0      4588.0      4589.0       10.5   \n", "          5511916       57.0       86.0      4588.0      4589.0        8.5   \n", "          5511916       35.5       77.0      4588.5      4589.5       19.5   \n", "          5511916        4.0       63.0      4589.0      4590.0       27.0   \n", "          5511916        9.0       66.0      4589.0      4590.0       66.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511916       45.5      163.0      4588.0      4589.0      201.0   \n", "          5511916       59.0      165.0      4588.0      4589.0      200.5   \n", "          5511916       60.0      165.0      4588.0      4589.0      197.5   \n", "          5511916       60.5      170.5      4588.0      4589.0      196.0   \n", "          5511916       51.5      170.0      4588.0      4589.0      193.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511916       32.0  \n", "          5511916       33.0  \n", "          5511916       47.5  \n", "          5511916       61.5  \n", "          5511916       63.0  \n", "...                      ...  \n", "          5511916       27.0  \n", "          5511916       27.0  \n", "          5511916       27.0  \n", "          5511916       27.0  \n", "          5511916       27.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511917),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511917  1653575100  4587.5   1.5      4587.0      4586.0   \n", "          5511917  1653575101  4587.5   6.5      4587.0      4586.0   \n", "          5511917  1653575102  4587.5   2.5      4587.0      4586.0   \n", "          5511917  1653575103  4587.5   4.5      4587.0      4586.0   \n", "          5511917  1653575104  4587.0  28.0      4587.0      4586.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511917  1653575395  4592.5  15.0      4592.5      4591.5   \n", "          5511917  1653575396  4593.0   2.5      4592.0      4591.0   \n", "          5511917  1653575397  4592.0   1.5      4592.0      4591.0   \n", "          5511917  1653575398  4592.5   2.0      4592.0      4591.0   \n", "          5511917  1653575399  4592.0   4.5      4592.0      4591.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511917       30.0      169.5      4588.0      4589.0      194.0   \n", "          5511917       39.5      169.0      4588.0      4589.0      190.0   \n", "          5511917       68.5      168.5      4588.0      4589.0      186.5   \n", "          5511917       69.5      168.0      4588.0      4589.0      182.5   \n", "          5511917       30.5      168.0      4588.0      4589.0      203.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511917       40.0       51.5      4593.5      4594.5       28.5   \n", "          5511917       47.0       40.0      4593.0      4594.0       10.5   \n", "          5511917       44.5       40.0      4593.0      4594.0        9.5   \n", "          5511917       44.5       40.0      4593.0      4594.0       11.0   \n", "          5511917       41.5       40.0      4593.0      4594.0       12.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511917       27.0  \n", "          5511917       26.5  \n", "          5511917       26.0  \n", "          5511917       23.5  \n", "          5511917       21.0  \n", "...                      ...  \n", "          5511917       56.5  \n", "          5511917       58.0  \n", "          5511917       58.0  \n", "          5511917       58.0  \n", "          5511917       58.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511918),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511918  1653575400  4592.0   0.0      4592.0      4591.0   \n", "          5511918  1653575401  4593.5  19.5      4592.5      4591.5   \n", "          5511918  1653575402  4593.0   8.5      4593.0      4592.0   \n", "          5511918  1653575403  4593.0  15.0      4593.5      4592.5   \n", "          5511918  1653575404  4595.0  54.0      4595.0      4594.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511918  1653575695  4595.5  12.5      4595.0      4594.0   \n", "          5511918  1653575696  4596.0   0.5      4595.0      4594.0   \n", "          5511918  1653575697  4595.0   0.5      4595.0      4594.0   \n", "          5511918  1653575698  4595.5   1.5      4595.0      4594.0   \n", "          5511918  1653575699  4595.5   1.5      4595.0      4594.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511918       43.0       42.0      4593.0      4594.0       16.0   \n", "          5511918       30.5       56.5      4593.5      4594.5       24.5   \n", "          5511918       55.5       48.0      4594.0      4595.0       37.0   \n", "          5511918       53.5       54.5      4595.0      4596.0       57.0   \n", "          5511918       23.0       65.5      4596.0      4597.0      153.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511918      102.5       47.5      4596.0      4597.0       50.0   \n", "          5511918      103.5       48.0      4596.0      4597.0       19.0   \n", "          5511918      104.0       48.0      4596.0      4597.0       19.0   \n", "          5511918       99.0       48.0      4596.0      4597.0       27.5   \n", "          5511918       99.5       48.0      4596.0      4597.0       26.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511918       58.0  \n", "          5511918       68.0  \n", "          5511918       77.5  \n", "          5511918      130.5  \n", "          5511918       58.0  \n", "...                      ...  \n", "          5511918      190.0  \n", "          5511918      190.0  \n", "          5511918      189.5  \n", "          5511918      189.0  \n", "          5511918      189.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511919),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511919  1653575700  4595.5   4.0      4595.0      4594.0   \n", "          5511919  1653575701  4595.5   9.0      4595.0      4594.0   \n", "          5511919  1653575702  4596.0   1.0      4595.0      4594.0   \n", "          5511919  1653575703  4596.0   0.5      4595.0      4594.0   \n", "          5511919  1653575704  4596.0   1.0      4595.0      4594.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511919  1653575995  4593.5   1.5      4593.0      4592.0   \n", "          5511919  1653575996  4593.5   0.5      4593.0      4592.0   \n", "          5511919  1653575997  4594.0   1.5      4593.0      4592.0   \n", "          5511919  1653575998  4593.0   0.5      4593.0      4592.0   \n", "          5511919  1653575999  4593.0   0.5      4593.0      4592.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511919       99.0       48.0      4596.0      4597.0       47.0   \n", "          5511919       87.0       48.0      4596.0      4597.0       64.5   \n", "          5511919       83.0       47.0      4596.0      4597.0       68.0   \n", "          5511919       83.5       46.0      4596.0      4597.0       73.0   \n", "          5511919       84.0       46.0      4596.0      4597.0       92.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511919       54.0       33.0      4594.0      4595.0       20.0   \n", "          5511919       55.5       33.0      4594.0      4595.0       20.0   \n", "          5511919       56.5       34.5      4594.0      4595.0       17.0   \n", "          5511919       57.0       35.0      4594.0      4595.0       15.0   \n", "          5511919       59.5       37.0      4594.0      4595.0       13.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511919      189.0  \n", "          5511919      190.0  \n", "          5511919      188.5  \n", "          5511919      186.0  \n", "          5511919      185.0  \n", "...                      ...  \n", "          5511919       75.5  \n", "          5511919       76.5  \n", "          5511919       77.0  \n", "          5511919       79.0  \n", "          5511919       80.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511920),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511920  1653576000  4594.0  12.0      4594.0      4593.0   \n", "          5511920  1653576001  4594.5   3.0      4594.0      4593.0   \n", "          5511920  1653576002  4595.0   4.0      4594.0      4593.0   \n", "          5511920  1653576003  4594.0   1.5      4594.0      4593.0   \n", "          5511920  1653576004  4594.0   5.5      4594.0      4593.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511920  1653576295  4591.0   4.0      4591.0      4590.0   \n", "          5511920  1653576296  4591.0   0.0      4591.0      4590.0   \n", "          5511920  1653576297  4591.0   0.5      4591.0      4590.0   \n", "          5511920  1653576298  4591.0   0.0      4591.0      4590.0   \n", "          5511920  1653576299  4591.5   3.5      4591.0      4590.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511920        8.0       66.0      4595.0      4596.0       83.0   \n", "          5511920        9.5       66.0      4595.0      4596.0       90.5   \n", "          5511920       45.0       66.0      4595.0      4596.0       96.0   \n", "          5511920       37.5       67.0      4595.0      4596.0      109.5   \n", "          5511920       32.0       68.0      4595.0      4596.0      111.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511920       20.0       74.0      4592.0      4593.0       26.5   \n", "          5511920       25.0       75.0      4592.0      4593.0       27.0   \n", "          5511920       40.5       75.0      4592.0      4593.0       27.0   \n", "          5511920       44.0       74.5      4592.0      4593.0       27.5   \n", "          5511920       44.0       74.5      4592.0      4593.0       23.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511920      117.0  \n", "          5511920      117.0  \n", "          5511920      117.0  \n", "          5511920      117.0  \n", "          5511920      117.0  \n", "...                      ...  \n", "          5511920       41.0  \n", "          5511920       41.0  \n", "          5511920       41.5  \n", "          5511920       42.0  \n", "          5511920       41.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511921),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511921  1653576300  4592.5  16.0      4591.0      4590.0   \n", "          5511921  1653576301  4592.5   5.0      4591.0      4590.0   \n", "          5511921  1653576302  4591.5   3.0      4591.0      4590.0   \n", "          5511921  1653576303  4592.0   2.5      4591.0      4590.0   \n", "          5511921  1653576304  4592.0   5.0      4591.0      4590.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511921  1653576595  4598.0   2.5      4598.0      4597.0   \n", "          5511921  1653576596  4599.0   2.5      4598.0      4597.0   \n", "          5511921  1653576597  4598.5  12.5      4597.5      4596.5   \n", "          5511921  1653576598  4598.0   4.5      4597.0      4596.0   \n", "          5511921  1653576599  4598.0   2.0      4597.0      4596.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511921       41.0       74.0      4592.5      4593.5       25.0   \n", "          5511921       28.0       74.0      4592.0      4593.0       12.0   \n", "          5511921       29.0       75.0      4592.0      4593.0       43.5   \n", "          5511921       27.0       75.0      4592.0      4593.0       41.5   \n", "          5511921       23.0       75.0      4592.0      4593.0       39.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511921       13.0       57.0      4599.0      4600.0       48.0   \n", "          5511921       14.0       57.0      4599.0      4600.0       49.5   \n", "          5511921       36.0       78.0      4598.5      4599.5       33.0   \n", "          5511921       50.0      100.0      4598.0      4599.0       16.5   \n", "          5511921       58.0      100.0      4598.0      4599.0       26.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511921       60.5  \n", "          5511921       38.5  \n", "          5511921       36.5  \n", "          5511921       37.0  \n", "          5511921       38.0  \n", "...                      ...  \n", "          5511921      124.0  \n", "          5511921      124.0  \n", "          5511921       85.5  \n", "          5511921       50.5  \n", "          5511921       53.5  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5511922),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511922  1653576600  4598.0  17.0      4597.5      4596.5   \n", "          5511922  1653576601  4599.0   2.5      4598.0      4597.0   \n", "          5511922  1653576602  4598.0   3.5      4598.0      4597.0   \n", "          5511922  1653576603  4598.0   9.5      4598.0      4597.0   \n", "          5511922  1653576604  4598.0  12.5      4597.0      4596.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511922  1653576895  4599.5   6.5      4599.0      4598.0   \n", "          5511922  1653576896  4599.0  14.0      4599.0      4598.0   \n", "          5511922  1653576897  4600.0   3.5      4599.0      4598.0   \n", "          5511922  1653576898  4599.5   3.0      4599.0      4598.0   \n", "          5511922  1653576899  4600.0   0.5      4599.0      4598.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511922       42.5       81.0      4598.5      4599.5       31.5   \n", "          5511922       36.5       59.5      4599.0      4600.0       51.5   \n", "          5511922       31.5       53.0      4599.0      4600.0       53.0   \n", "          5511922       20.0       45.0      4599.0      4600.0       64.5   \n", "          5511922       42.0      100.0      4598.0      4599.0       15.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511922        9.5      171.0      4600.0      4601.0      261.5   \n", "          5511922       43.5      171.0      4600.0      4601.0      257.0   \n", "          5511922       42.0      174.0      4600.0      4601.0      248.0   \n", "          5511922       40.5      177.0      4600.0      4601.0      260.0   \n", "          5511922       40.0      177.0      4600.0      4601.0      264.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511922       93.5  \n", "          5511922      131.5  \n", "          5511922      132.0  \n", "          5511922      134.0  \n", "          5511922       89.0  \n", "...                      ...  \n", "          5511922       24.0  \n", "          5511922       24.0  \n", "          5511922       24.0  \n", "          5511922       24.0  \n", "          5511922       30.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5511923),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5511923  1653576900  4599.0  25.0      4598.5      4597.5   \n", "          5511923  1653576901  4599.0  24.5      4598.0      4597.0   \n", "          5511923  1653576902  4598.5   7.0      4598.0      4597.0   \n", "          5511923  1653576903  4599.0  29.0      4599.0      4598.0   \n", "          5511923  1653576904  4599.0   1.0      4599.0      4598.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5511923  1653577195  4602.5  22.5      4602.0      4600.0   \n", "          5511923  1653577196  4602.0  32.5      4600.0      4599.0   \n", "          5511923  1653577197  4602.5  35.0      4602.0      4600.5   \n", "          5511923  1653577198  4602.5  17.0      4602.0      4601.0   \n", "          5511923  1653577199  4603.0  38.0      4602.0      4601.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5511923       83.0      108.0      4599.5      4600.5      141.0   \n", "          5511923      111.0       88.5      4599.0      4600.0        6.5   \n", "          5511923      109.0       88.0      4599.0      4600.0       11.0   \n", "          5511923       24.0      116.0      4600.0      4601.0      252.0   \n", "          5511923       33.0      116.0      4600.0      4601.0      255.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5511923       41.0       22.0      4603.0      4604.0       66.0   \n", "          5511923       21.5      127.0      4602.0      4603.0       14.0   \n", "          5511923       80.0       11.0      4603.0      4604.0       68.0   \n", "          5511923       80.0        2.0      4603.0      4604.0       37.5   \n", "          5511923       47.0        3.0      4603.0      4604.0       31.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5511923      134.5  \n", "          5511923      240.0  \n", "          5511923      241.5  \n", "          5511923       30.0  \n", "          5511923       30.0  \n", "...                      ...  \n", "          5511923       11.5  \n", "          5511923       67.5  \n", "          5511923       16.0  \n", "          5511923       16.0  \n", "          5511923       18.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512044),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512044  1653613228  4594.5  35.5      4594.5      4593.5   \n", "          5512044  1653613229  4593.5  59.5      4592.5      4591.5   \n", "          5512044  1653613230  4591.0  80.0      4589.5      4588.5   \n", "          5512044  1653613231  4589.5  33.0      4588.5      4587.5   \n", "          5512044  1653613232  4587.5  57.0      4587.0      4586.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512044  1653613495  4594.5   2.5      4594.0      4593.0   \n", "          5512044  1653613496  4594.5   3.0      4594.0      4593.0   \n", "          5512044  1653613497  4595.0  50.5      4595.0      4594.0   \n", "          5512044  1653613498  4596.5  13.0      4595.5      4594.5   \n", "          5512044  1653613499  4597.0  11.0      4596.0      4595.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512044       44.5       59.0      4595.5      4596.5       31.0   \n", "          5512044       41.5       96.5      4593.5      4594.5       54.0   \n", "          5512044       60.5       45.0      4591.0      4592.0       91.0   \n", "          5512044       26.0       34.0      4589.5      4590.5       43.5   \n", "          5512044       50.0      121.5      4588.0      4589.0        9.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512044        8.5       35.0      4595.0      4596.0       34.0   \n", "          5512044        3.0       35.0      4595.0      4596.0       31.5   \n", "          5512044       16.0       15.5      4596.5      4597.5       54.5   \n", "          5512044       29.0       28.5      4596.5      4597.5       12.5   \n", "          5512044       14.0       39.0      4597.0      4598.0       13.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512044       60.0  \n", "          5512044       78.5  \n", "          5512044       95.5  \n", "          5512044       51.0  \n", "          5512044       64.5  \n", "...                      ...  \n", "          5512044       64.0  \n", "          5512044       64.0  \n", "          5512044       84.5  \n", "          5512044       72.5  \n", "          5512044      126.0  \n", "\n", "[272 rows x 11 columns])\n", "(('BU8888.SC', 5512045),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512045  1653613500  4597.0   5.5      4596.0      4595.0   \n", "          5512045  1653613501  4597.5  12.0      4597.0      4596.0   \n", "          5512045  1653613502  4597.5  33.5      4597.0      4596.0   \n", "          5512045  1653613503  4598.5  75.0      4598.5      4597.5   \n", "          5512045  1653613504  4599.5  15.0      4599.0      4598.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512045  1653613795  4616.5  32.5      4615.5      4614.5   \n", "          5512045  1653613796  4616.0  20.5      4616.0      4615.0   \n", "          5512045  1653613797  4616.0   8.0      4615.5      4614.5   \n", "          5512045  1653613798  4615.5   6.0      4615.0      4614.0   \n", "          5512045  1653613799  4616.0  25.0      4615.0      4614.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512045       13.5       39.0      4597.0      4598.0        3.5   \n", "          5512045       13.5       22.0      4598.0      4599.0      125.0   \n", "          5512045       61.5       36.0      4598.0      4599.0       80.0   \n", "          5512045       38.5      106.5      4599.5      4600.5      120.5   \n", "          5512045       40.5       79.0      4600.0      4601.0      172.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512045       58.5       74.5      4616.5      4617.5       15.5   \n", "          5512045        5.0      114.0      4617.0      4618.0       32.5   \n", "          5512045       58.0       75.5      4617.0      4618.0       33.0   \n", "          5512045      117.0       36.0      4616.0      4617.0        3.0   \n", "          5512045      118.5       36.0      4617.0      4618.0       29.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512045      131.0  \n", "          5512045       58.0  \n", "          5512045       58.0  \n", "          5512045      124.5  \n", "          5512045       65.0  \n", "...                      ...  \n", "          5512045       52.5  \n", "          5512045       75.0  \n", "          5512045       75.0  \n", "          5512045       37.0  \n", "          5512045       74.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512046),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512046  1653613800  4616.0   19.0      4616.0      4615.0   \n", "          5512046  1653613801  4616.5   29.0      4616.5      4615.5   \n", "          5512046  1653613802  4617.5   17.0      4617.0      4616.0   \n", "          5512046  1653613803  4618.0   11.5      4617.0      4616.0   \n", "          5512046  1653613804  4618.0    8.0      4617.5      4616.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512046  1653614095  4625.0   43.5      4624.0      4623.0   \n", "          5512046  1653614096  4624.5   35.5      4624.0      4623.0   \n", "          5512046  1653614097  4625.5   61.0      4625.0      4624.0   \n", "          5512046  1653614098  4625.0   27.5      4625.0      4624.0   \n", "          5512046  1653614099  4625.0  164.0      4625.0      4624.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512046       33.5      117.5      4617.5      4618.5       31.0   \n", "          5512046       23.0       81.0      4618.0      4619.0       49.5   \n", "          5512046       39.0       59.5      4618.0      4619.0       26.5   \n", "          5512046       26.0       62.0      4618.0      4619.0       17.5   \n", "          5512046       32.5       45.0      4618.5      4619.5       73.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512046      254.5       50.0      4625.0      4626.0       39.5   \n", "          5512046      412.0       55.0      4625.0      4626.0       19.5   \n", "          5512046       33.0      418.5      4626.0      4627.0      474.5   \n", "          5512046       65.5      417.0      4626.0      4627.0      435.5   \n", "          5512046      959.5      417.0      4626.0      4627.0      216.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512046      104.5  \n", "          5512046      135.0  \n", "          5512046      135.0  \n", "          5512046      135.0  \n", "          5512046       96.5  \n", "...                      ...  \n", "          5512046      531.0  \n", "          5512046      530.0  \n", "          5512046       50.5  \n", "          5512046       53.0  \n", "          5512046       55.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512047),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512047  1653614100  4625.5  138.0      4624.0      4623.0   \n", "          5512047  1653614101  4624.0   44.5      4622.0      4621.0   \n", "          5512047  1653614102  4624.0   52.0      4623.0      4622.0   \n", "          5512047  1653614103  4624.5  105.5      4624.5      4623.5   \n", "          5512047  1653614104  4625.0   29.5      4624.0      4623.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512047  1653614395  4619.5    1.5      4619.0      4618.0   \n", "          5512047  1653614396  4620.0    2.0      4619.0      4618.0   \n", "          5512047  1653614397  4619.0    1.5      4619.0      4618.0   \n", "          5512047  1653614398  4619.0    2.5      4619.0      4618.0   \n", "          5512047  1653614399  4620.0    1.5      4619.0      4618.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512047       65.5       85.5      4625.5      4626.5      135.0   \n", "          5512047       41.0       42.5      4624.0      4625.0       71.0   \n", "          5512047      433.0       37.5      4624.0      4625.0      106.5   \n", "          5512047       25.0      319.0      4626.0      4627.0       39.5   \n", "          5512047       58.5      579.0      4626.0      4627.0       40.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512047       20.5      494.0      4620.0      4621.0       43.5   \n", "          5512047       11.0      494.0      4620.0      4621.0       45.0   \n", "          5512047        9.5      494.0      4620.0      4621.0       45.5   \n", "          5512047       16.5      494.0      4620.0      4621.0       43.5   \n", "          5512047       30.0      494.0      4620.0      4621.0       39.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512047       75.5  \n", "          5512047      111.5  \n", "          5512047      106.5  \n", "          5512047       61.5  \n", "          5512047       61.0  \n", "...                      ...  \n", "          5512047      143.0  \n", "          5512047      146.0  \n", "          5512047      146.0  \n", "          5512047      146.5  \n", "          5512047      147.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512048),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512048  1653614400  4620.0   4.0      4619.0      4618.0   \n", "          5512048  1653614401  4619.5  14.5      4618.0      4617.0   \n", "          5512048  1653614402  4619.0  14.5      4619.0      4618.0   \n", "          5512048  1653614403  4620.0   5.5      4619.0      4618.0   \n", "          5512048  1653614404  4619.0  26.5      4618.0      4617.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512048  1653614695  4619.5   2.0      4619.0      4618.0   \n", "          5512048  1653614696  4619.0   1.5      4619.0      4618.0   \n", "          5512048  1653614697  4619.0   7.0      4618.0      4617.0   \n", "          5512048  1653614698  4619.0   1.0      4618.0      4617.0   \n", "          5512048  1653614699  4619.0  36.0      4617.5      4616.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512048       28.5      494.0      4620.0      4621.0       60.0   \n", "          5512048      493.5       72.0      4619.0      4620.0        2.5   \n", "          5512048       21.5      497.0      4620.0      4621.0       70.0   \n", "          5512048       30.0      495.0      4620.0      4621.0       70.0   \n", "          5512048      494.0       72.0      4619.5      4620.5       36.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512048       15.5       54.5      4620.0      4621.0        4.5   \n", "          5512048       14.0       65.5      4620.0      4621.0       23.5   \n", "          5512048       66.0       22.0      4619.0      4620.0        1.5   \n", "          5512048       66.0       22.0      4619.0      4620.0       22.0   \n", "          5512048       11.5       24.5      4619.0      4620.0       36.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512048      146.5  \n", "          5512048       84.0  \n", "          5512048      144.5  \n", "          5512048      144.5  \n", "          5512048      126.5  \n", "...                      ...  \n", "          5512048       42.0  \n", "          5512048       42.0  \n", "          5512048       29.5  \n", "          5512048       25.0  \n", "          5512048       27.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512049),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512049  1653614700  4618.0   3.0      4617.5      4616.5   \n", "          5512049  1653614701  4617.5   5.0      4617.0      4616.0   \n", "          5512049  1653614702  4617.5  11.0      4617.0      4616.0   \n", "          5512049  1653614703  4619.0  31.5      4618.5      4617.5   \n", "          5512049  1653614704  4619.0   2.5      4619.5      4618.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512049  1653614995  4627.5   2.5      4627.0      4626.0   \n", "          5512049  1653614996  4627.5  12.5      4627.0      4626.0   \n", "          5512049  1653614997  4627.5   7.5      4628.0      4627.0   \n", "          5512049  1653614998  4628.0  20.5      4628.0      4627.0   \n", "          5512049  1653614999  4628.5  11.5      4627.0      4626.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512049        8.5       25.5      4618.5      4619.5       16.0   \n", "          5512049       15.0       34.0      4618.5      4619.5       20.5   \n", "          5512049       28.0       34.5      4618.5      4619.5       26.0   \n", "          5512049      267.0      288.5      4620.0      4621.0       38.5   \n", "          5512049       44.5      298.0      4621.0      4622.0       31.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512049       24.5       51.5      4628.0      4629.0       21.0   \n", "          5512049       26.0       66.5      4628.0      4629.0        4.0   \n", "          5512049       14.0       50.0      4629.0      4630.0      169.0   \n", "          5512049       15.5       90.0      4629.0      4630.0      168.5   \n", "          5512049      120.5       87.0      4628.5      4629.5       94.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512049       35.5  \n", "          5512049       37.5  \n", "          5512049       38.0  \n", "          5512049       60.0  \n", "          5512049       82.5  \n", "...                      ...  \n", "          5512049      171.0  \n", "          5512049      171.0  \n", "          5512049     1185.5  \n", "          5512049     1184.0  \n", "          5512049      674.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512050),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512050  1653615000  4629.0  44.0      4628.0      4627.0   \n", "          5512050  1653615001  4628.5  56.5      4627.5      4626.5   \n", "          5512050  1653615002  4629.0   8.0      4628.0      4627.0   \n", "          5512050  1653615003  4628.5  12.0      4628.0      4627.0   \n", "          5512050  1653615004  4628.0   9.5      4628.0      4627.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512050  1653615295  4641.5  10.0      4641.0      4640.0   \n", "          5512050  1653615296  4642.0   6.0      4641.0      4640.0   \n", "          5512050  1653615297  4641.0   8.0      4641.0      4640.0   \n", "          5512050  1653615298  4641.0  23.5      4640.0      4639.0   \n", "          5512050  1653615299  4641.0   3.0      4640.0      4639.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512050        6.5      116.5      4629.0      4630.0      149.5   \n", "          5512050       62.0      102.5      4628.5      4629.5       58.5   \n", "          5512050       22.5      115.0      4629.0      4630.0      105.5   \n", "          5512050       29.0      115.0      4629.0      4630.0      103.5   \n", "          5512050       53.0      117.0      4629.0      4630.0       99.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512050       25.5       69.0      4642.0      4643.0       91.0   \n", "          5512050       29.5       73.5      4642.0      4643.0       80.0   \n", "          5512050       35.0       74.0      4642.0      4643.0       77.5   \n", "          5512050       69.5       59.0      4641.0      4642.0       36.5   \n", "          5512050       59.5       58.5      4641.0      4642.0       54.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512050     1184.0  \n", "          5512050      645.0  \n", "          5512050     1184.0  \n", "          5512050     1184.0  \n", "          5512050     1184.0  \n", "...                      ...  \n", "          5512050      231.0  \n", "          5512050      232.5  \n", "          5512050      234.0  \n", "          5512050       82.0  \n", "          5512050       86.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512051),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512051  1653615300  4640.5   14.0      4640.0      4639.0   \n", "          5512051  1653615301  4641.0   30.5      4640.5      4639.5   \n", "          5512051  1653615302  4641.0   13.0      4641.0      4640.0   \n", "          5512051  1653615303  4641.0    4.5      4641.0      4640.0   \n", "          5512051  1653615304  4641.5   11.5      4641.0      4640.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512051  1653615595  4635.0    7.0      4634.0      4633.0   \n", "          5512051  1653615596  4635.0  152.5      4634.0      4633.0   \n", "          5512051  1653615597  4635.0  109.0      4634.5      4633.5   \n", "          5512051  1653615598  4634.0    3.0      4634.0      4633.0   \n", "          5512051  1653615599  4635.0    5.5      4634.5      4633.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512051       53.0       58.0      4641.0      4642.0       54.0   \n", "          5512051       17.0       51.5      4641.5      4642.5       72.0   \n", "          5512051       41.0       36.5      4642.0      4643.0       94.5   \n", "          5512051       48.5       41.0      4642.0      4643.0       95.5   \n", "          5512051       49.5       44.0      4642.0      4643.0       91.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512051       83.0       47.0      4636.0      4637.0      307.0   \n", "          5512051       85.5       47.0      4635.5      4636.5      173.5   \n", "          5512051       43.5       56.0      4635.5      4636.5       24.5   \n", "          5512051       64.0       47.0      4635.0      4636.0       11.5   \n", "          5512051       43.0       56.0      4635.5      4636.5       31.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512051       99.0  \n", "          5512051      178.0  \n", "          5512051      257.5  \n", "          5512051      259.0  \n", "          5512051      264.0  \n", "...                      ...  \n", "          5512051      102.0  \n", "          5512051       75.0  \n", "          5512051       75.0  \n", "          5512051       56.0  \n", "          5512051       79.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512052),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512052  1653615600  4634.5   29.0      4634.0      4633.0   \n", "          5512052  1653615601  4634.5    6.0      4634.0      4633.0   \n", "          5512052  1653615602  4634.5   65.0      4634.0      4633.0   \n", "          5512052  1653615603  4634.5   88.0      4634.0      4633.0   \n", "          5512052  1653615604  4634.5  106.0      4634.0      4633.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512052  1653615895  4639.0    8.0      4638.5      4637.5   \n", "          5512052  1653615896  4640.0   14.0      4639.0      4638.0   \n", "          5512052  1653615897  4639.5    5.0      4639.0      4638.0   \n", "          5512052  1653615898  4639.5    2.0      4639.0      4638.0   \n", "          5512052  1653615899  4639.5    3.5      4639.0      4638.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512052       66.5       47.0      4635.0      4636.0       24.5   \n", "          5512052       64.5       47.0      4635.0      4636.0       39.5   \n", "          5512052      101.0       46.0      4635.0      4636.0       21.0   \n", "          5512052      126.0       45.5      4635.0      4636.0       81.0   \n", "          5512052      157.0       46.0      4635.0      4636.0       52.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512052       23.0       29.5      4639.5      4640.5       29.5   \n", "          5512052       86.0       34.0      4640.0      4641.0       22.5   \n", "          5512052       98.5       35.5      4640.0      4641.0       10.5   \n", "          5512052      104.0       36.0      4640.0      4641.0       12.5   \n", "          5512052       99.5       36.0      4640.0      4641.0       13.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512052       12.0  \n", "          5512052       13.5  \n", "          5512052       12.5  \n", "          5512052       13.0  \n", "          5512052       12.5  \n", "...                      ...  \n", "          5512052       48.5  \n", "          5512052       49.5  \n", "          5512052       47.5  \n", "          5512052       48.0  \n", "          5512052       48.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512053),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512053  1653615900  4639.5   7.0      4639.0      4638.0   \n", "          5512053  1653615901  4639.5   9.0      4639.5      4638.5   \n", "          5512053  1653615902  4640.5   5.5      4640.0      4639.0   \n", "          5512053  1653615903  4640.0   8.0      4640.0      4639.0   \n", "          5512053  1653615904  4640.5  20.5      4640.0      4639.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512053  1653616195  4635.0   7.0      4633.0      4632.0   \n", "          5512053  1653616196  4633.5   1.0      4633.0      4632.0   \n", "          5512053  1653616197  4634.0   1.5      4633.0      4632.0   \n", "          5512053  1653616198  4634.0   0.0      4633.0      4632.0   \n", "          5512053  1653616199  4633.0   1.5      4633.0      4632.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512053       99.0       35.5      4640.0      4641.0        7.0   \n", "          5512053       66.0       67.5      4641.0      4642.0       57.0   \n", "          5512053       39.5       97.0      4641.0      4642.0       56.5   \n", "          5512053       82.5       87.0      4641.0      4642.0       69.0   \n", "          5512053       74.0       77.0      4641.0      4642.0       54.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512053       68.0      679.0      4635.0      4636.0       64.0   \n", "          5512053       67.0      678.0      4634.0      4635.0        1.5   \n", "          5512053       66.5      678.0      4634.5      4635.5       37.0   \n", "          5512053       69.0      678.0      4635.0      4636.0       73.0   \n", "          5512053       68.0      678.0      4635.0      4636.0       75.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512053       52.0  \n", "          5512053       53.5  \n", "          5512053       53.0  \n", "          5512053       52.5  \n", "          5512053       52.5  \n", "...                      ...  \n", "          5512053      137.0  \n", "          5512053       64.0  \n", "          5512053      105.0  \n", "          5512053      137.0  \n", "          5512053      137.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5512054),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512054  1653616200  4633.0   2.0      4633.0      4632.0   \n", "          5512054  1653616201  4633.5   3.0      4633.0      4632.0   \n", "          5512054  1653616202  4633.0   1.5      4633.0      4632.0   \n", "          5512054  1653616203  4633.5   1.5      4634.0      4633.0   \n", "          5512054  1653616204  4634.0  14.0      4633.0      4632.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512054  1653616495  4627.0   6.0      4626.0      4625.0   \n", "          5512054  1653616496  4626.5   4.0      4626.0      4625.0   \n", "          5512054  1653616497  4626.5   8.5      4626.0      4625.0   \n", "          5512054  1653616498  4626.0   1.5      4626.0      4625.0   \n", "          5512054  1653616499  4627.0   3.0      4626.0      4625.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512054       69.0      674.0      4635.0      4636.0       77.0   \n", "          5512054       61.0      675.0      4635.0      4636.0       78.0   \n", "          5512054       65.0      675.5      4634.5      4635.5       39.5   \n", "          5512054        6.5       90.5      4635.0      4636.0       78.5   \n", "          5512054       91.0      677.0      4634.5      4635.5       44.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512054       13.0      109.0      4627.0      4628.0       31.0   \n", "          5512054       10.0      109.0      4627.0      4628.0       24.5   \n", "          5512054        8.0      109.0      4627.0      4628.0       19.0   \n", "          5512054        2.5      110.0      4627.0      4628.0       24.5   \n", "          5512054        4.0      110.0      4627.0      4628.0       21.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512054      137.0  \n", "          5512054      137.0  \n", "          5512054      108.5  \n", "          5512054      137.0  \n", "          5512054      112.5  \n", "...                      ...  \n", "          5512054      100.0  \n", "          5512054      100.0  \n", "          5512054      100.0  \n", "          5512054       99.0  \n", "          5512054       99.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512055),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512055  1653616500  4626.0   0.5      4626.0      4625.0   \n", "          5512055  1653616501  4626.5   6.5      4625.5      4624.5   \n", "          5512055  1653616502  4626.5   4.5      4625.5      4624.5   \n", "          5512055  1653616503  4626.5   4.5      4626.0      4625.0   \n", "          5512055  1653616504  4627.0   8.0      4626.5      4625.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512055  1653616795  4628.5   7.0      4627.5      4626.5   \n", "          5512055  1653616796  4629.0   8.5      4628.0      4627.0   \n", "          5512055  1653616797  4628.0   1.5      4628.0      4627.0   \n", "          5512055  1653616798  4628.0  75.5      4628.5      4627.5   \n", "          5512055  1653616799  4628.0   1.0      4629.0      4628.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512055        8.5      112.5      4627.0      4628.0       22.5   \n", "          5512055       58.5       81.5      4626.5      4627.5       14.0   \n", "          5512055       59.0       82.5      4626.5      4627.5       10.0   \n", "          5512055       21.0      117.5      4627.0      4628.0       17.5   \n", "          5512055       24.0       80.0      4627.5      4628.5       55.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512055       28.5       48.0      4629.0      4630.0      161.0   \n", "          5512055       19.5       56.0      4629.0      4630.0      143.5   \n", "          5512055       27.0       58.0      4629.0      4630.0      144.0   \n", "          5512055       18.0       50.5      4630.0      4631.0       55.0   \n", "          5512055       29.0       44.5      4630.0      4631.0       55.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512055       99.0  \n", "          5512055       63.0  \n", "          5512055       61.5  \n", "          5512055       99.0  \n", "          5512055       99.0  \n", "...                      ...  \n", "          5512055       56.0  \n", "          5512055       56.0  \n", "          5512055       56.0  \n", "          5512055       28.0  \n", "          5512055       28.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512056),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512056  1653616800  4629.5  28.0      4629.0      4628.0   \n", "          5512056  1653616801  4631.0  19.0      4630.5      4629.5   \n", "          5512056  1653616802  4631.0  11.5      4631.5      4630.5   \n", "          5512056  1653616803  4632.0   9.0      4632.0      4631.0   \n", "          5512056  1653616804  4632.5  29.0      4631.5      4630.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512056  1653617095  4625.5  11.0      4625.0      4624.0   \n", "          5512056  1653617096  4626.0  14.0      4625.0      4624.0   \n", "          5512056  1653617097  4627.0   3.0      4625.0      4624.0   \n", "          5512056  1653617098  4625.0   0.5      4625.0      4624.0   \n", "          5512056  1653617099  4626.0   1.0      4625.0      4624.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512056       34.0       45.0      4630.0      4631.0       30.0   \n", "          5512056       20.0       47.5      4631.5      4632.5       19.5   \n", "          5512056       24.0       50.5      4633.0      4634.0       23.0   \n", "          5512056       47.5       46.0      4633.0      4634.0       15.5   \n", "          5512056       24.0       47.0      4632.5      4633.5        7.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512056       33.0       51.0      4626.0      4627.0       23.5   \n", "          5512056       34.0       51.0      4626.5      4627.5       30.0   \n", "          5512056       28.0       51.0      4626.5      4627.5       26.5   \n", "          5512056       32.5       51.0      4626.0      4627.0        4.5   \n", "          5512056       34.0       51.0      4626.0      4627.0        4.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512056       28.0  \n", "          5512056       31.0  \n", "          5512056       35.0  \n", "          5512056       32.0  \n", "          5512056       29.5  \n", "...                      ...  \n", "          5512056       49.0  \n", "          5512056       47.0  \n", "          5512056       47.5  \n", "          5512056       51.0  \n", "          5512056       51.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512057),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512057  1653617100  4625.0   3.0      4625.0      4624.0   \n", "          5512057  1653617101  4626.0   2.5      4625.5      4624.5   \n", "          5512057  1653617102  4627.0   1.5      4625.0      4624.0   \n", "          5512057  1653617103  4625.5  42.5      4624.0      4623.0   \n", "          5512057  1653617104  4624.5   9.0      4624.0      4623.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512057  1653617395  4627.0   4.0      4625.5      4624.5   \n", "          5512057  1653617396  4627.0   0.0      4626.0      4625.0   \n", "          5512057  1653617397  4626.0   3.5      4626.0      4625.0   \n", "          5512057  1653617398  4627.0   4.5      4626.5      4625.5   \n", "          5512057  1653617399  4627.0   3.0      4626.0      4625.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512057       31.5       51.0      4626.5      4627.5       26.0   \n", "          5512057       39.5       39.0      4626.5      4627.5       30.5   \n", "          5512057       78.0       51.0      4626.0      4627.0        9.0   \n", "          5512057       52.5       69.0      4625.5      4626.5       10.0   \n", "          5512057       40.5       69.0      4625.0      4626.0       11.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512057       69.5       94.5      4627.0      4628.0       12.0   \n", "          5512057       18.0      120.0      4627.0      4628.0        8.0   \n", "          5512057       12.5      120.0      4627.0      4628.0        7.5   \n", "          5512057        9.5       69.0      4627.5      4628.5       16.5   \n", "          5512057        8.5      121.5      4627.0      4628.0        1.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512057       51.0  \n", "          5512057       51.0  \n", "          5512057       58.0  \n", "          5512057       58.0  \n", "          5512057       61.0  \n", "...                      ...  \n", "          5512057       32.0  \n", "          5512057       32.0  \n", "          5512057       32.0  \n", "          5512057       48.5  \n", "          5512057       21.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512058),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512058  1653617400  4627.0   0.5      4626.5      4625.5   \n", "          5512058  1653617401  4628.0   1.5      4626.0      4625.0   \n", "          5512058  1653617402  4627.5   2.0      4626.0      4625.0   \n", "          5512058  1653617403  4627.5   6.0      4627.0      4626.0   \n", "          5512058  1653617404  4628.0  31.0      4628.5      4627.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512058  1653617695  4624.5  31.0      4624.0      4623.0   \n", "          5512058  1653617696  4624.0   6.0      4624.0      4623.0   \n", "          5512058  1653617697  4624.5   4.0      4623.0      4622.0   \n", "          5512058  1653617698  4625.0   7.0      4623.5      4622.5   \n", "          5512058  1653617699  4624.0   4.5      4623.0      4622.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512058        5.5       66.0      4627.5      4628.5       11.0   \n", "          5512058       13.0      122.0      4627.5      4628.5       14.0   \n", "          5512058       16.5      123.0      4627.5      4628.5       15.5   \n", "          5512058       39.0       20.0      4628.0      4629.0       15.5   \n", "          5512058       19.5       36.5      4629.5      4630.5       62.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512058        2.5       66.0      4625.0      4626.0      118.0   \n", "          5512058        2.5       66.0      4625.0      4626.0      111.0   \n", "          5512058       67.0       60.0      4625.0      4626.0      108.0   \n", "          5512058       34.5       64.0      4625.0      4626.0      104.5   \n", "          5512058       68.0       60.0      4624.0      4625.0       24.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512058       43.0  \n", "          5512058       46.5  \n", "          5512058       46.5  \n", "          5512058       65.0  \n", "          5512058       42.5  \n", "...                      ...  \n", "          5512058        7.0  \n", "          5512058       11.0  \n", "          5512058       11.0  \n", "          5512058       11.0  \n", "          5512058       94.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512062),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512062  1653618601  4624.0  24.5      4622.0      4621.0   \n", "          5512062  1653618602  4623.5  19.5      4623.0      4622.0   \n", "          5512062  1653618603  4624.0  39.5      4623.0      4622.0   \n", "          5512062  1653618604  4623.5  11.5      4624.0      4623.0   \n", "          5512062  1653618605  4623.5  36.5      4623.0      4622.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512062  1653618895  4615.0   0.5      4615.0      4614.0   \n", "          5512062  1653618896  4616.0   1.0      4616.0      4615.0   \n", "          5512062  1653618897  4617.0   5.5      4615.0      4614.0   \n", "          5512062  1653618898  4617.0   4.5      4615.5      4614.5   \n", "          5512062  1653618899  4616.0   7.0      4616.0      4615.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512062       42.0       89.0      4624.0      4625.0       35.5   \n", "          5512062       25.0       42.0      4625.0      4626.0       93.5   \n", "          5512062       47.5       38.0      4624.5      4625.5       19.0   \n", "          5512062        4.0       22.0      4625.0      4626.0       31.5   \n", "          5512062       11.0       39.0      4624.0      4625.0       13.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512062       24.0       36.0      4616.5      4617.5      117.0   \n", "          5512062        3.5       18.0      4617.0      4618.0      230.0   \n", "          5512062       22.0       36.0      4616.0      4617.0        9.0   \n", "          5512062       14.5       31.5      4616.5      4617.5      117.5   \n", "          5512062        1.5       17.5      4617.0      4618.0      225.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512062       94.0  \n", "          5512062       24.0  \n", "          5512062       30.0  \n", "          5512062       24.0  \n", "          5512062       20.0  \n", "...                      ...  \n", "          5512062      140.5  \n", "          5512062       51.0  \n", "          5512062      224.5  \n", "          5512062      138.0  \n", "          5512062       54.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5512063),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512063  1653618900  4617.0   0.0      4616.0      4615.0   \n", "          5512063  1653618901  4616.0  38.5      4615.5      4614.5   \n", "          5512063  1653618902  4616.0   0.0      4616.0      4615.0   \n", "          5512063  1653618903  4615.5   8.0      4615.5      4614.5   \n", "          5512063  1653618904  4616.0   5.0      4615.0      4614.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512063  1653619195  4606.0   6.0      4606.0      4605.0   \n", "          5512063  1653619196  4606.5   3.0      4606.0      4605.0   \n", "          5512063  1653619197  4607.0   2.5      4606.0      4605.0   \n", "          5512063  1653619198  4606.5   3.0      4606.0      4605.0   \n", "          5512063  1653619199  4607.0   4.5      4607.0      4606.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512063        7.0       20.0      4617.0      4618.0      225.0   \n", "          5512063       11.5       38.0      4616.5      4617.5       88.0   \n", "          5512063       18.5       41.0      4617.0      4618.0      185.0   \n", "          5512063       28.5       38.5      4616.5      4617.5      137.5   \n", "          5512063       35.0       35.5      4616.0      4617.0       37.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512063       27.0       80.0      4607.5      4608.5       27.5   \n", "          5512063       25.0       81.0      4607.0      4608.0        4.5   \n", "          5512063       25.0       81.5      4607.0      4608.0        4.0   \n", "          5512063       27.5       84.0      4607.0      4608.0        7.0   \n", "          5512063        2.5       32.0      4608.0      4609.0       50.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512063       57.0  \n", "          5512063      116.0  \n", "          5512063       57.0  \n", "          5512063      157.5  \n", "          5512063      259.0  \n", "...                      ...  \n", "          5512063      240.5  \n", "          5512063       47.0  \n", "          5512063       48.0  \n", "          5512063       48.0  \n", "          5512063      436.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512064),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512064  1653619200  4607.0  34.5      4606.0      4605.0   \n", "          5512064  1653619201  4606.5   4.0      4606.0      4605.0   \n", "          5512064  1653619202  4607.0   5.0      4607.0      4606.0   \n", "          5512064  1653619203  4607.5   1.5      4607.0      4606.0   \n", "          5512064  1653619204  4607.5  11.5      4606.5      4605.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512064  1653619495  4603.0  65.5      4602.5      4601.5   \n", "          5512064  1653619496  4603.0   4.5      4602.0      4601.0   \n", "          5512064  1653619497  4602.5  10.0      4602.0      4601.0   \n", "          5512064  1653619498  4603.0  45.5      4601.5      4600.5   \n", "          5512064  1653619499  4602.0  19.5      4601.0      4600.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512064       28.5       84.0      4607.5      4608.5       29.5   \n", "          5512064       20.5       84.0      4607.0      4608.0       11.5   \n", "          5512064        8.5       31.0      4608.0      4609.0       52.0   \n", "          5512064       13.0       31.0      4608.0      4609.0       72.0   \n", "          5512064       25.5       61.0      4607.5      4608.5       43.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512064       89.0       88.0      4603.5      4604.5       29.5   \n", "          5512064       57.0      108.0      4603.0      4604.0       43.5   \n", "          5512064       52.0      108.0      4603.0      4604.0       46.0   \n", "          5512064       59.0      213.5      4603.0      4604.0       26.0   \n", "          5512064      110.5      322.0      4602.5      4603.5       11.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512064      245.0  \n", "          5512064       49.5  \n", "          5512064      433.5  \n", "          5512064      433.0  \n", "          5512064      248.5  \n", "...                      ...  \n", "          5512064       44.5  \n", "          5512064       55.0  \n", "          5512064       52.0  \n", "          5512064       52.5  \n", "          5512064       27.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512065),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512065  1653619500  4603.0  15.5      4602.5      4601.5   \n", "          5512065  1653619501  4603.0   8.0      4603.0      4602.0   \n", "          5512065  1653619502  4603.5  18.0      4603.5      4602.5   \n", "          5512065  1653619503  4604.5   3.0      4604.0      4603.0   \n", "          5512065  1653619504  4604.0   7.0      4603.0      4602.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512065  1653619795  4607.0   2.5      4606.0      4605.0   \n", "          5512065  1653619796  4607.0   4.5      4607.0      4606.0   \n", "          5512065  1653619797  4607.5   3.5      4607.0      4606.0   \n", "          5512065  1653619798  4607.5   1.0      4607.0      4606.0   \n", "          5512065  1653619799  4608.0   1.0      4607.0      4606.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512065       24.5       75.5      4603.5      4604.5       26.0   \n", "          5512065       16.5       38.5      4604.0      4605.0       29.5   \n", "          5512065        6.5       29.0      4605.0      4606.0       32.0   \n", "          5512065        1.5       16.0      4605.0      4606.0       32.5   \n", "          5512065       27.0       32.0      4605.0      4606.0       47.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512065       59.0      472.0      4607.0      4608.0        1.5   \n", "          5512065       59.0       54.0      4608.0      4609.0       61.0   \n", "          5512065       74.0       66.0      4608.0      4609.0       55.0   \n", "          5512065       75.5       69.0      4608.0      4609.0       54.0   \n", "          5512065       78.0       69.0      4608.0      4609.0       52.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512065       32.5  \n", "          5512065       31.0  \n", "          5512065       36.0  \n", "          5512065       36.0  \n", "          5512065       36.0  \n", "...                      ...  \n", "          5512065       61.0  \n", "          5512065       80.0  \n", "          5512065       81.0  \n", "          5512065       81.0  \n", "          5512065       81.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512066),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512066  1653619800  4608.0  28.5      4607.5      4606.5   \n", "          5512066  1653619801  4608.0  11.5      4608.0      4607.0   \n", "          5512066  1653619802  4607.0   6.0      4607.0      4606.0   \n", "          5512066  1653619803  4608.0  15.0      4607.5      4606.5   \n", "          5512066  1653619804  4608.0   8.5      4608.0      4607.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512066  1653620095  4601.5   6.5      4600.0      4599.0   \n", "          5512066  1653620096  4601.5   2.5      4600.5      4599.5   \n", "          5512066  1653620097  4600.0   4.0      4600.0      4599.0   \n", "          5512066  1653620098  4600.5   1.0      4600.0      4599.0   \n", "          5512066  1653620099  4601.0   4.0      4600.0      4599.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512066       46.0       75.5      4608.5      4609.5       64.5   \n", "          5512066        7.5       82.0      4609.0      4610.0       86.5   \n", "          5512066       79.0       67.5      4608.0      4609.0       11.0   \n", "          5512066       50.0       72.0      4608.5      4609.5       50.5   \n", "          5512066       15.0       80.5      4609.0      4610.0       92.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512066       39.5       60.0      4601.5      4602.5       32.5   \n", "          5512066       22.5       46.0      4602.0      4603.0       63.0   \n", "          5512066       37.0       51.0      4601.5      4602.5       36.0   \n", "          5512066       37.0       51.0      4601.0      4602.0        9.5   \n", "          5512066       37.5       51.0      4601.0      4602.0       15.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512066      122.5  \n", "          5512066      164.0  \n", "          5512066       92.0  \n", "          5512066      128.0  \n", "          5512066      164.0  \n", "...                      ...  \n", "          5512066       39.0  \n", "          5512066       13.0  \n", "          5512066       38.0  \n", "          5512066       63.0  \n", "          5512066       53.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512067),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512067  1653620100  4601.0   6.0      4600.0      4599.0   \n", "          5512067  1653620101  4600.0   5.0      4600.0      4599.0   \n", "          5512067  1653620102  4600.5   7.5      4600.5      4599.5   \n", "          5512067  1653620103  4600.0   4.0      4600.0      4599.0   \n", "          5512067  1653620104  4600.5  17.0      4599.5      4598.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512067  1653620395  4598.0   6.5      4597.5      4596.5   \n", "          5512067  1653620396  4598.0   2.5      4598.0      4597.0   \n", "          5512067  1653620397  4599.0   4.5      4598.0      4597.0   \n", "          5512067  1653620398  4599.0  20.0      4598.0      4597.0   \n", "          5512067  1653620399  4599.0   8.0      4598.0      4597.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512067       38.5       51.0      4601.0      4602.0        6.0   \n", "          5512067       32.5       51.0      4601.0      4602.0        9.0   \n", "          5512067       15.0       40.0      4601.5      4602.5       31.5   \n", "          5512067       24.0       52.0      4601.0      4602.0        2.0   \n", "          5512067       37.0       58.0      4601.0      4602.0        6.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512067        6.5       38.0      4598.5      4599.5       28.0   \n", "          5512067        3.0       32.5      4599.0      4600.0       48.0   \n", "          5512067        4.0       32.5      4599.0      4600.0       45.0   \n", "          5512067        8.0       33.0      4599.0      4600.0       25.5   \n", "          5512067       31.5       33.0      4599.0      4600.0        7.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512067       53.0  \n", "          5512067       53.0  \n", "          5512067       33.0  \n", "          5512067       65.5  \n", "          5512067       66.0  \n", "...                      ...  \n", "          5512067       92.0  \n", "          5512067      129.0  \n", "          5512067      129.0  \n", "          5512067      129.0  \n", "          5512067      129.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512068),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512068  1653620400  4598.0  38.5      4597.0      4596.0   \n", "          5512068  1653620401  4597.5  23.5      4597.5      4596.5   \n", "          5512068  1653620402  4598.0   2.5      4597.5      4596.5   \n", "          5512068  1653620403  4599.5  75.0      4599.0      4598.0   \n", "          5512068  1653620404  4602.0  48.0      4601.0      4600.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512068  1653620695  4606.5   8.0      4606.0      4605.0   \n", "          5512068  1653620696  4607.0   8.5      4606.0      4605.0   \n", "          5512068  1653620697  4607.0   3.5      4606.0      4605.0   \n", "          5512068  1653620698  4607.0   0.5      4606.0      4605.0   \n", "          5512068  1653620699  4607.0   0.0      4606.5      4605.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512068       17.5       45.5      4598.0      4599.0       14.5   \n", "          5512068       14.0       34.0      4599.0      4600.0       21.0   \n", "          5512068       19.0       37.0      4599.0      4600.0       24.5   \n", "          5512068       28.5       22.5      4600.5      4601.5       82.5   \n", "          5512068       26.0       25.5      4602.0      4603.0       24.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512068       26.0      471.0      4607.0      4608.0       18.5   \n", "          5512068       22.0      474.0      4607.0      4608.0       12.0   \n", "          5512068       30.5      479.0      4607.0      4608.0        4.5   \n", "          5512068       37.0      479.0      4607.0      4608.0        5.5   \n", "          5512068       27.5      266.5      4608.0      4609.0      149.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512068       23.5  \n", "          5512068      128.0  \n", "          5512068      127.0  \n", "          5512068       36.0  \n", "          5512068       39.5  \n", "...                      ...  \n", "          5512068      151.0  \n", "          5512068      151.0  \n", "          5512068      151.0  \n", "          5512068      151.0  \n", "          5512068       68.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512069),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512069  1653620700  4607.0   4.5      4607.0      4606.0   \n", "          5512069  1653620701  4607.0   0.5      4607.0      4606.0   \n", "          5512069  1653620702  4607.0   3.5      4607.0      4606.0   \n", "          5512069  1653620703  4607.0   6.0      4607.0      4606.0   \n", "          5512069  1653620704  4607.0   4.5      4606.5      4605.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512069  1653620995  4600.0   1.0      4600.0      4599.0   \n", "          5512069  1653620996  4600.0   9.0      4600.0      4599.0   \n", "          5512069  1653620997  4600.5  39.5      4600.0      4599.0   \n", "          5512069  1653620998  4601.0   1.0      4601.0      4600.0   \n", "          5512069  1653620999  4601.0   0.5      4601.0      4600.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512069        6.0       56.0      4608.0      4609.0      149.0   \n", "          5512069       10.0       57.0      4608.0      4609.0      149.0   \n", "          5512069       12.5       58.0      4608.0      4609.0      149.0   \n", "          5512069        4.5       58.0      4608.0      4609.0      149.0   \n", "          5512069       31.0      269.0      4607.5      4608.5       74.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512069       29.0       50.0      4601.0      4602.0       74.0   \n", "          5512069       48.5       49.0      4601.0      4602.0       73.0   \n", "          5512069       82.5       49.0      4601.5      4602.5       57.0   \n", "          5512069       27.0       84.5      4602.0      4603.0       38.0   \n", "          5512069       31.0       87.0      4602.0      4603.0       38.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512069       66.0  \n", "          5512069       66.0  \n", "          5512069       66.0  \n", "          5512069       66.0  \n", "          5512069      178.5  \n", "...                      ...  \n", "          5512069       41.0  \n", "          5512069       41.0  \n", "          5512069       40.0  \n", "          5512069       36.0  \n", "          5512069       37.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512070),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512070  1653621000  4602.5  34.0      4602.0      4601.0   \n", "          5512070  1653621001  4602.0   1.0      4602.0      4601.0   \n", "          5512070  1653621002  4603.0   1.5      4602.0      4601.0   \n", "          5512070  1653621003  4603.0   2.0      4602.0      4601.0   \n", "          5512070  1653621004  4603.0   0.0      4602.0      4601.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512070  1653621295  4605.5   2.0      4605.0      4604.0   \n", "          5512070  1653621296  4605.5   2.5      4605.0      4604.0   \n", "          5512070  1653621297  4606.0   1.0      4605.0      4604.0   \n", "          5512070  1653621298  4606.0   8.0      4605.5      4604.5   \n", "          5512070  1653621299  4606.0   5.5      4605.0      4604.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512070       27.5       22.0      4603.0      4604.0       36.5   \n", "          5512070       29.0       22.0      4603.0      4604.0       36.0   \n", "          5512070       28.0       20.0      4603.0      4604.0       35.0   \n", "          5512070       27.5       18.0      4603.0      4604.0       46.0   \n", "          5512070       28.0       18.0      4603.0      4604.0       47.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512070       20.5       62.0      4606.0      4607.0        9.5   \n", "          5512070       16.5       61.0      4606.0      4607.0        3.0   \n", "          5512070       16.0       61.0      4606.0      4607.0        2.0   \n", "          5512070       16.5       40.5      4606.5      4607.5       18.0   \n", "          5512070        9.0       59.0      4606.0      4607.0       14.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512070       48.0  \n", "          5512070       48.0  \n", "          5512070       48.0  \n", "          5512070       47.0  \n", "          5512070       46.5  \n", "...                      ...  \n", "          5512070       22.0  \n", "          5512070       22.0  \n", "          5512070       22.0  \n", "          5512070       34.0  \n", "          5512070       33.0  \n", "\n", "[295 rows x 11 columns])\n", "(('BU8888.SC', 5512071),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512071  1653621300  4605.5   2.0      4605.0      4604.0   \n", "          5512071  1653621301  4605.0   1.0      4605.0      4604.0   \n", "          5512071  1653621302  4606.0   4.0      4605.0      4604.0   \n", "          5512071  1653621303  4605.0   2.5      4605.0      4604.0   \n", "          5512071  1653621304  4606.0   2.5      4605.5      4604.5   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512071  1653621595  4613.0   1.0      4612.0      4611.0   \n", "          5512071  1653621596  4613.0  15.5      4612.5      4611.5   \n", "          5512071  1653621597  4613.0   4.5      4613.0      4612.0   \n", "          5512071  1653621598  4613.5   3.5      4613.0      4612.0   \n", "          5512071  1653621599  4613.5   2.5      4613.0      4612.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512071       18.5       61.0      4606.0      4607.0       14.0   \n", "          5512071       18.0       61.0      4606.0      4607.0       14.0   \n", "          5512071       19.5       61.0      4606.0      4607.0        8.5   \n", "          5512071       21.0       61.0      4606.0      4607.0        4.5   \n", "          5512071        9.5       41.0      4606.5      4607.5       22.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512071       36.0       64.0      4613.0      4614.0       10.0   \n", "          5512071       14.5       48.5      4613.5      4614.5       24.0   \n", "          5512071       15.0       33.0      4614.0      4615.0       36.0   \n", "          5512071       24.5       34.0      4614.0      4615.0       31.0   \n", "          5512071       21.5       34.5      4614.0      4615.0       31.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512071       32.5  \n", "          5512071       32.0  \n", "          5512071       33.5  \n", "          5512071       33.5  \n", "          5512071       35.0  \n", "...                      ...  \n", "          5512071       39.0  \n", "          5512071       70.0  \n", "          5512071      100.5  \n", "          5512071      101.0  \n", "          5512071      101.0  \n", "\n", "[293 rows x 11 columns])\n", "(('BU8888.SC', 5512072),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512072  1653621600  4613.5  23.0      4613.0      4612.0   \n", "          5512072  1653621601  4613.5  10.0      4613.5      4612.5   \n", "          5512072  1653621602  4613.0   2.0      4613.0      4612.0   \n", "          5512072  1653621603  4614.0   4.5      4613.0      4612.0   \n", "          5512072  1653621604  4613.5   1.5      4613.0      4612.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512072  1653621895  4605.5   2.5      4605.0      4604.0   \n", "          5512072  1653621896  4605.5   3.5      4605.0      4604.0   \n", "          5512072  1653621897  4605.0   0.5      4605.0      4604.0   \n", "          5512072  1653621898  4605.0   3.5      4604.5      4603.5   \n", "          5512072  1653621899  4605.0   1.0      4604.5      4603.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512072       23.5       31.0      4614.0      4615.0       17.0   \n", "          5512072       20.5       42.0      4614.5      4615.5       53.5   \n", "          5512072       40.5       34.0      4614.0      4615.0        9.5   \n", "          5512072       38.0       32.0      4614.0      4615.0       12.0   \n", "          5512072       36.5       31.0      4614.0      4615.0       16.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512072       10.0       33.0      4606.0      4607.0       30.5   \n", "          5512072        8.0       33.0      4606.0      4607.0       25.5   \n", "          5512072        8.0       33.0      4606.0      4607.0       28.0   \n", "          5512072       18.0       39.0      4606.0      4607.0       29.0   \n", "          5512072       14.0       36.0      4605.5      4606.5       20.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512072      100.0  \n", "          5512072       84.0  \n", "          5512072       99.0  \n", "          5512072       99.5  \n", "          5512072      100.0  \n", "...                      ...  \n", "          5512072       72.0  \n", "          5512072       72.0  \n", "          5512072       73.0  \n", "          5512072       74.0  \n", "          5512072       52.0  \n", "\n", "[297 rows x 11 columns])\n", "(('BU8888.SC', 5512073),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512073  1653621900  4605.0   7.0      4604.0      4603.0   \n", "          5512073  1653621901  4605.5   4.5      4604.5      4603.5   \n", "          5512073  1653621902  4606.0   1.0      4604.0      4603.0   \n", "          5512073  1653621903  4605.0   0.5      4604.0      4603.0   \n", "          5512073  1653621904  4604.0   0.0      4604.0      4603.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512073  1653622195  4604.0   2.0      4604.0      4603.0   \n", "          5512073  1653622196  4604.5   3.5      4604.0      4603.0   \n", "          5512073  1653622197  4604.0  10.5      4604.0      4603.0   \n", "          5512073  1653622198  4604.0   5.5      4603.5      4602.5   \n", "          5512073  1653622199  4603.0   4.5      4603.5      4602.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512073       26.0       45.0      4605.5      4606.5       21.0   \n", "          5512073       11.5       35.5      4606.0      4607.0       30.0   \n", "          5512073       25.0       45.0      4605.5      4606.5       17.0   \n", "          5512073       24.5       45.0      4605.0      4606.0        1.5   \n", "          5512073       25.0       45.0      4605.0      4606.0        5.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512073       24.0       25.0      4605.0      4606.0      429.5   \n", "          5512073       22.0       21.5      4605.0      4606.0      426.5   \n", "          5512073       10.0       17.5      4605.0      4606.0      413.0   \n", "          5512073       10.0       15.5      4604.5      4605.5      207.0   \n", "          5512073        7.5       11.0      4605.0      4606.0      412.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512073       52.0  \n", "          5512073       74.5  \n", "          5512073       53.0  \n", "          5512073       33.0  \n", "          5512073       33.5  \n", "...                      ...  \n", "          5512073       13.0  \n", "          5512073       10.5  \n", "          5512073       10.0  \n", "          5512073      211.0  \n", "          5512073       10.0  \n", "\n", "[298 rows x 11 columns])\n", "(('BU8888.SC', 5512098),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512098  1653629401  4601.0  104.5      4599.5      4598.5   \n", "          5512098  1653629402  4600.5   33.5      4600.5      4599.5   \n", "          5512098  1653629403  4601.0   20.0      4600.5      4599.5   \n", "          5512098  1653629404  4600.5   22.0      4600.0      4599.0   \n", "          5512098  1653629405  4600.0   49.0      4598.5      4597.5   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512098  1653629695  4584.0   10.0      4584.5      4583.5   \n", "          5512098  1653629696  4585.5    2.0      4584.0      4583.0   \n", "          5512098  1653629697  4585.5    3.0      4585.0      4584.0   \n", "          5512098  1653629698  4586.0    1.0      4585.0      4584.0   \n", "          5512098  1653629699  4586.0   36.5      4585.0      4584.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512098       34.0       42.0      4601.0      4603.5       32.0   \n", "          5512098       38.5       22.0      4603.5      4604.5       58.5   \n", "          5512098       18.0       20.5      4602.5      4603.5       19.5   \n", "          5512098       19.5       13.0      4601.0      4602.0       35.0   \n", "          5512098       45.0       87.0      4600.5      4601.5       38.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512098       23.5       52.5      4585.5      4586.5       32.0   \n", "          5512098       45.5       58.5      4585.5      4586.5       31.5   \n", "          5512098        5.5       52.0      4586.0      4587.0       61.5   \n", "          5512098       20.0       56.0      4586.0      4587.0       61.5   \n", "          5512098       32.0       60.0      4586.5      4587.5       52.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512098       38.5  \n", "          5512098      248.0  \n", "          5512098       59.0  \n", "          5512098       32.0  \n", "          5512098       39.5  \n", "...                      ...  \n", "          5512098       51.5  \n", "          5512098       52.0  \n", "          5512098       42.0  \n", "          5512098       42.0  \n", "          5512098       56.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5512099),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512099  1653629700  4585.5   6.0      4586.0      4585.0   \n", "          5512099  1653629701  4586.5  33.0      4585.5      4584.5   \n", "          5512099  1653629702  4587.0   5.5      4585.5      4584.5   \n", "          5512099  1653629703  4587.0  32.5      4586.5      4585.5   \n", "          5512099  1653629704  4588.0  38.0      4587.0      4586.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512099  1653629995  4584.0  15.0      4584.0      4583.0   \n", "          5512099  1653629996  4585.5   6.0      4584.5      4583.5   \n", "          5512099  1653629997  4585.5   3.5      4585.0      4584.0   \n", "          5512099  1653629998  4585.5   6.5      4585.0      4584.0   \n", "          5512099  1653629999  4585.0   2.5      4585.0      4584.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512099       17.0       66.5      4587.0      4588.0       38.0   \n", "          5512099       28.5       55.5      4587.0      4588.0       52.0   \n", "          5512099       34.0       63.0      4587.0      4588.0       65.0   \n", "          5512099       78.5       63.5      4587.5      4588.5       56.5   \n", "          5512099      115.0       60.5      4588.5      4589.5      132.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512099       68.5      113.0      4585.0      4586.0        3.5   \n", "          5512099       39.0       95.5      4586.0      4587.0       28.5   \n", "          5512099       16.0       77.5      4586.0      4587.0       26.5   \n", "          5512099       24.5       76.0      4586.0      4587.0       28.5   \n", "          5512099       24.0       76.0      4586.0      4587.0       32.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512099       68.0  \n", "          5512099       74.5  \n", "          5512099       75.0  \n", "          5512099      141.5  \n", "          5512099      171.5  \n", "...                      ...  \n", "          5512099       39.0  \n", "          5512099       53.0  \n", "          5512099       53.0  \n", "          5512099       53.0  \n", "          5512099       53.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512100),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512100  1653630000  4585.0   1.0      4585.0      4584.0   \n", "          5512100  1653630001  4585.0  13.5      4585.0      4584.0   \n", "          5512100  1653630002  4585.0   1.0      4585.0      4584.0   \n", "          5512100  1653630003  4585.5  19.5      4585.0      4584.0   \n", "          5512100  1653630004  4586.0  23.5      4586.0      4585.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512100  1653630295  4589.0   6.5      4588.0      4587.0   \n", "          5512100  1653630296  4589.0  24.5      4587.0      4586.0   \n", "          5512100  1653630297  4588.0   3.5      4587.0      4586.0   \n", "          5512100  1653630298  4588.0   1.5      4587.0      4586.0   \n", "          5512100  1653630299  4588.0   0.5      4587.0      4586.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512100       24.5       82.5      4586.0      4587.0       33.0   \n", "          5512100       21.5       84.0      4586.0      4587.0       28.5   \n", "          5512100       31.0       84.5      4586.0      4587.0       31.5   \n", "          5512100       12.0       84.5      4586.0      4587.0       45.5   \n", "          5512100       26.0       26.5      4587.0      4588.0       68.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512100       45.0       48.5      4589.0      4590.0       19.5   \n", "          5512100       51.5      444.0      4588.0      4589.0       10.5   \n", "          5512100       59.0      444.0      4588.0      4589.0        6.5   \n", "          5512100       59.0      434.0      4588.0      4589.0       15.5   \n", "          5512100       61.0      434.0      4588.0      4589.0       17.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512100       53.0  \n", "          5512100       52.0  \n", "          5512100       51.0  \n", "          5512100       65.5  \n", "          5512100       77.0  \n", "...                      ...  \n", "          5512100      508.5  \n", "          5512100       39.5  \n", "          5512100       40.5  \n", "          5512100       41.0  \n", "          5512100       44.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512101),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512101  1653630300  4587.5   1.5      4587.0      4586.0   \n", "          5512101  1653630301  4587.0   7.5      4587.0      4586.0   \n", "          5512101  1653630302  4587.5  41.5      4587.0      4586.0   \n", "          5512101  1653630303  4588.0   4.0      4588.0      4587.0   \n", "          5512101  1653630304  4588.5   1.0      4588.0      4587.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512101  1653630595  4590.0  11.0      4589.0      4588.0   \n", "          5512101  1653630596  4589.0  17.5      4589.0      4588.0   \n", "          5512101  1653630597  4589.0   5.0      4589.0      4588.0   \n", "          5512101  1653630598  4590.0   4.5      4589.0      4588.0   \n", "          5512101  1653630599  4589.5   2.0      4589.0      4588.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512101       62.5      433.5      4588.0      4589.0       21.5   \n", "          5512101       51.0      433.0      4588.0      4589.0       64.0   \n", "          5512101       56.0      433.0      4589.0      4590.0       47.0   \n", "          5512101       34.0       62.0      4589.0      4590.0       42.5   \n", "          5512101       26.0       65.0      4589.0      4590.0       41.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512101       24.0        8.0      4590.0      4591.0      355.0   \n", "          5512101       24.0       13.0      4590.0      4591.0      339.5   \n", "          5512101       19.5       17.0      4590.0      4591.0      332.0   \n", "          5512101       11.5       17.5      4590.0      4591.0      329.5   \n", "          5512101        9.5       20.0      4590.0      4591.0      331.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512101       49.0  \n", "          5512101       49.0  \n", "          5512101      510.0  \n", "          5512101      510.0  \n", "          5512101      510.0  \n", "...                      ...  \n", "          5512101      176.0  \n", "          5512101      176.5  \n", "          5512101      177.0  \n", "          5512101      178.0  \n", "          5512101      178.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5512102),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512102  1653630600  4589.0    6.0      4589.0      4588.0   \n", "          5512102  1653630601  4589.5    5.0      4589.0      4588.0   \n", "          5512102  1653630602  4589.5    7.5      4589.0      4588.0   \n", "          5512102  1653630603  4590.0   63.0      4589.0      4588.0   \n", "          5512102  1653630604  4590.0  180.0      4590.0      4589.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512102  1653630895  4591.0    2.5      4591.0      4590.0   \n", "          5512102  1653630896  4591.5   17.5      4591.0      4590.0   \n", "          5512102  1653630897  4591.0    5.0      4592.0      4591.0   \n", "          5512102  1653630898  4591.0    0.5      4592.0      4591.0   \n", "          5512102  1653630899  4592.5    1.0      4592.0      4591.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512102        6.0       20.0      4590.0      4591.0      330.5   \n", "          5512102        2.5       20.0      4590.0      4591.0      329.5   \n", "          5512102       17.5       20.0      4590.0      4591.0      317.5   \n", "          5512102       25.0       20.0      4590.0      4591.0      261.5   \n", "          5512102       65.5       43.0      4591.0      4592.0       89.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512102       12.0       50.0      4592.0      4593.0       30.0   \n", "          5512102       33.0       50.0      4592.5      4593.5      126.5   \n", "          5512102        9.0       44.0      4593.0      4594.0      218.0   \n", "          5512102       16.5       37.0      4593.0      4594.0      218.0   \n", "          5512102       16.5       33.5      4593.0      4594.0      218.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512102      179.5  \n", "          5512102      180.0  \n", "          5512102      176.0  \n", "          5512102      165.0  \n", "          5512102       81.0  \n", "...                      ...  \n", "          5512102      218.0  \n", "          5512102      130.5  \n", "          5512102       43.0  \n", "          5512102       43.0  \n", "          5512102       43.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512103),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512103  1653630900  4592.5   2.5      4592.0      4591.0   \n", "          5512103  1653630901  4593.0   1.0      4592.0      4591.0   \n", "          5512103  1653630902  4592.0   8.0      4591.5      4590.5   \n", "          5512103  1653630903  4592.0   0.0      4591.0      4590.0   \n", "          5512103  1653630904  4592.0   0.5      4591.0      4590.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512103  1653631195  4597.0   3.0      4597.0      4596.0   \n", "          5512103  1653631196  4597.0   2.0      4597.0      4596.0   \n", "          5512103  1653631197  4597.0   0.5      4597.0      4596.0   \n", "          5512103  1653631198  4598.0  24.0      4597.0      4596.0   \n", "          5512103  1653631199  4598.0  18.0      4597.5      4596.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512103       14.5       35.0      4593.0      4594.0      217.0   \n", "          5512103        8.5       35.0      4593.0      4594.0      215.0   \n", "          5512103       18.0       43.5      4592.5      4593.5      107.5   \n", "          5512103       36.5       49.0      4592.0      4593.0       16.0   \n", "          5512103       32.0       49.5      4592.0      4593.0       21.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512103      129.5       51.5      4598.0      4599.0       73.5   \n", "          5512103      126.5       52.5      4598.0      4599.0       75.5   \n", "          5512103      130.0       53.5      4598.0      4599.0       74.5   \n", "          5512103      134.5       55.0      4598.0      4599.0       50.5   \n", "          5512103       71.0       95.5      4599.0      4600.0      114.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512103       43.0  \n", "          5512103       43.0  \n", "          5512103      129.0  \n", "          5512103      213.0  \n", "          5512103      213.0  \n", "...                      ...  \n", "          5512103      113.0  \n", "          5512103      113.0  \n", "          5512103      113.0  \n", "          5512103      113.5  \n", "          5512103      550.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512104),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512104  1653631200  4598.5   66.5      4598.5      4597.5   \n", "          5512104  1653631201  4599.5   50.5      4599.0      4598.0   \n", "          5512104  1653631202  4599.5  135.5      4599.0      4598.0   \n", "          5512104  1653631203  4600.5  132.0      4600.5      4599.5   \n", "          5512104  1653631204  4601.5   27.0      4601.0      4600.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512104  1653631495  4605.5    2.0      4605.0      4604.0   \n", "          5512104  1653631496  4605.0    2.0      4605.0      4604.0   \n", "          5512104  1653631497  4605.0    0.5      4605.0      4604.0   \n", "          5512104  1653631498  4605.5   14.0      4605.5      4604.5   \n", "          5512104  1653631499  4606.0    3.0      4606.0      4605.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512104       20.5       80.5      4599.5      4600.5      332.5   \n", "          5512104       50.5       34.5      4600.0      4601.0      500.5   \n", "          5512104       39.0       35.0      4600.0      4601.0      273.5   \n", "          5512104       27.0       56.5      4601.5      4602.5       33.5   \n", "          5512104       45.0       49.0      4602.0      4603.0       21.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512104       13.0       74.0      4606.0      4607.0       21.0   \n", "          5512104       10.5       75.0      4606.0      4607.0       23.0   \n", "          5512104        7.5       75.0      4606.0      4607.0       25.0   \n", "          5512104        8.0       44.0      4606.5      4607.5      325.5   \n", "          5512104        5.0       14.0      4607.0      4608.0      626.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512104      288.0  \n", "          5512104       26.0  \n", "          5512104       26.0  \n", "          5512104       51.0  \n", "          5512104       34.0  \n", "...                      ...  \n", "          5512104      602.0  \n", "          5512104      602.0  \n", "          5512104      602.0  \n", "          5512104      329.0  \n", "          5512104       56.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512105),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512105  1653631500  4605.5   5.0      4605.0      4604.0   \n", "          5512105  1653631501  4605.5   3.0      4605.0      4604.0   \n", "          5512105  1653631502  4605.5   2.5      4605.0      4604.0   \n", "          5512105  1653631503  4605.5   4.5      4605.0      4604.0   \n", "          5512105  1653631504  4605.5   0.5      4605.0      4604.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512105  1653631795  4602.0   4.0      4601.0      4600.0   \n", "          5512105  1653631796  4601.5  39.0      4600.5      4599.5   \n", "          5512105  1653631797  4601.0  16.0      4600.0      4599.0   \n", "          5512105  1653631798  4600.5   7.0      4599.5      4598.5   \n", "          5512105  1653631799  4599.0   9.5      4599.0      4598.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512105       14.0       78.0      4606.0      4607.0       10.0   \n", "          5512105       21.0       80.0      4606.0      4607.0       17.0   \n", "          5512105       21.0       80.0      4606.0      4607.0       16.5   \n", "          5512105       33.0       80.0      4606.0      4607.0       18.5   \n", "          5512105       34.5       80.0      4606.0      4607.0       25.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512105       36.5       37.0      4603.0      4604.0       42.5   \n", "          5512105       37.5      206.5      4601.5      4603.0      172.5   \n", "          5512105       12.0      375.0      4601.0      4602.0      336.5   \n", "          5512105      193.5      199.5      4601.0      4602.0      332.5   \n", "          5512105      374.5       18.0      4600.5      4601.5      168.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512105      632.5  \n", "          5512105      626.0  \n", "          5512105      626.0  \n", "          5512105      631.5  \n", "          5512105      632.0  \n", "...                      ...  \n", "          5512105       43.0  \n", "          5512105       41.0  \n", "          5512105        3.0  \n", "          5512105        3.0  \n", "          5512105      168.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512106),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512106  1653631800  4599.5    4.0      4599.0      4598.0   \n", "          5512106  1653631801  4599.5  116.0      4599.0      4598.0   \n", "          5512106  1653631802  4598.0  108.5      4597.0      4596.0   \n", "          5512106  1653631803  4598.5   21.0      4598.0      4597.0   \n", "          5512106  1653631804  4598.5   12.0      4599.0      4598.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512106  1653632095  4590.0    8.0      4589.0      4588.0   \n", "          5512106  1653632096  4590.0    6.0      4589.5      4588.5   \n", "          5512106  1653632097  4590.0    7.5      4589.0      4588.0   \n", "          5512106  1653632098  4589.5    6.5      4589.5      4588.5   \n", "          5512106  1653632099  4589.0   11.5      4589.0      4588.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512106      373.5       18.0      4600.0      4601.0       37.5   \n", "          5512106      263.5       18.0      4600.0      4601.0       30.0   \n", "          5512106       23.5       16.5      4598.5      4599.5        2.0   \n", "          5512106       17.5       29.0      4599.5      4600.5       26.5   \n", "          5512106        1.0       34.0      4600.0      4601.0       34.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512106       15.0      103.5      4590.0      4591.0        1.5   \n", "          5512106       13.5       63.0      4590.5      4591.5       27.0   \n", "          5512106       22.0      102.0      4591.0      4592.0       48.5   \n", "          5512106       12.0       62.5      4591.0      4592.0       49.0   \n", "          5512106        9.5      100.0      4590.5      4591.5       26.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512106      334.0  \n", "          5512106      334.0  \n", "          5512106       38.0  \n", "          5512106      185.5  \n", "          5512106      333.0  \n", "...                      ...  \n", "          5512106       46.5  \n", "          5512106       54.5  \n", "          5512106       61.0  \n", "          5512106       61.0  \n", "          5512106       52.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512107),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512107  1653632100  4590.0  12.0      4589.0      4588.0   \n", "          5512107  1653632101  4591.0  47.0      4590.0      4589.0   \n", "          5512107  1653632102  4591.0  31.5      4590.5      4589.5   \n", "          5512107  1653632103  4591.0  13.5      4591.0      4590.0   \n", "          5512107  1653632104  4591.0  14.0      4591.0      4590.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512107  1653632395  4596.5   1.5      4595.5      4594.5   \n", "          5512107  1653632396  4596.0   0.0      4596.0      4595.0   \n", "          5512107  1653632397  4596.0   2.0      4595.0      4594.0   \n", "          5512107  1653632398  4596.5  36.0      4596.5      4595.0   \n", "          5512107  1653632399  4596.5  19.5      4596.0      4595.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512107       27.0      100.0      4590.5      4591.5       29.5   \n", "          5512107       25.0       90.0      4591.5      4592.5       25.0   \n", "          5512107       52.5       55.5      4592.0      4593.0       12.0   \n", "          5512107        6.5       99.0      4592.5      4593.5       13.5   \n", "          5512107        9.5      100.0      4593.0      4594.0       23.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512107       55.5       66.5      4596.5      4597.5       12.5   \n", "          5512107        3.0      112.0      4597.0      4598.0       24.5   \n", "          5512107      116.0       23.0      4597.0      4598.0       25.0   \n", "          5512107       17.5      116.0      4597.5      4598.5       53.5   \n", "          5512107        1.5      116.0      4597.5      4598.5       49.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512107       52.5  \n", "          5512107       41.0  \n", "          5512107       24.0  \n", "          5512107       31.5  \n", "          5512107       31.0  \n", "...                      ...  \n", "          5512107       48.0  \n", "          5512107       73.0  \n", "          5512107       74.0  \n", "          5512107       57.0  \n", "          5512107       55.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512108),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512108  1653632400  4596.0   1.0      4595.5      4594.5   \n", "          5512108  1653632401  4596.5   3.0      4595.0      4594.0   \n", "          5512108  1653632402  4595.0  65.5      4594.0      4593.0   \n", "          5512108  1653632403  4594.5  59.0      4594.5      4593.5   \n", "          5512108  1653632404  4594.0  12.5      4594.0      4593.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512108  1653632695  4595.0   4.0      4595.0      4594.0   \n", "          5512108  1653632696  4595.0   1.0      4595.0      4594.0   \n", "          5512108  1653632697  4596.0   1.0      4595.0      4594.0   \n", "          5512108  1653632698  4595.0   1.5      4595.0      4594.0   \n", "          5512108  1653632699  4595.0  19.5      4594.5      4593.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512108       60.0       75.0      4597.0      4598.0       51.5   \n", "          5512108      119.0       35.0      4596.5      4597.5       35.0   \n", "          5512108       50.5       22.5      4595.0      4596.0        8.0   \n", "          5512108       48.5       46.5      4596.0      4597.0       11.0   \n", "          5512108       20.0       24.0      4595.0      4596.0       24.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512108       42.0       36.0      4596.0      4597.0       13.0   \n", "          5512108       42.0       37.0      4596.0      4597.0       13.0   \n", "          5512108       42.0       37.0      4596.0      4597.0       12.0   \n", "          5512108       40.0       38.5      4596.0      4597.0       11.0   \n", "          5512108       39.5       41.5      4596.0      4597.0       13.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512108       72.0  \n", "          5512108       70.0  \n", "          5512108        9.0  \n", "          5512108       66.0  \n", "          5512108       48.5  \n", "...                      ...  \n", "          5512108       18.0  \n", "          5512108       18.0  \n", "          5512108       18.0  \n", "          5512108       18.0  \n", "          5512108       18.0  \n", "\n", "[299 rows x 11 columns])\n", "(('BU8888.SC', 5512109),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512109  1653632700  4595.0   4.5      4594.0      4593.0   \n", "          5512109  1653632701  4595.0   3.0      4594.0      4593.0   \n", "          5512109  1653632702  4594.0   0.5      4594.5      4593.5   \n", "          5512109  1653632703  4594.0   0.0      4595.0      4594.0   \n", "          5512109  1653632704  4595.0   8.0      4595.0      4594.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512109  1653632995  4606.0   1.0      4605.0      4604.0   \n", "          5512109  1653632996  4606.0  13.0      4604.0      4603.0   \n", "          5512109  1653632997  4604.5   7.5      4604.0      4603.0   \n", "          5512109  1653632998  4604.5   6.5      4604.0      4603.0   \n", "          5512109  1653632999  4604.0  26.5      4603.5      4602.5   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512109       57.0       44.0      4595.5      4596.5        8.5   \n", "          5512109       55.0       47.0      4596.0      4597.0       12.5   \n", "          5512109       27.5       50.5      4596.0      4597.0       11.0   \n", "          5512109        5.5       50.0      4596.0      4597.0       15.0   \n", "          5512109        2.0       52.0      4596.0      4597.0       16.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512109       18.0       41.5      4606.0      4607.0        9.0   \n", "          5512109       41.0       63.5      4605.5      4606.5        8.0   \n", "          5512109       40.5       64.0      4605.0      4606.0       17.5   \n", "          5512109       36.5       64.0      4605.0      4606.0       11.5   \n", "          5512109       33.5      177.5      4605.0      4606.0       12.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512109       19.5  \n", "          5512109       25.0  \n", "          5512109       24.5  \n", "          5512109       24.0  \n", "          5512109       24.0  \n", "...                      ...  \n", "          5512109       67.0  \n", "          5512109       43.0  \n", "          5512109       23.5  \n", "          5512109       26.0  \n", "          5512109       28.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512110),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512110  1653633000  4603.5    1.5      4603.0      4602.0   \n", "          5512110  1653633001  4604.0    4.0      4603.0      4602.0   \n", "          5512110  1653633002  4603.5    3.5      4603.0      4602.0   \n", "          5512110  1653633003  4604.0    6.0      4603.0      4602.0   \n", "          5512110  1653633004  4603.0   15.0      4603.0      4602.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512110  1653633295  4609.0    1.0      4607.0      4606.0   \n", "          5512110  1653633296  4608.5    3.0      4607.0      4606.0   \n", "          5512110  1653633297  4608.0   15.5      4607.0      4606.0   \n", "          5512110  1653633298  4609.0  155.5      4607.5      4606.5   \n", "          5512110  1653633299  4610.0   39.0      4609.0      4608.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512110       64.5      291.0      4604.0      4605.0        9.0   \n", "          5512110       63.0      292.0      4604.0      4605.0        9.0   \n", "          5512110       63.0      297.0      4604.0      4605.0        4.5   \n", "          5512110       63.5      297.0      4604.5      4605.5       14.0   \n", "          5512110       32.5      297.0      4604.5      4605.5       19.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512110       35.0       53.0      4609.0      4610.0       40.0   \n", "          5512110       35.0       53.0      4609.0      4610.0       38.5   \n", "          5512110       20.0       53.0      4608.5      4609.5       23.0   \n", "          5512110       12.5       32.5      4609.0      4610.0      217.0   \n", "          5512110       18.0       33.0      4610.0      4611.0      400.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512110       23.0  \n", "          5512110       23.0  \n", "          5512110       23.0  \n", "          5512110       28.0  \n", "          5512110       28.0  \n", "...                      ...  \n", "          5512110      645.0  \n", "          5512110      648.0  \n", "          5512110      343.0  \n", "          5512110       55.5  \n", "          5512110       69.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512111),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512111  1653633300  4610.0   8.0      4609.0      4608.0   \n", "          5512111  1653633301  4609.5  32.0      4608.5      4607.5   \n", "          5512111  1653633302  4608.5   8.0      4608.0      4607.0   \n", "          5512111  1653633303  4609.0   9.5      4608.0      4607.0   \n", "          5512111  1653633304  4609.0   5.0      4609.0      4608.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512111  1653633595  4606.5   2.5      4606.0      4605.0   \n", "          5512111  1653633596  4607.0   0.0      4606.0      4605.0   \n", "          5512111  1653633597  4605.5  19.0      4605.5      4604.5   \n", "          5512111  1653633598  4605.5  13.5      4605.5      4604.5   \n", "          5512111  1653633599  4607.0   5.0      4606.0      4605.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512111       19.0       32.0      4610.0      4611.0      388.0   \n", "          5512111       23.5       30.5      4609.5      4610.5      183.5   \n", "          5512111       19.5       26.0      4609.0      4610.0       22.0   \n", "          5512111       12.0       28.0      4609.0      4610.0        7.5   \n", "          5512111        7.0       14.0      4610.0      4611.0      337.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512111       19.5       22.0      4607.5      4608.5       19.5   \n", "          5512111       24.0       29.5      4607.5      4608.5       11.5   \n", "          5512111       22.0       36.5      4606.5      4607.5        4.5   \n", "          5512111       10.0       28.0      4606.5      4607.5        6.0   \n", "          5512111        8.5       18.0      4607.5      4608.5       17.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512111       68.0  \n", "          5512111      205.5  \n", "          5512111      340.0  \n", "          5512111      334.0  \n", "          5512111       68.0  \n", "...                      ...  \n", "          5512111       45.0  \n", "          5512111       48.0  \n", "          5512111       18.5  \n", "          5512111       18.5  \n", "          5512111       46.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512112),                      datetime   price   size  bid_price1  bid_price2  \\\n", "label     time_id                                                      \n", "BU8888.SC 5512112  1653633600  4606.5    2.0      4606.0      4605.0   \n", "          5512112  1653633601  4606.5    2.5      4606.0      4605.0   \n", "          5512112  1653633602  4606.5    2.5      4606.5      4605.5   \n", "          5512112  1653633603  4607.0    0.5      4606.0      4605.0   \n", "          5512112  1653633604  4607.0    1.0      4606.0      4605.0   \n", "...                       ...     ...    ...         ...         ...   \n", "          5512112  1653633895  4600.0  110.0      4599.0      4598.0   \n", "          5512112  1653633896  4599.0    2.5      4599.0      4598.0   \n", "          5512112  1653633897  4599.0   13.5      4599.0      4598.0   \n", "          5512112  1653633898  4598.0   30.0      4598.0      4597.0   \n", "          5512112  1653633899  4599.0    9.5      4598.0      4597.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512112       10.5       19.0      4607.0      4608.0        6.0   \n", "          5512112       16.0       19.5      4607.0      4608.0        5.0   \n", "          5512112       11.5       23.5      4608.0      4609.0       39.0   \n", "          5512112       26.0       23.0      4608.0      4609.0       39.0   \n", "          5512112       27.0       24.0      4607.0      4608.0        2.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512112       33.0       87.5      4600.0      4602.0      135.0   \n", "          5512112       28.0       83.0      4600.0      4602.0      191.5   \n", "          5512112       25.5       78.0      4600.0      4602.0      172.0   \n", "          5512112       77.0       27.0      4599.0      4600.0      267.0   \n", "          5512112       56.5       24.0      4599.0      4600.0      271.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512112       43.5  \n", "          5512112       40.0  \n", "          5512112       63.0  \n", "          5512112       63.0  \n", "          5512112       36.5  \n", "...                      ...  \n", "          5512112       22.0  \n", "          5512112       22.0  \n", "          5512112       22.0  \n", "          5512112      172.0  \n", "          5512112      121.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512113),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512113  1653633900  4599.0  10.0      4598.0      4597.0   \n", "          5512113  1653633901  4599.0  23.5      4598.0      4597.0   \n", "          5512113  1653633902  4598.0   1.0      4598.0      4597.0   \n", "          5512113  1653633903  4598.5  16.5      4598.0      4597.0   \n", "          5512113  1653633904  4597.5  35.0      4597.0      4596.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512113  1653634195  4611.5   1.5      4611.0      4610.0   \n", "          5512113  1653634196  4611.0   3.5      4611.0      4610.0   \n", "          5512113  1653634197  4611.5  11.0      4611.0      4610.0   \n", "          5512113  1653634198  4612.0   1.5      4611.0      4610.0   \n", "          5512113  1653634199  4611.5   6.5      4611.0      4610.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512113       67.0       24.0      4599.0      4600.0      335.5   \n", "          5512113       66.0       24.0      4599.0      4600.0      302.0   \n", "          5512113       65.0       24.0      4599.0      4600.0      327.5   \n", "          5512113       57.5       24.0      4599.0      4600.0      310.5   \n", "          5512113       23.5      721.0      4598.0      4599.0      259.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512113       14.0       49.0      4612.0      4613.0       30.5   \n", "          5512113        8.5       50.5      4612.0      4613.0       38.0   \n", "          5512113        6.5       52.0      4612.0      4613.0       40.5   \n", "          5512113        9.0       52.0      4612.0      4613.0       38.5   \n", "          5512113        5.5       48.5      4612.0      4613.0       36.5   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512113       84.0  \n", "          5512113       85.5  \n", "          5512113       86.5  \n", "          5512113       87.5  \n", "          5512113      309.0  \n", "...                      ...  \n", "          5512113      191.5  \n", "          5512113      192.0  \n", "          5512113      191.5  \n", "          5512113      191.0  \n", "          5512113      190.0  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512114),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512114  1653634200  4610.0  30.0      4609.5      4608.5   \n", "          5512114  1653634201  4610.5  57.5      4609.5      4608.5   \n", "          5512114  1653634202  4609.5  12.0      4609.0      4608.0   \n", "          5512114  1653634203  4610.0   8.0      4609.0      4608.0   \n", "          5512114  1653634204  4609.5   1.0      4609.0      4608.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512114  1653634495  4613.0  20.0      4612.5      4611.5   \n", "          5512114  1653634496  4613.5  32.0      4612.0      4611.0   \n", "          5512114  1653634497  4613.5   6.0      4613.0      4612.0   \n", "          5512114  1653634498  4612.0  51.0      4611.0      4610.0   \n", "          5512114  1653634499  4612.0  51.0      4611.0      4610.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512114       37.5      102.0      4611.0      4612.0      142.0   \n", "          5512114        7.5      109.5      4611.0      4612.0       61.0   \n", "          5512114       35.0      172.0      4610.0      4611.0        6.0   \n", "          5512114       20.0      172.0      4610.0      4611.0       21.5   \n", "          5512114       22.0      171.0      4610.0      4611.0       20.0   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512114       23.0       64.0      4613.5      4614.5      139.5   \n", "          5512114       25.5       97.5      4613.5      4614.5      115.5   \n", "          5512114       15.5       65.5      4614.0      4615.0      222.5   \n", "          5512114       96.0       68.0      4612.0      4613.0      212.5   \n", "          5512114       97.5       68.0      4612.0      4613.0      168.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512114       42.0  \n", "          5512114       48.0  \n", "          5512114       58.0  \n", "          5512114       54.0  \n", "          5512114       53.5  \n", "...                      ...  \n", "          5512114      173.0  \n", "          5512114      169.5  \n", "          5512114       60.0  \n", "          5512114        9.0  \n", "          5512114       12.5  \n", "\n", "[300 rows x 11 columns])\n", "(('BU8888.SC', 5512115),                      datetime   price  size  bid_price1  bid_price2  \\\n", "label     time_id                                                     \n", "BU8888.SC 5512115  1653634500  4612.0  59.0      4611.0      4610.0   \n", "          5512115  1653634501  4612.0  29.0      4612.0      4611.0   \n", "          5512115  1653634502  4612.0  20.5      4611.0      4610.0   \n", "          5512115  1653634503  4612.5  13.5      4612.0      4611.0   \n", "          5512115  1653634504  4613.0   3.0      4612.0      4611.0   \n", "...                       ...     ...   ...         ...         ...   \n", "          5512115  1653634795  4606.5  37.0      4606.0      4604.5   \n", "          5512115  1653634796  4605.5  15.0      4605.0      4604.0   \n", "          5512115  1653634797  4605.5  36.5      4605.0      4604.0   \n", "          5512115  1653634798  4606.5  16.5      4606.0      4604.5   \n", "          5512115  1653634799  4606.0   9.0      4606.0      4605.0   \n", "\n", "                   bid_size1  bid_size2  ask_price1  ask_price2  ask_size1  \\\n", "label     time_id                                                            \n", "BU8888.SC 5512115      107.0       71.0      4612.0      4613.0       48.0   \n", "          5512115       28.0      110.0      4613.0      4614.0       15.0   \n", "          5512115      110.0       70.5      4612.0      4613.0       19.0   \n", "          5512115       13.0      115.0      4613.0      4614.0       16.5   \n", "          5512115       22.5      124.0      4613.0      4614.0       12.5   \n", "...                      ...        ...         ...         ...        ...   \n", "          5512115       27.0       55.0      4607.0      4608.5       65.5   \n", "          5512115       23.5       87.0      4606.0      4607.0       66.5   \n", "          5512115        5.5       86.5      4606.0      4607.0       18.0   \n", "          5512115       11.0       43.5      4607.0      4608.0       51.5   \n", "          5512115        8.0        2.0      4607.0      4608.0       57.0   \n", "\n", "                   ask_size2  \n", "label     time_id             \n", "BU8888.SC 5512115       15.5  \n", "          5512115      218.0  \n", "          5512115       10.0  \n", "          5512115      218.0  \n", "          5512115      218.0  \n", "...                      ...  \n", "          5512115       29.0  \n", "          5512115        1.0  \n", "          5512115        1.0  \n", "          5512115       47.0  \n", "          5512115       47.0  \n", "\n", "[300 rows x 11 columns])\n"]}], "source": ["for item in df.groupby(['label', 'time_id']):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}, "kernelspec": {"display_name": "Python 3.8.10 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}