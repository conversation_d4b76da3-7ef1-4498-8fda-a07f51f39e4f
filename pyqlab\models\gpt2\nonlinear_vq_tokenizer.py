"""
NonlinearVQTokenizer

结合非线性映射和VQ-VAE的K线数据tokenizer
兼具NonlinearCandlestickTokenizer的灵活性和VQ-VAE的表示能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import pickle
from typing import List, Dict, Tuple, Optional, Union, Any, Callable
from datetime import datetime
import talib
import warnings
from pyqlab.models.gpt2.utils import load_single_data

# 导入基础模块
from pyqlab.models.gpt2.vq_tokenizer import VQEmbedding
from pyqlab.models.gpt2.nonlinear_tokenizer import (
    NonlinearMappingFunction, LinearMapping, LogarithmicMapping,
    ExponentialMapping, SigmoidMapping, SquareRootMapping
)


class NonlinearVQTokenizer:
    """
    结合非线性映射和VQ-VAE的K线数据tokenizer

    特点:
    1. 使用非线性映射预处理K线特征
    2. 使用VQ-VAE进行向量量化
    3. 支持丰富的特殊token和异常检测
    4. 提供高保真度的K线还原功能
    """
    # 特殊标记常量
    PAD_TOKEN = '<PAD>'          # 填充标记
    UNK_TOKEN = '<UNK>'          # 未知标记
    ANOMALY_TOKEN = '<ANOMALY>'  # 异常数据标记
    DAY_GAP_TOKEN = '<DAY_GAP>'  # 交易日间隔标记
    HOLIDAY_TOKEN = '<HOLIDAY>'  # 假期间隔标记

    def __init__(self,
                 codebook_weights_path=None,
                 num_embeddings=512,
                 embedding_dim=5,
                 atr_window=100,
                 atr_mult=0.88,
                 ma_volume_period=20,
                 mapping_functions=None,
                 special_tokens=True,
                 include_volume=False,
                 enable_anomaly_detection=False,
                 anomaly_threshold=7.0,
                 verbose=False,
                 cache_dir=None):
        """
        初始化NonlinearVQTokenizer

        Args:
            codebook_weights_path: 预训练的VQ-VAE码本权重文件路径
            num_embeddings: 码本中的码向量数量 (词汇表大小)
            embedding_dim: K线向量的维度，也是码向量的维度
            atr_window: 计算ATR的周期
            atr_mult: ATR乘数
            ma_volume_period: 计算移动平均成交量的周期
            mapping_functions: 特征到映射函数的字典，可用的特征有'change', 'entity', 'upline', 'downline', 'volume'
            special_tokens: 是否使用特殊token
            include_volume: 是否包含交易量特征
            enable_anomaly_detection: 是否检测和处理异常值
            anomaly_threshold: 异常检测阈值
            verbose: 是否输出详细信息
            cache_dir: 缓存目录
        """
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.atr_window = atr_window
        self.atr_mult = atr_mult
        self.ma_volume_period = ma_volume_period
        self.special_tokens = special_tokens
        self.include_volume = include_volume
        self.enable_anomaly_detection = enable_anomaly_detection
        self.anomaly_threshold = anomaly_threshold
        self.verbose = verbose
        self.cache_dir = cache_dir

        # 初始化VQ层
        self.vq_layer = VQEmbedding(num_embeddings, embedding_dim)

        # 加载预训练的码本权重
        if codebook_weights_path:
            try:
                state_dict = torch.load(codebook_weights_path)
                # 检查state_dict的类型
                if isinstance(state_dict, dict) and 'weight' in state_dict:
                    # 如果保存的是整个embedding层的state_dict
                    self.vq_layer.embedding.load_state_dict(state_dict)
                elif isinstance(state_dict, torch.Tensor):
                    # 如果直接保存的是权重张量
                    self.vq_layer.embedding.weight.data.copy_(state_dict)
                else:
                    # 如果是其他格式，尝试直接使用
                    self.vq_layer.embedding.weight.data.copy_(state_dict)
                print(f"已加载码本权重: {codebook_weights_path}")
            except Exception as e:
                print(f"警告: 无法加载码本权重 {codebook_weights_path}. 使用随机初始化. 错误: {e}")
        else:
            print("警告: 未提供码本权重路径. 使用随机初始化码本.")

        self.vq_layer.eval()  # 设置为评估模式

        # 初始化映射函数
        self.mapping_functions = mapping_functions or {}

        # 打印映射函数信息
        for feature, mapping_func in self.mapping_functions.items():
            print(f"使用 {mapping_func.__class__.__name__} 映射 {feature} 特征")

        # 特殊tokens
        self.special_tokens_list = [self.PAD_TOKEN, self.UNK_TOKEN, self.ANOMALY_TOKEN,
                                   self.DAY_GAP_TOKEN, self.HOLIDAY_TOKEN]
        self.vocab_size = self.num_embeddings + len(self.special_tokens_list)

        # 建立token到ID的映射
        self.token_to_id_map = {}
        self.id_to_token_map = {}

        # 码本中的token (ID从0到num_embeddings-1)
        for i in range(self.num_embeddings):
            token_name = f"CODE_{i}"
            self.token_to_id_map[token_name] = i
            self.id_to_token_map[i] = token_name

        # 特殊token的ID
        offset = self.num_embeddings
        for i, token in enumerate(self.special_tokens_list):
            self.token_to_id_map[token] = offset + i
            self.id_to_token_map[offset + i] = token

        # 未知token的ID
        self.unk_token_id = self.token_to_id_map[self.UNK_TOKEN]

        print(f"NonlinearVQTokenizer 初始化完成，词汇表大小: {self.vocab_size}")

    def _create_default_mapping_function(self, feature, data):
        """为特定特征创建默认的映射函数"""
        # 计算数据范围，排除异常值
        q1, q3 = np.percentile(data, [1, 99])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        # 限制数据范围
        filtered_data = data[(data >= lower_bound) & (data <= upper_bound)]

        # 如果数据太少，使用原始数据
        if len(filtered_data) < 10:
            filtered_data = data

        input_range = (np.min(filtered_data), np.max(filtered_data))

        # 根据特征类型选择合适的映射函数
        if feature == 'change':
            # 价格变化通常有正负值，使用S形映射
            return SigmoidMapping(input_range, (-1, 1))
        elif feature == 'entity':
            # 实体也有正负值，使用S形映射
            return SigmoidMapping(input_range, (-1, 1))
        elif feature in ['upline', 'downline']:
            # 影线通常是非负值，使用平方根映射
            if input_range[0] < 0:
                input_range = (0, input_range[1])
            return SquareRootMapping(input_range, (0, 1))
        elif feature == 'volume':
            # 交易量变化可能有较大的异常值，使用对数映射
            if input_range[0] <= 0:
                # 如果有负值或零，进行偏移
                offset = abs(input_range[0]) + 1e-6
                input_range = (offset, input_range[1] + offset)
            return LogarithmicMapping(input_range, (-1, 1))
        else:
            # 默认使用线性映射
            return LinearMapping(input_range, (-1, 1))

    def _calculate_atr(self, df_ohlcv, window=None):
        """计算ATR (Average True Range)"""
        if window is None:
            window = self.atr_window

        df = df_ohlcv.copy()

        # 确保列名小写
        df.columns = [col.lower() for col in df.columns]

        # 计算TR (True Range)
        df['h-l'] = df['high'] - df['low']
        df['h-pc'] = abs(df['high'] - df['close'].shift(1))
        df['l-pc'] = abs(df['low'] - df['close'].shift(1))
        df['tr'] = df[['h-l', 'h-pc', 'l-pc']].max(axis=1)

        # 计算ATR
        atr = df['tr'].rolling(window=window, min_periods=1).mean()

        # 填充NaN值
        atr = atr.bfill().fillna(df['close'].std())

        return atr

    def _preprocess_df(self, df_ohlcv):
        """辅助函数，计算ATR, MA_Volume, Prev_Close"""
        df = df_ohlcv.copy()

        # 确保列名小写
        df.columns = [col.lower() for col in df.columns]

        # 计算ATR
        df['atr'] = self._calculate_atr(df)

        # MA Volume
        df['ma_volume'] = df['volume'].rolling(window=self.ma_volume_period, min_periods=1).mean()

        # Prev Close
        df['prev_close'] = df['close'].shift(1)

        # 填充NaN值
        df['ma_volume'] = df['ma_volume'].bfill().fillna(df['volume'].mean())
        df['prev_close'] = df['prev_close'].bfill().fillna(df['close'].iloc[0])

        return df

    def detect_anomalies(self, df):
        """检测K线数据中的异常值"""
        if not self.enable_anomaly_detection:
            return df

        df_copy = df.copy()

        # 计算价格变化率
        df_copy['price_change'] = df_copy['close'].pct_change()

        # 计算滚动均值和标准差
        window = min(self.atr_window, len(df_copy) // 2)
        window = max(window, 5)  # 最小窗口大小

        df_copy['rolling_mean'] = df_copy['price_change'].rolling(window=window).mean()
        df_copy['rolling_std'] = df_copy['price_change'].rolling(window=window).std()

        # 计算z分数
        df_copy['z_score'] = (df_copy['price_change'] - df_copy['rolling_mean']) / df_copy['rolling_std']

        # 标记异常
        df_copy['is_price_anomaly'] = abs(df_copy['z_score']) > self.anomaly_threshold

        # 如果有交易量数据，也检测交易量异常
        if 'volume' in df_copy.columns:
            df_copy['volume_change'] = df_copy['volume'].pct_change()
            df_copy['volume_rolling_mean'] = df_copy['volume_change'].rolling(window=window).mean()
            df_copy['volume_rolling_std'] = df_copy['volume_change'].rolling(window=window).std()
            df_copy['volume_z_score'] = (df_copy['volume_change'] - df_copy['volume_rolling_mean']) / df_copy['volume_rolling_std']
            df_copy['is_volume_anomaly'] = abs(df_copy['volume_z_score']) > self.anomaly_threshold

            # 组合异常标记
            df_copy['is_anomaly'] = df_copy['is_price_anomaly'] | df_copy['is_volume_anomaly']
        else:
            df_copy['is_anomaly'] = df_copy['is_price_anomaly']

        # 填充NaN值
        df_copy['is_anomaly'] = df_copy['is_anomaly'].bfill().fillna(False)

        # 打印异常统计
        anomaly_count = df_copy['is_anomaly'].sum()
        if anomaly_count > 0 and self.verbose:
            print(f"检测到 {anomaly_count} 个异常值 ({anomaly_count/len(df_copy):.2%})")

        return df_copy

    def correct_anomalies(self, df):
        """修正K线数据中的异常值"""
        if not self.enable_anomaly_detection or 'is_anomaly' not in df.columns:
            return df

        df_copy = df.copy()
        anomaly_mask = df_copy['is_anomaly']

        if anomaly_mask.sum() == 0:
            return df_copy

        # 使用插值法修正异常值
        for col in ['open', 'high', 'low', 'close']:
            if col in df_copy.columns:
                df_copy.loc[anomaly_mask, col] = np.nan
                df_copy[col] = df_copy[col].interpolate(method='linear')

        if 'volume' in df_copy.columns:
            df_copy.loc[anomaly_mask, 'volume'] = np.nan
            df_copy['volume'] = df_copy['volume'].interpolate(method='linear')

        # 确保OHLC关系保持一致
        if all(col in df_copy.columns for col in ['open', 'high', 'low', 'close']):
            df_copy['high'] = df_copy[['high', 'open', 'close']].max(axis=1)
            df_copy['low'] = df_copy[['low', 'open', 'close']].min(axis=1)

        return df_copy

    def candlestick_to_vector(self, ohlcv_row, prev_close, ma_volume, atr_val):
        """
        将单根K线转换为数值向量

        Args:
            ohlcv_row: 包含OHLCV数据的Series
            prev_close: 前一根K线的收盘价
            ma_volume: 成交量移动平均
            atr_val: ATR值

        Returns:
            包含K线特征的向量
        """
        # 确保列名小写
        ohlcv_row = {k.lower(): v for k, v in ohlcv_row.items()} if isinstance(ohlcv_row, dict) else ohlcv_row

        # 提取OHLCV值
        o = ohlcv_row['open'] if 'open' in ohlcv_row else ohlcv_row.open
        h = ohlcv_row['high'] if 'high' in ohlcv_row else ohlcv_row.high
        l = ohlcv_row['low'] if 'low' in ohlcv_row else ohlcv_row.low
        c = ohlcv_row['close'] if 'close' in ohlcv_row else ohlcv_row.close
        v = ohlcv_row['volume'] if 'volume' in ohlcv_row else (ohlcv_row.volume if hasattr(ohlcv_row, 'volume') else None)

        # 检查数据有效性
        if prev_close is None or ma_volume is None or atr_val is None or atr_val == 0:
            # 返回零向量
            print(f"警告: 缺少必要的数据用于向量化: prev_close={prev_close}, ma_volume={ma_volume}, atr_val={atr_val}")
            return np.zeros(self.embedding_dim)

        # 计算原始特征
        raw_features = {}
        raw_features['change'] = (c - prev_close) / atr_val
        raw_features['entity'] = (c - o) / atr_val
        raw_features['upline'] = (h - max(o, c)) / atr_val
        raw_features['downline'] = (min(o, c) - l) / atr_val

        if raw_features['change'] == 0.:
            print(f"警告: {o} {h} {l} {c} - {prev_close} - {atr_val}")
            print(raw_features)

        if self.include_volume and v is not None and ma_volume != 0:
            raw_features['volume'] = v / ma_volume

        # 应用非线性映射
        mapped_features = {}
        for feature, value in raw_features.items():
            if feature in self.mapping_functions:
                # 使用预定义的映射函数
                mapped_features[feature] = self.mapping_functions[feature](value)
            else:
                # 使用默认线性映射
                if feature == 'volume':
                    # 交易量映射到[-1, 1]
                    mapped_features[feature] = max(-1, min(1, value - 1))
                else:
                    # 其他特征使用原始值
                    mapped_features[feature] = value

        # 构建向量
        if self.include_volume:
            vec = [
                mapped_features['change'],
                mapped_features['entity'],
                mapped_features['upline'],
                mapped_features['downline'],
                mapped_features['volume']
            ]
        else:
            vec = [
                mapped_features['change'],
                mapped_features['entity'],
                mapped_features['upline'],
                mapped_features['downline']
            ]

        # 确保向量维度与embedding_dim匹配
        if len(vec) != self.embedding_dim:
            # 如果维度不匹配，进行填充或截断
            if len(vec) < self.embedding_dim:
                # 填充
                vec.extend([0] * (self.embedding_dim - len(vec)))
            else:
                # 截断
                vec = vec[:self.embedding_dim]
            print(f"警告: 向量维度不匹配，已进行填充或截断: {len(vec)} -> {self.embedding_dim}")

        return np.array(vec, dtype=np.float32)

    def tokenize_single_candlestick(self, ohlcv_row, prev_close, ma_volume, atr_val):
        """
        将单个K线数据点转换为token ID

        Args:
            ohlcv_row: 包含OHLCV数据的Series
            prev_close: 前一根K线的收盘价
            ma_volume: 成交量移动平均
            atr_val: ATR值

        Returns:
            token ID
        """
        # 转换为向量
        vector = self.candlestick_to_vector(ohlcv_row, prev_close, ma_volume, atr_val)

        # 检查是否为零向量
        if np.all(vector == 0):
            print(f"警告: 向量包含NaN或无穷值: {vector}，使用UNK标记")
            return self.unk_token_id

        # 使用VQ层量化
        vector_tensor = torch.tensor(vector, dtype=torch.float32)
        with torch.no_grad():
            token_id_tensor = self.vq_layer(vector_tensor)

        return token_id_tensor.item()

    def tokenize(self, df):
        """
        将K线数据序列转换为token ID序列

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            token ID列表
        """
        # 检查数据有效性
        if df.empty:
            print("警告: 输入数据为空")
            return []

        # 确保列名小写
        df.columns = [col.lower() for col in df.columns]

        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            print("警告: 输入数据缺少必要的OHLC列")
            return []

        # 预处理数据
        processed_df = self._preprocess_df(df)
        print(f"预处理后数据形状: {processed_df.shape}")

        # 检测并修正异常值
        if self.enable_anomaly_detection:
            processed_df = self.detect_anomalies(processed_df)
            processed_df = self.correct_anomalies(processed_df)
            print(f"异常检测后数据形状: {processed_df.shape}")

        # 检测交易日间隔
        # special_date_markers = np.zeros(len(processed_df), dtype=object)

        # if 'datetime' in processed_df.columns:
        #     dt = pd.to_datetime(processed_df['datetime'])
        #     date_diff = dt.dt.date.diff().dt.days

        #     # 标记交易日间隔和假期
        #     for i in range(1, len(processed_df)):
        #         if date_diff.iloc[i] > 3:  # 假期间隔
        #             special_date_markers[i] = self.HOLIDAY_TOKEN
        #         elif date_diff.iloc[i] > 1:  # 交易日间隔
        #             special_date_markers[i] = self.DAY_GAP_TOKEN

        # 标记异常值
        # if self.enable_anomaly_detection and 'is_anomaly' in processed_df.columns:
        #     anomaly_indices = processed_df.index[processed_df['is_anomaly']]
        #     for idx in anomaly_indices:
        #         idx_loc = processed_df.index.get_loc(idx)
        #         special_date_markers[idx_loc] = self.ANOMALY_TOKEN

        # 生成token序列
        token_ids = []

        for i in range(len(processed_df)):
            # 检查是否有特殊标记
            # if special_date_markers[i]:
            #     # 使用特殊标记
            #     token_ids.append(self.token_to_id_map[special_date_markers[i]])
            #     continue

            # 检查是否有足够的历史数据
            row = processed_df.iloc[i]
            if pd.isna(row['atr']) or pd.isna(row['ma_volume']) or pd.isna(row['prev_close']):
                # 使用PAD_TOKEN
                print(f"警告: 缺少必要的数据用于向量化: atr={row['atr']}, ma_volume={row['ma_volume']}, prev_close={row['prev_close']}")
                token_ids.append(self.token_to_id_map[self.PAD_TOKEN])
                continue

            # 生成token
            token_id = self.tokenize_single_candlestick(
                row,
                row['prev_close'],
                row['ma_volume'],
                row['atr']
            )
            token_ids.append(token_id)

        return token_ids

    def decode_id(self, token_id):
        """将token ID转换为符号表示"""
        if token_id not in self.id_to_token_map:
            return self.UNK_TOKEN
        return self.id_to_token_map[token_id]

    def decode_sequence(self, token_ids):
        """将token ID序列转换为符号表示序列"""
        return [self.decode_id(tid) for tid in token_ids]

    def get_quantized_vector(self, token_id):
        """
        获取token ID对应的量化向量

        Args:
            token_id: token ID

        Returns:
            量化向量，如果token ID无效则返回None
        """
        if token_id < 0 or token_id >= self.num_embeddings:
            if self.verbose:
                print(f"警告: Token ID {token_id} 超出码本范围 [0, {self.num_embeddings-1}]")
            return None

        with torch.no_grad():
            quantized_vec = self.vq_layer.quantize(torch.tensor([token_id]))

        return quantized_vec.squeeze().numpy()

    def tokens_to_candlesticks(self, tokens, start_price, atr, start_volume=None):
        """
        将token序列转换回K线数据

        Args:
            tokens: token ID列表
            start_price: 起始价格
            atr: ATR值
            start_volume: 起始交易量（可选）

        Returns:
            包含OHLCV数据的DataFrame
        """
        if not tokens:
            print("警告: 输入tokens为空")
            columns = ['open', 'high', 'low', 'close']
            if self.include_volume:
                columns.append('volume')
            return pd.DataFrame(columns=columns)

        # 过滤无效token
        valid_tokens = []
        for token in tokens:
            if token >= self.num_embeddings:  # 特殊token
                if token in self.id_to_token_map:
                    token_name = self.id_to_token_map[token]
                    if token_name in [self.PAD_TOKEN, self.UNK_TOKEN, self.ANOMALY_TOKEN,
                                     self.DAY_GAP_TOKEN, self.HOLIDAY_TOKEN]:
                        # 跳过特殊token
                        continue
            valid_tokens.append(token)
        print(f"有效token数量: {len(valid_tokens)} / {len(tokens)}")

        if not valid_tokens:
            print("警告: 过滤后没有有效token")
            columns = ['open', 'high', 'low', 'close']
            if self.include_volume:
                columns.append('volume')
            return pd.DataFrame(columns=columns)

        # 确保atr有效
        if atr <= 0 or not np.isfinite(atr):
            print(f"警告: ATR值无效 ({atr})，使用默认值0.01")
            atr = 0.01

        # 确保start_price有效
        if not np.isfinite(start_price) or start_price <= 0:
            print(f"警告: 起始价格无效 ({start_price})，使用默认值100")
            start_price = 100.0

        # 创建OHLC数据
        ohlc = pd.DataFrame(index=range(len(valid_tokens)))

        # 获取量化向量并还原K线
        prices = [start_price]
        opens = []
        highs = []
        lows = []
        volumes = [] if self.include_volume else None

        for i, token_id in enumerate(valid_tokens):
            if token_id < self.num_embeddings:  # 码本token
                # 获取量化向量
                quantized_vec = self.get_quantized_vector(token_id)

                if quantized_vec is None:
                    # 使用前一个价格
                    curr_price = prices[-1]
                    opens.append(curr_price * 0.99)
                    highs.append(curr_price * 1.01)
                    lows.append(curr_price * 0.99)
                    if self.include_volume:
                        volumes.append(start_volume if i == 0 else volumes[-1])
                else:
                    # 从量化向量中提取特征
                    if self.include_volume and len(quantized_vec) >= 5:
                        change, entity, upline, downline, volume_change = quantized_vec[:5]
                    else:
                        change, entity, upline, downline = quantized_vec[:4]
                        volume_change = 0

                    # 反向应用非线性映射
                    if 'change' in self.mapping_functions:
                        # 尝试反向映射，如果失败则使用原始值
                        try:
                            raw_change = self.mapping_functions['change'].inverse(change)
                        except:
                            raw_change = change
                    else:
                        raw_change = change

                    # 计算价格变动
                    price_change = raw_change * atr
                    curr_price = prices[-1] + price_change

                    # 确保价格为正
                    curr_price = max(0.001, curr_price)

                    # 计算开盘价
                    if 'entity' in self.mapping_functions:
                        try:
                            raw_entity = self.mapping_functions['entity'].inverse(entity)
                        except:
                            raw_entity = entity
                    else:
                        raw_entity = entity

                    entity_change = raw_entity * atr
                    open_price = curr_price - entity_change

                    # 确保开盘价为正
                    open_price = max(0.001, open_price)

                    # 计算最高价和最低价
                    if 'upline' in self.mapping_functions:
                        try:
                            raw_upline = self.mapping_functions['upline'].inverse(upline)
                        except:
                            raw_upline = upline
                    else:
                        raw_upline = upline

                    if 'downline' in self.mapping_functions:
                        try:
                            raw_downline = self.mapping_functions['downline'].inverse(downline)
                        except:
                            raw_downline = downline
                    else:
                        raw_downline = downline

                    upline_change = raw_upline * atr
                    downline_change = raw_downline * atr

                    high_price = max(open_price, curr_price) + upline_change
                    low_price = min(open_price, curr_price) - downline_change

                    # 确保价格关系正确
                    high_price = max(high_price, open_price, curr_price)
                    low_price = min(low_price, open_price, curr_price)
                    low_price = max(0.001, low_price)  # 确保最低价为正

                    opens.append(open_price)
                    highs.append(high_price)
                    lows.append(low_price)

                    # 计算交易量
                    if self.include_volume:
                        if 'volume' in self.mapping_functions:
                            try:
                                raw_volume = self.mapping_functions['volume'].inverse(volume_change)
                            except:
                                raw_volume = volume_change
                        else:
                            raw_volume = volume_change

                        if i == 0:
                            if start_volume is None or start_volume <= 0:
                                curr_volume = 1000000  # 默认交易量
                            else:
                                curr_volume = start_volume
                        else:
                            # 使用前一个交易量作为基准，根据raw_volume调整
                            curr_volume = volumes[-1] * (1 + raw_volume * 0.2)
                            curr_volume = max(1.0, curr_volume)

                        volumes.append(curr_volume)
            else:  # 特殊token
                # 使用前一个价格
                curr_price = prices[-1]
                opens.append(curr_price * 0.99)
                highs.append(curr_price * 1.01)
                lows.append(curr_price * 0.99)
                if self.include_volume:
                    volumes.append(start_volume if i == 0 else volumes[-1])

            prices.append(curr_price)

        # 移除第一个价格（它只是起始价格）
        prices = prices[1:]

        # 填充DataFrame
        ohlc['close'] = prices
        ohlc['open'] = opens
        ohlc['high'] = highs
        ohlc['low'] = lows

        if self.include_volume and volumes:
            ohlc['volume'] = volumes

        return ohlc

    def visualize_tokenization(self, df, tokens=None, reconstructed_df=None, title=None, show_volume=True):
        """
        可视化原始K线数据和重建的K线数据

        Args:
            df: 原始K线数据
            tokens: token ID列表（可选）
            reconstructed_df: 重建的K线数据（可选）
            title: 图表标题
            show_volume: 是否显示交易量
        """
        # 确保列名小写
        df.columns = [col.lower() for col in df.columns]

        if tokens is not None and reconstructed_df is None:
            # 如果提供了tokens但没有提供重建的数据，则尝试重建
            atr = df['atr'].iloc[-1] if 'atr' in df.columns else self._calculate_atr(df).iloc[-1]
            start_price = df['close'].iloc[0]
            start_volume = df['volume'].iloc[0] if 'volume' in df.columns else None
            reconstructed_df = self.tokens_to_candlesticks(tokens, start_price, atr, start_volume)

        # 确定是否显示交易量
        has_volume = 'volume' in df.columns and show_volume
        has_reconstructed_volume = reconstructed_df is not None and 'volume' in reconstructed_df.columns and show_volume

        # 确定子图数量和布局
        n_rows = 1 + (reconstructed_df is not None)
        if has_volume or has_reconstructed_volume:
            n_rows *= 2

        # 创建图表
        fig, axes = plt.subplots(n_rows, 1, figsize=(12, 4 * n_rows), sharex=True,
                                gridspec_kw={'height_ratios': [3, 1] * (1 + (reconstructed_df is not None)) if has_volume or has_reconstructed_volume else [1] * n_rows})

        # 确保axes是数组
        if n_rows == 1:
            axes = [axes]

        # 绘制原始K线
        ax_idx = 0
        ax1 = axes[ax_idx]
        ax_idx += 1
        ax1.set_title('原始K线数据' if title is None else title)

        # 绘制K线
        for i in range(len(df)):
            # 计算位置和颜色
            x = i
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)

        # 如果有交易量，绘制交易量
        if has_volume:
            ax_vol = axes[ax_idx]
            ax_idx += 1
            ax_vol.set_title('原始交易量')
            ax_vol.bar(range(len(df)), df['volume'], color='blue', alpha=0.5)

        # 如果有重建的数据，绘制重建的K线
        if reconstructed_df is not None:
            ax2 = axes[ax_idx]
            ax_idx += 1
            ax2.set_title('重建的K线数据')

            for i in range(len(reconstructed_df)):
                # 计算位置和颜色
                x = i
                open_price = reconstructed_df['open'].iloc[i]
                close_price = reconstructed_df['close'].iloc[i]
                high_price = reconstructed_df['high'].iloc[i]
                low_price = reconstructed_df['low'].iloc[i]
                color = 'red' if close_price >= open_price else 'green'

                # 绘制实体
                ax2.plot([x, x], [open_price, close_price], color=color, linewidth=6)
                # 绘制影线
                ax2.plot([x, x], [low_price, high_price], color=color, linewidth=1)

            # 如果有重建的交易量，绘制重建的交易量
            if has_reconstructed_volume:
                ax_vol2 = axes[ax_idx]
                ax_vol2.set_title('重建的交易量')
                ax_vol2.bar(range(len(reconstructed_df)), reconstructed_df['volume'], color='purple', alpha=0.5)

        # 添加网格线
        for ax in axes:
            ax.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()

        # 如果是交互式环境，显示图表
        if plt.isinteractive():
            plt.show()

        return fig, axes

    def save(self, path=None):
        """
        保存tokenizer到文件

        Args:
            path: 保存路径，如果为None则使用cache_dir
        """
        if path is None and self.cache_dir is not None:
            os.makedirs(self.cache_dir, exist_ok=True)
            path = os.path.join(self.cache_dir, 'nonlinear_vq_tokenizer.pkl')

        if path is not None:
            # 保存VQ层的权重
            vq_weights = self.vq_layer.embedding.weight.data.cpu().numpy()

            with open(path, 'wb') as f:
                pickle.dump({
                    'num_embeddings': self.num_embeddings,
                    'embedding_dim': self.embedding_dim,
                    'atr_window': self.atr_window,
                    'atr_mult': self.atr_mult,
                    'ma_volume_period': self.ma_volume_period,
                    'special_tokens': self.special_tokens,
                    'include_volume': self.include_volume,
                    'detect_anomalies': self.enable_anomaly_detection,
                    'anomaly_threshold': self.anomaly_threshold,
                    'vq_weights': vq_weights,
                    'mapping_functions': self.mapping_functions,
                    'token_to_id_map': self.token_to_id_map,
                    'id_to_token_map': self.id_to_token_map
                }, f)

            if self.verbose:
                print(f"Tokenizer已保存到 {path}")

    @classmethod
    def load(cls, path):
        """
        从文件加载tokenizer

        Args:
            path: 文件路径

        Returns:
            NonlinearVQTokenizer实例
        """
        with open(path, 'rb') as f:
            data = pickle.load(f)

        # 创建tokenizer实例
        tokenizer = cls(
            num_embeddings=data['num_embeddings'],
            embedding_dim=data['embedding_dim'],
            atr_window=data['atr_window'],
            atr_mult=data['atr_mult'],
            ma_volume_period=data['ma_volume_period'],
            mapping_functions=data.get('mapping_functions', {}),
            special_tokens=data['special_tokens'],
            include_volume=data['include_volume'],
            enable_anomaly_detection=data['detect_anomalies'],
            anomaly_threshold=data['anomaly_threshold']
        )

        # 加载VQ层权重
        if 'vq_weights' in data:
            tokenizer.vq_layer.embedding.weight.data.copy_(torch.tensor(data['vq_weights']))

        # 加载token映射
        if 'token_to_id_map' in data:
            tokenizer.token_to_id_map = data['token_to_id_map']

        if 'id_to_token_map' in data:
            tokenizer.id_to_token_map = data['id_to_token_map']

        return tokenizer


# 示例用法
if __name__ == '__main__':
    # 创建一些示例K线数据
    # data = {
    #     'datetime': pd.date_range(start='2023-01-01', periods=20),
    #     'open': np.random.rand(20) * 10 + 100,
    #     'high': np.random.rand(20) * 5 + 105,
    #     'low': np.random.rand(20) * -5 + 95,
    #     'close': np.random.rand(20) * 10 + 100,
    #     'volume': np.random.randint(1000, 5000, size=20)
    # }
    # df_ohlcv = pd.DataFrame(data)

    # # 确保high >= open, close和low <= open, close
    # df_ohlcv['high'] = df_ohlcv[['open', 'close', 'high']].max(axis=1)
    # df_ohlcv['low'] = df_ohlcv[['open', 'close', 'low']].min(axis=1)

    _, _, df_ohlcv, _  = load_single_data('f:/hqdata/fut_top_min1.parquet')
    df_ohlcv = df_ohlcv[0][-100:]
    print(df_ohlcv.head())

    # 1. 初始化NonlinearVQTokenizer
    # 创建映射函数
    mapping_functions = {
        'change': SigmoidMapping((-5, 5), (-1, 1)),
        'entity': SigmoidMapping((-3, 3), (-1, 1)),
        'upline': SquareRootMapping((0, 3), (0, 1)),
        'downline': SquareRootMapping((0, 3), (0, 1))
    }

    # 创建tokenizer
    weights_path = R'e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250508_155202\vqvae_codebook_fut_top_min1.pt'
    tokenizer = NonlinearVQTokenizer(
        codebook_weights_path=weights_path,
        num_embeddings=1024,
        embedding_dim=4,
        atr_window=100,
        ma_volume_period=100,
        mapping_functions=mapping_functions,
        include_volume=False,
        enable_anomaly_detection=True,
        verbose=True
    )

    # 2. 对K线数据进行tokenize
    tokens = tokenizer.tokenize(df_ohlcv)
    print(f"\nTokenize结果 (长度 {len(tokens)}):")
    print(tokens)

    # 3. 解码token序列
    decoded_tokens = tokenizer.decode_sequence(tokens)
    print("\n解码后的token序列:")
    print(decoded_tokens)

    # 4. 获取某个token对应的量化向量
    if len(tokens) > 1:
        example_token = tokens[1]
        if example_token < tokenizer.num_embeddings:
            quantized_vector = tokenizer.get_quantized_vector(example_token)
            print(f"\nToken ID {example_token} ('{tokenizer.decode_id(example_token)}') 对应的量化向量:")
            print(quantized_vector)

    # 5. 将token序列转换回K线数据
    atr = df_ohlcv['high'].std()  # 简化的ATR计算
    start_price = df_ohlcv['close'].iloc[0]
    reconstructed_df = tokenizer.tokens_to_candlesticks(tokens, start_price, atr)

    # 6. 可视化原始K线和重建的K线
    try:
        print("\n尝试可视化K线数据...")
        fig, axes = tokenizer.visualize_tokenization(df_ohlcv, tokens, reconstructed_df, title="NonlinearVQTokenizer示例")

        # 保存图表到文件
        save_path = "nonlinear_vq_tokenizer_example.png"
        fig.savefig(save_path)
        plt.close(fig)
        print(f"可视化结果已保存到: {save_path}")
    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
