{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES\n", "from pyqlab.utils.timefeatures import time_features"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36652\\3658139251.py:26: FutureWarning: the convert_dtype parameter is deprecated and will be removed in a future version.  Do ``ser.astype(object).apply()`` instead if you want ``convert_dtype=False``.\n", "  df['month'] = df.date.apply(lambda row: row.month, 1)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36652\\3658139251.py:27: FutureWarning: the convert_dtype parameter is deprecated and will be removed in a future version.  Do ``ser.astype(object).apply()`` instead if you want ``convert_dtype=False``.\n", "  df['day'] = df.date.apply(lambda row: row.day, 1)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36652\\3658139251.py:28: FutureWarning: the convert_dtype parameter is deprecated and will be removed in a future version.  Do ``ser.astype(object).apply()`` instead if you want ``convert_dtype=False``.\n", "  df['weekday'] = df.date.apply(lambda row: row.weekday(), 1)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36652\\3658139251.py:29: FutureWarning: the convert_dtype parameter is deprecated and will be removed in a future version.  Do ``ser.astype(object).apply()`` instead if you want ``convert_dtype=False``.\n", "  df['hour'] = df.date.apply(lambda row: row.hour, 1)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["    code_encoded                date  change  entity  upline  downline  month  \\\n", "0              0 2024-01-02 09:05:00      13      24      38         0      1   \n", "1              0 2024-01-02 09:10:00     -14     -18      12         0      1   \n", "2              0 2024-01-02 09:15:00     -30     -30       2         0      1   \n", "3              0 2024-01-02 09:20:00      -2      -4      10         2      1   \n", "4              0 2024-01-02 09:25:00      10       8       6         8      1   \n", "5              0 2024-01-02 09:30:00       8      10       0         4      1   \n", "6              0 2024-01-02 09:35:00     -12     -12       2         2      1   \n", "7              0 2024-01-02 09:40:00       2       4       8         2      1   \n", "8              0 2024-01-02 09:45:00       0       0       0        10      1   \n", "9              0 2024-01-02 09:50:00      -6      -6       4         2      1   \n", "10             0 2024-01-02 09:55:00       8       8       0         4      1   \n", "11             0 2024-01-02 10:00:00      10      12       2         4      1   \n", "12             0 2024-01-02 10:05:00     -16     -16       2         2      1   \n", "13             0 2024-01-02 10:10:00      -4      -4       4         0      1   \n", "14             0 2024-01-02 10:30:00     -28     -28       2         8      1   \n", "\n", "    day  weekday  hour  minute  \n", "0     2        1     9       1  \n", "1     2        1     9       2  \n", "2     2        1     9       3  \n", "3     2        1     9       4  \n", "4     2        1     9       5  \n", "5     2        1     9       6  \n", "6     2        1     9       7  \n", "7     2        1     9       8  \n", "8     2        1     9       9  \n", "9     2        1     9      10  \n", "10    2        1     9      11  \n", "11    2        1    10       0  \n", "12    2        1    10       1  \n", "13    2        1    10       2  \n", "14    2        1    10       6  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36652\\3658139251.py:30: FutureWarning: the convert_dtype parameter is deprecated and will be removed in a future version.  Do ``ser.astype(object).apply()`` instead if you want ``convert_dtype=False``.\n", "  df['minute'] = df.date.apply(lambda row: row.minute, 1)\n"]}], "source": ["market = 'fut'\n", "timeenc = 0\n", "df = pd.DataFrame()\n", "if market == 'sf':\n", "    for y in range(2024, 2025):\n", "        df2 = pd.read_csv(f'd:/RoboQuant2/store/barenc/sf/min5_{y}.csv', header=None)\n", "        df = pd.concat([df, df2], axis=0, ignore_index=True)\n", "else:\n", "    for y in range(2024, 2025):\n", "        df2 = pd.read_csv(f'd:/RoboQuant2/store/barenc/fut/min5_{y}.csv', header=None)\n", "        df = pd.concat([df, df2], axis=0, ignore_index=True)\n", "\n", "df.columns = ['symbol', 'date', 'change', 'entity', 'upline', 'downline']\n", "fut_codes_dict = {code: i for i, code in enumerate(sorted(MAIN_FUT_CODES + SF_FUT_CODES))}\n", "df['code_encoded'] = df['symbol'].apply(lambda x: fut_codes_dict[x])\n", "df.drop(columns=['symbol'], inplace=True)\n", "# move code_encoded to the first column\n", "# 将date列由timestamp转换为东8区日期时间\n", "df['date'] = pd.to_datetime(df['date'], unit='s') + pd.Timedelta(hours=8)\n", "cols = df.columns.tolist()\n", "cols = cols[-1:] + cols[:-1]\n", "df = df[cols]\n", "df = df.sort_values(by=['code_encoded', 'date']).reset_index(drop=True)\n", "\n", "if timeenc == 0:\n", "    df['month'] = df.date.apply(lambda row: row.month, 1)\n", "    df['day'] = df.date.apply(lambda row: row.day, 1)\n", "    df['weekday'] = df.date.apply(lambda row: row.weekday(), 1)\n", "    df['hour'] = df.date.apply(lambda row: row.hour, 1)\n", "    df['minute'] = df.date.apply(lambda row: row.minute, 1)\n", "    df['minute'] = df.minute.map(lambda x: x // 5)\n", "    # df = df.drop(['date'], axis=1).values\n", "elif timeenc == 1:\n", "    df_stamp= time_features(pd.to_datetime(df['date'].values), freq='t').transpose(1, 0)\n", "    df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])\n", "    df_tf.drop(columns=['DayOfYear'], inplace=True)\n", "    df = pd.concat([df, df_tf], axis=1)\n", "print(df.head(15))\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["minute\n", "11    29186\n", "7     29153\n", "6     29151\n", "2     29148\n", "9     29147\n", "1     29146\n", "8     29146\n", "10    29141\n", "5     23982\n", "3     23977\n", "4     23976\n", "0     19737\n", "Name: count, dtype: int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df['minute'].value_counts()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["hour\n", "14    62016\n", "9     56849\n", "22    50886\n", "21    46637\n", "10    46496\n", "13    31010\n", "11    30996\n", "Name: count, dtype: int64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df['hour'].value_counts()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["weekday\n", "1    72404\n", "2    68887\n", "0    67311\n", "3    60554\n", "4    55734\n", "Name: count, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df['weekday'].value_counts()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code_encoded</th>\n", "      <th>date</th>\n", "      <th>change</th>\n", "      <th>entity</th>\n", "      <th>upline</th>\n", "      <th>downline</th>\n", "      <th>month</th>\n", "      <th>day</th>\n", "      <th>weekday</th>\n", "      <th>hour</th>\n", "      <th>minute</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>324890.000000</td>\n", "      <td>324890</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "      <td>324890.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>33.738552</td>\n", "      <td>2024-03-16 11:42:01.317676544</td>\n", "      <td>0.169202</td>\n", "      <td>0.013774</td>\n", "      <td>2.534513</td>\n", "      <td>2.592594</td>\n", "      <td>2.962707</td>\n", "      <td>16.435969</td>\n", "      <td>1.892259</td>\n", "      <td>14.428822</td>\n", "      <td>5.731512</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>2024-01-02 09:05:00</td>\n", "      <td>-538.000000</td>\n", "      <td>-537.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>9.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1%</th>\n", "      <td>0.000000</td>\n", "      <td>2024-01-02 22:30:00</td>\n", "      <td>-20.000000</td>\n", "      <td>-20.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>9.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5%</th>\n", "      <td>2.000000</td>\n", "      <td>2024-01-08 14:40:00</td>\n", "      <td>-11.000000</td>\n", "      <td>-11.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>9.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10%</th>\n", "      <td>5.000000</td>\n", "      <td>2024-01-15 11:10:00</td>\n", "      <td>-8.000000</td>\n", "      <td>-8.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>5.000000</td>\n", "      <td>0.000000</td>\n", "      <td>9.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>14.000000</td>\n", "      <td>2024-02-02 11:10:00</td>\n", "      <td>-4.000000</td>\n", "      <td>-4.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>9.000000</td>\n", "      <td>1.000000</td>\n", "      <td>10.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>36.000000</td>\n", "      <td>2024-03-15 22:15:00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>3.000000</td>\n", "      <td>17.000000</td>\n", "      <td>2.000000</td>\n", "      <td>13.000000</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>52.000000</td>\n", "      <td>2024-04-23 12:58:45</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>23.000000</td>\n", "      <td>3.000000</td>\n", "      <td>21.000000</td>\n", "      <td>9.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90%</th>\n", "      <td>61.000000</td>\n", "      <td>2024-05-16 21:15:00</td>\n", "      <td>8.000000</td>\n", "      <td>8.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>5.000000</td>\n", "      <td>28.000000</td>\n", "      <td>4.000000</td>\n", "      <td>22.000000</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95%</th>\n", "      <td>64.000000</td>\n", "      <td>2024-05-23 11:10:00</td>\n", "      <td>11.000000</td>\n", "      <td>11.000000</td>\n", "      <td>8.000000</td>\n", "      <td>8.000000</td>\n", "      <td>5.000000</td>\n", "      <td>29.000000</td>\n", "      <td>4.000000</td>\n", "      <td>22.000000</td>\n", "      <td>11.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99%</th>\n", "      <td>68.000000</td>\n", "      <td>2024-05-29 09:25:00</td>\n", "      <td>21.000000</td>\n", "      <td>21.000000</td>\n", "      <td>13.000000</td>\n", "      <td>13.000000</td>\n", "      <td>5.000000</td>\n", "      <td>31.000000</td>\n", "      <td>4.000000</td>\n", "      <td>22.000000</td>\n", "      <td>11.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>68.000000</td>\n", "      <td>2024-05-29 22:55:00</td>\n", "      <td>2030.000000</td>\n", "      <td>174.000000</td>\n", "      <td>234.000000</td>\n", "      <td>231.000000</td>\n", "      <td>5.000000</td>\n", "      <td>31.000000</td>\n", "      <td>4.000000</td>\n", "      <td>22.000000</td>\n", "      <td>11.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>20.540287</td>\n", "      <td>NaN</td>\n", "      <td>9.088338</td>\n", "      <td>8.058830</td>\n", "      <td>3.452565</td>\n", "      <td>3.535769</td>\n", "      <td>1.439196</td>\n", "      <td>8.418552</td>\n", "      <td>1.382950</td>\n", "      <td>4.948979</td>\n", "      <td>3.429770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        code_encoded                           date         change  \\\n", "count  324890.000000                         324890  324890.000000   \n", "mean       33.738552  2024-03-16 11:42:01.317676544       0.169202   \n", "min         0.000000            2024-01-02 09:05:00    -538.000000   \n", "1%          0.000000            2024-01-02 22:30:00     -20.000000   \n", "5%          2.000000            2024-01-08 14:40:00     -11.000000   \n", "10%         5.000000            2024-01-15 11:10:00      -8.000000   \n", "25%        14.000000            2024-02-02 11:10:00      -4.000000   \n", "50%        36.000000            2024-03-15 22:15:00       0.000000   \n", "75%        52.000000            2024-04-23 12:58:45       4.000000   \n", "90%        61.000000            2024-05-16 21:15:00       8.000000   \n", "95%        64.000000            2024-05-23 11:10:00      11.000000   \n", "99%        68.000000            2024-05-29 09:25:00      21.000000   \n", "max        68.000000            2024-05-29 22:55:00    2030.000000   \n", "std        20.540287                            NaN       9.088338   \n", "\n", "              entity         upline       downline          month  \\\n", "count  324890.000000  324890.000000  324890.000000  324890.000000   \n", "mean        0.013774       2.534513       2.592594       2.962707   \n", "min      -537.000000       0.000000       0.000000       1.000000   \n", "1%        -20.000000       0.000000       0.000000       1.000000   \n", "5%        -11.000000       0.000000       0.000000       1.000000   \n", "10%        -8.000000       0.000000       0.000000       1.000000   \n", "25%        -4.000000       0.000000       0.000000       2.000000   \n", "50%         0.000000       2.000000       2.000000       3.000000   \n", "75%         4.000000       4.000000       4.000000       4.000000   \n", "90%         8.000000       6.000000       6.000000       5.000000   \n", "95%        11.000000       8.000000       8.000000       5.000000   \n", "99%        21.000000      13.000000      13.000000       5.000000   \n", "max       174.000000     234.000000     231.000000       5.000000   \n", "std         8.058830       3.452565       3.535769       1.439196   \n", "\n", "                 day        weekday           hour         minute  \n", "count  324890.000000  324890.000000  324890.000000  324890.000000  \n", "mean       16.435969       1.892259      14.428822       5.731512  \n", "min         1.000000       0.000000       9.000000       0.000000  \n", "1%          1.000000       0.000000       9.000000       0.000000  \n", "5%          3.000000       0.000000       9.000000       0.000000  \n", "10%         5.000000       0.000000       9.000000       1.000000  \n", "25%         9.000000       1.000000      10.000000       3.000000  \n", "50%        17.000000       2.000000      13.000000       6.000000  \n", "75%        23.000000       3.000000      21.000000       9.000000  \n", "90%        28.000000       4.000000      22.000000      10.000000  \n", "95%        29.000000       4.000000      22.000000      11.000000  \n", "99%        31.000000       4.000000      22.000000      11.000000  \n", "max        31.000000       4.000000      22.000000      11.000000  \n", "std         8.418552       1.382950       4.948979       3.429770  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe(percentiles=[0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# 将列change的值限定在-20到20之间\n", "df2=df.copy()\n", "df2.loc[df['change'] < -12, 'change'] = -12\n", "df2.loc[df['change'] > 12, 'change'] = 12\n", "df2.loc[df['entity'] < -12, 'entity'] = -12\n", "df2.loc[df['entity'] > 12, 'entity'] = 12\n", "df2.loc[df['upline'] > 7, 'upline'] = 7\n", "df2.loc[df['downline'] > 7, 'downline'] = 7\n", "# df2.describe(percentiles=[0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["codes: 55, chgs: 25, ents: 25, ups: 8, downs: 8\n", "vocab: 40000\n"]}], "source": ["codes = sorted(list(set(df2.values[:, 0])))\n", "chgs = sorted(list(set(df2.values[:, 2])))\n", "ents = sorted(list(set(df2.values[:, 3])))\n", "ups = sorted(list(set(df2.values[:, 4])))\n", "downs = sorted(list(set(df2.values[:, 5])))\n", "print(f'codes: {len(codes)}, chgs: {len(chgs)}, ents: {len(ents)}, ups: {len(ups)}, downs: {len(downs)}')\n", "vocab = 25 * 25 * 8 * 8\n", "print(f'vocab: {vocab}')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["bar_set: 40000\n"]}], "source": ["bar_set = []\n", "for i in range(-12, 13):\n", "    for j in range(-12, 13):\n", "        for k in range(0, 8):\n", "            for l in range(0, 8):\n", "                bar_set.append(f'{i}|{j}|{k}|{l}')\n", "bar_set = sorted(bar_set)\n", "print(f'bar_set: {len(bar_set)}')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code_encoded</th>\n", "      <th>month</th>\n", "      <th>day</th>\n", "      <th>weekday</th>\n", "      <th>hour</th>\n", "      <th>minute</th>\n", "      <th>bar</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>25016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td>3384</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>3</td>\n", "      <td>3344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>4</td>\n", "      <td>6842</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>22327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324885</th>\n", "      <td>68</td>\n", "      <td>5</td>\n", "      <td>29</td>\n", "      <td>2</td>\n", "      <td>22</td>\n", "      <td>7</td>\n", "      <td>8362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324886</th>\n", "      <td>68</td>\n", "      <td>5</td>\n", "      <td>29</td>\n", "      <td>2</td>\n", "      <td>22</td>\n", "      <td>8</td>\n", "      <td>16680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324887</th>\n", "      <td>68</td>\n", "      <td>5</td>\n", "      <td>29</td>\n", "      <td>2</td>\n", "      <td>22</td>\n", "      <td>9</td>\n", "      <td>31675</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324888</th>\n", "      <td>68</td>\n", "      <td>5</td>\n", "      <td>29</td>\n", "      <td>2</td>\n", "      <td>22</td>\n", "      <td>10</td>\n", "      <td>19995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324889</th>\n", "      <td>68</td>\n", "      <td>5</td>\n", "      <td>29</td>\n", "      <td>2</td>\n", "      <td>22</td>\n", "      <td>11</td>\n", "      <td>34955</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>324890 rows × 7 columns</p>\n", "</div>"], "text/plain": ["        code_encoded  month  day  weekday  hour  minute    bar\n", "0                  0      1    2        1     9       1  25016\n", "1                  0      1    2        1     9       2   3384\n", "2                  0      1    2        1     9       3   3344\n", "3                  0      1    2        1     9       4   6842\n", "4                  0      1    2        1     9       5  22327\n", "...              ...    ...  ...      ...   ...     ...    ...\n", "324885            68      5   29        2    22       7   8362\n", "324886            68      5   29        2    22       8  16680\n", "324887            68      5   29        2    22       9  31675\n", "324888            68      5   29        2    22      10  19995\n", "324889            68      5   29        2    22      11  34955\n", "\n", "[324890 rows x 7 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将3,4,5,6列的值转换为bar_set中的索引\n", "df3 = df2.copy()\n", "df3['bar'] = df3['change'].astype(str) + '|' + df3['entity'].astype(str) + '|' + df3['upline'].astype(str) + '|' + df3['downline'].astype(str)\n", "df3['bar'] = df3['bar'].apply(lambda x: bar_set.index(x))\n", "df3['bar'] = df3['bar'].astype(int)\n", "df3.drop(columns=['date', 'change', 'entity', 'upline', 'downline'], inplace=True)\n", "# df3 = df3[['code_encoded', 'bar', 'MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear']]\n", "df3"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["codecount=df3['code_encoded'].value_counts()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["[6229,\n", " 6229,\n", " 6229,\n", " 6229,\n", " 4136,\n", " 6229,\n", " 6229,\n", " 6229,\n", " 6229,\n", " 6229,\n", " 6163,\n", " 4136,\n", " 6229,\n", " 6229,\n", " 6162,\n", " 6229,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 6229,\n", " 6229,\n", " 4136,\n", " 6229,\n", " 6229,\n", " 4136,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 4136,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 6163,\n", " 6061,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 4136,\n", " 6163,\n", " 4136,\n", " 6229,\n", " 6229,\n", " 6163,\n", " 6229,\n", " 6163,\n", " 4136,\n", " 6229,\n", " 6229,\n", " 6229]"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["codecount = dict(sorted(codecount.to_dict().items()))\n", "codecount = [i for i in codecount.values()]\n", "codecount"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def i_to_idx(i):\n", "    # 统计每个合约的数据量\n", "    codecount=df3['code_encoded'].value_counts()\n", "    codes_len=codecount.to_list()\n", "    codes_len2 = [i-11 for i in codes_len]\n", "    # 每个值都必须大于等于0\n", "    assert all([i >= 0 for i in codes_len2])\n", "    # codes_len2累加\n", "    codes_len2 = np.cumsum(codes_len2)\n", "    idx = np.searchsorted(codes_len2, 13358, side='right')\n", "\n"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["# dataframe group by symbol to ndarray\n", "df4 = df3.groupby('code_encoded')\n", "block_size = 10\n", "data = []\n", "data_mark = []\n", "for code_encoded, group in df4:\n", "    for i in range(0, len(group) - block_size):\n", "        data.append(group.iloc[i:i+block_size+1, :2].values)\n", "        data_mark.append(group.iloc[i:i+block_size+1, 2:].values)\n", "data = np.array(data)\n", "data_mark = np.array(data_mark)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["((3011, 11, 2), (3011, 11, 5))"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["data.shape, data_mark.shape"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[   26, 23296],\n", "       [   26, 33296],\n", "       [   26,  5009],\n", "       [   26, 31616],\n", "       [   26,  4995],\n", "       [   26, 19984],\n", "       [   26,  8329],\n", "       [   26, 36608],\n", "       [   26, 13249],\n", "       [   26,  5001],\n", "       [   26, 33288]], dtype=int64)"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["data[-1]"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.07627119, -0.10869565,  0.16666667,  0.        ,  0.45616438],\n", "       [ 0.09322034, -0.10869565,  0.16666667,  0.        ,  0.45616438],\n", "       [ 0.1779661 , -0.10869565,  0.16666667,  0.        ,  0.45616438],\n", "       [ 0.26271186, -0.10869565,  0.16666667,  0.        ,  0.45616438],\n", "       [ 0.34745763, -0.10869565,  0.16666667,  0.        ,  0.45616438],\n", "       [ 0.43220339, -0.10869565,  0.16666667,  0.        ,  0.45616438],\n", "       [-0.5       , -0.06521739,  0.16666667,  0.        ,  0.45616438],\n", "       [-0.41525424, -0.06521739,  0.16666667,  0.        ,  0.45616438],\n", "       [-0.33050847, -0.06521739,  0.16666667,  0.        ,  0.45616438],\n", "       [-0.24576271, -0.06521739,  0.16666667,  0.        ,  0.45616438],\n", "       [-0.16101695, -0.06521739,  0.16666667,  0.        ,  0.45616438]])"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["data_mark[0]"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(15138, 8)\n", "(14891, 8)\n", "(14988, 8)\n", "(15039, 8)\n"]}], "source": ["for i in range(data.shape[0]):\n", "    print(data[i].shape)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[23, 24, 25, 26]\n"]}], "source": ["print(codes)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["# 将data中的数据保存到文件中\n", "np.save('d:/RoboQuant2/store/barenc/sf_min5_2023_2024.npy', data)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["data = np.load('d:/RoboQuant2/store/barenc/sf_min5_2023_2024.npy', allow_pickle=True)\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[       3, 20230105,        0,        8,        0,        0,\n", "               0,    14724],\n", "       [       3, 20230105,        1,      -10,      -10,        0,\n", "               5,        5],\n", "       [       3, 20230105,        2,        5,        5,        2,\n", "               1,    12685],\n", "       [       3, 20230105,        3,        2,        2,        3,\n", "               4,    10318],\n", "       [       3, 20230105,        4,       10,       10,        2,\n", "               3,     8727],\n", "       [       3, 20230105,        5,        0,        0,        5,\n", "               0,     7950],\n", "       [       3, 20230105,        6,       10,       10,        0,\n", "               0,     8712],\n", "       [       3, 20230105,        7,        3,        2,        1,\n", "               5,    11063],\n", "       [       3, 20230105,        8,       -6,       -6,        2,\n", "               1,     4765],\n", "       [       3, 20230105,        9,       -6,       -6,        2,\n", "               2,     4766]], dtype=int64)"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["data[3][:10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}