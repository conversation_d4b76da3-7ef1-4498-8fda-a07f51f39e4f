"""
测试预测分布统计功能

该脚本用于测试回测系统的预测分布统计和可视化功能。
"""

import os
import sys
import numpy as np
import torch
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.fintimeseries.backtest_time_series_model2dr_v2 import TimeSeriesBacktester
from pyqlab.models.fintimeseries.test_backtest import MockDataset

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PredictiveModel:
    """模拟一个有预测性的模型"""
    
    def __init__(self, prediction_type='diverse'):
        """
        Args:
            prediction_type: 预测类型
                - 'diverse': 多样化预测
                - 'concentrated': 集中预测（模拟模型退化）
                - 'biased': 有偏预测
                - 'extreme': 极端预测
        """
        self.prediction_type = prediction_type
        self.step = 0
    
    def __call__(self, embds, x):
        """模拟模型预测"""
        batch_size = embds.shape[0]
        self.step += 1
        
        if self.prediction_type == 'diverse':
            # 多样化预测：正态分布 + 一些噪声
            predictions = np.random.normal(0, 0.02, batch_size)
            # 添加一些趋势
            trend = 0.001 * np.sin(self.step * 0.1)
            predictions += trend
            
        elif self.prediction_type == 'concentrated':
            # 集中预测：大部分预测值相同（模拟模型退化）
            if np.random.random() < 0.8:
                predictions = np.full(batch_size, 0.001)  # 80%的预测都是0.001
            else:
                predictions = np.random.normal(0, 0.005, batch_size)
                
        elif self.prediction_type == 'biased':
            # 有偏预测：明显的看涨偏向
            predictions = np.random.normal(0.01, 0.015, batch_size)  # 均值为正
            
        elif self.prediction_type == 'extreme':
            # 极端预测：经常出现大幅变动
            if np.random.random() < 0.3:
                # 30%概率出现极端预测
                predictions = np.random.choice([-0.08, 0.08], batch_size)
            else:
                predictions = np.random.normal(0, 0.01, batch_size)
        
        # 限制预测范围
        predictions = np.clip(predictions, -0.1, 0.1)
        
        return torch.tensor(predictions.reshape(-1, 1), dtype=torch.float32)


def test_prediction_stats_diverse():
    """测试多样化预测的统计"""
    logger.info("测试多样化预测统计...")
    
    model = PredictiveModel('diverse')
    dataset = MockDataset(size=200, seq_len=30)
    
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=10000.0
    )
    
    results = backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    # 验证预测统计
    assert 'prediction_stats' in results
    stats = results['prediction_stats']
    
    logger.info(f"多样化预测统计:")
    logger.info(f"  多样性比率: {stats['diversity_ratio']:.4f}")
    logger.info(f"  标准差: {stats['std']:.6f}")
    logger.info(f"  偏度: {stats['skewness']:.4f}")
    
    # 多样化预测应该有较高的多样性比率
    assert stats['diversity_ratio'] > 0.3, f"多样性比率过低: {stats['diversity_ratio']}"
    
    logger.info("✓ 多样化预测统计测试通过")
    return results


def test_prediction_stats_concentrated():
    """测试集中预测的统计"""
    logger.info("测试集中预测统计...")
    
    model = PredictiveModel('concentrated')
    dataset = MockDataset(size=200, seq_len=30)
    
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=10000.0
    )
    
    results = backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    stats = results['prediction_stats']
    
    logger.info(f"集中预测统计:")
    logger.info(f"  多样性比率: {stats['diversity_ratio']:.4f}")
    logger.info(f"  标准差: {stats['std']:.6f}")
    
    # 集中预测应该有较低的多样性比率
    assert stats['diversity_ratio'] < 0.5, f"多样性比率过高: {stats['diversity_ratio']}"
    
    logger.info("✓ 集中预测统计测试通过")
    return results


def test_prediction_stats_biased():
    """测试有偏预测的统计"""
    logger.info("测试有偏预测统计...")
    
    model = PredictiveModel('biased')
    dataset = MockDataset(size=200, seq_len=30)
    
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=10000.0
    )
    
    results = backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    stats = results['prediction_stats']
    
    logger.info(f"有偏预测统计:")
    logger.info(f"  均值: {stats['mean']:.6f}")
    logger.info(f"  正值比率: {stats['positive_ratio']:.4f}")
    logger.info(f"  负值比率: {stats['negative_ratio']:.4f}")
    
    # 有偏预测应该有明显的正偏向
    assert stats['mean'] > 0.005, f"均值偏向不明显: {stats['mean']}"
    assert stats['positive_ratio'] > 0.6, f"正值比率不够高: {stats['positive_ratio']}"
    
    logger.info("✓ 有偏预测统计测试通过")
    return results


def test_prediction_stats_extreme():
    """测试极端预测的统计"""
    logger.info("测试极端预测统计...")
    
    model = PredictiveModel('extreme')
    dataset = MockDataset(size=200, seq_len=30)
    
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=10000.0
    )
    
    results = backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    stats = results['prediction_stats']
    
    logger.info(f"极端预测统计:")
    logger.info(f"  标准差: {stats['std']:.6f}")
    logger.info(f"  极端值比率: {stats['>5%_ratio'] + stats['<-5%_ratio']:.4f}")
    logger.info(f"  峰度: {stats['kurtosis']:.4f}")
    
    # 极端预测应该有较高的标准差和峰度
    extreme_ratio = stats['>5%_ratio'] + stats['<-5%_ratio']
    assert extreme_ratio > 0.1, f"极端值比率过低: {extreme_ratio}"
    
    logger.info("✓ 极端预测统计测试通过")
    return results


def test_prediction_visualization():
    """测试预测分布可视化"""
    logger.info("测试预测分布可视化...")
    
    model = PredictiveModel('diverse')
    dataset = MockDataset(size=100, seq_len=30)
    
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=10000.0
    )
    
    results = backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    # 测试可视化功能
    output_dir = "./test_prediction_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成预测分布图
    chart_path = os.path.join(output_dir, 'test_prediction_distribution.png')
    backtester.visualize_prediction_distribution(results, chart_path)
    
    # 生成预测分布报告
    report_path = os.path.join(output_dir, 'test_prediction_report.txt')
    report_text = backtester.create_prediction_distribution_report(results, report_path)
    
    # 验证文件是否生成
    assert os.path.exists(chart_path), "预测分布图未生成"
    assert os.path.exists(report_path), "预测分布报告未生成"
    
    logger.info("✓ 预测分布可视化测试通过")
    logger.info(f"输出文件保存在: {output_dir}")
    
    return results


def run_all_prediction_tests():
    """运行所有预测统计测试"""
    logger.info("=" * 60)
    logger.info("开始运行预测分布统计测试")
    logger.info("=" * 60)
    
    try:
        # 运行各种预测类型的测试
        diverse_results = test_prediction_stats_diverse()
        concentrated_results = test_prediction_stats_concentrated()
        biased_results = test_prediction_stats_biased()
        extreme_results = test_prediction_stats_extreme()
        
        # 测试可视化功能
        viz_results = test_prediction_visualization()
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ 所有预测分布统计测试通过！")
        logger.info("=" * 60)
        
        # 比较不同预测类型的统计特征
        logger.info("\n预测类型比较:")
        logger.info("类型\t\t多样性\t标准差\t\t偏度\t\t峰度")
        logger.info("-" * 70)
        
        results_map = {
            '多样化': diverse_results,
            '集中化': concentrated_results,
            '有偏向': biased_results,
            '极端化': extreme_results
        }
        
        for name, results in results_map.items():
            stats = results['prediction_stats']
            logger.info(f"{name}\t\t{stats['diversity_ratio']:.4f}\t{stats['std']:.6f}\t{stats['skewness']:.4f}\t\t{stats['kurtosis']:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 预测统计测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = run_all_prediction_tests()
        if success:
            logger.info("🎉 预测分布统计功能测试完成！")
        else:
            logger.error("❌ 测试失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断了测试")
    except Exception as e:
        logger.error(f"测试执行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
