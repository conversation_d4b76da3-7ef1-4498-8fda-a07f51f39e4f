{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              name          stategy        pnl  times  long_atr_mult  \\\n", "label                                                                  \n", "A1909.DC    豆一1909  FUT-STR-BK-b-RL    1530.00      4            0.4   \n", "AG1912.SC   白银1912  FUT-STR-BK-b-RL   11561.40      2            0.4   \n", "AL1907.SC   沪铝1907  FUT-STR-BK-b-RL   -3600.00      3            0.4   \n", "AP1910.ZC   苹果1910  FUT-STR-BK-b-RL -120810.00      4            0.4   \n", "AU1912.SC   黄金1912  FUT-STR-BK-b-RL     700.01      4            0.4   \n", "C1909.DC    玉米1909  FUT-STR-BK-b-RL    7240.00      8            0.4   \n", "CF1909.ZC   郑棉1909  FUT-STR-BK-b-RL  -14385.00      7            0.4   \n", "CS1909.DC   淀粉1909  FUT-STR-BK-b-RL   -5460.00      2            0.4   \n", "CU1907.SC   沪铜1907  FUT-STR-BK-b-RL   -3700.00     13            0.4   \n", "CY2001.ZC   棉纱2001  FUT-STR-BK-b-RL  -14700.00      5            0.4   \n", "FG1909.ZC   玻璃1909  FUT-STR-BK-b-RL  -14940.00      5            0.4   \n", "HC1910.SC   热卷1910  FUT-STR-BK-b-RL   -2430.00      2            0.4   \n", "I1909.DC    铁矿1909  FUT-STR-BK-b-RL    1800.00      8            0.4   \n", "J1909.DC    焦炭1909  FUT-STR-BK-b-RL  -10150.00      5            0.4   \n", "JD1909.DC   鸡蛋1909  FUT-STR-BK-b-RL    -800.00      1            0.4   \n", "JM1909.DC   焦煤1909  FUT-STR-BK-b-RL    5940.00      1            0.4   \n", "L1909.DC    乙烯1909  FUT-STR-BK-b-RL   -9895.00      9            0.4   \n", "M1909.DC    豆粕1909  FUT-STR-BK-b-RL  -13100.00      4            0.4   \n", "MA1909.ZC   甲醇1909  FUT-STR-BK-b-RL   -6260.00      2            0.4   \n", "NI1907.SC   沪镍1907  FUT-STR-BK-b-RL    4840.00      5            0.4   \n", "OI1909.ZC   菜油1909  FUT-STR-BK-b-RL  -52880.00     16            0.4   \n", "P1909.DC    棕榈1909  FUT-STR-BK-b-RL   -2300.00      2            0.4   \n", "PB1907.SC   沪铅1907  FUT-STR-BK-b-RL    -600.00      1            0.4   \n", "PP1909.DC   丙烯1909  FUT-STR-BK-b-RL  -26130.00      3            0.4   \n", "RB1910.SC   螺纹1910  FUT-STR-BK-b-RL     -70.00      1            0.4   \n", "RM1909.ZC   菜粕1909  FUT-STR-BK-b-RL  -18980.00      7            0.4   \n", "RU1909.SC   橡胶1909  FUT-STR-BK-b-RL  -12020.00      7            0.4   \n", "SC1907.SC   原油1907  FUT-STR-BK-b-RL   -7800.02      1            0.4   \n", "SF1909.ZC   硅铁1909  FUT-STR-BK-b-RL  -13440.00     13            0.4   \n", "SM1909.ZC   锰硅1909  FUT-STR-BK-b-RL    4650.00      4            0.4   \n", "...            ...              ...        ...    ...            ...   \n", "CU1907.SC   沪铜1907  FUT-STR-BK-b-RL   -3150.00     10            0.9   \n", "CY2001.ZC   棉纱2001  FUT-STR-BK-b-RL  -34975.00      9            0.9   \n", "FG1909.ZC   玻璃1909  FUT-STR-BK-b-RL  -26760.00      6            0.9   \n", "HC1910.SC   热卷1910  FUT-STR-BK-b-RL   -9100.00      3            0.9   \n", "I1909.DC    铁矿1909  FUT-STR-BK-b-RL  -48300.00      6            0.9   \n", "J1909.DC    焦炭1909  FUT-STR-BK-b-RL   23200.00      4            0.9   \n", "JD1909.DC   鸡蛋1909  FUT-STR-BK-b-RL   -3520.00      1            0.9   \n", "JM1909.DC   焦煤1909  FUT-STR-BK-b-RL   -2760.00      4            0.9   \n", "L1909.DC    乙烯1909  FUT-STR-BK-b-RL   14115.00      6            0.9   \n", "M1909.DC    豆粕1909  FUT-STR-BK-b-RL   11100.00      3            0.9   \n", "MA1909.ZC   甲醇1909  FUT-STR-BK-b-RL  -13070.00      3            0.9   \n", "NI1907.SC   沪镍1907  FUT-STR-BK-b-RL   10985.00      6            0.9   \n", "OI1909.ZC   菜油1909  FUT-STR-BK-b-RL  -25920.00      9            0.9   \n", "P1909.DC    棕榈1909  FUT-STR-BK-b-RL   18100.00      4            0.9   \n", "PB1907.SC   沪铅1907  FUT-STR-BK-b-RL    6030.00      3            0.9   \n", "PP1909.DC   丙烯1909  FUT-STR-BK-b-RL   -3410.00      6            0.9   \n", "RB1910.SC   螺纹1910  FUT-STR-BK-b-RL     440.00      5            0.9   \n", "RM1909.ZC   菜粕1909  FUT-STR-BK-b-RL   33940.00      2            0.9   \n", "RU1909.SC   橡胶1909  FUT-STR-BK-b-RL  -11900.00     10            0.9   \n", "SC1907.SC   原油1907  FUT-STR-BK-b-RL   -4000.00      1            0.9   \n", "SF1909.ZC   硅铁1909  FUT-STR-BK-b-RL    4560.00      7            0.9   \n", "SM1909.ZC   锰硅1909  FUT-STR-BK-b-RL    2030.00      5            0.9   \n", "SN1909.SC   沪锡1909  FUT-STR-BK-b-RL    -980.00      5            0.9   \n", "SP1909.SC   纸浆1909  FUT-STR-BK-b-RL   56420.00      3            0.9   \n", "SR1909.Z<PERSON>   白糖1909  FUT-STR-BK-b-RL    7620.00      4            0.9   \n", "TA1909.ZC  PTA1909  FUT-STR-BK-b-RL   -2460.00      7            0.9   \n", "V1909.DC   PVC1909  FUT-STR-BK-b-RL     100.00      4            0.9   \n", "Y1909.DC    豆油1909  FUT-STR-BK-b-RL   15800.00      5            0.9   \n", "ZC1909.ZC   动煤1909  FUT-STR-BK-b-RL   13437.00      5            0.9   \n", "ZN1907.SC   沪锌1907  FUT-STR-BK-b-RL  -13800.00      5            0.9   \n", "\n", "           short_atr_mult  long_atr  short_atr  \n", "label                                           \n", "A1909.DC              0.9        18      14.00  \n", "AG1912.SC             0.9        10       4.00  \n", "AL1907.SC             0.9        35      21.00  \n", "AP1910.ZC             0.9        88      73.00  \n", "AU1912.SC             0.9         1       0.31  \n", "C1909.DC              0.9         6       5.00  \n", "CF1909.ZC             0.9        88      39.00  \n", "CS1909.DC             0.9         8       5.00  \n", "CU1907.SC             0.9       145      72.00  \n", "CY2001.ZC             0.9       142      50.00  \n", "FG1909.ZC             0.9         6       5.00  \n", "HC1910.SC             0.9        21      13.00  \n", "I1909.DC              0.9         6       5.00  \n", "J1909.DC              0.9        17      11.00  \n", "JD1909.DC             0.9        22      22.00  \n", "JM1909.DC             0.9         8       5.00  \n", "L1909.DC              0.9        38      25.00  \n", "M1909.DC              0.9        16      12.00  \n", "MA1909.ZC             0.9        17      11.00  \n", "NI1907.SC             0.9       580     221.00  \n", "OI1909.ZC             0.9        29      19.00  \n", "P1909.DC              0.9        20      13.00  \n", "PB1907.SC             0.9        70      34.00  \n", "PP1909.DC             0.9       103      26.00  \n", "RB1910.SC             0.9        20      13.00  \n", "RM1909.ZC             0.9        16      13.00  \n", "RU1909.SC             0.9        89      58.00  \n", "SC1907.SC             0.9         4       2.00  \n", "SF1909.ZC             0.9        24      17.00  \n", "SM1909.ZC             0.9        34      23.00  \n", "...                   ...       ...        ...  \n", "CU1907.SC             1.9       326     152.00  \n", "CY2001.ZC             1.9       320     105.00  \n", "FG1909.ZC             1.9        14      10.00  \n", "HC1910.SC             1.9        48      27.00  \n", "I1909.DC              1.9        14      10.00  \n", "J1909.DC              1.9        39      23.00  \n", "JD1909.DC             1.9        50      46.00  \n", "JM1909.DC             1.9        18      11.00  \n", "L1909.DC              1.9        86      53.00  \n", "M1909.DC              1.9        35      25.00  \n", "MA1909.ZC             1.9        39      23.00  \n", "NI1907.SC             1.9      1304     466.00  \n", "OI1909.ZC             1.9        65      40.00  \n", "P1909.DC              1.9        44      27.00  \n", "PB1907.SC             1.9       158      72.00  \n", "PP1909.DC             1.9       103      26.00  \n", "RB1910.SC             1.9        46      27.00  \n", "RM1909.ZC             1.9        37      27.00  \n", "RU1909.SC             1.9       201     122.00  \n", "SC1907.SC             1.9         9       4.00  \n", "SF1909.ZC             1.9        54      36.00  \n", "SM1909.ZC             1.9        76      48.00  \n", "SN1909.SC             1.9       874     441.00  \n", "SP1909.SC             1.9        68      17.00  \n", "SR1909.ZC             1.9        57      25.00  \n", "TA1909.ZC             1.9        87      38.00  \n", "V1909.DC              1.9        76      49.00  \n", "Y1909.DC              1.9        47      32.00  \n", "ZC1909.ZC             1.9         5       2.00  \n", "ZN1907.SC             1.9       247     105.00  \n", "\n", "[2712 rows x 8 columns]\n"]}], "source": ["CSV_PATH = \"d:/QuantLab/rpt/\"\n", "JSON_PATH = \"d:/QuantLab/\"\n", "\n", "def read_backtest_range(csv_file):\n", "    df = pd.read_csv(CSV_PATH + csv_file, encoding=\"gbk\")\n", "    df.set_index('label',inplace=True)\n", "    return df\n", "\n", "# csv_file = \"FUT-GAP-b.223544.ord2.csv\"\n", "csv_file = \"FUT-STR-BK-b.030837.ord.csv\"\n", "df = pd.read_csv(CSV_PATH + csv_file, encoding=\"gbk\")\n", "df.set_index('label',inplace=True)\n", "print(df)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["with open(JSO<PERSON>_PATH + \"RangeBar.json\",'r') as json_f:\n", "    range_dict = json.load(json_f)\n", "\n", "for index,row in df.iterrows():\n", "#     range_dict['fut']['long_range_bar'][index] = row['long_atr']\n", "#     range_dict['fut']['short_range_bar'][index] = row['short_atr']\n", "    range_dict['fut']['long_range_bar'][index[:-7]] = row['long_atr']\n", "    range_dict['fut']['short_range_bar'][index[:-7]] = row['short_atr']\n", "    if index in range_dict['fut']['long_range_bar']:\n", "        del range_dict['fut']['long_range_bar'][index]\n", "    if index in range_dict['fut']['short_range_bar']:\n", "        del range_dict['fut']['short_range_bar'][index]\n", "\n", "with open(J<PERSON><PERSON>_PATH + \"RangeBar.\"+ df.iloc[1, 1][:-2] + csv_file[-15:-9] + \".json\",\"w\") as json_f:\n", "    json.dump(range_dict,json_f,indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}