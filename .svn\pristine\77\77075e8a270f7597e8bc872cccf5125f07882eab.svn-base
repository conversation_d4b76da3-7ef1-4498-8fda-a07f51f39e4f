# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8
"""
Loader里，主要功能是加载原始数据到内存，不做太多的处理
"""
import abc
import warnings
import pandas as pd
import numpy as np
import datetime
import json
import os

from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

from typing import Tuple, Union, List

from qlib.data import D
from qlib.utils import load_dataset, init_instance_by_config, time_to_slc_point
from qlib.log import get_module_logger
from qlib.data.dataset.loader import DataLoader
from .factors import AicmFactorsData
from pyqlab.const import MAIN_SEL_FUT_CODES

   
class AFDataLoader(DataLoader):
    '''
    (A)icm (F)actor Data Loader
    '''
    def __init__(
           self,
           direct, data_path, train_codes=[],
           sel_lf_names=None, sel_sf_names=None, sel_ct_names=None
       ) -> None:
            self.direct = direct
            self.data_path = data_path
            self.portfolios = ""
            self.lb_df = pd.DataFrame()
            self.lf_df = pd.DataFrame()
            self.sf_df = pd.DataFrame()
            self.ct_df = pd.DataFrame()
            self.train_codes = train_codes
            self.fd = AicmFactorsData(
                direct=self.direct, data_path=self.data_path,
                sel_lf_names=sel_lf_names, sel_sf_names=sel_sf_names, sel_ct_names=sel_ct_names
            )
            self.interface_params = {
                'input_dim': 1, # 1: expression call 2: API call
                'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
            }
            self.le = LabelEncoder()
            super().__init__()

    def config(self, **kwargs):
        attr_list = {"direct", "only_trading_code"}
        for k, v in kwargs.items():
            if k in attr_list:
                setattr(self, k, v)

    def set_data(self, lb_df, lf_df, sf_df, ct_df):
        self.lb_df = lb_df
        self.lf_df = lf_df
        self.sf_df = sf_df
        self.ct_df = ct_df

    def _load_data_from_file(self):

        for pf in self.portfolios:
            if os.path.isfile('%s/factors_%s_lf.%s.csv'%(self.data_path, self.direct, pf)):
                self.lf_df = self.lf_df.append(pd.read_csv('%s/factors_%s_lf.%s.csv'%(self.data_path, self.direct, pf)))
            if os.path.isfile('%s/factors_%s_sf.%s.csv'%(self.data_path, self.direct, pf)):
                self.sf_df = self.sf_df.append(pd.read_csv('%s/factors_%s_sf.%s.csv'%(self.data_path, self.direct, pf)))
            if os.path.isfile('%s/factors_%s_ct.%s.csv'%(self.data_path, self.direct, pf)):
                self.ct_df = self.ct_df.append(pd.read_csv('%s/factors_%s_ct.%s.csv'%(self.data_path, self.direct, pf)))
            if os.path.isfile('%s/orders_%s_label.%s.csv'%(self.data_path, self.direct, pf)):
                self.lb_df = self.lb_df.append(pd.read_csv('%s/orders_%s_label.%s.csv'%(self.data_path, self.direct, pf)), ignore_index=True)
        
    def _load(self):
        self.lb_df, self.lf_df, self.sf_df, self.ct_df = self.fd.get_pf_data(self.portfolios)

    def _prepare_data(self):
        self._load()
        assert not self.lb_df.empty, "load raw data is empty."

        # 清除不需要的数据
        if len(self.train_codes) > 0:
            self.lb_df = self.lb_df[self.lb_df['CODE'].isin(self.train_codes)]
        # lb_df的排序和重新索引步骤不能少，否则FOLD标记有问题
        self.lb_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.sf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.lf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.ct_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        # self.lb_df.sort_values(by='ord_id', inplace=True)
        # self.lb_df.reset_index(drop=True, inplace=True)
        self.lb_df.set_index("ord_id", inplace=True)
        self.lf_df.set_index("ord_id", inplace=True)
        self.sf_df.set_index("ord_id", inplace=True)
        self.ct_df.set_index("ord_id", inplace=True)
        self.lf_df = self.lf_df[self.lf_df.index.isin(self.lb_df.index)]
        self.sf_df = self.sf_df[self.sf_df.index.isin(self.lb_df.index)]
        self.ct_df = self.ct_df[self.ct_df.index.isin(self.lb_df.index)]
        self.lb_df = self.lb_df[self.lb_df.index.isin(self.lf_df.index)]
        self.lf_df.fillna(0.0, inplace=True)
        self.sf_df.fillna(0.0, inplace=True)
        self.ct_df.fillna(0.0, inplace=True)
        self.lf_df['CODE'] = self.lb_df['CODE']
        self.sf_df['CODE'] = self.lb_df['CODE']
        self.ct_df['CODE'] = self.lb_df['CODE']

        print(f'Feature total: {len(self.lf_df)}')
        # print(self.lb_df.label.value_counts())
        print(f"Today add {self.direct} count: {(self.lb_df['datetime'] >= datetime.datetime.now().strftime('%Y%m%d 00:00:00')).sum()}")
        
    def _label_encode(self):

        # Encoding instrument_id for embeddings
        self.lb_df['code_encoded'] = self.le.fit_transform(self.lb_df['CODE'].values)
    
    # def _get_folds(self):
    #     skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
    #     for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):
    #         self.lb_df.loc[val_idx, 'fold'] = fold
    #     self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)

    # def _dump_input_param_json(self):
    #     if self.model_name_suff == "":
    #         return
    #     f_sel = {}
    #     # todo: lb_df.CODE有可能遗漏CODE
    #     f_sel['codes'] = self.le.classes_.tolist() # sorted(self.lb_df.CODE.unique().tolist())

    #     f_sel['mean'] = self.lf_df.values.mean(axis=0).tolist()
    #     f_sel['mean'] += (self.sf_df.values.mean(axis=0).tolist())
    #     f_sel['mean'] += (self.ct_df.values.mean(axis=0).tolist())
    #     f_sel['std'] = self.lf_df.values.std(axis=0).tolist()
    #     f_sel['std'] += (self.sf_df.values.std(axis=0).tolist())
    #     f_sel['std'] += (self.ct_df.values.std(axis=0).tolist())

    #     f_sel['lf_len'] = self.lf_df.shape[1]
    #     f_sel['sf_len'] = self.sf_df.shape[1]
    #     f_sel['ct_len'] = self.ct_df.shape[1]

    #     with open(f'{self.data_path}/using_factor.json', 'r') as using_file:
    #         using_factor = json.load(using_file)
    #         f_sel.update(using_factor)
    #     f_sel.update(self.interface_params)
    #     if self.model_name_suff != "":
    #         jfile_name = f"{self.model_name}_{self.model_name_suff}_{self.direct}"
    #     else:
    #         jfile_name = f"{self.model_name}_{self.direct}"
    #     with open(f'{self.model_path}/{jfile_name}.json', 'w') as factor_sel_file:
    #         json.dump(f_sel, factor_sel_file)
                        
    def transform(self):
        self._prepare_data()
        # self._label_encode()
        # self._get_folds()
        # self._dump_input_param_json()
        return self.lb_df, self.lf_df, self.sf_df, self.ct_df

    def get_num_embeddings(self):
        return len(self.lb_df.CODE.unique())    

    def load(self, instruments, start_time=None, end_time=None) -> pd.DataFrame:
        # TODO: ...
        self.portfolios=instruments
        return self.transform()   


class AHFDataLoader(DataLoader):
    '''
    (A)icm (H)istory (F)actor Data Loader

    '''
    def __init__(
           self,
           data_path = '',
           train_codes=[],
       ) -> None:
            self.data_path = data_path
            self.train_codes = train_codes
            # self.lb_df = pd.DataFrame()
            self.lf_df = pd.DataFrame()
            self.sf_df = pd.DataFrame()
            self.mf_df = pd.DataFrame()
            self.ct_df = pd.DataFrame()
            #self.fd = AicmFactorsData(
            #    direct=self.direct, data_path=self.data_path,
            #    sel_lf_names=sel_lf_names, sel_sf_names=sel_sf_names, sel_ct_names=sel_ct_names
            #)
            self.interface_params = {
                'input_dim': 2, # 1: expression call 2: API call
                'code_encoding': 0, # 0:unsing, 1:onehot, 2:embedding
            }
            super().__init__()

    #def config(self, **kwargs):
    #    attr_list = {"direct", "model_name", "model_name_suff", "model_path", "only_trading_code"}
    #    for k, v in kwargs.items():
    #        if k in attr_list:
    #            setattr(self, k, v)

    def set_data(self, lf_df, sf_df, mf_df, ct_df):
        self.lf_df = lf_df
        self.sf_df = sf_df
        self.mf_df = mf_df
        self.ct_df = ct_df

    def clear_data(self):
        self.lf_df = pd.DataFrame()
        self.sf_df = pd.DataFrame()
        self.mf_df = pd.DataFrame()
        self.ct_df = pd.DataFrame()

    def _load_data_from_file(self):
        for year in self.years:
            print(f"Loading {year} data...")
            if os.path.isfile('%s/ffs_lf.%s.parquet'%(self.data_path, year)):
                self.lf_df = pd.concat([self.lf_df, pd.read_parquet('%s/ffs_lf.%s.parquet'%(self.data_path, year), engine='fastparquet')])
            if os.path.isfile('%s/ffs_sf.%s.parquet'%(self.data_path, year)):
                self.sf_df = pd.concat([self.sf_df, pd.read_parquet('%s/ffs_sf.%s.parquet'%(self.data_path, year), engine='fastparquet')])
            if os.path.isfile('%s/ffs_mf.%s.parquet'%(self.data_path, year)):
                self.mf_df = pd.concat([self.mf_df, pd.read_parquet('%s/ffs_mf.%s.parquet'%(self.data_path, year), engine='fastparquet')])
            if os.path.isfile('%s/ffs_ct.%s.parquet'%(self.data_path, year)):
                self.ct_df = pd.concat([self.ct_df, pd.read_parquet('%s/ffs_ct.%s.parquet'%(self.data_path, year), engine='fastparquet')])
        
    def _load(self):
        # self.lf_df, self.sf_df, self.ct_df = self.fd.get_pf_data(self.years)
        pass

    def _prepare_data(self):
        self._load_data_from_file()
        assert not self.sf_df.empty, "load raw data is empty."

        # 清除不需要的数据
        if len(self.train_codes) > 0:
            self.sf_df = self.sf_df[self.sf_df['code'].isin(self.train_codes)]
            self.mf_df = self.mf_df[self.mf_df['code'].isin(self.train_codes)]
            self.lf_df = self.lf_df[self.lf_df['code'].isin(self.train_codes)]
            self.ct_df = self.ct_df[self.ct_df['code'].isin(self.train_codes)]

        if not self.sf_df.empty:
            # todo: 最好是分code处理，在两个code衔接处分组时会发生跨代码的问题
            self.sf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.sf_df['change'] = self.sf_df['change'].astype(np.float32)
            # 将当前的涨跌幅移动后5行，作为当前的label
            self.sf_df['change'] = self.sf_df['change'].shift(-5)
            self.sf_df = self.sf_df[:-5]

        if not self.lf_df.empty:
            self.lf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.lf_df = self.lf_df[:-5]
            # self.lf_df['change'] = self.lf_df['change'].astype(np.float32)
            # self.lf_df['change'] = self.lf_df['change'].shift(-5)
            # self.lf_df = self.lf_df[:-1]
            # self.lf_df['RSI_2'] = self.lf_df['RSI_2'].astype(np.float32)
            # self.lf_df = self.lf_df[self.lf_df['RSI_2'] != 0.0]

        if not self.mf_df.empty:
            self.mf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.mf_df = self.mf_df[:-5]

        if not self.ct_df.empty:
            self.ct_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.ct_df = self.ct_df[:-5]


    def _get_folds(self):
        # skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
        # for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):
        #     self.lb_df.loc[val_idx, 'fold'] = fold
        # self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)
        pass

                        
    def transform(self):
        self._prepare_data()
        # self._label_encode()
        # self._get_folds()
        # self._dump_input_param_json()
        return self.lf_df, self.sf_df, self.mf_df, self.ct_df

    def get_num_embeddings(self):
        return len(self.sf_df.code.unique())    

    def load(self, instruments, start_time=None, end_time=None) -> pd.DataFrame:
        # TODO: ...
        self.years=instruments
        return self.transform()   
        