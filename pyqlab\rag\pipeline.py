"""
通过tdxhq从网络服务器download历史行情数据到本地db文件后
保存数据格式为parquet
从db文件读取历史数据，生成bar token
"""

import os
# import sqlite3
import talib
import pandas as pd
from datetime import datetime
from pyqlab.const import *
from argparse import ArgumentParser
import json
from pyqlab.data.dataset.pipeline import Pipeline

class HdDbToBarTokens:
    """
    用于通过历史行情数据生成训练数据
    主要支持两个周期数据：1. 日数据   2. 5分钟数据
    支持两个市场：1. A股   2. 期货
    数据源来自通达信pytdx导出，保存数据格式为parquet
    将历史行情数据转换为Bar训练数据
    """
    def __init__(self, data_path: str, save_path: str, market: str, period: str, block_name: str, year: int, append: bool=True):
        self.data_path = data_path
        self.save_path = save_path
        self.market = market
        self.period = period
        self.block_name = block_name
        self.year = year
        self.append = append
        self.code_list = []
        self.df = pd.DataFrame()
        self.atr = dict()
        # 注意：要与系统中配置参数一致
        if self.period == 'day':
            self.window = 20
        else:
            self.window = 100
        self.scale = 10
        self.atr_mult = 0.88
        self._read_config()

    def load_data(self):
        path = f'{self.data_path}/store/{self.market}_{self.block_name}_{self.period}.parquet'
        self.df = pd.read_parquet(path)
        self.code_list = self.df['code'].unique()
        print(f'load {path} success, {self.df.shape}')

    def save_data(self, bar_data):
        df = pd.DataFrame(bar_data, columns=['symbol', 'timestamp', 'change', 'entity', 'upline', 'downline'])
        df.to_csv(f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}.tmp.csv', index=False)

        if self.period == 'day' or self.block_name == 'sf':
            if self.period == 'min5' and self.block_name == 'sf':
              df = df[(pd.to_datetime(df['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.time >= pd.to_datetime('09:30:00').time()]
            path = f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}.csv'
            self.process_and_save_data(df, path)
        else:
            # 删除每日时间在00:00:00到06:00:00之间的数据
            print(df.shape)
            if self.period == 'min5':
                df = df[(pd.to_datetime(df['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 9]
            print(df.shape)
            if self.append and self.year != 0:
                path = f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}_{self.year}.csv'
                df = df[pd.to_datetime(df['timestamp'], unit='s').dt.year == self.year]
                self.process_and_save_data(df, path)
            else:
                years = self._get_years(df['timestamp'])
                print(f'years: {years}')
                for year in years:
                    path = f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}_{year}.csv'
                    df_year = df[pd.to_datetime(df['timestamp'], unit='s').dt.year == year]
                    self.process_and_save_data(df_year, path)

    def process_and_save_data(self, df, path):
        if df.empty:
            print(f'save data is empty')
            return
        if self.append and os.path.exists(path):
            print(f'append {path}')
            old_df = pd.read_csv(path, header=0, dtype={'symbol': str, 'timestamp': int, 'bar': int})
            if len(old_df) > 0:
                print("old", old_df.shape, pd.to_datetime(old_df['timestamp'].iloc[-1], unit='s') + pd.Timedelta(hours=8))
                df = df[df['timestamp'] > old_df['timestamp'].iloc[-1]]
                print("new", df.shape)
                if df.empty:
                    print("no new data to append")
                    return
                df = Pipeline.to_bar(df)
                print("new", df.shape, pd.to_datetime(df['timestamp'].iloc[-1], unit='s') + pd.Timedelta(hours=8))
                df = pd.concat([old_df, df], axis=0)
                df = df.drop_duplicates(subset=['symbol', 'timestamp'], keep='last')
                df.sort_values(by=['symbol', 'timestamp'], inplace=True)
                df.reset_index(drop=True, inplace=True)
                print("all", df.shape, pd.to_datetime(df['timestamp'].iloc[-1], unit='s') + pd.Timedelta(hours=8))
                df.to_csv(path, index=False, mode='w')
        else:
            # Process and save data in batches of 10,000 records
            batch_size = 20000
            for i in range(0, len(df), batch_size):
                batch = df[i:i+batch_size]
                batch = Pipeline.to_bar(batch)
                
                if i == 0:
                    # For the first batch, create a new file
                    print(f'new file: {path}')
                    batch.to_csv(path, index=False, mode='w')
                else:
                    # For subsequent batches, append to the existing file
                    batch.to_csv(path, index=False, mode='a', header=False)
                
                print(f'Processed and saved batch {i//batch_size + 1}, records {i} to {min(i+batch_size, len(df))}')
            
        print(f'Save {path} success, total shape: {df.shape}')

    def split_main_to_subset_data(self, period, year):
        path = f'{self.save_path}/bar_fut_main_{period}_{year}.csv'
        if not os.path.exists(path):
            raise FileNotFoundError(f'{path} not exists')
            return
        df = pd.read_csv(path, header=0, dtype={'symbol': str, 'timestamp': int, 'bar': int})
        if df.empty:
            print(f'save data is empty')
            return
        
        blk = ['HOT', 'TOP', 'AAC']
        for b in blk:
            dfb = df[df['symbol'].isin(eval(f'MAIN_{b}_FUT_CODES'))]
            if dfb.empty:
                print(f'no data in MAIN_{b}_FUT_CODES')
                return
            print(f'{dfb.shape} in MAIN_{b}_FUT_CODES')
            dfb.sort_values(by=['symbol', 'timestamp'], inplace=True)
            dfb.reset_index(drop=True, inplace=True)
            path = f'{self.save_path}/bar_fut_{b.lower()}_{period}_{year}.csv'
            dfb.to_csv(path, index=False)

    def _get_years(self, date: pd.Series):
        # 将timestamp转换为datetime格式
        date = pd.to_datetime(date, unit='s')
        return date.dt.year.unique()

    def _read_config(self):
        """
        读取配置文件
        """
        with open(f'{self.data_path}/alphaquant.json', 'r', encoding='gbk') as f:
            config = json.load(f)
            if self.market == 'fut':
                if self.period == 'day':
                    self.window = config['techindex']['fut_drange_atr_period']
                    self.atr_mult = config['techindex']['fut_drange_atr_mult']
                else:
                    self.window = config['techindex']['fut_mrange_atr_period']
                    self.atr_mult = config['techindex']['fut_mrange_atr_mult']
            elif self.market == 'stk':
                if self.period == 'day':
                    self.window = config['techindex']['stk_drange_atr_period']
                    self.atr_mult = config['techindex']['stk_drange_atr_mult']
                else:
                    self.window = config['techindex']['stk_mrange_atr_period']
                    self.atr_mult = config['techindex']['stk_mrange_atr_mult']


    def _calc_atr(self, df):
        """
        通过历史行情数据更新atr表
        """
        # 技术指标ATR
        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=self.window)
        return atr * self.atr_mult

    def encode_to_bar_token(self):
        """
        将历史行情数据转换为Bar训练数据
        """
        bar_data = []
        for code in self.code_list:
            df = self.df[self.df['code']==code]
            df.sort_values(by=['datetime'], inplace=True, ascending=True)
            df.reset_index(drop=True, inplace=True)
            print(f'{code} {df.shape}')
            open = df['open']
            high = df['high']
            low = df['low']
            close = df['close']
            date = df['datetime']
            if self.market == 'fut':
                symbol = code[:len(code)-7]
            else:
                symbol = code
            cur_date = date.iloc[self.window + 1].date()
            atrs = self._calc_atr(df) / self.scale
            assert len(atrs) == len(df), f'atrs length not equal df'
            atr = atrs[self.window + 1]
            for i in range(self.window + 1, len(df)):
                # 期货有主力期货切换，行情数据不连续
                # 切换日期时，前后两日有较大的跳空，需要跳过，否则，数据会失真
                '''
                chg = (close.iloc[i] - close.iloc[i-1])/close.iloc[i-1] * 100
                if abs(chg) > 7:
                    print(f'{date.iloc[i]} {code} 跳空 {chg:.2f}%')
                    i += self.window + 1
                    continue
                '''
                barenc = []
                if date.iloc[i].date() != cur_date:
                    cur_date = date.iloc[i].date()
                    atr = atrs[i]
                if atr == 0:
                    print(f'{date.iloc[i]} {code} atr is 0')
                    continue
                barenc.append(symbol)
                # 日期 to timestamp
                barenc.append(int(date.iloc[i].timestamp()))
                # 涨跌
                barenc.append(int((close.iloc[i] - close.iloc[i-1])/atr))
                # 实体
                barenc.append(int((close.iloc[i] - open.iloc[i])/atr))

                if close.iloc[i] > open.iloc[i]: # 阳线
                    # 上影线
                    barenc.append(int((high.iloc[i] - close.iloc[i])/atr))
                    # 下影线
                    barenc.append(int((open.iloc[i] - low.iloc[i])/atr))
                else: # 阴线
                    # 上影线
                    barenc.append(int((high.iloc[i] - open.iloc[i])/atr))
                    # 下影线
                    barenc.append(int((close.iloc[i] - low.iloc[i])/atr))

                bar_data.append(barenc)
        return bar_data
    
    def check_data(self):
        """
        检查数据是否存在问题
        """
        for code in self.code_list:
            df = self.df[self.df['code']==code]
            df.sort_values(by=['datetime'], inplace=True, ascending=True)
            df.reset_index(drop=True, inplace=True)
            # 通过datetime检查数据是否有重复或缺失
            if df['datetime'].duplicated().any():
                print(f"代码 {code} 存在重复的datetime")
            if df['datetime'].isnull().any():
                print(f"代码 {code} 存在缺失的datetime")
            # 检查datetime是否连续
            if not df['datetime'].is_monotonic_increasing:
                print(f"代码 {code} 的datetime不是单调递增的")
            # 检查datetime是否有前后间隔相差12天以上的，并打印出来  
            if (df['datetime'].diff().dt.days > 12).any():
                print(f"代码 {code} 存在前后间隔超过12天的情况")
                print(df[df['datetime'].diff().dt.days > 12])


if __name__ == '__main__':
    """通过本地第三方(通达信pytdx)历史行情数据生成训练数据"""
    parser = ArgumentParser()
    parser.add_argument('--data_path', type=str, default='d:/RoboQuant2', help='data path')
    parser.add_argument('--save_path', type=str, default='f:/featdata/barenc/db', help='data path')
    parser.add_argument('--market', type=str, default='fut', choices=['fut', 'stk'], help='market')
    parser.add_argument('--period', type=str, default='min5', choices=['day', 'min5'], help='period')
    parser.add_argument('--block_name', type=str, default='sf', choices=['main', 'sf', 'hs300', 'zz500', 'zz1000'])
    parser.add_argument('--year', type=int, default=0, help='year to append, 0 is all')
    parser.add_argument('--append', type=bool, default=False, help='append')
    args = parser.parse_args()
    print(f'args: {args}')
    hd = HdDbToBarTokens(
        args.data_path,
        args.save_path,
        args.market,
        args.period,
        args.block_name,
        args.year,
        args.append
    )
    # hd.load_data()
    # # hd.check_data()
    # bar_data = hd.encode_to_bar_token() 
    # hd.save_data(bar_data)
    hd.split_main_to_subset_data('min5', 2021)
