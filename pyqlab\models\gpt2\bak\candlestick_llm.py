"""
Candlestick LLM Model

基于LLM的K线预测模型
"""

import math
import inspect
from dataclasses import dataclass
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import TransformerEncoder, TransformerEncoderLayer
from typing import List, Dict, Tuple, Optional, Union, Any

class LayerNorm(nn.Module):
    """带有可选偏置的LayerNorm"""
    def __init__(self, ndim, bias=True):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(ndim))
        self.bias = nn.Parameter(torch.zeros(ndim)) if bias else None

    def forward(self, input):
        return F.layer_norm(input, self.weight.shape, self.weight, self.bias, 1e-5)

class CausalSelfAttention(nn.Module):
    """因果自注意力层"""
    def __init__(self, d_model, n_head, block_size, dropout=0.1, bias=True):
        super().__init__()
        assert d_model % n_head == 0
        # key, query, value投影
        self.c_attn = nn.Linear(d_model, 3 * d_model, bias=bias)
        # 输出投影
        self.c_proj = nn.Linear(d_model, d_model, bias=bias)
        # 正则化
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.n_head = n_head
        self.d_model = d_model
        self.dropout = dropout
        # 检查是否支持Flash Attention
        self.flash = hasattr(torch.nn.functional, 'scaled_dot_product_attention')
        if not self.flash:
            print("警告: 使用较慢的注意力机制。Flash Attention需要PyTorch >= 2.0")
            # 因果掩码
            self.register_buffer("bias", torch.tril(torch.ones(block_size, block_size))
                                      .view(1, 1, block_size, block_size))

    def forward(self, x):
        B, T, C = x.size() # 批大小，序列长度，嵌入维度

        # 计算query, key, values
        q, k, v  = self.c_attn(x).split(self.d_model, dim=2)
        k = k.view(B, T, self.n_head, C // self.n_head).transpose(1, 2) # (B, nh, T, hs)
        q = q.view(B, T, self.n_head, C // self.n_head).transpose(1, 2) # (B, nh, T, hs)
        v = v.view(B, T, self.n_head, C // self.n_head).transpose(1, 2) # (B, nh, T, hs)

        # 因果自注意力
        if self.flash:
            # 使用Flash Attention
            y = torch.nn.functional.scaled_dot_product_attention(
                q, k, v, attn_mask=None, dropout_p=self.dropout if self.training else 0, is_causal=True
            )
        else:
            # 手动实现注意力
            att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
            att = att.masked_fill(self.bias[:,:,:T,:T] == 0, float('-inf'))
            att = F.softmax(att, dim=-1)
            att = self.attn_dropout(att)
            y = att @ v # (B, nh, T, T) x (B, nh, T, hs) -> (B, nh, T, hs)
        y = y.transpose(1, 2).contiguous().view(B, T, C) # 重组所有头的输出

        # 输出投影
        y = self.resid_dropout(self.c_proj(y))
        return y

class MLP(nn.Module):
    """多层感知机"""
    def __init__(self, d_model, bias=True, dropout=0.1):
        super().__init__()
        self.c_fc    = nn.Linear(d_model, 4 * d_model, bias=bias)
        self.gelu    = nn.GELU()
        self.c_proj  = nn.Linear(4 * d_model, d_model, bias=bias)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        x = self.c_fc(x)
        x = self.gelu(x)
        x = self.c_proj(x)
        x = self.dropout(x)
        return x

class Block(nn.Module):
    """Transformer块"""
    def __init__(self, block_size, n_head, d_model, dropout=0.1, bias=True):
        super().__init__()
        self.ln_1 = LayerNorm(d_model, bias=bias)
        self.attn = CausalSelfAttention(d_model, n_head, block_size, dropout, bias)
        self.ln_2 = LayerNorm(d_model, bias=bias)
        self.mlp = MLP(d_model, bias, dropout)

    def forward(self, x):
        x = x + self.attn(self.ln_1(x))
        x = x + self.mlp(self.ln_2(x))
        return x

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, max_len=5000):
        super().__init__()

        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)

        self.register_buffer('pe', pe)

    def forward(self, x):
        """
        Args:
            x: [batch_size, seq_len, d_model]
        """
        return self.pe[:, :x.size(1)]

class TimeFeatureEmbedding(nn.Module):
    """时间特征嵌入"""
    def __init__(self, d_model, n_time_features=5):
        super().__init__()
        self.time_proj = nn.Linear(n_time_features, d_model)

    def forward(self, time_features):
        """
        Args:
            time_features: [batch_size, seq_len, n_time_features]
        """
        return self.time_proj(time_features)

class CandlestickLLM(nn.Module):
    """基于LLM的K线预测模型"""
    def __init__(self,
                 vocab_size: int,
                 code_size: int,
                 block_size: int = 30,
                 n_layer: int = 12,
                 n_head: int = 12,
                 d_model: int = 128,
                 dropout: float = 0.1,
                 bias: bool = True,
                 use_time_features: bool = True,
                 n_time_features: int = 5):
        """
        初始化K线LLM模型

        Args:
            vocab_size: 词汇表大小
            code_size: 证券代码数量
            block_size: 最大序列长度
            n_layer: Transformer层数
            n_head: 注意力头数
            d_model: 模型维度
            dropout: Dropout比例
            bias: 是否使用偏置
            use_time_features: 是否使用时间特征
            n_time_features: 时间特征数量
        """
        super().__init__()
        self.block_size = block_size
        self.vocab_size = vocab_size
        self.code_size = code_size
        self.use_time_features = use_time_features
        self.d_model = d_model
        self.n_head = n_head
        self.n_layer = n_layer

        # 各种嵌入
        self.token_embedding = nn.Embedding(vocab_size, d_model)
        self.code_embedding = nn.Embedding(code_size, d_model)
        self.position_encoding = PositionalEncoding(d_model, block_size)

        if use_time_features:
            self.time_embedding = TimeFeatureEmbedding(d_model, n_time_features)

        self.dropout = nn.Dropout(dropout)

        # Transformer块
        self.blocks = nn.ModuleList([
            Block(block_size, n_head, d_model, dropout, bias)
            for _ in range(n_layer)
        ])

        # 最终层归一化
        self.ln_f = LayerNorm(d_model, bias=bias)

        # 语言模型头
        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)

        # 权重绑定
        self.token_embedding.weight = self.lm_head.weight

        # 初始化权重
        self.apply(self._init_weights)

        # 对残差投影应用特殊的缩放初始化
        for pn, p in self.named_parameters():
            if pn.endswith('c_proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        print(f"参数数量: {self.get_num_params()/1e6:.2f}M")

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)

    def get_num_params(self, non_embedding=False):
        """获取参数数量"""
        n_params = sum(p.numel() for p in self.parameters())
        if non_embedding:
            n_params -= self.token_embedding.weight.numel()
            n_params -= self.code_embedding.weight.numel()
        return n_params

    def get_model_info(self):
        """获取模型信息"""
        return {
            'vocab_size': self.vocab_size,
            'code_size': self.code_size,
            'block_size': self.block_size,
            'n_layer': self.n_layer,
            'n_head': self.n_head,
            'd_model': self.d_model,
            'use_time_features': self.use_time_features,
            'total_params': self.get_num_params(),
            'non_embedding_params': self.get_num_params(non_embedding=True)
        }

    def forward(self, input_tokens, code_ids, time_features=None, targets=None,
              direction_targets=None, volatility_targets=None, volume_targets=None, **kwargs):
        """
        前向传播

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            targets: 目标token序列 [batch_size, seq_len]
            direction_targets: 方向预测目标（仅高级模型使用）
            volatility_targets: 波动性预测目标（仅高级模型使用）
            volume_targets: 交易量预测目标（仅高级模型使用）
            **kwargs: 其他参数（用于兼容性）

        Returns:
            logits: 输出logits [batch_size, seq_len, vocab_size]
            loss: 损失值（如果提供了targets）
        """
        batch_size, seq_len = input_tokens.size()
        # assert seq_len <= self.block_size, f"输入序列长度{seq_len}超过了最大长度{self.block_size}"

        # 获取各种嵌入
        token_emb = self.token_embedding(input_tokens)  # [batch_size, seq_len, d_model]

        # 扩展code_ids并获取嵌入
        code_ids = code_ids.unsqueeze(1).expand(-1, seq_len)  # [batch_size, seq_len]
        code_emb = self.code_embedding(code_ids)  # [batch_size, seq_len, d_model]

        # 位置编码
        pos_emb = self.position_encoding(token_emb)  # [1, seq_len, d_model]

        # 组合嵌入
        x = token_emb + code_emb + pos_emb

        # 添加时间特征
        if self.use_time_features and time_features is not None:
            time_emb = self.time_embedding(time_features)
            x = x + time_emb

        # x = self.dropout(x)

        # 通过Transformer块
        for block in self.blocks:
            x = block(x)

        x = self.ln_f(x)

        # 计算logits
        logits = self.lm_head(x)  # [batch_size, seq_len, vocab_size]

        # 计算损失
        loss = None
        if targets is not None:
            # 标准交叉熵损失
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
        return logits, loss

    @torch.no_grad()
    def generate(self,
                input_tokens,
                code_ids,
                time_features=None,
                max_new_tokens=10,
                temperature=1.0,
                top_k=None,
                **kwargs):
        """
        生成新的token序列

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            max_new_tokens: 生成的最大新token数量
            temperature: 温度参数，控制采样的随机性
            top_k: 只考虑概率最高的前k个token
            **kwargs: 其他参数（用于兼容性）

        Returns:
            生成的token序列 [batch_size, seq_len + max_new_tokens]
        """
        try:
            # 打印输入参数
            print(f"\n生成参数:")
            print(f"input_tokens shape: {input_tokens.shape}")
            print(f"code_ids shape: {code_ids.shape}")
            if time_features is not None:
                print(f"time_features shape: {time_features.shape}")
            print(f"max_new_tokens: {max_new_tokens}")
            print(f"temperature: {temperature}")
            print(f"top_k: {top_k}")

            # 打印输入token的统计信息
            print(f"\n输入token统计:")
            unique_tokens = torch.unique(input_tokens)
            print(f"唯一token数量: {len(unique_tokens)}")
            print(f"最小token值: {input_tokens.min().item()}")
            print(f"最大token值: {input_tokens.max().item()}")
            print(f"前10个token: {input_tokens[0, :10].tolist()}")

            batch_size, seq_len = input_tokens.size()

            # 检查输入有效性
            if seq_len == 0:
                print("警告: 输入序列长度为0，无法生成预测")
                # 返回一个全零的序列
                return torch.zeros((batch_size, max_new_tokens), dtype=torch.int32, device=input_tokens.device)

            if batch_size == 0:
                print("警告: 批次大小为0，无法生成预测")
                # 返回一个空张量
                return torch.zeros((0, seq_len + max_new_tokens), dtype=torch.int32, device=input_tokens.device)

            # 复制输入，以便我们可以追加生成的token
            tokens = input_tokens.clone()

            # 如果提供了时间特征，我们需要扩展它以容纳新生成的token
            if self.use_time_features and time_features is not None:
                # 假设时间特征的最后一个时间步可以重复
                last_time_feature = time_features[:, -1:, :]
                extended_time_features = time_features
            else:
                extended_time_features = None

            # 打印生成过程
            print(f"\n开始生成 {max_new_tokens} 个新token:")

            # 存储生成的token和概率
            generated_tokens = []
            token_probs = []

            for i in range(max_new_tokens):
                # 如果序列太长，截断它
                if tokens.size(1) > self.block_size:
                    tokens = tokens[:, -self.block_size:]
                    if extended_time_features is not None:
                        extended_time_features = extended_time_features[:, -self.block_size:, :]

                # 前向传播
                try:
                    if extended_time_features is not None:
                        logits, _ = self(tokens, code_ids, extended_time_features)
                    else:
                        logits, _ = self(tokens, code_ids)
                except Exception as e:
                    print(f"前向传播出错: {str(e)}")
                    # 返回已生成的token
                    return tokens

                # 只关注最后一个时间步的logits
                logits = logits[:, -1, :] / max(temperature, 1e-6)  # 确保温度不为零

                # 打印logits统计信息
                if i == 0:  # 只打印第一次迭代的信息，避免输出过多
                    print(f"\nLogits统计 (第1次迭代):")
                    print(f"Logits形状: {logits.shape}")
                    print(f"Logits最小值: {logits.min().item()}")
                    print(f"Logits最大值: {logits.max().item()}")
                    print(f"Logits平均值: {logits.mean().item()}")
                    print(f"Logits是否包含NaN: {torch.isnan(logits).any().item()}")
                    print(f"Logits是否包含Inf: {torch.isinf(logits).any().item()}")

                # 可选地只保留top-k个logits
                if top_k is not None and top_k > 0:
                    v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                    logits[logits < v[:, [-1]]] = -float('Inf')

                # 应用softmax得到概率
                probs = F.softmax(logits, dim=-1)

                # 检查概率分布
                if torch.isnan(probs).any() or torch.isinf(probs).any():
                    print(f"警告: 概率分布包含NaN或Inf值，使用均匀分布")
                    probs = torch.ones_like(probs) / probs.size(-1)

                # 打印概率统计信息
                if i == 0:  # 只打印第一次迭代的信息
                    print(f"\n概率统计 (第1次迭代):")
                    print(f"概率和: {probs.sum().item()}")
                    print(f"最大概率: {probs.max().item()}")
                    print(f"最大概率对应的token: {probs.argmax().item()}")

                    # 打印前5个最高概率的token
                    top_probs, top_indices = torch.topk(probs, min(5, probs.size(-1)))
                    print(f"前5个最高概率的token:")
                    for j in range(len(top_indices[0])):
                        print(f"  Token {top_indices[0][j].item()}: 概率 {top_probs[0][j].item():.4f}")

                # 采样下一个token
                try:
                    next_token = torch.multinomial(probs, num_samples=1)
                except Exception as e:
                    print(f"采样出错: {str(e)}")
                    # 使用最高概率的token
                    next_token = torch.argmax(probs, dim=-1, keepdim=True)

                # 获取采样token的概率
                token_prob = probs[0, next_token[0, 0]].item()
                token_probs.append(token_prob)
                generated_tokens.append(next_token.item())

                # 打印生成的token
                if i < 5 or i >= max_new_tokens - 5:  # 只打印前5个和后5个
                    print(f"生成第{i+1}个token: {next_token.item()} (概率: {token_prob:.4f})")

                # 追加到序列
                tokens = torch.cat((tokens, next_token), dim=1)

                # 如果有时间特征，也扩展它
                if extended_time_features is not None:
                    extended_time_features = torch.cat((extended_time_features, last_time_feature), dim=1)

            # 打印生成的token统计信息
            print(f"\n生成的token统计:")
            print(f"生成的token: {generated_tokens}")
            print(f"token概率: {[f'{p:.4f}' for p in token_probs]}")
            print(f"平均概率: {sum(token_probs)/len(token_probs):.4f}")

            print(f"生成完成，最终序列长度: {tokens.size(1)}")
            return tokens

        except Exception as e:
            import traceback
            print(f"生成过程出错: {str(e)}")
            traceback.print_exc()
            # 返回输入序列
            return input_tokens

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        """配置优化器"""
        # 获取所有参数
        param_dict = {pn: p for pn, p in self.named_parameters() if p.requires_grad}

        # 将参数分为需要权重衰减的和不需要的
        decay_params = [param for _, param in param_dict.items() if param.dim() >= 2]
        nodecay_params = [param for _, param in param_dict.items() if param.dim() < 2]

        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]

        # 打印参数信息
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"需要权重衰减的参数: {len(decay_params)}个，共{num_decay_params:,}个参数")
        print(f"不需要权重衰减的参数: {len(nodecay_params)}个，共{num_nodecay_params:,}个参数")

        # 创建AdamW优化器
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"使用fused AdamW: {use_fused}")

        return optimizer

    def to_onnx(self, file_path, example_inputs, **kwargs):
        """
        将模型导出为ONNX格式

        Args:
            file_path: 保存路径
            example_inputs: 示例输入，格式为(input_tokens, code_ids, time_features, targets)
                           其中targets是可选的
            **kwargs: 传递给torch.onnx.export的其他参数

        Returns:
            bool: 是否成功导出
        """
        try:
            # 确保模型处于评估模式
            self.eval()

            # 解包示例输入
            if len(example_inputs) == 4:
                input_tokens, code_ids, time_features, _ = example_inputs
            elif len(example_inputs) == 3:
                input_tokens, code_ids, time_features = example_inputs
            else:
                raise ValueError("示例输入格式不正确，应为(input_tokens, code_ids, time_features[, targets])")

            # 创建一个包装模型类，只返回logits
            class ModelWrapper(torch.nn.Module):
                def __init__(self, model):
                    super().__init__()
                    self.model = model

                def forward(self, input_tokens, code_ids, time_features):
                    # 确保code_ids是一维的
                    if len(code_ids.shape) > 1 and code_ids.shape[1] == 1:
                        code_ids = code_ids.squeeze(1)
                    logits, _ = self.model(input_tokens, code_ids, time_features)
                    return logits

            # 创建包装模型实例
            wrapped_model = ModelWrapper(self)

            # 设置默认参数
            export_params = kwargs.pop('export_params', True)
            opset_version = kwargs.pop('opset_version', 12)
            do_constant_folding = kwargs.pop('do_constant_folding', True)
            input_names = kwargs.pop('input_names', ['input_tokens', 'code_ids', 'time_features'])
            output_names = kwargs.pop('output_names', ['logits'])
            dynamic_axes = kwargs.pop('dynamic_axes', {
                'input_tokens': {0: 'batch_size', 1: 'sequence_length'},
                'code_ids': {0: 'batch_size'},
                'time_features': {0: 'batch_size', 1: 'sequence_length'},
                'logits': {0: 'batch_size', 1: 'sequence_length'}
            })

            # 导出模型
            torch.onnx.export(
                wrapped_model,
                (input_tokens, code_ids, time_features),
                file_path,
                export_params=export_params,
                opset_version=opset_version,
                do_constant_folding=do_constant_folding,
                input_names=input_names,
                output_names=output_names,
                dynamic_axes=dynamic_axes,
                **kwargs
            )

            print(f"模型已成功导出为ONNX格式: {file_path}")
            return True
        except Exception as e:
            print(f"导出ONNX模型时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

