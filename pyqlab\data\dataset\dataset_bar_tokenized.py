"""
使用BarTokenizer的高质量tokens数据集

这个数据集类专门用于处理BarTokenizer生成的高质量tokens，
解决样本不均衡问题，提供给BarGpt4模型训练使用。
"""

import torch
import pandas as pd
import numpy as np
from torch.utils.data import Dataset
from typing import Dict, List, Tuple, Optional
import os
import pickle
from pathlib import Path

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
from pyqlab.utils.config import CfgNode as CN


class BarTokenizedDataset(Dataset):
    """
    使用BarTokenizer处理的K线数据集

    特点：
    1. 使用BarTokenizer生成高质量tokens
    2. 自动处理token分布不平衡问题
    3. 支持多种映射策略和平衡方法
    4. 提供详细的分布分析
    """

    @staticmethod
    def get_default_config():
        C = CN()
        # 数据配置
        C.data_path = 'f:/hqdata'
        C.market = 'fut'
        C.block_name = 'top'
        C.period = 'min1'
        C.start_year = 2025
        C.end_year = 2025
        C.start_date = ""
        C.end_date = ""
        C.block_size = 30
        C.timeenc = 1  # 1 for timeF, 0 for others

        # BarTokenizer配置
        C.tokenizer = CN()
        C.tokenizer.mapping_strategy = 'quantile'  # 'linear', 'quantile', 'adaptive'
        C.tokenizer.balancing_strategy = 'frequency'  # 'frequency', 'none'
        C.tokenizer.n_bins = 100
        C.tokenizer.features = ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        C.tokenizer.combination_method = 'independent'  # 'independent', 'hash', 'hierarchical'
        C.tokenizer.atr_period = 14

        # 数据平衡配置
        C.balance = CN()
        C.balance.max_token_frequency = 0.1  # 单个token最大频率
        C.balance.min_samples_per_token = 5  # 每个token最少样本数
        C.balance.gini_threshold = 0.7  # 基尼系数阈值

        return C

    def __init__(self, config: CN, data_file: str = None, tokenizer_path: str = None):
        """
        初始化数据集

        Args:
            config: 配置对象
            data_file: 数据文件路径（parquet格式）
            tokenizer_path: 预训练的tokenizer路径
        """
        self.config = config
        self.data_file = data_file
        self.tokenizer_path = tokenizer_path

        # 初始化tokenizer
        self.tokenizer = None
        self.is_tokenizer_fitted = False

        # 数据存储
        self.raw_data = None
        self.processed_data = None
        self.tokens = None
        self.time_features = None
        self.code_mapping = {}

        # 统计信息
        self.vocab_size = 0
        self.code_size = 0
        self.distribution_stats = {}

        # 加载数据
        if data_file:
            self._load_data()
            self._setup_tokenizer()
            self._process_data()

    def _load_data(self):
        """加载原始数据"""
        print(f"正在加载数据: {self.data_file}")

        if not os.path.exists(self.data_file):
            raise FileNotFoundError(f"数据文件不存在: {self.data_file}")

        # 根据文件扩展名选择读取方式
        if self.data_file.endswith('.parquet'):
            self.raw_data = pd.read_parquet(self.data_file)
        elif self.data_file.endswith('.csv'):
            self.raw_data = pd.read_csv(self.data_file)
        else:
            raise ValueError(f"不支持的文件格式: {self.data_file}，仅支持 .parquet 和 .csv 文件")
        print(f"加载数据完成，共 {len(self.raw_data)} 条记录")

        # 检查必要的列
        required_cols = ['datetime', 'code', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in self.raw_data.columns]
        if missing_cols:
            raise ValueError(f"缺少必要的列: {missing_cols}")

        # 过滤时间
        if self.config.start_date and self.config.end_date:
            self.raw_data = self.raw_data[(self.raw_data['datetime'] >= self.config.start_date) & (self.raw_data['datetime'] <= self.config.end_date)]

        # 按时间排序
        self.raw_data = self.raw_data.sort_values(['code', 'datetime']).reset_index(drop=True)

        # 创建代码映射
        unique_codes = sorted(self.raw_data['code'].unique())
        self.code_mapping = {code: idx for idx, code in enumerate(unique_codes)}
        self.code_size = len(unique_codes)

        print(f"发现 {self.code_size} 个不同的证券代码")

    def _setup_tokenizer(self):
        """设置tokenizer"""
        if self.tokenizer_path and os.path.exists(self.tokenizer_path):
            # 加载预训练的tokenizer
            print(f"加载预训练tokenizer: {self.tokenizer_path}")
            self.tokenizer = BarTokenizer.load_model(self.tokenizer_path)
            self.is_tokenizer_fitted = True
        else:
            # 创建新的tokenizer
            print("创建新的BarTokenizer")
            self.tokenizer = BarTokenizer(
                mapping_strategy=self.config.tokenizer.mapping_strategy,
                balancing_strategy=self.config.tokenizer.balancing_strategy,
                n_bins=self.config.tokenizer.n_bins,
                features=self.config.tokenizer.features,
                combination_method=self.config.tokenizer.combination_method,
                atr_period=self.config.tokenizer.atr_period
            )
            self.is_tokenizer_fitted = False

    def _process_data(self):
        """处理数据并生成tokens"""
        print("开始处理数据...")

        processed_rows = []

        # 按证券代码分组处理
        for code, group_data in self.raw_data.groupby('code'):
            print(f"处理证券代码: {code}")

            # 确保数据按时间排序
            group_data = group_data.sort_values('datetime').reset_index(drop=True)

            # 如果数据量不足，跳过
            if len(group_data) < self.config.block_size + 1:
                print(f"  跳过 {code}：数据量不足 ({len(group_data)} < {self.config.block_size + 1})")
                continue

            # 拟合tokenizer（仅在第一次处理时）
            if not self.is_tokenizer_fitted:
                print(f"  使用 {code} 的数据拟合tokenizer...")
                self.tokenizer.fit(group_data)
                self.is_tokenizer_fitted = True
                self.vocab_size = self.tokenizer.get_vocab_size()
                print(f"  Tokenizer拟合完成，词汇表大小: {self.vocab_size}")

            # 生成tokens
            tokens = self.tokenizer.transform(group_data)

            # 生成时间特征
            time_features = self._extract_time_features(group_data)

            # 创建滑动窗口样本
            code_idx = self.code_mapping[code]
            for i in range(len(tokens) - self.config.block_size):
                sample = {
                    'code': code_idx,
                    'tokens': tokens[i:i + self.config.block_size + 1],
                    'time_features': time_features[i:i + self.config.block_size + 1],
                    'datetime': group_data.iloc[i + self.config.block_size]['datetime']
                }
                processed_rows.append(sample)

        # 转换为DataFrame
        if not processed_rows:
            print("⚠️  没有生成任何样本，请检查数据文件和配置")
            self.processed_data = pd.DataFrame(columns=['code', 'tokens', 'time_features', 'datetime'])
            return

        self.processed_data = pd.DataFrame(processed_rows)
        print(f"数据处理完成，生成 {len(self.processed_data)} 个样本")

        # 分析token分布
        self._analyze_token_distribution()

        # 应用数据平衡策略
        self._apply_data_balancing()

    def _extract_time_features(self, df: pd.DataFrame) -> np.ndarray:
        """提取时间特征"""
        dt = pd.to_datetime(df['datetime'])

        if self.config.timeenc == 1:  # timeF
            # 提取5维时间特征，与原始模型兼容
            time_features = np.column_stack([
                dt.dt.minute / 60.0,      # 分钟 [0, 1)
                dt.dt.hour / 24.0,        # 小时 [0, 1)
                dt.dt.weekday / 7.0,      # 星期几 [0, 1)
                dt.dt.month / 12.0,       # 月份 [0, 1)
                dt.dt.year / 2030.0       # 年份（归一化）
            ])
        else:
            # 简单的3维时间特征
            time_features = np.column_stack([
                dt.dt.hour / 24.0,
                dt.dt.weekday / 7.0,
                dt.dt.month / 12.0
            ])

        return time_features.astype(np.float32)

    def _analyze_token_distribution(self):
        """分析token分布"""
        print("分析token分布...")

        # 检查是否有数据
        if len(self.processed_data) == 0:
            print("⚠️  没有数据可供分析")
            self.distribution_stats = {
                'gini_coefficient': 0.0,
                'normalized_entropy': 0.0,
                'coefficient_of_variation': 0.0
            }
            return

        # 收集所有tokens
        all_tokens = []
        for tokens in self.processed_data['tokens']:
            all_tokens.extend(tokens[:-1])  # 排除最后一个token（作为target）

        if not all_tokens:
            print("⚠️  没有找到任何tokens")
            self.distribution_stats = {
                'gini_coefficient': 0.0,
                'normalized_entropy': 0.0,
                'coefficient_of_variation': 0.0
            }
            return

        all_tokens = np.array(all_tokens)

        # 使用tokenizer的分析功能
        self.distribution_stats = self.tokenizer.analyze_balance(all_tokens)

        print(f"Token分布分析结果:")
        print(f"  基尼系数: {self.distribution_stats['gini_coefficient']:.4f}")
        print(f"  标准化熵: {self.distribution_stats['normalized_entropy']:.4f}")
        print(f"  变异系数: {self.distribution_stats['coefficient_of_variation']:.4f}")
        print(f"  唯一tokens数量: {len(np.unique(all_tokens))}")

        # 检查是否需要平衡
        if self.distribution_stats['gini_coefficient'] > self.config.balance.gini_threshold:
            print(f"⚠️  检测到严重的token分布不平衡（基尼系数: {self.distribution_stats['gini_coefficient']:.4f}）")
        else:
            print(f"✅ Token分布相对平衡")

    def _apply_data_balancing(self):
        """应用数据平衡策略"""
        # 检查是否有数据
        if len(self.processed_data) == 0:
            print("⚠️  没有数据可供平衡")
            return

        if self.distribution_stats['gini_coefficient'] <= self.config.balance.gini_threshold:
            print("Token分布已经平衡，跳过数据平衡步骤")
            return

        print("应用数据平衡策略...")

        # 统计每个样本的token分布
        token_counts = {}
        sample_tokens = []

        for idx, tokens in enumerate(self.processed_data['tokens']):
            # 使用输入tokens（排除target）创建token字符串
            input_tokens = tokens[:-1]
            token_str = '|'.join(map(str, input_tokens))

            if token_str not in token_counts:
                token_counts[token_str] = []
            token_counts[token_str].append(idx)
            sample_tokens.append(token_str)

        # 计算保留概率
        total_samples = len(self.processed_data)
        max_samples_per_token = int(total_samples * self.config.balance.max_token_frequency)

        keep_indices = []
        np.random.seed(42)  # 确保可重现

        for token_str, indices in token_counts.items():
            if len(indices) <= max_samples_per_token:
                # 保留所有样本
                keep_indices.extend(indices)
            else:
                # 随机采样
                sampled_indices = np.random.choice(
                    indices,
                    size=max_samples_per_token,
                    replace=False
                )
                keep_indices.extend(sampled_indices)

        # 过滤数据
        original_size = len(self.processed_data)
        self.processed_data = self.processed_data.iloc[keep_indices].reset_index(drop=True)

        print(f"数据平衡完成:")
        print(f"  原始样本数: {original_size}")
        print(f"  平衡后样本数: {len(self.processed_data)}")
        print(f"  保留比例: {len(self.processed_data) / original_size:.2%}")

        # 重新分析分布
        self._analyze_token_distribution()

    def save_tokenizer(self, save_path: str):
        """保存tokenizer"""
        if self.tokenizer and self.is_tokenizer_fitted:
            self.tokenizer.save_model(save_path)
            print(f"Tokenizer已保存到: {save_path}")
        else:
            print("Tokenizer未拟合，无法保存")

    def get_vocab_size(self) -> int:
        """获取词汇表大小"""
        return self.vocab_size

    def get_code_size(self) -> int:
        """获取代码数量"""
        return self.code_size

    def get_distribution_stats(self) -> Dict:
        """获取分布统计信息"""
        return self.distribution_stats

    def __len__(self) -> int:
        """数据集大小"""
        if self.processed_data is None:
            return 0
        return len(self.processed_data)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        获取单个样本

        Returns:
            code: 证券代码 (1,)
            x: 输入tokens (block_size,)
            x_mark: 时间特征 (block_size, n_time_features)
            y: 目标token (block_size,)
        """
        if self.processed_data is None:
            raise RuntimeError("数据未处理，请先调用_process_data()")

        sample = self.processed_data.iloc[idx]

        # 提取数据
        code = sample['code']
        tokens = sample['tokens']
        time_features = sample['time_features']

        # 分离输入和目标
        x_tokens = tokens[:-1]  # 输入序列
        y_tokens = tokens[1:]   # 目标序列（向右偏移1位）
        x_time = time_features[:-1]  # 输入时间特征

        # 确保token值在有效范围内
        x_tokens = np.clip(x_tokens, 0, self.vocab_size - 1)
        y_tokens = np.clip(y_tokens, 0, self.vocab_size - 1)

        # 转换为tensor
        code_tensor = torch.tensor([code] * len(x_tokens), dtype=torch.long)
        x_tensor = torch.tensor(x_tokens, dtype=torch.long)
        x_mark_tensor = torch.tensor(x_time, dtype=torch.float32)
        y_tensor = torch.tensor(y_tokens, dtype=torch.long)

        return code_tensor, x_tensor, x_mark_tensor, y_tensor

    def get_sample_info(self, idx: int) -> Dict:
        """获取样本详细信息（用于调试）"""
        if self.processed_data is None:
            return {}

        sample = self.processed_data.iloc[idx]

        # 反向查找代码名称
        code_name = None
        for name, code_idx in self.code_mapping.items():
            if code_idx == sample['code']:
                code_name = name
                break

        return {
            'index': idx,
            'code_name': code_name,
            'code_idx': sample['code'],
            'datetime': sample['datetime'],
            'tokens': sample['tokens'],
            'token_sequence_length': len(sample['tokens']),
            'time_features_shape': sample['time_features'].shape
        }

    def print_sample_info(self, idx: int):
        """打印样本信息"""
        info = self.get_sample_info(idx)
        print(f"样本 {idx} 信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")

    def get_class_weights(self) -> torch.Tensor:
        """
        计算类别权重，用于处理不平衡数据

        Returns:
            class_weights: 形状为 (vocab_size,) 的权重张量
        """
        if self.processed_data is None:
            return None

        # 收集所有目标tokens
        all_targets = []
        for tokens in self.processed_data['tokens']:
            all_targets.extend(tokens[1:])  # 目标序列

        all_targets = np.array(all_targets)

        # 计算每个类别的频率
        unique_tokens, counts = np.unique(all_targets, return_counts=True)

        # 计算权重（逆频率）
        total_samples = len(all_targets)
        weights = np.ones(self.vocab_size, dtype=np.float32)

        for token, count in zip(unique_tokens, counts):
            if token < self.vocab_size:
                weights[token] = total_samples / (len(unique_tokens) * count)

        return torch.tensor(weights, dtype=torch.float32)
