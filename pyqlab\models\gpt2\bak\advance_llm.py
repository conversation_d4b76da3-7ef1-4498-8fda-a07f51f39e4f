import torch
import torch.nn as nn
from torch.nn import functional as F
import numpy as np
import pandas as pd

class MultiScaleTimeEmbedding(nn.Module):
    """多尺度时间特征嵌入"""
    def __init__(self, d_model):
        super().__init__()
        self.day_embed = nn.Linear(5, d_model // 4)  # 日内特征
        self.week_embed = nn.Linear(2, d_model // 4)  # 周特征
        self.month_embed = nn.Linear(2, d_model // 4)  # 月特征
        self.season_embed = nn.Linear(2, d_model // 4)  # 季节特征
        self.proj = nn.Linear(d_model, d_model)

    def forward(self, dt):
        # 提取各种时间尺度特征
        day_feats = torch.tensor([
            dt.hour / 23.0, dt.minute / 59.0,
            dt.second / 59.0, dt.microsecond / 1e6,
            (dt.hour * 60 + dt.minute) / (24 * 60)  # 日内进度
        ])

        week_feats = torch.tensor([
            dt.weekday() / 6.0,  # 星期几
            (dt.weekday() < 5).float()  # 是否工作日
        ])

        month_feats = torch.tensor([
            (dt.day - 1) / 30.0,  # 月内天数
            dt.day <= 15  # 上半月/下半月
        ])

        season_feats = torch.tensor([
            ((dt.month - 1) % 3) / 2.0,  # 季度内月份
            ((dt.month - 1) // 3) / 3.0  # 季度
        ])

        # 嵌入并组合
        day_emb = self.day_embed(day_feats)
        week_emb = self.week_embed(week_feats)
        month_emb = self.month_embed(month_feats)
        season_emb = self.season_embed(season_feats)

        combined = torch.cat([day_emb, week_emb, month_emb, season_emb], dim=-1)
        return self.proj(combined)

class CrossSecurityAttention(nn.Module):
    """跨证券注意力层"""
    def __init__(self, d_model, n_head):
        super().__init__()
        self.mha = nn.MultiheadAttention(d_model, n_head)

    def forward(self, x_batch):
        """
        x_batch: [batch_size, seq_len, d_model]
        每个样本代表一个证券的序列
        """
        # 转置为[seq_len, batch_size, d_model]
        x_t = x_batch.transpose(0, 1)

        # 应用跨证券注意力
        attn_output, _ = self.mha(x_t, x_t, x_t)

        # 转回原始形状
        return attn_output.transpose(0, 1)

class MixtureOfExperts(nn.Module):
    """混合专家模型"""
    def __init__(self, d_model, n_experts=4):
        super().__init__()
        self.gate = nn.Linear(d_model, n_experts)
        self.experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model * 4),
                nn.GELU(),
                nn.Linear(d_model * 4, d_model)
            ) for _ in range(n_experts)
        ])

    def forward(self, x):
        # 计算门控权重
        gate_logits = self.gate(x)  # [batch_size, seq_len, n_experts]
        gate_weights = F.softmax(gate_logits, dim=-1)

        # 应用每个专家
        expert_outputs = []
        for i, expert in enumerate(self.experts):
            expert_out = expert(x)
            # 加权专家输出
            weighted_out = expert_out * gate_weights[:, :, i].unsqueeze(-1)
            expert_outputs.append(weighted_out)

        # 组合所有专家的输出
        combined = sum(expert_outputs)
        return combined


# def curriculum_training(model, datasets, optimizer, device, epochs_per_stage=5):
#     """课程学习训练"""
#     # 阶段1：短序列，低波动性数据
#     dataloader_easy = datasets['easy'].get_dataloader(batch_size=32)
#     train_model(model, dataloader_easy, optimizer, device, epochs_per_stage)

#     # 阶段2：中等序列，中等波动性
#     dataloader_medium = datasets['medium'].get_dataloader(batch_size=32)
#     train_model(model, dataloader_medium, optimizer, device, epochs_per_stage)

#     # 阶段3：长序列，高波动性
#     dataloader_hard = datasets['hard'].get_dataloader(batch_size=32)
#     train_model(model, dataloader_hard, optimizer, device, epochs_per_stage)

#     # 阶段4：混合数据
#     dataloader_mixed = datasets['mixed'].get_dataloader(batch_size=32)
#     train_model(model, dataloader_mixed, optimizer, device, epochs_per_stage)


def adversarial_training_step(model, batch, optimizer, device, epsilon=0.01):
    """对抗训练步骤"""
    # 将数据移动到设备
    input_tokens = batch['input_tokens'].to(device)
    target_tokens = batch['target_tokens'].to(device)
    code_ids = batch['code_ids'].to(device)

    # 获取嵌入
    token_emb = model.token_embedding(input_tokens)
    token_emb.requires_grad = True

    # 前向传播
    logits, loss = model.forward_with_embeddings(token_emb, code_ids, targets=target_tokens)

    # 计算嵌入的梯度
    loss.backward(retain_graph=True)
    emb_grad = token_emb.grad.detach()

    # 生成对抗样本
    delta = epsilon * emb_grad.sign()
    adv_token_emb = token_emb + delta

    # 使用对抗样本进行训练
    optimizer.zero_grad()
    adv_logits, adv_loss = model.forward_with_embeddings(adv_token_emb, code_ids, targets=target_tokens)

    # 最终损失是原始损失和对抗损失的组合
    total_loss = 0.5 * (loss + adv_loss)
    total_loss.backward()
    optimizer.step()

    return total_loss.item()

class MultiTaskCandlestickLLM(CandlestickLLM):
    """多任务K线预测模型"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        d_model = kwargs.get('d_model', 768)

        # 额外的预测头
        self.direction_head = nn.Linear(d_model, 3)  # 上涨/下跌/持平
        self.volatility_head = nn.Linear(d_model, 5)  # 波动性分类
        self.volume_head = nn.Linear(d_model, 5)  # 交易量分类

    def forward(self, input_tokens, code_ids, time_features=None, targets=None,
               direction_targets=None, volatility_targets=None, volume_targets=None):
        # 基本前向传播
        logits, token_loss = super().forward(input_tokens, code_ids, time_features, targets)

        # 获取最后一个时间步的特征
        last_features = self.ln_f(x[:, -1, :])

        # 方向预测
        direction_logits = self.direction_head(last_features)
        direction_loss = None
        if direction_targets is not None:
            direction_loss = F.cross_entropy(direction_logits, direction_targets)

        # 波动性预测
        volatility_logits = self.volatility_head(last_features)
        volatility_loss = None
        if volatility_targets is not None:
            volatility_loss = F.cross_entropy(volatility_logits, volatility_targets)

        # 交易量预测
        volume_logits = self.volume_head(last_features)
        volume_loss = None
        if volume_targets is not None:
            volume_loss = F.cross_entropy(volume_logits, volume_targets)

        # 组合损失
        total_loss = token_loss
        if direction_loss is not None:
            total_loss = total_loss + 0.2 * direction_loss
        if volatility_loss is not None:
            total_loss = total_loss + 0.2 * volatility_loss
        if volume_loss is not None:
            total_loss = total_loss + 0.2 * volume_loss

        return {
            'token_logits': logits,
            'direction_logits': direction_logits,
            'volatility_logits': volatility_logits,
            'volume_logits': volume_logits
        }, total_loss

class ProbabilisticTradingStrategy:
    """基于概率的交易策略"""
    def __init__(self, model, tokenizer, threshold=0.7, risk_factor=0.1):
        self.model = model
        self.tokenizer = tokenizer
        self.threshold = threshold
        self.risk_factor = risk_factor

        # 导入信号生成器模块
        from pyqlab.models.gpt2.signal_generator import SignalGenerator, TopKStrategy

        # 创建信号生成策略
        self.signal_strategy = TopKStrategy(k=5, min_prob=threshold)
        self.signal_generator = SignalGenerator(self.signal_strategy)

    def generate_signals(self, current_data, code_id):
        """生成交易信号"""
        # 将当前数据转换为token
        tokens = self.tokenizer.tokenize(current_data)
        input_tokens = torch.tensor(tokens[-30:], dtype=torch.long).unsqueeze(0)
        code_id_tensor = torch.tensor([code_id], dtype=torch.long)

        # 生成预测
        with torch.no_grad():
            logits, _ = self.model(input_tokens, code_id_tensor)

        # 使用信号生成器生成信号
        signal_info = self.signal_generator.generate_signal(
            logits=logits,
            tokenizer=self.tokenizer,
            temperature=1.0
        )

        # 转换为原有格式的信号
        signal = signal_info['signal']
        confidence = signal_info['confidence']

        if signal == 'BUY':
            position_size = confidence * self.risk_factor
            return {'signal': 'BUY', 'strength': confidence, 'size': position_size}
        elif signal == 'SELL':
            position_size = confidence * self.risk_factor
            return {'signal': 'SELL', 'strength': confidence, 'size': position_size}
        else:
            return {'signal': 'HOLD', 'strength': confidence}

class RLTradingAgent:
    """强化学习交易代理"""
    def __init__(self, llm_model, state_dim, action_dim, hidden_dim=128):
        self.llm_model = llm_model

        # 策略网络
        self.policy_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim)
        )

        # 价值网络
        self.value_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )

        self.optimizer = torch.optim.Adam(
            list(self.policy_net.parameters()) + list(self.value_net.parameters()),
            lr=1e-4
        )

    def get_state(self, tokens, code_id):
        """从LLM获取状态表示"""
        with torch.no_grad():
            # 获取LLM的隐藏状态
            input_tokens = torch.tensor(tokens, dtype=torch.long).unsqueeze(0)
            code_id_tensor = torch.tensor([code_id], dtype=torch.long)

            # 前向传播但只获取特征
            features = self.llm_model.get_features(input_tokens, code_id_tensor)

            # 使用最后一个时间步的特征作为状态
            state = features[0, -1].cpu().numpy()

        return state

    def select_action(self, state):
        """选择交易动作"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)

        # 获取策略分布
        action_probs = F.softmax(self.policy_net(state_tensor), dim=-1)

        # 采样动作
        m = torch.distributions.Categorical(action_probs)
        action = m.sample()

        # 计算动作的对数概率，用于后续计算策略梯度
        log_prob = m.log_prob(action)

        # 获取价值估计
        value = self.value_net(state_tensor)

        return action.item(), log_prob, value

    def update(self, rewards, log_probs, values, next_value, gamma=0.99, lambda_=0.95):
        """更新策略和价值网络"""
        # 计算优势和回报
        returns = []
        advantages = []

        # GAE计算
        gae = 0
        for i in reversed(range(len(rewards))):
            delta = rewards[i] + gamma * next_value - values[i]
            gae = delta + gamma * lambda_ * gae
            next_value = values[i]

            advantages.insert(0, gae)
            returns.insert(0, gae + values[i])

        # 转换为张量
        returns = torch.FloatTensor(returns)
        advantages = torch.FloatTensor(advantages)
        log_probs = torch.stack(log_probs)
        values = torch.cat(values)

        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # 计算损失
        policy_loss = -(log_probs * advantages).mean()
        value_loss = F.mse_loss(values, returns)

        # 总损失
        loss = policy_loss + 0.5 * value_loss

        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        return loss.item()

class RiskManager:
    """风险管理模块"""
    def __init__(self, max_position=1.0, max_drawdown=0.1, stop_loss=0.05, take_profit=0.1):
        self.max_position = max_position  # 最大仓位比例
        self.max_drawdown = max_drawdown  # 最大回撤限制
        self.stop_loss = stop_loss  # 止损比例
        self.take_profit = take_profit  # 止盈比例
        self.positions = {}  # 当前持仓 {symbol: {size, entry_price, stop_price, target_price}}
        self.equity_curve = []  # 权益曲线
        self.peak_equity = 0  # 峰值权益

    def adjust_position_size(self, symbol, signal, price, confidence, volatility):
        """根据风险参数调整仓位大小"""
        # 基础仓位大小
        base_size = signal['size'] if 'size' in signal else 0.1

        # 根据置信度调整
        size = base_size * confidence

        # 根据波动性调整
        size = size / (1 + volatility)  # 波动性越大，仓位越小

        # 确保不超过最大仓位
        size = min(size, self.max_position)

        # 计算止损和止盈价格
        if signal['signal'] == 'BUY':
            stop_price = price * (1 - self.stop_loss)
            target_price = price * (1 + self.take_profit)
        else:  # SELL
            stop_price = price * (1 + self.stop_loss)
            target_price = price * (1 - self.take_profit)

        # 更新持仓信息
        self.positions[symbol] = {
            'size': size,
            'entry_price': price,
            'stop_price': stop_price,
            'target_price': target_price,
            'signal': signal['signal']
        }

        return size

    def check_risk_limits(self, current_equity):
        """检查是否违反风险限制"""
        # 更新峰值权益
        self.peak_equity = max(self.peak_equity, current_equity)

        # 计算当前回撤
        current_drawdown = (self.peak_equity - current_equity) / self.peak_equity if self.peak_equity > 0 else 0

        # 更新权益曲线
        self.equity_curve.append(current_equity)

        # 检查是否超过最大回撤限制
        if current_drawdown > self.max_drawdown:
            return False, f"超过最大回撤限制: {current_drawdown:.2%} > {self.max_drawdown:.2%}"

        return True, None

    def check_stop_orders(self, symbol, current_price):
        """检查是否触发止损或止盈"""
        if symbol not in self.positions:
            return None

        position = self.positions[symbol]

        # 检查止损
        if position['signal'] == 'BUY' and current_price <= position['stop_price']:
            return {'action': 'CLOSE', 'reason': 'STOP_LOSS'}

        if position['signal'] == 'SELL' and current_price >= position['stop_price']:
            return {'action': 'CLOSE', 'reason': 'STOP_LOSS'}

        # 检查止盈
        if position['signal'] == 'BUY' and current_price >= position['target_price']:
            return {'action': 'CLOSE', 'reason': 'TAKE_PROFIT'}

        if position['signal'] == 'SELL' and current_price <= position['target_price']:
            return {'action': 'CLOSE', 'reason': 'TAKE_PROFIT'}

        return None


class MultiTimeframeTokenizer:
    """多时间框架K线tokenizer"""
    def __init__(self, base_tokenizer, timeframes=['1m', '5m', '15m', '1h', '1d']):
        self.base_tokenizer = base_tokenizer
        self.timeframes = timeframes

    def resample_data(self, df, timeframe):
        """将数据重采样到指定时间框架"""
        if 'm' in timeframe:
            minutes = int(timeframe.replace('m', ''))
            rule = f'{minutes}min'
        elif 'h' in timeframe:
            hours = int(timeframe.replace('h', ''))
            rule = f'{hours}H'
        elif 'd' in timeframe:
            days = int(timeframe.replace('d', ''))
            rule = f'{days}D'
        else:
            raise ValueError(f"不支持的时间框架: {timeframe}")

        # 确保datetime列是索引且为datetime类型
        if 'datetime' in df.columns:
            df = df.set_index('datetime')
        df.index = pd.to_datetime(df.index)

        # 重采样
        resampled = df.resample(rule).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum' if 'volume' in df.columns else None
        }).dropna()

        # 重置索引
        resampled = resampled.reset_index()

        return resampled

    def tokenize_multi_timeframe(self, df):
        """对多个时间框架的数据进行tokenize"""
        tokens_dict = {}

        for tf in self.timeframes:
            # 重采样数据
            resampled_df = self.resample_data(df, tf)

            # tokenize
            tokens = self.base_tokenizer.tokenize(resampled_df)
            tokens_dict[tf] = tokens

        return tokens_dict

    def combine_tokens(self, tokens_dict, method='concat'):
        """组合不同时间框架的tokens"""
        if method == 'concat':
            # 简单拼接
            combined = []
            for tf in self.timeframes:
                combined.extend(tokens_dict[tf])
            return combined
        elif method == 'interleave':
            # 交错排列
            max_len = max(len(tokens_dict[tf]) for tf in self.timeframes)
            combined = []
            for i in range(max_len):
                for tf in self.timeframes:
                    if i < len(tokens_dict[tf]):
                        combined.append(tokens_dict[tf][i])
            return combined
        else:
            raise ValueError(f"不支持的组合方法: {method}")

class AnomalyDetector:
    """K线数据异常检测器"""
    def __init__(self, window_size=20, std_threshold=3.0):
        self.window_size = window_size
        self.std_threshold = std_threshold

    def detect_price_anomalies(self, df):
        """检测价格异常"""
        # 计算价格变化率
        df['price_change'] = df['close'].pct_change()

        # 计算滚动均值和标准差
        df['rolling_mean'] = df['price_change'].rolling(window=self.window_size).mean()
        df['rolling_std'] = df['price_change'].rolling(window=self.window_size).std()

        # 计算z分数
        df['z_score'] = (df['price_change'] - df['rolling_mean']) / df['rolling_std']

        # 标记异常
        df['is_anomaly'] = abs(df['z_score']) > self.std_threshold

        return df

    def detect_volume_anomalies(self, df):
        """检测交易量异常"""
        if 'volume' not in df.columns:
            return df

        # 计算交易量变化率
        df['volume_change'] = df['volume'].pct_change()

        # 计算滚动均值和标准差
        df['volume_rolling_mean'] = df['volume_change'].rolling(window=self.window_size).mean()
        df['volume_rolling_std'] = df['volume_change'].rolling(window=self.window_size).std()

        # 计算z分数
        df['volume_z_score'] = (df['volume_change'] - df['volume_rolling_mean']) / df['volume_rolling_std']

        # 标记异常
        df['volume_anomaly'] = abs(df['volume_z_score']) > self.std_threshold

        return df

    def correct_anomalies(self, df, method='interpolate'):
        """修正异常值"""
        # 检测价格和交易量异常
        df = self.detect_price_anomalies(df)
        df = self.detect_volume_anomalies(df)

        # 创建异常掩码
        price_anomaly_mask = df['is_anomaly'] if 'is_anomaly' in df.columns else pd.Series(False, index=df.index)
        volume_anomaly_mask = df['volume_anomaly'] if 'volume_anomaly' in df.columns else pd.Series(False, index=df.index)

        # 组合异常掩码
        anomaly_mask = price_anomaly_mask | volume_anomaly_mask

        # 修正异常值
        if method == 'interpolate':
            # 使用插值法
            for col in ['open', 'high', 'low', 'close']:
                df.loc[anomaly_mask, col] = np.nan
                df[col] = df[col].interpolate(method='linear')

            if 'volume' in df.columns:
                df.loc[anomaly_mask, 'volume'] = np.nan
                df['volume'] = df['volume'].interpolate(method='linear')

        elif method == 'rolling_mean':
            # 使用滚动均值
            for col in ['open', 'high', 'low', 'close']:
                rolling_mean = df[col].rolling(window=self.window_size, center=True).mean()
                df.loc[anomaly_mask, col] = rolling_mean.loc[anomaly_mask]

            if 'volume' in df.columns:
                volume_rolling_mean = df['volume'].rolling(window=self.window_size, center=True).mean()
                df.loc[anomaly_mask, 'volume'] = volume_rolling_mean.loc[anomaly_mask]

        return df


class CandlestickDataAugmentation:
    """K线数据增强"""
    def __init__(self, tokenizer):
        self.tokenizer = tokenizer

    def time_warp(self, df, warp_factor=0.2):
        """时间扭曲增强"""
        # 复制数据
        augmented_df = df.copy()

        # 获取时间索引
        if 'datetime' in augmented_df.columns:
            augmented_df = augmented_df.set_index('datetime')

        # 计算新的时间索引
        time_index = pd.to_datetime(augmented_df.index)
        time_deltas = np.diff(time_index)

        # 随机扭曲时间间隔
        random_factors = np.random.normal(1.0, warp_factor, len(time_deltas))
        new_deltas = [td * rf for td, rf in zip(time_deltas, random_factors)]

        # 创建新的时间索引
        new_index = [time_index[0]]
        for delta in new_deltas:
            new_index.append(new_index[-1] + delta)

        # 应用新的时间索引
        augmented_df.index = new_index[:len(augmented_df)]

        # 重置索引
        augmented_df = augmented_df.reset_index()

        return augmented_df

    def magnitude_warp(self, df, warp_factor=0.2):
        """幅度扭曲增强"""
        # 复制数据
        augmented_df = df.copy()

        # 生成随机扭曲因子
        n_samples = len(augmented_df)
        warp_factors = 1.0 + np.random.normal(0, warp_factor, n_samples)

        # 应用扭曲因子
        for col in ['open', 'high', 'low', 'close']:
            base_price = augmented_df[col].iloc[0]
            price_changes = (augmented_df[col] / augmented_df[col].shift(1)).fillna(1.0)

            # 应用扭曲
            warped_changes = price_changes * warp_factors

            # 重建价格序列
            augmented_df[col] = base_price
            for i in range(1, n_samples):
                augmented_df.loc[augmented_df.index[i], col] = augmented_df[col].iloc[i-1] * warped_changes.iloc[i]

        return augmented_df

    def jitter(self, df, jitter_factor=0.01):
        """抖动增强"""
        # 复制数据
        augmented_df = df.copy()

        # 生成随机抖动
        n_samples = len(augmented_df)

        for col in ['open', 'high', 'low', 'close']:
            # 计算价格范围
            price_range = augmented_df[col].max() - augmented_df[col].min()

            # 生成抖动
            jitter = np.random.normal(0, jitter_factor * price_range, n_samples)

            # 应用抖动
            augmented_df[col] = augmented_df[col] + jitter

        # 确保OHLC关系保持一致
        augmented_df['high'] = augmented_df[['high', 'open', 'close']].max(axis=1)
        augmented_df['low'] = augmented_df[['low', 'open', 'close']].min(axis=1)

        return augmented_df

    def generate_augmented_dataset(self, df, n_augmentations=5):
        """生成增强数据集"""
        augmented_dfs = [df]  # 包含原始数据

        for i in range(n_augmentations):
            # 随机选择增强方法
            augmentation_method = np.random.choice(['time_warp', 'magnitude_warp', 'jitter'])

            if augmentation_method == 'time_warp':
                aug_df = self.time_warp(df)
            elif augmentation_method == 'magnitude_warp':
                aug_df = self.magnitude_warp(df)
            else:  # jitter
                aug_df = self.jitter(df)

            augmented_dfs.append(aug_df)

        return augmented_dfs

class AttentionVisualizer:
    """注意力权重可视化"""
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer

    def get_attention_weights(self, input_tokens, code_id):
        """获取模型的注意力权重"""
        # 确保模型处于评估模式
        self.model.eval()

        # 准备输入
        input_tensor = torch.tensor(input_tokens, dtype=torch.long).unsqueeze(0)
        code_tensor = torch.tensor([code_id], dtype=torch.long)

        # 获取注意力权重
        with torch.no_grad():
            # 假设模型有一个方法可以返回注意力权重
            _, attention_weights = self.model.forward_with_attention(input_tensor, code_tensor)

        return attention_weights

    def visualize_attention(self, df, tokens, attention_weights, layer_idx=None, head_idx=None):
        """可视化注意力权重"""
        import matplotlib.pyplot as plt
        import seaborn as sns

        # 如果没有指定层和头，则使用最后一层的平均值
        if layer_idx is None:
            # 平均所有层
            attn = attention_weights.mean(dim=0)
        else:
            attn = attention_weights[layer_idx]

        if head_idx is None:
            # 平均所有头
            attn = attn.mean(dim=0)
        else:
            attn = attn[head_idx]

        # 获取最后一个token对所有token的注意力
        last_token_attn = attn[-1, :].cpu().numpy()

        # 解码tokens
        token_strs = [self.tokenizer.idx2token[t] for t in tokens]

        # 创建K线图和注意力热图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), gridspec_kw={'height_ratios': [3, 1]})

        # 绘制K线图
        for i in range(len(df)):
            x = i
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)

        ax1.set_title('K线图与注意力权重')

        # 绘制注意力热图
        sns.heatmap(last_token_attn.reshape(1, -1), ax=ax2, cmap='viridis', cbar=True)
        ax2.set_title('最后一个token的注意力权重')
        ax2.set_xlabel('Token位置')

        plt.tight_layout()
        plt.show()

        # 返回最重要的token及其注意力权重
        important_indices = np.argsort(last_token_attn)[::-1][:5]  # 前5个最重要的token
        important_tokens = [(token_strs[i], last_token_attn[i]) for i in important_indices]

        return important_tokens


class FeatureImportanceAnalyzer:
    """特征重要性分析"""
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer

    def compute_integrated_gradients(self, input_tokens, code_id, target_idx, steps=50):
        """计算集成梯度"""
        # 准备输入
        input_tensor = torch.tensor(input_tokens, dtype=torch.long).unsqueeze(0)
        code_tensor = torch.tensor([code_id], dtype=torch.long)

        # 获取token嵌入
        token_embeddings = self.model.token_embedding(input_tensor)

        # 创建基线（全零嵌入）
        baseline = torch.zeros_like(token_embeddings)

        # 计算从基线到输入的路径
        alphas = torch.linspace(0, 1, steps)

        # 存储梯度
        integrated_grads = torch.zeros_like(token_embeddings)

        # 启用梯度计算
        for alpha in alphas:
            # 插值嵌入
            # 插值嵌入
            interpolated_embedding = baseline + alpha * (token_embeddings - baseline)
            interpolated_embedding.requires_grad_(True)

            # 前向传播
            logits, _ = self.model.forward_with_embeddings(interpolated_embedding, code_tensor)

            # 计算目标类别的梯度
            if target_idx is None:
                # 使用预测的最可能类别
                target_idx = logits[0, -1].argmax().item()

            self.model.zero_grad()
            target_logit = logits[0, -1, target_idx]
            target_logit.backward(retain_graph=True)

            # 累积梯度
            integrated_grads += interpolated_embedding.grad

        # 平均梯度
        integrated_grads /= steps

        # 计算特征重要性
        feature_importance = (token_embeddings - baseline) * integrated_grads

        # 按token维度求和
        token_importance = feature_importance.sum(dim=-1)[0].detach().cpu().numpy()

        return token_importance, target_idx

    def analyze_feature_importance(self, df, tokens, target_idx=None):
        """分析特征重要性"""
        # 获取token的重要性分数
        token_importance, predicted_idx = self.compute_integrated_gradients(tokens, 0, target_idx)

        # 解码tokens和预测的token
        token_strs = [self.tokenizer.idx2token[t] for t in tokens]
        predicted_token = self.tokenizer.idx2token[predicted_idx]

        # 解析预测的token
        if '|' in predicted_token:
            change, entity, upline, downline = predicted_token.split('|')
            prediction_info = {
                'change': int(change),
                'entity': int(entity),
                'upline': int(upline),
                'downline': int(downline)
            }
        else:
            prediction_info = {'special_token': predicted_token}

        # 可视化特征重要性
        self.visualize_feature_importance(df, tokens, token_importance, token_strs, prediction_info)

        # 返回最重要的tokens
        important_indices = np.argsort(np.abs(token_importance))[::-1][:10]  # 前10个最重要的token
        important_tokens = [(token_strs[i], token_importance[i]) for i in important_indices]

        return important_tokens, prediction_info

    def visualize_feature_importance(self, df, tokens, token_importance, token_strs, prediction_info):
        """可视化特征重要性"""
        import matplotlib.pyplot as plt
        import seaborn as sns

        # 创建K线图和特征重要性图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), gridspec_kw={'height_ratios': [3, 1]})

        # 绘制K线图
        for i in range(len(df)):
            x = i
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)

        # 添加预测信息
        if 'change' in prediction_info:
            prediction_text = f"预测: 变化={prediction_info['change']}, 实体={prediction_info['entity']}, "
            prediction_text += f"上影线={prediction_info['upline']}, 下影线={prediction_info['downline']}"
        else:
            prediction_text = f"预测: {prediction_info['special_token']}"

        ax1.set_title(f'K线图与特征重要性 - {prediction_text}')

        # 绘制特征重要性
        colors = ['red' if x < 0 else 'blue' for x in token_importance]
        ax2.bar(range(len(token_importance)), token_importance, color=colors)
        ax2.set_title('Token特征重要性')
        ax2.set_xlabel('Token位置')
        ax2.set_ylabel('重要性分数')

        # 添加垂直网格线
        ax2.grid(axis='x', linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

class UncertaintyVisualizer:
    """预测不确定性可视化"""
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer

    def get_prediction_distribution(self, input_tokens, code_id, temperature=1.0):
        """获取预测分布"""
        # 准备输入
        input_tensor = torch.tensor(input_tokens, dtype=torch.long).unsqueeze(0)
        code_tensor = torch.tensor([code_id], dtype=torch.long)

        # 获取预测
        with torch.no_grad():
            logits, _ = self.model(input_tensor, code_tensor)

        # 获取最后一个时间步的logits
        last_logits = logits[0, -1] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        return probs

    def visualize_prediction_uncertainty(self, df, tokens, temperature=1.0, top_k=10):
        """可视化预测不确定性"""
        import matplotlib.pyplot as plt
        import seaborn as sns

        # 获取预测分布
        probs = self.get_prediction_distribution(tokens, 0, temperature)

        # 获取top-k预测
        top_indices = np.argsort(probs)[::-1][:top_k]
        top_probs = probs[top_indices]
        top_tokens = [self.tokenizer.idx2token[idx] for idx in top_indices]

        # 解析top tokens
        parsed_tokens = []
        for token in top_tokens:
            if '|' in token:
                change, entity, upline, downline = token.split('|')
                parsed_tokens.append({
                    'token': token,
                    'change': int(change),
                    'entity': int(entity),
                    'upline': int(upline),
                    'downline': int(downline)
                })
            else:
                parsed_tokens.append({
                    'token': token,
                    'special': True
                })

        # 计算方向概率
        up_prob = sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                     if '|' in token and int(token.split('|')[0]) > 0)
        down_prob = sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                       if '|' in token and int(token.split('|')[0]) < 0)
        flat_prob = sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                       if '|' in token and int(token.split('|')[0]) == 0)

        # 创建可视化
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 15), gridspec_kw={'height_ratios': [3, 2, 1]})

        # 绘制K线图
        for i in range(len(df)):
            x = i
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)

        ax1.set_title('K线图与预测不确定性')

        # 绘制top-k预测概率
        ax2.bar(range(len(top_probs)), top_probs, color='skyblue')
        ax2.set_xticks(range(len(top_probs)))
        ax2.set_xticklabels([t['token'] for t in parsed_tokens], rotation=45)
        ax2.set_title(f'Top-{top_k}预测概率')
        ax2.set_ylabel('概率')

        # 绘制方向概率
        direction_probs = [up_prob, flat_prob, down_prob]
        ax3.bar(['上涨', '持平', '下跌'], direction_probs, color=['red', 'gray', 'green'])
        ax3.set_title('方向预测概率')
        ax3.set_ylabel('概率')

        # 添加概率值标签
        for i, prob in enumerate(direction_probs):
            ax3.text(i, prob + 0.01, f'{prob:.2f}', ha='center')

        plt.tight_layout()
        plt.show()

        return {
            'top_predictions': list(zip(top_tokens, top_probs)),
            'direction_probs': {
                'up': up_prob,
                'flat': flat_prob,
                'down': down_prob
            }
        }

class ModelOptimizer:
    """模型优化工具"""
    def __init__(self, model):
        self.model = model

    def quantize_model(self, quantization_type='dynamic'):
        """量化模型"""
        import torch.quantization

        # 确保模型处于评估模式
        self.model.eval()

        if quantization_type == 'dynamic':
            # 动态量化
            quantized_model = torch.quantization.quantize_dynamic(
                self.model,
                {torch.nn.Linear},  # 量化线性层
                dtype=torch.qint8
            )
            return quantized_model

        elif quantization_type == 'static':
            # 静态量化（需要校准数据）
            # 1. 准备量化配置
            self.model.qconfig = torch.quantization.get_default_qconfig('fbgemm')

            # 2. 准备模型进行量化
            torch.quantization.prepare(self.model, inplace=True)

            # 3. 校准模型（需要运行一些代表性数据）
            # 这里需要调用者提供校准数据并运行

            # 4. 转换为量化模型
            torch.quantization.convert(self.model, inplace=True)

            return self.model

        else:
            raise ValueError(f"不支持的量化类型: {quantization_type}")

    def optimize_for_inference(self):
        """优化模型用于推理"""
        # 冻结模型权重
        for param in self.model.parameters():
            param.requires_grad = False

        # 融合操作（如果适用）
        # 例如，将Conv+BN+ReLU融合为一个操作
        # 这通常需要针对特定模型结构进行定制

        # 导出为TorchScript
        traced_model = torch.jit.trace(
            self.model,
            (
                torch.zeros(1, 30, dtype=torch.long),  # 示例输入tokens
                torch.zeros(1, dtype=torch.long)       # 示例code_id
            )
        )

        return traced_model

    def export_to_onnx(self, save_path, input_shape=(1, 30)):
        """导出为ONNX格式"""
        dummy_input = (
            torch.zeros(input_shape, dtype=torch.long),  # 示例输入tokens
            torch.zeros(1, dtype=torch.long)             # 示例code_id
        )

        torch.onnx.export(
            self.model,
            dummy_input,
            save_path,
            export_params=True,
            opset_version=12,
            do_constant_folding=True,
            input_names=['input_tokens', 'code_id'],
            output_names=['logits'],
            dynamic_axes={
                'input_tokens': {0: 'batch_size', 1: 'sequence_length'},
                'logits': {0: 'batch_size', 1: 'sequence_length'}
            }
        )

        print(f"模型已导出为ONNX格式: {save_path}")
        return save_path

class RealTimePredictionService:
    """实时预测服务"""
    def __init__(self, model, tokenizer, device='cpu'):
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        self.model.to(device)
        self.model.eval()

        # 缓存最近的数据和预测
        self.recent_data = {}
        self.recent_predictions = {}

    def update_data(self, symbol, new_candle):
        """更新数据"""
        if symbol not in self.recent_data:
            self.recent_data[symbol] = []

        # 添加新K线
        self.recent_data[symbol].append(new_candle)

        # 保持最近的N条K线
        max_candles = 100  # 保留足够多的K线以便进行预测
        if len(self.recent_data[symbol]) > max_candles:
            self.recent_data[symbol] = self.recent_data[symbol][-max_candles:]

    def predict(self, symbol, code_id, seq_len=30, temperature=0.8):
        """生成预测"""
        if symbol not in self.recent_data or len(self.recent_data[symbol]) < seq_len:
            return None

        # 准备数据
        recent_candles = self.recent_data[symbol][-seq_len:]
        df = pd.DataFrame(recent_candles)

        # Tokenize
        tokens = self.tokenizer.tokenize(df)

        # 准备输入
        input_tokens = torch.tensor(tokens, dtype=torch.long).unsqueeze(0).to(self.device)
        code_tensor = torch.tensor([code_id], dtype=torch.long).to(self.device)

        # 生成预测
        with torch.no_grad():
            logits, _ = self.model(input_tokens, code_tensor)

        # 获取最后一个时间步的logits
        last_logits = logits[0, -1] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        # 获取top-5预测
        top_indices = np.argsort(probs)[::-1][:5]
        top_probs = probs[top_indices]
        top_tokens = [self.tokenizer.idx2token[idx] for idx in top_indices]

        # 解析预测
        parsed_predictions = []
        for token, prob in zip(top_tokens, top_probs):
            if '|' in token:
                change, entity, upline, downline = token.split('|')
                parsed_predictions.append({
                    'token': token,
                    'change': int(change),
                    'entity': int(entity),
                    'upline': int(upline),
                    'downline': int(downline),
                    'probability': float(prob)
                })
            else:
                parsed_predictions.append({
                    'token': token,
                    'special': True,
                    'probability': float(prob)
                })

        # 计算方向概率
        direction_probs = {
            'up': sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                     if '|' in token and int(token.split('|')[0]) > 0),
            'flat': sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                       if '|' in token and int(token.split('|')[0]) == 0),
            'down': sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                       if '|' in token and int(token.split('|')[0]) < 0)
        }

        # 保存预测
        self.recent_predictions[symbol] = {
            'timestamp': pd.Timestamp.now(),
            'top_predictions': parsed_predictions,
            'direction_probs': direction_probs
        }

        return self.recent_predictions[symbol]

    def get_trading_signal(self, symbol, threshold=0.6):
        """获取交易信号"""
        if symbol not in self.recent_predictions:
            return {'signal': 'HOLD', 'confidence': 0.0}

        prediction = self.recent_predictions[symbol]
        direction_probs = prediction['direction_probs']

        # 生成信号
        if direction_probs['up'] > threshold:
            return {'signal': 'BUY', 'confidence': direction_probs['up']}
        elif direction_probs['down'] > threshold:
            return {'signal': 'SELL', 'confidence': direction_probs['down']}
        else:
            return {'signal': 'HOLD', 'confidence': max(direction_probs.values())}


class CandlestickLLMBacktester:
    """K线LLM模型回测器"""
    def __init__(self, model, tokenizer, initial_capital=10000.0):
        self.model = model
        self.tokenizer = tokenizer
        self.initial_capital = initial_capital

    def backtest(self, df, code_id=0, seq_len=30, commission=0.001, threshold=0.6):
        """回测模型"""
        # 确保模型处于评估模式
        self.model.eval()

        # 初始化回测状态
        capital = self.initial_capital
        position = 0
        entry_price = 0
        trades = []
        equity_curve = [capital]

        # 准备回测数据
        for i in range(seq_len, len(df)):
            # 获取当前K线
            current_candle = df.iloc[i]
            current_price = current_candle['close']

            # 获取历史数据
            historical_data = df.iloc[i-seq_len:i]

            # Tokenize
            tokens = self.tokenizer.tokenize(historical_data)

            # 生成预测
            input_tokens = torch.tensor(tokens, dtype=torch.long).unsqueeze(0)
            code_tensor = torch.tensor([code_id], dtype=torch.long)

            with torch.no_grad():
                logits, _ = self.model(input_tokens, code_tensor)

            # 获取最后一个时间步的logits
            last_logits = logits[0, -1]

            # 计算概率分布
            probs = F.softmax(last_logits, dim=-1).cpu().numpy()

            # 计算方向概率
            up_prob = sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                         if '|' in token and int(token.split('|')[0]) > 0)
            down_prob = sum(probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                           if '|' in token and int(token.split('|')[0]) < 0)

            # 生成交易信号
            signal = 'HOLD'
            if position == 0:  # 当前没有持仓
                if up_prob > threshold:
                    signal = 'BUY'
                elif down_prob > threshold:
                    signal = 'SELL'
            elif position > 0:  # 当前持有多头
                if down_prob > threshold:
                    signal = 'CLOSE'
            elif position < 0:  # 当前持有空头
                if up_prob > threshold:
                    signal = 'CLOSE'

            # 执行交易
            if signal == 'BUY' and position == 0:
                # 买入
                position = capital / current_price
                entry_price = current_price
                capital = 0
                trades.append({
                    'datetime': current_candle['datetime'] if 'datetime' in current_candle else i,
                    'action': 'BUY',
                    'price': current_price,
                    'position': position,
                    'capital': capital,
                    'equity': position * current_price
                })
            elif signal == 'SELL' and position == 0:
                # 卖空
                position = -capital / current_price
                entry_price = current_price
                capital = 0
                trades.append({
                    'datetime': current_candle['datetime'] if 'datetime' in current_candle else i,
                    'action': 'SELL',
                    'price': current_price,
                    'position': position,
                    'capital': capital,
                    'equity': -position * current_price
                })
            elif signal == 'CLOSE' and position != 0:
                # 平仓
                if position > 0:
                    # 平多
                    capital = position * current_price * (1 - commission)
                    trades.append({
                        'datetime': current_candle['datetime'] if 'datetime' in current_candle else i,
                        'action': 'CLOSE_LONG',
                        'price': current_price,
                        'position': 0,
                        'capital': capital,
                        'equity': capital,
                        'profit': (current_price - entry_price) * position - commission * (entry_price + current_price) * position
                    })
                else:
                    # 平空
                    capital = -position * (2 * entry_price - current_price) * (1 - commission)
                    trades.append({
                        'datetime': current_candle['datetime'] if 'datetime' in current_candle else i,
                        'action': 'CLOSE_SHORT',
                        'price': current_price,
                        'position': 0,
                        'capital': capital,
                        'equity': capital,
                        'profit': (entry_price - current_price) * (-position) - commission * (entry_price + current_price) * (-position)
                    })
                position = 0

            # 更新权益曲线
            if position == 0:
                equity = capital
            elif position > 0:
                equity = position * current_price
            else:
                equity = -position * (2 * entry_price - current_price)

            equity_curve.append(equity)

        # 计算回测指标
        returns = np.diff(equity_curve) / equity_curve[:-1]
        total_return = equity_curve[-1] / self.initial_capital - 1
        annual_return = (1 + total_return) ** (252 / len(df)) - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        max_drawdown = 0
        peak = equity_curve[0]

        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)

        # 汇总结果
        results = {
            'initial_capital': self.initial_capital,
            'final_equity': equity_curve[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'trades': trades,
            'equity_curve': equity_curve
        }

        return results

    def visualize_backtest(self, df, results):
        """可视化回测结果"""
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), gridspec_kw={'height_ratios': [3, 1]})

        # 绘制K线图
        if 'datetime' in df.columns:
            x = pd.to_datetime(df['datetime'])
        else:
            x = range(len(df))

        for i in range(len(df)):
            if 'datetime' in df.columns:
                x_pos = x[i]
            else:
                x_pos = i

            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x_pos, x_pos], [open_price, close_price], color=color, linewidth=2)
            # 绘制影线
            ax1.plot([x_pos, x_pos], [low_price, high_price], color=color, linewidth=1)

        # 标记交易点
        for trade in results['trades']:
            if 'datetime' in df.columns and isinstance(trade['datetime'], (pd.Timestamp, str)):
                x_pos = pd.to_datetime(trade['datetime'])
            else:
                x_pos = x[trade['datetime']] if isinstance(trade['datetime'], int) else trade['datetime']

            if trade['action'] == 'BUY':
                ax1.scatter(x_pos, trade['price'], marker='^', color='blue', s=100)
            elif trade['action'] == 'SELL':
                ax1.scatter(x_pos, trade['price'], marker='v', color='purple', s=100)
            elif trade['action'] == 'CLOSE_LONG':
                ax1.scatter(x_pos, trade['price'], marker='o', color='blue', s=100)
            elif trade['action'] == 'CLOSE_SHORT':
                ax1.scatter(x_pos, trade['price'], marker='o', color='purple', s=100)

        # 设置K线图标题和标签
        ax1.set_title('K线图与交易信号')
        ax1.set_ylabel('价格')
        if 'datetime' in df.columns:
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.xticks(rotation=45)

        # 绘制权益曲线
        if 'datetime' in df.columns:
            equity_x = [pd.to_datetime(df['datetime'].iloc[0])] + [
                pd.to_datetime(df['datetime'].iloc[i]) if i < len(df) else pd.to_datetime(df['datetime'].iloc[-1])
                for i in range(seq_len, len(df))
            ]
        else:
            equity_x = range(len(results['equity_curve']))

        ax2.plot(equity_x, results['equity_curve'], color='blue', linewidth=2)
        ax2.set_title('权益曲线')
        ax2.set_ylabel('权益')
        if 'datetime' in df.columns:
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))

        # 添加回测指标
        textstr = '\n'.join((
            f'初始资金: ${results["initial_capital"]:.2f}',
            f'最终权益: ${results["final_equity"]:.2f}',
            f'总收益率: {results["total_return"]:.2%}',
            f'年化收益率: {results["annual_return"]:.2%}',
            f'夏普比率: {results["sharpe_ratio"]:.2f}',
            f'最大回撤: {results["max_drawdown"]:.2%}',
            f'交易次数: {len(results["trades"])}'
        ))

        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax2.text(0.05, 0.95, textstr, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', bbox=props)

        plt.tight_layout()
        plt.show()

class EnsemblePredictor:
    """集成预测器"""
    def __init__(self, models, tokenizer, weights=None):
        self.models = models
        self.tokenizer = tokenizer
        self.weights = weights if weights is not None else [1.0/len(models)] * len(models)

        # 确保权重和归一化
        self.weights = np.array(self.weights)
        self.weights = self.weights / np.sum(self.weights)

    def predict(self, df, code_id=0, temperature=1.0):
        """生成集成预测"""
        # Tokenize
        tokens = self.tokenizer.tokenize(df)

        # 准备输入
        input_tokens = torch.tensor(tokens, dtype=torch.long).unsqueeze(0)
        code_tensor = torch.tensor([code_id], dtype=torch.long)

        # 获取每个模型的预测
        all_probs = []

        for model in self.models:
            model.eval()
            with torch.no_grad():
                logits, _ = model(input_tokens, code_tensor)

            # 获取最后一个时间步的logits
            last_logits = logits[0, -1] / temperature

            # 计算概率分布
            probs = F.softmax(last_logits, dim=-1).cpu().numpy()
            all_probs.append(probs)

        # 加权平均概率
        ensemble_probs = np.zeros_like(all_probs[0])
        for i, probs in enumerate(all_probs):
            ensemble_probs += probs * self.weights[i]

        # 获取top-k预测
        top_indices = np.argsort(ensemble_probs)[::-1][:5]
        top_probs = ensemble_probs[top_indices]
        top_tokens = [self.tokenizer.idx2token[idx] for idx in top_indices]

        # 解析预测
        parsed_predictions = []
        for token, prob in zip(top_tokens, top_probs):
            if '|' in token:
                change, entity, upline, downline = token.split('|')
                parsed_predictions.append({
                    'token': token,
                    'change': int(change),
                    'entity': int(entity),
                    'upline': int(upline),
                    'downline': int(downline),
                    'probability': float(prob)
                })
            else:
                parsed_predictions.append({
                    'token': token,
                    'special': True,
                    'probability': float(prob)
                })

        # 计算方向概率
        direction_probs = {
            'up': sum(ensemble_probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                     if '|' in token and int(token.split('|')[0]) > 0),
            'flat': sum(ensemble_probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                       if '|' in token and int(token.split('|')[0]) == 0),
            'down': sum(ensemble_probs[i] for i, token in enumerate(self.tokenizer.idx2token.values())
                       if '|' in token and int(token.split('|')[0]) < 0)
        }

        return {
            'top_predictions': parsed_predictions,
            'direction_probs': direction_probs,
            'ensemble_probs': ensemble_probs
        }


class MultiMarketAdapter:
    """多市场适配器"""
    def __init__(self, base_tokenizer, market_specific_params=None):
        self.base_tokenizer = base_tokenizer
        self.market_specific_params = market_specific_params or {}
        self.market_tokenizers = {}

        # 为每个市场创建特定的tokenizer
        for market, params in self.market_specific_params.items():
            self.market_tokenizers[market] = self._create_market_tokenizer(params)

    def _create_market_tokenizer(self, params):
        """创建市场特定的tokenizer"""
        # 复制基础tokenizer的参数
        tokenizer_params = {
            'change_range': self.base_tokenizer.change_range,
            'entity_range': self.base_tokenizer.entity_range,
            'shadow_range': self.base_tokenizer.shadow_range,
            'atr_window': self.base_tokenizer.atr_window,
            'atr_mult': self.base_tokenizer.atr_mult,
            'scale': self.base_tokenizer.scale,
            'special_tokens': self.base_tokenizer.special_tokens
        }

        # 更新市场特定参数
        tokenizer_params.update(params)

        # 创建新的tokenizer
        from copy import deepcopy
        market_tokenizer = deepcopy(self.base_tokenizer)

        # 更新参数
        for key, value in tokenizer_params.items():
            setattr(market_tokenizer, key, value)

        return market_tokenizer

    def tokenize(self, df, market):
        """根据市场选择合适的tokenizer进行tokenize"""
        if market in self.market_tokenizers:
            return self.market_tokenizers[market].tokenize(df)
        else:
            # 如果没有特定市场的tokenizer，使用基础tokenizer
            return self.base_tokenizer.tokenize(df)

    def get_tokenizer(self, market):
        """获取特定市场的tokenizer"""
        if market in self.market_tokenizers:
            return self.market_tokenizers[market]
        else:
            return self.base_tokenizer


class MultiModalFusion:
    """多模态融合模型"""
    def __init__(self, candlestick_model, news_model, sentiment_model, fusion_weights=None):
        self.candlestick_model = candlestick_model
        self.news_model = news_model
        self.sentiment_model = sentiment_model

        # 默认权重
        if fusion_weights is None:
            self.fusion_weights = {
                'candlestick': 0.6,
                'news': 0.2,
                'sentiment': 0.2
            }
        else:
            self.fusion_weights = fusion_weights

    def predict(self, candlestick_data, news_data=None, sentiment_data=None, code_id=0):
        """融合多模态数据进行预测"""
        # 获取K线预测
        candlestick_pred = self._get_candlestick_prediction(candlestick_data, code_id)

        # 获取新闻预测（如果有）
        news_pred = None
        if news_data is not None and self.news_model is not None:
            news_pred = self._get_news_prediction(news_data)

        # 获取情绪预测（如果有）
        sentiment_pred = None
        if sentiment_data is not None and self.sentiment_model is not None:
            sentiment_pred = self._get_sentiment_prediction(sentiment_data)

        # 融合预测
        fused_prediction = self._fuse_predictions(candlestick_pred, news_pred, sentiment_pred)

        return fused_prediction

    def _get_candlestick_prediction(self, df, code_id):
        """获取K线预测"""
        # 假设candlestick_model有一个predict方法
        return self.candlestick_model.predict(df, code_id)

    def _get_news_prediction(self, news_data):
        """获取新闻预测"""
        # 假设news_model有一个predict方法
        return self.news_model.predict(news_data)

    def _get_sentiment_prediction(self, sentiment_data):
        """获取情绪预测"""
        # 假设sentiment_model有一个predict方法
        return self.sentiment_model.predict(sentiment_data)

    def _fuse_predictions(self, candlestick_pred, news_pred, sentiment_pred):
        """融合多模态预测"""
        # 初始化融合预测
        fused_pred = {
            'direction_probs': {
                'up': 0.0,
                'flat': 0.0,
                'down': 0.0
            }
        }

        # 添加K线预测
        if candlestick_pred is not None:
            weight = self.fusion_weights.get('candlestick', 0.0)
            for direction in fused_pred['direction_probs']:
                fused_pred['direction_probs'][direction] += candlestick_pred['direction_probs'].get(direction, 0.0) * weight

        # 添加新闻预测
        if news_pred is not None:
            weight = self.fusion_weights.get('news', 0.0)
            for direction in fused_pred['direction_probs']:
                fused_pred['direction_probs'][direction] += news_pred.get(direction, 0.0) * weight

        # 添加情绪预测
        if sentiment_pred is not None:
            weight = self.fusion_weights.get('sentiment', 0.0)
            for direction in fused_pred['direction_probs']:
                fused_pred['direction_probs'][direction] += sentiment_pred.get(direction, 0.0) * weight

        # 归一化方向概率
        total_prob = sum(fused_pred['direction_probs'].values())
        if total_prob > 0:
            for direction in fused_pred['direction_probs']:
                fused_pred['direction_probs'][direction] /= total_prob

        return fused_pred



