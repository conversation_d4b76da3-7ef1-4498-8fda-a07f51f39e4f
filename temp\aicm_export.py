# pip install -i https://pypi.tuna.tsinghua.edu.cn/simple some-package -r requirements.txt

# ## 数据预处理
# 特征向量的构建是机器学习的关键步骤

import sys
import time
from datetime import datetime
import json
import ast
import sqlite3
import pandas as pd
import numpy as np
# from pprint import pprint
from redis import Redis
from typing import Dict
import base64

sys.path.append("c:/TRobot")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

class AicmTradingDataEtl():

    def __init__(self, db_path, data_path, host="", port=0, password="") -> None:
        self.db_path = db_path
        self.data_path = data_path
        self.host = host
        self.port = port
        self.password=password
        self.redis = None

    def _connect_redis(self):
        self.redis=Redis(host=self.host, db=3, password=self.password, port=self.port)

    def set_data(self, key: str, value: str) -> None:
        if not self.redis:
            self._connect_redis()
        return self.redis.set(key, value)

    def hmset_data(self, key: str, value: Dict) -> None:
        if not self.redis:
            self._connect_redis()
        return self.redis.hmset(key, value)

    def hgetall_data(self, key):
        if not self.redis:
            self._connect_redis()
        return self.redis.hgetall(key)

    def week_idx_by_long(self, longdate) -> int:
        dt=datetime.strptime(longdate, "%Y%m%d")
        return int(dt.timestamp()+259200)//604800 # 开始计数时间为星期四

    def week_idx_by_ordid(self, ordid) -> int:
        dt=datetime.strptime(ordid[2:14], "%y%m%d%H%M%S")
        return int(dt.timestamp()+259200)//604800 # 开始计数时间为星期四

    # 从数据库加载账户结算报告数据
    def load_settlement_from_db(self, week_idx):
        db=create_db("leveldb", f"{self.db_path}/store/kv.db" , Mode.read)
        cursor = db.new_cursor()
        # cursor.seek_to_first()
        data = []
        while cursor.valid():
            if cursor.key()[0:17] == b'settlementreport:':
                key = cursor.key().decode(encoding='gb2312')
                # print(key[-8:])
                # print(key[17:-8])
                wdx = self.week_idx_by_long(key[-8:])
                if week_idx != -1 and week_idx != wdx:
                    continue
                item={}
                item['account'] = key[17:-9]
                item['date'] = key[-8:]
                item['settlement'] = cursor.value().decode('gb2312', errors='ignore')
                # print(item)
                data.append(item)
            cursor.next()
        del cursor
        db.close()
        del db

        df = pd.DataFrame.from_dict(data)
        return df


    def get_orders_label(self, ord_df):
        data = []
        for _, group in ord_df.groupby(['account_id', 'label']):
            for i in range(len(group)):
                if i + 1 >= len(group):
                    break
                if group.iat[i, 4] != "open":
                    continue
                item = []
                item.append(np.int64(group.iat[i, 0]))
                item.append(group.iat[i, 2])
                i = i + 1
                if group.iat[i, 4] != "open":
                    item.append(group.iat[i, 3])
                    item.append(group.iat[i, 5])
                    if float(group.iat[i, 7]) > 0:
                        item.append(1)
                    else:
                        item.append(0)
                    data.append(item)
                # else:
                #     print("warning:", group.iat[i, 2])
        return pd.DataFrame(data, columns=['ord_id', 'instrument', 'datetime', 'direct', 'label'])
        
    """
    从orders数据库导出订单标签
    """
    def load_orders(self, portfolio_id, week_idx):
        conn = sqlite3.connect(f'{self.db_path}/data/ot_store.db')
        cur = conn.cursor()

        # cur.execute("""SELECT * FROM T_Filled_Order WHERE account_id = "%s" ORDER BY id DESC"""%('*****************'))
        cur.execute("SELECT * FROM T_Filled_Order ORDER BY id ASC")
        orders = cur.fetchall()

        data = []
        for item in orders:
            record = []
            ord = json.loads(item[1])
            if ord['account_id'] != portfolio_id:
                continue
            if week_idx!=-1 and week_idx!=self.week_idx_by_ordid(ord['order_id']):
                continue
            record.append(np.int64(ord['order_id']))
            record.append(ord['account_id'])
            record.append(ord['order_book_id'])
            record.append(time.strftime("%Y%m%d %H:%M:%S", time.localtime(ord['trading_dt'])))
            msg = ord['message'].split(',')
            if len(msg) <= 1:
                print(msg)
                continue            
            if len(msg) <= 2:
                record.append(msg[0])
                record.append(msg[1])
                record.append("")
                record.append("")
            elif len(msg) > 7:
                record.append(msg[0])
                record.append(msg[1])
                record.append(msg[3])
                record.append(msg[7])
            else:
                record.append(msg[0])
                record.append(msg[1])
                record.append("")
                record.append("")
            data.append(record)

        cur.close()
        conn.close()

        ord_df = pd.DataFrame(data, columns=['ord_id', 'account_id', 'label', 'datetime', 'oprater', 'direct', 'cost_atr', 'pnl'])
        # lb_df = self.get_orders_label(ord_df)
        if len(ord_df) == 0:
            return pd.DataFrame()

        # lb_df['CODE'] = lb_df.apply(lambda x: x['instrument'][0:-7], axis=1)

        print(f'Portfolio: {portfolio_id} export order data: {len(ord_df)} \
        Today add count: {(ord_df["datetime"] >= datetime.now().strftime("%Y%m%d 00:00:00")).sum()}')
        return ord_df

    def export_portfolio_orders_data(self, portfolio_id, week_idx=None, save_file=False):
        """
        功能:
            导出一周或全部order数据, 可以保存到文件或服务器REDIS数据库, 
        参数:
            week_idx:
                None 导出当前周特征数据
                -1 导出所有特征数据
                n 导出第n周特征数据, 如: n=2720(********~********)
            save_file:
                True 保持为csv文件
                False 写入redis数据库
        """
        if not week_idx:
            week_idx=int(datetime.now().timestamp()+259200)//604800

        lb_df = self.load_orders(portfolio_id, week_idx)
        if len(lb_df) == 0:
            print("portfolio: %s not find." % portfolio_id)
            return

        if week_idx==-1:
            week_idx="all"

        if save_file:
            lb_df.to_parquet(f"./{portfolio_id}.{week_idx}.order.parquet", engine='fastparquet')
        else:
            self.set_data(f"orders:{portfolio_id}:{week_idx}", str(lb_df.to_dict()))
            print(f"orders:{portfolio_id}:{week_idx}, size: {len(lb_df)}")

    def export_account_settlement_data(self, week_idx=None, save_file=False):
        """
        功能:
            导出一周或全部order数据, 可以保存到文件或服务器REDIS数据库, 
        参数:
            week_idx:
                None 导出当前周特征数据
                -1 导出所有特征数据
                n 导出第n周特征数据, 如: n=2720(********~********)
            save_file:
                True 保持为csv文件
                False 写入redis数据库
        """
        if not week_idx:
            week_idx=int(datetime.now().timestamp()+259200)//604800

        df = self.load_settlement_from_db(week_idx)
        if len(df) == 0:
            print(f"account settlement: {week_idx} not find.")
            return

        if week_idx==-1:
            week_idx="all"

        if save_file:
            df.to_parquet(f"./{df['account'][0]}.settlement.parquet", engine='fastparquet')
        else:
            self.set_data(f"settlement:{df['account'][0]}:{week_idx}", str(df.to_dict()))
            print(f"settlement:{df['account'][0]}:{week_idx}, size: {len(df)}")

if __name__ == '__main__':
    # 配置
    config_server = {
        "db_path": "c:/TRobot",
        "data_path": 'e:/lab/RoboQuant/pylab/data',
        "host": "**********",
        "port": 51301,
        "password": "wdljshbsjzsszsbbzyjcsz~1974"
    }

    config_local = {
        "db_path": "c:/TRobot",
        "data_path": 'e:/lab/RoboQuant/pylab/data',
        "host": "**********",
        "port": 51301,
        "password": "wdljshbsjzsszsbbzyjcsz~1974"
    }

    pfs_name_ids = {
        "zxjt_pjj": {
            "FUT-ZXJT-P21": "*****************",
            "FUT-ZXJT-JMO": "*****************",
            "FUT-ZXJT-JHL": "*****************",
        },
        "zxjt_xzy": {
            "FUT-ZXJT-X21": "*****************",
            "FUT-ZXJT-XMO": "*****************",
            "FUT-ZXJT-XHL": "*****************",
        },
        "nhqh_xzy": {
            "FUT-NHQH-X21": "*****************",
            "FUT-NHQH-W1B": "*****************",
            "FUT-NHQH-NN1": "*****************",
        },
        "gtja_xzy": {
            "FUT-GTJA-X21": "*****************",
            "FUT-GTJA-W1B": "*****************",
        }
    }

    pfs_main = ['00211229152555000', '00170623114649000']
    pfs_zxjt_pjj = ['*****************', '*****************', '*****************']
    pfs_zxjt_xzy = ['*****************', '*****************', '*****************']
    pfs_zxjt_nhqh = ['*****************', '*****************', '*****************']
    pfs_zxjt_gtja = ['*****************', '*****************']
    pfs_local = ['*****************', '*****************']
    # pfs_local = ['*****************', '*****************', '*****************', '*****************']

    etl = AicmTradingDataEtl(**config_local)
    # 导出所有本地数据
    for pf in pfs_zxjt_pjj:
        etl.export_portfolio_orders_data(pf, week_idx=-1, save_file=True)
    etl.export_account_settlement_data(week_idx=-1, save_file=True)

    # 上载数据
    week_idx=int(datetime.now().timestamp()+259200)//604800
    print(f"============== UPDATE FACTOR DATASET: {week_idx}================")

    # 更新本地库数据到本地
    # for pf in pfs_zxjt_pjj:
    #     etl.export_portfolio_orders_data(pf, week_idx=week_idx, save_file=False)
    # etl.export_account_settlement_data(week_idx=week_idx, save_file=False)

