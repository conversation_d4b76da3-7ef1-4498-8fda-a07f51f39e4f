@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2\examples

python train_candlestick_vq_gpt_cv.py ^
--data_dir f:/hqdata/tsdb ^
--market fut ^
--block_name top ^
--period min1 ^
--begin_date 2025-03-01 ^
--end_date 2025-12-31 ^
--stride 1 ^
--num_folds 5 ^
--codebook_path  E:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250522\vqcb_atr_based_fut_top_min1_512_0.0202.pt ^
--num_embeddings 512 ^
--embedding_dim 4 ^
--atr_period 14 ^
--ma_volume_period 14 ^
--vectorization_method atr_based ^
--seq_len 30 ^
--code_size 100 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 16 ^
--dropout 0.1 ^
--use_time_features ^
--label_smoothing 0.1 ^
--use_auxiliary_loss ^
--batch_size 64 ^
--epochs 2 ^
--learning_rate 1e-3 ^
--weight_decay 0.01 ^
--warmup_ratio 0.1 ^
--grad_clip 1.0 ^
--grad_accum_steps 2 ^
--early_stopping 5 ^
--num_workers 4 ^
--save_dir e:\lab\RoboQuant\pylab\models\candlestick_vq_gpt_cv ^
--seed 42 ^
--use_code_dim ^
--code_dim 5

@REM --mixed_precision ^ 
