import torch
import torch.nn.functional as F
import math

def flash_attn_func(q, k, v, causal=False, dropout_p=0.0, softmax_scale=None):
    """
    模拟 Flash Attention 的 PyTorch 实现
    不依赖外部库，但可以提供相似的接口以便后续集成真正的 Flash Attention

    参数:
    - q: 查询张量 [batch_size, seq_len, num_heads, head_dim]
    - k: 键张量 [batch_size, seq_len, num_heads, head_dim]
    - v: 值张量 [batch_size, seq_len, num_heads, head_dim]
    - causal: 是否使用因果掩码（三角掩码）
    - dropout_p: dropout 概率
    - softmax_scale: softmax 缩放因子，默认为 1/sqrt(head_dim)

    返回:
    - 输出张量 [batch_size, seq_len, num_heads, head_dim]
    """
    batch_size, seqlen_q, num_heads, head_dim = q.shape
    _, seqlen_k, _, _ = k.shape

    # 计算缩放因子
    if softmax_scale is None:
        softmax_scale = 1.0 / math.sqrt(head_dim)

    # 将维度调整为适合批量矩阵乘法的形状
    q = q.reshape(batch_size, seqlen_q, num_heads * head_dim)  # [B, Sq, H*D]
    k = k.reshape(batch_size, seqlen_k, num_heads * head_dim)  # [B, Sk, H*D]
    v = v.reshape(batch_size, seqlen_k, num_heads * head_dim)  # [B, Sk, H*D]

    # 计算注意力分数
    attn_weights = torch.bmm(q, k.transpose(1, 2)) * softmax_scale  # [B, Sq, Sk]

    # 应用因果掩码（如果需要）
    if causal:
        causal_mask = torch.triu(torch.ones(seqlen_q, seqlen_k, dtype=torch.bool, device=q.device), diagonal=1)
        attn_weights = attn_weights.masked_fill(causal_mask, float('-inf'))

    # 应用 softmax
    attn_weights = F.softmax(attn_weights, dim=-1)

    # 应用 dropout
    if dropout_p > 0.0:
        attn_weights = F.dropout(attn_weights, p=dropout_p)

    # 计算输出
    output = torch.bmm(attn_weights, v)  # [B, Sq, H*D]

    # 重新整形为原始形状
    output = output.reshape(batch_size, seqlen_q, num_heads, head_dim)  # [B, Sq, H, D]

    return output 