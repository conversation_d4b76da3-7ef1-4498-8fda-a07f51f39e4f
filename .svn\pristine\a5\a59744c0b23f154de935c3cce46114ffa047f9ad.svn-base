from enum import Enum, EnumMeta

MAIN_FUT_MARKET_CODES = {
    'SC': ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG', 'RB', 'HC', 'BU', 'RU', 'FU', 'SP', 'WR', 'SC', 'NR', 'SS', 'AO', 'BR'],
    'ZC': ['SR', 'CF', 'ZC', 'FG', 'TA', 'MA', 'OI', 'RM', 'CY', 'PM', 'RI', 'LR', 'JR', 'RS', 'SF', 'SM', 'AP', 'CJ', 'UR', 'SA', 'PF', 'PK', 'PX', 'SH'], 
    'DC': ['M', 'Y', 'A', 'B', 'P', 'J', 'JM', 'I', 'RR', 'C', 'CS', 'JD', 'BB', 'FB', 'L', 'V', 'PP', 'EG', 'EB', 'PG', 'LH']
}

MAIN_SEL_FUT_MARKET_CODES = {
    'SC': ['AG', 'RB', 'HC', 'BU', 'RU', 'SP', 'SC', 'SS', 'AO', 'BR'],
    'ZC': ['SR', 'CF', 'FG', 'TA', 'MA', 'OI', 'RM', 'CY', 'SF', 'SM', 'AP', 'UR', 'SA', 'PK', 'PX', 'SH'], 
    'DC': ['M', 'Y', 'A', 'P', 'JM', 'I', 'C', 'CS', 'L', 'V', 'PP', 'EG', 'EB']
}

MAIN_FUT_CODES = ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG', 'RB', 'HC', 'BU', 'RU', 'FU', 'SP', 'WR', 'SC', 'NR', 'SS', 'AO', 'BR',
    'SR', 'CF', 'ZC', 'FG', 'TA', 'MA', 'OI', 'RM', 'CY', 'PM', 'RI', 'LR', 'JR', 'RS', 'SF', 'SM', 'AP', 'CJ', 'UR', 'SA', 'PF', 'PK', 'PX', 'SH',
    'M', 'Y', 'A', 'B', 'P', 'J', 'JM', 'I', 'RR', 'C', 'CS', 'JD', 'BB', 'FB', 'L', 'V', 'PP', 'EG', 'EB', 'PG', 'LH']

MAIN_SEL_FUT_CODES = ['AG', 'RB', 'HC', 'BU', 'RU', 'SP', 'SC', 'SS', 'AO', 'BR',
    'SR', 'CF', 'FG', 'TA', 'MA', 'OI', 'RM', 'CY', 'SF', 'SM', 'AP', 'UR', 'SA', 'PK', 'PX', 'SH',
    'M', 'Y', 'A', 'P', 'JM', 'I', 'C', 'CS', 'L', 'V', 'PP', 'EG', 'EB']

SF_MARKET_FUT_CODES = {'SF': ['IH', 'IF', 'IC', 'IM']}
SF_FUT_CODES = ['IH', 'IF', 'IC', 'IM']

class MARKET_CODE_SET(Enum):
    MAIN_FUT = 1      # 主力合约
    MAIN_SEL_FUT = 2  # 主选期货
    SF_FUT = 3        # 金融期货


# AICM中的一些常量定义
# 注意：其定义的顺序两边必须保证完全一致
FACOTR_NUM = 113
ALL_FACTOR_NAMES = [
    # 价量因子
    "OPEN", "HIGH", "LOW", "CLOSE", "VOLUME", "TYPICAL_PRICE", "NEW",
    "NEW_CHANGE_PERCENT", "SHORT_TERM_HIGH", "LONG_TERM_HIGH", "SHORT_TERM_LOW",
    "LONG_TERM_LOW",

    # 技术指标类因子
    "AD", "DX", "ADX", "ADXR", "APO", "AROON_UP", "AROON_DOWN", "ATR",
    "BOLL_UP", "BOLL_MID", "BOLL_DOWN", "CCI", "CMO",

    "MA_FAST", "MA_SLOW", "EMA_FAST", "EMA_SLOW", "DEMA_FAST", "DEMA_SLOW",
    "KAMA_FAST", "KAMA_SLOW", "MAMA_FAST", "MAMA_SLOW", "T3_FAST", "T3_SLOW",
    "TEMA_FAST", "TEMA_SLOW", "TRIMA_FAST", "TRIMA_SLOW", "TRIX_FAST",
    "TRIX_SLOW",

    "MACD", "MACD_DIFF", "MACD_DEA", "MFI", "MOM", "NATR", "OBV", "ROC", "RSI",
    "SAR", "TRANGE", "TSF", "ULTOSC", "WILLR",
    "KDJ_K", "KDJ_D",

    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE", "SQUEEZE_SIGNAL", "SQUEEZE_ZERO_BARS", "SQUEEZE_BAND_UPL",
    "SQUEEZE_BAND_DWL", "SQUEEZE_MDL", "SQUEEZE_KC_UPL", "SQUEEZE_KC_DWL",
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_UPL", "BAND_MDL", "BAND_DWL", "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",
    "BAND_BK_BARS", "BAR_STICK_LENGTH",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HIGHEST", "TREND_LOWEST", "TREND_HLR",
    "TREND_LEVEL",

    "HYO_TENKAN_SEN", "HYO_KIJUN_SEN", "HYO_CROSS_BARS", "TATR",
    "NATR_TL", "BAR_LENGTH",
]

# 使用两个值的因子
TWO_VAL_FACTOR_NAMES = [
    "VOLUME",
    "RSI", "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "BAND_GRADIENT",
    "TL_FAST", "TL_SLOW",
    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR"
]

# 市场及组合因子
# 增加了部分市场统计因子

SNAPSHOT_CONTEXT = [
  "COST_RNG", "DRAWDOWN_RNG", "STDDEV_RNG", "PNL", "POS_DAYS",
  "POS_SHORT_BARS", "POS_LONG_BARS", "SHORT_RANGE", "LONG_RANGE",
  
  "PF_YIELD_TREND", "PF_YIELD_HL", "AD_PS_RATIO", "PF_PS_RATIO",

  "DAYOFWEEK", "HOUR", "CURRENT_TIME",

  # AG市场
  "FAST_AG_RSI",  "FAST_AG_ZSCORE",  "FAST_AG_STDDEV",  "FAST_AG_DIRECT",
  "SLOW_AG_RSI",  "SLOW_AG_ZSCORE",  "SLOW_AG_STDDEV",  "SLOW_AG_DIRECT",

  # FUT市场
  # RSI
  "FAST_QH_RSI",  "FAST_QH_ZSCORE",  "FAST_QH_STDDEV", "FAST_QH_DIRECT",
  "SLOW_QH_RSI",  "SLOW_QH_ZSCORE",  "SLOW_QH_STDDEV", "SLOW_QH_DIRECT",

  # NATR
  "FAST_QH_NATR", "FAST_QH_NATR_ZSCORE",  "FAST_QH_NATR_STDDEV", "FAST_QH_NATR_DIRECT",
  "SLOW_QH_NATR", "SLOW_QH_NATR_ZSCORE",  "SLOW_QH_NATR_STDDEV", "SLOW_QH_NATR_DIRECT",

  # FLRS
  "FAST_QH_FLRS", "FAST_QH_FLRS_ZSCORE",  "FAST_QH_FLRS_STDDEV", "FAST_QH_FLRS_DIRECT",
  "SLOW_QH_FLRS", "SLOW_QH_FLRS_ZSCORE",  "SLOW_QH_FLRS_STDDEV", "SLOW_QH_FLRS_DIRECT",

  # MLRS
  "FAST_QH_MLRS", "FAST_QH_MLRS_ZSCORE",  "FAST_QH_MLRS_STDDEV", "FAST_QH_MLRS_DIRECT",
  "SLOW_QH_MLRS", "SLOW_QH_MLRS_ZSCORE",  "SLOW_QH_MLRS_STDDEV", "SLOW_QH_MLRS_DIRECT",

  # MOM
  "FAST_QH_MOM", "FAST_QH_MOM_ZSCORE",  "FAST_QH_MOM_STDDEV",  "FAST_QH_MOM_DIRECT",
  "SLOW_QH_MOM", "SLOW_QH_MOM_ZSCORE",  "SLOW_QH_MOM_STDDEV",  "SLOW_QH_MOM_DIRECT",
]

CATEGORY_FACTOR_NAMES = ["NATR_TL", "TREND_LEVEL", # TREND_VALUE
                         "FAST_QH_DIRECT", "FAST_QH_NATR_DIRECT", "FAST_QH_MOM_DIRECT", "FAST_QH_FLRS_DIRECT", "FAST_QH_MLRS_DIRECT",
                         "SLOW_QH_DIRECT", "SLOW_QH_NATR_DIRECT", "SLOW_QH_MOM_DIRECT", "SLOW_QH_FLRS_DIRECT", "SLOW_QH_MLRS_DIRECT",
                         "DAYOFWEEK", "HOUR"]
CATEGORY_FACTOR_NUMS = [9, 9,
                        9, 9, 9, 9, 9,
                        9, 9, 9, 9, 9,
                        5, 11]
class CustomEnumMeta(EnumMeta):
    def __new__(metacls, cls, bases, classdict):
        enum_class = super(CustomEnumMeta, metacls).__new__(metacls, cls, bases, classdict)
        enum_class._member_reverse_map = {v.value: v for v in enum_class.__members__.values()}
        return enum_class

    def __contains__(cls, member):
        return member in cls.__members__ or member in cls._member_reverse_map

    def __getitem__(self, item):
        try:
            return super(CustomEnumMeta, self).__getitem__(item)
        except KeyError:
            return self._member_reverse_map[item]


class CustomEnum(str, Enum, metaclass=CustomEnumMeta):
    def __repr__(self):
        return "%s.%s" % (
            self.__class__.__name__, self._name_)


class AGENT_MODE(CustomEnum):
    TRAIN = "训练"
    TEST = "测试"
    PRED = "预测"


