#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BarTokenizer可视化分析脚本
用于比较不同BarTokenizer方法的token分布情况和质量
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
import sys
import os
from typing import Dict, Any, Tuple

warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入BarTokenizer相关模块
try:
    from pyqlab.data.tokenizers.bar_tokenizer import (
        BarTokenizer,
        LinearMapping,
        QuantileMapping,
        AdaptiveMapping
    )
    print("成功导入BarTokenizer模块")
except ImportError as e:
    print(f"导入BarTokenizer模块失败: {e}")
    print("请确保项目路径正确")

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def prepare_test_data(data_path: str = None, sample_size: int = 10000) -> pd.DataFrame:
    """准备用于测试的K线数据"""
    try:
        if data_path:
            # 尝试加载真实数据
            if data_path.endswith('.csv'):
                df = pd.read_csv(data_path)
            elif data_path.endswith('.parquet'):
                df = pd.read_parquet(data_path)
            else:
                raise ValueError("不支持的文件格式")

            print(f"加载真实数据成功，数据量: {len(df)}")

            # 如果数据量太大，随机采样
            if len(df) > sample_size:
                df = df.sample(n=sample_size, random_state=42)
                print(f"数据采样到: {len(df)}")

            # 转换为标准OHLCV格式（如果需要）
            if not all(col in df.columns for col in ['open', 'high', 'low', 'close', 'volume']):
                print("数据不包含标准OHLCV列，生成模拟数据...")
                df = generate_mock_ohlcv_data(len(df))

        else:
            print("未提供数据路径，生成模拟数据")
            df = generate_mock_ohlcv_data(sample_size)

    except Exception as e:
        print(f"加载数据失败: {e}，使用模拟数据")
        df = generate_mock_ohlcv_data(sample_size)

    return df

def generate_mock_ohlcv_data(n: int = 10000) -> pd.DataFrame:
    """生成模拟的OHLCV数据"""
    np.random.seed(42)

    # 生成价格序列
    base_price = 100
    returns = np.random.normal(0, 0.02, n)
    prices = base_price * np.exp(np.cumsum(returns))

    # 生成OHLCV数据
    df = pd.DataFrame({
        'open': prices,
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),
        'close': prices * (1 + np.random.normal(0, 0.005, n)),
        'volume': np.random.lognormal(10, 1, n)
    })

    # 确保OHLC数据的合理性
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))

    return df

def analyze_tokenizer_methods(test_data: pd.DataFrame, n_bins: int = 100) -> Tuple[Dict, Dict]:
    """分析不同BarTokenizer方法的token分布情况和质量"""

    print(f"使用数据样本大小: {len(test_data)}")

    # 定义要测试的方法
    methods = {
        'Linear': {'mapping_strategy': 'linear', 'balancing_strategy': 'none'},
        'Linear+Balance': {'mapping_strategy': 'linear', 'balancing_strategy': 'frequency'},
        'Quantile': {'mapping_strategy': 'quantile', 'balancing_strategy': 'none'},
        'Quantile+Balance': {'mapping_strategy': 'quantile', 'balancing_strategy': 'frequency'},
        'Adaptive': {'mapping_strategy': 'adaptive', 'balancing_strategy': 'none'},
        'Adaptive+Balance': {'mapping_strategy': 'adaptive', 'balancing_strategy': 'frequency'}
    }

    results = {}
    tokenizers = {}

    # 测试每种方法
    for method_name, config in methods.items():
        print(f"\n测试方法: {method_name}")

        try:
            # 创建tokenizer
            tokenizer = BarTokenizer(
                mapping_strategy=config['mapping_strategy'],
                balancing_strategy=config['balancing_strategy'],
                n_bins=n_bins,
                features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
                atr_period=14
            )

            # 拟合和转换
            tokens = tokenizer.fit_transform(test_data)

            # 分析分布
            balance_metrics = tokenizer.analyze_balance(tokens)

            # 计算额外的统计信息
            unique_tokens, counts = np.unique(tokens, return_counts=True)
            frequencies = counts / len(tokens)

            results[method_name] = {
                'tokenizer': tokenizer,
                'tokens': tokens,
                'unique_tokens': unique_tokens,
                'counts': counts,
                'frequencies': frequencies,
                'balance_metrics': balance_metrics,
                'vocab_size': tokenizer.get_vocab_size(),
                'vocab_utilization': len(unique_tokens) / tokenizer.get_vocab_size()
            }

            tokenizers[method_name] = tokenizer

            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  唯一token数: {len(unique_tokens)}")
            print(f"  词汇表利用率: {len(unique_tokens) / tokenizer.get_vocab_size():.2%}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")

        except Exception as e:
            print(f"  错误: {e}")
            results[method_name] = None

    return results, tokenizers

def plot_token_distributions(results: Dict, max_tokens_to_show: int = 50):
    """绘制不同方法的token分布对比图"""

    valid_results = {k: v for k, v in results.items() if v is not None}
    n_methods = len(valid_results)

    if n_methods == 0:
        print("没有有效的结果可以绘制")
        return

    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    for idx, (method_name, result) in enumerate(valid_results.items()):
        if idx >= len(axes):
            break

        ax = axes[idx]

        # 获取频率数据
        frequencies = result['frequencies']
        unique_tokens = result['unique_tokens']

        # 只显示前N个最频繁的tokens
        if len(frequencies) > max_tokens_to_show:
            # 按频率排序
            sorted_indices = np.argsort(frequencies)[::-1]
            top_indices = sorted_indices[:max_tokens_to_show]
            plot_frequencies = frequencies[top_indices]
        else:
            plot_frequencies = frequencies

        # 绘制条形图
        bars = ax.bar(range(len(plot_frequencies)), plot_frequencies, alpha=0.7)

        # 设置标题和标签
        gini = result["balance_metrics"]["gini_coefficient"]
        entropy = result["balance_metrics"]["normalized_entropy"]
        ax.set_title(f'{method_name}\n基尼系数: {gini:.4f}, 标准化熵: {entropy:.3f}',
                    fontsize=11, fontweight='bold')
        ax.set_xlabel('Token排名')
        ax.set_ylabel('频率')

        # 添加网格
        ax.grid(True, alpha=0.3)

        # 高亮显示高频token
        max_freq = np.max(plot_frequencies)
        for i, (bar, freq) in enumerate(zip(bars, plot_frequencies)):
            if freq > max_freq * 0.1:  # 超过最大频率10%的token用红色标记
                bar.set_color('red')
                bar.set_alpha(0.8)

    # 隐藏多余的子图
    for idx in range(len(valid_results), len(axes)):
        axes[idx].set_visible(False)

    plt.tight_layout()
    plt.suptitle('不同BarTokenizer方法的Token分布对比', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

def plot_quality_metrics_comparison(results: Dict):
    """绘制质量指标对比图"""

    valid_results = {k: v for k, v in results.items() if v is not None}

    if len(valid_results) == 0:
        print("没有有效的结果可以绘制")
        return

    # 提取指标数据
    methods = list(valid_results.keys())
    gini_coeffs = [valid_results[m]['balance_metrics']['gini_coefficient'] for m in methods]
    normalized_entropies = [valid_results[m]['balance_metrics']['normalized_entropy'] for m in methods]
    vocab_utilizations = [valid_results[m]['vocab_utilization'] for m in methods]
    cv_values = [valid_results[m]['balance_metrics']['coefficient_of_variation'] for m in methods]

    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 基尼系数
    axes[0, 0].bar(methods, gini_coeffs, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('基尼系数 (越小越好)', fontweight='bold')
    axes[0, 0].set_ylabel('基尼系数')
    axes[0, 0].tick_params(axis='x', rotation=45)

    # 标准化熵
    axes[0, 1].bar(methods, normalized_entropies, color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('标准化熵 (越大越好)', fontweight='bold')
    axes[0, 1].set_ylabel('标准化熵')
    axes[0, 1].tick_params(axis='x', rotation=45)

    # 词汇表利用率
    axes[1, 0].bar(methods, vocab_utilizations, color='orange', alpha=0.7)
    axes[1, 0].set_title('词汇表利用率 (越大越好)', fontweight='bold')
    axes[1, 0].set_ylabel('利用率')
    axes[1, 0].tick_params(axis='x', rotation=45)

    # 变异系数
    axes[1, 1].bar(methods, cv_values, color='pink', alpha=0.7)
    axes[1, 1].set_title('变异系数 (越小越好)', fontweight='bold')
    axes[1, 1].set_ylabel('变异系数')
    axes[1, 1].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.suptitle('BarTokenizer质量指标对比', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

def print_summary_table(results: Dict):
    """打印汇总表格"""

    valid_results = {k: v for k, v in results.items() if v is not None}

    if len(valid_results) == 0:
        print("没有有效的结果")
        return

    print("\n" + "="*80)
    print("BarTokenizer方法对比汇总表")
    print("="*80)

    # 创建汇总数据
    summary_data = []
    for method_name, result in valid_results.items():
        metrics = result['balance_metrics']
        summary_data.append({
            '方法': method_name,
            '词汇表大小': result['vocab_size'],
            '唯一Token数': len(result['unique_tokens']),
            '利用率(%)': f"{result['vocab_utilization']*100:.1f}",
            '基尼系数': f"{metrics['gini_coefficient']:.4f}",
            '标准化熵': f"{metrics['normalized_entropy']:.3f}",
            '变异系数': f"{metrics['coefficient_of_variation']:.2f}",
            '频率范围': f"{metrics['frequency_range']:.4f}"
        })

    # 转换为DataFrame并打印
    df_summary = pd.DataFrame(summary_data)
    print(df_summary.to_string(index=False))
    print("="*80)

def main():
    """主函数"""
    print("BarTokenizer可视化分析工具")
    print("="*50)

    # 准备测试数据
    print("\n1. 准备测试数据...")

    # 可以在这里指定数据路径
    data_path = None  # 例如: 'f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv'

    # 检查环境变量中是否有数据路径
    import os
    if 'DATA_PATH' in os.environ:
        data_path = os.environ['DATA_PATH']
        print(f"使用环境变量指定的数据路径: {data_path}")

    test_data = prepare_test_data(data_path, sample_size=5000)

    print(f"测试数据准备完成，形状: {test_data.shape}")
    print(f"数据列: {test_data.columns.tolist()}")

    # 分析不同方法
    print("\n2. 分析不同BarTokenizer方法...")
    analysis_results, tokenizers = analyze_tokenizer_methods(test_data, n_bins=100)

    # 检查是否有有效结果
    valid_results = {k: v for k, v in analysis_results.items() if v is not None}
    if len(valid_results) == 0:
        print("❌ 没有成功的分析结果，请检查BarTokenizer模块是否正确安装")
        return

    print(f"✅ 成功分析了 {len(valid_results)} 种方法")

    # 可视化结果
    print("\n3. 生成可视化图表...")

    try:
        # 绘制token分布对比
        plot_token_distributions(analysis_results, max_tokens_to_show=50)

        # 绘制质量指标对比
        plot_quality_metrics_comparison(analysis_results)

        # 打印汇总表格
        print_summary_table(analysis_results)

        print("\n✅ 分析完成！请查看生成的图表。")

    except Exception as e:
        print(f"❌ 可视化过程中出错: {e}")
        print("但分析数据已生成，可以手动查看结果")

if __name__ == "__main__":
    main()
