# ONNX模型C++集成指南

## 问题描述

在C++中调用ONNX模型时，可能会遇到输出值异常的问题，例如出现极大或极小值（如-4.5943e+18）。这通常是由于数据类型不匹配、精度问题或内存布局差异导致的。

## 常见原因

1. **数据类型不匹配**：Python和C++处理数据类型的方式不同，特别是在浮点数精度方面。
2. **输入预处理差异**：C++和Python对输入数据的预处理方式可能不同。
3. **ONNX模型导出配置问题**：导出模型时的设置可能导致C++推理时出现问题。
4. **ORT会话配置差异**：ONNXRuntime在C++和Python中的会话配置可能不同。
5. **数值溢出**：C++实现中可能存在数值溢出问题。

## 解决方案

### 1. 修改模型导出代码

确保在导出ONNX模型时，明确指定输入和输出的数据类型：

```python
# 修改to_onnx方法
def to_onnx(self, path, input_shape=(1, 30), code_shape=(1,), time_shape=None, dynamic_axes=None):
    # ...
    
    # 准备示例输入 - 使用int32类型以确保C++兼容性
    dummy_input = torch.randint(0, self.vocab_size, input_shape, dtype=torch.int32)
    dummy_code = torch.randint(0, self.code_size, code_shape, dtype=torch.int32)
    
    if self.use_time_features and time_shape is not None:
        # 时间特征使用float32类型
        dummy_time = torch.rand(time_shape, dtype=torch.float32)
        # ...
    
    # 确保输出是float32类型
    def forward(self, *inputs):
        # ...
        return logits.to(torch.float32)
    
    # ...
```

### 2. 在C++中正确处理输入类型

确保在C++代码中使用与ONNX模型匹配的数据类型：

```cpp
// 输入tokens - 使用int32_t类型
std::vector<int32_t> input_tokens_int32(input_tokens.begin(), input_tokens.end());
input_tensors.push_back(Ort::Value::CreateTensor<int32_t>(
    memory_info,
    input_tokens_int32.data(),
    input_tokens_int32.size(),
    input_shape.data(),
    input_shape.size()
));

// 证券代码ID - 使用int32_t类型
std::vector<int32_t> code_id_int32 = {static_cast<int32_t>(code_id)};
// ...

// 时间特征 - 使用float类型
input_tensors.push_back(Ort::Value::CreateTensor<float>(
    memory_info,
    time_features_flat.data(),
    time_features_flat.size(),
    time_shape.data(),
    time_shape.size()
));
```

### 3. 检查输出数据类型

确保在C++中正确处理输出数据类型：

```cpp
// 获取输出 - 使用float类型
auto* output_data = output_tensors[0].GetTensorData<float>();
// ...
```

### 4. 验证ONNX模型

使用Python脚本验证导出的ONNX模型：

```python
# 验证模型输出类型
print("验证模型输入输出类型:")
for input in onnx_model.graph.input:
    print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
for output in onnx_model.graph.output:
    print(f"输出 {output.name}: {output.type.tensor_type.elem_type}")
```

### 5. 设置ONNXRuntime会话选项

在C++中设置适当的ONNXRuntime会话选项：

```cpp
Ort::SessionOptions session_options;
session_options.SetIntraOpNumThreads(1);
session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);
```

## 测试步骤

1. 使用修改后的`to_onnx`方法导出模型
2. 使用Python脚本验证导出的模型
3. 使用修改后的C++代码加载和运行模型
4. 比较Python和C++的输出结果

## 常见问题

### Q: 为什么Python中运行正常，但C++中出现异常值？

A: 这通常是由于数据类型不匹配导致的。Python中的数据类型处理更加灵活，而C++则更加严格。确保在导出模型时明确指定数据类型，并在C++中使用匹配的数据类型。

### Q: 如何检查ONNX模型的输入输出类型？

A: 可以使用以下Python代码检查：

```python
import onnx
model = onnx.load("model.onnx")
for input in model.graph.input:
    print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
for output in model.graph.output:
    print(f"输出 {output.name}: {output.type.tensor_type.elem_type}")
```

### Q: 如何在C++中调试ONNX模型？

A: 可以打印输入输出的形状和值，检查是否与Python中的一致：

```cpp
std::cout << "输出形状: [";
for (size_t i = 0; i < output_shape.size(); i++) {
    std::cout << output_shape[i];
    if (i < output_shape.size() - 1) std::cout << ", ";
}
std::cout << "]" << std::endl;

float min_val = *std::min_element(logits.begin(), logits.end());
float max_val = *std::max_element(logits.begin(), logits.end());
float sum = std::accumulate(logits.begin(), logits.end(), 0.0f);
float mean = sum / logits.size();

std::cout << "logits: [" << logits.size() << "] "
          << "Min: " << min_val << ", "
          << "Max: " << max_val << ", "
          << "Mean: " << mean << std::endl;
```

## 参考资料

- [ONNX Runtime C++ API](https://onnxruntime.ai/docs/api/c/namespace_ort.html)
- [PyTorch ONNX导出](https://pytorch.org/docs/stable/onnx.html)
- [ONNX数据类型](https://github.com/onnx/onnx/blob/master/docs/IR.md#types)
