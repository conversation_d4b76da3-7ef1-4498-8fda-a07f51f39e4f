"""
CandlestickVQGPT模型的token分布分析工具
专门分析和解决token 795等高频token过度集中的问题
"""

import os
import sys
import torch
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt2.candlestick_vq_dataset import CandlestickDataset
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer
from pyqlab.models.gpt.token_balance_utils import TokenBalancer


def load_candlestick_data(data_path):
    """加载K线数据"""
    print(f"加载K线数据: {data_path}")
    
    # 这里需要根据实际数据格式实现
    # 示例：假设数据是CSV格式
    data_files = []
    code_ids = []
    
    if os.path.isfile(data_path) and data_path.endswith('.csv'):
        # 单个文件
        df = pd.read_csv(data_path)
        data_files.append(df)
        code_ids.append(0)  # 默认代码ID
    elif os.path.isdir(data_path):
        # 目录中的多个文件
        for i, file_path in enumerate(sorted(Path(data_path).glob("*.csv"))):
            df = pd.read_csv(file_path)
            data_files.append(df)
            code_ids.append(i)
    else:
        raise ValueError(f"无效的数据路径: {data_path}")
    
    print(f"加载完成，共 {len(data_files)} 个数据文件")
    return data_files, code_ids


def analyze_dataset_tokens(dataset, save_dir):
    """分析数据集中的token分布"""
    print("分析数据集token分布...")
    
    # 收集所有token
    all_tokens = []
    sequence_tokens = []
    
    for i in range(len(dataset)):
        sample = dataset[i]
        
        if isinstance(sample, dict):
            if 'targets' in sample:
                tokens = sample['targets'].tolist()
            else:
                tokens = sample['x'].tolist()
        else:
            # 假设是tuple格式
            tokens = sample[0].tolist() if hasattr(sample[0], 'tolist') else sample[0]
        
        # 过滤无效token
        valid_tokens = [t for t in tokens if t != -1]
        if valid_tokens:
            all_tokens.extend(valid_tokens)
            sequence_tokens.append(valid_tokens)
    
    # 基础统计
    total_tokens = len(all_tokens)
    token_counter = Counter(all_tokens)
    unique_tokens = len(token_counter)
    
    print(f"总token数: {total_tokens:,}")
    print(f"唯一token数: {unique_tokens}")
    print(f"平均序列长度: {total_tokens/len(sequence_tokens):.1f}")
    
    # 分析高频token
    print("\n=== 高频Token分析 ===")
    most_common = token_counter.most_common(20)
    
    high_freq_tokens = []
    for token_id, count in most_common:
        frequency = count / total_tokens
        high_freq_tokens.append((token_id, count, frequency))
        
        status = ""
        if frequency > 0.3:
            status = "🚨 严重不平衡"
        elif frequency > 0.2:
            status = "⚠️ 中度不平衡"
        elif frequency > 0.1:
            status = "⚡ 轻度不平衡"
        
        print(f"Token {token_id}: {count:,}次 ({frequency:.2%}) {status}")
    
    # 计算基尼系数
    frequencies = [count/total_tokens for count in token_counter.values()]
    frequencies.sort()
    n = len(frequencies)
    gini = (2 * sum((i+1) * freq for i, freq in enumerate(frequencies))) / (n * sum(frequencies)) - (n+1) / n
    
    print(f"\n=== 分布不平衡指标 ===")
    print(f"基尼系数: {gini:.4f} (0=完全平衡, 1=完全不平衡)")
    print(f"Top-10 token占比: {sum(count for _, count in token_counter.most_common(10))/total_tokens:.2%}")
    print(f"Top-5 token占比: {sum(count for _, count in token_counter.most_common(5))/total_tokens:.2%}")
    
    # 绘制分布图
    plot_token_distribution(token_counter, save_dir)
    
    # 生成解决方案建议
    generate_solutions(gini, high_freq_tokens, save_dir)
    
    return {
        'total_tokens': total_tokens,
        'unique_tokens': unique_tokens,
        'gini_coefficient': gini,
        'high_freq_tokens': high_freq_tokens,
        'token_counter': token_counter,
        'sequence_tokens': sequence_tokens
    }


def plot_token_distribution(token_counter, save_dir):
    """绘制token分布图"""
    print("绘制token分布图...")
    
    # 获取前50个最常见的token
    top_tokens = token_counter.most_common(50)
    tokens, counts = zip(*top_tokens)
    
    plt.figure(figsize=(16, 10))
    
    # 子图1: 条形图
    plt.subplot(2, 2, 1)
    bars = plt.bar(range(len(tokens)), counts, color='skyblue')
    plt.xlabel('Token排名')
    plt.ylabel('出现次数')
    plt.title('Top 50 Token分布')
    plt.xticks(range(0, len(tokens), 5), [f'T{tokens[i]}' for i in range(0, len(tokens), 5)], rotation=45)
    
    # 标记高频token
    total_tokens = sum(token_counter.values())
    for i, (token_id, count) in enumerate(top_tokens[:10]):
        percentage = count / total_tokens
        if percentage > 0.1:
            bars[i].set_color('red')
            plt.text(i, count, f'{percentage:.1%}', ha='center', va='bottom', fontsize=8)
    
    # 子图2: 累积分布
    plt.subplot(2, 2, 2)
    cumulative_counts = np.cumsum(counts)
    cumulative_percentages = cumulative_counts / total_tokens * 100
    plt.plot(range(len(tokens)), cumulative_percentages, 'b-', linewidth=2)
    plt.xlabel('Token排名')
    plt.ylabel('累积百分比 (%)')
    plt.title('Token累积分布')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=80, color='r', linestyle='--', alpha=0.7, label='80%线')
    plt.axhline(y=90, color='orange', linestyle='--', alpha=0.7, label='90%线')
    plt.legend()
    
    # 子图3: 频率直方图
    plt.subplot(2, 2, 3)
    frequencies = [count/total_tokens for count in token_counter.values()]
    plt.hist(frequencies, bins=50, alpha=0.7, color='green')
    plt.xlabel('Token频率')
    plt.ylabel('Token数量')
    plt.title('Token频率分布直方图')
    plt.yscale('log')
    
    # 子图4: 洛伦兹曲线（基尼系数可视化）
    plt.subplot(2, 2, 4)
    sorted_freqs = sorted(frequencies)
    cumulative_freqs = np.cumsum(sorted_freqs)
    cumulative_freqs = cumulative_freqs / cumulative_freqs[-1]
    x = np.linspace(0, 1, len(cumulative_freqs))
    
    plt.plot(x, cumulative_freqs, 'b-', linewidth=2, label='洛伦兹曲线')
    plt.plot([0, 1], [0, 1], 'r--', alpha=0.7, label='完全平等线')
    plt.fill_between(x, cumulative_freqs, x, alpha=0.3)
    plt.xlabel('累积Token比例')
    plt.ylabel('累积频率比例')
    plt.title('洛伦兹曲线 (基尼系数可视化)')
    plt.legend()
    
    plt.tight_layout()
    
    # 保存图片
    plot_path = os.path.join(save_dir, "token_distribution_analysis.png")
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"分布图已保存到: {plot_path}")


def generate_solutions(gini, high_freq_tokens, save_dir):
    """生成解决方案建议"""
    print("\n=== 生成解决方案建议 ===")
    
    solutions = []
    
    # 根据基尼系数提供建议
    if gini > 0.8:
        solutions.append("🚨 严重不平衡，建议采用多种方法组合:")
        solutions.append("   1. 使用Focal Loss (gamma=2.0, alpha=0.25)")
        solutions.append("   2. 计算类别权重并应用到损失函数")
        solutions.append("   3. 数据重采样或增强")
        solutions.append("   4. 增加多样性正则化权重到0.2")
        solutions.append("   5. 降低学习率到1e-5")
    elif gini > 0.6:
        solutions.append("⚠️ 中度不平衡，建议:")
        solutions.append("   1. 使用Focal Loss或加权交叉熵")
        solutions.append("   2. 轻微的数据重采样")
        solutions.append("   3. 增加dropout到0.15")
        solutions.append("   4. 多样性正则化权重0.1")
    else:
        solutions.append("⚡ 轻度不平衡，建议:")
        solutions.append("   1. 使用标签平滑(0.05)")
        solutions.append("   2. 调整学习率和训练策略")
    
    # 针对特定高频token的建议
    if high_freq_tokens:
        top_token = high_freq_tokens[0]
        if top_token[2] > 0.5:  # 频率超过50%
            solutions.append(f"\n🎯 Token {top_token[0]} 占比过高 ({top_token[2]:.1%}):")
            solutions.append("   1. 检查该token对应的K线模式")
            solutions.append("   2. 考虑细分该模式或调整向量化方法")
            solutions.append("   3. 在损失函数中特别处理该token")
            solutions.append("   4. 增加该token的惩罚权重")
    
    # 保存建议到文件
    solutions_path = os.path.join(save_dir, "balance_solutions.txt")
    with open(solutions_path, 'w', encoding='utf-8') as f:
        f.write("Token平衡解决方案建议\n")
        f.write("=" * 50 + "\n")
        for solution in solutions:
            f.write(solution + "\n")
            print(solution)
        
        f.write("\n具体配置建议:\n")
        f.write("=" * 30 + "\n")
        f.write("# 模型训练参数\n")
        f.write("TRAINING_CONFIG = {\n")
        f.write(f"    'learning_rate': {3e-5 if gini > 0.7 else 5e-5},\n")
        f.write("    'weight_decay': 0.1,\n")
        f.write("    'batch_size': 32,\n")
        f.write(f"    'epochs': {150 if gini > 0.7 else 100},\n")
        f.write("    'dropout': 0.15,\n")
        f.write(f"    'label_smoothing': {0.05 if gini > 0.7 else 0.03},\n")
        f.write("    'use_focal_loss': True,\n")
        f.write("    'focal_gamma': 2.0,\n")
        f.write("    'focal_alpha': 0.25,\n")
        f.write(f"    'diversity_weight': {0.2 if gini > 0.8 else 0.1},\n")
        f.write("    'use_class_weights': True,\n")
        f.write("    'use_balanced_sampling': True\n")
        f.write("}\n")
    
    print(f"解决方案已保存到: {solutions_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析CandlestickVQGPT的token分布')
    parser.add_argument('--data_path', type=str, required=True, help='K线数据路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本权重路径')
    parser.add_argument('--output_dir', type=str, default='./token_analysis', help='输出目录')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--stride', type=int, default=1, help='滑动窗口步长')
    
    # Tokenizer参数
    parser.add_argument('--num_embeddings', type=int, default=1024, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=5, help='嵌入维度')
    parser.add_argument('--atr_period', type=int, default=14, help='ATR周期')
    parser.add_argument('--ma_volume_period', type=int, default=20, help='成交量移动平均周期')
    parser.add_argument('--vectorization_method', type=str, default='atr_based', help='向量化方法')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("开始CandlestickVQGPT Token分布分析...")
    
    try:
        # 1. 加载数据
        data_files, code_ids = load_candlestick_data(args.data_path)
        
        # 2. 创建tokenizer
        print(f"创建tokenizer: {args.codebook_path}")
        tokenizer = CandlestickVQTokenizer(
            codebook_weights_path=args.codebook_path,
            num_embeddings=args.num_embeddings,
            embedding_dim=args.embedding_dim,
            atr_period=args.atr_period,
            ma_volume_period=args.ma_volume_period,
            vectorization_method=args.vectorization_method
        )
        
        # 3. 创建数据集
        print("创建数据集...")
        dataset = CandlestickDataset(
            data=data_files,
            code_ids=code_ids,
            tokenizer=tokenizer,
            seq_len=args.seq_len,
            stride=args.stride
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 4. 分析token分布
        analysis_results = analyze_dataset_tokens(dataset, args.output_dir)
        
        # 5. 使用TokenBalancer进行更详细的分析
        print("\n=== 使用TokenBalancer进行详细分析 ===")
        balancer = TokenBalancer(tokenizer.vocab_size)
        balancer.analyze_token_distribution(
            analysis_results['sequence_tokens'],
            save_plot=True,
            plot_path=os.path.join(args.output_dir, "detailed_token_distribution.png")
        )
        
        # 计算类别权重
        class_weights = balancer.compute_class_weights(method='sqrt_inverse_freq')
        
        # 保存类别权重
        weights_path = os.path.join(args.output_dir, "class_weights.pt")
        torch.save(class_weights, weights_path)
        print(f"类别权重已保存到: {weights_path}")
        
        print(f"\n✅ 分析完成！所有结果已保存到: {args.output_dir}")
        print("\n下一步建议:")
        print("1. 查看生成的分析图表和解决方案")
        print("2. 使用平衡训练脚本重新训练模型")
        print("3. 应用建议的配置参数")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
