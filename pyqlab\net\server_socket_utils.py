import errno
import socket
import threading
import time
import struct
import zlib
# from logging import getLogger
import logging
from abc import ABC, abstractmethod
from pyqlab.net.message import Message


class ServerSocketUtils(ABC):

    def __init__(self, ip: str, port: int):
        # self.name = name
        self.ip = ip
        self.port = port
        self.BUFSIZE = 1024
        self.HEARTBEAT_INTERVAL = 60
        self.server_socket = None
        self.client_sockets = []
        # console_handler = logging.StreamHandler()
        # formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        # console_handler.setFormatter(formatter)
        # self.logger = logging.getLogger(__name__)
        # self.logger.addHandler(console_handler)

    # Define the heartbeat function to be executed each n seconds
    def heart_beat(self):
        while True:
            try:
                for connection in self.client_sockets:
                    msg = Message()
                    msg.command = 0
                    msg.business_type = 0
                    msg.app_id = 1
                    msg.body = "H"
                    # Send a heartbeat message to each connected client
                    connection.sendall(msg.package_msg())
                # Wait for the specified period
            except Exception as e:
                print(e)
            time.sleep(self.HEARTBEAT_INTERVAL)

    def regist(self):
        msg = Message()
        msg.command = 1
        msg.business_type = 0
        msg.app_id = 1
        msg.body = "R"
        self.send(msg.package_msg())

    def handle_client(self, client_socket, client_address):
        """
        处理客户端连接的函数
        """
        print(f"[INFO] New client connected: {client_address}")
        remain_size = 0
        recv_len = 0
        total_data = b''
        data = b''
        while True:
            try:
                if remain_size == 0 or remain_size >= self.BUFSIZE:
                    recv_len = self.BUFSIZE
                else:
                    recv_len = remain_size

                data = client_socket.recv(recv_len)
                if not data:
                    # print("error:not data.")
                    break
                total_data = total_data + data
                if len(total_data) < 8:
                    print("recev message data error. %s", total_data)
                    total_data = b''
                    remain_size == 0
                    continue
                header = total_data[0:8]
                tag, st, msg_size = struct.unpack('<3sbi', header)
                #if msg_size > 7:
                #    print("Header tag: %s %d msg_size: %d" % (tag, st, msg_size))
                #    pass
                if tag.decode('ascii', 'ignore') == 'XXH':
                    if (len(total_data) - 8) < msg_size: # 包未收全
                        remain_size = msg_size - len(total_data) + 8
                        print("remain_size: %d = %d - %d" % (remain_size, msg_size, len(total_data)+8))
                    else: # 包已收全
                        #if msg_size > 15:
                        #    # print("next msg size: %d = %d - %d" % (len(total_data) - 8 - msg_size, len(total_data) - 8, msg_size))
                        #    pass
                        msg = Message()
                        msg.unpackage_msg(total_data[8:msg_size + 8], msg_size) # 解包
                        if msg.command == 0:
                            print("heart beat.")
                            #pass
                        elif msg.command == 1:
                            print("regist client")
                        else:
                            # 解包校验：json(app_id = 0) 和 压缩包(app_id = 1)
                            # if msg.app_id ==  0 and len(msg.body) > 3 and msg.body[-1:] != b'}' and msg.body[-2:] != b'}\n':
                            #     self.logger.error("error message(%d, %d)", len(total_data), remain_size)
                            #     remain_size = 0
                            #     total_data = b''
                            #     continue
                            # else:
                            self.handle_data(msg)
                        if remain_size - 8 > msg_size: # 将多余部分转给下一个包
                            remain_size = len(total_data) - 8 - msg_size
                            total_data = total_data[(8 + msg_size):]
                            # print("next msg size: %d " % remain_size)
                        else:
                            remain_size = 0
                            total_data = b''
                else:
                    print(f"unknow message({remain_size})")
                    data = b''
                    total_data = b''
                    remain_size = 0
                    continue
                #time.sleep(1)
            except socket.error as e:
                if e.errno == errno.ECONNRESET:
                    print('recv failed %s' % e)
                else:
                    total_data = b''
                    remain_size = 0
                    # raise
                break

        self.client_sockets.remove(client_socket)
        client_socket.shutdown(socket.SHUT_RDWR)
        client_socket.close()
        self.handle_disconnect()
        print("stop client work thread!")

    @abstractmethod
    def handle_disconnect(self):
        """
        处理客户端断开连接的函数
        """
        raise NotImplementedError
    
    @abstractmethod
    def handle_data(self, data: Message):
        """
        处理接收到的数据的函数
        """
        raise NotImplementedError

    def send(self, client_socket, data: bytes):
        """
        发送数据的函数
        """
        try:
            if client_socket:
                client_socket.sendall(data)
        except socket.error as e:
            errmsg = ''
            if e.errno in (errno.ECONNRESET, errno.EPIPE):
                errmsg = 'send failed1, errno:%d, %s' % (e.errno, e)
                print(errmsg)
            else:
                errmsg = 'send failed2, errno:%d, %s' % (e.errno, e)
                print(errmsg)
                # raise
            self.close()
        except Exception as e:
            print('send failed, errno: %s' % e)

    def send_all(self, data: bytes):
        try:
            for connection in self.client_sockets:
                # Send a heartbeat message to each connected client
                connection.sendall(data)
            # Wait for the specified period
        except Exception as e:
            print(e)

    def start_server(self):
        """
        启动socket服务器的函数
        """
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.bind((self.ip, self.port))
            self.server_socket.listen()
            print(f"[INFO] Server is listening on {self.ip}:{self.port}")

            # Start the heartbeat thread
            t = threading.Thread(target=self.heart_beat)
            t.start()
            while True:
                client_socket, client_address = self.server_socket.accept()
                self.client_sockets.append(client_socket)
                print(f"New connected: {client_address}")
                client_thread = threading.Thread(target=self.handle_client, args=(client_socket, client_address))
                client_thread.start()
        except Exception as e:
            print(e)