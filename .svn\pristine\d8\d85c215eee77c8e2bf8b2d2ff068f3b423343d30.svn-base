
import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary

from pyqlab.models import PLData
from pyqlab.models import PLTsModel

from pyqlab.data.data_api import get_dataset
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import TimeSeriesSplit, KFold
import os
import random
import numpy as np
from datetime import datetime
from time import time

from pyqlab.data import FTSDataset
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
from pyqlab.pl.utils import get_best_saved_model_filename

def get_trainer_name(args):
    time_encoding = 'T' if args.embed_time == 'timeF' else 'F'
    if  "IF" in args.fut_codes:
        trainer_name = f'SF_{args.version}_{time_encoding}_{args.seq_len}_{args.n_heads}_{args.e_layers}'
    else:
        trainer_name = f'FUT_{args.version}_{time_encoding}_{args.seq_len}_{args.n_heads}_{args.e_layers}'
    return trainer_name

def save_model_as_to_onnx(args, dataset):
    trainer_name=get_trainer_name(args)
    model_path, best_socre = get_best_saved_model_filename(
        log_dir=args.log_dir,
        sub_dir=trainer_name
    )
    if model_path is None:
        raise Exception("No saved model found!")
    print(f"=== Resume Training ===")
    print(f"Loading model from {model_path}")
    try:
        model = PLTsModel.load_from_checkpoint(checkpoint_path=model_path)
    except Exception as e:
        print(f"Error: {e}")

    model.freeze()
    embds = torch.zeros(1, args.seq_len* len(args.num_embeds)).to(torch.int32)
    embds = embds.reshape(1, args.seq_len, len(args.num_embeds))
    x = torch.zeros(1, args.seq_len * args.enc_in).to(torch.float32)
    x = x.reshape(1, args.seq_len, args.enc_in)
    y = torch.zeros(1, (args.pred_len + args.label_len) * args.enc_in).to(torch.float32)
    y = y.reshape(1, (args.pred_len + args.label_len), args.enc_in)
    if args.embed_time == 'timeF':
        x_mark = torch.zeros(1, args.seq_len * 5).to(torch.float32)
        x_mark = x_mark.reshape(1, args.seq_len, 5)
        y_mark = torch.zeros(1, (args.pred_len + args.label_len) * 5).to(torch.float32)
        y_mark = y_mark.reshape(1, (args.pred_len + args.label_len), 5)
    else:
        x_mark = torch.zeros(1, args.seq_len * 5).to(torch.int32)
        x_mark = x_mark.reshape(1, args.seq_len, 5)
        y_mark = torch.zeros(1, (args.pred_len + args.label_len) * 5).to(torch.int32)
        y_mark = y_mark.reshape(1, (args.pred_len + args.label_len), 5)
    tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
    model_name = f"{trainer_name}_{tm_str}_{best_socre}_ls"
    model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (embds, x, x_mark, None, None), export_params=True)
    print(f"Model saved to： {args.model_dir}/{model_name}.onnx")
    dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")


def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    return callbacks


def main(args):
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    args.num_embeds = eval(args.num_embeds)
    args.ds_files = eval(args.ds_files)
    args.fut_codes = eval(args.fut_codes)
    args.seed = random.randint(0, 10000)
    timeenc = None
    if args.embed_time == 'timeF':
        timeenc = 1
    elif args.embed_time == 'fixed':
        timeenc = 0

    print(args)

    dataset = get_dataset(ds_files=args.ds_files,
                          ins_nums=args.ins_nums,
                          is_normal=args.is_normal,
                          verbose=args.verbose,
                          fut_codes=args.fut_codes,
                          data_path=args.data_path,
                          start_time=args.start_time,
                          end_time=args.end_time,
                          timeenc=timeenc,
                          model_type=args.model_type,
                          seq_len=args.seq_len,
                          label_len=args.label_len,
                          pred_len=args.pred_len,                          
                          )

    if args.save_as_to_onnx:
        save_model_as_to_onnx(args, dataset)
        return

    pl.seed_everything(args.seed)

    # 加载数据集: 直接从dataframe中获取数据，不分割打包
    dataset.load_data()

    trainer_name=get_trainer_name(args)
    model = None

    # 定义交叉验证
    # 使用 KFold 分割数据集
    kfold = KFold(n_splits=args.k_folds, shuffle=True, random_state=args.seed)
    # 创建一个数据模块列表，每个数据模块对应一个 fold
    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
        print(f"=== Training fold {fold} ===")
        train_data = Subset(dataset, train_idx)
        val_data = Subset(dataset, val_idx)
        print(f"Train data: {len(train_data)}, Val data: {len(val_data)}, Total data: {len(dataset)}")
        data_module = PLData(
            train_data,
            val_data,
            batch_size=args.batch_size,
            num_workers=args.num_workers,
            seed=args.seed
        )

        logger = TensorBoardLogger(save_dir=args.log_dir, name=trainer_name)
        # if fold > 0 and callbacks[0].stopped_epoch is not None:
            # 加载之前训练的模型
            # print(f"Fold: {fold} Loading model from {callbacks[1].best_model_path}")
            # model = PLTsModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        if model is None:
            if args.resume:
                model_path, _= get_best_saved_model_filename(
                    log_dir=args.log_dir,
                    sub_dir=trainer_name
                )
                if model_path is None:
                    raise Exception("No saved model found!")
                print(f"=== Resume Training ===")
                print(f"Loading model from {model_path}")
                try:
                    model = PLTsModel.load_from_checkpoint(checkpoint_path=model_path)
                except Exception as e:
                    print(f"Error: {e}")
            else:
                print(f"=== New Training ===")
                model = PLTsModel(**vars(args))

        # 创建回调函数
        callbacks = load_callbacks(args)

        # 创建训练器
        trainer = Trainer(
            accelerator='auto',
            devices='auto',
            # precision='16-true',
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
        )
        trainer.fit(model, data_module)
        # 在每个数据集的训练之后，执行其他操作（如保存模型、记录日志等）
        # log_metrics(logger, trainer)

    # 训练完成后，保存编译最后一个模型
    if callbacks[0].stopped_epoch is not None:
        # 加载之前训练的模型
        print(f"Best model to save {callbacks[1].best_model_path}")
        best_score=callbacks[1].best_model_score.cpu().numpy()
        model = PLTsModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        model.freeze()
        embds = torch.zeros(1, args.seq_len* len(args.num_embeds)).to(torch.int32)
        embds = embds.reshape(1, args.seq_len, len(args.num_embeds))
        x = torch.zeros(1, args.seq_len * args.enc_in).to(torch.float32)
        x = x.reshape(1, args.seq_len, args.enc_in)
        y = torch.zeros(1, (args.pred_len + args.label_len) * args.enc_in).to(torch.float32)
        y = y.reshape(1, (args.pred_len + args.label_len), args.enc_in)
        if args.embed_time == 'timeF':
            x_mark = torch.zeros(1, args.seq_len * 5).to(torch.float32)
            x_mark = x_mark.reshape(1, args.seq_len, 5)
            y_mark = torch.zeros(1, (args.pred_len + args.label_len) * 5).to(torch.float32)
            y_mark = y_mark.reshape(1, (args.pred_len + args.label_len), 5)
        else:
            x_mark = torch.zeros(1, args.seq_len * 5).to(torch.int32)
            x_mark = x_mark.reshape(1, args.seq_len, 5)
            y_mark = torch.zeros(1, (args.pred_len + args.label_len) * 5).to(torch.int32)
            y_mark = y_mark.reshape(1, (args.pred_len + args.label_len), 5)
        tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
        model_name=f"{trainer_name}_{tm_str}_%.3f_ls" % best_score
        model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (embds, x, x_mark, y, y_mark), export_params=True)
        dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")

        #查看模型大小
        model.example_input_array = (embds, x, x_mark,  y, y_mark)
        summary = ModelSummary(model, max_depth=-1)
        print(summary) 

        print(f"Model saved to： {model_name}.onnx")
        print("=== Training Finished ===\n\n")

if __name__ == '__main__':
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', type=str)
    parser.add_argument('--ds_files', default='["main.2024"]', type=str)
    parser.add_argument('--start_time', default='', type=str)
    parser.add_argument('--end_time', default='', type=str)
    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)
    parser.add_argument('--is_normal', default=True, action='store_true')
    parser.add_argument('--verbose', action='store_true')
    parser.add_argument('--fut_codes', default='MAIN_SEL_FUT_CODES', choices=['MAIN_FUT_CODES', 'MAIN_SEL_FUT_CODES', 'SF_FUT_CODES'], type=str)
    parser.add_argument('--data_path', default='e:/featdata/main', type=str)
    parser.add_argument('--model_type', default=1, type=int)
    parser.add_argument('--freq', type=str, default='t', help='freq for time features encoding, options:[s:secondly, t:minutely, h:hourly, d:daily, b:business days, w:weekly, m:monthly], you can also use more detailed freq like 15min or 3h')

    # Model Hyperparameters =================
    parser.add_argument('--version', default='NonTrans', type=str)
    parser.add_argument('--model_name', default='Nonstationary_Transformer', type=str)
    parser.add_argument('--model_path', default='pyqlab.models.transformer', type=str)
    parser.add_argument('--loss', default='mse', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)

    # model
    parser.add_argument('--num_embeds', default='[72]', type=str)
    parser.add_argument('--num_channel', default=10, type=int) # 通道数,与上面的DataHander中的win保持一致
    parser.add_argument('--num_input', default=51, type=int)
    parser.add_argument('--out_channels', default='(24, 48, 1200, 1200)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)
    parser.add_argument('--kernel_size', default=3, type=int)
    parser.add_argument('--pooling', default='max', choices=['max', 'avg'], type=str)

    # Training Info
    parser.add_argument('--max_epochs', default=5, type=int)
    parser.add_argument('--early_stop', default=5, type=int)
    parser.add_argument('--min_delta', default=1e-3, type=float) 
    parser.add_argument('--k_folds', default='5', type=int)

    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)
    parser.add_argument('--optimizer', default='adam', choices=['adam', 'adamw'], type=str)

    # basic config
    parser.add_argument('--task_name', type=str, default='long_term_forecast', help='task name, options:[long_term_forecast, short_term_forecast, imputation, classification, anomaly_detection]')
    # parser.add_argument('--is_training', type=int, default=1, help='status')
    # parser.add_argument('--model_id', type=str, default='test', help='model id')
    # parser.add_argument('--model', type=str, default='Autoformer', help='model name, options: [Autoformer, Transformer, TimesNet]')

    # forecasting task
    parser.add_argument('--seq_len', type=int, default=30, help='input sequence length')
    parser.add_argument('--label_len', type=int, default=15, help='start token length')
    parser.add_argument('--pred_len', type=int, default=3, help='prediction sequence length')
    parser.add_argument('--seasonal_patterns', type=str, default='Monthly', help='subset for M4')
    parser.add_argument('--inverse', action='store_true', help='inverse output data')

    # model define
    parser.add_argument('--expand', type=int, default=2, help='expansion factor for Mamba')
    parser.add_argument('--d_conv', type=int, default=4, help='conv kernel size for Mamba')
    parser.add_argument('--top_k', type=int, default=5, help='for TimesBlock')
    parser.add_argument('--num_kernels', type=int, default=6, help='for Inception')
    parser.add_argument('--enc_in', type=int, default=120, help='encoder input size')
    parser.add_argument('--dec_in', type=int, default=120, help='decoder input size')
    parser.add_argument('--c_out', type=int, default=120, help='output size')
    parser.add_argument('--d_model', type=int, default=256, help='dimension of model')
    parser.add_argument('--n_heads', type=int, default=8, help='num of heads')
    parser.add_argument('--e_layers', type=int, default=2, help='num of encoder layers')
    parser.add_argument('--d_layers', type=int, default=1, help='num of decoder layers')
    parser.add_argument('--d_ff', type=int, default=2048, help='dimension of fcn')
    parser.add_argument('--moving_avg', type=int, default=25, help='window size of moving average')
    parser.add_argument('--factor', type=int, default=1, help='attn factor')
    parser.add_argument('--distil', action='store_false', help='whether to use distilling in encoder, using this argument means not using distilling')
    parser.add_argument('--dropout', type=float, default=0.1, help='dropout')
    parser.add_argument('--embed_time', type=str, default='fixed', help='time features encoding, options:[timeF, fixed, learned]')
    parser.add_argument('--activation', type=str, default='gelu', help='activation')
    parser.add_argument('--output_attention', action='store_true', help='whether to output attention in ecoder')
    parser.add_argument('--channel_independence', type=int, default=1, help='0: channel dependence 1: channel independence for FreTS model')
    parser.add_argument('--decomp_method', type=str, default='moving_avg', help='method of series decompsition, only support moving_avg or dft_decomp')
    parser.add_argument('--use_norm', type=int, default=1, help='whether to use normalize; True 1 False 0')
    parser.add_argument('--down_sampling_layers', type=int, default=0, help='num of down sampling layers')
    parser.add_argument('--down_sampling_window', type=int, default=1, help='down sampling window size')
    parser.add_argument('--down_sampling_method', type=str, default=None, help='down sampling method, only support avg, max, conv')
    parser.add_argument('--seg_len', type=int, default=48, help='the length of segmen-wise iteration of SegRNN')

    # optimization
    parser.add_argument('--num_workers', type=int, default=0, help='data loader num workers')
    # parser.add_argument('--itr', type=int, default=1, help='experiments times')
    # parser.add_argument('--train_epochs', type=int, default=10, help='train epochs')
    parser.add_argument('--batch_size', type=int, default=32, help='batch size of train input data')
    parser.add_argument('--patience', type=int, default=3, help='early stopping patience')
    # parser.add_argument('--learning_rate', type=float, default=0.0001, help='optimizer learning rate')
    # parser.add_argument('--des', type=str, default='test', help='exp description')
    # parser.add_argument('--loss', type=str, default='MSE', help='loss function')
    parser.add_argument('--lradj', type=str, default='type1', help='adjust learning rate')
    parser.add_argument('--use_amp', action='store_true', help='use automatic mixed precision training')

    # de-stationary projector params
    parser.add_argument('--p_hidden_dims', type=int, nargs='+', default=[16, 16, 16, 16], help='hidden layer dimensions of projector (List)')
    parser.add_argument('--p_hidden_layers', type=int, default=4, help='number of hidden layers in projector')

    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    parser.add_argument('--resume', action='store_true')
    parser.add_argument('--model_dir', default='model', type=str)
    parser.add_argument('--save_as_to_onnx', action='store_true')

    args = parser.parse_args()

    main(args)


