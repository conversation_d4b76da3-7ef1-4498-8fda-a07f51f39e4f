{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import warnings\n", "import numpy\n", "\n", "def warn(*args, **kwargs):\n", "    pass\n", "\n", "warnings.warn = warn\n", "warnings.simplefilter(action='ignore', category=FutureWarning)\n", "numpy.seterr(divide = 'ignore') \n", "\n", "sys.path.append(os.path.dirname(os.path.abspath('')))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [], "source": ["import ccxt\n", "\n", "from tensortrade.strategies import TensorforceTradingStrategy"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"scrolled": true}, "outputs": [], "source": ["from tensortrade.rewards import SimpleProfitStrategy\n", "from tensortrade.actions import DiscreteActionStrategy\n", "from tensortrade.exchanges.simulated import FBMExchange\n", "from tensortrade.features.stationarity import FractionalDifference\n", "from tensortrade.features.scalers import MinMaxNormalizer\n", "from tensortrade.features import FeaturePipeline\n", "\n", "normalize = MinMaxNormalizer(inplace=True)\n", "difference = FractionalDifference(difference_order=0.6,\n", "                                  inplace=True)\n", "feature_pipeline = FeaturePipeline(steps=[normalize, difference])\n", "\n", "reward_strategy = SimpleProfitStrategy()\n", "action_strategy = DiscreteActionStrategy(n_actions=20, instrument_symbol='ETH/BTC')\n", "\n", "exchange = FBMExchange(base_instrument='BTC',\n", "                       timeframe='1h',\n", "                       should_pretransform_obs=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": true}, "outputs": [], "source": ["from tensortrade.environments import TradingEnvironment\n", "\n", "network_spec = [\n", "    dict(type='dense', size=128, activation=\"tanh\"),\n", "    dict(type='dense', size=64, activation=\"tanh\"),\n", "    dict(type='dense', size=32, activation=\"tanh\")\n", "]\n", "\n", "agent_spec = {\n", "    \"type\": \"ppo\",\n", "    \"learning_rate\": 1e-4,\n", "    \"discount\": 0.99,\n", "    \"likelihood_ratio_clipping\": 0.2,\n", "    \"estimate_terminal\": <PERSON><PERSON><PERSON>,\n", "    \"max_episode_timesteps\": 2000,\n", "    \"network\": network_spec,\n", "    \"batch_size\": 10,\n", "    \"update_frequency\": \"never\"\n", "}\n", "\n", "environment = TradingEnvironment(exchange=exchange,\n", "                                 action_strategy=action_strategy,\n", "                                 reward_strategy=reward_strategy,\n", "                                 feature_pipeline=feature_pipeline)\n", "\n", "strategy = TensorforceTradingStrategy(environment=environment, agent_spec=agent_spec)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Episodes: 100%|██████████████████████| 10/10 [03:12, reward=1105.92, ts/ep=1666, sec/ep=19.23, ms/ts=11.5, agent=14.1%]"]}, {"name": "stdout", "output_type": "stream", "text": ["Finished running strategy.\n", "Total episodes: 10 (16660 timesteps).\n", "Average reward: 1105.922084331043.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>balance</th>\n", "      <th>net_worth</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1661</th>\n", "      <td>10030.081135</td>\n", "      <td>10030.668133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1662</th>\n", "      <td>10030.148918</td>\n", "      <td>10030.756229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1663</th>\n", "      <td>10030.342851</td>\n", "      <td>10030.810741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1664</th>\n", "      <td>10030.350925</td>\n", "      <td>10030.772078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1665</th>\n", "      <td>10030.321345</td>\n", "      <td>10030.588339</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           balance     net_worth\n", "1661  10030.081135  10030.668133\n", "1662  10030.148918  10030.756229\n", "1663  10030.342851  10030.810741\n", "1664  10030.350925  10030.772078\n", "1665  10030.321345  10030.588339"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["performance = strategy.run(episodes=10, evaluation=False)\n", "\n", "performance[-5:]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x2633c399390>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["performance.balance.plot()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["strategy.save_agent(directory='agents')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}