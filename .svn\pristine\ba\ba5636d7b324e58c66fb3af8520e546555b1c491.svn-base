2022-01-14 20:19:26: -- 分析开始 User Command
2022-01-14 20:19:27: 变更：9， 冲突：0， 复制时间：0， 复制状态：0， 错误： 0, All: 57
2022-01-14 20:19:27: Left to Right: Copy File: 9 
2022-01-14 20:19:27: -- 分析已结束。历时 00:00:01, 速度： 48 文件/秒
2022-01-14 20:19:27: 
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /gbdt_long.json
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /gbdt_long.txt
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /gbdt_short.json
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /gbdt_short.txt
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP2_long.json
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_long.json
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_long.model
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_short.json
2022-01-14 20:19:29: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_short.model
2022-01-14 20:19:29: Done.
2022-01-14 20:19:41: === User Reverts Action from 'NO_COPY' to 'COPY_FILE_LTOR' on item /gbdt_long.json
2022-01-14 20:19:41: Done.
2022-01-14 20:19:42: === User Reverts Action from 'NO_COPY' to 'COPY_FILE_LTOR' on item /gbdt_long.txt
2022-01-14 20:19:42: Done.
2022-01-14 20:19:45: == 同步开始由 User Command
2022-01-14 20:19:45: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_long.json' -> 'D:/RoboQuant/model/gbdt_long.json' (4,489)
2022-01-14 20:19:45: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_long.txt' -> 'D:/RoboQuant/model/gbdt_long.txt' (8,701,760)
2022-01-14 20:19:45: == 同步完成. 历时: 00:00:00, 速度: 0 字节/s, 完成: 2, 错误: 0
2022-01-14 20:19:45: 
