2022-01-20 15:47:34: -- 分析开始 User Command
2022-01-20 15:47:34: 变更：36， 冲突：0， 复制时间：0， 复制状态：0， 错误： 0, All: 82
2022-01-20 15:47:34: Left to Right: Copy File: 36 
2022-01-20 15:47:34: -- 分析已结束。历时 00:00:00, 速度： Many 文件/秒
2022-01-20 15:47:34: 
2022-01-20 15:47:41: == 同步开始由 User Command
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_5R_long.json' -> 'D:/RoboQuant/model/GBDT_5R_long.json' (4,492)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_5R_long.model' -> 'D:/RoboQuant/model/GBDT_5R_long.model' (175,174)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_5R_short.json' -> 'D:/RoboQuant/model/GBDT_5R_short.json' (4,498)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_5R_short.model' -> 'D:/RoboQuant/model/GBDT_5R_short.model' (66,333)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_7R_long.json' -> 'D:/RoboQuant/model/GBDT_7R_long.json' (4,485)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_7R_long.model' -> 'D:/RoboQuant/model/GBDT_7R_long.model' (159,566)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_7R_short.json' -> 'D:/RoboQuant/model/GBDT_7R_short.json' (4,487)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_7R_short.model' -> 'D:/RoboQuant/model/GBDT_7R_short.model' (132,076)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_ALL_long.json' -> 'D:/RoboQuant/model/GBDT_ALL_long.json' (4,486)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_ALL_long.model' -> 'D:/RoboQuant/model/GBDT_ALL_long.model' (5,053,284)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_ALL_short.json' -> 'D:/RoboQuant/model/GBDT_ALL_short.json' (4,482)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_ALL_short.model' -> 'D:/RoboQuant/model/GBDT_ALL_short.model' (5,602,291)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_MIX_long.json' -> 'D:/RoboQuant/model/GBDT_MIX_long.json' (4,499)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_MIX_long.model' -> 'D:/RoboQuant/model/GBDT_MIX_long.model' (6,830,697)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_MIX_short.json' -> 'D:/RoboQuant/model/GBDT_MIX_short.json' (4,494)
2022-01-20 15:47:41: 新建复制 'E:/lab/RoboQuant/pylab/model/GBDT_MIX_short.model' -> 'D:/RoboQuant/model/GBDT_MIX_short.model' (1,695,205)
2022-01-20 15:47:41: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_long.json' -> 'D:/RoboQuant/model/MLP_5R_long.json' (4,497)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_long.model' -> 'D:/RoboQuant/model/MLP_5R_long.model' (137,150)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_short.json' -> 'D:/RoboQuant/model/MLP_5R_short.json' (4,498)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_short.model' -> 'D:/RoboQuant/model/MLP_5R_short.model' (137,195)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_long.json' -> 'D:/RoboQuant/model/MLP_7R_long.json' (4,480)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_long.model' -> 'D:/RoboQuant/model/MLP_7R_long.model' (137,150)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_short.json' -> 'D:/RoboQuant/model/MLP_7R_short.json' (4,487)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_short.model' -> 'D:/RoboQuant/model/MLP_7R_short.model' (137,195)
2022-01-20 15:47:42: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP_ALL_long.json' -> 'D:/RoboQuant/model/MLP_ALL_long.json' (4,490)
2022-01-20 15:47:42: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP_ALL_long.model' -> 'D:/RoboQuant/model/MLP_ALL_long.model' (137,195)
2022-01-20 15:47:42: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP_ALL_short.json' -> 'D:/RoboQuant/model/MLP_ALL_short.json' (4,482)
2022-01-20 15:47:42: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP_ALL_short.model' -> 'D:/RoboQuant/model/MLP_ALL_short.model' (137,240)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_long.json' -> 'D:/RoboQuant/model/MLP_long.json' (4,511)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_long.model' -> 'D:/RoboQuant/model/MLP_long.model' (136,629)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_long.json' -> 'D:/RoboQuant/model/MLP_MIX_long.json' (4,501)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_long.model' -> 'D:/RoboQuant/model/MLP_MIX_long.model' (137,195)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_short.json' -> 'D:/RoboQuant/model/MLP_MIX_short.json' (4,485)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_short.model' -> 'D:/RoboQuant/model/MLP_MIX_short.model' (137,240)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_short.json' -> 'D:/RoboQuant/model/MLP_short.json' (4,494)
2022-01-20 15:47:42: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_short.model' -> 'D:/RoboQuant/model/MLP_short.model' (136,674)
2022-01-20 15:47:42: == 同步完成. 历时: 00:00:01, 速度: 20.2 MB/s, 完成: 36, 错误: 0
2022-01-20 15:47:42: 
