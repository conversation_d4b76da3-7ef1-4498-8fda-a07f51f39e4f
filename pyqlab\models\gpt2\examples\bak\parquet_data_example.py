"""
从Parquet文件加载K线数据并训练模型示例

演示如何从Parquet文件加载K线数据，并使用CandlestickLLM模型进行训练
"""

import os
import sys
import argparse
import torch

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和训练器
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.trainer import CandlestickLLMTrainer
from pyqlab.models.gpt2.utils import (
    load_klines_for_training,
    load_multi_timeframe_klines,
    compare_candlesticks,
    save_model_info
)

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='从Parquet文件加载K线数据并训练模型示例')
    parser.add_argument('--data_dir', type=str, required=True, help='数据目录，包含parquet文件')
    parser.add_argument('--model', type=str, default='basic', choices=['basic', 'advanced'],
                        help='选择模型类型: basic (基础版CandlestickLLM) 或 advanced (高级版AdvancedCandlestickLLM)')
    parser.add_argument('--symbols', type=str, nargs='+', help='证券代码列表，如果不提供则加载所有证券')
    parser.add_argument('--start_date', type=str, help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--timeframe', type=str, default='daily', help='时间周期，如daily, 1h, 15min等')
    parser.add_argument('--epochs', type=int, default=3, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=16, help='批大小')
    parser.add_argument('--seq_len', type=int, default=20, help='序列长度')
    parser.add_argument('--min_samples', type=int, default=100, help='最小样本数量')
    parser.add_argument('--volume', action='store_true', help='是否包含交易量')
    args = parser.parse_args()
    
    # 打印选择的模型类型
    model_type = "基础版CandlestickLLM" if args.model == 'basic' else "高级版AdvancedCandlestickLLM"
    print(f"选择的模型类型: {model_type}")
    
    # 加载K线数据
    print(f"从{args.data_dir}加载{args.timeframe}周期的K线数据...")
    train_data, train_code_ids, val_data, val_code_ids = load_klines_for_training(
        data_dir=args.data_dir,
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date,
        timeframe=args.timeframe,
        min_samples=args.min_samples
    )
    
    if not train_data or not val_data:
        print("未找到足够的数据，请检查数据目录和参数设置")
        return
    
    print(f"训练数据: {len(train_data)} 个证券，每个证券平均 {sum(len(df) for df in train_data) / len(train_data):.1f} 个样本")
    print(f"验证数据: {len(val_data)} 个证券，每个证券平均 {sum(len(df) for df in val_data) / len(val_data):.1f} 个样本")

    # 创建tokenizer
    if args.model == 'basic':
        print("创建基础版tokenizer...")
        tokenizer = CandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            atr_window=100,
            atr_mult=0.88,
            scale=10,
            include_volume=args.volume
        )
    else:
        print("创建非线性tokenizer...")
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            include_volume=args.volume
        )

    # 创建模型
    if args.model == 'basic':
        print("创建基础版CandlestickLLM模型...")
        model = CandlestickLLM(
            vocab_size=tokenizer.vocab_size,
            code_size=max(max(train_code_ids), max(val_code_ids)) + 1,
            block_size=30,
            n_layer=2,  # 大幅减少层数以加快训练
            n_head=4,
            d_model=128,  # 大幅减少模型维度以加快训练
            dropout=0.1,
            use_time_features=True
        )
        checkpoint_dir = './checkpoints/parquet_basic_candlestick_llm'
    else:
        print("创建高级版AdvancedCandlestickLLM模型...")
        model = AdvancedCandlestickLLM(
            vocab_size=tokenizer.vocab_size,
            code_size=max(max(train_code_ids), max(val_code_ids)) + 1,
            block_size=30,
            n_layer=2,  # 大幅减少层数以加快训练
            n_head=4,
            d_model=128,  # 大幅减少模型维度以加快训练
            dropout=0.1,
            use_time_features=True,
            use_multi_task=True
        )
        checkpoint_dir = './checkpoints/parquet_advanced_candlestick_llm'

    # 创建训练器
    print("创建训练器...")
    trainer = CandlestickLLMTrainer(
        model=model,
        tokenizer=tokenizer,
        train_data=train_data,
        train_code_ids=train_code_ids,
        val_data=val_data,
        val_code_ids=val_code_ids,
        seq_len=args.seq_len,
        batch_size=args.batch_size,
        learning_rate=5e-4,
        max_epochs=args.epochs,
        log_interval=10,
        eval_interval=50,
        save_interval=100,
        checkpoint_dir=checkpoint_dir
    )

    # 训练模型
    print("开始训练...")
    try:
        results = trainer.train()
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        results = None

    # 绘制训练历史
    print("绘制训练历史...")
    if results is not None:
        try:
            trainer.plot_training_history()
        except Exception as e:
            print(f"绘制训练历史时出现错误: {e}")

    # 生成样本预测
    print("生成样本预测...")
    try:
        sample_df = val_data[0].iloc[-30:].copy()
        predicted_df = trainer.generate_sample(
            input_df=sample_df,
            code_id=val_code_ids[0],
            max_new_tokens=5,
            temperature=0.8,
            top_k=50
        )
    except Exception as e:
        print(f"生成样本预测时出现错误: {e}")
        predicted_df = None

    print("预测结果:")
    print(predicted_df)

    # 可视化预测结果
    if predicted_df is not None:
        try:
            # 使用工具函数比较K线
            title = f"{model_type}预测结果"
            compare_candlesticks(
                input_df=sample_df,
                predicted_df=predicted_df,
                title=title,
                save_path=f'{checkpoint_dir}/prediction_sample.png'
            )
            
            # 保存模型信息
            save_model_info(model, tokenizer, checkpoint_dir)
        except Exception as e:
            print(f"可视化预测结果时出现错误: {e}")

    print(f"示例完成，结果保存在 {checkpoint_dir}/ 目录下")

if __name__ == '__main__':
    main()
