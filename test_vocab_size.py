"""
测试BarTokenizer不同组合方法的词汇表大小

演示如何通过不同的combination_method获得更大的词汇表
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
import numpy as np
import pandas as pd

def create_sample_data(n_samples=1000):
    """创建示例K线数据"""
    np.random.seed(42)
    
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='5min')
    df = pd.DataFrame({
        'datetime': dates,
        'open': 100 + np.cumsum(np.random.randn(n_samples) * 0.1),
        'high': 0,
        'low': 0,
        'close': 0,
        'volume': np.random.randint(1000, 10000, n_samples)
    })
    
    # 生成OHLC数据
    for i in range(n_samples):
        base_price = df.loc[i, 'open']
        change = np.random.randn() * 0.5
        df.loc[i, 'close'] = base_price + change
        df.loc[i, 'high'] = max(df.loc[i, 'open'], df.loc[i, 'close']) + abs(np.random.randn() * 0.2)
        df.loc[i, 'low'] = min(df.loc[i, 'open'], df.loc[i, 'close']) - abs(np.random.randn() * 0.2)
    
    return df

def test_vocab_sizes():
    """测试不同组合方法的词汇表大小"""
    
    # 创建测试数据
    df = create_sample_data()
    
    # 测试参数
    n_bins = 100
    features = ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
    
    print(f"测试参数:")
    print(f"  n_bins: {n_bins}")
    print(f"  features: {features}")
    print(f"  特征数量: {len(features)}")
    print("\n" + "="*60)
    
    # 测试不同的组合方法
    combination_methods = [
        'independent',
        'multiplicative', 
        'hybrid',
        'enhanced',
        'hash',
        'hierarchical'
    ]
    
    results = []
    
    for method in combination_methods:
        try:
            # 创建tokenizer
            if method == 'hash':
                # hash方法需要指定target_vocab_size
                tokenizer = BarTokenizer(
                    mapping_strategy='adaptive',
                    balancing_strategy='frequency',
                    n_bins=n_bins,
                    features=features,
                    combination_method=method,
                    target_vocab_size=50000  # 指定目标词汇表大小
                )
            else:
                tokenizer = BarTokenizer(
                    mapping_strategy='adaptive',
                    balancing_strategy='frequency',
                    n_bins=n_bins,
                    features=features,
                    combination_method=method
                )
            
            # 获取词汇表大小
            vocab_size = tokenizer.get_vocab_size()
            
            # 拟合和转换数据
            tokens = tokenizer.fit_transform(df)
            
            # 分析分布
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            result = {
                'method': method,
                'vocab_size': vocab_size,
                'actual_unique_tokens': len(np.unique(tokens)),
                'gini_coefficient': balance_metrics['gini_coefficient'],
                'normalized_entropy': balance_metrics['normalized_entropy']
            }
            
            results.append(result)
            
            print(f"方法: {method}")
            print(f"  理论词汇表大小: {vocab_size:,}")
            print(f"  实际唯一tokens: {len(np.unique(tokens)):,}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
            print()
            
        except Exception as e:
            print(f"方法 {method} 测试失败: {e}")
            print()
    
    # 总结
    print("="*60)
    print("总结:")
    print()
    
    # 按词汇表大小排序
    results.sort(key=lambda x: x['vocab_size'], reverse=True)
    
    print("按词汇表大小排序:")
    for result in results:
        print(f"  {result['method']:12} : {result['vocab_size']:>8,} tokens")
    
    print()
    print("推荐方案:")
    
    # 找到平衡性较好且词汇表较大的方法
    good_balance_methods = [r for r in results if r['gini_coefficient'] < 0.8 and r['normalized_entropy'] > 0.7]
    
    if good_balance_methods:
        best_method = max(good_balance_methods, key=lambda x: x['vocab_size'])
        print(f"  最佳平衡方案: {best_method['method']} (词汇表: {best_method['vocab_size']:,}, 基尼: {best_method['gini_coefficient']:.3f})")
    
    # 最大词汇表方案
    max_vocab_method = max(results, key=lambda x: x['vocab_size'])
    print(f"  最大词汇表方案: {max_vocab_method['method']} (词汇表: {max_vocab_method['vocab_size']:,})")
    
    return results

def demonstrate_vocab_scaling():
    """演示词汇表大小随参数变化的情况"""
    
    print("\n" + "="*60)
    print("词汇表大小缩放演示")
    print("="*60)
    
    df = create_sample_data(500)  # 使用较小的数据集
    
    # 测试不同的bins和features组合
    test_configs = [
        {'n_bins': 50, 'n_features': 3},
        {'n_bins': 100, 'n_features': 3},
        {'n_bins': 100, 'n_features': 5},
        {'n_bins': 200, 'n_features': 5},
        {'n_bins': 100, 'n_features': 7},
    ]
    
    methods_to_test = ['independent', 'multiplicative', 'hybrid', 'enhanced']
    
    for config in test_configs:
        n_bins = config['n_bins']
        n_features = config['n_features']
        features = ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio', 'volatility', 'rsi'][:n_features]
        
        print(f"\n配置: bins={n_bins}, features={n_features}")
        print("-" * 40)
        
        for method in methods_to_test:
            tokenizer = BarTokenizer(
                mapping_strategy='adaptive',
                n_bins=n_bins,
                features=features,
                combination_method=method
            )
            
            vocab_size = tokenizer.get_vocab_size()
            print(f"  {method:12} : {vocab_size:>8,} tokens")

if __name__ == "__main__":
    print("BarTokenizer 词汇表大小测试")
    print("="*60)
    
    # 测试不同组合方法的词汇表大小
    results = test_vocab_sizes()
    
    # 演示词汇表缩放
    demonstrate_vocab_scaling()
    
    print("\n" + "="*60)
    print("测试完成!")
    
    # 给出具体建议
    print("\n建议:")
    print("1. 如果需要大词汇表且能接受一定计算复杂度，使用 'multiplicative' 方法")
    print("2. 如果需要平衡词汇表大小和计算效率，使用 'enhanced' 方法") 
    print("3. 如果需要控制词汇表大小，使用 'hash' 方法并指定 target_vocab_size")
    print("4. 如果优先考虑计算效率，使用 'independent' 方法")
