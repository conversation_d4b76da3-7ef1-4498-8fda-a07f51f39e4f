"""
回测CandlestickGPT4模型示例

演示如何使用回测器对CandlestickGPT4模型进行回测
"""

import os
import sys
import argparse
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和回测器
from pyqlab.models.gpt2.bak.candlestick_gpt4 import CandlestickGPT4
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.backtester import CandlestickLLMBacktester
from pyqlab.models.gpt2.utils import load_single_data, generate_mock_data

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='回测CandlestickGPT4模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, default=None, help='数据文件路径')
    parser.add_argument('--n_samples', type=int, default=100, help='生成模拟数据的样本数量')
    parser.add_argument('--trend', type=str, default='up', choices=['up', 'down', 'random'], help='生成模拟数据的趋势')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--nonlinear_tokenizer', action='store_true', help='使用非线性tokenizer')
    parser.add_argument('--safe_tokenize', action='store_true', help='使用安全的tokenize方法，确保token索引不会超出范围')

    # 回测参数
    parser.add_argument('--seq_len', type=int, default=64, help='序列长度')
    parser.add_argument('--commission', type=float, default=0.001, help='交易手续费率')
    parser.add_argument('--threshold', type=float, default=0.6, help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float, default=None, help='止损比例')
    parser.add_argument('--take_profit', type=float, default=None, help='止盈比例')
    parser.add_argument('--temperature', type=float, default=0.8, help='温度参数')
    parser.add_argument('--signal_type', type=str, default=None,
                        choices=[None, '传统策略', 'threshold', 'topk', 'momentum', 'ensemble'],
                        help='信号生成器类型')

    # 输出参数
    parser.add_argument('--output_dir', type=str, default='./backtest_results', help='输出目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子"""
    import random
    import numpy as np
    import torch

    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def create_tokenizer(args):
    """创建tokenizer"""
    print("创建tokenizer")

    # 尝试从模型检查点中加载tokenizer配置
    tokenizer_config_path = os.path.join(os.path.dirname(args.model_path), 'tokenizer_config.json')
    if os.path.exists(tokenizer_config_path):
        print(f"从配置文件加载tokenizer: {tokenizer_config_path}")
        import json
        with open(tokenizer_config_path, 'r') as f:
            tokenizer_config = json.load(f)

        # 根据配置创建tokenizer
        if tokenizer_config.get('type') == 'nonlinear' or args.nonlinear_tokenizer:
            print("使用非线性tokenizer")
            tokenizer = NonlinearCandlestickTokenizer(
                change_range=tokenizer_config.get('change_range', (-12, 12)),
                entity_range=tokenizer_config.get('entity_range', (-12, 12)),
                shadow_range=tokenizer_config.get('shadow_range', (0, 7)),
                atr_window=tokenizer_config.get('atr_window', 100),
                atr_mult=tokenizer_config.get('atr_mult', 0.88),
                scale=tokenizer_config.get('scale', 10),
                include_volume=tokenizer_config.get('include_volume', True)
            )
        else:
            from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
            print("使用线性tokenizer")
            tokenizer = CandlestickTokenizer(
                change_range=tokenizer_config.get('change_range', (-12, 12)),
                entity_range=tokenizer_config.get('entity_range', (-12, 12)),
                shadow_range=tokenizer_config.get('shadow_range', (0, 7)),
                atr_window=tokenizer_config.get('atr_window', 100),
                atr_mult=tokenizer_config.get('atr_mult', 0.88),
                scale=tokenizer_config.get('scale', 10),
                include_volume=tokenizer_config.get('include_volume', True)
            )
    else:
        # 如果没有找到配置文件，使用默认参数创建tokenizer
        print("使用默认参数创建tokenizer")
        if args.nonlinear_tokenizer:
            print("使用非线性tokenizer")
            tokenizer = NonlinearCandlestickTokenizer(
                change_range=(-12, 12),
                entity_range=(-12, 12),
                shadow_range=(0, 7),
                atr_window=100,
                atr_mult=0.88,
                scale=10,
                include_volume=True
            )
        else:
            from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
            print("使用线性tokenizer")
            tokenizer = CandlestickTokenizer(
                change_range=(-12, 12),
                entity_range=(-12, 12),
                shadow_range=(0, 7),
                atr_window=100,
                atr_mult=0.88,
                scale=10,
                include_volume=True
            )

    return tokenizer

def load_model(args, tokenizer):
    """加载模型"""
    print(f"加载模型: {args.model_path}")

    # 加载检查点
    checkpoint = torch.load(args.model_path, map_location='cpu')

    # 从检查点中获取词汇表大小
    vocab_size = None
    if 'model_state_dict' in checkpoint:
        # 尝试从模型权重中获取词汇表大小
        if 'token_embedding.weight' in checkpoint['model_state_dict']:
            vocab_size = checkpoint['model_state_dict']['token_embedding.weight'].shape[0]
            print(f"从检查点中检测到词汇表大小: {vocab_size}")

    # 如果无法从检查点获取词汇表大小，则使用tokenizer的词汇表大小
    if vocab_size is None:
        vocab_size = tokenizer.vocab_size
        print(f"使用tokenizer的词汇表大小: {vocab_size}")
    elif args.safe_tokenize or vocab_size < tokenizer.vocab_size:
        # 如果从检查点获取到了词汇表大小，且启用了安全模式或模型词汇表小于tokenizer词汇表
        # 更新tokenizer的词汇表大小，确保tokenizer生成的token索引不会超出模型的词汇表范围
        print(f"更新tokenizer的词汇表大小为: {vocab_size}")
        # 我们需要确保tokenizer的token2idx和idx2token字典与模型兼容
        # 这里我们采用一个简单的方法：限制tokenizer生成的token索引不超过vocab_size
        original_tokenize = tokenizer.tokenize

        def safe_tokenize(df):
            tokens = original_tokenize(df)
            # 确保所有token索引都在vocab_size范围内
            return [min(t, vocab_size - 1) for t in tokens]

        # 替换tokenizer的tokenize方法
        tokenizer.tokenize = safe_tokenize
        print("已安装安全的tokenize方法，确保token索引不会超出范围")

    # 从检查点中获取模型配置
    config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
    if os.path.exists(config_path):
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)

        # 创建模型
        model = CandlestickGPT4(
            vocab_size=vocab_size,  # 使用检测到的词汇表大小
            code_size=100,  # 假设最多100个不同的证券代码
            block_size=config.get('block_size', args.seq_len),
            n_layer=config.get('n_layer', 4),
            n_head=config.get('n_head', 4),
            d_model=config.get('d_model', 64),
            dropout=config.get('dropout', 0.1),
            use_time_features=config.get('use_time_features', False),
            n_time_features=5,  # 假设有5个时间特征
            label_smoothing=config.get('label_smoothing', 0.1),
            use_auxiliary_loss=config.get('use_auxiliary_loss', True)
        )
    else:
        # 使用默认配置创建模型
        model = CandlestickGPT4(
            vocab_size=vocab_size,  # 使用检测到的词汇表大小
            code_size=100,
            block_size=args.seq_len,
            n_layer=4,
            n_head=4,
            d_model=64,
            dropout=0.1,
            use_time_features=False,
            n_time_features=5,
            label_smoothing=0.1,
            use_auxiliary_loss=True
        )

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])

    # 移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()

    print(f"模型参数数量: {model.get_num_params():,}")
    print(f"使用设备: {device}")

    return model, device

def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载数据
    # 加载或生成数据
    if args.data_path:
        train_data, train_code_ids, val_data, val_code_ids = load_single_data(args.data_path)
        df = val_data[0][-150:]
        code_id = val_code_ids[0]
        print(f"已加载数据: {args.data_path}")
        print(f"数据形状: {df.shape}")
    else:
        df = generate_mock_data(n_samples=args.n_samples, trend=args.trend)
        print(f"已生成模拟数据")
        print(f"数据形状: {df.shape}")
        print(df)

    # 创建tokenizer
    tokenizer = create_tokenizer(args)

    # 加载模型
    model, device = load_model(args, tokenizer)

    # 创建回测器
    backtester = CandlestickLLMBacktester(
        model=model,
        tokenizer=tokenizer,
        initial_capital=10000.0,
        device=device,
        signal_type=args.signal_type
    )

    # 进行回测
    print("\n开始回测...")
    results = backtester.backtest(
        df=df,
        code_id=code_id,
        seq_len=args.seq_len,
        commission=args.commission,
        threshold=args.threshold,
        stop_loss=args.stop_loss,
        take_profit=args.take_profit,
        temperature=args.temperature
    )

    # 可视化回测结果
    print("\n可视化回测结果...")
    backtester.visualize_backtest(
        df=df,
        results=results,
        seq_len=args.seq_len,
        save_path=os.path.join(args.output_dir, 'backtest_chart.png')
    )

    # 保存回测结果
    results_path = os.path.join(args.output_dir, 'backtest_results.json')
    backtester.save_results(results, results_path)

    print(f"\n回测完成，结果已保存到 {args.output_dir}")

if __name__ == "__main__":
    main()
