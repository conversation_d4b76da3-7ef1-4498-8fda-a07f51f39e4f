"""
OrderBook数据收集器

用于从pytdx获取股票和期货的orderbook数据，并保存为标准格式
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from pytdx.hq import TdxHq_API
from pytdx.exhq import TdxExHq_API
from pytdx.params import TDXParams
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("orderbook_collector.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("OrderBookCollector")

class OrderBookCollector:
    """OrderBook数据收集器，用于从pytdx获取股票和期货的orderbook数据"""
    
    def __init__(self, save_path, max_workers=5):
        """
        初始化OrderBook数据收集器
        
        参数:
            save_path: 数据保存路径
            max_workers: 最大工作线程数
        """
        self.save_path = save_path
        self.max_workers = max_workers
        self.hq_api = TdxHq_API(heartbeat=True)
        self.exhq_api = TdxExHq_API(heartbeat=True)
        self.connected = False
        self.ex_connected = False
        
        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)
        os.makedirs(os.path.join(save_path, 'stock'), exist_ok=True)
        os.makedirs(os.path.join(save_path, 'future'), exist_ok=True)
    
    def connect(self):
        """连接到行情服务器"""
        if not self.connected:
            # 连接股票行情服务器
            try:
                self.connected = self.hq_api.connect('119.147.212.81', 7709)
                logger.info("连接到股票行情服务器成功")
            except Exception as e:
                logger.error(f"连接股票行情服务器失败: {str(e)}")
                self.connected = False
        
        if not self.ex_connected:
            # 连接期货行情服务器
            try:
                self.ex_connected = self.exhq_api.connect('61.152.107.141', 7727)
                logger.info("连接到期货行情服务器成功")
            except Exception as e:
                logger.error(f"连接期货行情服务器失败: {str(e)}")
                self.ex_connected = False
        
        return self.connected or self.ex_connected
    
    def disconnect(self):
        """断开与行情服务器的连接"""
        if self.connected:
            self.hq_api.disconnect()
            self.connected = False
            logger.info("断开与股票行情服务器的连接")
        
        if self.ex_connected:
            self.exhq_api.disconnect()
            self.ex_connected = False
            logger.info("断开与期货行情服务器的连接")
    
    def get_stock_orderbook(self, market, code):
        """
        获取股票的orderbook数据
        
        参数:
            market: 市场代码，0-深圳，1-上海
            code: 股票代码
            
        返回:
            pd.DataFrame: orderbook数据
        """
        if not self.connected:
            logger.error("未连接到股票行情服务器")
            return None
        
        try:
            # 获取股票行情
            quotes = self.hq_api.get_security_quotes([(market, code)])
            if not quotes or len(quotes) == 0:
                logger.warning(f"获取股票 {market}_{code} 行情失败")
                return None
            
            quote = quotes[0]
            
            # 构建orderbook数据
            timestamp = datetime.now()
            
            data = {
                'timestamp': timestamp,
                'code': code,
                'market': market,
                'last_price': quote.get('price', 0),
                'volume': quote.get('vol', 0),
                'bid_price_1': quote.get('bid1', 0),
                'bid_volume_1': quote.get('bid1_vol', 0),
                'bid_price_2': quote.get('bid2', 0),
                'bid_volume_2': quote.get('bid2_vol', 0),
                'bid_price_3': quote.get('bid3', 0),
                'bid_volume_3': quote.get('bid3_vol', 0),
                'bid_price_4': quote.get('bid4', 0),
                'bid_volume_4': quote.get('bid4_vol', 0),
                'bid_price_5': quote.get('bid5', 0),
                'bid_volume_5': quote.get('bid5_vol', 0),
                'ask_price_1': quote.get('ask1', 0),
                'ask_volume_1': quote.get('ask1_vol', 0),
                'ask_price_2': quote.get('ask2', 0),
                'ask_volume_2': quote.get('ask2_vol', 0),
                'ask_price_3': quote.get('ask3', 0),
                'ask_volume_3': quote.get('ask3_vol', 0),
                'ask_price_4': quote.get('ask4', 0),
                'ask_volume_4': quote.get('ask4_vol', 0),
                'ask_price_5': quote.get('ask5', 0),
                'ask_volume_5': quote.get('ask5_vol', 0),
            }
            
            return pd.DataFrame([data])
        except Exception as e:
            logger.error(f"获取股票 {market}_{code} orderbook数据失败: {str(e)}")
            return None
    
    def get_future_orderbook(self, market_id, code):
        """
        获取期货的orderbook数据
        
        参数:
            market_id: 市场ID
            code: 期货代码
            
        返回:
            pd.DataFrame: orderbook数据
        """
        if not self.ex_connected:
            logger.error("未连接到期货行情服务器")
            return None
        
        try:
            # 获取期货行情
            quotes = self.exhq_api.get_instrument_quote(market_id, code)
            if not quotes or len(quotes) == 0:
                logger.warning(f"获取期货 {market_id}_{code} 行情失败")
                return None
            
            # 构建orderbook数据
            timestamp = datetime.now()
            
            data = {
                'timestamp': timestamp,
                'code': code,
                'market': market_id,
                'last_price': quotes[0].get('last', 0),
                'volume': quotes[0].get('amount', 0),
                'bid_price_1': quotes[0].get('bid1', 0),
                'bid_volume_1': quotes[0].get('bid_vol1', 0),
                'bid_price_2': quotes[0].get('bid2', 0),
                'bid_volume_2': quotes[0].get('bid_vol2', 0),
                'bid_price_3': quotes[0].get('bid3', 0),
                'bid_volume_3': quotes[0].get('bid_vol3', 0),
                'bid_price_4': quotes[0].get('bid4', 0),
                'bid_volume_4': quotes[0].get('bid_vol4', 0),
                'bid_price_5': quotes[0].get('bid5', 0),
                'bid_volume_5': quotes[0].get('bid_vol5', 0),
                'ask_price_1': quotes[0].get('ask1', 0),
                'ask_volume_1': quotes[0].get('ask_vol1', 0),
                'ask_price_2': quotes[0].get('ask2', 0),
                'ask_volume_2': quotes[0].get('ask_vol2', 0),
                'ask_price_3': quotes[0].get('ask3', 0),
                'ask_volume_3': quotes[0].get('ask_vol3', 0),
                'ask_price_4': quotes[0].get('ask4', 0),
                'ask_volume_4': quotes[0].get('ask_vol4', 0),
                'ask_price_5': quotes[0].get('ask5', 0),
                'ask_volume_5': quotes[0].get('ask_vol5', 0),
            }
            
            return pd.DataFrame([data])
        except Exception as e:
            logger.error(f"获取期货 {market_id}_{code} orderbook数据失败: {str(e)}")
            return None
    
    def collect_stock_orderbook(self, market, code, interval=1, duration=60):
        """
        收集股票的orderbook数据
        
        参数:
            market: 市场代码，0-深圳，1-上海
            code: 股票代码
            interval: 收集间隔，单位为秒
            duration: 收集时长，单位为秒
        """
        if not self.connect():
            logger.error("连接行情服务器失败")
            return
        
        logger.info(f"开始收集股票 {market}_{code} 的orderbook数据，间隔 {interval}秒，时长 {duration}秒")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=duration)
        
        all_data = []
        
        try:
            while datetime.now() < end_time:
                data = self.get_stock_orderbook(market, code)
                if data is not None:
                    all_data.append(data)
                
                time.sleep(interval)
        except KeyboardInterrupt:
            logger.info("用户中断收集过程")
        except Exception as e:
            logger.error(f"收集股票 {market}_{code} orderbook数据过程中发生错误: {str(e)}")
        finally:
            if all_data:
                # 合并所有数据
                df = pd.concat(all_data, ignore_index=True)
                
                # 保存数据
                market_name = "SZ" if market == 0 else "SH"
                filename = f"{market_name}_{code}_{start_time.strftime('%Y%m%d_%H%M%S')}.parquet"
                save_path = os.path.join(self.save_path, 'stock', filename)
                df.to_parquet(save_path)
                
                logger.info(f"股票 {market}_{code} orderbook数据已保存到 {save_path}，共 {len(df)} 条记录")
            else:
                logger.warning(f"没有收集到股票 {market}_{code} 的orderbook数据")
    
    def collect_future_orderbook(self, market_id, code, interval=1, duration=60):
        """
        收集期货的orderbook数据
        
        参数:
            market_id: 市场ID
            code: 期货代码
            interval: 收集间隔，单位为秒
            duration: 收集时长，单位为秒
        """
        if not self.connect():
            logger.error("连接行情服务器失败")
            return
        
        logger.info(f"开始收集期货 {market_id}_{code} 的orderbook数据，间隔 {interval}秒，时长 {duration}秒")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=duration)
        
        all_data = []
        
        try:
            while datetime.now() < end_time:
                data = self.get_future_orderbook(market_id, code)
                if data is not None:
                    all_data.append(data)
                
                time.sleep(interval)
        except KeyboardInterrupt:
            logger.info("用户中断收集过程")
        except Exception as e:
            logger.error(f"收集期货 {market_id}_{code} orderbook数据过程中发生错误: {str(e)}")
        finally:
            if all_data:
                # 合并所有数据
                df = pd.concat(all_data, ignore_index=True)
                
                # 保存数据
                filename = f"{market_id}_{code}_{start_time.strftime('%Y%m%d_%H%M%S')}.parquet"
                save_path = os.path.join(self.save_path, 'future', filename)
                df.to_parquet(save_path)
                
                logger.info(f"期货 {market_id}_{code} orderbook数据已保存到 {save_path}，共 {len(df)} 条记录")
            else:
                logger.warning(f"没有收集到期货 {market_id}_{code} 的orderbook数据")
    
    def collect_multiple_stocks(self, stocks, interval=1, duration=60):
        """
        收集多只股票的orderbook数据
        
        参数:
            stocks: 股票列表，每个元素为 (market, code) 元组
            interval: 收集间隔，单位为秒
            duration: 收集时长，单位为秒
        """
        if not self.connect():
            logger.error("连接行情服务器失败")
            return
        
        logger.info(f"开始收集 {len(stocks)} 只股票的orderbook数据，间隔 {interval}秒，时长 {duration}秒")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for market, code in stocks:
                future = executor.submit(self.collect_stock_orderbook, market, code, interval, duration)
                futures.append(future)
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"收集股票orderbook数据过程中发生错误: {str(e)}")
    
    def collect_multiple_futures(self, futures, interval=1, duration=60):
        """
        收集多个期货的orderbook数据
        
        参数:
            futures: 期货列表，每个元素为 (market_id, code) 元组
            interval: 收集间隔，单位为秒
            duration: 收集时长，单位为秒
        """
        if not self.connect():
            logger.error("连接行情服务器失败")
            return
        
        logger.info(f"开始收集 {len(futures)} 个期货的orderbook数据，间隔 {interval}秒，时长 {duration}秒")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures_list = []
            for market_id, code in futures:
                future = executor.submit(self.collect_future_orderbook, market_id, code, interval, duration)
                futures_list.append(future)
            
            for future in as_completed(futures_list):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"收集期货orderbook数据过程中发生错误: {str(e)}")

if __name__ == "__main__":
    # 示例用法
    collector = OrderBookCollector(save_path="./data/orderbook")
    
    # 收集单只股票的orderbook数据
    # collector.collect_stock_orderbook(0, '000001', interval=1, duration=60)
    
    # 收集单个期货的orderbook数据
    # collector.collect_future_orderbook(47, 'IF2306', interval=1, duration=60)
    
    # 收集多只股票的orderbook数据
    stocks = [(0, '000001'), (1, '600000'), (0, '000063')]
    collector.collect_multiple_stocks(stocks, interval=1, duration=60)
    
    # 收集多个期货的orderbook数据
    futures = [(47, 'IF2306'), (47, 'IC2306'), (47, 'IH2306')]
    collector.collect_multiple_futures(futures, interval=1, duration=60)
    
    # 断开连接
    collector.disconnect()
