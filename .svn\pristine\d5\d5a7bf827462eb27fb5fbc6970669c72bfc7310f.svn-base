2022-01-18 20:54:19: -- 分析开始 User Command
2022-01-18 20:54:19: 变更：4， 冲突：0， 复制时间：0， 复制状态：0， 错误： 0, All: 62
2022-01-18 20:54:19: Left to Right: Copy File: 4 
2022-01-18 20:54:19: -- 分析已结束。历时 00:00:00, 速度： Many 文件/秒
2022-01-18 20:54:19: 
2022-01-18 20:54:22: == 同步开始由 User Command
2022-01-18 20:54:22: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_long.json' -> 'D:/RoboQuant/model/MLP_long.json' (4,481)
2022-01-18 20:54:22: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_long.model' -> 'D:/RoboQuant/model/MLP_long.model' (136,629)
2022-01-18 20:54:22: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_short.json' -> 'D:/RoboQuant/model/MLP_short.json' (4,492)
2022-01-18 20:54:22: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_short.model' -> 'D:/RoboQuant/model/MLP_short.model' (136,674)
2022-01-18 20:54:22: == 同步完成. 历时: 00:00:00, 速度: 0 字节/s, 完成: 4, 错误: 0
2022-01-18 20:54:22: 
