import os
import torch
from datetime import datetime
from time import time

from pyqlab.models.mingpt.model import GPT
from pyqlab.models.mingpt.trainer import Trainer
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
from pyqlab.data.pipeline import Pipeline
from pyqlab.data.bardataset import BarDataset
from argparse import ArgumentParser


def get_config():

    C = CN()

    # system
    C.system = CN()
    C.system.seed = 3407
    C.system.work_dir = './out/chargpt'
    C.system.model_dir = R'E:\lab\RoboQuant\pylab\model'
    C.system.model_name = 'BARGPT'
    C.system.resave_model_onnx = False
    C.system.init_from = 'scratch'

    # data
    C.data = BarDataset.get_default_config()

    # model
    C.model = GPT.get_default_config()
    C.model.model_type = 'gpt-mini'

    # trainer
    C.trainer = Trainer.get_default_config()
    C.trainer.learning_rate = 5e-4 # the model we're using is so small that we can go a bit faster

    return C

def load_model_from_ckpt(config):
    ckpt_path = os.path.join(config.system.work_dir, f'ckpt_{config.data.block_size}_{config.model.n_layer}_{config.model.n_head}.pt')
    checkpoint = torch.load(ckpt_path, map_location=config.trainer.device)    
    checkpoint_model_args = checkpoint['model_args']
    print(f"loaded model: {checkpoint_model_args}")
    
    # create the model
    config.model.vocab_size = checkpoint_model_args['vocab_size']
    config.model.block_size = checkpoint_model_args['block_size']
    config.model.n_layer = checkpoint_model_args['n_layer']
    config.model.n_head = checkpoint_model_args['n_head']
    config.model.n_embd = checkpoint_model_args['n_embd']
    # config.model.bias = checkpoint_model_args['bias']
    config.model.code_size = checkpoint_model_args['code_size']
    config.model.pos_size = checkpoint_model_args['pos_size']
    config.model.embd_pdrop = checkpoint_model_args['embd_pdrop']
    config.model.resid_pdrop = checkpoint_model_args['resid_pdrop']
    config.model.attn_pdrop = checkpoint_model_args['attn_pdrop']
    # print(config.model)
    model = GPT(config.model)
    state_dict = checkpoint['model']
    # fix the keys of the state dictionary :(
    # honestly no idea how checkpoints sometimes get this prefix, have to debug more
    unwanted_prefix = '_orig_mod.'
    for k,v in list(state_dict.items()):
        if k.startswith(unwanted_prefix):
            state_dict[k[len(unwanted_prefix):]] = state_dict.pop(k)
    model.load_state_dict(state_dict)
    return model
    
def save_model(config, model):
    code = torch.zeros(1, config.data.block_size).to(torch.long)
    pos = torch.zeros(1, config.data.block_size).to(torch.long)
    x = torch.zeros(1, config.data.block_size).to(torch.long)
    # 模型的命名规则必须为: {前缀名}_{block_size}_{xx}_..._ls
    # 因为程序会自动从模型名中提取出block_size做为参数传入
    now = time()
    tm_str = datetime.fromtimestamp(now).strftime('%m%d%H')
    model_name = f"{config.system.model_name}_{config.data.block_size}_{config.model.n_layer}_{config.model.n_head}_{tm_str}_ls"
    torch.onnx.export(model, (code, pos, x), f"{config.system.model_dir}/{model_name}.onnx", export_params=True)
    print(f"Model saved to {config.system.model_dir}/{model_name}.onnx")
    
def main(args):
    # get default config and overrides from the command line, if any
    config = get_config()
    # print(config)
    config.system.update_from_dict(vars(args))
    config.data.update_from_dict(vars(args))
    config.model.update_from_dict(vars(args))
    config.trainer.update_from_dict(vars(args))
    setup_logging(config)
    set_seed(config.system.seed)

    if config.system.resave_model_onnx:
        model = load_model_from_ckpt(config)
        save_model(config, model)
        return

    # construct the training dataset
    pipe = Pipeline(
        config.data.data_path,
        config.data.start_year,
        config.data.end_year,
        config.data.block_size
    )
    data = pipe.get_data()
    assert len(data) > 0 and len(data.shape) == 3, 'No data or wrong shape'
    config.data.is_sf = pipe.is_sf
    train_dataset = BarDataset(config.data, data[:-1000])
    test_dataset = BarDataset(config.data, data[-1000:])

    # construct the model
    config.model.vocab_size = train_dataset.get_vocab_size()
    config.model.block_size = train_dataset.get_block_size()
    config.model.code_size = pipe.code_size
    config.model.pos_size = pipe.pos_size

    print(config)

    # model init
    # ==================================================================
    if config.system.init_from == 'scratch':
        # init a new model from scratch
        print("Initializing a new model from scratch")
        model = GPT(config.model)
    elif config.system.init_from == 'resume':
        print(f"Resuming training from {config.system.work_dir}")
        # resume training from a checkpoint.
        ckpt_path = os.path.join(config.system.work_dir, f'{config.system.model_name}_{config.data.block_size}_{config.model.n_layer}_{config.model.n_head}.pt')
        checkpoint = torch.load(ckpt_path, map_location=config.trainer.device)
        checkpoint_model_args = checkpoint['model_args']
        print(f"loaded model: {checkpoint_model_args}")
        
        # create the model
        config.model.vocab_size = checkpoint_model_args['vocab_size']
        config.model.block_size = checkpoint_model_args['block_size']
        config.model.n_layer = checkpoint_model_args['n_layer']
        config.model.n_head = checkpoint_model_args['n_head']
        config.model.n_embd = checkpoint_model_args['n_embd']
        # config.model.bias = checkpoint_model_args['bias']
        config.model.code_size = checkpoint_model_args['code_size']
        config.model.pos_size = checkpoint_model_args['pos_size']
        config.model.embd_pdrop = checkpoint_model_args['embd_pdrop']
        config.model.resid_pdrop = checkpoint_model_args['resid_pdrop']
        config.model.attn_pdrop = checkpoint_model_args['attn_pdrop']
        # print(config.model)
        model = GPT(config.model)
        state_dict = checkpoint['model']
        # fix the keys of the state dictionary :(
        # honestly no idea how checkpoints sometimes get this prefix, have to debug more
        unwanted_prefix = '_orig_mod.'
        for k,v in list(state_dict.items()):
            if k.startswith(unwanted_prefix):
                state_dict[k[len(unwanted_prefix):]] = state_dict.pop(k)
        model.load_state_dict(state_dict)
        trainer_iter_num = checkpoint['iter_num']
        # best_val_loss = checkpoint['best_val_loss'] 
        print(f"resumed from iteration {trainer_iter_num}")  
    # ==================================================================
    # optimizer
    optimizer = None
    if config.system.init_from == 'resume':
        optimizer = model.configure_optimizers(config.trainer)
        optimizer.load_state_dict(checkpoint['optimizer'])
    checkpoint = None # free up memory

    # construct the trainer object
    trainer = Trainer(config.trainer, model, train_dataset, test_dataset, optimizer)
    trainer.resume_iter_num = trainer_iter_num if config.system.init_from == 'resume' else 0

    def batch_end_callback(trainer):

        if trainer.iter_num % 50 == 0:
            print(f"iter_dt {trainer.iter_dt * 1000:.2f}ms; iter {trainer.iter_num}: train loss {trainer.loss.item():.5f}")

        if trainer.iter_num % 500 == 0:
            # evaluate both the train and test score
            model.eval()
            with torch.no_grad():

                try:
                    batch = next(trainer.tdata_iter)
                except StopIteration: # reset the iterator
                    trainer.tdata_iter = iter(trainer.test_loader)
                    batch = next(trainer.tdata_iter)
                # batch = [t.to(self.device) for t in batch]
                code, pos, x, y = batch

                y = model.generate(code, pos, x, 5, temperature=1.0, do_sample=True, top_k=3)[0]
                completion = [train_dataset.itobar[int(i)] for i in y]
                print(completion)
            # revert model to training mode
            model.train()

        if trainer.iter_num % 2000 == 0:
            # save the latest model
            if trainer.iter_num > trainer.resume_iter_num:
                checkpoint = {
                    'model': model.state_dict(),
                    'optimizer': trainer.optimizer.state_dict(),
                    'model_args': config.model.to_dict(),
                    'iter_num': trainer.iter_num,
                    'best_val_loss': trainer.best_val_loss,
                    'config': config,
                }            
                ckpt_path = os.path.join(config.system.work_dir, f'{config.system.model_name}_{config.data.block_size}_{config.model.n_layer}_{config.model.n_head}.pt')
                torch.save(checkpoint, ckpt_path)
                print("saving model done.")

    trainer.set_callback('on_batch_end', batch_end_callback)

    # run the optimization
    trainer.run()

if __name__ == '__main__':
    parser = ArgumentParser()

    # system
    parser.add_argument('--work_dir', default='E:/lab/RoboQuant/pylab/out/chargpt', type=str, help='working directory for the model')
    parser.add_argument('--seed', default=3407, type=int, help='random seed')
    parser.add_argument('--init_from', default='scratch', type=str, choices=['scratch', 'resume'], help='init model from scratch or resume from a checkpoint')
    parser.add_argument('--model_dir', default='E:/lab/RoboQuant/pylab/model', type=str, help='directory to save the model')
    parser.add_argument('--model_name', default='GPT', type=str, help='name of the model')
    parser.add_argument('--resave_model_onnx', default=False, type=bool, help='resave the model to onnx')

    # data
    parser.add_argument('--data_path', default='d:/RoboQuant2/store/barenc/fut', type=str, help='path to the data')  
    parser.add_argument('--start_year', default=2022, type=int, help='start year of the data')  
    parser.add_argument('--end_year', default=2023, type=int, help='end year of the data')
    parser.add_argument('--block_size', default=20, type=int, help='block size')

    # model
    parser.add_argument('--n_layer', default=6, type=int, help='number of layers')
    parser.add_argument('--n_head', default=12, type=int, help='number of heads')
    parser.add_argument('--n_embd', default=132, type=int, help='embedding dimension, n*n_head')
    parser.add_argument('--bias', default=False, type=bool, help='whether to use bias in the model')
    parser.add_argument('--model_type', default='gpt-mini', type=str, help='type of model to use')

    # trainer
    parser.add_argument('--max_iters', default=0, type=int, help='maximum number of epochs to train for')
    parser.add_argument('--batch_size', default=64, type=int, help='batch size')
    parser.add_argument('--device', default='cpu', type=str, help='device to run the model on')
    parser.add_argument('--num_workers', default=0, type=int, help='number of workers for the dataloader')
    parser.add_argument('--learning_rate', default=5e-4, type=float, help='learning rate')

    args = parser.parse_args()

    main(args)    