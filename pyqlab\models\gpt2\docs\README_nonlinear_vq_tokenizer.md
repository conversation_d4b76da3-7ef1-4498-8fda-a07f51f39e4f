# NonlinearVQTokenizer

NonlinearVQTokenizer是一个结合非线性映射和VQ-VAE (Vector Quantized Variational Autoencoder) 的K线数据tokenizer，兼具NonlinearCandlestickTokenizer的灵活性和VQ-VAE的表示能力。

## 主要特点

1. **非线性映射预处理**：使用多种非线性映射函数（S形、对数、指数、平方根等）对K线特征进行预处理，提高特征表示的灵活性
2. **VQ-VAE向量量化**：使用VQ-VAE进行向量量化，将连续的K线特征向量映射到离散的码本索引
3. **丰富的特殊token**：支持多种特殊token（填充、未知、异常、交易日间隔、假期等）
4. **异常检测与处理**：内置异常检测和处理功能，提高数据质量
5. **高保真度K线还原**：通过非线性映射的反向函数和VQ-VAE的量化向量，提供高保真度的K线还原功能
6. **可视化支持**：提供K线数据和token化结果的可视化功能

## 安装依赖

```bash
pip install torch pandas numpy matplotlib talib
```

## 基本用法

### 1. 初始化Tokenizer

```python
from pyqlab.models.base.nonlinear_vq_tokenizer import NonlinearVQTokenizer
from pyqlab.models.llm.nonlinear_tokenizer import SigmoidMapping, SquareRootMapping

# 创建映射函数
mapping_functions = {
    'change': SigmoidMapping((-5, 5), (-1, 1)),
    'entity': SigmoidMapping((-3, 3), (-1, 1)),
    'upline': SquareRootMapping((0, 3), (0, 1)),
    'downline': SquareRootMapping((0, 3), (0, 1))
}

# 初始化tokenizer
tokenizer = NonlinearVQTokenizer(
    num_embeddings=512,  # 码本大小
    embedding_dim=4,     # 特征向量维度
    atr_window=100,      # ATR计算窗口
    ma_volume_period=20, # 成交量移动平均周期
    mapping_functions=mapping_functions,
    include_volume=False,  # 是否包含交易量特征
    detect_anomalies=True, # 是否检测异常值
    verbose=True
)
```

### 2. 对K线数据进行Tokenize

```python
import pandas as pd

# 加载K线数据
df = pd.read_csv('kline_data.csv')

# 对K线数据进行tokenize
tokens = tokenizer.tokenize(df)
print(f"生成的token数量: {len(tokens)}")
print(f"前10个token: {tokens[:10]}")
```

### 3. 将Token序列转换回K线数据

```python
# 计算ATR
atr = df['atr'].iloc[-1] if 'atr' in df.columns else df['high'].std()

# 获取起始价格
start_price = df['close'].iloc[0]

# 将token序列转换回K线数据
reconstructed_df = tokenizer.tokens_to_candlesticks(tokens, start_price, atr)
print(reconstructed_df.head())
```

### 4. 可视化原始K线和重建的K线

```python
# 可视化原始K线和重建的K线
tokenizer.visualize_tokenization(df, tokens, reconstructed_df, title="K线数据Tokenization示例")
```

### 5. 保存和加载Tokenizer

```python
# 保存tokenizer
tokenizer.save('tokenizer.pkl')

# 加载tokenizer
loaded_tokenizer = NonlinearVQTokenizer.load('tokenizer.pkl')
```

## 高级用法

### 1. 训练VQ-VAE码本

NonlinearVQTokenizer默认使用随机初始化的码本，但为了获得更好的表示能力，可以通过训练VQ-VAE模型来获取更优的码本权重。

```python
# 加载预训练的码本权重
tokenizer = NonlinearVQTokenizer(
    codebook_weights_path='vq_codebook_weights.pt',
    num_embeddings=512,
    embedding_dim=4
)
```

### 2. 自定义映射函数

可以为不同的特征创建不同的映射函数，以适应不同特征的分布特性。

```python
from pyqlab.models.llm.nonlinear_tokenizer import (
    LogarithmicMapping, ExponentialMapping, SigmoidMapping, SquareRootMapping
)

# 为不同特征创建不同的映射函数
mapping_functions = {
    'change': SigmoidMapping((-5, 5), (-1, 1)),      # S形映射适合价格变化
    'entity': SigmoidMapping((-3, 3), (-1, 1)),      # S形映射适合实体
    'upline': SquareRootMapping((0, 3), (0, 1)),     # 平方根映射适合上影线
    'downline': SquareRootMapping((0, 3), (0, 1)),   # 平方根映射适合下影线
    'volume': LogarithmicMapping((0.1, 10), (-1, 1)) # 对数映射适合交易量
}
```

### 3. 获取量化向量

可以获取token ID对应的量化向量，用于分析token的语义。

```python
# 获取token ID对应的量化向量
token_id = tokens[0]
quantized_vector = tokenizer.get_quantized_vector(token_id)
print(f"Token ID {token_id} 对应的量化向量: {quantized_vector}")
```

## 与其他Tokenizer的对比

NonlinearVQTokenizer结合了NonlinearCandlestickTokenizer和CandlestickVQTokenizer的优点：

1. 从NonlinearCandlestickTokenizer继承了非线性映射和高保真度K线还原的能力
2. 从CandlestickVQTokenizer继承了VQ-VAE的表示能力和紧凑的token表示

这种结合使得NonlinearVQTokenizer在保持高保真度的同时，具有更强的表示能力和更紧凑的token表示。

## 注意事项

1. VQ-VAE码本的质量对tokenizer的性能有重要影响，建议使用预训练的码本权重
2. 非线性映射函数的选择应根据特征的分布特性来确定
3. 对于大规模数据，可能需要调整ATR窗口和成交量移动平均周期等参数
4. 在使用tokens_to_candlesticks函数时，需要提供合适的起始价格和ATR值
