# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8


from __future__ import division
from __future__ import print_function

import numpy as np
import pandas as pd
from typing import Text, Union
import copy
from qlib.utils import get_or_create_path
from qlib.log import get_module_logger, set_log_with_config

import torch
import torch.nn as nn
import torch.optim as optim
# from torch.nn.utils import weight_norm
from torch.optim import lr_scheduler

from .pytorch_utils import count_parameters
from qlib.model.base import Model
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES
from torch.utils.data import DataLoader, TensorDataset, random_split

LOGGING_CONFIG = {
    "version": 1,
    "formatters": {
        "default": {
            # 'format':'%(asctime)s %(filename)s %(lineno)s %(levelname)s %(message)s',
            'format':'%(asctime)s %(levelname)s %(message)s',
        },
        "plain": {
            "format": "%(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "default",
        },
        "console_plain": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "plain"
        },
        "file":{
            "class": "logging.FileHandler",
            "level":20,
            "filename": "./log.txt",
            "formatter": "default",
        }
    },
    "loggers": {
        "console_logger": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "console_plain_logger": {
            "handlers": ["console_plain"],
            "level": "DEBUG",
            "propagate": False,
        },
        "file_logger":{
            "handlers": ["file"],
            "level": "INFO",
            "propagate": False,
        }
    },
    "disable_existing_loggers": True,
}

class MLP1DModelPytorch(Model):
    """MLP Model

    Parameters
    ----------
    d_feat : int
        input dimension for each time step
    n_chans: int
        number of channels
    metric: str
        the evaluate metric used in early stop
    optimizer : str
        optimizer name
    GPU : str
        the GPU ID(s) used for training
    """

    def __init__(
        self,
        num_code=50,
        num_channels=5,
        num_input=65, #87
        layers=(96, 96, 64),
        dropout=0.5,
        n_epochs=200,
        lr=0.0001,
        metric="",
        batch_size=64,
        early_stop=30,
        loss="mse",
        optimizer="adam",
        GPU=0,
        seed=None,
        best_cond="loss",
        **kwargs
    ):
        # Set logger.
        self.logger = get_module_logger("MLP")
        # set_log_with_config(LOGGING_CONFIG)
        self.logger.info("=======================================")

        # set hyper-parameters.
        self.num_code = num_code
        self.num_channels = num_channels
        self.num_input = num_input
        self.layers = layers
        self.dropout = dropout
        self.n_epochs = n_epochs
        self.lr = lr
        self.metric = metric
        self.batch_size = batch_size
        self.early_stop = early_stop
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.device = torch.device("cuda:%d" % (GPU) if torch.cuda.is_available() and GPU >= 0 else "cpu")
        self.seed = seed
        self.best_cond = best_cond

        self.logger.info(
            "MLP parameters setting:"
            "\nnum_code : {}"
            "\nnum_input : {}"
            "\nlayers : {}"
            "\ndropout : {}"
            "\nn_epochs : {}"
            "\nlr : {}"
            "\nmetric : {}"
            "\nbatch_size : {}"
            "\nearly_stop : {}"
            "\noptimizer : {}"
            "\nloss_type : {}"
            "\nvisible_GPU : {}"
            "\nuse_GPU : {}"
            "\nseed : {}"
            "\nbest_cond : {}".format(
                num_code,
                num_input,
                layers,
                dropout,
                n_epochs,
                lr,
                metric,
                batch_size,
                early_stop,
                optimizer.lower(),
                loss,
                GPU,
                self.use_gpu,
                seed,
                best_cond,
            )
        )

        if self.seed is not None:
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)

        # self.mlp_model = MLPModel(
        self.mlp_model = Net(
            num_code=self.num_code,
            num_channels=self.num_channels,
            num_input=self.num_input,
            # output_size=1,
            dropout=self.dropout,
            layers=self.layers,
        )
        self.logger.info("model:\n{:}".format(self.mlp_model))
        self.logger.info("model size: {:.4f} MB".format(count_parameters(self.mlp_model)))

        if optimizer.lower() == "adam":
            self.train_optimizer = optim.Adam(self.mlp_model.parameters(), lr=self.lr)
        elif optimizer.lower() == "gd":
            self.train_optimizer = optim.SGD(self.mlp_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError("optimizer {} is not supported!".format(optimizer))

        self.fitted = False
        self.mlp_model.to(self.device)

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    def mse(self, pred, label):
        loss = (pred - label) ** 2
        return torch.mean(loss)

    def loss_fn(self, pred, label):
        if self.loss == "mse":
            mask = ~torch.isnan(label)
            return self.mse(pred[mask], label[mask])
        # if self.loss == "mse":
        #     sqr_loss = torch.mul(pred - label, pred - label)
        #     loss = sqr_loss.mean()
        #     return loss
        elif self.loss == "binary":
            loss = nn.BCELoss()
            return loss(pred, label)
        raise ValueError("unknown loss `%s`" % self.loss)

    def metric_fn(self, pred, label):

        mask = torch.isfinite(label)

        if self.metric == "" or self.metric == "loss":
            return -self.loss_fn(pred[mask], label[mask])

        raise ValueError("unknown metric `%s`" % self.metric)

    def accuracy(self, pred, label):
        if self.use_gpu:
            preds = (pred>0.5).type(torch.IntTensor).cuda()
        else:
            preds = (pred>0.5).type(torch.IntTensor)
        return (preds == label).float().mean()

    def loader(self, dataset, batch_size=32, shuffle=True):
        # 将数据集划分为训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        return train_dataloader, val_dataloader
    
    def train_epoch(self, dataloader):

        self.mlp_model.train()
        running_loss = 0.0

        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            pred = self.mlp_model(encodeds, inputs)
            loss = self.loss_fn(pred.view(-1), targets.float())
            self.train_optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_value_(self.mlp_model.parameters(), 3.0)
            self.train_optimizer.step()
            running_loss += loss.item()
        return running_loss / len(dataloader)

    def test_epoch(self, dataloader):

        self.mlp_model.eval()

        losses = []
        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            with torch.no_grad():
                pred = self.mlp_model(encodeds, inputs)
                loss = self.loss_fn(pred.view(-1), targets.float()) 
                losses.append(loss.item())

        return np.mean(losses)

    def fit(
        self,
        dataset: DatasetH,
        evals_result=dict(),
        save_path=None,
    ):

        x_data, y_data, encoded_data = dataset.prepare(
            ["train", "valid"],
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_dl, val_dl = self.loader(dataset, 32)

        save_path = get_or_create_path(save_path)
        stop_steps = 0
        train_loss = 0
        best_loss = np.inf
        best_epoch = 0
        evals_result["loss"] = {}
        evals_result["accuracy"] = {}
        evals_result["loss"]["train"] = []
        evals_result["loss"]["valid"] = []

        # train
        self.logger.info("training...")
        self.fitted = True

        # 学习速率衰减设置
        exp_lr_scheduler = lr_scheduler.StepLR(self.train_optimizer, step_size=30, gamma=0.5) # 按步数
        # exp_lr_scheduler = lr_scheduler.MultiStepLR(opt, milestones=[50, 100, 150], gamma=0.1) # 按里程碑
        # exp_lr_scheduler = lr_scheduler.ExponentialLR(opt, gamma=0.1) # 按系数每步

        for step in range(self.n_epochs):
            self.logger.info("Epoch %d:", step)
            # self.logger.info("training...")
            self.train_epoch(train_dl)
            exp_lr_scheduler.step()

            # self.logger.info("evaluating...")
            train_loss = self.test_epoch(train_dl)
            val_loss = self.test_epoch(val_dl)
            self.logger.info("loss: train %.3f, valid %.3f" % (train_loss*10e6, val_loss*10e6))
            evals_result["loss"]["train"].append(train_loss)
            evals_result["loss"]["valid"].append(val_loss)

            # todo: best model cond.
            if val_loss < best_loss:
                best_loss = val_loss
                stop_steps = 0
                best_epoch = step
                best_param = copy.deepcopy(self.mlp_model.state_dict())
            else:
                stop_steps += 1
                if stop_steps >= self.early_stop:
                    self.logger.info("early stop")
                    break


        self.logger.info("best epoch: %d loss: %.6lf" % (best_epoch, best_loss))
        self.mlp_model.load_state_dict(best_param)
        # save model
        # torch.save(best_param, save_path)
        model = self.mlp_model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        sm.save(save_path)

        if self.use_gpu:
            torch.cuda.empty_cache()

        return best_epoch, best_loss

    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        if not self.fitted:
            raise ValueError("model is not fitted yet!")

        x_test = dataset.prepare(segment, col_set="feature", data_key=DataHandlerLP.DK_I)
        # x_test = torch.from_numpy(x_test_pd.values).float().to(self.device)
        index = x_test.index
        self.mlp_model.eval()
        x_values = x_test.values
        sample_num = x_values.shape[0]
        preds = []

        for begin in range(sample_num)[:: self.batch_size]:

            if sample_num - begin < self.batch_size:
                end = sample_num
            else:
                end = begin + self.batch_size

            x_batch = torch.from_numpy(x_values[begin:end]).float().to(self.device)

            with torch.no_grad():
                pred = self.mlp_model(x_batch).detach().cpu().numpy()

            preds.append(pred)

        return pd.Series(np.concatenate(preds), index=index)


class MLPModel(nn.Module):
    '''多层感知机模型'''
    def __init__(self, num_code, num_channel, num_input, dropout=0.05, bn=True):

        super(MLPModel, self).__init__()
        
        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=1)
        self.bn = bn
        self.dropout = dropout
        self.flatten = nn.Flatten()
        self.lin_1 = nn.Linear(num_input * num_channel + 1, 96)
        self.bn_1 = nn.BatchNorm1d(96)
        self.lin_2 = nn.Linear(96, 96)
        self.bn_2 = nn.BatchNorm1d(96)
        self.lin_3 = nn.Linear(96, 96)
        self.bn_3 = nn.BatchNorm1d(96)
        self.lin_4 = nn.Linear(96, 1, bias=True)
        self.drop = nn.Dropout(self.dropout)
        self.activate = nn.ReLU()
        # self.sigmoid = nn.Sigmoid()

    def forward(self, code_ids, input):
        '''
        注意:
        模型不能这样写: self.bn_1(F.dropout(F.relu(self.lin_1(input))))
        模型层嵌套写法的问题,dropout在模型的train时执行,在eval时不执行
        Dropout: 放在全连接层防止过拟合，一般放在激活函数层之后
        BatchNorm: 归一化放在激活层前后好像都有，最初放在了
        激活层池化层后面，而现在普遍放在激活层前。
        '''
        # input layer
        # 加入code向量
        embedded_code_ids = self.code_embeddings(code_ids)
        input = torch.cat([embedded_code_ids, x], dim=-1)
        x = self.flatten(input)
        x = self.lin_1(input)
        # print(x.shape)
        if self.bn:
            x = self.bn_1(x)
        x = self.activate(x)
        if self.dropout>0:
            x = self.drop(x)
        # hidden layer1
        x = self.lin_2(x)
        if self.bn:
            x = self.bn_2(x)
        x = self.activate(x)
        if self.dropout > 0:
            x = self.drop(x)
        # hidden layer2
        x = self.lin_3(x)
        if self.bn:
            x = self.bn_3(x)
        x = self.activate(x)
        if self.dropout > 0:
            x = self.drop(x)
        # out layer
        x = self.lin_4(x)
        # x = self.sigmoid(x)
        return x.view(-1)


class Net(nn.Module):
    '''
    layers=(256, 512, 768, 512, 256, 128, 64)
    '''
    def __init__(self, num_code, num_channels, num_input, dropout=0.05, output_dim=1, layers=(96, 128, 96, 64), loss="binary", embed_dim=5):
        super(Net, self).__init__()
        self.flatten = nn.Flatten()
        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=embed_dim)
        num_input = (num_input + embed_dim) * num_channels
        layers = [num_input] + list(layers)
        dnn_layers = []
        drop_input = nn.Dropout(dropout)
        dnn_layers.append(drop_input)
        for i, (num_input, hidden_units) in enumerate(zip(layers[:-1], layers[1:])):
            fc = nn.Linear(num_input, hidden_units)
            activation = nn.LeakyReLU(negative_slope=0.1, inplace=False)
            bn = nn.BatchNorm1d(hidden_units)
            seq = nn.Sequential(fc, bn, activation)
            dnn_layers.append(seq)
        drop_input = nn.Dropout(dropout)
        dnn_layers.append(drop_input)
        fc = nn.Linear(hidden_units, output_dim)
        dnn_layers.append(fc)
        # add sigmoid layer
        # dnn_layers.append(nn.Sigmoid())
        # optimizer
        self.dnn_layers = nn.ModuleList(dnn_layers)
        self._weight_init()

    def _weight_init(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, a=0.1, mode="fan_in", nonlinearity="leaky_relu")

    def forward(self, code_ids, x):
        # 加入code向量
        embedded_code_ids = self.code_embeddings(code_ids)
        cur_output = torch.cat([embedded_code_ids, x], dim=-1)
        cur_output = self.flatten(cur_output)
        for i, now_layer in enumerate(self.dnn_layers):
            cur_output = now_layer(cur_output)
        return cur_output.view(-1)
