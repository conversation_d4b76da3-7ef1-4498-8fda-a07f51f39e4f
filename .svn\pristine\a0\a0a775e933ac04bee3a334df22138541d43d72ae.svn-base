import pandas as pd
import numpy as np
from pyqlab import spectre
from pyqlab.spectre import factors

import warnings
warnings.filterwarnings("ignore")

loader = spectre.data.ProviderLoader()
engine = spectre.factors.FactorEngine(loader)
fast_ema = factors.EMA(20)
slow_ema = factors.EMA(40)
engine.add(fast_ema, 'fast_ema')
engine.add(slow_ema, 'slow_ema')
engine.add(fast_ema > slow_ema, 'buy_signal')
engine.add(fast_ema < slow_ema, 'sell_signal')
engine.add(factors.OHLCV.close, 'close')
retsult=engine.run('2021-10-14', '2022-11-03', False)
factors=engine.get_factors_raw_value()
print(factors)
