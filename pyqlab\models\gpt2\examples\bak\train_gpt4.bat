@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python train_candlestick_gpt4.py ^
--data_path f:/hqdata/fut_sf_min5.parquet ^
--begin_date 2024-01-01 ^
--end_date 2025-12-31 ^
--val_ratio 0.1 ^
--stride 1 ^
--block_size 30 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 128 ^
--dropout 0.1 ^
--batch_size 64 ^
--epochs 5 ^
--learning_rate 1e-3 ^
--weight_decay 0.01 ^
--warmup_ratio 0.1 ^
--grad_clip 1.0 ^
--grad_accum_steps 2 ^
--label_smoothing 0.1 ^
--use_auxiliary_loss ^
--early_stopping 5 ^
--mixed_precision ^
--save_dir e:/lab/RoboQuant/pylab/checkpoints/candlestick_gpt4 ^
--log_interval 50 ^
--eval_interval 500 ^
--save_interval 500 ^
--seed 42
@REM --nonlinear_tokenizer ^

pause
