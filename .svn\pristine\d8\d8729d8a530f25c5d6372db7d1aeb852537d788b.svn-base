
import pandas as pd
from datetime import datetime
from pytdx.exhq import *
from pyqlab.const import MAIN_FUT_MARKET_CODES, MAIN_SEL_FUT_MARKET_CODES, SF_MARKET_FUT_CODES
from argparse import ArgumentParser

class TdxExHq:
    """TDX扩展行情数据"""
    def __init__(self, api: TdxExHq_API = None) -> None:
        self.api = api

    def connect_hq_server(self, ipaddr='*************', port=7722):
        self.api = TdxExHq_API(heartbeat=True, auto_retry=True, raise_exception=False)
        # 扩展行情配置在 connect.cfg->DSH
        # **********:7722
        # **************:7722
        # ************:7722
        # *************:7722
        if self.api.connect(ipaddr, port):
            print("connect success!")
            # api.disconnect()
        else:
            print("connect error!")
            # raise Exception("hq server connect error!")
            return False
        return True
    
    def close_hq_server(self):
        if self.api:
            self.api.disconnect()

    def get_market_code_num(self, market):
        if market == 'SF':
            return 47
        elif market == 'ZC':
            return 28
        elif market == 'DC':
            return 29
        elif market == 'SC':
            return 30
        raise Exception("market code error")

    #----------------------------------------------------------------------
    # TICK行情
    #----------------------------------------------------------------------

    def restore_order_of_night_trading_time(self, df: pd.DataFrame):
        """恢复夜盘时间顺序"""
        if "datetime" not in df.columns:
            raise Exception("datetime column not exists")
        # 分成两个dataframe
        df1 = df.loc[df['datetime'].dt.hour < 20]
        df2 = df.loc[df['datetime'].dt.hour >= 20] # 夜盘时间
        # 进一步分批处理
        df2_1 = df2.loc[(df2['datetime'].dt.weekday <= 4) & (df2['datetime'].dt.weekday > 0)]
        df2_2 = df2.loc[df2['datetime'].dt.weekday == 0]
        del df2
        #如果datetime是星期二到星期五，且时间在21:00到24:00之间，那么datetime减一天
        df2_1['datetime'] = df2_1['datetime'] - pd.Timedelta(days=1)
        #如果datetime是星期一，且时间在21:00到24:00之间，那么datetime减三天
        df2_2['datetime'] = df2_2['datetime'] - pd.Timedelta(days=3)
        dfs = pd.concat([df1, df2_1, df2_2])
        dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
        dfs.reset_index(drop=True, inplace=True)
        return dfs

    def get_all_history_trans_data(self, market, code, date):

        if not self.api:
            raise Exception("hq server not connect")

        df = pd.DataFrame()
        i = 0
        len = 1800
        data = pd.DataFrame(self.api.get_history_transaction_data(market, code, date=date, start=i*len, count=len)) # 查询历史分笔成交
        while not data.empty:
            df = pd.concat([data, df])
            i += 1
            data = pd.DataFrame(self.api.get_history_transaction_data(market, code, date=date, start=i*len, count=len)) # 查询历史分笔成交
        return df

    def get_workdays(self, year, month, begin=1, end=31):
        import calendar
        # 使用 monthrange() 函数获取该月的天数
        _, days_in_month = calendar.monthrange(year, month)
        # 创建一个包含该月份所有日期的日期范围
        # date_range = pd.date_range(start=f"{year}-{month}-01", end=f"{year}-{month}-{days_in_month}")

        # 使用 bdate_range() 函数获取该日期范围内的所有工作日
        if begin < 1:
            begin = 1
        if end > days_in_month:
            end = days_in_month
        workdays = pd.bdate_range(start=f"{year}-{month}-{begin}", end=f"{year}-{month}-{end}")

        # 将结果转换为列表
        workdays_list = workdays.tolist()

        # 返回结果
        wdlist = []
        for workday in workdays_list:
            wdlist.append(int(workday.strftime("%Y%m%d")))
        return wdlist


    def export_all_history_trans_data_by_day_range(self, market, code_list, year, month, begin=1, end=31, sorted=True):
        days = self.get_workdays(year, month, begin, end)
        dfs = pd.DataFrame()
        market_code_num = self.get_market_code_num(market)
        for day in days:
            for code in code_list:
                df = self.get_all_history_trans_data(market_code_num, code + "L8", day)
                if df.empty:
                    # print(f"{market} {day} {code} empty")
                    continue
                # code	datetime	price	volume
                df['code'] = f'{code}9999.{market}'
                df.rename(columns={'date': 'datetime'}, inplace=True)
                # df = df[['datetime', 'code', 'price', 'volume']]
                df['price'] = df['price'] / 1000
                # TODO: 合并相同时间的数据，取最后一条
                # grouped = df.groupby(['code', 'datetime']).agg({'price': 'last', 'volume': 'sum'})
                # df = grouped.reset_index()
                df = df[['datetime', 'code', 'price', 'volume']]
                dfs = pd.concat([df, dfs])
                print(f"{market} {day} {code} {df.shape[0]}")
        # dfs = restore_order_of_night_trading_time(dfs)
        if sorted:
            dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
        return dfs

    def export_all_history_trans_data_by_month(self, market, code_list, year, month, sorted=True):
        return self.export_all_history_trans_data_by_day_range(market, code_list, year, month, 1, 31, sorted)

    def update_history_trans_data_by_day_range(self, data_path, is_sf, year, month, begin_day=1, end_day=31):
        """增量更新所有合约的历史分笔数据"""
        dfs = pd.DataFrame()
        if is_sf:
            MARKET_CODES = SF_MARKET_FUT_CODES # 金融期货
        else:
            MARKET_CODES = MAIN_FUT_MARKET_CODES # 商品期货
        for market, codes in MARKET_CODES.items():
            df = self.export_all_history_trans_data_by_day_range(market, codes, year, month, begin=begin_day, end=end_day, sorted=False)
            dfs = pd.concat([df, dfs])
        dfs = self.restore_order_of_night_trading_time(dfs)
        if market == 'SF':
            filename=f'{data_path}/{year}/SF{year*100 + month}.parquet'
        else:
            filename=f'{data_path}/{year}/{year*100 + month}.parquet'
        loc_df = pd.DataFrame()
        if os.path.exists(filename):
            loc_df = pd.read_parquet(filename)
            print(f"local last {loc_df['datetime'].iloc[-1]} update to {dfs['datetime'].iloc[0]} ~ {dfs['datetime'].iloc[-1]}")
        dfs = pd.concat([loc_df, dfs])
        # dfs.drop_duplicates(subset=['datetime', 'code'], keep='last', inplace=True)
        dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
        dfs.reset_index(drop=True, inplace=True)
        dfs.to_parquet(filename)
        print(f"{year}-{month} {loc_df.shape[0]} -> {dfs.shape[0]}")

    #----------------------------------------------------------------------
    # 历史行情
    #----------------------------------------------------------------------
    
    def get_fut_history_data(self, market, code, period='day', start_date=20150101):
        # 参数： K线周期， 市场ID， 证券代码，起始位置， 数量
        # K线周期参考 TDXParams
        # 市场ID可以通过 get_markets 获得
        if not self.api:
            raise Exception("hq server not connect")

        market_code_num = self.get_market_code_num(market)
        if period == 'day':
            bars_category = TDXParams.KLINE_TYPE_DAILY
            n = 11
        elif period == 'min5':
            bars_category = TDXParams.KLINE_TYPE_5MIN
            n = 100
        else:
            raise Exception('period error')

        datas = []
        n = 0
        while True: # 一次取800条，循环取，时间是倒序的
            data = self.api.get_instrument_bars(bars_category, market_code_num, code+"L8", n*800, 800)
            n += 1
            if len(data) == 0:
                break
            datas.extend(data)
            cur_date = int(data[0]['year'])*10000 + int(data[0]['month'])*100 + int(data[0]['day'])
            if cur_date < start_date:
                break
        if len(datas) == 0:
            return None
        df = pd.DataFrame(datas)
        df['code'] = f'{code}9999.{market}'
        # df.drop(columns=['year', 'month', 'day', 'hour', 'minute', 'position', 'price'], inplace=True)
        df = df.rename(columns={'trade': 'volume'})
        # column datetime string to datetime
        df['datetime'] = df['datetime'].apply(lambda x: datetime.datetime.strptime(x, "%Y-%m-%d %H:%M"))
        df = df.loc[df['datetime'] >= datetime.datetime.strptime(str(start_date), '%Y%m%d')]
        df = df[['code', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount']]
        if period == 'min5':
            df = self.restore_order_of_night_trading_time(df)
        else:
            df.sort_values(by=['datetime'], inplace=True, ascending=True)
            df.reset_index(drop=True, inplace=True)
        return df

    def save_history_data_binary(self, df: pd.DataFrame, path: str):
        import struct
        with open(path, 'wb') as file:
            for _, row in df.iterrows():
                values = struct.pack('<lfffffff',
                                    int(row['datetime'].timestamp()),
                                    row['open'],
                                    row['high'],
                                    row['low'],
                                    row['close'],
                                    row['volume'],
                                    row['amount'],
                                    0.0)
                file.write(values)
            return len(df)
        
    def update_fut_history_data(self, start_date, is_sf=False, period='day', rq_path='d:/RoboQuant2'):
        """更新商品期货的历史K线数据,以parquet格式存储，方便增量更新"""
        dfs = pd.DataFrame()
        if is_sf:
            MARKET_CODES = SF_MARKET_FUT_CODES
            filename = f'{rq_path}/store/fut_sf_{period}.parquet'
        else:
            MARKET_CODES = MAIN_FUT_MARKET_CODES
            filename = f'{rq_path}/store/fut_{period}.parquet'
        for market, codes in MARKET_CODES.items():
            for code in codes:
                df = self.get_fut_history_data(market, code, period, start_date)
                if df is None:
                    print(f"{period}: {market} {code} None")
                    continue
                df['code'] = f'{code}9999.{market}'
                print(market, code, period, df.shape[0])
                dfs = pd.concat([df, dfs])
        if dfs.empty:
            print("update_fut_history_data is NULL.")
            return
        # 文件是否存在
        if not os.path.exists(filename):
            dfs.to_parquet(filename, engine='fastparquet')
            print(f"{period} {dfs.shape[0]}")
        else:
            loc_df = pd.read_parquet(filename, engine='fastparquet')
            dfs = pd.concat([loc_df, dfs])
            # 去除code和datetime列相同的行
            dfs.drop_duplicates(subset=['code', 'datetime'], keep='last', inplace=True)
            dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
            dfs.reset_index(drop=True, inplace=True)
            dfs.to_parquet(filename, engine='fastparquet')
            print(f"update last datetime from {loc_df['datetime'].iloc[-1]} to {dfs['datetime'].iloc[-1]}")
            print(f"{period} add count {dfs.shape[0] - loc_df.shape[0]}")


    def export_fut_history_data(self, is_sf=False, period='day', rq_path='d:/RoboQuant2'):
        """从parquet文件导出商品期货的历史K线数据，以二进制文件存储，方便C++程序读取"""
        if is_sf:
            filename = f'{rq_path}/store/fut_sf_{period}.parquet'
        else:
            filename = f'{rq_path}/store/fut_{period}.parquet'
        df = pd.read_parquet(filename, engine='fastparquet')
        df = df.groupby('code')
        for code, group in df:
            group.sort_values(by=['datetime'], inplace=True, ascending=True)
            group.reset_index(drop=True, inplace=True)
            print(code, group.shape[0])
            self.save_history_data_binary(group, f'{rq_path}/exdata/{code[-2:]}/{period}/{code}.dat')

    def download_sf_fut_tick_data(self, start_year=2015, end_year=2023):
        """下载期货的历史分笔数据"""
        # 导出金融期货的历史分笔成交数据
        data_path = 'e:/hqdata/tick/'
        code_list = ['IF', 'IH', 'IC', 'IM']
        for year in range(start_year, end_year):
            for month in range(1, 13):
                df = self.export_all_history_trans_data_by_month('SF', code_list, year, month)
                print(f"{year}-{month} {df.shape[0]}")
                df.to_parquet(f'{data_path}/{year}/SF{year*100 + month}.parquet')


    def download_all_fut_tick_data(self, start_year=2015, end_year=2023):
        # 导出商品期货的历史分笔成交数据
        data_path = 'e:/hqdata/tick/'
        for year in range(start_year, end_year):
            for month in range(1, 13):
                dfs = pd.DataFrame()
                for market, codes in MAIN_FUT_MARKET_CODES.items():
                    df = self.export_all_history_trans_data_by_month(market, codes, year, month, sorted=False)
                    dfs = pd.concat([df, dfs])
                if dfs.empty:
                    print(f"{year}-{month} empty")
                    continue
                dfs = self.restore_order_of_night_trading_time(dfs)
                # dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
                dfs.to_parquet(f'{data_path}/{year}/{year*100 + month}.parquet')
                print(f"{year}-{month} {dfs.shape[0]}")

    def download_single_fut_tick_data(self, market, codes=[], start_year=2015, end_year=2023):
        # 导出商品期货的历史分笔成交数据
        data_path = 'e:/hqdata/tick/'
        for year in range(start_year, end_year):
            dfs = pd.DataFrame()
            for month in range(1, 13):
                df = self.export_all_history_trans_data_by_month(market, codes, year, month, sorted=False)
                if df.empty:
                    print(f"{year}-{month} empty")
                    continue
                dfs = pd.concat([df, dfs])
            if dfs.empty:
                continue
            dfs = self.restore_order_of_night_trading_time(dfs)
            # dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
            dfs.to_parquet(f'{data_path}/sfut/{codes[0]}.{year}.parquet')
            print(f"{year}-{month} {dfs.shape[0]}")


def main(args):

    print(args)

    tdxhq = TdxExHq()
    # 连接行情服务器
    if not tdxhq.connect_hq_server(ipaddr='*************', port=7722):
        raise Exception("hq server connect error!")
    
    # tdxhq.download_single_fut_tick_data(market='SC', codes=['AG'], start_year=2013, end_year=2024)
    # tdxhq.download_sf_fut_tick_data(start_year=2022, end_year=2023)
    # tdxhq.download_all_fut_tick_data(start_year=2022, end_year=2023)
    # 按月增量更新历史TICK数据
    if args.update_tick:
        tdxhq.update_history_trans_data_by_day_range(
            data_path=args.data_path,
            is_sf=args.is_sf,
            year=args.year,
            month=args.month,
            begin_day=args.begin_day,
            end_day=args.end_day,
        )

    # 商品期货增量更新
    # 20231120
    if args.update_history:
        tdxhq.update_fut_history_data(start_date=args.start_date, is_sf=args.is_sf, period='day', rq_path=args.rq_path)
        tdxhq.update_fut_history_data(start_date=args.start_date, is_sf=args.is_sf, period='min5', rq_path=args.rq_path)
        tdxhq.export_fut_history_data(is_sf=args.is_sf, period='day', rq_path=args.rq_path)
        tdxhq.export_fut_history_data(is_sf=args.is_sf, period='min5', rq_path=args.rq_path)

    tdxhq.close_hq_server()

if __name__ == '__main__':
    parser = ArgumentParser()

    # tick ==============================
    parser.add_argument('--update_tick', default=False, action='store_true')
    parser.add_argument('--data_path', default='e:/hqdata/tick', type=str)
    parser.add_argument('--is_sf', default=False, action='store_true')
    parser.add_argument('--year', default=2024, type=int)
    parser.add_argument('--month', default=2, type=int)
    parser.add_argument('--begin_day', default=1, type=int)
    parser.add_argument('--end_day', default=20, type=int)

    # history ===========================
    parser.add_argument('--update_history', default=True, action='store_true')
    parser.add_argument('--start_date', default=20200101, type=int)
    parser.add_argument('--rq_path', default='d:/RoboQuant2', type=str)

    args = parser.parse_args()
    main(args)
