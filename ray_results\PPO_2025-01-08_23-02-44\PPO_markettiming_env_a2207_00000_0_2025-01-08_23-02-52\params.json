{"_AlgorithmConfig__prior_exploration_config": null, "_disable_action_flattening": false, "_disable_execution_plan_api": -1, "_disable_initialize_loss_from_dummy_batch": false, "_disable_preprocessor_api": false, "_dont_auto_sync_env_runner_states": false, "_enable_rl_module_api": -1, "_env_to_module_connector": null, "_evaluation_parallel_to_training_wo_thread": false, "_fake_gpus": false, "_is_atari": null, "_learner_class": null, "_learner_connector": null, "_model_config": {}, "_module_to_env_connector": null, "_per_module_overrides": {}, "_rl_module_spec": null, "_run_training_always_in_thread": false, "_tf_policy_handles_more_than_one_loss": false, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "action_mask_key": "action_mask", "action_space": null, "actions_in_input_normalized": false, "add_default_connectors_to_env_to_module_pipeline": true, "add_default_connectors_to_learner_pipeline": true, "add_default_connectors_to_module_to_env_pipeline": true, "algorithm_config_overrides_per_module": {}, "always_attach_evaluation_results": -1, "auto_wrap_old_gym_envs": -1, "batch_mode": "truncate_episodes", "callbacks": "<class '__main__.CrashAfterNIters'>", "checkpoint_trainable_policies_only": false, "clip_actions": false, "clip_param": 0.3, "clip_rewards": null, "compress_observations": false, "count_steps_by": "env_steps", "create_env_on_driver": true, "custom_async_evaluation_function": -1, "custom_eval_function": null, "custom_resources_per_env_runner": {}, "dataset_num_iters_per_learner": null, "delay_between_env_runner_restarts_s": 60.0, "disable_env_checking": false, "eager_max_retraces": 20, "eager_tracing": true, "enable_async_evaluation": -1, "enable_connectors": -1, "enable_env_runner_and_connector_v2": false, "enable_rl_module_and_learner": false, "enable_tf1_exec_eagerly": false, "entropy_coeff": 0.0, "entropy_coeff_schedule": null, "env": "markettiming_env", "env_config": {"data_file": "ft_all.all.00170607085233003.csv", "data_path": "E:/lab/RoboQuant/pylab/data", "gamma": 0.98, "initial_amount": 1000000, "mode": "train", "name": "MarketTimingEnv", "split_percent": 0.9, "version": "v1"}, "env_runner_cls": null, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "env_task_fn": null, "episode_lookback_horizon": 1, "evaluation_config": null, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_force_reset_envs_before_iteration": true, "evaluation_interval": null, "evaluation_num_env_runners": 0, "evaluation_parallel_to_training": false, "evaluation_sample_timeout_s": 120.0, "exploration_config": {"type": "StochasticSampling"}, "explore": true, "export_native_model_files": false, "extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "fake_sampler": false, "framework": "torch", "gamma": 0.99, "grad_clip": null, "grad_clip_by": "global_norm", "ignore_env_runner_failures": false, "in_evaluation": false, "input": "sampler", "input_compress_columns": ["obs", "new_obs"], "input_config": {}, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_read_batch_size": null, "input_read_episodes": false, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_sample_batches": false, "input_read_schema": {}, "input_spaces_jsonable": true, "iter_batches_kwargs": {}, "keep_per_episode_custom_metrics": false, "kl_coeff": 0.2, "kl_target": 0.01, "lambda": 1.0, "learner_config_dict": {}, "local_gpu_idx": 0, "local_tf_session_args": {"inter_op_parallelism_threads": 8, "intra_op_parallelism_threads": 8}, "log_gradients": true, "log_level": "WARN", "log_sys_usage": true, "logger_config": null, "logger_creator": null, "lr": 0.0001, "lr_schedule": null, "map_batches_kwargs": {}, "materialize_data": false, "materialize_mapped_data": true, "max_num_env_runner_restarts": 1000, "max_requests_in_flight_per_env_runner": 2, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_sample_timesteps_per_iteration": 0, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "minibatch_size": 128, "model": {"_disable_action_flattening": false, "_disable_preprocessor_api": false, "_time_major": false, "_use_default_native_models": -1, "always_check_shapes": false, "attention_dim": 64, "attention_head_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_num_heads": 1, "attention_num_transformer_units": 1, "attention_position_wise_mlp_dim": 32, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "conv_activation": "relu", "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_filters": null, "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "custom_action_dist": null, "custom_model": null, "custom_model_config": {}, "custom_preprocessor": null, "dim": 84, "encoder_latent_dim": null, "fcnet_activation": "tanh", "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "fcnet_hiddens": [256, 256], "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "framestack": true, "free_log_std": false, "grayscale": false, "log_std_clip_param": 20.0, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_action_reward": -1, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "max_seq_len": 20, "no_final_linear": false, "post_fcnet_activation": "relu", "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "use_attention": false, "use_lstm": false, "vf_share_layers": false, "zero_mean": true}, "normalize_actions": true, "num_consecutive_env_runner_failures_tolerance": 100, "num_cpus_for_main_process": 1, "num_cpus_per_env_runner": 1, "num_cpus_per_learner": 1, "num_env_runners": 8, "num_envs_per_env_runner": 1, "num_epochs": 30, "num_gpus": 0, "num_gpus_per_env_runner": 0, "num_gpus_per_learner": 0, "num_learners": 0, "observation_filter": "NoFilter", "observation_fn": null, "observation_space": null, "off_policy_estimation_methods": {}, "offline_sampling": false, "ope_split_batch_by_episode": true, "optimizer": {"lr": 0.01, "momentum": 0.9, "type": "SGD"}, "output": null, "output_compress_columns": ["obs", "new_obs"], "output_config": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_episodes": true, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "placement_strategy": "PACK", "policies": {"default_policy": [null, null, null, null]}, "policies_to_train": null, "policy_map_cache": -1, "policy_map_capacity": 100, "policy_mapping_fn": "<function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x0000024F460F8EA0>", "policy_states_are_swappable": false, "postprocess_inputs": false, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_class": null, "prelearner_module_synch_period": 10, "preprocessor_pref": "deepmind", "remote_env_batch_wait_ms": 0, "remote_worker_envs": false, "render_env": false, "replay_sequence_length": null, "restart_failed_env_runners": true, "restart_failed_sub_environments": false, "rollout_fragment_length": "auto", "sample_collector": "<class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>", "sample_timeout_s": 60.0, "sampler_perf_stats_ema_coef": null, "seed": null, "sgd_minibatch_size": -1, "shuffle_batch_per_epoch": true, "shuffle_buffer_size": 0, "simple_optimizer": -1, "sync_filters_on_rollout_workers_timeout_s": 10.0, "synchronize_filters": -1, "tf_session_args": {"allow_soft_placement": true, "device_count": {"CPU": 1}, "gpu_options": {"allow_growth": true}, "inter_op_parallelism_threads": 2, "intra_op_parallelism_threads": 2, "log_device_placement": false}, "torch_compile_learner": false, "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_learner_what_to_compile": "forward_train", "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "train_batch_size": 4000, "train_batch_size_per_learner": null, "update_worker_filter_stats": true, "use_critic": true, "use_gae": true, "use_kl_loss": true, "use_worker_filter_stats": true, "validate_env_runners_after_construction": true, "vf_clip_param": 10.0, "vf_loss_coeff": 1.0, "vf_share_layers": -1, "worker_cls": -1}