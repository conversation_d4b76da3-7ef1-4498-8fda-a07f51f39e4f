"""
Full definition of a GPT Language Model, all of it in this single file.
References:
1) the official GPT-2 TensorFlow implementation released by OpenAI:
https://github.com/openai/gpt-2/blob/master/src/model.py
2) huggingface/transformers PyTorch implementation:
https://github.com/huggingface/transformers/blob/main/src/transformers/models/gpt2/modeling_gpt2.py
"""

import math
import inspect
from dataclasses import dataclass
from pyqlab.utils.config import CfgNode as CN

import torch
import torch.nn as nn
from torch.nn import functional as F

from pyqlab.models.layers.Embed import PositionalEmbedding, TimeFeatureEmbedding, TemporalEmbedding, RotaryPositionalEmbedding, apply_rotary_pos_emb

# class LayerNorm(nn.Module):
#     """ LayerNorm but with an optional bias. PyTorch doesn't support simply bias=False """

#     def __init__(self, ndim, bias):
#         super().__init__()
#         self.weight = nn.Parameter(torch.ones(ndim))
#         self.bias = nn.Parameter(torch.zeros(ndim)) if bias else None

#     def forward(self, input):
#         # Check if input is a tuple and extract the tensor
#         if isinstance(input, tuple):
#             input = input[0]
#         return F.layer_norm(input, self.weight.shape, self.weight, self.bias, 1e-5)

class RMSNorm(nn.Module):
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def _norm(self, x):
        return x * torch.rsqrt(torch.pow(x, 2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        output = self._norm(x)
        return output * self.weight


class FlashAttention(nn.Module):
    def __init__(self, n_embd, n_head, dropout=0.1):
        super().__init__()
        self.c_attn = nn.Linear(n_embd, 3 * n_embd)
        self.c_proj = nn.Linear(n_embd, n_embd)
        self.n_head = n_head
        self.dropout = dropout

    def forward(self, x):
        B, T, C = x.size()
        q, k, v = self.c_attn(x).split(C, dim=2)
        q = q.view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        k = k.view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        v = v.view(B, T, self.n_head, C // self.n_head).transpose(1, 2)

        y = F.scaled_dot_product_attention(q, k, v, attn_mask=None, dropout_p=self.dropout if self.training else 0, is_causal=True)
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        return self.c_proj(y)

class SwiGLU(nn.Module):
    def forward(self, x):
        x, gate = x.chunk(2, dim=-1)
        return F.silu(gate) * x

class MLP(nn.Module):
    def __init__(self, n_embd, bias=True, dropout=0.1):
        super().__init__()
        self.c_fc = nn.Linear(n_embd, 4 * n_embd, bias=bias)
        self.swiglu = SwiGLU()
        self.c_proj = nn.Linear(2 * n_embd, n_embd, bias=bias)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        x = self.c_fc(x)
        x = self.swiglu(x)
        x = self.c_proj(x)
        x = self.dropout(x)
        return x

class MoELayer(nn.Module):
    def __init__(self, n_embd, n_experts, top_k, bias=True, dropout=0.1):
        super().__init__()
        self.n_experts = n_experts  # 专家网络的数量
        self.top_k = top_k  # 每个token使用的专家数量
        # 创建n_experts个MLP作为专家网络
        self.experts = nn.ModuleList([MLP(n_embd, bias, dropout) for _ in range(n_experts)])
        # 门控网络，用于决定每个token应该使用哪些专家
        self.gate = nn.Linear(n_embd, n_experts, bias=False)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        x = x.view(-1, d_model)  # 将输入展平为(batch_size * seq_len, d_model)
        
        # 计算门控logits
        gate_logits = self.gate(x)  # (batch_size * seq_len, n_experts)
        # 选择top_k个专家
        weights, indices = torch.topk(gate_logits, self.top_k, dim=-1)
        # 对权重进行softmax归一化
        weights = F.softmax(weights, dim=-1)
        
        # 计算每个专家的输出
        expert_outputs = torch.zeros_like(x)
        for i, expert in enumerate(self.experts):
            # 创建mask，标识哪些token使用了当前专家
            expert_mask = (indices == i).any(dim=-1)
            if expert_mask.any():
                expert_inputs = x[expert_mask]
                expert_outputs[expert_mask] += expert(expert_inputs)
        
        # 组合专家输出
        combined_outputs = torch.zeros_like(x)
        for k in range(self.top_k):
            expert_mask = indices[:, k].unsqueeze(-1).expand(-1, d_model)
            expert_output = torch.gather(expert_outputs, 0, expert_mask)
            combined_outputs += weights[:, k].unsqueeze(-1) * expert_output
        
        # 应用dropout并恢复原始形状
        output = self.dropout(combined_outputs)
        return output.view(batch_size, seq_len, d_model)
    
class Block(nn.Module):

    def __init__(self, block_size, n_head, n_embd, n_experts, top_k, dropout=0.1, bias=True):
        super().__init__()
        # self.ln_1 = LayerNorm(n_embd, bias=bias)
        self.ln_1 = RMSNorm(n_embd)
        self.attn = FlashAttention(n_embd, n_head, dropout)
        # self.ln_2 = LayerNorm(n_embd, bias=bias)
        self.ln_2 = RMSNorm(n_embd)
        self.moe = MoELayer(n_embd, n_experts, top_k, bias, dropout)

    def forward(self, x):
        x = x + self.attn(self.ln_1(x))
        x = x + self.moe(self.ln_2(x))
        return x

class PositionalEmbeddingFactory:
    @staticmethod
    def get_positional_embedding(embed_type, d_model, max_len=5000):
        if embed_type == 'learned':
            return nn.Embedding(max_len, d_model)
        elif embed_type == 'sinusoidal':
            return PositionalEmbedding(d_model, max_len)
        # elif embed_type == 'rope':
        #     return RotaryPositionalEmbedding(d_model, max_len)
        else:
            raise ValueError(f"Unknown positional embedding type: {embed_type}")


class BarGpt3(nn.Module):

    @staticmethod
    def get_default_config():
        C = CN()
        C.block_size = 20
        C.vocab_size = 40000
        C.n_layer = 12
        C.n_head = 12
        C.n_embd = 768
        C.dropout = 0.0
        C.bias = True # True: bias in Linears and LayerNorms, like GPT-2. False: a bit better and faster
        C.n_experts = 8  # Number of expert networks
        C.top_k = 2  # Number of experts to use for each token
        return C

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            
    def __init__(self, block_size, code_size, vocab_size, n_layer, n_head, n_embd, n_experts, top_k, embed_time, embed_pos, dropout=0.0, bias=True):
        super().__init__()
        assert vocab_size is not None
        assert block_size is not None
        self.block_size = block_size

        self.transformer = nn.ModuleDict(dict(
            bar_eb = nn.Embedding(vocab_size, n_embd), # bar embeddings
            # pos_eb = PositionalEmbedding(n_embd, block_size), # positional embeddings
            pos_eb = PositionalEmbeddingFactory.get_positional_embedding(embed_pos, n_embd, block_size),
            code_eb = nn.Embedding(code_size, n_embd), # code embeddings
            time_eb = TimeFeatureEmbedding(d_model=n_embd, embed_type='timeF', freq='t') if embed_time == 'timeF' else TemporalEmbedding(d_model=n_embd, embed_type=embed_time, freq='t'),
            drop = nn.Dropout(dropout),
            h = nn.ModuleList([Block(block_size, n_head, n_embd, n_experts, top_k, dropout, bias) for _ in range(n_layer)]),
            # ln_f = LayerNorm(n_embd, bias=bias),
            ln_f = RMSNorm(n_embd),
        ))
        self.lm_head = nn.Linear(n_embd, vocab_size, bias=False)
        # with weight tying when using torch.compile() some warnings get generated:
        # "UserWarning: functional_call was passed multiple values for tied weights.
        # This behavior is deprecated and will be an error in future versions"
        # not 100% sure what this is, so far seems to be harmless. TODO investigate
        self.transformer.bar_eb.weight = self.lm_head.weight # https://paperswithcode.com/method/weight-tying

        # init all weights
        self.apply(self._init_weights)
        # apply special scaled init to the residual projections, per GPT-2 paper
        for pn, p in self.named_parameters():
            if pn.endswith('c_proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        # report number of parameters
        print("number of parameters: %.2fM" % (self.get_num_params(False)/1e6,))

    def get_num_params(self, non_embedding=True):
        """
        Return the number of parameters in the model.
        For non-embedding count (default), the position embeddings get subtracted.
        The token embeddings would too, except due to the parameter sharing these
        params are actually used as weights in the final layer, so we include them.
        """
        n_params = sum(p.numel() for p in self.parameters())
        if non_embedding:
            n_params -= self.transformer.wpe.weight.numel()
        return n_params


    def forward(self, code, x, x_mark, targets=None):
        b, t = x.size()
        assert t <= self.block_size, f"Cannot forward sequence of length {t}, block size is only {self.block_size}"
        bar_emb = self.transformer.bar_eb(x) # token embeddings of shape (b, t, n_embd)
        pos_emb = self.transformer.pos_eb(x)
        min5_emb = self.transformer.time_eb(x_mark) # min5 position embeddings of shape (1, t, n_embd)
        code_emb = self.transformer.code_eb(code)
        x = self.transformer.drop(bar_emb + code_emb + min5_emb + pos_emb)
        for block in self.transformer.h:
            x = block(x)
        x = self.transformer.ln_f(x)

        if targets is not None:
            # if we are given some desired targets also calculate the loss
            logits = self.lm_head(x)
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
        else:
            # inference-time mini-optimization: only forward the lm_head on the very last position
            logits = self.lm_head(x[:, [-1], :]) # note: using list [-1] to preserve the time dim
            loss = None
        return logits, loss

    def crop_block_size(self, block_size):
        # model surgery to decrease the block size if necessary
        # e.g. we may load the GPT2 pretrained model checkpoint (block size 1024)
        # but want to use a smaller block size for some smaller, simpler model
        assert block_size <= self.block_size
        self.block_size = block_size
        self.transformer.wpe.weight = nn.Parameter(self.transformer.wpe.weight[:block_size])
        for block in self.transformer.h:
            if hasattr(block.attn, 'bias'):
                block.attn.bias = block.attn.bias[:,:,:block_size,:block_size]

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        # start with all of the candidate parameters
        param_dict = {pn: p for pn, p in self.named_parameters()}
        # filter out those that do not require grad
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        # create optim groups. Any parameters that is 2D will be weight decayed, otherwise no.
        # i.e. all weight tensors in matmuls + embeddings decay, all biases and layernorms don't.
        decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
        nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]
        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters")
        print(f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters")
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"using fused AdamW: {use_fused}")

        return optimizer

    def estimate_mfu(self, fwdbwd_per_iter, dt):
        """ estimate model flops utilization (MFU) in units of A100 bfloat16 peak FLOPS """
        # first estimate the number of flops we do per iteration.
        # see PaLM paper Appendix B as ref: https://arxiv.org/abs/2204.02311
        N = self.get_num_params()
        cfg = self.config
        L, H, Q, T = cfg.n_layer, cfg.n_head, cfg.n_embd//cfg.n_head, cfg.block_size
        flops_per_token = 6*N + 12*L*H*Q*T
        flops_per_fwdbwd = flops_per_token * T
        flops_per_iter = flops_per_fwdbwd * fwdbwd_per_iter
        # express our flops throughput as ratio of A100 bfloat16 peak flops
        flops_achieved = flops_per_iter * (1.0/dt) # per second
        flops_promised = 312e12 # A100 GPU bfloat16 peak flops is 312 TFLOPS
        mfu = flops_achieved / flops_promised
        return mfu

    @torch.no_grad()
    def generate(self, idx, max_new_tokens, temperature=1.0, top_k=None):
        """
        Take a conditioning sequence of indices idx (LongTensor of shape (b,t)) and complete
        the sequence max_new_tokens times, feeding the predictions back into the model each time.
        Most likely you'll want to make sure to be in model.eval() mode of operation for this.
        """
        for _ in range(max_new_tokens):
            # if the sequence context is growing too long we must crop it at block_size
            idx_cond = idx if idx.size(1) <= self.block_size else idx[:, -self.block_size:]
            # forward the model to get the logits for the index in the sequence
            logits, _ = self(idx_cond)
            # pluck the logits at the final step and scale by desired temperature
            logits = logits[:, -1, :] / temperature
            # optionally crop the logits to only the top k options
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')
            # apply softmax to convert logits to (normalized) probabilities
            probs = F.softmax(logits, dim=-1)
            # sample from the distribution
            idx_next = torch.multinomial(probs, num_samples=1)
            # append sampled index to the running sequence and continue
            idx = torch.cat((idx, idx_next), dim=1)

        return idx

