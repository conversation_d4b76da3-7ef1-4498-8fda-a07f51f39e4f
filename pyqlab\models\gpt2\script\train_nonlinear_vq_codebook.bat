@echo off
REM Training script for VQ-VAE codebook weights

REM Default parameters
set MARKET=fut
set BLOCK_NAME=top
set PERIOD=min1
set DATA_DIR=f:\hqdata\tsdb
set SAVE_DIR=e:\lab\RoboQuant\pylab\models\vqvae
set NUM_EPOCHS=5
set BATCH_SIZE=128
set LEARNING_RATE=0.001
set NUM_EMBEDDINGS=1024
set EMBEDDING_DIM=4
set HIDDEN_DIM=64
set ATR_WINDOW=100
set MA_VOLUME_PERIOD=100
set INCLUDE_VOLUME=
set MIN_SAMPLES=1000
set MAX_SAMPLES=100000
set SAVE_INTERVAL=50
set DEVICE=cpu

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :run
if /i "%~1"=="--data_dir" (
    set DATA_DIR=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--market" (
    set MARKET=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--block_name" (
    set BLOCK_NAME=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--period" (
    set PERIOD=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--save_dir" (
    set SAVE_DIR=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--num_epochs" (
    set NUM_EPOCHS=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--batch_size" (
    set BATCH_SIZE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--learning_rate" (
    set LEARNING_RATE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--num_embeddings" (
    set NUM_EMBEDDINGS=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--embedding_dim" (
    set EMBEDDING_DIM=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--hidden_dim" (
    set HIDDEN_DIM=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--atr_window" (
    set ATR_WINDOW=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--ma_volume_period" (
    set MA_VOLUME_PERIOD=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--include_volume" (
    set INCLUDE_VOLUME=--include_volume
    shift
    goto :parse_args
)
if /i "%~1"=="--min_samples" (
    set MIN_SAMPLES=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--max_samples" (
    set MAX_SAMPLES=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--save_interval" (
    set SAVE_INTERVAL=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--device" (
    set DEVICE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--help" (
    echo Usage: train_vq_codebook.bat [options]
    echo.
    echo Options:
    echo   --data_dir DIR          Directory containing K-line data (default: data\klines)
    echo   --save_dir DIR          Directory to save models (default: models\vqvae)
    echo   --num_epochs N          Number of training epochs (default: 50)
    echo   --batch_size N          Batch size (default: 128)
    echo   --learning_rate N       Learning rate (default: 0.001)
    echo   --num_embeddings N      Codebook size (default: 512)
    echo   --embedding_dim N       Embedding dimension (default: 4)
    echo   --hidden_dim N          Hidden dimension (default: 64)
    echo   --atr_window N          ATR calculation window (default: 100)
    echo   --ma_volume_period N    Volume moving average period (default: 20)
    echo   --include_volume        Include volume feature
    echo   --min_samples N         Minimum number of samples (default: 1000)
    echo   --max_samples N         Maximum number of samples (default: 100000)
    echo   --save_interval N       Save interval (default: 5)
    echo   --device DEVICE         Training device (default: cuda)
    echo   --help                  Show this help message
    exit /b 0
)

echo Unknown parameter: %~1
exit /b 1

:run
echo Training VQ-VAE Codebook Weights
echo Data directory: %DATA_DIR%
echo Save directory: %SAVE_DIR%
echo Number of epochs: %NUM_EPOCHS%
echo Batch size: %BATCH_SIZE%
echo Learning rate: %LEARNING_RATE%
echo Codebook size: %NUM_EMBEDDINGS%
echo Embedding dimension: %EMBEDDING_DIM%
echo Hidden dimension: %HIDDEN_DIM%
echo ATR window: %ATR_WINDOW%
echo Volume MA period: %MA_VOLUME_PERIOD%
if defined INCLUDE_VOLUME (
    echo Include volume: Yes
) else (
    echo Include volume: No
)
echo Minimum samples: %MIN_SAMPLES%
echo Maximum samples: %MAX_SAMPLES%
echo Save interval: %SAVE_INTERVAL%
echo Training device: %DEVICE%
echo.

REM Run the training script
python -m pyqlab.models.base.train_nonlinear_vq_codebook ^
    --data_dir %DATA_DIR% ^
    --market %MARKET% ^
    --block_name %BLOCK_NAME% ^
    --period %PERIOD% ^
    --save_dir %SAVE_DIR% ^
    --num_epochs %NUM_EPOCHS% ^
    --batch_size %BATCH_SIZE% ^
    --learning_rate %LEARNING_RATE% ^
    --num_embeddings %NUM_EMBEDDINGS% ^
    --embedding_dim %EMBEDDING_DIM% ^
    --hidden_dim %HIDDEN_DIM% ^
    --atr_window %ATR_WINDOW% ^
    --ma_volume_period %MA_VOLUME_PERIOD% ^
    %INCLUDE_VOLUME% ^
    --min_samples %MIN_SAMPLES% ^
    --max_samples %MAX_SAMPLES% ^
    --save_interval %SAVE_INTERVAL% ^
    --device %DEVICE%

echo.
if %ERRORLEVEL% EQU 0 (
    echo Training completed successfully!
) else (
    echo Training failed with error code: %ERRORLEVEL%
)

