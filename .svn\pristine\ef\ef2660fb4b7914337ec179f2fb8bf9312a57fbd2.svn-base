{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pytorch中文文档（https://pytorch-cn.readthedocs.io/zh/latest/）\n", "\n", "torchvision：包含了目前流行的数据集，模型结构和常用的图片转换工具。\n", "1.数据集：MNIST\n", "   https://pytorch-cn.readthedocs.io/zh/latest/torchvision/torchvision-datasets/\n", "2.模型：\n", "   https://pytorch-cn.readthedocs.io/zh/latest/torchvision/torchvision-models/"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# images lib\n", "import torchvision\n", "from torchvision import datasets,transforms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = torchvision.models.vgg16(pretrained=True)\n", "model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["TORCHTEXT.DATASETS\n", "\n", "所有数据集都是的子类torchtext.data.Dataset，它们继承自torch.utils.data.Dataset，\n", "\n", "并且具有split和 iters实现的方法。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torchtext\n", "from torchtext.vocab import Glove"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["codes = ['A', 'AG', 'AL', 'AP', 'AU', 'B', 'B<PERSON>', 'C', 'CF', 'C<PERSON>', 'CS', 'CU', 'CY', 'EB', 'EG', 'FG', 'HC', 'I', 'IC', 'IF', 'IH', 'J', 'JD', 'J<PERSON>', 'L', 'LH', 'M', 'MA', 'NI', 'NR', 'OI', 'P', 'PB', 'PF', 'PG', 'PK', 'PP', 'RB', 'RM', 'RR', 'RU', 'SA', 'SC', 'SF', 'SM', 'SN', 'SP', 'SR', 'SS', 'TA', 'UR', 'V', 'Y', 'ZN']\n", "base_l = [0 for _ in range(len(codes))]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["54\n"]}], "source": ["print(len(codes))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["code_to_int = dict((c, i) for i, c in enumerate(codes))\n", "int_to_code = dict((c, i) for i, c in enumerate(codes))\n", "\n", "onehot_list = [0 for _ in range(len(codes))]\n", "onehot_encode = list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_to_int"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.cuda.device_count()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1;31mInit signature:\u001b[0m\n", "\u001b[0mnn\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mLSTMCell\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0minput_size\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mint\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mhidden_size\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mint\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mbias\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mbool\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mdevice\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mdtype\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mDocstring:\u001b[0m     \n", "A long short-term memory (LSTM) cell.\n", "\n", ".. math::\n", "\n", "    \\begin{array}{ll}\n", "    i = \\sigma(W_{ii} x + b_{ii} + W_{hi} h + b_{hi}) \\\\\n", "    f = \\sigma(W_{if} x + b_{if} + W_{hf} h + b_{hf}) \\\\\n", "    g = \\tanh(W_{ig} x + b_{ig} + W_{hg} h + b_{hg}) \\\\\n", "    o = \\sigma(W_{io} x + b_{io} + W_{ho} h + b_{ho}) \\\\\n", "    c' = f * c + i * g \\\\\n", "    h' = o * \\tanh(c') \\\\\n", "    \\end{array}\n", "\n", "where :math:`\\sigma` is the sigmoid function, and :math:`*` is the Hadamard product.\n", "\n", "Args:\n", "    input_size: The number of expected features in the input `x`\n", "    hidden_size: The number of features in the hidden state `h`\n", "    bias: If ``False``, then the layer does not use bias weights `b_ih` and\n", "        `b_hh`. <PERSON><PERSON><PERSON>: ``True``\n", "\n", "Inputs: input, (h_0, c_0)\n", "    - **input** of shape `(batch, input_size)`: tensor containing input features\n", "    - **h_0** of shape `(batch, hidden_size)`: tensor containing the initial hidden\n", "      state for each element in the batch.\n", "    - **c_0** of shape `(batch, hidden_size)`: tensor containing the initial cell state\n", "      for each element in the batch.\n", "\n", "      If `(h_0, c_0)` is not provided, both **h_0** and **c_0** default to zero.\n", "\n", "Outputs: (h_1, c_1)\n", "    - **h_1** of shape `(batch, hidden_size)`: tensor containing the next hidden state\n", "      for each element in the batch\n", "    - **c_1** of shape `(batch, hidden_size)`: tensor containing the next cell state\n", "      for each element in the batch\n", "\n", "Attributes:\n", "    weight_ih: the learnable input-hidden weights, of shape\n", "        `(4*hidden_size, input_size)`\n", "    weight_hh: the learnable hidden-hidden weights, of shape\n", "        `(4*hidden_size, hidden_size)`\n", "    bias_ih: the learnable input-hidden bias, of shape `(4*hidden_size)`\n", "    bias_hh: the learnable hidden-hidden bias, of shape `(4*hidden_size)`\n", "\n", ".. note::\n", "    All the weights and biases are initialized from :math:`\\mathcal{U}(-\\sqrt{k}, \\sqrt{k})`\n", "    where :math:`k = \\frac{1}{\\text{hidden\\_size}}`\n", "\n", "Examples::\n", "\n", "    >>> rnn = nn.L<PERSON><PERSON>ell(10, 20) # (input_size, hidden_size)\n", "    >>> input = torch.randn(2, 3, 10) # (time_steps, batch, input_size)\n", "    >>> hx = torch.randn(3, 20) # (batch, hidden_size)\n", "    >>> cx = torch.randn(3, 20)\n", "    >>> output = []\n", "    >>> for i in range(input.size()[0]):\n", "            hx, cx = rnn(input[i], (hx, cx))\n", "            output.append(hx)\n", "    >>> output = torch.stack(output, dim=0)\n", "\u001b[1;31mInit docstring:\u001b[0m Initializes internal Module state, shared by both nn.Module and ScriptModule.\n", "\u001b[1;31mFile:\u001b[0m           d:\\anaconda3\\lib\\site-packages\\torch\\nn\\modules\\rnn.py\n", "\u001b[1;31mType:\u001b[0m           type\n", "\u001b[1;31mSubclasses:\u001b[0m     \n"]}], "source": ["nn.LSTMCell?"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([32, 100, 29])\n"]}], "source": ["conv1 = nn.Conv1d(in_channels=256, out_channels=100, kernel_size=7)\n", "input = torch.randn(32,35,256)\n", "# batch_size x text_len x embedding_size -> batch_size x embedding_size x text_len\n", "input = input.permute(0,2,1)\n", "out = conv1(input)\n", "print(out.size())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}