"""
Candlestick Tokenizer

将K线数据离散化为token，以便用于LLM模型
增强版本：支持交易量特征、异常检测和多粒度表示
"""

import pandas as pd
import numpy as np
import torch
import talib
from typing import List, Dict, Tuple, Optional, Union, Any
import matplotlib.pyplot as plt
import os
import pickle
from datetime import datetime, timedelta
import warnings

class CandlestickTokenizer:
    """
    将K线数据转换为token序列，以便用于LLM模型
    """
    # 特殊标记常量
    PAD_TOKEN = '<PAD>'          # 填充标记
    UNK_TOKEN = '<UNK>'          # 未知标记
    # 删除 BOS_TOKEN 和 EOS_TOKEN，因为行情数据是连续的，不需要序列开始和结束标记
    ANOMALY_TOKEN = '<ANOMALY>'  # 异常数据标记
    DAY_GAP_TOKEN = '<DAY_GAP>'  # 交易日间隔标记
    HOLIDAY_TOKEN = '<HOLIDAY>'  # 假期间隔标记

    def __init__(self,
                 change_range: Tuple[int, int] = (-12, 12),
                 entity_range: Tuple[int, int] = (-12, 12),
                 shadow_range: Tuple[int, int] = (0, 7),
                 volume_range: Tuple[int, int] = (-9, 9),
                 atr_window: int = 100,
                 atr_mult: float = 0.88,
                 scale: int = 10,
                 holiday_gap: bool = False,
                 special_tokens: bool = True,
                 include_volume: bool = False,
                 detect_anomalies_value: bool = False,
                 anomaly_threshold: float = 7.0,
                 cache_dir: str = None,
                 verbose: bool = False):
        """
        初始化K线tokenizer

        Args:
            change_range: change值的范围
            entity_range: entity值的范围
            shadow_range: 影线值的范围
            volume_range: 交易量变化值的范围
            atr_window: ATR计算窗口
            atr_mult: ATR乘数
            scale: 缩放因子
            special_tokens: 是否使用特殊token（如交易日间隔）
            include_volume: 是否包含交易量特征
            detect_anomalies_value: 是否检测和处理异常值
            anomaly_threshold: 异常检测阈值（标准差倍数）
            cache_dir: 缓存目录，用于保存和加载tokenizer
            verbose: 是否输出详细的特征统计信息
        """
        self.change_range = change_range
        self.entity_range = entity_range
        self.shadow_range = shadow_range
        self.volume_range = volume_range
        self.atr_window = atr_window
        self.atr_mult = atr_mult
        self.scale = scale
        self.holiday_gap = holiday_gap
        self.special_tokens = special_tokens
        self.include_volume = include_volume
        self.detect_anomalies_value = detect_anomalies_value
        self.anomaly_threshold = anomaly_threshold
        self.cache_dir = cache_dir
        self.verbose = verbose

        # 生成词汇表
        self.vocab = self._generate_vocab()
        self.vocab_size = len(self.vocab)

        # 创建token到索引和索引到token的映射
        self.token2idx = {token: idx for idx, token in enumerate(self.vocab)}
        self.idx2token = {idx: token for idx, token in enumerate(self.vocab)}

        print(f"Candlestick Tokenizer initialized with vocabulary size: {self.vocab_size}")
        if self.verbose:
            if self.include_volume:
                print(f"Volume features enabled with range: {self.volume_range}")
            if self.detect_anomalies_value:
                print(f"Anomaly detection enabled with threshold: {self.anomaly_threshold}")

    def _generate_vocab(self) -> List[str]:
        """生成词汇表"""
        vocab = []

        # 生成常规K线token
        if self.include_volume:
            # 包含交易量的token格式: change|entity|upline|downline|volume
            for i in range(self.change_range[0], self.change_range[1] + 1):
                for j in range(self.entity_range[0], self.entity_range[1] + 1):
                    for k in range(self.shadow_range[0], self.shadow_range[1] + 1):
                        for l in range(self.shadow_range[0], self.shadow_range[1] + 1):
                            for v in range(self.volume_range[0], self.volume_range[1] + 1):
                                vocab.append(f'{i}|{j}|{k}|{l}|{v}')
        else:
            # 不包含交易量的token格式: change|entity|upline|downline
            for i in range(self.change_range[0], self.change_range[1] + 1):
                for j in range(self.entity_range[0], self.entity_range[1] + 1):
                    for k in range(self.shadow_range[0], self.shadow_range[1] + 1):
                        for l in range(self.shadow_range[0], self.shadow_range[1] + 1):
                            vocab.append(f'{i}|{j}|{k}|{l}')

        # 添加特殊token
        if self.special_tokens:
            # 添加文本形式的特殊标记，而不是数值形式
            vocab.append(self.PAD_TOKEN)      # 填充标记
            vocab.append(self.UNK_TOKEN)      # 未知标记
            # 删除 BOS_TOKEN 和 EOS_TOKEN，因为行情数据是连续的，不需要序列开始和结束标记
            vocab.append(self.ANOMALY_TOKEN)  # 异常数据标记
            vocab.append(self.DAY_GAP_TOKEN)  # 交易日间隔标记
            vocab.append(self.HOLIDAY_TOKEN)  # 假期(周末/节假日)间隔标记

        return vocab

    def detect_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        检测K线数据中的异常值

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            添加了异常标记的DataFrame
        """
        if not self.detect_anomalies_value:
            return df

        # 创建一个副本，避免修改原始数据
        df_copy = df.copy()

        # 计算价格变化率
        df_copy['price_change'] = df_copy['close'].pct_change()

        # 计算滚动均值和标准差
        window = min(self.atr_window, len(df_copy) // 2)  # 确保窗口大小合理
        if window < 5:
            window = 5  # 最小窗口大小

        df_copy['rolling_mean'] = df_copy['price_change'].rolling(window=window).mean()
        df_copy['rolling_std'] = df_copy['price_change'].rolling(window=window).std()

        # 计算z分数
        df_copy['z_score'] = (df_copy['price_change'] - df_copy['rolling_mean']) / df_copy['rolling_std']

        # 标记异常
        df_copy['is_price_anomaly'] = abs(df_copy['z_score']) > self.anomaly_threshold

        # 如果有交易量数据，也检测交易量异常
        if 'volume' in df_copy.columns:
            df_copy['volume_change'] = df_copy['volume'].pct_change()
            df_copy['volume_rolling_mean'] = df_copy['volume_change'].rolling(window=window).mean()
            df_copy['volume_rolling_std'] = df_copy['volume_change'].rolling(window=window).std()
            df_copy['volume_z_score'] = (df_copy['volume_change'] - df_copy['volume_rolling_mean']) / df_copy['volume_rolling_std']
            df_copy['is_volume_anomaly'] = abs(df_copy['volume_z_score']) > self.anomaly_threshold

            # 组合异常标记
            df_copy['is_anomaly'] = df_copy['is_price_anomaly'] | df_copy['is_volume_anomaly']
        else:
            df_copy['is_anomaly'] = df_copy['is_price_anomaly']

        # 填充NaN值
        df_copy['is_anomaly'] = df_copy['is_anomaly'].bfill()

        # 打印异常统计
        anomaly_count = df_copy['is_anomaly'].sum()
        if anomaly_count > 0 and self.verbose:
            print(f"检测到 {anomaly_count} 个异常值 ({anomaly_count/len(df_copy):.2%})")

        return df_copy

    def correct_anomalies(self, df: pd.DataFrame, method: str = 'interpolate') -> pd.DataFrame:
        """
        修正K线数据中的异常值

        Args:
            df: 包含异常标记的DataFrame
            method: 修正方法，'interpolate'或'rolling_mean'

        Returns:
            修正后的DataFrame
        """
        if not self.detect_anomalies_value or 'is_anomaly' not in df.columns:
            return df

        # 创建一个副本，避免修改原始数据
        df_copy = df.copy()

        # 获取异常掩码
        anomaly_mask = df_copy['is_anomaly']

        if anomaly_mask.sum() == 0:
            return df_copy

        # 修正异常值
        if method == 'interpolate':
            # 使用插值法
            for col in ['open', 'high', 'low', 'close']:
                if col in df_copy.columns:
                    df_copy.loc[anomaly_mask, col] = np.nan
                    df_copy[col] = df_copy[col].interpolate(method='linear')

            if 'volume' in df_copy.columns:
                df_copy.loc[anomaly_mask, 'volume'] = np.nan
                df_copy['volume'] = df_copy['volume'].interpolate(method='linear')

        elif method == 'rolling_mean':
            # 使用滚动均值
            window = min(self.atr_window, len(df_copy) // 2)
            if window < 5:
                window = 5

            for col in ['open', 'high', 'low', 'close']:
                if col in df_copy.columns:
                    rolling_mean = df_copy[col].rolling(window=window, center=True).mean()
                    df_copy.loc[anomaly_mask, col] = rolling_mean.loc[anomaly_mask]

            if 'volume' in df_copy.columns:
                volume_rolling_mean = df_copy['volume'].rolling(window=window, center=True).mean()
                df_copy.loc[anomaly_mask, 'volume'] = volume_rolling_mean.loc[anomaly_mask]

        # 确保OHLC关系保持一致
        if all(col in df_copy.columns for col in ['open', 'high', 'low', 'close']):
            df_copy['high'] = df_copy[['high', 'open', 'close']].max(axis=1)
            df_copy['low'] = df_copy[['low', 'open', 'close']].min(axis=1)

        return df_copy

    def save(self, path: str = None):
        """保存tokenizer到文件"""
        if path is None and self.cache_dir is not None:
            os.makedirs(self.cache_dir, exist_ok=True)
            path = os.path.join(self.cache_dir, 'candlestick_tokenizer.pkl')

        if path is not None:
            with open(path, 'wb') as f:
                pickle.dump({
                    'change_range': self.change_range,
                    'entity_range': self.entity_range,
                    'shadow_range': self.shadow_range,
                    'volume_range': self.volume_range,
                    'atr_window': self.atr_window,
                    'atr_mult': self.atr_mult,
                    'scale': self.scale,
                    'special_tokens': self.special_tokens,
                    'include_volume': self.include_volume,
                    'detect_anomalies_value': self.detect_anomalies_value,
                    'anomaly_threshold': self.anomaly_threshold,
                    'vocab': self.vocab,
                    'token2idx': self.token2idx,
                    'idx2token': self.idx2token
                }, f)
            if self.verbose:
                print(f"Tokenizer saved to {path}")

    @classmethod
    def load(cls, path: str):
        """从文件加载tokenizer"""
        with open(path, 'rb') as f:
            data = pickle.load(f)

        # 处理旧版本的tokenizer数据
        if 'volume_range' not in data:
            data['volume_range'] = (-9, 9)
        if 'include_volume' not in data:
            data['include_volume'] = False
        if 'detect_anomalies_value' not in data:
            data['detect_anomalies_value'] = False
        if 'anomaly_threshold' not in data:
            data['anomaly_threshold'] = 7.0

        tokenizer = cls(
            change_range=data['change_range'],
            entity_range=data['entity_range'],
            shadow_range=data['shadow_range'],
            volume_range=data['volume_range'],
            atr_window=data['atr_window'],
            atr_mult=data['atr_mult'],
            scale=data['scale'],
            special_tokens=data['special_tokens'],
            include_volume=data['include_volume'],
            detect_anomalies_value=data['detect_anomalies_value'],
            anomaly_threshold=data['anomaly_threshold']
        )

        tokenizer.vocab = data['vocab']
        tokenizer.token2idx = data['token2idx']
        tokenizer.idx2token = data['idx2token']
        tokenizer.vocab_size = len(tokenizer.vocab)

        return tokenizer

    def _create_special_feature(self, special_type: str) -> Dict[str, int]:
        """
        创建特殊特征字典

        Args:
            special_type: 特殊类型，可以是 'anomaly', 'day_gap', 'holiday'

        Returns:
            特征字典
        """
        # 为不同类型的特殊标记创建不同的特征值
        # 使用更小的值，避免与正常的change值混淆
        if special_type == 'anomaly':
            change_value = 0  # 异常值使用0
        elif special_type == 'day_gap':
            change_value = 0  # 交易日间隔使用0
        elif special_type == 'holiday':
            change_value = 0  # 假期间隔使用0
        else:
            change_value = 0  # 默认值

        # 创建特征字典
        if self.include_volume:
            return {
                'change': change_value,
                'entity': 0,
                'upline': 0,
                'downline': 0,
                'volume': 0
            }
        else:
            return {
                'change': change_value,
                'entity': 0,
                'upline': 0,
                'downline': 0
            }

    def _calculate_atr(self, df: pd.DataFrame, window: int = None) -> pd.Series:
        """计算ATR"""
        if window is None:
            window = self.atr_window

        # 使用pandas计算ATR
        # high_low = df['high'] - df['low']
        # high_close = (df['high'] - df['close'].shift(1)).abs()
        # low_close = (df['low'] - df['close'].shift(1)).abs()

        # ranges = pd.concat([high_low, high_close, low_close], axis=1)
        # true_range = ranges.max(axis=1)
        # atr = true_range.rolling(window=window).mean()

        # 使用TA-Lib计算ATR
        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=window)

        return atr

    def tokenize(self, df: pd.DataFrame) -> List[int]:
        """
        将K线数据转换为token序列

        Args:
            df: 包含OHLCV数据的DataFrame，必须包含datetime, open, high, low, close列

        Returns:
            token索引列表
        """
        # 打印输入数据统计
        if self.verbose:
            print(f"\n输入数据统计:")
            print(f"数据形状: {df.shape}")
            print(f"列名: {df.columns.tolist()}")
            print(df.head())
            print(df.describe())

        # 检查数据有效性
        if df.empty:
            print("警告: 输入数据为空")
            return []

        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            print("警告: 输入数据缺少必要的OHLC列")
            return []

        if 'atr' not in df.columns and len(df) < self.atr_window:
            print("警告: 输入数据缺少atr列，且数据长度小于ATR窗口大小，无法计算ATR")
            return []

        # 检查数据中的无效值
        # 使用stack().map()代替已弃用的applymap方法
        # 使用逻辑非操作符代替位操作符~，避免在布尔值上使用~的警告
        is_finite = df[['open', 'high', 'low', 'close']].stack().map(np.isfinite).unstack().all(axis=1)
        invalid_mask = is_finite.apply(lambda x: not x)
        if invalid_mask.any():
            print(f"警告: 发现{invalid_mask.sum()}行包含无效值，将被填充")
            # 填充无效值
            df_copy = df.copy()
            for col in ['open', 'high', 'low', 'close']:
                df_copy.loc[invalid_mask, col] = df_copy[col].median()
            df = df_copy

        # 确保数据按时间排序
        if 'datetime' in df.columns:
            df = df.sort_values('datetime')

        # 检测并修正异常值
        if self.detect_anomalies_value:
            df = self.detect_anomalies(df)
            df = self.correct_anomalies(df, method='interpolate')

        # 计算ATR
        if 'atr' not in df.columns or df['atr'].isnull().any():
            # 检查数据长度是否足够计算ATR
            if len(df) >= self.atr_window:
                # 数据长度足够，正常计算ATR
                atr = self._calculate_atr(df)
                atr = atr.bfill()  # 填充开始的NaN值
            else:
                # 数据长度不足，使用简化的ATR计算方法
                print(f"警告: 数据长度({len(df)})小于ATR窗口大小({self.atr_window})，使用简化的ATR计算方法")
                # 使用收盘价的标准差作为ATR的估计值
                atr = df['close'].rolling(window=min(5, len(df))).std()
                atr = atr.bfill().fillna(df['close'].std())  # 填充NaN值

                # 如果标准差为0或NaN，使用收盘价的一个小比例
                if atr.isnull().any() or (atr == 0).any():
                    atr = pd.Series([df['close'].mean() * 0.01] * len(df), index=df.index)

            # 确保ATR不为零
            min_atr = df['close'].mean() * 0.001  # 使用收盘价平均值的0.1%作为最小ATR
            atr = atr.clip(lower=min_atr)
            df['atr'] = atr
            # 应用ATR乘数和缩放因子
            df['atr'] = df['atr'] * self.atr_mult / self.scale

        # 打印ATR统计
        if self.verbose:
            print(f"\nATR统计:")
            print(f"ATR平均值: {df['atr'].mean()}")
            print(f"ATR最小值: {df['atr'].min()}")
            print(f"ATR最大值: {df['atr'].max()}")

        # 计算K线特征
        features = pd.DataFrame(index=df.index)

        # 计算原始价格变化
        price_change = df['close'] - df['close'].shift(1)

        # 打印价格变化统计
        if self.verbose:
            print(f"\n价格变化统计:")
            print(f"平均变化: {price_change.mean()}")
            print(f"最小变化: {price_change.min()}")
            print(f"最大变化: {price_change.max()}")
            print(f"变化/ATR比例: {(price_change / df['atr']).describe()}")

        # 处理NaN和无穷值，先计算，然后填充NaN，再转换为整数
        features['change'] = (price_change / df['atr']).round()
        features['change'] = features['change'].fillna(0).replace([np.inf, -np.inf], 0).astype(int)

        features['entity'] = ((df['close'] - df['open']) / df['atr']).round()
        features['entity'] = features['entity'].fillna(0).replace([np.inf, -np.inf], 0).astype(int)

        features['upline'] = ((df['high'] - df[['open', 'close']].max(axis=1)) / df['atr']).round()
        features['upline'] = features['upline'].fillna(0).replace([np.inf, -np.inf], 0).astype(int)

        features['downline'] = ((df[['open', 'close']].min(axis=1) - df['low']) / df['atr']).round()
        features['downline'] = features['downline'].fillna(0).replace([np.inf, -np.inf], 0).astype(int)

        # 打印特征统计
        if self.verbose:
            print(f"\n特征统计:")
            print(features.describe())

        # 如果包含交易量特征
        if self.include_volume and 'volume' in df.columns:
            # 计算交易量变化
            volume_change = df['volume'].pct_change()
            # 计算交易量的标准差
            volume_std = volume_change.rolling(window=self.atr_window).std().bfill()
            # 确保标准差不为零
            volume_std = volume_std.clip(lower=0.001)
            # 标准化交易量变化，处理NaN和无穷值
            features['volume'] = (volume_change / volume_std).round()
            features['volume'] = features['volume'].fillna(0).replace([np.inf, -np.inf], 0).astype(int)
            # 限制交易量值在有效范围内
            features['volume'] = features['volume'].clip(self.volume_range[0], self.volume_range[1])

        # 限制值在有效范围内
        features['change'] = features['change'].clip(self.change_range[0], self.change_range[1])
        features['entity'] = features['entity'].clip(self.entity_range[0], self.entity_range[1])
        features['upline'] = features['upline'].clip(self.shadow_range[0], self.shadow_range[1])
        features['downline'] = features['downline'].clip(self.shadow_range[0], self.shadow_range[1])

        # 打印特征分布
        if self.verbose:
            print("\n特征分布:")
            for col in features.columns:
                value_counts = features[col].value_counts().sort_index()
                print(f"{col}值分布:")
                print(value_counts)

        # 检测交易日间隔
        if self.holiday_gap and 'datetime' in df.columns:
            dt = pd.to_datetime(df['datetime'])
            # 计算日期差
            date_diff = dt.dt.date.diff().dt.days

            # 标记交易日间隔和假期
            # 创建一个标记数组，用于记录特殊日期
            special_date_markers = np.zeros(len(features), dtype=object)

            for i in range(1, len(features)):
                if date_diff.iloc[i] > 3:  # 假期间隔
                    special_date_markers[i] = self.HOLIDAY_TOKEN
                elif date_diff.iloc[i] > 1:  # 交易日间隔
                    special_date_markers[i] = self.DAY_GAP_TOKEN

        # 标记异常值
        if self.detect_anomalies_value and 'is_anomaly' in df.columns:
            anomaly_indices = df.index[df['is_anomaly']]
            if len(anomaly_indices) > 0:
                # 如果之前没有创建特殊日期标记数组，现在创建
                if 'special_date_markers' not in locals():
                    special_date_markers = np.zeros(len(features), dtype=object)

                for idx in anomaly_indices:
                    if idx in features.index:
                        # 将异常值标记为特殊token
                        idx_loc = features.index.get_loc(idx)
                        special_date_markers[idx_loc] = self.ANOMALY_TOKEN

        # 生成token字符串，确保与输入DataFrame长度一致
        token_strs = [None] * len(features)

        for i, (_, row) in enumerate(features.iterrows()):
            # 对于前atr_window+1行，使用PAD_TOKEN
            if i < self.atr_window + 1:
                token_strs[i] = self.PAD_TOKEN
                continue

            # 检查是否有特殊标记
            if 'special_date_markers' in locals() and special_date_markers[i]:
                # 使用特殊标记
                token_strs[i] = special_date_markers[i]
                if self.verbose:
                    print(f"使用特殊标记: {special_date_markers[i]}")
            else:
                # 使用正常的特征值
                if self.include_volume and 'volume' in row:
                    token_strs[i] = f"{row['change']}|{row['entity']}|{row['upline']}|{row['downline']}|{row['volume']}"
                else:
                    token_strs[i] = f"{row['change']}|{row['entity']}|{row['upline']}|{row['downline']}"

        # 临时保存到文件分析
        # with open(f'./tokens_{df["code"][0]}.txt', 'w') as f:
        #     f.write('\n'.join(map(str, token_strs)))

        # 转换为token索引，确保与输入DataFrame长度一致
        tokens = []
        for token_str in token_strs:
            if token_str in self.token2idx:
                tokens.append(self.token2idx[token_str])
            else:
                if self.special_tokens:
                    tokens.append(self.token2idx[self.UNK_TOKEN])
                else:
                    tokens.append(self.token2idx['0|0|0|0'])

        return tokens

    def batch_tokenize(self, df_list: List[pd.DataFrame]) -> List[List[int]]:
        """批量将K线数据转换为token序列"""
        return [self.tokenize(df) for df in df_list]

    def decode(self, tokens: List[int]) -> List[Dict[str, int]]:
        """
        将token索引序列解码为K线特征

        Args:
            tokens: token索引列表

        Returns:
            K线特征列表，每个元素是包含change, entity, upline, downline的字典，
            如果包含交易量特征，还会包含volume
        """
        features = []

        for token_idx in tokens:
            if token_idx in self.idx2token:
                token_str = self.idx2token[token_idx]

                # 处理特殊token
                if token_str in [self.PAD_TOKEN, self.UNK_TOKEN]:
                    # 跳过这些特殊token
                    continue
                elif token_str == self.ANOMALY_TOKEN:
                    # 异常数据标记
                    feature = self._create_special_feature('anomaly')
                    features.append(feature)
                    continue
                elif token_str == self.DAY_GAP_TOKEN:
                    # 交易日间隔标记
                    feature = self._create_special_feature('day_gap')
                    features.append(feature)
                    continue
                elif token_str == self.HOLIDAY_TOKEN:
                    # 假期间隔标记
                    feature = self._create_special_feature('holiday')
                    features.append(feature)
                    continue

                # 解析token字符串
                try:
                    parts = token_str.split('|')
                    if len(parts) == 5:  # 包含交易量
                        change, entity, upline, downline, volume = parts
                        feature = {
                            'change': int(change),
                            'entity': int(entity),
                            'upline': int(upline),
                            'downline': int(downline),
                            'volume': int(volume)
                        }
                    elif len(parts) == 4:  # 不包含交易量
                        change, entity, upline, downline = parts
                        feature = {
                            'change': int(change),
                            'entity': int(entity),
                            'upline': int(upline),
                            'downline': int(downline)
                        }
                    else:
                        raise ValueError(f"无法解析token: {token_str}")

                    features.append(feature)
                except Exception as e:
                    # 对于无法解析的token，添加默认值
                    if self.include_volume:
                        features.append({
                            'change': 0,
                            'entity': 0,
                            'upline': 0,
                            'downline': 0,
                            'volume': 0
                        })
                    else:
                        features.append({
                            'change': 0,
                            'entity': 0,
                            'upline': 0,
                            'downline': 0
                        })
            else:
                # 对于不在词汇表中的token，添加默认值
                if self.include_volume:
                    features.append({
                        'change': 0,
                        'entity': 0,
                        'upline': 0,
                        'downline': 0,
                        'volume': 0
                    })
                else:
                    features.append({
                        'change': 0,
                        'entity': 0,
                        'upline': 0,
                        'downline': 0
                    })

        return features

    def tokens_to_candlesticks(self, tokens: List[int], start_price: float, atr: float,
                              start_volume: float = None) -> pd.DataFrame:
        """
        将token序列转换回K线数据

        Args:
            tokens: token索引列表
            start_price: 起始价格
            atr: ATR值
            start_volume: 起始交易量（可选）

        Returns:
            包含OHLC数据的DataFrame，如果包含交易量特征，还会包含volume列
        """
        # 检查输入参数
        if self.verbose:
            print(f"\n输入参数检查:")
            print(f"tokens: {tokens}")
            print(f"start_price: {start_price}")
            print(f"atr: {atr}")
            print(f"start_volume: {start_volume}")

        # 检查tokens是否有效
        if not tokens:
            print("警告: 输入tokens为空")
            columns = ['open', 'high', 'low', 'close']
            if self.include_volume:
                columns.append('volume')
            return pd.DataFrame(columns=columns)

        # 检查tokens是否包含无效值
        invalid_tokens = [t for t in tokens if t < 0 or t >= self.vocab_size]
        if invalid_tokens:
            print(f"警告: 发现{len(invalid_tokens)}个无效token: {invalid_tokens}")
            # 过滤掉无效token
            tokens = [t for t in tokens if 0 <= t < self.vocab_size]
            if not tokens:
                print("警告: 过滤后没有有效token")
                columns = ['open', 'high', 'low', 'close']
                if self.include_volume:
                    columns.append('volume')
                return pd.DataFrame(columns=columns)

        # 确保atr不为零
        if atr <= 0 or not np.isfinite(atr):
            print(f"警告: ATR值无效 ({atr})，使用默认值0.01")
            atr = 0.01

        # 确保start_price有效
        if not np.isfinite(start_price) or start_price <= 0:
            print(f"警告: 起始价格无效 ({start_price})，使用默认值100")
            start_price = 100.0

        try:
            # 解码token
            features = self.decode(tokens)

            if not features:
                print("警告: 解码后的特征为空")
                columns = ['open', 'high', 'low', 'close']
                if self.include_volume:
                    columns.append('volume')
                return pd.DataFrame(columns=columns)

            # 创建DataFrame
            df = pd.DataFrame(features)

            # 打印解码后的特征统计信息
            if self.verbose:
                print("\n解码后的特征统计:")
                print(f"数据形状: {df.shape}")
                print(df.head())

            # 检查是否有必要的列
            required_columns = ['change', 'entity', 'upline', 'downline']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告: 缺少必要的列: {missing_columns}")
                # 添加缺失的列，使用默认值0
                for col in missing_columns:
                    df[col] = 0

            # 打印change值的统计信息
            if 'change' in df.columns and self.verbose:
                print("\n解码后的change值统计:")
                print(f"平均值: {df['change'].mean()}")
                print(f"最小值: {df['change'].min()}")
                print(f"最大值: {df['change'].max()}")
                print(f"正值比例: {(df['change'] > 0).mean():.2%}")
                print(f"负值比例: {(df['change'] < 0).mean():.2%}")
                print(f"零值比例: {(df['change'] == 0).mean():.2%}")

                # 打印change值的分布
                value_counts = df['change'].value_counts().sort_index()
                print("\nchange值分布:")
                print(value_counts)

            # 应用ATR乘数和缩放因子
            atr_scaled = atr * self.atr_mult / self.scale
            if self.verbose:
                print(f"ATR缩放值: {atr_scaled}")

            # 计算价格
            prices = [start_price]
            for i in range(len(df)):
                try:
                    change_value = df.iloc[i]['change']

                    # 打印当前处理的值
                    if self.verbose and (i < 5 or i >= len(df) - 5):  # 只打印前5个和后5个，避免输出过多
                        print(f"处理第{i}个K线: change={change_value}, 当前价格={prices[-1]}")

                    # 处理所有可能的特殊值
                    if change_value < self.change_range[0] or change_value > self.change_range[1]:
                        # 对于超出范围的值，使用前一个价格
                        print(f"警告: change值{change_value}超出范围{self.change_range}，使用前一个价格")
                        curr_price = prices[-1]
                    else:
                        # 正常计算当前K线的收盘价
                        price_change = change_value * atr_scaled
                        curr_price = prices[-1] + price_change

                        # 检查计算结果
                        if not np.isfinite(curr_price) or curr_price <= 0:
                            print(f"警告: 计算的价格无效 ({curr_price})，使用前一个价格")
                            curr_price = prices[-1]

                        if self.verbose and (i < 5 or i >= len(df) - 5):
                            print(f"  价格变化: {price_change}, 新价格: {curr_price}")
                except Exception as e:
                    print(f"处理第{i}个K线时出错: {str(e)}，使用前一个价格")
                    curr_price = prices[-1]

                prices.append(curr_price)

            # 移除第一个价格（它只是起始价格）
            prices = prices[1:]

            # 检查价格列表
            if self.verbose:
                print(f"\n价格列表检查:")
                print(f"价格数量: {len(prices)}")
                print(f"前5个价格: {prices[:5]}")
                print(f"后5个价格: {prices[-5:] if len(prices) >= 5 else prices}")

            # 检查是否有无效价格
            invalid_prices = [p for p in prices if not np.isfinite(p) or p <= 0]
            if invalid_prices:
                print(f"警告: 发现{len(invalid_prices)}个无效价格，将进行修复")
                # 替换无效价格
                for i in range(len(prices)):
                    if not np.isfinite(prices[i]) or prices[i] <= 0:
                        # 使用前一个有效价格或起始价格
                        valid_prices = [p for p in prices[:i] if np.isfinite(p) and p > 0]
                        prices[i] = valid_prices[-1] if valid_prices else start_price

            # 创建OHLC数据
            try:
                ohlc = pd.DataFrame(index=range(len(prices)))
                ohlc['close'] = prices

                # 根据entity计算开盘价
                try:
                    entity_values = df['entity'] * atr_scaled
                    ohlc['open'] = ohlc['close'] - entity_values
                except Exception as e:
                    print(f"计算开盘价时出错: {str(e)}，使用收盘价的偏移值")
                    # 使用收盘价的偏移值作为开盘价
                    ohlc['open'] = ohlc['close'] * 0.99

                # 确保开盘价为正
                ohlc['open'] = ohlc['open'].clip(lower=0.001)

                # 根据影线计算最高价和最低价
                try:
                    upline_values = df['upline'] * atr_scaled
                    downline_values = df['downline'] * atr_scaled

                    ohlc['high'] = ohlc[['open', 'close']].max(axis=1) + upline_values
                    ohlc['low'] = ohlc[['open', 'close']].min(axis=1) - downline_values
                except Exception as e:
                    print(f"计算最高/最低价时出错: {str(e)}，使用开盘/收盘价的偏移值")
                    # 使用开盘价和收盘价的偏移值
                    ohlc['high'] = ohlc[['open', 'close']].max(axis=1) * 1.01
                    ohlc['low'] = ohlc[['open', 'close']].min(axis=1) * 0.99

                # 确保最低价为正
                ohlc['low'] = ohlc['low'].clip(lower=0.001)

                # 确保OHLC关系正确
                ohlc['high'] = ohlc[['high', 'open', 'close']].max(axis=1)
                ohlc['low'] = ohlc[['low', 'open', 'close']].min(axis=1)

                # 如果包含交易量特征，计算交易量
                if self.include_volume and 'volume' in df.columns:
                    if start_volume is None or not np.isfinite(start_volume) or start_volume <= 0:
                        # 如果没有提供起始交易量或无效，使用一个默认值
                        start_volume = 1000000  # 默认交易量
                        print(f"使用默认起始交易量: {start_volume}")

                    # 计算交易量
                    volumes = [start_volume]
                    volume_std = max(1.0, start_volume * 0.2)  # 确保标准差为正

                    for i in range(len(df)):
                        try:
                            # 计算当前K线的交易量
                            volume_change = df.iloc[i]['volume'] * volume_std
                            curr_volume = max(1.0, volumes[-1] + volume_change)  # 确保交易量为正
                            volumes.append(curr_volume)
                        except Exception as e:
                            print(f"计算第{i}个K线交易量时出错: {str(e)}，使用前一个交易量")
                            volumes.append(volumes[-1])

                    # 移除第一个交易量（它只是起始交易量）
                    volumes = volumes[1:]

                    # 添加交易量列
                    ohlc['volume'] = volumes

                # 打印结果统计
                if self.verbose:
                    print("\nOHLC数据统计:")
                    print(f"数据形状: {ohlc.shape}")
                    print(ohlc.head())
                    print(ohlc.describe())

                return ohlc

            except Exception as e:
                import traceback
                print(f"生成OHLC数据时出错: {str(e)}")
                traceback.print_exc()
                # 返回空DataFrame
                columns = ['open', 'high', 'low', 'close']
                if self.include_volume:
                    columns.append('volume')
                return pd.DataFrame(columns=columns)

        except Exception as e:
            import traceback
            print(f"tokens_to_candlesticks总体处理出错: {str(e)}")
            traceback.print_exc()
            # 返回空DataFrame
            columns = ['open', 'high', 'low', 'close']
            if self.include_volume:
                columns.append('volume')
            return pd.DataFrame(columns=columns)

    def visualize_tokenization(self, df: pd.DataFrame, tokens: List[int] = None,
                              reconstructed_df: pd.DataFrame = None, title: str = None,
                              show_volume: bool = True):
        """
        可视化原始K线数据、token化后的数据和重建的K线数据

        Args:
            df: 原始K线数据
            tokens: token索引列表（可选）
            reconstructed_df: 重建的K线数据（可选）
            title: 图表标题
            show_volume: 是否显示交易量
        """
        if tokens is not None and reconstructed_df is None:
            # 如果提供了tokens但没有提供重建的数据，则尝试重建
            atr = self._calculate_atr(df).iloc[-1]
            start_price = df['close'].iloc[0]
            start_volume = df['volume'].iloc[0] if 'volume' in df.columns else None
            reconstructed_df = self.tokens_to_candlesticks(tokens, start_price, atr, start_volume)

        # 确定是否显示交易量
        has_volume = 'volume' in df.columns and show_volume
        has_reconstructed_volume = reconstructed_df is not None and 'volume' in reconstructed_df.columns and show_volume

        # 确定子图数量和布局
        n_rows = 1 + (reconstructed_df is not None)
        if has_volume or has_reconstructed_volume:
            n_rows *= 2

        # 创建图表
        fig, axes = plt.subplots(n_rows, 1, figsize=(12, 4 * n_rows), sharex=True,
                                gridspec_kw={'height_ratios': [3, 1] * (1 + (reconstructed_df is not None)) if has_volume or has_reconstructed_volume else [1] * n_rows})

        # 确保axes是数组
        if n_rows == 1:
            axes = [axes]

        # 绘制原始K线
        ax_idx = 0
        ax1 = axes[ax_idx]
        ax_idx += 1
        ax1.set_title('原始K线数据' if title is None else title)

        # 绘制K线
        for i in range(len(df)):
            # 计算位置和颜色
            x = i
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)

        # 如果有交易量，绘制交易量
        if has_volume:
            ax_vol = axes[ax_idx]
            ax_idx += 1
            ax_vol.set_title('原始交易量')
            ax_vol.bar(range(len(df)), df['volume'], color='blue', alpha=0.5)

        # 如果有重建的数据，绘制重建的K线
        if reconstructed_df is not None:
            ax2 = axes[ax_idx]
            ax_idx += 1
            ax2.set_title('重建的K线数据')

            for i in range(len(reconstructed_df)):
                # 计算位置和颜色
                x = i
                open_price = reconstructed_df['open'].iloc[i]
                close_price = reconstructed_df['close'].iloc[i]
                high_price = reconstructed_df['high'].iloc[i]
                low_price = reconstructed_df['low'].iloc[i]
                color = 'red' if close_price >= open_price else 'green'

                # 绘制实体
                ax2.plot([x, x], [open_price, close_price], color=color, linewidth=6)
                # 绘制影线
                ax2.plot([x, x], [low_price, high_price], color=color, linewidth=1)

            # 如果有重建的交易量，绘制重建的交易量
            if has_reconstructed_volume:
                ax_vol2 = axes[ax_idx]
                ax_vol2.set_title('重建的交易量')
                ax_vol2.bar(range(len(reconstructed_df)), reconstructed_df['volume'], color='purple', alpha=0.5)

        # 添加网格线
        for ax in axes:
            ax.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

        return fig, axes
