"""
测试支持证券代码维度的码本模型

该脚本用于测试CodeAwareEncoder和CodeAwareDecoder模型，
并将它们导出为ONNX格式，确保在C++中能够正确使用。
"""

import os
import sys
import argparse
import torch
import numpy as np
import onnx
import onnxruntime as ort
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型
from pyqlab.models.gpt2.code_aware_codebook import CodeAwareEncoder, CodeAwareDecoder

def create_test_codebook(num_embeddings=1024, embedding_dim=5):
    """创建测试用的码本权重"""
    return torch.randn(num_embeddings, embedding_dim)

def test_encoder(codebook_weights, output_dir, code_size=100):
    """测试编码器并导出为ONNX格式"""
    print("\n=== 测试编码器 ===")
    
    # 创建编码器
    encoder = CodeAwareEncoder(codebook_weights, code_size=code_size)
    
    # 创建测试输入
    test_vector = torch.randn(1, codebook_weights.size(1))
    test_code_id = torch.tensor([0], dtype=torch.int32)
    
    # 测试前向传播
    with torch.no_grad():
        token_id = encoder(test_vector, test_code_id)
        print(f"编码器输入: {test_vector}")
        print(f"证券代码ID: {test_code_id}")
        print(f"编码器输出: {token_id}")
    
    # 导出为ONNX格式
    onnx_path = os.path.join(output_dir, "code_aware_encoder.onnx")
    encoder.to_onnx(onnx_path)
    
    return onnx_path

def test_decoder(codebook_weights, output_dir, code_size=100):
    """测试解码器并导出为ONNX格式"""
    print("\n=== 测试解码器 ===")
    
    # 创建解码器
    num_embeddings, embedding_dim = codebook_weights.size()
    decoder = CodeAwareDecoder(
        codebook_weights, 
        num_embeddings=num_embeddings, 
        embedding_dim=embedding_dim,
        code_size=code_size
    )
    
    # 创建测试输入
    test_token_id = torch.tensor([0], dtype=torch.int32)
    test_code_id = torch.tensor([0], dtype=torch.int32)
    
    # 测试前向传播
    with torch.no_grad():
        vector = decoder(test_token_id, test_code_id)
        print(f"解码器输入: {test_token_id}")
        print(f"证券代码ID: {test_code_id}")
        print(f"解码器输出: {vector}")
    
    # 导出为ONNX格式
    onnx_path = os.path.join(output_dir, "code_aware_decoder.onnx")
    decoder.to_onnx(onnx_path)
    
    return onnx_path

def test_onnx_encoder(onnx_path):
    """测试ONNX编码器"""
    print(f"\n=== 测试ONNX编码器: {onnx_path} ===")
    
    # 加载ONNX模型
    model = onnx.load(onnx_path)
    onnx.checker.check_model(model)
    
    # 打印模型输入输出类型
    print("ONNX模型输入输出类型:")
    for input in model.graph.input:
        print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
    for output in model.graph.output:
        print(f"输出 {output.name}: {input.type.tensor_type.elem_type}")
    
    # 创建ONNX Runtime会话
    session = ort.InferenceSession(onnx_path)
    
    # 打印会话输入输出信息
    print("ONNX会话输入信息:")
    for input in session.get_inputs():
        print(f"名称: {input.name}, 形状: {input.shape}, 类型: {input.type}")
    
    print("ONNX会话输出信息:")
    for output in session.get_outputs():
        print(f"名称: {output.name}, 形状: {output.shape}, 类型: {output.type}")
    
    # 创建测试输入
    input_vector = np.random.randn(1, 5).astype(np.float32)
    code_id = np.array([0], dtype=np.int32)
    
    # 运行推理
    onnx_input = {
        'input_vector': input_vector,
        'code_id': code_id
    }
    
    output_name = session.get_outputs()[0].name
    onnx_output = session.run([output_name], onnx_input)
    
    print(f"ONNX输入: {input_vector}")
    print(f"证券代码ID: {code_id}")
    print(f"ONNX输出: {onnx_output[0]}")
    print(f"ONNX输出类型: {onnx_output[0].dtype}")
    
    return onnx_output[0]

def test_onnx_decoder(onnx_path):
    """测试ONNX解码器"""
    print(f"\n=== 测试ONNX解码器: {onnx_path} ===")
    
    # 加载ONNX模型
    model = onnx.load(onnx_path)
    onnx.checker.check_model(model)
    
    # 打印模型输入输出类型
    print("ONNX模型输入输出类型:")
    for input in model.graph.input:
        print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
    for output in model.graph.output:
        print(f"输出 {output.name}: {input.type.tensor_type.elem_type}")
    
    # 创建ONNX Runtime会话
    session = ort.InferenceSession(onnx_path)
    
    # 打印会话输入输出信息
    print("ONNX会话输入信息:")
    for input in session.get_inputs():
        print(f"名称: {input.name}, 形状: {input.shape}, 类型: {input.type}")
    
    print("ONNX会话输出信息:")
    for output in session.get_outputs():
        print(f"名称: {output.name}, 形状: {output.shape}, 类型: {output.type}")
    
    # 创建测试输入
    token_id = np.array([0], dtype=np.int32)
    code_id = np.array([0], dtype=np.int32)
    
    # 运行推理
    onnx_input = {
        'token_id': token_id,
        'code_id': code_id
    }
    
    output_name = session.get_outputs()[0].name
    onnx_output = session.run([output_name], onnx_input)
    
    print(f"ONNX输入: {token_id}")
    print(f"证券代码ID: {code_id}")
    print(f"ONNX输出: {onnx_output[0]}")
    print(f"ONNX输出类型: {onnx_output[0].dtype}")
    
    return onnx_output[0]

def main():
    parser = argparse.ArgumentParser(description='测试支持证券代码维度的码本模型')
    parser.add_argument('--output_dir', type=str, default='./output', help='输出目录')
    parser.add_argument('--num_embeddings', type=int, default=1024, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=5, help='嵌入维度')
    parser.add_argument('--code_size', type=int, default=100, help='证券代码数量')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建测试用的码本权重
    codebook_weights = create_test_codebook(args.num_embeddings, args.embedding_dim)
    
    # 测试编码器
    encoder_onnx_path = test_encoder(codebook_weights, args.output_dir, args.code_size)
    
    # 测试解码器
    decoder_onnx_path = test_decoder(codebook_weights, args.output_dir, args.code_size)
    
    # 测试ONNX编码器
    test_onnx_encoder(encoder_onnx_path)
    
    # 测试ONNX解码器
    test_onnx_decoder(decoder_onnx_path)
    
    print("\n=== 测试完成 ===")
    print(f"编码器ONNX模型: {encoder_onnx_path}")
    print(f"解码器ONNX模型: {decoder_onnx_path}")

if __name__ == "__main__":
    main()
