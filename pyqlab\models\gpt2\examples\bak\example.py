"""
K线数据离散化与LLM预测模型示例
"""

import os
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import argparse
import json
from datetime import datetime

from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset, TimeSeriesCandlestickDataset
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM

def load_sample_data(file_path=None):
    """
    加载示例数据，如果没有提供文件路径，则生成随机数据
    """
    if file_path and os.path.exists(file_path):
        # 加载真实数据
        df = pd.read_csv(file_path)
        # 确保列名正确
        if 'date' in df.columns and 'datetime' not in df.columns:
            df.rename(columns={'date': 'datetime'}, inplace=True)
        return df
    else:
        # 生成随机数据
        print("未找到数据文件，生成随机数据...")
        np.random.seed(42)
        n_samples = 500
        
        # 生成日期时间
        start_date = pd.Timestamp('2020-01-01')
        dates = [start_date + pd.Timedelta(days=i) for i in range(n_samples)]
        
        # 生成价格
        close = np.random.normal(loc=100, scale=1, size=n_samples).cumsum() + 1000
        daily_volatility = 0.01
        
        high = close * (1 + np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        low = close * (1 - np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        open_price = low + (high - low) * np.random.random(size=n_samples)
        volume = np.random.normal(loc=1000000, scale=200000, size=n_samples).clip(100000, None)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
        
        return df

def tokenize_and_visualize(df, tokenizer):
    """
    将K线数据tokenize并可视化
    """
    # 将K线数据转换为token
    tokens = tokenizer.tokenize(df)
    
    # 计算ATR
    atr = tokenizer._calculate_atr(df).iloc[-1]
    
    # 重建K线数据
    start_price = df['close'].iloc[0]
    reconstructed_df = tokenizer.tokens_to_candlesticks(tokens, start_price, atr)
    
    # 可视化
    tokenizer.visualize_tokenization(df, tokens, reconstructed_df)
    
    return tokens, reconstructed_df

def create_and_train_model(dataset, vocab_size, code_size, device, args):
    """
    创建并训练模型
    """
    # 创建模型
    model = CandlestickLLM(
        vocab_size=vocab_size,
        code_size=code_size,
        block_size=args.seq_len,
        n_layer=args.n_layer,
        n_head=args.n_head,
        d_model=args.d_model,
        dropout=args.dropout,
        use_time_features=args.use_time_features
    ).to(device)
    
    # 创建DataLoader
    dataloader = dataset.get_dataloader(
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers
    )
    
    # 配置优化器
    optimizer = model.configure_optimizers(
        weight_decay=args.weight_decay,
        learning_rate=args.learning_rate,
        betas=(0.9, 0.95),
        device_type='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    # 训练模型
    model.train()
    for epoch in range(args.num_epochs):
        total_loss = 0
        for batch_idx, batch in enumerate(dataloader):
            # 将数据移动到设备
            input_tokens = batch['input_tokens'].to(device)
            target_tokens = batch['target_tokens'].to(device)
            code_ids = batch['code_ids'].to(device)
            
            # 准备时间特征（如果有）
            time_features = None
            if args.use_time_features and 'input_time_features' in batch:
                time_features = batch['input_time_features'].to(device)
            
            # 前向传播
            logits, loss = model(input_tokens, code_ids, time_features, target_tokens)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            if batch_idx % args.log_interval == 0:
                print(f'Epoch: {epoch+1}/{args.num_epochs}, Batch: {batch_idx}/{len(dataloader)}, '
                      f'Loss: {loss.item():.4f}')
        
        avg_loss = total_loss / len(dataloader)
        print(f'Epoch: {epoch+1}/{args.num_epochs}, Average Loss: {avg_loss:.4f}')
    
    return model

def generate_predictions(model, dataset, tokenizer, device, args):
    """
    生成预测并可视化
    """
    model.eval()
    
    # 获取一个样本
    sample = dataset[0]
    input_tokens = torch.tensor(sample['input_tokens'], dtype=torch.long).unsqueeze(0).to(device)
    code_id = torch.tensor(sample['code_id'], dtype=torch.long).unsqueeze(0).to(device)
    
    # 准备时间特征（如果有）
    time_features = None
    if args.use_time_features and 'input_time_features' in sample:
        time_features = torch.tensor(sample['input_time_features'], dtype=torch.float).unsqueeze(0).to(device)
    
    # 生成预测
    with torch.no_grad():
        generated_tokens = model.generate(
            input_tokens,
            code_id,
            time_features,
            max_new_tokens=args.pred_len,
            temperature=args.temperature,
            top_k=args.top_k
        )
    
    # 获取原始数据
    df = dataset.data[0]
    
    # 计算ATR
    atr = tokenizer._calculate_atr(df).iloc[-1]
    
    # 获取最后一个价格作为起始价格
    start_price = df['close'].iloc[-1]
    
    # 将生成的token转换回K线数据
    predicted_ohlc = tokenizer.tokens_to_candlesticks(
        generated_tokens[0, -args.pred_len:].cpu().numpy(),
        start_price,
        atr
    )
    
    # 添加日期
    last_date = pd.to_datetime(df['datetime'].iloc[-1])
    if 'datetime' in df.columns:
        # 计算日期间隔
        date_diff = pd.to_datetime(df['datetime']).diff().median()
        predicted_dates = [last_date + (i+1) * date_diff for i in range(len(predicted_ohlc))]
        predicted_ohlc['datetime'] = predicted_dates
    
    # 可视化预测结果
    plt.figure(figsize=(12, 6))
    
    # 绘制历史数据
    plt.subplot(1, 2, 1)
    plt.title('历史K线')
    for i in range(min(30, len(df))):
        idx = len(df) - 30 + i if len(df) > 30 else i
        x = i
        open_price = df['open'].iloc[idx]
        close_price = df['close'].iloc[idx]
        high_price = df['high'].iloc[idx]
        low_price = df['low'].iloc[idx]
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)
    
    # 绘制预测数据
    plt.subplot(1, 2, 2)
    plt.title('预测K线')
    for i in range(len(predicted_ohlc)):
        x = i
        open_price = predicted_ohlc['open'].iloc[i]
        close_price = predicted_ohlc['close'].iloc[i]
        high_price = predicted_ohlc['high'].iloc[i]
        low_price = predicted_ohlc['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)
    
    plt.tight_layout()
    plt.show()
    
    return predicted_ohlc

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='K线数据离散化与LLM预测模型示例')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, default=None, help='数据文件路径')
    parser.add_argument('--seq_len', type=int, default=30, help='输入序列长度')
    parser.add_argument('--pred_len', type=int, default=5, help='预测序列长度')
    parser.add_argument('--stride', type=int, default=1, help='滑动窗口步长')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    
    # Tokenizer参数
    parser.add_argument('--change_range', type=json.loads, default=[-12, 12], help='change值的范围')
    parser.add_argument('--entity_range', type=json.loads, default=[-12, 12], help='entity值的范围')
    parser.add_argument('--shadow_range', type=json.loads, default=[0, 7], help='影线值的范围')
    parser.add_argument('--atr_window', type=int, default=100, help='ATR计算窗口')
    parser.add_argument('--atr_mult', type=float, default=0.88, help='ATR乘数')
    parser.add_argument('--scale', type=int, default=10, help='缩放因子')
    
    # 模型参数
    parser.add_argument('--n_layer', type=int, default=6, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=512, help='模型维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比例')
    
    # 训练参数
    parser.add_argument('--num_epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32, help='批大小')
    parser.add_argument('--learning_rate', type=float, default=3e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.1, help='权重衰减')
    parser.add_argument('--log_interval', type=int, default=10, help='日志间隔')
    parser.add_argument('--num_workers', type=int, default=0, help='DataLoader工作进程数')
    
    # 生成参数
    parser.add_argument('--temperature', type=float, default=0.8, help='温度参数')
    parser.add_argument('--top_k', type=int, default=50, help='Top-K采样')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--no_cuda', action='store_true', help='不使用CUDA')
    parser.add_argument('--save_model', action='store_true', help='保存模型')
    parser.add_argument('--save_path', type=str, default='./model.pt', help='模型保存路径')
    
    args = parser.parse_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 设置设备
    use_cuda = not args.no_cuda and torch.cuda.is_available()
    device = torch.device('cuda' if use_cuda else 'cpu')
    print(f'使用设备: {device}')
    
    # 加载数据
    df = load_sample_data(args.data_path)
    print(f'数据形状: {df.shape}')
    
    # 创建tokenizer
    tokenizer = CandlestickTokenizer(
        change_range=tuple(args.change_range),
        entity_range=tuple(args.entity_range),
        shadow_range=tuple(args.shadow_range),
        atr_window=args.atr_window,
        atr_mult=args.atr_mult,
        scale=args.scale
    )
    
    # Tokenize并可视化
    tokens, reconstructed_df = tokenize_and_visualize(df, tokenizer)
    
    # 创建数据集
    if args.use_time_features:
        dataset = TimeSeriesCandlestickDataset(
            data=df,
            tokenizer=tokenizer,
            seq_len=args.seq_len,
            pred_len=args.pred_len,
            stride=args.stride,
            time_features=True
        )
    else:
        dataset = CandlestickDataset(
            data=df,
            tokenizer=tokenizer,
            seq_len=args.seq_len,
            pred_len=args.pred_len,
            stride=args.stride
        )
    
    print(f'数据集大小: {len(dataset)}')
    
    # 创建并训练模型
    model = create_and_train_model(
        dataset=dataset,
        vocab_size=tokenizer.vocab_size,
        code_size=1,  # 这里只有一个证券
        device=device,
        args=args
    )
    
    # 生成预测
    predicted_ohlc = generate_predictions(
        model=model,
        dataset=dataset,
        tokenizer=tokenizer,
        device=device,
        args=args
    )
    
    print("预测结果:")
    print(predicted_ohlc)
    
    # 保存模型
    if args.save_model:
        torch.save(model.state_dict(), args.save_path)
        print(f'模型已保存到 {args.save_path}')

if __name__ == '__main__':
    main()
