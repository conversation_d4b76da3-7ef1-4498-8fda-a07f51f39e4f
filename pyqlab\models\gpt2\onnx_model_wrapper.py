"""
ONNX模型包装器

为ONNX格式的CandlestickLLM模型提供与PyTorch模型兼容的接口，以便在回测器中使用。
"""

import torch
import torch.nn.functional as F
import numpy as np
import onnxruntime as ort

class OnnxModelWrapper:
    """ONNX模型包装器，提供与PyTorch模型兼容的接口"""

    def __init__(self, session, device='cpu', block_size=30):
        """
        初始化ONNX模型包装器

        Args:
            session: ONNX运行时会话
            device: 计算设备
            block_size: 序列长度
        """
        self.session = session
        self.device = device
        self.is_training = False
        self.block_size = block_size

        # 获取模型输入输出信息
        self.input_names = [input.name for input in session.get_inputs()]
        self.output_names = [output.name for output in session.get_outputs()]

        # 检查模型提供商
        self.providers = session.get_providers()

        # 打印模型信息
        print(f"ONNX模型输入: {self.input_names}")
        print(f"ONNX模型输出: {self.output_names}")
        print(f"ONNX模型提供商: {self.providers}")

    def __call__(self, input_tokens, code_ids, time_features=None, targets=None):
        """
        模拟PyTorch模型的前向传播

        Args:
            input_tokens: 输入token序列，形状为(batch_size, seq_len)
            code_ids: 证券代码ID，形状为(batch_size)
            time_features: 时间特征，形状为(batch_size, seq_len, n_time_features)
            targets: 目标输出（可选），形状为(batch_size, seq_len)

        Returns:
            logits: 模型输出
            loss: 损失值（始终为None，因为ONNX模型不计算损失）
        """
        # 准备输入
        inputs = {}

        # 根据输入名称准备数据
        if 'input_tokens' in self.input_names:
            inputs['input_tokens'] = input_tokens.cpu().numpy().astype(np.int32)
        elif 'input.1' in self.input_names:
            inputs['input.1'] = input_tokens.cpu().numpy().astype(np.int32)

        if 'code_ids' in self.input_names:
            # 保持code_ids的维度不变
            inputs['code_ids'] = code_ids.cpu().numpy().astype(np.int32)
        elif 'input.2' in self.input_names:
            # 保持code_ids的维度不变
            inputs['input.2'] = code_ids.cpu().numpy().astype(np.int32)

        if time_features is not None:
            if 'time_features' in self.input_names:
                inputs['time_features'] = time_features.cpu().numpy().astype(np.float32)
            elif 'input.3' in self.input_names:
                inputs['input.3'] = time_features.cpu().numpy().astype(np.float32)

        # 运行推理
        outputs = self.session.run(None, inputs)

        # 将输出转换为PyTorch张量
        logits = torch.tensor(outputs[0])

        # 如果设备是CUDA，则将输出移动到GPU
        if self.device == 'cuda':
            logits = logits.cuda()

        return logits, None

    def to(self, device):
        """
        模拟PyTorch模型的to方法

        Args:
            device: 计算设备

        Returns:
            self: 返回自身以支持链式调用
        """
        self.device = device
        return self

    def eval(self):
        """
        模拟PyTorch模型的eval方法

        Returns:
            self: 返回自身以支持链式调用
        """
        self.is_training = False
        return self

    def train(self):
        """
        模拟PyTorch模型的train方法

        Returns:
            self: 返回自身以支持链式调用
        """
        self.is_training = True
        return self

    def get_num_params(self):
        """
        模拟CandlestickLLM模型的get_num_params方法

        Returns:
            0: ONNX模型不提供参数数量信息
        """
        return 0

    @torch.no_grad()
    def generate(self, input_tokens, code_ids, time_features=None, max_new_tokens=10, temperature=1.0, top_k=None):
        """
        生成新的token序列

        Args:
            input_tokens: 输入token序列 [batch_size, seq_len]
            code_ids: 证券代码ID [batch_size]
            time_features: 时间特征 [batch_size, seq_len, n_time_features]
            max_new_tokens: 生成的最大新token数量
            temperature: 温度参数，控制采样的随机性
            top_k: 只考虑概率最高的前k个token

        Returns:
            生成的token序列 [batch_size, seq_len + max_new_tokens]
        """
        # 复制输入，以便我们可以追加生成的token
        tokens = input_tokens.clone()

        # 生成新token
        for i in range(max_new_tokens):
            # 如果序列太长，截断它
            if tokens.size(1) > self.block_size:
                tokens = tokens[:, -self.block_size:]
                if time_features is not None:
                    time_features = time_features[:, -self.block_size:, :]

            # 前向传播
            logits, _ = self(tokens, code_ids, time_features)

            # 只关注最后一个时间步的logits
            logits = logits[:, -1, :] / max(temperature, 1e-6)

            # 可选地只保留top-k个logits
            if top_k is not None and top_k > 0:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')

            # 应用softmax得到概率
            probs = F.softmax(logits, dim=-1)

            # 采样下一个token
            next_token = torch.multinomial(probs, num_samples=1)

            # 追加到序列
            tokens = torch.cat((tokens, next_token), dim=1)

        return tokens
