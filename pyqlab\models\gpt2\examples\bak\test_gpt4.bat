@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python test_candlestick_gpt4.py ^
--data_path f:/hqdata/fut_sf_min5.parquet ^
--begin_date 2023-01-01 ^
--end_date 2025-12-31 ^
--model_path e:/lab/RoboQuant/pylab/checkpoints/candlestick_gpt4/best_model.pt ^
--nonlinear_tokenizer ^
--seq_len 64 ^
--pred_len 10 ^
--temperature 0.8 ^
--top_k 50 ^
--output_dir e:/lab/RoboQuant/pylab/results/candlestick_gpt4 ^
--seed 42

pause
