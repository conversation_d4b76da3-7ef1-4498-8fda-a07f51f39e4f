"""
Candlestick LLM 训练示例

演示如何使用CandlestickLLMTrainer训练高级版CandlestickLLM模型
本示例默认使用AdvancedCandlestickLLM，可以通过参数选择使用基础版CandlestickLLM
"""

import os
import sys
import argparse
import torch

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和训练器
import numpy as np
from datetime import datetime
from time import time

from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.trainer import CandlestickLLMTrainer
from pyqlab.models.gpt2.utils import load_single_data, generate_mock_data, compare_candlesticks, save_model_info, get_model_name
from pyqlab.const import MODEL_FUT_CODES

# 使用utils.py中的generate_mock_data函数

def save_model_as_onnx(args, model, checkpoint_dir, seq_len=30, best_val_loss=None):
    """将模型保存为ONNX格式

    Args:
        model: 训练好的模型
        checkpoint_dir: 保存目录
        seq_len: 序列长度
        best_val_loss: 最佳验证损失，用于文件名

    Returns:
        bool: 是否成功导出
    """
    try:
        # 准备模型输入
        code_ids = torch.zeros(1, dtype=torch.int32)  # 批大小为1的代码ID
        input_tokens = torch.zeros((1, seq_len), dtype=torch.int32)  # 批大小为1，序列长度为seq_len的输入token
        time_features = torch.zeros((1, seq_len, 5), dtype=torch.float32)  # 批大小为1，序列长度为seq_len，5个时间特征

        # 创建导出路径
        os.makedirs(checkpoint_dir, exist_ok=True)

        # 生成模型名称
        model_type = "basic" if isinstance(model, CandlestickLLM) else "advanced"
        tm_str = datetime.now().strftime('%m%d%H')
        loss_str = f"{best_val_loss:.3f}" if best_val_loss is not None else "unknown"
        model_name = f"{model_type}_ct_llm_{get_model_name(args)}_{tm_str}_{loss_str}"
        onnx_path = os.path.join(checkpoint_dir, f"{model_name}.onnx")

        # 设置模型为评估模式
        model.eval()

        # 导出模型
        torch.onnx.export(
            model,                                      # 模型
            (input_tokens, code_ids, time_features),    # 模型输入
            onnx_path,                                  # 输出路径
            export_params=True,                         # 存储训练好的参数权重
            opset_version=14,                           # ONNX版本
            do_constant_folding=True,                   # 是否执行常量折叠优化
            input_names=['input_tokens', 'code_ids', 'time_features'],  # 输入名称
            output_names=['logits'],                    # 输出名称
            dynamic_axes={                              # 动态轴
                'input_tokens': {0: 'batch_size', 1: 'sequence_length'},
                'code_ids': {0: 'batch_size'},
                'time_features': {0: 'batch_size', 1: 'sequence_length'},
                'logits': {0: 'batch_size', 1: 'sequence_length'}
            }
        )

        print(f"模型已成功导出为ONNX格式: {onnx_path}")
        return True
    except Exception as e:
        print(f"导出ONNX模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='K线LLM模型训练示例')
    parser.add_argument('--model', type=str, default='basic', choices=['basic', 'advanced'],
                        help='选择模型类型: basic (基础版CandlestickLLM) 或 advanced (高级版AdvancedCandlestickLLM)')
    parser.add_argument('--samples', type=int, default=1000, help='每个证券的样本数量')
    parser.add_argument('--securities', type=int, default=5, help='证券数量')
    parser.add_argument('--data_path', type=str, default=None, help='数据文件路径')
    parser.add_argument('--market', default='fut', choices=['fut', 'stk'], type=str, help='市场类型')
    parser.add_argument('--block_name', default='sf', choices=['sf', 'main', 'top', 'hot'], type=str, help='板块名称')
    parser.add_argument('--period', default='min5', choices=['day', 'min5', 'min1'], type=str, help='周期')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--seq_len', type=int, default=20, help='序列长度')
    parser.add_argument('--n_layer', type=int, default=5, help='')
    parser.add_argument('--n_head', type=int, default=8, help='')
    parser.add_argument('--d_model', type=int, default=128, help='')
    parser.add_argument('--stride', type=int, default=5, help='滑动窗口步长')
    parser.add_argument('--batch_size', type=int, default=32, help='批大小')
    parser.add_argument('--epochs', type=int, default=3, help='训练轮数')
    parser.add_argument('--checkpoint_dir', type=str, default='e:/lab/RoboQuant/pylab/checkpoints', help='检查点目录')
    parser.add_argument('--export_onnx', action='store_true', help='是否导出ONNX模型')
    args = parser.parse_args()

    # 打印选择的模型类型
    model_type = "基础版CandlestickLLM" if args.model == 'basic' else "高级版AdvancedCandlestickLLM"
    print(f"选择的模型类型: {model_type}")

    if args.data_path is not None:
        # 加载数据
        print(f"从{args.data_path}加载数据...")
        train_data, train_code_ids, val_data, val_code_ids = load_single_data(
            f'{args.data_path}/{args.market}_{args.block_name}_{args.period}.parquet',
            args.begin_date,
            args.end_date
        )
    else:
        # 生成模拟数据
        train_data, train_code_ids = generate_mock_data(n_samples=args.samples, n_securities=args.securities, trend='random')
        val_data, val_code_ids = generate_mock_data(n_samples=args.samples//5, n_securities=args.securities//2, trend='random')

    print(f"训练数据: {len(train_data)} 个证券，每个证券 {len(train_data[0])} 个样本")
    print(f"验证数据: {len(val_data)} 个证券，每个证券 {len(val_data[0])} 个样本")

    # 创建tokenizer
    if args.model == 'basic':
        print("创建基础版tokenizer...")
        tokenizer = CandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            atr_window=100,
            atr_mult=0.88,
            scale=10,
            include_volume=False
        )
    else:
        print("创建非线性tokenizer...")
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            include_volume=False
        )

    # 创建模型
    if args.model == 'basic':
        print("创建基础版CandlestickLLM模型...")
        model = CandlestickLLM(
            vocab_size=tokenizer.vocab_size,
            code_size=len(MODEL_FUT_CODES),
            block_size=30,
            n_layer=args.n_layer,  # 大幅减少层数以加快训练
            n_head=args.n_head,
            d_model=args.d_model,  # 大幅减少模型维度以加快训练
            dropout=0.1,
            use_time_features=True
        )
        checkpoint_dir = f'{args.checkpoint_dir}/basic_candlestick_llm'
    else:
        print("创建高级版AdvancedCandlestickLLM模型...")
        model = AdvancedCandlestickLLM(
            vocab_size=tokenizer.vocab_size,
            code_size=len(MODEL_FUT_CODES),
            block_size=30,
            n_layer=args.n_layer,  # 大幅减少层数以加快训练
            n_head=args.n_head,
            d_model=args.d_model,  # 大幅减少模型维度以加快训练
            dropout=0.1,
            use_time_features=True,
            use_multi_task=True
        )
        checkpoint_dir = f'{args.checkpoint_dir}/advanced_candlestick_llm'

    # 创建训练器
    print("创建训练器...")
    trainer = CandlestickLLMTrainer(
        model=model,
        tokenizer=tokenizer,
        train_data=train_data,
        train_code_ids=train_code_ids,
        stride=args.stride,
        val_data=val_data,
        val_code_ids=val_code_ids,
        seq_len=args.seq_len,
        batch_size=args.batch_size,
        learning_rate=5e-4,
        max_epochs=args.epochs,
        log_interval=50,
        eval_interval=200,
        save_interval=500,
        checkpoint_dir=checkpoint_dir
    )

    # 训练模型
    print("开始训练...")
    try:
        results = trainer.train()
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        results = None

    # 绘制训练历史
    print("绘制训练历史...")
    if results is not None:
        try:
            trainer.plot_training_history()
        except Exception as e:
            print(f"绘制训练历史时出现错误: {e}")

    # 生成样本预测
    print("生成样本预测...")
    try:
        sample_df = val_data[0].iloc[-120:].copy()
        predicted_df = trainer.generate_sample(
            input_df=sample_df,
            code_id=val_code_ids[0],
            max_new_tokens=5,
            temperature=0.8,
            top_k=50
        )
    except Exception as e:
        print(f"生成样本预测时出现错误: {e}")
        predicted_df = None

    print("预测结果:")
    print(predicted_df)

    # 可视化预测结果
    if predicted_df is not None:
        try:
            # 使用工具函数比较K线
            title = f"{model_type}预测结果"
            compare_candlesticks(
                input_df=sample_df,
                predicted_df=predicted_df,
                title=title,
                save_path=f'{checkpoint_dir}/prediction_sample.png'
            )

            # 保存模型信息
            save_model_info(model, tokenizer, checkpoint_dir)
        except Exception as e:
            print(f"可视化预测结果时出现错误: {e}")

    # 导出ONNX模型
    if args.export_onnx:
        print("导出ONNX模型...")
        try:
            # 获取最佳验证损失
            best_val_loss = results.get('best_val_loss') if results else None

            # 导出模型
            success = save_model_as_onnx(
                args=args,
                model=model,
                checkpoint_dir=checkpoint_dir,
                seq_len=args.seq_len,
                best_val_loss=best_val_loss
            )

            if success:
                print("模型已成功导出为ONNX格式")
            else:
                print("导出ONNX模型失败")
        except Exception as e:
            print(f"导出ONNX模型时出现错误: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("跳过导出ONNX模型 (使用--export_onnx参数启用)")

    print(f"示例完成，结果保存在 {checkpoint_dir}/ 目录下")

if __name__ == '__main__':
    main()
