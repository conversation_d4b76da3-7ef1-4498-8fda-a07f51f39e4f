@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python backtest_candlestick_gpt4.py ^
--data_path f:/hqdata/fut_sf_min5.parquet ^
--begin_date 2024-01-01 ^
--end_date 2025-12-31 ^
--model_path e:/lab/RoboQuant/pylab/checkpoints/candlestick_gpt4/best_model.pt ^
--safe_tokenize ^
--seq_len 30 ^
--commission 0.001 ^
--threshold 0.6 ^
--temperature 0.8 ^
--signal_type topk ^
--output_dir e:/lab/RoboQuant/pylab/results/candlestick_gpt4_backtest ^
--seed 42
@REM --nonlinear_tokenizer ^

pause
