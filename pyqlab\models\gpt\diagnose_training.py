#!/usr/bin/env python3
"""
诊断训练脚本 - 检查训练过程中可能的卡顿点
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

def test_data_loading():
    """测试数据加载速度"""
    print("🧪 测试数据加载...")
    start_time = time.time()
    
    try:
        from pyqlab.models.gpt.train_balanced_bar_gpt4 import BalancedBarDataModule
        
        class MockArgs:
            data_path = 'f:/featdata/barenc/db2'
            market = 'fut'
            block_name = 'top'  # 使用实际的配置
            period = 'min1'
            start_year = 2024
            end_year = 2024
            start_date = ''
            end_date = ''
            block_size = 20
            batch_size = 32
            num_workers = 0
            time_encoding = 'timeF'
            vocab_size = 1602
            code_size = 96
            seed = 42
        
        args = MockArgs()
        
        print("创建数据模块...")
        datamodule = BalancedBarDataModule(args, {'method': 'none'})
        
        print("获取数据集...")
        dataset = datamodule.get_dataset()
        print(f"数据集大小: {len(dataset)}")
        
        # 测试数据集划分
        from torch.utils.data import Subset
        from sklearn.model_selection import KFold
        
        print("测试交叉验证划分...")
        kfold = KFold(n_splits=2, shuffle=True, random_state=42)
        for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):
            print(f"Fold {fold}: 训练集 {len(train_idx)}, 验证集 {len(val_idx)}")
            
            train_data = Subset(dataset, train_idx[:1000])  # 只取前1000个样本测试
            val_data = Subset(dataset, val_idx[:1000])
            
            datamodule.set_fold_data(train_data, val_data)
            
            print("测试数据加载器...")
            train_loader = datamodule.train_dataloader()
            val_loader = datamodule.val_dataloader()
            
            print(f"训练加载器批次数: {len(train_loader)}")
            print(f"验证加载器批次数: {len(val_loader)}")
            
            # 测试加载第一个批次
            print("加载第一个训练批次...")
            batch_start = time.time()
            first_batch = next(iter(train_loader))
            batch_time = time.time() - batch_start
            print(f"第一个批次加载时间: {batch_time:.2f}秒")
            print(f"批次数据形状: {[x.shape if hasattr(x, 'shape') else type(x) for x in first_batch]}")
            
            break  # 只测试第一个fold
        
        elapsed = time.time() - start_time
        print(f"✅ 数据加载测试完成，耗时: {elapsed:.2f}秒")
        return True
        
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 数据加载测试失败，耗时: {elapsed:.2f}秒")
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🧪 测试模型创建...")
    start_time = time.time()
    
    try:
        from pyqlab.models.gpt.train_balanced_bar_gpt4 import BalancedBarGpt4Trainer
        
        model_config = {
            'block_size': 20,
            'code_size': 96,
            'vocab_size': 1602,  # 使用较小的词汇表
            'n_layer': 2,
            'n_head': 4,
            'd_model': 64,
            'time_encoding': 'timeF',
            'time_embed_type': 'time_feature',
            'freq': 't',
            'pos_embed_type': 'rope',
            'dropout': 0.1
        }
        
        training_config = {
            'learning_rate': 1e-3,
            'weight_decay': 0.0,
            'use_class_weights': False,  # 不使用类别权重
            'balance_method': 'none'
        }
        
        print("创建模型...")
        model = BalancedBarGpt4Trainer(model_config, training_config)
        
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        elapsed = time.time() - start_time
        print(f"✅ 模型创建测试完成，耗时: {elapsed:.2f}秒")
        return True
        
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 模型创建测试失败，耗时: {elapsed:.2f}秒")
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_setup():
    """测试训练设置"""
    print("\n🧪 测试训练设置...")
    start_time = time.time()
    
    try:
        import pytorch_lightning as pl
        from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
        
        print("创建训练器...")
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=3, mode='min'),
            ModelCheckpoint(monitor='val_loss', mode='min', save_top_k=1)
        ]
        
        trainer = pl.Trainer(
            max_epochs=1,
            callbacks=callbacks,
            enable_progress_bar=True,
            enable_model_summary=True,
            accelerator='auto',
            devices='auto'
        )
        
        print(f"训练器创建成功，最大轮数: {trainer.max_epochs}")
        
        elapsed = time.time() - start_time
        print(f"✅ 训练设置测试完成，耗时: {elapsed:.2f}秒")
        return True
        
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 训练设置测试失败，耗时: {elapsed:.2f}秒")
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有诊断测试"""
    print("开始训练诊断...")
    print("=" * 50)
    
    tests = [
        ("数据加载测试", test_data_loading),
        ("模型创建测试", test_model_creation),
        ("训练设置测试", test_training_setup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"   ❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"诊断结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！训练脚本应该可以正常运行。")
        print("💡 建议：")
        print("   1. 确保不使用 --use_class_weights 参数")
        print("   2. 使用较小的 batch_size 和 block_size")
        print("   3. 设置 num_workers=0 避免多进程问题")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
