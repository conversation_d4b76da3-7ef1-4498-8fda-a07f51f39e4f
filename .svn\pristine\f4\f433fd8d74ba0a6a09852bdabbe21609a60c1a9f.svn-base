
import torch
from torch.utils.data import Dataset
from pyqlab.data.dataset.pipeline import Pipeline
from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
import numpy as np

class BarDataset(Dataset):
    """
    Emits batches of bars
    """

    @staticmethod
    def get_default_config():
        C = CN()
        C.block_size = 15
        C.is_sf = False
        C.data_path = 'd:/RoboQuant2/store/barenc'
        C.market = 'fut'
        C.block_name = 'sf'
        C.period = 'day'
        C.start_year = None
        C.end_year = None
        C.start_time = ""
        C.end_time = ""
        C.sel_codes = None
        C.timeenc = 0
        C.step_size = 1
        return C
    
    def __init__(self, config, data):
        self.config = config

        bars = Pipeline.get_vocab()

        self.itobar = { i:ch for i,ch in enumerate(bars) }

        self.vocab_size = len(bars)
        self.data = data
        self.block_size = self.config.block_size
        # 统计每个合约的数据量
        codecount=self.data['code_encoded'].value_counts().to_dict()
        codecount = [i for i in dict(sorted(codecount.items())).values()]
        codecount = [i - self.block_size -1 for i in codecount]
        # 每个值都必须大于等于0
        assert all([i >= 0 for i in codecount])
        self.codecount = np.cumsum(codecount)

    def get_vocab_size(self):
        return self.vocab_size

    def get_block_size(self):
        return self.block_size
    
    def i_to_idx(self, i):
        n = np.searchsorted(self.codecount, i, side='right')
        return i + n * (self.block_size + 1)

    def __len__(self):
        return self.codecount[-1]
    
    def __getitem__(self, idx):
        idx = self.i_to_idx(idx)
        code = self.data.iloc[idx:idx+self.block_size+1, 0].astype(int).values
        assert code[0] == code[-1], "Code mismatch!"
        bar = self.data.iloc[idx:idx+self.block_size+1, 1].astype(int).values
        tf = self.data.iloc[idx:idx+self.block_size+1, -5:].astype(float).values
        x_mark = torch.tensor(tf[:-1], dtype=torch.float)
        y_mark = torch.tensor(tf[1:], dtype=torch.float)
        code = torch.tensor(code[:-1], dtype=torch.long)
        x = torch.tensor(bar[:-1], dtype=torch.long)
        y = torch.tensor(bar[1:], dtype=torch.long)
        return code, x, x_mark, y, y_mark
    
    """
    def __getitem__(self, idx):
        code = self.data[idx][:, 0].astype(int)
        dix = self.data[idx][:, 1].astype(int)
        dtf = self.data_mark[idx][:, :].astype(float)
        code = torch.tensor(code[:-1], dtype=torch.long)
        x = torch.tensor(dix[:-1], dtype=torch.long)
        x_mark = torch.tensor(dtf[:-1, :], dtype=torch.float)
        y = torch.tensor(dix[1:], dtype=torch.long)
        y_mark = torch.tensor(dtf[1:, :], dtype=torch.float)
        return code, x, x_mark, y, y_mark
    """