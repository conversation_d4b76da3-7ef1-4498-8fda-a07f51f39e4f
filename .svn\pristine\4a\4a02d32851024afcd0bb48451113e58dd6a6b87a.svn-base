{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 盘口数据特征\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- tick数据量太大，如果保存所有数据，没有足够的存储空间\n", "    - 只保存主连合约\n", "    - 保存文件格式除考虑支持压缩\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import date, datetime\n", "from pyqlab.const import MAIN_FUT_MARKET_CODES, SF_FUT_CODES\n", "data_path = 'f:/hqdata'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def is_exist_folder(path: str, sub_folder: str):\n", "    return sub_folder in os.listdir(path) \n", "    \n", "def is_exist_file(filename: str):\n", "    return os.path.isfile(filename)\n", "\n", "def get_dir_list(dir_path):\n", "    # Get a list of the content inside the directory\n", "    files = os.listdir(dir_path)\n", "\n", "    # Print out the list of files\n", "    dir_list = []\n", "    for file in files:\n", "        dir_list.append(file)\n", "    return dir_list    \n", "\n", "def year_month_segment(year: int):\n", "    seg = []\n", "    for month in range(1, 13):\n", "        first_day = datetime.date(year, month, 1)\n", "        if month == 12:\n", "            last_day = datetime.date(year+1, 1, 1) - datetime.timedelta(days=1)\n", "        else:\n", "            last_day = datetime.date(year, month+1, 1) - datetime.timedelta(days=1)\n", "        seg.append((first_day.strftime('%Y%m%d'), last_day.strftime('%Y%m%d')))\n", "        # print(f\"Month {month}: First day: {first_day.strftime('%Y%m%d')}, Last day: {last_day.strftime('%Y%m%d')}\")\n", "    return seg\n", "\n", "def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def restore_order_of_night_trading_time(df: pd.DataFrame):\n", "    \"\"\"恢复夜盘时间顺序\"\"\"\n", "    if \"datetime\" not in df.columns:\n", "        raise Exception(\"datetime column not exists\")\n", "    # 分成两个dataframe\n", "    df1 = df.loc[df['datetime'].dt.hour < 20]\n", "    df2 = df.loc[df['datetime'].dt.hour >= 20] # 夜盘时间\n", "    # 进一步分批处理\n", "    df2_1 = df2.loc[(df2['datetime'].dt.weekday <= 4) & (df2['datetime'].dt.weekday > 0)]\n", "    df2_2 = df2.loc[df2['datetime'].dt.weekday == 0]\n", "    del df2\n", "    #如果datetime是星期二到星期五，且时间在21:00到24:00之间，那么datetime减一天\n", "    df2_1['datetime'] = df2_1['datetime'] - pd.<PERSON><PERSON><PERSON>(days=1)\n", "    #如果datetime是星期一，且时间在21:00到24:00之间，那么datetime减三天\n", "    df2_2['datetime'] = df2_2['datetime'] - pd.<PERSON><PERSON><PERSON>(days=3)\n", "    dfs = pd.concat([df1, df2_1, df2_2])\n", "    dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "    dfs.reset_index(drop=True, inplace=True)\n", "    return dfs\n", "\n", "def read_csv_file(fname, code, market):\n", "    # columns=[\n", "    #     \"交易日\",\"合约代码\",\"交易所代码\",\"合约在交易所的代码\",\n", "    #     \"最新价\",\"上次结算价\",\"昨收盘\",\"昨持仓量\",\"今开盘\",\"最高价\",\n", "    #     \"最低价\",\"数量\",\"成交金额\",\"持仓量\",\"今收盘\",\"本次结算价\",\"涨停板价\",\n", "    #     \"跌停板价\",\"昨虚实度\",\"今虚实度\",\"最后修改时间\",\"最后修改毫秒\",\n", "    #     \"申买价一\",\"申买量一\",\"申卖价一\",\"申卖量一\",\"申买价二\",\"申买量二\",\"申卖价二\",\"申卖量二\",\n", "    #     \"申买价三\",\"申买量三\",\"申卖价三\",\"申卖量三\",\"申买价四\",\"申买量四\",\"申卖价四\",\"申卖量四\",\n", "    #     \"申买价五\",\"申买量五\",\"申卖价五\",\"申卖量五\",\"当日均价\",\"业务日期\"\n", "    # ]\n", "    #read csv file \n", "    df = pd.read_csv(fname, encoding='gbk', header='infer')\n", "    df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]\n", "    df = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'}).reset_index()\n", "    df['交易时间'] = df.apply(combine_datetime, axis=1)\n", "    df = df[['合约代码', '交易时间', '最新价', '数量']]\n", "    df = df.rename(columns={'合约代码': 'code', '交易时间': 'datetime', '最新价': 'price', '数量':'volume'})\n", "    df = df.loc[df['volume']>0]\n", "    df['code'] = f'{code}9999.{market}'\n", "    return df\n", "\n", "def test_signle_code():\n", "    # 加载主力日期切换表\n", "    # print(maintb)\n", "    # 起止日期间所有日期列表\n", "    start = '20230101'\n", "    end = '20230131'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td < td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = 'f:/hqdata/raw/2023' \n", "    # 有交易数据的日期列表\n", "    market = 'ZC' # 'SC', 'DC'\n", "    code='SA' # SA\n", "    dfs = pd.DataFrame()\n", "    for day in all_day:\n", "        # ag主力连续_20230213\n", "        fname = f'{directory}/{code}主力连续_{day}.csv'\n", "        if not is_exist_file(fname):\n", "            continue\n", "        print(f'read csv file: {fname}')\n", "        df = read_csv_file(fname)\n", "        dfs = pd.concat([dfs, df])\n", "        print(dfs.shape)\n", "    # 将按年汇总的tick数据写入文件\n", "    # print(f'{code}: {dfs.shape}')\n", "    # dfs.to_parquet(f'e:/hqdata/tick/{code}2020.parquet')\n", "    print(dfs.tail())"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def etl_fut_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):\n", "    # 起止日期间所有日期列表\n", "    # start = '20230201'\n", "    # end = '20230231'\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td <= td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = f'{data_path}/raw/{start[0:4]}' \n", "    cnt = 0\n", "    markets=['SC', 'ZC', 'DC'] # \n", "    dfs = pd.DataFrame()\n", "    for mk, codes in MAIN_FUT_MARKET_CODES.items():\n", "        for code in codes:\n", "            for day in all_day:\n", "                # 获取当日主力期货的代码名即文件名\n", "                if is_sec:\n", "                    fname = f'{directory}/{code}次主力连续_{day}.csv'\n", "                else:\n", "                    fname = f'{directory}/{code}主力连续_{day}.csv'\n", "                if not is_exist_file(fname):\n", "                    continue\n", "                df = read_csv_file(fname, code, mk)\n", "                dfs = pd.concat([dfs, df])\n", "            # 将按年汇总的tick数据写入文件\n", "            print(f'{cnt} {code}: {dfs.shape}')\n", "            cnt = cnt + 1\n", "    # dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    dfs = restore_order_of_night_trading_time(dfs) # 恢复夜盘时间顺序\n", "    if is_sec:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.sec.parquet')\n", "    else:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.parquet')\n", "    return dfs\n", "\n", "def etl_sf_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):\n", "    td=datetime.strptime(start, '%Y%m%d').date()\n", "    td_end=datetime.strptime(end, '%Y%m%d').date()\n", "    all_day = []\n", "    while td <= td_end:\n", "        td = date.fromordinal(td.toordinal() + 1)\n", "        all_day.append(td.strftime(\"%Y%m%d\"))\n", "    print(f'all day: {len(all_day)}')\n", "    # Define directory to list from\n", "    directory = f'{data_path}/raw/{start[0:4]}sf' \n", "    cnt = 0\n", "    dfs = pd.DataFrame()\n", "    for code in SF_FUT_CODES:\n", "        for day in all_day:\n", "            # 获取当日主力期货的代码名即文件名\n", "            if is_sec:\n", "                fname = f'{directory}/{code}次主力连续_{day}.csv'\n", "            else:\n", "                fname = f'{directory}/{code}主力连续_{day}.csv'\n", "            if not is_exist_file(fname):\n", "                continue\n", "            df = read_csv_file(fname, code, 'SF')\n", "            dfs = pd.concat([dfs, df])\n", "        # 将按年汇总的tick数据写入文件\n", "        print(f'{cnt} {code}: {dfs.shape}')\n", "        cnt = cnt + 1\n", "    dfs.sort_values(by=['datetime'], ascending=True, inplace=True)\n", "    if is_sec:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.sec.parquet')\n", "    else:\n", "        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.parquet')\n", "    return dfs\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all day: 30\n", "0 CU: (353390, 4)\n", "1 AL: (671668, 4)\n", "2 ZN: (1031346, 4)\n", "3 PB: (1279533, 4)\n", "4 NI: (1773741, 4)\n", "5 SN: (2276311, 4)\n", "6 AU: (2862387, 4)\n", "7 AG: (3393339, 4)\n", "8 RB: (3780048, 4)\n", "9 HC: (4071899, 4)\n", "10 BU: (4457535, 4)\n", "11 RU: (4737166, 4)\n", "12 FU: (5122238, 4)\n", "13 SP: (5515363, 4)\n", "14 WR: (5517323, 4)\n", "15 SC: (6109555, 4)\n", "16 NR: (6486636, 4)\n", "17 SS: (6771963, 4)\n", "18 SR: (7072325, 4)\n", "19 CF: (7315908, 4)\n", "20 ZC: (7315908, 4)\n", "21 FG: (7557280, 4)\n", "22 TA: (7893231, 4)\n", "23 MA: (8236010, 4)\n", "24 OI: (8588919, 4)\n", "25 RM: (8888577, 4)\n", "26 CY: (8907835, 4)\n", "27 WH: (8907835, 4)\n", "28 PM: (8907835, 4)\n", "29 RI: (8907835, 4)\n", "30 LR: (8907835, 4)\n", "31 JR: (8907835, 4)\n", "32 RS: (8908209, 4)\n", "33 SF: (9102388, 4)\n", "34 SM: (9273380, 4)\n", "35 AP: (9377119, 4)\n", "36 CJ: (9461092, 4)\n", "37 UR: (9638778, 4)\n", "38 SA: (9974213, 4)\n", "39 PF: (10227534, 4)\n", "40 PK: (10345561, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["41 M: (10651191, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["42 Y: (10914845, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["43 A: (11148970, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["44 B: (11395062, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["45 P: (11681827, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["46 J: (11880795, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["47 JM: (12069316, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["48 I: (12335033, 4)\n", "49 RR: (12378235, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["50 C: (12657600, 4)\n", "51 CS: (12818261, 4)\n", "52 JD: (12945949, 4)\n", "53 BB: (12945949, 4)\n", "54 FB: (12948355, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["55 L: (13266803, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["56 V: (13612446, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["57 PP: (13935001, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["58 EG: (14252692, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["59 EB: (14606778, 4)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\1813990073.py:63: DtypeWarning: Columns (3) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(fname, encoding='gbk', header='infer')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["60 PG: (14922607, 4)\n", "61 LH: (15039151, 4)\n", "(15039151, 4)\n", "all day: 30\n", "0 IH: (193589, 4)\n", "1 IF: (340409, 4)\n", "2 IC: (511332, 4)\n", "3 IM: (694587, 4)\n", "(694587, 4)\n"]}], "source": ["# dt_seg=[('20230101', '20230131'), ('20230201', '20230228'), ('20230301', '20230331')]\n", "dt_seg=[('20230901', '20230930')] # 按月份分段导出\n", "for seg in dt_seg:\n", "    second = True # 是否导出次主力\n", "    df = etl_fut_tick_data(start=seg[0], end=seg[1], data_path=data_path, is_sec=second)\n", "    print(df.shape)\n", "    df = etl_sf_tick_data(start=seg[0], end=seg[1], data_path=data_path, is_sec=second)\n", "    print(df.shape)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["del df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Clearn"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from datetime import datetime\n", "codes = {\n", "    'SC': ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG', 'RB', 'HC', 'BU', 'RU', 'FU', 'SP', 'WR', 'SC', 'NR', 'SS'],\n", "    'ZC': ['SR', '<PERSON>', 'Z<PERSON>', 'F<PERSON>', 'T<PERSON>', 'MA', 'O<PERSON>', 'R<PERSON>', 'C<PERSON>', 'WH', 'PM', 'R<PERSON>', 'L<PERSON>', '<PERSON>', 'RS', 'SF', 'SM', 'AP', 'CJ', 'UR', 'SA', 'PF', 'PK'], \n", "    'DC': ['M', 'Y', 'A', 'B', 'P', 'J', 'J<PERSON>', 'I', 'RR', 'C', 'CS', 'JD', 'BB', 'FB', 'L', 'V', 'PP', 'EG', 'EB', 'PG', 'LH']\n", "}\n", "sfcodes = ['IH', 'IF', 'IC', 'IM']\n", "data_path = 'f:/hqdata/tick'"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def combine_datetime(row):\n", "    return datetime.strptime(f\"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}\", \"%Y%m%d %H:%M:%S %f\")\n", "    # return datetime.strptime(f\"{row['TradingDay']} {row['UpdateTime']} {row['UpdateMillisec']}\", \"%Y%m%d %H:%M:%S %f\")\n", "\n", "def load_clearn_tick_data(code, start, end):\n", "    # 读取tick数据parquet文件\n", "    # filename = f'{self.data_path}/{start//10000}/sf/{code}{start//10000}.parquet'\n", "    filename = f'{data_path}/{start//10000}/{code}{start//10000}.parquet'\n", "    dt0 = datetime.strptime(str(start), '%Y%m%d')\n", "    dt1 = datetime.strptime(str(end), '%Y%m%d')\n", "    if os.path.isfile(filename):\n", "        df = pd.read_parquet(filename)\n", "        if start > 20230000:\n", "            # df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]\n", "            # df[['最新价', '数量']] = df[['最新价', '数量']].astype(float)\n", "            grouped = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'})\n", "            df = grouped.reset_index()\n", "            df['datetime'] = df.apply(combine_datetime, axis=1)\n", "            df = df[['合约代码', 'datetime', '最新价', '数量']]\n", "            df.to_parquet(filename, engine='fastparquet')\n", "            df = df.loc[(df['datetime'] >= dt0) & (df['datetime'] <= dt1)]\n", "        return df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "def load_all_combine_tick_data(start, end):\n", "    dfs = pd.DataFrame()\n", "    for mkt, codess in codes.items():\n", "        for code in codess:\n", "            df = load_clearn_tick_data(code, start, end)\n", "            if df.shape[0] > 0:\n", "                print(code, df.shape)\n", "                # df['label'] = f'{code}9999.{mkt}'\n", "                # dfs = pd.concat([dfs, df])\n", "    # if dfs.shape[0] > 0:\n", "    #     # dfs['datetime'] = dfs.apply(self.combine_datetime, axis=1)\n", "    #     dfs = dfs.sort_values(by='datetime', ascending=True)\n", "    return dfs"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CU (680899, 4)\n", "AL (679759, 4)\n", "ZN (672462, 4)\n", "PB (411998, 4)\n", "NI (822463, 4)\n", "SN (826244, 4)\n", "AU (818763, 4)\n", "AG (819969, 4)\n", "RB (709174, 4)\n", "HC (690167, 4)\n", "BU (698748, 4)\n", "RU (693135, 4)\n", "FU (709034, 4)\n", "SP (646093, 4)\n", "WR (72531, 4)\n", "SC (775300, 4)\n", "NR (608981, 4)\n", "SS (615511, 4)\n", "SR (583868, 4)\n", "CF (664177, 4)\n", "ZC (1, 4)\n", "FG (701675, 4)\n", "TA (704306, 4)\n", "MA (705622, 4)\n", "OI (692186, 4)\n", "RM (681946, 4)\n", "CY (149830, 4)\n", "WH (1066, 4)\n", "JR (1, 4)\n", "RS (6557, 4)\n", "SF (417644, 4)\n", "SM (390364, 4)\n", "AP (417980, 4)\n", "CJ (201773, 4)\n", "UR (417344, 4)\n", "SA (704999, 4)\n", "PF (588404, 4)\n", "PK (351991, 4)\n", "M (680033, 4)\n", "Y (687724, 4)\n", "B (431890, 4)\n", "P (702260, 4)\n", "J (426905, 4)\n", "JM (532149, 4)\n", "RR (89656, 4)\n", "C (562226, 4)\n", "CS (502043, 4)\n", "JD (326201, 4)\n", "FB (41106, 4)\n", "L (662545, 4)\n", "V (705831, 4)\n", "PP (674742, 4)\n", "EG (665628, 4)\n", "EB (613396, 4)\n", "PG (586192, 4)\n", "LH (281257, 4)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["load_all_combine_tick_data(start=20230101, end=20230228)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2023-01-03 09:29:00.200</td>\n", "      <td>2635.0</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2023-01-03 09:29:00.200</td>\n", "      <td>5854.6</td>\n", "      <td>84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2023-01-03 09:29:00.200</td>\n", "      <td>3861.0</td>\n", "      <td>92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2023-01-03 09:29:00.200</td>\n", "      <td>6280.0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2023-01-03 09:30:00.700</td>\n", "      <td>5857.0</td>\n", "      <td>132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13865</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2023-02-01 15:27:20.700</td>\n", "      <td>2827.8</td>\n", "      <td>43409</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13866</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2023-02-01 15:32:25.700</td>\n", "      <td>2827.8</td>\n", "      <td>43409</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13676</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2023-02-01 15:32:25.700</td>\n", "      <td>6361.0</td>\n", "      <td>39323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14039</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2023-02-01 15:32:25.700</td>\n", "      <td>4198.0</td>\n", "      <td>61669</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13456</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2023-02-01 15:32:25.700</td>\n", "      <td>6922.6</td>\n", "      <td>26743</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>923516 rows × 4 columns</p>\n", "</div>"], "text/plain": ["            code                datetime   price  volume\n", "index                                                   \n", "0      IH9999.SF 2023-01-03 09:29:00.200  2635.0      68\n", "0      IC9999.SF 2023-01-03 09:29:00.200  5854.6      84\n", "0      IF9999.SF 2023-01-03 09:29:00.200  3861.0      92\n", "0      IM9999.SF 2023-01-03 09:29:00.200  6280.0      32\n", "1      IC9999.SF 2023-01-03 09:30:00.700  5857.0     132\n", "...          ...                     ...     ...     ...\n", "13865  IH9999.SF 2023-02-01 15:27:20.700  2827.8   43409\n", "13866  IH9999.SF 2023-02-01 15:32:25.700  2827.8   43409\n", "13676  IC9999.SF 2023-02-01 15:32:25.700  6361.0   39323\n", "14039  IF9999.SF 2023-02-01 15:32:25.700  4198.0   61669\n", "13456  IM9999.SF 2023-02-01 15:32:25.700  6922.6   26743\n", "\n", "[923516 rows x 4 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet(f'{data_path}/tick/2023/SF202301.parquet')\n", "df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Test"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyqlab.scripts.SimulationServer import SimulationSvr"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ss = SimulationSvr(ip=\"\", port=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = ss.load_tick_data('A', 20230101, 20230228)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        合约代码       交易日    最后修改时间 最后修改毫秒        最新价    数量       申买价一 申买量一  \\\n", "index                                                                      \n", "2      a2305  20230103  09:00:00    428  5181.0000  1054  5181.0000   18   \n", "3      a2305  20230103  09:00:00    943  5183.0000  1166  5183.0000    6   \n", "4      a2305  20230103  09:00:01    431  5190.0000  1216  5186.0000   10   \n", "5      a2305  20230103  09:00:01    921  5191.0000  1341  5192.0000    6   \n", "6      a2305  20230103  09:00:02    439  5199.0000  1495  5194.0000    2   \n", "\n", "            申卖价一 申卖量一  \n", "index                  \n", "2      5183.0000   39  \n", "3      5186.0000    1  \n", "4      5192.0000   60  \n", "5      5193.0000    4  \n", "6      5198.0000    1  \n"]}], "source": ["print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}