# CANDLESTICK_FEATURES 向量化方法

## 概述

我们为 `pyqlab/models/gpt2/vq_tokenizer.py` 添加了一个新的向量化方法 `CANDLESTICK_FEATURES`，该方法基于K线的技术分析特征来进行向量化，更直观地反映K线的形态特征。

## 新增功能

### 1. 新的向量化方法常量

在 `VectorizationMethod` 类中添加了：
```python
CANDLESTICK_FEATURES = "candlestick_features"  # 基于K线特征的向量化
```

### 2. 核心计算公式

该方法使用以下公式计算K线特征：

#### 5维向量（包含成交量）：
- **实体 (body)**: `(Close - Open) / Open`
- **上影线 (upper_shadow)**: `(High - max(Open, Close)) / Open`
- **下影线 (lower_shadow)**: `(min(Open, Close) - Low) / Open`
- **成交量比率 (volume_ratio)**: `Volume / MovingAverage(Volume, 20)`
- **预留维度**: 设为 0.0，可用于未来扩展

#### 4维向量（不包含成交量）：
- **实体 (body)**: `(Close - Open) / Open`
- **上影线 (upper_shadow)**: `(High - max(Open, Close)) / Open`
- **下影线 (lower_shadow)**: `(min(Open, Close) - Low) / Open`
- **价格变化 (price_change)**: `(Close - Prev_Close) / Prev_Close`

### 3. 特征含义

- **实体 (body)**：
  - 正值表示阳线（收盘价 > 开盘价）
  - 负值表示阴线（收盘价 < 开盘价）
  - 接近0表示十字星形态

- **上影线 (upper_shadow)**：
  - 反映向上的价格压力
  - 值越大表示上方阻力越强

- **下影线 (lower_shadow)**：
  - 反映向下的价格支撑
  - 值越大表示下方支撑越强

- **成交量比率 (volume_ratio)**：
  - 反映成交活跃程度
  - 大于1表示成交量高于平均水平

- **价格变化 (price_change)**：
  - 反映相对于前一交易日的整体变化

### 4. 数值范围限制

为确保数值稳定性，对各特征值进行了合理的范围限制：

- **实体**: [-1.0, 1.0] （涨跌100%）
- **上影线**: [0.0, 1.0] （影线长度相对于开盘价）
- **下影线**: [0.0, 1.0] （影线长度相对于开盘价）
- **成交量比率**: [0.0, 10.0] （成交量倍数）
- **价格变化**: [-0.2, 0.2] （涨跌20%）

## 使用方法

### 基本用法

```python
from pyqlab.models.gpt2.vq_tokenizer import VectorizationMethod, CandlestickVQTokenizer

# 创建使用新向量化方法的tokenizer
tokenizer = CandlestickVQTokenizer(
    vectorization_method=VectorizationMethod.CANDLESTICK_FEATURES,
    embedding_dim=5,  # 或 4
    # 其他参数...
)

# 编码K线数据
token_ids = tokenizer.encode(df_ohlcv)

# 解码回K线数据
reconstructed_df = tokenizer.tokens_to_candlesticks(token_ids, df_ohlcv)
```

### 命令行使用

```bash
# 测试新的向量化方法
python pyqlab/models/gpt2/vq_tokenizer.py --method candlestick_features

# 测试所有方法（包括新方法）
python pyqlab/models/gpt2/vq_tokenizer.py --method all
```

## 优势

1. **直观性**：基于传统技术分析的K线特征，更容易理解和解释
2. **标准化**：使用相对比例而非绝对价格，适用于不同价格水平的证券
3. **完整性**：涵盖了K线的主要形态特征（实体、影线、成交量）
4. **稳定性**：通过合理的数值范围限制确保计算稳定

## 测试验证

我们提供了完整的测试套件：

1. **基础功能测试** (`test_candlestick_features.py`)：
   - 验证计算公式的正确性
   - 测试边界情况处理
   - 验证不同K线形态的特征提取

2. **实际应用示例** (`example_candlestick_features.py`)：
   - 完整的编码-解码流程演示
   - 数据质量分析
   - K线特征分析

## 技术实现

### 主要修改文件

- `pyqlab/models/gpt2/vq_tokenizer.py`：主要实现文件

### 关键函数修改

1. **candlestick_to_vector()**: 添加了 CANDLESTICK_FEATURES 方法的处理逻辑
2. **vector_to_candlestick()**: 添加了相应的逆变换逻辑
3. **get_quantized_vector()**: 添加了规范化支持
4. **命令行参数**: 更新了可选的向量化方法列表

### 错误处理

- 开盘价为0时返回零向量（避免除零错误）
- 成交量移动平均为0时返回零向量（5维情况）
- 所有计算结果都经过NaN和无穷大值处理

## 与现有方法的比较

| 方法 | 特点 | 适用场景 |
|------|------|----------|
| ATR_BASED | 基于ATR归一化，考虑波动性 | 需要考虑市场波动性的场景 |
| PERCENT_CHANGE | 基于百分比变化 | 通用的相对变化分析 |
| CANDLESTICK_FEATURES | 基于K线技术分析特征 | 技术分析、形态识别 |

## 未来扩展

预留的第5维度可以用于添加更多技术指标，如：
- RSI相对强弱指标
- MACD指标
- 布林带位置
- 其他自定义技术指标

## 注意事项

1. 该方法需要足够的历史数据来计算移动平均成交量
2. 对于成交量数据缺失的情况，建议使用4维向量
3. 在实际应用中，建议根据具体的证券类型调整数值范围限制
