"""
测试带有证券代码维度的码本模型
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.code_aware_codebook import CodeAwareEncoder, CodeAwareDecoder


def load_test_data(data_dir, market='fut', code='RB9999.SC', period='min1'):
    """加载测试数据"""
    data_path = os.path.join(data_dir, market, period, f"{code}.parquet")
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"找不到测试数据: {data_path}")
    
    df = pd.read_parquet(data_path)
    print(f"加载测试数据: {data_path}, 形状: {df.shape}")
    return df


def test_code_aware_tokenizer(codebook_weights_path, data_dir, market='fut', codes=None, period='min1'):
    """测试带有证券代码维度的码本模型"""
    if codes is None:
        codes = ['RB9999.SC', 'IF9999.SC', 'AU9999.SC']
    
    # 创建带有证券代码维度的tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=codebook_weights_path,
        num_embeddings=1024,
        embedding_dim=5,
        atr_period=14,
        ma_volume_period=20,
        vectorization_method=VectorizationMethod.ATR_BASED,
        use_code_dim=True,
        code_size=100,
        code_dim=16
    )
    
    # 创建不使用证券代码维度的tokenizer作为对照
    tokenizer_no_code = CandlestickVQTokenizer(
        codebook_weights_path=codebook_weights_path,
        num_embeddings=1024,
        embedding_dim=5,
        atr_period=14,
        ma_volume_period=20,
        vectorization_method=VectorizationMethod.ATR_BASED,
        use_code_dim=False
    )
    
    # 加载测试数据
    dfs = {}
    for code in codes:
        try:
            dfs[code] = load_test_data(data_dir, market, code, period)
        except FileNotFoundError as e:
            print(f"警告: {e}")
    
    if not dfs:
        print("错误: 没有找到任何测试数据")
        return
    
    # 测试tokenize和重建
    for code, df in dfs.items():
        print(f"\n测试证券代码: {code}")
        
        # 使用带有证券代码维度的tokenizer
        print("\n使用带有证券代码维度的tokenizer:")
        token_ids = tokenizer.encode(df.iloc[:200], code=code)
        print(f"生成的token ID数量: {len(token_ids)}")
        print(f"前10个token ID: {token_ids[:10]}")
        
        # 使用不带证券代码维度的tokenizer
        print("\n使用不带证券代码维度的tokenizer:")
        token_ids_no_code = tokenizer_no_code.encode(df.iloc[:200])
        print(f"生成的token ID数量: {len(token_ids_no_code)}")
        print(f"前10个token ID: {token_ids_no_code[:10]}")
        
        # 比较两种tokenizer的结果
        same_count = sum(1 for a, b in zip(token_ids, token_ids_no_code) if a == b)
        total_count = min(len(token_ids), len(token_ids_no_code))
        diff_ratio = 1 - same_count / total_count if total_count > 0 else 0
        print(f"\n两种tokenizer的结果差异率: {diff_ratio:.2%}")
        
        # 可视化比较
        if len(token_ids) > 0 and len(token_ids_no_code) > 0:
            plt.figure(figsize=(12, 6))
            plt.subplot(2, 1, 1)
            plt.plot(token_ids[:100], label='带证券代码维度')
            plt.title(f"{code} - 带证券代码维度的token ID")
            plt.legend()
            
            plt.subplot(2, 1, 2)
            plt.plot(token_ids_no_code[:100], label='不带证券代码维度', color='orange')
            plt.title(f"{code} - 不带证券代码维度的token ID")
            plt.legend()
            
            plt.tight_layout()
            plt.savefig(f"token_comparison_{code}.png")
            plt.close()
            print(f"保存比较图: token_comparison_{code}.png")


def test_code_aware_encoder_decoder(codebook_weights_path, output_dir):
    """测试带有证券代码维度的编码器和解码器"""
    # 加载码本权重
    codebook_weights = torch.load(codebook_weights_path)
    print(f"加载码本权重: {codebook_weights_path}, 形状: {codebook_weights.shape}")
    
    # 创建带有证券代码维度的编码器和解码器
    encoder = CodeAwareEncoder(
        codebook_weights=codebook_weights,
        code_size=100,
        code_dim=5
    )
    
    decoder = CodeAwareDecoder(
        codebook_weights=codebook_weights,
        num_embeddings=codebook_weights.shape[0],
        embedding_dim=codebook_weights.shape[1],
        code_size=100,
        code_dim=5
    )
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 导出编码器和解码器为ONNX格式
    encoder_onnx_path = os.path.join(output_dir, "code_aware_encoder.onnx")
    decoder_onnx_path = os.path.join(output_dir, "code_aware_decoder.onnx")
    
    encoder_success = encoder.to_onnx(encoder_onnx_path)
    decoder_success = decoder.to_onnx(decoder_onnx_path)
    
    if encoder_success and decoder_success:
        print(f"成功导出带有证券代码维度的编码器和解码器为ONNX格式:")
        print(f"编码器: {encoder_onnx_path}")
        print(f"解码器: {decoder_onnx_path}")
    else:
        print("导出带有证券代码维度的编码器和解码器为ONNX格式失败")
    
    # 测试编码和解码
    test_vector = torch.randn(5)
    test_code_id = torch.tensor(42)
    
    print(f"\n测试向量: {test_vector}")
    print(f"测试证券代码ID: {test_code_id}")
    
    # 编码
    with torch.no_grad():
        token_id = encoder(test_vector, test_code_id)
        print(f"编码结果 (token ID): {token_id.item()}")
        
        # 解码
        decoded_vector = decoder(token_id, test_code_id)
        print(f"解码结果 (向量): {decoded_vector.squeeze().numpy()}")
        
        # 计算重建误差
        mse = torch.mean((test_vector - decoded_vector.squeeze())**2)
        print(f"重建误差 (MSE): {mse.item()}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="测试带有证券代码维度的码本模型")
    parser.add_argument("--codebook_weights_path", type=str, required=True, help="码本权重文件路径")
    parser.add_argument("--data_dir", type=str, default="f:/hqdata/tsdb", help="数据目录")
    parser.add_argument("--market", type=str, default="fut", help="市场类型")
    parser.add_argument("--codes", type=str, nargs="+", default=["RB9999.SC", "IF9999.SC", "AU9999.SC"], help="证券代码列表")
    parser.add_argument("--period", type=str, default="min1", help="周期")
    parser.add_argument("--output_dir", type=str, default="models", help="输出目录")
    parser.add_argument("--test_tokenizer", action="store_true", help="测试tokenizer")
    parser.add_argument("--test_encoder_decoder", action="store_true", help="测试编码器和解码器")
    
    args = parser.parse_args()
    
    if args.test_tokenizer:
        test_code_aware_tokenizer(
            args.codebook_weights_path,
            args.data_dir,
            args.market,
            args.codes,
            args.period
        )
    
    if args.test_encoder_decoder:
        test_code_aware_encoder_decoder(
            args.codebook_weights_path,
            args.output_dir
        )
    
    if not args.test_tokenizer and not args.test_encoder_decoder:
        print("请指定至少一个测试选项: --test_tokenizer 或 --test_encoder_decoder")
