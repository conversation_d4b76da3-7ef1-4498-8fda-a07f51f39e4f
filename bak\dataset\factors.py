# ## 数据预处理
# 特征向量的构建是机器学习的关键步骤

from datetime import datetime
import json
import pandas as pd

# ### AICM系统中的因子数据作为机器学习的主要特征向量数据
# - 市场数据
# - 技术指标数据
# - 财务数据
# - 订单上下文数据

# AICM中的一些常量定义
from pyqlab.const import FACOTR_NUM, ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SELECT_FACTOR_NAMES, SELECT_SNAPSHOT_CONTEXT, SNAPSHOT_CONTEXT
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES


class AicmFactorsData():

    def __init__(self, direct, data_path,
            sel_lf_names=None, sel_sf_names=None, sel_ct_names=None,
            only_trading_instrument=True, drop_zero=True
        ) -> None:
        self.direct=direct
        self.data_path=data_path
        self.drop_zero=drop_zero
        self.only_trading_instrument=only_trading_instrument
        self.portfs=[]
        self.last_widx=None
        self.lb_dict={}
        self.lf_dict={}
        self.sf_dict={}
        self.mf_dict={}
        self.ct_dict={}
        self.sel_lf_names=sel_lf_names
        self.sel_sf_names=sel_sf_names
        self.sel_ct_names=sel_ct_names
        if self.sel_lf_names is None:
            self.sel_lf_names = SELECT_FACTOR_NAMES[0]
        if self.sel_sf_names is None:
            self.sel_sf_names = SELECT_FACTOR_NAMES[1]
        if self.sel_lf_names is None:
            self.sel_lf_names = SELECT_SNAPSHOT_CONTEXT

    def long_factor_select(self, n):
        if len(self.sel_lf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_lf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def short_factor_select(self, n):
        if len(self.sel_sf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_sf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def context_select(self, n):
        if len(self.sel_ct_names) == 0:
            return 0
        if SNAPSHOT_CONTEXT[n] in self.sel_ct_names:
            return 1
        return 0


    def dump_using_factor_json(self):
        f_sel = {}
        f_sel['slow'] = [self.long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [self.short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [self.context_select(n) for n in range(len(SNAPSHOT_CONTEXT))]

        with open(f'{self.data_path}/using_factor.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)

    def get_factor_cols(self, factor_type):

        col_names = ['ord_id']
        if factor_type == "lf":
            for name in self.sel_lf_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append("{}_1".format(name))
                    col_names.append("{}_2".format(name))
                else:
                    col_names.append("%s_2" % name)

        if factor_type == "sf":
            for name in self.sel_sf_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append("{}_1".format(name))
                    col_names.append("{}_2".format(name))
                else:
                    col_names.append("%s_2" % name)

        if factor_type == "ct":
            col_names.extend(self.sel_ct_names)

        return col_names

    def _load(self, pf):
        try:
            # read factor data
            lb_df=pd.read_csv(f"{self.data_path}/ft_lb.{self.direct}.all.{pf}.csv")
            lf_df=pd.read_csv(f"{self.data_path}/ft_lf.{self.direct}.all.{pf}.csv")
            sf_df=pd.read_csv(f"{self.data_path}/ft_sf.{self.direct}.all.{pf}.csv")
            ct_df=pd.read_csv(f"{self.data_path}/ft_ct.{self.direct}.all.{pf}.csv")

            if self.drop_zero and 'SLOW_QH_LR_SLOPE' in self.sel_ct_names:
                ct_df = ct_df[ct_df.SLOW_QH_LR_SLOPE>0.0]
                lb_df = lb_df[lb_df['ord_id'].isin(ct_df['ord_id'])]
                ct_df = ct_df[ct_df['ord_id'].isin(lb_df['ord_id'])]
                lf_df = lf_df[lf_df['ord_id'].isin(lb_df['ord_id'])]
                sf_df = sf_df[sf_df['ord_id'].isin(lb_df['ord_id'])]
            # one-hot encode
            '''
            if self.is_onehot:
                data = data.join(pd.get_dummies(data.CODE))
                self.fut_codes = data.columns.to_list()[-1*len(data.CODE.unique()):]
                lb = data.pop('label')
                data.insert(loc=data.shape[1], column='label', value=lb)
            data.drop(columns=['CODE'], inplace=True)
            '''

            # select factors columns
            # lf_df=lf_df[self.get_factor_cols("lf")]
            # sf_df=sf_df[self.get_factor_cols("sf")]
            # ct_df=ct_df[self.get_factor_cols("ct")]
            
            self.lb_dict[pf]=lb_df
            self.lf_dict[pf]=lf_df
            self.sf_dict[pf]=sf_df
            self.ct_dict[pf]=ct_df

            self.dump_using_factor_json()
        except IOError as e:
            print(e)
            # raise ValueError(e)
        except Exception as e:
            print (e)

    def calc_diff_lf_cols(self, df):
        for name in self.sel_lf_names:
            if f"{name}_1" in df.columns.values:
                df[f"{name}_1"] = df[f"{name}_2"] - df[f"{name}_1"]
        return df

    def calc_diff_sf_cols(self, df):
        for name in self.sel_sf_names:
            if f"{name}_1" in df.columns.values:
                df[f"{name}_1"] = df[f"{name}_2"] - df[f"{name}_1"]
        return df

    def get_pf_data(self, pfs):
        lb_df=pd.DataFrame()
        lf_df=pd.DataFrame()
        sf_df=pd.DataFrame()
        ct_df=pd.DataFrame()

        for pf in pfs:
            if pf not in self.lb_dict.keys():
                self._load(pf)

            if pf in self.lb_dict.keys():
                if len(lb_df)==0:
                    lb_df = self.lb_dict[pf]
                    lf_df = self.lf_dict[pf]
                    sf_df = self.sf_dict[pf]
                    ct_df = self.ct_dict[pf]
                else:
                    lb_df = lb_df.append(self.lb_dict[pf])
                    lf_df = lf_df.append(self.lf_dict[pf])
                    sf_df = sf_df.append(self.sf_dict[pf])
                    ct_df = ct_df.append(self.ct_dict[pf])

        ''' 
        lf_df = self.calc_diff_lf_cols(lf_df)
        sf_df = self.calc_diff_sf_cols(sf_df)
        if 'FAST_QH_LR_SLOPE_PREV' in self.sel_ct_names:
            ct_df['FAST_QH_LR_SLOPE_PREV'] = ct_df['FAST_QH_LR_SLOPE'] - ct_df['FAST_QH_LR_SLOPE_PREV']

        if 'SLOW_QH_LR_SLOPE_PREV' in self.sel_ct_names:
            ct_df['SLOW_QH_LR_SLOPE_PREV'] = ct_df['SLOW_QH_LR_SLOPE'] - ct_df['SLOW_QH_LR_SLOPE_PREV']
        '''

        return lb_df, lf_df, sf_df, ct_df        


