
from pprint import pprint
from time import time
from datetime import datetime
from pyqlab.data.dataset import <PERSON><PERSON><PERSON><PERSON>
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES
from pyqlab.utils import init_instance_by_config
from sklearn.model_selection import train_test_split
from sklearn.model_selection import KFold

SEL_LONG_FACTOR_NAMES = [ # Slow period factor
    # "VOLUME", # 在RangeBar下，Volume是Bar的时长seconds
    "NEW_CHANGE_PERCENT",

    # "AROON_UP", "AROON_DOWN",
    "MACD", "MACD_DIFF", "MACD_DEA", "RSI",
    "SQUEEZE_NARROW_BARS", "SQUEEZE_ZERO_BARS",

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]

SEL_SHORT_FACTOR_NAMES = [ # Fast period factor
    # "VOLUME", # 在RangeBar下，Volume是Bar的时长seconds
    "NEW_CHANGE_PERCENT",

    # "AROON_UP", "AROON_DOWN",
    "MACD", "MACD_DIFF", "MACD_DEA", "RSI",
    "SQUEEZE_NARROW_BARS", "SQUEEZE_ZERO_BARS",

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]
SEL_MAIN_FACTOR_NAMES = SEL_SHORT_FACTOR_NAMES
SEL_FAST_CONTEXT_FACTOR_NAMES = [
  "STDDEV_RNG", "SHORT_RANGE",
  "FAST_RSI", "FAST_RSI_ZSCORE", "FAST_RSI_STDDEV",
  "FAST_NATR", "FAST_NATR_ZSCORE", "FAST_NATR_STDDEV",
  "FAST_FLRS", "FAST_FLRS_ZSCORE", "FAST_FLRS_STDDEV",
  "FAST_MLRS", "FAST_MLRS_ZSCORE", "FAST_MLRS_STDDEV",
  "FAST_MOM", "FAST_MOM_ZSCORE", "FAST_MOM_STDDEV",
  "DAYOFWEEK", "HOUR"
]
SEL_SLOW_CONTEXT_FACTOR_NAMES = [
  "STDDEV_RNG", "LONG_RANGE",
  "SLOW_RSI", "SLOW_RSI_ZSCORE", "SLOW_RSI_STDDEV",
  "SLOW_NATR", "SLOW_NATR_ZSCORE", "SLOW_NATR_STDDEV",
  "SLOW_FLRS", "SLOW_FLRS_ZSCORE", "SLOW_FLRS_STDDEV",
  "SLOW_MLRS", "SLOW_MLRS_ZSCORE", "SLOW_MLRS_STDDEV",
  "SLOW_MOM", "SLOW_MOM_ZSCORE", "SLOW_MOM_STDDEV",
  "DAYOFWEEK", "HOUR"
]

# FUT_CODES=MAIN_FUT_CODES
FUT_CODES=MAIN_SEL_FUT_CODES
INS_NUMS=(0, 51, 51, 17)

# FUT_CODES=SF_FUT_CODES
# SEL_CONTEXT_FACTOR_NAMES = ["DAYOFWEEK", "HOUR"]
# INS_NUMS=(0, 51, 51, 0)

# if VERSION == "1DR":
#     SEL_MAIN_FACTOR_NAMES = []
#     INS_NUMS=(0, 51, 0, 8)

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "years": ['2024',],
    "kwargs": {
        "win": 0,                       # 采样窗口,与下面的num_channel通道数保持一致
        "step": 1,                      # 采样步长，通常为1
        "filter_win": 0,                # 是否过滤掉特征数据
        "is_filter_extreme": False,     # 是否过滤极端值
        "is_normal": True,              # 是否归一化
        "verbose": False,               # 是否打印日志
        "direct": "ls",
        "sel_lf_names": SEL_LONG_FACTOR_NAMES,
        "sel_sf_names": SEL_SHORT_FACTOR_NAMES,
        "sel_mf_names": SEL_MAIN_FACTOR_NAMES,
        "sel_ct_names": SEL_FAST_CONTEXT_FACTOR_NAMES,
        "ins_nums": INS_NUMS,
    },
    "data_loader": {
        "class": "AHFDataLoader",
        "module_path": "pyqlab.data",
        "kwargs": {
            "data_path": "e:/featdata",
            "train_codes": FUT_CODES,   # 选期货交易标的
        },
    },
}

handler_class_config = {
            "class": "DataHandler",
            "module_path": "pyqlab.data",
            "kwargs": data_handler_config,
        }

dataset_config = {
    "class": "FTSDataset",
    "module_path": "pyqlab.data",
    "kwargs": {
        "handler": handler_class_config,
        "model_type": 0,
        "seq_len": 20,
        "pred_len": 1,
    },
}

def get_dataset(ds_files=[],
                ins_nums=INS_NUMS,
                is_normal=True,
                verbose=False,
                fut_codes=MAIN_FUT_CODES,
                data_path="e:/featdata",
                start_time="",
                end_time="",
                timeenc=None,
                model_type=0,
                seq_len=32,
                label_len=0,
                pred_len=1,
                ):
    assert len(ins_nums) == 4, "ins_nums must be a tuple of 4 elements"
    data_handler_config["years"] = ds_files
    data_handler_config["start_time"] = start_time
    data_handler_config["end_time"] = end_time
    data_handler_config["kwargs"]["timeenc"] = timeenc
    data_handler_config["kwargs"]["win"] = seq_len
    data_handler_config["kwargs"]["is_normal"] = is_normal
    data_handler_config["kwargs"]["verbose"] = verbose
    data_handler_config["data_loader"]["kwargs"]["data_path"] = data_path
    data_handler_config["data_loader"]["kwargs"]["train_codes"] = fut_codes
    if ins_nums[0] == 0: # lf is empty
        data_handler_config["kwargs"]["sel_lf_names"] = []
    if ins_nums[1] == 0: # sf is empty
        data_handler_config["kwargs"]["sel_sf_names"] = []
    if ins_nums[2] == 0: # mf is empty
        data_handler_config["kwargs"]["sel_mf_names"] = []
    if ins_nums[3] == 0: # ct is empty
        data_handler_config["kwargs"]["sel_ct_names"] = []
    elif ins_nums[3] == 1:
        data_handler_config["kwargs"]["sel_ct_names"] = ["HOUR"]
        ins_nums = (ins_nums[0], ins_nums[1], ins_nums[2], 0)
    elif ins_nums[3] == 2:
        data_handler_config["kwargs"]["sel_ct_names"] = ["DAYOFWEEK", "HOUR"]
        ins_nums = (ins_nums[0], ins_nums[1], ins_nums[2], 0)
    else:
        if ins_nums[3] > 2 and ins_nums[0] > 0:
            data_handler_config["kwargs"]["sel_ct_names"] = SEL_SLOW_CONTEXT_FACTOR_NAMES
        elif ins_nums[3] > 2 and ins_nums[1] > 0:
            data_handler_config["kwargs"]["sel_ct_names"] = SEL_FAST_CONTEXT_FACTOR_NAMES
    # data_handler_config["kwargs"]["ins_nums"] = ins_nums
    handler_class_config["kwargs"] = data_handler_config
    hd: DataHandler = init_instance_by_config(handler_class_config)
    dataset_config["kwargs"]["handler"] = hd
    dataset_config["kwargs"]["model_type"] = model_type
    dataset_config["kwargs"]["seq_len"] = seq_len
    dataset_config["kwargs"]["label_len"] = label_len
    dataset_config["kwargs"]["pred_len"] = pred_len
    dataset = init_instance_by_config(dataset_config)
    dataset.setup_data(handler_kwargs=data_handler_config)
    return dataset

