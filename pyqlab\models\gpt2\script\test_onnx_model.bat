@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2\examples

REM 设置模型路径和输出路径
set MODEL_PATH=E:\lab\RoboQuant\pylab\models\candlestick_vq_gpt_cv\csvq_gpt_percent_change_fut_top_min1_30_4_8_16_1024_1.9959.pt
set OUTPUT_PATH=E:\lab\RoboQuant\pylab\models\model_fixed.onnx

REM 测试ONNX模型导出和推理
python test_onnx_model.py --model_path %MODEL_PATH% --output_path %OUTPUT_PATH% --seq_len 30 --use_time_features

echo.
echo 测试完成！
pause
