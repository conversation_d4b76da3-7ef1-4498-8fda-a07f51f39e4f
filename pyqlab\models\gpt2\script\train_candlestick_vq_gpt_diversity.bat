@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2

REM 专门解决预测多样性问题的训练配置
REM 主要改进：
REM 1. 进一步降低学习率，使用更细致的学习
REM 2. 增加模型容量但减少过拟合风险
REM 3. 使用更强的多样性约束
REM 4. 调整批次大小和梯度累积
REM 5. 增加训练轮数但使用更严格的早停

python train_candlestick_vq_gpt.py ^
--data_dir f:/hqdata/tsdb ^
--market fut ^
--block_name top ^
--period min1 ^
--begin_date 2025-03-01 ^
--end_date 2025-12-31 ^
--val_ratio 0.15 ^
--stride 1 ^
--codebook_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250525\vqcb_atr_based_fut_top_min1_512_0.0209.pt ^
--num_embeddings 512 ^
--embedding_dim 4 ^
--atr_period 14 ^
--ma_volume_period 14 ^
--vectorization_method atr_based ^
--seq_len 30 ^
--code_size 100 ^
--n_layer 6 ^
--n_head 8 ^
--d_model 128 ^
--dropout 0.15 ^
--use_time_features ^
--label_smoothing 0.02 ^
--use_auxiliary_loss ^
--batch_size 16 ^
--epochs 20 ^
--learning_rate 2e-4 ^
--weight_decay 0.02 ^
--warmup_ratio 0.3 ^
--grad_clip 0.5 ^
--grad_accum_steps 8 ^
--early_stopping 3 ^
--num_workers 4 ^
--save_dir e:/lab/RoboQuant/pylab/models/candlestick_vq_gpt_diversity ^
--log_interval 25 ^
--eval_interval 100 ^
--save_interval 500 ^
--seed 42 ^
--mixed_precision

pause
