{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import random\n", "from tqdm.notebook import tqdm\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集\n", "from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler\n", "import torch.optim as optim\n", "# from fastai.layers import SigmoidRange"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1542 1542 1542 1542\n", "\n", "Today add long count:  15\n"]}], "source": ["data_path = 'e:/lab/RoboQuant/pylab/data'\n", "portfolios = ['00200910081133001', '00171106132928000', '00170623114649000']\n", "direct = \"long\"\n", "\n", "lf_data = pd.DataFrame()\n", "sf_data = pd.DataFrame()\n", "ct_data = pd.DataFrame()\n", "ord_lb_data = pd.DataFrame()\n", "\n", "for pf in portfolios:\n", "    if os.path.isfile('%s/factors_%s_lf.%s.csv'%(data_path, direct, pf)):\n", "        lf_data = lf_data.append(pd.read_csv('%s/factors_%s_lf.%s.csv'%(data_path, direct, pf)))\n", "    if os.path.isfile('%s/factors_%s_sf.%s.csv'%(data_path, direct, pf)):\n", "        sf_data = sf_data.append(pd.read_csv('%s/factors_%s_sf.%s.csv'%(data_path, direct, pf)))\n", "    if os.path.isfile('%s/factors_%s_ct.%s.csv'%(data_path, direct, pf)):\n", "        ct_data = ct_data.append(pd.read_csv('%s/factors_%s_ct.%s.csv'%(data_path, direct, pf)))\n", "    if os.path.isfile('%s/orders_%s_label.%s.csv'%(data_path, direct, pf)):\n", "        ord_lb_data = ord_lb_data.append(pd.read_csv('%s/orders_%s_label.%s.csv'%(data_path, direct, pf)), ignore_index=True)\n", "\n", "lf_data.to_csv(\"%s/factors_%s_lf.csv\"%(data_path, direct), index=0)\n", "sf_data.to_csv(\"%s/factors_%s_sf.csv\"%(data_path, direct), index=0)\n", "ct_data.to_csv(\"%s/factors_%s_ct.csv\"%(data_path, direct), index=0)\n", "ord_lb_data.to_csv(\"%s/orders_%s_label.csv\"%(data_path, direct), index=0)\n", "\n", "lf_data.set_index(\"ord_id\", inplace=True)\n", "sf_data.set_index(\"ord_id\", inplace=True)\n", "ct_data.set_index(\"ord_id\", inplace=True)\n", "# ord_lb_data.set_index(\"ord_id\", inplace=True)\n", "print(len(lf_data), len(sf_data), len(ct_data), len(ord_lb_data))\n", "print(\"\\nToday add %s count: \" % direct, (ord_lb_data['datetime'] >= datetime.datetime.now().strftime(\"%Y%m%d 00:00:00\")).sum())\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1542, 41)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["lf_data.shape"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 1542 entries, 210922090006095 to 211019135253379\n", "Data columns (total 7 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   STDDEV_RNG        1542 non-null   float64\n", " 1   SHORT_RANGE       1542 non-null   float64\n", " 2   LONG_RANGE        1542 non-null   float64\n", " 3   FAST_QH_RSI       1542 non-null   float64\n", " 4   FAST_QH_RSI_PREV  1542 non-null   float64\n", " 5   SLOW_QH_RSI       1542 non-null   float64\n", " 6   SLOW_QH_RSI_PREV  1542 non-null   float64\n", "dtypes: float64(7)\n", "memory usage: 96.4 KB\n"]}], "source": ["ct_data.info()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["38"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ord_lb_data.CODE.unique())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class PreprocessingPipeline:\n", "    \n", "    def __init__(self, df_train, n_splits, shuffle, random_state):\n", "        \n", "        self.df_train = df_train.copy(deep=True)\n", "        self.n_splits = n_splits\n", "        self.shuffle = shuffle\n", "        self.random_state = random_state\n", "        \n", "    def _label_encode(self):\n", "\n", "        # Encoding stock_id for embeddings\n", "        le = LabelEncoder()\n", "        self.df_train['code_encoded'] = le.fit_transform(self.df_train['CODE'].values)\n", "    \n", "    def _get_folds(self):\n", "        print(ord_lb_data.index)\n", "        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)\n", "        for fold, (_, val_idx) in enumerate(skf.split(X=self.df_train, y=self.df_train['label']), 1):\n", "            ord_lb_data.loc[val_idx, 'fold'] = fold\n", "        self.df_train['fold'] = ord_lb_data['fold'].astype(np.uint8)\n", "            \n", "    def transform(self):\n", "        \n", "        self._label_encode()\n", "        self._get_folds()\n", "        \n", "        return self.df_train"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RangeIndex(start=0, stop=1542, step=1)\n"]}], "source": ["preprocessing_parameters = {\n", "    'df_train': ord_lb_data,\n", "    'n_splits': 5,\n", "    'shuffle': True,\n", "    'random_state': 42\n", "}\n", "\n", "preprocessing_pipeline = PreprocessingPipeline(**preprocessing_parameters)\n", "df_train = preprocessing_pipeline.transform()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "      <th>CODE</th>\n", "      <th>code_encoded</th>\n", "      <th>fold</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>210922090831255</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 09:08:31</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>210922133807043</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 13:38:07</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>210922140751125</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210922 14:07:51</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>210923093149123</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210923 09:31:49</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>210923111322363</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210923 11:13:22</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1537</th>\n", "      <td>210927210007017</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210927 21:00:07</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1538</th>\n", "      <td>210927213705105</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210927 21:37:05</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1539</th>\n", "      <td>210928093756285</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210928 09:37:56</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1540</th>\n", "      <td>210928111845457</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210928 11:18:45</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1541</th>\n", "      <td>210930140924235</td>\n", "      <td>ZN2111.SC</td>\n", "      <td>20210930 14:09:24</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>ZN</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1542 rows × 8 columns</p>\n", "</div>"], "text/plain": ["               ord_id instrument           datetime direct  label CODE  \\\n", "0     210922090831255   A2111.DC  20210922 09:08:31      L      1    A   \n", "1     210922133807043   A2111.DC  20210922 13:38:07      L      0    A   \n", "2     210922140751125   A2111.DC  20210922 14:07:51      L      0    A   \n", "3     210923093149123   A2111.DC  20210923 09:31:49      L      1    A   \n", "4     210923111322363   A2111.DC  20210923 11:13:22      L      1    A   \n", "...               ...        ...                ...    ...    ...  ...   \n", "1537  210927210007017  ZN2111.SC  20210927 21:00:07      L      0   ZN   \n", "1538  210927213705105  ZN2111.SC  20210927 21:37:05      L      0   ZN   \n", "1539  210928093756285  ZN2111.SC  20210928 09:37:56      L      0   ZN   \n", "1540  210928111845457  ZN2111.SC  20210928 11:18:45      L      0   ZN   \n", "1541  210930140924235  ZN2111.SC  20210930 14:09:24      L      1   ZN   \n", "\n", "      code_encoded  fold  \n", "0                0     5  \n", "1                0     5  \n", "2                0     1  \n", "3                0     2  \n", "4                0     3  \n", "...            ...   ...  \n", "1537            37     4  \n", "1538            37     3  \n", "1539            37     5  \n", "1540            37     1  \n", "1541            37     1  \n", "\n", "[1542 rows x 8 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["class Factor2DDataset(Dataset):\n", "\n", "    def __init__(self, ord_df, lf_df, sf_df, ct_df, flip_probability=0.):\n", "\n", "        self.ord_df = ord_df\n", "        self.lf_df = lf_df\n", "        self.sf_df = sf_df\n", "        self.ct_df = ct_df\n", "        \n", "        self.lf_means =  self.lf_df.values.mean(axis=0)\n", "        self.lf_stds = self.lf_df.values.std(axis=0)\n", "        self.sf_means =  self.sf_df.values.mean(axis=0)\n", "        self.sf_stds = self.sf_df.values.std(axis=0)\n", "        self.ct_means =  self.ct_df.values.mean(axis=0)\n", "        self.ct_stds = self.ct_df.values.std(axis=0)\n", "\n", "        self.transforms = {\n", "            'flip': flip_probability,\n", "        }\n", "\n", "    def __len__(self):\n", "        return len(self.ord_df)\n", "\n", "    def __getitem__(self, idx):\n", "\n", "        \"\"\"\n", "        Get the idxth element in the dataset\n", "\n", "        Parameters\n", "        ----------\n", "        idx (int): Index of the sample (0 <= idx < len(self.df))\n", "\n", "        Returns\n", "        -------\n", "        \"\"\"\n", "\n", "        id = self.ord_df.iloc[idx]['ord_id']\n", "\n", "        item = []\n", "        item.append((self.lf_df.loc[id].tolist() - self.lf_means)/self.lf_stds)\n", "        item.append((self.sf_df.loc[id].tolist() - self.sf_means)/self.sf_stds)\n", "        ct_list = (self.ct_df.loc[id].tolist() - self.ct_means)/self.ct_stds\n", "        item.append(ct_list.tolist() + [0]*(len(self.lf_df.loc[id])-len(self.ct_df.loc[id])))\n", "        ft_arr = np.array(item)        \n", "\n", "        # Sequences from book data\n", "        sequences = torch.as_tensor(ft_arr, dtype=torch.float)\n", "\n", "        # Flip sequences on zeroth dimension\n", "        if np.random.rand() < self.transforms['flip']:\n", "            sequences = torch.flip(sequences, dims=[0])\n", "\n", "        code_encoded = torch.as_tensor(self.ord_df.iloc[idx]['code_encoded'], dtype=torch.long)\n", "        target = self.ord_df.iloc[idx]['label']\n", "        target = torch.as_tensor(target, dtype=torch.float)\n", "        return code_encoded, sequences, target\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["fold = 0\n", "trn_idx, val_idx = df_train.loc[df_train['fold'] != fold].index, df_train.loc[df_train['fold'] == fold].index\n", "train_dataset = Factor2DDataset(ord_df=df_train.loc[trn_idx, :], lf_df=lf_data, sf_df=sf_data, ct_df=ct_data, flip_probability=0.)\n", "train_loader = DataLoader(\n", "    train_dataset,\n", "    batch_size=32,\n", "    sampler=RandomSampler(train_dataset),\n", "    pin_memory=True,\n", "    drop_last=False,\n", "    # num_workers=8,\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["td0, td1, td2 = next(iter(train_loader))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1., 1., 0., 1., 1., 0., 1., 1., 0., 1., 1., 0., 1., 1., 0., 1., 1., 0.,\n", "        1., 1., 1., 1., 1., 0., 1., 0., 0., 1., 0., 1., 0., 1.])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["td2"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def visualize_learning_curve(training_losses, validation_losses, title, path=None):\n", "    \n", "    \"\"\"\n", "    Visualize learning curves of the models\n", "\n", "    Parameters\n", "    ----------\n", "    training_losses [array-like of shape (n_epochs)]: Array of training losses computed after every epoch\n", "    validation_losses [array-like of shape (n_epochs)]: Array of validation losses computed after every epoch\n", "    title (str): Title of the plot\n", "    path (str or None): Path of the output file (if path is None, plot is displayed with selected backend)\n", "    \"\"\"\n", "\n", "    fig, ax = plt.subplots(figsize=(32, 8), dpi=100)\n", "\n", "    sns.lineplot(\n", "        x=np.arange(1, len(training_losses) + 1),\n", "        y=training_losses,\n", "        ax=ax,\n", "        label='train_loss'\n", "    )\n", "    sns.lineplot(\n", "        x=np.arange(1, len(validation_losses) + 1),\n", "        y=validation_losses,\n", "        ax=ax,\n", "        label='val_loss'\n", "    )\n", "\n", "    ax.set_xlabel('Epochs/Steps', size=15, labelpad=12.5)\n", "    ax.set_ylabel('Loss', size=15, labelpad=12.5)\n", "    ax.tick_params(axis='x', labelsize=12.5, pad=10)\n", "    ax.tick_params(axis='y', labelsize=12.5, pad=10)\n", "    ax.legend(prop={'size': 18})\n", "    ax.set_title(title, size=20, pad=15)\n", "\n", "    if path is None:\n", "        plt.show()\n", "    else:\n", "        plt.savefig(path)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def set_seed(seed, deterministic_cudnn=False):\n", "\n", "    \"\"\"\n", "    Set random seed for reproducible results\n", "    \n", "    Parameters\n", "    ----------\n", "    seed (int): Random seed\n", "    deterministic_cudnn (bool): Whether to set deterministic cuDNN or not\n", "    \"\"\"\n", "\n", "    if deterministic_cudnn:\n", "        torch.backends.cudnn.deterministic = True\n", "        torch.backends.cudnn.benchmark = False\n", "\n", "    os.environ['PYTHONHASHSEED'] = str(seed)\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    torch.manual_seed(seed)\n", "    torch.cuda.manual_seed(seed)\n", "    torch.cuda.manual_seed_all(seed)\n", "\n", "\n", "def rmspe_metric(y_true, y_pred):\n", "\n", "    \"\"\"\n", "    Calculate root mean squared percentage error between ground-truth and predictions\n", "    \n", "    Parameters\n", "    ----------\n", "    y_true [array-like of shape (n_samples)]: Ground-truth\n", "    y_pred [array-like of shape (n_samples)]: Predictions\n", "    \n", "    Returns\n", "    -------\n", "    rmspe (float): Root mean squared percentage error\n", "    \"\"\"\n", "\n", "    # rmspe = np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))\n", "    # return rmspe\n", "    return (y_true == y_pred).astype(float).mean()\n", "\n", "\n", "def rmspe_loss(y_true, y_pred):\n", "\n", "    \"\"\"\n", "    Calculate root mean squared percentage error between ground-truth and predictions\n", "    \n", "    Parameters\n", "    ----------\n", "    y_true [torch.tensor of shape (n_samples)]: Ground-truth\n", "    y_pred [torch.tensor of shape (n_samples)]: Predictions\n", "    \n", "    Returns\n", "    -------\n", "    rmspe (torch.FloatTensor): Root mean squared percentage error\n", "    \"\"\"\n", "\n", "    rmspe = torch.sqrt(torch.mean(torch.square((y_true - y_pred) / y_true)))\n", "    return rmspe\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["class Conv1dBlock(nn.Module):\n", "\n", "    def __init__(self, in_channels, out_channels, kernel_size=(5,), stride=(1,), padding=(2,), skip_connection=False):\n", "\n", "        super(Conv1d<PERSON><PERSON>, self).__init__()\n", "\n", "        self.skip_connection = skip_connection\n", "        self.conv_block = nn.Sequential(\n", "            nn.Conv1d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),\n", "            nn.BatchNorm1d(out_channels),\n", "            nn.ReLU(inplace=True),\n", "            nn.Conv1d(out_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),\n", "            nn.BatchNorm1d(out_channels),\n", "        )\n", "        self.downsample = nn.Sequential(\n", "            nn.Conv1d(in_channels, out_channels, kernel_size=(1,), stride=(1,), bias=False),\n", "            nn.BatchNorm1d(out_channels)\n", "        )\n", "        self.relu = nn.ReLU(inplace=True)\n", "\n", "    def forward(self, x):\n", "\n", "        output = self.conv_block(x)\n", "        if self.skip_connection:\n", "            x = self.downsample(x)\n", "            output += x\n", "        output = self.relu(output)\n", "\n", "        return output\n", "\n", "\n", "class CNN1DModel(nn.Module):\n", "\n", "    def __init__(self, in_channels):\n", "\n", "        super(<PERSON>1<PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.stock_embeddings = nn.Embedding(num_embeddings=38, embedding_dim=41)\n", "        self.conv_block1 = Conv1dBlock(in_channels=in_channels, out_channels=32, skip_connection=True)\n", "        self.conv_block2 = Conv1dBlock(in_channels=32, out_channels=64, skip_connection=True)\n", "        self.conv_block3 = Conv1dBlock(in_channels=64, out_channels=128, skip_connection=True)\n", "        self.conv_block4 = Conv1dBlock(in_channels=128, out_channels=64, skip_connection=True)\n", "        self.conv_block5 = Conv1dBlock(in_channels=64, out_channels=32, skip_connection=True)\n", "        self.conv_block6 = Conv1dBlock(in_channels=32, out_channels=16, skip_connection=True)\n", "        self.conv_block7 = Conv1dBlock(in_channels=16, out_channels=8, skip_connection=True)\n", "        self.conv_block8 = Conv1dBlock(in_channels=8, out_channels=1, skip_connection=True)\n", "        self.pooling = nn.AvgPool1d(kernel_size=(3,), stride=(1,), padding=(1,))\n", "        self.linear = nn.Linear(3+41, 32, bias=True)\n", "        self.relu = nn.ReLU()\n", "        self.dropout = nn.Dropout(0.5)\n", "        self.head = nn.Sequential(\n", "            nn.Linear(32, 1, bias=True),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "            # SigmoidRange(0, 0.1)\n", "        )\n", "\n", "    def forward(self, stock_ids, sequences):\n", "\n", "        x = torch.transpose(sequences, 1, 2)\n", "        x = self.conv_block1(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block2(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block3(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block4(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block5(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block6(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block7(x)\n", "        x = self.pooling(x)\n", "        x = self.conv_block8(x)\n", "        x = self.pooling(x)\n", "        x = x.view(x.size(0), -1)\n", "        embedded_stock_ids = self.stock_embeddings(stock_ids)\n", "        x = torch.cat([x, self.dropout(embedded_stock_ids)], dim=1)\n", "        x = self.relu(self.linear(x))\n", "        output = self.head(x)\n", "        \n", "        return output.view(-1)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["class Trainer:\n", "\n", "    def __init__(self, model_name, model_path, model_parameters, training_parameters):\n", "\n", "        self.model_name = model_name\n", "        self.model_path = model_path\n", "        self.model_parameters = model_parameters\n", "        self.training_parameters = training_parameters\n", "\n", "    def get_model(self):\n", "\n", "        model = None\n", "\n", "        if self.model_name == 'cnn1d':\n", "            model = CNN1DModel(**self.model_parameters)\n", "\n", "        return model\n", "\n", "    def train_fn(self, train_loader, model, criterion, optimizer, device):\n", "\n", "        print('\\n')\n", "        model.train()\n", "        progress_bar = tqdm(train_loader)\n", "        losses = []\n", "        correct = 0\n", "        total = 0\n", "\n", "        if self.training_parameters['amp']:\n", "            scaler = torch.cuda.amp.GradScaler()\n", "        else:\n", "            scaler = None\n", "\n", "        for stock_id_encoded, sequences, target in progress_bar:\n", "\n", "            stock_id_encoded, sequences, target = stock_id_encoded.to(device), sequences.to(device), target.to(device)\n", "\n", "            if scaler is not None:\n", "                with torch.cuda.amp.autocast():\n", "                    optimizer.zero_grad()\n", "                    output = model(stock_id_encoded, sequences)\n", "                    loss = criterion(output, target)\n", "                scaler.scale(loss).backward()\n", "                scaler.step(optimizer)\n", "                scaler.update()\n", "                with torch.no_grad():\n", "                    correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()\n", "                    total += target.size(0)\n", "            else:\n", "                optimizer.zero_grad()\n", "                output = model(stock_id_encoded, sequences)\n", "                loss = criterion(output, target)\n", "                loss.backward()\n", "                optimizer.step()\n", "                with torch.no_grad():\n", "                    correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()\n", "                    total += target.size(0)\n", "\n", "            losses.append(loss.item())\n", "            average_loss = np.mean(losses)\n", "            progress_bar.set_description(f'train_rmspe: {average_loss:.6f}')\n", "\n", "        train_loss = np.mean(losses)\n", "        train_acc = correct / total\n", "        return train_loss, train_acc\n", "\n", "    def val_fn(self, val_loader, model, criterion, device):\n", "\n", "        model.eval()\n", "        progress_bar = tqdm(val_loader)\n", "        losses = []\n", "        correct = 0\n", "        total = 0\n", "\n", "        with torch.no_grad():\n", "            \n", "            for stock_id_encoded, sequences, target in progress_bar:\n", "                \n", "                stock_id_encoded, sequences, target = stock_id_encoded.to(device), sequences.to(device), target.to(device)\n", "                output = model(stock_id_encoded, sequences)\n", "                loss = criterion(output, target)\n", "                losses.append(loss.item())\n", "                average_loss = np.mean(losses)\n", "                correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()\n", "                total += target.size(0)\n", "                progress_bar.set_description(f'val_rmspe: {average_loss:.6f}')\n", "\n", "        val_loss = np.mean(losses)\n", "        val_acc = correct / total\n", "        return val_loss, val_acc\n", "\n", "    def train_and_validate(self, df_train):\n", "\n", "        print(f'\\n{\"-\" * 26}\\nRunning Model for Training\\n{\"-\" * 26}\\n')\n", "\n", "        for fold in sorted(df_train['fold'].unique()):\n", "\n", "            print(f'\\nFold {fold}\\n{\"-\" * 6}')\n", "\n", "            trn_idx, val_idx = df_train.loc[df_train['fold'] != fold].index, df_train.loc[df_train['fold'] == fold].index\n", "            train_dataset = Factor2DDataset(ord_df=df_train.loc[trn_idx, :], lf_df=lf_data, sf_df=sf_data, ct_df=ct_data, flip_probability=0.)\n", "            train_loader = DataLoader(\n", "                train_dataset,\n", "                batch_size=self.training_parameters['batch_size'],\n", "                sampler=RandomSampler(train_dataset),\n", "                # pin_memory=True,\n", "                drop_last=False,\n", "                # num_workers=self.training_parameters['num_workers'],\n", "            )\n", "            \n", "            val_dataset = Factor2DDataset(ord_df=df_train.loc[val_idx, :], lf_df=lf_data, sf_df=sf_data, ct_df=ct_data, flip_probability=0.)\n", "            val_loader = DataLoader(\n", "                val_dataset,\n", "                batch_size=self.training_parameters['batch_size'],\n", "                sampler=SequentialSampler(val_dataset),\n", "                # pin_memory=True,\n", "                drop_last=False,\n", "                # num_workers=self.training_parameters['num_workers'],\n", "            )\n", "\n", "            set_seed(self.training_parameters['random_state'], deterministic_cudnn=self.training_parameters['deterministic_cudnn'])\n", "            device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')\n", "            # device = torch.device('cpu')\n", "            model = self.get_model()\n", "            model = model.to(device)\n", "\n", "\n", "\n", "            optimizer = optim.<PERSON>(\n", "                model.parameters(),\n", "                lr=self.training_parameters['learning_rate'],\n", "                betas=self.training_parameters['betas'],\n", "                weight_decay=self.training_parameters['weight_decay']\n", "            )\n", "            scheduler = optim.lr_scheduler.ReduceLROnPlateau(\n", "                optimizer,\n", "                mode='min',\n", "                patience=self.training_parameters['reduce_lr_patience'],\n", "                factor=self.training_parameters['reduce_lr_factor'],\n", "                min_lr=self.training_parameters['reduce_lr_min'],\n", "                verbose=True\n", "            )\n", "\n", "            early_stopping = False\n", "            summary = {\n", "                'train_loss': [],\n", "                'val_loss': []\n", "            }\n", "            \n", "            criterion = nn.BCELoss() # rmspe_loss\n", "\n", "            for epoch in range(1, self.training_parameters['epochs'] + 1):\n", "\n", "                if early_stopping:\n", "                    break\n", "\n", "                train_loss, train_acc = self.train_fn(train_loader, model, criterion, optimizer, device)\n", "                val_loss, val_acc = self.val_fn(val_loader, model, criterion, device)\n", "                print(f'Epoch {epoch} - Training & Validation Loss: [{train_loss:.6f} -  {val_loss:.6f}] Accuracy: [{train_acc:.6f} - {val_acc:.6f}]')\n", "                scheduler.step(val_loss)\n", "\n", "                best_val_loss = np.min(summary['val_loss']) if len(summary['val_loss']) > 0 else np.inf\n", "                if val_loss < best_val_loss:\n", "                    model_path = f'{self.model_path}/{self.model_name}_fold{fold}.pt'\n", "                    torch.save(model.state_dict(), model_path)\n", "                    print(f'Saving model to {model_path} (validation loss decreased from {best_val_loss:.6f} to {val_loss:.6f})')\n", "\n", "                summary['train_loss'].append(train_loss)\n", "                summary['val_loss'].append(val_loss)\n", "\n", "                best_iteration = np.argmin(summary['val_loss']) + 1\n", "                if len(summary['val_loss']) - best_iteration >= self.training_parameters['early_stopping_patience']:\n", "                    print(f'Early stopping (validation loss didn\\'t increase for {self.training_parameters[\"early_stopping_patience\"]} epochs/steps)')\n", "                    print(f'Best validation loss is {np.min(summary[\"val_loss\"]):.6f}')\n", "                    visualize_learning_curve(\n", "                        training_losses=summary['train_loss'],\n", "                        validation_losses=summary['val_loss'],\n", "                        title=f'{self.model_name} - Fold {fold} Learning Curve',\n", "                        path=f'{self.model_path}/{self.model_name}_fold{fold}_learning_curve.png'\n", "                    )\n", "                    early_stopping = True\n", "\n", "    def inference(self, df_train):\n", "\n", "        print(f'\\n{\"-\" * 27}\\nRunning Model for Inference\\n{\"-\" * 27}')\n", "        df_train[f'{self.model_name}_predictions'] = 0\n", "\n", "        for fold in sorted(df_train['fold'].unique()):\n", "\n", "            _, val_idx = df_train.loc[df_train['fold'] != fold].index, df_train.loc[df_train['fold'] == fold].index\n", "            val_dataset = Factor2DDataset(ord_df=df_train.loc[val_idx, :], lf_df=lf_data, sf_df=sf_data, ct_df=ct_data, flip_probability=0.)\n", "            val_loader = DataLoader(\n", "                val_dataset,\n", "                batch_size=self.training_parameters['batch_size'],\n", "                sampler=SequentialSampler(val_dataset),\n", "                pin_memory=True,\n", "                drop_last=False,\n", "                # num_workers=self.training_parameters['num_workers'], 此参数在笔记本电脑不能使用\n", "            )\n", "\n", "            set_seed(self.training_parameters['random_state'], deterministic_cudnn=self.training_parameters['deterministic_cudnn'])\n", "            device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')\n", "            model = self.get_model()\n", "            model.load_state_dict(torch.load(f'{self.model_path}/{self.model_name}_fold{fold}.pt'))\n", "            model.to(device)\n", "            model.eval()\n", "\n", "            val_predictions = []\n", "            with torch.no_grad():\n", "                for stock_id, sequences, target in val_loader:\n", "                    stock_id, sequences, target = stock_id.to(device), sequences.to(device), target.to(device)\n", "                    output = model(stock_id, sequences)\n", "                    output = (output>0.5).type(torch.IntTensor)\n", "                    output = output.detach().cpu().numpy().squeeze().tolist()\n", "                    \n", "                    if isinstance(output, int):\n", "                        val_predictions.append(output)\n", "                    else:\n", "                        val_predictions += output\n", "\n", "            df_train.loc[val_idx, f'{self.model_name}_predictions'] = val_predictions\n", "            fold_score = rmspe_metric(df_train.loc[val_idx, 'label'].to_numpy(), np.array(val_predictions))\n", "            print(f'Fold {fold} - Accuracy: {fold_score:.6}')\n", "\n", "            del _, val_idx, val_dataset, val_loader, val_predictions, model\n", "\n", "        print(f'{\"-\" * 30}')\n", "        for stock_id in df_train['CODE'].unique():\n", "            df_stock = df_train.loc[df_train['CODE'] == stock_id, :]\n", "            stock_oof_score = rmspe_metric(df_stock['label'].to_numpy(), df_stock[f'{self.model_name}_predictions'].to_numpy())\n", "            print(f'Instrument code: {stock_id} - Accuracy: {stock_oof_score:.6}')\n", "\n", "        oof_score = rmspe_metric(df_train['label'], df_train[f'{self.model_name}_predictions'])\n", "        print(f'{\"-\" * 30}\\nOOF Accuracy: {oof_score:.6}\\n{\"-\" * 30}')\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--------------------------\n", "Running Model for Training\n", "--------------------------\n", "\n", "\n", "Fold 1\n", "------\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a20519913f31464a8d7389e9d257a7fc", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "00c64c5698234cefae4dc4938b8228c7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1 - Training & Validation Loss: [0.691655 -  0.685694] Accuracy: [0.531225 - 0.524272]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from inf to 0.685694)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9805558e6cf483f86c85ab1d4e35ce1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e6a949d3314044b892238848bee26bea", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2 - Training & Validation Loss: [0.683471 -  0.684488] Accuracy: [0.579075 - 0.556634]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from 0.685694 to 0.684488)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a12d4971f5d2476d9108ce83494ccba4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5207e27d47064156bc532c915c8d6b94", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3 - Training & Validation Loss: [0.679972 -  0.682893] Accuracy: [0.563666 - 0.556634]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from 0.684488 to 0.682893)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e9cd70c845f14724b1f56ac3ef825a24", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "77dd5d46d4dc42e7b07807724490eb20", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4 - Training & Validation Loss: [0.680828 -  0.685308] Accuracy: [0.569343 - 0.540453]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b718831f8b8643b8a694d55fdab8bebc", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6179b8996b6e4f81bf62c4580a2fc716", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5 - Training & Validation Loss: [0.670236 -  0.676809] Accuracy: [0.571776 - 0.559871]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from 0.682893 to 0.676809)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d4be538857f94e1db502de2d0345459c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5184f67cfb5d49408526ae676ec13be7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6 - Training & Validation Loss: [0.669648 -  0.672592] Accuracy: [0.586375 - 0.579288]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from 0.676809 to 0.672592)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "96ee3900502c451d9a48f873aceb61fa", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef47c236aa5649259f3b8fe9a94a1a9c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7 - Training & Validation Loss: [0.652958 -  0.670262] Accuracy: [0.601784 - 0.569579]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from 0.672592 to 0.670262)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "81ec607114d9410196194551b8b8089c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "10e44c07897246ca9a2efcf384956142", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8 - Training & Validation Loss: [0.641000 -  0.708413] Accuracy: [0.606650 - 0.553398]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bdb5e92ad9b541cea8d189fda0015889", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c418b32380e4431bb8b9cfd12b01e39", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9 - Training & Validation Loss: [0.648568 -  0.677932] Accuracy: [0.579075 - 0.553398]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "462aec9b792c4acaa42d9aee2d4531d4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c3a4bd41063a49688ce68b381a12b2ba", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10 - Training & Validation Loss: [0.648066 -  0.658930] Accuracy: [0.589619 - 0.566343]\n", "Saving model to ./cnn1d_fold1.pt (validation loss decreased from 0.670262 to 0.658930)\n", "\n", "Fold 2\n", "------\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e7499391f084bc8a0f6149feae48f13", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca68009db2ee4553a5e475409129c09d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1 - Training & Validation Loss: [0.691659 -  0.691107] Accuracy: [0.536902 - 0.533981]\n", "Saving model to ./cnn1d_fold2.pt (validation loss decreased from inf to 0.691107)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e739deb0317e475195951c00af322a41", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "de75295f2c7649fe93c173e3a234faed", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2 - Training & Validation Loss: [0.687019 -  0.685960] Accuracy: [0.553122 - 0.556634]\n", "Saving model to ./cnn1d_fold2.pt (validation loss decreased from 0.691107 to 0.685960)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "02a580fd521240fea70c621e86933c58", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bee4c7811d7f401981cf8c9e13e8ff9e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3 - Training & Validation Loss: [0.682558 -  0.681206] Accuracy: [0.570154 - 0.566343]\n", "Saving model to ./cnn1d_fold2.pt (validation loss decreased from 0.685960 to 0.681206)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "281f9337175d4b76a6c3afca5f0e8aa2", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "49746f1546434461899974217f87532a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4 - Training & Validation Loss: [0.684600 -  0.679820] Accuracy: [0.562855 - 0.569579]\n", "Saving model to ./cnn1d_fold2.pt (validation loss decreased from 0.681206 to 0.679820)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e36c6b3889dd4f28b855d87c34ce6ab2", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "71bb2b4d2f654defa3eb5357e5949e98", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5 - Training & Validation Loss: [0.667815 -  0.680416] Accuracy: [0.570965 - 0.553398]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0a77ab86bed0415c897335ef07a7bc8e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ad85a338af204cfd9d17d3e4189efc2a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6 - Training & Validation Loss: [0.654658 -  0.660658] Accuracy: [0.591241 - 0.598706]\n", "Saving model to ./cnn1d_fold2.pt (validation loss decreased from 0.679820 to 0.660658)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f22e9719a5d5420eb1fdb25d2e018c79", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ea411ecfb001437aa17509a774764422", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7 - Training & Validation Loss: [0.662143 -  0.663737] Accuracy: [0.577453 - 0.576052]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c756d5ac46a441f6822a0bb34a96b0fd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "459e6069cd7942169cb5685f0e452d61", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8 - Training & Validation Loss: [0.644784 -  0.658761] Accuracy: [0.596918 - 0.585761]\n", "Saving model to ./cnn1d_fold2.pt (validation loss decreased from 0.660658 to 0.658761)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54d1ff41a96d459583bef3ada7741c2f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "738b9d3f092b404a94ec29ec6124a917", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9 - Training & Validation Loss: [0.646887 -  0.663194] Accuracy: [0.584753 - 0.611650]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "409871241e674621888ae895b4b01c70", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d09ad63952544b4b3c1c4e32e8505e5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10 - Training & Validation Loss: [0.630989 -  0.661746] Accuracy: [0.612328 - 0.598706]\n", "\n", "Fold 3\n", "------\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "de78053496924b7b9a58dfe3d5c1eac6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8214b78c0ebb4e1dbcc0cdb9c1990e3b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1 - Training & Validation Loss: [0.689875 -  0.683129] Accuracy: [0.545381 - 0.529221]\n", "Saving model to ./cnn1d_fold3.pt (validation loss decreased from inf to 0.683129)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2326c83d58544587bc17ae3bbf6566ef", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1dc60271513c4f7481998faf195a6afc", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2 - Training & Validation Loss: [0.687640 -  0.682292] Accuracy: [0.547812 - 0.542208]\n", "Saving model to ./cnn1d_fold3.pt (validation loss decreased from 0.683129 to 0.682292)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "26ef0865bfa24dfb996d07c8a1eaac2b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "52d25a6ce33f4d579f1cc4e03bfa13bb", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3 - Training & Validation Loss: [0.685530 -  0.684829] Accuracy: [0.563209 - 0.538961]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "11fb39fcbbeb47d9b8ba734ecdf6e6ee", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fc8d12c18c3b4c33b3556dffe9e2942d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4 - Training & Validation Loss: [0.678356 -  0.682156] Accuracy: [0.575365 - 0.542208]\n", "Saving model to ./cnn1d_fold3.pt (validation loss decreased from 0.682292 to 0.682156)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "90363f9a1e4741caa922f3785deca071", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d5790dae6822449da6b6c27ca7fa63d9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5 - Training & Validation Loss: [0.670661 -  0.680465] Accuracy: [0.587520 - 0.561688]\n", "Saving model to ./cnn1d_fold3.pt (validation loss decreased from 0.682156 to 0.680465)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a9207480204d49a496d3996646a7739f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "854e5024cca74b27a7dba6cb3d210cbd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6 - Training & Validation Loss: [0.664661 -  0.682781] Accuracy: [0.602107 - 0.561688]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b85156df67840fe8bce773da6d70bc4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "16d4fd8d03c74ee8b4e321d21ecb4437", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7 - Training & Validation Loss: [0.657805 -  0.684909] Accuracy: [0.615073 - 0.538961]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8df82fff6c224548a688a16494747204", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c9efd288fb747d9b851bc82da719649", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8 - Training & Validation Loss: [0.653528 -  0.689396] Accuracy: [0.619125 - 0.571429]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8bcd07d0423447fbaa5e7a167f882018", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "319bbdb704c948caa6efe60f9329be5f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9 - Training & Validation Loss: [0.647975 -  0.723950] Accuracy: [0.621556 - 0.542208]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e621af3554e94ea0b07d904c36ab96df", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b2157c6893a040fa911dcdd17eb5fafe", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10 - Training & Validation Loss: [0.650318 -  0.703261] Accuracy: [0.640194 - 0.555195]\n", "\n", "Fold 4\n", "------\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "52045a1b34654dc8994631d3879aeed8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "557016a2fc64410da180726f57a49ae7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1 - Training & Validation Loss: [0.687285 -  0.685849] Accuracy: [0.547002 - 0.542208]\n", "Saving model to ./cnn1d_fold4.pt (validation loss decreased from inf to 0.685849)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "07a9b1fd12334b4cae3fbd2085a82f7a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6d148abcca094f43a7351ae6d00ab68d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2 - Training & Validation Loss: [0.690595 -  0.685563] Accuracy: [0.538898 - 0.571429]\n", "Saving model to ./cnn1d_fold4.pt (validation loss decreased from 0.685849 to 0.685563)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "530a74025f0c46fcb3f832cc54cfc327", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e2771d424d3a4b0a9c928e76e86749b8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3 - Training & Validation Loss: [0.681684 -  0.690025] Accuracy: [0.580227 - 0.548701]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "293dfe8cf88c4b92855d0b2232be8f6e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "711f3577d21d40748fef4b7f045dcc56", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4 - Training & Validation Loss: [0.670990 -  0.690757] Accuracy: [0.585900 - 0.564935]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7264aebb95c44e6c90d7335b8f864d7d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f416f0e474dc454b9c74b61b8147715c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5 - Training & Validation Loss: [0.674468 -  0.688573] Accuracy: [0.589141 - 0.525974]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "50c8418607094bbab4b7c1a9c05ba02f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0188c20c55d846b495f05c74ea01f3c3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6 - Training & Validation Loss: [0.671369 -  0.693677] Accuracy: [0.576175 - 0.574675]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "04b5d9c379bf45d3b4f63da898545796", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "23bc46cf66ff4987af1b0c992fdf4a6e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7 - Training & Validation Loss: [0.673293 -  0.682631] Accuracy: [0.578606 - 0.535714]\n", "Saving model to ./cnn1d_fold4.pt (validation loss decreased from 0.685563 to 0.682631)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "40eab0e0daa04376915de98100e2f3d2", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3d5eb13abf314265a33b7a8d2d05b9a9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8 - Training & Validation Loss: [0.665545 -  0.691443] Accuracy: [0.594814 - 0.568182]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8ae4ede2c69a415d9f65e166465d569b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "575e763825634acc87e884837532c8f6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9 - Training & Validation Loss: [0.657685 -  0.686509] Accuracy: [0.601297 - 0.538961]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7815887bad49420eb9fd9111b392c225", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "574e09159a6140aab39ff7dca508f30a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10 - Training & Validation Loss: [0.656321 -  0.689031] Accuracy: [0.594003 - 0.519481]\n", "\n", "Fold 5\n", "------\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "83358add4f66427abc12c0f423ef5f14", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c142ac0d1f9f4e4180d2a685358f2e1d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1 - Training & Validation Loss: [0.690939 -  0.685575] Accuracy: [0.552674 - 0.542208]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from inf to 0.685575)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ae0f0daff52d4c4a9cf9c334a0964a5d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b07d8d293219498db45cfc4f6fe28f4b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2 - Training & Validation Loss: [0.686258 -  0.684949] Accuracy: [0.556726 - 0.525974]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from 0.685575 to 0.684949)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a6bec35f403844d5943ef2a9231dff21", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6449d8d27db640fabf948814af2ecf60", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3 - Training & Validation Loss: [0.677237 -  0.675983] Accuracy: [0.571313 - 0.542208]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from 0.684949 to 0.675983)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "11ce88fc8c4d4167922ad44da6fc443f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cbaad94bcfab4acf8ca821534d19496a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4 - Training & Validation Loss: [0.677622 -  0.679661] Accuracy: [0.559157 - 0.571429]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e3951c1f46644ddf9c00d795d8934538", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7dfaa50730c54fb8b5b9a49411c028d7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5 - Training & Validation Loss: [0.675313 -  0.672833] Accuracy: [0.569692 - 0.594156]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from 0.675983 to 0.672833)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "80c4a8068ade410895e9247476af65f5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "83269a15ccd0458c82cf833bc1151b6a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6 - Training & Validation Loss: [0.666647 -  0.673168] Accuracy: [0.583468 - 0.574675]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1cb1536863b74c608984365c1c089c05", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d738d9398334602929038f93bfd9fae", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7 - Training & Validation Loss: [0.661602 -  0.659045] Accuracy: [0.572934 - 0.607143]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from 0.672833 to 0.659045)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3606f64a1fc14f7ea6b4de5990e1d480", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca76219df6a2437497a7c9bc2c3e1c06", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8 - Training & Validation Loss: [0.660000 -  0.655692] Accuracy: [0.588331 - 0.620130]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from 0.659045 to 0.655692)\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "292c778eeb2c473390969dfd8e95038a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "910d5be84bad46e89c42ea90745701ea", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9 - Training & Validation Loss: [0.647182 -  0.672797] Accuracy: [0.598865 - 0.581169]\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f4e0de424bcb412589d75e1ccb0c6ef9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ff60a5a85eb4cf7b673a1935884b389", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10 - Training & Validation Loss: [0.648168 -  0.654978] Accuracy: [0.601297 - 0.603896]\n", "Saving model to ./cnn1d_fold5.pt (validation loss decreased from 0.655692 to 0.654978)\n"]}], "source": ["cnn1d_parameters = {\n", "    'model_name': 'cnn1d',\n", "    'model_path': '.',\n", "    'model_parameters': {\n", "        'in_channels': 41,\n", "    },\n", "    'training_parameters': {\n", "        'amp': <PERSON><PERSON><PERSON>,\n", "        'learning_rate': 0.0005,\n", "        'betas': (0.9, 0.999),\n", "        'weight_decay': 0,\n", "        'epochs': 10,\n", "        'batch_size': 32,\n", "        'reduce_lr_patience': 5,\n", "        'reduce_lr_factor': 0.25,\n", "        'reduce_lr_min': 0.000001,\n", "        'early_stopping_patience': 20,\n", "        'num_workers': 3,\n", "        'random_state': 42,\n", "        'deterministic_cudnn': <PERSON><PERSON><PERSON>,\n", "        'random_state': 42\n", "    }\n", "}\n", "\n", "trainer = Trainer(**cnn1d_parameters)\n", "trainer.train_and_validate(df_train)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "---------------------------\n", "Running Model for Inference\n", "---------------------------\n", "Fold 1 - Accuracy: 0.566343\n", "Fold 2 - Accuracy: 0.585761\n", "Fold 3 - Accuracy: 0.561688\n", "Fold 4 - Accuracy: 0.535714\n", "Fold 5 - Accuracy: 0.603896\n", "------------------------------\n", "Instrument code: A - Accuracy: 0.625\n", "Instrument code: AG - Accuracy: 0.490566\n", "Instrument code: AP - Accuracy: 0.683333\n", "Instrument code: B - Accuracy: 1.0\n", "Instrument code: BU - Accuracy: 0.326531\n", "Instrument code: C - Accuracy: 0.631579\n", "Instrument code: CF - Accuracy: 0.602273\n", "Instrument code: CJ - Accuracy: 0.604651\n", "Instrument code: CS - Accuracy: 0.727273\n", "Instrument code: CY - Accuracy: 0.666667\n", "Instrument code: EB - Accuracy: 0.75\n", "Instrument code: EG - Accuracy: 0.54\n", "Instrument code: FG - Accuracy: 0.647059\n", "Instrument code: HC - Accuracy: 0.509804\n", "Instrument code: I - Accuracy: 0.636364\n", "Instrument code: JD - Accuracy: 0.378378\n", "Instrument code: L - Accuracy: 0.512195\n", "Instrument code: M - Accuracy: 0.627907\n", "Instrument code: MA - Accuracy: 0.61194\n", "Instrument code: OI - Accuracy: 0.62\n", "Instrument code: P - Accuracy: 0.511111\n", "Instrument code: PF - Accuracy: 0.516129\n", "Instrument code: PP - Accuracy: 0.55102\n", "Instrument code: RB - Accuracy: 0.431818\n", "Instrument code: RM - Accuracy: 0.707317\n", "Instrument code: RU - Accuracy: 0.764706\n", "Instrument code: SA - Accuracy: 0.614035\n", "Instrument code: SF - Accuracy: 0.469388\n", "Instrument code: SM - Accuracy: 0.639344\n", "Instrument code: SR - Accuracy: 0.521739\n", "Instrument code: TA - Accuracy: 0.525424\n", "Instrument code: UR - Accuracy: 0.464286\n", "Instrument code: V - Accuracy: 0.653061\n", "Instrument code: Y - Accuracy: 0.6\n", "Instrument code: JM - Accuracy: 0.615385\n", "Instrument code: AL - Accuracy: 0.357143\n", "Instrument code: SP - Accuracy: 0.5\n", "Instrument code: ZN - Accuracy: 0.2\n", "------------------------------\n", "OOF Accuracy: 0.570687\n", "------------------------------\n"]}], "source": ["trainer.inference(df_train)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}