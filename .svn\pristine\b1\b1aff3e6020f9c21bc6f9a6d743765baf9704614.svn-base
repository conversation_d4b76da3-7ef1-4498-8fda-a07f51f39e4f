{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"d:/QuantLab\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d:\\QuantLab\n"]}], "source": ["ds=DataSource(RunMode.passive)\n", "print(ds.get_run_dir())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[574.5 606.  603.5 613.  608.5 610.  601.  591.  591.  604.  591.5 596.5\n", "  600.  595.5 595.  594.  596.  603.  607.  613.  615.  615.5 629.5 612.\n", "  612.5 610.  621.5 616.  612.  619.5 621.  621.  625.5 605.  563.5 574.5\n", "  566.5 584.5 582.5 580.  596.  605.  607.  608.  627.5 625.  630.  647.\n", "  648.5 642.  642.  623.  616.  602.  631.  626.5 634.  635.5 621.5 611.5\n", "  630.5 620.5 627.5 633.5 618.  621.  623.5 615.  591.  564.  588.5 598.\n", "  593.  588.  570.  574.5 557.5 577.5 568.  569.5 584.  597.5 599.5 596.\n", "  606.5 606.5 606.  612.  620.  602.  613.  604.5 607.  602.  594.5 595.5\n", "  610.  610.5 623.  633.  631. ]]\n"]}], "source": ["symbol = 'I2009.DC'\n", "hist_data=ds.get_history_data(symbol, 100, [BarData.close], BarSize.day, DoRight.forward)\n", "print(hist_data)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['IC2005.SF', 'IF2005.SF', 'IH2005.SF', 'AP2010.ZC', 'CF2009.ZC', 'CJ2009.ZC', 'CY2009.ZC', 'FG2009.ZC', 'MA2009.ZC', 'OI2009.ZC', 'RM2009.ZC', 'SA2009.ZC', 'SF2009.ZC', 'SM2009.ZC', 'SR2009.ZC', 'TA2009.ZC', 'UR2009.ZC', 'A2009.DC', 'C2009.DC', 'CS2009.DC', 'EB2009.DC', 'EG2009.DC', 'I2009.DC', 'J2009.DC', 'JD2006.DC', 'JM2009.DC', 'L2009.DC', 'M2009.DC', 'P2009.DC', 'PP2009.DC', 'RR2009.DC', 'V2009.DC', 'Y2009.DC', 'AG2006.SC', 'AL2007.SC', 'AU2006.SC', 'BU2006.SC', 'CU2006.SC', 'HC2010.SC', 'NI2007.SC', 'NR2007.SC', 'PB2006.SC', 'RB2010.SC', 'RU2009.SC', 'SC2007.SC', 'SN2007.SC', 'SP2009.SC', 'SS2007.SC', 'ZN2007.SC']\n"]}], "source": ["zlqh = ds.get_block_data(\"ZLQH\")\n", "print(zlqh)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>LH9999.DC</td>\n", "      <td>2021-05-19 15:00:00</td>\n", "      <td>24515.0</td>\n", "      <td>24515.0</td>\n", "      <td>23550.0</td>\n", "      <td>23550.0</td>\n", "      <td>9747</td>\n", "      <td>2.198217e-41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>LH9999.DC</td>\n", "      <td>2021-05-20 15:00:00</td>\n", "      <td>23400.0</td>\n", "      <td>23870.0</td>\n", "      <td>23080.0</td>\n", "      <td>23565.0</td>\n", "      <td>7762</td>\n", "      <td>2.177197e-41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>LH9999.DC</td>\n", "      <td>2021-05-21 15:00:00</td>\n", "      <td>23500.0</td>\n", "      <td>24200.0</td>\n", "      <td>23380.0</td>\n", "      <td>23890.0</td>\n", "      <td>7302</td>\n", "      <td>2.103349e-41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>LH9999.DC</td>\n", "      <td>2021-05-24 15:00:00</td>\n", "      <td>23880.0</td>\n", "      <td>24050.0</td>\n", "      <td>23430.0</td>\n", "      <td>23940.0</td>\n", "      <td>5008</td>\n", "      <td>2.106011e-41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LH9999.DC</td>\n", "      <td>2021-05-25 15:00:00</td>\n", "      <td>24050.0</td>\n", "      <td>24300.0</td>\n", "      <td>23620.0</td>\n", "      <td>23895.0</td>\n", "      <td>5569</td>\n", "      <td>2.063832e-41</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            code            datetime     open     high      low    close  \\\n", "index                                                                      \n", "0      LH9999.DC 2021-05-19 15:00:00  24515.0  24515.0  23550.0  23550.0   \n", "1      LH9999.DC 2021-05-20 15:00:00  23400.0  23870.0  23080.0  23565.0   \n", "2      LH9999.DC 2021-05-21 15:00:00  23500.0  24200.0  23380.0  23890.0   \n", "3      LH9999.DC 2021-05-24 15:00:00  23880.0  24050.0  23430.0  23940.0   \n", "4      LH9999.DC 2021-05-25 15:00:00  24050.0  24300.0  23620.0  23895.0   \n", "\n", "       volume        amount  \n", "index                        \n", "0        9747  2.198217e-41  \n", "1        7762  2.177197e-41  \n", "2        7302  2.103349e-41  \n", "3        5008  2.106011e-41  \n", "4        5569  2.063832e-41  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# df = pd.read_parquet(\"e:/featdata/ffs_sf.main.2024.parquet\", engine='fastparquet')\n", "# df = pd.read_parquet(\"f:/hqdata/tick/2023/202306.parquet\", engine='fastparquet')\n", "df = pd.read_parquet(\"d:/RoboQuant2/store/fut_day.parquet\", engine='fastparquet')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df = df[df['code'] == 'SN9999.SC']"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A9999.DC</td>\n", "      <td>2020-01-02 15:00:00</td>\n", "      <td>3856.0</td>\n", "      <td>3876.0</td>\n", "      <td>3847.0</td>\n", "      <td>3861.0</td>\n", "      <td>29163</td>\n", "      <td>1.082573e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A9999.DC</td>\n", "      <td>2020-01-03 15:00:00</td>\n", "      <td>3861.0</td>\n", "      <td>3893.0</td>\n", "      <td>3860.0</td>\n", "      <td>3877.0</td>\n", "      <td>41752</td>\n", "      <td>1.098478e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A9999.DC</td>\n", "      <td>2020-01-06 15:00:00</td>\n", "      <td>3873.0</td>\n", "      <td>3911.0</td>\n", "      <td>3864.0</td>\n", "      <td>3909.0</td>\n", "      <td>47803</td>\n", "      <td>1.107180e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A9999.DC</td>\n", "      <td>2020-01-07 15:00:00</td>\n", "      <td>3915.0</td>\n", "      <td>3943.0</td>\n", "      <td>3906.0</td>\n", "      <td>3916.0</td>\n", "      <td>56399</td>\n", "      <td>1.142703e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A9999.DC</td>\n", "      <td>2020-01-08 15:00:00</td>\n", "      <td>3916.0</td>\n", "      <td>3965.0</td>\n", "      <td>3907.0</td>\n", "      <td>3954.0</td>\n", "      <td>52739</td>\n", "      <td>1.188918e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>926</th>\n", "      <td>ZN9999.SC</td>\n", "      <td>2024-03-28 15:00:00</td>\n", "      <td>20730.0</td>\n", "      <td>20955.0</td>\n", "      <td>20675.0</td>\n", "      <td>20880.0</td>\n", "      <td>108591</td>\n", "      <td>1.338632e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>927</th>\n", "      <td>ZN9999.SC</td>\n", "      <td>2024-03-29 15:00:00</td>\n", "      <td>20795.0</td>\n", "      <td>20960.0</td>\n", "      <td>20790.0</td>\n", "      <td>20945.0</td>\n", "      <td>85016</td>\n", "      <td>1.297771e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>928</th>\n", "      <td>ZN9999.SC</td>\n", "      <td>2024-04-01 15:00:00</td>\n", "      <td>20900.0</td>\n", "      <td>21065.0</td>\n", "      <td>20835.0</td>\n", "      <td>21045.0</td>\n", "      <td>80852</td>\n", "      <td>1.291170e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>929</th>\n", "      <td>ZN9999.SC</td>\n", "      <td>2024-04-02 15:00:00</td>\n", "      <td>21045.0</td>\n", "      <td>21165.0</td>\n", "      <td>20975.0</td>\n", "      <td>21115.0</td>\n", "      <td>84005</td>\n", "      <td>1.262178e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>930</th>\n", "      <td>ZN9999.SC</td>\n", "      <td>2024-04-03 15:00:00</td>\n", "      <td>21230.0</td>\n", "      <td>21350.0</td>\n", "      <td>21175.0</td>\n", "      <td>21270.0</td>\n", "      <td>101648</td>\n", "      <td>1.159084e-40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>50790 rows × 8 columns</p>\n", "</div>"], "text/plain": ["            code            datetime     open     high      low    close  \\\n", "index                                                                      \n", "0       A9999.DC 2020-01-02 15:00:00   3856.0   3876.0   3847.0   3861.0   \n", "1       A9999.DC 2020-01-03 15:00:00   3861.0   3893.0   3860.0   3877.0   \n", "2       A9999.DC 2020-01-06 15:00:00   3873.0   3911.0   3864.0   3909.0   \n", "3       A9999.DC 2020-01-07 15:00:00   3915.0   3943.0   3906.0   3916.0   \n", "4       A9999.DC 2020-01-08 15:00:00   3916.0   3965.0   3907.0   3954.0   \n", "...          ...                 ...      ...      ...      ...      ...   \n", "926    ZN9999.SC 2024-03-28 15:00:00  20730.0  20955.0  20675.0  20880.0   \n", "927    ZN9999.SC 2024-03-29 15:00:00  20795.0  20960.0  20790.0  20945.0   \n", "928    ZN9999.SC 2024-04-01 15:00:00  20900.0  21065.0  20835.0  21045.0   \n", "929    ZN9999.SC 2024-04-02 15:00:00  21045.0  21165.0  20975.0  21115.0   \n", "930    ZN9999.SC 2024-04-03 15:00:00  21230.0  21350.0  21175.0  21270.0   \n", "\n", "       volume        amount  \n", "index                        \n", "0       29163  1.082573e-40  \n", "1       41752  1.098478e-40  \n", "2       47803  1.107180e-40  \n", "3       56399  1.142703e-40  \n", "4       52739  1.188918e-40  \n", "...       ...           ...  \n", "926    108591  1.338632e-40  \n", "927     85016  1.297771e-40  \n", "928     80852  1.291170e-40  \n", "929     84005  1.262178e-40  \n", "930    101648  1.159084e-40  \n", "\n", "[50790 rows x 8 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 按code,日期排序\n", "df = df.sort_values(by=['code', 'datetime'])\n", "df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# 保存到parquet文件\n", "df.to_parquet(\"d:/RoboQuant2/store/fut_day.parquet\", engine='fastparquet')\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}