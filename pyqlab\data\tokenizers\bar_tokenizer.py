"""
BarTokenizer - ATR标准化K线数据Token化器

基于ATR（平均真实波幅）标准化的K线数据Token化器，支持多周期、多种映射方法，
并通过多种策略解决token分布不平衡问题。

主要特性：
1. ATR标准化：使用ATR值对价格变化进行标准化
2. 多周期支持：支持不同时间周期的K线数据
3. 多种映射方法：线性映射、非线性映射、分位数映射等
4. 平衡策略：解决token分布不平衡问题
5. 可扩展性：支持自定义映射函数和平衡策略
"""

import numpy as np
import pandas as pd
import talib
from typing import Dict, List, Tuple, Optional, Union, Callable
from abc import ABC, abstractmethod
from sklearn.preprocessing import QuantileTransformer
from scipy import stats
import warnings
warnings.filterwarnings('ignore')


class MappingStrategy(ABC):
    """映射策略抽象基类"""

    @abstractmethod
    def fit(self, data: np.ndarray) -> 'MappingStrategy':
        """拟合映射参数"""
        pass

    @abstractmethod
    def transform(self, data: np.ndarray) -> np.ndarray:
        """应用映射变换"""
        pass

    @abstractmethod
    def inverse_transform(self, tokens: np.ndarray) -> np.ndarray:
        """逆映射变换"""
        pass


class LinearMapping(MappingStrategy):
    """线性映射策略"""

    def __init__(self, n_bins: int = 100, clip_std: float = 3.0):
        self.n_bins = n_bins
        self.clip_std = clip_std
        self.min_val = None
        self.max_val = None
        self.bin_edges = None

    def fit(self, data: np.ndarray) -> 'LinearMapping':
        # 使用标准差裁剪异常值
        mean_val = np.mean(data)
        std_val = np.std(data)
        self.min_val = mean_val - self.clip_std * std_val
        self.max_val = mean_val + self.clip_std * std_val

        # 创建等间距的bin边界
        self.bin_edges = np.linspace(self.min_val, self.max_val, self.n_bins + 1)
        return self

    def transform(self, data: np.ndarray) -> np.ndarray:
        # 裁剪数据到指定范围
        clipped_data = np.clip(data, self.min_val, self.max_val)
        # 数字化到bins
        tokens = np.digitize(clipped_data, self.bin_edges) - 1
        tokens = np.clip(tokens, 0, self.n_bins - 1)
        return tokens

    def inverse_transform(self, tokens: np.ndarray) -> np.ndarray:
        # 使用bin中心值进行逆变换
        bin_centers = (self.bin_edges[:-1] + self.bin_edges[1:]) / 2
        return bin_centers[tokens]


class QuantileMapping(MappingStrategy):
    """分位数映射策略"""

    def __init__(self, n_bins: int = 100, output_distribution: str = 'uniform'):
        self.n_bins = n_bins
        self.output_distribution = output_distribution
        self.quantile_transformer = QuantileTransformer(
            n_quantiles=min(n_bins, 1000),
            output_distribution=output_distribution,
            subsample=100000
        )
        self.bin_edges = None

    def fit(self, data: np.ndarray) -> 'QuantileMapping':
        # 拟合分位数变换器
        self.quantile_transformer.fit(data.reshape(-1, 1))

        # 创建均匀分布的bin边界
        if self.output_distribution == 'uniform':
            self.bin_edges = np.linspace(0, 1, self.n_bins + 1)
        else:  # normal
            self.bin_edges = np.linspace(-3, 3, self.n_bins + 1)

        return self

    def transform(self, data: np.ndarray) -> np.ndarray:
        # 应用分位数变换
        transformed = self.quantile_transformer.transform(data.reshape(-1, 1)).flatten()
        # 数字化到bins
        tokens = np.digitize(transformed, self.bin_edges) - 1
        tokens = np.clip(tokens, 0, self.n_bins - 1)
        return tokens

    def inverse_transform(self, tokens: np.ndarray) -> np.ndarray:
        # 使用bin中心值
        bin_centers = (self.bin_edges[:-1] + self.bin_edges[1:]) / 2
        transformed_values = bin_centers[tokens]
        # 逆分位数变换
        original_values = self.quantile_transformer.inverse_transform(
            transformed_values.reshape(-1, 1)
        ).flatten()
        return original_values


class AdaptiveMapping(MappingStrategy):
    """自适应映射策略 - 基于数据分布自动调整bin大小"""

    def __init__(self, n_bins: int = 100, balance_factor: float = 0.5):
        self.n_bins = n_bins
        self.balance_factor = balance_factor  # 0: 完全等频, 1: 完全等宽
        self.bin_edges = None
        self.bin_centers = None

    def fit(self, data: np.ndarray) -> 'AdaptiveMapping':
        # 计算等频分位点
        quantiles = np.linspace(0, 1, self.n_bins + 1)
        equal_freq_edges = np.percentile(data, quantiles * 100)

        # 计算等宽边界
        equal_width_edges = np.linspace(np.min(data), np.max(data), self.n_bins + 1)

        # 混合两种策略
        self.bin_edges = (
            self.balance_factor * equal_width_edges +
            (1 - self.balance_factor) * equal_freq_edges
        )

        # 确保边界单调递增
        self.bin_edges = np.sort(np.unique(self.bin_edges))

        # 如果unique后bin数量不足，补充
        if len(self.bin_edges) < self.n_bins + 1:
            additional_edges = np.linspace(
                self.bin_edges[-2], self.bin_edges[-1],
                self.n_bins + 1 - len(self.bin_edges) + 2
            )[1:-1]
            self.bin_edges = np.sort(np.concatenate([self.bin_edges[:-1], additional_edges, [self.bin_edges[-1]]]))

        self.bin_centers = (self.bin_edges[:-1] + self.bin_edges[1:]) / 2
        return self

    def transform(self, data: np.ndarray) -> np.ndarray:
        tokens = np.digitize(data, self.bin_edges) - 1
        tokens = np.clip(tokens, 0, len(self.bin_centers) - 1)
        return tokens

    def inverse_transform(self, tokens: np.ndarray) -> np.ndarray:
        return self.bin_centers[tokens]


class BalancingStrategy(ABC):
    """平衡策略抽象基类"""

    @abstractmethod
    def apply(self, tokens: np.ndarray, target_distribution: str = 'uniform') -> np.ndarray:
        """应用平衡策略"""
        pass


class FrequencyBalancing(BalancingStrategy):
    """频率平衡策略"""

    def __init__(self, smoothing_factor: float = 0.1):
        self.smoothing_factor = smoothing_factor
        self.token_mapping = None

    def apply(self, tokens: np.ndarray, target_distribution: str = 'uniform') -> np.ndarray:
        # 计算token频率
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        total_count = len(tokens)

        # 计算目标频率
        if target_distribution == 'uniform':
            target_freq = np.ones(len(unique_tokens)) / len(unique_tokens)
        elif target_distribution == 'normal':
            # 使用正态分布作为目标
            z_scores = stats.norm.ppf(np.linspace(0.01, 0.99, len(unique_tokens)))
            target_freq = stats.norm.pdf(z_scores)
            target_freq = target_freq / np.sum(target_freq)
        else:
            raise ValueError(f"Unsupported target distribution: {target_distribution}")

        # 计算当前频率
        current_freq = counts / total_count

        # 计算重映射权重
        weights = target_freq / (current_freq + self.smoothing_factor)
        weights = weights / np.sum(weights) * len(unique_tokens)

        # 创建token映射
        self.token_mapping = dict(zip(unique_tokens, weights))

        # 应用重采样（这里简化为返回原tokens，实际应用中可以根据权重重采样）
        return tokens


class BarTokenizer:
    """
    基于ATR标准化的K线数据Token化器

    支持多周期、多种映射方法，并解决token分布不平衡问题
    """

    def __init__(
        self,
        atr_period: int = 14,
        mapping_strategy: str = 'adaptive',
        balancing_strategy: str = 'frequency',
        n_bins: int = 100,
        features: List[str] = None,
        combination_method: str = 'enhanced',
        target_vocab_size: int = None,
        **kwargs
    ):
        """
        初始化BarTokenizer

        Args:
            atr_period: ATR计算周期
            mapping_strategy: 映射策略 ('linear', 'quantile', 'adaptive')
            balancing_strategy: 平衡策略 ('frequency', 'none')
            n_bins: token数量
            features: 要token化的特征列表
            combination_method: 特征组合方法 ('independent', 'hash', 'hierarchical')
            target_vocab_size: 目标词汇表大小（用于hash方法）
            **kwargs: 其他参数
        """
        self.atr_period = atr_period
        self.mapping_strategy_name = mapping_strategy
        self.balancing_strategy_name = balancing_strategy
        self.n_bins = n_bins
        self.features = features or ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        self.combination_method = combination_method
        self.target_vocab_size = target_vocab_size

        # 初始化策略
        self.mapping_strategies = {}
        self.balancing_strategy = self._create_balancing_strategy()

        # 存储拟合参数
        self.is_fitted = False
        self.vocab_size = self._calculate_vocab_size()
        self.feature_mappers = {}

        # 统计信息
        self.token_distribution = None
        self.gini_coefficient = None

    def _calculate_vocab_size(self) -> int:
        """
        计算词汇表大小

        提供多种计算方式以获得更大的词汇表：
        - independent: 线性增长 (n_bins * features)
        - multiplicative: 乘法组合 (n_bins ^ features)
        - hybrid: 混合方式 (部分乘法 + 部分线性)
        - hash: 哈希映射到固定大小
        - hierarchical: 分层组合
        - enhanced: 增强版，提供更大词汇表
        """
        if self.combination_method == 'independent':
            # 独立特征：线性增长 (较小的词汇表)
            return self.n_bins * len(self.features)

        elif self.combination_method == 'multiplicative':
            # 乘法组合：指数增长 (较大的词汇表)
            return self.n_bins ** len(self.features)

        elif self.combination_method == 'hybrid':
            # 混合方式：部分特征乘法组合，部分线性
            if len(self.features) <= 2:
                return self.n_bins ** len(self.features)
            else:
                # 前2个特征乘法组合，其余线性
                core_vocab = self.n_bins ** 2
                aux_vocab = self.n_bins * (len(self.features) - 2)
                return core_vocab + aux_vocab

        elif self.combination_method == 'enhanced':
            # 增强版：提供更大的词汇表
            if len(self.features) <= 3:
                # 少于等于3个特征时使用完全乘法
                return self.n_bins ** len(self.features)
            else:
                # 多于3个特征时使用分组策略
                # 核心特征组(前3个): 乘法组合
                core_vocab = self.n_bins ** 3
                # 辅助特征: 每个特征独立映射
                aux_features = len(self.features) - 3
                aux_vocab = self.n_bins * aux_features
                return core_vocab + aux_vocab

        elif self.combination_method == 'hash':
            # 哈希映射：固定大小
            return self.target_vocab_size or (self.n_bins ** min(len(self.features), 3))

        elif self.combination_method == 'hierarchical':
            # 分层组合：核心特征组合 + 辅助特征
            core_features = min(2, len(self.features))  # 最多2个核心特征
            return (self.n_bins ** core_features) * len(self.features)
        else:
            # 向后兼容：原始方法（不推荐）
            return self.n_bins ** len(self.features)

    def _create_mapping_strategy(self, feature: str) -> MappingStrategy:
        """创建映射策略实例"""
        if self.mapping_strategy_name == 'linear':
            return LinearMapping(n_bins=self.n_bins)
        elif self.mapping_strategy_name == 'quantile':
            return QuantileMapping(n_bins=self.n_bins)
        elif self.mapping_strategy_name == 'adaptive':
            return AdaptiveMapping(n_bins=self.n_bins)
        else:
            raise ValueError(f"Unsupported mapping strategy: {self.mapping_strategy_name}")

    def _create_balancing_strategy(self) -> BalancingStrategy:
        """创建平衡策略实例"""
        if self.balancing_strategy_name == 'frequency':
            return FrequencyBalancing()
        elif self.balancing_strategy_name == 'none':
            return None
        else:
            raise ValueError(f"Unsupported balancing strategy: {self.balancing_strategy_name}")

    def _calculate_atr(self, df: pd.DataFrame) -> pd.Series:
        """计算ATR（平均真实波幅）"""
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values

        # 使用talib计算ATR
        atr = talib.ATR(high, low, close, timeperiod=self.atr_period)

        # 填充NaN值
        atr = pd.Series(atr, index=df.index)
        atr = atr.bfill().fillna(atr.mean())

        return atr

    def _extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """提取K线特征"""
        features_df = pd.DataFrame(index=df.index)

        # 计算ATR用于标准化
        atr = self._calculate_atr(df)

        # 价格变化特征
        if 'change' in self.features:
            features_df['change'] = (df['close'] - df['close'].shift(1)) / atr

        # K线实体
        if 'body' in self.features:
            features_df['body'] = (df['close'] - df['open']) / atr

        # 上影线
        if 'upper_shadow' in self.features:
            features_df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / atr

        # 下影线
        if 'lower_shadow' in self.features:
            features_df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / atr

        # 成交量比率
        if 'volume_ratio' in self.features:
            volume_ma = df['volume'].rolling(window=20).mean()
            features_df['volume_ratio'] = np.log1p(df['volume'] / volume_ma.fillna(volume_ma.mean()))

        # 波动率特征
        if 'volatility' in self.features:
            returns = df['close'].pct_change()
            volatility = returns.rolling(window=20).std()
            features_df['volatility'] = volatility / volatility.rolling(window=100).mean()

        # 相对强弱指标
        if 'rsi' in self.features:
            rsi = talib.RSI(df['close'].values, timeperiod=14)
            features_df['rsi'] = (rsi - 50) / 50  # 标准化到[-1, 1]

        # 布林带位置
        if 'bb_position' in self.features:
            bb_upper, _, bb_lower = talib.BBANDS(df['close'].values, timeperiod=20)
            bb_position = (df['close'] - bb_lower) / (bb_upper - bb_lower)
            features_df['bb_position'] = (bb_position - 0.5) * 2  # 标准化到[-1, 1]

        # 移除NaN值
        features_df = features_df.fillna(0)

        return features_df

    def fit(self, df: pd.DataFrame) -> 'BarTokenizer':
        """
        拟合tokenizer参数

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            self
        """
        # 提取特征
        features_df = self._extract_features(df)

        # 为每个特征创建并拟合映射策略
        for feature in self.features:
            if feature in features_df.columns:
                mapper = self._create_mapping_strategy(feature)
                mapper.fit(features_df[feature].values)
                self.feature_mappers[feature] = mapper

        # 设置为已拟合状态
        self.is_fitted = True

        # 计算初始token分布以评估平衡性
        sample_tokens = self.transform(df)
        self._calculate_distribution_metrics(sample_tokens)

        return self

    def transform(self, df: pd.DataFrame) -> np.ndarray:
        """
        将K线数据转换为tokens

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            tokens: shape (n_samples,) 的token数组
        """
        if not self.is_fitted:
            raise ValueError("Tokenizer must be fitted before transform")

        # 提取特征
        features_df = self._extract_features(df)

        # 对每个特征进行token化
        feature_tokens = {}
        for feature in self.features:
            if feature in features_df.columns and feature in self.feature_mappers:
                tokens = self.feature_mappers[feature].transform(features_df[feature].values)
                feature_tokens[feature] = tokens

        # 组合多个特征的tokens
        combined_tokens = self._combine_feature_tokens(feature_tokens)

        # 应用平衡策略
        if self.balancing_strategy is not None:
            combined_tokens = self.balancing_strategy.apply(combined_tokens)

        return combined_tokens

    def _combine_feature_tokens(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """组合多个特征的tokens"""
        if len(feature_tokens) == 1:
            return list(feature_tokens.values())[0]

        if self.combination_method == 'independent':
            return self._independent_combine(feature_tokens)
        elif self.combination_method == 'multiplicative':
            return self._multiplicative_combine(feature_tokens)
        elif self.combination_method == 'hybrid':
            return self._hybrid_combine(feature_tokens)
        elif self.combination_method == 'enhanced':
            return self._enhanced_combine(feature_tokens)
        elif self.combination_method == 'hash':
            return self._hash_combine(feature_tokens)
        elif self.combination_method == 'hierarchical':
            return self._hierarchical_combine(feature_tokens)
        else:
            # 向后兼容：原始方法（不推荐）
            return self._legacy_combine(feature_tokens)

    def _independent_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """独立特征组合：使用偏移量区分不同特征"""
        combined = np.zeros(len(list(feature_tokens.values())[0]), dtype=np.int32)
        offset = 0

        for feature in self.features:
            if feature in feature_tokens:
                combined += feature_tokens[feature] + offset
                offset += self.n_bins

        return combined

    def _multiplicative_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """乘法组合：完全乘法组合所有特征"""
        combined = np.zeros(len(list(feature_tokens.values())[0]), dtype=np.int32)

        for i, feature in enumerate(self.features):
            if feature in feature_tokens:
                combined += feature_tokens[feature] * (self.n_bins ** i)

        return combined

    def _hybrid_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """混合组合：前2个特征乘法组合，其余线性组合"""
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)

        if len(self.features) <= 2:
            # 少于等于2个特征，使用乘法组合
            return self._multiplicative_combine(feature_tokens)
        else:
            # 前2个特征乘法组合
            core_features = self.features[:2]
            core_combined = np.zeros(n_samples, dtype=np.int32)
            for i, feature in enumerate(core_features):
                if feature in feature_tokens:
                    core_combined += feature_tokens[feature] * (self.n_bins ** i)

            # 其余特征线性组合
            aux_features = self.features[2:]
            aux_combined = np.zeros(n_samples, dtype=np.int32)
            for i, feature in enumerate(aux_features):
                if feature in feature_tokens:
                    aux_combined += feature_tokens[feature] + i * self.n_bins

            # 组合结果
            core_vocab_size = self.n_bins ** 2
            combined = core_combined + aux_combined * core_vocab_size

        return combined

    def _enhanced_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """增强组合：前3个特征乘法组合，其余线性组合"""
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)

        if len(self.features) <= 3:
            # 少于等于3个特征，使用完全乘法组合
            return self._multiplicative_combine(feature_tokens)
        else:
            # 前3个特征乘法组合
            core_features = self.features[:3]
            core_combined = np.zeros(n_samples, dtype=np.int32)
            for i, feature in enumerate(core_features):
                if feature in feature_tokens:
                    core_combined += feature_tokens[feature] * (self.n_bins ** i)

            # 其余特征线性组合
            aux_features = self.features[3:]
            aux_combined = np.zeros(n_samples, dtype=np.int32)
            for i, feature in enumerate(aux_features):
                if feature in feature_tokens:
                    aux_combined += feature_tokens[feature] + i * self.n_bins

            # 组合结果
            core_vocab_size = self.n_bins ** 3
            combined = core_combined + aux_combined * core_vocab_size

        return combined

    def _hash_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """哈希组合：将特征组合映射到固定范围"""
        combined = []
        n_samples = len(list(feature_tokens.values())[0])

        for i in range(n_samples):
            # 构建特征元组
            feature_tuple = tuple(
                feature_tokens[feature][i] if feature in feature_tokens else 0
                for feature in self.features
            )
            # 使用哈希函数映射到目标词汇表大小
            hash_value = hash(feature_tuple) % self.vocab_size
            combined.append(hash_value)

        return np.array(combined, dtype=np.int32)

    def _hierarchical_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """分层组合：核心特征组合 + 辅助特征"""
        # 定义核心特征（最重要的2个）
        core_features = ['change', 'body']
        aux_features = [f for f in self.features if f not in core_features]

        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)

        # 核心特征组合（使用原始方法）
        core_combined = np.zeros(n_samples, dtype=np.int32)
        for i, feature in enumerate(core_features):
            if feature in feature_tokens:
                core_combined += feature_tokens[feature] * (self.n_bins ** i)

        # 辅助特征求和
        aux_sum = np.zeros(n_samples, dtype=np.int32)
        for feature in aux_features:
            if feature in feature_tokens:
                aux_sum += feature_tokens[feature]

        # 最终组合
        if len(aux_features) > 0:
            aux_sum = aux_sum % len(aux_features)
            combined = core_combined * len(aux_features) + aux_sum
        else:
            combined = core_combined

        return combined

    def _legacy_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """原始组合方法（向后兼容，不推荐使用）"""
        combined = np.zeros(len(list(feature_tokens.values())[0]), dtype=np.int32)

        for i, (_, tokens) in enumerate(feature_tokens.items()):
            combined += tokens * (self.n_bins ** i)

        return combined

    def inverse_transform(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """
        将tokens逆变换为特征值

        Args:
            tokens: token数组

        Returns:
            features: 特征字典
        """
        if not self.is_fitted:
            raise ValueError("Tokenizer must be fitted before inverse_transform")

        # 分解组合的tokens
        feature_tokens = self._decompose_tokens(tokens)

        # 对每个特征进行逆变换
        features = {}
        for feature, feature_token in feature_tokens.items():
            if feature in self.feature_mappers:
                features[feature] = self.feature_mappers[feature].inverse_transform(feature_token)

        return features

    def _decompose_tokens(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """分解组合的tokens"""
        if self.combination_method == 'independent':
            return self._independent_decompose(tokens)
        elif self.combination_method == 'multiplicative':
            return self._multiplicative_decompose(tokens)
        elif self.combination_method == 'hybrid':
            return self._hybrid_decompose(tokens)
        elif self.combination_method == 'enhanced':
            return self._enhanced_decompose(tokens)
        elif self.combination_method == 'hash':
            return self._hash_decompose(tokens)
        elif self.combination_method == 'hierarchical':
            return self._hierarchical_decompose(tokens)
        else:
            # 向后兼容：原始方法
            return self._legacy_decompose(tokens)

    def _independent_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """独立特征分解"""
        feature_tokens = {}

        for i, feature in enumerate(self.features):
            if feature in self.feature_mappers:
                # 提取对应特征的token值
                offset = i * self.n_bins
                feature_tokens[feature] = (tokens - offset) % self.n_bins
                # 注意：这是简化的逆变换，可能不完全准确
                # 更准确的方法需要存储原始的特征token值

        return feature_tokens

    def _multiplicative_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """乘法分解"""
        feature_tokens = {}
        remaining = tokens.copy()

        for i, feature in enumerate(self.features):
            if feature in self.feature_mappers:
                feature_tokens[feature] = remaining % self.n_bins
                remaining = remaining // self.n_bins

        return feature_tokens

    def _hybrid_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """混合分解"""
        feature_tokens = {}

        if len(self.features) <= 2:
            # 少于等于2个特征，使用乘法分解
            return self._multiplicative_decompose(tokens)
        else:
            # 分离核心特征和辅助特征
            core_vocab_size = self.n_bins ** 2
            aux_combined = tokens // core_vocab_size
            core_combined = tokens % core_vocab_size

            # 分解核心特征（前2个）
            remaining = core_combined
            for i, feature in enumerate(self.features[:2]):
                if feature in self.feature_mappers:
                    feature_tokens[feature] = remaining % self.n_bins
                    remaining = remaining // self.n_bins

            # 分解辅助特征
            for i, feature in enumerate(self.features[2:]):
                if feature in self.feature_mappers:
                    feature_tokens[feature] = (aux_combined - i * self.n_bins) % self.n_bins

        return feature_tokens

    def _enhanced_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """增强分解"""
        feature_tokens = {}

        if len(self.features) <= 3:
            # 少于等于3个特征，使用乘法分解
            return self._multiplicative_decompose(tokens)
        else:
            # 分离核心特征和辅助特征
            core_vocab_size = self.n_bins ** 3
            aux_combined = tokens // core_vocab_size
            core_combined = tokens % core_vocab_size

            # 分解核心特征（前3个）
            remaining = core_combined
            for i, feature in enumerate(self.features[:3]):
                if feature in self.feature_mappers:
                    feature_tokens[feature] = remaining % self.n_bins
                    remaining = remaining // self.n_bins

            # 分解辅助特征
            for i, feature in enumerate(self.features[3:]):
                if feature in self.feature_mappers:
                    feature_tokens[feature] = (aux_combined - i * self.n_bins) % self.n_bins

        return feature_tokens

    def _hash_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """哈希分解（近似）"""
        # 哈希方法的逆变换是近似的，因为存在信息损失
        feature_tokens = {}

        for feature in self.features:
            if feature in self.feature_mappers:
                # 使用token值的简单映射作为近似
                feature_tokens[feature] = tokens % self.n_bins

        return feature_tokens

    def _hierarchical_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """分层分解"""
        feature_tokens = {}
        core_features = ['change', 'body']
        aux_features = [f for f in self.features if f not in core_features]

        if len(aux_features) > 0:
            # 分离辅助特征
            aux_sum = tokens % len(aux_features)
            core_combined = tokens // len(aux_features)

            # 分解核心特征
            remaining = core_combined
            for i, feature in enumerate(core_features):
                if feature in self.feature_mappers:
                    feature_tokens[feature] = remaining % self.n_bins
                    remaining = remaining // self.n_bins

            # 辅助特征（简化处理）
            for feature in aux_features:
                if feature in self.feature_mappers:
                    feature_tokens[feature] = aux_sum % self.n_bins
        else:
            # 只有核心特征
            remaining = tokens
            for i, feature in enumerate(core_features):
                if feature in self.feature_mappers:
                    feature_tokens[feature] = remaining % self.n_bins
                    remaining = remaining // self.n_bins

        return feature_tokens

    def _legacy_decompose(self, tokens: np.ndarray) -> Dict[str, np.ndarray]:
        """原始分解方法"""
        feature_tokens = {}
        remaining = tokens.copy()

        for i, feature in enumerate(self.features):
            if feature in self.feature_mappers:
                feature_tokens[feature] = remaining % self.n_bins
                remaining = remaining // self.n_bins

        return feature_tokens

    def _calculate_distribution_metrics(self, tokens: np.ndarray):
        """计算token分布的统计指标"""
        # 计算token分布
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        total_count = len(tokens)
        frequencies = counts / total_count

        # 计算基尼系数
        sorted_freq = np.sort(frequencies)
        n = len(sorted_freq)
        cumsum = np.cumsum(sorted_freq)
        self.gini_coefficient = (n + 1 - 2 * np.sum(cumsum)) / n

        # 存储分布信息
        self.token_distribution = {
            'unique_tokens': len(unique_tokens),
            'total_tokens': total_count,
            'frequencies': frequencies,
            'entropy': -np.sum(frequencies * np.log2(frequencies + 1e-10)),
            'max_frequency': np.max(frequencies),
            'min_frequency': np.min(frequencies)
        }

    def get_vocab_size(self) -> int:
        """获取词汇表大小"""
        return self.vocab_size

    def get_distribution_info(self) -> Dict:
        """获取token分布信息"""
        if self.token_distribution is None:
            return {}

        info = self.token_distribution.copy()
        info['gini_coefficient'] = self.gini_coefficient
        return info

    def fit_transform(self, df: pd.DataFrame) -> np.ndarray:
        """拟合并转换数据"""
        return self.fit(df).transform(df)

    def save_model(self, filepath: str):
        """保存模型参数"""
        import pickle

        model_data = {
            'atr_period': self.atr_period,
            'mapping_strategy_name': self.mapping_strategy_name,
            'balancing_strategy_name': self.balancing_strategy_name,
            'n_bins': self.n_bins,
            'features': self.features,
            'feature_mappers': self.feature_mappers,
            'is_fitted': self.is_fitted,
            'vocab_size': self.vocab_size,
            'token_distribution': self.token_distribution,
            'gini_coefficient': self.gini_coefficient
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)

    @classmethod
    def load_model(cls, filepath: str) -> 'BarTokenizer':
        """加载模型参数"""
        import pickle

        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)

        # 创建实例
        tokenizer = cls(
            atr_period=model_data['atr_period'],
            mapping_strategy=model_data['mapping_strategy_name'],
            balancing_strategy=model_data['balancing_strategy_name'],
            n_bins=model_data['n_bins'],
            features=model_data['features']
        )

        # 恢复状态
        tokenizer.feature_mappers = model_data['feature_mappers']
        tokenizer.is_fitted = model_data['is_fitted']
        tokenizer.vocab_size = model_data['vocab_size']
        tokenizer.token_distribution = model_data['token_distribution']
        tokenizer.gini_coefficient = model_data['gini_coefficient']

        return tokenizer

    def analyze_balance(self, tokens: np.ndarray, plot: bool = False) -> Dict:
        """分析token分布平衡性"""
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        frequencies = counts / len(tokens)

        # 计算各种平衡性指标
        metrics = {
            'gini_coefficient': self._calculate_gini(frequencies),
            'entropy': -np.sum(frequencies * np.log2(frequencies + 1e-10)),
            'max_entropy': np.log2(len(unique_tokens)),
            'normalized_entropy': (-np.sum(frequencies * np.log2(frequencies + 1e-10))) / np.log2(len(unique_tokens)),
            'coefficient_of_variation': np.std(frequencies) / np.mean(frequencies),
            'frequency_range': np.max(frequencies) - np.min(frequencies),
            'top_10_percent_share': np.sum(np.sort(frequencies)[-len(frequencies)//10:])
        }

        if plot:
            self._plot_distribution(frequencies, unique_tokens)

        return metrics

    def _calculate_gini(self, frequencies: np.ndarray) -> float:
        """计算基尼系数"""
        sorted_freq = np.sort(frequencies)
        n = len(sorted_freq)
        cumsum = np.cumsum(sorted_freq)
        return (n + 1 - 2 * np.sum(cumsum)) / n

    def _plot_distribution(self, frequencies: np.ndarray, tokens: np.ndarray):
        """绘制token分布图"""
        try:
            import matplotlib.pyplot as plt

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

            # 频率分布直方图
            ax1.hist(frequencies, bins=50, alpha=0.7, edgecolor='black')
            ax1.set_xlabel('Token Frequency')
            ax1.set_ylabel('Count')
            ax1.set_title('Token Frequency Distribution')
            ax1.grid(True, alpha=0.3)

            # 累积分布
            sorted_freq = np.sort(frequencies)[::-1]
            cumulative = np.cumsum(sorted_freq)
            ax2.plot(range(len(cumulative)), cumulative)
            ax2.set_xlabel('Token Rank')
            ax2.set_ylabel('Cumulative Frequency')
            ax2.set_title('Cumulative Token Distribution')
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

        except ImportError:
            print("Matplotlib not available for plotting")


# 多周期支持的扩展类
class MultiPeriodBarTokenizer:
    """
    多周期K线数据Token化器

    支持同时处理多个时间周期的K线数据，并生成统一的token表示
    """

    def __init__(
        self,
        periods: List[str] = None,
        base_tokenizer_config: Dict = None,
        combination_method: str = 'concatenate'
    ):
        """
        初始化多周期tokenizer

        Args:
            periods: 时间周期列表，如 ['1min', '5min', '15min', '1h', '1d']
            base_tokenizer_config: 基础tokenizer配置
            combination_method: 组合方法 ('concatenate', 'weighted', 'hierarchical')
        """
        self.periods = periods or ['5min', '15min', '1h']
        self.combination_method = combination_method
        self.base_config = base_tokenizer_config or {}

        # 为每个周期创建tokenizer
        self.tokenizers = {}
        for period in self.periods:
            config = self.base_config.copy()
            config['features'] = config.get('features', ['change', 'body', 'upper_shadow', 'lower_shadow'])
            self.tokenizers[period] = BarTokenizer(**config)

        self.is_fitted = False
        self.vocab_size = None

    def fit(self, data_dict: Dict[str, pd.DataFrame]) -> 'MultiPeriodBarTokenizer':
        """
        拟合多周期数据

        Args:
            data_dict: 字典，键为周期名，值为对应的DataFrame
        """
        for period in self.periods:
            if period in data_dict:
                self.tokenizers[period].fit(data_dict[period])

        # 计算组合后的词汇表大小
        if self.combination_method == 'concatenate':
            self.vocab_size = sum(tokenizer.get_vocab_size() for tokenizer in self.tokenizers.values())
        elif self.combination_method == 'weighted':
            self.vocab_size = max(tokenizer.get_vocab_size() for tokenizer in self.tokenizers.values())

        self.is_fitted = True
        return self

    def transform(self, data_dict: Dict[str, pd.DataFrame]) -> np.ndarray:
        """转换多周期数据为tokens"""
        if not self.is_fitted:
            raise ValueError("MultiPeriodBarTokenizer must be fitted before transform")

        period_tokens = {}
        for period in self.periods:
            if period in data_dict:
                tokens = self.tokenizers[period].transform(data_dict[period])
                period_tokens[period] = tokens

        # 组合不同周期的tokens
        return self._combine_period_tokens(period_tokens)

    def _combine_period_tokens(self, period_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """组合不同周期的tokens"""
        if self.combination_method == 'concatenate':
            # 简单拼接
            combined = []
            min_length = min(len(tokens) for tokens in period_tokens.values())

            for i in range(min_length):
                period_token_at_i = []
                for period in self.periods:
                    if period in period_tokens:
                        period_token_at_i.append(period_tokens[period][i])
                combined.append(tuple(period_token_at_i))

            return np.array(combined)

        elif self.combination_method == 'weighted':
            # 加权组合
            weights = {'5min': 0.5, '15min': 0.3, '1h': 0.2}  # 可配置
            combined = np.zeros(min(len(tokens) for tokens in period_tokens.values()))

            for period, tokens in period_tokens.items():
                weight = weights.get(period, 1.0 / len(period_tokens))
                combined += tokens * weight

            return combined.astype(np.int32)

        else:
            raise ValueError(f"Unsupported combination method: {self.combination_method}")


if __name__ == "__main__":
    # 示例用法
    import pandas as pd

    # 创建示例数据
    np.random.seed(42)
    n_samples = 1000

    dates = pd.date_range('2024-01-01', periods=n_samples, freq='5min')
    df = pd.DataFrame({
        'datetime': dates,
        'open': 100 + np.cumsum(np.random.randn(n_samples) * 0.1),
        'high': 0,
        'low': 0,
        'close': 0,
        'volume': np.random.randint(1000, 10000, n_samples)
    })

    # 生成OHLC数据
    for i in range(n_samples):
        base_price = df.loc[i, 'open']
        change = np.random.randn() * 0.5
        df.loc[i, 'close'] = base_price + change
        df.loc[i, 'high'] = max(df.loc[i, 'open'], df.loc[i, 'close']) + abs(np.random.randn() * 0.2)
        df.loc[i, 'low'] = min(df.loc[i, 'open'], df.loc[i, 'close']) - abs(np.random.randn() * 0.2)

    # 测试BarTokenizer
    print("=== BarTokenizer 测试 ===")

    # 测试不同的映射策略
    strategies = ['linear', 'quantile', 'adaptive']

    for strategy in strategies:
        print(f"\n--- 测试 {strategy} 映射策略 ---")

        tokenizer = BarTokenizer(
            mapping_strategy=strategy,
            balancing_strategy='frequency',
            n_bins=50,
            features=['change', 'body', 'upper_shadow', 'lower_shadow']
        )

        # 拟合和转换
        tokens = tokenizer.fit_transform(df)

        # 分析分布
        balance_metrics = tokenizer.analyze_balance(tokens)

        print(f"词汇表大小: {tokenizer.get_vocab_size()}")
        print(f"基尼系数: {balance_metrics['gini_coefficient']:.4f}")
        print(f"标准化熵: {balance_metrics['normalized_entropy']:.4f}")
        print(f"变异系数: {balance_metrics['coefficient_of_variation']:.4f}")

        # 测试逆变换
        reconstructed = tokenizer.inverse_transform(tokens[:10])
        print(f"逆变换特征数: {len(reconstructed)}")

    # 测试多周期tokenizer
    print("\n=== MultiPeriodBarTokenizer 测试 ===")

    # 创建多周期数据（简化示例）
    data_dict = {
        '5min': df,
        '15min': df[::3],  # 简化的15分钟数据
        '1h': df[::12]     # 简化的1小时数据
    }

    multi_tokenizer = MultiPeriodBarTokenizer(
        periods=['5min', '15min', '1h'],
        base_tokenizer_config={
            'mapping_strategy': 'adaptive',
            'n_bins': 30,
            'features': ['change', 'body']
        }
    )

    multi_tokens = multi_tokenizer.fit(data_dict).transform(data_dict)
    print(f"多周期tokens形状: {multi_tokens.shape}")
    print(f"多周期词汇表大小: {multi_tokenizer.vocab_size}")
