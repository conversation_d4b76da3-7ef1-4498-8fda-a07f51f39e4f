"""
历史数据准备 - 优化版本
支持数据质量检查、样本平衡处理和智能过滤
"""
import talib
import pandas as pd
import numpy as np
from datetime import datetime
from pyqlab.const import MAIN_FUT_CODES, SF_FUT_CODES
from argparse import ArgumentParser
import json
import sys, os
from collections import Counter
import matplotlib.pyplot as plt
sys.path.append("d:/QuantLab")
# from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode
from pyqlab.data.dataset.utils import get_vocab, to_bar

class HistoryDataToBar:
    """
    用于通过本地第三方(通达信)历史行情数据生成训练数据
    主要支持两个周期数据：1. 日数据   2. 5分钟数据
    支持两个市场：1. A股   2. 期货
    数据源来自通达信pytdx导出，保存数据格式为parquet
    将历史行情数据转换为Bar训练数据
    """
    def __init__(self, data_path: str, save_path: str, market: str, period: str, block_name: str, seq_length: int, year: int, start_date: str, end_date: str, special_token: bool, append: bool=False, enable_quality_check: bool=True, enable_balance: bool=True):
        self.data_path = data_path # d:/RoboQuant2
        self.save_path = save_path # f:/featdata/barenc
        self.market = market
        self.period = period
        self.seq_length = seq_length
        self.append = append
        self.year = year
        self.block_name = block_name
        self.start_date = start_date
        self.end_date = end_date
        self.special_token = special_token
        self.enable_quality_check = enable_quality_check
        self.enable_balance = enable_balance
        self.symbols = dict()
        self.atr = dict()

        # 数据质量统计
        self.quality_stats = {
            'total_bars': 0,
            'filtered_bars': 0,
            'gap_bars': 0,
            'invalid_atr_bars': 0,
            'token_distribution': Counter()
        }

        # 注意：要与系统中配置参数一致
        if self.period == 'day':
            self.window = 100
        else:
            self.window = 100
        self.scale = 10
        self.atr_mult = 0.88

        # 优化的参数设置
        self._setup_optimized_params()
        self._read_config()

    def _setup_optimized_params(self):
        """设置优化的参数"""
        # 根据周期调整参数
        if self.period in ['min1', 'min5']:
            # 短周期数据需要更严格的过滤
            self.max_gap_threshold = 5.0  # 最大跳空阈值(%)
            self.min_volume_threshold = 100  # 最小成交量阈值
            self.atr_outlier_threshold = 3.0  # ATR异常值阈值
            self.noise_filter_enabled = True
        else:
            # 日线数据相对稳定
            self.max_gap_threshold = 10.0
            self.min_volume_threshold = 0
            self.atr_outlier_threshold = 5.0
            self.noise_filter_enabled = False

        # Token平衡参数
        self.max_token_frequency = 0.15  # 单个token最大频率
        self.min_token_count = 10  # token最小出现次数
        self.balance_method = 'adaptive'  # 平衡方法: 'none', 'undersample', 'adaptive'

    def check_data_quality(self, df, symbol):
        """
        检查数据质量
        """
        if not self.enable_quality_check:
            return df

        print(f"检查 {symbol} 数据质量...")
        original_len = len(df)

        # 1. 检查基本数据完整性
        df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])

        # 2. 检查价格逻辑性
        invalid_price_mask = (
            (df['high'] < df['low']) |
            (df['high'] < df['open']) |
            (df['high'] < df['close']) |
            (df['low'] > df['open']) |
            (df['low'] > df['close']) |
            (df['open'] <= 0) |
            (df['high'] <= 0) |
            (df['low'] <= 0) |
            (df['close'] <= 0)
        )
        df = df[~invalid_price_mask]

        # 3. 检查成交量
        if 'volume' in df.columns and self.min_volume_threshold > 0:
            df = df[df['volume'] >= self.min_volume_threshold]

        # 4. 检查异常跳空
        if len(df) > 1:
            price_change = (df['close'] - df['close'].shift(1)) / df['close'].shift(1) * 100
            gap_mask = abs(price_change) > self.max_gap_threshold

            if gap_mask.sum() > 0:
                print(f"  发现 {gap_mask.sum()} 个异常跳空数据点")
                self.quality_stats['gap_bars'] += gap_mask.sum()

                if self.noise_filter_enabled:
                    # 对于短周期数据，移除异常跳空
                    df = df[~gap_mask]

        filtered_count = original_len - len(df)
        if filtered_count > 0:
            print(f"  过滤了 {filtered_count} 个低质量数据点 ({filtered_count/original_len:.1%})")
            self.quality_stats['filtered_bars'] += filtered_count

        self.quality_stats['total_bars'] += original_len

        return df

    def balance_token_distribution(self, bar_data):
        """
        平衡token分布
        """
        if not self.enable_balance or not bar_data:
            return bar_data

        print("分析和平衡token分布...")

        # 统计token分布
        if len(bar_data[0]) >= 6:
            # 提取change, entity, upline, downline字段
            tokens = []
            for row in bar_data:
                if len(row) >= 6:
                    # 构建token字符串
                    if len(bar_data[0]) == 6:
                        token_str = f"{row[2]}|{row[3]}|{row[4]}|{row[5]}"
                    else:
                        token_str = f"{row[2]}|{row[3]}|{row[4]}|{row[5]}"
                    tokens.append(token_str)

            token_counter = Counter(tokens)
            total_tokens = len(tokens)

            # 分析分布
            print(f"  总token数: {total_tokens:,}")
            print(f"  唯一token数: {len(token_counter)}")

            # 找出高频token
            high_freq_tokens = []
            for token, count in token_counter.most_common(10):
                frequency = count / total_tokens
                high_freq_tokens.append((token, count, frequency))
                if frequency > self.max_token_frequency:
                    print(f"  高频token: {token} 出现 {count:,} 次 ({frequency:.1%})")

            # 应用平衡策略
            if self.balance_method == 'undersample':
                bar_data = self._undersample_high_freq_tokens(bar_data, token_counter, total_tokens)
            elif self.balance_method == 'adaptive':
                bar_data = self._adaptive_balance_tokens(bar_data, token_counter, total_tokens)

        return bar_data

    def _undersample_high_freq_tokens(self, bar_data, token_counter, total_tokens):
        """下采样高频token"""
        max_count = int(total_tokens * self.max_token_frequency)

        # 为每个token计算保留概率
        keep_probs = {}
        for token, count in token_counter.items():
            if count > max_count:
                keep_probs[token] = max_count / count
            else:
                keep_probs[token] = 1.0

        # 过滤数据
        filtered_data = []
        np.random.seed(42)  # 确保可重现

        for row in bar_data:
            if len(row) >= 6:
                if len(row) == 6:
                    token_str = f"{row[2]}|{row[3]}|{row[4]}|{row[5]}"
                else:
                    token_str = f"{row[2]}|{row[3]}|{row[4]}|{row[5]}"

                keep_prob = keep_probs.get(token_str, 1.0)
                if np.random.random() < keep_prob:
                    filtered_data.append(row)

        print(f"  下采样后保留 {len(filtered_data):,} / {len(bar_data):,} 样本 ({len(filtered_data)/len(bar_data):.1%})")
        return filtered_data

    def _adaptive_balance_tokens(self, bar_data, token_counter, total_tokens):
        """自适应平衡token分布"""
        # 计算基尼系数
        frequencies = [count/total_tokens for count in token_counter.values()]
        frequencies.sort()
        n = len(frequencies)
        gini = (2 * sum((i+1) * freq for i, freq in enumerate(frequencies))) / (n * sum(frequencies)) - (n+1) / n

        print(f"  基尼系数: {gini:.4f}")

        if gini > 0.7:
            # 严重不平衡，使用激进的下采样
            return self._undersample_high_freq_tokens(bar_data, token_counter, total_tokens)
        elif gini > 0.5:
            # 中度不平衡，使用温和的下采样
            max_count = int(total_tokens * (self.max_token_frequency * 1.5))
            keep_probs = {}
            for token, count in token_counter.items():
                if count > max_count:
                    keep_probs[token] = max_count / count
                else:
                    keep_probs[token] = 1.0

            filtered_data = []
            np.random.seed(42)

            for row in bar_data:
                if len(row) >= 6:
                    if len(row) == 6:
                        token_str = f"{row[2]}|{row[3]}|{row[4]}|{row[5]}"
                    else:
                        token_str = f"{row[2]}|{row[3]}|{row[4]}|{row[5]}"

                    keep_prob = keep_probs.get(token_str, 1.0)
                    if np.random.random() < keep_prob:
                        filtered_data.append(row)

            print(f"  温和平衡后保留 {len(filtered_data):,} / {len(bar_data):,} 样本")
            return filtered_data
        else:
            print(f"  分布相对平衡，无需处理")
            return bar_data

    def _update_symbols(self):
        """
        更新symbols
        """
        if self.market == 'fut':
            zllx = self.ds.get_block_data("ZLLX")
            self.symbols['sf'] = set()
            self.symbols['main'] = set()
            for code in zllx:
                if code[-2:] == 'SF':
                    self.symbols['sf'].add(code)
                else:
                    self.symbols['main'].add(code)
        elif self.market == 'stk':
            hs300 = self.ds.get_block_data("沪深300")
            self.symbols['hs300'] = set(hs300)
            zz500 = self.ds.get_block_data("中证500")
            self.symbols['zz500'] = set(zz500)
            zz1000 = self.ds.get_block_data("中证1000")
            self.symbols['zz1000'] = set(zz1000)
            # self.symbols = list(self.symbols)[:5]
            # self.symbols = set(self.symbols)
        else:
            raise ValueError(f'market {self.market} not supported')

    def _read_config(self):
        """
        读取配置文件
        """
        with open(f'd:/RoboQuant2/alphaquant.json', 'r', encoding='gbk') as f:
            config = json.load(f)
            if self.market == 'fut':
                if self.period == 'day':
                    self.window = config['techindex']['fut_drange_atr_period']
                    self.atr_mult = config['techindex']['fut_drange_atr_mult']
                else:
                    self.window = config['techindex']['fut_mrange_atr_period']
                    self.atr_mult = config['techindex']['fut_mrange_atr_mult']
            elif self.market == 'stk':
                if self.period == 'day':
                    self.window = config['techindex']['stk_drange_atr_period']
                    self.atr_mult = config['techindex']['stk_drange_atr_mult']
                else:
                    self.window = config['techindex']['stk_mrange_atr_period']
                    self.atr_mult = config['techindex']['stk_mrange_atr_mult']

    def _calc_atr(self, df):
        """
        通过历史行情数据更新atr表
        """
        # 技术指标ATR
        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=self.window)
        return atr * self.atr_mult

    def _load_data(self):
        df = pd.DataFrame()
        if self.market != 'fut':
            return df
        df = pd.read_parquet(f'{self.data_path}/{self.market}_{self.block_name}_{self.period}.parquet')
        print(f"load data shape: {df.shape}")
        if self.start_date != "" and self.end_date !="" and self.start_date < self.end_date:
            start_time = int(pd.Timestamp(self.start_date).timestamp())
            end_time = int(pd.Timestamp(self.end_date).timestamp())
            df = df[(df['datetime'] >= start_time) & (df['datetime'] <= end_time)]
            print(f"after time filter: {df.shape}")

        return df

    def save_data(self, bar_data):
        if not bar_data:
            print("没有数据需要保存")
            return

        # 应用样本平衡处理
        bar_data = self.balance_token_distribution(bar_data)

        if not bar_data:
            print("平衡处理后没有数据需要保存")
            return

        if len(bar_data[0]) == 6:
            columns=['symbol', 'datetime', 'change', 'entity', 'upline', 'downline']
        elif len(bar_data[0]) == 10:
            columns=['symbol', 'datetime', 'change', 'entity', 'upline', 'downline', 'seq_volatility', 'seq_amplitude', 'seq_change', 'fut_change']
        else:
            raise ValueError(f'bar_data length not supported {len(bar_data[0])}')

        df = pd.DataFrame(bar_data, columns=columns)
        # df['datetime'] = pd.to_datetime(df['datetime'], unit='s')+ pd.Timedelta(hours=8)
        df.to_csv(f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}.tmp.csv', index=False)

        if self.period == 'day' or self.block_name == 'sf':
            if (self.period == 'min5' or self.period == 'min1') and self.block_name == 'sf':
              df = df[(pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)).dt.time >= pd.to_datetime('09:30:00').time()]
            path = f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}.csv'
            self.process_and_save_data(df, path)
        else:
            # 删除每日时间在00:00:00到06:00:00之间的数据
            print(df.shape)
            if self.period == 'min5' or self.period == 'min1':
                df = df[(pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 9]
            print(df.shape)
            if self.append and self.year != 0:
                path = f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}_{self.year}.csv'
                df = df[pd.to_datetime(df['datetime'], unit='s').dt.year == self.year]
                self.process_and_save_data(df, path)
            else:
                years = self._get_years(df['datetime'])
                print(f'years: {years}')
                for year in years:
                    path = f'{self.save_path}/bar_{self.market}_{self.block_name}_{self.period}_{year}.csv'
                    df_year = df[pd.to_datetime(df['datetime'], unit='s').dt.year == year]
                    self.process_and_save_data(df_year, path)

    def process_and_save_data(self, df, path):
        if df.empty:
            print(f'save data is empty')
            return
        get_vocab(field_num=3 if self.period == 'min5' or self.period == 'min1' else 4)
        if self.append and os.path.exists(path):
            print(f'append {path}')
            old_df = pd.read_csv(path, header=0, dtype={'symbol': str, 'datetime': int, 'bar': int})
            if len(old_df) > 0:
                print("old", old_df.shape, pd.to_datetime(old_df['datetime'].iloc[-1], unit='s') + pd.Timedelta(hours=8))
                df = df[df['datetime'] > old_df['datetime'].iloc[-1]]
                print("new", df.shape)
                if df.empty:
                    print("no new data to append")
                    return
                df = to_bar(df)
                print("new", df.shape, pd.to_datetime(df['datetime'].iloc[-1], unit='s') + pd.Timedelta(hours=8))
                df = pd.concat([old_df, df], axis=0)
                df = df.drop_duplicates(subset=['symbol', 'datetime'], keep='last')
                df.sort_values(by=['symbol', 'datetime'], inplace=True)
                df.reset_index(drop=True, inplace=True)
                print("all", df.shape, pd.to_datetime(df['datetime'].iloc[-1], unit='s') + pd.Timedelta(hours=8))
                df.to_csv(path, index=False, mode='w')
        else:
            # Process and save data in batches of 10,000 records
            batch_size = 20000
            for i in range(0, len(df), batch_size):
                batch = df[i:i+batch_size]
                batch = to_bar(batch)

                if i == 0:
                    # For the first batch, create a new file
                    print(f'new file: {path}')
                    batch.to_csv(path, index=False, mode='w')
                else:
                    # For subsequent batches, append to the existing file
                    batch.to_csv(path, index=False, mode='a', header=False)

                print(f'Processed and saved batch {i//batch_size + 1}, records {i} to {min(i+batch_size, len(df))}')

        print(f'Save {path} success, total shape: {df.shape}')

        # 打印质量统计报告
        self.print_quality_report()

    def print_quality_report(self):
        """打印数据质量统计报告"""
        if not self.enable_quality_check and not self.enable_balance:
            return

        print("\n" + "="*60)
        print("数据质量统计报告")
        print("="*60)

        if self.enable_quality_check:
            total_bars = self.quality_stats['total_bars']
            filtered_bars = self.quality_stats['filtered_bars']
            gap_bars = self.quality_stats['gap_bars']

            print(f"数据质量检查:")
            print(f"  总数据条数: {total_bars:,}")
            print(f"  过滤条数: {filtered_bars:,} ({filtered_bars/total_bars:.1%})")
            print(f"  异常跳空: {gap_bars:,}")
            print(f"  保留条数: {total_bars - filtered_bars:,}")

        if self.enable_balance:
            print(f"\n样本平衡设置:")
            print(f"  平衡方法: {self.balance_method}")
            print(f"  最大token频率: {self.max_token_frequency:.1%}")
            print(f"  噪声过滤: {'启用' if self.noise_filter_enabled else '禁用'}")

        print("="*60)

    def _get_years(self, date: pd.Series):
        # 将timestamp转换为datetime格式
        date = pd.to_datetime(date, unit='s')
        return sorted(date.dt.year.unique())

    def history_data_to_bar_token(self, symbol, df):
        """
        将历史行情数据转换为Bar token训练数据 - 优化版本
        """
        if df.empty or len(df) < self.window + 2:
            print(f'{symbol} 数据为空或长度不足 {len(df)}')
            return []

        # 数据质量检查
        df = self.check_data_quality(df, symbol)

        if df.empty or len(df) < self.window + 2:
            print(f'{symbol} 质量检查后数据不足 {len(df)}')
            return []

        df = df.sort_values(by='datetime')
        # 根据seq_length计算:波动率
        df['seq_volatility'] = df['close'].rolling(window=self.seq_length).std()
        # seq_length最大振幅
        df['seq_amplitude'] = df['high'].rolling(window=self.seq_length).max() - df['low'].rolling(window=self.seq_length).min()
        # seq_length内涨跌幅
        df['seq_change'] = (df['close'] - df['close'].shift(self.seq_length)) / df['close'].shift(self.seq_length) * 100
        print(f'{symbol} {df.shape}')

        open = df['open']
        high = df['high']
        low = df['low']
        close = df['close']
        date = df['datetime']
        if self.market == 'fut':
            code = symbol[:len(symbol)-7]
        else:
            code = symbol
        current_date = pd.to_datetime(date.iloc[self.window + 1], unit='s').date()
        atrs = self._calc_atr(df) / self.scale
        assert len(atrs) == len(df), f'atrs length not equal df'
        atrs = atrs.values
        atr = atrs[self.window + 1]

        # 记录上一个交易日，用于检测交易日间隔
        prev_date = current_date

        bar_data = []
        for i in range(self.window + 1, len(df) - 3):
            barenc = []
            # 股票有除权日期货有主力期货切换，行情数据不连续
            # 切换日期时，前后两日有较大的跳空，需要跳过，否则，数据会失真
            # TODO: 对于股票，有的涨跌幅为[-20, 20]，有的涨跌幅为[-10, 10]

            # 处理方式一：
            chg = (close.iloc[i] - close.iloc[i-1])/close.iloc[i-1] * 100
            if (self.market == 'stk' and abs(chg) > 10) or (self.market == 'fut' and abs(chg) > 7):
                # print(f'{pd.to_datetime(date.iloc[i], unit="s").date()} {symbol} 跳空 {chg:.2f}%')
                # 跳空数据用一个现实中不会出现的-12，12，7，7标记
                barenc.append(code)
                barenc.append(int(date.iloc[i].timestamp()))
                barenc.append(0)
                barenc.append(0)
                barenc.append(0)
                barenc.append(0)
                bar_data.append(barenc)
                # 跳过了15根bar，或者窗口长度，消除跳空的影响
                i += min(15, self.window + 1)
                if i < len(df):
                    atr = atrs[i]
                continue


            # 处理方式二：
            current_date = pd.to_datetime(date.iloc[i], unit='s').date()

            # 检测交易日间隔，如果不是连续的交易日，则添加特殊标记
            if self.special_token and current_date != prev_date:
                # 计算日期间隔
                date_diff = (current_date - prev_date).days

                # 如果日期间隔大于1天，说明中间有周末或假期
                if self.period == 'day' and date_diff > 2:
                    # 添加特殊标记token，表示非交易日间隔
                    # 使用特殊值来标记周末或假期：-99表示特殊token
                    holiday_barenc = []
                    holiday_barenc.append(code)
                    holiday_barenc.append(int(date.iloc[i-1]) + 86400)  # 使用前一天日期+1天的时间戳
                    holiday_barenc.append(-99)  # 特殊标记值
                    holiday_barenc.append(date_diff - 1)  # 记录间隔天数
                    holiday_barenc.append(0)
                    holiday_barenc.append(0)
                    bar_data.append(holiday_barenc)
                else:
                    # 添加特殊标记token，表示非交易日间隔
                    # 使用特殊值来标记周末或假期：-88,-99表示特殊token
                    holiday_barenc = []
                    holiday_barenc.append(code)
                    holiday_barenc.append(int(date.iloc[i-1]) + 86400)  # 使用前一天日期+1天的时间戳
                    if date_diff > 2:
                        holiday_barenc.append(-99)  # 特殊标记值
                    else:
                        holiday_barenc.append(-88)  # 特殊标记值
                    holiday_barenc.append(date_diff - 1)  # 记录间隔天数
                    holiday_barenc.append(0)
                    holiday_barenc.append(0)
                    bar_data.append(holiday_barenc)

                prev_date = current_date
                atr = atrs[i]
                # print(f'{symbol} {pd.to_datetime(date.iloc[i], unit="s").date()} atr is {atr}')

            if atr == 0:
                print(f'{symbol} {pd.to_datetime(date.iloc[i], unit="s").date()} atr is 0')
                continue

            barenc.append(code)
            # 日期
            barenc.append(int(pd.to_datetime(date.iloc[i]).timestamp()))
            # 涨跌
            barenc.append(int((close.iloc[i] - close.iloc[i-1])/atr))
            # 实体
            barenc.append(int((close.iloc[i] - open.iloc[i])/atr))

            if close.iloc[i] > open.iloc[i]: # 阳线
                # 上影线
                barenc.append(int((high.iloc[i] - close.iloc[i])/atr))
                # 下影线
                barenc.append(int((open.iloc[i] - low.iloc[i])/atr))
            else: # 阴线
                # 上影线
                barenc.append(int((high.iloc[i] - open.iloc[i])/atr))
                # 下影线
                barenc.append(int((close.iloc[i] - low.iloc[i])/atr))

            # 扩展序列相关数据
            # 计算波动率
            seq_volatility = df['seq_volatility'].iloc[i]
            barenc.append(seq_volatility)
            # 计算最大振幅
            seq_amplitude = df['seq_amplitude'].iloc[i]
            barenc.append(seq_amplitude)
            # 计算涨跌幅
            seq_change = df['seq_change'].iloc[i]
            barenc.append(seq_change)

            # 计算未来3个Bar涨跌作为预测的标签
            fut_change = (close.iloc[i+3] - close.iloc[i])/close.iloc[i] * 100
            barenc.append(fut_change)

            bar_data.append(barenc)
        return bar_data

    def db_history_data_to_bar_token(self):
        """
        将历史行情数据转换为Bar token训练数据
        """
        bar_data = []
        df = self._load_data()
        for code, data in df.groupby('code'):
            bar_data.extend(self.history_data_to_bar_token(code, data))
        return bar_data

    def file_history_data_to_bar_token(self):
        """
        将历史行情数据转换为Bar token训练数据
        说明：期货历史数据不连续的几种情况：
          1. 有主力合约切换
          2. 有周末或节假日
        另外，要区分时间周期长短的影响：5分钟，日线
        """
        for block_name, symbols in self.symbols.items():
            if self.block_name != 'all' and block_name != self.block_name:
                continue
            bar_data = []
            cnt = 0
            print(f'block_name: {block_name}, symbols: {len(symbols)}')
            for symbol in symbols:
                cnt += 1
                df = self.get_hist_data(symbol)
                bar_data.extend(self.history_data_to_bar_token(symbol, df))
                print(f'{cnt}/{len(symbols)} {symbol} {len(df)}')
            self.save_data(block_name, bar_data)


if __name__ == '__main__':
    """通过本地第三方(通达信)历史行情数据生成训练数据 - 优化版本"""
    parser = ArgumentParser()
    parser.add_argument('--data_path', type=str, default='f:/hqdata', help='data path')
    parser.add_argument('--save_path', type=str, default='f:/featdata/barenc/db2', help='data path')
    parser.add_argument('--market', type=str, default='fut', choices=['fut', 'stk'], help='market')
    parser.add_argument('--period', type=str, default='min1', choices=['day', 'min5', 'min1'], help='period')
    parser.add_argument('--seq_length', type=int, default=30, help='seq length')
    parser.add_argument('--block_name', type=str, default='top', choices=['main', 'top', 'sf', 'hs300', 'zz500', 'zz1000'])
    parser.add_argument('--year', type=int, default=2024, help='year')
    parser.add_argument('--start_date', type=str, default='', help='start date')
    parser.add_argument('--end_date', type=str, default='', help='end date')
    parser.add_argument('--special_token', type=bool, default=False, help='special token')
    parser.add_argument('--append', type=bool, default=False, help='append')

    # 新增优化参数
    parser.add_argument('--enable_quality_check', type=bool, default=True, help='enable data quality check')
    parser.add_argument('--enable_balance', type=bool, default=True, help='enable token balance')
    parser.add_argument('--balance_method', type=str, default='adaptive', choices=['none', 'undersample', 'adaptive'], help='balance method')
    parser.add_argument('--max_token_frequency', type=float, default=0.15, help='max token frequency')
    parser.add_argument('--max_gap_threshold', type=float, default=None, help='max gap threshold (%)')
    parser.add_argument('--min_volume_threshold', type=int, default=None, help='min volume threshold')
    args = parser.parse_args()

    # 创建优化的数据处理器
    hd = HistoryDataToBar(
        args.data_path,
        args.save_path,
        args.market,
        args.period,
        args.block_name,
        args.seq_length,
        args.year,
        args.start_date,
        args.end_date,
        args.special_token,
        args.append,
        args.enable_quality_check,
        args.enable_balance
    )

    # 应用自定义参数
    if args.balance_method != 'adaptive':
        hd.balance_method = args.balance_method
    if args.max_token_frequency != 0.15:
        hd.max_token_frequency = args.max_token_frequency
    if args.max_gap_threshold is not None:
        hd.max_gap_threshold = args.max_gap_threshold
    if args.min_volume_threshold is not None:
        hd.min_volume_threshold = args.min_volume_threshold

    print(f"数据处理配置:")
    print(f"  数据质量检查: {'启用' if args.enable_quality_check else '禁用'}")
    print(f"  样本平衡处理: {'启用' if args.enable_balance else '禁用'}")
    print(f"  平衡方法: {hd.balance_method}")
    print(f"  最大token频率: {hd.max_token_frequency:.1%}")
    print(f"  最大跳空阈值: {hd.max_gap_threshold}%")
    print(f"  最小成交量: {hd.min_volume_threshold}")
    print(f"  噪声过滤: {'启用' if hd.noise_filter_enabled else '禁用'}")
    print()
    # bar_data = hd.file_history_data_to_bar_token()
    bar_data = hd.db_history_data_to_bar_token()
    print(f'bar_data: {len(bar_data)} {len(bar_data[0])}')
    print(bar_data[0])
    hd.save_data(bar_data)

    # hd.encode_to_bar_token()

