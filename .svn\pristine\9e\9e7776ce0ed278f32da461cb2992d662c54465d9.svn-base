{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Pytdx\n", "https://rainx.gitbooks.io/pytdx/content/pytdx_hq.html"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Stock"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 分时数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pytdx.exhq import *\n", "from pytdx.hq import *\n", "api_hq = TdxHq_API(heartbeat=True, raise_exception=True, auto_retry=True)\n", "api_hq = api_hq.connect('119.147.212.81', 7709)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(api_hq.get_history_transaction_data(TDXParams.MARKET_SZ, \"002560\", 0, 500, 20220916))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查询完整历史分时数据\n", "# 在前面的示例中，我们查询了 002560 这个股票在 2022-09-16 的最后500条数据。\n", "# 如果我们想要查询当天的全部数据，需要不断改变start和limit，\n", "# 即api_hq.get_history_transaction_data的第三个参数和第四个参数。\n", "def get_all_trans_data(api, code, date):\n", "    start = 0\n", "    data = []\n", "    while True:\n", "        part = api.get_history_transaction_data(TDXParams.MARKET_SZ, code, start, 888, int(date))\n", "        data.extend(part)\n", "        if len(part) < 888:\n", "            break\n", "        start += 888\n", "    return data\n", "data = get_all_trans_data(api_hq, \"002560\", 20220916)\n", "print(pd.DataFrame(data))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 历史行情"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Futures"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### pub"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "from pytdx.exhq import *\n", "from pyqlab.const import MAIN_FUT_MARKET_CODES, MAIN_SEL_FUT_MARKET_CODES, SF_MARKET_FUT_CODES"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_api():\n", "    api = TdxExHq_API(heartbeat=True, auto_retry=True, raise_exception=False)\n", "    # 扩展行情配置在 connect.cfg->DSH\n", "    # 59.36.9.25:7722\n", "    # 116.239.29.136:7722\n", "    # 52.80.205.16:7722\n", "    if api.connect('120.79.210.76', 7722):\n", "        print(\"connect success!\")\n", "        # api.disconnect()\n", "    else:\n", "        print(\"connect error!\")\n", "    return api\n", "\n", "def get_market_code_num(market):\n", "    if market == 'SF':\n", "        return 47\n", "    elif market == 'ZC':\n", "        return 28\n", "    elif market == 'DC':\n", "        return 29\n", "    elif market == 'SC':\n", "        return 30\n", "    raise Exception(\"market code error\")\n", "\n", "def restore_order_of_night_trading_time(df: pd.DataFrame):\n", "    \"\"\"恢复夜盘时间顺序\"\"\"\n", "    if \"datetime\" not in df.columns:\n", "        raise Exception(\"datetime column not exists\")\n", "    # 分成两个dataframe\n", "    df1 = df.loc[df['datetime'].dt.hour < 20]\n", "    df2 = df.loc[df['datetime'].dt.hour >= 20] # 夜盘时间\n", "    # 进一步分批处理\n", "    df2_1 = df2.loc[(df2['datetime'].dt.weekday <= 4) & (df2['datetime'].dt.weekday > 0)]\n", "    df2_2 = df2.loc[df2['datetime'].dt.weekday == 0]\n", "    del df2\n", "    #如果datetime是星期二到星期五，且时间在21:00到24:00之间，那么datetime减一天\n", "    df2_1['datetime'] = df2_1['datetime'] - pd.<PERSON><PERSON><PERSON>(days=1)\n", "    #如果datetime是星期一，且时间在21:00到24:00之间，那么datetime减三天\n", "    df2_2['datetime'] = df2_2['datetime'] - pd.<PERSON><PERSON><PERSON>(days=3)\n", "    dfs = pd.concat([df1, df2_1, df2_2])\n", "    dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "    dfs.reset_index(drop=True, inplace=True)\n", "    return dfs\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["connect success!\n"]}], "source": ["api = get_api()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    market  category    name short_name\n", "0        1         1     临时股         TP\n", "1        8        12    个股期权         QQ\n", "2        9        12  深证个股期权         SQ\n", "3       27         5    香港指数         FH\n", "4       28         3    郑州商品         QZ\n", "5       29         3    大连商品         QD\n", "6       30         3    上海期货         QS\n", "7       31         2    香港主板         KH\n", "8       33         8   开放式基金         FU\n", "9       34         9   货币型基金         FB\n", "10      38        10    宏观指标         HG\n", "11      42         3    商品指数         TI\n", "12      43         1   B股转H股         HB\n", "13      44         1    股份转让         SB\n", "14      47         3    股指期货         CZ\n", "15      48         2   香港创业板         KG\n", "16      49         2  香港信托基金         KT\n", "17      54         6   国债预发行         GY\n", "18      56         8  阳光私募基金         TA\n", "19      60         3  主力期货合约         MA\n", "20      62         5    中证指数         ZZ\n", "21      68         5    风控指数         TZ\n", "22      70         5  扩展板块指数         UZ\n", "23      71         2     港股通         GH\n", "24     102         5    国证指数         GZ\n"]}], "source": ["data = api.get_markets() # 获取市场代码列表\n", "print(pd.DataFrame(data))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = api.get_instrument_info(0, 100) # 查询代码列表\n", "print(pd.DataFrame(data))\n", "data = api.get_instrument_count() # 查询市场中商品数量\n", "print(data)\n", "data = api.get_instrument_quote(47, \"IFL8\") # 查询五档行情\n", "print(pd.DataFrame(data))\n", "# data = api.get_minute_time_data(47, \"IF2305\") # 查询分时行情\n", "# print(pd.DataFrame(data))\n", "\n", "# data = api.get_history_minute_time_data(47, \"IF2305\", 20220510) # 查询历史分时行情\n", "# print(pd.DataFrame(data))\n", "data = api.get_instrument_bars(TDXParams.KLINE_TYPE_DAILY, 47, \"IFL8\", 0, 700)   # 查询K线数据\n", "print(pd.DataFrame(data))\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                    date  hour  minute  second    price  volume  zengcang  \\\n", "0    2023-12-20 21:40:02    21      40       2  6017000       5         3   \n", "1    2023-12-20 21:40:02    21      40       2  6016000      20        -5   \n", "2    2023-12-20 21:40:03    21      40       3  6016000       3         0   \n", "3    2023-12-20 21:40:04    21      40       4  6017000       4         0   \n", "4    2023-12-20 21:40:04    21      40       4  6017000       2         0   \n", "...                  ...   ...     ...     ...      ...     ...       ...   \n", "1795 2023-12-20 21:58:28    21      58      28  6047000      13        -8   \n", "1796 2023-12-20 21:58:29    21      58      29  6047000       2         0   \n", "1797 2023-12-20 21:58:29    21      58      29  6047000       6        -1   \n", "1798 2023-12-20 21:58:30    21      58      30  6047000       7        -3   \n", "1799 2023-12-20 21:58:31    21      58      31  6046000       5         2   \n", "\n", "      nature  nature_mark  nature_value nature_name  direction  \n", "0          2            0             2          多开          1  \n", "1      10002            1             2          多平         -1  \n", "2      10003            1             3          空换         -1  \n", "3          4            0             4          多换          1  \n", "4          4            0             4          多换          1  \n", "...      ...          ...           ...         ...        ...  \n", "1795   10028            1            28          多平         -1  \n", "1796      29            0            29          多换          1  \n", "1797      29            0            29          空平          1  \n", "1798      30            0            30          空平          1  \n", "1799   10031            1            31          空开         -1  \n", "\n", "[1800 rows x 12 columns]\n"]}], "source": ["data = api.get_transaction_data(30, \"AG2402\") # 查询分笔成交\n", "print(pd.DataFrame(data))\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Tick"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_all_history_trans_data(market, code, date):\n", "    df = pd.DataFrame()\n", "    i = 0\n", "    len = 1800\n", "    data = pd.DataFrame(api.get_history_transaction_data(market, code, date=date, start=i*len, count=len)) # 查询历史分笔成交\n", "    while not data.empty:\n", "        df = pd.concat([data, df])\n", "        i += 1\n", "        data = pd.DataFrame(api.get_history_transaction_data(market, code, date=date, start=i*len, count=len)) # 查询历史分笔成交\n", "    return df\n", "\n", "def get_workdays(year, month, begin=1, end=31):\n", "    import calendar\n", "    # 使用 monthrange() 函数获取该月的天数\n", "    _, days_in_month = calendar.monthrange(year, month)\n", "    # 创建一个包含该月份所有日期的日期范围\n", "    # date_range = pd.date_range(start=f\"{year}-{month}-01\", end=f\"{year}-{month}-{days_in_month}\")\n", "\n", "    # 使用 bdate_range() 函数获取该日期范围内的所有工作日\n", "    if begin < 1:\n", "        begin = 1\n", "    if end > days_in_month:\n", "        end = days_in_month\n", "    workdays = pd.bdate_range(start=f\"{year}-{month}-{begin}\", end=f\"{year}-{month}-{end}\")\n", "\n", "    # 将结果转换为列表\n", "    workdays_list = workdays.tolist()\n", "\n", "    # 返回结果\n", "    wdlist = []\n", "    for workday in workdays_list:\n", "        wdlist.append(int(workday.strftime(\"%Y%m%d\")))\n", "    return wdlist\n", "\n", "\n", "def export_all_history_trans_data_by_day_range(market, code_list, year, month, begin=1, end=31, sorted=True):\n", "    days = get_workdays(year, month, begin, end)\n", "    dfs = pd.DataFrame()\n", "    market_code_num = get_market_code_num(market)\n", "    for day in days:\n", "        for code in code_list:\n", "            df = get_all_history_trans_data(market_code_num, code + \"L8\", day)\n", "            if df.empty:\n", "                # print(f\"{market} {day} {code} empty\")\n", "                continue\n", "            # code\tdatetime\tprice\tvolume\n", "            df['code'] = f'{code}9999.{market}'\n", "            df.rename(columns={'date': 'datetime'}, inplace=True)\n", "            # df = df[['datetime', 'code', 'price', 'volume']]\n", "            df['price'] = df['price'] / 1000\n", "            # grouped = df.groupby(['code', 'datetime']).agg({'price': 'last', 'volume': 'sum'})\n", "            # df = grouped.reset_index()\n", "            df = df[['datetime', 'code', 'price', 'volume']]\n", "            dfs = pd.concat([df, dfs])\n", "            print(f\"{market} {day} {code} {df.shape[0]}\")\n", "    # dfs = restore_order_of_night_trading_time(dfs)\n", "    if sorted:\n", "        dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "    return dfs\n", "\n", "def export_all_history_trans_data_by_month(market, code_list, year, month, sorted=True):\n", "    return export_all_history_trans_data_by_day_range(market, code_list, year, month, 1, 31, sorted)\n", "\n", "\n", "def update_history_trans_data_by_day_range(data_path, is_sf, year, month, begin_day=1, end_day=31):\n", "    \"\"\"增量更新所有合约的历史分笔数据\"\"\"\n", "    dfs = pd.DataFrame()\n", "    if is_sf:\n", "        MARKET_CODES = SF_MARKET_FUT_CODES # 金融期货\n", "    else:\n", "        MARKET_CODES = MAIN_FUT_MARKET_CODES # 商品期货\n", "    for market, codes in MARKET_CODES.items():\n", "        df = export_all_history_trans_data_by_day_range(market, codes, year, month, begin=begin_day, end=end_day, sorted=False)\n", "        dfs = pd.concat([df, dfs])\n", "    dfs = restore_order_of_night_trading_time(dfs)\n", "    if market == 'SF':\n", "        filename=f'{data_path}/{year}/SF{year*100 + month}.parquet'\n", "    else:\n", "        filename=f'{data_path}/{year}/{year*100 + month}.parquet'\n", "    loc_df = pd.read_parquet(filename)\n", "    print(f\"local last {loc_df['datetime'].iloc[-1]} update to {dfs['datetime'].iloc[0]} ~ {dfs['datetime'].iloc[-1]}\")\n", "    dfs = pd.concat([loc_df, dfs])\n", "    # dfs.drop_duplicates(subset=['datetime', 'code'], keep='last', inplace=True)\n", "    dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "    dfs.reset_index(drop=True, inplace=True)\n", "    dfs.to_parquet(filename)\n", "    print(f\"{year}-{month} {loc_df.shape[0]} -> {dfs.shape[0]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 按月增量更新"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/'\n", "is_sf = False\n", "year = 2023\n", "month = 9\n", "begin_day = 27\n", "end_day = 28\n", "update_history_trans_data_by_day_range(data_path, is_sf, year, month, begin_day, end_day)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出金融期货的历史分笔成交数据\n", "data_path = 'e:/hqdata/tick/'\n", "market = 'SF'\n", "code_list = ['IF', 'IH', 'IC', 'IM']\n", "# years = [2020, 2021, 2022]\n", "# for year in years:\n", "for year in range(2015, 2020):\n", "    for month in range(1, 13):\n", "        df = export_all_history_trans_data_by_month(market, code_list, year, month)\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        if market == 'SF':\n", "            df.to_parquet(f'{data_path}/{year}/SF{year*100 + month}.parquet')\n", "        else:\n", "            df.to_parquet(f'{data_path}/{year}/{year*100 + month}.parquet')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 商品期货 按年"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出商品期货的历史分笔成交数据\n", "data_path = 'e:/hqdata/tick/'\n", "for year in range(2019, 2021):\n", "    for month in range(1, 13):\n", "        dfs = pd.DataFrame()\n", "        for market, codes in MAIN_FUT_MARKET_CODES.items():\n", "            df = export_all_history_trans_data_by_month(market, codes, year, month, sorted=False)\n", "            dfs = pd.concat([df, dfs])\n", "        if dfs.empty:\n", "            print(f\"{year}-{month} empty\")\n", "            continue\n", "        dfs = restore_order_of_night_trading_time(dfs)\n", "        # dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        dfs.to_parquet(f'{data_path}/{year}/{year*100 + month}.parquet')\n", "        print(f\"{year}-{month} {dfs.shape[0]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = export_all_history_trans_data_by_month('DC', 'I', 2023, 12, sorted=False)\n", "df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 商品期货 按月"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出期货单月的历史分笔成交数据\n", "data_path = 'e:/hqdata/tick/'\n", "year = 2023\n", "month = 9\n", "dfs = pd.DataFrame()\n", "# MARKET_CODES = SF_MARKET_FUT_CODES # 金融期货\n", "MARKET_CODES = MAIN_FUT_MARKET_CODES # 商品期货\n", "for market, codes in MARKET_CODES.items():\n", "    df = export_all_history_trans_data_by_month(market, codes, year, month, sorted=False)\n", "    dfs = pd.concat([df, dfs])\n", "dfs = restore_order_of_night_trading_time(dfs)\n", "# dfs.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "if market == 'SF':\n", "    dfs.to_parquet(f'{data_path}/{year}/SF{year*100 + month}.parquet')\n", "else:\n", "    dfs.to_parquet(f'{data_path}/{year}/{year*100 + month}.parquet')\n", "print(f\"{year}-{month} {dfs.shape[0]}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 历史行情"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def get_fut_history_data(market, code, period='day', start_date=20150101):\n", "    # 参数： K线周期， 市场ID， 证券代码，起始位置， 数量\n", "    # K线周期参考 TDXParams\n", "    # 市场ID可以通过 get_markets 获得\n", "\n", "    market_code_num = get_market_code_num(market)\n", "    if period == 'day':\n", "        bars_category = TDXParams.KLINE_TYPE_DAILY\n", "        n = 11\n", "    elif period == 'min5':\n", "        bars_category = TDXParams.KLINE_TYPE_5MIN\n", "        n = 100\n", "    else:\n", "        raise Exception('period error')\n", "\n", "    datas = []\n", "    n = 0\n", "    while True: # 一次取800条，循环取，时间是倒序的\n", "        data = api.get_instrument_bars(bars_category, market_code_num, code+\"L8\", n*800, 800)\n", "        n += 1\n", "        if len(data) == 0:\n", "            break\n", "        datas.extend(data)\n", "        cur_date = int(data[0]['year'])*10000 + int(data[0]['month'])*100 + int(data[0]['day'])\n", "        if cur_date < start_date:\n", "            break\n", "    if len(datas) == 0:\n", "        return None\n", "    df = pd.DataFrame(datas)\n", "    df['code'] = f'{code}9999.{market}'\n", "    # df.drop(columns=['year', 'month', 'day', 'hour', 'minute', 'position', 'price'], inplace=True)\n", "    df = df.rename(columns={'trade': 'volume'})\n", "    # column datetime string to datetime\n", "    df['datetime'] = df['datetime'].apply(lambda x: datetime.datetime.strptime(x, \"%Y-%m-%d %H:%M\"))\n", "    df = df.loc[df['datetime'] >= datetime.datetime.strptime(str(start_date), '%Y%m%d')]\n", "    df = df[['code', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount']]\n", "    if period == 'min5':\n", "        df = restore_order_of_night_trading_time(df)\n", "    else:\n", "        df.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        df.reset_index(drop=True, inplace=True)\n", "    return df\n", "\n", "def save_history_data_binary(df: pd.DataFrame, path: str):\n", "    import struct\n", "    with open(path, 'wb') as file:\n", "        for _, row in df.iterrows():\n", "            values = struct.pack('<lfffffff',\n", "                                  int(row['datetime'].timestamp()),\n", "                                  row['open'],\n", "                                  row['high'],\n", "                                  row['low'],\n", "                                  row['close'],\n", "                                  row['volume'],\n", "                                  row['amount'],\n", "                                  0.0)\n", "            file.write(values)\n", "        return len(df)\n", "    \n", "def update_fut_history_data(start_date, is_sf=False, period='day', rq_path='d:/RoboQuant2'):\n", "    \"\"\"更新商品期货的历史K线数据,以parquet格式存储，方便增量更新\"\"\"\n", "    dfs = pd.DataFrame()\n", "    if is_sf:\n", "        MARKET_CODES = SF_MARKET_FUT_CODES\n", "        filename = f'{rq_path}/store/fut_sf_{period}.parquet'\n", "    else:\n", "        MARKET_CODES = MAIN_FUT_MARKET_CODES\n", "        filename = f'{rq_path}/store/fut_{period}.parquet'\n", "    for market, codes in MARKET_CODES.items():\n", "        for code in codes:\n", "            df = get_fut_history_data(market, code, period, start_date)\n", "            if df is None:\n", "                print(f\"{period}: {market} {code} None\")\n", "                continue\n", "            df['code'] = f'{code}9999.{market}'\n", "            print(market, code, period, df.shape[0])\n", "            dfs = pd.concat([df, dfs])\n", "    if dfs.empty:\n", "        print(\"update_fut_history_data is NULL.\")\n", "        return\n", "    # 文件是否存在\n", "    if not os.path.exists(filename):\n", "        dfs.to_parquet(filename, engine='fastparquet')\n", "        print(f\"{period} {dfs.shape[0]}\")\n", "    else:\n", "        loc_df = pd.read_parquet(filename, engine='fastparquet')\n", "        dfs = pd.concat([loc_df, dfs])\n", "        # 去除code和datetime列相同的行\n", "        dfs.drop_duplicates(subset=['code', 'datetime'], keep='last', inplace=True)\n", "        df.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        dfs.reset_index(drop=True, inplace=True)\n", "        dfs.to_parquet(filename, engine='fastparquet')\n", "        print(f\"update last datetime from {loc_df['datetime'].iloc[-1]} to {dfs['datetime'].iloc[-1]}\")\n", "        print(f\"{period} add count {dfs.shape[0] - loc_df.shape[0]}\")\n", "\n", "\n", "def export_fut_history_data(is_sf=False, period='day', rq_path='d:/RoboQuant2'):\n", "    \"\"\"从parquet文件导出商品期货的历史K线数据，以二进制文件存储，方便C++程序读取\"\"\"\n", "    if is_sf:\n", "        filename = f'{rq_path}/store/fut_sf_{period}.parquet'\n", "    else:\n", "        filename = f'{rq_path}/store/fut_{period}.parquet'\n", "    df = pd.read_parquet(filename, engine='fastparquet')\n", "    df = df.groupby('code')\n", "    for code, group in df:\n", "        group.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        group.reset_index(drop=True, inplace=True)\n", "        print(code, group.shape[0])\n", "        save_history_data_binary(group, f'{rq_path}/exdata/{code[-2:]}/{period}/{code}.dat')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试\n", "df = get_fut_history_data('SC', 'RB', 'min5', 20230919)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 增量更新"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 商品期货增量更新\n", "update_fut_history_data(start_date=20230901, is_sf=False, period='day', rq_path='d:/RoboQuant2')\n", "update_fut_history_data(start_date=20230901, is_sf=False, period='min5', rq_path='d:/RoboQuant2')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["export_fut_history_data(is_sf=False, period='day', rq_path='d:/RoboQuant2')\n", "export_fut_history_data(is_sf=False, period='min5', rq_path='d:/RoboQuant2')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 股指期货增量更新\n", "update_fut_history_data(start_date=20120101, is_sf=True, period='day', rq_path='d:/RoboQuant2')\n", "update_fut_history_data(start_date=20150101, is_sf=True, period='min5', rq_path='d:/RoboQuant2')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["export_fut_history_data(is_sf=True, period='day', rq_path='d:/RoboQuant2')\n", "export_fut_history_data(is_sf=True, period='min5', rq_path='d:/RoboQuant2')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 商品期货的历史K线"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rq_data_path = 'd:/RoboQuant2/exdata/'\n", "# 导出商品期货的历史K线数据\n", "for market, codes in MAIN_FUT_MARKET_CODES.items():\n", "    for code in codes:\n", "        df = get_fut_history_data(market, code, period='day', start_year=2018)\n", "        if df is None:\n", "            print(f\"day: {market} {code} None\")\n", "            continue\n", "        ret = save_history_data_binary(df, f'{rq_data_path}/{market}/day/{code}9999.{market}.dat')\n", "        print(f\"day: {market} {code} {ret}\")\n", "        df = get_fut_history_data(market, code, period='min5', start_year=2018)\n", "        if df is None:\n", "            print(f\"min5: {market} {code} None\")\n", "            continue\n", "        ret = save_history_data_binary(df, f'{rq_data_path}/{market}/min5/{code}9999.{market}.dat')\n", "        print(f\"min5: {market} {code} {ret}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 股指期货的历史K线"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rq_data_path = 'd:/RoboQuant2/exdata/'\n", "# 导出股指期货的历史K线数据\n", "for market, codes in SF_MARKET_FUT_CODES.items():\n", "    for code in codes:\n", "        df = get_fut_history_data(market, code, period='day', start_year=2015)\n", "        ret = save_history_data_binary(df, f'{rq_data_path}/{market}/day/{code}9999.{market}.dat')\n", "        print(f\"day: {market} {code} {ret}\")\n", "        df = get_fut_history_data(market, code, period='min5', start_year=2015)\n", "        ret = save_history_data_binary(df, f'{rq_data_path}/{market}/min5/{code}9999.{market}.dat')\n", "        print(f\"min5: {market} {code} {ret}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["market = 'SF'\n", "code = 'IF'\n", "df = get_fut_history_data(market, code, 'day', start_year=2015)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["market = 'SF'\n", "code = 'IF'\n", "df = get_fut_history_data(market, code, 'min5', start_year=2015)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/'\n", "for year in range(2019, 2023):\n", "    for month in range(1, 13):\n", "        df = pd.read_parquet(f'{data_path}/{year}/SF{year*100 + month}.parquet')\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        df['price'] = df['price'] / 1000\n", "        grouped = df.groupby(['code', 'datetime']).agg({'price': 'last', 'volume': 'sum'})\n", "        df = grouped.reset_index()\n", "        df = df[['datetime', 'code', 'price', 'volume']]\n", "        df.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        df.to_parquet(f'{data_path}/{year}/SF{year*100 + month}.parquet')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 更正\n", "期货交易所将夜盘的行情时间通常作为下一个交易日的行情，行情软件保存行情数据的时间与实际行情发生的时间相差一日（周二~周五夜盘）或三日（周一夜盘），如果在使用行情时不调整时间，将造成乱序"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/'\n", "for year in range(2023, 2024):\n", "    for month in range(1, 13):\n", "        df = pd.read_parquet(f'{data_path}/{year}/bak/{year*100 + month}.parquet', engine='fastparquet')\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        #如果datetime是星期一到星期四，且时间在21:00到24:00之间，那么datetime减一天\n", "        df.loc[(df['datetime'].dt.weekday > 0) & (df['datetime'].dt.weekday <= 4) & (df['datetime'].dt.hour >= 20) & (df['datetime'].dt.hour < 24), 'datetime'] = df['datetime'] - pd.Timedelta(days=1)\n", "        #如果datetime是星期五，且时间在21:00到24:00之间，那么datetime减三天\n", "        df.loc[(df['datetime'].dt.weekday == 0) & (df['datetime'].dt.hour >= 20) & (df['datetime'].dt.hour < 24), 'datetime'] = df['datetime'] - pd.Timedelta(days=3)\n", "        df.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        df.to_parquet(f'{data_path}/{year}/{year*100 + month}.parquet', engine='fastparquet')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/'\n", "for year in range(2023, 2024):\n", "    for month in range(1, 8):\n", "        df = pd.read_parquet(f'{data_path}/{year}/bak/{year*100 + month}.sec.parquet', engine='fastparquet')\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        # 由于df太大，所以分成两个dataframe\n", "        df1 = df.loc[df['datetime'].dt.hour < 20]\n", "        df2 = df.loc[df['datetime'].dt.hour >= 20]\n", "        del df\n", "        print(f\"{year}-{month} {df1.shape[0]} {df2.shape[0]}\")\n", "        # df2['datetime'] = df2['datetime'] - pd.<PERSON><PERSON><PERSON>(days=1)\n", "        \n", "        # df2 处理太慢，所以进一步分批处理\n", "        df2_1 = df2.loc[(df2['datetime'].dt.weekday > 0) & (df2['datetime'].dt.weekday <= 4)]\n", "        df2_2 = df2.loc[df2['datetime'].dt.weekday == 0]\n", "        # del df2\n", "        # print(f\"{year}-{month} {df2_1.shape[0]} {df2_2.shape[0]}\")\n", "        \n", "        #如果datetime是星期一到星期四，且时间在21:00到24:00之间，那么datetime减一天\n", "        df2_1['datetime'] = df2_1['datetime'] - pd.<PERSON><PERSON><PERSON>(days=1)\n", "        #如果datetime是星期五，且时间在21:00到24:00之间，那么datetime减三天\n", "        df2_2['datetime'] = df2_2['datetime'] - pd.<PERSON><PERSON><PERSON>(days=3)\n", "        df = pd.concat([df1, df2_1, df2_2])\n", "        df.sort_values(by=['datetime'], inplace=True, ascending=True)\n", "        print(f\"{year}-{month} {df.shape[0]}\")\n", "        df.to_parquet(f'{data_path}/{year}/{year*100 + month}.sec.parquet', engine='fastparquet')\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/'\n", "year=2023\n", "month=12\n", "df = pd.read_parquet(f'{data_path}/{year}/{year*100 + month}.parquet')\n", "df.loc[df['code'] == 'I9999.DC'].head(100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=pd.read_parquet(\"d:/RoboQuant2/store/fut_day.parquet\", engine='fastparquet')\n", "df.loc[df['code'] == 'I9999.DC']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=pd.read_parquet(\"d:/RoboQuant2/store/fut_min5.parquet\", engine='fastparquet')\n", "df.loc[df['code'] == 'I9999.DC']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}