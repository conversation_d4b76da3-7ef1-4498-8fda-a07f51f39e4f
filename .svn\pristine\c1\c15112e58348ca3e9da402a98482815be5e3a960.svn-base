{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading data from https://www.cryptodatadownload.com/cdd/Coinbase_BTCUSD_1h.csv\n", "1466368/1458682 [==============================] - 7s 5us/step\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Symbol</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume BTC</th>\n", "      <th>Volume USD</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2019-10-17 09:00:00</th>\n", "      <td>BTCUSD</td>\n", "      <td>8051.00</td>\n", "      <td>8056.83</td>\n", "      <td>8021.23</td>\n", "      <td>8035.88</td>\n", "      <td>61.25</td>\n", "      <td>492394.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019-10-17 08:00:00</th>\n", "      <td>BTCUSD</td>\n", "      <td>7975.89</td>\n", "      <td>8070.00</td>\n", "      <td>7975.89</td>\n", "      <td>8051.00</td>\n", "      <td>370.45</td>\n", "      <td>2971610.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019-10-17 07:00:00</th>\n", "      <td>BTCUSD</td>\n", "      <td>7964.62</td>\n", "      <td>7987.82</td>\n", "      <td>7964.61</td>\n", "      <td>7975.89</td>\n", "      <td>121.68</td>\n", "      <td>970521.83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019-10-17 06:00:00</th>\n", "      <td>BTCUSD</td>\n", "      <td>7984.02</td>\n", "      <td>7993.97</td>\n", "      <td>7958.29</td>\n", "      <td>7964.62</td>\n", "      <td>212.35</td>\n", "      <td>1692336.84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019-10-17 05:00:00</th>\n", "      <td>BTCUSD</td>\n", "      <td>7941.71</td>\n", "      <td>7986.99</td>\n", "      <td>7937.01</td>\n", "      <td>7984.02</td>\n", "      <td>97.26</td>\n", "      <td>774064.91</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     Symbol     Open     High      Low    Close  Volume BTC  \\\n", "Date                                                                          \n", "2019-10-17 09:00:00  BTCUSD  8051.00  8056.83  8021.23  8035.88       61.25   \n", "2019-10-17 08:00:00  BTCUSD  7975.89  8070.00  7975.89  8051.00      370.45   \n", "2019-10-17 07:00:00  BTCUSD  7964.62  7987.82  7964.61  7975.89      121.68   \n", "2019-10-17 06:00:00  BTCUSD  7984.02  7993.97  7958.29  7964.62      212.35   \n", "2019-10-17 05:00:00  BTCUSD  7941.71  7986.99  7937.01  7984.02       97.26   \n", "\n", "                     Volume USD  \n", "Date                             \n", "2019-10-17 09:00:00   492394.56  \n", "2019-10-17 08:00:00  2971610.86  \n", "2019-10-17 07:00:00   970521.83  \n", "2019-10-17 06:00:00  1692336.84  \n", "2019-10-17 05:00:00   774064.91  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import ssl\n", "\n", "import tensorflow as tf\n", "import pandas as pd\n", "import numpy as np\n", "\n", "ssl._create_default_https_context = ssl._create_unverified_context\n", "\n", "csv_file = tf.keras.utils.get_file('Coinbase_BTCUSD_1h.csv', 'https://www.cryptodatadownload.com/cdd/Coinbase_BTCUSD_1h.csv')\n", "data_frame = pd.read_csv(csv_file, skiprows=1, index_col=\"Date\")\n", "\n", "data_frame.index = pd.to_datetime(data_frame.index, format='%Y-%m-%d %I-%p')\n", "\n", "data_frame.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                        Open     High      Low    Close  Volume BTC  \\\n", "Date                                                                  \n", "2017-07-01 11:00:00  2505.56  2513.38  2495.12  2509.17      114.60   \n", "2017-07-01 12:00:00  2509.17  2512.87  2484.99  2488.43      157.36   \n", "2017-07-01 13:00:00  2488.43  2488.43  2454.40  2454.43      280.28   \n", "2017-07-01 14:00:00  2454.43  2473.93  2450.83  2459.35      289.42   \n", "2017-07-01 15:00:00  2459.35  2475.00  2450.00  2467.83      276.82   \n", "\n", "                     Volume USD        Date  \n", "Date                                         \n", "2017-07-01 11:00:00   287000.32  1498906800  \n", "2017-07-01 12:00:00   393142.50  1498910400  \n", "2017-07-01 13:00:00   693254.01  1498914000  \n", "2017-07-01 14:00:00   712864.80  1498917600  \n", "2017-07-01 15:00:00   682105.41  1498921200  \n"]}, {"ename": "RuntimeError", "evalue": "dataset.__iter__() is only supported when eager execution is enabled.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-5-3b5d035bb64d>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[0mtrain_dataset\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdata\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mDataset\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mfrom_tensor_slices\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdict\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mprepared_df\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      8\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 9\u001b[1;33m \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mx\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'Close'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnumpy\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mx\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mtrain_dataset\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtake\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m5\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensorflow\\python\\data\\ops\\dataset_ops.py\u001b[0m in \u001b[0;36m__iter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1644\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1645\u001b[0m   \u001b[1;32mdef\u001b[0m \u001b[0m__iter__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1646\u001b[1;33m     \u001b[1;32mreturn\u001b[0m \u001b[0miter\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_dataset\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1647\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1648\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensorflow\\python\\data\\ops\\dataset_ops.py\u001b[0m in \u001b[0;36m__iter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    204\u001b[0m       \u001b[1;32mreturn\u001b[0m \u001b[0miterator_ops\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEagerIterator\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    205\u001b[0m     \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 206\u001b[1;33m       raise RuntimeError(\"dataset.__iter__() is only supported when eager \"\n\u001b[0m\u001b[0;32m    207\u001b[0m                          \"execution is enabled.\")\n\u001b[0;32m    208\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mRuntimeError\u001b[0m: dataset.__iter__() is only supported when eager execution is enabled."]}], "source": ["prepared_df = data_frame.drop('Symbol', axis=1)\n", "prepared_df['Date'] = prepared_df.index.values.astype(np.int64) // 10 ** 9\n", "prepared_df = prepared_df.sort_index()\n", "\n", "print(prepared_df.head())\n", "\n", "train_dataset = tf.data.Dataset.from_tensor_slices(dict(prepared_df))\n", "\n", "print([x['Close'].numpy() for x in train_dataset.take(5)])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "dataset.__iter__() is only supported when eager execution is enabled.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-3-996850f51dde>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mds_iter\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0miter\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mtrain_dataset\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;32mwhile\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m     \u001b[0mbatch\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mnext\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mds_iter\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m     \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mbatch\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'Close'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensorflow\\python\\data\\ops\\dataset_ops.py\u001b[0m in \u001b[0;36m__iter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1644\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1645\u001b[0m   \u001b[1;32mdef\u001b[0m \u001b[0m__iter__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1646\u001b[1;33m     \u001b[1;32mreturn\u001b[0m \u001b[0miter\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_dataset\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1647\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1648\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensorflow\\python\\data\\ops\\dataset_ops.py\u001b[0m in \u001b[0;36m__iter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    204\u001b[0m       \u001b[1;32mreturn\u001b[0m \u001b[0miterator_ops\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEagerIterator\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    205\u001b[0m     \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 206\u001b[1;33m       raise RuntimeError(\"dataset.__iter__() is only supported when eager \"\n\u001b[0m\u001b[0;32m    207\u001b[0m                          \"execution is enabled.\")\n\u001b[0;32m    208\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mRuntimeError\u001b[0m: dataset.__iter__() is only supported when eager execution is enabled."]}], "source": ["ds_iter = iter(train_dataset)\n", "\n", "while True:\n", "    batch = next(ds_iter)\n", "    print(batch['Close'])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([[0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 1., 0., 0.],\n", "        ...,\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 1., 0.]], dtype=float32),\n", " array([[0., 0., 0., ..., 0., 1., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        ...,\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.]], dtype=float32))"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "train_df, test_df = train_test_split(prepared_df, test_size=0.2)\n", "\n", "volume = tf.feature_column.numeric_column(\"Volume USD\")\n", "volume_buckets = tf.feature_column.bucketized_column(volume, [1000, 10000, 50000, 100000, 250000, 500000, 1000000, 2500000, 5000000, 10000000])\n", "\n", "feature_layer = tf.keras.layers.DenseFeatures(volume_buckets)\n", "\n", "train_labels = feature_layer(dict(train_df)).numpy()\n", "test_labels = feature_layer(dict(test_df)).numpy()\n", "\n", "train_labels, test_labels"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["460/460 [==============================] - 1s 2ms/step - loss: 10.6348 - accuracy: 0.1459\n", "116/116 [==============================] - 0s 1ms/step - loss: 4.8328 - accuracy: 0.1437\n"]}], "source": ["no_volume_train = train_df.drop(['Volume BTC', 'Volume USD'], axis=1)\n", "no_volume_test = test_df.drop(['Volume BTC', 'Volume USD'], axis=1)\n", "\n", "train_ds = tf.data.Dataset.from_tensor_slices((no_volume_train.values, train_labels)).shuffle(1024).batch(32)\n", "test_ds = tf.data.Dataset.from_tensor_slices((no_volume_test.values, test_labels)).shuffle(1024).batch(32)\n", "\n", "model = tf.keras.Sequential([\n", "  tf.keras.layers.<PERSON><PERSON>(),\n", "  tf.keras.layers.<PERSON><PERSON>(128),\n", "  tf.keras.layers.<PERSON><PERSON>(64),\n", "  tf.keras.layers.<PERSON><PERSON>(11),\n", "])\n", "\n", "model.compile(optimizer='adam',\n", "              loss=tf.keras.losses.CategoricalCrossentropy(), \n", "              metrics=['accuracy'])\n", "\n", "model.fit(train_ds)\n", "\n", "loss, accuracy = model.evaluate(test_ds)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "import numpy as np\n", "\n", "from IPython.display import SVG, Image\n", "from tensorflow.keras.utils import plot_model\n", "\n", "items_per_gen = 1000\n", "n_samples = 100\n", "output_shape=(items_per_gen, 1)\n", "\n", "generator = tf.keras.Sequential([\n", "            tf.keras.layers.Dense(units=256, input_shape=(1, n_samples), activation=\"relu\"),\n", "            tf.keras.layers.LeakyReLU(alpha=0.2),\n", "            tf.keras.layers.BatchNormalization(momentum=0.8),\n", "            tf.keras.layers.Dense(512),\n", "            tf.keras.layers.LeakyReLU(alpha=0.2),\n", "            tf.keras.layers.BatchNormalization(momentum=0.8),\n", "            tf.keras.layers.<PERSON><PERSON>(1024),\n", "            tf.keras.layers.LeakyReLU(alpha=0.2),\n", "            tf.keras.layers.BatchNormalization(momentum=0.8),\n", "            tf.keras.layers.Dense(np.prod(output_shape), activation='tanh'),\n", "            tf.keras.layers.Reshape(output_shape)\n", "        ])\n", "\n", "discriminator = tf.keras.Sequential([\n", "    tf.keras.layers.LSTM(512, input_shape=output_shape, return_sequences=True),\n", "    tf.keras.layers.Bidirectional(tf.keras.layers.LSTM(512)),\n", "    tf.keras.layers.Dense(512),\n", "    tf.keras.layers.LeakyReLU(alpha=0.2),\n", "    tf.keras.layers.<PERSON><PERSON>(256),\n", "    tf.keras.layers.LeakyReLU(alpha=0.2),\n", "    tf.keras.layers.Dense(1, activation='sigmoid'),\n", "])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_model(generator, to_file='test_keras_plot_model.png', show_shapes=True)\n", "Image('test_keras_plot_model.png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_model(discriminator, to_file='test_keras_plot_model_1.png', show_shapes=True)\n", "Image('test_keras_plot_model_1.png')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}