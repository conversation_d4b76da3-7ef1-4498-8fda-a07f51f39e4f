"""
完整优化建议总结
1. 针对金融时序的特殊优化：
    添加金融先验知识
    引入残差时间注意力，捕捉多尺度时间依赖
    添加市场状态识别，增强对市场情境的理解
2. 提高准确性：
    整合多尺度时间信息
    对交易时段数据给予更高权重
    使用更专业的残差连接设计
3. 减少幻觉：
    添加不确定性估计
    基于不确定性阈值过滤不可靠预测
    根据市场状态调整预测策略
4. 防止过拟合：
    添加L2正则化
    简化时间编码方法选择
    根据数据规模动态调整模型复杂度
5. 提高效率：
    更有针对性地使用复杂特性
    简化冗余计算
    对时间敏感区域进行重点计算
通过这些优化，BarGpt42模型将更适合K线时序数据预测任务，
同时提高准确性、减少幻觉并防止过拟合。
"""
import math
import torch
import torch.nn as nn
from torch.nn import functional as F
from pyqlab.models.layers.Embed import (
    TimeFeatureEmbedding, TemporalEmbedding,
    PeriodicTimeEncoding, RelativeTimeEncoding,
    ContinuousTimeEmbedding, MultiScaleTimeEncoding,
    AdaptiveTimeEncoding
)

class EnhancedRMSNorm(nn.Module):
    """
    增强型RMSNorm
    添加条件归一化和自适应参数
    """
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))
        self.bias = nn.Parameter(torch.zeros(dim))
        self.adaptive_weight = nn.Parameter(torch.ones(1))
        
    def forward(self, x, condition=None):
        norm = torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)
        x_normed = x * norm
        if condition is not None:
            condition_weight = torch.sigmoid(condition.mean(dim=-1, keepdim=True))
            return x_normed * (self.weight * self.adaptive_weight * condition_weight + 1) + self.bias
        return x_normed * (self.weight * self.adaptive_weight + 1) + self.bias

class ResidualTemporalAttention(nn.Module):
    """残差时间注意力，专门处理时间序列间的依赖关系"""
    def __init__(self, dim, n_heads, dropout=0.1):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        
        self.wq_time = nn.Linear(dim, dim, bias=False)
        self.wk_time = nn.Linear(dim, dim, bias=False)
        self.wv_time = nn.Linear(dim, dim, bias=False)
        self.proj = nn.Linear(dim, dim, bias=False)
        self.dropout = nn.Dropout(dropout)
        
        # 学习不同时间尺度的权重
        self.time_scales = nn.Parameter(torch.ones(3) / 3)  # 日内、日间、周间尺度
        
    def forward(self, x, x_mark):
        B, T, C = x.size()
        
        # 从时间标记中提取时间信息
        time_features = x_mark[:, :, :3]  # 假设前3个特征是相关时间特征
        
        # 根据不同时间尺度计算注意力权重
        daily_pattern = torch.sin(2 * math.pi * time_features[:, :, 0:1] / 24)
        weekly_pattern = torch.sin(2 * math.pi * time_features[:, :, 1:2] / 7)
        monthly_pattern = torch.sin(2 * math.pi * time_features[:, :, 2:3] / 30)
        
        # 融合不同时间尺度
        time_weights = torch.softmax(self.time_scales, dim=0)
        temporal_bias = (daily_pattern * time_weights[0] + 
                        weekly_pattern * time_weights[1] + 
                        monthly_pattern * time_weights[2])
        
        # 生成注意力掩码，增强特定时间模式的权重
        time_mask = torch.matmul(temporal_bias, temporal_bias.transpose(1, 2))
        
        # 应用注意力机制
        q = self.wq_time(x).view(B, T, self.n_heads, self.head_dim).permute(0, 2, 1, 3)
        k = self.wk_time(x).view(B, T, self.n_heads, self.head_dim).permute(0, 2, 1, 3)
        v = self.wv_time(x).view(B, T, self.n_heads, self.head_dim).permute(0, 2, 1, 3)
        
        attn = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attn = attn + time_mask.unsqueeze(1)  # 添加时间偏置
        
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)
        
        out = torch.matmul(attn, v).permute(0, 2, 1, 3).contiguous().view(B, T, C)
        out = self.proj(out)
        
        return out

class RotaryEmbedding(nn.Module):
    """
    RoPE（Rotary Position Embedding）
    使用旋转位置嵌入进行位置编码。
    """
    def __init__(self, dim, max_position_embeddings=2048, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(self.max_seq_len_cached, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x, seq_len=None):
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len
            t = torch.arange(self.max_seq_len_cached, device=x.device).type_as(self.inv_freq)
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)
            self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
            self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

def rotate_half(x):
    """
    将输入向量的后半部分旋转到前半部分。
    """
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def apply_rotary_pos_emb(q, k, cos, sin, offset=0):
    """
    应用旋转位置嵌入到查询和键向量。
    """
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed

class SwiGLU(nn.Module):
    """
    SwiGLU激活函数
    比GELU有更好的性能
    """
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.w1 = nn.Linear(dim, hidden_dim, bias=False)
        self.w2 = nn.Linear(dim, hidden_dim, bias=False)
        self.w3 = nn.Linear(hidden_dim, dim, bias=False)
        
    def forward(self, x):
        return self.w3(F.silu(self.w1(x)) * self.w2(x))

class MLP(nn.Module):
    """
    MLP（Multi-Layer Perceptron）
    包含两个全连接层和GELU激活函数。
    """
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.c_fc = nn.Linear(dim, hidden_dim, bias=False)
        self.c_proj = nn.Linear(hidden_dim, dim, bias=False)
        self.act = nn.GELU()
        
    def forward(self, x):
        return self.c_proj(self.act(self.c_fc(x)))

class AlibiPositionalEmbedding(nn.Module):
    """
    ALiBi（Attention with Linear Biases）位置嵌入
    使用线性偏置进行位置嵌入，适用于处理变长序列和外推。
    """
    def __init__(self, num_heads, max_seq_len=2048):
        super().__init__()
        self.num_heads = num_heads
        self.max_seq_len = max_seq_len
        slopes = torch.Tensor(self._get_slopes(num_heads))
        self.register_buffer("slopes", slopes)
        self.register_buffer("bias", self._get_bias(max_seq_len))
        
    def _get_slopes(self, num_heads):
        def get_slopes_power_of_2(n):
            start = (2**(-2**-(math.log2(n)-3)))
            ratio = start
            return [start*ratio**i for i in range(n)]

        if math.log2(num_heads).is_integer():
            return get_slopes_power_of_2(num_heads)
        else:
            closest_power_of_2 = 2**math.floor(math.log2(num_heads))
            return get_slopes_power_of_2(closest_power_of_2) + self._get_slopes(2*closest_power_of_2)[0::2][:num_heads-closest_power_of_2]

    def _get_bias(self, max_seq_len):
        bias = torch.arange(max_seq_len).unsqueeze(0).unsqueeze(0)
        return bias * self.slopes.unsqueeze(1).unsqueeze(1)

    def forward(self, x, seq_len):
        return self.bias[:, :, :seq_len, :seq_len]

class GroupedQueryAttention(nn.Module):
    """
    分组查询注意力（GQA）
    减少内存使用并提高效率
    """
    def __init__(self, dim, n_heads, n_kv_heads, dropout):
        super().__init__()
        self.n_heads = n_heads
        self.n_kv_heads = n_kv_heads
        self.head_dim = dim // n_heads
        assert n_heads % n_kv_heads == 0, "n_heads必须能被n_kv_heads整除"
        
        self.wq = nn.Linear(dim, dim, bias=False)
        self.wk = nn.Linear(dim, self.n_kv_heads * self.head_dim, bias=False)
        self.wv = nn.Linear(dim, self.n_kv_heads * self.head_dim, bias=False)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.proj = nn.Linear(dim, dim, bias=False)
        
    def forward(self, x, rotary_emb=None):
        B, T, C = x.size()
        q = self.wq(x).view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        k = self.wk(x).view(B, T, self.n_kv_heads, self.head_dim).transpose(1, 2)
        v = self.wv(x).view(B, T, self.n_kv_heads, self.head_dim).transpose(1, 2)
        
        # 复制k,v以匹配查询头数
        heads_per_kv = self.n_heads // self.n_kv_heads
        k = k.repeat_interleave(heads_per_kv, dim=0)
        v = v.repeat_interleave(heads_per_kv, dim=0)
        
        # 应用旋转位置编码
        if rotary_emb is not None:
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)
            
        y = F.scaled_dot_product_attention(q, k, v, dropout_p=self.attn_dropout.p if self.training else 0, is_causal=True)
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.resid_dropout(self.proj(y))
        return y

class Attention(nn.Module):
    """
    注意力机制
    支持RoPE和ALiBi位置嵌入。
    """
    def __init__(self, dim, n_heads, dropout, pos_embed_type='rope'):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        self.wq = nn.Linear(dim, dim, bias=False)
        self.wk = nn.Linear(dim, dim, bias=False)
        self.wv = nn.Linear(dim, dim, bias=False)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.proj = nn.Linear(dim, dim, bias=False)
        self.pos_embed_type = pos_embed_type
        
        if pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_heads)

    def forward(self, x, rotary_emb=None):
        B, T, C = x.size()
        q, k, v = self.wq(x), self.wk(x), self.wv(x)
        q = q.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        k = k.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        v = v.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)

        if self.pos_embed_type == 'rope':
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)
            attn_bias = None
        elif self.pos_embed_type == 'alibi':
            attn_bias = self.alibi(x, T)
        else:
            attn_bias = None

        # 尝试使用FlashAttention
        try:
            from pyqlab.models.layers.flash_attn import flash_attn_func
            # 重塑形状以适应flash_attn的输入要求
            q = q.transpose(1, 2)  # [B, T, H, D]
            k = k.transpose(1, 2)  # [B, T, H, D]
            v = v.transpose(1, 2)  # [B, T, H, D]
            y = flash_attn_func(q, k, v, causal=True, dropout_p=self.attn_dropout.p if self.training else 0.0)
            y = y.reshape(B, T, C)  # [B, T, C]
        except ImportError:
            # 回退到标准注意力
            y = F.scaled_dot_product_attention(q, k, v, attn_mask=attn_bias, 
                                              dropout_p=self.attn_dropout.p if self.training else 0, 
                                              is_causal=True)
            y = y.transpose(1, 2).contiguous().view(B, T, C)
        
        y = self.resid_dropout(self.proj(y))
        return y

class Block(nn.Module):
    """
    Transformer Block
    包含RMSNorm、注意力机制和MLP。
    """
    def __init__(self, dim, n_heads, n_kv_heads, dropout, pos_embed_type='rope', 
                attn_class=Attention, mlp_class=MLP, window_size=128, 
                use_temporal_attn=False):  # 新增参数
        super().__init__()
        self.ln_1 = EnhancedRMSNorm(dim)
        if attn_class == GroupedQueryAttention:
            self.attn = attn_class(dim, n_heads, n_kv_heads, dropout)
        elif attn_class == SlidingWindowAttention:
            self.attn = attn_class(dim, n_heads, window_size, dropout)
        else:
            self.attn = attn_class(dim, n_heads, dropout, pos_embed_type)
        self.ln_2 = EnhancedRMSNorm(dim)
        self.mlp = mlp_class(dim, 4 * dim)
        self.pos_embed_type = pos_embed_type

        # 新增残差时间注意力
        self.use_temporal_attn = use_temporal_attn
        if use_temporal_attn:
            self.temporal_attn = ResidualTemporalAttention(dim, n_heads, dropout)
            self.ln_3 = EnhancedRMSNorm(dim)

    def forward(self, x, rotary_emb=None, x_mark=None):  # 添加x_mark参数
        x = x + self.attn(self.ln_1(x), rotary_emb)
        x = x + self.mlp(self.ln_2(x))
        
        # 应用残差时间注意力（如果启用）
        if self.use_temporal_attn and x_mark is not None:
            x = x + self.temporal_attn(self.ln_3(x), x_mark)
            
        return x

class HybridTimeEncoding(nn.Module):
    """
    混合时间编码
    结合多种时间编码方法的优势
    """
    def __init__(self, d_model, max_len=2048):
        super().__init__()
        self.periodic = PeriodicTimeEncoding(d_model=d_model, max_len=max_len)
        self.relative = RelativeTimeEncoding(d_model=d_model, max_len=max_len)
        self.continuous = ContinuousTimeEmbedding(d_model=d_model)
        self.fusion = nn.Linear(d_model * 3, d_model)
        
    def forward(self, x):
        p_emb = self.periodic(x)
        r_emb = self.relative(x)
        c_emb = self.continuous(x)
        # 融合不同的时间编码
        combined = torch.cat([p_emb, r_emb, c_emb], dim=-1)
        return self.fusion(combined)

class SlidingWindowAttention(nn.Module):
    """
    滑动窗口注意力
    用于处理长序列
    """
    def __init__(self, dim, n_heads, window_size, dropout):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        self.window_size = window_size
        
        self.wq = nn.Linear(dim, dim, bias=False)
        self.wk = nn.Linear(dim, dim, bias=False)
        self.wv = nn.Linear(dim, dim, bias=False)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.proj = nn.Linear(dim, dim, bias=False)
        
    def forward(self, x, rotary_emb=None):
        B, T, C = x.size()
        q, k, v = self.wq(x), self.wk(x), self.wv(x)
        q = q.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        k = k.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        v = v.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)

        if rotary_emb is not None:
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)
        
        # 创建滑动窗口注意力掩码
        mask = torch.ones(T, T, device=x.device).tril(diagonal=0)
        # 仅在窗口内关注
        for i in range(T):
            start = max(0, i - self.window_size + 1)
            mask[i, :start] = 0
            
        y = F.scaled_dot_product_attention(q, k, v, attn_mask=mask, 
                                          dropout_p=self.attn_dropout.p if self.training else 0)
                                          
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.resid_dropout(self.proj(y))
        return y

class MarketStateClassifier(nn.Module):
    """识别市场状态（如趋势、震荡、高波动等）的分类器"""
    def __init__(self, d_model, n_states=4):
        super().__init__()
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Linear(d_model // 2, n_states)
        )
        
    def forward(self, x):
        # 使用序列中的最后几个token来预测市场状态
        x_last = x[:, -5:].mean(dim=1)  # 使用最后5个token的平均表示
        return self.classifier(x_last)

class BarGpt42(nn.Module):
    """
    BarGpt4模型
    支持多种时间编码嵌入方法和特征向量合并方法。
    """
    def __init__(self, block_size, code_size, vocab_size, n_layer, n_head, d_model, time_encoding,
                 time_embed_type='periodic', freq='t', pos_embed_type='rope', dropout=0.1,
                 n_kv_heads=None, attn_type='standard', activation='gelu', window_size=128,
                 use_flash_attn=False, use_market_state=False, estimate_uncertainty=False, regularization_strength=0.01):
        """
        初始化BarGpt4模型。

        参数:
        - block_size: 序列块大小
        - code_size: 代码嵌入大小
        - vocab_size: 词汇表大小
        - n_layer: Transformer层数
        - n_head: 注意力头数
        - d_model: 嵌入维度
        - time_encoding: 时间编码类型
        - time_embed_type: 时间编码嵌入方法
        - freq: 频率类型
        - pos_embed_type: 位置嵌入方法
        - dropout: Dropout概率
        - n_kv_heads: KV头数量（用于GQA，默认等于n_head）
        - attn_type: 注意力类型 ('standard', 'grouped', 'sliding')
        - activation: 激活函数类型 ('gelu', 'swiglu')
        - window_size: 滑动窗口大小
        - use_flash_attn: 是否使用FlashAttention
        - use_market_state: 是否使用市场状态分类器
        - estimate_uncertainty: 是否估计预测不确定性
        - regularization_strength: L2正则化强度
        """
        super().__init__()
        self.block_size = block_size
        self.vocab_size = vocab_size
        self.n_layer = n_layer
        self.n_head = n_head
        self.d_model = d_model
        self.time_encoding = time_encoding
        self.pos_embed_type = pos_embed_type
        self.time_embed_type = time_embed_type
        self.freq = freq

        # 设置n_kv_heads默认值
        if n_kv_heads is None:
            n_kv_heads = n_head
        
        self.use_flash_attn = use_flash_attn
        self.attn_type = attn_type
        self.activation = activation
        self.n_kv_heads = n_kv_heads
        self.window_size = window_size
        
        # 根据attn_type选择注意力类型
        attn_class = Attention
        if attn_type == 'grouped':
            attn_class = GroupedQueryAttention
        elif attn_type == 'sliding':
            attn_class = SlidingWindowAttention
            
        # 根据activation选择激活函数
        mlp_class = MLP
        if activation == 'swiglu':
            mlp_class = SwiGLU
        
        self.transformer = nn.ModuleDict(dict(
            bar_eb = nn.Embedding(vocab_size, d_model),
            code_eb = nn.Embedding(code_size, d_model),
            tf_eb = TimeFeatureEmbedding(d_model=d_model, freq=freq) if time_encoding =='timeF' else TemporalEmbedding(d_model=d_model, embed_type=time_encoding, freq=freq),
            time_eb = self._get_time_embedding(time_embed_type, d_model),
            drop = nn.Dropout(dropout),
            h = nn.ModuleList([Block(d_model, n_head, n_kv_heads, dropout, 
                                   pos_embed_type, attn_class, mlp_class, window_size, 
                                   use_temporal_attn=True) 
                             for _ in range(n_layer)]),
            ln_f = EnhancedRMSNorm(d_model),
        ))
        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)
        
        if pos_embed_type == 'rope':
            self.rotary_emb = RotaryEmbedding(d_model // n_head)
        elif pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_head, block_size)

        # 添加市场状态分类器
        self.use_market_state = use_market_state
        if use_market_state:
            self.market_classifier = MarketStateClassifier(d_model)

        # 添加不确定性估计
        self.estimate_uncertainty = estimate_uncertainty
        if estimate_uncertainty:
            self.uncertainty_head = nn.Sequential(
                nn.Linear(d_model, d_model // 2),
                nn.GELU(),
                nn.Linear(d_model // 2, vocab_size)
            )

        # 存储正则化强度参数
        self.regularization_strength = regularization_strength

        # Initialize weights
        self.apply(self._init_weights)
        for pn, p in self.named_parameters():
            if pn.endswith('proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        # Weight tying
        self.transformer.bar_eb.weight = self.lm_head.weight

        print("="*30)
        print(f"Number of parameters: {sum(p.numel() for p in self.parameters())/1e6:.2f}M")
        print("="*30)

    def _init_weights(self, module):
        """
        初始化权重
        """
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()

    def _get_time_embedding(self, time_embed_type, d_model):
        """
        获取时间编码嵌入方法。

        参数:
        - time_embed_type: 时间编码嵌入方法
        - d_model: 嵌入维度

        返回:
        - 时间编码嵌入层
        """
        if time_embed_type == 'periodic':
            return PeriodicTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'relative':
            return RelativeTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'time_feature':
            return TimeFeatureEmbedding(d_model=d_model, freq=self.freq)
        elif time_embed_type == 'continuous':
            return ContinuousTimeEmbedding(d_model=d_model)
        elif time_embed_type == 'multiscale':
            return MultiScaleTimeEncoding(d_model=d_model)
        elif time_embed_type == 'adaptive':
            return AdaptiveTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'hybrid':
            return HybridTimeEncoding(d_model=d_model, max_len=self.block_size)
        else:
            raise ValueError(f"Unknown time embedding type: {time_embed_type}")

    def forward(self, code, x, x_mark, targets=None):
        """
        前向传播。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - targets: 目标输出（可选）

        返回:
        - logits: 模型输出
        - loss: 损失（如果提供了目标输出）
        """
        b, t = x.size()
        assert t <= self.block_size, f"Cannot forward sequence of length {t}, block size is only {self.block_size}"
        
        bar_emb = self.transformer.bar_eb(x)
        code_emb = self.transformer.code_eb(code)
        if self.time_embed_type == 'time_feature':
            time_emb = self.transformer.tf_eb(x_mark if self.freq == 't' else x_mark[:, :, -3:])
        else:
            time_emb = self.transformer.tf_eb(x_mark if self.freq == 't' else x_mark[:, :, -3:])
            time_emb = self.transformer.time_eb(time_emb)

        x = self.transformer.drop(bar_emb + code_emb + time_emb)
        
        if self.pos_embed_type == 'rope':
            rotary_emb = self.rotary_emb(x, seq_len=t)
        else:
            rotary_emb = None
        
        for block in self.transformer.h:
            x = block(x, rotary_emb, x_mark)
        x = self.transformer.ln_f(x)

        if targets is not None:
            logits = self.lm_head(x)
            ce_loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
            
            # 添加L2正则化损失以防止过拟合
            l2_reg = 0.0
            for param in self.parameters():
                l2_reg += torch.norm(param)**2
                
            loss = ce_loss + self.regularization_strength * l2_reg
        else:
            logits = self.lm_head(x[:, [-1], :])
            loss = None

        # 市场状态识别
        market_state_logits = None
        if self.use_market_state:
            market_state_logits = self.market_classifier(x)

        # 如果启用不确定性估计
        if self.estimate_uncertainty:
            uncertainty = self.uncertainty_head(x)
            uncertainty = F.softplus(uncertainty)  # 确保不确定性为正

        # 添加市场状态信息到返回值
        if self.use_market_state:
            if self.estimate_uncertainty:
                return logits, loss, market_state_logits, uncertainty
            else:
                return logits, loss, market_state_logits
            
        if self.estimate_uncertainty:
            return logits, loss, uncertainty
        
        return logits, loss

    def crop_block_size(self, block_size):
        """
        裁剪序列块大小。

        参数:
        - block_size: 新的序列块大小
        """
        # model surgery to decrease the block size if necessary
        # e.g. we may load the GPT2 pretrained model checkpoint (block size 1024)
        # but want to use a smaller block size for some smaller, simpler model
        assert block_size <= self.block_size
        self.block_size = block_size
        self.transformer.wpe.weight = nn.Parameter(self.transformer.wpe.weight[:block_size])
        for block in self.transformer.h:
            if hasattr(block.attn, 'bias'):
                block.attn.bias = block.attn.bias[:,:,:block_size,:block_size]

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        """
        配置优化器。

        参数:
        - weight_decay: 权重衰减系数
        - learning_rate: 学习率
        - betas: Adam优化器的beta参数
        - device_type: 设备类型

        返回:
        - 优化器
        """
        import inspect
        # start with all of the candidate parameters
        param_dict = {pn: p for pn, p in self.named_parameters()}
        # filter out those that do not require grad
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        # create optim groups. Any parameters that is 2D will be weight decayed, otherwise no.
        # i.e. all weight tensors in matmuls + embeddings decay, all biases and layernorms don't.
        decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
        nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]
        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters")
        print(f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters")
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"using fused AdamW: {use_fused}")

        return optimizer

    def estimate_mfu(self, fwdbwd_per_iter, dt):
        """
        估计模型FLOPs利用率（MFU），单位为A100 bfloat16峰值FLOPS。

        参数:
        - fwdbwd_per_iter: 每次迭代的前向传播和反向传播次数
        - dt: 迭代时间

        返回:
        - MFU
        """
        # first estimate the number of flops we do per iteration.
        # see PaLM paper Appendix B as ref: https://arxiv.org/abs/2204.02311
        N = self.get_num_params()
        cfg = self.config
        L, H, Q, T = cfg.n_layer, cfg.n_head, cfg.d_model//cfg.n_head, cfg.block_size
        flops_per_token = 6*N + 12*L*H*Q*T
        flops_per_fwdbwd = flops_per_token * T
        flops_per_iter = flops_per_fwdbwd * fwdbwd_per_iter
        # express our flops throughput as ratio of A100 bfloat16 peak flops
        flops_achieved = flops_per_iter * (1.0/dt) # per second
        flops_promised = 312e12 # A100 GPU bfloat16 peak flops is 312 TFLOPS
        mfu = flops_achieved / flops_promised
        return mfu

    @torch.no_grad()
    def generate(self, idx, max_new_tokens, temperature=1.0, top_k=None, uncertainty_threshold=None):
        """
        增强的生成函数，支持基于不确定性的过滤
        
        参数:
        - uncertainty_threshold: 不确定性阈值，超过此值的预测将被过滤
        """
        for _ in range(max_new_tokens):
            # 修改现有代码来处理不确定性
            idx_cond = idx if idx.size(1) <= self.block_size else idx[:, -self.block_size:]
            
            if self.estimate_uncertainty:
                logits, _, uncertainty = self(idx_cond)
                # 基于不确定性调整采样
                if uncertainty_threshold is not None:
                    # 对高不确定性的token降低概率
                    mask = (uncertainty[:, -1] > uncertainty_threshold).float()
                    logits[:, -1] = logits[:, -1] * (1 - mask.unsqueeze(-1))
            else:
                logits, _ = self(idx_cond)
                
            # pluck the logits at the final step and scale by desired temperature
            logits = logits[:, -1, :] / temperature
            # optionally crop the logits to only the top k options
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')
            # apply softmax to convert logits to (normalized) probabilities
            probs = F.softmax(logits, dim=-1)
            # sample from the distribution
            idx_next = torch.multinomial(probs, num_samples=1)
            # append sampled index to the running sequence and continue
            idx = torch.cat((idx, idx_next), dim=1)

        return idx

