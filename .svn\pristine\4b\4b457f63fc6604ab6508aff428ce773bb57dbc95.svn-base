{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"d:/QuantLab\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d:\\QuantLab\n"]}], "source": ["ds=DataSource(RunMode.passive)\n", "print(ds.get_run_dir())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[574.5 606.  603.5 613.  608.5 610.  601.  591.  591.  604.  591.5 596.5\n", "  600.  595.5 595.  594.  596.  603.  607.  613.  615.  615.5 629.5 612.\n", "  612.5 610.  621.5 616.  612.  619.5 621.  621.  625.5 605.  563.5 574.5\n", "  566.5 584.5 582.5 580.  596.  605.  607.  608.  627.5 625.  630.  647.\n", "  648.5 642.  642.  623.  616.  602.  631.  626.5 634.  635.5 621.5 611.5\n", "  630.5 620.5 627.5 633.5 618.  621.  623.5 615.  591.  564.  588.5 598.\n", "  593.  588.  570.  574.5 557.5 577.5 568.  569.5 584.  597.5 599.5 596.\n", "  606.5 606.5 606.  612.  620.  602.  613.  604.5 607.  602.  594.5 595.5\n", "  610.  610.5 623.  633.  631. ]]\n"]}], "source": ["symbol = 'I2009.DC'\n", "hist_data=ds.get_history_data(symbol, 100, [BarData.close], BarSize.day, DoRight.forward)\n", "print(hist_data)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['IC2005.SF', 'IF2005.SF', 'IH2005.SF', 'AP2010.ZC', 'CF2009.ZC', 'CJ2009.ZC', 'CY2009.ZC', 'FG2009.ZC', 'MA2009.ZC', 'OI2009.ZC', 'RM2009.ZC', 'SA2009.ZC', 'SF2009.ZC', 'SM2009.ZC', 'SR2009.ZC', 'TA2009.ZC', 'UR2009.ZC', 'A2009.DC', 'C2009.DC', 'CS2009.DC', 'EB2009.DC', 'EG2009.DC', 'I2009.DC', 'J2009.DC', 'JD2006.DC', 'JM2009.DC', 'L2009.DC', 'M2009.DC', 'P2009.DC', 'PP2009.DC', 'RR2009.DC', 'V2009.DC', 'Y2009.DC', 'AG2006.SC', 'AL2007.SC', 'AU2006.SC', 'BU2006.SC', 'CU2006.SC', 'HC2010.SC', 'NI2007.SC', 'NR2007.SC', 'PB2006.SC', 'RB2010.SC', 'RU2009.SC', 'SC2007.SC', 'SN2007.SC', 'SP2009.SC', 'SS2007.SC', 'ZN2007.SC']\n"]}], "source": ["zlqh = ds.get_block_data(\"ZLQH\")\n", "print(zlqh)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}