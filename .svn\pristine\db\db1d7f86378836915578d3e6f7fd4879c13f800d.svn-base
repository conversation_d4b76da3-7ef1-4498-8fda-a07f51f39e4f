# 要添加一个新单元，输入 '# %%'
# 要添加一个新的标记单元，输入 '# %% [markdown]'
# %% [markdown]
# # 导入AICM交易特征数据

import sys
import time
import datetime
import json
import sqlite3
import pandas as pd
import numpy as np
sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

factor_num = 112
all_factor_names = [
    # 价量因子
    "OPEN", "HIGH", "LOW", "CLOSE", "VOLUME", "TYPICAL_PRICE", "NEW",
    "NEW_CHANGE_PERCENT", "SHORT_TERM_HIGH", "LONG_TERM_HIGH", "SHORT_TERM_LOW",
    "LONG_TERM_LOW",

    # 技术指标类因子
    "AD", "DX", "ADX", "ADXR", "APO", "AROON_UP", "AROON_DOWN", "ATR",
    "BOLL_UP", "BOLL_MID", "BOLL_DOWN", "CCI", "CMO",

    "MA_FAST", "MA_SLOW", "EMA_FAST", "EMA_SLOW", "DEMA_FAST", "DEMA_SLOW",
    "KAMA_FAST", "KAMA_SLOW", "MAMA_FAST", "MAMA_SLOW", "T3_FAST", "T3_SLOW",
    "TEMA_FAST", "TEMA_SLOW", "TRIMA_FAST", "TRIMA_SLOW", "TRIX_FAST",
    "TRIX_SLOW",

    "MACD", "MACD_DIFF", "MACD_DEA", "MFI", "MOM", "NATR", "OBV", "ROC", "RSI",
    "SAR", "TRANGE", "TSF", "ULTOSC", "WILLR",
    "KDJ_K", "KDJ_D",

    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE", "SQUEEZE_SIGNAL", "SQUEEZE_ZERO_BARS", "SQUEEZE_BAND_UPL",
    "SQUEEZE_BAND_DWL", "SQUEEZE_MDL", "SQUEEZE_KC_UPL", "SQUEEZE_KC_DWL",
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_UPL", "BAND_MDL", "BAND_DWL", "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",
    "BAND_BK_BARS", "BAR_STICK_LENGTH",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HIGHEST", "TREND_LOWEST", "TREND_HLR",
    "TREND_LEVEL",

    "HYO_TENKAN_SEN", "HYO_KIJUN_SEN", "HYO_CROSS_BARS", "TATR",
    "TATR_THRESHOLD"    
]

# 使用两个值的因子
two_val_factor_name = [
    "RSI", "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "BAND_GRADIENT",
    "TL_FAST", "TL_SLOW",
    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR"
]

context_name = [
    "AD_PS_RATIO", "COST_RNG", "CURRENT_TIME", "DRAWDOWN_RNG", 
    "FAST_AG_RSI", "FAST_AG_RSI_PREV", "FAST_QH_RSI", "FAST_QH_RSI_PREV", 
    "LONG_RANGE", "PF_PS_RATIO", "PF_YIELD_HL", "PF_YIELD_TREND", 
    "PNL", "POS_DAYS", "POS_LONG_BARS", "POS_SHORT_BARS", "SHORT_RANGE",
    "SLOW_AG_RSI", "SLOW_AG_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV", "STDDEV_RNG"
]

factor_names = [[ # Slow period factor
    "RSI",
    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
],
[ # Fast period factor
    "RSI",
    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]]

pos_context = [
  "COST_RNG", "DRAWDOWN_RNG", "STDDEV_RNG", "PNL", "POS_DAYS",
  "POS_SHORT_BARS", "POS_LONG_BARS", "SHORT_RANGE", "LONG_RANGE",
  "PF_YIELD_TREND", "PF_YIELD_HL", "AD_PS_RATIO", "PF_PS_RATIO",
  "FAST_AG_RSI", "FAST_AG_RSI_PREV", "SLOW_AG_RSI", "SLOW_AG_RSI_PREV",
  "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV",
  "CURRENT_TIME"
]

open_pos_context = [
  "STDDEV_RNG", "SHORT_RANGE", "LONG_RANGE",
  "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV"
]

def factor_select(n, i):
    if all_factor_names[n] in factor_names[i]:
        if all_factor_names[n] in two_val_factor_name:
            return 2
        else:
            return 1
    return 0

def context_select(n, drop_cols):
    if pos_context[n] in open_pos_context and pos_context[n] not in drop_cols:
        return 1
    return 0
        

class AicmFactorsData():

    def __init__(self) -> None:
        self.portfolio_id = '*****************'
        self.db_path = "d:/RoboQuant"
        self.data_path = 'd:/RobQuant/data'
        self.drop_columns = ['ord_id', 'instrument', 'datetime', 'direct']


    def dump_factor_select_items(self):
        f_sel = {}
        f_sel['slow'] = [factor_select(n, 0) for n in range(len(all_factor_names))]
        f_sel['fast'] = [factor_select(n, 1) for n in range(len(all_factor_names))]
        f_sel['context'] = [context_select(n, self.drop_columns) for n in range(len(pos_context))]
        with open('%s/factor_sel.json' % self.data_path, 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)
        print(f_sel)

    # [markdown]
    # ff = lambda n: 2 if all_factor_names[n] in two_val_factor_name else 1
    # [ff(n)  for n in range(len(all_factor_names))]

    # 从数据库加载因子数据
    def load_factor_from_db(self):
        db=create_db("leveldb", "%s/store/kv.db" % self.db_path, Mode.read)
        cursor = db.new_cursor()
        # cursor.seek_to_first()
        data = {}
        while cursor.valid():
            # print(cursor.key())
            # print(cursor.key(), cursor.value())
            # fss:lf:
            if cursor.key()[0:4] == b'fss:':
                key = cursor.key().decode()
                ordid = key[7:]
                if ordid not in data:
                    data[ordid] = {}
                item = data[ordid]
                if key[0:7] == 'fss:lf:':
                    item['lf'] = cursor.value().decode()
                if key[0:7] == 'fss:sf:':
                    item['sf'] = cursor.value().decode()
                if key[0:7] == 'fss:ct:':
                    item['ct'] = cursor.value().decode()
                # data.append(cursor.value())
            cursor.next()
        del cursor
        db.close()
        del db
        return data

    def unpack_factor_data(self, data):
        return json.loads(data)

    def get_factor_names(self, factor_name, is_open_ord=True):
        ds = self.load_factor_from_db()

        col_names = ['ord_id']
        for name in factor_name:
            if name in two_val_factor_name:
                col_names.append("lf_{}_1".format(name))
                col_names.append("lf_{}_2".format(name))
            else:
                col_names.append("lf_%s" % name)

        for name in factor_name:
            if name in two_val_factor_name:
                col_names.append("sf_{}_1".format(name))
                col_names.append("sf_{}_2".format(name))
            else:
                col_names.append("sf_%s" % name)

        if is_open_ord:
            col_names.extend(open_pos_context)
        else:
            col_names.extend(pos_context)

        return col_names

    def get_order_factors(self, factor_name, is_open_ord=True):
        ds = self.load_factor_from_db()

        factor_data = []
        for key, value in ds.items():
            if 'lf' not in value or 'sf' not in value or 'ct' not in value:
                continue

            item = []
            item.append(key)

            data = json.loads(value['lf'])
            for name in factor_name:
                if name in two_val_factor_name:
                    item.extend(data[name][1:])
                else:
                    item.append(data[name][2])

            data = json.loads(value['sf'])
            for name in factor_name:
                if name in two_val_factor_name:
                    item.extend(data[name][1:])
                else:
                    item.append(data[name][2])

            data = json.loads(value['ct'])
            if is_open_ord and data['PNL'] == 0.0:
                for key in open_pos_context:
                    if key in data.keys():
                        item.append(data[key])
                    else:
                        item.append(0.0)
            elif not is_open_ord:
                for key in pos_context:
                    if key in data.keys():
                        item.append(data[key])
                    else:
                        item.append(0.0)

            factor_data.append(item)
        return pd.DataFrame(factor_data, columns=self.get_factor_names(factor_name, is_open_ord))


    """
    从orders数据库导出订单标签
    """
    def load_orders(self):
        conn = sqlite3.connect('%s/data/ot_store.db'%self.db_path)
        cur = conn.cursor()

        # cur.execute("""SELECT * FROM T_Filled_Order WHERE account_id = "%s" ORDER BY id DESC"""%('*****************'))
        cur.execute("SELECT * FROM T_Filled_Order ORDER BY id ASC")
        orders = cur.fetchall()

        data = []
        for item in orders:
            record = []
            ord = json.loads(item[1])
            if ord['account_id'] != self.portfolio_id:
                continue
            record.append(ord['order_id'])
            record.append(ord['account_id'])
            record.append(ord['order_book_id'])
            record.append(time.strftime("%Y%m%d %H:%M:%S", time.localtime(ord['trading_dt'])))
            msg = ord['message'].split(',')
            if len(msg) <= 2:
                record.append(msg[0])
                record.append(msg[1])
                record.append(0)
                record.append(0)
            elif len(msg) > 7:
                record.append(msg[0])
                record.append(msg[1])
                record.append(msg[3])
                record.append(msg[7])
            else:
                record.append(msg[0])
                record.append(msg[1])
                record.append(0)
                record.append(0)
            data.append(record)

        cur.close()
        conn.close()

        return pd.DataFrame(data, columns=['ord_id', 'account_id', 'label', 'datetime', 'oprater', 'direct', 'cost_atr', 'pnl'])

    def get_orders_label(self, ord_df):
        data = []
        for name, group in ord_df.groupby(['account_id', 'label']):
            for i in range(len(group)):
                if i + 1 >= len(group):
                    break
                if group.iat[i, 4] != "open":
                    continue
                item = []
                item.append(group.iat[i, 0])
                item.append(group.iat[i, 2])
                i = i + 1
                if group.iat[i, 4] != "open":
                    item.append(group.iat[i, 3])
                    item.append(group.iat[i, 5])
                    if float(group.iat[i, 7]) > 0:
                        item.append(1)
                    else:
                        item.append(0)
                    data.append(item)
                # else:
                #     print("warning:", group.iat[i, 2])
        return pd.DataFrame(data, columns=['ord_id', 'instrument', 'datetime', 'direct', 'label'])


    def export_factors_data(self):
        open_df = self.get_order_factors(factor_name=factor_names[1], is_open_ord=True)
        # open_df.to_csv("e:/lab/RoboQuant/pylab/data/open_factor.csv")
        if len(open_df) == 0:
            print("portfolio: %s is null" % self.portfolio_id)
            return

        # close_df = self.get_order_factors(factor_names[1], is_open_ord=False)
        # close_df.to_csv("e:/lab/RoboQuant/pylab/data/close_factor.csv")

        label_df = self.get_orders_label(self.load_orders())
        # label_df.to_csv("e:/lab/RoboQuant/pylab/data/ord_label.csv")
        if len(label_df) == 0:
            print("portfolio: %s is null" % self.portfolio_id)
            return
        print(f'Portfolio: {self.portfolio_id} export order data: {len(label_df)} \
        Today add count: {(label_df["datetime"] >= datetime.datetime.now().strftime("%Y%m%d 00:00:00")).sum()}')

        factor_df = pd.merge(open_df, label_df, how='inner', on='ord_id') # right

        # [markdown]
        # # 删除列值全为0的列
        # factor_df = factor_df.loc[:, (factor_df != 0).any(axis=0)]
        # factor_df

        factor_df = factor_df.drop(factor_df[factor_df.lf_TREND_HLR <= 0.01].index)
        # factor_df.to_csv("e:/lab/RoboQuant/pylab/data/factors.csv")

        # [markdown]
        # [c for c in factor_df.columns if c != 'label']

        for name, group in factor_df.groupby(['direct']):
            if name == 'L':
                factor_long_df = group
            if name == 'S':
                factor_short_df = group

        # factor_long_df.drop(self.drop_columns, axis=1, inplace=True)
        # factor_short_df.drop(self.drop_columns, axis=1, inplace=True)

        factor_long_df.to_csv("%s/factors_long_xy.%s.csv"%(self.data_path, self.portfolio_id), index=0) #header=0
        factor_short_df.to_csv("%s/factors_short_xy.%s.csv"%(self.data_path, self.portfolio_id), index=0)

        print(f"portfolio: {self.portfolio_id} export long: {len(factor_long_df)} short: {len(factor_short_df)}\
             total: {len(factor_long_df) + len(factor_short_df)}")


if __name__ == "__main__":
    fd = AicmFactorsData()
    # fd.db_path = "c:/TRobot"
    fd.db_path = "d:/RoboQuant"
    fd.data_path = "../data"
    # fd.portfolio_id = "00171106132928000"
    # fd.portfolio_id = "00170623114649000"
    # fd.drop_columns = ['ord_id', 'instrument', 'datetime', 'direct',
    #     "STDDEV_RNG", "SHORT_RANGE", "LONG_RANGE",
    #     "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV"]
    # fd.dump_factor_select_items()
    portfolios = ['*****************', '00171106132928000', '00170623114649000']
    for pf in portfolios:
        fd.portfolio_id = pf
        fd.export_factors_data()
