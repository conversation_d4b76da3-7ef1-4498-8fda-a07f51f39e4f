# DRL models from ElegantRL: https://github.com/AI4Finance-Foundation/ElegantRL
import torch
from elegantrl.agents import Agent<PERSON><PERSON><PERSON>
from elegantrl.agents import <PERSON><PERSON><PERSON>
from elegantrl.agents import AgentSAC
from elegantrl.agents import AgentTD3
from elegantrl.agents import Agent<PERSON>Q<PERSON>
from elegantrl.agents import Agent<PERSON><PERSON><PERSON><PERSON>
from elegantrl.train.config import Arguments, get_gym_env_args
# from elegantrl.agents import Agent<PERSON>2<PERSON>
from elegantrl.train.run import train_and_evaluate, init_agent

MODELS = {"ddpg": AgentDDPG, "td3": AgentTD3, "sac": AgentSAC, "ppo": AgentPPO, "d3qn": AgentD3QN, "dqn": AgentDQN}
OFF_POLICY_MODELS = ["ddpg", "td3", "sac"]
ON_POLICY_MODELS = ["ppo"]
"""MODEL_KWARGS = {x: config.__dict__[f"{x.upper()}_PARAMS"] for x in MODELS.keys()}

NOISE = {
    "normal": <PERSON>ActionN<PERSON>,
    "ornstein_uhlenbeck": Or<PERSON><PERSON><PERSON><PERSON>beckActionN<PERSON>,
}"""


class DRLAgent:
    """Implementations of DRL algorithms
    Attributes
    ----------
        env: gym environment class
            user-defined class
    Methods
    -------
        get_model()
            setup DRL algorithms
        train_model()
            train DRL algorithms in a train dataset
            and output the trained model
        DRL_prediction()
            make a prediction in a test dataset and get results
    """

    def __init__(self, env, ): # price_array, tech_array, turbulence_array
        self.env = env
        # self.price_array = price_array
        # self.tech_array = tech_array
        # self.turbulence_array = turbulence_array

    def get_model(self, model_name, model_kwargs):
        env_config = {
            # "price_array": self.price_array,
            # "tech_array": self.tech_array,
            # "turbulence_array": self.turbulence_array,
            "if_train": True,
        }
        env = self.env() # config=env_config
        env.env_num = 1
        env_args = get_gym_env_args(env, True)
        print(env_args)
        # env_args["env_num"] = 1
        agent = MODELS[model_name]
        if model_name not in MODELS:
            raise NotImplementedError("NotImplementedError")
        model = Arguments(agent=agent, env=env)
        model.if_off_policy = model_name in OFF_POLICY_MODELS

        if model_kwargs is not None:
            try:
                model.learning_rate = model_kwargs["learning_rate"]
                model.batch_size = model_kwargs["batch_size"]
                model.gamma = model_kwargs["gamma"]
                model.seed = model_kwargs["seed"]
                model.net_dim = model_kwargs["net_dimension"]
                model.target_step = model_kwargs["target_step"]
                model.eval_gap = model_kwargs["eval_gap"]
                model.eval_times = model_kwargs["eval_times"]
            except BaseException:
                raise ValueError(
                    "Fail to read arguments, please check 'model_kwargs' input."
                )
        model.print()
        
        return model

    def train_model(self, model, cwd, total_timesteps=5000):
        model.cwd = cwd
        model.break_step = total_timesteps
        train_and_evaluate(model)

    @staticmethod
    def DRL_prediction(model_name, cwd, net_dimension, environment):
        if model_name not in MODELS:
            raise NotImplementedError("NotImplementedError")
        agent = MODELS[model_name]
        environment.env_num = 1
        args = Arguments(agent=agent, env=environment)
        args.cwd = cwd
        args.net_dim = net_dimension
        # load agent
        try:
            agent = init_agent(args, gpu_id=0)
            act = agent.act
            device = agent.device
        except BaseException:
            raise ValueError("Fail to load agent!")

        # test on the testing env
        _torch = torch
        state = environment.reset()
        episode_returns = []  # the cumulative_return / initial_account
        episode_total_assets = [environment.initial_total_asset]
        with _torch.no_grad():
            for i in range(environment.max_step):
                s_tensor = _torch.as_tensor((state,), device=device)
                a_tensor = act(s_tensor)  # action_tanh = act.forward()
                action = (
                    a_tensor.detach().cpu().numpy()[0]
                )  # not need detach(), because with torch.no_grad() outside
                state, reward, done, _ = environment.step(action)

                total_asset = (
                        environment.amount
                        + (
                                environment.price_ary[environment.day] * environment.stocks
                        ).sum()
                )
                episode_total_assets.append(total_asset)
                episode_return = total_asset / environment.initial_total_asset
                episode_returns.append(episode_return)
                if done:
                    break
        print("Test Finished!")
        # return episode total_assets on testing data
        print("episode_return", episode_return)
        return episode_total_assets
