"""
xLSTM Candlestick Model Example

这个脚本展示了如何使用xLSTM模型进行蜡烛图时序数据预测。
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from pyqlab.models.candlestick.xlstm_model import CandlestickXLSTMModel
from pyqlab.models.candlestick.data_processor import CandlestickProcessor


def load_sample_data(file_path=None):
    """
    加载示例数据或生成随机数据
    """
    if file_path and os.path.exists(file_path):
        # 加载真实数据
        df = pd.read_csv(file_path)
        if 'datetime' not in df.columns and 'date' in df.columns:
            df['datetime'] = pd.to_datetime(df['date'])
        return df
    else:
        # 生成随机数据
        print("未找到数据文件，生成随机数据...")
        np.random.seed(42)
        n_samples = 1000
        
        # 生成日期序列
        dates = pd.date_range(start='2023-01-01', periods=n_samples, freq='1H')
        
        # 生成随机价格
        base_price = 100
        volatility = 1.0
        
        # 生成随机走势
        returns = np.random.normal(0, volatility, n_samples) / 100
        close_prices = base_price * (1 + np.cumsum(returns))
        
        # 生成OHLC
        daily_volatility = volatility * 0.5
        high_prices = close_prices * (1 + np.random.uniform(0, daily_volatility, n_samples) / 100)
        low_prices = close_prices * (1 - np.random.uniform(0, daily_volatility, n_samples) / 100)
        open_prices = low_prices + np.random.uniform(0, 1, n_samples) * (high_prices - low_prices)
        
        # 生成成交量
        volumes = np.random.lognormal(10, 1, n_samples)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        })
        
        return df


def train_model(model, train_loader, val_loader=None, epochs=50, lr=0.001, device='cuda'):
    """
    训练模型
    
    Args:
        model: 模型实例
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        epochs: 训练轮数
        lr: 学习率
        device: 训练设备
        
    Returns:
        训练历史记录
    """
    model = model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=lr)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)
    
    # 损失函数
    criterion = nn.MSELoss()
    
    history = {
        'train_loss': [],
        'val_loss': []
    }
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        for batch in train_loader:
            features = batch['features'].to(device)
            targets = batch['targets'].to(device)
            
            # 准备时间特征和代码（如果有）
            time_features = batch.get('time_features')
            if time_features is not None:
                time_features = time_features.to(device)
                
            codes = batch.get('codes')
            if codes is not None:
                codes = codes.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            outputs = model(features, time_features, codes)
            
            # 计算损失
            if isinstance(outputs, tuple):  # 概率预测
                mean, var = outputs
                loss = -torch.distributions.Normal(mean, torch.sqrt(var)).log_prob(targets).mean()
            else:  # 确定性预测
                loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # 验证阶段
        if val_loader:
            model.eval()
            val_loss = 0
            with torch.no_grad():
                for batch in val_loader:
                    features = batch['features'].to(device)
                    targets = batch['targets'].to(device)
                    
                    time_features = batch.get('time_features')
                    if time_features is not None:
                        time_features = time_features.to(device)
                        
                    codes = batch.get('codes')
                    if codes is not None:
                        codes = codes.to(device)
                    
                    outputs = model(features, time_features, codes)
                    
                    if isinstance(outputs, tuple):  # 概率预测
                        mean, var = outputs
                        loss = -torch.distributions.Normal(mean, torch.sqrt(var)).log_prob(targets).mean()
                    else:  # 确定性预测
                        loss = criterion(outputs, targets)
                    
                    val_loss += loss.item()
            
            val_loss /= len(val_loader)
            history['val_loss'].append(val_loss)
            
            # 更新学习率
            scheduler.step(val_loss)
            
            print(f'Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
        else:
            print(f'Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}')
    
    return history


def evaluate_model(model, test_loader, processor, device='cuda'):
    """
    评估模型
    
    Args:
        model: 模型实例
        test_loader: 测试数据加载器
        processor: 数据处理器实例
        device: 设备
        
    Returns:
        预测结果和真实值
    """
    model = model.to(device)
    model.eval()
    
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for batch in test_loader:
            features = batch['features'].to(device)
            targets = batch['targets'].to(device)
            
            time_features = batch.get('time_features')
            if time_features is not None:
                time_features = time_features.to(device)
                
            codes = batch.get('codes')
            if codes is not None:
                codes = codes.to(device)
            
            outputs = model(features, time_features, codes)
            
            if isinstance(outputs, tuple):  # 概率预测
                mean, _ = outputs
                predictions = mean
            else:  # 确定性预测
                predictions = outputs
            
            all_preds.append(predictions.cpu().numpy())
            all_targets.append(targets.cpu().numpy())
    
    all_preds = np.concatenate(all_preds, axis=0)
    all_targets = np.concatenate(all_targets, axis=0)
    
    # 计算评估指标
    mse = np.mean((all_preds - all_targets) ** 2)
    mae = np.mean(np.abs(all_preds - all_targets))
    
    print(f'测试集 MSE: {mse:.6f}, MAE: {mae:.6f}')
    
    return all_preds, all_targets


def plot_predictions(predictions, targets, idx=0, feature_idx=3, feature_name='close'):
    """
    绘制预测结果
    
    Args:
        predictions: 预测值
        targets: 真实值
        idx: 样本索引
        feature_idx: 特征索引
        feature_name: 特征名称
    """
    plt.figure(figsize=(12, 6))
    
    # 绘制单个样本的预测结果
    plt.plot(targets[idx, :, feature_idx], 'b-', label='真实值')
    plt.plot(predictions[idx, :, feature_idx], 'r--', label='预测值')
    
    plt.title(f'{feature_name}价格预测')
    plt.xlabel('时间步')
    plt.ylabel('价格')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()


def main():
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 检查CUDA是否可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'使用设备: {device}')
    
    # 加载数据
    df = load_sample_data()
    print(f'数据形状: {df.shape}')
    
    # 数据处理
    seq_len = 30
    pred_len = 5
    processor = CandlestickProcessor(
        seq_len=seq_len,
        pred_len=pred_len,
        use_time_features=True,
        time_encoding='timeF',
        normalize_method='zscore'
    )
    
    # 准备数据
    features, time_features, targets = processor.prepare_data(df)
    print(f'特征形状: {features.shape}')
    if time_features is not None:
        print(f'时间特征形状: {time_features.shape}')
    print(f'目标形状: {targets.shape}')
    
    # 创建数据集
    dataset = processor.create_torch_dataset(features, time_features, targets)
    
    # 划分训练集、验证集和测试集
    train_size = int(0.7 * len(dataset))
    val_size = int(0.15 * len(dataset))
    test_size = len(dataset) - train_size - val_size
    
    train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size, test_size]
    )
    
    # 创建数据加载器
    batch_size = 32
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    # 创建模型
    input_size = features.shape[2]  # 特征维度
    time_features_size = time_features.shape[2] if time_features is not None else 0
    output_size = targets.shape[2]  # 目标维度
    
    model = CandlestickXLSTMModel(
        input_size=input_size,
        hidden_size=64,
        head_size=16,
        num_heads=4,
        num_layers=3,
        layer_config='sms',  # sLSTM-mLSTM-sLSTM
        dropout=0.1,
        output_size=output_size,
        seq_len=seq_len,
        pred_len=pred_len,
        use_time_features=time_features is not None,
        time_features_size=time_features_size,
        use_code_embedding=False,
        probabilistic=False
    )
    
    print(f'模型参数数量: {sum(p.numel() for p in model.parameters())}')
    
    # 训练模型
    history = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=50,
        lr=0.001,
        device=device
    )
    
    # 绘制训练历史
    plt.figure(figsize=(10, 5))
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title('训练历史')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.legend()
    plt.grid(True)
    plt.show()
    
    # 评估模型
    predictions, targets = evaluate_model(model, test_loader, processor, device)
    
    # 绘制预测结果
    plot_predictions(predictions, targets, idx=0, feature_idx=3, feature_name='close')
    
    # 保存模型
    torch.save(model.state_dict(), 'candlestick_xlstm_model.pth')
    print('模型已保存')


if __name__ == '__main__':
    main()
