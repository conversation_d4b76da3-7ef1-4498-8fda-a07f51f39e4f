"""
多时间框架 Candlestick LLM 训练示例

演示如何使用多时间框架训练高级K线LLM模型
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和训练器
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.trainer import CandlestickLLMTrainer

def generate_multi_timeframe_data(n_days=100, n_securities=3):
    """
    生成多时间框架的模拟K线数据
    
    Args:
        n_days: 天数
        n_securities: 证券数量
        
    Returns:
        data_dict: 时间框架到数据列表的字典
        code_ids: 证券代码ID列表
    """
    # 定义时间框架
    timeframes = ['1d', '1h', '15m']
    
    # 计算每个时间框架的样本数量
    samples_per_timeframe = {
        '1d': n_days,
        '1h': n_days * 8,  # 每天8小时
        '15m': n_days * 8 * 4  # 每小时4个15分钟
    }
    
    # 创建数据字典
    data_dict = {tf: [] for tf in timeframes}
    code_ids = list(range(n_securities))
    
    for i in range(n_securities):
        # 为每个证券生成基础价格序列（日线）
        base_price = 100.0 * (i + 1)
        daily_prices = [base_price]
        
        for _ in range(n_days - 1):
            # 日线价格变动
            change = np.random.normal(0.0, 0.015)
            daily_prices.append(daily_prices[-1] * (1 + change))
        
        # 生成日线数据
        daily_dates = [datetime.now() - timedelta(days=n_days-j-1) for j in range(n_days)]
        daily_df = pd.DataFrame({
            'datetime': daily_dates,
            'close': daily_prices
        })
        
        # 生成open, high, low
        daily_df['open'] = daily_df['close'].shift(1)
        daily_df.loc[0, 'open'] = daily_df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.01))
        
        for j in range(len(daily_df)):
            high_range = np.random.uniform(0, 0.02)
            low_range = np.random.uniform(0, 0.02)
            daily_df.loc[j, 'high'] = max(daily_df.loc[j, 'open'], daily_df.loc[j, 'close']) * (1 + high_range)
            daily_df.loc[j, 'low'] = min(daily_df.loc[j, 'open'], daily_df.loc[j, 'close']) * (1 - low_range)
        
        # 生成交易量
        daily_df['volume'] = np.random.randint(10000, 100000, size=len(daily_df))
        
        # 添加到数据字典
        data_dict['1d'].append(daily_df)
        
        # 生成小时线数据
        hourly_dates = []
        hourly_prices = []
        
        for day_idx in range(n_days):
            day_open = daily_df['open'].iloc[day_idx]
            day_close = daily_df['close'].iloc[day_idx]
            day_date = daily_df['datetime'].iloc[day_idx]
            
            # 生成当天的8小时数据
            for hour in range(9, 17):  # 9:00 - 16:00
                hour_date = day_date.replace(hour=hour, minute=0, second=0)
                hourly_dates.append(hour_date)
                
                # 计算小时价格
                if hour == 9:
                    hour_price = day_open
                elif hour == 16:
                    hour_price = day_close
                else:
                    # 在日内随机波动
                    progress = (hour - 9) / 7  # 日内进度
                    target = day_open + progress * (day_close - day_open)
                    noise = np.random.normal(0, 0.005)
                    hour_price = target * (1 + noise)
                
                hourly_prices.append(hour_price)
        
        hourly_df = pd.DataFrame({
            'datetime': hourly_dates,
            'close': hourly_prices
        })
        
        # 生成open, high, low
        hourly_df['open'] = hourly_df['close'].shift(1)
        hourly_df.loc[0, 'open'] = hourly_df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.005))
        
        for j in range(len(hourly_df)):
            high_range = np.random.uniform(0, 0.01)
            low_range = np.random.uniform(0, 0.01)
            hourly_df.loc[j, 'high'] = max(hourly_df.loc[j, 'open'], hourly_df.loc[j, 'close']) * (1 + high_range)
            hourly_df.loc[j, 'low'] = min(hourly_df.loc[j, 'open'], hourly_df.loc[j, 'close']) * (1 - low_range)
        
        # 生成交易量
        hourly_df['volume'] = np.random.randint(1000, 10000, size=len(hourly_df))
        
        # 添加到数据字典
        data_dict['1h'].append(hourly_df)
        
        # 生成15分钟线数据
        minute15_dates = []
        minute15_prices = []
        
        for hour_idx in range(len(hourly_df)):
            hour_open = hourly_df['open'].iloc[hour_idx]
            hour_close = hourly_df['close'].iloc[hour_idx]
            hour_date = hourly_df['datetime'].iloc[hour_idx]
            
            # 生成当小时的4个15分钟数据
            for minute in range(0, 60, 15):
                minute_date = hour_date.replace(minute=minute)
                minute15_dates.append(minute_date)
                
                # 计算15分钟价格
                if minute == 0:
                    minute_price = hour_open
                elif minute == 45:
                    minute_price = hour_close
                else:
                    # 在小时内随机波动
                    progress = minute / 45  # 小时内进度
                    target = hour_open + progress * (hour_close - hour_open)
                    noise = np.random.normal(0, 0.003)
                    minute_price = target * (1 + noise)
                
                minute15_prices.append(minute_price)
        
        minute15_df = pd.DataFrame({
            'datetime': minute15_dates,
            'close': minute15_prices
        })
        
        # 生成open, high, low
        minute15_df['open'] = minute15_df['close'].shift(1)
        minute15_df.loc[0, 'open'] = minute15_df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.003))
        
        for j in range(len(minute15_df)):
            high_range = np.random.uniform(0, 0.005)
            low_range = np.random.uniform(0, 0.005)
            minute15_df.loc[j, 'high'] = max(minute15_df.loc[j, 'open'], minute15_df.loc[j, 'close']) * (1 + high_range)
            minute15_df.loc[j, 'low'] = min(minute15_df.loc[j, 'open'], minute15_df.loc[j, 'close']) * (1 - low_range)
        
        # 生成交易量
        minute15_df['volume'] = np.random.randint(100, 1000, size=len(minute15_df))
        
        # 添加到数据字典
        data_dict['15m'].append(minute15_df)
    
    return data_dict, code_ids

def prepare_multi_timeframe_data(data_dict, code_ids, train_ratio=0.8):
    """
    准备多时间框架的训练和验证数据
    
    Args:
        data_dict: 时间框架到数据列表的字典
        code_ids: 证券代码ID列表
        train_ratio: 训练集比例
        
    Returns:
        train_data_dict: 训练数据字典
        train_code_ids: 训练数据代码ID
        val_data_dict: 验证数据字典
        val_code_ids: 验证数据代码ID
    """
    train_data_dict = {}
    val_data_dict = {}
    
    for tf, data_list in data_dict.items():
        train_data_dict[tf] = []
        val_data_dict[tf] = []
        
        for i, df in enumerate(data_list):
            # 计算分割点
            split_idx = int(len(df) * train_ratio)
            
            # 分割数据
            train_df = df.iloc[:split_idx].copy()
            val_df = df.iloc[split_idx:].copy()
            
            # 添加到相应的列表
            train_data_dict[tf].append(train_df)
            val_data_dict[tf].append(val_df)
    
    # 所有时间框架使用相同的代码ID
    train_code_ids = code_ids
    val_code_ids = code_ids
    
    return train_data_dict, train_code_ids, val_data_dict, val_code_ids

def main():
    """主函数"""
    print("生成多时间框架模拟数据...")
    data_dict, code_ids = generate_multi_timeframe_data(n_days=100, n_securities=3)
    
    # 准备训练和验证数据
    train_data_dict, train_code_ids, val_data_dict, val_code_ids = prepare_multi_timeframe_data(
        data_dict, code_ids, train_ratio=0.8
    )
    
    # 打印数据信息
    for tf in train_data_dict.keys():
        print(f"{tf} 训练数据: {len(train_data_dict[tf])} 个证券，每个证券 {len(train_data_dict[tf][0])} 个样本")
        print(f"{tf} 验证数据: {len(val_data_dict[tf])} 个证券，每个证券 {len(val_data_dict[tf][0])} 个样本")
    
    # 创建tokenizer
    print("创建tokenizer...")
    tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )
    
    # 创建模型
    print("创建多时间框架模型...")
    model = AdvancedCandlestickLLM(
        vocab_size=tokenizer.vocab_size,
        code_size=len(code_ids),
        block_size=30,
        n_layer=6,  # 减少层数以加快训练
        n_head=8,
        d_model=512,  # 减少模型维度以加快训练
        dropout=0.1,
        use_time_features=True,
        use_multi_timeframe=True,
        timeframes=list(train_data_dict.keys()),
        use_multi_task=True
    )
    
    # 为简化示例，我们只使用日线数据进行训练
    print("使用日线数据进行训练...")
    trainer = CandlestickLLMTrainer(
        model=model,
        tokenizer=tokenizer,
        train_data=train_data_dict['1d'],
        train_code_ids=train_code_ids,
        val_data=val_data_dict['1d'],
        val_code_ids=val_code_ids,
        seq_len=20,
        batch_size=16,
        learning_rate=5e-4,
        max_epochs=3,  # 减少轮数以加快训练
        log_interval=10,
        eval_interval=50,
        save_interval=100,
        checkpoint_dir='./checkpoints/multi_timeframe_llm'
    )
    
    # 训练模型
    print("开始训练...")
    results = trainer.train()
    
    # 绘制训练历史
    print("绘制训练历史...")
    trainer.plot_training_history()
    
    # 生成样本预测
    print("生成样本预测...")
    sample_df = val_data_dict['1d'][0].iloc[-30:].copy()
    predicted_df = trainer.generate_sample(
        input_df=sample_df,
        code_id=val_code_ids[0],
        max_new_tokens=5,
        temperature=0.8,
        top_k=50
    )
    
    print("预测结果:")
    print(predicted_df)
    
    # 可视化预测结果
    plt.figure(figsize=(12, 6))
    
    # 绘制输入数据
    plt.subplot(1, 2, 1)
    for i in range(len(sample_df)):
        x = i
        open_price = sample_df['open'].iloc[i]
        close_price = sample_df['close'].iloc[i]
        high_price = sample_df['high'].iloc[i]
        low_price = sample_df['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)
    
    plt.title('输入K线数据')
    plt.grid(True)
    
    # 绘制预测数据
    plt.subplot(1, 2, 2)
    for i in range(len(predicted_df)):
        x = i
        open_price = predicted_df['open'].iloc[i]
        close_price = predicted_df['close'].iloc[i]
        high_price = predicted_df['high'].iloc[i]
        low_price = predicted_df['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)
    
    plt.title('预测K线数据')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('./checkpoints/multi_timeframe_llm/prediction_sample.png')
    plt.close()
    
    print("示例完成，结果保存在 ./checkpoints/multi_timeframe_llm/ 目录下")
    print("注意：完整的多时间框架训练需要更复杂的数据处理和训练逻辑，这只是一个简化示例")

if __name__ == '__main__':
    main()
