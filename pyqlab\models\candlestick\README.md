# xLSTM蜡烛图时序预测模型

本模块实现了一个基于xLSTM（扩展长短期记忆网络）的蜡烛图时序数据预测模型。xLSTM结合了稳定化LSTM（sLSTM）和多头LSTM（mLSTM）的优点，能够更好地捕获长期依赖关系，提高预测性能。

## 模型特点

- **xLSTM架构**：结合了sLSTM和mLSTM的优点，更适合长期时间序列预测
- **稳定化机制**：使用指数门控机制提高训练稳定性
- **多头注意力**：结合注意力机制和LSTM，增强模型表达能力
- **概率预测**：支持输出预测分布（均值和方差），而不仅仅是点估计
- **时间特征编码**：支持多种时间特征编码方式
- **证券代码嵌入**：支持证券代码嵌入，捕获不同证券的特性

## 文件结构

- `xlstm_model.py`：xLSTM模型实现
- `data_processor.py`：数据处理工具
- `example.py`：使用示例
- `README.md`：说明文档

## 使用方法

### 1. 数据准备

使用`CandlestickProcessor`类处理蜡烛图数据：

```python
from pyqlab.models.candlestick.data_processor import CandlestickProcessor

# 初始化处理器
processor = CandlestickProcessor(
    seq_len=30,           # 输入序列长度
    pred_len=5,           # 预测序列长度
    use_time_features=True,  # 使用时间特征
    time_encoding='timeF',   # 时间编码方式
    normalize_method='zscore' # 归一化方法
)

# 准备数据
features, time_features, targets = processor.prepare_data(df)

# 创建PyTorch数据集
dataset = processor.create_torch_dataset(features, time_features, targets)
```

### 2. 模型创建

创建xLSTM模型：

```python
from pyqlab.models.candlestick.xlstm_model import CandlestickXLSTMModel

# 创建模型
model = CandlestickXLSTMModel(
    input_size=5,           # 输入特征维度
    hidden_size=64,         # 隐藏层大小
    head_size=16,           # 每个头的大小
    num_heads=4,            # 头的数量
    num_layers=3,           # 层数
    layer_config='sms',     # 层配置，s表示sLSTM，m表示mLSTM
    dropout=0.1,            # Dropout比率
    output_size=5,          # 输出维度
    seq_len=30,             # 输入序列长度
    pred_len=5,             # 预测序列长度
    use_time_features=True, # 是否使用时间特征
    time_features_size=8,   # 时间特征维度
    use_code_embedding=True,# 是否使用证券代码嵌入
    code_embedding_size=16, # 证券代码嵌入维度
    num_codes=100,          # 证券代码数量
    probabilistic=False     # 是否进行概率预测
)
```

### 3. 模型训练

训练模型：

```python
import torch
import torch.optim as optim
from torch.utils.data import DataLoader

# 创建数据加载器
train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=32)

# 训练模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = model.to(device)
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = torch.nn.MSELoss()

for epoch in range(50):
    model.train()
    train_loss = 0
    for batch in train_loader:
        features = batch['features'].to(device)
        targets = batch['targets'].to(device)
        time_features = batch.get('time_features')
        if time_features is not None:
            time_features = time_features.to(device)
        codes = batch.get('codes')
        if codes is not None:
            codes = codes.to(device)
        
        optimizer.zero_grad()
        outputs = model(features, time_features, codes)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()
        
        train_loss += loss.item()
    
    print(f'Epoch {epoch+1}/50, Loss: {train_loss/len(train_loader):.6f}')
```

### 4. 模型预测

使用模型进行预测：

```python
model.eval()
with torch.no_grad():
    features = batch['features'].to(device)
    time_features = batch.get('time_features')
    if time_features is not None:
        time_features = time_features.to(device)
    codes = batch.get('codes')
    if codes is not None:
        codes = codes.to(device)
    
    predictions = model(features, time_features, codes)
    
    # 如果是概率预测模型
    if model.probabilistic:
        mean, var = predictions
        # 使用均值作为预测结果
        predictions = mean
```

### 5. 完整示例

查看`example.py`文件获取完整的使用示例。

## 参考文献

- xLSTMTime: Long-term Time Series Forecasting With xLSTM (https://arxiv.org/abs/2407.10240)
