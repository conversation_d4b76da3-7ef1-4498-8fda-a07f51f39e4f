{"cells": [{"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import datetime"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv('e:/lab/RoboQuant/pylab/data/factors_short_xy.csv')"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>SM</th>\n", "      <th>SP</th>\n", "      <th>SR</th>\n", "      <th>SS</th>\n", "      <th>TA</th>\n", "      <th>UR</th>\n", "      <th>V</th>\n", "      <th>Y</th>\n", "      <th>ZN</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>210922090045119</td>\n", "      <td>77.789003</td>\n", "      <td>78.640329</td>\n", "      <td>200.4</td>\n", "      <td>302.2</td>\n", "      <td>142.030303</td>\n", "      <td>153.951515</td>\n", "      <td>137.015385</td>\n", "      <td>135.775385</td>\n", "      <td>144.101446</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>210922090045123</td>\n", "      <td>58.708151</td>\n", "      <td>58.803915</td>\n", "      <td>-93.6</td>\n", "      <td>-53.0</td>\n", "      <td>43.793939</td>\n", "      <td>20.975758</td>\n", "      <td>41.997692</td>\n", "      <td>42.429231</td>\n", "      <td>61.884091</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>210922090053127</td>\n", "      <td>58.140772</td>\n", "      <td>59.828301</td>\n", "      <td>-34.4</td>\n", "      <td>-40.1</td>\n", "      <td>10.654545</td>\n", "      <td>20.787879</td>\n", "      <td>48.699231</td>\n", "      <td>45.233846</td>\n", "      <td>85.413369</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>210922090139133</td>\n", "      <td>66.567302</td>\n", "      <td>68.784082</td>\n", "      <td>0.3</td>\n", "      <td>43.5</td>\n", "      <td>5.521212</td>\n", "      <td>15.303030</td>\n", "      <td>19.176154</td>\n", "      <td>19.130000</td>\n", "      <td>33.102468</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>210922090503173</td>\n", "      <td>42.458217</td>\n", "      <td>41.638966</td>\n", "      <td>43.5</td>\n", "      <td>-40.5</td>\n", "      <td>-59.569697</td>\n", "      <td>-37.563636</td>\n", "      <td>-38.450769</td>\n", "      <td>-50.829231</td>\n", "      <td>115.702259</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 133 columns</p>\n", "</div>"], "text/plain": ["            ord_id   lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  \\\n", "0  210922090045119  77.789003  78.640329               200.4   \n", "1  210922090045123  58.708151  58.803915               -93.6   \n", "2  210922090053127  58.140772  59.828301               -34.4   \n", "3  210922090139133  66.567302  68.784082                 0.3   \n", "4  210922090503173  42.458217  41.638966                43.5   \n", "\n", "   lf_LR_SLOPE_FAST_2  lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  \\\n", "0               302.2          142.030303          153.951515   \n", "1               -53.0           43.793939           20.975758   \n", "2               -40.1           10.654545           20.787879   \n", "3                43.5            5.521212           15.303030   \n", "4               -40.5          -59.569697          -37.563636   \n", "\n", "   lf_LR_SLOPE_SLOW_1  lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  ...  \\\n", "0          137.015385          135.775385                  144.101446  ...   \n", "1           41.997692           42.429231                   61.884091  ...   \n", "2           48.699231           45.233846                   85.413369  ...   \n", "3           19.176154           19.130000                   33.102468  ...   \n", "4          -38.450769          -50.829231                  115.702259  ...   \n", "\n", "   SM  SP  SR  SS  TA  UR  V  Y  ZN  label  \n", "0   0   0   0   0   0   0  0  0   0      1  \n", "1   0   0   0   0   0   0  0  0   0      1  \n", "2   0   0   0   0   0   0  1  0   0      1  \n", "3   0   0   0   0   0   0  0  0   0      1  \n", "4   0   0   0   0   0   0  0  0   0      1  \n", "\n", "[5 rows x 133 columns]"]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Today add count: 79\n"]}], "source": ["print(\"\\nToday add count: %d\" %  (data['datetime'] >= datetime.datetime.now().strftime(\"%Y%m%d 00:00:00\")).sum())"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1825 entries, 0 to 1824\n", "Columns: 133 entries, ord_id to label\n", "dtypes: float64(89), int64(41), object(3)\n", "memory usage: 1.9+ MB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>lf_LR_SLOPE_SLOW_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>SM</th>\n", "      <th>SP</th>\n", "      <th>SR</th>\n", "      <th>SS</th>\n", "      <th>TA</th>\n", "      <th>UR</th>\n", "      <th>V</th>\n", "      <th>Y</th>\n", "      <th>ZN</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1602</th>\n", "      <td>50.341704</td>\n", "      <td>49.462070</td>\n", "      <td>23.4</td>\n", "      <td>38.0</td>\n", "      <td>5.103030</td>\n", "      <td>3.842424</td>\n", "      <td>2.046154</td>\n", "      <td>3.725385</td>\n", "      <td>39.156238</td>\n", "      <td>20.843648</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1603</th>\n", "      <td>61.857910</td>\n", "      <td>63.391216</td>\n", "      <td>-74.2</td>\n", "      <td>1.8</td>\n", "      <td>-26.151515</td>\n", "      <td>-36.321212</td>\n", "      <td>76.399231</td>\n", "      <td>74.673077</td>\n", "      <td>90.331982</td>\n", "      <td>32.550524</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1604</th>\n", "      <td>59.273599</td>\n", "      <td>59.410253</td>\n", "      <td>-206.9</td>\n", "      <td>-59.5</td>\n", "      <td>-95.600000</td>\n", "      <td>-95.260606</td>\n", "      <td>160.906923</td>\n", "      <td>141.221538</td>\n", "      <td>179.133736</td>\n", "      <td>69.067246</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1605</th>\n", "      <td>49.545625</td>\n", "      <td>49.328048</td>\n", "      <td>-2.2</td>\n", "      <td>36.0</td>\n", "      <td>-55.933333</td>\n", "      <td>-44.721212</td>\n", "      <td>-4.384615</td>\n", "      <td>-5.141538</td>\n", "      <td>58.618849</td>\n", "      <td>28.865475</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1606</th>\n", "      <td>49.545625</td>\n", "      <td>50.793610</td>\n", "      <td>-2.2</td>\n", "      <td>46.8</td>\n", "      <td>-55.933333</td>\n", "      <td>-41.775758</td>\n", "      <td>-4.384615</td>\n", "      <td>-4.643077</td>\n", "      <td>58.652835</td>\n", "      <td>28.863974</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 125 columns</p>\n", "</div>"], "text/plain": ["       lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  lf_LR_SLOPE_FAST_2  \\\n", "1602  50.341704  49.462070                23.4                38.0   \n", "1603  61.857910  63.391216               -74.2                 1.8   \n", "1604  59.273599  59.410253              -206.9               -59.5   \n", "1605  49.545625  49.328048                -2.2                36.0   \n", "1606  49.545625  50.793610                -2.2                46.8   \n", "\n", "      lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  lf_LR_SLOPE_SLOW_1  \\\n", "1602            5.103030            3.842424            2.046154   \n", "1603          -26.151515          -36.321212           76.399231   \n", "1604          -95.600000          -95.260606          160.906923   \n", "1605          -55.933333          -44.721212           -4.384615   \n", "1606          -55.933333          -41.775758           -4.384615   \n", "\n", "      lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  \\\n", "1602            3.725385                   39.156238   \n", "1603           74.673077                   90.331982   \n", "1604          141.221538                  179.133736   \n", "1605           -5.141538                   58.618849   \n", "1606           -4.643077                   58.652835   \n", "\n", "      lf_LR_SLOPE_SLOW_THRESHOLD  ...  SM  SP  SR  SS  TA  UR  V  Y  ZN  label  \n", "1602                   20.843648  ...   0   0   0   0   0   0  0  0   0      0  \n", "1603                   32.550524  ...   0   0   0   0   0   0  0  0   0      0  \n", "1604                   69.067246  ...   0   0   0   0   0   0  0  0   0      0  \n", "1605                   28.865475  ...   0   0   0   0   0   0  0  0   0      0  \n", "1606                   28.863974  ...   0   0   0   0   0   0  0  0   0      1  \n", "\n", "[5 rows x 125 columns]"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["data.drop(columns=['ord_id', 'instrument', 'datetime', 'direct', \"SHORT_RANGE\", \"LONG_RANGE\", \"FAST_QH_RSI_PREV\", \"SLOW_QH_RSI_PREV\" ], axis=1, inplace=True)\n", "data.dropna(axis=0, inplace=True)\n", "data.tail()"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    735\n", "1    585\n", "Name: label, dtype: int64"]}, "execution_count": 141, "metadata": {}, "output_type": "execute_result"}], "source": ["data.label.value_counts()"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [], "source": ["Y_data = data.label.values.reshape(-1, 1)\n", "Y_data = Y_data.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1320, 124)"]}, "execution_count": 143, "metadata": {}, "output_type": "execute_result"}], "source": ["X_data = data[[c for c in data.columns if c != 'label']].values\n", "X_data = X_data.astype(np.float32)\n", "X_data.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 创建模型"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [], "source": ["from torch import nn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 我们将定义一个小函数来创建我们的模型和优化器，以便将来可以重用。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 定义损失函数"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [], "source": ["loss_fn = nn.BCELoss()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 使用DataLoader进行重构"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pytorch DataLoader负责管理批次。\n", "\n", "DataLoader从Dataset创建。\n", "\n", "DataLoader使遍历批次变得更容易。DataLoader会自动为我们提供每个小批量。\n", "\n", "无需使用 HRdataset[i * batch: i * batch + batch]"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [], "source": ["class FactorDataset(torch.utils.data.Dataset):\n", "    def __init__(self, factors, labels) -> None:\n", "        super().__init__()\n", "        self.factors = factors\n", "        self.labels = labels\n", "\n", "    def __getitem__(self, index):\n", "        factor = self.factors[index]\n", "        label = self.labels[index]\n", "        return factor, label\n", "\n", "    def __len__(self):\n", "        return len(self.factors)"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [], "source": ["batch = 64"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import DataLoader, TensorDataset\n", "\n"]}, {"cell_type": "raw", "metadata": {}, "source": ["现在，我们的循环更加简洁了，因为（xb，yb）是从数据加载器自动加载的：\n", "\n", "for x,y in F_dl:\n", "    pred = model(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 添加验证"]}, {"cell_type": "markdown", "metadata": {}, "source": ["前面我们只是试图建立一个合理的训练循环以用于我们的训练数据。实际上，您始终还应该具有一个验证集，以识别您是否过度拟合。\n", "\n", "训练数据的乱序（shuffle）对于防止批次与过度拟合之间的相关性很重要。另一方面，无论我们是否乱序验证集，验证损失都是相同的。由于shufle需要额外的开销，因此shuffle验证数据没有任何意义。\n", "\n", "我们将为验证集使用批大小，该批大小是训练集的两倍。这是因为验证集不需要反向传播，因此占用的内存更少（不需要存储梯度）。我们利用这一优势来使用更大的批量，并更快地计算损失。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["!pip install sklearn -i https://pypi.douban.com/simple/"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [], "source": ["train_x, test_x, train_y, test_y = train_test_split(X_data, Y_data)"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"data": {"text/plain": ["((990, 124), (990, 1), (330, 124), (330, 1))"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["train_x.shape, train_y.shape, test_x.shape, test_y.shape"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [], "source": ["mean = train_x.mean(axis=0)\n", "std = train_x.std(axis=0)\n", "std[std==0] = 1"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["train_x = (train_x - mean) / std\n", "test_x = (test_x - mean) / std\n"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [], "source": ["# train_x = torch.from_numpy(train_x).type(torch.FloatTensor)\n", "# test_x = torch.from_numpy(test_x).type(torch.FloatTensor)\n", "# train_y = torch.from_numpy(train_y).type(torch.FloatTensor)\n", "# test_y = torch.from_numpy(test_y).type(torch.FloatTensor)"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [], "source": ["train_ds = FactorDataset(train_x, train_y)\n", "train_dl = DataLoader(train_ds, batch_size=batch, shuffle=True)\n", "\n", "test_ds = FactorDataset(test_x, test_y)\n", "test_dl = DataLoader(test_ds, batch_size=batch)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 定义计算正确率函数"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["def accuracy(out, yb):\n", "    preds = (out>0.5).type(torch.IntTensor)\n", "    return (preds == yb).float().mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["model.train()在训练之前调用代表训练模式\n", "\n", "model.eval() 推理之前进行调用代表推理模式\n", "\n", "不同的模式仅会在使用nn.BatchNorm2d ，nn.Dropout等层时以确保这些不同阶段的行为正确。"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [], "source": ["epochs = 200"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 优化"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["class Logistic(torch.jit.ScriptModule): # nn.<PERSON><PERSON>\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.lin_1 = nn.Linear(X_data.shape[1], 64)\n", "        self.drop = nn.Dropout(0.5)\n", "        self.bn_1 = nn.BatchNorm1d(64)\n", "        self.lin_2 = nn.<PERSON><PERSON>(64, 64)\n", "        self.bn_2 = nn.BatchNorm1d(64)\n", "        # self.lin_3 = nn.<PERSON>(64, 64)\n", "        self.bn_3 = nn.BatchNorm1d(64)\n", "        self.lin_4 = nn.<PERSON>ar(64, 1)\n", "        self.activate = nn.ReLU()\n", "        self.sigmoid = nn.Sigmoid()\n", "\n", "    @torch.jit.script_method    \n", "    def forward(self, input):\n", "        x = self.lin_1(input)\n", "        x = self.activate(x)\n", "        x= self.drop(x)\n", "        x = self.bn_1(x)\n", "        x = self.lin_2(x)\n", "        x = self.activate(x)\n", "        x = self.bn_2(x)\n", "        x = self.drop(x)\n", "        # x = self.lin_3(x)\n", "        # x = self.activate(x)\n", "        # x = self.drop(x)\n", "        x = self.bn_3(x)\n", "        x = self.lin_4(x)\n", "        x = self.sigmoid(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [], "source": ["lr = 0.0001"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [], "source": ["def get_model():\n", "    model = Logistic()\n", "    return model, torch.optim.Adam(model.parameters(), lr=lr)"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"data": {"text/plain": ["Logistic(\n", "  (lin_1): RecursiveScriptModule(original_name=Linear)\n", "  (drop): RecursiveScriptModule(original_name=Dropout)\n", "  (bn_1): RecursiveScriptModule(original_name=BatchNorm1d)\n", "  (lin_2): RecursiveScriptModule(original_name=Linear)\n", "  (bn_2): RecursiveScriptModule(original_name=BatchNorm1d)\n", "  (bn_3): RecursiveScriptModule(original_name=BatchNorm1d)\n", "  (lin_4): RecursiveScriptModule(original_name=Linear)\n", "  (activate): RecursiveScriptModule(original_name=ReLU)\n", "  (sigmoid): RecursiveScriptModule(original_name=Sigmoid)\n", ")"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}], "source": ["model, opt = get_model()\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [], "source": ["def fit(epoch, model, opt, trainloader, testloader):\n", "\n", "    correct = 0\n", "    total = 0\n", "    running_loss = 0\n", "    for x, y in trainloader:\n", "        x, y = x.to(device), y.to(device)\n", "        y_pred = model(x)\n", "        loss = loss_fn(y_pred, y)\n", "        opt.zero_grad()\n", "        loss.backward()\n", "        opt.step()\n", "        with torch.no_grad():\n", "            correct += ((y_pred>0.5).type(torch.IntTensor).cuda() == y).sum().item()\n", "            total += y.size(0)\n", "            running_loss += loss.item()\n", "\n", "    epoch_loss = running_loss / len(trainloader.dataset) \n", "    epoch_acc = correct / total\n", "\n", "    test_correct = 0\n", "    test_total = 0\n", "    test_running_loss = 0\n", "    with torch.no_grad():\n", "        for x, y in testloader:\n", "            x, y = x.to(device), y.to(device)\n", "            y_pred = model(x)\n", "            loss = loss_fn(y_pred, y)\n", "            test_correct += ((y_pred>0.5).type(torch.IntTensor).cuda() == y).sum().item()\n", "            test_total += y.size(0)\n", "            test_running_loss += loss.item()\n", "    epoch_test_loss = test_running_loss / len(testloader.dataset) \n", "    epoch_test_acc = test_correct / test_total\n", "\n", "    if epoch%50 == 0:\n", "        print('epoch: ', epoch,\n", "            'loss: ', round(epoch_loss, 3),\n", "            'accuracy:', round(epoch_acc, 3),\n", "            'test_loss: ', round(epoch_test_loss, 3),\n", "            'test_accuracy:', round(epoch_test_acc, 3),\n", "        )\n", "    return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch:  50 loss:  0.011 accuracy: 0.576 test_loss:  0.013 test_accuracy: 0.555\n", "epoch:  100 loss:  0.01 accuracy: 0.617 test_loss:  0.013 test_accuracy: 0.576\n", "epoch:  150 loss:  0.01 accuracy: 0.668 test_loss:  0.013 test_accuracy: 0.597\n", "epoch:  200 loss:  0.01 accuracy: 0.692 test_loss:  0.013 test_accuracy: 0.588\n"]}], "source": ["epochs = 200\n", "\n", "train_loss = []\n", "train_acc = []\n", "test_loss = []\n", "test_acc = []\n", "for epoch in range(1, epochs+1):\n", "    epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = fit(epoch,\n", "                            model, opt, train_dl, test_dl)\n", "    train_loss.append(epoch_loss)\n", "    train_acc.append(epoch_acc)\n", "    test_loss.append(epoch_test_loss)\n", "    test_acc.append(epoch_test_acc)"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1fef4033af0>"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(range(1, epochs+1), train_loss, label='train loss')\n", "plt.plot(range(1, epochs+1), test_loss, label='test loss')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1fef389f580>"]}, "execution_count": 166, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(range(1, epochs+1), train_acc, label='train acc')\n", "plt.plot(range(1, epochs+1), test_acc, label='test acc')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["import itertools\n", "from sklearn.metrics import classification_report, confusion_matrix"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [], "source": ["# 画出混淆矩阵\n", "def plot_confusion_matrix(cm, classes):\n", "    cmap=plt.cm.Blues\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "    plt.title(\"Confusion Matrix\")\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "    plt.grid(False)\n", "\n", "    fmt = 'd'\n", "    thresh = cm.max() / 2.\n", "    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):\n", "        plt.text(j, i, format(cm[i, j], 'd'), horizontalalignment=\"center\", \n", "                 color=\"white\" if cm[i, j] > thresh else \"black\")\n", "\n", "    plt.ylabel('True label')\n", "    plt.xlabel('Predicted label')\n", "    plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["from sklearn.metrics import confusion_matrix\n", "confusion_matrix(y_test, y_log_predict)\n", "\n", "from sklearn.metrics import precision_score\n", "precision_score(y_test, y_log_predict)\n", "\n", "from sklearn.metrics import recall_score\n", "recall_score(y_test, y_log_predict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 混淆矩阵\n", "model.eval()\n", "test_x_cuda = torch.from_numpy(test_x).float().to(device)\n", "pred_test = model(test_x_cuda)\n", "pred_test = pred_test.cpu()\n", "cm = confusion_matrix(test_y, pred_test)\n", "plot_confusion_matrix(cm=cm, classes=[0, 1, 2])\n", "print (classification_report(test_y, pred_test))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([64, 124])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["feats.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.eval()\n", "pred_y = model(feats.to(device))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.5077],\n", "        [0.1811],\n", "        [0.1770],\n", "        [0.3691],\n", "        [0.4276]], device='cuda:0', grad_fn=<SliceBackward>)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["pred_y[0:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["md = torch.jit.load(R'e:/lab/RoboQuant/pylab/model/LRS_context_long.model', map_location=device)\n", "md.eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["y = md(feats.to(device))\n", "y[0:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}