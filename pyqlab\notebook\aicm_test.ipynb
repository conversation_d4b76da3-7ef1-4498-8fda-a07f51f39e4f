{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tokenizer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Quotion Dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Min1 Data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>V9999.DC</td>\n", "      <td>2024-01-02 09:01:00</td>\n", "      <td>5880.0</td>\n", "      <td>5882.0</td>\n", "      <td>5861.0</td>\n", "      <td>5881.0</td>\n", "      <td>20277</td>\n", "      <td>1.204727e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>RB9999.SC</td>\n", "      <td>2024-01-02 09:01:00</td>\n", "      <td>4005.0</td>\n", "      <td>4005.0</td>\n", "      <td>3985.0</td>\n", "      <td>3987.0</td>\n", "      <td>41631</td>\n", "      <td>1.951438e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MA9999.ZC</td>\n", "      <td>2024-01-02 09:01:00</td>\n", "      <td>2440.0</td>\n", "      <td>2440.0</td>\n", "      <td>2413.0</td>\n", "      <td>2423.0</td>\n", "      <td>35105</td>\n", "      <td>1.585174e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>M9999.DC</td>\n", "      <td>2024-01-02 09:01:00</td>\n", "      <td>3290.0</td>\n", "      <td>3292.0</td>\n", "      <td>3276.0</td>\n", "      <td>3282.0</td>\n", "      <td>47193</td>\n", "      <td>1.947599e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>P9999.DC</td>\n", "      <td>2024-01-02 09:01:00</td>\n", "      <td>7100.0</td>\n", "      <td>7100.0</td>\n", "      <td>7068.0</td>\n", "      <td>7092.0</td>\n", "      <td>18157</td>\n", "      <td>6.049938e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>511834</th>\n", "      <td>M9999.DC</td>\n", "      <td>2025-05-30 15:00:00</td>\n", "      <td>2967.0</td>\n", "      <td>2968.0</td>\n", "      <td>2966.0</td>\n", "      <td>2968.0</td>\n", "      <td>10103</td>\n", "      <td>3.096322e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>511835</th>\n", "      <td>P9999.DC</td>\n", "      <td>2025-05-30 15:00:00</td>\n", "      <td>8056.0</td>\n", "      <td>8062.0</td>\n", "      <td>8054.0</td>\n", "      <td>8060.0</td>\n", "      <td>6964</td>\n", "      <td>5.403547e-40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>511836</th>\n", "      <td>V9999.DC</td>\n", "      <td>2025-05-30 15:00:00</td>\n", "      <td>4765.0</td>\n", "      <td>4766.0</td>\n", "      <td>4763.0</td>\n", "      <td>4764.0</td>\n", "      <td>4827</td>\n", "      <td>1.415519e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>511837</th>\n", "      <td>MA9999.ZC</td>\n", "      <td>2025-05-30 15:00:00</td>\n", "      <td>2210.0</td>\n", "      <td>2210.0</td>\n", "      <td>2208.0</td>\n", "      <td>2208.0</td>\n", "      <td>6738</td>\n", "      <td>1.155145e-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>511838</th>\n", "      <td>RB9999.SC</td>\n", "      <td>2025-05-30 15:00:00</td>\n", "      <td>2963.0</td>\n", "      <td>2963.0</td>\n", "      <td>2961.0</td>\n", "      <td>2961.0</td>\n", "      <td>13612</td>\n", "      <td>3.217488e-39</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>511839 rows × 8 columns</p>\n", "</div>"], "text/plain": ["             code            datetime    open    high     low   close  volume  \\\n", "0        V9999.DC 2024-01-02 09:01:00  5880.0  5882.0  5861.0  5881.0   20277   \n", "1       RB9999.SC 2024-01-02 09:01:00  4005.0  4005.0  3985.0  3987.0   41631   \n", "2       MA9999.ZC 2024-01-02 09:01:00  2440.0  2440.0  2413.0  2423.0   35105   \n", "3        M9999.DC 2024-01-02 09:01:00  3290.0  3292.0  3276.0  3282.0   47193   \n", "4        P9999.DC 2024-01-02 09:01:00  7100.0  7100.0  7068.0  7092.0   18157   \n", "...           ...                 ...     ...     ...     ...     ...     ...   \n", "511834   M9999.DC 2025-05-30 15:00:00  2967.0  2968.0  2966.0  2968.0   10103   \n", "511835   P9999.DC 2025-05-30 15:00:00  8056.0  8062.0  8054.0  8060.0    6964   \n", "511836   V9999.DC 2025-05-30 15:00:00  4765.0  4766.0  4763.0  4764.0    4827   \n", "511837  MA9999.ZC 2025-05-30 15:00:00  2210.0  2210.0  2208.0  2208.0    6738   \n", "511838  RB9999.SC 2025-05-30 15:00:00  2963.0  2963.0  2961.0  2961.0   13612   \n", "\n", "              amount  \n", "0       1.204727e-39  \n", "1       1.951438e-39  \n", "2       1.585174e-39  \n", "3       1.947599e-39  \n", "4       6.049938e-40  \n", "...              ...  \n", "511834  3.096322e-39  \n", "511835  5.403547e-40  \n", "511836  1.415519e-39  \n", "511837  1.155145e-39  \n", "511838  3.217488e-39  \n", "\n", "[511839 rows x 8 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "data_path = 'f:/hqdata'\n", "df = pd.read_parquet(f'{data_path}/tsdb/fut_top_min1.parquet')\n", "df\n", "# df = df[(df['code'] != 'AG9999.SC')]\n", "# df.reset_index(drop=True, inplace=True)\n", "# df\n", "# df.to_parquet(f'{data_path}/tsdb/fut_top_min1.parquet', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Market Timing Features"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_parquet(R\"F:\\featdata\\tmp\\ffs_fd_1_1.top.2025.parquet\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>AD_1</th>\n", "      <th>AD_2</th>\n", "      <th>ADX_1</th>\n", "      <th>ADX_2</th>\n", "      <th>ADXR_1</th>\n", "      <th>ADXR_2</th>\n", "      <th>APO_1</th>\n", "      <th>APO_2</th>\n", "      <th>...</th>\n", "      <th>TSF_2</th>\n", "      <th>TYPICAL_PRICE_1</th>\n", "      <th>TYPICAL_PRICE_2</th>\n", "      <th>ULTOSC_1</th>\n", "      <th>ULTOSC_2</th>\n", "      <th>VOLUME_1</th>\n", "      <th>VOLUME_2</th>\n", "      <th>WILLR_1</th>\n", "      <th>WILLR_2</th>\n", "      <th>change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>P</td>\n", "      <td>1748327160</td>\n", "      <td>540.0</td>\n", "      <td>540.0</td>\n", "      <td>23.400529</td>\n", "      <td>23.702136</td>\n", "      <td>21.145461</td>\n", "      <td>21.709629</td>\n", "      <td>1.627002</td>\n", "      <td>1.081006</td>\n", "      <td>...</td>\n", "      <td>8030.565806</td>\n", "      <td>24.0</td>\n", "      <td>24.0</td>\n", "      <td>60.000000</td>\n", "      <td>53.248422</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.928711</td>\n", "      <td>-0.000000</td>\n", "      <td>0.0032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>1748327340</td>\n", "      <td>600.0</td>\n", "      <td>600.0</td>\n", "      <td>22.406677</td>\n", "      <td>21.841654</td>\n", "      <td>21.267579</td>\n", "      <td>20.998369</td>\n", "      <td>0.081006</td>\n", "      <td>-0.864990</td>\n", "      <td>...</td>\n", "      <td>8026.641732</td>\n", "      <td>36.0</td>\n", "      <td>36.0</td>\n", "      <td>44.761905</td>\n", "      <td>33.734969</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-48.312378</td>\n", "      <td>-65.199570</td>\n", "      <td>0.0035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>P</td>\n", "      <td>1748327400</td>\n", "      <td>600.0</td>\n", "      <td>600.0</td>\n", "      <td>21.816120</td>\n", "      <td>21.703030</td>\n", "      <td>21.295789</td>\n", "      <td>21.724345</td>\n", "      <td>-0.972998</td>\n", "      <td>-1.000000</td>\n", "      <td>...</td>\n", "      <td>8026.884384</td>\n", "      <td>24.0</td>\n", "      <td>24.0</td>\n", "      <td>40.952381</td>\n", "      <td>46.751578</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-57.299805</td>\n", "      <td>-60.000000</td>\n", "      <td>0.0035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>P</td>\n", "      <td>1748327460</td>\n", "      <td>600.0</td>\n", "      <td>600.0</td>\n", "      <td>21.703030</td>\n", "      <td>21.659641</td>\n", "      <td>21.724345</td>\n", "      <td>21.678205</td>\n", "      <td>-0.654004</td>\n", "      <td>-0.227002</td>\n", "      <td>...</td>\n", "      <td>8028.574000</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "      <td>55.238095</td>\n", "      <td>49.083991</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-22.700195</td>\n", "      <td>-20.000000</td>\n", "      <td>0.0035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>P</td>\n", "      <td>1748327520</td>\n", "      <td>360.0</td>\n", "      <td>360.0</td>\n", "      <td>17.311039</td>\n", "      <td>16.585877</td>\n", "      <td>20.032562</td>\n", "      <td>19.859015</td>\n", "      <td>-1.145996</td>\n", "      <td>-1.972998</td>\n", "      <td>...</td>\n", "      <td>8023.461523</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>43.809524</td>\n", "      <td>48.068832</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-68.916829</td>\n", "      <td>-66.666667</td>\n", "      <td>0.0035</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 237 columns</p>\n", "</div>"], "text/plain": ["  code        date   AD_1   AD_2      ADX_1      ADX_2     ADXR_1     ADXR_2  \\\n", "0    P  1748327160  540.0  540.0  23.400529  23.702136  21.145461  21.709629   \n", "1    P  1748327340  600.0  600.0  22.406677  21.841654  21.267579  20.998369   \n", "2    P  1748327400  600.0  600.0  21.816120  21.703030  21.295789  21.724345   \n", "3    P  1748327460  600.0  600.0  21.703030  21.659641  21.724345  21.678205   \n", "4    P  1748327520  360.0  360.0  17.311039  16.585877  20.032562  19.859015   \n", "\n", "      APO_1     APO_2  ...        TSF_2  TYPICAL_PRICE_1  TYPICAL_PRICE_2  \\\n", "0  1.627002  1.081006  ...  8030.565806             24.0             24.0   \n", "1  0.081006 -0.864990  ...  8026.641732             36.0             36.0   \n", "2 -0.972998 -1.000000  ...  8026.884384             24.0             24.0   \n", "3 -0.654004 -0.227002  ...  8028.574000             24.0              0.0   \n", "4 -1.145996 -1.972998  ...  8023.461523             12.0             12.0   \n", "\n", "    ULTOSC_1   ULTOSC_2  VOLUME_1  VOLUME_2    WILLR_1    WILLR_2  change  \n", "0  60.000000  53.248422       0.0       0.0  -1.928711  -0.000000  0.0032  \n", "1  44.761905  33.734969       0.0       0.0 -48.312378 -65.199570  0.0035  \n", "2  40.952381  46.751578       0.0       0.0 -57.299805 -60.000000  0.0035  \n", "3  55.238095  49.083991       0.0       0.0 -22.700195 -20.000000  0.0035  \n", "4  43.809524  48.068832       0.0       0.0 -68.916829 -66.666667  0.0035  \n", "\n", "[5 rows x 237 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "col_names = [\n", "                # RSI\n", "                \"FAST_RSI_ZSCORE\", \"FAST_RSI_DIRECT\",\n", "                \"SLOW_RSI_ZSCORE\", \"SLOW_RSI_DIRECT\",\n", "                # MOM\n", "                \"FAST_MOM_ZSCORE\", \"FAST_MOM_DIRECT\",\n", "                \"SLOW_MOM_ZSCORE\", \"SLOW_MOM_DIRECT\",\n", "                # FLRS\n", "                \"FAST_FLRS_ZSCORE\", \"FAST_FLRS_DIRECT\",\n", "                \"SLOW_FLRS_ZSCORE\", \"SLOW_FLRS_DIRECT\",\n", "                # MLRS\n", "                \"FAST_MLRS_ZSCORE\", \"FAST_MLRS_DIRECT\",\n", "                \"SLOW_MLRS_ZSCORE\", \"SLOW_MLRS_DIRECT\",\n", "                # NATR\n", "                \"FAST_NATR_ZSCORE\", \"FAST_NATR_DIRECT\",\n", "                \"SLOW_NATR_ZSCORE\", \"SLOW_NATR_DIRECT\",\n", "            ]\n", "df = pd.read_csv('E:/lab/RoboQuant/pylab/data/ft_all.all.00170607085233003.csv')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FAST_RSI_ZSCORE</th>\n", "      <th>FAST_RSI_DIRECT</th>\n", "      <th>SLOW_RSI_ZSCORE</th>\n", "      <th>SLOW_RSI_DIRECT</th>\n", "      <th>FAST_MOM_ZSCORE</th>\n", "      <th>FAST_MOM_DIRECT</th>\n", "      <th>SLOW_MOM_ZSCORE</th>\n", "      <th>SLOW_MOM_DIRECT</th>\n", "      <th>FAST_FLRS_ZSCORE</th>\n", "      <th>FAST_FLRS_DIRECT</th>\n", "      <th>SLOW_FLRS_ZSCORE</th>\n", "      <th>SLOW_FLRS_DIRECT</th>\n", "      <th>FAST_MLRS_ZSCORE</th>\n", "      <th>FAST_MLRS_DIRECT</th>\n", "      <th>SLOW_MLRS_ZSCORE</th>\n", "      <th>SLOW_MLRS_DIRECT</th>\n", "      <th>FAST_NATR_ZSCORE</th>\n", "      <th>FAST_NATR_DIRECT</th>\n", "      <th>SLOW_NATR_ZSCORE</th>\n", "      <th>SLOW_NATR_DIRECT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1509.000000</td>\n", "      <td>1510.000000</td>\n", "      <td>1509.000000</td>\n", "      <td>1510.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.298805</td>\n", "      <td>0.489404</td>\n", "      <td>-0.215131</td>\n", "      <td>0.509934</td>\n", "      <td>0.314929</td>\n", "      <td>0.501987</td>\n", "      <td>0.345237</td>\n", "      <td>0.741060</td>\n", "      <td>0.333098</td>\n", "      <td>0.517219</td>\n", "      <td>0.116085</td>\n", "      <td>0.315232</td>\n", "      <td>0.308010</td>\n", "      <td>0.460265</td>\n", "      <td>0.119812</td>\n", "      <td>0.380795</td>\n", "      <td>0.361049</td>\n", "      <td>0.195364</td>\n", "      <td>1.433616</td>\n", "      <td>0.452980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.250234</td>\n", "      <td>2.502130</td>\n", "      <td>1.350580</td>\n", "      <td>2.607192</td>\n", "      <td>0.962283</td>\n", "      <td>2.317350</td>\n", "      <td>0.672272</td>\n", "      <td>2.154373</td>\n", "      <td>0.995687</td>\n", "      <td>2.366803</td>\n", "      <td>1.448868</td>\n", "      <td>2.850360</td>\n", "      <td>1.175316</td>\n", "      <td>2.468505</td>\n", "      <td>1.439758</td>\n", "      <td>2.904721</td>\n", "      <td>1.165621</td>\n", "      <td>2.329027</td>\n", "      <td>0.927695</td>\n", "      <td>1.720373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>-5.662700</td>\n", "      <td>-4.000000</td>\n", "      <td>-4.993400</td>\n", "      <td>-4.000000</td>\n", "      <td>-3.500000</td>\n", "      <td>-4.000000</td>\n", "      <td>-1.036800</td>\n", "      <td>-4.000000</td>\n", "      <td>-3.643900</td>\n", "      <td>-4.000000</td>\n", "      <td>-2.972600</td>\n", "      <td>-4.000000</td>\n", "      <td>-3.672700</td>\n", "      <td>-4.000000</td>\n", "      <td>-3.196000</td>\n", "      <td>-4.000000</td>\n", "      <td>-2.076900</td>\n", "      <td>-4.000000</td>\n", "      <td>0.666100</td>\n", "      <td>-2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>-0.570200</td>\n", "      <td>-2.000000</td>\n", "      <td>-1.096650</td>\n", "      <td>-2.000000</td>\n", "      <td>-0.334675</td>\n", "      <td>-1.000000</td>\n", "      <td>-0.155125</td>\n", "      <td>-1.000000</td>\n", "      <td>-0.339250</td>\n", "      <td>-1.000000</td>\n", "      <td>-0.980400</td>\n", "      <td>-2.000000</td>\n", "      <td>-0.503675</td>\n", "      <td>-2.000000</td>\n", "      <td>-0.664850</td>\n", "      <td>-2.000000</td>\n", "      <td>-0.506400</td>\n", "      <td>-2.000000</td>\n", "      <td>0.907100</td>\n", "      <td>-1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.252450</td>\n", "      <td>1.000000</td>\n", "      <td>-0.549900</td>\n", "      <td>1.000000</td>\n", "      <td>0.309850</td>\n", "      <td>1.000000</td>\n", "      <td>0.238800</td>\n", "      <td>1.000000</td>\n", "      <td>0.352400</td>\n", "      <td>1.000000</td>\n", "      <td>-0.069400</td>\n", "      <td>1.000000</td>\n", "      <td>0.300200</td>\n", "      <td>1.000000</td>\n", "      <td>0.032850</td>\n", "      <td>1.000000</td>\n", "      <td>0.370900</td>\n", "      <td>-1.000000</td>\n", "      <td>1.178900</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.094200</td>\n", "      <td>2.000000</td>\n", "      <td>0.212925</td>\n", "      <td>3.000000</td>\n", "      <td>0.935800</td>\n", "      <td>2.000000</td>\n", "      <td>0.758675</td>\n", "      <td>2.000000</td>\n", "      <td>0.961075</td>\n", "      <td>2.000000</td>\n", "      <td>1.054775</td>\n", "      <td>3.000000</td>\n", "      <td>1.103550</td>\n", "      <td>2.000000</td>\n", "      <td>0.670000</td>\n", "      <td>3.000000</td>\n", "      <td>1.043700</td>\n", "      <td>2.000000</td>\n", "      <td>1.532600</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>4.274100</td>\n", "      <td>4.000000</td>\n", "      <td>4.606900</td>\n", "      <td>4.000000</td>\n", "      <td>3.466600</td>\n", "      <td>4.000000</td>\n", "      <td>2.645700</td>\n", "      <td>4.000000</td>\n", "      <td>3.248500</td>\n", "      <td>4.000000</td>\n", "      <td>4.482200</td>\n", "      <td>4.000000</td>\n", "      <td>4.031500</td>\n", "      <td>4.000000</td>\n", "      <td>5.217800</td>\n", "      <td>4.000000</td>\n", "      <td>5.405500</td>\n", "      <td>4.000000</td>\n", "      <td>6.709800</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       FAST_RSI_ZSCORE  FAST_RSI_DIRECT  SLOW_RSI_ZSCORE  SLOW_RSI_DIRECT  \\\n", "count      1510.000000      1510.000000      1510.000000      1510.000000   \n", "mean          0.298805         0.489404        -0.215131         0.509934   \n", "std           1.250234         2.502130         1.350580         2.607192   \n", "min          -5.662700        -4.000000        -4.993400        -4.000000   \n", "25%          -0.570200        -2.000000        -1.096650        -2.000000   \n", "50%           0.252450         1.000000        -0.549900         1.000000   \n", "75%           1.094200         2.000000         0.212925         3.000000   \n", "max           4.274100         4.000000         4.606900         4.000000   \n", "\n", "       FAST_MOM_ZSCORE  FAST_MOM_DIRECT  SLOW_MOM_ZSCORE  SLOW_MOM_DIRECT  \\\n", "count      1510.000000      1510.000000      1510.000000      1510.000000   \n", "mean          0.314929         0.501987         0.345237         0.741060   \n", "std           0.962283         2.317350         0.672272         2.154373   \n", "min          -3.500000        -4.000000        -1.036800        -4.000000   \n", "25%          -0.334675        -1.000000        -0.155125        -1.000000   \n", "50%           0.309850         1.000000         0.238800         1.000000   \n", "75%           0.935800         2.000000         0.758675         2.000000   \n", "max           3.466600         4.000000         2.645700         4.000000   \n", "\n", "       FAST_FLRS_ZSCORE  FAST_FLRS_DIRECT  SLOW_FLRS_ZSCORE  SLOW_FLRS_DIRECT  \\\n", "count       1510.000000       1510.000000       1510.000000       1510.000000   \n", "mean           0.333098          0.517219          0.116085          0.315232   \n", "std            0.995687          2.366803          1.448868          2.850360   \n", "min           -3.643900         -4.000000         -2.972600         -4.000000   \n", "25%           -0.339250         -1.000000         -0.980400         -2.000000   \n", "50%            0.352400          1.000000         -0.069400          1.000000   \n", "75%            0.961075          2.000000          1.054775          3.000000   \n", "max            3.248500          4.000000          4.482200          4.000000   \n", "\n", "       FAST_MLRS_ZSCORE  FAST_MLRS_DIRECT  SLOW_MLRS_ZSCORE  SLOW_MLRS_DIRECT  \\\n", "count       1510.000000       1510.000000       1510.000000       1510.000000   \n", "mean           0.308010          0.460265          0.119812          0.380795   \n", "std            1.175316          2.468505          1.439758          2.904721   \n", "min           -3.672700         -4.000000         -3.196000         -4.000000   \n", "25%           -0.503675         -2.000000         -0.664850         -2.000000   \n", "50%            0.300200          1.000000          0.032850          1.000000   \n", "75%            1.103550          2.000000          0.670000          3.000000   \n", "max            4.031500          4.000000          5.217800          4.000000   \n", "\n", "       FAST_NATR_ZSCORE  FAST_NATR_DIRECT  SLOW_NATR_ZSCORE  SLOW_NATR_DIRECT  \n", "count       1509.000000       1510.000000       1509.000000       1510.000000  \n", "mean           0.361049          0.195364          1.433616          0.452980  \n", "std            1.165621          2.329027          0.927695          1.720373  \n", "min           -2.076900         -4.000000          0.666100         -2.000000  \n", "25%           -0.506400         -2.000000          0.907100         -1.000000  \n", "50%            0.370900         -1.000000          1.178900          1.000000  \n", "75%            1.043700          2.000000          1.532600          1.000000  \n", "max            5.405500          4.000000          6.709800          4.000000  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[col_names].describe()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAj8AAAHHCAYAAABQhTneAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABJOUlEQVR4nO3dd3RUdf7/8deEkCKkEEwhGkKkhaJ0kCYiUQREEFCBqDTFdekgKioiWAAVRFgE8QioS1YXFRT9CrJIUXoREMVQhJAVEmKADIkwpNzfH/wyy6RABibMJPf5OOee43xumfedCeaVz/187rUYhmEIAADAJLzcXQAAAMD1RPgBAACmQvgBAACmQvgBAACmQvgBAACmQvgBAACmQvgBAACmQvgBAACmQvgBAACmQvgBgDLGYrHo5Zdftr9evHixLBaLjh49WurvPXDgQNWoUcP++ujRo7JYLHrrrbdK/b0l6eWXX5bFYrku74Xyi/CDMu/nn39Wnz59FB0dLT8/********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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# df[\"SLOW_NATR_ZSCORE\"]的分布图\n", "import matplotlib.pyplot as plt\n", "\n", "plt.hist(df[\"SLOW_NATR_ZSCORE\"], bins=100, alpha=0.7, color='blue', ec='black')\n", "plt.title(\"SLOW_NATR_ZSCORE distribution\")\n", "plt.xlabel(\"value\")\n", "plt.ylabel(\"frequency\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bar encoding"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "from copy import deepcopy\n", "import pandas as pd\n", "from pyqlab.data.dataset.utils import get_vocab, idx2token\n", "from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES, SNAPSHOT_CONTEXT"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["bar_set = get_vocab()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(bar_set, columns=['bar'], dtype=str, index=None)\n", "df.to_csv('bar_set.csv')"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>datetime</th>\n", "      <th>bar</th>\n", "      <th>seq_volatility</th>\n", "      <th>seq_amplitude</th>\n", "      <th>seq_change</th>\n", "      <th>fut_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>M</td>\n", "      <td>1735809360</td>\n", "      <td>1176</td>\n", "      <td>5.971195</td>\n", "      <td>35.0</td>\n", "      <td>1.112347</td>\n", "      <td>-0.036670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>M</td>\n", "      <td>1735809420</td>\n", "      <td>1176</td>\n", "      <td>8.347413</td>\n", "      <td>37.0</td>\n", "      <td>1.224036</td>\n", "      <td>-0.183217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>M</td>\n", "      <td>1735809480</td>\n", "      <td>134</td>\n", "      <td>9.763938</td>\n", "      <td>37.0</td>\n", "      <td>1.112760</td>\n", "      <td>-0.036684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>M</td>\n", "      <td>1735809540</td>\n", "      <td>963</td>\n", "      <td>10.890468</td>\n", "      <td>37.0</td>\n", "      <td>1.150278</td>\n", "      <td>-0.036684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>M</td>\n", "      <td>1735809600</td>\n", "      <td>603</td>\n", "      <td>11.677712</td>\n", "      <td>37.0</td>\n", "      <td>1.076067</td>\n", "      <td>0.073421</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol    datetime   bar  seq_volatility  seq_amplitude  seq_change  \\\n", "0      M  1735809360  1176        5.971195           35.0    1.112347   \n", "1      M  1735809420  1176        8.347413           37.0    1.224036   \n", "2      M  1735809480   134        9.763938           37.0    1.112760   \n", "3      M  1735809540   963       10.890468           37.0    1.150278   \n", "4      M  1735809600   603       11.677712           37.0    1.076067   \n", "\n", "   fut_change  \n", "0   -0.036670  \n", "1   -0.183217  \n", "2   -0.036684  \n", "3   -0.036684  \n", "4    0.073421  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv')\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           datetime           bar  seq_volatility  seq_amplitude  \\\n", "count  7.413500e+04  74135.000000    74135.000000   74135.000000   \n", "mean   1.740901e+09    781.156215        5.652554      23.367175   \n", "std    2.884243e+06    320.060776        7.189769      24.204511   \n", "min    1.735809e+09      0.000000        0.711967       3.000000   \n", "25%    1.738764e+09    600.000000        2.235811      10.000000   \n", "50%    1.741082e+09    790.000000        3.500575      15.000000   \n", "75%    1.743415e+09    963.000000        6.414423      27.000000   \n", "max    1.745593e+09   1599.000000      188.504093     432.000000   \n", "\n", "         seq_change    fut_change  \n", "count  74135.000000  74135.000000  \n", "mean      -0.001091     -0.000899  \n", "std        0.420777      0.138382  \n", "min       -5.415617     -7.087166  \n", "25%       -0.176484     -0.059067  \n", "50%        0.000000      0.000000  \n", "75%        0.175871      0.057742  \n", "max        8.260413      7.364747  \n"]}], "source": ["print(df.describe())"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制df['bar']分布图\n", "import matplotlib.pyplot as plt\n", "plt.hist(df['bar'], bins=100, alpha=0.7, color='blue', ec='black')\n", "plt.title(\"bar distribution\")\n", "plt.xlabel(\"value\")\n", "plt.ylabel(\"frequency\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           datetime         change         entity         upline  \\\n", "count  6.238300e+05  623830.000000  623830.000000  623830.000000   \n", "mean   1.725045e+09       0.006673      -0.009549       2.292597   \n", "std    1.196923e+07      11.326645       8.595124       3.177351   \n", "min    1.704193e+09   -1898.000000    -336.000000       0.000000   \n", "25%    1.715118e+09      -3.000000      -3.000000       0.000000   \n", "50%    1.724758e+09       0.000000       0.000000       2.000000   \n", "75%    1.735134e+09       3.000000       3.000000       3.000000   \n", "max    1.745593e+09    1086.000000     314.000000     165.000000   \n", "\n", "            downline  seq_volatility  seq_amplitude     seq_change  \\\n", "count  623830.000000   623830.000000  623830.000000  623830.000000   \n", "mean        2.303275        6.312850      26.117682       0.000470   \n", "std         3.320045        7.979646      25.741265       0.394416   \n", "min         0.000000        0.000000       0.000000      -7.271996   \n", "25%         0.000000        2.473770      11.000000      -0.162602   \n", "50%         2.000000        4.083046      18.000000       0.000000   \n", "75%         3.000000        7.416121      32.000000       0.164183   \n", "max       303.000000      354.454348     730.000000       8.260413   \n", "\n", "          fut_change  \n", "count  623830.000000  \n", "mean        0.000045  \n", "std         0.125866  \n", "min        -7.087166  \n", "25%        -0.051211  \n", "50%         0.000000  \n", "75%         0.051216  \n", "max         7.364747  \n"]}], "source": ["df = pd.read_csv('f:/featdata/barenc/db2/bar_fut_top_min1.tmp.csv')\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>change</th>\n", "      <th>entity</th>\n", "      <th>upline</th>\n", "      <th>downline</th>\n", "      <th>seq_volatility</th>\n", "      <th>seq_amplitude</th>\n", "      <th>seq_change</th>\n", "      <th>fut_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>6.238300e+05</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "      <td>623830.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.725045e+09</td>\n", "      <td>0.006673</td>\n", "      <td>-0.009549</td>\n", "      <td>2.292597</td>\n", "      <td>2.303275</td>\n", "      <td>6.312850</td>\n", "      <td>26.117682</td>\n", "      <td>0.000470</td>\n", "      <td>0.000045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.196923e+07</td>\n", "      <td>11.326645</td>\n", "      <td>8.595124</td>\n", "      <td>3.177351</td>\n", "      <td>3.320045</td>\n", "      <td>7.979646</td>\n", "      <td>25.741265</td>\n", "      <td>0.394416</td>\n", "      <td>0.125866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.704193e+09</td>\n", "      <td>-1898.000000</td>\n", "      <td>-336.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-7.271996</td>\n", "      <td>-7.087166</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.715118e+09</td>\n", "      <td>-3.000000</td>\n", "      <td>-3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.473770</td>\n", "      <td>11.000000</td>\n", "      <td>-0.162602</td>\n", "      <td>-0.051211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.724758e+09</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>4.083046</td>\n", "      <td>18.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.735134e+09</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>7.416121</td>\n", "      <td>32.000000</td>\n", "      <td>0.164183</td>\n", "      <td>0.051216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.745593e+09</td>\n", "      <td>1086.000000</td>\n", "      <td>314.000000</td>\n", "      <td>165.000000</td>\n", "      <td>303.000000</td>\n", "      <td>354.454348</td>\n", "      <td>730.000000</td>\n", "      <td>8.260413</td>\n", "      <td>7.364747</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           datetime         change         entity         upline  \\\n", "count  6.238300e+05  623830.000000  623830.000000  623830.000000   \n", "mean   1.725045e+09       0.006673      -0.009549       2.292597   \n", "std    1.196923e+07      11.326645       8.595124       3.177351   \n", "min    1.704193e+09   -1898.000000    -336.000000       0.000000   \n", "25%    1.715118e+09      -3.000000      -3.000000       0.000000   \n", "50%    1.724758e+09       0.000000       0.000000       2.000000   \n", "75%    1.735134e+09       3.000000       3.000000       3.000000   \n", "max    1.745593e+09    1086.000000     314.000000     165.000000   \n", "\n", "            downline  seq_volatility  seq_amplitude     seq_change  \\\n", "count  623830.000000   623830.000000  623830.000000  623830.000000   \n", "mean        2.303275        6.312850      26.117682       0.000470   \n", "std         3.320045        7.979646      25.741265       0.394416   \n", "min         0.000000        0.000000       0.000000      -7.271996   \n", "25%         0.000000        2.473770      11.000000      -0.162602   \n", "50%         2.000000        4.083046      18.000000       0.000000   \n", "75%         3.000000        7.416121      32.000000       0.164183   \n", "max       303.000000      354.454348     730.000000       8.260413   \n", "\n", "          fut_change  \n", "count  623830.000000  \n", "mean        0.000045  \n", "std         0.125866  \n", "min        -7.087166  \n", "25%        -0.051211  \n", "50%         0.000000  \n", "75%         0.051216  \n", "max         7.364747  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(846521, 3)\n", "(846521, 3)\n", "(717687, 3)\n", "(717687, 3)\n"]}], "source": ["import pandas as pd\n", "years = range(2023, 2025)\n", "dfs = pd.DataFrame()\n", "for year in years:\n", "    df = pd.read_csv(f'f:/featdata/barenc/bar_fut_main_min5_{year}.csv')\n", "    print(df.shape)\n", "    df = df[(pd.to_datetime(df['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 9]\n", "    print(df.shape)\n", "    df.to_csv(f'f:/featdata/barenc/bar_fut_main_min5_{year}.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(421523, 3)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>timestamp</th>\n", "      <th>bar</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IF</td>\n", "      <td>1271659500</td>\n", "      <td>18427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>IF</td>\n", "      <td>1271659800</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>IF</td>\n", "      <td>1271660100</td>\n", "      <td>21692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>IF</td>\n", "      <td>1271660400</td>\n", "      <td>39993</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>IF</td>\n", "      <td>1271660700</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421518</th>\n", "      <td>IH</td>\n", "      <td>1727678400</td>\n", "      <td>39972</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421519</th>\n", "      <td>IH</td>\n", "      <td>1727678700</td>\n", "      <td>23366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421520</th>\n", "      <td>IH</td>\n", "      <td>1727679000</td>\n", "      <td>28347</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421521</th>\n", "      <td>IH</td>\n", "      <td>1727679300</td>\n", "      <td>25000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421522</th>\n", "      <td>IH</td>\n", "      <td>1727679600</td>\n", "      <td>33306</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>421523 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       symbol   timestamp    bar\n", "0          IF  1271659500  18427\n", "1          IF  1271659800      7\n", "2          IF  1271660100  21692\n", "3          IF  1271660400  39993\n", "4          IF  1271660700      7\n", "...       ...         ...    ...\n", "421518     IH  1727678400  39972\n", "421519     IH  1727678700  23366\n", "421520     IH  1727679000  28347\n", "421521     IH  1727679300  25000\n", "421522     IH  1727679600  33306\n", "\n", "[421523 rows x 3 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "years = range(2010, 2025)\n", "dfs = pd.DataFrame()\n", "for year in years:\n", "    df = pd.read_csv(f'f:/featdata/barenc/bak/bar_fut_sf_min5_{year}.csv')\n", "    dfs = pd.concat([dfs, df], axis=0)\n", "    # 通过columns=['symbol','timestamp']去重\n", "    # dfs = dfs.drop_duplicates(subset=['symbol', 'timestamp'], keep='last')\n", "    # print(year, dfs.shape)\n", "# dfs['timestamp'] = pd.to_datetime(dfs['timestamp'], unit='s') + pd.Timedelta(hours=8)\n", "dfs = dfs[(pd.to_datetime(dfs['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.time >= pd.to_datetime('09:30:00').time()]\n", "dfs.reset_index(drop=True, inplace=True)\n", "dfs.to_csv('f:/featdata/barenc/bar_fut_sf_min5.csv', index=False)\n", "print(dfs.shape)\n", "dfs"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["df=pd.read_parquet('e:/featdata/main/ffs_ct.main.2024.parquet')\n", "df = df[SNAPSHOT_CONTEXT[16:-8]]\n", "df.describe().to_csv('e:/featdata/main/ffs_ct.main.2024.csv')"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1850109, 4)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["df=pd.read_parquet('e:/hqdata/tick/2024/SF202409.parquet')\n", "df.shape"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1015780, 4)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>datetime</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2024-09-02 09:29:00.100</td>\n", "      <td>4623.0</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2024-09-02 09:29:00.100</td>\n", "      <td>3309.0</td>\n", "      <td>160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2024-09-02 09:29:00.100</td>\n", "      <td>2326.0</td>\n", "      <td>147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2024-09-02 09:29:00.100</td>\n", "      <td>4612.0</td>\n", "      <td>191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2024-09-02 09:30:00.600</td>\n", "      <td>4618.2</td>\n", "      <td>202</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2024-09-02 09:30:00.600</td>\n", "      <td>3310.8</td>\n", "      <td>364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2024-09-02 09:30:00.600</td>\n", "      <td>2326.8</td>\n", "      <td>272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2024-09-02 09:30:00.600</td>\n", "      <td>4614.8</td>\n", "      <td>282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2024-09-02 09:30:01.600</td>\n", "      <td>4622.8</td>\n", "      <td>259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2024-09-02 09:30:01.600</td>\n", "      <td>3310.8</td>\n", "      <td>498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2024-09-02 09:30:01.600</td>\n", "      <td>2325.4</td>\n", "      <td>337</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2024-09-02 09:30:01.600</td>\n", "      <td>4617.2</td>\n", "      <td>379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2024-09-02 09:30:02.600</td>\n", "      <td>4622.0</td>\n", "      <td>332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2024-09-02 09:30:02.600</td>\n", "      <td>3311.0</td>\n", "      <td>592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2024-09-02 09:30:02.600</td>\n", "      <td>2325.6</td>\n", "      <td>451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2024-09-02 09:30:02.600</td>\n", "      <td>4616.8</td>\n", "      <td>431</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>IC9999.SF</td>\n", "      <td>2024-09-02 09:30:03.600</td>\n", "      <td>4624.2</td>\n", "      <td>358</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>IF9999.SF</td>\n", "      <td>2024-09-02 09:30:03.600</td>\n", "      <td>3311.2</td>\n", "      <td>698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>IH9999.SF</td>\n", "      <td>2024-09-02 09:30:03.600</td>\n", "      <td>2324.8</td>\n", "      <td>543</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>IM9999.SF</td>\n", "      <td>2024-09-02 09:30:03.600</td>\n", "      <td>4619.6</td>\n", "      <td>516</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         code                datetime   price  volume\n", "0   IC9999.SF 2024-09-02 09:29:00.100  4623.0     107\n", "1   IF9999.SF 2024-09-02 09:29:00.100  3309.0     160\n", "2   IH9999.SF 2024-09-02 09:29:00.100  2326.0     147\n", "3   IM9999.SF 2024-09-02 09:29:00.100  4612.0     191\n", "4   IC9999.SF 2024-09-02 09:30:00.600  4618.2     202\n", "5   IF9999.SF 2024-09-02 09:30:00.600  3310.8     364\n", "6   IH9999.SF 2024-09-02 09:30:00.600  2326.8     272\n", "7   IM9999.SF 2024-09-02 09:30:00.600  4614.8     282\n", "8   IC9999.SF 2024-09-02 09:30:01.600  4622.8     259\n", "9   IF9999.SF 2024-09-02 09:30:01.600  3310.8     498\n", "10  IH9999.SF 2024-09-02 09:30:01.600  2325.4     337\n", "11  IM9999.SF 2024-09-02 09:30:01.600  4617.2     379\n", "12  IC9999.SF 2024-09-02 09:30:02.600  4622.0     332\n", "13  IF9999.SF 2024-09-02 09:30:02.600  3311.0     592\n", "14  IH9999.SF 2024-09-02 09:30:02.600  2325.6     451\n", "15  IM9999.SF 2024-09-02 09:30:02.600  4616.8     431\n", "16  IC9999.SF 2024-09-02 09:30:03.600  4624.2     358\n", "17  IF9999.SF 2024-09-02 09:30:03.600  3311.2     698\n", "18  IH9999.SF 2024-09-02 09:30:03.600  2324.8     543\n", "19  IM9999.SF 2024-09-02 09:30:03.600  4619.6     516"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df=df.groupby(['code', df['datetime'].dt.floor('1s')], as_index=False).last()\n", "df.sort_values(['datetime', 'code'], inplace=True)\n", "df.reset_index(inplace=True, drop=True)\n", "print(df.shape)\n", "df.head(20)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20620\\181238675.py:1: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  df1 = df.groupby(['code', df['datetime'].dt.floor('10s')]).apply(lambda group: group.loc[group['price'].idxmin()])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(219406, 4)\n", "         code                datetime   price  volume\n", "0   IC9999.SF 2024-09-02 09:29:00.100  4623.0     107\n", "1   IC9999.SF 2024-09-02 09:29:00.100  4623.0     107\n", "2   IF9999.SF 2024-09-02 09:29:00.100  3309.0     160\n", "3   IF9999.SF 2024-09-02 09:29:00.100  3309.0     160\n", "4   IH9999.SF 2024-09-02 09:29:00.100  2326.0     147\n", "5   IH9999.SF 2024-09-02 09:29:00.100  2326.0     147\n", "6   IM9999.SF 2024-09-02 09:29:00.100  4612.0     191\n", "7   IM9999.SF 2024-09-02 09:29:00.100  4612.0     191\n", "8   IF9999.SF 2024-09-02 09:30:00.100  3309.2     203\n", "9   IH9999.SF 2024-09-02 09:30:00.100  2327.4     175\n", "10  IC9999.SF 2024-09-02 09:30:00.600  4618.2     202\n", "11  IM9999.SF 2024-09-02 09:30:00.600  4614.8     282\n", "12  IH9999.SF 2024-09-02 09:30:03.600  2324.8     543\n", "13  IC9999.SF 2024-09-02 09:30:07.100  4628.0     454\n", "14  IM9999.SF 2024-09-02 09:30:07.100  4623.4     690\n", "15  IF9999.SF 2024-09-02 09:30:09.100  3312.4    1057\n", "16  IC9999.SF 2024-09-02 09:30:10.100  4627.8     586\n", "17  IF9999.SF 2024-09-02 09:30:10.600  3312.4    1129\n", "18  IH9999.SF 2024-09-02 09:30:10.600  2326.6     905\n", "19  IM9999.SF 2024-09-02 09:30:10.600  4622.0     892\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20620\\181238675.py:3: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  df2 = df.groupby(['code', df['datetime'].dt.floor('10s')]).apply(lambda group: group.loc[group['price'].idxmax()])\n"]}], "source": ["df1 = df.groupby(['code', df['datetime'].dt.floor('10s')]).apply(lambda group: group.loc[group['price'].idxmin()])\n", "df1.reset_index(inplace=True, drop=True)\n", "df2 = df.groupby(['code', df['datetime'].dt.floor('10s')]).apply(lambda group: group.loc[group['price'].idxmax()])\n", "df2.reset_index(inplace=True, drop=True)\n", "df=pd.concat([df1, df2])\n", "df.sort_values(['datetime', 'code'], inplace=True)\n", "df.reset_index(inplace=True, drop=True)\n", "print(df.shape)\n", "print(df.head(20))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Qtunnel Test"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d:\\QuantLab\n"]}], "source": ["ds=DataSource(RunMode.passive)\n", "print(ds.get_run_dir())"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['IC8888.SF', 'IC8888.SF', 'IF8888.SF', 'IF8888.SF', 'IH8888.SF', 'IH8888.SF', 'IM8888.SF', 'IM8888.SF', 'AP8888.ZC', 'CF8888.ZC', 'CJ8888.ZC', 'CY8888.ZC', 'FG8888.ZC', 'MA8888.ZC', 'OI8888.ZC', 'PF8888.ZC', 'PK8888.ZC', 'PX8888.ZC', 'RM8888.ZC', 'SA8888.ZC', 'SF8888.ZC', 'SH8888.ZC', 'SM8888.ZC', 'SR8888.ZC', 'TA8888.ZC', 'UR8888.ZC', 'A8888.DC', 'B8888.DC', 'C8888.DC', 'CS8888.DC', 'EB8888.DC', 'EG8888.DC', 'I8888.DC', 'J8888.DC', 'JD8888.DC', 'JM8888.DC', 'L8888.DC', 'LH8888.DC', 'M8888.DC', 'P8888.DC', 'PG8888.DC', 'PP8888.DC', 'RR8888.DC', 'RR8888.DC', 'V8888.DC', 'Y8888.DC', 'AG8888.SC', 'AL8888.SC', 'AO8888.SC', 'AU8888.SC', 'BR8888.SC', 'BU8888.SC', 'CU8888.SC', 'HC8888.SC', 'NI8888.SC', 'NR8888.SC', 'PB8888.SC', 'RB8888.SC', 'RU8888.SC', 'SC8888.SC', 'SN8888.SC', 'SP8888.SC', 'SS8888.SC', 'ZN8888.SC']\n"]}], "source": ["blkname=[\"ZLQH\", \"ZLLX\",\"主选期货\", \"主力9999\", \"股指板块\", \"沪深300\", \"中证500\", \"中证1000\"]\n", "zlqh = ds.get_block_data(blkname[1])\n", "print(zlqh)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gzbk = ds.get_block_data(blkname[4])\n", "for i in range(10):\n", "    print(gzbk[i], ds.get_stk_code_index(gzbk[i]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zllx = ds.get_block_data(blkname[1])\n", "for i in range(10):\n", "    code = zllx[i][0:len(zllx[i])-7]\n", "    print(code, ds.get_fut_code_index(code))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2665, 5)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2013-10-18</td>\n", "      <td>978.0</td>\n", "      <td>984.0</td>\n", "      <td>962.0</td>\n", "      <td>977.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2013-10-21</td>\n", "      <td>976.0</td>\n", "      <td>977.0</td>\n", "      <td>960.0</td>\n", "      <td>969.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2013-10-22</td>\n", "      <td>963.0</td>\n", "      <td>966.0</td>\n", "      <td>948.0</td>\n", "      <td>948.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2013-10-23</td>\n", "      <td>949.0</td>\n", "      <td>953.0</td>\n", "      <td>936.0</td>\n", "      <td>939.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2013-10-24</td>\n", "      <td>938.0</td>\n", "      <td>939.0</td>\n", "      <td>916.0</td>\n", "      <td>926.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2660</th>\n", "      <td>2024-09-19</td>\n", "      <td>675.0</td>\n", "      <td>694.5</td>\n", "      <td>660.5</td>\n", "      <td>693.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2661</th>\n", "      <td>2024-09-20</td>\n", "      <td>692.0</td>\n", "      <td>703.0</td>\n", "      <td>676.5</td>\n", "      <td>680.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2662</th>\n", "      <td>2024-09-23</td>\n", "      <td>680.0</td>\n", "      <td>681.5</td>\n", "      <td>658.0</td>\n", "      <td>658.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2663</th>\n", "      <td>2024-09-24</td>\n", "      <td>658.0</td>\n", "      <td>701.5</td>\n", "      <td>657.5</td>\n", "      <td>699.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2664</th>\n", "      <td>2024-09-25</td>\n", "      <td>704.0</td>\n", "      <td>730.5</td>\n", "      <td>702.5</td>\n", "      <td>723.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2665 rows × 5 columns</p>\n", "</div>"], "text/plain": ["       datetime   open   high    low  close\n", "0    2013-10-18  978.0  984.0  962.0  977.0\n", "1    2013-10-21  976.0  977.0  960.0  969.0\n", "2    2013-10-22  963.0  966.0  948.0  948.0\n", "3    2013-10-23  949.0  953.0  936.0  939.0\n", "4    2013-10-24  938.0  939.0  916.0  926.0\n", "...         ...    ...    ...    ...    ...\n", "2660 2024-09-19  675.0  694.5  660.5  693.0\n", "2661 2024-09-20  692.0  703.0  676.5  680.0\n", "2662 2024-09-23  680.0  681.5  658.0  658.5\n", "2663 2024-09-24  658.0  701.5  657.5  699.5\n", "2664 2024-09-25  704.0  730.5  702.5  723.5\n", "\n", "[2665 rows x 5 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取文华财经的主连数据，用8888代码，长度不能超过6000\n", "symbol = 'I8888.DC' # 'I2501.DC'\n", "hist_data=ds.get_history_data(symbol, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close], \n", "                              BarSize.day, DoRight.none)\n", "print(hist_data.shape)\n", "df = pd.DataFrame(hist_data)\n", "df.columns = ['datetime', 'open', 'high', 'low', 'close']\n", "df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)\n", "df\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(7776, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-02 09:35:00</td>\n", "      <td>11.76</td>\n", "      <td>11.78</td>\n", "      <td>11.66</td>\n", "      <td>11.70</td>\n", "      <td>594000.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-02 09:40:00</td>\n", "      <td>11.71</td>\n", "      <td>11.71</td>\n", "      <td>11.69</td>\n", "      <td>11.71</td>\n", "      <td>357100.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-02 09:45:00</td>\n", "      <td>11.70</td>\n", "      <td>11.72</td>\n", "      <td>11.69</td>\n", "      <td>11.70</td>\n", "      <td>222100.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-02 09:50:00</td>\n", "      <td>11.69</td>\n", "      <td>11.70</td>\n", "      <td>11.66</td>\n", "      <td>11.67</td>\n", "      <td>437000.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-02 09:55:00</td>\n", "      <td>11.67</td>\n", "      <td>11.72</td>\n", "      <td>11.66</td>\n", "      <td>11.71</td>\n", "      <td>405700.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7771</th>\n", "      <td>2024-08-30 14:40:00</td>\n", "      <td>7.64</td>\n", "      <td>7.64</td>\n", "      <td>7.63</td>\n", "      <td>7.64</td>\n", "      <td>284900.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7772</th>\n", "      <td>2024-08-30 14:45:00</td>\n", "      <td>7.64</td>\n", "      <td>7.64</td>\n", "      <td>7.61</td>\n", "      <td>7.61</td>\n", "      <td>850400.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7773</th>\n", "      <td>2024-08-30 14:50:00</td>\n", "      <td>7.62</td>\n", "      <td>7.62</td>\n", "      <td>7.59</td>\n", "      <td>7.60</td>\n", "      <td>1623100.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7774</th>\n", "      <td>2024-08-30 14:55:00</td>\n", "      <td>7.60</td>\n", "      <td>7.61</td>\n", "      <td>7.59</td>\n", "      <td>7.59</td>\n", "      <td>935100.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7775</th>\n", "      <td>2024-08-30 15:00:00</td>\n", "      <td>7.59</td>\n", "      <td>7.61</td>\n", "      <td>7.59</td>\n", "      <td>7.60</td>\n", "      <td>10546900.0</td>\n", "      <td>000009.SZ</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7776 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                datetime   open   high    low  close      volume       code\n", "0    2024-01-02 09:35:00  11.76  11.78  11.66  11.70    594000.0  000009.SZ\n", "1    2024-01-02 09:40:00  11.71  11.71  11.69  11.71    357100.0  000009.SZ\n", "2    2024-01-02 09:45:00  11.70  11.72  11.69  11.70    222100.0  000009.SZ\n", "3    2024-01-02 09:50:00  11.69  11.70  11.66  11.67    437000.0  000009.SZ\n", "4    2024-01-02 09:55:00  11.67  11.72  11.66  11.71    405700.0  000009.SZ\n", "...                  ...    ...    ...    ...    ...         ...        ...\n", "7771 2024-08-30 14:40:00   7.64   7.64   7.63   7.64    284900.0  000009.SZ\n", "7772 2024-08-30 14:45:00   7.64   7.64   7.61   7.61    850400.0  000009.SZ\n", "7773 2024-08-30 14:50:00   7.62   7.62   7.59   7.60   1623100.0  000009.SZ\n", "7774 2024-08-30 14:55:00   7.60   7.61   7.59   7.59    935100.0  000009.SZ\n", "7775 2024-08-30 15:00:00   7.59   7.61   7.59   7.60  10546900.0  000009.SZ\n", "\n", "[7776 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["symbol = '000009.SZ'\n", "hist_data=ds.get_history_data(symbol, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close, BarData.volume], \n", "                              BarSize.min5, DoRight.none)\n", "print(hist_data.shape)\n", "df = pd.DataFrame(hist_data)\n", "df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']\n", "df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)\n", "df['code'] = symbol\n", "df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### AICM EDA"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyqlab.data.dataset.pipeline import Pipeline\n", "import pandas as pd\n", "# bars vocabulary\n", "# bars=Pipeline.get_vocab()\n", "# pd.DataFrame(bars).to_csv(\"bars.csv\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>AD_PS_RATIO</th>\n", "      <th>COST_RNG</th>\n", "      <th>CURRENT_TIME</th>\n", "      <th>DAYOFWEEK</th>\n", "      <th>DRAWDOWN_RNG</th>\n", "      <th>FAST_FLRS</th>\n", "      <th>FAST_FLRS_DIRECT</th>\n", "      <th>FAST_FLRS_STDDEV</th>\n", "      <th>...</th>\n", "      <th>SLOW_MOM_ZSCORE</th>\n", "      <th>SLOW_NATR</th>\n", "      <th>SLOW_NATR_DIRECT</th>\n", "      <th>SLOW_NATR_STDDEV</th>\n", "      <th>SLOW_NATR_ZSCORE</th>\n", "      <th>SLOW_RSI</th>\n", "      <th>SLOW_RSI_DIRECT</th>\n", "      <th>SLOW_RSI_STDDEV</th>\n", "      <th>SLOW_RSI_ZSCORE</th>\n", "      <th>STDDEV_RNG</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>1704157272</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>901.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.503622</td>\n", "      <td>2.0</td>\n", "      <td>1.230784</td>\n", "      <td>...</td>\n", "      <td>0.454288</td>\n", "      <td>2.171963</td>\n", "      <td>4.0</td>\n", "      <td>0.316813</td>\n", "      <td>6.70979</td>\n", "      <td>-1.964341</td>\n", "      <td>2.0</td>\n", "      <td>2.291028</td>\n", "      <td>1.234309</td>\n", "      <td>1.014816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A</td>\n", "      <td>1704157283</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>901.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>2.104169</td>\n", "      <td>4.0</td>\n", "      <td>1.288912</td>\n", "      <td>...</td>\n", "      <td>0.519169</td>\n", "      <td>2.171963</td>\n", "      <td>4.0</td>\n", "      <td>0.316813</td>\n", "      <td>6.70979</td>\n", "      <td>-1.996705</td>\n", "      <td>2.0</td>\n", "      <td>2.290164</td>\n", "      <td>1.220943</td>\n", "      <td>1.019705</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A</td>\n", "      <td>1704157284</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>901.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>2.104169</td>\n", "      <td>4.0</td>\n", "      <td>1.288912</td>\n", "      <td>...</td>\n", "      <td>0.519169</td>\n", "      <td>2.171963</td>\n", "      <td>4.0</td>\n", "      <td>0.316813</td>\n", "      <td>6.70979</td>\n", "      <td>-1.996705</td>\n", "      <td>2.0</td>\n", "      <td>2.290164</td>\n", "      <td>1.220943</td>\n", "      <td>1.029144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A</td>\n", "      <td>1704157297</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>901.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>2.104169</td>\n", "      <td>4.0</td>\n", "      <td>1.288912</td>\n", "      <td>...</td>\n", "      <td>0.519169</td>\n", "      <td>2.171963</td>\n", "      <td>4.0</td>\n", "      <td>0.316813</td>\n", "      <td>6.70979</td>\n", "      <td>-1.996705</td>\n", "      <td>2.0</td>\n", "      <td>2.290164</td>\n", "      <td>1.220943</td>\n", "      <td>1.036366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A</td>\n", "      <td>1704157345</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>902.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.423686</td>\n", "      <td>4.0</td>\n", "      <td>1.259247</td>\n", "      <td>...</td>\n", "      <td>0.456446</td>\n", "      <td>2.171963</td>\n", "      <td>4.0</td>\n", "      <td>0.316813</td>\n", "      <td>6.70979</td>\n", "      <td>-2.296418</td>\n", "      <td>1.0</td>\n", "      <td>2.282614</td>\n", "      <td>1.096473</td>\n", "      <td>1.045130</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 66 columns</p>\n", "</div>"], "text/plain": ["  code        date  AD_PS_RATIO  COST_RNG  CURRENT_TIME  DAYOFWEEK  \\\n", "0    A  1704157272          0.0       0.0         901.0        1.0   \n", "1    A  1704157283          0.0       0.0         901.0        1.0   \n", "2    A  1704157284          0.0       0.0         901.0        1.0   \n", "3    A  1704157297          0.0       0.0         901.0        1.0   \n", "4    A  1704157345          0.0       0.0         902.0        1.0   \n", "\n", "   DRAWDOWN_RNG  FAST_FLRS  FAST_FLRS_DIRECT  FAST_FLRS_STDDEV  ...  \\\n", "0           0.0   0.503622               2.0          1.230784  ...   \n", "1           0.0   2.104169               4.0          1.288912  ...   \n", "2           0.0   2.104169               4.0          1.288912  ...   \n", "3           0.0   2.104169               4.0          1.288912  ...   \n", "4           0.0   1.423686               4.0          1.259247  ...   \n", "\n", "   SLOW_MOM_ZSCORE  SLOW_NATR  SLOW_NATR_DIRECT  SLOW_NATR_STDDEV  \\\n", "0         0.454288   2.171963               4.0          0.316813   \n", "1         0.519169   2.171963               4.0          0.316813   \n", "2         0.519169   2.171963               4.0          0.316813   \n", "3         0.519169   2.171963               4.0          0.316813   \n", "4         0.456446   2.171963               4.0          0.316813   \n", "\n", "   SLOW_NATR_ZSCORE  SLOW_RSI  SLOW_RSI_DIRECT  SLOW_RSI_STDDEV  \\\n", "0           6.70979 -1.964341              2.0         2.291028   \n", "1           6.70979 -1.996705              2.0         2.290164   \n", "2           6.70979 -1.996705              2.0         2.290164   \n", "3           6.70979 -1.996705              2.0         2.290164   \n", "4           6.70979 -2.296418              1.0         2.282614   \n", "\n", "   SLOW_RSI_ZSCORE  STDDEV_RNG  \n", "0         1.234309    1.014816  \n", "1         1.220943    1.019705  \n", "2         1.220943    1.029144  \n", "3         1.220943    1.036366  \n", "4         1.096473    1.045130  \n", "\n", "[5 rows x 66 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data_path = \"e:/featdata/main/ffs_ct.main.2024.parquet\"\n", "data = pd.read_parquet(data_path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>AD_PS_RATIO</th>\n", "      <th>COST_RNG</th>\n", "      <th>CURRENT_TIME</th>\n", "      <th>DAYOFWEEK</th>\n", "      <th>DRAWDOWN_RNG</th>\n", "      <th>FAST_FLRS</th>\n", "      <th>FAST_FLRS_DIRECT</th>\n", "      <th>FAST_FLRS_STDDEV</th>\n", "      <th>FAST_FLRS_ZSCORE</th>\n", "      <th>...</th>\n", "      <th>SLOW_MOM_ZSCORE</th>\n", "      <th>SLOW_NATR</th>\n", "      <th>SLOW_NATR_DIRECT</th>\n", "      <th>SLOW_NATR_STDDEV</th>\n", "      <th>SLOW_NATR_ZSCORE</th>\n", "      <th>SLOW_RSI</th>\n", "      <th>SLOW_RSI_DIRECT</th>\n", "      <th>SLOW_RSI_STDDEV</th>\n", "      <th>SLOW_RSI_ZSCORE</th>\n", "      <th>STDDEV_RNG</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>5.980800e+04</td>\n", "      <td>59808.0</td>\n", "      <td>59808.0</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.0</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>...</td>\n", "      <td>59808.000000</td>\n", "      <td>5.980800e+04</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "      <td>59808.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.705104e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1459.140984</td>\n", "      <td>1.880100</td>\n", "      <td>0.0</td>\n", "      <td>-0.001960</td>\n", "      <td>0.056263</td>\n", "      <td>1.481361</td>\n", "      <td>0.075647</td>\n", "      <td>...</td>\n", "      <td>0.838486</td>\n", "      <td>2.171963e+00</td>\n", "      <td>2.644730</td>\n", "      <td>0.751019</td>\n", "      <td>2.780868</td>\n", "      <td>-2.733781</td>\n", "      <td>-0.090138</td>\n", "      <td>2.300953</td>\n", "      <td>0.799549</td>\n", "      <td>1.265867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>6.066227e+05</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>511.752003</td>\n", "      <td>1.357331</td>\n", "      <td>0.0</td>\n", "      <td>1.544389</td>\n", "      <td>2.462336</td>\n", "      <td>0.148127</td>\n", "      <td>1.030173</td>\n", "      <td>...</td>\n", "      <td>0.740956</td>\n", "      <td>4.440929e-16</td>\n", "      <td>1.457161</td>\n", "      <td>0.193473</td>\n", "      <td>1.367934</td>\n", "      <td>1.906245</td>\n", "      <td>2.905024</td>\n", "      <td>0.049971</td>\n", "      <td>0.778707</td>\n", "      <td>0.283652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.704157e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>901.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>-4.746839</td>\n", "      <td>-4.000000</td>\n", "      <td>1.216952</td>\n", "      <td>-2.916976</td>\n", "      <td>...</td>\n", "      <td>-0.945256</td>\n", "      <td>2.171963e+00</td>\n", "      <td>1.000000</td>\n", "      <td>0.316813</td>\n", "      <td>1.631849</td>\n", "      <td>-6.696088</td>\n", "      <td>-4.000000</td>\n", "      <td>2.228379</td>\n", "      <td>-0.859867</td>\n", "      <td>0.484069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.704676e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1003.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>-1.089353</td>\n", "      <td>-2.000000</td>\n", "      <td>1.377472</td>\n", "      <td>-0.633298</td>\n", "      <td>...</td>\n", "      <td>0.220335</td>\n", "      <td>2.171963e+00</td>\n", "      <td>1.000000</td>\n", "      <td>0.652298</td>\n", "      <td>1.865070</td>\n", "      <td>-3.948580</td>\n", "      <td>-3.000000</td>\n", "      <td>2.256920</td>\n", "      <td>0.311877</td>\n", "      <td>1.084685</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.705029e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1346.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.0</td>\n", "      <td>-0.041315</td>\n", "      <td>1.000000</td>\n", "      <td>1.463353</td>\n", "      <td>0.032456</td>\n", "      <td>...</td>\n", "      <td>1.077873</td>\n", "      <td>2.171963e+00</td>\n", "      <td>4.000000</td>\n", "      <td>0.810913</td>\n", "      <td>2.240130</td>\n", "      <td>-2.859179</td>\n", "      <td>-1.000000</td>\n", "      <td>2.295475</td>\n", "      <td>0.762822</td>\n", "      <td>1.223626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.705583e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2113.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.051369</td>\n", "      <td>2.000000</td>\n", "      <td>1.549373</td>\n", "      <td>0.765737</td>\n", "      <td>...</td>\n", "      <td>1.387816</td>\n", "      <td>2.171963e+00</td>\n", "      <td>4.000000</td>\n", "      <td>0.907952</td>\n", "      <td>3.003266</td>\n", "      <td>-1.256760</td>\n", "      <td>2.000000</td>\n", "      <td>2.329935</td>\n", "      <td>1.426654</td>\n", "      <td>1.404365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.706108e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2259.000000</td>\n", "      <td>4.000000</td>\n", "      <td>0.0</td>\n", "      <td>4.888709</td>\n", "      <td>4.000000</td>\n", "      <td>1.952950</td>\n", "      <td>3.524886</td>\n", "      <td>...</td>\n", "      <td>2.157584</td>\n", "      <td>2.171963e+00</td>\n", "      <td>4.000000</td>\n", "      <td>0.971829</td>\n", "      <td>6.709790</td>\n", "      <td>1.918106</td>\n", "      <td>4.000000</td>\n", "      <td>2.444217</td>\n", "      <td>2.527272</td>\n", "      <td>3.746183</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 65 columns</p>\n", "</div>"], "text/plain": ["               date  AD_PS_RATIO  COST_RNG  CURRENT_TIME     DAYOFWEEK  \\\n", "count  5.980800e+04      59808.0   59808.0  59808.000000  59808.000000   \n", "mean   1.705104e+09          0.0       0.0   1459.140984      1.880100   \n", "std    6.066227e+05          0.0       0.0    511.752003      1.357331   \n", "min    1.704157e+09          0.0       0.0    901.000000      0.000000   \n", "25%    1.704676e+09          0.0       0.0   1003.000000      1.000000   \n", "50%    1.705029e+09          0.0       0.0   1346.000000      2.000000   \n", "75%    1.705583e+09          0.0       0.0   2113.000000      3.000000   \n", "max    1.706108e+09          0.0       0.0   2259.000000      4.000000   \n", "\n", "       DRAWDOWN_RNG     FAST_FLRS  FAST_FLRS_DIRECT  FAST_FLRS_STDDEV  \\\n", "count       59808.0  59808.000000      59808.000000      59808.000000   \n", "mean            0.0     -0.001960          0.056263          1.481361   \n", "std             0.0      1.544389          2.462336          0.148127   \n", "min             0.0     -4.746839         -4.000000          1.216952   \n", "25%             0.0     -1.089353         -2.000000          1.377472   \n", "50%             0.0     -0.041315          1.000000          1.463353   \n", "75%             0.0      1.051369          2.000000          1.549373   \n", "max             0.0      4.888709          4.000000          1.952950   \n", "\n", "       FAST_FLRS_ZSCORE  ...  SLOW_MOM_ZSCORE     SLOW_NATR  SLOW_NATR_DIRECT  \\\n", "count      59808.000000  ...     59808.000000  5.980800e+04      59808.000000   \n", "mean           0.075647  ...         0.838486  2.171963e+00          2.644730   \n", "std            1.030173  ...         0.740956  4.440929e-16          1.457161   \n", "min           -2.916976  ...        -0.945256  2.171963e+00          1.000000   \n", "25%           -0.633298  ...         0.220335  2.171963e+00          1.000000   \n", "50%            0.032456  ...         1.077873  2.171963e+00          4.000000   \n", "75%            0.765737  ...         1.387816  2.171963e+00          4.000000   \n", "max            3.524886  ...         2.157584  2.171963e+00          4.000000   \n", "\n", "       SLOW_NATR_STDDEV  SLOW_NATR_ZSCORE      SLOW_RSI  SLOW_RSI_DIRECT  \\\n", "count      59808.000000      59808.000000  59808.000000     59808.000000   \n", "mean           0.751019          2.780868     -2.733781        -0.090138   \n", "std            0.193473          1.367934      1.906245         2.905024   \n", "min            0.316813          1.631849     -6.696088        -4.000000   \n", "25%            0.652298          1.865070     -3.948580        -3.000000   \n", "50%            0.810913          2.240130     -2.859179        -1.000000   \n", "75%            0.907952          3.003266     -1.256760         2.000000   \n", "max            0.971829          6.709790      1.918106         4.000000   \n", "\n", "       SLOW_RSI_STDDEV  SLOW_RSI_ZSCORE    STDDEV_RNG  \n", "count     59808.000000     59808.000000  59808.000000  \n", "mean          2.300953         0.799549      1.265867  \n", "std           0.049971         0.778707      0.283652  \n", "min           2.228379        -0.859867      0.484069  \n", "25%           2.256920         0.311877      1.084685  \n", "50%           2.295475         0.762822      1.223626  \n", "75%           2.329935         1.426654      1.404365  \n", "max           2.444217         2.527272      3.746183  \n", "\n", "[8 rows x 65 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from functools import partial\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import LabelEncoder\n", "from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 1.Factors\n", "- lf: long period factors\n", "- sf: short period factors\n", "- ct: market and portfolio context factors"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["feat_path = 'e:/featdata'\n", "\n", "year = 2023\n", "# lf_df = pd.read_parquet(f'{feat_path}/ffs_lf.main.{year}.parquet')\n", "sf_df = pd.read_parquet(f'{feat_path}/ffs_sf.main.{year}.parquet')\n", "# mf_df = pd.read_parquet(f'{feat_path}/ffs_mf.main.{year}.parquet')\n", "# ct_df = pd.read_parquet(f'{feat_path}/ffs_ct.main.{year}.parquet')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["fut_codes_dict = {code: i for i, code in enumerate(MAIN_FUT_CODES)}\n", "sf_df['code_encoded'] = sf_df['code'].apply(lambda x: fut_codes_dict[x])\n", "sf_df['change'] = sf_df['change'].astype(np.float32)\n", "sf_df['change'].fillna(0.0, inplace=True)\n", "# sf_df['long_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "# sf_df['short_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)\n", "sf_df['change'] = sf_df['change'] * 100.0\n", "sf_df['change'] = sf_df.loc[(sf_df['change'] > -1) & (sf_df['change'] < 1), 'change']"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# change列标签化，用于分类问题<-0.25, -0.25~0.25, >0.25\n", "sf_df['label'] = sf_df['change'].apply(lambda x: 0 if x < -0.25 else 2 if x > 0.25 else 1)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["label\n", "1    435112\n", "0    214636\n", "2    211684\n", "Name: count, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["sf_df['label'].value_counts()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([  288.,   343.,   165.,   590.,   203.,   498.,   841.,   324.,\n", "          702.,  1301.,   459.,  1077.,  1824.,   681.,  2392.,  1825.,\n", "          998.,  2195.,  4079.,  3237.,  1712.,  5782.,  4715.,  2593.,\n", "         5548.,  9830.,  7575.,  4030.,  9324., 16034.,  6027., 13182.,\n", "        22946.,  8375., 28304., 21415., 11662., 37333., 12963., 41586.,\n", "        14024., 27680., 39259., 21165., 17003., 16114., 16156., 14447.,\n", "        14771., 18416., 35260., 25612., 13372., 39774., 12351., 36032.,\n", "        11413., 20820., 27842.,  8031., 22419., 12906.,  5919., 15908.,\n", "         9249.,  4065.,  7563.,  6694.,  8516.,  2528.,  4637.,  6049.,\n", "         1723.,  3234.,  4145.,  2216.,  1018.,  1807.,  2326.,   663.,\n", "         1855.,  1061.,   513.,  1181.,   686.,   298.,   860.,   520.,\n", "          248.,   594.,   176.,   343.,   287.]),\n", " array([-1.  , -0.98, -0.96, -0.94, -0.92, -0.9 , -0.88, -0.86, -0.84,\n", "        -0.82, -0.8 , -0.78, -0.76, -0.74, -0.72, -0.7 , -0.68, -0.66,\n", "        -0.64, -0.62, -0.6 , -0.58, -0.56, -0.54, -0.52, -0.5 , -0.48,\n", "        -0.46, -0.44, -0.42, -0.4 , -0.38, -0.36, -0.34, -0.32, -0.3 ,\n", "        -0.28, -0.26, -0.24, -0.22, -0.2 , -0.18, -0.16, -0.14, -0.12,\n", "        -0.1 , -0.07,  0.07,  0.1 ,  0.12,  0.14,  0.16,  0.18,  0.2 ,\n", "         0.22,  0.24,  0.26,  0.28,  0.3 ,  0.32,  0.34,  0.36,  0.38,\n", "         0.4 ,  0.42,  0.44,  0.46,  0.48,  0.5 ,  0.52,  0.54,  0.56,\n", "         0.58,  0.6 ,  0.62,  0.64,  0.66,  0.68,  0.7 ,  0.72,  0.74,\n", "         0.76,  0.78,  0.8 ,  0.82,  0.84,  0.86,  0.88,  0.9 ,  0.92,\n", "         0.94,  0.96,  0.98,  1.  ]),\n", " <BarContainer object of 93 artists>)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 画出change的分布图\n", "import matplotlib.pyplot as plt\n", "step=2\n", "midd=4\n", "bins = [x/100.0 for x in range(-100, 100+step, step)]\n", "bins = bins[:100//step-midd] + [-0.07, 0.07] + bins[-100//step+midd:]\n", "# bins = [-np.inf] + bins + [np.inf]\n", "plt.hist(sf_df['change'], bins=bins, label='change')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(bins)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用自定义分箱\n", "sf_df['change_bins'] = pd.cut(sf_df['change'], bins=bins, labels=False)\n", "sf_df['change_bins'].fillna(0, inplace=True)\n", "sf_df['change_bins'] = sf_df['change_bins'].astype(np.int32)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# change2列change按分箱区间映射取值\n", "sf_df['change2'] = sf_df['change_bins'].apply(lambda x: bins[x])\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### All Factors Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df['code'].unique().shape, sf_df['code'].unique().shape, ct_df['code'].unique().shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# lf_df.head(100).to_csv(f'{feat_path}/lf_df.csv')\n", "sf_df.head(300).to_csv(f'{feat_path}/sf_df.csv')\n", "# ct_df.head(100).to_csv(f'{feat_path}/ct_df.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df[\"DAYOFWEEK\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df[\"HOUR\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 通过date列timestamp，并将时区转换为北京时间\n", "ct_df['datetime'] = pd.to_datetime(ct_df['date'] + 28800, unit='s')\n", "ct_df['DAYOFWEEK'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.dayofweek\n", "ct_df['HOUR'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.hour\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df.describe(percentiles=[0.01, 0.05, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 统计BAND_EXPAND的分布\n", "sf_df['BAND_EXPAND_2'].describe(percentiles=[0.01, 0.05, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df['BAND_EXPAND_2'].hist(bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df.loc[sf_df['BAND_EXPAND_2']>15]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Select Factors\n", "- 有很多列都为空值，根据需要将其剔除"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    # \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\",\n", "    \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    # \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    # \"SQUEEZE_ZERO_BARS\", \n", "    # \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    # \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    # \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    # \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    # \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    # \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    # \"VOLUME\", # 在RangeBar下，Volume是Bar的时长seconds\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"STDDEV_FAST\", \"STDDEV_SLOW\", \"STDDEV_THRESHOLD\",\n", "\n", "    \"MOMENTUM_FAST\", \"MOMENTUM_MIDD\", \"MOMENTUM_SLOW\", \"MOMENTUM\",\n", "    \"MOMENTUM_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_NATR\", \"FAST_QH_NATR_ZSCORE\", \"FAST_QH_NATR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]\n", "\n", "def _long_factor_select(n):\n", "    if len(SEL_LONG_FACTOR_NAMES) == 0:\n", "        return 0\n", "    if ALL_FACTOR_NAMES[n] in SEL_LONG_FACTOR_NAMES:\n", "        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:\n", "            return 2\n", "        else:\n", "            return 1\n", "    return 0\n", "\n", "def _short_factor_select(n):\n", "    if len(SEL_SHORT_FACTOR_NAMES) == 0:\n", "        return 0\n", "    if ALL_FACTOR_NAMES[n] in SEL_SHORT_FACTOR_NAMES:\n", "        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:\n", "            return 2\n", "        else:\n", "            return 1\n", "    return 0\n", "\n", "def _factor_select_name(sel_list):\n", "    sel_name=[]\n", "    if len(sel_list) == 0:\n", "        return sel_name\n", "    for n in range(len(ALL_FACTOR_NAMES)):\n", "        if sel_list[n] > 0:\n", "            sel_name.append(ALL_FACTOR_NAMES[n])\n", "    return sel_name\n", "\n", "def _context_select_name(sel_list):\n", "    sel_name=[]\n", "    if len(sel_list) == 0:\n", "        return sel_name\n", "    for n in range(len(SNAPSHOT_CONTEXT)):\n", "        if sel_list[n] > 0:\n", "            sel_name.append(SNAPSHOT_CONTEXT[n])\n", "    return sel_name\n", "\n", "def _get_factor_cols(factor_type=\"lf\"):\n", "    \"\"\"\n", "    因子列名称\n", "    \"\"\"\n", "    col_names = []\n", "    if factor_type == \"lf\":\n", "        for name in SEL_LONG_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "\n", "    if factor_type == \"sf\":\n", "        for name in SEL_SHORT_FACTOR_NAMES: # SEL_SHORT_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "\n", "    if factor_type == \"ct\":\n", "        col_names.extend(SEL_CONTEXT_FACTOR_NAMES)\n", "\n", "    return col_names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["col_names = _get_factor_cols(\"lf\")+_get_factor_cols(\"sf\")+_get_factor_cols(\"ct\")\n", "print(len(col_names))\n", "col_names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_lf = [_long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]\n", "sel_name = _factor_select_name(sel_lf)\n", "print(sel_name)\n", "print(len(sel_name), len(SEL_LONG_FACTOR_NAMES))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf = [_short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]\n", "sel_name = _factor_select_name(sel_sf)\n", "print(sel_name)\n", "print(len(sel_name), len(SEL_SHORT_FACTOR_NAMES))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df['change'] = sf_df['change'].astype('float')\n", "sf_df['label_long'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "sf_df['label_short'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["col_names = _get_factor_cols(factor_type=\"sf\")\n", "col_names += ['code', 'date', 'change', 'label_long', 'label_short']\n", "sel_sf_df = sf_df[col_names]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter win"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算绝对值最大的行序号\n", "rolling_max_index = sel_sf_df['change'].rolling(window=5).apply(lambda x: abs(x).idxmax())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rolling_max_index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 移动窗口的第一个值会返回 NaN，所以可以使用 fillna 方法填充为 0 或其他合适的值\n", "rolling_max_index = rolling_max_index.fillna(0)\n", "\n", "# 将结果转换为整数\n", "rolling_max_index = rolling_max_index.astype(int)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(rolling_max_index)):\n", "    if rolling_max_index[i] == sel_sf_df.index[i]:\n", "        print(rolling_max_index[i])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "print(rolling_max_index.head(50))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Standard"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from functools import partial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def standardize(group, means, stds):\n", "    code = group.name\n", "    mean = means.loc[code]\n", "    std = stds.loc[code]\n", "    group = (group - mean) / std\n", "    # print(code, mean, std)\n", "    return group\n", "\n", "lf_mean = pd.read_csv(f'{feat_path}/lf_mean.csv', index_col=0)\n", "lf_std = pd.read_csv(f'{feat_path}/lf_std.csv', index_col=0)\n", "sf_mean = pd.read_csv(f'{feat_path}/sf_mean.csv', index_col=0)\n", "sf_std = pd.read_csv(f'{feat_path}/sf_std.csv', index_col=0)\n", "ct_mean = pd.read_csv(f'{feat_path}/ct_mean.csv', index_col=0)\n", "ct_std = pd.read_csv(f'{feat_path}/ct_std.csv', index_col=0)\n", "print(sf_mean.shape, sf_std.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preparing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["le = LabelEncoder()\n", "sf_df['code_encoded'] = le.fit_transform(sf_df['code'].values)\n", "\n", "# 生成模型输入数据配置文件\n", "# 放在数据处理的前面，以保证因子顺序与系统一致\n", "# self._dump_input_param_json()\n", "is_class = False\n", "direct = 'long'\n", "if is_class: # 分类问题，生成标签\n", "    print(\"-----分类问题-----\")\n", "    if direct == 'long':\n", "        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "    elif direct == 'short':\n", "        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)\n", "    else:\n", "        raise ValueError(f\"direct {direct} is not supported\")\n", "else:\n", "    print(\"-----非分类问题-----\")\n", "    sf_df['label'] = sf_df.loc[:, 'change']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "col_names = _get_factor_cols(factor_type=\"lf\")\n", "if len(col_names) > 0:\n", "    lf_df = lf_df[col_names + ['code']]\n", "    df_mean = lf_mean[col_names]\n", "    df_std = lf_std[col_names]\n", "    partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "    df_standardized = lf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)\n", "    df_standardized.fillna(0.0, inplace=True)\n", "    df_standardized.reset_index(drop=False, inplace=True)\n", "    # df_standardized.sort_values(by=['code'], inplace=True)\n", "    lf_df = df_standardized[col_names]\n", "else:\n", "    lf_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "col_names = _get_factor_cols(factor_type=\"sf\")\n", "if len(col_names) > 0:\n", "    sf_df = sf_df[col_names + ['code', 'date', 'change', 'code_encoded', 'label']]\n", "    df_mean = sf_mean[col_names]\n", "    df_std = sf_std[col_names]\n", "    partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "    df_standardized = sf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)\n", "    df_standardized.fillna(0.0, inplace=True)\n", "    df_standardized.reset_index(drop=False, inplace=True)\n", "    # df_standardized.sort_values(by=['code'], inplace=True)\n", "    sf_df[col_names] = df_standardized[col_names]\n", "else:\n", "    sf_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "col_names = _get_factor_cols(factor_type=\"ct\")\n", "if len(col_names) > 0:\n", "    ct_df = ct_df[col_names + ['code']]\n", "    df_mean = ct_mean[col_names]\n", "    df_std = ct_std[col_names]\n", "    partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "    df_standardized = ct_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)\n", "    df_standardized.fillna(0.0, inplace=True)\n", "    df_standardized.reset_index(drop=False, inplace=True)\n", "    # df_standardized.sort_values(by=['code'], inplace=True)\n", "    ct_df = df_standardized[col_names]\n", "else:\n", "    ct_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(lf_df)\n", "print(sf_df)\n", "print(ct_df)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft_df = pd.concat([lf_df, sf_df, ct_df], axis=1)\n", "print(ft_df.columns.tolist())\n", "print(f\"\\n===============\\n\\nft{ft_df.shape} lf{lf_df.shape} sf{sf_df.shape} ct{ct_df.shape}\\n\\n================\\n\")\n", "print(ft_df)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 合并后清除数据\n", "# ft_df.dropna(axis=0, how='any', inplace=True)\n", "ft_df.fillna(0.0, inplace=True)\n", "if 'RSI_2' in ft_df.columns:\n", "    ft_df = ft_df[ft_df['RSI_2'] != 0.0]\n", "if 'FAST_QH_NATR_ZSCORE' in ft_df.columns:\n", "    ft_df = ft_df[ft_df['FAST_QH_NATR_ZSCORE'] != 0.0]\n", "\n", "lb_df = ft_df[['code', 'date', 'change', 'code_encoded', 'label']]\n", "lb_df.reset_index(drop=True, inplace=True)\n", "\n", "ft_df.drop(['code', 'date', 'change', 'code_encoded', 'label'], axis=1, inplace=True)\n", "ft_df = ft_df.astype(np.float32)\n", "\n", "print(f\"\\n===============\\n\\nlb{lb_df.shape} ft{ft_df.shape}\\n\\n================\\n\")\n", "print(ft_df)\n", "print(lb_df)\n", "print(direct)\n", "print(lb_df['label'].value_counts())\n", "\n", "data1 = ft_df.values\n", "data2 = lb_df.values[:, -1]\n", "data3 = lb_df.values[:, -2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_col_names = df_mean.columns.to_list()\n", "col_names = _get_factor_cols(factor_type=\"sf\")\n", "# all_col_names在col_names中的索引\n", "sel_index = [all_col_names.index(name) for name in col_names]\n", "print(len(sel_index), sel_index)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mean = df_mean[col_names]\n", "df_std = df_std[col_names]\n", "print(df_mean.shape, df_std.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df2 = sel_sf_df[['code', 'date'] + col_names]\n", "sel_sf_df2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建一个偏函数\n", "my_partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "df_standardized = sel_sf_df2.groupby('code')[col_names].apply(my_partial_func)\n", "# df_standardized.reset_index(drop=False, inplace=True)\n", "# df_standardized.fillna(0.0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_standardized"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_stand = df_standardized.loc[(df_standardized['RSI_2'] < 3) & (df_standardized['RSI_2'] > -3)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_stand"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_stand.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_standardized.describe().to_csv(f'{feat_path}/sf_standardized_describe.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df[col_names] = df_standardized[col_names]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将因子数据按CODE分组求均值和标准差\n", "sf_df_mean = sel_sf_df.groupby('code').mean()\n", "sf_df_std = sel_sf_df.groupby('code').std()\n", "print(sf_df_mean.shape, sf_df_std.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter Factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: 由于长假或主力期货合约变更，导致部分合约数据有很大跳空，行情失真，因此需要去除这些数据\n", "# 1. 选择特定Factor（不受合约不太影响，如RSI等），计算统计值\n", "# 2. 选择统计值在一定范围内的数据"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Check Model Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rpt_path = 'd:/RoboQuant2/rpt'\n", "json_file = 'model_test_202306172311.json'\n", "with open(f'{rpt_path}/{json_file}') as file:\n", "    # Load the JSON data\n", "    data = json.load(file)\n", "models = data['models']\n", "data.pop('models')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs = pd.DataFrame()\n", "for code in data.keys():\n", "    df = pd.DataFrame(data[code], columns=['change'] + models)\n", "    df [\"change\"] = df[\"change\"].shift(-1)\n", "    df.dropna(inplace=True)\n", "    df = df.loc[df['change'] != 0.0, :]\n", "    df.insert(0, 'code', code)\n", "    dfs = pd.concat([dfs, df], axis=0)\n", "# 删除loc.columns[2:]列所有列元素都为0的列\n", "# dfs = dfs.loc[:, dfs[dfs.columns[2:]].sum(axis=0) > 0.0]\n", "# # 删除所有行元素都为0的行\n", "dfs = dfs.loc[dfs[dfs.columns[2:]].sum(axis=1) > 0.0, :]\n", "dfs.reset_index(drop=True, inplace=True)\n", "print(dfs.shape)\n", "dfs.to_csv(f'{rpt_path}/{json_file}.csv', index=False)\n", "dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs.describe(percentiles=[0.75, 0.95, 0.98, 0.99])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Code test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'Column1': [1, 2, 3, 4, 5],\n", "    'Column2': [3, 4, 5, 25, 30],\n", "    'Column3': [0.5, 1.5, 2.5, 3.5, 4.5]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置阈值\n", "threshold = 3\n", "\n", "# 创建逻辑掩码，筛选值大于阈值的行\n", "mask = df > threshold\n", "\n", "# 使用掩码剔除不满足条件的行\n", "filtered_df = df[~mask.any(axis=1)]\n", "\n", "print(filtered_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# 创建一个示例矩阵\n", "matrix = np.array([[1, 2, 0.5],\n", "                   [2, 3, 1.5],\n", "                   [3, 4, 2.5],\n", "                   [4, 25, 3.5],\n", "                   [5, 30, 4.5]])\n", "\n", "print(\"Original matrix:\")\n", "print(matrix)\n", "\n", "# 设置阈值\n", "threshold_lower = 2\n", "threshold_upper = 4\n", "\n", "# 创建逻辑掩码，同时满足两个条件\n", "mask_lower = matrix[:, 1] > threshold_lower\n", "mask_upper = matrix[:, 1] < threshold_upper\n", "combined_mask = mask_lower & mask_upper\n", "\n", "# 使用掩码剔除不满足条件的行\n", "filtered_matrix = matrix[combined_mask]\n", "\n", "print(\"Filtered matrix:\")\n", "print(filtered_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(filtered_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# 创建一个示例矩阵\n", "matrix = np.array([[1, 2, 0.5],\n", "                   [2, 3, 1.5],\n", "                   [3, 4, 2.5],\n", "                   [4, 25, 3.5],\n", "                   [5, 30, 4.5]])\n", "\n", "print(\"Original matrix:\")\n", "print(matrix)\n", "(matrix > 3).any() or (matrix < -3).any()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### TICK DATA"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/2022'\n", "df = pd.read_parquet(f'{data_path}/SF202201.parquet', engine='fastparquet')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df.sort_values(by=['datetime'], inplace=True)\n", "df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>code</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-04 09:29:00</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4960.0</td>\n", "      <td>144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-04 09:30:00</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4961.0</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-01-04 09:30:01</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4964.0</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-01-04 09:30:01</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4962.6</td>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-04 09:30:01</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4962.0</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1795</th>\n", "      <td>2022-01-28 14:59:58</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4541.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1796</th>\n", "      <td>2022-01-28 14:59:58</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4542.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1797</th>\n", "      <td>2022-01-28 14:59:59</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4540.2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1798</th>\n", "      <td>2022-01-28 14:59:59</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4540.6</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1799</th>\n", "      <td>2022-01-28 15:00:00</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4540.6</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>337269 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                 datetime       code   price  volume\n", "index                                               \n", "0     2022-01-04 09:29:00  IF9999.SF  4960.0     144\n", "1     2022-01-04 09:30:00  IF9999.SF  4961.0      76\n", "4     2022-01-04 09:30:01  IF9999.SF  4964.0      58\n", "3     2022-01-04 09:30:01  IF9999.SF  4962.6      55\n", "2     2022-01-04 09:30:01  IF9999.SF  4962.0      67\n", "...                   ...        ...     ...     ...\n", "1795  2022-01-28 14:59:58  IF9999.SF  4541.0       3\n", "1796  2022-01-28 14:59:58  IF9999.SF  4542.0       2\n", "1797  2022-01-28 14:59:59  IF9999.SF  4540.2       2\n", "1798  2022-01-28 14:59:59  IF9999.SF  4540.6       2\n", "1799  2022-01-28 15:00:00  IF9999.SF  4540.6       2\n", "\n", "[337269 rows x 4 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['code'] == 'IF9999.SF', :]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quant Kv Db Factor Data Export"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date\n", "import time\n", "import pytz\n", "import pandas as pd\n", "import sys\n", "import io\n", "\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class MarketCsKvDB():\n", "    def __init__(self,\n", "        key_prefix='mkt_cs:', #fsfs 股指期货因子 ffs 商品期货因子 mkt_cs: 市场截面指标\n", "        years=[],\n", "        dbfile=\"d:/RoboQuant2/store/kv.db\",\n", "        save_path=\"e:/featdata\",\n", "        save_file=\"\",\n", "    ) -> None:\n", "        self.years = years\n", "        self._dbfile=dbfile\n", "        self._save_path = save_path\n", "        self._save_file = save_file\n", "        self._db=None\n", "        self._key_prefix=key_prefix\n", "        self._keys=[]\n", "        self._values=[]\n", "        self._ls_col_names=[]\n", "        self._ct_col_names=[]\n", "        self._tz=pytz.timezone('Asia/Shanghai')\n", "\n", "    def open_db(self, mode):\n", "        if self._db:\n", "            self.close_db()\n", "        \n", "        try:\n", "            self._db=create_db(\"leveldb\", self._dbfile, mode)\n", "        except:\n", "            raise 'Fail to open db!'\n", "\n", "    def close_db(self):\n", "        if not self._db:\n", "            raise \"not db open.\"\n", "        self._db.close()\n", "        del self._db\n", "        self._db=None\n", "\n", "    def load_all_keys(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        self._keys.clear()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key().decode('gbk', 'ignore')\n", "            if len(key) >= 16 and key[0:len(self._key_prefix)] == self._key_prefix: #'ffs:'\n", "                self._keys.append(key)\n", "                self._values.append(cursor.value().decode('gbk', 'ignore'))\n", "                print\n", "            cursor.next()\n", "        del cursor"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["mcs = MarketCsKvDB(key_prefix='mkt_cs:', dbfile=\"d:/RoboQuant2/store/kv.db\")\n", "mcs.open_db(Mode.read)\n", "mcs.load_all_keys()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["\n", "data = mcs._values[0].split(',')\n", "# df = pd.read_csv(io.StringIO(data), header=None)\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['中证1000',\n", " '-6.0146',\n", " '-0.7633',\n", " '5.9734',\n", " '-1',\n", " '-23.2094',\n", " '-4.8090',\n", " '3.8896',\n", " '-4',\n", " '0.4594',\n", " '1.0863',\n", " '0.2790',\n", " '-2',\n", " '5.0847',\n", " '4.6927',\n", " '1.0374',\n", " '4',\n", " '-0.0030',\n", " '0.0605',\n", " '0.0134',\n", " '1',\n", " '-0.3476',\n", " '-0.4084',\n", " '0.1760',\n", " '-3',\n", " '-0.0112',\n", " '-0.6433',\n", " '0.0117',\n", " '-1',\n", " '-0.3701',\n", " '-1.2374',\n", " '0.1043',\n", " '-3',\n", " '0.0386',\n", " '0.0432',\n", " '0.6400',\n", " '1',\n", " '-26.4868',\n", " '-3.2973',\n", " '4.9671',\n", " '-4']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}