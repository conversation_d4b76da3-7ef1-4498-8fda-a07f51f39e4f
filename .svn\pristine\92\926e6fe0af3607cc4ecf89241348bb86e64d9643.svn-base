{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date\n", "import time\n", "import pytz\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "import json\n", "\n", "# sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class FactorsKvDB():\n", "    def __init__(self,\n", "        # dbfile=\"e:/featdata/kv.db\",\n", "        key_prefix='ffs:', #fsfs 股指期货因子 ffs 商品期货因子\n", "        years=[],\n", "        dbfile=\"d:/RoboQuant2/store/kv.db\",\n", "        save_path=\"e:/featdata\",\n", "        save_file=\"\",\n", "    ) -> None:\n", "        self.years = years\n", "        self._dbfile=dbfile\n", "        self._save_path = save_path\n", "        self._save_file = save_file\n", "        self._db=None\n", "        self._key_prefix=key_prefix\n", "        self._keys=[]\n", "        self._ls_col_names=[]\n", "        self._ct_col_names=[]\n", "        self._tz=pytz.timezone('Asia/Shanghai')\n", "\n", "    def open_db(self, mode):\n", "        if self._db:\n", "            self.close_db()\n", "        \n", "        try:\n", "            self._db=create_db(\"leveldb\", self._dbfile, mode)\n", "        except:\n", "            raise 'Fail to open db!'\n", "        self._ls_col_names, self._ct_col_names = self.get_factors_colnames()\n", "\n", "    def close_db(self):\n", "        if not self._db:\n", "            raise \"not db open.\"\n", "        self._db.close()\n", "        del self._db\n", "        self._db=None\n", "\n", "    def load_all_keys(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        self._keys.clear()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key().decode('gbk', 'ignore')\n", "            if len(key) >= 16 and key[0:len(self._key_prefix)] == self._key_prefix: #'ffs:'\n", "                self._keys.append(key)\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def get_all_labels(self)->set:\n", "        lbs = set()\n", "        if len(self._keys) == 0:\n", "            self.load_all_keys()\n", "            \n", "        for key in self._keys:\n", "            pos0=key.find(':')\n", "            pos1=key.find(':', pos0+1)\n", "            lb=key[pos0+1:pos1]\n", "            lbs.add(lb)\n", "        return lbs\n", "\n", "    def get_factors_colnames(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        ls_colnames = []\n", "        ct_colnames = []\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                value = cursor.value().decode()\n", "                s2 = value.split('|')\n", "                if len(s2) <= 3:\n", "                    print(key)\n", "                    cursor.next()\n", "                    continue\n", "                lf = json.loads(s2[0])\n", "                ls_colnames.append('code')\n", "                ls_colnames.append('date')\n", "                for k, v in lf.items():\n", "                    if isinstance(v, list):\n", "                        ls_colnames.append(f'{k}_1')\n", "                        ls_colnames.append(f'{k}_2')\n", "                    else:\n", "                        ls_colnames.append(f'{k}')\n", "                ls_colnames.append('change')\n", "                if len(s2) == 4:\n", "                    ct_colnames.append('code')\n", "                    ct_colnames.append('date')\n", "                    ct = json.loads(s2[2])\n", "                    for k, v in ct.items():\n", "                        if isinstance(v, list):\n", "                            ct_colnames.append(f'{k}_1')\n", "                            ct_colnames.append(f'{k}_2')\n", "                        else:\n", "                            ct_colnames.append(f'{k}')\n", "                del cursor\n", "                return ls_colnames, ct_colnames\n", "            cursor.next()\n", "\n", "    def get_all_factors(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        ldata = []\n", "        sdata = []\n", "        cdata = []\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) >= 16 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                litem = []\n", "                sitem = []\n", "                citem = []\n", "                value = cursor.value().decode()\n", "                s1 = key.decode().split(':')\n", "                if (len(self.years) > 0):\n", "                    dt = date.fromtimestamp(int(s1[2]))\n", "                    if dt.year not in self.years:\n", "                        cursor.next()\n", "                        continue\n", "                litem.append(s1[1])\n", "                litem.append(int(s1[2]))\n", "                sitem.append(s1[1])\n", "                sitem.append(int(s1[2]))\n", "                citem.append(s1[1])\n", "                citem.append(int(s1[2]))\n", "                s2 = value.split('|')\n", "                assert len(s2) == 4, f'invalid value: {value}'\n", "                lf = json.loads(s2[0])\n", "                sf = json.loads(s2[1])\n", "                ct = json.loads(s2[2])\n", "                for _, v in lf.items():\n", "                    if isinstance(v, list):\n", "                        litem.append(v[1])\n", "                        litem.append(v[2])\n", "                    else:\n", "                        litem.append(v)\n", "                litem.append(s2[3]) # change\n", "                for _, v in sf.items():\n", "                    if isinstance(v, list):\n", "                        sitem.append(v[1])\n", "                        sitem.append(v[2])\n", "                    else:\n", "                        sitem.append(v)\n", "                for _, v in ct.items():\n", "                    if isinstance(v, list):\n", "                        citem.append(v[1])\n", "                        citem.append(v[2])\n", "                    else:\n", "                        citem.append(v)\n", "                sitem.append(s2[3]) # change\n", "                ldata.append(litem)\n", "                sdata.append(sitem)\n", "                cdata.append(citem)\n", "            cursor.next()\n", "        del cursor\n", "        ldf = pd.DataFrame(ldata, columns=self._ls_col_names)\n", "        sdf = pd.DataFrame(sdata, columns=self._ls_col_names)\n", "        cdf = pd.DataFrame(cdata, columns=self._ct_col_names)\n", "        return ldf, sdf, cdf\n", "    \n", "    def write(self, key, value):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.put(key, value)\n", "        transaction.commit()\n", "        del transaction\n", "        \n", "    def delete(self, key):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.delete(key)\n", "        transaction.commit()\n", "        del transaction\n", "\n", "    def clear(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                transaction.delete(key)\n", "                print(f'del key: {key}')\n", "            cursor.next()\n", "        transaction.commit()\n", "        del transaction\n", "        del cursor\n", "\n", "    def query(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            print(cursor.key())\n", "            # print(cursor.key(), cursor.value())\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def export_all(self):\n", "        def trans_timestamp(dt):\n", "            # return int(time.mktime(dt.timetuple()))//300\n", "            return int(dt//300)\n", "\n", "        def log_return(series):\n", "            return np.log(series).diff()\n", "            \n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        # self.open_db(Mode.read)\n", "        df=self.read_all()\n", "        # self.close_db()\n", "        print(df.shape)\n", "\n", "        try:\n", "            cols=df.columns\n", "            df = df.groupby([\"label\", \"datetime\"]).agg({\"mean\"}).reset_index()\n", "            df.columns = cols\n", "            df[\"time_id\"]=df[\"datetime\"].apply(trans_timestamp)\n", "\n", "            for lb in df[\"label\"].unique():\n", "                # df.to_parquet(f\"../data/tickdata.parquet\", engine='fastparquet')\n", "                df2=df[df[\"label\"]==lb]\n", "                df2.to_parquet(f\"{self._save_path}/tickdata.{lb}.parquet\", engine='fastparquet')\n", "                # df = pd.read_parquet(f\"../data/tickdata.parquet\")\n", "                df2 = df2.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()\n", "                df2['return'] = log_return(df2['price'])\n", "                df2=df2.fillna(0)\n", "                df2['target'] = (df2['return']>0).astype(int)\n", "                df2=df2.drop(['price', 'return'], axis=1)\n", "                df2.to_parquet(f\"{self._save_path}/tickdata_target.{lb}.parquet\", engine='fastparquet')\n", "        except:\n", "            raise 'Fail to export all data!'\n", "\n", "    def export_to_file(self):\n", "        if not self._db:\n", "            self.open_db(Mode.read)\n", "        ldf, sdf, cdf = self.get_all_factors()\n", "        self.close_db()\n", "        print(ldf.shape, sdf.shape, cdf.shape)\n", "        ldf.to_parquet(f\"{self._save_path}/ffs_lf.{self._save_file}.parquet\", engine='fastparquet')\n", "        sdf.to_parquet(f\"{self._save_path}/ffs_sf.{self._save_file}.parquet\", engine='fastparquet')\n", "        cdf.to_parquet(f\"{self._save_path}/ffs_ct.{self._save_file}.parquet\", engine='fastparquet')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["ff = FactorsKvDB(key_prefix='fsfs:',\n", "                 years=[2023],\n", "                 dbfile=\"d:/RoboQuant2/store/kv.db\",\n", "                 save_path=\"e:/featdata\",\n", "                 save_file=\"sf.2023\"\n", "                )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ff.export_to_file()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(6952, 235)\n", "(6952, 235)\n", "(6952, 48)\n"]}], "source": ["ff.open_db(Mode.read)\n", "ldf, sdf, cdf = ff.get_all_factors()\n", "ff.close_db()\n", "print(ldf.shape)\n", "print(sdf.shape)\n", "print(cdf.shape)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["ldf.to_parquet(f\"{ff._save_path}/ffs_lf.sf.2023.parquet\", engine='fastparquet')\n", "sdf.to_parquet(f\"{ff._save_path}/ffs_sf.sf.2023.parquet\", engine='fastparquet')\n", "cdf.to_parquet(f\"{ff._save_path}/ffs_ct.sf.2023.parquet\", engine='fastparquet')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 清除所有历史记录\n", "ff = FactorsKvDB(key_prefix='ffs:')\n", "ff.open_db(Mode.write)\n", "ff.clear()\n", "ff.close_db()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>AD_1</th>\n", "      <th>AD_2</th>\n", "      <th>ADX_1</th>\n", "      <th>ADX_2</th>\n", "      <th>ADXR_1</th>\n", "      <th>ADXR_2</th>\n", "      <th>APO_1</th>\n", "      <th>APO_2</th>\n", "      <th>...</th>\n", "      <th>TSF_2</th>\n", "      <th>TYPICAL_PRICE_1</th>\n", "      <th>TYPICAL_PRICE_2</th>\n", "      <th>ULTOSC_1</th>\n", "      <th>ULTOSC_2</th>\n", "      <th>VOLUME_1</th>\n", "      <th>VOLUME_2</th>\n", "      <th>WILLR_1</th>\n", "      <th>WILLR_2</th>\n", "      <th>change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>1566781200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A</td>\n", "      <td>1566781204</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A</td>\n", "      <td>1566781211</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A</td>\n", "      <td>1566781216</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A</td>\n", "      <td>1566781235</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539408</th>\n", "      <td>ZN</td>\n", "      <td>1595602854</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539409</th>\n", "      <td>ZN</td>\n", "      <td>1595603046</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539410</th>\n", "      <td>ZN</td>\n", "      <td>1595603564</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539411</th>\n", "      <td>ZN</td>\n", "      <td>1595603919</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0018</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539412</th>\n", "      <td>ZN</td>\n", "      <td>1595604910</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0026</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>539413 rows × 235 columns</p>\n", "</div>"], "text/plain": ["       code        date  AD_1  AD_2  ADX_1  ADX_2  ADXR_1  ADXR_2  APO_1  \\\n", "0         A  1566781200   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "1         A  1566781204   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "2         A  1566781211   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "3         A  1566781216   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "4         A  1566781235   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "...     ...         ...   ...   ...    ...    ...     ...     ...    ...   \n", "539408   ZN  1595602854   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "539409   ZN  1595603046   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "539410   ZN  1595603564   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "539411   ZN  1595603919   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "539412   ZN  1595604910   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "\n", "        APO_2  ...  TSF_2  TYPICAL_PRICE_1  TYPICAL_PRICE_2  ULTOSC_1  \\\n", "0         0.0  ...    0.0              0.0              0.0       0.0   \n", "1         0.0  ...    0.0              0.0              0.0       0.0   \n", "2         0.0  ...    0.0              0.0              0.0       0.0   \n", "3         0.0  ...    0.0              0.0              0.0       0.0   \n", "4         0.0  ...    0.0              0.0              0.0       0.0   \n", "...       ...  ...    ...              ...              ...       ...   \n", "539408    0.0  ...    0.0              0.0              0.0       0.0   \n", "539409    0.0  ...    0.0              0.0              0.0       0.0   \n", "539410    0.0  ...    0.0              0.0              0.0       0.0   \n", "539411    0.0  ...    0.0              0.0              0.0       0.0   \n", "539412    0.0  ...    0.0              0.0              0.0       0.0   \n", "\n", "        ULTOSC_2  VOLUME_1  VOLUME_2  WILLR_1  WILLR_2   change  \n", "0            0.0       0.0       0.0      0.0      0.0   0.0000  \n", "1            0.0       0.0       0.0      0.0      0.0   0.0000  \n", "2            0.0       0.0       0.0      0.0      0.0   0.0000  \n", "3            0.0       0.0       0.0      0.0      0.0   0.0000  \n", "4            0.0       0.0       0.0      0.0      0.0   0.0000  \n", "...          ...       ...       ...      ...      ...      ...  \n", "539408       0.0       0.0       0.0      0.0      0.0   0.0023  \n", "539409       0.0       0.0       0.0      0.0      0.0  -0.0032  \n", "539410       0.0       0.0       0.0      0.0      0.0  -0.0023  \n", "539411       0.0       0.0       0.0      0.0      0.0   0.0018  \n", "539412       0.0       0.0       0.0      0.0      0.0  -0.0026  \n", "\n", "[539413 rows x 235 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ldf"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>AD_1</th>\n", "      <th>AD_2</th>\n", "      <th>ADX_1</th>\n", "      <th>ADX_2</th>\n", "      <th>ADXR_1</th>\n", "      <th>ADXR_2</th>\n", "      <th>APO_1</th>\n", "      <th>APO_2</th>\n", "      <th>...</th>\n", "      <th>TSF_2</th>\n", "      <th>TYPICAL_PRICE_1</th>\n", "      <th>TYPICAL_PRICE_2</th>\n", "      <th>ULTOSC_1</th>\n", "      <th>ULTOSC_2</th>\n", "      <th>VOLUME_1</th>\n", "      <th>VOLUME_2</th>\n", "      <th>WILLR_1</th>\n", "      <th>WILLR_2</th>\n", "      <th>change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>1672707600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A</td>\n", "      <td>1672707601</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A</td>\n", "      <td>1672707604</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A</td>\n", "      <td>1672707606</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A</td>\n", "      <td>1672707620</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66112</th>\n", "      <td>Y</td>\n", "      <td>1677508209</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>8928.666667</td>\n", "      <td>8918.666667</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66113</th>\n", "      <td>Y</td>\n", "      <td>1677508512</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>8918.666667</td>\n", "      <td>8914.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66114</th>\n", "      <td>Y</td>\n", "      <td>1677508708</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>8918.666667</td>\n", "      <td>8921.333333</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66115</th>\n", "      <td>Y</td>\n", "      <td>1677509666</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>8924.666667</td>\n", "      <td>8927.333333</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.0015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66116</th>\n", "      <td>Y</td>\n", "      <td>1677509951</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>8930.000000</td>\n", "      <td>8932.666667</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0028</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>66117 rows × 235 columns</p>\n", "</div>"], "text/plain": ["      code        date  AD_1  AD_2  ADX_1  ADX_2  ADXR_1  ADXR_2  APO_1  \\\n", "0        A  1672707600   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "1        A  1672707601   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "2        A  1672707604   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "3        A  1672707606   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "4        A  1672707620   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "...    ...         ...   ...   ...    ...    ...     ...     ...    ...   \n", "66112    Y  1677508209   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "66113    Y  1677508512   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "66114    Y  1677508708   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "66115    Y  1677509666   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "66116    Y  1677509951   0.0   0.0    0.0    0.0     0.0     0.0    0.0   \n", "\n", "       APO_2  ...  TSF_2  TYPICAL_PRICE_1  TYPICAL_PRICE_2  ULTOSC_1  \\\n", "0        0.0  ...    0.0         0.000000         0.000000       0.0   \n", "1        0.0  ...    0.0         0.000000         0.000000       0.0   \n", "2        0.0  ...    0.0         0.000000         0.000000       0.0   \n", "3        0.0  ...    0.0         0.000000         0.000000       0.0   \n", "4        0.0  ...    0.0         0.000000         0.000000       0.0   \n", "...      ...  ...    ...              ...              ...       ...   \n", "66112    0.0  ...    0.0      8928.666667      8918.666667       0.0   \n", "66113    0.0  ...    0.0      8918.666667      8914.000000       0.0   \n", "66114    0.0  ...    0.0      8918.666667      8921.333333       0.0   \n", "66115    0.0  ...    0.0      8924.666667      8927.333333       0.0   \n", "66116    0.0  ...    0.0      8930.000000      8932.666667       0.0   \n", "\n", "       ULTOSC_2  VOLUME_1  VOLUME_2  WILLR_1  WILLR_2   change  \n", "0           0.0       0.0       0.0      0.0      0.0   0.0000  \n", "1           0.0       0.0       0.0      0.0      0.0   0.0000  \n", "2           0.0       0.0       0.0      0.0      0.0   0.0000  \n", "3           0.0       0.0       0.0      0.0      0.0   0.0000  \n", "4           0.0       0.0       0.0      0.0      0.0   0.0000  \n", "...         ...       ...       ...      ...      ...      ...  \n", "66112       0.0       0.0       0.0      0.0      0.0   0.0017  \n", "66113       0.0       0.0       0.0      0.0      0.0  -0.0015  \n", "66114       0.0       0.0       0.0      0.0      0.0  -0.0026  \n", "66115       0.0       0.0       0.0      0.0      0.0  -0.0015  \n", "66116       0.0       0.0       0.0      0.0      0.0   0.0028  \n", "\n", "[66117 rows x 235 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["sdf"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_NATR\", \"FAST_QH_NATR_ZSCORE\", \"FAST_QH_NATR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>AD_PS_RATIO</th>\n", "      <th>COST_RNG</th>\n", "      <th>CURRENT_TIME</th>\n", "      <th>DRAWDOWN_RNG</th>\n", "      <th>FAST_AG_DIRECT</th>\n", "      <th>FAST_AG_RSI</th>\n", "      <th>FAST_AG_STDDEV</th>\n", "      <th>FAST_AG_ZSCORE</th>\n", "      <th>...</th>\n", "      <th>SLOW_QH_MOM_STDDEV</th>\n", "      <th>SLOW_QH_MOM_ZSCORE</th>\n", "      <th>SLOW_QH_NATR</th>\n", "      <th>SLOW_QH_NATR_DIRECT</th>\n", "      <th>SLOW_QH_NATR_STDDEV</th>\n", "      <th>SLOW_QH_NATR_ZSCORE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_STDDEV</th>\n", "      <th>SLOW_QH_ZSCORE</th>\n", "      <th>STDDEV_RNG</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>1672707600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>900.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A</td>\n", "      <td>1672707601</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>900.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A</td>\n", "      <td>1672707604</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>900.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A</td>\n", "      <td>1672707606</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>900.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A</td>\n", "      <td>1672707620</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>900.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66112</th>\n", "      <td>Y</td>\n", "      <td>1677508209</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2230.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.361103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66113</th>\n", "      <td>Y</td>\n", "      <td>1677508512</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2235.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.360068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66114</th>\n", "      <td>Y</td>\n", "      <td>1677508708</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2238.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.358977</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66115</th>\n", "      <td>Y</td>\n", "      <td>1677509666</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2254.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.357922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66116</th>\n", "      <td>Y</td>\n", "      <td>1677509951</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2259.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.356786</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>66117 rows × 48 columns</p>\n", "</div>"], "text/plain": ["      code        date  AD_PS_RATIO  COST_RNG  CURRENT_TIME  DRAWDOWN_RNG  \\\n", "0        A  1672707600          0.0       0.0         900.0           0.0   \n", "1        A  1672707601          0.0       0.0         900.0           0.0   \n", "2        A  1672707604          0.0       0.0         900.0           0.0   \n", "3        A  1672707606          0.0       0.0         900.0           0.0   \n", "4        A  1672707620          0.0       0.0         900.0           0.0   \n", "...    ...         ...          ...       ...           ...           ...   \n", "66112    Y  1677508209          0.0       0.0        2230.0           0.0   \n", "66113    Y  1677508512          0.0       0.0        2235.0           0.0   \n", "66114    Y  1677508708          0.0       0.0        2238.0           0.0   \n", "66115    Y  1677509666          0.0       0.0        2254.0           0.0   \n", "66116    Y  1677509951          0.0       0.0        2259.0           0.0   \n", "\n", "       FAST_AG_DIRECT  FAST_AG_RSI  FAST_AG_STDDEV  FAST_AG_ZSCORE  ...  \\\n", "0                 0.0          0.0             0.0             0.0  ...   \n", "1                 0.0          0.0             0.0             0.0  ...   \n", "2                 0.0          0.0             0.0             0.0  ...   \n", "3                 0.0          0.0             0.0             0.0  ...   \n", "4                 0.0          0.0             0.0             0.0  ...   \n", "...               ...          ...             ...             ...  ...   \n", "66112             0.0          0.0             0.0             0.0  ...   \n", "66113             0.0          0.0             0.0             0.0  ...   \n", "66114             0.0          0.0             0.0             0.0  ...   \n", "66115             0.0          0.0             0.0             0.0  ...   \n", "66116             0.0          0.0             0.0             0.0  ...   \n", "\n", "       SLOW_QH_MOM_STDDEV  SLOW_QH_MOM_ZSCORE  SLOW_QH_NATR  \\\n", "0                     0.0                 0.0          None   \n", "1                     0.0                 0.0          None   \n", "2                     0.0                 0.0          None   \n", "3                     0.0                 0.0          None   \n", "4                     0.0                 0.0          None   \n", "...                   ...                 ...           ...   \n", "66112                 0.0                 0.0          None   \n", "66113                 0.0                 0.0          None   \n", "66114                 0.0                 0.0          None   \n", "66115                 0.0                 0.0          None   \n", "66116                 0.0                 0.0          None   \n", "\n", "       SLOW_QH_NATR_DIRECT  SLOW_QH_NATR_STDDEV  SLOW_QH_NATR_ZSCORE  \\\n", "0                      0.0                  0.0                  0.0   \n", "1                      0.0                  0.0                  0.0   \n", "2                      0.0                  0.0                  0.0   \n", "3                      0.0                  0.0                  0.0   \n", "4                      0.0                  0.0                  0.0   \n", "...                    ...                  ...                  ...   \n", "66112                  0.0                  0.0                  0.0   \n", "66113                  0.0                  0.0                  0.0   \n", "66114                  0.0                  0.0                  0.0   \n", "66115                  0.0                  0.0                  0.0   \n", "66116                  0.0                  0.0                  0.0   \n", "\n", "       SLOW_QH_RSI  SLOW_QH_STDDEV  SLOW_QH_ZSCORE  STDDEV_RNG  \n", "0             None             0.0             0.0    0.000000  \n", "1             None             0.0             0.0    0.000000  \n", "2             None             0.0             0.0    0.000000  \n", "3             None             0.0             0.0    0.000000  \n", "4             None             0.0             0.0    0.000000  \n", "...            ...             ...             ...         ...  \n", "66112         None             0.0             0.0    1.361103  \n", "66113         None             0.0             0.0    1.360068  \n", "66114         None             0.0             0.0    1.358977  \n", "66115         None             0.0             0.0    1.357922  \n", "66116         None             0.0             0.0    1.356786  \n", "\n", "[66117 rows x 48 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["cdf"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["cdf = cdf[SEL_CONTEXT_FACTOR_NAMES]"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_ZSCORE</th>\n", "      <th>FAST_QH_DIRECT</th>\n", "      <th>FAST_QH_NATR</th>\n", "      <th>FAST_QH_NATR_ZSCORE</th>\n", "      <th>FAST_QH_NATR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.000000</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.000000</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.000000</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.000000</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.000000</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66112</th>\n", "      <td>1.361103</td>\n", "      <td>13.0</td>\n", "      <td>-2.680533</td>\n", "      <td>-0.653018</td>\n", "      <td>-2.0</td>\n", "      <td>0.172091</td>\n", "      <td>-1.102799</td>\n", "      <td>-2.0</td>\n", "      <td>-0.077824</td>\n", "      <td>-0.992180</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66113</th>\n", "      <td>1.360068</td>\n", "      <td>13.0</td>\n", "      <td>-3.356433</td>\n", "      <td>-0.791486</td>\n", "      <td>-2.0</td>\n", "      <td>0.174459</td>\n", "      <td>-1.021465</td>\n", "      <td>-1.0</td>\n", "      <td>-0.022790</td>\n", "      <td>-0.319680</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66114</th>\n", "      <td>1.358977</td>\n", "      <td>13.0</td>\n", "      <td>-4.228369</td>\n", "      <td>-0.970081</td>\n", "      <td>-4.0</td>\n", "      <td>0.174267</td>\n", "      <td>-1.027549</td>\n", "      <td>-1.0</td>\n", "      <td>-0.036787</td>\n", "      <td>-0.490605</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66115</th>\n", "      <td>1.357922</td>\n", "      <td>13.0</td>\n", "      <td>-4.728012</td>\n", "      <td>-1.071297</td>\n", "      <td>-3.0</td>\n", "      <td>0.174510</td>\n", "      <td>-1.017936</td>\n", "      <td>-1.0</td>\n", "      <td>-0.008532</td>\n", "      <td>-0.145117</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66116</th>\n", "      <td>1.356786</td>\n", "      <td>13.0</td>\n", "      <td>-2.811719</td>\n", "      <td>-0.677892</td>\n", "      <td>-2.0</td>\n", "      <td>0.174380</td>\n", "      <td>-1.021892</td>\n", "      <td>1.0</td>\n", "      <td>0.055278</td>\n", "      <td>0.634680</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>66117 rows × 11 columns</p>\n", "</div>"], "text/plain": ["       STDDEV_RNG  SHORT_RANGE  FAST_QH_RSI  FAST_QH_ZSCORE  FAST_QH_DIRECT  \\\n", "0        0.000000          7.0          NaN        0.000000             0.0   \n", "1        0.000000          7.0          NaN        0.000000             0.0   \n", "2        0.000000          7.0          NaN        0.000000             0.0   \n", "3        0.000000          7.0          NaN        0.000000             0.0   \n", "4        0.000000          7.0          NaN        0.000000             0.0   \n", "...           ...          ...          ...             ...             ...   \n", "66112    1.361103         13.0    -2.680533       -0.653018            -2.0   \n", "66113    1.360068         13.0    -3.356433       -0.791486            -2.0   \n", "66114    1.358977         13.0    -4.228369       -0.970081            -4.0   \n", "66115    1.357922         13.0    -4.728012       -1.071297            -3.0   \n", "66116    1.356786         13.0    -2.811719       -0.677892            -2.0   \n", "\n", "       FAST_QH_NATR  FAST_QH_NATR_ZSCORE  FAST_QH_NATR_DIRECT  FAST_QH_MOM  \\\n", "0               NaN             0.000000                  0.0          NaN   \n", "1               NaN             0.000000                  0.0          NaN   \n", "2               NaN             0.000000                  0.0          NaN   \n", "3               NaN             0.000000                  0.0          NaN   \n", "4               NaN             0.000000                  0.0          NaN   \n", "...             ...                  ...                  ...          ...   \n", "66112      0.172091            -1.102799                 -2.0    -0.077824   \n", "66113      0.174459            -1.021465                 -1.0    -0.022790   \n", "66114      0.174267            -1.027549                 -1.0    -0.036787   \n", "66115      0.174510            -1.017936                 -1.0    -0.008532   \n", "66116      0.174380            -1.021892                  1.0     0.055278   \n", "\n", "       FAST_QH_MOM_ZSCORE  FAST_QH_MOM_DIRECT  \n", "0                0.000000                 0.0  \n", "1                0.000000                 0.0  \n", "2                0.000000                 0.0  \n", "3                0.000000                 0.0  \n", "4                0.000000                 0.0  \n", "...                   ...                 ...  \n", "66112           -0.992180                 1.0  \n", "66113           -0.319680                 1.0  \n", "66114           -0.490605                -1.0  \n", "66115           -0.145117                -1.0  \n", "66116            0.634680                 1.0  \n", "\n", "[66117 rows x 11 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["cdf"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23616\\347242293.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  cdf.dropna(axis=0, how='any', inplace=True)\n"]}], "source": ["cdf.dropna(axis=0, how='any', inplace=True)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["nan_count = (cdf.isna().sum(axis=1))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["print((nan_count>0).sum())"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_ZSCORE</th>\n", "      <th>FAST_QH_DIRECT</th>\n", "      <th>FAST_QH_NATR</th>\n", "      <th>FAST_QH_NATR_ZSCORE</th>\n", "      <th>FAST_QH_NATR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>1.244037</td>\n", "      <td>7.0</td>\n", "      <td>8.319250</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.358624</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.087094</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>1.254707</td>\n", "      <td>7.0</td>\n", "      <td>10.378830</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.342164</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.081228</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>1.268204</td>\n", "      <td>7.0</td>\n", "      <td>10.014950</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.340868</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.092897</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>1.278108</td>\n", "      <td>7.0</td>\n", "      <td>8.566335</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.337795</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.075716</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>1.286947</td>\n", "      <td>7.0</td>\n", "      <td>8.625566</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.337520</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.078084</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66112</th>\n", "      <td>1.361103</td>\n", "      <td>13.0</td>\n", "      <td>-2.680533</td>\n", "      <td>-0.653018</td>\n", "      <td>-2.0</td>\n", "      <td>0.172091</td>\n", "      <td>-1.102799</td>\n", "      <td>-2.0</td>\n", "      <td>-0.077824</td>\n", "      <td>-0.992180</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66113</th>\n", "      <td>1.360068</td>\n", "      <td>13.0</td>\n", "      <td>-3.356433</td>\n", "      <td>-0.791486</td>\n", "      <td>-2.0</td>\n", "      <td>0.174459</td>\n", "      <td>-1.021465</td>\n", "      <td>-1.0</td>\n", "      <td>-0.022790</td>\n", "      <td>-0.319680</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66114</th>\n", "      <td>1.358977</td>\n", "      <td>13.0</td>\n", "      <td>-4.228369</td>\n", "      <td>-0.970081</td>\n", "      <td>-4.0</td>\n", "      <td>0.174267</td>\n", "      <td>-1.027549</td>\n", "      <td>-1.0</td>\n", "      <td>-0.036787</td>\n", "      <td>-0.490605</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66115</th>\n", "      <td>1.357922</td>\n", "      <td>13.0</td>\n", "      <td>-4.728012</td>\n", "      <td>-1.071297</td>\n", "      <td>-3.0</td>\n", "      <td>0.174510</td>\n", "      <td>-1.017936</td>\n", "      <td>-1.0</td>\n", "      <td>-0.008532</td>\n", "      <td>-0.145117</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66116</th>\n", "      <td>1.356786</td>\n", "      <td>13.0</td>\n", "      <td>-2.811719</td>\n", "      <td>-0.677892</td>\n", "      <td>-2.0</td>\n", "      <td>0.174380</td>\n", "      <td>-1.021892</td>\n", "      <td>1.0</td>\n", "      <td>0.055278</td>\n", "      <td>0.634680</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>64658 rows × 11 columns</p>\n", "</div>"], "text/plain": ["       STDDEV_RNG  SHORT_RANGE  FAST_QH_RSI  FAST_QH_ZSCORE  FAST_QH_DIRECT  \\\n", "58       1.244037          7.0     8.319250        0.000000             0.0   \n", "59       1.254707          7.0    10.378830        0.000000             0.0   \n", "60       1.268204          7.0    10.014950        0.000000             0.0   \n", "61       1.278108          7.0     8.566335        0.000000             0.0   \n", "62       1.286947          7.0     8.625566        0.000000             0.0   \n", "...           ...          ...          ...             ...             ...   \n", "66112    1.361103         13.0    -2.680533       -0.653018            -2.0   \n", "66113    1.360068         13.0    -3.356433       -0.791486            -2.0   \n", "66114    1.358977         13.0    -4.228369       -0.970081            -4.0   \n", "66115    1.357922         13.0    -4.728012       -1.071297            -3.0   \n", "66116    1.356786         13.0    -2.811719       -0.677892            -2.0   \n", "\n", "       FAST_QH_NATR  FAST_QH_NATR_ZSCORE  FAST_QH_NATR_DIRECT  FAST_QH_MOM  \\\n", "58         0.358624             0.000000                  0.0     0.087094   \n", "59         0.342164             0.000000                  0.0     0.081228   \n", "60         0.340868             0.000000                  0.0     0.092897   \n", "61         0.337795             0.000000                  0.0     0.075716   \n", "62         0.337520             0.000000                  0.0     0.078084   \n", "...             ...                  ...                  ...          ...   \n", "66112      0.172091            -1.102799                 -2.0    -0.077824   \n", "66113      0.174459            -1.021465                 -1.0    -0.022790   \n", "66114      0.174267            -1.027549                 -1.0    -0.036787   \n", "66115      0.174510            -1.017936                 -1.0    -0.008532   \n", "66116      0.174380            -1.021892                  1.0     0.055278   \n", "\n", "       FAST_QH_MOM_ZSCORE  FAST_QH_MOM_DIRECT  \n", "58               0.000000                 0.0  \n", "59               0.000000                 0.0  \n", "60               0.000000                 0.0  \n", "61               0.000000                 0.0  \n", "62               0.000000                 0.0  \n", "...                   ...                 ...  \n", "66112           -0.992180                 1.0  \n", "66113           -0.319680                 1.0  \n", "66114           -0.490605                -1.0  \n", "66115           -0.145117                -1.0  \n", "66116            0.634680                 1.0  \n", "\n", "[64658 rows x 11 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["cdf"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["cdf = cdf[cdf['FAST_QH_NATR_ZSCORE'] != 0.0]\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_ZSCORE</th>\n", "      <th>FAST_QH_DIRECT</th>\n", "      <th>FAST_QH_NATR</th>\n", "      <th>FAST_QH_NATR_ZSCORE</th>\n", "      <th>FAST_QH_NATR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>1.219410</td>\n", "      <td>7.0</td>\n", "      <td>7.483320</td>\n", "      <td>-0.065058</td>\n", "      <td>2.0</td>\n", "      <td>0.248586</td>\n", "      <td>-1.531147</td>\n", "      <td>-2.0</td>\n", "      <td>0.059627</td>\n", "      <td>0.164466</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>1.216296</td>\n", "      <td>7.0</td>\n", "      <td>7.230292</td>\n", "      <td>-0.136874</td>\n", "      <td>2.0</td>\n", "      <td>0.240707</td>\n", "      <td>-1.512046</td>\n", "      <td>-2.0</td>\n", "      <td>0.057629</td>\n", "      <td>0.142430</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>1.207981</td>\n", "      <td>7.0</td>\n", "      <td>7.443738</td>\n", "      <td>-0.066313</td>\n", "      <td>1.0</td>\n", "      <td>0.236771</td>\n", "      <td>-1.388441</td>\n", "      <td>-2.0</td>\n", "      <td>0.023074</td>\n", "      <td>-0.187130</td>\n", "      <td>-2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>1.199298</td>\n", "      <td>7.0</td>\n", "      <td>7.470556</td>\n", "      <td>-0.053810</td>\n", "      <td>1.0</td>\n", "      <td>0.225670</td>\n", "      <td>-1.562814</td>\n", "      <td>-2.0</td>\n", "      <td>0.059068</td>\n", "      <td>0.171862</td>\n", "      <td>-2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>1.191999</td>\n", "      <td>7.0</td>\n", "      <td>7.588455</td>\n", "      <td>-0.016225</td>\n", "      <td>1.0</td>\n", "      <td>0.225746</td>\n", "      <td>-1.560944</td>\n", "      <td>-2.0</td>\n", "      <td>0.057268</td>\n", "      <td>0.153972</td>\n", "      <td>-2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66112</th>\n", "      <td>1.361103</td>\n", "      <td>13.0</td>\n", "      <td>-2.680533</td>\n", "      <td>-0.653018</td>\n", "      <td>-2.0</td>\n", "      <td>0.172091</td>\n", "      <td>-1.102799</td>\n", "      <td>-2.0</td>\n", "      <td>-0.077824</td>\n", "      <td>-0.992180</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66113</th>\n", "      <td>1.360068</td>\n", "      <td>13.0</td>\n", "      <td>-3.356433</td>\n", "      <td>-0.791486</td>\n", "      <td>-2.0</td>\n", "      <td>0.174459</td>\n", "      <td>-1.021465</td>\n", "      <td>-1.0</td>\n", "      <td>-0.022790</td>\n", "      <td>-0.319680</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66114</th>\n", "      <td>1.358977</td>\n", "      <td>13.0</td>\n", "      <td>-4.228369</td>\n", "      <td>-0.970081</td>\n", "      <td>-4.0</td>\n", "      <td>0.174267</td>\n", "      <td>-1.027549</td>\n", "      <td>-1.0</td>\n", "      <td>-0.036787</td>\n", "      <td>-0.490605</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66115</th>\n", "      <td>1.357922</td>\n", "      <td>13.0</td>\n", "      <td>-4.728012</td>\n", "      <td>-1.071297</td>\n", "      <td>-3.0</td>\n", "      <td>0.174510</td>\n", "      <td>-1.017936</td>\n", "      <td>-1.0</td>\n", "      <td>-0.008532</td>\n", "      <td>-0.145117</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66116</th>\n", "      <td>1.356786</td>\n", "      <td>13.0</td>\n", "      <td>-2.811719</td>\n", "      <td>-0.677892</td>\n", "      <td>-2.0</td>\n", "      <td>0.174380</td>\n", "      <td>-1.021892</td>\n", "      <td>1.0</td>\n", "      <td>0.055278</td>\n", "      <td>0.634680</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>64185 rows × 11 columns</p>\n", "</div>"], "text/plain": ["       STDDEV_RNG  SHORT_RANGE  FAST_QH_RSI  FAST_QH_ZSCORE  FAST_QH_DIRECT  \\\n", "76       1.219410          7.0     7.483320       -0.065058             2.0   \n", "77       1.216296          7.0     7.230292       -0.136874             2.0   \n", "78       1.207981          7.0     7.443738       -0.066313             1.0   \n", "79       1.199298          7.0     7.470556       -0.053810             1.0   \n", "80       1.191999          7.0     7.588455       -0.016225             1.0   \n", "...           ...          ...          ...             ...             ...   \n", "66112    1.361103         13.0    -2.680533       -0.653018            -2.0   \n", "66113    1.360068         13.0    -3.356433       -0.791486            -2.0   \n", "66114    1.358977         13.0    -4.228369       -0.970081            -4.0   \n", "66115    1.357922         13.0    -4.728012       -1.071297            -3.0   \n", "66116    1.356786         13.0    -2.811719       -0.677892            -2.0   \n", "\n", "       FAST_QH_NATR  FAST_QH_NATR_ZSCORE  FAST_QH_NATR_DIRECT  FAST_QH_MOM  \\\n", "76         0.248586            -1.531147                 -2.0     0.059627   \n", "77         0.240707            -1.512046                 -2.0     0.057629   \n", "78         0.236771            -1.388441                 -2.0     0.023074   \n", "79         0.225670            -1.562814                 -2.0     0.059068   \n", "80         0.225746            -1.560944                 -2.0     0.057268   \n", "...             ...                  ...                  ...          ...   \n", "66112      0.172091            -1.102799                 -2.0    -0.077824   \n", "66113      0.174459            -1.021465                 -1.0    -0.022790   \n", "66114      0.174267            -1.027549                 -1.0    -0.036787   \n", "66115      0.174510            -1.017936                 -1.0    -0.008532   \n", "66116      0.174380            -1.021892                  1.0     0.055278   \n", "\n", "       FAST_QH_MOM_ZSCORE  FAST_QH_MOM_DIRECT  \n", "76               0.164466                 2.0  \n", "77               0.142430                 1.0  \n", "78              -0.187130                -2.0  \n", "79               0.171862                -2.0  \n", "80               0.153972                -2.0  \n", "...                   ...                 ...  \n", "66112           -0.992180                 1.0  \n", "66113           -0.319680                 1.0  \n", "66114           -0.490605                -1.0  \n", "66115           -0.145117                -1.0  \n", "66116            0.634680                 1.0  \n", "\n", "[64185 rows x 11 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["cdf"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["df=pd.read_parquet(\"e:/featdata/ffs_sf.sf.2023.parquet\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#df统计\n", "df.describe().to_csv(\"e:/featdata/ffs_sf.sf.2023.describe.csv\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["del df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}