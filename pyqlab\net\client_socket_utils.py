import errno
import socket
import threading
import time
import struct
import zlib
from logging import getLogger
from abc import ABC, abstractmethod


from pyqlab.net.message import Message


class ClientSocketUtils(ABC):

    def __init__(self, ip: str, port: int):
        # self.name = name
        self.__ip__ = ip
        self.__port__ = port
        self.BUFSIZE = 8192
        self.connected = False
        self.thrd = None
        self.thrd_heart = None
        self.logger = getLogger(__name__)
        self.s = socket.socket()

    def heart_handler(self):
        '''发送心跳包'''
        while True:
            if self.connected:
                self.heart()
            time.sleep(24)

    def connect(self):
        try:
            self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.s.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1) # 在客户端开启心跳维护
            self.logger.info('Socket created.')
            self.s.settimeout(12)
            self.s.connect((self.__ip__, self.__port__))
            self.s.settimeout(None)
            self.connected = True
            # thread sending heart beat message
            self.thrd_heart = threading.Thread(target = self.heart_handler)
            self.thrd_heart.setDaemon(True)
            self.thrd_heart.start()
        except socket.error as e:
            self.logger.error('Failed to create socket %s' % e)
            return False

        return True
            
    def is_connect(self):
        return self.connected

    def start(self):
        self.thrd = threading.Thread(target=self.recv_worker)
        self.thrd.setDaemon(True)
        self.thrd.start()

    def stop(self):
        # self.thrd.join()
        self.close()

    def close(self):
        # Clean up
        self.logger.debug('closing socket')
        # self.thrd_heart.join()
        self.s.shutdown(2)
        self.s.close()
        self.logger.debug('done')

    def heart(self):
        msg = Message()
        msg.command = 0
        msg.business_type = 0
        msg.app_id = 1
        msg.body = "H"
        self.send(msg.package_msg())

    def regist(self):
        msg = Message()
        msg.command = 1
        msg.business_type = 0
        msg.app_id = 1
        msg.body = "R"
        self.send(msg.package_msg())

    def send(self, message):
        try:
            self.s.sendall(message)
        except socket.error as e:
            errmsg = ''
            if e.errno == errno.ECONNRESET:
                self.connected = False
                errmsg = 'sendall failed, errno:%d %s' % (e.errno, e)
                print(errmsg)
                self.logger.error(errmsg)
            else:
                errmsg = 'send failed, errno:%d, %s' % (e.errno, e)
                print(errmsg)
                self.logger.error(errmsg)
                self.connected = False
                # raise
            self.close()

    @abstractmethod
    def process_recv_data(self, msg: Message):
        raise NotImplementedError

    def recv_worker(self):
        remain_size = 0
        recv_len = 0
        total_data = b''
        data = b''
        while True:
            try:
                if remain_size == 0 or remain_size >= self.BUFSIZE:
                    recv_len = self.BUFSIZE
                else:
                    recv_len = remain_size

                data = self.s.recv(recv_len)
                if not data:
                    # print("error:not data.")
                    break
                total_data = total_data + data
                if len(total_data) < 8:
                    self.logger.error("recev message data error. %s", total_data)
                    total_data = b''
                    remain_size == 0
                    continue
                header = total_data[0:8]
                tag, st, msg_size = struct.unpack('<3sbi', header)
                if msg_size > 7:
                    # print("Header tag: %s %d msg_size: %d" % (tag, st, msg_size))
                    pass
                if tag.decode('ascii', 'ignore') == 'XXH':
                    if (len(total_data) - 8) < msg_size: # 包未收全
                        remain_size = msg_size - len(total_data) + 8
                        # print("remain_size: %d = %d - %d" % (remain_size, msg_size, len(total_data)+8))
                    else: # 包已收全
                        if msg_size > 15:
                            # print("next msg size: %d = %d - %d" % (len(total_data) - 8 - msg_size, len(total_data) - 8, msg_size))
                            pass
                        msg = Message()
                        msg.unpackage_msg(total_data[8:msg_size + 8], msg_size) # 解包
                        if msg.command == 0:
                            # print("heart beat.")
                            pass
                        elif msg.command == 1:
                            # 注册客户端
                            print("regist client")
                            # self.regist()
                        else:
                            self.process_recv_data(msg)
                            
                        if remain_size - 8 > msg_size: # 将多余部分转给下一个包
                            remain_size = len(total_data) - 8 - msg_size
                            total_data = total_data[(8 + msg_size):]
                            # print("next msg size: %d " % remain_size)
                        else:
                            remain_size = 0
                            total_data = b''
                else:
                    if remain_size > 0:
                        self.logger.error(f"unknow message({remain_size})")
                    data = b''
                    total_data = b''
                    remain_size = 0
                    continue
                time.sleep(1)
            except socket.error as e:
                if e.errno == errno.ECONNRESET:
                    self.connected = False
                else:
                    total_data = b''
                    remain_size = 0
                    # raise
                # self.logger.error('recv failed %s' % e)
                self.close()
                time.sleep(3)

    