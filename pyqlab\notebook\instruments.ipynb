{"cells": [{"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "\n", "def get_data_frame(dbfile='secdata.db'):\n", "    # 打开数据库连接\n", "    conn = sqlite3.connect(dbfile)\n", "    conn.text_factory = lambda x: str(x, 'gbk', 'ignore')\n", "    # 使用 pandas 的 read_sql_query 函数读取数据\n", "    # 这里假设您的数据库和表支持 UTF-8 编码\n", "    # pandas 默认处理 UTF-8 编码，所以不需要额外指定\n", "    df = pd.read_sql_query('select * from T_CtpInstrumentData', conn)\n", "    # 关闭数据库连接\n", "    conn.close()\n", "    return df\n", "\n", "# Function to convert GBK to UTF-8\n", "def gbk_to_utf8(text):\n", "    try:\n", "        return text.encode('gbK').decode('UTF-8')\n", "    except (AttributeError, UnicodeDecodeError):\n", "        return text"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["df = get_data_frame(r\"C:\\Users\\<USER>\\Desktop\\secdata.db\")\n", "df['InstrumentName'] = df['InstrumentName'].apply(gbk_to_utf8)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InstrumentID</th>\n", "      <th>ExchangeID</th>\n", "      <th>InstrumentName</th>\n", "      <th>ExchangeInstID</th>\n", "      <th>ProductID</th>\n", "      <th>ProductClass</th>\n", "      <th>Delivery<PERSON>ear</th>\n", "      <th>DeliveryMonth</th>\n", "      <th>MaxMarketOrderVolume</th>\n", "      <th>MinMarketOrderVolume</th>\n", "      <th>...</th>\n", "      <th>StrikePrice</th>\n", "      <th>OptionsType</th>\n", "      <th>UnderlyingMultiple</th>\n", "      <th>CombinationType</th>\n", "      <th>HedgeFlag</th>\n", "      <th>LongMarginRatioByMoney</th>\n", "      <th>LongMarginRatioByVolume</th>\n", "      <th>ShortMarginRatioByMoney</th>\n", "      <th>ShortMarginRatioByVolume</th>\n", "      <th>IsRelative</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>a2407</td>\n", "      <td>DCE</td>\n", "      <td>豆一2407</td>\n", "      <td>a2407</td>\n", "      <td>a</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>7</td>\n", "      <td>1000</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>a2409</td>\n", "      <td>DCE</td>\n", "      <td>豆一2409</td>\n", "      <td>a2409</td>\n", "      <td>a</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>9</td>\n", "      <td>1000</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ag2408</td>\n", "      <td>SHFE</td>\n", "      <td>ag2408</td>\n", "      <td>ag2408</td>\n", "      <td>ag</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>8</td>\n", "      <td>30</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.18</td>\n", "      <td>0.0</td>\n", "      <td>0.18</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>al2407</td>\n", "      <td>SHFE</td>\n", "      <td>al2407</td>\n", "      <td>al2407</td>\n", "      <td>al</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>7</td>\n", "      <td>30</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.15</td>\n", "      <td>0.0</td>\n", "      <td>0.15</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>al2408</td>\n", "      <td>SHFE</td>\n", "      <td>al2408</td>\n", "      <td>al2408</td>\n", "      <td>al</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>8</td>\n", "      <td>30</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.15</td>\n", "      <td>0.0</td>\n", "      <td>0.15</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>UR409</td>\n", "      <td>CZCE</td>\n", "      <td>尿素9月</td>\n", "      <td>UR409</td>\n", "      <td>UR</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>9</td>\n", "      <td>200</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.13</td>\n", "      <td>0.0</td>\n", "      <td>0.13</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>v2409</td>\n", "      <td>DCE</td>\n", "      <td>聚氯乙烯2409</td>\n", "      <td>v2409</td>\n", "      <td>v</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>9</td>\n", "      <td>1000</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>y2409</td>\n", "      <td>DCE</td>\n", "      <td>豆油2409</td>\n", "      <td>y2409</td>\n", "      <td>y</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>9</td>\n", "      <td>1000</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>zn2407</td>\n", "      <td>SHFE</td>\n", "      <td>zn2407</td>\n", "      <td>zn2407</td>\n", "      <td>zn</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>7</td>\n", "      <td>30</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.14</td>\n", "      <td>0.0</td>\n", "      <td>0.14</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>zn2408</td>\n", "      <td>SHFE</td>\n", "      <td>zn2408</td>\n", "      <td>zn2408</td>\n", "      <td>zn</td>\n", "      <td>49</td>\n", "      <td>2024</td>\n", "      <td>8</td>\n", "      <td>30</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>48</td>\n", "      <td>49</td>\n", "      <td>0.14</td>\n", "      <td>0.0</td>\n", "      <td>0.14</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>78 rows × 37 columns</p>\n", "</div>"], "text/plain": ["   InstrumentID ExchangeID InstrumentName ExchangeInstID ProductID  \\\n", "0         a2407        DCE         豆一2407          a2407         a   \n", "1         a2409        DCE         豆一2409          a2409         a   \n", "2        ag2408       SHFE         ag2408         ag2408        ag   \n", "3        al2407       SHFE         al2407         al2407        al   \n", "4        al2408       SHFE         al2408         al2408        al   \n", "..          ...        ...            ...            ...       ...   \n", "73        UR409       CZCE           尿素9月          UR409        UR   \n", "74        v2409        DCE       聚氯乙烯2409          v2409         v   \n", "75        y2409        DCE         豆油2409          y2409         y   \n", "76       zn2407       SHFE         zn2407         zn2407        zn   \n", "77       zn2408       SHFE         zn2408         zn2408        zn   \n", "\n", "    ProductClass  DeliveryYear  DeliveryMonth  MaxMarketOrderVolume  \\\n", "0             49          2024              7                  1000   \n", "1             49          2024              9                  1000   \n", "2             49          2024              8                    30   \n", "3             49          2024              7                    30   \n", "4             49          2024              8                    30   \n", "..           ...           ...            ...                   ...   \n", "73            49          2024              9                   200   \n", "74            49          2024              9                  1000   \n", "75            49          2024              9                  1000   \n", "76            49          2024              7                    30   \n", "77            49          2024              8                    30   \n", "\n", "    MinMarketOrderVolume  ...  StrikePrice  OptionsType UnderlyingMultiple  \\\n", "0                      1  ...          0.0            0                0.0   \n", "1                      1  ...          0.0            0                0.0   \n", "2                      1  ...          0.0            0                1.0   \n", "3                      1  ...          0.0            0                1.0   \n", "4                      1  ...          0.0            0                1.0   \n", "..                   ...  ...          ...          ...                ...   \n", "73                     1  ...          0.0            0                1.0   \n", "74                     1  ...          0.0            0                0.0   \n", "75                     1  ...          0.0            0                0.0   \n", "76                     1  ...          0.0            0                1.0   \n", "77                     1  ...          0.0            0                1.0   \n", "\n", "    CombinationType HedgeFlag LongMarginRatioByMoney LongMarginRatioByVolume  \\\n", "0                48        49                   0.10                     0.0   \n", "1                48        49                   0.10                     0.0   \n", "2                48        49                   0.18                     0.0   \n", "3                48        49                   0.15                     0.0   \n", "4                48        49                   0.15                     0.0   \n", "..              ...       ...                    ...                     ...   \n", "73               48        49                   0.13                     0.0   \n", "74               48        49                   0.10                     0.0   \n", "75               48        49                   0.10                     0.0   \n", "76               48        49                   0.14                     0.0   \n", "77               48        49                   0.14                     0.0   \n", "\n", "   ShortMarginRatioByMoney ShortMarginRatioByVolume  IsRelative  \n", "0                     0.10                      0.0           0  \n", "1                     0.10                      0.0           0  \n", "2                     0.18                      0.0           0  \n", "3                     0.15                      0.0           0  \n", "4                     0.15                      0.0           0  \n", "..                     ...                      ...         ...  \n", "73                    0.13                      0.0           0  \n", "74                    0.10                      0.0           0  \n", "75                    0.10                      0.0           0  \n", "76                    0.14                      0.0           0  \n", "77                    0.14                      0.0           0  \n", "\n", "[78 rows x 37 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["df.to_csv(r\"C:\\Users\\<USER>\\Desktop\\secdata.csv\", index=False, encoding='GBK')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}