"""
测试CandlestickDataset和OptimizedCandlestickDataset的性能
"""

import os
import sys
import time
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import logging

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入自定义模块
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset
from pyqlab.models.gpt2.bak.optimized_candlestick_dataset import OptimizedCandlestickDataset
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_data(data_path, num_samples=None):
    """加载测试数据"""
    logger.info(f"加载测试数据: {data_path}")
    
    if data_path.endswith('.parquet'):
        df = pd.read_parquet(data_path)
    elif data_path.endswith('.csv'):
        df = pd.read_csv(data_path)
    else:
        raise ValueError(f"不支持的文件格式: {data_path}")
        
    # 确保数据按时间排序
    if 'datetime' in df.columns:
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime')
        
    # 如果指定了样本数量，截取部分数据
    if num_samples is not None and num_samples < len(df):
        df = df.iloc[:num_samples]
        
    # 按代码分组
    if 'code' in df.columns:
        grouped = df.groupby('code')
        data_list = [group for _, group in grouped]
        code_ids = list(range(len(data_list)))
    else:
        data_list = [df]
        code_ids = [0]
        
    logger.info(f"加载了 {len(data_list)} 个证券，总数据量: {sum(len(df) for df in data_list)}")
    
    return data_list, code_ids

def create_tokenizer(tokenizer_type='linear'):
    """创建Tokenizer"""
    logger.info(f"创建 {tokenizer_type} Tokenizer")
    
    if tokenizer_type == 'linear':
        tokenizer = CandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            volume_range=(-9, 9),
            special_tokens=True,
            include_volume=False,
            verbose=False
        )
    else:
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            volume_range=(-9, 9),
            mapping_type='logarithmic'
        )
        
    return tokenizer

def test_dataset_initialization(data_list, code_ids, tokenizer, seq_len=30, pred_len=1, stride=1):
    """测试数据集初始化性能"""
    logger.info("测试数据集初始化性能...")
    
    # 测试原始数据集
    start_time = time.time()
    original_dataset = CandlestickDataset(
        data=data_list,
        tokenizer=tokenizer,
        seq_len=seq_len,
        pred_len=pred_len,
        stride=stride,
        add_special_tokens=True,
        code_ids=code_ids,
        use_time_features=True
    )
    original_time = time.time() - start_time
    logger.info(f"原始数据集初始化时间: {original_time:.4f}秒，样本数: {len(original_dataset)}")
    
    # 测试优化数据集
    start_time = time.time()
    optimized_dataset = OptimizedCandlestickDataset(
        data=data_list,
        tokenizer=tokenizer,
        seq_len=seq_len,
        pred_len=pred_len,
        stride=stride,
        add_special_tokens=True,
        code_ids=code_ids,
        use_time_features=True
    )
    optimized_time = time.time() - start_time
    logger.info(f"优化数据集初始化时间: {optimized_time:.4f}秒，样本数: {len(optimized_dataset)}")
    
    return original_dataset, optimized_dataset, original_time, optimized_time

def test_dataset_iteration(original_dataset, optimized_dataset, num_iterations=1000):
    """测试数据集迭代性能"""
    logger.info(f"测试数据集迭代性能 (迭代 {num_iterations} 次)...")
    
    # 测试原始数据集
    start_time = time.time()
    for i in range(min(num_iterations, len(original_dataset))):
        sample = original_dataset[i]
    original_time = time.time() - start_time
    logger.info(f"原始数据集迭代时间: {original_time:.4f}秒")
    
    # 测试优化数据集
    start_time = time.time()
    for i in range(min(num_iterations, len(optimized_dataset))):
        sample = optimized_dataset[i]
    optimized_time = time.time() - start_time
    logger.info(f"优化数据集迭代时间: {optimized_time:.4f}秒")
    
    return original_time, optimized_time

def test_dataloader_performance(original_dataset, optimized_dataset, batch_size=32, num_workers=4):
    """测试DataLoader性能"""
    logger.info(f"测试DataLoader性能 (批大小: {batch_size}, 工作进程: {num_workers})...")
    
    # 创建DataLoader
    original_loader = DataLoader(
        original_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=num_workers > 0
    )
    
    optimized_loader = DataLoader(
        optimized_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=num_workers > 0
    )
    
    # 测试原始DataLoader
    start_time = time.time()
    for batch in original_loader:
        pass
    original_time = time.time() - start_time
    logger.info(f"原始DataLoader迭代时间: {original_time:.4f}秒")
    
    # 测试优化DataLoader
    start_time = time.time()
    for batch in optimized_loader:
        pass
    optimized_time = time.time() - start_time
    logger.info(f"优化DataLoader迭代时间: {optimized_time:.4f}秒")
    
    return original_time, optimized_time

def plot_performance_comparison(init_times, iter_times, loader_times):
    """绘制性能比较图表"""
    labels = ['初始化时间', '迭代时间', 'DataLoader时间']
    original_times = [init_times[0], iter_times[0], loader_times[0]]
    optimized_times = [init_times[1], iter_times[1], loader_times[1]]
    
    x = np.arange(len(labels))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(10, 6))
    rects1 = ax.bar(x - width/2, original_times, width, label='原始数据集')
    rects2 = ax.bar(x + width/2, optimized_times, width, label='优化数据集')
    
    ax.set_ylabel('时间 (秒)')
    ax.set_title('数据集性能比较')
    ax.set_xticks(x)
    ax.set_xticklabels(labels)
    ax.legend()
    
    # 添加数值标签
    def autolabel(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.2f}',
                        xy=(rect.get_x() + rect.get_width() / 2, height),
                        xytext=(0, 3),
                        textcoords="offset points",
                        ha='center', va='bottom')
    
    autolabel(rects1)
    autolabel(rects2)
    
    fig.tight_layout()
    plt.savefig('dataset_performance_comparison.png')
    logger.info("性能比较图表已保存为 dataset_performance_comparison.png")

def main():
    """主函数"""
    # 加载测试数据
    data_path = "data/futures/IC9999.SF_min5.parquet"
    if not os.path.exists(data_path):
        logger.warning(f"测试数据 {data_path} 不存在，使用随机生成的数据")
        # 生成随机测试数据
        dates = pd.date_range(start='2023-01-01', periods=10000, freq='5min')
        np.random.seed(42)
        
        # 生成随机价格数据
        close_prices = np.random.normal(loc=5000, scale=100, size=len(dates))
        close_prices = np.maximum(close_prices, 4800)
        close_prices = np.cumsum(np.random.normal(loc=0, scale=10, size=len(dates))) + close_prices
        
        # 生成其他价格数据
        open_prices = close_prices + np.random.normal(loc=0, scale=5, size=len(dates))
        high_prices = np.maximum(close_prices, open_prices) + np.random.normal(loc=5, scale=3, size=len(dates))
        low_prices = np.minimum(close_prices, open_prices) - np.random.normal(loc=5, scale=3, size=len(dates))
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'code': 'IC9999.SF',
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': np.random.randint(100, 1000, size=len(dates))
        })
        
        data_list = [df]
        code_ids = [0]
    else:
        data_list, code_ids = load_test_data(data_path)
    
    # 创建Tokenizer
    tokenizer = create_tokenizer('linear')
    
    # 测试数据集初始化性能
    original_dataset, optimized_dataset, init_original_time, init_optimized_time = test_dataset_initialization(
        data_list, code_ids, tokenizer
    )
    
    # 测试数据集迭代性能
    iter_original_time, iter_optimized_time = test_dataset_iteration(
        original_dataset, optimized_dataset
    )
    
    # 测试DataLoader性能
    loader_original_time, loader_optimized_time = test_dataloader_performance(
        original_dataset, optimized_dataset
    )
    
    # 绘制性能比较图表
    plot_performance_comparison(
        (init_original_time, init_optimized_time),
        (iter_original_time, iter_optimized_time),
        (loader_original_time, loader_optimized_time)
    )
    
    # 计算性能提升百分比
    init_improvement = (init_original_time - init_optimized_time) / init_original_time * 100
    iter_improvement = (iter_original_time - iter_optimized_time) / iter_original_time * 100
    loader_improvement = (loader_original_time - loader_optimized_time) / loader_original_time * 100
    
    logger.info(f"初始化性能提升: {init_improvement:.2f}%")
    logger.info(f"迭代性能提升: {iter_improvement:.2f}%")
    logger.info(f"DataLoader性能提升: {loader_improvement:.2f}%")
    
    # 验证两个数据集的输出是否一致
    logger.info("验证数据集输出...")
    original_sample = original_dataset[0]
    optimized_sample = optimized_dataset[0]
    
    # 检查键是否一致
    logger.info(f"原始数据集样本键: {list(original_sample.keys())}")
    logger.info(f"优化数据集样本键: {list(optimized_sample.keys())}")
    
    # 检查形状是否一致
    for key in original_sample:
        if key in optimized_sample:
            logger.info(f"键 '{key}' 形状: 原始={original_sample[key].shape}, 优化={optimized_sample[key].shape}")

if __name__ == "__main__":
    main()
