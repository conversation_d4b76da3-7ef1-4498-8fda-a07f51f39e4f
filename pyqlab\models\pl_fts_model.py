import inspect
import logging
import os
from typing import Any, Dict, List, Optional, Tuple, Union

import torch
import importlib
from torch.nn import functional as F
import torch.optim.lr_scheduler as lrs
from torch.optim.lr_scheduler import ReduceLROnPlateau
import pytorch_lightning as pl
from torch.optim import Adam, AdamW, SGD, RMSprop
from torchmetrics.functional import accuracy, f1_score, precision, recall
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint

# 设置日志
logger = logging.getLogger(__name__)

class PLFtsModel(pl.LightningModule):
    def __init__(
        self, 
        model_name: str, 
        loss: str, 
        lr: float, 
        model_path: str = "pyqlab.models",
        optimizer: str = "adam",
        lr_scheduler: Optional[str] = None,
        weight_decay: float = 0.0,
        num_classes: int = 3,
        **kargs
    ):
        """
        初始化PLFtsModel
        
        Args:
            model_name: 模型名称
            loss: 损失函数类型
            lr: 学习率
            model_path: 模型路径
            optimizer: 优化器类型
            lr_scheduler: 学习率调度器类型
            weight_decay: 权重衰减
            num_classes: 分类数量
            **kargs: 其他参数
        """
        super().__init__()
        self.kargs = kargs
        self.save_hyperparameters()
        self.num_classes = num_classes
        self.load_model()
        self.configure_loss()
        
        # 设置日志级别
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def forward(self, *args, **kwargs) -> torch.Tensor:
        embeds, inputs = args
        return self.model(embeds, inputs)

    def training_step(self, batch: List[torch.Tensor], batch_idx: int) -> Dict[str, torch.Tensor]:
        embeds, inputs, targets = batch
        outputs = self(embeds, inputs)
        loss = self.loss_function(outputs, targets)

        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        
        # 记录其他指标
        metrics = self._calculate_metrics(outputs, targets)
        for name, value in metrics.items():
            self.log(f'train_{name}', value, on_step=True, on_epoch=True, prog_bar=True)
        
        metrics['loss'] = loss
        return metrics

    def validation_step(self, batch: List[torch.Tensor], batch_idx: int) -> Dict[str, torch.Tensor]:
        embeds, inputs, targets = batch
        outputs = self(embeds, inputs)
        loss = self.loss_function(outputs, targets)
        
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        
        # 记录其他指标
        metrics = self._calculate_metrics(outputs, targets)
        for name, value in metrics.items():
            self.log(f'val_{name}', value, on_step=False, on_epoch=True, prog_bar=True)
        
        metrics['loss'] = loss
        return metrics

    def test_step(self, batch: List[torch.Tensor], batch_idx: int) -> Dict[str, torch.Tensor]:
        embeds, inputs, targets = batch
        outputs = self(embeds, inputs)
        loss = self.loss_function(outputs, targets)
        
        self.log('test_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        
        # 记录其他指标
        metrics = self._calculate_metrics(outputs, targets)
        for name, value in metrics.items():
            self.log(f'test_{name}', value, on_step=False, on_epoch=True, prog_bar=True)
        
        metrics['loss'] = loss
        return metrics
    
    def _calculate_metrics(self, outputs: torch.Tensor, targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算各种评估指标"""
        metrics = {}
        
        if self.is_classification:
            preds = torch.argmax(outputs, dim=1)
            metrics['acc'] = accuracy(preds, targets, task='multiclass', num_classes=self.num_classes)
            
            # 添加更多分类指标
            metrics['f1'] = f1_score(preds, targets, task='multiclass', num_classes=self.num_classes)
            metrics['precision'] = precision(preds, targets, task='multiclass', num_classes=self.num_classes)
            metrics['recall'] = recall(preds, targets, task='multiclass', num_classes=self.num_classes)
        
        return metrics

    def configure_optimizers(self) -> Union[torch.optim.Optimizer, Dict[str, Any]]:
        """配置优化器和学习率调度器"""
        weight_decay = self.hparams.weight_decay
        
        # 扩展优化器选项
        if self.hparams.optimizer.lower() == 'adamw':
            optimizer = AdamW(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)
        elif self.hparams.optimizer.lower() == 'adam':
            optimizer = Adam(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)
        elif self.hparams.optimizer.lower() == 'sgd':
            optimizer = SGD(self.parameters(), lr=self.hparams.lr, momentum=0.9, weight_decay=weight_decay)
        elif self.hparams.optimizer.lower() == 'rmsprop':
            optimizer = RMSprop(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)
        else:
            logger.warning(f"未知的优化器类型: {self.hparams.optimizer}，使用默认的Adam")
            optimizer = Adam(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)

        if self.hparams.lr_scheduler is None:
            return optimizer
        else:
            if self.hparams.lr_scheduler == 'step':
                scheduler = lrs.StepLR(optimizer,
                                       step_size=self.hparams.lr_decay_steps,
                                       gamma=self.hparams.lr_decay_rate)
                return [optimizer], [scheduler]
            elif self.hparams.lr_scheduler == 'cosine':
                scheduler = lrs.CosineAnnealingLR(optimizer,
                                                  T_max=self.hparams.lr_decay_steps,
                                                  eta_min=self.hparams.lr_decay_min_lr)
                return [optimizer], [scheduler]
            elif self.hparams.lr_scheduler == 'plateau':
                scheduler = lrs.ReduceLROnPlateau(optimizer,
                                              mode='min',
                                              factor=self.hparams.lr_decay_rate,
                                              patience=self.hparams.lr_decay_steps,
                                              min_lr=self.hparams.lr_decay_min_lr,
                                              verbose=True)
                return [optimizer], [scheduler]
            elif self.hparams.lr_scheduler == 'reduce_on_plateau':
                return {
                    "optimizer": optimizer,
                    "lr_scheduler": {
                        "scheduler": ReduceLROnPlateau(optimizer,
                                              mode='min',
                                              factor=self.hparams.lr_decay_rate,
                                              patience=self.hparams.lr_decay_steps,
                                              min_lr=self.hparams.lr_decay_min_lr,
                                              verbose=True),
                        "interval": "epoch",
                        "monitor": "val_loss",
                    }
                }
            elif self.hparams.lr_scheduler == 'one_cycle':
                scheduler = lrs.OneCycleLR(
                    optimizer,
                    max_lr=self.hparams.lr,
                    total_steps=self.hparams.lr_total_steps,
                    pct_start=0.3,
                    div_factor=25.0,
                    final_div_factor=10000.0
                )
                return [optimizer], [scheduler]
            else:
                raise ValueError(f'无效的学习率调度器类型: {self.hparams.lr_scheduler}!')

    def configure_loss(self) -> None:
        """配置损失函数"""
        loss = self.hparams.loss.lower()
        self.is_classification = False
        
        if loss == 'mse':
            self.loss_function = F.mse_loss
        elif loss == 'l1':
            self.loss_function = F.l1_loss
        elif loss == 'smooth_l1':
            self.loss_function = F.smooth_l1_loss
        elif loss == 'huber':
            self.loss_function = F.huber_loss
        elif loss == 'bce':
            self.loss_function = F.binary_cross_entropy
            self.is_classification = True
        elif loss == 'bce_with_logits':
            self.loss_function = F.binary_cross_entropy_with_logits
            self.is_classification = True
        elif loss == 'ce':
            self.loss_function = F.cross_entropy
            self.is_classification = True
        elif loss == 'nll':
            self.loss_function = F.nll_loss
            self.is_classification = True
        else:
            raise ValueError(f"无效的损失函数类型: {loss}!")
        
        logger.info(f"使用损失函数: {loss}, 是否为分类任务: {self.is_classification}")

    def configure_model(self) -> None:
        """配置模型，使用torch.compile进行优化"""
        try:
            self = torch.compile(self, backend='inductor')
            # logger.info("模型已使用torch.compile优化")
        except Exception as e:
            logger.warning(f"模型编译失败: {e}")
                
    def load_model(self) -> None:
        """加载模型"""
        name = self.hparams.model_name
        # 将`snake_case.py`文件名转换为`CamelCase`类名
        # 请始终将模型文件名命名为`snake_case.py`，类名对应为`CamelCase`
        camel_name = ''.join([i.capitalize() for i in name.split('_')])
        mod_path = self.hparams.model_path + '.' + name
        try:
            logger.info(f"正在加载模型 {mod_path}.{camel_name}")
            Model = getattr(importlib.import_module(
                mod_path, package=__package__), camel_name)
            logger.info(f"模型 {mod_path}.{camel_name} 加载成功!")
        except Exception as e:
            error_msg = f'无效的模块文件名或类名 {name}.{camel_name}! 错误: {e}'
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        self.model = self.instancialize(Model)

    def instancialize(self, Model: Any, **other_args) -> Any:
        """ 使用self.hparams字典中的相应参数实例化模型
            您还可以输入任何args来覆盖self.hparams中的相应值
        """
        class_args = inspect.getfullargspec(Model.__init__).args[1:]
        inkeys = self.hparams.keys()
        args1 = {}
        for arg in class_args:
            if arg in inkeys:
                args1[arg] = getattr(self.hparams, arg)
            if arg in self.kargs.keys():
                args1[arg] = self.kargs[arg]
        args1.update(other_args)
        return Model(**args1)
    
    def save_model_weights(self, path: str) -> None:
        """保存模型权重"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(self.model.state_dict(), path)
        logger.info(f"模型权重已保存到 {path}")
    
    def load_model_weights(self, path: str) -> None:
        """加载模型权重"""
        if os.path.exists(path):
            self.model.load_state_dict(torch.load(path))
            logger.info(f"模型权重已从 {path} 加载")
        else:
            logger.error(f"权重文件 {path} 不存在")
            raise FileNotFoundError(f"权重文件 {path} 不存在")
    
    @staticmethod
    def get_callbacks(
        monitor: str = 'val_loss', 
        mode: str = 'min', 
        patience: int = 10,
        save_top_k: int = 3,
        checkpoint_dir: str = 'checkpoints'
    ) -> List[pl.Callback]:
        """获取常用回调函数，如早停和模型检查点"""
        callbacks = []
        
        # 早停回调
        early_stop_callback = EarlyStopping(
            monitor=monitor,
            min_delta=0.00,
            patience=patience,
            verbose=True,
            mode=mode
        )
        callbacks.append(early_stop_callback)
        
        # 模型检查点回调
        os.makedirs(checkpoint_dir, exist_ok=True)
        checkpoint_callback = ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename='{epoch:02d}-{' + monitor + ':.4f}',
            save_top_k=save_top_k,
            verbose=True,
            monitor=monitor,
            mode=mode
        )
        callbacks.append(checkpoint_callback)
        
        return callbacks

