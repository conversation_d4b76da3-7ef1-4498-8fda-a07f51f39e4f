{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# from pyecharts import online\n", "import datetime\n", "import talib as ta\n", "import pandas as pd\n", "# from pyecharts import Kline\n", "# online()\n", "import sys\n", "sys.path.append(\"d:/QuantLab\")\n", "# sys.path.append(\"e:/Lab/RoboQuant/bin/x64/Debug\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d:\\QuantLab\n", "7534\n"]}], "source": ["print(ds.get_run_dir())\n", "print(ds.get_sec_num())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['IC2210.SF', 'IF2210.SF', 'IH2210.SF', 'IM2210.SF', 'AP2301.ZC', 'CF2301.ZC', 'CJ2301.ZC', 'CY2301.ZC', 'FG2301.ZC', 'MA2301.ZC', 'OI2301.ZC', 'PF2212.ZC', 'PK2301.ZC', 'RM2301.ZC', 'SA2301.ZC', 'SF2301.ZC', 'SM2301.ZC', 'SR2301.ZC', 'TA2301.ZC', 'UR2301.ZC', 'A2301.DC', 'B2211.DC', 'C2301.DC', 'CS2211.DC', 'EB2211.DC', 'EG2301.DC', 'I2301.DC', 'J2301.DC', 'JD2301.DC', 'JM2301.DC', 'L2301.DC', 'LH2301.DC', 'M2301.DC', 'P2301.DC', 'PG2211.DC', 'PP2301.DC', 'RR2212.DC', 'V2301.DC', 'Y2301.DC', 'AG2212.SC', 'AL2211.SC', 'AU2212.SC', 'BU2212.SC', 'CU2211.SC', 'HC2301.SC', 'NI2211.SC', 'NR2212.SC', 'PB2211.SC', 'RB2301.SC', 'RU2301.SC', 'SC2212.SC', 'SN2212.SC', 'SP2301.SC', 'SS2211.SC', 'ZN2211.SC']\n"]}], "source": ["print(ds.get_block_data('ZLQH'))"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["len=120\n", "symbol='MA2301.ZC'\n", "barsize=BarSize.min5\n", "hist=ds.get_history_data(symbol, len, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["hist=ds.get_bar_series(symbol, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[2663. 2663. 2663. 2663.]\n", " [2663. 2667. 2661. 2665.]\n", " [2666. 2666. 2662. 2665.]\n", " [2664. 2665. 2659. 2665.]\n", " [2664. 2672. 2664. 2667.]\n", " [2667. 2672. 2665. 2665.]\n", " [2665. 2668. 2661. 2668.]\n", " [2668. 2671. 2665. 2669.]\n", " [2669. 2678. 2669. 2676.]\n", " [2677. 2680. 2672. 2676.]\n", " [2677. 2677. 2673. 2676.]\n", " [2676. 2676. 2670. 2675.]\n", " [2675. 2676. 2668. 2670.]\n", " [2671. 2672. 2663. 2664.]\n", " [2664. 2667. 2663. 2666.]\n", " [2666. 2668. 2659. 2660.]\n", " [2660. 2665. 2658. 2665.]\n", " [2665. 2666. 2659. 2663.]\n", " [2663. 2664. 2657. 2660.]\n", " [2659. 2659. 2655. 2657.]\n", " [2657. 2658. 2645. 2651.]\n", " [2650. 2653. 2644. 2644.]\n", " [2644. 2649. 2644. 2647.]\n", " [2646. 2649. 2645. 2648.]\n", " [2648. 2653. 2647. 2649.]\n", " [2649. 2655. 2649. 2652.]\n", " [2652. 2657. 2651. 2654.]\n", " [2654. 2655. 2652. 2655.]\n", " [2655. 2656. 2648. 2649.]\n", " [2649. 2650. 2639. 2643.]\n", " [2643. 2647. 2642. 2643.]\n", " [2644. 2652. 2643. 2652.]\n", " [2652. 2654. 2648. 2652.]\n", " [2652. 2656. 2650. 2651.]\n", " [2651. 2662. 2649. 2659.]\n", " [2659. 2665. 2659. 2664.]\n", " [2664. 2666. 2660. 2663.]\n", " [2663. 2670. 2662. 2669.]\n", " [2669. 2670. 2666. 2669.]\n", " [2670. 2670. 2665. 2665.]\n", " [2665. 2669. 2665. 2665.]\n", " [2666. 2672. 2665. 2670.]\n", " [2670. 2670. 2662. 2664.]\n", " [2665. 2666. 2659. 2661.]\n", " [2660. 2662. 2658. 2661.]\n", " [2661. 2665. 2658. 2663.]\n", " [2662. 2665. 2658. 2660.]\n", " [2660. 2661. 2656. 2657.]\n", " [2657. 2662. 2656. 2659.]\n", " [2659. 2661. 2655. 2660.]\n", " [2661. 2666. 2659. 2663.]\n", " [2662. 2663. 2652. 2656.]\n", " [2656. 2661. 2655. 2659.]\n", " [2669. 2687. 2666. 2684.]\n", " [2684. 2685. 2674. 2678.]\n", " [2678. 2679. 2673. 2678.]\n", " [2679. 2680. 2672. 2672.]\n", " [2672. 2678. 2671. 2678.]\n", " [2678. 2680. 2671. 2673.]\n", " [2672. 2673. 2666. 2667.]\n", " [2666. 2674. 2662. 2670.]\n", " [2669. 2671. 2666. 2668.]\n", " [2667. 2669. 2655. 2658.]\n", " [2659. 2662. 2656. 2660.]\n", " [2660. 2666. 2659. 2665.]\n", " [2664. 2668. 2657. 2659.]\n", " [2658. 2663. 2653. 2656.]\n", " [2657. 2659. 2646. 2652.]\n", " [2651. 2652. 2643. 2643.]\n", " [2644. 2649. 2641. 2646.]\n", " [2647. 2651. 2644. 2650.]\n", " [2650. 2656. 2650. 2654.]\n", " [2654. 2655. 2648. 2652.]\n", " [2652. 2658. 2650. 2653.]\n", " [2653. 2659. 2652. 2656.]\n", " [2656. 2658. 2648. 2649.]\n", " [2649. 2650. 2636. 2639.]\n", " [2639. 2645. 2638. 2644.]\n", " [2643. 2653. 2641. 2653.]\n", " [2652. 2654. 2647. 2648.]\n", " [2647. 2654. 2642. 2653.]\n", " [2653. 2655. 2649. 2651.]\n", " [2651. 2654. 2645. 2650.]\n", " [2650. 2663. 2648. 2659.]\n", " [2659. 2659. 2643. 2646.]\n", " [2646. 2647. 2641. 2641.]\n", " [2641. 2646. 2639. 2640.]\n", " [2641. 2644. 2634. 2639.]\n", " [2639. 2643. 2638. 2640.]\n", " [2640. 2645. 2639. 2642.]\n", " [2642. 2650. 2642. 2649.]\n", " [2648. 2649. 2642. 2646.]\n", " [2646. 2648. 2644. 2645.]\n", " [2645. 2650. 2644. 2647.]\n", " [2646. 2650. 2644. 2648.]\n", " [2647. 2648. 2643. 2645.]\n", " [2644. 2648. 2641. 2643.]\n", " [2643. 2645. 2600. 2608.]\n", " [2609. 2618. 2604. 2618.]\n", " [2618. 2624. 2615. 2623.]\n", " [2621. 2627. 2618. 2624.]\n", " [2625. 2627. 2620. 2627.]\n", " [2626. 2627. 2621. 2625.]\n", " [2625. 2633. 2620. 2631.]\n", " [2631. 2637. 2628. 2628.]\n", " [2628. 2630. 2617. 2620.]\n", " [2620. 2624. 2617. 2620.]\n", " [2619. 2623. 2618. 2620.]\n", " [2620. 2626. 2619. 2625.]\n", " [2624. 2626. 2619. 2624.]\n", " [2623. 2627. 2621. 2625.]\n", " [2626. 2631. 2624. 2630.]\n", " [2630. 2635. 2627. 2628.]\n", " [2628. 2630. 2620. 2622.]\n", " [2622. 2624. 2621. 2621.]\n", " [2622. 2632. 2620. 2630.]\n", " [2630. 2633. 2629. 2629.]\n", " [2628. 2632. 2625. 2626.]\n", " [2626. 2626. 2622. 2625.]\n", " [2624. 2627. 2623. 2625.]]\n"]}], "source": ["print(hist[:, 1:5])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["dt_str = []\n", "len=hist.shape[1]\n", "if len == 0:\n", "    print(len)\n", "\n", "for i in range(len):\n", "    dt_str.append(datetime.datetime.fromtimestamp(int(hist[0][i])).strftime('%Y-%m-%d'))\n", "\n", "bars = []\n", "for i in range(len):\n", "    bar = []\n", "    bar.append(hist[1][i])\n", "    bar.append(hist[4][i])\n", "    bar.append(hist[3][i])\n", "    bar.append(hist[2][i])\n", "    bars.append(bar)\n", "    \n", "atr = ta.ATR(hist[:,2], hist[:,3], hist[:,4], 20)\n", "\n", "# kline = Kline(\"K 线图\", width=1000, height=400)\n", "# kline.add(\"{} {} KLine (bar count:{})(ATR:{})\".format(symbol, barsize, len, atr[-1]), dt_str, bars, mark_point=[\"max\"], is_datazoom_show=True)\n", "# kline.show_config()\n", "# kline"]}, {"cell_type": "code", "execution_count": 173, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from mpl_finance import candlestick_ohlc\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import numpy as np\n", "# %matplotlib inline\n", "mpl.rcParams['font.sans-serif'] = ['SimHei'] # 指定默认字体\n", "mpl.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def plot_kline(symbol, hist_data, plt_volume=False):\n", "\n", "    fig = plt.figure(figsize=(12,10))\n", "    grid = plt.GridSpec(12, 10, wspace=0.5, hspace=0.5)\n", "\n", "    #（1）绘制K线图\n", "    # K线数据\n", "    # ohlc = hist_data[['Date','open_price','high_price','low_price','close_price']]\n", "    ohlc = hist_data.copy()\n", "    ohlc[:,0:1] = np.array([i for i in range(ohlc.shape[0])]).reshape(ohlc.shape[0], 1)     # 重新赋值横轴数据，绘制K线图无间隔\n", "    # 绘制K线\n", "    ax1 = fig.add_subplot(grid[0:8,0:12])   # 设置K线图的尺寸\n", "    candlestick_ohlc(ax1, ohlc, width=.7, colorup='red', colordown='green')\n", "#     plt.title(symbol,fontsize = 14)     # 设置图片标题\n", "#     plt.ylabel('价 格',fontsize = 14)   # 设置纵轴标题\n", "#     ax1.set_xticks([])                      # 日期标注在成交量中，故清空此处x轴刻度\n", "#     ax1.set_xticklabels([])                 # 日期标注在成交量中，故清空此处x轴 \n", "    # ax1.xaxis.set_major_formatter(mdates.num2date) # 设置横轴日期格式\n", "    plt.xticks(rotation=30)                                        # 日期显示的旋转角度\n", "    plt.title(symbol,fontsize = 14)                            # 设置图片标题\n", "    plt.xlabel('日 期',fontsize = 14)                               # 设置横轴标题\n", "    plt.ylabel('价 格',fontsize = 14)                          # 设置纵轴标题\n", "    # 修改横轴标注日期\n", "    date_list = hist_data[:, 0]           # 获取日期列表\n", "    print(ax1.get_xticks().shape[0]-1)\n", "    xticks_len = round(date_list.shape[0]/(ax1.get_xticks().shape[0]-1))    # 获取默认横轴标注的间隔\n", "    xticks_num = range(0,date_list.shape[0],xticks_len)                # 生成横轴标注位置列表\n", "    xticks_str = list(map(lambda x:datetime.fromtimestamp(date_list[int(x)]).strftime(\"%Y%m%d %H:%M\"),xticks_num))  # 生成正在标注日期列表\n", "    ax1.set_xticks(xticks_num)                                      # 设置横轴标注位置\n", "    ax1.set_xticklabels(xticks_str)                                 # 设置横轴标注日期\n", "    plt.show()\n", "    '''\n", "    #（2）绘制成交量\n", "    # 成交量数据\n", "    volume_list = hist_data[['Date','close_price','open_price','business_amount']]\n", "    # color_list = data_volume.apply(lambda row: 1 if row['close_price'] >= row['open_price'] else 0, axis=1)        # 计算成交量柱状图对应的颜色，使之与K线颜色一致\n", "    date_list = hist_data[:, 0]\n", "    # 绘制成交量\n", "    ax2 = fig.add_subplot(grid[8:10,0:12])  # 设置成交量图形尺寸\n", "    ax2.bar(data_volume.query('color==1')['Date']\n", "            , data_volume.query('color==1')['business_amount']\n", "            , color='r')                    # 绘制红色柱状图\n", "    ax2.bar(data_volume.query('color==0')['Date']\n", "            , data_volume.query('color==0')['business_amount']\n", "            , color='g')                    # 绘制绿色柱状图\n", "    plt.xticks(rotation=30) \n", "    plt.xlabel('日 期',fontsize = 14)                               # 设置横轴标题\n", "\n", "    # 修改横轴日期标注\n", "    date_list = ohlc.index.tolist()           # 获取日期列表\n", "    xticks_len = round(len(date_list)/(len(ax2.get_xticks())-1))      # 获取默认横轴标注的间隔\n", "    xticks_num = range(0,len(date_list),xticks_len)                   # 生成横轴标注位置列表\n", "    xticks_str = list(map(lambda x:date_list[int(x)],xticks_num))     # 生成正在标注日期列表\n", "    ax2.set_xticks(xticks_num)                                        # 设置横轴标注位置\n", "    ax2.set_xticklabels(xticks_str)                                   # 设置横轴标注日期\n", "    plt.show()'''"]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_kline(symbol, hist)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}