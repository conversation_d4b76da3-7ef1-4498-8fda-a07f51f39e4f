"""
KlineLLM - 基于大型语言模型的K线预测模型
该模型将K线数据转换为tokens，并使用LLM进行预测
"""

import torch
import torch.nn as nn
from torch.nn import functional as F
from transformers import (
    PreTrainedModel, 
    PreTrainedTokenizer,
    AutoModelForCausalLM, 
    AutoTokenizer,
    GPT2Config,
    GPT2Model,
    GPT2Tokenizer,
    LlamaConfig,
    LlamaModel,
    LlamaTokenizer
)
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import logging

class KlineTokenizer:
    """
    K线数据的tokenizer
    将K线数据转换为LLM可以处理的tokens
    """
    def __init__(self, vocab_size: int = 40000, special_tokens: List[str] = None):
        """
        初始化K线tokenizer
        
        Args:
            vocab_size: 词汇表大小
            special_tokens: 特殊token列表，如交易日间隔token等
        """
        self.vocab_size = vocab_size
        self.bar_set = {}  # 存储bar字符串到token id的映射
        self.id_to_bar = {}  # 存储token id到bar字符串的映射
        self.next_id = 0
        
        # 添加特殊tokens
        self.special_tokens = special_tokens or ["<PAD>", "<UNK>", "<SEP>", "<DAY_SEP>"]
        for token in self.special_tokens:
            self.add_special_token(token)
            
    def add_special_token(self, token: str) -> int:
        """添加特殊token"""
        if token not in self.bar_set:
            self.bar_set[token] = self.next_id
            self.id_to_bar[self.next_id] = token
            self.next_id += 1
        return self.bar_set[token]
    
    def get_vocab(self) -> Dict[str, int]:
        """获取词汇表"""
        return self.bar_set
    
    def get_vocab_size(self) -> int:
        """获取词汇表大小"""
        return len(self.bar_set)
    
    def encode_bar(self, change: int, entity: int, upline: int, downline: int) -> int:
        """
        将K线bar编码为token id
        
        Args:
            change: 涨跌幅
            entity: 实体
            upline: 上影线
            downline: 下影线
            
        Returns:
            token id
        """
        # 限制值范围
        change = max(-12, min(12, change))
        entity = max(-12, min(12, entity))
        upline = max(0, min(7, upline))
        downline = max(0, min(7, downline))
        
        # 创建bar字符串
        bar_str = f"{change}|{entity}|{upline}|{downline}"
        
        # 如果bar不在词汇表中，添加它
        if bar_str not in self.bar_set:
            if len(self.bar_set) >= self.vocab_size:
                return self.bar_set["<UNK>"]
            self.bar_set[bar_str] = self.next_id
            self.id_to_bar[self.next_id] = bar_str
            self.next_id += 1
            
        return self.bar_set[bar_str]
    
    def decode_bar(self, token_id: int) -> Tuple[int, int, int, int]:
        """
        将token id解码为K线bar
        
        Args:
            token_id: token id
            
        Returns:
            (change, entity, upline, downline)
        """
        if token_id not in self.id_to_bar:
            return (0, 0, 0, 0)  # 默认值
            
        bar_str = self.id_to_bar[token_id]
        
        # 处理特殊token
        if bar_str in self.special_tokens:
            return (0, 0, 0, 0)
            
        # 解析bar字符串
        try:
            change, entity, upline, downline = map(int, bar_str.split('|'))
            return (change, entity, upline, downline)
        except:
            return (0, 0, 0, 0)
    
    def __call__(
        self, 
        text: Union[str, List[str]], 
        padding: str = None,
        truncation: bool = False,
        max_length: int = None,
        return_tensors: str = None
    ) -> Dict[str, torch.Tensor]:
        """
        将文本转换为tokens
        
        Args:
            text: 输入文本或文本列表
            padding: 填充策略
            truncation: 是否截断
            max_length: 最大长度
            return_tensors: 返回的tensor类型
            
        Returns:
            包含input_ids和attention_mask的字典
        """
        if isinstance(text, str):
            text = [text]
            
        batch_input_ids = []
        
        for t in text:
            # 分割文本并转换为tokens
            tokens = []
            for item in t.split():
                if item in self.bar_set:
                    tokens.append(self.bar_set[item])
                else:
                    try:
                        # 尝试解析为bar格式
                        parts = item.split('|')
                        if len(parts) == 4:
                            change, entity, upline, downline = map(int, parts)
                            token_id = self.encode_bar(change, entity, upline, downline)
                            tokens.append(token_id)
                        else:
                            tokens.append(self.bar_set["<UNK>"])
                    except:
                        tokens.append(self.bar_set["<UNK>"])
            
            batch_input_ids.append(tokens)
        
        # 处理padding和truncation
        if truncation and max_length:
            batch_input_ids = [ids[:max_length] for ids in batch_input_ids]
            
        if padding:
            max_len = max(len(ids) for ids in batch_input_ids)
            if max_length:
                max_len = min(max_len, max_length)
                
            pad_id = self.bar_set["<PAD>"]
            
            if padding == "max_length" and max_length:
                batch_input_ids = [ids + [pad_id] * (max_length - len(ids)) for ids in batch_input_ids]
                attention_mask = [[1] * len(ids) + [0] * (max_length - len(ids)) for ids in batch_input_ids]
            else:  # padding to longest sequence
                batch_input_ids = [ids + [pad_id] * (max_len - len(ids)) for ids in batch_input_ids]
                attention_mask = [[1] * len(ids) + [0] * (max_len - len(ids)) for ids in batch_input_ids]
        else:
            attention_mask = [[1] * len(ids) for ids in batch_input_ids]
        
        # 转换为tensors
        if return_tensors == "pt":
            return {
                "input_ids": torch.tensor(batch_input_ids),
                "attention_mask": torch.tensor(attention_mask)
            }
        
        return {
            "input_ids": batch_input_ids,
            "attention_mask": attention_mask
        }
    
    def save_pretrained(self, save_directory: str):
        """保存tokenizer"""
        import os
        import json
        
        os.makedirs(save_directory, exist_ok=True)
        
        # 保存词汇表
        vocab_file = os.path.join(save_directory, "vocab.json")
        with open(vocab_file, "w", encoding="utf-8") as f:
            json.dump(self.bar_set, f, ensure_ascii=False, indent=2)
            
        # 保存配置
        config_file = os.path.join(save_directory, "tokenizer_config.json")
        with open(config_file, "w", encoding="utf-8") as f:
            config = {
                "vocab_size": self.vocab_size,
                "special_tokens": self.special_tokens
            }
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    @classmethod
    def from_pretrained(cls, directory: str) -> "KlineTokenizer":
        """从保存的目录加载tokenizer"""
        import os
        import json
        
        # 加载配置
        config_file = os.path.join(directory, "tokenizer_config.json")
        with open(config_file, "r", encoding="utf-8") as f:
            config = json.load(f)
            
        # 创建tokenizer
        tokenizer = cls(
            vocab_size=config.get("vocab_size", 40000),
            special_tokens=config.get("special_tokens", ["<PAD>", "<UNK>", "<SEP>", "<DAY_SEP>"])
        )
        
        # 加载词汇表
        vocab_file = os.path.join(directory, "vocab.json")
        with open(vocab_file, "r", encoding="utf-8") as f:
            bar_set = json.load(f)
            
        # 更新tokenizer
        tokenizer.bar_set = bar_set
        tokenizer.id_to_bar = {v: k for k, v in bar_set.items()}
        tokenizer.next_id = max(tokenizer.id_to_bar.keys()) + 1 if tokenizer.id_to_bar else len(tokenizer.special_tokens)
        
        return tokenizer


class KlineLLM(nn.Module):
    """
    K线预测模型
    基于大型语言模型，将K线数据转换为tokens进行预测
    """
    def __init__(
        self, 
        model_name: str = "gpt2", 
        tokenizer: Optional[KlineTokenizer] = None,
        pretrained: bool = True,
        config: Optional[Dict] = None
    ):
        """
        初始化K线LLM模型
        
        Args:
            model_name: 底层LLM模型名称，支持"gpt2", "llama"等
            tokenizer: K线tokenizer
            pretrained: 是否使用预训练模型
            config: 模型配置
        """
        super().__init__()
        self.model_name = model_name
        self.tokenizer = tokenizer or KlineTokenizer()
        
        # 初始化底层LLM模型
        if model_name.lower() == "gpt2":
            if pretrained:
                self.model = AutoModelForCausalLM.from_pretrained("openai-community/gpt2")
            else:
                model_config = GPT2Config(**(config or {}))
                self.model = AutoModelForCausalLM.from_config(model_config)
        elif "llama" in model_name.lower():
            if pretrained:
                self.model = AutoModelForCausalLM.from_pretrained("huggyllama/llama-7b")
            else:
                model_config = LlamaConfig(**(config or {}))
                self.model = AutoModelForCausalLM.from_config(model_config)
        else:
            # 默认使用自动加载
            if pretrained:
                self.model = AutoModelForCausalLM.from_pretrained(model_name)
            else:
                raise ValueError(f"不支持的模型类型: {model_name}")
        
        # 调整模型的词汇表大小以匹配K线tokenizer
        vocab_size = self.tokenizer.get_vocab_size()
        self.model.resize_token_embeddings(vocab_size)
        
        # 添加K线特定的嵌入层
        self.kline_embeddings = nn.Embedding(vocab_size, self.model.config.hidden_size)
        
        # 添加时间特征嵌入层
        self.time_feature_dim = 4  # 时间特征维度，如小时、星期、月份、季度等
        self.time_embeddings = nn.Linear(self.time_feature_dim, self.model.config.hidden_size)
        
        # 添加证券代码嵌入层
        self.code_embedding_dim = 100  # 证券代码嵌入维度
        self.code_embeddings = nn.Embedding(10000, self.code_embedding_dim)  # 假设最多10000个证券代码
        self.code_projection = nn.Linear(self.code_embedding_dim, self.model.config.hidden_size)
        
        # 输出投影层
        self.output_projection = nn.Linear(self.model.config.hidden_size, 4)  # 预测4个值: change, entity, upline, downline
        
    def forward(
        self, 
        input_ids: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None,
        time_features: Optional[torch.Tensor] = None,
        security_codes: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            input_ids: 输入token ids, shape (batch_size, seq_len)
            attention_mask: 注意力掩码, shape (batch_size, seq_len)
            time_features: 时间特征, shape (batch_size, seq_len, time_feature_dim)
            security_codes: 证券代码, shape (batch_size,)
            labels: 标签, shape (batch_size, seq_len)
            
        Returns:
            包含loss和logits的字典
        """
        batch_size, seq_len = input_ids.shape
        
        # 获取K线嵌入
        kline_embeds = self.kline_embeddings(input_ids)
        
        # 添加时间特征嵌入
        if time_features is not None:
            time_embeds = self.time_embeddings(time_features)
            kline_embeds = kline_embeds + time_embeds
        
        # 添加证券代码嵌入
        if security_codes is not None:
            code_embeds = self.code_embeddings(security_codes)
            code_embeds = self.code_projection(code_embeds).unsqueeze(1).expand(-1, seq_len, -1)
            kline_embeds = kline_embeds + code_embeds
        
        # 使用底层LLM模型
        outputs = self.model(
            inputs_embeds=kline_embeds,
            attention_mask=attention_mask,
            labels=labels,
            return_dict=True
        )
        
        # 获取最后一层的隐藏状态
        hidden_states = outputs.hidden_states[-1] if hasattr(outputs, 'hidden_states') else outputs.last_hidden_state
        
        # 预测K线值
        kline_predictions = self.output_projection(hidden_states)
        
        # 计算损失
        loss = outputs.loss if labels is not None else None
        
        return {
            "loss": loss,
            "logits": outputs.logits,
            "kline_predictions": kline_predictions,
            "hidden_states": hidden_states
        }
    
    def predict(
        self, 
        input_sequence: torch.Tensor, 
        time_features: Optional[torch.Tensor] = None,
        security_code: Optional[int] = None,
        prediction_length: int = 5,
        temperature: float = 1.0,
        top_k: int = 50
    ) -> np.ndarray:
        """
        预测未来的K线数据
        
        Args:
            input_sequence: 输入K线序列, shape (seq_len, 4) 或 (batch_size, seq_len, 4)
            time_features: 时间特征, shape (seq_len, time_feature_dim) 或 (batch_size, seq_len, time_feature_dim)
            security_code: 证券代码
            prediction_length: 预测长度
            temperature: 采样温度
            top_k: top-k采样
            
        Returns:
            预测的K线数据, shape (prediction_length, 4) 或 (batch_size, prediction_length, 4)
        """
        self.eval()
        with torch.no_grad():
            # 确保输入是3D张量 [batch_size, seq_len, 4]
            if input_sequence.ndim == 2:
                input_sequence = input_sequence.unsqueeze(0)
                if time_features is not None and time_features.ndim == 2:
                    time_features = time_features.unsqueeze(0)
            
            batch_size, seq_len, _ = input_sequence.shape
            device = next(self.parameters()).device
            
            # 将输入序列转换为token ids
            input_ids = []
            for b in range(batch_size):
                seq_tokens = []
                for t in range(seq_len):
                    change, entity, upline, downline = input_sequence[b, t].tolist()
                    token_id = self.tokenizer.encode_bar(
                        int(change), int(entity), int(upline), int(downline)
                    )
                    seq_tokens.append(token_id)
                input_ids.append(seq_tokens)
            
            input_ids = torch.tensor(input_ids, device=device)
            attention_mask = torch.ones_like(input_ids)
            
            # 准备时间特征
            if time_features is not None:
                time_features = torch.tensor(time_features, device=device)
            
            # 准备证券代码
            if security_code is not None:
                security_codes = torch.tensor([security_code] * batch_size, device=device)
            else:
                security_codes = None
            
            # 自回归生成
            generated_klines = []
            current_input_ids = input_ids
            current_attention_mask = attention_mask
            current_time_features = time_features
            
            for i in range(prediction_length):
                # 前向传播
                outputs = self.forward(
                    input_ids=current_input_ids,
                    attention_mask=current_attention_mask,
                    time_features=current_time_features,
                    security_codes=security_codes
                )
                
                # 获取预测的K线值
                next_kline = outputs["kline_predictions"][:, -1, :]  # [batch_size, 4]
                generated_klines.append(next_kline.cpu().numpy())
                
                # 将预测的K线转换为token id
                next_tokens = []
                for b in range(batch_size):
                    change, entity, upline, downline = next_kline[b].tolist()
                    token_id = self.tokenizer.encode_bar(
                        int(change), int(entity), int(upline), int(downline)
                    )
                    next_tokens.append(token_id)
                
                next_tokens = torch.tensor(next_tokens, device=device).unsqueeze(1)  # [batch_size, 1]
                
                # 更新输入序列
                current_input_ids = torch.cat([current_input_ids, next_tokens], dim=1)
                current_attention_mask = torch.cat([
                    current_attention_mask, 
                    torch.ones_like(next_tokens)
                ], dim=1)
                
                # 更新时间特征（如果有）
                if current_time_features is not None:
                    # 这里需要根据实际情况更新时间特征
                    # 简单示例：复制最后一个时间步的特征
                    next_time_feature = current_time_features[:, -1:, :]
                    current_time_features = torch.cat([
                        current_time_features, next_time_feature
                    ], dim=1)
            
            # 将生成的K线数据堆叠起来
            generated_klines = np.stack(generated_klines, axis=1)  # [batch_size, prediction_length, 4]
            
            # 如果输入是单个序列，则返回单个预测
            if input_sequence.shape[0] == 1:
                generated_klines = generated_klines.squeeze(0)
            
            return generated_klines
    
    def save_pretrained(self, save_directory: str):
        """保存模型和tokenizer"""
        import os
        import torch
        
        os.makedirs(save_directory, exist_ok=True)
        
        # 保存模型
        self.model.save_pretrained(save_directory)
        
        # 保存K线特定的层
        model_state = {
            "kline_embeddings.weight": self.kline_embeddings.weight.data,
            "time_embeddings.weight": self.time_embeddings.weight.data,
            "time_embeddings.bias": self.time_embeddings.bias.data,
            "code_embeddings.weight": self.code_embeddings.weight.data,
            "code_projection.weight": self.code_projection.weight.data,
            "code_projection.bias": self.code_projection.bias.data,
            "output_projection.weight": self.output_projection.weight.data,
            "output_projection.bias": self.output_projection.bias.data
        }
        
        torch.save(model_state, os.path.join(save_directory, "kline_layers.pt"))
        
        # 保存tokenizer
        if self.tokenizer:
            self.tokenizer.save_pretrained(save_directory)
    
    @classmethod
    def from_pretrained(cls, directory: str) -> "KlineLLM":
        """从保存的目录加载模型和tokenizer"""
        import os
        import torch
        
        # 加载tokenizer
        tokenizer = KlineTokenizer.from_pretrained(directory)
        
        # 加载底层LLM模型
        model = AutoModelForCausalLM.from_pretrained(directory)
        
        # 创建KlineLLM实例
        kline_llm = cls(
            model_name=model.config._name_or_path,
            tokenizer=tokenizer,
            pretrained=False
        )
        
        # 替换底层模型
        kline_llm.model = model
        
        # 加载K线特定的层
        kline_layers_path = os.path.join(directory, "kline_layers.pt")
        if os.path.exists(kline_layers_path):
            kline_layers = torch.load(kline_layers_path, map_location="cpu")
            
            kline_llm.kline_embeddings.weight.data = kline_layers["kline_embeddings.weight"]
            kline_llm.time_embeddings.weight.data = kline_layers["time_embeddings.weight"]
            kline_llm.time_embeddings.bias.data = kline_layers["time_embeddings.bias"]
            kline_llm.code_embeddings.weight.data = kline_layers["code_embeddings.weight"]
            kline_llm.code_projection.weight.data = kline_layers["code_projection.weight"]
            kline_llm.code_projection.bias.data = kline_layers["code_projection.bias"]
            kline_llm.output_projection.weight.data = kline_layers["output_projection.weight"]
            kline_llm.output_projection.bias.data = kline_layers["output_projection.bias"]
        
        return kline_llm


def preprocess_kline_data(df, atr_window=100):
    """
    预处理K线数据，将其转换为模型可用的格式
    
    Args:
        df: 包含OHLC数据的DataFrame
        atr_window: 计算ATR的窗口大小
        
    Returns:
        处理后的DataFrame，包含change, entity, upline, downline列
    """
    # 计算ATR
    high_low = df['high'] - df['low']
    high_close = (df['high'] - df['close'].shift(1)).abs()
    low_close = (df['low'] - df['close'].shift(1)).abs()
    
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    atr = true_range.rolling(window=atr_window).mean()
    
    # 计算K线特征
    df['change'] = ((df['close'] - df['close'].shift(1)) / atr * 100).round().astype(int)
    
    # 计算实体
    df['entity'] = ((df['close'] - df['open']) / atr * 100).round().astype(int)
    
    # 计算上影线和下影线
    df['upline'] = ((df['high'] - df[['open', 'close']].max(axis=1)) / atr * 100).round().astype(int)
    df['downline'] = ((df[['open', 'close']].min(axis=1) - df['low']) / atr * 100).round().astype(int)
    
    # 限制范围
    df.loc[df['change'] < -12, 'change'] = -12
    df.loc[df['change'] > 12, 'change'] = 12
    df.loc[df['entity'] < -12, 'entity'] = -12
    df.loc[df['entity'] > 12, 'entity'] = 12
    df.loc[df['upline'] > 7, 'upline'] = 7
    df.loc[df['downline'] > 7, 'downline'] = 7
    
    return df[['change', 'entity', 'upline', 'downline']]
