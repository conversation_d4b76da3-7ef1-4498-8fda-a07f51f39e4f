{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "import random\n", "# from tqdm.notebook import tqdm\n", "\n", "import numpy as np\n", "import pandas as pd\n", "pd.set_option('display.max_rows', 500)\n", "pd.set_option('display.max_columns', 500)\n", "pd.set_option('display.width', 1000)\n", "\n", "# import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "# import torch\n", "# import torch.nn as nn\n", "# from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler\n", "# import torch.optim as optim\n", "# from fastai.layers import SigmoidRange\n", "\n", "import datetime\n", "import copy\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["class PreprocessingPipeline:\n", "    \"\"\"\n", "    生成df table: code, time_id\n", "    \"\"\"\n", "    def __init__(self, n_splits, shuffle, random_state):\n", "        self.df = pd.DataFrame()\n", "        self.df_train = pd.DataFrame()\n", "        self.df_valid = pd.DataFrame()\n", "        # self.df_train = df_train.copy(deep=True)\n", "        # self.df_test = df_test.copy(deep=True)\n", "        self.n_splits = n_splits\n", "        self.shuffle = shuffle\n", "        self.random_state = random_state\n", "        self.LABEL = \"RB8888.SC\"\n", "\n", "    def _load_data(self):\n", "        self.df = pd.read_parquet(f\"../data/tickdata_target.{self.LABEL}.parquet\")\n", "        # self.df = df[[\"label\",\"time_id\"]]\n", "        # self.df.drop_duplicates(subset=\"time_id\", inplace=True)\n", "        \n", "    def _label_encode(self):\n", "\n", "        # Encoding stock_id for embeddings\n", "        le = LabelEncoder()\n", "        self.df['stock_id_encoded'] = le.fit_transform(self.df['stock_id'].values)\n", "        # self.df_test['stock_id_encoded'] = le.transform(self.df_test['stock_id'].values)\n", "    \n", "    def _get_folds(self):\n", "        \n", "        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)\n", "        for fold, (_, val_idx) in enumerate(skf.split(X=self.df, y=self.df['target']), 1):\n", "            self.df.loc[val_idx, 'fold'] = fold\n", "        self.df['fold'] = self.df['fold'].astype(np.uint8)\n", "            \n", "    def transform(self):\n", "        \n", "        # self._label_encode()\n", "        self._load_data()\n", "        # self._get_folds()\n", "        # fold = 5\n", "        # self.df_train, self.df_valid = self.df.loc[self.df['fold'] != fold], self.df.loc[self.df['fold'] == fold]\n", "        pos = int(len(self.df)*0.8)\n", "        self.df_train=self.df[0:pos]\n", "        self.df_valid=self.df[pos+1:len(self.df)]\n", "        return self.df_train, self.df_valid\n", "\n", "\n", "# Function to calculate first WAP\n", "def calc_wap1(df):\n", "    wap = (df['bid_price1'] * df['ask_size1'] + df['ask_price1'] * df['bid_size1']) / (df['bid_size1'] + df['ask_size1'])\n", "    return wap\n", "\n", "# Function to calculate second WAP\n", "def calc_wap2(df):\n", "    wap = (df['bid_price2'] * df['ask_size2'] + df['ask_price2'] * df['bid_size2']) / (df['bid_size2'] + df['ask_size2'])\n", "    return wap\n", "\n", "def calc_wap3(df):\n", "    wap = (df['bid_price1'] * df['bid_size1'] + df['ask_price1'] * df['ask_size1']) / (df['bid_size1'] + df['ask_size1'])\n", "    return wap\n", "\n", "def calc_wap4(df):\n", "    wap = (df['bid_price2'] * df['bid_size2'] + df['ask_price2'] * df['ask_size2']) / (df['bid_size2'] + df['ask_size2'])\n", "    return wap\n", "\n", "# Function to calculate the log of the return\n", "# Remember that logb(x / y) = logb(x) - logb(y)\n", "def log_return(series):\n", "    return np.log(series).diff()\n", "\n", "# Calculate the realized volatility\n", "def realized_volatility(series):\n", "    return np.sqrt(np.sum(series**2))\n", "\n", "# Function to count unique elements of a series\n", "def count_unique(series):\n", "    return len(np.unique(series))\n", "\n", "\n", "def rmspe_metric(y_true, y_pred):\n", "\n", "    \"\"\"\n", "    Calculate root mean squared percentage error between ground-truth and predictions\n", "    \n", "    Parameters\n", "    ----------\n", "    y_true [array-like of shape (n_samples)]: Ground-truth\n", "    y_pred [array-like of shape (n_samples)]: Predictions\n", "    \n", "    Returns\n", "    -------\n", "    rmspe (float): Root mean squared percentage error\n", "    \"\"\"\n", "\n", "    rmspe = np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))\n", "    return rmspe"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [], "source": ["class FactorDataset():\n", "\n", "    def __init__(self, df):\n", "\n", "        self.LABEL = \"RB8888.SC\"\n", "        self.df = df\n", "        self.data_df = pd.read_parquet(f\"../data/tickdata.{self.LABEL}.parquet\")\n", "        self.data_df['datetime'].astype(int)\n", "        self.data_df.set_index([\"label\", \"time_id\"], inplace=True)\n", "\n", "    def _completion(self, df):\n", "        i=None\n", "        data=[]\n", "        for idx, row in df.iterrows():\n", "            if not i:\n", "                i=int(row['datetime'])\n", "                continue\n", "            i=i+1\n", "            if i==int(row['datetime']):\n", "                continue\n", "            elif len(df)+len(data)<300:\n", "                tmp=row\n", "                tmp['datetime']=i\n", "                data.append(tmp)\n", "                print(f\"add: {i}\")\n", "\n", "        if len(data) > 0:\n", "            df = df.append(data)\n", "        for i in range(300-len(df)):\n", "            tmp = df.iloc[-1]\n", "            tmp['datetime']=tmp['datetime']+1\n", "            df = df.append(tmp)\n", "        df = df.sort_values(by='datetime')\n", "        return df\n", "        \n", "\n", "    def len(self):\n", "        return len(self.df)\n", "\n", "    def getitem(self, idx):\n", "\n", "        \"\"\"\n", "        Get the idxth element in the dataset\n", "\n", "        Parameters\n", "        ----------\n", "        idx (int): Index of the sample (0 <= idx < len(self.df))\n", "\n", "        Returns\n", "        -------\n", "        \"\"\"\n", "\n", "        sample = self.df.iloc[idx]\n", "        stock_id = sample['label']\n", "        time_id = sample['time_id']\n", "\n", "\n", "        # Sequences from book data\n", "        df = self.data_df.loc[(stock_id, time_id)]\n", "        if len(df) < 300:\n", "            df = self._completion(df)\n", "        # df['datetime']=df['datetime'].apply(datetime.datetime.fromtimestamp)\n", "        # print(df['datetime'])\n", "        # print(df.head())\n", "\n", "        \"\"\"\n", "        # Calculate Wap\n", "        df['wap1'] = calc_wap1(df)\n", "        df['wap2'] = calc_wap2(df)\n", "        df['wap3'] = calc_wap3(df)\n", "        df['wap4'] = calc_wap4(df)\n", "\n", "        # Calculate log returns\n", "        df['log_return1'] = df.groupby(['time_id'])['wap1'].apply(log_return)\n", "        df['log_return2'] = df.groupby(['time_id'])['wap2'].apply(log_return)\n", "        df['log_return3'] = df.groupby(['time_id'])['wap3'].apply(log_return)\n", "        df['log_return4'] = df.groupby(['time_id'])['wap4'].apply(log_return)\n", "\n", "        # Calculate wap balance\n", "        df['wap_balance'] = abs(df['wap1'] - df['wap2'])\n", "        # Calculate spread\n", "        df['price_spread'] = (df['ask_price1'] - df['bid_price1']) / ((df['ask_price1'] + df['bid_price1']) / 2)\n", "        df['price_spread2'] = (df['ask_price2'] - df['bid_price2']) / ((df['ask_price2'] + df['bid_price2']) / 2)\n", "        df['bid_spread'] = df['bid_price1'] - df['bid_price2']\n", "        df['ask_spread'] = df['ask_price1'] - df['ask_price2']\n", "        df[\"bid_ask_spread\"] = abs(df['bid_spread'] - df['ask_spread'])\n", "        df['total_volume'] = (df['ask_size1'] + df['ask_size2']) + (df['bid_size1'] + df['bid_size2'])\n", "        df['volume_imbalance'] = abs((df['ask_size1'] + df['ask_size2']) - (df['bid_size1'] + df['bid_size2']))\n", "\n", "        df.drop(['datetime'], axis=1, inplace=True)\n", "        # sequences = torch.as_tensor(np.array(df.values), dtype=torch.float)\n", "\n", "        # Flip sequences on zeroth dimension\n", "        # if np.random.rand() < self.transforms['flip']:\n", "        #     sequences = torch.flip(sequences, dims=[0])\n", "            \n", "        target = sample['target']\n", "        # target = torch.as_tensor(target, dtype=torch.float)\n", "        return df, target\n", "        \"\"\"\n", "\n", "        \n", "\n", "        # Calculate Wap\n", "        df['wap1'] = calc_wap1(df)\n", "        df['wap2'] = calc_wap2(df)\n", "        df['wap3'] = calc_wap3(df)\n", "        df['wap4'] = calc_wap4(df)\n", "        # Calculate log returns\n", "        df['log_return1'] = df.groupby(['time_id'])['wap1'].apply(log_return)\n", "        df['log_return2'] = df.groupby(['time_id'])['wap2'].apply(log_return)\n", "        df['log_return3'] = df.groupby(['time_id'])['wap3'].apply(log_return)\n", "        df['log_return4'] = df.groupby(['time_id'])['wap4'].apply(log_return)\n", "        # Calculate wap balance\n", "        df['wap_balance'] = abs(df['wap1'] - df['wap2'])\n", "        # Calculate spread\n", "        df['price_spread'] = (df['ask_price1'] - df['bid_price1']) / ((df['ask_price1'] + df['bid_price1']) / 2)\n", "        df['price_spread2'] = (df['ask_price2'] - df['bid_price2']) / ((df['ask_price2'] + df['bid_price2']) / 2)\n", "        df['bid_spread'] = df['bid_price1'] - df['bid_price2']\n", "        df['ask_spread'] = df['ask_price1'] - df['ask_price2']\n", "        df[\"bid_ask_spread\"] = abs(df['bid_spread'] - df['ask_spread'])\n", "        df['total_volume'] = (df['ask_size1'] + df['ask_size2']) + (df['bid_size1'] + df['bid_size2'])\n", "        df['volume_imbalance'] = abs((df['ask_size1'] + df['ask_size2']) - (df['bid_size1'] + df['bid_size2']))\n", "        \n", "        # Dict for aggregations\n", "        create_feature_dict = {\n", "            'wap1': [np.sum, np.std],\n", "            'wap2': [np.sum, np.std],\n", "            'wap3': [np.sum, np.std],\n", "            'wap4': [np.sum, np.std],\n", "            'log_return1': [realized_volatility],\n", "            'log_return2': [realized_volatility],\n", "            'log_return3': [realized_volatility],\n", "            'log_return4': [realized_volatility],\n", "            'wap_balance': [np.sum, np.max],\n", "            'price_spread':[np.sum, np.max],\n", "            'price_spread2':[np.sum, np.max],\n", "            'bid_spread':[np.sum, np.max],\n", "            'ask_spread':[np.sum, np.max],\n", "            'total_volume':[np.sum, np.max],\n", "            'volume_imbalance':[np.sum, np.max],\n", "            \"bid_ask_spread\":[np.sum,  np.max],\n", "        }\n", "        create_feature_dict_time = {\n", "            'log_return1': [realized_volatility],\n", "            'log_return2': [realized_volatility],\n", "            'log_return3': [realized_volatility],\n", "            'log_return4': [realized_volatility],\n", "        }\n", "        \n", "        # Function to get group stats for different windows (seconds in bucket)\n", "        def get_stats_window(fe_dict,seconds_in_bucket, add_suffix = False):\n", "            # Group by the window\n", "            first = df['datetime'][0]\n", "\n", "            # print(df[df['datetime'] >= first + seconds_in_bucket].shape)\n", "            df_feature = df[df['datetime'] >= first + seconds_in_bucket].groupby(['time_id']).agg(fe_dict).reset_index()\n", "            # Rename columns joining suffix\n", "            df_feature.columns = ['_'.join(col) for col in df_feature.columns]\n", "            # Add a suffix to differentiate windows\n", "            if add_suffix:\n", "                df_feature = df_feature.add_suffix('_' + str(seconds_in_bucket))\n", "            return df_feature\n", "        \n", "        # Get the stats for different windows\n", "        df_feature = get_stats_window(create_feature_dict,seconds_in_bucket = 0, add_suffix = False)\n", "        # df_feature_500 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 500, add_suffix = True)\n", "        # df_feature_400 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 400, add_suffix = True)\n", "        # df_feature_300 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 300, add_suffix = True)\n", "        df_feature_200 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 200, add_suffix = True)\n", "        df_feature_100 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 100, add_suffix = True)\n", "\n", "        # Merge all\n", "        # df_feature = df_feature.merge(df_feature_500, how = 'left', left_on = 'time_id_', right_on = 'time_id__500')\n", "        # df_feature = df_feature.merge(df_feature_400, how = 'left', left_on = 'time_id_', right_on = 'time_id__400')\n", "        # df_feature = df_feature.merge(df_feature_300, how = 'left', left_on = 'time_id_', right_on = 'time_id__300')\n", "        df_feature = df_feature.merge(df_feature_200, how = 'left', left_on = 'time_id_', right_on = 'time_id__200')\n", "        df_feature = df_feature.merge(df_feature_100, how = 'left', left_on = 'time_id_', right_on = 'time_id__100')\n", "        # Drop unnecesary time_ids\n", "        df_feature.drop(['time_id__200','time_id__100'], axis = 1, inplace = True)\n", "        \n", "        \n", "        # Create row_id so we can merge\n", "        # stock_id = file_path.split('=')[1]\n", "        # df_feature['row_id'] = df_feature['time_id_'].apply(lambda x: f'{stock_id}-{x}')\n", "        df_feature.drop(['time_id_'], axis = 1, inplace = True)\n", "        # df_feature.drop(['datetime'], axis=1, inplace=True)\n", "        return df_feature, np.mean(df['price'])\n", "\n"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [], "source": ["preprocessing_parameters = {\n", "    'n_splits': 5,\n", "    'shuffle': True,\n", "    'random_state': 42,\n", "    # 'only_trading_code': True,\n", "    # 'data_path': 'e:/lab/RoboQuant/pylab/data',\n", "    # 'portfolios': ['00200910081133001', '00171106132928000', '00170623114649000'],\n", "    # 'interface_params': {\n", "    #     'input_dim': 1, # 1: expression call 2: API call\n", "    #     'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding\n", "    # }\n", "}\n", "\n", "ppp = PreprocessingPipeline(**preprocessing_parameters)\n", "df_train, df_valid = ppp.transform()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_train.head(100))"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2413\n"]}], "source": ["fd = FactorDataset(df_train)\n", "print(fd.len())"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [], "source": ["item, close = fd.getitem(0)"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4689.74\n"]}], "source": ["print(close)"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bound method DataFrame.info of    time_id_      wap1_sum  wap1_std      wap2_sum  wap2_std      wap3_sum  wap3_std      wap4_sum  wap4_std  log_return1_realized_volatility  log_return2_realized_volatility  log_return3_realized_volatility  log_return4_realized_volatility  wap_balance_sum  wap_balance_amax  price_spread_sum  price_spread_amax  price_spread2_sum  price_spread2_amax  bid_spread_sum  bid_spread_amax  ask_spread_sum  ask_spread_amax  total_volume_sum  total_volume_amax  volume_imbalance_sum  volume_imbalance_amax  bid_ask_spread_sum  bid_ask_spread_amax  log_return1_realized_volatility_200  log_return2_realized_volatility_200  log_return3_realized_volatility_200  log_return4_realized_volatility_200  log_return1_realized_volatility_100  log_return2_realized_volatility_100  log_return3_realized_volatility_100  log_return4_realized_volatility_100\n", "0   5475781  1.406799e+06  0.421672  1.406972e+06   0.35071  1.406802e+06  0.575593  1.406629e+06  0.634742                          0.00032                         0.000353                         0.000853                         0.000615       172.988882          1.323807          0.064295            0.00032           0.192245            0.000747           300.0              1.0          -300.0             -1.0          139445.5             1475.5               46938.5                 1102.0               600.0                  2.0                                  0.0                                  0.0                                  0.0                                  0.0                                  0.0                                  0.0                                  0.0                                  0.0>\n"]}], "source": ["print(item.info)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["item2=item.groupby(['time_id']).agg(np.nanmean)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5475781.0\n"]}], "source": ["print(item2.iloc[0,0])"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 5.47578100e+06  1.40679886e+06  4.21671978e-01  1.40697185e+06\n", "  3.50709625e-01  1.40680164e+06  5.75593307e-01  1.40662865e+06\n", "  6.34741574e-01  3.19713234e-04  3.53005322e-04  8.52777728e-04\n", "  6.14810605e-04  1.72988882e+02  1.32380738e+00  6.42949396e-02\n", "  3.19982934e-04  1.92244876e-01  7.46626847e-04  3.00000000e+02\n", "  1.00000000e+00 -3.00000000e+02 -1.00000000e+00  1.39445500e+05\n", "  1.47550000e+03  4.69385000e+04  1.10200000e+03  6.00000000e+02\n", "  2.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00\n", "  0.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00\n", "  0.00000000e+00]\n"]}], "source": ["print(item2.values.ravel())"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 5.47578100e+06  1.40679886e+06  4.21671978e-01  1.40697185e+06\n", "  3.50709625e-01  1.40680164e+06  5.75593307e-01  1.40662865e+06\n", "  6.34741574e-01  3.19713234e-04  3.53005322e-04  8.52777728e-04\n", "  6.14810605e-04  1.72988882e+02  1.32380738e+00  6.42949396e-02\n", "  3.19982934e-04  1.92244876e-01  7.46626847e-04  3.00000000e+02\n", "  1.00000000e+00 -3.00000000e+02 -1.00000000e+00  1.39445500e+05\n", "  1.47550000e+03  4.69385000e+04  1.10200000e+03  6.00000000e+02\n", "  2.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00\n", "  0.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00\n", "  0.00000000e+00]\n"]}], "source": ["print(item2.values.reshape(-1))"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 37)\n"]}], "source": ["print(item.shape)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["()"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array(5)\n", "a.shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["labels=['I8888.DC', 'M8888.DC', 'MA8888.ZC', 'RB8888.SC', 'SA8888.ZC','TA8888.ZC']\n", "LABEL=\"RB8888.SC\"\n", "df = pd.read_parquet(f\"../data/tickdata.parquet\")"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["df2 = df.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["df2['return'] = log_return(df2['price'])\n", "df2=df2.fillna(0)\n", "df2['target'] = (df2['return']>0).astype(int)\n", "df2=df2.drop(['price', 'return'], axis=1)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>label</th>\n", "      <th>time_id</th>\n", "      <th>target</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>I8888.DC</td>\n", "      <td>5475781</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>I8888.DC</td>\n", "      <td>5475782</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>I8888.DC</td>\n", "      <td>5475783</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>I8888.DC</td>\n", "      <td>5475784</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>I8888.DC</td>\n", "      <td>5475785</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7011</th>\n", "      <td>TA8888.ZC</td>\n", "      <td>5485123</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7012</th>\n", "      <td>TA8888.ZC</td>\n", "      <td>5485124</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7013</th>\n", "      <td>TA8888.ZC</td>\n", "      <td>5485125</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7014</th>\n", "      <td>TA8888.ZC</td>\n", "      <td>5485126</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7015</th>\n", "      <td>TA8888.ZC</td>\n", "      <td>5485127</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7016 rows × 3 columns</p>\n", "</div>"], "text/plain": ["          label  time_id  target\n", "0      I8888.DC  5475781       0\n", "1      I8888.DC  5475782       0\n", "2      I8888.DC  5475783       1\n", "3      I8888.DC  5475784       0\n", "4      I8888.DC  5475785       1\n", "...         ...      ...     ...\n", "7011  TA8888.ZC  5485123       1\n", "7012  TA8888.ZC  5485124       0\n", "7013  TA8888.ZC  5485125       1\n", "7014  TA8888.ZC  5485126       0\n", "7015  TA8888.ZC  5485127       0\n", "\n", "[7016 rows x 3 columns]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["df2"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Calculate Wap\n", "df['wap1'] = calc_wap1(df)\n", "df['wap2'] = calc_wap2(df)\n", "df['wap3'] = calc_wap3(df)\n", "df['wap4'] = calc_wap4(df)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Calculate log returns\n", "df['log_return1'] = df.groupby(['time_id'])['wap1'].apply(log_return)\n", "df['log_return2'] = df.groupby(['time_id'])['wap2'].apply(log_return)\n", "df['log_return3'] = df.groupby(['time_id'])['wap3'].apply(log_return)\n", "df['log_return4'] = df.groupby(['time_id'])['wap4'].apply(log_return)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# Calculate wap balance\n", "df['wap_balance'] = abs(df['wap1'] - df['wap2'])\n", "# Calculate spread\n", "df['price_spread'] = (df['ask_price1'] - df['bid_price1']) / ((df['ask_price1'] + df['bid_price1']) / 2)\n", "df['price_spread2'] = (df['ask_price2'] - df['bid_price2']) / ((df['ask_price2'] + df['bid_price2']) / 2)\n", "df['bid_spread'] = df['bid_price1'] - df['bid_price2']\n", "df['ask_spread'] = df['ask_price1'] - df['ask_price2']\n", "df[\"bid_ask_spread\"] = abs(df['bid_spread'] - df['ask_spread'])\n", "df['total_volume'] = (df['ask_size1'] + df['ask_size2']) + (df['bid_size1'] + df['bid_size2'])\n", "df['volume_imbalance'] = abs((df['ask_size1'] + df['ask_size2']) - (df['bid_size1'] + df['bid_size2']))"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time_id</th>\n", "      <th>label</th>\n", "      <th>datetime</th>\n", "      <th>price</th>\n", "      <th>size</th>\n", "      <th>bid_price1</th>\n", "      <th>bid_price2</th>\n", "      <th>bid_size1</th>\n", "      <th>bid_size2</th>\n", "      <th>ask_price1</th>\n", "      <th>...</th>\n", "      <th>log_return3</th>\n", "      <th>log_return4</th>\n", "      <th>wap_balance</th>\n", "      <th>price_spread</th>\n", "      <th>price_spread2</th>\n", "      <th>bid_spread</th>\n", "      <th>ask_spread</th>\n", "      <th>bid_ask_spread</th>\n", "      <th>total_volume</th>\n", "      <th>volume_imbalance</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>550159</th>\n", "      <td>5475781</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-21 11:09:23</td>\n", "      <td>4687.0</td>\n", "      <td>14</td>\n", "      <td>4687.0</td>\n", "      <td>4686.0</td>\n", "      <td>541</td>\n", "      <td>745</td>\n", "      <td>4688.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.779370</td>\n", "      <td>0.000213</td>\n", "      <td>0.000640</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1470</td>\n", "      <td>1102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550160</th>\n", "      <td>5475781</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-21 11:09:24</td>\n", "      <td>4687.0</td>\n", "      <td>18</td>\n", "      <td>4687.0</td>\n", "      <td>4686.0</td>\n", "      <td>525</td>\n", "      <td>745</td>\n", "      <td>4688.0</td>\n", "      <td>...</td>\n", "      <td>6.601413e-06</td>\n", "      <td>6.671371e-07</td>\n", "      <td>0.807186</td>\n", "      <td>0.000213</td>\n", "      <td>0.000640</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1475</td>\n", "      <td>1065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550161</th>\n", "      <td>5475781</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-21 11:09:24</td>\n", "      <td>4687.0</td>\n", "      <td>9</td>\n", "      <td>4687.0</td>\n", "      <td>4686.0</td>\n", "      <td>496</td>\n", "      <td>745</td>\n", "      <td>4688.0</td>\n", "      <td>...</td>\n", "      <td>9.299737e-06</td>\n", "      <td>1.991979e-06</td>\n", "      <td>0.841440</td>\n", "      <td>0.000213</td>\n", "      <td>0.000640</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1476</td>\n", "      <td>1006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550162</th>\n", "      <td>5475781</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-21 11:09:25</td>\n", "      <td>4687.0</td>\n", "      <td>18</td>\n", "      <td>4687.0</td>\n", "      <td>4686.0</td>\n", "      <td>489</td>\n", "      <td>745</td>\n", "      <td>4688.0</td>\n", "      <td>...</td>\n", "      <td>7.742274e-07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.845069</td>\n", "      <td>0.000213</td>\n", "      <td>0.000640</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1470</td>\n", "      <td>998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550163</th>\n", "      <td>5475781</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-21 11:09:25</td>\n", "      <td>4687.0</td>\n", "      <td>22</td>\n", "      <td>4687.0</td>\n", "      <td>4686.0</td>\n", "      <td>498</td>\n", "      <td>745</td>\n", "      <td>4688.0</td>\n", "      <td>...</td>\n", "      <td>-9.672385e-06</td>\n", "      <td>-6.624266e-07</td>\n", "      <td>0.802837</td>\n", "      <td>0.000213</td>\n", "      <td>0.000640</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1446</td>\n", "      <td>1040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764557</th>\n", "      <td>5477843</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-28 14:59:57</td>\n", "      <td>4830.0</td>\n", "      <td>255</td>\n", "      <td>4829.0</td>\n", "      <td>4828.0</td>\n", "      <td>374</td>\n", "      <td>720</td>\n", "      <td>4830.0</td>\n", "      <td>...</td>\n", "      <td>-2.843351e-05</td>\n", "      <td>-5.560332e-06</td>\n", "      <td>1.625494</td>\n", "      <td>0.000207</td>\n", "      <td>0.000621</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1962</td>\n", "      <td>226</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764558</th>\n", "      <td>5477843</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-28 14:59:58</td>\n", "      <td>4829.0</td>\n", "      <td>222</td>\n", "      <td>4829.0</td>\n", "      <td>4828.0</td>\n", "      <td>301</td>\n", "      <td>753</td>\n", "      <td>4830.0</td>\n", "      <td>...</td>\n", "      <td>1.993566e-06</td>\n", "      <td>-6.142604e-07</td>\n", "      <td>1.638088</td>\n", "      <td>0.000207</td>\n", "      <td>0.000621</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1788</td>\n", "      <td>320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764559</th>\n", "      <td>5477843</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-28 14:59:58</td>\n", "      <td>4830.0</td>\n", "      <td>123</td>\n", "      <td>4829.0</td>\n", "      <td>4828.0</td>\n", "      <td>254</td>\n", "      <td>751</td>\n", "      <td>4830.0</td>\n", "      <td>...</td>\n", "      <td>3.994113e-06</td>\n", "      <td>3.572523e-08</td>\n", "      <td>1.657206</td>\n", "      <td>0.000207</td>\n", "      <td>0.000621</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1687</td>\n", "      <td>323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764560</th>\n", "      <td>5477843</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-28 14:59:59</td>\n", "      <td>4830.0</td>\n", "      <td>438</td>\n", "      <td>4829.0</td>\n", "      <td>4828.0</td>\n", "      <td>252</td>\n", "      <td>769</td>\n", "      <td>4830.0</td>\n", "      <td>...</td>\n", "      <td>-4.369706e-05</td>\n", "      <td>-3.149820e-07</td>\n", "      <td>1.447687</td>\n", "      <td>0.000207</td>\n", "      <td>0.000621</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1303</td>\n", "      <td>739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764561</th>\n", "      <td>5477843</td>\n", "      <td>RB8888.SC</td>\n", "      <td>2022-01-28 14:59:59</td>\n", "      <td>4830.0</td>\n", "      <td>179</td>\n", "      <td>4829.0</td>\n", "      <td>4828.0</td>\n", "      <td>186</td>\n", "      <td>769</td>\n", "      <td>4830.0</td>\n", "      <td>...</td>\n", "      <td>-1.168493e-05</td>\n", "      <td>6.882177e-06</td>\n", "      <td>1.358027</td>\n", "      <td>0.000207</td>\n", "      <td>0.000621</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>2.0</td>\n", "      <td>1137</td>\n", "      <td>773</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>214403 rows × 29 columns</p>\n", "</div>"], "text/plain": ["        time_id      label            datetime   price  size  bid_price1  \\\n", "index                                                                      \n", "550159  5475781  RB8888.SC 2022-01-21 11:09:23  4687.0    14      4687.0   \n", "550160  5475781  RB8888.SC 2022-01-21 11:09:24  4687.0    18      4687.0   \n", "550161  5475781  RB8888.SC 2022-01-21 11:09:24  4687.0     9      4687.0   \n", "550162  5475781  RB8888.SC 2022-01-21 11:09:25  4687.0    18      4687.0   \n", "550163  5475781  RB8888.SC 2022-01-21 11:09:25  4687.0    22      4687.0   \n", "...         ...        ...                 ...     ...   ...         ...   \n", "764557  5477843  RB8888.SC 2022-01-28 14:59:57  4830.0   255      4829.0   \n", "764558  5477843  RB8888.SC 2022-01-28 14:59:58  4829.0   222      4829.0   \n", "764559  5477843  RB8888.SC 2022-01-28 14:59:58  4830.0   123      4829.0   \n", "764560  5477843  RB8888.SC 2022-01-28 14:59:59  4830.0   438      4829.0   \n", "764561  5477843  RB8888.SC 2022-01-28 14:59:59  4830.0   179      4829.0   \n", "\n", "        bid_price2  bid_size1  bid_size2  ask_price1  ...   log_return3  \\\n", "index                                                 ...                 \n", "550159      4686.0        541        745      4688.0  ...           NaN   \n", "550160      4686.0        525        745      4688.0  ...  6.601413e-06   \n", "550161      4686.0        496        745      4688.0  ...  9.299737e-06   \n", "550162      4686.0        489        745      4688.0  ...  7.742274e-07   \n", "550163      4686.0        498        745      4688.0  ... -9.672385e-06   \n", "...            ...        ...        ...         ...  ...           ...   \n", "764557      4828.0        374        720      4830.0  ... -2.843351e-05   \n", "764558      4828.0        301        753      4830.0  ...  1.993566e-06   \n", "764559      4828.0        254        751      4830.0  ...  3.994113e-06   \n", "764560      4828.0        252        769      4830.0  ... -4.369706e-05   \n", "764561      4828.0        186        769      4830.0  ... -1.168493e-05   \n", "\n", "         log_return4  wap_balance  price_spread  price_spread2  bid_spread  \\\n", "index                                                                        \n", "550159           NaN     0.779370      0.000213       0.000640         1.0   \n", "550160  6.671371e-07     0.807186      0.000213       0.000640         1.0   \n", "550161  1.991979e-06     0.841440      0.000213       0.000640         1.0   \n", "550162  0.000000e+00     0.845069      0.000213       0.000640         1.0   \n", "550163 -6.624266e-07     0.802837      0.000213       0.000640         1.0   \n", "...              ...          ...           ...            ...         ...   \n", "764557 -5.560332e-06     1.625494      0.000207       0.000621         1.0   \n", "764558 -6.142604e-07     1.638088      0.000207       0.000621         1.0   \n", "764559  3.572523e-08     1.657206      0.000207       0.000621         1.0   \n", "764560 -3.149820e-07     1.447687      0.000207       0.000621         1.0   \n", "764561  6.882177e-06     1.358027      0.000207       0.000621         1.0   \n", "\n", "        ask_spread  bid_ask_spread  total_volume  volume_imbalance  \n", "index                                                               \n", "550159        -1.0             2.0          1470              1102  \n", "550160        -1.0             2.0          1475              1065  \n", "550161        -1.0             2.0          1476              1006  \n", "550162        -1.0             2.0          1470               998  \n", "550163        -1.0             2.0          1446              1040  \n", "...            ...             ...           ...               ...  \n", "764557        -1.0             2.0          1962               226  \n", "764558        -1.0             2.0          1788               320  \n", "764559        -1.0             2.0          1687               323  \n", "764560        -1.0             2.0          1303               739  \n", "764561        -1.0             2.0          1137               773  \n", "\n", "[214403 rows x 29 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}