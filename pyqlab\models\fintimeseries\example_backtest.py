"""
TimeSeriesModel2drV2模型回测示例

该脚本展示如何使用回测系统对训练好的模型进行回测评估。
"""

import os
import sys
import logging
import torch
import numpy as np
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.fintimeseries.backtest_time_series_model2dr_v2 import TimeSeriesBacktester, load_model
from pyqlab.data import get_dataset

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_model():
    """
    创建一个示例模型用于演示
    注意：在实际使用中，您应该加载训练好的模型
    """
    from pyqlab.models.fintimeseries.time_series_model2dr_v2 import TimeSeriesModel2drV2
    
    # 创建模型实例（使用较小的参数以便快速演示）
    model = TimeSeriesModel2drV2(
        num_embeds=[72, 5, 11],
        num_channel=10,
        num_input=51,
        dropout=0.1,
        num_conv_layers=2,
        conv_channels=[32, 64],
        use_residual=True,
        use_attention=False,
        use_temporal_conv=True,
        num_outputs=1,
        probabilistic=False,
        multi_task=False,
        out_channels=(24, 48, 256, 128),
        ins_nums=(0, 51, 51, 17),
        inference_mode=True  # 设置为推理模式
    )
    
    # 设置为评估模式
    model.eval()
    
    logger.info("已创建示例模型（注意：这是随机初始化的模型，仅用于演示）")
    return model


def run_simple_backtest():
    """运行简单的回测示例"""
    logger.info("=" * 60)
    logger.info("TimeSeriesModel2drV2 回测示例")
    logger.info("=" * 60)
    
    # 1. 创建或加载模型
    logger.info("步骤 1: 准备模型...")
    
    # 检查是否有预训练模型
    model_path = "./model/best_model.ckpt"
    if os.path.exists(model_path):
        logger.info(f"发现预训练模型: {model_path}")
        try:
            model = load_model(model_path, 'pytorch')
            logger.info("成功加载预训练模型")
        except Exception as e:
            logger.warning(f"加载预训练模型失败: {e}")
            logger.info("使用示例模型代替...")
            model = create_sample_model()
    else:
        logger.info("未找到预训练模型，使用示例模型...")
        model = create_sample_model()
    
    # 2. 准备数据
    logger.info("步骤 2: 准备数据...")
    
    # 检查数据路径
    data_path = "e:/featdata/main"
    if not os.path.exists(data_path):
        logger.warning(f"数据路径不存在: {data_path}")
        logger.info("请确保数据路径正确，或修改 data_path 变量")
        return
    
    try:
        # 加载数据集
        dataset = get_dataset(
            ds_files=["main.2024"],
            fut_codes=["IF", "IH", "IC"],
            data_path=data_path,
            model_type=0,  # FTS模型类型
            seq_len=30,
            pred_len=1
        )
        dataset.load_data()
        logger.info(f"成功加载数据集，大小: {len(dataset)}")
        
        # 如果数据集太大，只使用一部分进行演示
        if len(dataset) > 1000:
            logger.info("数据集较大，仅使用前1000个样本进行演示")
            # 创建子集
            subset_indices = list(range(1000))
            from torch.utils.data import Subset
            dataset = Subset(dataset, subset_indices)
            
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        logger.info("请检查数据路径和文件是否存在")
        return
    
    # 3. 创建回测器
    logger.info("步骤 3: 创建回测器...")
    
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=100000.0,  # 10万初始资金
        leverage=1.0  # 无杠杆
    )
    
    # 4. 执行回测
    logger.info("步骤 4: 执行回测...")
    
    start_time = datetime.now()
    
    results = backtester.backtest(
        dataset=dataset,
        seq_len=30,
        commission=0.0005,  # 0.05%手续费
        threshold=0.01,     # 1%的信号阈值
        stop_loss=0.05,     # 5%止损
        take_profit=0.10,   # 10%止盈
        print_interval=200
    )
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 5. 展示结果
    logger.info("步骤 5: 回测结果分析...")
    logger.info("=" * 60)
    logger.info("回测结果摘要")
    logger.info("=" * 60)
    
    logger.info(f"回测耗时: {duration:.2f} 秒")
    logger.info(f"数据样本数: {len(dataset)}")
    logger.info("")
    
    # 收益指标
    logger.info("📈 收益指标:")
    logger.info(f"  总收益率: {results['total_return']:.4f} ({results['total_return']*100:.2f}%)")
    logger.info(f"  年化收益率: {results['annualized_return']:.4f} ({results['annualized_return']*100:.2f}%)")
    logger.info(f"  最终资金: {results['final_capital']:,.2f}")
    logger.info("")
    
    # 风险指标
    logger.info("⚠️ 风险指标:")
    logger.info(f"  最大回撤: {results['max_drawdown']:.4f} ({results['max_drawdown']*100:.2f}%)")
    logger.info(f"  夏普比率: {results['sharpe_ratio']:.4f}")
    logger.info("")
    
    # 交易统计
    logger.info("📊 交易统计:")
    logger.info(f"  总交易次数: {results['total_trades']}")
    logger.info(f"  胜率: {results['win_rate']:.4f} ({results['win_rate']*100:.2f}%)")
    logger.info(f"  平均盈利: {results['avg_profit']:.2f}")
    logger.info(f"  平均亏损: {results['avg_loss']:.2f}")
    logger.info(f"  盈亏比: {results['profit_loss_ratio']:.4f}")
    logger.info("")
    
    # 信号分析
    signals = results['signals']
    signal_counts = {}
    for signal in signals:
        signal_counts[signal] = signal_counts.get(signal, 0) + 1
    
    logger.info("📡 信号分析:")
    for signal, count in signal_counts.items():
        percentage = count / len(signals) * 100
        logger.info(f"  {signal}: {count} 次 ({percentage:.1f}%)")
    logger.info("")
    
    # 6. 保存结果
    logger.info("步骤 6: 保存结果...")
    
    output_dir = "./example_backtest_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存详细结果
    results_path = os.path.join(output_dir, 'backtest_results.json')
    backtester.save_results(results, results_path)
    
    # 生成可视化图表
    try:
        chart_path = os.path.join(output_dir, 'backtest_chart.png')
        backtester.visualize_backtest(results, chart_path)
        logger.info(f"图表已保存到: {chart_path}")
    except Exception as e:
        logger.warning(f"图表生成失败: {e}")
    
    logger.info("=" * 60)
    logger.info("回测完成！")
    logger.info(f"详细结果已保存到: {output_dir}")
    logger.info("=" * 60)
    
    return results


def run_parameter_sensitivity_test():
    """运行参数敏感性测试"""
    logger.info("运行参数敏感性测试...")
    
    # 不同的阈值设置
    thresholds = [0.005, 0.01, 0.02, 0.03]
    
    # 创建模型
    model = create_sample_model()
    
    # 简化的数据集（用于快速测试）
    from pyqlab.models.fintimeseries.test_backtest import MockDataset
    dataset = MockDataset(size=200, seq_len=30)
    
    results_summary = []
    
    for threshold in thresholds:
        logger.info(f"测试阈值: {threshold}")
        
        backtester = TimeSeriesBacktester(
            model=model,
            initial_capital=10000.0,
            leverage=1.0
        )
        
        results = backtester.backtest(
            dataset=dataset,
            seq_len=30,
            commission=0.001,
            threshold=threshold,
            print_interval=1000  # 减少输出
        )
        
        results_summary.append({
            'threshold': threshold,
            'total_return': results['total_return'],
            'max_drawdown': results['max_drawdown'],
            'sharpe_ratio': results['sharpe_ratio'],
            'total_trades': results['total_trades'],
            'win_rate': results['win_rate']
        })
    
    # 打印对比结果
    logger.info("\n参数敏感性测试结果:")
    logger.info("阈值\t总收益率\t最大回撤\t夏普比率\t交易次数\t胜率")
    logger.info("-" * 70)
    
    for result in results_summary:
        logger.info(f"{result['threshold']:.3f}\t"
                   f"{result['total_return']:.4f}\t"
                   f"{result['max_drawdown']:.4f}\t"
                   f"{result['sharpe_ratio']:.4f}\t"
                   f"{result['total_trades']}\t\t"
                   f"{result['win_rate']:.4f}")


if __name__ == "__main__":
    try:
        # 运行简单回测示例
        run_simple_backtest()
        
        # 可选：运行参数敏感性测试
        # run_parameter_sensitivity_test()
        
    except KeyboardInterrupt:
        logger.info("用户中断了程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
