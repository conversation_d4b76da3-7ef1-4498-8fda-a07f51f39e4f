{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from redis import Redis\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyqlab.scripts.settlementdata import SettlementData\n", "sd=SettlementData()\n", "sd.DisplayTodayPositions()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["redis=Redis(host='**************', db=3, password='wdljshbsjzsszsbbzyjcsz~1974', port=51301)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["settlementreport:中信建投实盘CTP交易-SE:********\n", "settlementreport:南华期货实盘CTP交易-SE:********\n", "settlementreport:中信建投实盘CTP交易-XZY:********\n", "settlementreport:中信建投实盘CTP交易-SE:********\n", "settlementreport:南华期货实盘CTP交易-SE:********\n", "settlementreport:中信建投实盘CTP交易-YQF:********\n"]}], "source": ["for key in redis.keys('*'):\n", "    print(key.decode('gbk'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(redis.get('settlementreport:中信建投实盘CTP交易-SE:********'.encode('gbk')))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def get_settlement_report(date: str):\n", "    data=[]\n", "    accounts=['中信建投实盘CTP交易-SE', '中信建投实盘CTP交易-XZY', '南华期货实盘CTP交易-SE']\n", "    for acc in accounts:\n", "        rpt=redis.get(f'settlementreport:{acc}:{date}'.encode('gbk'))\n", "        if len(rpt)>100:\n", "           data.append(rpt.decode('gbk', 'ignore')) \n", "    return data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data=get_settlement_report('********')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[1]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>投资单元</th>\n", "      <th>交易编码</th>\n", "      <th>品种</th>\n", "      <th>合约</th>\n", "      <th>买持</th>\n", "      <th>买均价</th>\n", "      <th>卖持</th>\n", "      <th>卖均价</th>\n", "      <th>昨结算</th>\n", "      <th>今结算</th>\n", "      <th>持仓盯市盈亏</th>\n", "      <th>保证金占用</th>\n", "      <th>投/保</th>\n", "      <th>多头期权市值</th>\n", "      <th>空头期权市值</th>\n", "      <th>资金账号</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>72100158</td>\n", "      <td>34285272</td>\n", "      <td>尿素</td>\n", "      <td>UR301</td>\n", "      <td>2</td>\n", "      <td>2423.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>2475.000</td>\n", "      <td>2453.000</td>\n", "      <td>-880.00</td>\n", "      <td>15699.20</td>\n", "      <td>投机</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>72100158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>72100158</td>\n", "      <td>02146864</td>\n", "      <td>铁矿石</td>\n", "      <td>i2301</td>\n", "      <td>1</td>\n", "      <td>683.500</td>\n", "      <td>1</td>\n", "      <td>602.500</td>\n", "      <td>697.000</td>\n", "      <td>718.000</td>\n", "      <td>0.00</td>\n", "      <td>14360.00</td>\n", "      <td>投机</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>72100158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>72100158</td>\n", "      <td>02146864</td>\n", "      <td>聚丙烯</td>\n", "      <td>pp2301</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>2</td>\n", "      <td>7544.000</td>\n", "      <td>7657.000</td>\n", "      <td>7777.000</td>\n", "      <td>-1200.00</td>\n", "      <td>13998.60</td>\n", "      <td>投机</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>72100158</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       投资单元      交易编码   品种      合约 买持       买均价 卖持       卖均价       昨结算  \\\n", "0  72100158  34285272   尿素   UR301  2  2423.000  0     0.000  2475.000   \n", "1  72100158  02146864  铁矿石   i2301  1   683.500  1   602.500   697.000   \n", "2  72100158  02146864  聚丙烯  pp2301  0     0.000  2  7544.000  7657.000   \n", "\n", "        今结算    持仓盯市盈亏     保证金占用 投/保 多头期权市值 空头期权市值      资金账号  \n", "0  2453.000   -880.00  15699.20  投机   0.00   0.00  72100158  \n", "1   718.000      0.00  14360.00  投机   0.00   0.00  72100158  \n", "2  7777.000  -1200.00  13998.60  投机   0.00   0.00  72100158  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["sd=SettlementData(data[2])\n", "df=sd.Positions()\n", "df"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["db_path='d:/RoboQuant'\n", "def load_settlement_report_from_db():\n", "    \"\"\"\n", "    从数据库加载写库因子数据\n", "    \"\"\"\n", "    db=create_db(\"leveldb\", f\"{db_path}/store/kv.db\" , Mode.read)\n", "    cursor = db.new_cursor()\n", "    # cursor.seek_to_first()\n", "    data = []\n", "    while cursor.valid():\n", "        # print(cursor.key())\n", "        # print(cursor.key(), cursor.value())\n", "        # fss:lf:\n", "        if cursor.key()[0:17] == b'settlementreport:':\n", "            item=[]\n", "            key = cursor.key().decode('gbk')\n", "            value = cursor.value().decode('gbk', 'ignore')\n", "            item.append(key[17:])\n", "            item.append(value)\n", "            # print(key, value)\n", "            data.append(item)\n", "        cursor.next()\n", "    del cursor\n", "    db.close()\n", "    del db\n", "    return data\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["data=load_settlement_report_from_db()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                        中信建投期货有限公司                                        \n", "                                                                    制表时间 Creation Date：********\n", "----------------------------------------------------------------------------------------------------\n", "                             交易结算单(盯市) Settlement Statement(MTM)                             \n", "客户号 Client ID：  ********          客户名称 Client Name：庞娟娟\n", "日期 Date：********\n", "\n", " \n", "                  资金状况  币种：人民币  Account Summary  Currency：CNY \n", "----------------------------------------------------------------------------------------------------\n", "期初结存 Balance b/f：                   515963.29  基础保证金 Initial Margin：                10.00\n", "出 入 金 Deposit/Withdrawal：                 0.00  期末结存 Balance c/f：                 519906.26\n", "平仓盈亏 Realized P/L：                       0.00  质 押 金 Pledge Amount：                    0.00\n", "持仓盯市盈亏 MTM \n", "P/L：                     3950.00  客户权益 Client Equity：：             519906.26\n", "期权执行盈亏 Exercise P/L：                   0.00  货币质押保证金占用 FX Pledge Occ.：         0.00\n", "手 续 费 Commission：                         7.03  保证金占用 Margin Occupied：            36483.90\n", "行权手续费 Exercise Fee：                     0.00  交割保证金 Delivery Margin：                0.00\n", "交割手续费 Delivery Fee：                     0.00  多头期权市值 Market value(long)：           0.00\n", "货币质\n", " New FX Pledge：                      0.00  空头期权市值 Market value(short)：          0.00\n", "货币质出 FX Redemption：                      0.00  市值权益 Market value(equity)：        519906.26\n", "质押变化金额 Chg in Pledge Amt：              0.00  可用资金 Fund Avail.：                 483422.36\n", "权利金收入 Premium received：                 0.00  风 险 度 Risk Degree：                     7.02%\n", "权利金支出 Premium paid：                     0.00  应追加资金 Margin Call：                    0.0\n", "0\n", "货币质押变化金额 Chg in FX Pledge:            0.00\n", "\n", "                                                                          成交记录 Transaction Record \n", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|成交日期| 交易所 |       品种       |      合约      |买/卖|   投/保    |  成交价  | 手数 |   成交额   |       开平       |  手续费  |  平仓\n", "盈亏  |     权利金收支      |  成交编号  |  成交类型  |\n", "|  Date  |Exchange|     Product      |   Instrument   | B/S |    S/H     |   Price  | Lots |  Turnover  |       O/C        |   Fee    |Realized P/L|Premium Received/Paid|  Trans.No. | Trade Type |\n", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|********|郑商所  |纯碱              |     SA30\n", "1      |买   |投机        |  2499.000|     2|    99960.00|开                |      7.03|        0.00|                 0.00|111001240229|  普通成交  |\n", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|共   1条|        |                  |                      |            |          |     2|    99960.00|                  |      7.03|        0.00|   \n", "              0.00|            |            |\n", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "能源中心---INE  上期所---SHFE   中金所---CFFEX  大商所---DCE   郑商所---CZCE\n", "买---Buy   卖---Sell\n", "投机---Speculation  套保---Hedge  套利---Arbitrage  一般---General  交易---Trade  做市商---Market Maker\n", "开---Open 平---Close 平今---Close Today 强平---For\n", "ced Liquidation 平昨---Close Prev. 强减---Forced Reduction 本地强平---Local Forced Liquidation \n", "\n", "                                                                            持仓明细 Positions Detail\n", "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "| 交易所 |       品种       |      合约      |开仓日期 |  开仓编号  |   投/保    |买/卖|持\n", "仓量 |    开仓价     |     昨结算     |     结算价     |  浮动盈亏  |  盯市盈亏 |  保证金   |       期权市值       |\n", "|Exchange|     Product      |   Instrument   |Open Date|  Open No.  |    S/H     | B/S |Positon|Pos. Open Price|   Prev. Sttl   |Settlement Price| Accum. P/L |  MTM P/L  |  Margin   | Market Value(Options)|\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "--------------------------------\n", "| 郑商所 |       纯碱       |     SA301      | ********|111001240229|投机        |买   |      2|       2499.000|        2478.000|        2503.000|      160.00|     160.00|   12014.40|                  0.00|\n", "| 郑商所 |       尿素       |     UR301      | 20221109|110905008475|投机        |买   |      1|       2423.000|        2419.000|        2430.000|      140.00|     220.00|    5346.00|                  0.00|\n", "| 郑商所 |       尿素       |     UR301      | 202\n", "21109|110905008477|投机        |买   |      1|       2423.000|        2419.000|        2430.000|      140.00|     220.00|    5346.00|                  0.00|\n", "| 郑商所 |      新甲醇      |     MA301      | 20221031|103109468226|投机        |   卖|      3|       2470.000|        2572.000|        2505.000|    -1050.00|    2010.00|    8266.50|                  0.00|\n", "| 郑商所 |      新甲醇      |     MA301      | 20221031|103109468228|投机        |   卖|      1|       2470.000|        2572.000|     \n", "   2505.000|     -350.00|     670.00|    2755.50|                  0.00|\n", "| 郑商所 |      新甲醇      |     MA301      | 20221031|103109468230|投机        |   卖|      1|       2470.000|        2572.000|        2505.000|     -350.00|     670.00|    2755.50|                  0.00|\n", "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|共   6条|\n", "                  |                |         |            |            |     |      9|               |                |                |    -1310.00|    3950.00|   36483.90|                  0.00|\n", "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "能源中心---INE  上期所---SHFE   中金所---CFFEX  大商所---DCE   郑商所---CZCE\n", "买---Buy   卖---\n", "<PERSON>ll  \n", "投机---Speculation  套保---Hedge  套利---Arbitrage  一般---General  交易---Trade  做市商---Market Maker\n", "\n", "                                                                            持仓汇总 Positions\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|       品种       |      合约      |    买持     |    买均价   |     卖持     |    卖均\n", "    |  昨结算  |  今结算  |持仓盯市盈亏|  保证金占用   |  投/保     |   多头期权市值   |   空头期权市值    |\n", "|      Product     |   Instrument   |  Long Pos.  |Avg Buy Price|  Short Pos.  |Avg Sell Price|Prev. Sttl|Sttl Today|  MTM P/L   |Margin Occupied|    S/H     |Market Value(Long)|Market Value(Short)|\n", "---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "---------\n", "|       纯碱       |     SA301      |            2|     2499.000|             0|         0.000|  2478.000|  2503.000|      160.00|       12014.40|投机        |              0.00|               0.00|\n", "|       尿素       |     UR301      |            2|     2423.000|             0|         0.000|  2419.000|  2430.000|      440.00|       10692.00|投机        |              0.00|               0.00|\n", "|      新甲醇      |     MA301      |            0|        0.000|             5|      247\n", "0.000|  2572.000|  2505.000|     3350.00|       13777.50|投机        |              0.00|               0.00|\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|共     3条        |                |            4|             |             5|              |          |          |     3950.00|       36483.90|            |              0.00|           \n", "    0.00|\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "\n", "尊敬的投资者：  \n", "      根据交易所规定，能源中心2212期权合约系列到期日为11月14日，期权买方如申请行权，请提前准备行权所需资金，期权卖方有履约义务，公司将于到期日当天提前至15:15关闭行权客户的银期转账通道，请您做好资金和持仓的管理，充分了解期权行权履约相关风险。进行期货交易风险较大，请您在\n", "入市交易前，全面了解《期货交易风险说明书》及期货交易法律法规、交易所及期货公司的业务规则 ，谨慎投资！为保障保证金安全，您可以登录中国期货市场监控中心www.cfmmc.com的查询系统，检查期货公司结算单信息与查询结果是否一致，如有疑问，应向期货公司询问或向公司所在地证监局反映。您可以登录本公司网站www.cfc108.com，期货公司信息公示平台www.cfachina.org，查询期货公司公示信息.\n", "注：若有异议的，请在下一交易日开市前通过书面提出，如果您参与连续交易品种，请在下一个交易日连续交易开市前三十分钟通过书面提出，否则视为\n", "员菊说ニ载事项确认。\n", "\n", "客户或被授权人签章：                               中信建投期货有限公司\n", "\n", "\n", "\n", "\n"]}], "source": ["print(data[46][1])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["str = data[22][1].split(\"\\r\\n\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["str=[s.lstrip().rstrip() for s in str]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中投天琪期货有限公司\n", "制表时间 Creation Date：********\n", "----------------------------------------------------------------------------------------------------\n", "交易结算单(盯市) Settlement Statement(MTM)\n", "客户号 Client ID：  ********          客户名称 Client Name：李文君\n", "日期 Date：********\n", "\n", "\n", "尊敬的客户：\n", "期市有风险，入市须谨慎。为保障您的合法权益，请您登录中国期货市场监控中心(www.cfmmc.com)进行帐单查询。公司应急下单电话：0755-********/********。\n", "\n", "资金状况   资金账号：********  币种：人民币  Account Summary AccountID：******** Currency：CNY\n", "----------------------------------------------------------------------------------------------------\n", "期初结存 Balance b/f：                   518858.13  基础保证金 Initial Margin：                20.00\n", "出 入 金 Deposit/Withd\n", "rawal：           -412800.00  期末结存 Balance c/f：                 103298.13\n", "平仓盈亏 Realized P/L：                   -2700.00  质 押 金 Pledge Amount：                    0.00\n", "持仓盯市盈亏 MTM P/L：                        0.00  客户权益 Client Equity：：             103298.13\n", "期权执行盈亏 Exercise P/L：                   0.00  货币质押保证金占用 FX Pledge Occ.：         0.00\n", "手 续 费 Commission：                        60.00  保证金占用 Margin Occupied：                0.00\n", "行权手续费 E\n", "xercise Fee：                     0.00  交割保证金 Delivery Margin：                0.00\n", "交割手续费 Delivery Fee：                     0.00  多头期权市值 Market value(long)：           0.00\n", "货币质入 New FX Pledge：                      0.00  空头期权市值 Market value(short)：          0.00\n", "货币质出 FX Redemption：                      0.00  市值权益 Market value(equity)：        103298.13\n", "质押变化金额 Chg in Pledge Amt：              0.00  可用资金 Fund Avail.：                 103298.13\n", "权\n", "利金收入 Premium received：                 0.00  风 险 度 Risk Degree：                     0.00%\n", "权利金支出 Premium paid：                     0.00  应追加资金 Margin Call：                    0.00\n", "交割盈亏 Delivery P/L：                       0.00  货币质押变化金额 Chg in FX Pledge:          0.00\n", "\n", "出入金明细 Deposit/Withdrawal\n", "--------------------------------------------------------------------------------------------------------------------------\n", "-----------------------\n", "|发生日期|       出入金类型       |      入金      |      出金      |       汇率       |   资金账号   |                   说明                   |\n", "|  Date  |          Type          |    Deposit     |   Withdrawal   |   ExchangeRate   |   AccountID  |                   Note                   |\n", "-------------------------------------------------------------------------------------------------------------------------------------------------\n", "|********|银期转账              \n", "  |            0.00|       412800.00|            0.0000|********      |******** 银期转账自动凭证 出金 （人民币）412800|\n", "-------------------------------------------------------------------------------------------------------------------------------------------------\n", "|共   1条|                        |            0.00|       412800.00|                  |              |                                          |\n", "------------------------------------------------------------------------------------\n", "-------------------------------------------------------------\n", "出入金---Deposit/Withdrawal     银期转账---Bank-Futures Transfer    银期换汇---Bank-Futures FX Exchange\n", "\n", "成交记录 Transaction Record\n", "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|成交\n", "掌趞 投资单元 | 交易所 | 交易编码  |       品种       |      合约      |买/卖|   投/保    |  成交价  | 手数 |   成交额   |       开平       |  手续费  |  平仓盈亏  |     权利金收支      |     成交序号     |   资金账号   |\n", "|  Date  |InvestUnit|Exchange|tradingcode|     Product      |   Instrument   | B/S |    S/H     |   Price  | Lots |  Turnover  |       O/C        |   Fee    |Realized P/L|Premium Received/Paid|     Trans.No.    |   AccountID  |\n", "------------------------------------------------\n", "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机        |  2267.000|     1|    22670.00|  平              |      4.00|     -180.00|                 0.00|********01597233  |********      |\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机  \n", "      |  2267.000|     2|    45340.00|  平              |      8.00|     -360.00|                 0.00|********01597231  |********      |\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机        |  2267.000|     2|    45340.00|  平              |      8.00|     -360.00|                 0.00|********01597229  |********      |\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机        |  2267.000|     3|    68010.00|  平\n", "              |     12.00|     -540.00|                 0.00|********01597227  |********      |\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机        |  2267.000|     1|    22670.00|  平              |      4.00|     -180.00|                 0.00|********01597239  |********      |\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机        |  2267.000|     1|    22670.00|  平              |      4.00|     -180.00|   \n", "              0.00|********01597235  |********      |\n", "|********|********  |郑商所  |********   |甲醇              |     MA105      |买   |投机        |  2267.000|     5|   113350.00|  平              |     20.00|     -900.00|                 0.00|********01597237  |********      |\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "-----------\n", "|共   7条|          |        |           |                  |                      |            |          |    15|   340050.00|                  |     60.00|    -2700.00|                 0.00|                  |              |\n", "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "能源中心---INE  上期所---SHFE\n", "   中金所---CFFEX  大商所---DCE   郑商所---CZCE\n", "买---Buy   卖---Sell\n", "投机---Speculation  套保---Hedge  套利---Arbitrage  一般---General  交易---Trade  做市商---Market Maker\n", "开---Open 平---Close 平今---Close Today 强平---Forced Liquidation 平昨---Close Prev. 强减---Forced Reduction 本地强平---Local Forced Liquidation\n", "\n", "平仓明细 Position Closed\n", "-------------------------------------------------------------------------------------------\n", "-----------------------------------------------------------------------------------------------------------------------\n", "| 平仓日期 | 投资单元 | 交易所 | 交易编码  |       品种       |      合约      |开仓日期 |投/保|买/卖|   手数   |     开仓价    |     昨结算     |   成交价   |  平仓盈亏  |     权利金收支      |   资金账号   |\n", "|Close Date|InvestUnit|Exchange|tradingcode|      Product     |   Instrument   |Open Date| S/H | B/S |   Lots   |Pos. Open Price|   Prev. Sttl   |Trans. Price|Realized \n", "P/L|Premium Received/Paid|   AccountID  |\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|********  |********  |郑商所  |********   |甲醇              |MA105           |******** |投机 |买   |         1|       2269.000|        2249.000|    2267.000|     -180.00|                0.000|********      |\n", "|********  |********  |郑商所  |36\n", "846241   |甲醇              |MA105           |******** |投机 |买   |         5|       2269.000|        2249.000|    2267.000|     -900.00|                0.000|********      |\n", "|********  |********  |郑商所  |********   |甲醇              |MA105           |******** |投机 |买   |         1|       2269.000|        2249.000|    2267.000|     -180.00|                0.000|********      |\n", "|********  |********  |郑商所  |********   |甲醇              |MA105           |******** |投机 |买   |         1\n", "|       2269.000|        2249.000|    2267.000|     -180.00|                0.000|********      |\n", "|********  |********  |郑商所  |********   |甲醇              |MA105           |******** |投机 |买   |         2|       2269.000|        2249.000|    2267.000|     -360.00|                0.000|********      |\n", "|********  |********  |郑商所  |********   |甲醇              |MA105           |******** |投机 |买   |         2|       2269.000|        2249.000|    2267.000|     -360.00|                0.\n", "000|********      |\n", "|********  |********  |郑商所  |********   |甲醇              |MA105           |******** |投机 |买   |         3|       2269.000|        2249.000|    2267.000|     -540.00|                0.000|********      |\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "|共     7条|          |        |           |            \n", "      |                |         |     |     |        15|               |                |            |    -2700.00|                  0.00|              |\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n", "能源中心---INE  上期所---SHFE   中金所---CFFEX  大商所---DCE   郑商所---CZCE\n", "买---Buy   卖---Sell\n", "投机---Speculation  套保---He\n", "dge  套利---Arbitrage  一般---General  交易---Trade  做市商---Market Maker\n", "\n", "\n", "注：若您对结算单有异议，请务必于下一交易日开市前30分钟向我公司提出书面异议，否则视为对该日及该日之前结算单所载事项的完全确认。结算部联系电话：0755-********/********，传真:0755-********。\n", "\n", "\n"]}], "source": ["for s in str:\n", "    print(s)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import re"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def Account<PERSON><PERSON><PERSON><PERSON>(str):\n", "    pos=0\n", "    while True:\n", "        pos+=1\n", "        if str[pos][0:4]=='资金状况':\n", "            break\n", "    account={}\n", "    for i in range(12):\n", "        item=[]\n", "        lines=str[pos+2+i].split('  ')\n", "        lines=[s.replace('\\n', '').replace('%','') for s in lines if s!='']\n", "        if len(lines)==4:\n", "            key=re.sub(r\"[^一-龥]\", \"\", lines[0][:-1])\n", "            account[key] = float(lines[1])\n", "            key=re.sub(r\"[^一-龥]\", \"\", lines[2][:-1])\n", "            account[key] = float(lines[3])\n", "    return account"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'期初结存': 518858.13, '基础保证金': 20.0, '出入金': -412800.0, '期末结存': 103298.13, '平仓盈亏': -2700.0, '质押金': 0.0, '持仓盯市盈亏': 0.0, '客户权益': 103298.13, '期权执行盈亏': 0.0, '货币质押保证金占用': 0.0, '手续费': 60.0, '保证金占用': 0.0, '行权手续费': 0.0, '交割保证金': 0.0, '交割手续费': 0.0, '多头期权市值': 0.0, '货币质入': 0.0, '空头期权市值': 0.0, '货币质出': 0.0, '市值权益': 103298.13, '质押变化金额': 0.0, '可用资金': 103298.13, '权利金收入': 0.0, '风险度': 0.0}\n"]}], "source": ["print(Account<PERSON><PERSON><PERSON><PERSON>(str))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["pos=0\n", "while True:\n", "    pos+=1\n", "    if pos >= len(str):\n", "        break\n", "    if len(str[pos]) > 4 and str[pos][0:4]=='持仓汇总':\n", "        break"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['新甲醇', 'MA301', '8', '2751.500', '0', '0.000', '2792.000', '2855.000', '5040.00', '25124.00', '投机', '0.00', '0.00']\n"]}], "source": ["lines=str[pos+5].split('|')\n", "lines=[s.replace('\\n', '').rstrip().lstrip() for s in lines if s!='']\n", "print(lines)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def Positions(str):\n", "    trade=[]\n", "    i=0\n", "    while True:\n", "        lines=str[pos+5+i].split('|')\n", "        lines=[s.replace('\\n', '').rstrip().lstrip() for s in lines if s!='']\n", "        trade.append(lines)\n", "        i+=1\n", "        if str[pos+5+i][:3]=='---':\n", "            break\n", "    df=pd.DataFrame(trade, columns=['品种','合约','买持','买均价','卖持','卖均价','昨结算','今结算','持仓盯市盈亏','保证金占用','投/保','多头期权市值','空头期权市值'])\n", "    return df"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'pos' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn [15], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[39mprint\u001b[39m(Positions(\u001b[39mstr\u001b[39;49m))\n", "Cell \u001b[1;32mIn [14], line 5\u001b[0m, in \u001b[0;36mPositions\u001b[1;34m(str)\u001b[0m\n\u001b[0;32m      3\u001b[0m i\u001b[39m=\u001b[39m\u001b[39m0\u001b[39m\n\u001b[0;32m      4\u001b[0m \u001b[39mwhile\u001b[39;00m \u001b[39mTrue\u001b[39;00m:\n\u001b[1;32m----> 5\u001b[0m     lines\u001b[39m=\u001b[39m\u001b[39mstr\u001b[39m[pos\u001b[39m+\u001b[39m\u001b[39m5\u001b[39m\u001b[39m+\u001b[39mi]\u001b[39m.\u001b[39msplit(\u001b[39m'\u001b[39m\u001b[39m|\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m      6\u001b[0m     lines\u001b[39m=\u001b[39m[s\u001b[39m.\u001b[39mreplace(\u001b[39m'\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m'\u001b[39m, \u001b[39m'\u001b[39m\u001b[39m'\u001b[39m)\u001b[39m.\u001b[39mrstrip()\u001b[39m.\u001b[39mlstrip() \u001b[39mfor\u001b[39;00m s \u001b[39min\u001b[39;00m lines \u001b[39mif\u001b[39;00m s\u001b[39m!=\u001b[39m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m]\n\u001b[0;32m      7\u001b[0m     trade\u001b[39m.\u001b[39mappend(lines)\n", "\u001b[1;31mNameError\u001b[0m: name 'pos' is not defined"]}], "source": ["print(Positions(str))"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["def PositionClosed(str):\n", "    trade=[]\n", "    i=0\n", "    while True:\n", "        lines=str[pos+5+i].split('|')\n", "        lines=[s.replace('\\n', '').rstrip().lstrip() for s in lines if s!='']\n", "        trade.append(lines)\n", "        i+=1\n", "        if str[pos+5+i][:3]=='---':\n", "            break\n", "    df=pd.DataFrame(trade, columns=['平仓日期','交易所','品种','合约','开仓日期','买/卖','手数','开仓价','昨结算','成交价','平仓盈亏','权利金收支','成交类型'])\n", "    return df"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        平仓日期  交易所    品种      合约      开仓日期 买/卖 手数       开仓价        昨结算  \\\n", "0   20220425  大商所   铁矿石   i2209  20220419   卖  1   927.000    890.000   \n", "1   20220425  大商所   棕榈油   p2205  20211215   买  1  7648.000  12578.000   \n", "2   20220425  大商所   棕榈油   p2205  20211215   买  1  7648.000  12578.000   \n", "3   20220425  大商所   棕榈油   p2205  20211215   买  1  7648.000  12578.000   \n", "4   20220425  上期所    白银  ag2206  20220418   卖  2  5315.000   5103.000   \n", "5   20220425  上期所   螺纹钢  rb2210  20220401   卖  1  5072.000   5015.000   \n", "6   20220425  上期所   螺纹钢  rb2210  20220401   卖  1  5084.000   5015.000   \n", "7   20220425  上期所   螺纹钢  rb2210  20220425   买  1  4956.000   5015.000   \n", "8   20220425  上期所   螺纹钢  rb2210  20220425   买  1  4971.000   5015.000   \n", "9   20220425  上期所   螺纹钢  rb2210  20220425   买  1  4934.000   5015.000   \n", "10  20220425  上期所  热轧卷板  hc2210  20220425   买  1  5067.000   5117.000   \n", "11  20220425  郑商所    玻璃   FG209  20220422   卖  2  2035.000   2039.000   \n", "12  20220425  郑商所    玻璃   FG209  20220422   卖  1  2031.000   2039.000   \n", "13  20220425  郑商所    玻璃   FG209  20220422   卖  2  2034.000   2039.000   \n", "\n", "          成交价      平仓盈亏  权利金收支  成交类型  \n", "0     818.500  -7150.00  0.000  普通成交  \n", "1   13090.000  -5120.00  0.000  普通成交  \n", "2   13090.000  -5120.00  0.000  普通成交  \n", "3   13090.000  -5120.00  0.000  普通成交  \n", "4    5024.000  -2370.00  0.000  普通成交  \n", "5    4895.000  -1200.00  0.000  普通成交  \n", "6    4895.000  -1200.00  0.000  普通成交  \n", "7    4965.000    -90.00  0.000  普通成交  \n", "8    4958.000    130.00  0.000  普通成交  \n", "9    4924.000    100.00  0.000  普通成交  \n", "10   5074.000    -70.00  0.000  普通成交  \n", "11   1973.000  -2640.00  0.000  普通成交  \n", "12   1973.000  -1320.00  0.000  普通成交  \n", "13   1973.000  -2640.00  0.000  普通成交  \n"]}], "source": ["print(PositionClosed(str))"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [], "source": ["def TransactionRecord(str):\n", "    trade=[]\n", "    i=0\n", "    while True:\n", "        lines=str[pos+5+i].split('|')\n", "        lines=[s.replace('\\n', '').rstrip().lstrip() for s in lines if s!='']\n", "        trade.append(lines)\n", "        i+=1\n", "        if str[pos+5+i][:3]=='---':\n", "            break\n", "    df=pd.DataFrame(trade, columns=['成交日期','交易所','品种','合约','买/卖','投/保','成交价','手数','成交额','开平','手续费','平仓盈亏','权利金收支','成交序号','成交类型'])\n", "    return df"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        成交日期  交易所    品种      合约 买/卖 投/保        成交价 手数        成交额  开平    手续费  \\\n", "0   20220425  大商所   铁矿石   i2209   卖  投机    818.500  1   81850.00   平  16.38   \n", "1   20220425  大商所   棕榈油   p2205   买  投机  13090.000  3  392700.00   平   7.55   \n", "2   20220425  上期所    白银  ag2206   卖  投机   5024.000  2  150720.00  平昨   7.57   \n", "3   20220425  上期所   螺纹钢  rb2210   卖  投机   4956.000  1   49560.00   开   4.97   \n", "4   20220425  上期所   螺纹钢  rb2210   买  投机   4965.000  1   49650.00  平今   4.98   \n", "5   20220425  上期所   螺纹钢  rb2210   卖  投机   4971.000  1   49710.00   开   4.98   \n", "6   20220425  上期所   螺纹钢  rb2210   买  投机   4958.000  1   49580.00  平今   4.97   \n", "7   20220425  上期所   螺纹钢  rb2210   卖  投机   4934.000  1   49340.00   开   4.95   \n", "8   20220425  上期所   螺纹钢  rb2210   买  投机   4924.000  1   49240.00  平今   4.94   \n", "9   20220425  上期所   螺纹钢  rb2210   卖  投机   4895.000  2   97900.00  平昨   9.82   \n", "10  20220425  上期所  热轧卷板  hc2210   卖  投机   5067.000  1   50670.00   开   5.08   \n", "11  20220425  上期所  热轧卷板  hc2210   买  投机   5074.000  1   50740.00  平今   5.09   \n", "12  20220425  上期所  热轧卷板  hc2210   卖  投机   4966.000  3  148980.00   开  14.94   \n", "13  20220425  上期所  天然橡胶  ru2209   卖  投机  12845.000  1  128450.00   开   3.02   \n", "14  20220425  郑商所    玻璃   FG209   卖  投机   1973.000  2   78920.00   平  12.02   \n", "15  20220425  郑商所    玻璃   FG209   卖  投机   1973.000  1   39460.00   平   6.01   \n", "16  20220425  郑商所    玻璃   FG209   卖  投机   1973.000  2   78920.00   平  12.02   \n", "\n", "         平仓盈亏 权利金收支          成交序号  成交类型  \n", "0    -7150.00  0.00     101974450  普通成交  \n", "1   -15360.00  0.00     102457015  普通成交  \n", "2    -2370.00  0.00  000002802321  普通成交  \n", "3        0.00  0.00  000000350486  普通成交  \n", "4      -90.00  0.00  000000441721  普通成交  \n", "5        0.00  0.00  000000958320  普通成交  \n", "6      130.00  0.00  000001034885  普通成交  \n", "7        0.00  0.00  000001324330  普通成交  \n", "8      100.00  0.00  000001449104  普通成交  \n", "9    -2400.00  0.00  000001518974  普通成交  \n", "10       0.00  0.00  000000506967  普通成交  \n", "11     -70.00  0.00  000000558371  普通成交  \n", "12       0.00  0.00  000002225010  普通成交  \n", "13       0.00  0.00  000002238908  普通成交  \n", "14   -2640.00  0.00  042502883639  普通成交  \n", "15   -1320.00  0.00  042502883640  普通成交  \n", "16   -2640.00  0.00  042502883641  普通成交  \n"]}], "source": ["print(TransactionRecord(str))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "# import torch.nn as nn"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["a = torch.empty([2, 192])\n", "a = a.fill_(385)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.,\n", "        385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385., 385.])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["a.index_select(0, )"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from pyqlab.spectre.parallel import Rolling"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.,  1.,  2.,  3.],\n", "        [ 4.,  5.,  6.,  7.],\n", "        [ 8.,  9., 10., 11.]])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["a = torch.Tensor([[0,1,2,3], [4,5,6,7], [8,9,10,11]])\n", "a"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "INDICES element is out of DATA bounds, id=-2 axis_dim=4", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn [56], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m a\u001b[39m.\u001b[39mindex_select(\u001b[39m1\u001b[39m, torch\u001b[39m.\u001b[39marange(\u001b[39m-\u001b[39m\u001b[39m2\u001b[39m, \u001b[39m0\u001b[39m))\n", "\u001b[1;31mRuntimeError\u001b[0m: INDICES element is out of DATA bounds, id=-2 axis_dim=4"]}], "source": ["a.index_select(1, torch.arange(-2, 0))"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 2.,  3.],\n", "        [ 6.,  7.],\n", "        [10., 11.]])"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["a.index_select(1, torch.arange(a.shape[1]-2, a.shape[1]))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[[nan, nan,  0.],\n", "         [nan,  0.,  1.],\n", "         [ 0.,  1.,  2.],\n", "         [ 1.,  2.,  3.]],\n", "\n", "        [[nan, nan,  4.],\n", "         [nan,  4.,  5.],\n", "         [ 4.,  5.,  6.],\n", "         [ 5.,  6.,  7.]],\n", "\n", "        [[nan, nan,  8.],\n", "         [nan,  8.,  9.],\n", "         [ 8.,  9., 10.],\n", "         [ 9., 10., 11.]]])\n", "torch.<PERSON><PERSON>([3, 4, 3])\n"]}], "source": ["roll = Rolling(a, 3)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[nan, nan, 0., 1.],\n", "        [nan, nan, 4., 5.],\n", "        [nan, nan, 8., 9.]])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.first()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.,  1.,  2.,  3.],\n", "        [ 4.,  5.,  6.,  7.],\n", "        [ 8.,  9., 10., 11.]])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.last()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[nan, nan,  3.,  6.],\n", "        [nan, nan, 15., 18.],\n", "        [nan, nan, 27., 30.]])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.sum()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.,  1.,  3.,  6.],\n", "        [ 4.,  9., 15., 18.],\n", "        [ 8., 17., 27., 30.]])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.nansum()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[nan, nan,  1.,  2.],\n", "        [nan, nan,  5.,  6.],\n", "        [nan, nan,  9., 10.]])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.mean()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.0000,  0.5000,  1.0000,  2.0000],\n", "        [ 4.0000,  4.5000,  5.0000,  6.0000],\n", "        [ 8.0000,  8.5000,  9.0000, 10.0000]])"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.nanmean()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[   nan,    nan, 0.6667, 0.6667],\n", "        [   nan,    nan, 0.6667, 0.6667],\n", "        [   nan,    nan, 0.6667, 0.6667]])"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.var()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0000, 0.2500, 0.6667, 0.6667],\n", "        [0.0000, 0.2500, 0.6667, 0.6667],\n", "        [0.0000, 0.2500, 0.6667, 0.6667]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.nanvar()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[nan, nan,  2.,  3.],\n", "        [nan, nan,  6.,  7.],\n", "        [nan, nan, 10., 11.]])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.max()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.,  1.,  2.,  3.],\n", "        [ 4.,  5.,  6.,  7.],\n", "        [ 8.,  9., 10., 11.]])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.nanmax()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[nan, nan, 0., 1.],\n", "        [nan, nan, 4., 5.],\n", "        [nan, nan, 8., 9.]])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.min()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0., 0., 0., 1.],\n", "        [4., 4., 4., 5.],\n", "        [8., 8., 8., 9.]])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.nanmin()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[   nan,    nan, 0.8165, 0.8165],\n", "        [   nan,    nan, 0.8165, 0.8165],\n", "        [   nan,    nan, 0.8165, 0.8165]])"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.std()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0000, 0.5000, 0.8165, 0.8165],\n", "        [0.0000, 0.5000, 0.8165, 0.8165],\n", "        [0.0000, 0.5000, 0.8165, 0.8165]])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["roll.nanstd()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["pytorch中文文档（https://pytorch-cn.readthedocs.io/zh/latest/）\n", "\n", "torchvision：包含了目前流行的数据集，模型结构和常用的图片转换工具。\n", "1.数据集：MNIST\n", "   https://pytorch-cn.readthedocs.io/zh/latest/torchvision/torchvision-datasets/\n", "2.模型：\n", "   https://pytorch-cn.readthedocs.io/zh/latest/torchvision/torchvision-models/"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# images lib\n", "import torchvision\n", "from torchvision import datasets,transforms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = torchvision.models.vgg16(pretrained=True)\n", "model"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["TORCHTEXT.DATASETS\n", "\n", "所有数据集都是的子类torchtext.data.Dataset，它们继承自torch.utils.data.Dataset，\n", "\n", "并且具有split和 iters实现的方法。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torchtext\n", "from torchtext.vocab import Glove"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["codes = ['A', 'AG', 'AL', 'AP', 'AU', 'B', 'B<PERSON>', 'C', 'CF', 'C<PERSON>', 'CS', 'CU', 'CY', 'EB', 'EG', 'FG', 'HC', 'I', 'IC', 'IF', 'IH', 'J', 'JD', 'J<PERSON>', 'L', 'LH', 'M', 'MA', 'NI', 'NR', 'OI', 'P', 'PB', 'PF', 'PG', 'PK', 'PP', 'RB', 'RM', 'RR', 'RU', 'SA', 'SC', 'SF', 'SM', 'SN', 'SP', 'SR', 'SS', 'TA', 'UR', 'V', 'Y', 'ZN']\n", "base_l = [0 for _ in range(len(codes))]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["54\n"]}], "source": ["print(len(codes))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["code_to_int = dict((c, i) for i, c in enumerate(codes))\n", "int_to_code = dict((c, i) for i, c in enumerate(codes))\n", "\n", "onehot_list = [0 for _ in range(len(codes))]\n", "onehot_encode = list()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_to_int"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.cuda.device_count()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1;31mInit signature:\u001b[0m\n", "\u001b[0mnn\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mLSTMCell\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0minput_size\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mint\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mhidden_size\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mint\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mbias\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mbool\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mdevice\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m    \u001b[0mdtype\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\n", "\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mDocstring:\u001b[0m     \n", "A long short-term memory (LSTM) cell.\n", "\n", ".. math::\n", "\n", "    \\begin{array}{ll}\n", "    i = \\sigma(W_{ii} x + b_{ii} + W_{hi} h + b_{hi}) \\\\\n", "    f = \\sigma(W_{if} x + b_{if} + W_{hf} h + b_{hf}) \\\\\n", "    g = \\tanh(W_{ig} x + b_{ig} + W_{hg} h + b_{hg}) \\\\\n", "    o = \\sigma(W_{io} x + b_{io} + W_{ho} h + b_{ho}) \\\\\n", "    c' = f * c + i * g \\\\\n", "    h' = o * \\tanh(c') \\\\\n", "    \\end{array}\n", "\n", "where :math:`\\sigma` is the sigmoid function, and :math:`*` is the Hadamard product.\n", "\n", "Args:\n", "    input_size: The number of expected features in the input `x`\n", "    hidden_size: The number of features in the hidden state `h`\n", "    bias: If ``False``, then the layer does not use bias weights `b_ih` and\n", "        `b_hh`. <PERSON><PERSON><PERSON>: ``True``\n", "\n", "Inputs: input, (h_0, c_0)\n", "    - **input** of shape `(batch, input_size)`: tensor containing input features\n", "    - **h_0** of shape `(batch, hidden_size)`: tensor containing the initial hidden\n", "      state for each element in the batch.\n", "    - **c_0** of shape `(batch, hidden_size)`: tensor containing the initial cell state\n", "      for each element in the batch.\n", "\n", "      If `(h_0, c_0)` is not provided, both **h_0** and **c_0** default to zero.\n", "\n", "Outputs: (h_1, c_1)\n", "    - **h_1** of shape `(batch, hidden_size)`: tensor containing the next hidden state\n", "      for each element in the batch\n", "    - **c_1** of shape `(batch, hidden_size)`: tensor containing the next cell state\n", "      for each element in the batch\n", "\n", "Attributes:\n", "    weight_ih: the learnable input-hidden weights, of shape\n", "        `(4*hidden_size, input_size)`\n", "    weight_hh: the learnable hidden-hidden weights, of shape\n", "        `(4*hidden_size, hidden_size)`\n", "    bias_ih: the learnable input-hidden bias, of shape `(4*hidden_size)`\n", "    bias_hh: the learnable hidden-hidden bias, of shape `(4*hidden_size)`\n", "\n", ".. note::\n", "    All the weights and biases are initialized from :math:`\\mathcal{U}(-\\sqrt{k}, \\sqrt{k})`\n", "    where :math:`k = \\frac{1}{\\text{hidden\\_size}}`\n", "\n", "Examples::\n", "\n", "    >>> rnn = nn.L<PERSON><PERSON>ell(10, 20) # (input_size, hidden_size)\n", "    >>> input = torch.randn(2, 3, 10) # (time_steps, batch, input_size)\n", "    >>> hx = torch.randn(3, 20) # (batch, hidden_size)\n", "    >>> cx = torch.randn(3, 20)\n", "    >>> output = []\n", "    >>> for i in range(input.size()[0]):\n", "            hx, cx = rnn(input[i], (hx, cx))\n", "            output.append(hx)\n", "    >>> output = torch.stack(output, dim=0)\n", "\u001b[1;31mInit docstring:\u001b[0m Initializes internal Module state, shared by both nn.Module and ScriptModule.\n", "\u001b[1;31mFile:\u001b[0m           d:\\anaconda3\\lib\\site-packages\\torch\\nn\\modules\\rnn.py\n", "\u001b[1;31mType:\u001b[0m           type\n", "\u001b[1;31mSubclasses:\u001b[0m     \n"]}], "source": ["nn.LSTMCell?"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([32, 100, 29])\n"]}], "source": ["conv1 = nn.Conv1d(in_channels=256, out_channels=100, kernel_size=7)\n", "input = torch.randn(32,35,256)\n", "# batch_size x text_len x embedding_size -> batch_size x embedding_size x text_len\n", "input = input.permute(0,2,1)\n", "out = conv1(input)\n", "print(out.size())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}