{"trial_data": [["{\n  \"stub\": false,\n  \"trainable_name\": \"<PERSON><PERSON>\",\n  \"trial_id\": \"e4287_00000\",\n  \"storage\": {\n    \"_type\": \"CLOUDPICKLE_FALLBACK\",\n    \"value\": \"800595ea020000000000008c1b7261792e747261696e2e5f696e7465726e616c2e73746f72616765948c0e53746f72616765436f6e746578749493942981947d94288c12637573746f6d5f66735f70726f766964656494898c136578706572696d656e745f6469725f6e616d65948c1750504f5f323032352d30312d30365f31342d30322d3536948c0e747269616c5f6469725f6e616d65948c3650504f5f6d61726b657474696d696e675f656e765f65343238375f30303030305f305f323032352d30312d30365f31342d30332d3033948c1863757272656e745f636865636b706f696e745f696e646578944affffffff8c0b73796e635f636f6e666967948c097261792e747261696e948c0a53796e63436f6e6669679493942981947d94288c0b73796e635f706572696f64944d2c018c0c73796e635f74696d656f7574944d08078c0e73796e635f61727469666163747394898c1c73796e635f6172746966616374735f6f6e5f636865636b706f696e7494888c0a75706c6f61645f646972948c0a44455052454341544544948c0673796e6365729468168c1273796e635f6f6e5f636865636b706f696e7494681675628c1273746f726167655f66696c6573797374656d948c0b70796172726f772e5f6673948c1c4c6f63616c46696c6553797374656d2e5f7265636f6e7374727563749493947d948c087573655f6d6d6170948973859452948c0f73746f726167655f66735f70617468948c22453a2f6c61622f526f626f5175616e742f70796c61622f7261795f726573756c747394681768008c115f46696c6573797374656d53796e6365729493942981947d94286819682068114d2c0168124d08078c116c6173745f73796e635f75705f74696d659447fff00000000000008c136c6173745f73796e635f646f776e5f74696d659447fff00000000000008c0d5f73796e635f70726f63657373944e8c0c5f63757272656e745f636d64944e75628c0a5f74696d657374616d70948c13323032352d30312d30365f31342d30332d30339475622e\"\n  },\n  \"config\": {\n    \"extra_python_environs_for_driver\": {},\n    \"extra_python_environs_for_worker\": {},\n    \"placement_strategy\": \"PACK\",\n    \"num_gpus\": 0,\n    \"_fake_gpus\": false,\n    \"num_cpus_for_main_process\": 1,\n    \"eager_tracing\": true,\n    \"eager_max_retraces\": 20,\n    \"tf_session_args\": {\n      \"intra_op_parallelism_threads\": 2,\n      \"inter_op_parallelism_threads\": 2,\n      \"gpu_options\": {\n        \"allow_growth\": true\n      },\n      \"log_device_placement\": false,\n      \"device_count\": {\n        \"CPU\": 1\n      },\n      \"allow_soft_placement\": true\n    },\n    \"local_tf_session_args\": {\n      \"intra_op_parallelism_threads\": 8,\n      \"inter_op_parallelism_threads\": 8\n    },\n    \"torch_compile_learner\": false,\n    \"torch_compile_learner_what_to_compile\": \"forward_train\",\n    \"torch_compile_learner_dynamo_backend\": \"inductor\",\n    \"torch_compile_learner_dynamo_mode\": null,\n    \"torch_compile_worker\": false,\n    \"torch_compile_worker_dynamo_backend\": \"onnxrt\",\n    \"torch_compile_worker_dynamo_mode\": null,\n    \"torch_ddp_kwargs\": {},\n    \"torch_skip_nan_gradients\": false,\n    \"enable_rl_module_and_learner\": false,\n    \"enable_env_runner_and_connector_v2\": false,\n    \"env\": \"markettiming_env\",\n    \"env_config\": {\n      \"name\": \"MarketTimingEnv\",\n      \"version\": \"v1\",\n      \"initial_amount\": 10000000,\n      \"gamma\": 0.98,\n      \"mode\": \"train\",\n      \"split_percent\": 0.9,\n      \"data_path\": \"E:/lab/RoboQuant/pylab/data\",\n      \"data_file\": \"ft_all.all.00170516142453003.csv\"\n    },\n    \"observation_space\": null,\n    \"action_space\": null,\n    \"clip_rewards\": null,\n    \"normalize_actions\": true,\n    \"clip_actions\": false,\n    \"_is_atari\": null,\n    \"disable_env_checking\": false,\n    \"env_task_fn\": null,\n    \"render_env\": false,\n    \"action_mask_key\": \"action_mask\",\n    \"env_runner_cls\": null,\n    \"num_env_runners\": 8,\n    \"num_envs_per_env_runner\": 1,\n    \"num_cpus_per_env_runner\": 1,\n    \"num_gpus_per_env_runner\": 0,\n    \"custom_resources_per_env_runner\": {},\n    \"validate_env_runners_after_construction\": true,\n    \"max_requests_in_flight_per_env_runner\": 2,\n    \"sample_timeout_s\": 60.0,\n    \"_env_to_module_connector\": null,\n    \"add_default_connectors_to_env_to_module_pipeline\": true,\n    \"_module_to_env_connector\": null,\n    \"add_default_connectors_to_module_to_env_pipeline\": true,\n    \"episode_lookback_horizon\": 1,\n    \"rollout_fragment_length\": \"auto\",\n    \"batch_mode\": \"truncate_episodes\",\n    \"compress_observations\": false,\n    \"remote_worker_envs\": false,\n    \"remote_env_batch_wait_ms\": 0,\n    \"enable_tf1_exec_eagerly\": false,\n    \"sample_collector\": {\n      \"_type\": \"CLOUDPICKLE_FALLBACK\",\n      \"value\": \"80059551000000000000008c357261792e726c6c69622e6576616c756174696f6e2e636f6c6c6563746f72732e73696d706c655f6c6973745f636f6c6c6563746f72948c1353696d706c654c697374436f6c6c6563746f729493942e\"\n    },\n    \"preprocessor_pref\": \"deepmind\",\n    \"observation_filter\": \"NoFilter\",\n    \"update_worker_filter_stats\": true,\n    \"use_worker_filter_stats\": true,\n    \"sampler_perf_stats_ema_coef\": null,\n    \"num_learners\": 0,\n    \"num_gpus_per_learner\": 0,\n    \"num_cpus_per_learner\": 1,\n    \"local_gpu_idx\": 0,\n    \"gamma\": 0.99,\n    \"lr\": 0.0001,\n    \"grad_clip\": null,\n    \"grad_clip_by\": \"global_norm\",\n    \"train_batch_size_per_learner\": null,\n    \"train_batch_size\": 4000,\n    \"num_epochs\": 30,\n    \"minibatch_size\": 128,\n    \"shuffle_batch_per_epoch\": true,\n    \"model\": {\n      \"fcnet_hiddens\": [\n        256,\n        256\n      ],\n      \"fcnet_activation\": \"tanh\",\n      \"fcnet_weights_initializer\": null,\n      \"fcnet_weights_initializer_config\": null,\n      \"fcnet_bias_initializer\": null,\n      \"fcnet_bias_initializer_config\": null,\n      \"conv_filters\": null,\n      \"conv_activation\": \"relu\",\n      \"conv_kernel_initializer\": null,\n      \"conv_kernel_initializer_config\": null,\n      \"conv_bias_initializer\": null,\n      \"conv_bias_initializer_config\": null,\n      \"conv_transpose_kernel_initializer\": null,\n      \"conv_transpose_kernel_initializer_config\": null,\n      \"conv_transpose_bias_initializer\": null,\n      \"conv_transpose_bias_initializer_config\": null,\n      \"post_fcnet_hiddens\": [],\n      \"post_fcnet_activation\": \"relu\",\n      \"post_fcnet_weights_initializer\": null,\n      \"post_fcnet_weights_initializer_config\": null,\n      \"post_fcnet_bias_initializer\": null,\n      \"post_fcnet_bias_initializer_config\": null,\n      \"free_log_std\": false,\n      \"log_std_clip_param\": 20.0,\n      \"no_final_linear\": false,\n      \"vf_share_layers\": false,\n      \"use_lstm\": false,\n      \"max_seq_len\": 20,\n      \"lstm_cell_size\": 256,\n      \"lstm_use_prev_action\": false,\n      \"lstm_use_prev_reward\": false,\n      \"lstm_weights_initializer\": null,\n      \"lstm_weights_initializer_config\": null,\n      \"lstm_bias_initializer\": null,\n      \"lstm_bias_initializer_config\": null,\n      \"_time_major\": false,\n      \"use_attention\": false,\n      \"attention_num_transformer_units\": 1,\n      \"attention_dim\": 64,\n      \"attention_num_heads\": 1,\n      \"attention_head_dim\": 32,\n      \"attention_memory_inference\": 50,\n      \"attention_memory_training\": 50,\n      \"attention_position_wise_mlp_dim\": 32,\n      \"attention_init_gru_gate_bias\": 2.0,\n      \"attention_use_n_prev_actions\": 0,\n      \"attention_use_n_prev_rewards\": 0,\n      \"framestack\": true,\n      \"dim\": 84,\n      \"grayscale\": false,\n      \"zero_mean\": true,\n      \"custom_model\": null,\n      \"custom_model_config\": {},\n      \"custom_action_dist\": null,\n      \"custom_preprocessor\": null,\n      \"encoder_latent_dim\": null,\n      \"always_check_shapes\": false,\n      \"lstm_use_prev_action_reward\": -1,\n      \"_use_default_native_models\": -1,\n      \"_disable_preprocessor_api\": false,\n      \"_disable_action_flattening\": false\n    },\n    \"_learner_connector\": null,\n    \"add_default_connectors_to_learner_pipeline\": true,\n    \"learner_config_dict\": {},\n    \"optimizer\": {\n      \"type\": \"SGD\",\n      \"lr\": 0.01,\n      \"momentum\": 0.9\n    },\n    \"_learner_class\": null,\n    \"explore\": true,\n    \"exploration_config\": {\n      \"type\": \"StochasticSampling\"\n    },\n    \"count_steps_by\": \"env_steps\",\n    \"policy_map_capacity\": 100,\n    \"policy_mapping_fn\": {\n      \"_type\": \"CLOUDPICKLE_FALLBACK\",\n      \"value\": \"80059557000000000000008c257261792e726c6c69622e616c676f726974686d732e616c676f726974686d5f636f6e666967948c29416c676f726974686d436f6e6669672e44454641554c545f504f4c4943595f4d415050494e475f464e9493942e\"\n    },\n    \"policies_to_train\": null,\n    \"policy_states_are_swappable\": false,\n    \"observation_fn\": null,\n    \"input_read_method\": \"read_parquet\",\n    \"input_read_method_kwargs\": {},\n    \"input_read_schema\": {},\n    \"input_read_episodes\": false,\n    \"input_read_sample_batches\": false,\n    \"input_read_batch_size\": null,\n    \"input_filesystem\": null,\n    \"input_filesystem_kwargs\": {},\n    \"input_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"input_spaces_jsonable\": true,\n    \"materialize_data\": false,\n    \"materialize_mapped_data\": true,\n    \"map_batches_kwargs\": {},\n    \"iter_batches_kwargs\": {},\n    \"prelearner_class\": null,\n    \"prelearner_buffer_class\": null,\n    \"prelearner_buffer_kwargs\": {},\n    \"prelearner_module_synch_period\": 10,\n    \"dataset_num_iters_per_learner\": null,\n    \"input_config\": {},\n    \"actions_in_input_normalized\": false,\n    \"postprocess_inputs\": false,\n    \"shuffle_buffer_size\": 0,\n    \"output\": null,\n    \"output_config\": {},\n    \"output_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"output_max_file_size\": 67108864,\n    \"output_max_rows_per_file\": null,\n    \"output_write_method\": \"write_parquet\",\n    \"output_write_method_kwargs\": {},\n    \"output_filesystem\": null,\n    \"output_filesystem_kwargs\": {},\n    \"output_write_episodes\": true,\n    \"offline_sampling\": false,\n    \"evaluation_interval\": null,\n    \"evaluation_duration\": 10,\n    \"evaluation_duration_unit\": \"episodes\",\n    \"evaluation_sample_timeout_s\": 120.0,\n    \"evaluation_parallel_to_training\": false,\n    \"evaluation_force_reset_envs_before_iteration\": true,\n    \"evaluation_config\": null,\n    \"off_policy_estimation_methods\": {},\n    \"ope_split_batch_by_episode\": true,\n    \"evaluation_num_env_runners\": 0,\n    \"in_evaluation\": false,\n    \"sync_filters_on_rollout_workers_timeout_s\": 10.0,\n    \"keep_per_episode_custom_metrics\": false,\n    \"metrics_episode_collection_timeout_s\": 60.0,\n    \"metrics_num_episodes_for_smoothing\": 100,\n    \"min_time_s_per_iteration\": null,\n    \"min_train_timesteps_per_iteration\": 0,\n    \"min_sample_timesteps_per_iteration\": 0,\n    \"log_gradients\": true,\n    \"export_native_model_files\": false,\n    \"checkpoint_trainable_policies_only\": false,\n    \"logger_creator\": null,\n    \"logger_config\": null,\n    \"log_level\": \"WARN\",\n    \"log_sys_usage\": true,\n    \"fake_sampler\": false,\n    \"seed\": null,\n    \"_run_training_always_in_thread\": false,\n    \"_evaluation_parallel_to_training_wo_thread\": false,\n    \"restart_failed_env_runners\": true,\n    \"ignore_env_runner_failures\": false,\n    \"max_num_env_runner_restarts\": 1000,\n    \"delay_between_env_runner_restarts_s\": 60.0,\n    \"restart_failed_sub_environments\": false,\n    \"num_consecutive_env_runner_failures_tolerance\": 100,\n    \"env_runner_health_probe_timeout_s\": 30.0,\n    \"env_runner_restore_timeout_s\": 1800.0,\n    \"_model_config\": {},\n    \"_rl_module_spec\": null,\n    \"_AlgorithmConfig__prior_exploration_config\": null,\n    \"algorithm_config_overrides_per_module\": {},\n    \"_per_module_overrides\": {},\n    \"_torch_grad_scaler_class\": null,\n    \"_torch_lr_scheduler_classes\": null,\n    \"_tf_policy_handles_more_than_one_loss\": false,\n    \"_disable_preprocessor_api\": false,\n    \"_disable_action_flattening\": false,\n    \"_disable_initialize_loss_from_dummy_batch\": false,\n    \"_dont_auto_sync_env_runner_states\": false,\n    \"enable_connectors\": -1,\n    \"simple_optimizer\": -1,\n    \"policy_map_cache\": -1,\n    \"worker_cls\": -1,\n    \"synchronize_filters\": -1,\n    \"enable_async_evaluation\": -1,\n    \"custom_async_evaluation_function\": -1,\n    \"_enable_rl_module_api\": -1,\n    \"auto_wrap_old_gym_envs\": -1,\n    \"always_attach_evaluation_results\": -1,\n    \"replay_sequence_length\": null,\n    \"_disable_execution_plan_api\": -1,\n    \"lr_schedule\": null,\n    \"use_critic\": true,\n    \"use_gae\": true,\n    \"use_kl_loss\": true,\n    \"kl_coeff\": 0.2,\n    \"kl_target\": 0.01,\n    \"vf_loss_coeff\": 1.0,\n    \"entropy_coeff\": 0.0,\n    \"entropy_coeff_schedule\": null,\n    \"clip_param\": 0.3,\n    \"vf_clip_param\": 10.0,\n    \"sgd_minibatch_size\": -1,\n    \"vf_share_layers\": -1,\n    \"lambda\": 1.0,\n    \"input\": \"sampler\",\n    \"policies\": {\n      \"default_policy\": [\n        null,\n        null,\n        null,\n        null\n      ]\n    },\n    \"callbacks\": {\n      \"_type\": \"CLOUDPICKLE_FALLBACK\",\n      \"value\": \"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\"\n    },\n    \"create_env_on_driver\": true,\n    \"custom_eval_function\": null,\n    \"framework\": \"torch\"\n  },\n  \"_Trial__unresolved_config\": {\n    \"extra_python_environs_for_driver\": {},\n    \"extra_python_environs_for_worker\": {},\n    \"placement_strategy\": \"PACK\",\n    \"num_gpus\": 0,\n    \"_fake_gpus\": false,\n    \"num_cpus_for_main_process\": 1,\n    \"eager_tracing\": true,\n    \"eager_max_retraces\": 20,\n    \"tf_session_args\": {\n      \"intra_op_parallelism_threads\": 2,\n      \"inter_op_parallelism_threads\": 2,\n      \"gpu_options\": {\n        \"allow_growth\": true\n      },\n      \"log_device_placement\": false,\n      \"device_count\": {\n        \"CPU\": 1\n      },\n      \"allow_soft_placement\": true\n    },\n    \"local_tf_session_args\": {\n      \"intra_op_parallelism_threads\": 8,\n      \"inter_op_parallelism_threads\": 8\n    },\n    \"torch_compile_learner\": false,\n    \"torch_compile_learner_what_to_compile\": \"forward_train\",\n    \"torch_compile_learner_dynamo_backend\": \"inductor\",\n    \"torch_compile_learner_dynamo_mode\": null,\n    \"torch_compile_worker\": false,\n    \"torch_compile_worker_dynamo_backend\": \"onnxrt\",\n    \"torch_compile_worker_dynamo_mode\": null,\n    \"torch_ddp_kwargs\": {},\n    \"torch_skip_nan_gradients\": false,\n    \"enable_rl_module_and_learner\": false,\n    \"enable_env_runner_and_connector_v2\": false,\n    \"env\": \"markettiming_env\",\n    \"env_config\": {\n      \"name\": \"MarketTimingEnv\",\n      \"version\": \"v1\",\n      \"initial_amount\": 10000000,\n      \"gamma\": 0.98,\n      \"mode\": \"train\",\n      \"split_percent\": 0.9,\n      \"data_path\": \"E:/lab/RoboQuant/pylab/data\",\n      \"data_file\": \"ft_all.all.00170516142453003.csv\"\n    },\n    \"observation_space\": null,\n    \"action_space\": null,\n    \"clip_rewards\": null,\n    \"normalize_actions\": true,\n    \"clip_actions\": false,\n    \"_is_atari\": null,\n    \"disable_env_checking\": false,\n    \"env_task_fn\": null,\n    \"render_env\": false,\n    \"action_mask_key\": \"action_mask\",\n    \"env_runner_cls\": null,\n    \"num_env_runners\": 8,\n    \"num_envs_per_env_runner\": 1,\n    \"num_cpus_per_env_runner\": 1,\n    \"num_gpus_per_env_runner\": 0,\n    \"custom_resources_per_env_runner\": {},\n    \"validate_env_runners_after_construction\": true,\n    \"max_requests_in_flight_per_env_runner\": 2,\n    \"sample_timeout_s\": 60.0,\n    \"_env_to_module_connector\": null,\n    \"add_default_connectors_to_env_to_module_pipeline\": true,\n    \"_module_to_env_connector\": null,\n    \"add_default_connectors_to_module_to_env_pipeline\": true,\n    \"episode_lookback_horizon\": 1,\n    \"rollout_fragment_length\": \"auto\",\n    \"batch_mode\": \"truncate_episodes\",\n    \"compress_observations\": false,\n    \"remote_worker_envs\": false,\n    \"remote_env_batch_wait_ms\": 0,\n    \"enable_tf1_exec_eagerly\": false,\n    \"sample_collector\": [\n      \"__ref_ph\",\n      \"f176708f\"\n    ],\n    \"preprocessor_pref\": \"deepmind\",\n    \"observation_filter\": \"NoFilter\",\n    \"update_worker_filter_stats\": true,\n    \"use_worker_filter_stats\": true,\n    \"sampler_perf_stats_ema_coef\": null,\n    \"num_learners\": 0,\n    \"num_gpus_per_learner\": 0,\n    \"num_cpus_per_learner\": 1,\n    \"local_gpu_idx\": 0,\n    \"gamma\": 0.99,\n    \"lr\": 0.0001,\n    \"grad_clip\": null,\n    \"grad_clip_by\": \"global_norm\",\n    \"train_batch_size_per_learner\": null,\n    \"train_batch_size\": 4000,\n    \"num_epochs\": 30,\n    \"minibatch_size\": 128,\n    \"shuffle_batch_per_epoch\": true,\n    \"model\": {\n      \"fcnet_hiddens\": [\n        256,\n        256\n      ],\n      \"fcnet_activation\": \"tanh\",\n      \"fcnet_weights_initializer\": null,\n      \"fcnet_weights_initializer_config\": null,\n      \"fcnet_bias_initializer\": null,\n      \"fcnet_bias_initializer_config\": null,\n      \"conv_filters\": null,\n      \"conv_activation\": \"relu\",\n      \"conv_kernel_initializer\": null,\n      \"conv_kernel_initializer_config\": null,\n      \"conv_bias_initializer\": null,\n      \"conv_bias_initializer_config\": null,\n      \"conv_transpose_kernel_initializer\": null,\n      \"conv_transpose_kernel_initializer_config\": null,\n      \"conv_transpose_bias_initializer\": null,\n      \"conv_transpose_bias_initializer_config\": null,\n      \"post_fcnet_hiddens\": [],\n      \"post_fcnet_activation\": \"relu\",\n      \"post_fcnet_weights_initializer\": null,\n      \"post_fcnet_weights_initializer_config\": null,\n      \"post_fcnet_bias_initializer\": null,\n      \"post_fcnet_bias_initializer_config\": null,\n      \"free_log_std\": false,\n      \"log_std_clip_param\": 20.0,\n      \"no_final_linear\": false,\n      \"vf_share_layers\": false,\n      \"use_lstm\": false,\n      \"max_seq_len\": 20,\n      \"lstm_cell_size\": 256,\n      \"lstm_use_prev_action\": false,\n      \"lstm_use_prev_reward\": false,\n      \"lstm_weights_initializer\": null,\n      \"lstm_weights_initializer_config\": null,\n      \"lstm_bias_initializer\": null,\n      \"lstm_bias_initializer_config\": null,\n      \"_time_major\": false,\n      \"use_attention\": false,\n      \"attention_num_transformer_units\": 1,\n      \"attention_dim\": 64,\n      \"attention_num_heads\": 1,\n      \"attention_head_dim\": 32,\n      \"attention_memory_inference\": 50,\n      \"attention_memory_training\": 50,\n      \"attention_position_wise_mlp_dim\": 32,\n      \"attention_init_gru_gate_bias\": 2.0,\n      \"attention_use_n_prev_actions\": 0,\n      \"attention_use_n_prev_rewards\": 0,\n      \"framestack\": true,\n      \"dim\": 84,\n      \"grayscale\": false,\n      \"zero_mean\": true,\n      \"custom_model\": null,\n      \"custom_model_config\": {},\n      \"custom_action_dist\": null,\n      \"custom_preprocessor\": null,\n      \"encoder_latent_dim\": null,\n      \"always_check_shapes\": false,\n      \"lstm_use_prev_action_reward\": -1,\n      \"_use_default_native_models\": -1,\n      \"_disable_preprocessor_api\": false,\n      \"_disable_action_flattening\": false\n    },\n    \"_learner_connector\": null,\n    \"add_default_connectors_to_learner_pipeline\": true,\n    \"learner_config_dict\": {},\n    \"optimizer\": {\n      \"type\": \"SGD\",\n      \"lr\": 0.01,\n      \"momentum\": 0.9\n    },\n    \"_learner_class\": null,\n    \"explore\": true,\n    \"exploration_config\": {\n      \"type\": \"StochasticSampling\"\n    },\n    \"count_steps_by\": \"env_steps\",\n    \"policy_map_capacity\": 100,\n    \"policy_mapping_fn\": [\n      \"__ref_ph\",\n      \"cdf20c8b\"\n    ],\n    \"policies_to_train\": null,\n    \"policy_states_are_swappable\": false,\n    \"observation_fn\": null,\n    \"input_read_method\": \"read_parquet\",\n    \"input_read_method_kwargs\": {},\n    \"input_read_schema\": {},\n    \"input_read_episodes\": false,\n    \"input_read_sample_batches\": false,\n    \"input_read_batch_size\": null,\n    \"input_filesystem\": null,\n    \"input_filesystem_kwargs\": {},\n    \"input_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"input_spaces_jsonable\": true,\n    \"materialize_data\": false,\n    \"materialize_mapped_data\": true,\n    \"map_batches_kwargs\": {},\n    \"iter_batches_kwargs\": {},\n    \"prelearner_class\": null,\n    \"prelearner_buffer_class\": null,\n    \"prelearner_buffer_kwargs\": {},\n    \"prelearner_module_synch_period\": 10,\n    \"dataset_num_iters_per_learner\": null,\n    \"input_config\": {},\n    \"actions_in_input_normalized\": false,\n    \"postprocess_inputs\": false,\n    \"shuffle_buffer_size\": 0,\n    \"output\": null,\n    \"output_config\": {},\n    \"output_compress_columns\": [\n      \"obs\",\n      \"new_obs\"\n    ],\n    \"output_max_file_size\": 67108864,\n    \"output_max_rows_per_file\": null,\n    \"output_write_method\": \"write_parquet\",\n    \"output_write_method_kwargs\": {},\n    \"output_filesystem\": null,\n    \"output_filesystem_kwargs\": {},\n    \"output_write_episodes\": true,\n    \"offline_sampling\": false,\n    \"evaluation_interval\": null,\n    \"evaluation_duration\": 10,\n    \"evaluation_duration_unit\": \"episodes\",\n    \"evaluation_sample_timeout_s\": 120.0,\n    \"evaluation_parallel_to_training\": false,\n    \"evaluation_force_reset_envs_before_iteration\": true,\n    \"evaluation_config\": null,\n    \"off_policy_estimation_methods\": {},\n    \"ope_split_batch_by_episode\": true,\n    \"evaluation_num_env_runners\": 0,\n    \"in_evaluation\": false,\n    \"sync_filters_on_rollout_workers_timeout_s\": 10.0,\n    \"keep_per_episode_custom_metrics\": false,\n    \"metrics_episode_collection_timeout_s\": 60.0,\n    \"metrics_num_episodes_for_smoothing\": 100,\n    \"min_time_s_per_iteration\": null,\n    \"min_train_timesteps_per_iteration\": 0,\n    \"min_sample_timesteps_per_iteration\": 0,\n    \"log_gradients\": true,\n    \"export_native_model_files\": false,\n    \"checkpoint_trainable_policies_only\": false,\n    \"logger_creator\": null,\n    \"logger_config\": null,\n    \"log_level\": \"WARN\",\n    \"log_sys_usage\": true,\n    \"fake_sampler\": false,\n    \"seed\": null,\n    \"_run_training_always_in_thread\": false,\n    \"_evaluation_parallel_to_training_wo_thread\": false,\n    \"restart_failed_env_runners\": true,\n    \"ignore_env_runner_failures\": false,\n    \"max_num_env_runner_restarts\": 1000,\n    \"delay_between_env_runner_restarts_s\": 60.0,\n    \"restart_failed_sub_environments\": false,\n    \"num_consecutive_env_runner_failures_tolerance\": 100,\n    \"env_runner_health_probe_timeout_s\": 30.0,\n    \"env_runner_restore_timeout_s\": 1800.0,\n    \"_model_config\": {},\n    \"_rl_module_spec\": null,\n    \"_AlgorithmConfig__prior_exploration_config\": null,\n    \"algorithm_config_overrides_per_module\": {},\n    \"_per_module_overrides\": {},\n    \"_torch_grad_scaler_class\": null,\n    \"_torch_lr_scheduler_classes\": null,\n    \"_tf_policy_handles_more_than_one_loss\": false,\n    \"_disable_preprocessor_api\": false,\n    \"_disable_action_flattening\": false,\n    \"_disable_initialize_loss_from_dummy_batch\": false,\n    \"_dont_auto_sync_env_runner_states\": false,\n    \"enable_connectors\": -1,\n    \"simple_optimizer\": -1,\n    \"policy_map_cache\": -1,\n    \"worker_cls\": -1,\n    \"synchronize_filters\": -1,\n    \"enable_async_evaluation\": -1,\n    \"custom_async_evaluation_function\": -1,\n    \"_enable_rl_module_api\": -1,\n    \"auto_wrap_old_gym_envs\": -1,\n    \"always_attach_evaluation_results\": -1,\n    \"replay_sequence_length\": null,\n    \"_disable_execution_plan_api\": -1,\n    \"lr_schedule\": null,\n    \"use_critic\": true,\n    \"use_gae\": true,\n    \"use_kl_loss\": true,\n    \"kl_coeff\": 0.2,\n    \"kl_target\": 0.01,\n    \"vf_loss_coeff\": 1.0,\n    \"entropy_coeff\": 0.0,\n    \"entropy_coeff_schedule\": null,\n    \"clip_param\": 0.3,\n    \"vf_clip_param\": 10.0,\n    \"sgd_minibatch_size\": -1,\n    \"vf_share_layers\": -1,\n    \"lambda\": 1.0,\n    \"input\": \"sampler\",\n    \"policies\": {\n      \"default_policy\": [\n        null,\n        null,\n        null,\n        null\n      ]\n    },\n    \"callbacks\": [\n      \"__ref_ph\",\n      \"8913b504\"\n    ],\n    \"create_env_on_driver\": true,\n    \"custom_eval_function\": null,\n    \"framework\": \"torch\"\n  },\n  \"evaluated_params\": {},\n  \"experiment_tag\": \"0\",\n  \"stopping_criterion\": {\n    \"env_runners/episode_return_mean\": 2000.0,\n    \"training_iteration\": 500\n  },\n  \"_setup_default_resource\": true,\n  \"_default_placement_group_factory\": \"80054e2e\",\n  \"placement_group_factory\": \"8005951b010000000000008c237261792e74756e652e657865637574696f6e2e706c6163656d656e745f67726f757073948c15506c6163656d656e7447726f7570466163746f72799493942981947d94288c085f62756e646c6573945d94287d948c0343505594473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff0000000000000737d946808473ff000000000000073658c155f686561645f62756e646c655f69735f656d70747994898c095f7374726174656779948c045041434b948c055f6172677394298c075f6b7761726773947d9475622e\",\n  \"log_to_file\": [\n    null,\n    null\n  ],\n  \"max_failures\": 0,\n  \"_default_result_or_future\": null,\n  \"export_formats\": [],\n  \"status\": \"RUNNING\",\n  \"relative_logdir\": \"PPO_markettiming_env_e4287_00000_0_2025-01-06_14-03-03\",\n  \"trial_name_creator\": null,\n  \"trial_dirname_creator\": null,\n  \"custom_trial_name\": null,\n  \"custom_dirname\": null,\n  \"restore_path\": null,\n  \"_restore_checkpoint_result\": null,\n  \"_state_json\": null,\n  \"results\": \"80054e2e\",\n  \"extra_arg\": \"80054e2e\",\n  \"_resources\": \"80054e2e\"\n}", "{\n  \"start_time\": 1736143402.447665,\n  \"num_failures\": 0,\n  \"num_failures_after_restore\": 0,\n  \"error_filename\": null,\n  \"pickled_error_filename\": null,\n  \"last_result\": {\n    \"custom_metrics\": {},\n    \"episode_media\": {},\n    \"info\": {\n      \"learner\": {\n        \"default_policy\": {\n          \"learner_stats\": {\n            \"allreduce_latency\": 0.0,\n            \"grad_gnorm\": 11.176471208628788,\n            \"cur_kl_coeff\": 0.20000000000000004,\n            \"cur_lr\": 0.00010000000000000003,\n            \"total_loss\": 0.4203817323611308,\n            \"policy_loss\": -0.006460704181783942,\n            \"vf_loss\": 0.4253196742565882,\n            \"vf_explained_var\": -0.3829774095807024,\n            \"kl\": 0.007613794697424241,\n            \"entropy\": 0.6914780310084743,\n            \"entropy_coeff\": 0.0\n          },\n          \"model\": {},\n          \"custom_metrics\": {},\n          \"num_agent_steps_trained\": 128.0,\n          \"num_grad_updates_lifetime\": 33945.5,\n          \"diff_num_grad_updates_vs_sampler_policy\": 464.5\n        }\n      },\n      \"num_env_steps_sampled\": 148000,\n      \"num_env_steps_trained\": 148000,\n      \"num_agent_steps_sampled\": 148000,\n      \"num_agent_steps_trained\": 148000\n    },\n    \"env_runners\": {\n      \"episode_reward_max\": NaN,\n      \"episode_reward_min\": NaN,\n      \"episode_reward_mean\": NaN,\n      \"episode_len_mean\": NaN,\n      \"episode_media\": {},\n      \"episodes_timesteps_total\": 0,\n      \"policy_reward_min\": {},\n      \"policy_reward_max\": {},\n      \"policy_reward_mean\": {},\n      \"custom_metrics\": {},\n      \"hist_stats\": {\n        \"episode_reward\": [],\n        \"episode_lengths\": []\n      },\n      \"sampler_perf\": {},\n      \"num_faulty_episodes\": 0,\n      \"connector_metrics\": {},\n      \"num_episodes\": 0,\n      \"episode_return_max\": NaN,\n      \"episode_return_min\": NaN,\n      \"episode_return_mean\": NaN,\n      \"episodes_this_iter\": 0\n    },\n    \"num_healthy_workers\": 8,\n    \"num_in_flight_async_sample_reqs\": 0,\n    \"num_remote_worker_restarts\": 0,\n    \"num_agent_steps_sampled\": 148000,\n    \"num_agent_steps_trained\": 148000,\n    \"num_env_steps_sampled\": 148000,\n    \"num_env_steps_trained\": 148000,\n    \"num_env_steps_sampled_this_iter\": 4000,\n    \"num_env_steps_trained_this_iter\": 4000,\n    \"num_env_steps_sampled_throughput_per_sec\": 427.71819931146337,\n    \"num_env_steps_trained_throughput_per_sec\": 427.71819931146337,\n    \"timesteps_total\": 148000,\n    \"num_env_steps_sampled_lifetime\": 148000,\n    \"num_agent_steps_sampled_lifetime\": 148000,\n    \"num_steps_trained_this_iter\": 4000,\n    \"agent_timesteps_total\": 148000,\n    \"timers\": {\n      \"training_iteration_time_ms\": 8704.669,\n      \"restore_workers_time_ms\": 0.0,\n      \"training_step_time_ms\": 8704.669,\n      \"sample_time_ms\": 2638.36,\n      \"load_time_ms\": 0.0,\n      \"load_throughput\": 0.0,\n      \"learn_time_ms\": 6051.835,\n      \"learn_throughput\": 660.957,\n      \"synch_weights_time_ms\": 14.273\n    },\n    \"counters\": {\n      \"num_env_steps_sampled\": 148000,\n      \"num_env_steps_trained\": 148000,\n      \"num_agent_steps_sampled\": 148000,\n      \"num_agent_steps_trained\": 148000\n    },\n    \"done\": false,\n    \"training_iteration\": 37,\n    \"trial_id\": \"e4287_00000\",\n    \"date\": \"2025-01-06_14-08-14\",\n    \"timestamp\": **********,\n    \"time_this_iter_s\": 9.362024784088135,\n    \"time_total_s\": 291.44240403175354,\n    \"pid\": 32464,\n    \"hostname\": \"Matebook-white\",\n    \"node_ip\": \"127.0.0.1\",\n    \"config\": {\n      \"extra_python_environs_for_driver\": {},\n      \"extra_python_environs_for_worker\": {},\n      \"placement_strategy\": \"PACK\",\n      \"num_gpus\": 0,\n      \"_fake_gpus\": false,\n      \"num_cpus_for_main_process\": 1,\n      \"eager_tracing\": true,\n      \"eager_max_retraces\": 20,\n      \"tf_session_args\": {\n        \"intra_op_parallelism_threads\": 2,\n        \"inter_op_parallelism_threads\": 2,\n        \"gpu_options\": {\n          \"allow_growth\": true\n        },\n        \"log_device_placement\": false,\n        \"device_count\": {\n          \"CPU\": 1\n        },\n        \"allow_soft_placement\": true\n      },\n      \"local_tf_session_args\": {\n        \"intra_op_parallelism_threads\": 8,\n        \"inter_op_parallelism_threads\": 8\n      },\n      \"torch_compile_learner\": false,\n      \"torch_compile_learner_what_to_compile\": \"forward_train\",\n      \"torch_compile_learner_dynamo_backend\": \"inductor\",\n      \"torch_compile_learner_dynamo_mode\": null,\n      \"torch_compile_worker\": false,\n      \"torch_compile_worker_dynamo_backend\": \"onnxrt\",\n      \"torch_compile_worker_dynamo_mode\": null,\n      \"torch_ddp_kwargs\": {},\n      \"torch_skip_nan_gradients\": false,\n      \"enable_rl_module_and_learner\": false,\n      \"enable_env_runner_and_connector_v2\": false,\n      \"env\": \"markettiming_env\",\n      \"env_config\": {\n        \"name\": \"MarketTimingEnv\",\n        \"version\": \"v1\",\n        \"initial_amount\": 10000000,\n        \"gamma\": 0.98,\n        \"mode\": \"train\",\n        \"split_percent\": 0.9,\n        \"data_path\": \"E:/lab/RoboQuant/pylab/data\",\n        \"data_file\": \"ft_all.all.00170516142453003.csv\"\n      },\n      \"observation_space\": null,\n      \"action_space\": null,\n      \"clip_rewards\": null,\n      \"normalize_actions\": true,\n      \"clip_actions\": false,\n      \"_is_atari\": null,\n      \"disable_env_checking\": false,\n      \"env_task_fn\": null,\n      \"render_env\": false,\n      \"action_mask_key\": \"action_mask\",\n      \"env_runner_cls\": null,\n      \"num_env_runners\": 8,\n      \"num_envs_per_env_runner\": 1,\n      \"num_cpus_per_env_runner\": 1,\n      \"num_gpus_per_env_runner\": 0,\n      \"custom_resources_per_env_runner\": {},\n      \"validate_env_runners_after_construction\": true,\n      \"max_requests_in_flight_per_env_runner\": 2,\n      \"sample_timeout_s\": 60.0,\n      \"_env_to_module_connector\": null,\n      \"add_default_connectors_to_env_to_module_pipeline\": true,\n      \"_module_to_env_connector\": null,\n      \"add_default_connectors_to_module_to_env_pipeline\": true,\n      \"episode_lookback_horizon\": 1,\n      \"rollout_fragment_length\": \"auto\",\n      \"batch_mode\": \"truncate_episodes\",\n      \"compress_observations\": false,\n      \"remote_worker_envs\": false,\n      \"remote_env_batch_wait_ms\": 0,\n      \"enable_tf1_exec_eagerly\": false,\n      \"sample_collector\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059551000000000000008c357261792e726c6c69622e6576616c756174696f6e2e636f6c6c6563746f72732e73696d706c655f6c6973745f636f6c6c6563746f72948c1353696d706c654c697374436f6c6c6563746f729493942e\"\n      },\n      \"preprocessor_pref\": \"deepmind\",\n      \"observation_filter\": \"NoFilter\",\n      \"update_worker_filter_stats\": true,\n      \"use_worker_filter_stats\": true,\n      \"sampler_perf_stats_ema_coef\": null,\n      \"num_learners\": 0,\n      \"num_gpus_per_learner\": 0,\n      \"num_cpus_per_learner\": 1,\n      \"local_gpu_idx\": 0,\n      \"gamma\": 0.99,\n      \"lr\": 0.0001,\n      \"grad_clip\": null,\n      \"grad_clip_by\": \"global_norm\",\n      \"train_batch_size_per_learner\": null,\n      \"train_batch_size\": 4000,\n      \"num_epochs\": 30,\n      \"minibatch_size\": 128,\n      \"shuffle_batch_per_epoch\": true,\n      \"model\": {\n        \"fcnet_hiddens\": [\n          256,\n          256\n        ],\n        \"fcnet_activation\": \"tanh\",\n        \"fcnet_weights_initializer\": null,\n        \"fcnet_weights_initializer_config\": null,\n        \"fcnet_bias_initializer\": null,\n        \"fcnet_bias_initializer_config\": null,\n        \"conv_filters\": null,\n        \"conv_activation\": \"relu\",\n        \"conv_kernel_initializer\": null,\n        \"conv_kernel_initializer_config\": null,\n        \"conv_bias_initializer\": null,\n        \"conv_bias_initializer_config\": null,\n        \"conv_transpose_kernel_initializer\": null,\n        \"conv_transpose_kernel_initializer_config\": null,\n        \"conv_transpose_bias_initializer\": null,\n        \"conv_transpose_bias_initializer_config\": null,\n        \"post_fcnet_hiddens\": [],\n        \"post_fcnet_activation\": \"relu\",\n        \"post_fcnet_weights_initializer\": null,\n        \"post_fcnet_weights_initializer_config\": null,\n        \"post_fcnet_bias_initializer\": null,\n        \"post_fcnet_bias_initializer_config\": null,\n        \"free_log_std\": false,\n        \"log_std_clip_param\": 20.0,\n        \"no_final_linear\": false,\n        \"vf_share_layers\": false,\n        \"use_lstm\": false,\n        \"max_seq_len\": 20,\n        \"lstm_cell_size\": 256,\n        \"lstm_use_prev_action\": false,\n        \"lstm_use_prev_reward\": false,\n        \"lstm_weights_initializer\": null,\n        \"lstm_weights_initializer_config\": null,\n        \"lstm_bias_initializer\": null,\n        \"lstm_bias_initializer_config\": null,\n        \"_time_major\": false,\n        \"use_attention\": false,\n        \"attention_num_transformer_units\": 1,\n        \"attention_dim\": 64,\n        \"attention_num_heads\": 1,\n        \"attention_head_dim\": 32,\n        \"attention_memory_inference\": 50,\n        \"attention_memory_training\": 50,\n        \"attention_position_wise_mlp_dim\": 32,\n        \"attention_init_gru_gate_bias\": 2.0,\n        \"attention_use_n_prev_actions\": 0,\n        \"attention_use_n_prev_rewards\": 0,\n        \"framestack\": true,\n        \"dim\": 84,\n        \"grayscale\": false,\n        \"zero_mean\": true,\n        \"custom_model\": null,\n        \"custom_model_config\": {},\n        \"custom_action_dist\": null,\n        \"custom_preprocessor\": null,\n        \"encoder_latent_dim\": null,\n        \"always_check_shapes\": false,\n        \"lstm_use_prev_action_reward\": -1,\n        \"_use_default_native_models\": -1,\n        \"_disable_preprocessor_api\": false,\n        \"_disable_action_flattening\": false\n      },\n      \"_learner_connector\": null,\n      \"add_default_connectors_to_learner_pipeline\": true,\n      \"learner_config_dict\": {},\n      \"optimizer\": {\n        \"type\": \"SGD\",\n        \"lr\": 0.01,\n        \"momentum\": 0.9\n      },\n      \"_learner_class\": null,\n      \"explore\": true,\n      \"exploration_config\": {\n        \"type\": \"StochasticSampling\"\n      },\n      \"count_steps_by\": \"env_steps\",\n      \"policy_map_capacity\": 100,\n      \"policy_mapping_fn\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059557000000000000008c257261792e726c6c69622e616c676f726974686d732e616c676f726974686d5f636f6e666967948c29416c676f726974686d436f6e6669672e44454641554c545f504f4c4943595f4d415050494e475f464e9493942e\"\n      },\n      \"policies_to_train\": null,\n      \"policy_states_are_swappable\": false,\n      \"observation_fn\": null,\n      \"input_read_method\": \"read_parquet\",\n      \"input_read_method_kwargs\": {},\n      \"input_read_schema\": {},\n      \"input_read_episodes\": false,\n      \"input_read_sample_batches\": false,\n      \"input_read_batch_size\": null,\n      \"input_filesystem\": null,\n      \"input_filesystem_kwargs\": {},\n      \"input_compress_columns\": [\n        \"obs\",\n        \"new_obs\"\n      ],\n      \"input_spaces_jsonable\": true,\n      \"materialize_data\": false,\n      \"materialize_mapped_data\": true,\n      \"map_batches_kwargs\": {},\n      \"iter_batches_kwargs\": {},\n      \"prelearner_class\": null,\n      \"prelearner_buffer_class\": null,\n      \"prelearner_buffer_kwargs\": {},\n      \"prelearner_module_synch_period\": 10,\n      \"dataset_num_iters_per_learner\": null,\n      \"input_config\": {},\n      \"actions_in_input_normalized\": false,\n      \"postprocess_inputs\": false,\n      \"shuffle_buffer_size\": 0,\n      \"output\": null,\n      \"output_config\": {},\n      \"output_compress_columns\": [\n        \"obs\",\n        \"new_obs\"\n      ],\n      \"output_max_file_size\": 67108864,\n      \"output_max_rows_per_file\": null,\n      \"output_write_method\": \"write_parquet\",\n      \"output_write_method_kwargs\": {},\n      \"output_filesystem\": null,\n      \"output_filesystem_kwargs\": {},\n      \"output_write_episodes\": true,\n      \"offline_sampling\": false,\n      \"evaluation_interval\": null,\n      \"evaluation_duration\": 10,\n      \"evaluation_duration_unit\": \"episodes\",\n      \"evaluation_sample_timeout_s\": 120.0,\n      \"evaluation_parallel_to_training\": false,\n      \"evaluation_force_reset_envs_before_iteration\": true,\n      \"evaluation_config\": null,\n      \"off_policy_estimation_methods\": {},\n      \"ope_split_batch_by_episode\": true,\n      \"evaluation_num_env_runners\": 0,\n      \"in_evaluation\": false,\n      \"sync_filters_on_rollout_workers_timeout_s\": 10.0,\n      \"keep_per_episode_custom_metrics\": false,\n      \"metrics_episode_collection_timeout_s\": 60.0,\n      \"metrics_num_episodes_for_smoothing\": 100,\n      \"min_time_s_per_iteration\": null,\n      \"min_train_timesteps_per_iteration\": 0,\n      \"min_sample_timesteps_per_iteration\": 0,\n      \"log_gradients\": true,\n      \"export_native_model_files\": false,\n      \"checkpoint_trainable_policies_only\": false,\n      \"logger_creator\": null,\n      \"logger_config\": null,\n      \"log_level\": \"WARN\",\n      \"log_sys_usage\": true,\n      \"fake_sampler\": false,\n      \"seed\": null,\n      \"_run_training_always_in_thread\": false,\n      \"_evaluation_parallel_to_training_wo_thread\": false,\n      \"restart_failed_env_runners\": true,\n      \"ignore_env_runner_failures\": false,\n      \"max_num_env_runner_restarts\": 1000,\n      \"delay_between_env_runner_restarts_s\": 60.0,\n      \"restart_failed_sub_environments\": false,\n      \"num_consecutive_env_runner_failures_tolerance\": 100,\n      \"env_runner_health_probe_timeout_s\": 30.0,\n      \"env_runner_restore_timeout_s\": 1800.0,\n      \"_model_config\": {},\n      \"_rl_module_spec\": null,\n      \"_AlgorithmConfig__prior_exploration_config\": null,\n      \"algorithm_config_overrides_per_module\": {},\n      \"_per_module_overrides\": {},\n      \"_torch_grad_scaler_class\": null,\n      \"_torch_lr_scheduler_classes\": null,\n      \"_tf_policy_handles_more_than_one_loss\": false,\n      \"_disable_preprocessor_api\": false,\n      \"_disable_action_flattening\": false,\n      \"_disable_initialize_loss_from_dummy_batch\": false,\n      \"_dont_auto_sync_env_runner_states\": false,\n      \"enable_connectors\": -1,\n      \"simple_optimizer\": false,\n      \"policy_map_cache\": -1,\n      \"worker_cls\": -1,\n      \"synchronize_filters\": -1,\n      \"enable_async_evaluation\": -1,\n      \"custom_async_evaluation_function\": -1,\n      \"_enable_rl_module_api\": -1,\n      \"auto_wrap_old_gym_envs\": -1,\n      \"always_attach_evaluation_results\": -1,\n      \"replay_sequence_length\": null,\n      \"_disable_execution_plan_api\": -1,\n      \"lr_schedule\": null,\n      \"use_critic\": true,\n      \"use_gae\": true,\n      \"use_kl_loss\": true,\n      \"kl_coeff\": 0.2,\n      \"kl_target\": 0.01,\n      \"vf_loss_coeff\": 1.0,\n      \"entropy_coeff\": 0.0,\n      \"entropy_coeff_schedule\": null,\n      \"clip_param\": 0.3,\n      \"vf_clip_param\": 10.0,\n      \"sgd_minibatch_size\": -1,\n      \"vf_share_layers\": -1,\n      \"__stdout_file__\": null,\n      \"__stderr_file__\": null,\n      \"lambda\": 1.0,\n      \"input\": \"sampler\",\n      \"policies\": {\n        \"default_policy\": [\n          null,\n          null,\n          null,\n          null\n        ]\n      },\n      \"callbacks\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"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\"\n      },\n      \"create_env_on_driver\": true,\n      \"custom_eval_function\": null,\n      \"framework\": \"torch\"\n    },\n    \"time_since_restore\": 291.44240403175354,\n    \"iterations_since_restore\": 37,\n    \"perf\": {\n      \"cpu_util_percent\": 29.507692307692306,\n      \"ram_util_percent\": 85.84615384615384\n    },\n    \"experiment_tag\": \"0\"\n  },\n  \"last_result_time\": **********.684316,\n  \"metric_analysis\": {\n    \"num_healthy_workers\": {\n      \"max\": 8,\n      \"min\": 8,\n      \"avg\": 8.0,\n      \"last\": 8,\n      \"last-5-avg\": 8.0,\n      \"last-10-avg\": 8.0\n    },\n    \"num_in_flight_async_sample_reqs\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"num_remote_worker_restarts\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"num_agent_steps_sampled\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_agent_steps_trained\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_env_steps_sampled\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_env_steps_trained\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_env_steps_sampled_this_iter\": {\n      \"max\": 4000,\n      \"min\": 4000,\n      \"avg\": 4000.0,\n      \"last\": 4000,\n      \"last-5-avg\": 4000.0,\n      \"last-10-avg\": 4000.0\n    },\n    \"num_env_steps_trained_this_iter\": {\n      \"max\": 4000,\n      \"min\": 4000,\n      \"avg\": 4000.0,\n      \"last\": 4000,\n      \"last-5-avg\": 4000.0,\n      \"last-10-avg\": 4000.0\n    },\n    \"num_env_steps_sampled_throughput_per_sec\": {\n      \"max\": 558.5597606969958,\n      \"min\": 427.71819931146337,\n      \"avg\": 510.5472852901676,\n      \"last\": 427.71819931146337,\n      \"last-5-avg\": 460.8560435348844,\n      \"last-10-avg\": 459.8813212182129\n    },\n    \"num_env_steps_trained_throughput_per_sec\": {\n      \"max\": 558.5597606969958,\n      \"min\": 427.71819931146337,\n      \"avg\": 510.5472852901676,\n      \"last\": 427.71819931146337,\n      \"last-5-avg\": 460.8560435348844,\n      \"last-10-avg\": 459.8813212182129\n    },\n    \"timesteps_total\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_env_steps_sampled_lifetime\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_agent_steps_sampled_lifetime\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"num_steps_trained_this_iter\": {\n      \"max\": 4000,\n      \"min\": 4000,\n      \"avg\": 4000.0,\n      \"last\": 4000,\n      \"last-5-avg\": 4000.0,\n      \"last-10-avg\": 4000.0\n    },\n    \"agent_timesteps_total\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"done\": {\n      \"max\": false,\n      \"min\": false,\n      \"avg\": 0.0,\n      \"last\": false,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"training_iteration\": {\n      \"max\": 37,\n      \"min\": 1,\n      \"avg\": 19.0,\n      \"last\": 37,\n      \"last-5-avg\": 35.0,\n      \"last-10-avg\": 32.5\n    },\n    \"time_this_iter_s\": {\n      \"max\": 9.362024784088135,\n      \"min\": 7.1612749099731445,\n      \"avg\": 7.876821730587934,\n      \"last\": 9.362024784088135,\n      \"last-5-avg\": 8.69904727935791,\n      \"last-10-avg\": 8.709741640090943\n    },\n    \"time_total_s\": {\n      \"max\": 291.44240403175354,\n      \"min\": 7.1612749099731445,\n      \"avg\": 144.41433572769165,\n      \"last\": 291.44240403175354,\n      \"last-5-avg\": 273.7599681377411,\n      \"last-10-avg\": 252.16003670692444\n    },\n    \"time_since_restore\": {\n      \"max\": 291.44240403175354,\n      \"min\": 7.1612749099731445,\n      \"avg\": 144.41433572769165,\n      \"last\": 291.44240403175354,\n      \"last-5-avg\": 273.7599681377411,\n      \"last-10-avg\": 252.16003670692444\n    },\n    \"iterations_since_restore\": {\n      \"max\": 37,\n      \"min\": 1,\n      \"avg\": 19.0,\n      \"last\": 37,\n      \"last-5-avg\": 35.0,\n      \"last-10-avg\": 32.5\n    },\n    \"info/num_env_steps_sampled\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"info/num_env_steps_trained\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"info/num_agent_steps_sampled\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"info/num_agent_steps_trained\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"env_runners/episode_reward_max\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_reward_min\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_reward_mean\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_len_mean\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episodes_timesteps_total\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"env_runners/num_faulty_episodes\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"env_runners/num_episodes\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"env_runners/episode_return_max\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_return_min\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episode_return_mean\": {\n      \"max\": NaN,\n      \"min\": NaN,\n      \"avg\": NaN,\n      \"last\": NaN,\n      \"last-5-avg\": NaN,\n      \"last-10-avg\": NaN\n    },\n    \"env_runners/episodes_this_iter\": {\n      \"max\": 0,\n      \"min\": 0,\n      \"avg\": 0.0,\n      \"last\": 0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"timers/training_iteration_time_ms\": {\n      \"max\": 8704.669,\n      \"min\": 7161.275,\n      \"avg\": 7692.782324324321,\n      \"last\": 8704.669,\n      \"last-5-avg\": 8525.5918,\n      \"last-10-avg\": 8293.9748\n    },\n    \"timers/restore_workers_time_ms\": {\n      \"max\": 0.0,\n      \"min\": 0.0,\n      \"avg\": 0.0,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"timers/training_step_time_ms\": {\n      \"max\": 8704.669,\n      \"min\": 7161.275,\n      \"avg\": 7692.782324324321,\n      \"last\": 8704.669,\n      \"last-5-avg\": 8525.5918,\n      \"last-10-avg\": 8293.9748\n    },\n    \"timers/sample_time_ms\": {\n      \"max\": 2638.36,\n      \"min\": 1575.077,\n      \"avg\": 1864.488081081081,\n      \"last\": 2638.36,\n      \"last-5-avg\": 2477.3458,\n      \"last-10-avg\": 2285.7204\n    },\n    \"timers/load_time_ms\": {\n      \"max\": 0.547,\n      \"min\": 0.0,\n      \"avg\": 0.14783783783783785,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"timers/load_throughput\": {\n      \"max\": 7317029.09,\n      \"min\": 0.0,\n      \"avg\": 1977575.42972973,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"timers/learn_time_ms\": {\n      \"max\": 6051.835,\n      \"min\": 5532.376,\n      \"avg\": 5810.82362162162,\n      \"last\": 6051.835,\n      \"last-5-avg\": 6033.5098,\n      \"last-10-avg\": 5992.7867\n    },\n    \"timers/learn_throughput\": {\n      \"max\": 723.017,\n      \"min\": 660.957,\n      \"avg\": 688.7359729729728,\n      \"last\": 660.957,\n      \"last-5-avg\": 662.9736,\n      \"last-10-avg\": 667.543\n    },\n    \"timers/synch_weights_time_ms\": {\n      \"max\": 20.408,\n      \"min\": 13.854,\n      \"avg\": 16.730972972972975,\n      \"last\": 14.273,\n      \"last-5-avg\": 14.5352,\n      \"last-10-avg\": 15.025200000000002\n    },\n    \"counters/num_env_steps_sampled\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"counters/num_env_steps_trained\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"counters/num_agent_steps_sampled\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"counters/num_agent_steps_trained\": {\n      \"max\": 148000,\n      \"min\": 4000,\n      \"avg\": 76000.0,\n      \"last\": 148000,\n      \"last-5-avg\": 140000.0,\n      \"last-10-avg\": 130000.0\n    },\n    \"perf/cpu_util_percent\": {\n      \"max\": 29.507692307692306,\n      \"min\": 21.245454545454546,\n      \"avg\": 23.84434038934039,\n      \"last\": 29.507692307692306,\n      \"last-5-avg\": 27.10551282051282,\n      \"last-10-avg\": 26.681089743589745\n    },\n    \"perf/ram_util_percent\": {\n      \"max\": 86.03999999999999,\n      \"min\": 85.63636363636364,\n      \"avg\": 85.84418099918102,\n      \"last\": 85.84615384615384,\n      \"last-5-avg\": 85.75897435897437,\n      \"last-10-avg\": 85.82416666666666\n    },\n    \"info/learner/default_policy/num_agent_steps_trained\": {\n      \"max\": 128.0,\n      \"min\": 128.0,\n      \"avg\": 128.0,\n      \"last\": 128.0,\n      \"last-5-avg\": 128.0,\n      \"last-10-avg\": 128.0\n    },\n    \"info/learner/default_policy/num_grad_updates_lifetime\": {\n      \"max\": 33945.5,\n      \"min\": 465.5,\n      \"avg\": 17205.5,\n      \"last\": 33945.5,\n      \"last-5-avg\": 32085.5,\n      \"last-10-avg\": 29760.5\n    },\n    \"info/learner/default_policy/diff_num_grad_updates_vs_sampler_policy\": {\n      \"max\": 464.5,\n      \"min\": 464.5,\n      \"avg\": 464.5,\n      \"last\": 464.5,\n      \"last-5-avg\": 464.5,\n      \"last-10-avg\": 464.5\n    },\n    \"info/learner/default_policy/learner_stats/allreduce_latency\": {\n      \"max\": 0.0,\n      \"min\": 0.0,\n      \"avg\": 0.0,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    },\n    \"info/learner/default_policy/learner_stats/grad_gnorm\": {\n      \"max\": 11.605336008661537,\n      \"min\": 1.232760720051104,\n      \"avg\": 6.59739243954017,\n      \"last\": 11.176471208628788,\n      \"last-5-avg\": 10.504347649639653,\n      \"last-10-avg\": 10.376532215709968\n    },\n    \"info/learner/default_policy/learner_stats/cur_kl_coeff\": {\n      \"max\": 0.20000000000000004,\n      \"min\": 0.20000000000000004,\n      \"avg\": 0.20000000000000004,\n      \"last\": 0.20000000000000004,\n      \"last-5-avg\": 0.20000000000000004,\n      \"last-10-avg\": 0.2\n    },\n    \"info/learner/default_policy/learner_stats/cur_lr\": {\n      \"max\": 0.00010000000000000003,\n      \"min\": 0.00010000000000000003,\n      \"avg\": 9.999999999999991e-05,\n      \"last\": 0.00010000000000000003,\n      \"last-5-avg\": 0.00010000000000000002,\n      \"last-10-avg\": 0.00010000000000000002\n    },\n    \"info/learner/default_policy/learner_stats/total_loss\": {\n      \"max\": 0.6602525447819492,\n      \"min\": 0.012732180408252183,\n      \"avg\": 0.3934691842898756,\n      \"last\": 0.4203817323611308,\n      \"last-5-avg\": 0.4903931462270538,\n      \"last-10-avg\": 0.5606871530752985\n    },\n    \"info/learner/default_policy/learner_stats/policy_loss\": {\n      \"max\": -0.006460704181783942,\n      \"min\": -0.018030159207441474,\n      \"avg\": -0.014191073653113884,\n      \"last\": -0.006460704181783942,\n      \"last-5-avg\": -0.01186683950004398,\n      \"last-10-avg\": -0.013394091352720253\n    },\n    \"info/learner/default_policy/learner_stats/vf_loss\": {\n      \"max\": 0.6728638142137037,\n      \"min\": 0.01809490356548634,\n      \"avg\": 0.4055315215518199,\n      \"last\": 0.4253196742565882,\n      \"last-5-avg\": 0.5003659133782851,\n      \"last-10-avg\": 0.5721372683074448\n    },\n    \"info/learner/default_policy/learner_stats/vf_explained_var\": {\n      \"max\": -0.11077490429724417,\n      \"min\": -0.6786010124991017,\n      \"avg\": -0.3763296228496531,\n      \"last\": -0.3829774095807024,\n      \"last-5-avg\": -0.3992686805161097,\n      \"last-10-avg\": -0.3986770395822423\n    },\n    \"info/learner/default_policy/learner_stats/kl\": {\n      \"max\": 0.012971171139968112,\n      \"min\": 0.007613794697424241,\n      \"avg\": 0.01064368085746999,\n      \"last\": 0.007613794697424241,\n      \"last-5-avg\": 0.009470356923383454,\n      \"last-10-avg\": 0.00971987877067482\n    },\n    \"info/learner/default_policy/learner_stats/entropy\": {\n      \"max\": 1.089018451911147,\n      \"min\": 0.5845601645849084,\n      \"avg\": 0.8518160152958422,\n      \"last\": 0.6914780310084743,\n      \"last-5-avg\": 0.6675162162447489,\n      \"last-10-avg\": 0.7159236967691811\n    },\n    \"info/learner/default_policy/learner_stats/entropy_coeff\": {\n      \"max\": 0.0,\n      \"min\": 0.0,\n      \"avg\": 0.0,\n      \"last\": 0.0,\n      \"last-5-avg\": 0.0,\n      \"last-10-avg\": 0.0\n    }\n  },\n  \"_n_steps\": [\n    5,\n    10\n  ],\n  \"metric_n_steps\": {\n    \"num_healthy_workers\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b084b084b084b084b08652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b084b084b084b084b084b084b084b084b084b08652e\"\n      }\n    },\n    \"num_in_flight_async_sample_reqs\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"num_remote_worker_restarts\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"num_agent_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_env_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_env_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_env_steps_sampled_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059531000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284da00f4da00f4da00f4da00f4da00f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059540000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f652e\"\n      }\n    },\n    \"num_env_steps_trained_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059531000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284da00f4da00f4da00f4da00f4da00f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059540000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f652e\"\n      }\n    },\n    \"num_env_steps_sampled_throughput_per_sec\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847407d257a634a86e847407d3cbd1323a2fe47407d2ef209fd631747407db7d4868bfd9147407abb7dbe8fabeb652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847407c1527a18ba82c47407d0a7c7ab76a9247407cdd2d6b551e3947407cd1ed91d8f34947407c99c80bce948147407d257a634a86e847407d3cbd1323a2fe47407d2ef209fd631747407db7d4868bfd9147407abb7dbe8fabeb652e\"\n      }\n    },\n    \"num_env_steps_trained_throughput_per_sec\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847407d257a634a86e847407d3cbd1323a2fe47407d2ef209fd631747407db7d4868bfd9147407abb7dbe8fabeb652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847407c1527a18ba82c47407d0a7c7ab76a9247407cdd2d6b551e3947407cd1ed91d8f34947407c99c80bce948147407d257a634a86e847407d3cbd1323a2fe47407d2ef209fd631747407db7d4868bfd9147407abb7dbe8fabeb652e\"\n      }\n    },\n    \"timesteps_total\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_env_steps_sampled_lifetime\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_agent_steps_sampled_lifetime\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"num_steps_trained_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059531000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284da00f4da00f4da00f4da00f4da00f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059540000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f4da00f652e\"\n      }\n    },\n    \"agent_timesteps_total\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"done\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059527000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288989898989652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942889898989898989898989652e\"\n      }\n    },\n    \"training_iteration\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b214b224b234b244b25652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b1c4b1d4b1e4b1f4b204b214b224b234b244b25652e\"\n      }\n    },\n    \"time_this_iter_s\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428474021279f88000000474021207278000000474021273d78000000474020d4e4d8000000474022b95b50000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428474021d348f0000000474021379148000000474021529800000000474021595af80000004740217d83c0000000474021279f88000000474021207278000000474021273d78000000474020d4e4d8000000474022b95b50000000652e\"\n      }\n    },\n    \"time_total_s\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740700864958000004740709168294000004740711aa215000000474071a1493bc00000474072371416400000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847406aa83eb280000047406bbbb7c700000047406cd0e14700000047406de676f680000047406efe4f328000004740700864958000004740709168294000004740711aa215000000474071a1493bc00000474072371416400000652e\"\n      }\n    },\n    \"time_since_restore\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740700864958000004740709168294000004740711aa215000000474071a1493bc00000474072371416400000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847406aa83eb280000047406bbbb7c700000047406cd0e14700000047406de676f680000047406efe4f328000004740700864958000004740709168294000004740711aa215000000474071a1493bc00000474072371416400000652e\"\n      }\n    },\n    \"iterations_since_restore\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b214b224b234b244b25652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b1c4b1d4b1e4b1f4b204b214b224b234b244b25652e\"\n      }\n    },\n    \"info/num_env_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"info/num_env_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"info/num_agent_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"info/num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"env_runners/episode_reward_max\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_reward_min\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_reward_mean\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_len_mean\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episodes_timesteps_total\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"env_runners/num_faulty_episodes\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"env_runners/num_episodes\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"env_runners/episode_return_max\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_return_min\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episode_return_mean\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000477ff8000000000000652e\"\n      }\n    },\n    \"env_runners/episodes_this_iter\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005952c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284b004b004b004b004b00652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059536000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284b004b004b004b004b004b004b004b004b004b00652e\"\n      }\n    },\n    \"timers/training_iteration_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740c05b60a3d70a3d4740c08219db22d0e54740c0a2dc8b4395814740c0c14e147ae1484740c10055a1cac083652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740bea24624dd2f1b4740bf0267ef9db22d4740bf7b804189374c4740bff69a1cac08314740c03080c49ba5e34740c05b60a3d70a3d4740c08219db22d0e54740c0a2dc8b4395814740c0c14e147ae1484740c10055a1cac083652e\"\n      }\n    },\n    \"timers/restore_workers_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      }\n    },\n    \"timers/training_step_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740c05b60a3d70a3d4740c08219db22d0e54740c0a2dc8b4395814740c0c14e147ae1484740c10055a1cac083652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740bea24624dd2f1b4740bf0267ef9db22d4740bf7b804189374c4740bff69a1cac08314740c03080c49ba5e34740c05b60a3d70a3d4740c08219db22d0e54740c0a2dc8b4395814740c0c14e147ae1484740c10055a1cac083652e\"\n      }\n    },\n    \"timers/sample_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740a20b947ae147ae4740a2a8353f7ced914740a35e3a5e353f7d4740a416b8d4fdf3b64740a49cb851eb851f652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847409e92d1eb851eb847409fa12d0e5604194740a05b604189374c4740a0e5b22d0e56044740a171e147ae147b4740a20b947ae147ae4740a2a8353f7ced914740a35e3a5e353f7d4740a416b8d4fdf3b64740a49cb851eb851f652e\"\n      }\n    },\n    \"timers/load_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      }\n    },\n    \"timers/load_throughput\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a8694529428470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000470000000000000000652e\"\n      }\n    },\n    \"timers/learn_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284740b7a15eb851eb854740b7a12ccccccccd4740b7888dd2f1a9fc4740b7689d70a3d70a4740b7a3d5c28f5c29652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740b6ed4c49ba5e354740b709d4395810624740b73d8d916872b04740b773249ba5e3544740b7987eb851eb854740b7a15eb851eb854740b7a12ccccccccd4740b7888dd2f1a9fc4740b7689d70a3d70a4740b7a3d5c28f5c29652e\"\n      }\n    },\n    \"timers/learn_throughput\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b058694529428474084a9ced916872b474084a9f9db22d0e5474084bf999999999a474084dbe76c8b4396474084a7a7ef9db22d652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284740854c189374bc6a47408531ba5e353f7d474085028d4fdf3b64474084d28b43958106474084b19374bc6a7f474084a9ced916872b474084a9f9db22d0e5474084bf999999999a474084dbe76c8b4396474084a7a7ef9db22d652e\"\n      }\n    },\n    \"timers/synch_weights_time_ms\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005954f000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b05869452942847402ec9374bc6a7f047402d722d0e56041947402bb53f7ced916847402cddb22d0e560447402c8bc6a7ef9db2652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005957c000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a869452942847402ea1cac083126f47402f0e560418937547402f028f5c28f5c347402fb6c8b439581047402ebd70a3d70a3d47402ec9374bc6a7f047402d722d0e56041947402bb53f7ced916847402cddb22d0e560447402c8bc6a7ef9db2652e\"\n      }\n    },\n    \"counters/num_env_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"counters/num_env_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"counters/num_agent_steps_sampled\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"counters/num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"8005953b000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294284aa00302004a401302004ae02202004a803202004a20420200652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059554000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294284a80b501004a20c501004ac0d401004a60e401004a00f401004aa00302004a401302004ae02202004a803202004a20420200652e\"\n      }\n    },\n    \"perf/cpu_util_percent\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308fcc00ffcc00f394094869452946807680d43082322222222623b4094869452946807680d43087777777777773a4094869452946807680d4308bbbbbbbbbb1b3b4094869452946807680d43081ff8811ff8813d409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243086866666666263b4094869452946807680d43087777777777f7394094869452946807680d4308343333333333394094869452946807680d43088888888888483a4094869452946807680d4308f0eeeeeeeeae3a4094869452946807680d4308fcc00ffcc00f394094869452946807680d43082322222222623b4094869452946807680d43087777777777773a4094869452946807680d4308bbbbbbbbbb1b3b4094869452946807680d43081ff8811ff8813d409486945294652e\"\n      }\n    },\n    \"perf/ram_util_percent\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308412ff4422f74554094869452946807680d4308212222222272554094869452946807680d4308cdcccccccc6c554094869452946807680d43089b9999999969554094869452946807680d430862277662277655409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308444444444474554094869452946807680d430833333333337b554094869452946807680d4308c10ffcc00f7c554094869452946807680d4308bcbbbbbbbb7b554094869452946807680d4308545555555575554094869452946807680d4308412ff4422f74554094869452946807680d4308212222222272554094869452946807680d4308cdcccccccc6c554094869452946807680d43089b9999999969554094869452946807680d430862277662277655409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/num_agent_steps_trained\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d430800000000000060409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d4308000000000000604094869452946807680d430800000000000060409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/num_grad_updates_lifetime\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000006084dd4094869452946807680d430800000000e06cde4094869452946807680d4308000000006055df4094869452946807680d430800000000f01ee04094869452946807680d4308000000003093e0409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430800000000e0f9d84094869452946807680d43080000000060e2d94094869452946807680d430800000000e0cada4094869452946807680d43080000000060b3db4094869452946807680d430800000000e09bdc4094869452946807680d4308000000006084dd4094869452946807680d430800000000e06cde4094869452946807680d4308000000006055df4094869452946807680d430800000000f01ee04094869452946807680d4308000000003093e0409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/diff_num_grad_updates_vs_sampler_policy\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d4094869452946807680d43080000000000087d409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/allreduce_latency\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/grad_gnorm\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430851dd0e45f02c234094869452946807680d4308bf95f099ee35274094869452946807680d430885dfcaddee19244094869452946807680d43084a5a9c1af933244094869452946807680d4308137e2b6f5a5a26409486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430863bd2878aaf8244094869452946807680d430888b97fce402a244094869452946807680d430866975dec1687234094869452946807680d4308611f173ee4ed244094869452946807680d4308c9f29796d0e4244094869452946807680d430851dd0e45f02c234094869452946807680d4308bf95f099ee35274094869452946807680d430885dfcaddee19244094869452946807680d43084a5a9c1af933244094869452946807680d4308137e2b6f5a5a26409486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/cur_kl_coeff\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f94869452946807680d43089b9999999999c93f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/cur_lr\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f94869452946807680d43082f431cebe2361a3f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/total_loss\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430854e83a5104e5e23f94869452946807680d4308a04d832f4682e23f94869452946807680d4308dca11af3aef2db3f94869452946807680d43083ae7dcef3444db3f94869452946807680d4308a1eb14c888e7da3f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430874ced92a6b56e33f94869452946807680d4308e62f195bb475e33f94869452946807680d4308500edef1c920e53f94869452946807680d4308982c7f64b654e43f94869452946807680d43087ffa99255db3e43f94869452946807680d430854e83a5104e5e23f94869452946807680d4308a04d832f4682e23f94869452946807680d4308dca11af3aef2db3f94869452946807680d43083ae7dcef3444db3f94869452946807680d4308a1eb14c888e7da3f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/policy_loss\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243088b93b4f810e58cbf94869452946807680d430814eb4571afce8abf94869452946807680d43086a409bc6c5d985bf94869452946807680d4308474f70f469bb8ebf94869452946807680d43082be1b7128a767abf9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308c2a01ccc327392bf94869452946807680d4308527b20f58cf591bf94869452946807680d4308eb45b1dea0c68dbf94869452946807680d43082eece3c279e189bf94869452946807680d430872611fc7cd5188bf94869452946807680d43088b93b4f810e58cbf94869452946807680d430814eb4571afce8abf94869452946807680d43086a409bc6c5d985bf94869452946807680d4308474f70f469bb8ebf94869452946807680d43082be1b7128a767abf9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/vf_loss\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430834023c20aa4ae33f94869452946807680d4308ece3e2cc33dee23f94869452946807680d43082a43c6ba137edc3f94869452946807680d4308fae9a72aba15dc3f94869452946807680d43083dc1d1027038db3f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b00749462430888b97fd2b5d9e33f94869452946807680d4308a07efa5daaf5e33f94869452946807680d4308adb596b11988e53f94869452946807680d43080fa41683d2abe43f94869452946807680d4308409bdae33203e53f94869452946807680d430834023c20aa4ae33f94869452946807680d4308ece3e2cc33dee23f94869452946807680d43082a43c6ba137edc3f94869452946807680d4308fae9a72aba15dc3f94869452946807680d43083dc1d1027038db3f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/vf_explained_var\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308b8127eeba8fbd7bf94869452946807680d4308141c3dc16fa3bdbf94869452946807680d4308cb90a91c13a5e3bf94869452946807680d4308d8f87c2d5c49e0bf94869452946807680d4308e56050aeb382d8bf9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308f2f91a5f726ee1bf94869452946807680d4308f366356f11f4d2bf94869452946807680d430825fc5602f11bd7bf94869452946807680d43089ca4c5e9b5fcd2bf94869452946807680d4308cc2ebbcc8a79dfbf94869452946807680d4308b8127eeba8fbd7bf94869452946807680d4308141c3dc16fa3bdbf94869452946807680d4308cb90a91c13a5e3bf94869452946807680d4308d8f87c2d5c49e0bf94869452946807680d4308e56050aeb382d8bf9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/kl\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243084c960fa6126a813f94869452946807680d430820e67e0a6920833f94869452946807680d430882693439dc21863f94869452946807680d4308b88624cfceb5863f94869452946807680d430845139573a42f7f3f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308eb13301eab62843f94869452946807680d430817f679bcffa3833f94869452946807680d4308e886654d74bd833f94869452946807680d430833c7c4963b84843f94869452946807680d43083919c4c1d4cd853f94869452946807680d43084c960fa6126a813f94869452946807680d430820e67e0a6920833f94869452946807680d430882693439dc21863f94869452946807680d4308b88624cfceb5863f94869452946807680d430845139573a42f7f3f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/entropy\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308f67161276336e53f94869452946807680d43084bf8ad84b7b4e23f94869452946807680d4308756c4b57f58ae63f94869452946807680d4308a7d0754ad036e63f94869452946807680d430812af22899620e63f9486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b0074946243081f48edd1c033e83f94869452946807680d430803da34807fa3e63f94869452946807680d4308a7016d6acaa3e83f94869452946807680d43082a749db2ec9fe93f94869452946807680d43089faff1d90930e93f94869452946807680d4308f67161276336e53f94869452946807680d43084bf8ad84b7b4e23f94869452946807680d4308756c4b57f58ae63f94869452946807680d4308a7d0754ad036e63f94869452946807680d430812af22899620e63f9486945294652e\"\n      }\n    },\n    \"info/learner/default_policy/learner_stats/entropy_coeff\": {\n      \"5\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"800595d6000000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0586945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      },\n      \"10\": {\n        \"_type\": \"CLOUDPICKLE_FALLBACK\",\n        \"value\": \"80059535010000000000008c0b636f6c6c656374696f6e73948c056465717565949394294b0a86945294288c156e756d70792e636f72652e6d756c74696172726179948c067363616c61729493948c056e756d7079948c0564747970659493948c02663894898887945294284b038c013c944e4e4e4affffffff4affffffff4b007494624308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d4308000000000000000094869452946807680d430800000000000000009486945294652e\"\n      }\n    }\n  },\n  \"checkpoint_manager\": {\n    \"_type\": \"CLOUDPICKLE_FALLBACK\",\n    \"value\": \"80059584010000000000008c267261792e747261696e2e5f696e7465726e616c2e636865636b706f696e745f6d616e61676572948c125f436865636b706f696e744d616e616765729493942981947d94288c125f636865636b706f696e745f636f6e666967948c097261792e747261696e948c10436865636b706f696e74436f6e6669679493942981947d94288c0b6e756d5f746f5f6b656570944e8c1a636865636b706f696e745f73636f72655f617474726962757465944e8c16636865636b706f696e745f73636f72655f6f72646572948c036d6178948c14636865636b706f696e745f6672657175656e6379944bc88c11636865636b706f696e745f61745f656e6494888c1a5f636865636b706f696e745f6b6565705f616c6c5f72616e6b73948c0a44455052454341544544948c1f5f636865636b706f696e745f75706c6f61645f66726f6d5f776f726b65727394681275628c135f636865636b706f696e745f726573756c7473945d948c195f6c61746573745f636865636b706f696e745f726573756c74944e75622e\"\n  }\n}"]], "runner_data": {"_earliest_stopping_actor": Infinity, "_actor_cleanup_timeout": 600, "_actor_force_cleanup_timeout": 10, "_reuse_actors": false, "_buffer_length": 1, "_buffer_min_time_s": 0.0, "_buffer_max_time_s": 100.0, "_max_pending_trials": 200, "_metric": null, "_total_time": 291.44240403175354, "_iteration": 3026, "_has_errored": false, "_fail_fast": false, "_print_trial_errors": true, "_cached_trial_decisions": {}, "_queued_trial_decisions": {}, "_should_stop_experiment": false, "_stopper": {"_type": "CLOUDPICKLE_FALLBACK", "value": "8005952c000000000000008c157261792e74756e652e73746f707065722e6e6f6f70948c0b4e6f6f7053746f707065729493942981942e"}, "_start_time": 1736143383.7840624, "_session_str": "2025-01-06_14-03-03", "_checkpoint_period": "auto", "_trial_checkpoint_config": {"_type": "CLOUDPICKLE_FALLBACK", "value": "800595f2000000000000008c097261792e747261696e948c10436865636b706f696e74436f6e6669679493942981947d94288c0b6e756d5f746f5f6b656570944e8c1a636865636b706f696e745f73636f72655f617474726962757465944e8c16636865636b706f696e745f73636f72655f6f72646572948c036d6178948c14636865636b706f696e745f6672657175656e6379944bc88c11636865636b706f696e745f61745f656e6494888c1a5f636865636b706f696e745f6b6565705f616c6c5f72616e6b73948c0a44455052454341544544948c1f5f636865636b706f696e745f75706c6f61645f66726f6d5f776f726b65727394680c75622e"}, "_resumed": false}, "stats": {"start_time": 1736143383.7840624}}