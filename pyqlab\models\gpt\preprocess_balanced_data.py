#!/usr/bin/env python3
"""
数据预处理脚本 - 解决极端token分布不平衡问题
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

def analyze_token_distribution(data_path, market='fut', block_name='sf', period='min5'):
    """分析token分布"""
    from pyqlab.data.dataset.pipeline import Pipeline

    pipe = Pipeline(
        data_path, market, block_name, period,
        2024, 2024, '', '', 20, 1, None
    )
    data = pipe.get_data()

    print(f"数据形状: {data.shape}")
    print(f"Bar列统计:")
    print(f"  最小值: {data['bar'].min()}")
    print(f"  最大值: {data['bar'].max()}")
    print(f"  唯一值数量: {data['bar'].nunique()}")

    # 分析token分布
    token_counts = Counter(data['bar'].values)
    total_tokens = len(data)

    print(f"\n=== Token分布分析 ===")
    print(f"总token数: {total_tokens:,}")
    print(f"唯一token数: {len(token_counts)}")

    # 找出高频token
    sorted_tokens = token_counts.most_common(20)
    print(f"\n前20个最频繁的tokens:")
    for token, count in sorted_tokens:
        percentage = count / total_tokens * 100
        print(f"  Token {token}: {count:,} 次 ({percentage:.2f}%)")

    # 分析不平衡程度
    max_count = max(token_counts.values())
    min_count = min(token_counts.values())
    imbalance_ratio = max_count / min_count

    print(f"\n=== 不平衡分析 ===")
    print(f"最高频次: {max_count:,}")
    print(f"最低频次: {min_count}")
    print(f"不平衡比例: {imbalance_ratio:.1f}:1")

    return data, token_counts

def create_balanced_dataset(data, token_counts, max_samples_per_token=1000):
    """创建平衡的数据集"""
    print(f"\n=== 创建平衡数据集 ===")
    print(f"每个token最大样本数: {max_samples_per_token}")

    # 按token分组
    grouped = data.groupby('bar')
    balanced_data = []

    for token, group in grouped:
        count = len(group)
        if count > max_samples_per_token:
            # 随机采样
            sampled = group.sample(n=max_samples_per_token, random_state=42)
            print(f"Token {token}: {count} -> {max_samples_per_token} (采样)")
        else:
            sampled = group
            print(f"Token {token}: {count} (保持)")

        balanced_data.append(sampled)

    balanced_df = pd.concat(balanced_data, ignore_index=True)

    # 打乱数据
    balanced_df = balanced_df.sample(frac=1, random_state=42).reset_index(drop=True)

    print(f"\n平衡后数据形状: {balanced_df.shape}")

    # 重新分析分布
    new_token_counts = Counter(balanced_df['bar'].values)
    max_count = max(new_token_counts.values())
    min_count = min(new_token_counts.values())
    new_imbalance_ratio = max_count / min_count

    print(f"新的不平衡比例: {new_imbalance_ratio:.1f}:1")

    return balanced_df

def save_balanced_data(balanced_df, output_path):
    """保存平衡后的数据"""
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # 确保包含所有必要的列
    required_cols = ['symbol', 'datetime', 'bar', 'seq_volatility', 'seq_amplitude', 'seq_change', 'fut_change']
    missing_cols = [col for col in required_cols if col not in balanced_df.columns]
    if missing_cols:
        print(f"警告：缺少列 {missing_cols}")
        print(f"当前列: {list(balanced_df.columns)}")

    balanced_df.to_csv(output_path, index=False)
    print(f"\n平衡数据已保存到: {output_path}")
    print(f"保存的列: {list(balanced_df.columns)}")

def main():
    """主函数"""
    data_path = 'f:/featdata/barenc/db2'
    output_path = 'f:/featdata/barenc/db2_balanced/bar_fut_sf_min5_balanced.csv'

    print("开始数据预处理...")

    # 分析原始数据
    data, token_counts = analyze_token_distribution(data_path)

    # 创建平衡数据集
    balanced_data = create_balanced_dataset(data, token_counts, max_samples_per_token=500)

    # 保存平衡数据
    save_balanced_data(balanced_data, output_path)

    print("\n=== 预处理完成 ===")
    print("现在可以使用平衡后的数据进行训练:")
    print(f"  数据路径: {Path(output_path).parent}")
    print("  建议使用标准训练方法，不需要额外的平衡技术")

if __name__ == "__main__":
    main()
