"""
使用交叉验证训练K线LLM模型示例
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
import argparse
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入模型和训练器
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.cv_trainer import run_cross_validation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('candlestick_llm_cv_training.log')
    ]
)
logger = logging.getLogger('CandlestickLLM_CV_Training')


def load_data(data_path, symbols=None, start_date=None, end_date=None):
    """
    加载K线数据
    
    Args:
        data_path: 数据路径
        symbols: 证券代码列表
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        数据列表和代码ID列表
    """
    data_list = []
    code_ids = []
    
    # 如果未指定证券代码，则使用目录中的所有CSV文件
    if symbols is None:
        symbols = [f.split('.')[0] for f in os.listdir(data_path) if f.endswith('.csv')]
    
    for i, symbol in enumerate(symbols):
        file_path = os.path.join(data_path, f"{symbol}.csv")
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            continue
        
        # 读取数据
        df = pd.read_csv(file_path)
        
        # 确保datetime列存在
        if 'datetime' not in df.columns and 'date' in df.columns:
            df['datetime'] = pd.to_datetime(df['date'])
        elif 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 过滤日期范围
        if start_date is not None:
            df = df[df['datetime'] >= pd.to_datetime(start_date)]
        if end_date is not None:
            df = df[df['datetime'] <= pd.to_datetime(end_date)]
        
        # 确保数据按时间排序
        df = df.sort_values('datetime')
        
        # 检查数据长度
        if len(df) < 200:  # 确保有足够的数据进行训练
            logger.warning(f"数据长度不足: {symbol}, 长度: {len(df)}")
            continue
        
        # 添加到列表
        data_list.append(df)
        code_ids.append(i)
        
        logger.info(f"加载数据: {symbol}, 长度: {len(df)}")
    
    return data_list, code_ids


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='使用交叉验证训练K线LLM模型')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据目录路径')
    parser.add_argument('--symbols', type=str, nargs='+', help='证券代码列表')
    parser.add_argument('--start_date', type=str, help='开始日期')
    parser.add_argument('--end_date', type=str, help='结束日期')
    
    # 模型参数
    parser.add_argument('--model_type', type=str, default='advanced', choices=['basic', 'advanced'], help='模型类型')
    parser.add_argument('--vocab_size', type=int, default=512, help='词汇表大小')
    parser.add_argument('--n_layer', type=int, default=6, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--n_embd', type=int, default=256, help='嵌入维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout率')
    parser.add_argument('--tokenizer_type', type=str, default='nonlinear', choices=['basic', 'nonlinear'], help='Tokenizer类型')
    
    # 训练参数
    parser.add_argument('--n_splits', type=int, default=5, help='交叉验证折数')
    parser.add_argument('--cv_type', type=str, default='kfold', choices=['kfold', 'timeseries'], help='交叉验证类型')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--batch_size', type=int, default=32, help='批大小')
    parser.add_argument('--learning_rate', type=float, default=5e-4, help='学习率')
    parser.add_argument('--max_epochs', type=int, default=10, help='最大训练轮数')
    parser.add_argument('--checkpoint_dir', type=str, default='./checkpoints', help='检查点保存目录')
    parser.add_argument('--save_best_models', action='store_true', help='是否保存每个折的最佳模型')
    parser.add_argument('--ensemble_prediction', action='store_true', help='是否使用集成预测')
    
    args = parser.parse_args()
    
    # 创建检查点目录
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    
    # 加载数据
    data_list, code_ids = load_data(
        args.data_path,
        args.symbols,
        args.start_date,
        args.end_date
    )
    
    if not data_list:
        logger.error("没有加载到有效数据")
        return
    
    logger.info(f"加载了 {len(data_list)} 个证券的数据")
    
    # 创建tokenizer
    if args.tokenizer_type == 'basic':
        tokenizer = CandlestickTokenizer(vocab_size=args.vocab_size)
    else:
        tokenizer = NonlinearCandlestickTokenizer(vocab_size=args.vocab_size)
    
    # 设置模型参数
    model_params = {
        'vocab_size': args.vocab_size,
        'n_layer': args.n_layer,
        'n_head': args.n_head,
        'n_embd': args.n_embd,
        'dropout': args.dropout,
        'use_time_features': True
    }
    
    # 选择模型类
    if args.model_type == 'basic':
        model_class = CandlestickLLM
    else:
        model_class = AdvancedCandlestickLLM
    
    # 运行交叉验证训练
    cv_trainer = run_cross_validation(
        model_class=model_class,
        model_params=model_params,
        tokenizer=tokenizer,
        train_data=data_list,
        train_code_ids=code_ids,
        n_splits=args.n_splits,
        cv_type=args.cv_type,
        seq_len=args.seq_len,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        max_epochs=args.max_epochs,
        checkpoint_dir=args.checkpoint_dir,
        save_best_models=args.save_best_models,
        ensemble_prediction=args.ensemble_prediction
    )
    
    logger.info("交叉验证训练完成")
    
    # 如果启用了集成预测，可以进行测试
    if args.ensemble_prediction and args.save_best_models:
        # 使用第一个数据集的最后部分进行测试
        test_df = data_list[0].iloc[-100:]
        code_id = code_ids[0]
        
        try:
            # 生成集成预测
            predicted_df = cv_trainer.generate_ensemble_prediction(
                input_df=test_df,
                code_id=code_id,
                max_new_tokens=5,
                temperature=0.8
            )
            
            if predicted_df is not None:
                logger.info(f"集成预测结果:\n{predicted_df}")
            else:
                logger.warning("集成预测失败")
        except Exception as e:
            logger.error(f"集成预测时出现错误: {e}")


if __name__ == "__main__":
    main()
