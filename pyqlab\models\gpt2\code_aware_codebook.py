"""
支持证券代码维度的码本模型，用于ONNX导出
"""

import os
import torch
import torch.nn as nn
import numpy as np
import onnx
import onnxruntime as ort
from typing import Tuple, List, Dict, Optional, Union


class CodeAwareEncoder(nn.Module):
    """
    支持证券代码维度的编码器模型，使用欧氏距离计算最近的码本向量
    """
    def __init__(self, codebook_weights, code_size=100, code_dim=5):
        """
        初始化编码器模型

        Args:
            codebook_weights: 码本权重，形状为[num_embeddings, embedding_dim]
            code_size: 证券代码数量
            code_dim: 证券代码嵌入维度
        """
        super(CodeAwareEncoder, self).__init__()
        self.register_buffer('codebook', codebook_weights)
        self.embedding_dim = codebook_weights.size(1)
        self.code_size = code_size
        self.code_dim = code_dim

        # 证券代码嵌入层
        self.code_embedding = nn.Embedding(code_size, code_dim)

        # 初始化嵌入层权重
        nn.init.normal_(self.code_embedding.weight, mean=0.0, std=0.02)

    def forward(self, x, code_id=None):
        """
        前向传播 - 将输入向量映射到最近的码本索引

        Args:
            x: 输入向量 [batch_size, embedding_dim] 或 [embedding_dim]
            code_id: 证券代码ID [batch_size] 或 标量，如果为None则不使用证券代码维度
                     当批量处理时，code_id可以是一个单值（应用到所有样本）或批量值（每个样本一个code_id）

        Returns:
            码本索引 [batch_size] 或 标量
        """
        # 确保输入是二维的
        input_is_1d = False
        if x.dim() == 1:
            x = x.unsqueeze(0)  # [embedding_dim] -> [1, embedding_dim]
            input_is_1d = True

        # 确保x是float32类型
        if x.dtype != torch.float32:
            x = x.to(torch.float32)

        batch_size = x.size(0)

        # 处理证券代码嵌入
        if code_id is not None:
            # 处理不同类型的code_id输入
            if isinstance(code_id, int) or (isinstance(code_id, torch.Tensor) and code_id.dim() == 0):
                # 单个标量值，扩展到整个批次
                code_id = torch.full((batch_size,), code_id, dtype=torch.long, device=x.device)
            elif isinstance(code_id, torch.Tensor):
                # 确保code_id是long类型（用于嵌入层）
                if code_id.dtype != torch.long:
                    code_id = code_id.to(torch.long)

                if code_id.dim() == 1:
                    # 一维张量，检查长度是否匹配批次大小
                    if code_id.size(0) == 1 and batch_size > 1:
                        # 单个值，扩展到整个批次
                        code_id = code_id.repeat(batch_size)
                    elif code_id.size(0) != batch_size:
                        # 长度不匹配，抛出错误
                        raise ValueError(f"code_id批量大小({code_id.size(0)})与输入批量大小({batch_size})不匹配")
                else:
                    # 高维张量，不支持
                    raise ValueError(f"不支持{code_id.dim()}维的code_id")

            # 获取证券代码嵌入
            code_emb = self.code_embedding(code_id)

            # 调整向量
            x = x + 0.1 * code_emb[:, :self.embedding_dim]

        # 计算欧氏距离
        # 展开 (x - codebook)^2 = x^2 - 2*x*codebook + codebook^2
        x_norm = torch.sum(x**2, dim=1, keepdim=True)  # [batch_size, 1]
        codebook_norm = torch.sum(self.codebook**2, dim=1)  # [num_embeddings]

        # 计算 -2 * x * codebook^T
        dist = x_norm - 2 * torch.matmul(x, self.codebook.t()) + codebook_norm

        # 找到最近的码向量索引
        indices = torch.argmin(dist, dim=1)

        # 如果输入是一维的，返回标量
        if input_is_1d:
            return indices[0]

        return indices

    def to_onnx(self, onnx_path, input_shape=(2, 5)):
        """
        导出为ONNX格式

        Args:
            onnx_path: ONNX文件路径
            input_shape: 输入形状，默认为(2, 5)表示两个5维向量的批量

        Returns:
            是否成功导出
        """
        try:
            # 创建示例输入 - 使用批量大小为2的示例
            batch_size = input_shape[0]
            dummy_input = torch.randn(input_shape, dtype=torch.float32)  # 保持float32类型

            # 创建批量的code_id - 每个样本一个不同的code_id，使用int32类型
            dummy_code_id = torch.randint(0, self.code_size, (batch_size,), dtype=torch.int32)

            # 打印调试信息
            print(f"编码器输入形状: {dummy_input.shape}, 类型: {dummy_input.dtype}")
            print(f"编码器输入示例: {dummy_input[0]}")
            print(f"证券代码ID形状: {dummy_code_id.shape}, 类型: {dummy_code_id.dtype}, 值: {dummy_code_id}")

            # 创建一个包装类，确保输出是int32类型
            class Int32OutputWrapper(torch.nn.Module):
                def __init__(self, model):
                    super().__init__()
                    self.model = model

                def forward(self, x, code_id):
                    # 调用原始模型
                    output = self.model(x, code_id)
                    # 确保输出是int32类型
                    return output.to(torch.int32)

            # 创建包装模型
            wrapped_model = Int32OutputWrapper(self)

            # 导出为ONNX格式
            torch.onnx.export(
                wrapped_model,
                (dummy_input, dummy_code_id),
                onnx_path,
                export_params=True,
                opset_version=11,  # 使用较低版本的opset
                do_constant_folding=True,
                input_names=['input_vector', 'code_id'],
                output_names=['token_id'],
                dynamic_axes={
                    'input_vector': {0: 'batch_size'},
                    'code_id': {0: 'batch_size'},
                    'token_id': {0: 'batch_size'}
                }
            )

            # 验证导出的模型
            model = onnx.load(onnx_path)
            onnx.checker.check_model(model)

            # 打印模型输入输出类型
            print("验证ONNX模型输入输出类型:")
            for input in model.graph.input:
                print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
            for output in model.graph.output:
                print(f"输出 {output.name}: {output.type.tensor_type.elem_type}")

            # 测试导出的模型
            session = ort.InferenceSession(onnx_path)
            output_name = session.get_outputs()[0].name

            # 打印会话输入输出信息
            print("ONNX会话输入信息:")
            for input in session.get_inputs():
                print(f"名称: {input.name}, 形状: {input.shape}, 类型: {input.type}")

            print("ONNX会话输出信息:")
            for output in session.get_outputs():
                print(f"名称: {output.name}, 形状: {output.shape}, 类型: {output.type}")

            # 准备输入
            onnx_input = {
                'input_vector': dummy_input.numpy().astype(np.float32),
                'code_id': dummy_code_id.numpy().astype(np.int32)
            }

            # 运行推理
            onnx_output = session.run([output_name], onnx_input)

            print(f"ONNX编码器输出: {onnx_output[0]}")
            print(f"ONNX输出类型: {onnx_output[0].dtype}")
            print(f"PyTorch编码器输出: {self(dummy_input, dummy_code_id).to(torch.int32).numpy()}")

            print(f"成功导出编码器模型为ONNX格式: {onnx_path}")
            return True
        except Exception as e:
            print(f"导出编码器模型为ONNX格式时出错: {e}")
            return False


class CodeAwareDecoder(nn.Module):
    """
    支持证券代码维度的解码器模型，使用嵌入层查找码本向量
    """
    def __init__(self, codebook_weights, num_embeddings, embedding_dim, code_size=100, code_dim=5):
        """
        初始化解码器模型

        Args:
            codebook_weights: 码本权重
            num_embeddings: 码本大小
            embedding_dim: 嵌入维度
            code_size: 证券代码数量
            code_dim: 证券代码嵌入维度
        """
        super(CodeAwareDecoder, self).__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.code_size = code_size
        self.code_dim = code_dim

        # 创建嵌入层
        self.embedding = nn.Embedding(num_embeddings, embedding_dim)
        self.embedding.weight.data.copy_(codebook_weights)

        # 证券代码嵌入层
        self.code_embedding = nn.Embedding(code_size, code_dim)

        # 初始化嵌入层权重
        nn.init.normal_(self.code_embedding.weight, mean=0.0, std=0.02)

    def forward(self, indices, code_id=None):
        """
        前向传播 - 将码本索引映射回K线向量

        Args:
            indices: 码本索引 [batch_size] 或 标量
            code_id: 证券代码ID [batch_size] 或 标量，如果为None则不使用证券代码维度
                     当批量处理时，code_id可以是一个单值（应用到所有样本）或批量值（每个样本一个code_id）

        Returns:
            量化后的向量 [batch_size, embedding_dim]
        """
        # 确保indices是二维的
        indices_is_scalar = False
        if isinstance(indices, int) or (isinstance(indices, torch.Tensor) and indices.dim() == 0):
            indices = torch.tensor([indices], dtype=torch.long, device=self.embedding.weight.device)
            indices_is_scalar = True

        # 确保indices是long类型（用于嵌入层）
        if isinstance(indices, torch.Tensor) and indices.dtype != torch.long:
            indices = indices.to(torch.long)

        # 获取码本向量
        vectors = self.embedding(indices)
        batch_size = vectors.size(0)

        # 处理证券代码嵌入
        if code_id is not None:
            # 处理不同类型的code_id输入
            if isinstance(code_id, int) or (isinstance(code_id, torch.Tensor) and code_id.dim() == 0):
                # 单个标量值，扩展到整个批次
                code_id = torch.full((batch_size,), code_id, dtype=torch.long, device=vectors.device)
            elif isinstance(code_id, torch.Tensor):
                # 确保code_id是long类型（用于嵌入层）
                if code_id.dtype != torch.long:
                    code_id = code_id.to(torch.long)

                if code_id.dim() == 1:
                    # 一维张量，检查长度是否匹配批次大小
                    if code_id.size(0) == 1 and batch_size > 1:
                        # 单个值，扩展到整个批次
                        code_id = code_id.repeat(batch_size)
                    elif code_id.size(0) != batch_size:
                        # 长度不匹配，抛出错误
                        raise ValueError(f"code_id批量大小({code_id.size(0)})与输入批量大小({batch_size})不匹配")
                else:
                    # 高维张量，不支持
                    raise ValueError(f"不支持{code_id.dim()}维的code_id")

            # 获取证券代码嵌入
            code_emb = self.code_embedding(code_id)

            # 调整向量 - 始终使用批量处理方式
            vectors = vectors + 0.1 * code_emb[:, :self.embedding_dim]

        # 确保输出是float32类型
        if vectors.dtype != torch.float32:
            vectors = vectors.to(torch.float32)

        # 如果输入是标量，返回一维向量
        if indices_is_scalar:
            return vectors.squeeze(0)

        return vectors

    def to_onnx(self, onnx_path, input_shape=(2,)):
        """
        导出为ONNX格式

        Args:
            onnx_path: ONNX文件路径
            input_shape: 输入形状，默认为(2,)表示两个标量索引的批量

        Returns:
            是否成功导出
        """
        try:
            # 创建示例输入 - 使用批量大小为2的示例
            batch_size = input_shape[0]
            # 使用int32类型的输入
            dummy_input = torch.randint(0, self.num_embeddings, input_shape, dtype=torch.int32)

            # 创建批量的code_id - 每个样本一个不同的code_id，使用int32类型
            dummy_code_id = torch.randint(0, self.code_size, (batch_size,), dtype=torch.int32)

            # 打印调试信息
            print(f"解码器输入形状: {dummy_input.shape}, 类型: {dummy_input.dtype}")
            print(f"解码器输入示例: {dummy_input}")
            print(f"证券代码ID形状: {dummy_code_id.shape}, 类型: {dummy_code_id.dtype}, 值: {dummy_code_id}")

            # 测试前向传播
            with torch.no_grad():
                output = self(dummy_input, dummy_code_id)
                print(f"解码器输出形状: {output.shape}, 类型: {output.dtype}")
                if batch_size > 0:
                    print(f"解码器输出示例: {output[0].detach().numpy()}")

            # 创建一个包装类，确保输出是float32类型
            class Float32OutputWrapper(torch.nn.Module):
                def __init__(self, model):
                    super().__init__()
                    self.model = model

                def forward(self, token_id, code_id):
                    # 调用原始模型
                    output = self.model(token_id, code_id)
                    # 确保输出是float32类型
                    return output.to(torch.float32)

            # 创建包装模型
            wrapped_model = Float32OutputWrapper(self)

            # 导出为ONNX格式
            torch.onnx.export(
                wrapped_model,
                (dummy_input, dummy_code_id),
                onnx_path,
                export_params=True,
                opset_version=11,  # 使用较低版本的opset
                do_constant_folding=True,
                input_names=['token_id', 'code_id'],
                output_names=['output_vector'],
                dynamic_axes={
                    'token_id': {0: 'batch_size'},
                    'code_id': {0: 'batch_size'},
                    'output_vector': {0: 'batch_size'}
                }
            )

            # 验证导出的模型
            model = onnx.load(onnx_path)
            onnx.checker.check_model(model)

            # 打印模型输入输出类型
            print("验证ONNX模型输入输出类型:")
            for input in model.graph.input:
                print(f"输入 {input.name}: {input.type.tensor_type.elem_type}")
            for output in model.graph.output:
                print(f"输出 {output.name}: {output.type.tensor_type.elem_type}")

            # 测试导出的模型
            session = ort.InferenceSession(onnx_path)
            output_name = session.get_outputs()[0].name

            # 打印会话输入输出信息
            print("ONNX会话输入信息:")
            for input in session.get_inputs():
                print(f"名称: {input.name}, 形状: {input.shape}, 类型: {input.type}")

            print("ONNX会话输出信息:")
            for output in session.get_outputs():
                print(f"名称: {output.name}, 形状: {output.shape}, 类型: {output.type}")

            # 准备输入
            onnx_input = {
                'token_id': dummy_input.numpy().astype(np.int32),
                'code_id': dummy_code_id.numpy().astype(np.int32)
            }

            # 运行推理
            onnx_output = session.run([output_name], onnx_input)

            print(f"ONNX解码器输出: {onnx_output[0]}")
            print(f"ONNX输出类型: {onnx_output[0].dtype}")
            print(f"PyTorch解码器输出: {self(dummy_input, dummy_code_id).to(torch.float32).detach().numpy()}")

            print(f"成功导出解码器模型为ONNX格式: {onnx_path}")
            return True
        except Exception as e:
            print(f"导出解码器模型为ONNX格式时出错: {e}")
            return False
