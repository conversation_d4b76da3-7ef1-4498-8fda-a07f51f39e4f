{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import time\n", "import pytz\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "import json"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 数据预处理"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(44709, 235)\n"]}], "source": ["sdf = pd.read_parquet('e:/featdata/sfd.parquet', engine='fastparquet')\n", "print(sdf.shape)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(44509, 235)\n"]}], "source": ["sdf['change'] = sdf['change'].astype(np.float32)\n", "sdf = sdf[sdf['change'] != 0.0]\n", "print(sdf.shape)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['A', 'AG', 'AL', 'AP', 'B', 'BU', 'C', 'CF', 'CJ', 'CS', 'CU',\n", "       'CY', 'EG', 'FG', 'HC', 'I', 'J', 'J<PERSON>', 'J<PERSON>', 'L', 'M', 'MA', 'NI',\n", "       'OI', 'P', 'PB', 'PP', 'RB', 'RM', 'RU', 'SC', 'SF', 'SM', 'SN',\n", "       'SP', 'SR', 'TA', 'V', 'Y', 'ZC', 'ZN'], dtype=object)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["sdf.code.unique()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["asdf = sdf[(sdf['code'] == 'A')]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- 选择列"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def get_factor_cols():\n", "    \"\"\"\n", "    因子列名称\n", "    \"\"\"\n", "    col_names = ['code', 'date', 'change']\n", "    for name in SEL_SHORT_FACTOR_NAMES:\n", "        if name in TWO_VAL_FACTOR_NAMES:\n", "            col_names.append(f\"{name}_1\")\n", "            col_names.append(f\"{name}_2\")\n", "        else:\n", "            col_names.append(f\"{name}_2\")\n", "    return col_names"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["col_names = get_factor_cols()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["df = asdf[col_names]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["df.to_csv('e:/featdata/feat.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}