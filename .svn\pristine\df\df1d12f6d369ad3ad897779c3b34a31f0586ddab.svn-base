{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "a72f68cc148b279a81ef0488079e33db58bd3e723d7638e46009e47145dcc430"}, "kernelspec": {"display_name": "Python 3.8.10 ('base')", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}