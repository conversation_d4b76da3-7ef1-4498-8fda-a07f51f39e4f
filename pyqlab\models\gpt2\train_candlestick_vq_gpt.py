"""
训练CandlestickVQGPT模型的脚本
"""

import os
import json
import time
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torch.optim import Adam<PERSON>
from torch.cuda.amp import GradScaler
from torch.optim.lr_scheduler import OneCycleLR
from tqdm import tqdm
import matplotlib.pyplot as plt


# 导入自定义模块
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.candlestick_vq_dataset import CandlestickDataset
from pyqlab.models.gpt2.utils import get_data_files, load_single_data

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("train_candlestick_vq_gpt.log")
    ]
)
logger = logging.getLogger(__name__)


def create_tokenizer(args):
    """创建tokenizer"""
    logger.info("创建tokenizer...")

    # 选择向量化方法
    if args.vectorization_method == 'atr_based':
        vector_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vector_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vector_method = VectorizationMethod.LOG_RETURN
    elif args.vectorization_method == 'zscore':
        vector_method = VectorizationMethod.ZSCORE
    elif args.vectorization_method == 'minmax':
        vector_method = VectorizationMethod.MINMAX
    elif args.vectorization_method == 'candlestick_features':
        vector_method = VectorizationMethod.CANDLESTICK_FEATURES
    else:
        raise ValueError(f"Unknown vectorization method: {args.vectorization_method}")

    # 创建tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim,
        atr_period=args.atr_period,
        ma_volume_period=args.ma_volume_period,
        vectorization_method=vector_method
    )

    logger.info(f"Tokenizer创建完成，词汇表大小: {tokenizer.vocab_size}")
    return tokenizer

def create_dataset(data, code_ids, tokenizer, args):
    """创建完整数据集"""

    dataset = CandlestickDataset(
        data,
        code_ids,
        tokenizer,
        seq_len=args.seq_len,
        stride=args.stride
    )

    logger.info(f"完整数据集样本数: {len(dataset)}")

    return dataset

def split_dataset(dataset, val_ratio, seed=42, shuffle=True):
    """分割数据集为训练集和验证集

    Args:
        dataset: 完整数据集
        val_ratio: 验证集比例
        seed: 随机种子
        shuffle: 是否随机打乱数据集，True为随机划分，False为按时间顺序划分
    """
    total_samples = len(dataset)
    val_size = int(total_samples * val_ratio)
    train_size = total_samples - val_size

    if shuffle:
        # 随机打乱划分：随机选择训练集和验证集
        import random
        random.seed(seed)

        # 创建所有索引的列表
        all_indices = list(range(total_samples))

        # 随机打乱索引
        random.shuffle(all_indices)

        # 分割索引
        train_indices = all_indices[:train_size]
        val_indices = all_indices[train_size:]

        logger.info(f"使用随机划分方式，随机种子: {seed}")
    else:
        # 按时间顺序分割：前面的作为训练集，后面的作为验证集
        train_indices = list(range(train_size))
        val_indices = list(range(train_size, total_samples))

        logger.info("使用时间顺序划分方式")

    # 创建子数据集
    from torch.utils.data import Subset
    train_dataset = Subset(dataset, train_indices)
    val_dataset = Subset(dataset, val_indices)

    logger.info(f"训练集样本数: {len(train_dataset)}")
    logger.info(f"验证集样本数: {len(val_dataset)}")

    return train_dataset, val_dataset

def create_dataloaders(train_dataset, args, shuffle=True):
    """创建数据加载器"""
    logger.info("创建数据加载器...")

    # 创建训练数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=shuffle,
        num_workers=args.num_workers,
        pin_memory=True,
        persistent_workers=True if args.num_workers > 0 else False
    )

    return train_loader

def create_model(tokenizer, args):
    """创建模型"""
    logger.info("创建模型...")

    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=tokenizer.vocab_size,
        code_size=args.code_size,
        seq_len=args.seq_len,
        n_layer=args.n_layer,
        n_head=args.n_head,
        d_model=args.d_model,
        dropout=args.dropout,
        bias=False,
        use_time_features=args.use_time_features,
        n_time_features=8,  # 固定为8个时间特征
        label_smoothing=args.label_smoothing,
        use_auxiliary_loss=args.use_auxiliary_loss
    )

    # 移动到设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    logger.info(f"模型参数数量: {model.get_num_params():,}")
    logger.info(f"使用设备: {device}")

    return model, device

def create_optimizer_and_scheduler(model, train_loader, args):
    """创建优化器和学习率调度器"""
    logger.info("创建优化器和学习率调度器...")

    # 创建优化器
    optimizer = AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=args.weight_decay,
        betas=(0.9, 0.95)
    )

    # 计算总步数
    # 每个epoch的实际优化器步数 = ceil(len(train_loader) / grad_accum_steps)
    steps_per_epoch = (len(train_loader) + args.grad_accum_steps - 1) // args.grad_accum_steps
    total_steps = steps_per_epoch * args.epochs

    logger.info(f"训练配置:")
    logger.info(f"  每个epoch批次数: {len(train_loader)}")
    logger.info(f"  梯度累积步数: {args.grad_accum_steps}")
    logger.info(f"  每个epoch优化器步数: {steps_per_epoch}")
    logger.info(f"  总epoch数: {args.epochs}")
    logger.info(f"  总优化器步数: {total_steps}")

    # 创建学习率调度器
    scheduler = OneCycleLR(
        optimizer,
        max_lr=args.learning_rate,
        total_steps=total_steps,
        pct_start=args.warmup_ratio,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=10000.0
    )

    return optimizer, scheduler

def train(model, train_loader, val_loader, optimizer, scheduler, device, args):
    """训练模型"""
    logger.info("开始训练")

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 保存配置
    with open(os.path.join(args.save_dir, 'config.json'), 'w') as f:
        json.dump(vars(args), f, indent=4)

    # 初始化混合精度训练
    scaler = GradScaler(enabled=args.mixed_precision)

    # 初始化训练状态
    best_val_loss = float('inf')
    no_improve_count = 0
    global_step = 0

    # 记录训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'lr': []
    }

    # 训练循环
    for epoch in range(args.epochs):
        logger.info(f"Epoch {epoch+1}/{args.epochs}")

        # 初始化进度条
        progress_bar = tqdm(total=len(train_loader), desc=f"Epoch {epoch+1}/{args.epochs}")

        # 初始化统计
        epoch_loss = 0
        epoch_samples = 0
        epoch_start_time = time.time()

        # 训练模式
        model.train()

        # 训练一个epoch
        for batch_idx, batch in enumerate(train_loader):
            # 解包批次数据
            if len(batch) == 4:
                input_tokens, target_tokens, code_ids, time_features = batch
                input_tokens = input_tokens.to(device)
                target_tokens = target_tokens.to(device)
                code_ids = code_ids.to(device)
                if time_features is not None:
                    time_features = time_features.to(device)
            else:
                input_tokens, target_tokens, code_ids = batch
                input_tokens = input_tokens.to(device)
                target_tokens = target_tokens.to(device)
                code_ids = code_ids.to(device)
                time_features = None

            # 混合精度训练
            with torch.cuda.amp.autocast(enabled=args.mixed_precision):
                # 前向传播
                _, loss = model(input_tokens, code_ids, time_features, target_tokens)

            # 反向传播
            scaler.scale(loss).backward()

            # 梯度累积
            if (batch_idx + 1) % args.grad_accum_steps == 0 or (batch_idx + 1) == len(train_loader):
                # 梯度裁剪
                if args.grad_clip > 0:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)

                # 更新参数
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True)

                # 更新学习率（添加保护机制防止超出步数限制）
                if global_step < scheduler.total_steps:
                    scheduler.step()
                else:
                    logger.warning(f"跳过学习率更新，已达到最大步数 {scheduler.total_steps}")

                # 更新全局步数
                global_step += 1

            # 累加损失
            batch_size = input_tokens.size(0)
            epoch_loss += loss.item() * batch_size
            epoch_samples += batch_size

            # 更新进度条
            progress_bar.update(1)
            progress_bar.set_postfix({
                'loss': loss.item(),
                'lr': scheduler.get_last_lr()[0]
            })

            # 记录训练历史
            history['train_loss'].append(loss.item())
            history['lr'].append(scheduler.get_last_lr()[0])

            # 定期评估
            if global_step % args.eval_interval == 0:
                val_loss = evaluate(model, val_loader, device, args)
                history['val_loss'].append(val_loss)

                # 保存最佳模型
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    no_improve_count = 0
                    save_checkpoint(model, optimizer, scheduler, global_step, val_loss, args, is_best=True)
                    logger.info(f"发现更好的模型! 验证损失: {val_loss:.4f}")
                else:
                    no_improve_count += 1

                # 早停
                if args.early_stopping > 0 and no_improve_count >= args.early_stopping:
                    logger.info(f"早停! {args.early_stopping} 次评估后验证损失没有改善")
                    break

                # 切换回训练模式
                model.train()

            # 定期保存
            if args.save_interval > 0 and global_step % args.save_interval == 0:
                save_checkpoint(model, optimizer, scheduler, global_step, loss.item(), args)

        # 关闭进度条
        progress_bar.close()

        # 计算epoch统计
        epoch_loss /= epoch_samples
        epoch_time = time.time() - epoch_start_time

        logger.info(f"Epoch {epoch+1}/{args.epochs} - 损失: {epoch_loss:.4f}, 时间: {epoch_time:.2f}s")

        # 每个epoch结束后评估
        val_loss = evaluate(model, val_loader, device, args)
        history['val_loss'].append(val_loss)

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            no_improve_count = 0
            save_checkpoint(model, optimizer, scheduler, global_step, val_loss, args, is_best=True)
            logger.info(f"发现更好的模型! 验证损失: {val_loss:.4f}")
        else:
            no_improve_count += 1

        # 早停
        if args.early_stopping > 0 and no_improve_count >= args.early_stopping:
            logger.info(f"早停! {args.early_stopping} 个epoch后验证损失没有改善")
            break

        # 保存每个epoch的模型
        save_checkpoint(model, optimizer, scheduler, global_step, epoch_loss, args, suffix=f"epoch_{epoch+1}")

    # 训练循环结束后
    logger.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")

    # 加载最佳模型
    best_model_path = os.path.join(args.save_dir, f'best_model.pt')
    if os.path.exists(best_model_path):
        # 导出最佳模型为ONNX格式
        onnx_path = export_model_to_onnx(best_model_path, args, device, best_val_loss)
        if onnx_path:
            logger.info(f"最佳模型已导出为ONNX格式: {onnx_path}")
        else:
            logger.error("导出最佳模型ONNX失败")

    # 绘制训练历史
    plot_history(history, args)

    logger.info("训练完成!")
    return best_val_loss, history

def evaluate(model, val_loader, device, args):
    """评估模型"""
    model.eval()
    total_loss = 0
    total_samples = 0

    with torch.no_grad():
        for batch in val_loader:
            # 解包批次数据
            if len(batch) == 4:
                input_tokens, target_tokens, code_ids, time_features = batch
                input_tokens = input_tokens.to(device)
                target_tokens = target_tokens.to(device)
                code_ids = code_ids.to(device)
                if time_features is not None:
                    time_features = time_features.to(device)
            else:
                input_tokens, target_tokens, code_ids = batch
                input_tokens = input_tokens.to(device)
                target_tokens = target_tokens.to(device)
                code_ids = code_ids.to(device)
                time_features = None

            # 前向传播
            _, loss = model(input_tokens, code_ids, time_features, target_tokens)

            # 累加损失
            batch_size = input_tokens.size(0)
            total_loss += loss.item() * batch_size
            total_samples += batch_size

    # 计算平均损失
    avg_loss = total_loss / total_samples
    logger.info(f"验证损失: {avg_loss:.4f}")
    return avg_loss

def save_checkpoint(model, optimizer, scheduler, step, loss, args, is_best=False, suffix=None):
    """保存检查点"""
    # 创建完整的模型配置，包括从模型获取的参数
    model_config = vars(args).copy()
    model_config.update({
        'vocab_size': model.vocab_size,
        'code_size': model.code_size,
        'seq_len': model.seq_len,
        'n_layer': model.n_layer,
        'n_head': model.n_head,
        'd_model': model.d_model,
        'use_time_features': model.use_time_features,
        'n_time_features': getattr(model, 'time_embedding', None) and model.time_embedding.time_proj.in_features or 8,
        'label_smoothing': model.label_smoothing,
        'use_auxiliary_loss': model.use_auxiliary_loss
    })

    checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'step': step,
        'loss': loss,
        'config': model_config
    }

    # output_name = f"vqgpt_{args.vectorization_method}_{args.market}_{args.block_name}_{args.period}_{args.seq_len}_{args.n_layer}_{args.n_head}_{args.d_model}_{args.num_embeddings}_{avg_best_val_loss:.4f}_ls"
    if is_best:
        checkpoint_path = os.path.join(args.save_dir, 'best_model.pt')
    elif suffix:
        checkpoint_path = os.path.join(args.save_dir, f'checkpoint_{suffix}.pt')
    else:
        checkpoint_path = os.path.join(args.save_dir, f'checkpoint_step_{step}.pt')

    torch.save(checkpoint, checkpoint_path)
    logger.info(f"模型保存到 {checkpoint_path}")

def plot_history(history, args):
    """绘制训练历史"""
    plt.figure(figsize=(12, 4))

    # 绘制损失
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('步数')
    plt.ylabel('损失')
    plt.title('训练和验证损失')
    plt.legend()

    # 绘制学习率
    plt.subplot(1, 2, 2)
    plt.plot(history['lr'])
    plt.xlabel('步数')
    plt.ylabel('学习率')
    plt.title('学习率变化')

    plt.tight_layout()
    plt.savefig(os.path.join(args.save_dir, 'training_history.png'))
    plt.close()

def export_model_to_onnx(model_path, args, device, best_val_loss):
    """将模型导出为ONNX格式"""
    logger.info(f"导出模型为ONNX格式: {model_path}")

    try:
        # 加载模型
        checkpoint = torch.load(model_path, map_location=device)

        # 从checkpoint中获取模型配置，确保参数匹配
        config = checkpoint.get('config', {})

        model = CandlestickVQGPT(
            vocab_size=config.get('vocab_size', args.num_embeddings + 6),
            code_size=config.get('code_size', args.code_size),
            seq_len=config.get('seq_len', args.seq_len),
            n_layer=config.get('n_layer', args.n_layer),
            n_head=config.get('n_head', args.n_head),
            d_model=config.get('d_model', args.d_model),
            dropout=0.0,  # 推理时不需要dropout
            bias=False,
            use_time_features=config.get('use_time_features', args.use_time_features),
            n_time_features=config.get('n_time_features', 8),
            label_smoothing=0.0,  # 推理时不需要label smoothing
            use_auxiliary_loss=False  # 推理时不需要辅助损失
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        model.to(device)

        # 准备示例输入
        seq_len = config.get('seq_len', args.seq_len)
        dummy_input_tokens = torch.zeros((1, seq_len), dtype=torch.int32).to(device)
        dummy_code_ids = torch.zeros((1,), dtype=torch.int32).to(device)

        # 准备时间特征（如果使用）
        dummy_time_features = None
        use_time_features = config.get('use_time_features', args.use_time_features)
        if use_time_features:
            dummy_time_features = torch.zeros((1, seq_len, 8), dtype=torch.float32).to(device)

        # 设置导出路径，使用实际的模型配置参数
        vocab_size = config.get('vocab_size', args.num_embeddings)
        n_layer = config.get('n_layer', args.n_layer)
        n_head = config.get('n_head', args.n_head)
        d_model = config.get('d_model', args.d_model)
        output_name = f"vqgpt_{args.vectorization_method}_{args.market}_{args.block_name}_{args.period}_{seq_len}_{n_layer}_{n_head}_{d_model}_{vocab_size}_{best_val_loss:.4f}_ls"
        onnx_path = model_path.replace('best_model.pt', f'{output_name}.onnx')

        # 导出模型
        torch.onnx.export(
            model,
            (dummy_input_tokens, dummy_code_ids, dummy_time_features, None),  # 输入参数
            onnx_path,  # 输出路径
            export_params=True,  # 导出模型参数
            opset_version=15,  # ONNX操作集版本
            do_constant_folding=True,  # 常量折叠优化
            input_names=['input_tokens', 'code_ids', 'time_features'],  # 输入名称
            output_names=['logits'],  # 输出名称
            dynamic_axes={
                'input_tokens': {0: 'batch_size', 1: 'sequence_length'},
                'time_features': {0: 'batch_size', 1: 'sequence_length'} if use_time_features else None,
                'logits': {0: 'batch_size', 1: 'sequence_length'}
            }
        )

        logger.info(f"模型成功导出为ONNX格式: {onnx_path}")
        return onnx_path
    except Exception as e:
        logger.error(f"导出ONNX模型失败: {str(e)}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练CandlestickVQGPT模型')

    # 数据参数
    parser.add_argument('--data_dir', type=str, required=True, help='数据目录')
    parser.add_argument('--market', type=str, default='fut', choices=['fut', 'stk'], help='市场类型')
    parser.add_argument('--block_name', type=str, default='sf', help='市场块名称')
    parser.add_argument('--period', type=str, default='min1', choices=['day', 'min5', 'min1'], help='数据周期')
    parser.add_argument('--multi_data', action='store_true', help='是否使用多个数据文件')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')
    parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集比例')
    parser.add_argument('--stride', type=int, default=1, help='滑动窗口步长')
    parser.add_argument('--shuffle_split', action='store_true', help='是否随机打乱数据集划分（默认按时间顺序划分）')

    # Tokenizer参数
    parser.add_argument('--codebook_path', type=str, default=None, help='码本权重路径')
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=4, help='嵌入维度')
    parser.add_argument('--atr_period', type=int, default=14, help='ATR周期')
    parser.add_argument('--ma_volume_period', type=int, default=20, help='成交量移动平均周期')
    parser.add_argument('--vectorization_method', type=str, default='atr_based',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax', 'candlestick_features'],
                        help='向量化方法')

    # 模型参数
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--code_size', type=int, default=100, help='证券代码数量')
    parser.add_argument('--n_layer', type=int, default=4, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比例')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    parser.add_argument('--label_smoothing', type=float, default=0.1, help='标签平滑系数')
    parser.add_argument('--use_auxiliary_loss', action='store_true', help='是否使用辅助损失')

    # 训练参数
    parser.add_argument('--batch_size', type=int, default=64, help='批次大小')
    parser.add_argument('--epochs', type=int, default=5, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=1e-3, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='权重衰减')
    parser.add_argument('--warmup_ratio', type=float, default=0.1, help='预热比例')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪')
    parser.add_argument('--grad_accum_steps', type=int, default=1, help='梯度累积步数')
    parser.add_argument('--early_stopping', type=int, default=5, help='早停轮数')
    parser.add_argument('--mixed_precision', action='store_true', help='是否使用混合精度训练')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器工作进程数')

    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./checkpoints/candlestick_vq_gpt', help='保存目录')
    parser.add_argument('--log_interval', type=int, default=50, help='日志间隔')
    parser.add_argument('--eval_interval', type=int, default=500, help='评估间隔')
    parser.add_argument('--save_interval', type=int, default=1000, help='保存间隔')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    # 加载数据
    data_files = get_data_files(args.data_dir, args.market, args.block_name, args.period)
    if len(data_files) == 0:
        raise ValueError(f"未找到数据文件: {args.data_dir}")
    logger.info(f"找到 {len(data_files)} 个数据文件")
    data, code_ids = load_single_data(
        data_files[0],
        begin_date=args.begin_date,
        end_date=args.end_date
    )

    # 创建tokenizer
    tokenizer = create_tokenizer(args)

    # 创建完整数据集
    full_dataset = create_dataset(data, code_ids, tokenizer, args)

    # 分割数据集（根据参数选择随机划分或时间顺序划分）
    train_dataset, val_dataset = split_dataset(full_dataset, args.val_ratio, args.seed, args.shuffle_split)

    # 创建数据加载器
    train_loader = create_dataloaders(train_dataset, args)
    val_loader = create_dataloaders(val_dataset, args, shuffle=False)

    # 创建模型
    model, device = create_model(tokenizer, args)

    # 创建优化器和学习率调度器
    optimizer, scheduler = create_optimizer_and_scheduler(model, train_loader, args)

    # 训练模型
    train(model, train_loader, val_loader, optimizer, scheduler, device, args)

if __name__ == "__main__":
    main()


