#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的CANDLESTICK_FEATURES向量化方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from pyqlab.models.gpt2.vq_tokenizer import VectorizationMethod, candlestick_to_vector

def test_candlestick_features_method():
    """测试CANDLESTICK_FEATURES向量化方法"""
    print("测试CANDLESTICK_FEATURES向量化方法...")
    
    # 创建测试数据
    test_data = {
        'open': 100.0,
        'high': 105.0,
        'low': 98.0,
        'close': 103.0,
        'volume': 1000
    }
    
    ohlcv_row = pd.Series(test_data)
    prev_close = 99.0
    ma_volume = 800.0
    atr_val = 2.0  # 这个参数在CANDLESTICK_FEATURES方法中不使用
    
    print(f"测试数据: {test_data}")
    print(f"前收盘价: {prev_close}")
    print(f"成交量移动平均: {ma_volume}")
    
    # 测试5维向量
    print("\n测试5维向量:")
    vector_5d = candlestick_to_vector(
        ohlcv_row, prev_close, ma_volume, atr_val, 
        vector_dim=5, method=VectorizationMethod.CANDLESTICK_FEATURES
    )
    print(f"5维向量: {vector_5d}")
    
    # 手动计算预期值进行验证
    expected_body = (test_data['close'] - test_data['open']) / test_data['open']
    expected_upper_shadow = (test_data['high'] - max(test_data['open'], test_data['close'])) / test_data['open']
    expected_lower_shadow = (min(test_data['open'], test_data['close']) - test_data['low']) / test_data['open']
    expected_volume_ratio = test_data['volume'] / ma_volume
    
    print(f"预期实体: {expected_body:.4f}, 实际: {vector_5d[0]:.4f}")
    print(f"预期上影线: {expected_upper_shadow:.4f}, 实际: {vector_5d[1]:.4f}")
    print(f"预期下影线: {expected_lower_shadow:.4f}, 实际: {vector_5d[2]:.4f}")
    print(f"预期成交量比率: {expected_volume_ratio:.4f}, 实际: {vector_5d[3]:.4f}")
    print(f"预留维度: {vector_5d[4]:.4f}")
    
    # 测试4维向量
    print("\n测试4维向量:")
    vector_4d = candlestick_to_vector(
        ohlcv_row, prev_close, ma_volume, atr_val, 
        vector_dim=4, method=VectorizationMethod.CANDLESTICK_FEATURES
    )
    print(f"4维向量: {vector_4d}")
    
    # 手动计算预期值进行验证
    expected_price_change = (test_data['close'] - prev_close) / prev_close
    
    print(f"预期实体: {expected_body:.4f}, 实际: {vector_4d[0]:.4f}")
    print(f"预期上影线: {expected_upper_shadow:.4f}, 实际: {vector_4d[1]:.4f}")
    print(f"预期下影线: {expected_lower_shadow:.4f}, 实际: {vector_4d[2]:.4f}")
    print(f"预期价格变化: {expected_price_change:.4f}, 实际: {vector_4d[3]:.4f}")
    
    # 验证计算结果
    tolerance = 1e-6
    assert abs(vector_5d[0] - expected_body) < tolerance, f"实体计算错误"
    assert abs(vector_5d[1] - expected_upper_shadow) < tolerance, f"上影线计算错误"
    assert abs(vector_5d[2] - expected_lower_shadow) < tolerance, f"下影线计算错误"
    assert abs(vector_5d[3] - expected_volume_ratio) < tolerance, f"成交量比率计算错误"
    
    assert abs(vector_4d[0] - expected_body) < tolerance, f"4维实体计算错误"
    assert abs(vector_4d[1] - expected_upper_shadow) < tolerance, f"4维上影线计算错误"
    assert abs(vector_4d[2] - expected_lower_shadow) < tolerance, f"4维下影线计算错误"
    assert abs(vector_4d[3] - expected_price_change) < tolerance, f"4维价格变化计算错误"
    
    print("\n✅ 所有测试通过！")

def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况...")
    
    # 测试开盘价为0的情况
    test_data_zero_open = {
        'open': 0.0,
        'high': 105.0,
        'low': 98.0,
        'close': 103.0,
        'volume': 1000
    }
    
    ohlcv_row = pd.Series(test_data_zero_open)
    prev_close = 99.0
    ma_volume = 800.0
    atr_val = 2.0
    
    vector = candlestick_to_vector(
        ohlcv_row, prev_close, ma_volume, atr_val, 
        vector_dim=5, method=VectorizationMethod.CANDLESTICK_FEATURES
    )
    
    print(f"开盘价为0时的向量: {vector}")
    assert np.all(vector == 0), "开盘价为0时应返回零向量"
    
    # 测试成交量移动平均为0的情况（5维）
    test_data_normal = {
        'open': 100.0,
        'high': 105.0,
        'low': 98.0,
        'close': 103.0,
        'volume': 1000
    }
    
    ohlcv_row = pd.Series(test_data_normal)
    ma_volume_zero = 0.0
    
    vector = candlestick_to_vector(
        ohlcv_row, prev_close, ma_volume_zero, atr_val, 
        vector_dim=5, method=VectorizationMethod.CANDLESTICK_FEATURES
    )
    
    print(f"成交量移动平均为0时的向量: {vector}")
    assert np.all(vector == 0), "成交量移动平均为0时应返回零向量"
    
    print("✅ 边界情况测试通过！")

def test_different_candlestick_patterns():
    """测试不同的K线形态"""
    print("\n测试不同的K线形态...")
    
    prev_close = 100.0
    ma_volume = 1000.0
    atr_val = 2.0
    
    # 阳线（收盘价 > 开盘价）
    yang_line = {
        'open': 100.0,
        'high': 105.0,
        'low': 98.0,
        'close': 103.0,
        'volume': 1200
    }
    
    # 阴线（收盘价 < 开盘价）
    yin_line = {
        'open': 103.0,
        'high': 105.0,
        'low': 98.0,
        'close': 100.0,
        'volume': 800
    }
    
    # 十字星（开盘价 ≈ 收盘价）
    doji = {
        'open': 100.0,
        'high': 102.0,
        'low': 98.0,
        'close': 100.1,
        'volume': 1000
    }
    
    patterns = [
        ("阳线", yang_line),
        ("阴线", yin_line),
        ("十字星", doji)
    ]
    
    for pattern_name, pattern_data in patterns:
        ohlcv_row = pd.Series(pattern_data)
        vector = candlestick_to_vector(
            ohlcv_row, prev_close, ma_volume, atr_val, 
            vector_dim=5, method=VectorizationMethod.CANDLESTICK_FEATURES
        )
        
        body = vector[0]
        upper_shadow = vector[1]
        lower_shadow = vector[2]
        volume_ratio = vector[3]
        
        print(f"{pattern_name}:")
        print(f"  实体: {body:.4f}")
        print(f"  上影线: {upper_shadow:.4f}")
        print(f"  下影线: {lower_shadow:.4f}")
        print(f"  成交量比率: {volume_ratio:.4f}")
        
        # 验证上影线和下影线应该 >= 0
        assert upper_shadow >= 0, f"{pattern_name}的上影线应该 >= 0"
        assert lower_shadow >= 0, f"{pattern_name}的下影线应该 >= 0"
    
    print("✅ 不同K线形态测试通过！")

if __name__ == "__main__":
    test_candlestick_features_method()
    test_edge_cases()
    test_different_candlestick_patterns()
    print("\n🎉 所有测试完成！新的CANDLESTICK_FEATURES向量化方法工作正常。")
