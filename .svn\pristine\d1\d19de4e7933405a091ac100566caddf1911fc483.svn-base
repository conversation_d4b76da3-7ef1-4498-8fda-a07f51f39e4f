
import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.memory import get_model_size_mb
from pytorch_lightning.utilities.model_summary import ModelSummary

from pyqlab.pl import PLData, PLDataModule
from pyqlab.pl import PLModel

from pyqlab.data.data_api import get_dataset, get_model_name

import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import KFold
import os

def get_best_saved_model_filename(log_dir, sub_dir):
    model_files = {}
    log_dir = os.path.join(log_dir, sub_dir)
    for root, dirs, files in os.walk(log_dir):
        for file in files:
            if file.startswith("best-"):
                model_files[file] = root
    if model_files:
        file = min(model_files.keys())
        return f"{model_files[file]}\\{file}"
    else:
        return None
    
def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    if args.use_swa:
        callbacks.append(plc.StochasticWeightAveraging())

    return callbacks


def main(args):
    """
    参考：
    https://zhuanlan.zhihu.com/p/556040754
    """
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    print(args)
    dataset = get_dataset(ds_files=args.ds_files)
    x_data, y_data, embedding_data = dataset.prepare(
        direct=args.direct,
        win=args.num_channel,
        filter_win=args.filter_win,
    )

    pl.seed_everything(args.seed)

     # 创建回调函数
    callbacks = load_callbacks(args)

    full_dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(embedding_data))

    # 创建一个数据模块列表，每个数据模块对应一个 fold
    data_module = PLDataModule(batch_size=args.batch_size, num_workers=args.num_workers, seed=args.seed)
    data_module.setup(full_dataset)

    model = PLModel(**vars(args))

    trainer = pl.Trainer.from_argparse_args( 
        args,
        max_epochs=1000,
        callbacks = callbacks) 

    print("hparams.auto_lr_find=",args.auto_lr_find)
    if args.auto_lr_find:
        
        #搜索学习率范围
        lr_finder = trainer.tuner.lr_find(model,
          datamodule = data_module,
          min_lr=1e-08,
          max_lr=1,
          num_training=100,
          mode='exponential',
          early_stop_threshold=4.0
          )
        lr_finder.plot() 
        lr = lr_finder.suggestion()
        model.hparams.learning_rate = lr 
        print("suggest lr=",lr)
        
        del model 
        
        args.learning_rate = lr
        model = Model(net,learning_rate=args.learning_rate,
                  use_CyclicLR = args.use_CyclicLR,
                  epoch_size=epoch_size)
        
    trainer.fit(model,data_module)
    train_result = trainer.test(model,data_module.train_dataloader(),ckpt_path='best')
    val_result = trainer.test(model,data_module.val_dataloader(),ckpt_path='best')
    test_result = trainer.test(model,data_module.test_dataloader(),ckpt_path='best')
    
    print("train_result:\n")
    print(train_result)
    print("val_result:\n")
    print(val_result)
    print("test_result:\n")
    print(test_result)

    # 创建训练器
    out_channels = args.out_channels
    trainer = Trainer(
        max_epochs=args.max_epochs,
        callbacks=callbacks,
    )
    trainer.fit(model, data_module)
    # 在每个数据集的训练之后，执行其他操作（如保存模型、记录日志等）
    # log_metrics(logger, trainer)



if __name__ == '__main__':
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', choices=['10HF', '15HF'], type=str)
    parser.add_argument('--ds_files', default=["main.2023"], type=list)
    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)

    # Data module ===========================
    parser.add_argument('--batch_size', default=256, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)

    # Model Hyperparameters =================
    parser.add_argument('--version', default='CV2DR', type=str)
    parser.add_argument('--model_name', default='time_series_model2dr', type=str)
    parser.add_argument('--loss', default='mse', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)

    # model
    parser.add_argument('--num_embeds', default=[64, 5, 11], type=list)
    parser.add_argument('--num_channel', default=10, type=int) # 通道数,与上面的DataHander中的win保持一致
    parser.add_argument('--num_input', default=51, type=int)
    # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
    # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
    # "out_channels": (16, 32, 800, 256), 
    # "out_channels": (24, 48, 1200, 256), 
    # parser.add_argument('--out_channels', default='(16, 32, 800, 256)', type=str)
    parser.add_argument('--out_channels', default='(24, 48, 1200, 256)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)
    parser.add_argument('--dropout', default=0.5, type=float)
    parser.add_argument('--activation', default='relu', choices=['relu', 'gelu', 'prelu', 'leakyrelu'], type=str)
    parser.add_argument('--pooling', default='max', choices=['max', 'avg'], type=str)

    # LR Scheduler
    # lr_scheduler 可以有以下选项：
    #     'step'：使用步进式学习率调度器
    #     'cosine'：使用余弦退火学习率调度器
    #     'plateau'：使用学习率衰减调度器
    #     'exponential'：使用指数衰减学习率调度器
    #     'reduce_on_plateau'：使用学习率衰减调度器，并在验证集上监测指标不再改善时降低学习率
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)

    # Restart Control
    parser.add_argument('--restart', default=False, type=bool)

    # Training Info
    parser.add_argument('--max_epochs', default=7, type=int)
    parser.add_argument('--early_stop', default=5, type=int)
    parser.add_argument('--min_delta', default=1e-4, type=float) 
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    parser.add_argument('--sub_dir', default='', type=str)
    
    # Others
    parser.add_argument('--model_dir', default='model', type=str)
    # Reset Some Default Trainer Arguments' Default Values
    # parser.set_defaults(max_epochs=10)

    parser.add_argument('--use_swa', default=False, type=bool)

    args = parser.parse_args()

    main(args)
