from pprint import pprint
from argparse import ArgumentParser
from time import time
from datetime import datetime

import os
import torch
import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPO, PPOConfig
from ray.tune.registry import register_env
from pyqlab.rl.env.env_market_timing import MarketTimingEnv


def export_onnx(version: str, checkpoint_path: str):

    # 加载训练好的模型
    print(f"加载模型 {checkpoint_path}")
    algo = PPO.from_checkpoint(checkpoint=checkpoint_path, )
    policy = algo.get_policy()

    # 获取模型的输入和输出
    if version == "v1":
        dummy_input = torch.zeros((1, 10), dtype=torch.int32)
    elif version == "v2":
        dummy_input = torch.zeros((1, 20), dtype=torch.float32)

    # 导出模型为 ONNX 格式
    # onnx_path = "E:/lab/RoboQuant/pylab/model_rl/markettiming_ppo_v1.onnx"
    result_pytorch, _ = policy.model({
        "obs": dummy_input,
    })

    # Evaluate tensor to fetch numpy array
    result_pytorch = result_pytorch.detach().numpy()

    # This line will export the model to ONNX.
    outdir = "E:/lab/RoboQuant/pylab/model"
    policy.export_model(outdir, onnx=18)

    tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
    os.rename(f"{outdir}/model.onnx", f"{outdir}/mktm_ppo_{tm_str}_{version}.onnx")

    print(f"模型已成功导出为 {outdir}")

def trainer(args):

    # 初始化 Ray
    ray.init()
    env_config = {
        "name": "MarketTimingEnv",
        "version": args.version,
        "initial_amount": 1e7,
        "gamma": 0.98,
        "mode": "train",
        "split_percent": 0.9,
        "data_path": "E:/lab/RoboQuant/pylab/data",
        "data_file": args.data_file,
    }

    # 配置 PPO 算法
    config = (
        PPOConfig()
        # ONNX is not supported by RLModule API yet.
        .api_stack(enable_rl_module_and_learner=False)
        .environment(env="markettiming_env", env_config=env_config)
        .framework("torch")
        .env_runners(create_env_on_local_worker=True, num_rollout_workers=args.num_rollout_workers)
        # .rollouts(num_rollout_workers=args.num_rollout_workers)
        # .training(model={"fcnet_hiddens": [64, 64]})
        # .evaluation(evaluation_num_episodes=10)
    )

    # 运行训练
    results = tune.run(
        "PPO",
        config=config.to_dict(),
        stop={"training_iteration": args.training_iteration},
        checkpoint_freq=args.checkpoint_freq,
        checkpoint_at_end=True,
    )

    # 打印结果
    print("最佳模型：")
    best_trial = results.get_best_trial("episode_reward_mean", mode="max")
    print(best_trial)

    print("最佳模型评估结果：")
    best_checkpoint = best_trial.checkpoint
    print(best_checkpoint)

    print("导出 ONNX 模型")
    export_onnx(env_config["version"], best_checkpoint)

    # 关闭 Ray
    ray.shutdown()


if __name__ == "__main__":

    # 注册自定义环境
    def env_creator(env_config):
        return MarketTimingEnv(env_config)

    register_env("markettiming_env", env_creator)
    
    parser = ArgumentParser()

    parser.add_argument("--version", type=str, default="v2", help="model version")
    parser.add_argument("--num_rollout_workers", type=int, default=12, help="num_rollout_workers")
    parser.add_argument("--checkpoint_freq", type=int, default=400, help="checkpoint freq")
    parser.add_argument("--training_iteration", type=int, default=10000, help="training iteration")
    parser.add_argument("--data_file", type=str, default="ft_all.06230215174059000.csv", help="data file")

    parser.add_argument("--export_onnx", action="store_true", help="export onnx model")

    args = parser.parse_args()

    if args.export_onnx:
        export_onnx("v2", r"c:\Users\<USER>\ray_results\PPO_2024-08-28_15-46-31\PPO_markettiming_env_a3e98_00000_0_2024-08-28_15-46-31\checkpoint_000012")
    else:
        trainer(args)