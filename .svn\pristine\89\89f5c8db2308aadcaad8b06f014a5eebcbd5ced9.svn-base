{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### easyquotation"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import easyquotation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["quotation = easyquotation.use('sina') # 新浪 ['sina'] 腾讯 ['tencent', 'qq'] "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quotation.real('162411') # 支持直接指定前缀，如 'sh000001'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quotation.stocks(['sh000001', 'sz000001'], prefix=True) "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["Res_Sina_AllSnap = quotation.market_snapshot(prefix=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Res_Sina_AllSnap"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'MultipleLocator' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 17\u001b[0m\n\u001b[0;32m     15\u001b[0m ax\u001b[39m.\u001b[39mset_xlabel(\u001b[39m'\u001b[39m\u001b[39mtime\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m     16\u001b[0m ax\u001b[39m.\u001b[39mset_title(\u001b[39m'\u001b[39m\u001b[39mstock realtime\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m---> 17\u001b[0m x_major_locator \u001b[39m=\u001b[39m MultipleLocator(\u001b[39m10\u001b[39m)\n\u001b[0;32m     18\u001b[0m ax\u001b[39m.\u001b[39mxaxis\u001b[39m.\u001b[39mset_major_locator(x_major_locator)\n", "\u001b[1;31mNameError\u001b[0m: name 'MultipleLocator' is not defined"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAo4AAAHFCAYAAAB8eXjSAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAC9lElEQVR4nOydeVhUZfvHv8M2AsIoIgKiaC4kIeZSikuuKQpoWbaohL2KmimZWq+avWlp+sulUstMTStNWhTDVFITtwRXKHdTUZFAFBEBkfX5/XF7ZoEBhmE24P5c11znzDnPnPPMDJzznXuVCSEEGIZhGIZhGKYSrMw9AYZhGIZhGKZmwMKRYRiGYRiG0QkWjgzDMAzDMIxOsHBkGIZhGIZhdIKFI8MwDMMwDKMTLBwZhmEYhmEYnWDhyDAMwzAMw+gEC0eGYRiGYRhGJ1g4MgzDMAzDMDrBwpFhmGrxww8/4LPPPjP6eVq0aIHg4GCjn6e69OnTB3369FE+f/DgAebOnYv9+/eXGbthwwbIZDJcu3bNZPNjGIapDiwcGYapFqYSjjWVBw8eYN68eVqFY1BQEOLi4uDh4WH6iTEMw+iBjbknwDAMYw7y8vJgb29v1jk0btwYjRs3NuscGIZhqgJbHBmGKZfbt29j/PjxaNasGeRyORo3bowePXpg7969AMgtu2PHDly/fh0ymUz5kLh79y4mTZqEpk2bws7ODo899hjee+895Ofna5ynpKQEK1aswJNPPgl7e3s0aNAA3bp1Q3R0dIXz+/LLL2FjY4MPPvigwnGSm3vr1q3o2LEj6tWrh3nz5gEA0tLSMGHCBHh5ecHOzg4tW7bEvHnzUFRUpHGMefPmoWvXrnBxcYGzszM6deqEdevWQQhR7nmvXbumFIbz5s1Tfj5jxowBoN1V3adPH/j5+SEuLg7du3eHvb09WrRogfXr1wMAduzYgU6dOsHBwQHt27dHTExMmfP+888/GDlyJNzc3CCXy9GuXTt88cUXFX5GDMMwusAWR4ZhyiU0NBSnTp3CggUL0LZtW9y7dw+nTp1CRkYGABJu48ePx5UrVxAVFaXx2ocPH6Jv3764cuUK5s2bB39/fxw6dAgLFy5EYmIiduzYoRw7ZswYbNy4EWPHjsWHH34IOzs7nDp1qtzYPyEE3nnnHSxfvhxr165VCrGKOHXqFM6fP485c+agZcuWcHR0RFpaGp5++mlYWVnhf//7H1q1aoW4uDjMnz8f165dU4o1gETghAkT0Lx5cwBAfHw8pkyZgpSUFPzvf//Tek4PDw/ExMQgMDAQY8eOxbhx4wCgUitjWloaXn/9dbz77rvw8vLCihUr8J///AfJycn45ZdfMHv2bCgUCnz44Yd47rnncPXqVXh6egIAzp07h+7du6N58+ZYunQp3N3d8fvvvyMiIgJ37typVGQzDMNUiGAYhimH+vXri6lTp1Y4JigoSHh7e5fZ/tVXXwkA4qefftLY/n//938CgNi9e7cQQoiDBw8KAOK9996r8Dze3t4iKChIPHjwQLzwwgtCoVCIvXv36vQ+vL29hbW1tbh48aLG9gkTJoj69euL69eva2xfsmSJACDOnj2r9XjFxcWisLBQfPjhh6JRo0aipKREua93796id+/eyue3b98WAMQHH3xQ5jjr168XAERSUpLG6wGIEydOKLdlZGQIa2trYW9vL1JSUpTbExMTBQCxfPly5bZBgwYJLy8vkZWVpXGuyZMni3r16om7d+9qfU8MwzC6wK5qhmHK5emnn8aGDRswf/58xMfHo7CwUOfX7tu3D46OjnjxxRc1tkvWwT/++AMAsGvXLgDAm2++WekxMzIy0K9fPxw7dgyHDx9G//79dZ6Pv78/2rZtq7Htt99+Q9++feHp6YmioiLlY/DgwQCAAwcOaLyfAQMGQKFQwNraGra2tvjf//6HjIwMpKen6zwPXfDw8EDnzp2Vz11cXODm5oYnn3xSaVkEgHbt2gEArl+/DoCsvH/88Qeef/55ODg4aLynIUOG4OHDh4iPjzfoXBmGqVuwcGQYplx+/PFHhIWFYe3atQgICICLiwtee+01pKWlVfrajIwMuLu7a8Q8AoCbmxtsbGyU7u7bt2/D2toa7u7ulR7z0qVLOHr0KAYPHgw/P78qvRdtmcu3bt3C9u3bYWtrq/F44oknAAB37twBABw7dgwDBw4EAKxZswZ//vknjh8/jvfeew8AJdoYEhcXlzLb7Ozsymy3s7MDQIIRoM+8qKgIK1asKPOehgwZovGeGIZh9IFjHBmGKRdXV1d89tln+Oyzz3Djxg1ER0dj5syZSE9P15qUoU6jRo1w9OhRCCE0xGN6ejqKiorg6uoKgOL9iouLkZaWVmlZmoCAAIwYMQJjx44FAKxatQpWVrr9/i0tYKX35+/vjwULFmh9jWTdi4yMhK2tLX777TfUq1dPuX/btm06ndtUNGzYENbW1ggNDS3XgtuyZUsTz4phmNoEC0eGYXSiefPmmDx5Mv744w/8+eefyu1yuVyrxa1///746aefsG3bNjz//PPK7d99951yPwAMHjwYCxcuxKpVq/Dhhx9WOo+wsDA4Ojpi5MiRyM3Nxbfffgtra2u93lNwcDB27tyJVq1aoWHDhuWOk8lksLGx0ThPXl4evv/++0rPIZfLleONjYODA/r27YuEhAT4+/srLZIMwzCGgoUjwzBaycrKQt++fTFy5Eg8/vjjcHJywvHjxxETE4Phw4crx7Vv3x5bt27FqlWr0LlzZ1hZWaFLly547bXX8MUXXyAsLAzXrl1D+/btcfjwYXz88ccYMmQIBgwYAADo1asXQkNDMX/+fNy6dQvBwcGQy+VISEiAg4MDpkyZUmZuL774IhwcHPDiiy8iLy8Pmzdv1kskffjhh9izZw+6d++OiIgI+Pj44OHDh7h27Rp27tyJr776Cl5eXggKCsKyZcswcuRIjB8/HhkZGViyZIlSFFaEk5MTvL298euvv6J///5wcXGBq6srWrRoUeX56sLnn3+Onj17olevXnjjjTfQokULZGdn4/Lly9i+fTv27dtnlPMyDFNHMHd2DsMwlsnDhw/FxIkThb+/v3B2dhb29vbCx8dHfPDBByI3N1c57u7du+LFF18UDRo0EDKZTKhfVjIyMsTEiROFh4eHsLGxEd7e3mLWrFni4cOHGucqLi4Wn376qfDz8xN2dnZCoVCIgIAAsX37duUYKatandjYWFG/fn0RGBgoHjx4UO570fZaidu3b4uIiAjRsmVLYWtrK1xcXETnzp3Fe++9J3JycpTjvvnmG+Hj4yPkcrl47LHHxMKFC8W6deu0ZkWrZ1ULIcTevXtFx44dhVwuFwBEWFiYEKL8rOonnnhC5/cAQLz55psa25KSksR//vMf0bRpU2FraysaN24sunfvLubPn1/uZ8QwDKMLMiEqqF7LMAzDMAzDMI/grGqGYRiGYRhGJ1g4MgzDMAzDMDrBwpFhGIZhGIbRCRaODMMwDMMwjE6wcGQYhmEYhmF0goUjwzAMwzAMoxNcAFxHioqKkJCQgCZNmujc4oxhGIZhGPNSUlKCW7duoWPHjrCxYdlTXfgT1JGEhAQ8/fTT5p4GwzAMwzB6cOzYMTz11FPmnkaNh4WjjjRp0gQA/eF5eHiYeTYMwzAMw+hCamoqnn76aeV9nKkeZhWOBw8exOLFi3Hy5EmkpqYiKioKzz33nHJ/Tk4OZs6ciW3btiEjIwMtWrRAREQE3njjDeWYtLQ0vPPOO9izZw+ys7Ph4+OD2bNn48UXX1SOyczMREREBKKjowEAQ4cOxYoVK9CgQQOd5yq5pz08PODl5VW9N84wDMMwjEnhMDPDYNZPMTc3Fx06dMDKlSu17n/77bcRExODjRs34vz583j77bcxZcoU/Prrr8oxoaGhuHjxIqKjo3H69GkMHz4cL7/8MhISEpRjRo4cicTERMTExCAmJgaJiYkIDQ01+vtjGIZhGIapTZjV4jh48GAMHjy43P1xcXEICwtDnz59AADjx4/H6tWrceLECQwbNkw5ZtWqVcr4wzlz5uDTTz/FqVOn0LFjR5w/fx4xMTGIj49H165dAQBr1qxBQEAALl68CB8fH+O+SYZhGIZhmFqCRdtte/bsiejoaKSkpEAIgdjYWFy6dAmDBg3SGPPjjz/i7t27KCkpQWRkJPLz85ViMy4uDgqFQikaAaBbt25QKBQ4cuRIuefOz8/H/fv3lY/s7GyjvU+GYRiGYZiagEUnxyxfvhzh4eHw8vKCjY0NrKyssHbtWvTs2VM55scff8TLL7+MRo0awcbGBg4ODoiKikKrVq0AUAykm5tbmWO7ubkhLS2t3HMvXLgQ8+bNq/Kci4uLUVhYWOXXMYbD1tYW1tbW5p4GwzAMw9Q6LF44xsfHIzo6Gt7e3jh48CAmTZoEDw8PDBgwAAC5pjMzM7F37164urpi27ZtGDFiBA4dOoT27dsDAGQyWZljCyG0bpeYNWsWpk2bpnyekpICX1/fcscLIZCWloZ79+7p+W4ZQ9KgQQO4u7tX+B0zDMMwDFM1LFY45uXlYfbs2YiKikJQUBAAwN/fH4mJiViyZAkGDBiAK1euYOXKlThz5gyeeOIJAECHDh1w6NAhfPHFF/jqq6/g7u6OW7dulTn+7du3K0zNl8vlkMvlyuf379+vcL6SaHRzc4ODgwMLFjMhhMCDBw+Qnp4OAFw6iWEYhmEMiMUKx8LCQhQWFpZJn7e2tkZJSQkA4MGDBwDKptirjwkICEBWVhaOHTumTKA5evQosrKy0L17d4PMtbi4WCkaGzVqZJBjMvpjb28PAEhPT4ebmxu7rRmGYRjGQJhVOObk5ODy5cvK50lJSUhMTISLiwuaN2+O3r1745133oG9vT28vb1x4MABfPfdd1i2bBkA4PHHH0fr1q0xYcIELFmyBI0aNcK2bduwZ88e/PbbbwCAdu3aITAwEOHh4Vi9ejUAys4ODg42WEa1FNPo4OBgkOMx1Uf6LgoLC1k4MgzDMIyBMKtwPHHiBPr27at8LsUUhoWFYcOGDYiMjMSsWbMwatQo3L17F97e3liwYAEmTpwIgJIgdu7ciZkzZyIkJAQ5OTlo3bo1vv32WwwZMkR53E2bNiEiIgIDBw4EQAXAy6sdWR3YPW058HfBMAzDMIZHJoQQ5p5ETeDmzZto1qwZkpOTy3SOefjwIZKSktCyZUvUq1fPTDNk1OHvhGEYhgEqvn8zVcei6zgylse1a9cgk8mQmJho7qkwDMMwDGNiLDY5hrFMmjVrhtTUVLi6upp7KgzDMAzDmBi2ODI6U1BQAGtra7i7u8PGhn9zMDWYRxUZGIaxDPLzgYQEWjKWDQvHOkyfPn0wefJkTJ48GQ0aNECjRo0wZ84cSGGvLVq0wPz58zFmzBgoFAqEh4drdVWfPXsWQUFBcHZ2hpOTE3r16oUrV64o969fvx7t2rVDvXr18Pjjj+PLL7809VtlGBUffgg4OwN//mnumTBMnebaNWDqVMDfH6hfH+jUCfjrL3PPiqkMNhsZAyHMZ9FwcACqkFH87bffYuzYsTh69ChOnDiB8ePHw9vbG+Hh4QCAxYsX4/3338ecOXO0vj4lJQXPPPMM+vTpg3379sHZ2Rl//vknioqKAABr1qzBBx98gJUrV6Jjx45ISEhAeHg4HB0dERYWVv33yzBVZc8eoLgYiI0FevQw92wYps6Rng68+y6wcSP9K0o0bAhU0AmYsRBYOBqDBw/o55M5yMkBHB11Ht6sWTN8+umnkMlk8PHxwenTp/Hpp58qhWO/fv0wY8YM5fhr165pvP6LL76AQqFAZGQkbG1tAQBt27ZV7v/oo4+wdOlSDB8+HADQsmVLnDt3DqtXr2bhyJiHGzdomZRk3nkwTB1DCGDLFuCNN4A7d2jbs88CEycCTz0FeHlVye7BmAkWjnWcbt26adQ8DAgIwNKlS1H86Gdgly5dKnx9YmIievXqpRSN6ty+fRvJyckYO3asUogCQFFRERQKhYHeAcNUgaIiICWF1lk4MoxJSE8H1q4FvvsOuHiRtvn7A19/DXTtat65MVWHhaMxcHAgy5+5zm1AHCuxXkrt/bQhtX1cs2YNupa6OnA3F8Ys/Puvyjd29ap558IwtZw7d4AvvgCWLFHdEu3tgWnTgP/9D7CzM+/8GP1g4WgMZLIquYvNSXx8fJnnbdq00VnY+fv749tvv0VhYWEZq2OTJk3QtGlTXL16FaNGjTLYnBlGbyQ3NQAkJwOFhYAWaznDMFXnwgUKIY6NBY4fB27eVO3r3BmYPBl44QXAycl8c2SqDwvHOk5ycjKmTZuGCRMm4NSpU1ixYgWWLl2q8+snT56MFStW4JVXXsGsWbOgUCgQHx+Pp59+Gj4+Ppg7dy4iIiLg7OyMwYMHIz8/HydOnEBmZqayxSTDmAx14VhSQs9btTLffBimhiMEsHMnsGwZsG9f2f1+fsD77wMjRnD8Ym2By/HUcV577TXk5eXh6aefxptvvokpU6Zg/PjxOr++UaNG2LdvH3JyctC7d2907twZa9asUVofx40bh7Vr12LDhg1o3749evfujQ0bNqBly5bGeksMUz7Xr2s+5zhHhtGbCxeAfv2A4GASjdbWwIABwMKFwMGDQFYWcPo08NJLNVc0Lly4EDKZDFOnTlVuE0Jg7ty58PT0hL29Pfr06YOzZ89qvC4/Px9TpkyBq6srHB0dMXToUNxUN8ECyMzMRGhoKBQKBRQKBUJDQ3Hv3j2NMTdu3EBISAgcHR3h6uqKiIgIFBQUGOvt6gRbHOs4tra2+Oyzz7Bq1aoy+0pnUANU27F0e3N/f3/8/vvv5Z5j5MiRGDlyZLXnyjAVUlQEbNtGfrJ33gFatCg7Rt3iCLBwZBgdEYL+fU6dokLdCQnA779TtIe9PWVKR0QA3t7mnqnhOH78OL7++mv4+/trbP/kk0+wbNkybNiwAW3btsX8+fPx7LPP4uLFi3B65IefOnUqtm/fjsjISDRq1AjTp09HcHAwTp48qQwFGzlyJG7evImYmBgAwPjx4xEaGort27cDAIqLixEUFITGjRvj8OHDyMjIQFhYGIQQWLFihQk/iVIIRieSk5MFAJGcnFxmX15enjh37pzIy8szw8z0p3fv3uKtt94y9zSMQk39Thg92b1biGbNhKD7mxAvvKB93JAhtN/RkZYzZ5p2ngxTQygsFOLYMSGWLhXiueeEcHVV/XupP4YMEeLqVXPPtmIqun+XR3Z2tmjTpo3Ys2ePxr2ypKREuLu7i0WLFinHPnz4UCgUCvHVV18JIYS4d++esLW1FZGRkcoxKSkpwsrKSsTExAghhDh37pwAIOLj45Vj4uLiBABx4cIFIYQQO3fuFFZWViIlJUU5ZvPmzUIul4usrKyqfxAGgl3VDMPUbAoKgDFjKNnFxYW2bd8OZGaWHStZHHv2pCVbHBkGAJUfjooCpkyhf48GDYCnnwamTydD/p07lEf25JPA668Dy5cDR48Cv/0G1MbIozfffBNBQUEYMGCAxvakpCSkpaVh4MCBym1yuRy9e/fGkSNHAAAnT55EYWGhxhhPT0/4+fkpx8TFxUGhUGhUHOnWrRsUCoXGGD8/P3h6eirHDBo0CPn5+Th58qTh37SOsKu6DrN//35zT4Fhqs8vv1CZHXd34MoVICAA+Ptv4KefgAkTVOOEUMU49ulDfjYuycPUYYqKgN27gQ0bSADm5Wnub9CARGSvXvTo1AmQy80xU8OQnZ2N+/fvK5/L5XLItbyhyMhInDp1CsePHy+zL+1Ra5smTZpobG/SpAmuP7q+pKWlwc7ODg0bNiwzRnp9Wloa3Nzcyhzfzc1NY0zp8zRs2BB2dnbKMeaAhSPDMDUXIYBPP6X1SZOojulrrwEzZlC1YXXhmJUFZGfT+jPP0JItjkwdpKAA+OYbSmJRD/tt0QIYOpQsjR07Ao8/DljVIr+kr6+vxvMPPvgAc+fO1diWnJyMt956C7t370a9evXKPZasVLaPEKLMttKUHqNtvD5jTA0LR4Zhai5HjgAnTpAZZOJE2jZyJDXCPXIEuHwZaN2atkt3SFdX4IknaP3OHRKTXFiOqQPcvUuCcflyiuwAgEaNgFGjgLAwEos1NftZF86dO4emTZsqn2uzNp48eRLp6eno3LmzcltxcTEOHjyIlStX4uKj1jdpaWnw8PBQjklPT1daB93d3VFQUIDMzEwNq2N6ejq6d++uHHPr1q0y5799+7bGcY4ePaqxPzMzE4WFhWUskaakFv2WYBimziFZG0ePBho3pnUPD2qACwAbN6rGSm5qb29AoVDFQ7LVkanFFBYCMTH0L+LlRQUHkpPp32T5cirS/fnn5IauzaIRAJycnODs7Kx8aBOO/fv3x+nTp5GYmKh8dOnSBaNGjUJiYiIee+wxuLu7Y8+ePcrXFBQU4MCBA0pR2LlzZ9ja2mqMSU1NxZkzZ5RjAgICkJWVhWPHjinHHD16FFlZWRpjzpw5g9TUVOWY3bt3Qy6XawhbU8MWR4Zhaib37wO//krrb72luW/UKIph3LEDkFxRksWxeXNatmxJJpikJGqcyzC1hPR04PvvSTAeOUKJLxIdOlACzMiRVEaH0cTJyQl+fn4a2xwdHdGoUSPl9qlTp+Ljjz9GmzZt0KZNG3z88cdwcHBQlp1TKBQYO3Yspk+fjkaNGsHFxQUzZsxA+/btlck27dq1Q2BgIMLDw7F69WoAVI4nODgYPj4+AICBAwfC19cXoaGhWLx4Me7evYsZM2YgPDwczs7OpvpIysDCkWGYmsnevRTd37Yt0L695j7p1/jFixQHKZOVFY6PPQacPMkJMkyN5/Zt4I8/gDNngL/+UtVXlGjcGHj5Zfo91bVr7bcsGpt3330XeXl5mDRpEjIzM9G1a1fs3r1bWcMRAD799FPY2NjgpZdeQl5eHvr3748NGzZotPPdtGkTIiIilNnXQ4cOxcqVK5X7ra2tsWPHDkyaNAk9evSAvb09Ro4ciSVLlpjuzWqBhSPDMDWTnTtpOXhw2X2tWlFUf3Y2kJZGfjltFkeAao2MH19j+sszDEDCcONG4OuvqSxOqb4MePppck/37Qv4+tauJBdTU7oCiUwmw9y5c8sk1qhTr149rFixosJC3S4uLtioHk6jhebNm+O3336rynSNDv8pMdVCJpNh27Zt5p4GU9cQAti1i9aHDCm7Xy5XdY55FMyuEeMIUB80uZx6o/XuDajFETGMpXL/PvDll4CPD/Cf/wDx8fTv8OST9Pvn00+pGtXRo+SS9vNj0cgYFrY4MgxT8/j7b6rd6OCgKq1TGh8fckNfvEh1GyWXtGRx7NyZ2hMOHUou6/BwKmbHMBbIzZvAokXAt98COTm0zc2NCnSPGgWoJQszjFFh4cgwTM1Dsjb26weUV2vNx4fGXbpEd91btwBra1UpHoCKhW/ZQhZHM3ZiYBhtPHwI/PknsHUrsG4dkJ9P2x9/nHpDjxtHv50YxpSwAbsOs3r1ajRt2hQlJSUa24cOHYqwsDAAwKpVq9CqVSvY2dnBx8cH33//fbnH279/P2QyGe7du6fclpiYCJlMhmvXrgEANmzYgAYNGuC3336Dj48PHBwc8OKLLyI3NxfffvstWrRogYYNG2LKlCkoLi5WHqegoADvvvsumjZtCkdHR3Tt2pU739RlKopvlHiUmYiLFwGp5EX79mXvtNK4W7c0MwoYxkwkJQFvvkklRwcMINd0fj4Z1/fuBc6dAyIiWDQy5oEtjkZACM3yB6bEwUH3jLkRI0YgIiICsbGx6N+/PwAqLvr7779j+/btiIqKwltvvYXPPvsMAwYMwG+//YbXX38dXl5e6Nu3r95zfPDgAZYvX47IyEhkZ2dj+PDhGD58OBo0aICdO3fi6tWreOGFF9CzZ0+8/PLLAIDXX38d165dQ2RkJDw9PREVFYXAwECcPn0abdq00XsuTA3kyhWqMQJULBzbtqXlxYsU8AVQxkBpGjemJryFhZRI06yZYefLMDoiBDBvHjB/PiD9bvbwAAYOBEJDycDOGdGMuWHhaAQePADq1zfPuXNydE8OdXFxQWBgIH744QelcPz555/h4uKC/v3745lnnsGYMWMwadIkAMC0adMQHx+PJUuWVEs4FhYWKi2ZAPDiiy/i+++/x61bt1C/fn34+vqib9++iI2Nxcsvv4wrV65g8+bNuHnzprLZ+4wZMxATE4P169fj448/1nsuTA3j33+puHdxMdCjhyozWhuSJTEpCTh0iNa7di07zsoK8PSk5JmUFBaOjFkQgjplLltGzwcNogZIffuyWGQsCxaOdZxRo0Zh/Pjx+PLLLyGXy7Fp0ya88sorsLa2xvnz5zF+/HiN8T169MDnn39erXM6ODgoRSNAjd9btGiB+mpqu0mTJkhPTwcAnDp1CkIItJUsSI/Iz89Ho0aNqjUXpgZx/z6JxqQkKrfzyy8Vj/f0pF9wOTlAXBxt02ZxBCiz4Pp1ioVkGBOQkkKPO3eAs2fJBb17N+374gtqvc4wlggLRyPg4KDKejPHuatCSEgISkpKsGPHDjz11FM4dOgQlkk/eVG1Ru5Wj2o+CLWCYoVaYsZsbW01nstkMq3bpNjLkpISWFtb4+TJkxrFUwFoiE2mlrNlCwV3eXjQXdbdveLxMhm5q0+douf16wPt2mkfK6WkpqQYbr4Mo4YQwOnTlOiydSutl0Ymo7qM48aZfn4MoyssHI2ATFZzagnb29tj+PDh2LRpEy5fvoy2bdsqe2C2a9cOhw8fxmuvvaYcf+TIEbQr5+bb+FGv4NTUVGVj98TExGrPsWPHjiguLkZ6ejp69epV7eMxNRSpHuOLL6pqNFaGj49KOHbpQlnV2vDyoiULR8bAXL9OGdGbNmk2KbK2pt8rjRpRxEXXrhTL+OSTZpsqw+gEC0cGo0aNQkhICM6ePYvRo0crt7/zzjt46aWX0KlTJ/Tv3x/bt2/H1q1bsXfvXq3Had26NZo1a4a5c+di/vz5+Oeff7B06dJqz69t27YYNWoUXnvtNSxduhQdO3bEnTt3sG/fPrRv3x5DtBWAZmofly/TsnVr3V+jHt6gLb5RQrI4squaMQD//kslQX/+mVoBSk6YevUodvH554GQEMDFxbzzZBh9YOHIoF+/fnBxccHFixeVTdoB4LnnnsPnn3+OxYsXIyIiAi1btsT69evRp08frcextbXF5s2b8cYbb6BDhw546qmnMH/+fIwYMaLac1y/fj3mz5+P6dOnIyUlBY0aNUJAQACLxrqEPsJRSpAByo9vBNhVzejFw4fA+fNUj1798Sg8W0m/flRfPiSk5nijGKY8ZEKU7nDJaOPmzZto1qwZkpOT4SW5tR7x8OFDJCUloWXLlqhXXjFixqRU+zuRCkxXVO6FMR1CAE5OQG4uuaxLJUqVy6lT1CEGIGtiee01Dh8GevWipBtJoDKMGkJQV8qEBErS37eP/rzUys0qkcnoz+6FF4ARI+jPijEfFd2/marDFkeGKU1GBrWhk8noTsGZ2+bn1i0SjVZWusc3AtSot3t36s1WUU82dVe1EFz/hAEAZGdTHtaOHUBMjHaDdMOGgL8/0KEDLf39qTkRF+dmaissHBmmNEePAkVFtH7sGFsdLYF//qGltzdgZ6f76+zsqGdbZTyqD4r8fODuXf6xUEe5e5csiX//TRWcDhzQbCZkZUXt/p5+mtzPzzxDrc/5dwZTl2DhyDClkbqMAEB8PAtHS0Cf+MaqIJdTB5nbt8msxMKxTlFcTGVwZs8G1DqmAqA/uaAgevTowZZEhmHhyDCliY/Xvs6YD2MLR4Dc1bdvk7va399452EshoIC4McfgcWLVXUV27alcNcnn6TyOLqG0zJMXYGFowHhPCPLQe/voqSE3NMSR4/StkfFzRkzYSrhmJjImdW1mOJi4PhxYM8e+jePi6OQZgBQKIAFC4CJE8sv98kwDAtHgyB1PXnw4AHs7e3NPBsGoO8CKNulplIuXSJfVb16JBazsiiLt7yOI4xpMIVw5CLgtZakJGD5cmDjRmrxp46HBzBlCjB+PEcoMIwumFU4Hjx4EIsXL8bJkyeRmpqKqKgoPPfcc8r9OTk5mDlzJrZt24aMjAy0aNECEREReOONNwAA165dQ8uWLbUe+6efflLWD8zMzERERASio6MBAEOHDsWKFSvQoEEDg7wPa2trNGjQQNlb2cHBody2fIxxEULgwYMHSE9PR4MGDcq0KKwUKb6xSxcSjgcPkruahaP5EMJ0FkeAhWMNRwjgyhXg5EngxAl6HDxIjgMAcHYmF3SvXvRv3qVL1fKtGKauY1bhmJubiw4dOuD111/HCy+8UGb/22+/jdjYWGzcuBEtWrTA7t27MWnSJHh6emLYsGFo1qwZUlNTNV7z9ddf45NPPsFgtYSGkSNH4ubNm4iJiQEAjB8/HqGhodi+fbvB3ov7o7656aUrvzJmoUGDBsrvpEpIwrFrV/JXScLx9dcNO0FGd+7cAe7fp9TVxx4z3nm4e0yNRAjq/bxuHbX3u3EDyMkpO27gQOCtt4BnnwWq6ohgGEaFWYXj4MGDNQReaeLi4hAWFqbsVDJ+/HisXr0aJ06cwLBhw2BtbV1GHERFReHll19G/fr1AQDnz59HTEwM4uPj0fVRy7E1a9YgICAAFy9ehI96Z4lqIJPJ4OHhATc3NxSq129gTI6trW3VLY0SUjJMt26qQCdOkDEvkrXRy4tCCIwFu6prFOnpFKu4fLlmWDJASfIdOlAR7i5dgJ49OcmFYQyFRcc49uzZE9HR0fjPf/4DT09P7N+/H5cuXcLnn3+udfzJkyeRmJiIL774QrktLi4OCoVCKRoBoFu3blAoFDhy5Ei5wjE/Px/5+fnK59nZ2TrN2draWn/RwpiXBw+ogBugsjgCwJkzwPvvUx2ON98kXxdjOiTh2KaNcc8jWRyTkoBvvqHv++ZNKuQ3ZQrw6McoYz7u3gV++gn47jtKbJFwcACmTgX69iX936oVWxUZxlhYtHBcvnw5wsPD4eXlBRsbG1hZWWHt2rXo2bOn1vHr1q1Du3bt0L17d+W2tLQ0uLm5lRnr5uaGtLS0cs+9cOFCzJs3r/pvgqk5SP3DPDzo7iOTUcHp69eB+fNpjJMTMHmyeedZ17h0iZbGjG8EqJKzjQ35OceOLbt/1izjnp8pl+RkYMkSYM0aIC9Ptf3JJ6nMakQEoE9kCsMwVcfihWN8fDyio6Ph7e2NgwcPYtKkSfDw8MCAAQM0xubl5eGHH37A+++/X+Y42hJVhBAVJrDMmjUL06ZNUz5PSUmBr69vNd4NY/GcPEnLp55StYJYuxbYvJki7P/+m92YpqakhExMANCpk3HP5eRERf1iYkip5OaStTE+HvjjDxaOJubqVeDXX4HoaOoNLfWEbt8eGDMGeOUVVcMfhmFMh8UKx7y8PMyePRtRUVEICgoCAPj7+yMxMRFLliwpIxx/+eUXPHjwAK+99prGdnd3d9y6davM8W/fvo0mTZqUe365XA65XK58fv/+/eq8HaYmcOoULTt3Vm0bMIAe8+aRcMzMNM/c6iq7dpHFUaEARo0y/vmGD6eHxLlz1Hj4zz+pHaHaNYExPFIHly+/pAgRdfr0Ad57D+jfn1v8MYw5sdiqxoWFhSgsLIRVqcLL1tbWKJHqKqixbt06DB06FI0bN9bYHhAQgKysLBxTi54+evQosrKyNFzaDKO0OGqzbEmlm0r3I2OMy2ef0XLcOPPEGLZrBzRpAjx8qNmKkjEoJSVk2O3ZE5g0iUSjtTXFLH72GVkfY2PpNxyLRoYxL2a1OObk5OCyFPgOICkpCYmJiXBxcUHz5s3Ru3dvvPPOO7C3t4e3tzcOHDiA7777DsuWLdM4zuXLl3Hw4EHs3LmzzDnatWuHwMBAhIeHY/Xq1QAoOzs4ONhgGdVMLeDBA+D8eVpXtzhKNGxIS7Y4mo7Tp4G9e6me5pQp5pmDTEamrh9/BPbvB555xjzzqGVItRb/+IMe+/apOrg4OQEffQSEhgIuLuadJ8MwWhBmJDY2VgAo8wgLCxNCCJGamirGjBkjPD09Rb169YSPj49YunSpKCkp0TjOrFmzhJeXlyguLtZ6noyMDDFq1Cjh5OQknJycxKhRo0RmZmaV5pqcnCwAiOTkZH3eKmPpHDkiBCCEu7v2/dHRtP+pp0w7r7rMuHH0mY8YYd55rFpF8+jTx7zzqOGkpgqxcaMQr78uRPPm9JGqPxwdhXj1VSH4EssYGr5/GxazWhz79OlTYU9hd3d3rF+/vtLjfPzxx/j444/L3e/i4oKNGzfqNUemjqAtvlEdtjiansOHaWnu4ut9+9IyLo5c1sasJVnLuH1bVT6ndK1FW1sql9q/P7mgn36aS+gwTE3AYpNjGMakVBTfCKhiHFk4mgYhKLMZMH4Znspo25ZKNKWmUiDeo4YETFlycykDWnJBJybSVwmQ1//JJ0ko9u9PLf8cHc05W4Zh9IGFI8MAKotjecJRsjjeu0d3Qo7QNy737pEKAVQdXcyFFOe4eTNlaLBw1CA5GdiwgcJR4+KogpE6nToBr71G5XMqKGTBMEwNgYUjwzx8CJw9S+uVuaqLi6lAtJOTaeZWV5Gsja6ugL29eecCULqvVM+TAUD/CitWAHPmqDQ+QHXUBwwgq2K/flyYm2FqGxZbjodhTMbp00BREYmU8qxb9vaAnR2ts7va+EjCsVkz885DQmp0fPWqeedhAZSUAFu3Ah07Am+/TaKxe3fgq6+Af/4Brl0D1q0DRo5k0cjUTFatWgV/f384OzvD2dkZAQEB2LVrl3L/mDFjIJPJNB7dunXTOEZ+fj6mTJkCV1dXODo6YujQobh586bGmMzMTISGhkKhUEChUCA0NBT3SpV8u3HjBkJCQuDo6AhXV1dERESgoKDAaO9dF1g4Mox6Ykx5LmiZjOMcTYkkHM3tppZ47DFaJiWRcqqj/PsvJbS88AL93lIoqGD3oUPAhAkUjspRHExNx8vLC4sWLcKJEydw4sQJ9OvXD8OGDcNZyTMFIDAwEKmpqcpH6XKAU6dORVRUFCIjI3H48GHk5OQgODgYxVILJAAjR45EYmIiYmJiEBMTg8TERISGhir3FxcXIygoCLm5uTh8+DAiIyOxZcsWTJ8+3fgfQkWYO627psDp/LWY8HCqBzJrVsXjfHxo3P79pplXXWb2bPqsJ00y90yIwkIhrK1pTjdvmns2ZuHSJSG8vekjcHISYs4cITIyzD0rhqkcQ9y/GzZsKNauXSuEECIsLEwMGzas3LH37t0Ttra2IjIyUrktJSVFWFlZiZiYGCGEEOfOnRMARHx8vHJMXFycACAuXLgghBBi586dwsrKSqSkpCjHbN68WcjlcpGVlaX3e6kubHFkmMpK8UhwSR7TIbl0LMVVbWMDeHvT+pUr5p2LGbh8GejRA7h+nayKf/1FRbq5QDdT2ykuLkZkZCRyc3MREBCg3L5//364ubmhbdu2CA8PR3p6unLfyZMnUVhYiIEDByq3eXp6ws/PD0eOHAEAxMXFQaFQoGvXrsox3bp1g0Kh0Bjj5+cHT7Wm7IMGDUJ+fj5OSpVAzAAnxzB1m4IC8rkB5WdUS7BwNB2WFuMIkLv66lV61KEOMgUFlBF9+zaV0/n9d8DNzdyzYpiqk52djfv37yufy+VyyMvpP3/69GkEBATg4cOHqF+/PqKiouDr6wsAGDx4MEaMGAFvb28kJSXh/fffR79+/XDy5EnI5XKkpaXBzs4ODaV7xiOaNGmCtLQ0AEBaWhrctPwjubm5aYxpUqoUQcOGDWFnZ6ccYw7Y4sjUbc6epTtjw4ZAixYVj2XhaDosUTi2akXLOpYg8/77VObUxQXYvp1FI1Nz8fX1VSaiKBQKLFy4sNyxPj4+SExMRHx8PN544w2EhYXh3LlzAICXX34ZQUFB8PPzQ0hICHbt2oVLly5hx44dFZ5fCAGZWhCwTEtAsD5jTA1bHJm6jXrh78r+ETk5xjQIYXmuakCVIFOHXNX79gGffELr69ZZTq4Sw+jDuXPn0LRpU+Xz8qyNAGBnZ4fWj5oPdOnSBcePH8fnn3+O1atXlxnr4eEBb29v/PPPPwCo611BQQEyMzM1rI7p6eno3r27csytW7fKHOv27dtKK6O7uzuOHj2qsT8zMxOFhYVlLJGmhC2OTN1G1/hGQLMIOGM87tyh2poyGaB2kTc7dczimJ8PvPEGrU+YADz3nFmnwzDVxsnJSVlix9nZuULhWBohBPLz87Xuy8jIQHJyMjw8PAAAnTt3hq2tLfbs2aMck5qaijNnziiFY0BAALKysnBMrRfn0aNHkZWVpTHmzJkzSE1NVY7ZvXs35HI5OutyzzISbHFk6jaVtRpUh13VpkFyUzdpoqqdaQnUMYvjZ58Bly7R1yBZHRmmLjB79mwMHjwYzZo1Q3Z2NiIjI7F//37ExMQgJycHc+fOxQsvvAAPDw9cu3YNs2fPhqurK55//nkAgEKhwNixYzF9+nQ0atQILi4umDFjBtq3b48BAwYAANq1a4fAwECEh4crrZjjx49HcHAwfHx8AAADBw6Er68vQkNDsXjxYty9exczZsxAeHg4nJ2dzfPhgIUjU5cpLKT0UKBqFkcWjsbFEuMbAZVwvH0byM4mVfXwIaUb1zJu3qSsaQBYvBgw4z2KYUzOrVu3EBoaitTUVCgUCvj7+yMmJgbPPvss8vLycPr0aXz33Xe4d+8ePDw80LdvX/z4449wUuso9umnn8LGxgYvvfQS8vLy0L9/f2zYsAHW1tbKMZs2bUJERIQy+3ro0KFYuXKlcr+1tTV27NiBSZMmoUePHrC3t8fIkSOxZMkS030YWmDhyNRdzp8nf5yzs0oUVATHOJoGSxWOCgXQqBGQkQEcPQoMHUrCcf/+WpdlLXWE6dEDGD3a3LNhGNOybt26cvfZ29vj999/r/QY9erVw4oVK7BixYpyx7i4uGDjxo0VHqd58+b47bffKj2fKeEYR6buIsU3duoEWOnwr8AxjqbBUoUjoPqBMXMmkJdHiTxjxpAFspYQGQn88guVrly5kjvBMAyjCQtHpu6iLhx1gV3VpsHS2g2qIyXISLGxcjm1ITR3CzADkZoKTJpE63PmUN1GhmEYdVg4MnWXS5do6een23gWjqahJlgcAcDDA4iOpvU1a1Ri8p9/qCZoBS4qSyQrCxg1iv68O3cGZs8294wYhrFEWDgydRepxIFaO6cKkYRjfj65KRnjYMnCUbI4AmSaGzgQCAqi54cP0/Knn6g335o1pp+fnpw8SYb32FigXj3g228BW1tzz4phGEuEhSNTd5GEo7u7buPr11fFQrLVUT/WrAFeeomSSrRRUACkpNC6JQrHRwWBIZdTcUMA6NKFllKGfkICLc+fL/99WghCUBxj9+5UnrJFC+DgQeCJJ8w9M4ZhLBUWjkzdpLCQyqoA5HLUBSsrVWY1J8hUnZISSir5+WfgwAHtY/76Cygqov52lhjj2LMnxTN+8w3QuDFt69CBlomJmsuiIuBRizJLJDubNPyUKaTXn3+eNO9TT5l7ZgzDWDJcjoepm0itnmxsAFdX3V/XsCFw9y5bHPXhwgX67ABVS8HSxMXRsls3y0zntbICStdQkzJIzp6lrjfqBcITEnRPvjIhqankYU9IIJf04sVARIRlfuQMw1gWLByZuonkpm7SRLdSPBKcIKM/f/6pWq9MOD5quVUjaNGCaoHevw/8+KPmPsn6aEFcuAAEBlIYZuPGlN/TrZu5Z8UwTE2BhSNTN5GEo65uagkuAl6W69fJ9SyTAdbW9KhfH+jfnzItJKTkEUCVAFOaI0doGRBgvPkaGpmMrI4HDwIbNtA2W1sKh7Aw4Xj4MNUtz8ykcM2YGM18H4ZhmMpg4cjUTfQVjlwEvCzBwcCZM2W3z54NLFigel6ZxfHff4EbN8gC/PTThp+nMenQgYTjiRP0PCQE2LqVYjZLSqpm1TYS0dEU05ifD3TtCmzfrgrTZBiG0RXzX80YxhxUVziyxZHIz6fYPoAsjP37A48/Ts+l7QB93uqxf9qEo+Smbt+eLJY1idKVsl95hTKvs7MpXdnM3LhBrQPz88niuG8fi0aGYfSDhSNTN2HhaBiSkqimS/36wJ49wN69KitjerpqnGRtdHKiZUXCsSa5qSVKC8enniIBDJjdXS0EEB5OGrZ7d2DLFsDBwaxTYhimBsPCkambcIyjYbh8mZatWqlScps0oaWUuQ6ohONzz9EyK6tsf+eaLBx9fSm2E6C/EW9vlZg0s3Bctw7YvZvCTb/5hgoJMAzD6AsLR6ZukpZGy6oKRxcXWkplZSyFW7eA+HjTn1dyP6tnWLi50VLd4iglxgweDCgUtK5udczLU7Xsq4nCsV49oF07Wn/ySVXCDKAqCK5OURHFQ5aUGG1KqalkaZTqlM+fD/j4GO10DMPUEVg4MnUTfS2OUmCYVDzcUhgxggTX33+b9rzahKNkcczJAR48oO4pknjq0UNV2FsSjps2UauS/Hz6fKXuLDWNjh01l5JwPHWK/MXqrFhB7uypUw0+jexs4IMP6GNcu5a06ejRRjkVwzB1EBaOjOVQWGia85SU6G9x1GZNswQkwXjsmGnPq004OjlRYghAn9PNm0BxMeDoSG0EpVaCN28CUVGkapKSSHCuX19zq1C/9x4QFkadZQASjtbW9LdWuvzQ9u20XLlSlYldFd54o4wSFAL44QegTRvgww9JswcEkLH3++9VnnSGYZjqwMKRsQzi4khwLF5s/HNlZJCrUCZTWcd0xRItjvfvU8wgYPoWd9qEo/rneuuWyrLo5UX71C2O0dG0/sordKygINPM2xj4+FAdx6ZN6bmjo6odoXoYQUGBKp5TCGDiRBLWunLrFvDVV8DnnyvLQt28SVWRRo2i3W3aUBLMn3+SkZdhGMZQsHBkLIPffiNXZWys8c8lualdXalQc1WQhGN2Ns3XElC3Zp0/b7rzFheTpRAo615Wt8yqC0f15c2bqtjH0FASWrUNKV5TEooAxXI+fEgZ+goFPV+9WvdjSn+/AJCSgp07ybi5cydgZwd89BGV1Rw+vOYabxmGsVxYODKWgVRA2hRJJ9KN19296q9t0ECVlmopVkd14WhKi2NKClnPbG1V7mcJdYujNL/SwvHkScrKlslqZkKMLmgTjgcP0rJPH2DePFpfs6bsa4VQFUVXD4149Pd7Gn4YN12BoCAyonfqRAncc+aQgGQYhjEGLBwZy0ASjqYoc6NvYgxAIkeyOlpKnOONG5rrOTnGPd/ff5PFVXJTt2hRNoBOsjiqu6olcakuHAFKjJHqY9Y2JOF46hRZGQHg0CFa9uqlKk905kxZC/bUqeT29vYmIf7llwCAo38WoR/+gD9OY93v9FlOnkzdGqXEboZhGGPBwpExP7m5qu4ali4cAcuLcyydeHHhgvHOdeIExe09/7xmDcfSSBZHba7q0tbJ2hyE17Il/b0UFpJ4LC5Wued79QKaNyfRXFSk2Wnn7FlKnAGUFu683Yfw6qtAtwUhiEU/WKMIL/qew4EDlKQt5SMxDMMYExaOjPlRd69mZpYtXWJoaptwVLc4AsZ1V0vFrP/4A/jxR1rXJhy1WRxLu6olevY0+DQtBnU3fHw8WRazsqjTjlTvsVMn2n/qlOp1//0vZf8PHw788gsAYP6xgYiMBGQowev4BlfxGH7u+Tmeeca0b4lhmLoNC0fG/Jw+rVovKjK+q7W6wlESRZYiHCWLo1Sc3JjCUb0bzB9/0FJb3cWKLI7OzqrWg0DttjgCmnGOUnxj9+6qWFmp7qNU6zI2Ftixg/YvWgQ0a4aLaIvFqaMAAD93W4ZvMBbNkay9dSPDMIwRYeHImB8pvlHCEO7q4mLgr7+0d+YwlMXR0mIcn32WlsbMrJbqX6pTkas6OVn1OalbGqV1Dw+KkazNSMJx925VH291M2Fpi+OsWbScOBFo0wbCqxmmYAUKYYchgcUYbv2r6rUpKcadO8MwTClYODLmxxjCcd06cgWOH6+5vaREVUKmNriqS0pUVqdBg2hpKoujREWuaikO0t5eZREFVMKxZ8/aXzOmSxeyHt6/T5+fiwvwwguq/ZJw/OsvlJw4hZ+PNsNoq01o9/unsLEBrJo0xh4MhBwPsfzdFMjS1MrxsMWRYRgTY1bhePDgQYSEhMDT0xMymQzbtm3T2J+Tk4PJkyfDy8sL9vb2aNeuHVatWlXmOHFxcejXrx8cHR3RoEED9OnTB3l5ecr9mZmZCA0NhUKhgEKhQGhoKO49KpzLWACScJQEhCGEo+T+XrcO2LpVtX3XLipx4uysaglXVSxJON6+Tdm4MhkwYABtu3pVlcFraCSL46uv0lImowSQ0pQurC4V/5Zo356WAwcafo6WhqMjJbqEhVFV7ps3gccfV+1v0wbCwRG78nqjcz9nvISfsalkJC78Y6NRF3wu5qKV7Kqm1Tcjw3jfNcMwjBZszHny3NxcdOjQAa+//jpeUP8F/oi3334bsbGx2LhxI1q0aIHdu3dj0qRJ8PT0xLBhwwCQaAwMDMSsWbOwYsUK2NnZ4a+//oKVlUoTjxw5Ejdv3kRMTAwAYPz48QgNDcV2qe0XYz4yMlSuYz8/EnyGqOWYkaFaHz+e3IUeHsCyZapt9evrd2xLajsoxTd6eJA4a9iQhPelS4C/v+HPJ1kcw8Ppc/DwIGtiaRo1AqysVKECpRNiPvqIWp3UlcyOCRPoUQohgD+PWOE9m0M4iI5ANuCMLLzx4h30/k8rPPEEZUvLRwxFg0PbgfPeVIUAoPqZhYX0Q+ixx0z8hhiGqbMICwGAiIqK0tj2xBNPiA8//FBjW6dOncScOXOUz7t27arxvDTnzp0TAER8fLxyW1xcnAAgLly4oPP8kpOTBQCRnJys82sYHThwQAhAiBYthAgKovW1a6t/3IED6Vg2NrTs2lWI3btp3dpaiOvX9T/2oUN0nFatqj/P6rJlC82lWzd63r07Pd+82TjnUyjo+OfPVz7WzY3GAkKEhhpnPjWQ4mIhYmOFmDhRCC8v1UckR56YjsXidpMnhCgs1HxRWBgNGjOGlvXrC9G6Na0fOGCOt8EwNQa+fxsWi45x7NmzJ6Kjo5GSkgIhBGJjY3Hp0iUMehTLlZ6ejqNHj8LNzQ3du3dHkyZN0Lt3bxyW6qSBLJIKhQJdu3ZVbuvWrRsUCgWOHDli8vfElEJyU/v5qYpAG8JVLVkcP/6Yur0cPapyi44YQfXz9KW0qzojw3zWR8niKNVGlHoj799v+HM9fKjqia1L1x3JMguUtTjWQVJTgQ8+oHrefftSu+mbN8lgO+6Zi/gHbbAE78B1TLAq41pC+n6PHaOlZGEGOEGGYRiTYtHCcfny5fD19YWXlxfs7OwQGBiIL7/8Ej0f1X27+qho9Ny5cxEeHo6YmBh06tQJ/fv3xz///AMASEtLg5v6DewRbm5uSNOWIfqI/Px83L9/X/nIzs42wjtklEWP1buHGEI4Su7unj2pfp56yZi3367esSXheP8+8OAB0K0bxewZu4yQNqSMakkIP/88LbdupdJGhkRyU9vZUY/lylCPcyxd9LsOUVBAnV2aNwc+/JDEokIBjB1L/aUzMoA1yx+iGR4lurz+etmDSN+vlDHv7k5dZQBOkGEYxqSYNcaxMpYvX474+HhER0fD29sbBw8exKRJk+Dh4YEBAwag5FH81IQJE/D6o4ttx44d8ccff+Cbb77BwoULAQAyLVmbQgit2yUWLlyIeVIfWcZ4SPGNzZurrHaGjHFs1Aho25bE43//C3h6Ak8/Xb1jS/2qi4qAAwdUmcPnzwNPPVW9Y1eV0hbHvn3pPd++TXPr399w55J+aDVpolsmNFsckZVFCdRSycsePUhEPvccUK+e2kB/f2DSJMDVFfDxKXsg6fuViuOzxZFhGDNhscIxLy8Ps2fPRlRUFIKCggAA/v7+SExMxJIlSzBgwAB4PCqn4uvrq/Hadu3a4cYjS4y7uztuaSkhcvv2bTQpnfmpxqxZszBt2jTl85SUlDLnYQyAusArLKT16locCwvJGgioSsA0agSsXVu940pYWdENPi0NiI5Wbb90yfTCUbI4SsLCxoa6jaxZA/z0k2GFo/R/pIubGtC0ONZB4Xj7Nn38p09THlZkJPDoUlYWmQz44ovyD1baYssWR4ZhzITFuqoLCwtRWFiokR0NANbW1kpLY4sWLeDp6YmLFy9qjLl06RK8vb0BAAEBAcjKysIxKTYIwNGjR5GVlYXu3buXe365XA5nZ2flw0m90wVjOCTrYqNGhnNVS8eUyVTHNDSSu1o9M/9ReIRJkSyO6jGbL71ES0O7qyXhWMEPLg3qsMUxK4vKap4+TRrv4MEKRKMulBaObHFkGKOxatUq+Pv7K+//AQEB2LVrl3K/EAJz586Fp6cn7O3t0adPH5xV7zUPCnebMmUKXF1d4ejoiKFDh+JmqR95upQKvHHjBkJCQuDo6AhXV1dERESgoKDAaO9dF8wqHHNycpCYmIjER/1vk5KSkJiYiBs3bsDZ2Rm9e/fGO++8g/379yMpKQkbNmzAd999h+cfxXHJZDK88847WL58OX755RdcvnwZ77//Pi5cuICxY8cCIOtjYGAgwsPDER8fj/j4eISHhyM4OBg+2lxCjGlRtzgaWjg2aABYW1fvWOUhiSL1m/alS8Y5V3kUFKhc/erCok8fsojeuaOZJLN4MTBnjv7nk1zVVbU4yuU0nzrCvXtUaSghgX5f7N+v6iqoN87OmnGlbHFkGKPh5eWFRYsW4cSJEzhx4gT69euHYcOGKcXhJ598gmXLlmHlypU4fvw43N3d8eyzz2rkQkydOhVRUVGIjIzE4cOHkZOTg+DgYBSrFWcdOXIkEhMTERMTg5iYGCQmJiI0NFS5v7i4GEFBQcjNzcXhw4cRGRmJLVu2YPr06ab7MLRhzpTu2NhYAaDMIywsTAghRGpqqhgzZozw9PQU9erVEz4+PmLp0qWipKRE4zgLFy4UXl5ewsHBQQQEBIhDhw5p7M/IyBCjRo0STk5OwsnJSYwaNUpkZmZWaa6czm8ESkqEsLOjkiLXrglx+LBhytxI5XJatzbMPLXxyiuqOirSo0sX451PG5cu0Xnt7emzVGfCBNo3YQI9v3NHNc8rV/Q735tv0uvfe0+38b/9RuMfe0y/89VAoqKE8PCgt61QCJGQYMCD+/mpvsOYGCFSUlTlpYqKDHgihqldGOL+3bBhQ7F27VpRUlIi3N3dxaJFi5T7Hj58KBQKhfjqq6+EEELcu3dP2NraisjISOWYlJQUYWVlJWJiYoQQupUK3Llzp7CyshIpKSnKMZs3bxZyuVxkZWXp/V6qi1ljHPv06QMhBXtrwd3dHevXr6/0ODNnzsTMmTPL3e/i4oKNGzfqNUfGiOTmktUMIIujVNi4uhZHyYqp3uLO0EiuaoAsavn5ZHEUwnQt9K5coWWrVmXPOWQIsHo1cOgQPT9xQrXv8mX9CkZX1VXduzeVQHruuaqfqwZRVARs2wZ8/jkgVQJr2xbYtEn/5kRaadZMVb7K3Z2+B2tr6suenq5/C02GqSNkZ2fjvhT/DgpJk8vlFb6muLgYP//8M3JzcxEQEICkpCSkpaVhoFrXK7lcjt69e+PIkSOYMGECTp48icLCQo0xnp6e8PPzw5EjRzBo0KBKSwX6+PggLi4Ofn5+8PT0VI4ZNGgQ8vPzcfLkSfTt29cQH0uVsdgYR6YOILmU7eyoLZvkqr53T9VxRB/U3d/GQl04hoSQcLt/37RtCNWFY2m6daPluXP0eR4/rtonZYFXlaq6quvXB37/HXjjDf3OZ+FkZAD/93+kwUeMINFoawvMnAn89Re1qDYo6uEIHh4kGiWxyO5qhqkUX19fZTyhQqFQVl7RxunTp1G/fn3I5XJMnDgRUVFR8PX1VZbxK51c26RJE+W+tLQ02NnZoWGpGPvSYyorFZiWllbmPA0bNoSdnV2F5QSNjcVmVTN1AHWBp57IUlJCIqxBg+of11io/8P36UPC7Pp1sjpquRgYBUkAqteolHBzI0Vz9SoVjTaEcKyqxbGWcvo0sHw5sHGjqk20qyswcSJpZDXjgGGREqCsrVUxox4eJBrNeBNhmJrCuXPn0FSKDQYqtDb6+PggMTER9+7dw5YtWxAWFoYDBw4o95cu5ycqKfGnbYwupQL1KSdobNjiyJiP0i7levVUfY+r465Wz9Q2FuoWx27dgDZtaN2UmdUVWRwB6s8NUA1LdeGo7xzV6zjWQZKTgdBQKrm4di2Jxo4dgQ0baN9HHxlRNAIqi2OTJlQSClBZf1k4MkylODk5aVRLqUg42tnZoXXr1ujSpQsWLlyIDh064PPPP4f7o/+50ha/9PR0pXXQ3d0dBQUFyCx1Hys9prJSge7u7mXOk5mZicLCwgrLCRobFo6M+dBmGTREZrUpYhwlq2K9eqQk2ral54bMrM7PV70XbUjCUZvFEVC5q7dsUWVfA/pZHPPyACljUFdXdS3hwgWyJrZtS1ZGAHjxRXJNnzwJhIWVKuZtLKR2kk88odrGwpFhTIIQAvn5+WjZsiXc3d2xZ88e5b6CggIcOHBAWeKvc+fOsLW11RiTmpqKM2fOKMfoUiowICAAZ86cQara9Xv37t2Qy+Xo3LmzUd9vRbCrmjEf5QnHf/81jHA0psWxSxfg2WeppaGtrUo4GtLiGByscjNLx5coKanc4igJx7//pmWjRvTZXL1KCRVVKVUk/TKWy6k0TC1HCCA2Fli2DNixQ7X9mWeApUuNEL+oCx06UJJTy5aqbZJwVP9hwDBMtZg9ezYGDx6MZs2aITs7G5GRkdi/fz9iYmIgk8kwdepUfPzxx2jTpg3atGmDjz/+GA4ODhg5ciQAQKFQYOzYsZg+fToaNWoEFxcXzJgxA+3bt8eAAQMAaJYKXL16NQBg/PjxGqUCBw4cCF9fX4SGhmLx4sW4e/cuZsyYgfDwcDib8TrMwpExH9pcypKVsDptB00hHOvVA3bvVj2XXNWGsjgKQSathw+BhQuB0tUF/v2XLJI2NprFv9Xx96d5SoF4ISGU6ltQQHFxj4rk64R6YowZY2uMzeXLZFXcvFn1VcpkwNChwLRpQK9eZn77pa0MbHFkGINz69YthIaGIjU1FQqFAv7+/oiJicGzzz4LAHj33XeRl5eHSZMmITMzE127dsXu3bs1GoV8+umnsLGxwUsvvYS8vDz0798fGzZsgLXaD/ZNmzYhIiJCmX09dOhQrFy5Urnf2toaO3bswKRJk9CjRw/Y29tj5MiRWLJkiYk+Ce2wcGTMh7Fc1aaIcSyNZBG8fJmsgVbVjALJzFQJvo0bgblzNYWeZG1s0YLEozbs7Mg0JtWICQgA4uKAixdpnlURjrU8Meb0aWDBAurSKFUIc3AAXn8deOst1e8Ci4OFI1MdcnPpOmFra+6ZWBTr1q2rcL9MJsPcuXMxd+7ccsfUq1cPK1aswIoVK8odo0upwObNm+O3336rcIyp4RhHxnxoi0WsKTGOpZEEXF6eYVrA/fuvar2oiLq+qCPFKZbnppaQ3NUA9dGW4iGr6lKvpcIxNxeYOpW8wD/+SKJx0CDg++/J+7typQWLRoCFI6M/d+9S20ovL6orpVbfkGEqgoUjYz6MYXEUwjSu6tLY2KiKal+4UP3jSeJTyjJfu1ZTHFQW3yghCcd69QA/P5UKqmqCTFVrONYA/voLaN+eCncLQQkviYlATAwwenQNCeVUF44VNFNgmDKcPk01XtPTqfiovz89Z5hKYOHImA9jxDg+eECxf6WPawqkjImDB6t/LMni+MwzwNNP03v65RfV/soyqiUGDqQEnqlTyR0lja+qcLx2jZZeXlV7nYVy+jTQvz+QlEQhort2AT//rEpcrjFIFmD1rHeG0QXpGuPtTXWkrl8HVq0y75yYGgELR8Z8GMPiKAlOW1vqXGJKpPZS6kkz+iJZHJs2JYUDqNrNAbq7qp2cqO2g1CFBX+Goq1CtAZw9Sx9pRgZ57//6CwgMNPes9MTRkb5jgN3VTNWQrjE9egCLFtH655+rYqsZphxYODLmwxgxjurHNHX666OMOxw/Xr2scEBlDfD0VNXtO3uWlkLoL+Sk8VeuVK2to66ucQsnPp6MuLdvA506kcbXt0GRxcBxjow+qP84feUVKjB/6xbw3XfmnRdj8bBwZMxDcbFKHGqzON68CSQkUPZCVTBHfKOEpyfFEQoB7N1bvWOpX9TVhaMUw5mVRdvUa/rpgrc3xWM+fKh7Eo96wk8NFo6//06Wxrt3yfu/Z08tEI0AC0dGP9SvMba2VG8KoES8M2c49IEpFxaOjHm4d08VzK9ucZQE38WLZBJq27ZqrhNzCkfAcO5q6aLu6Qk8/jiV98nMJHEguZmbNlUlz+iKjY1KbEoWzMpISqKls7P5PtdqcucO8PLLFAIbGAjs22fapHuj4uFBSxaOTFVQ92oAwLhx9MP98mXKGnNxAbZuNd/8GIuFhSNjHiRXrpMT1RGT6NwZGDUK8PWlzib//lu10jHmqOGojrpwrE6Wq3RRb9qUMqIlS9/Zs8CRI7T+5JP6HfuZZ2hZuqh4eajHU9bQ4t/z5pGR9skngehoCg2sNbDFkdEHdYsjQDHhX3xBGWJOTlQGbPt2882PsVhYODLmobxaizY2VPD67FlViquU0Vud45qKXr2oLV9yMllN9aGoSFU3Ubqo+/nR8uxZ6oUHAH376nf8iAhabtkC3LhR+fganhhz8SLw1Ve0vnRpLax1zMKRqSpCaP44lXj1VapJJRXANlQnLKZWwcKRMQ+6uJRbtKClJBz//Rd47z3Va/U9rjFxcCDxCOjvrr51ixJXrK2Bxo1pmxTn+NdfwIEDtK6vcPT3B/r1ozhTtfZW5VKDE2Py8yl0q6iIWn/362fuGRkBFo5MVcnIoNajgCrUQR1Dt1BlahUsHBkiL8+056uKcLx+nZYff0wPqbSMvsc1NtWNc5RcSB4eJB4BlXDcupWC1hs0qF7RwbffpuXXXwM5ORWPrYHCUQhqy/3448DOnfQxfvKJuWdlJFg4MlVFusY0bqwZKiQheRfu3Kl+hQim1sHCkaHyC05OmgWmjY0usYhSL2XJ4ijVMZRi/LRhScIxNlZVjLwqlA5aB1TCUcqmfuYZlajUhyFD6OaQlUWhARVRw4Rjdjbw0kvU/eXaNdLfmzYB7dqZe2ZGgoUjU1VKxzeWpn591T4pxjw5meo8DhpEbZb0ubYxtQIWjgwQF0duy4oEmaHRx1UtxQyeOgUUFmp/ze3btHR1re4M9ad9e+ro8eCBfp+ptot627aaQlFfN7WElRUwfjytb9lS/riiItXnXwOE45UrVGrnl18olvGjjyi35+WXzT0zIyIJx/R0+j9mmMqoTDgCdM0ByF199y4lLE6dSp6ULVtUITNMnYOFI6NyU5uyyb0uSSzqwjErS2VRyc8H/v5b+2vS02kptWIzB1ZW1XNXa7M4yuWquCOg+sIRAJ57jpb796ssmaVJTiaRLpdbfLvBW7eoBvuFC3Q/PHAAmDOHwk5rNY0bU7Z7cXHF8b8MI6EtMaY00vXmn3/onyknh66rHTvS9j//NO4cGYuFhSNjXuGoi6s6IwM4eVJz39GjZccLocpGdnOr/hyrQ3WEY3nWAMld3agRWTWrS5s2gI8PWRV//137GMlN3bIlCWILJTubvO9JScBjj1HznoAAc8/KRNjYqJKo2F3N6IJ6ndjyULc4HjxI68OHqzwVhw8bb36MRWO5dwLGdDx4QMvyhGN16hGWhy7CUaFQtfYoLWy0CcfcXJUINqfFEQAGDKDlqVMq97muaLM4AqpkmL59DSfihg6lZXS09v01IL6xsBAYMYI+aldXICZGe6JorYbjHJmqUFVXtSQcn3mGelsDdA0uKjLeHBmLhYUjU7HF8fnnVUkU6ly9ShbBuXP1O6euSSySu1oSjtJzbcJRsjba25u/wrO7u0roVbX9YHkX9YgIYNYsaglmKCThuHOn9puAhQtHIajhxe+/k0t6xw5Nj36dQRKOqanmnQdTM6iKcDx/nmo7AlRq7Ikn6Ed9bi6VB2PqHCwcmfItjiUl1Dng6tWyFr9vvqHi0du26XfOmzdpWZlpSBKK0gVq9GhaXryo6nUtoR7faAkdTvR1V5fnRlIoqByR9JkYgoAAEu+ZmZoxS//+CyxYQOnIgMUKxw8+oKIA1tbAzz9TYkydRPp+vvhCVZ+PsRyWLCEhpm9TAEOjS4yjFJ7y8CHdC1q1ovFWVkD37jSG4xzrJCwcmfItjnfvqrI09+zR3Cf1ML13r+rny8mh+mCAqm9yeUhxjhI9eqhuksePa+6zlPhGiZ49aVmVX+UPHqisuxVd1A2FtTUQFETrkrtaCLIszJlDNxgHB8Mk4xiYmzdJRwNUjnLIEPPOx6zMnEl9ho8fp3XGsli5kpJM9PXQGJL8fFX4TEUxjnZ2mtdnqbEBoHJXs3Csk7BwZMq3OEoWPECz9/KFC+S+AMrPxq0IqbxLw4ZkRauI0ta1xx8Hunal9dLuakvIqFbHx4eWly7pHicquYQaNQKcnY0yrTIMHkzLQ4domZ5OVmaZDFi7lhSaIZJxDMzXX9PvmmeeAf7zH3PPxsw0bw58+y2tf/op8Ntv5p0PoyI1VdXE4KefVL3fzTkfgColVBYqJLmrAVWPe0AlHA8fNk4MPGPRsHBkNC2O6hcByYIHkFtaKgQbFaXanpVFboyqkJRES11crupj6tWjG6QkHE+c0BxraRbHli3Jopebq3vsmRQP2a+f6dztUnmNs2fpu5QKrbdqBYwdSwLfwigoIOEIAG++ad65WAwhIVRnD6i4uxJjWtR/4JaUGDZGuark5alErKdn5deY8oTj009TNv+//6qOx9QZWDgyKoujECRyJNQtjoAqVk9dOApRecu60kgWx8rc1ICmq7ptW4qvkcRk6QxSS7M4qrt6Svd8PXeObiCluy9IwlHKyjYFrVuTKH/wgCyNZ8/Sdj8/082himzdSr8TPDwof4t5xDvv0PLIEVWsLGNe4uNp6e9Pyw0bKPX/r7+ozNiBA/QraPx44N13jZep/OWXlDTYpw891yUURso08/SkOlcSDg5Ap060vmuXQafJWD4sHBnNPtXq7mp1iyNAcY7JyRRHJZOpSsJU1V0tWRx1EY7qFkfJ9Su5V0oXO7Y0iyOgWdJCnalT6SYhxYoCJMClm0z//iaZHgCyiko1Iv/+W2VxtGDh+MUXtBw/njrEMI/w9FS5EdX/thjzIf1Pv/UWfTcFBRQe8uSTQJcuJOQmTADWrKEfk+WVxtKV/fupSoJ6GbD8fGqjpO5R6tev8mMFB9OP98mTy1onX32VlosWcfvBOgYLx7qOEJrCUV0EShY8qZLyvn3A66/Teo8eqq4vVU2QkSyOuriqGzRQxfo9/jgtpfNK/a5Lz9dSLI6ASjhKbn4JSZypd8A5dIiKErZoofnr3hRIMYw1QDj+/juFVtnYqGoRM2q88AItTdl7ntFOUZEqia9bN+r1/Mwz9CPYzY2sfm3bkoehd28aJ8Vg6MPp05TIFhREx+/bl36Q/vQTeWg8PekH9q1bwLx5lR/P25uu17Nmld03YQId78YNYN06/efM1DhYONZ18vM1f4VqszgOHEiVlXNygD/+IAvVjBmq4tzGtDjKZCqBWdrieO+eZm/emmJxvH9fFfN44YJqu7qb2tTlhCQ3moULx8xMVSLMpEkVJ4XWWSTheOgQFwQ3N2fOUAiIszP98O3cmVzTFy7Q9ermTSrRs2cPlTgDKCRIukZWFSmeUi6na8j+/STwPv2Utr/5Jl0fDXGNtLcHZs+m9QULNA0QTK2GhWNdp/Q/u7pwlCx4Hh4qS+Mrr1B83rBhqoxofYWjrvUI58yhm6FUrFqyOAqhWcvREi2OUoyQunBUX5ey0wHzxDdKSMLxjz+of5+trUVW0p4yheLx27bl/I9yad6ckheE0IxHZkyP5Kbu2rXybk+PPUbN1oWgagb6IMUnv/EGdXuxtgZ++AFISKA45gkT9DtueYwbBzRrRv+UbHWsM7BwrOtIiTES2iyObm4Ux/LwIbB5s8qKJgnHqriq791TCU1dheOIEeR2c3Ki5zY2Kve15K4uLFStW5JwlD6rK1dUQe/qRYCvXKG5p6er3Na6xB4ZGslVLX3/Pj6U3GNBfPst1SO3sqJ1Bwdzz8iCefFFWrK7umISEsqGvKgjBHDsWNnrpK5IwrFbN93GS7EX33xD14Wqop7Y1rOn5q+r116rvPxOVZHLKXYToIQfpk7AwrGuo4vFsUkTulvL5Zpj9XFVS9ZGN7fqtQUsnSAjBYJbW6sskpaAlxf90i8sVJWtULc4FhVRXbfYWHru7w80bmz6ebq5aQpuC3NT798PhIfT+vvv634frrNI1vnDh+kHH1OWkycpM3jgwPJLiq1fT9bCOXP0O0dVheOwYfR/mJamX7ayFGYiJbtNnw6MHEnXSynj3tBIyVjHj3NNxzoCC8e6jq4WR21UZnE8doxeKxUmBqrupi6P0sJRmmvjxpW7hEyJlVVZd3XptmMXLpAyAszboUW9yLcFCcfz54Hhw0l7v/wy8L//mXtGNYC2bel/oaCABBJTFik05ORJ4NdftY+RXMZScfyqUFCg+p+XaqVWhq2tKkb1jz+qdr7MTFXstK8vLa2sgI0b6Yd169ZVO56udOhAXqD0dKq6wdR6LOgOy5iF8iyOubkqUVme67cyi+OuXXTBmjVL5XapSg3HiiidWS1ZRy0pMUaidIKMJByl93D+PAXMA6rMSnMgxTkCFiMc9+8ng0ZmJhlt1q+3rN8FFotMpmp5yW3htKPesvTDD8tay65dA+LiaP38+apb01JS6DVyOeDurvvrpGtAVcWq5KZu3lyz65RMZtxkO3t71fWidFMGplbCl+C6TnkWR8mCV68eUL++9tdWlhwjibrUVGDbNlqvSkZ1RZRncbSk+EYJdeFYUqISkCEhtDx4UJUko96dwdRYmMXx22/JiyiJxu3b6R7F6IgkHA8fNu88LBV14ZiYSH9g6kRGqtZzc6tuTbtxg5bNm1dNuEk9oRMTqxYGVNpNbUqeeoqW6p8pU2th4VjXKc/iqB7fWN5FrzJXtXrGs1SxuSo1HCuitHCsKRbHf/8lsW5jAwwZQtuljjzt2xs+eL0qPPkkLevXr76wrwYlJRRSNmaMyj29bx9VhGKqgBR7duRI1duC1nbS00nYyWSqnpX/+59mPOgPP2i+5ty5qp1Dimlu3rxqr/PwILeyEPTd6YpkcTSHcOzShZZscawTsHCs65QnHHWpiViZq1pdOB44QL+IDWVxLO2qrgkWx7NnVTefxx5TWfgkF5g53dQACcclS8jUZyZ/cGYm8NJLVBYOAN57j+7fbGnUg44d6YPLyCgbV1vXkSxjjz8OfPABuXb/+ou6oRQV0f/q6dMUcyh1caqqcJQsjuptU3VFsjoePKj7a8zZKlSyOJ44USsSZBYuXIinnnoKTk5OcHNzw3PPPYeLpf6HxowZA5lMpvHoVioJKj8/H1OmTIGrqyscHR0xdOhQ3Lx5U2NMZmYmQkNDoVAooFAoEBoainuljDE3btxASEgIHB0d4erqioiICBQUFBjlveuCWYXjwYMHERISAk9PT8hkMmyT3JmPyMnJweTJk+Hl5QV7e3u0a9cOq1at0hjTp0+fMl/eK6+8ojFGly+mzlKeq1qXmoi6uqqlcUOHqm5ghnZVW7LFsUMHoGFDctnPnUvbfHyAVq0oC1zC3MIRoCzM4cNNflohqJpH+/bAli10v96wAZg/n2Ma9cbOjjKCAXZXl0YSjk89RUlEUVEUi7htG7UAHDaM9g8erLLcmsriCKhCVqoS52hOV7WfH31+9+5RibEazoEDB/Dmm28iPj4ee/bsQVFREQYOHIjc3FyNcYGBgUhNTVU+du7cqbF/6tSpiIqKQmRkJA4fPoycnBwEBwejWK1xxciRI5GYmIiYmBjExMQgMTERoaGhyv3FxcUICgpCbm4uDh8+jMjISGzZsgXTp0837odQEcKM7Ny5U7z33ntiy5YtAoCIiorS2D9u3DjRqlUrERsbK5KSksTq1auFtbW12LZtm3JM7969RXh4uEhNTVU+7t27p3GcwMBA4efnJ44cOSKOHDki/Pz8RHBwcJXmmpycLACI5ORkvd+vRbJ6tRB036ZHjx60/aOP6Pl//lP+a//4g8a0a6d9f7t2tH/uXM1zNGokRH5+9ea9cSMdq39/ej5oED1fv756xzUW//d/mp/B9Om03cdHte3WLfPO0cQUFQlx5IgQs2YJ8dhjqo+hTRshjh419+xqCe+9Rx9qWJi5Z2JZDBlCn8uKFapt27YJYW2t+kO0thZi1y4hIiPpeUBA1c4xcKD+16TLl+m1trZCPHhQ+fj0dNW8c3Kqfj5D0LUrnf+HH8xz/gqo7v07PT1dABAHDhxQbgsLCxPDhg0r9zX37t0Ttra2IjIyUrktJSVFWFlZiZiYGCGEEOfOnRMARHx8vHJMXFycACAuXLgghCCdZGVlJVJSUpRjNm/eLORyucjKytLr/VQXs/6WHzx4MObPn4/h5Vg44uLiEBYWhj59+qBFixYYP348OnTogBOl4igcHBzg7u6ufCgkCxeA8+fPIyYmBmvXrkVAQAACAgKwZs0a/Pbbb2VMz3USyeIoBZBVxeKoq6t62DDK7PzlFyA6moruVre4tOSqrgkWR4BanjRtqnoutU+U+m/7+lru3A3MrVvAu+/S2+3enWoUX71KBb0jIujP4+mnzT3LWgInyJRFCE2Lo8SwYZQgM3Mmmb1TUoDAQFVpm3PnquaGVU+OqSqPPUb9NAsLqaxZZUhu6sceq1593OogxTkePKhf8XITkJ2djfv37ysf+fn5Or0u69E9zqVUjeD9+/fDzc0Nbdu2RXh4ONKl+xCAkydPorCwEAMHDlRu8/T0hJ+fH448il2Ni4uDQqFAV8kzAKBbt25QKBQaY/z8/OCp1l910KBByM/Px0kzldqyaCdQz549ER0djZSUFAghEBsbi0uXLmHQoEEa4zZt2gRXV1c88cQTmDFjBrKzs5X7dPlitJGfn6/xB6Z+zFqFFOMoCcSqxDhWlBwjhMpV3bAhKYQXXqBM4mbNqj1tpau6JsQ4AhRrNm+e6rkkHDt1ouWzz5p+TibmwQNqbduyJbB4MX11DRpQY6DNm0n7f/65+e57tZKAAFpeuVJxh5S6QGIi/WLZsoXKhNnYUBiJOoMH0y+Z4cNV15K2bSleIitLs/d3Wpqq/mpphKieq1omq1qcoznd1BKSCP/qK7revfyyxcU7+vr6KkPWFAoFFurQt1QIgWnTpqFnz57wU4sfHTx4MDZt2oR9+/Zh6dKlOH78OPr166cUo2lpabCzs0PDhg01jtekSROkPfo7SktLg5uWe6ybm5vGmCal7msNGzaEnZ2dcoypsTHLWXVk+fLlCA8Ph5eXF2xsbGBlZYW1a9eip/QrGsCoUaPQsmVLuLu748yZM5g1axb++usv7NmzB4BuX4w2Fi5ciHnqN/raimRxdHenX636WBwfPKBfmLa2qn15eVQAFzBOJxf1GMe8PFXhW3WrnqURFkaFCK9fVwnG6dMpi7JUXG5tQghg505g8mRVUr3UjCMwkO7fjJFQKMibcOcOWdAsqauSKSkooB+uV6+qtvn7U7mxypDLKR75n3/I6ujhQeV5evYkQR4VBTz3nOZrpOsSoP8P5e7dgR9/1C1T+a+/aKlei9XUhIRQPOipU/Tef/qJqmlYUDmEc+fOoanaPUJeuhuaFiZPnoy///4bh0tZ7V9++WXlup+fH7p06QJvb2/s2LGjXC8qQEJUplapRKalaok+Y0yJRVscly9fjvj4eERHR+PkyZNYunQpJk2ahL1SxX8A4eHhGDBgAPz8/PDKK6/gl19+wd69e3Hq1CnlGH0+9FmzZiErK0v5OFfVwOiagnRxkwrU3r9Pd3pdLI7qRWbVO84AKje1tXX5dSCrg3QDzM2lX9tCkJC1VIsjQApp/34SjtJn4uhIvfSkPty1jGPHKCk1OJhEY7NmlH8QF0fbWDSaAMnF9e+/5p1HeezaRVUXjGmdWrOGRKOTk6p1alW6NKm7qwFqaiAlgfzvf2XLHUnWRnf3sq1adUXqNpOQUPlYSTiWtqCaEhcXConIyVGJZfX2qhaAk5MTnJ2dlY/KhOOUKVMQHR2N2NhYeHl5VTjWw8MD3t7e+OeffwAA7u7uKCgoQKZ6dREA6enpSguiu7s7bkn3WjVu376tMaa0kSszMxOFhYVlLJGmwmKFY15eHmbPno1ly5YhJCQE/v7+mDx5Ml5++WUsWbKk3Nd16tQJtra2Gl9eZV+MNuRyucYfmFMtvbGXcVUXF9M2XSyOtrYUmAaUdVdL/ywNGxqna4FCoUq3lTpjtGtn3A4JhsDGpk6kCR84AAwaRJbF2FgKaZ0+ne67w4ZZ/tdUq7Bk4XjsGNUz7dMH6Ny5bBFuQ5CTQ51hAOD//o8KeUdFqSoc6IK6cDxwAFixgp7Xq0dle7Zs0RxfnVI8EpIITE5WxXJro6iI5gCoarGaEysrVQmyR/fhmoYQApMnT8bWrVuxb98+tNShCkhGRgaSk5Ph4eEBAOjcuTNsbW2V3k8ASE1NxZkzZ9C9e3cAQEBAALKysnBMLY716NGjyMrK0hhz5swZpEpeNQC7d++GXC5H586dDfJ+q4rF3sEKCwtRWFgIq1I3WWtra5RUUMz27NmzKCwsVH55unwxdRrJVd24sepunpGhulBVlrBRXoKMFE9lLNeYlZXq2OrCkTEr9+8Do0eTDti9mwzOYWFkeFiyxDjGZ6YSLFk4xsSo1hMSgOef16z/Wl0KC6lOY3o6FdUeN46udc89V7U/Rkk4rl0L9OtH6+PGAf/9L63PnUs/uiWqkxgj4exMLnJAZVHUxj//UOFyR0fVeHNTus1qDePNN9/Exo0b8cMPP8DJyQlpaWlIS0tD3iNDS05ODmbMmIG4uDhcu3YN+/fvR0hICFxdXfH8888DABQKBcaOHYvp06fjjz/+QEJCAkaPHo327dtjwIABAIB27dohMDAQ4eHhiI+PR3x8PMLDwxEcHAyfR3HwAwcOhK+vL0JDQ5GQkIA//vgDM2bMQHh4OJzVvX6mxCy53I/Izs4WCQkJIiEhQQAQy5YtEwkJCeL69etCCCq188QTT4jY2Fhx9epVsX79elGvXj3x5ZdfCiGEuHz5spg3b544fvy4SEpKEjt27BCPP/646NixoygqKlKeJzAwUPj7+4u4uDgRFxcn2rdvz+V4JF5+mUoofP65EAoFre/fT0srK6qZUhFSyZ19+zS3b9tG27t2NdrURdu2dA53d1p+8onxzsVUyokTQrRsqapk8sYbQly9au5ZMcqSPJMmmXsmZenTh+Y2f77q/zgurvLXTZkixIsvln99Kimha5p0TIDK6ujL5cuapXq6dBEiK0uIzEwhGjSgbZs3q8a//bZm2S19eeEFOs7SpeWP+eEH/coFGZNPP6U5vfiiuWcihKj6/RuA1sf6R6WVHjx4IAYOHCgaN24sbG1tRfPmzUVYWJi4ceOGxnHy8vLE5MmThYuLi7C3txfBwcFlxmRkZIhRo0YJJycn4eTkJEaNGiUyMzM1xly/fl0EBQUJe3t74eLiIiZPniwePnyo9+dRXcwqHGNjY7V+OWGPao6lpqaKMWPGCE9PT1GvXj3h4+Mjli5dKkpKSoQQQty4cUM888wzwsXFRdjZ2YlWrVqJiIgIkZGRoXEeXb6Yyqi1wjEkhP7B16wRolkzWl+0iJYtWlT++m7daOzWrZrb16+n7YGBRpm2EIIulOq1EbdvN965mAqJiRHC0VH1Z3PkiLlnxCj54gv6Yp5/3twz0eTBAyHs7GhuFy8K0a8frX/7bcWvO3xY9T+fkKB9zIEDqjFNmtA17dF9Q28uXqQCo//+K0RxsWr7vHl0Hn9/1Tkkwbd8efXOKdXTHT26/DH//S+NmTixeucyJDt2qD4TC6DW3r915PLly+K9994Tr7zyirj1qF7wrl27xJkzZ/Q6nllD0/v06QNRQUC0u7s71q9fX+7+Zs2a4cCBA5Wex8XFBRs3btRrjrUeKcbR3l6V7PL777Qs1T5JK+ZyVQNl+zpLNREZk7JhA+X3FBVRVaGfflL9WTAWgKW6quPiKNvZ0xNo04Ye+/ZV7t5cvFi1/s8/2uP6li6l5ejRwLp11a8bC6jcr6WZMoXm9PffVD4gKKh6pXjUkRJkEhPLHyPtM2diTGnUYxxLSupEXLelcuDAAQwePBg9evTAwYMHsWDBAri5ueHvv//G2rVr8csvv1T5mPxt1nWkGEcHB5VwlMoOSDXgKqK8toPqyTHGQl2UyuXVb2PIVIn794HXXgNef51E48iRwG+/sWi0OCxVOMbG0rJvX4qv1iWh4uJFaiIgoU1kXrqkSrKZM8cworEiGjYEJk6kdakuoCGSYwCVKD5/nuIYtSHFP1pCYoxEixaUCJiXZ3l/d3WMmTNnYv78+dizZw/s1P4X+vbti7i4OL2OycKxrqPN4ihV/ddFOEoqoaKsamOhbnFs21az7zNjVK5coSTY778nY8IHH9C6se/RjB5IwjE1tWzZGHOybx8tpWQTXRIqli4lB7RkwdI29tNPaUxwsKrQvrF5+2364//zT2D5clVViupaHD09qQ5icbGqyLc6t25RIXKZjBq9Wwo2NtTFBqixCTK1hdOnTysTdtRp3LgxMirK1q8AFo51hfx8upC++65mvTRtFkeAykzo4vooz+Joalc1u6lNxl9/UZ3fy5fpvnjgACWVsjfKQmnShIRFUVHFZV1MSU6OqpWeVE9RXTiWDmEqKCDR+O239HzKFFqWtk7euaMaM3264eddHp6ewJgxtP7WW7R0dKz+D2eZrGJ3tWRtbNPG8lou1fDM6tpCgwYNNEr5SCQkJGgUQ68KfKmvKxw9CuzYQbE4H3yg2q7N4giQOUkX81F5bQdN7armUjwm4dAhoHdvMnR06EB/VmqNnBhLxNaWStAA5ncb5uQAX3+tim9o0UIVYtKyJXkNHjzQnOetW2RNmzGDBOTQofR6oKwo2buXrml+fvSHakrmzqUf5127UmeoDz80TMFSyQWtTThaYnyjBAtHi2DkyJH473//i7S0NMhkMpSUlODPP//EjBkz8Nprr+l1TO7bUFdITlatf/QR1fsKC9MUjpIIBHRzUwPlJ8eY2lXNFkejs3078NJLFGrVqxeFmnE8Yw3B05Pcp//+a16RMWsWsHKl6nlwsGrd1pbE4+XLJDYka8jatfS8SROKIXztNVW8X0YGeTekH5FSbGGHDqavMu/hYZwC5pJw1NZBRir8bcnCsYYWAa8tLFiwAGPGjEHTpk0hhICvry+Ki4sxcuRIzJkzR69jssWxriAJR6kDzsSJlN1QnqtaV+FoKa5qtjgajYICYMECqs388CG1pP39dxaNNQpLSZC5eJGWI0ZQ70kp+1lCm9iQKmfMmUOWRmtrcstKwlJ97M2btNS3P7QlIsUunjtX1oWflETL1q1NOyddaNOGlmxxNCu2trbYtGkTLl26hJ9++gkbN27EhQsX8P3338Naz7wAtjjWFSThOHkyuYoyMuiiU56rWpdSPIB5XdWScFTPyGQMysmTZOCRWvSGhZEBiHtM1zDUE2TMiXT+ceOAgQPL7i8tNgoLVZ2hSrue27YFUlJobNeutE0SjpX0Fa5RtG1LAcT37tHnJ32XgKrsT4sW5phZxUjX5KtX6Xu0tTXvfOo4rVq1QisDdRbS+/L//fff46uvvkJSUhLi4uLg7e2Nzz77DC1btsSwYcMMMjnGgEjCsXlz+qWekUEXHSnLUt3i2Ly55sWpIrS5qoUwjXD08aGLk5+fqmc2YzBOnwYGDKD7lZsbsGwZldzhPtM1kEctWPHvv5Sh+9dflHRh6i8zLY2W7u7a95eOiztxgrwijRoBTzxRdmxsrKZFS7rO1SaLo1xOFsVLl+gXnHRtLihQWZCrW/bHGHh60nX5wQMyUvCPe7MghMAvv/yC2NhYpKenl2nZvHXr1iofUy9X9apVqzBt2jQMGTIE9+7dQ/GjHp0NGjTAZ599ps8hGWOjfkGVLjxXrqj229sDTz1Fy5EjdT+uNld1draqb6sxXdX29sCFC8CWLcY7Rx3l2jVg0CASjT16UBm5UaNYNNZY1F3V779PyW9ffmnaORQWUtYzoBKypSktHCU39TPPlE3bl6yT2lzVtcniCKhEs2T6B+i9lpSQsHRzM8+8KsLKShV7/vff5p1LHeatt95CaGgokpKSUL9+fSgUCo2HPuhlcVyxYgXWrFmD5557DosWLVJu79KlC2bMmKHXRBgjoy4cpYu2JBytrcmN4O9PSqEqxfjU6zhKHQIka6NcTuLOmLCSMThnzlAcY2oqGXO3bzeu4ZgxAZJwTEqi1HgA+O474M03TTeHW7doaWNTtuuThLp7s6hIJRy1ZUiXFpkFBapz1Dbh6OsLREVpCkf17jSWWgurSxfg1CmyHL/4orlnUyfZuHEjtm7diiFDhhjsmHr9tSUlJaGjVFtKDblcjtzc3GpPijEwDx6o6rdpszja26sEWFUrOLu7042gsFD1a98UbmrG4JSUAL/8QnlR165R4n1MDH+NtQLpf/7vv1WJa8eOaVZbMDaSm7pJk/KFjpcX1ZAtLKTsaqmLVWXCUQiypgpB1zCp/FBtwdeXltqEoyXGN0o89RQtjx837zzqMAqFAo9JxdgNhF7CsWXLlkjUUlNq165d8JX+wBnLQRJ0jo5kIZRuIpcv07I6VkFbW9UFXLqosXCsMQhBxoAZMyhMasQIKrXXty/VaNSzPixjaZQXs7xtm+nmICXGlBffCGi6N0NC6I+xQQPtXVFatqTxubkkStXd1LXNEyHdV8+eVWVWS8LREuMbJSTheOKEZXUtqkPMnTsX8+bNQ56UCGsA9BKO77zzDt588038+OOPEELg2LFjWLBgAWbPno133nnHYJNjqsGaNSQI9+/XdFPLZCpX9bVrtKxuYknpX8OmKMXDVIuCAgpxa9OGru1Ll9J919mZmgv9/nv53kSmBuLmpimmJBd1VJTp5iBZHMuLb5RYtozEovTDtlcv7e1E7exUxcMvXqydpXgkfHzo+7t7V9XOsCYIxyeeoPvQ/ftcz9FMjBgxApmZmXBzc0P79u3RqVMnjYc+6BXj+Prrr6OoqAjvvvsuHjx4gJEjR6Jp06b4/PPP8corr+g1EcaAlJQA8+dT0b3vv6cLL6C6oErWh6IiWlY3DrG0cGSLo0WzaxdVZbp6lZ7b21MzjldeAQIDyVPI1DJsbMhFnJZG5uTp04EvvqAYwjt3qB+ysdHF4gjQ/P76i7KxDh8GKqrS0a4dhdz8/Tf9GgJqX3wjQP+kjz1G7/XcOfoupR/+liwcbWwoe//IEXJXm6p3OKNkzJgxOHnyJEaPHo0mTZpAZgBrvN7leMLDwxEeHo47d+6gpKQEbpaY1VVX2bdP1UHhyBFVDIwkHEv/4je0xZGFo8WyYgW10hWC7j3vv0+1GevXN/fMGKPTpg0Jx3HjyFL35JPUsm77dlULP2Oiq8URUDVBv3ZNZVXURteuwG+/AfHxqrjG2igcAbrOSsKxb9+aEeMIUIKMJBxHjzb3bOocO3bswO+//46eBuwNq3dyzD+PzM6urq5K0fjPP//gmvQriDEfGzao1i9cUPUzlYRj6V/8hrQ4CsGuagvk5k1g/HggIoK+onHj6B705pssGusMX30FfPst8Oqr9Hz4cFqayl1dWQ3H0lhZkZWtIguJVPg7Pr52u6oBzetsSYkqBMmSLY6AZpwjY3KaNWsGZ/XmHgZAL+E4ZswYHDlypMz2o0ePYsyYMdWdE1MdsrJUdQ0lRbBzJy2lC6qdnaZrqroWR6mzQVYWuaPY4mh2hKDfC19+Cbz8Mhlt1qyhfQsXUvMgR0ezTpExNb6+1AZIEmJDh9Jy715VByljIrmqdbE46srTT9P7SUqisi9A7bY4AiQcU1Mp89zaWvdmDeZCEo4JCarwKMZkLF26FO+++65BjXp6CceEhAT06NGjzPZu3bppzbZmTMhPP1Fso68vpcgC9BzQ/CWufrGprsVR6mwA0EVN+iXMFkeTk5EBLFpEoV8dO5JF8aef6HrduzewZw8wc2btSzpl9MDfn0RWXh4l0RmDoiKVWKiqxVEXFApVn3rpxlhbhaNUBPzsWdV7bdbM8vt/tmlDWXd5eTR3xqSMHj0asbGxaNWqFZycnODi4qLx0Ae9/uJkMhmys7PLbM/KylJ2kWHMxPr1tBwzhoSb9BzQFI4eHqpq/oZo1+frS/XUYmOB3btpW58+1T8uoxM5OZSMumQJNe4B6Gvt1Yu8eSEhFGrEMEpkMmDIEDI/79gBDB5s2OPn5wMdOtAPy5MnjWNxBIBu3TTrG9ZWV3W7dvQj//ZtlfvA0t3UAHmjunSh2PvYWPqbYEyGMbr56SUce/XqhYULF2Lz5s2wflQmobi4GAsXLjRoACZTRS5cAOLiyH0RGqpyGUsYy+IIkHDctg347DOyMHTrpr32GmNwzp8nr6NUveTJJymW8cUXAScns06NsXSCglTCccUKw5qiT5ygMjkAJbrk59O6IS2OAP0y+uYbWi8dhlObcHAAJk4EPv2UYlWBmiEcAeD550k4LllC74FLN5iMsLAwgx9TL+H4ySef4JlnnoGPjw96PSr1cujQIdy/fx/79u0z6ASZKiBdTAYPpouzmxtZHe/epbpo6lkQxhCOAHWpAYDw8Oofk6kQIYDoaApbu3+fElEXLybBaKkdyBgLo39/sgheu0a/QAzZwEHq+gIAkZG0bNDA8KKhWzfVupdX7f7jf+cdClyWRHhNEY7h4cAnn1AY0+rVVNqBMQk3pAor5dC8efMqH1Ov/zBfX1/8/fffeOmll5Ceno7s7Gy89tpruHDhAvz8/PQ5JFNdioup9yxAbmqALqABAbRe2n2j7i4ylKtawsmJMjIYo1BcTInzTz4JPPccicZevcjA89JLtfu+yRgYR0dVSMmOHYY9trpwlBL2DG1tBCj2T8r0qq3xjRIeHlQSQcLSS/FIyOVU+wsAPv6Yuv0wJqFFixZo2bJluQ990Duq1tPTEx9//LG+L2cMze7d1Ku1USMKaJPo3p1uCKV/mRra4ih1NhCCCvdyyq5ROHKEEl6kHDQHB+CNN+haXNU24wwDgNzVv/9O1wlDdf4qKQH+/FP1XAqbMXR8I0ChOU89RQk+tV04AsB//0vhBYWFNUc4AmTQ+L//ozpg7u508Zo0CfjgA3PPrFaTkJCg8bywsBAJCQlYtmwZFixYoNcxdRaOf//9N/z8/GBlZYW/paSKcvD399drMkw1kGo3jhypqSDCwyn2ccIEzfGGtjg6ONDF+9QpimFhDMqtW3S/kKIRGjSg7Ojx47nqEVNNBg6k5dGjJPgMYbK+cIHEor09Zf1KGVvGsDgCQL9+JBylzOPaTLNmVJMzPh545hlzz0Z3bG1JOL74ImXz5eRQr9M5c7S3lGQMQgctyUhdunSBp6cnFi9ejOFSPdcqoLNwfPLJJ5GWlgY3Nzc8+eSTkMlkEFKzdTVkMhlnVpuaK1coMQUo2wGicWOVC1sdQ1scAepAceeOYeOkGHz3HTBlCrmkAWDsWLIwcrMmxiC0akU39YcPqYi2HjFPZZDc1N260fVFqiVrDIsjAMyYQZm7daWSw3/+Q4+axgsvUK/tzEygc2f6QXH+PMAhbianbdu2OH78uF6v1Vk4JiUlofGjlk5JSUl6nYwxAkKQub+gABgwgALfdEH9l78hLI4AKRlWMwajqIjuh59/Ts87d6b2wlKzDIYxCDY2VIf1/HnKgjakcOzZk5LyJOFoLIujvb3hywkxxqFxY3o89RSV54mPZ+FoRO5LFodHCCGQmpqKuXPnok2bNnodU2fh6P0oRq6wsBBz587F+++/j8cee0yvkzIG5KefKL5RLqdsO13LaUhlK+7cMZzFkTEYBw+Sazo+np7/73/0YI8OYxTatlUJx2efrf7x1IVjgwaq7cayODI1j27dVMJRPeGHMSgNGjSArJQuEEKgWbNmiJSqHVSRKifH2NraIioqCu9LGVKM+cjJAaZOpfXZs6lCf1Xw9CThaCiLI1NtHjygMNVff6Xnjo4U1/jCC+adF1PL8fGh5aVL1T/W9evUAtDKisSBgwNZHXNyjGdxZGoeUhmlo0fNO49aTmxsrMZzKysrNG7cGK1bt4aNnl2H9HrV888/j23btmHatGl6nZQxECdOUBsvd3cyT1WViRNJlTyqxcmYl4ICEogxMeQ9HDeOKlhYeitaphbQti0tpYLd+nLlispl/NRT1GoOAObNo36X3btX7/hM7UGKuTl7lgK4pb8VxqD07t3b4MfUSzi2bt0aH330EY4cOYLOnTvDsVTplYiICINMjqkEqRZW06bkqq4qb7xBD8bsFBZSs5+YGDLQ7N4NaGkHzzDGwRAWx8uXqW7snTsUJyl1cwGAadPowTASTZpQOaFr14Djx6kYPWMQoqOjdR47dOjQKh9fL+G4du1aNGjQACdPnsTJkyc19slkMhaOpiIvj5Yco1ijSU4GXn2Vyt7Z2gJbt7JoZEyMZHG8fp2uK/pcU775hkSjvz/9AuJ4RqYyunUj4Rgfz8LRgDz33HM6jdO3Co5eBbuSkpKUj6tXr+Lq1asazxkT8fAhLVk41lh27QI6diTR6OxMonHQIHPPiqlzNG5MSSxCkLtZHy5coOW4cSwaGd2Q3NVSFqCFsHDhQjz11FNwcnKCm5sbnnvuOVwsFcYhhMDcuXPh6ekJe3t79OnTB2fPntUYk5+fjylTpsDV1RWOjo4YOnQobt68qTEmMzMToaGhUCgUUCgUCA0Nxb179zTG3LhxAyEhIXB0dISrqysiIiJQUFBQ7vxLSkp0euhbOlHvSq/r1q2Dn58f6tWrh3r16sHPzw9r167V93CMPkgWR24YX+MoKgJmzQKGDAEyMqjUzqlTQHCwuWfG1ElkMu1xjgcPUh/L06crP4b0Ouk4DFMZUoJMfDz9aLEQDhw4gDfffBPx8fHYs2cPioqKMHDgQOSqtUr85JNPsGzZMqxcuRLHjx+Hu7s7nn32WWRLxe4BTJ06FVFRUYiMjMThw4eRk5OD4OBgDcE2cuRIJCYmIiYmBjExMUhMTERoaKhyf3FxMYKCgpCbm4vDhw8jMjISW7ZswfTp003zYWhD6MGcOXOEo6OjmDlzpvj111/Fr7/+KmbOnCnq168v3nvvPX0OafEkJycLACI5OdncU1Hx+edCAEK89JK5Z8JUgcOHhejYkb46QIjJk4V4+NDcs2LqPKGh9Af58cf0fN06IWxsaNuYMRW/tqhICDs7GpuUZPSpMrWEhw+FcHCgv5vYWKOdprr37/T0dAFAHDhwQAghRElJiXB3dxeLFi1Sjnn48KFQKBTiq6++EkIIce/ePWFraysiIyOVY1JSUoSVlZWIiYkRQghx7tw5AUDEx8crx8TFxQkA4sKFC0IIIXbu3CmsrKxESkqKcszmzZuFXC4XWVlZOs1///79Ijg4WLRq1Uq0bt1ahISEiIMHD+r1WQghhF4Wx1WrVmHNmjVYuHAhhg4diqFDh2LhwoX4+uuv8dVXXxlQ1jIVwq7qGoUQwOTJVNouIYE8gz//DKxYoV9uE8MYFClB5uJFYMECalFUVETb4uIqfu3161QWQC43TAFxpm4glwNhYbT+6adGP112djbu37+vfOTn5+v0uqysLACAi4sLAArXS0tLw0CpXScAuVyO3r1748iRIwCAkydPorCwUGOMp6cn/Pz8lGPi4uKgUCjQVa2rQ7du3aBQKDTG+Pn5wVOtvMagQYOQn59fJsdEGxs3bsSAAQPg4OCAiIgITJ48Gfb29ujfvz9++OEHnd5/afQSjsXFxejSpUuZ7Z07d0aRdKFhjA+7qmsUX3xBD5mMekxfukRtWxnGIpBczNHR1D8YAN5+m5YXLwJ375b/WslN3aaNYXpdM3WHt96i5fbtlJlvRHx9fZWxhAqFAgsXLqz0NUIITJs2DT179oTfow43aWlpAIAmTZpojG3SpIlyX1paGuzs7NCwYcMKx7hp6bbm5uamMab0eRo2bAg7OzvlmIpYsGABPvnkE/z444+IiIjAW2+9hR9//BGLFi3CRx99VOnrtaHXf/jo0aOxatWqMtu//vprjBo1Sq+JMHrAWdU1hhMnVNVIli0DVq+mfASGsRgki2NmJi3ffpv+WKXGAhUVapbK+EjHYBhd8fGhYG8hgOXLjXqqc+fOISsrS/mYNWtWpa+ZPHky/v77b2zevLnMPm0dWUpvK03pMdrG6zOmPK5evYqQkJAy24cOHap3++hqJ8eMGzcO48aNg5+fH9asWQMrKytMmzZN+WCMCLuqawRpaZRfUFgIPP+86gc2w1gUrVur1vv1Az75hNbVExjKQ7I4snBk9EHqgPbNN0CpjGJD4uTkBGdnZ+VDXkmM0JQpUxAdHY3Y2Fh4eXkpt7s/6oBU2uKXnp6utA66u7ujoKAAmdIPsXLG3Lp1q8x5b9++rTGm9HkyMzNRWFhYxhKpjWbNmuGPP/4os/2PP/5As2bNKn29NvQSjmfOnEGnTp3QuHFjXLlyBVeuXEHjxo3RqVMnnDlzBgkJCUhISEBiYqJek2J0hF3VFs+tW0DfvtSBrWVLui7q2k6cYUyKgwPwn/9QEe/ISGpfBNBzoOI4R86oZqrDgAHAE09QU4uNG809GwghMHnyZGzduhX79u1Dy5YtNfa3bNkS7u7u2LNnj3JbQUEBDhw4gO6PuiN17twZtra2GmNSU1Nx5swZ5ZiAgABkZWXh2LFjyjFHjx5FVlaWxpgzZ84gNTVVOWb37t2Qy+Xo3Llzpe9l+vTpiIiIwBtvvIHvv/8eGzduxMSJE/HWW29hxowZenw60C+r2lAcOHBABAcHCw8PDwFAREVFaezPzs4Wb775pmjatKmoV6+eePzxx8WXX36p9VglJSUiMDBQ63Hu3r0rRo8eLZydnYWzs7MYPXq0yMzMrNJcLTKrWsqC/OQTc8+EKcX9+0JERgrRrh19RV5eQly+bO5ZMYweJCTQH7GzsxDFxdrHeHnRmLg4k06NqUVs3y7E5s1CFBQY/NBVvX+/8cYbQqFQiP3794vU1FTl48GDB8oxixYtEgqFQmzdulWcPn1avPrqq8LDw0Pcv39fOWbixInCy8tL7N27V5w6dUr069dPdOjQQRQVFSnHBAYGCn9/fxEXFyfi4uJE+/btRXBwsHJ/UVGR8PPzE/379xenTp0Se/fuFV5eXmLy5Mk6v/+tW7eKHj16CBcXF+Hi4iJ69Oghtm3bpvPrS2NW4bhz507x3nvviS1btmgVfOPGjROtWrUSsbGxIikpSaxevVpYW1trfcPLli0TgwcP1nqcwMBA4efnJ44cOSKOHDki/Pz8NL4YXbBI4ThiBF2sV6ww90yYR9y/L8T06ULI5apyO02bCvHPP+aeGcPoSWGhEI6O9Md89mzZ/Tk5qj/2jAzTz49hKqGq928AWh/r169XjikpKREffPCBcHd3F3K5XDzzzDPi9OnTGsfJy8sTkydPFi4uLsLe3l4EBweLGzduaIzJyMgQo0aNEk5OTsLJyUmMGjWqjGHr+vXrIigoSNjb2wsXFxcxefJk8VDHGm5jxowRe/fuFSUlJTqN1wWzCkd1tAm+J554Qnz44Yca2zp16iTmzJmjsS0xMVF4eXmJ1NTUMsfRpU6SLlikcAwOpov1mjXmngkjhIiKIpEo3UPbtBHiv/8VwpL+ZBhGL/r0oT/qtWvpeUmJEBcuCHHmjBCnTtE+V1fzzpFhysEi798mIiQkRMjlcuHp6SmmT58uEhISqn1Mi66b0LNnT0RHRyMlJQVCCMTGxuLSpUsYpNaT7cGDB3j11VexcuVKZcCqOrrUSdJGfn6+Rr0n9WrwFgNnVVsEmZlAaCglvqSkAI89BuzYQWFfixYBajHVDFMzkRJkFi6kGlJt2gCPPw60bw/Mm0f7ODGGYSyO6OhopKWl4YMPPsCJEyfQuXNn+Pr64uOPP8a1a9f0OqZFC8fly5fD19cXXl5esLOzQ2BgIL788kv07NlTOebtt99G9+7dMWzYMK3H0KVOkjYWLlyoUe/J19e3+m/I0HBWtVm5fRuYO5fuoRs3Uvm6mTOBM2eougQnwTC1hgEDaHnlCrBlCy2trMi4/uuvtI+FI8NYJA0aNMD48eOxf/9+XL9+Ha+//jq+//57tFavpFAFbAw8P4OyfPlyxMfHIzo6Gt7e3jh48CAmTZoEDw8PDBgwANHR0di3bx8SEhIqPI4+NZBmzZqlUU4oJSXF8sQjWxzNxvHjwLPPAo8aCuDxx4H161WGGYapVfTrB+zbRx1icnLIjN6/P/C//wGffUZjOKOaYSyawsJCnDhxAkePHsW1a9d0KuejDYsVjnl5eZg9ezaioqIQFBQEAPD390diYiKWLFmCAQMGYN++fbhy5QoaNGig8doXXngBvXr1wv79+3Wqk6QNuVyuUePp/v37hnljhoTL8ZiFf/4hi2JWFnnq3n8fGD4csLY298wYxkjIZFRXqjTLllHvzB9+AJ57ztSzYhhGB2JjY/HDDz9gy5YtKC4uxvDhw7F9+3b069dPr+NZrHAsLCxEYWEhrEq1r7K2tkZJSQkAYObMmRg3bpzG/vbt2+PTTz9VVkpXr5P09NNPAyhbJ6nGwq5qk3PjBjBoEHDnDtCpE7B/P+DkZO5ZMYyZkMmADz6gB8MwFoeXlxcyMjIwaNAgrF69GiEhIahXTWOTWYVjTk4OLqv1pkxKSkJiYiJcXFzQvHlz9O7dG++88w7s7e3h7e2NAwcO4LvvvsOyZcsAUEV1bQkxzZs3VxbsbNeuHQIDAxEeHo7Vq1cDAMaPH4/g4GD41PSYHHZVm5SDBykv4PZtoFUrYOdOFo0MwzCM5fK///0PI0aMKNMzuzqYVTieOHECfdXcH1JMYVhYGDZs2IDIyEjMmjULo0aNwt27d+Ht7Y0FCxZg4sSJVTrPpk2bEBERgYEDBwKgHo0rV6403BsxF+yqNhk//giMHg0UFQFPPgls2wboGR7CMAzDMCZh/PjxBj+mTAghDH7UWsjNmzfRrFkzJCcna/SsNCtyOVBQQAHrzZubeza1lrNngaeeIp3+yivAunXUnY1hGIaxfCzy/l2DsdgYR6YSSkpINALsqjYiubnAiBEkGp99lsrucBIMwzAMU1ex6DqOTAVIiTEAu6qNRGYmWRjPnwc8PFg0MgzDMAxbHGsqUnwjwBZHI3DgAMU03rwJ2NgAmzcDWurIMwzDMEydgi2ONRXJ4mhjQw/GYNy9CwQFkWhs3Ro4fBjo3dvcs2IYhmEY88OKo6bCGdVG4+efKbbR1xc4ehSoX9/cM2IYhmEYy4AtjjUVruFoNDZtouWYMSwaGYZhGEYdFo41Fe4aYxSuXwcOHaKGGK++au7ZMAzDMIxlwcKxpsIWR6OweTMte/cGuNwXwzAMw2jCwrGmwjGORkFyU48aZd55MAzDMIwlwsKxpsKuaoOzfTtw5gxgZ0c9qRmGYRiG0YSFY02FXdUG5dAh4KWXaH3sWKBBA7NOh2EYhmEsEhaONRV2VRuMv/4CgoPJiBsUBHz+ublnxDAMwzCWCQvHmgq7qg1CSgqJxfv3gZ49gZ9+AmxtzT0rhmEYhrFMWDjWVNhVXW1ycsjSmJICtGtHMY4ODuaeFcMwDMNYLiwcayrsqq42b7wBJCZSD+odOziukWEYhmEqg4VjTYVd1dXiwAFg40Yq9B0VBbRsae4ZMQzDMIzlw8KxpsKuar0pKgImT6b1CROA7t3NOx+GYRiGqSmwcKypsKtab774guo1NmoELFhg7tkwDMMwTM2BhWNNhV3VerFpEzBjBq1//DHg4mLe+TAMwzBMTYKFY02ioICyOYRgV3UVEQL49FNg9GhyVY8eDYwbZ+5ZMQzDMEzNgoVjTeK//wU6dgSio1k4VoHUVCAkBJg2jZ6/9Rbw7beAFf/1MwzDMEyVsDH3BJgq8M8/tPzrL5WrmmMcK+TcOaBXL+DuXepBvWgRMHUqZVMzDMMwDFM1WDjWJHJzaZmayhZHHSgoIJf03btAhw4U3/jEE+aeFcMwDMPUXFg41iRYOFaJjz4CEhIoAWbXLsDDw9wzYhiGYZiaDUd51SRycmipLhzZVa2VmBjKmgaAr75i0cgwDMPoxsGDBxESEgJPT0/IZDJs27ZNY/+YMWMgk8k0Ht26ddMYk5+fjylTpsDV1RWOjo4YOnQobt68qTEmMzMToaGhUCgUUCgUCA0Nxb179zTG3LhxAyEhIXB0dISrqysiIiJQUFBgjLetMywcaxLqFkcux6OVoiJgzhxgyBCgpAQYNQoYMcLcs2IYhmFqCrm5uejQoQNWrlxZ7pjAwECkpqYqHzt37tTYP3XqVERFRSEyMhKHDx9GTk4OgoODUVxcrBwzcuRIJCYmIiYmBjExMUhMTERoaKhyf3FxMYKCgpCbm4vDhw8jMjISW7ZswfTp0w3/pqsAu6prEpJwTEujBssAC8dHFBZSDOOiRcDFi7Rt/Hjgs8/MOi2GYRimhjF48GAMHjy4wjFyuRzu7u5a92VlZWHdunX4/vvvMWDAAADAxo0b0axZM+zduxeDBg3C+fPnERMTg/j4eHTt2hUAsGbNGgQEBODixYvw8fHB7t27ce7cOSQnJ8PT0xMAsHTpUowZMwYLFiyAs7OzAd+17rDFsSYhuaoLC0k8AnXeVf3gAbBiBdC6NfD66yQaGzYEfvwRWL2adTXDMAxDZGdn4/79+8pHfn6+3sfav38/3Nzc0LZtW4SHhyM9PV257+TJkygsLMTAgQOV2zw9PeHn54cjR44AAOLi4qBQKJSiEQC6desGhUKhMcbPz08pGgFg0KBByM/Px8mTJ/Wee3Vh4VhTKC4G1P/IJXN3HVZGN28C7doBERHAjRuAuzuweDFw/Trw0kvmnh3DMAxjSfj6+irjCRUKBRYuXKjXcQYPHoxNmzZh3759WLp0KY4fP45+/fophWhaWhrs7OzQsGFDjdc1adIEaY+MPmlpaXCTPIdquLm5aYxp0qSJxv6GDRvCzs5OOcYcsKu6piC5qUtTR4VjQQHFLt64ATRrBsyeDYwZU+cNsAzDMEw5nDt3Dk2bNlU+l8vleh3n5ZdfVq77+fmhS5cu8Pb2xo4dOzB8+PByXyeEgEytiLBMS0FhfcaYGhaONQXJTV2aOqqUpk0D4uOBBg2A/fuBxx4z94wYhmEYS8bJyckocYEeHh7w9vbGP4+adLi7u6OgoACZmZkaVsf09HR0795dOebWrVtljnX79m2lldHd3R1Hjx7V2J+ZmYnCwsIylkhTwq7qmgJbHAEAmZnA2LHAF1/Q840bWTQyDMMw5iMjIwPJycnweFT3rXPnzrC1tcWePXuUY1JTU3HmzBmlcAwICEBWVhaOHTumHHP06FFkZWVpjDlz5gxSU1OVY3bv3g25XI7OnTub4q1phS2ONQVtwlEmA/Q0tdc0SkqAzZuBGTMoL0gmowzqoCBzz4xhGIapTeTk5ODy5cvK50lJSUhMTISLiwtcXFwwd+5cvPDCC/Dw8MC1a9cwe/ZsuLq64vnnnwcAKBQKjB07FtOnT0ejRo3g4uKCGTNmoH379sos63bt2iEwMBDh4eFYvXo1AGD8+PEIDg6Gj48PAGDgwIHw9fVFaGgoFi9ejLt372LGjBkIDw83W0Y1AEAwOpGcnCwAiOTkZPNM4NAhIQDNh729eeZiYv7+W4inn1a97ccfF+LwYXPPimEYhqkJVPX+HRsbKwCUeYSFhYkHDx6IgQMHisaNGwtbW1vRvHlzERYWJm7cuKFxjLy8PDF58mTh4uIi7O3tRXBwcJkxGRkZYtSoUcLJyUk4OTmJUaNGiczMTI0x169fF0FBQcLe3l64uLiIyZMni4cPH1br86guMiGEMJ9srTncvHkTzZo1Q3JyMry8vEw/gd9/BwIDNbc1bEiNmGsxBQXA448DSUlA/fqUBPP223U2tJNhGIapIma/f9cy2FVdU5Bc1dbWdaoUz5o1JBo9PICTJ7l1IMMwDMOYE06OqSlIWdUtWqi21XLhmJsLfPQRrb//PotGhmEYhjE3LBxrCpLFsXVr1bZa7q9dsQK4dQto2ZIyqRmGYRiGMS8sHGsKknB0cwOcnGi9Flscf/gB+PBDWp83D7CzM+98GIZhGIYxs3A8ePAgQkJC4OnpCZlMhm3btmnsz8nJweTJk+Hl5QV7e3u0a9cOq1at0hgzYcIEtGrVCvb29mjcuDGGDRuGCxcuaIzJzMxEaGioss1QaGgo7t27Z+R3Z2AkV7Wjo8pnWwuFY0kJldwZNQrIywOCg4GRI809K4ZhGIZhADMLx9zcXHTo0AErV67Uuv/tt99GTEwMNm7ciPPnz+Ptt9/GlClT8OuvvyrHdO7cGevXr8f58+fx+++/QwiBgQMHolhKIAEwcuRIJCYmIiYmBjExMUhMTERoaKjR359BkSyO9eurhGMtdFXPmwcsXUrrs2cD27ZRPhDDMAzDMObHrFnVgwcPxuDBg8vdHxcXh7CwMPTp0wcAFcdcvXo1Tpw4gWHDhim3SbRo0QLz589Hhw4dcO3aNbRq1Qrnz59HTEwM4uPj0bVrVwDAmjVrEBAQgIsXLyoLbVo8knB0dATc3Wm9llkcf/5Z5Z5eswYYN86882EYhmEYRhOLjnHs2bMnoqOjkZKSAiEEYmNjcenSJQwaNEjr+NzcXKxfvx4tW7ZEs2bNAJD4VCgUStEIAN26dYNCocCRI0dM8j4MQi13VV+4AISF0fr06SwaGYZhGMYSsWjhuHz5cvj6+sLLywt2dnYIDAzEl19+iZ49e2qM+/LLL1G/fn3Ur18fMTEx2LNnD+weZVOkpaXBzc2tzLHd3NyQlpZW7rnz8/Nx//595SM7O9uwb66qqLuq27ShdTM2OTc069ZRTGO/fsD//Z+5Z8MwDMMwjDYsXjjGx8cjOjoaJ0+exNKlSzFp0iTs3btXY9yoUaOQkJCAAwcOoE2bNnjppZfw8OFD5X6ZTFbm2EIIrdslFi5cqEymUSgU8PX1Ndwb0wd1V/WYMcD69cCcOWadkiHZtYuW48dzTCPDMAzDWCoW2zkmLy8Ps2fPRlRUFIKCggAA/v7+SExMxJIlS5SNwgEoxV2bNm3QrVs3NGzYEFFRUXj11Vfh7u6OW7dulTn+7du30aQCi92sWbMwbdo05fOUlBTzikd1V7WDA4nHWkJyMnD2LGBlBTz7rLlnwzAMwzBMeVisxbGwsBCFhYWwstKcorW1NUpKSip8rRAC+fn5AICAgABkZWXh2LFjyv1Hjx5FVlYWunfvXu4x5HI5nJ2dlQ8nqXaiKblxA5Dc6equ6lpGTAwtu3YFXFzMOxeGYRiGYcrHrBbHnJwcXL58Wfk8KSkJiYmJcHFxQfPmzdG7d2+88847sLe3h7e3Nw4cOIDvvvsOy5YtAwBcvXoVP/74IwYOHIjGjRsjJSUF//d//wd7e3sMGTIEANCuXTsEBgYiPDwcq1evBkCZ2MHBwZadUZ2XB7RvDzg7k4BUd1XXMiQ3dQUJ9gzDMAzDWABmFY4nTpxA3759lc8l13BYWBg2bNiAyMhIzJo1C6NGjcLdu3fh7e2NBQsWYOLEiQCAevXq4dChQ/jss8+QmZmJJk2a4JlnnsGRI0c0EmI2bdqEiIgIDBw4EAAwdOjQcmtHWgy3bwP379Pj7l1NV3UtoqAAkEJWWTgyDMMwjGVjVuHYp08fCCHK3e/u7o7169eXu9/T0xM7d+6s9DwuLi7YuHGjXnM0G3l5qvW0tFprcTxyBMjOBho3Bjp1MvdsGIZhGIapCIuNcazzPHigWk9NrbUxjtHRtBw0iJJjGIZhGIaxXPhWbamoWxyvXQOkFoq1yOKYmQmsXUvrI0aYdy4MwzAMw1QOC0dLRV04qiUQ1Sbh+Pnn5KZu3x4IDjb3bBiGYRiGqQwWjpaKunC8coWWcjlgY7GlN6tEVhYJRwB4/312UzMMwzBMTYBv15aKeoyjZHGsRdbGZcuAe/cAX1/ghRfMPRuGYRiGYXShdpivaiPaXNW1QDg+fAi88w4gVUN67z22NjIMwzBMTYGFo6WiLhylGo41PKN6715g2jTg9Gl6/u67wKuvmndODMMwDMPoDgtHS0VdOErUUItjSgowfjwgldxs1Aj47jvgUXMfhmEYhmFqCOwktFTUYxwlaqBw/O03oEMHEo02NsBbbwEXLrBoZBiGYZiaCFscLRVtFsca5KrOzwdmzgQ++4yed+oEbNoEPP64WafFMAzDMEw1YIujpVKDXdUnTgDdu6tE49Sp1FqQRSPDMAzD1GzY4mip1EDhGBMDfPABcOwYPW/UCFi/HggJMe+8GIZhGIYxDGxxtFS0xThasKt6xQqKWzx2DLC1BUaPBhITWTQyDMMwTG2ChaOlIlkc1YscWqDFUQhg9mwgIoLWx40DkpOB778HvLzMPTuGYRiGYQwJC0dLRRKOzZqptlmgcPzyS2DhQlpfsAD4+mugSRPzzolhGIZhGOPAwtFSkYTjY4+ptlmYcDx4kBJfAGDRIrI8ymRmnRLDMAzDMEaEhaOlIsU4qgtHC4pxTEoCRowAioqAV16hLjAMwzAMw9RuWDhaKhZscUxOBvr1A9LTqbj3unVsaWQYhmFqBwcPHkRISAg8PT0hk8mwbds2jf1CCMydOxeenp6wt7dHnz59cPbsWY0x+fn5mDJlClxdXeHo6IihQ4fi5s2bGmMyMzMRGhoKhUIBhUKB0NBQ3Lt3T2PMjRs3EBISAkdHR7i6uiIiIgIFBQXGeNs6w8LRUrFQ4fjvv0D//sC1a0Dr1sCuXYCDg7lnxTAMwzCGITc3Fx06dMDKlSu17v/kk0+wbNkyrFy5EsePH4e7uzueffZZZGdnK8dMnToVUVFRiIyMxOHDh5GTk4Pg4GAUFxcrx4wcORKJiYmIiYlBTEwMEhMTERoaqtxfXFyMoKAg5Obm4vDhw4iMjMSWLVswffp04715XRCMTiQnJwsAIjk52TQn9PYWAhDiyBEhZDJa37vXNOcuh3PnhGjenKbi7S3E9etmnQ7DMAzDVEp17t8ARFRUlPJ5SUmJcHd3F4sWLVJue/jwoVAoFOKrr74SQghx7949YWtrKyIjI5VjUlJShJWVlYiJiRFCCHHu3DkBQMTHxyvHxMXFCQDiwoULQgghdu7cKaysrERKSopyzObNm4VcLhdZWVlVfi+Ggi2OlooU41i/vipN2cnJbNM5cgTo0QO4cQNo0waIjQWaNzfbdBiGYRjm/9u79+AoykT949/JhAyTkAwJkoSQcJGFHCKXZfmtEAoXqEUIy8XV1XWFjWghl3WBAwtaop7CVYEF7xSFsrjLRVTcU4pQezwRrAOoRQCJlV1uikhEkAzXMAmQy0D698fYQ+7phElmSJ5PVdf09Lzz9tuTycwzb7/d3SBFRUUUFhb6p9LS0gbXkZeXh9vtZtSoUf5lDoeDYcOGsWvXLgBycnLwer2VyiQlJdGnTx9/mezsbFwuF4MGDfKXGTx4MC6Xq1KZPn36kJSU5C8zevRoSktLycnJaXDbA0XBMVSZu6ojI+G552DyZN8Fn4Pg3/+GMWOgoAAGD/aFyO7dg9IUERGRRklLS/OPJ3S5XCwxzyXXAG63G4CEKuedS0hI8D/mdruJiIggNja2zjLx8fHV6o+Pj69Upup6YmNjiYiI8JcJBl1yMFSZwdHp9J1V+5FHgtKM48d9obGwEO64w3dZQY1pFBGRm82hQ4fo3Lmz/77D4Wh0XbYqR4QahlFtWVVVy9RUvjFlmpt6HEOR1wvmAFqnM2jNOH8eMjJ8B8Tcdhts3qzQKCIiN6fo6GhiYmL8U2OCY2JiIkC1Hr8zZ874ewcTExMpKyujoKCgzjKnT5+uVv/Zs2crlam6noKCArxeb7WeyOak4BiKKl6nOkjB8coV33Wmv/rKd+nArCyo0usuIiLSqnTv3p3ExES2bdvmX1ZWVsbOnTsZMmQIAAMHDqRNmzaVyuTn53PgwAF/mfT0dDweD3v37vWX2bNnDx6Pp1KZAwcOkJ+f7y+zdetWHA4HAwcObNLtrIt2VYcicze1zQY30JXeWOZJvbOzfWHx44913WkREWkdLl26xNGjR/338/LyyM3NJS4uji5dujBnzhwWL15Mz5496dmzJ4sXLyYyMpKJEycC4HK5mDJlCvPmzaNDhw7ExcUxf/58+vbty8iRIwHo3bs3GRkZTJ06lVWrVgEwbdo0xo0bR2pqKgCjRo0iLS2NzMxMXnjhBS5cuMD8+fOZOnUqMTExzfyqVBC047lvMs16Op5jx3znvImMbPp1VVFebhiPPOJbfdu2hvH5583eBBERkYBp6Pf39u3bDaDaNHnyZMMwfKfkWbhwoZGYmGg4HA7jF7/4hbF///5KdRQXFxszZ8404uLiDKfTaYwbN874/vvvK5U5f/68MWnSJCM6OtqIjo42Jk2aZBQUFFQqc/z4cWPs2LGG0+k04uLijJkzZxolJSWNfi0CwWYYhhG82HrzOHnyJCkpKZw4cYLkpu5+O3TIN6iwQwc4d65p11XFwoXw7LMQFgbvvw+//nWzrl5ERCSgmvX7uxXQrupQZI5xbMbxjeXl8PTTYJ6dYOVKhUYRERGpTMExFFU8h2MzuHwZJk3yHTUN8PzzMH16s6xaREREbiIKjsG2bx/k58OAAdePQKl4Dsdm8OyzvtDocMCbb8Lvf98sqxUREZGbjE7HE2wLFsCECbBz5/VlzRgcDQPee883//e/KzSKiIhI7RQcg83cHV3x3I3NOMYxN9d3dRinU2MaRUREpG4KjsFWU3Bsxh7HTZt8txkZuiqMiIiI1E3BMdjMtGaGxYrzzZDkzOB4991NvioRERG5ySk4BpvZqxiEHsejR+HAAbDbYdy4Jl2ViIiItAAKjsEWxDGOH37oux0+XNehFhERkfopOAZbkMY4lpfD+vW+ee2mFhERESsUHIOtruDYhGMc33sP9u+HmBh44IEmW42IiIi0IAqOwRaEHkevF/7rv3zzjz8OcXFNshoRERFpYYIaHD/99FPGjx9PUlISNpuND81Bdz+6dOkSM2fOJDk5GafTSe/evXn99df9j1+4cIFZs2aRmppKZGQkXbp0Yfbs2Xg8nkr1FBQUkJmZicvlwuVykZmZycWLF5thCy0IwhjHv/0Nvv0WEhLgP/+zSVYhIiIiLVBQg+Ply5fp378/K1asqPHxuXPnkpWVxYYNGzh8+DBz585l1qxZbP7xosqnTp3i1KlTvPjii+zfv5+1a9eSlZXFlClTKtUzceJEcnNzycrKIisri9zcXDIzM5t8+yxp5h7HK1fgz3/2zT/9NLRrF/BViIiISAsV1GtVjxkzhjFjxtT6eHZ2NpMnT2b48OEATJs2jVWrVrFv3z7uuusu+vTpw/vvv+8v36NHDxYtWsTvf/97rl69Snh4OIcPHyYrK4vdu3czaNAgAFavXk16ejpff/01qampTbqN9WrmMY7Ll4PbDd26wbRpAa9eREREWrCQHuM4dOhQtmzZwg8//IBhGGzfvp0jR44wevToWp/j8XiIiYkhPNyXibOzs3G5XP7QCDB48GBcLhe7du2qtZ7S0lIKCwv9U1FRUeA2rKJm7HEsKIClS33zzz4LEREBrV5ERERauJAOjsuXLyctLY3k5GQiIiLIyMhg5cqVDB06tMby58+f57nnnmP69On+ZW63m/j4+Gpl4+Pjcbvdta57yZIl/jGRLpeLtLS0G9+gmjTjGMdly+DiRejTByZODGjVIiIi0gqEfHDcvXs3W7ZsIScnh5deeolHH32UTz75pFrZwsJCxo4dS1paGgsXLqz0mM1mq1beMIwal5sWLFiAx+PxT4cOHbrxDaqJGQ5ruuRgAIPjqVPw2mu++UWLfFeLEREREWmIoI5xrEtxcTFPPvkkmzZtYuzYsQD069eP3NxcXnzxRUaOHOkvW1RUREZGBu3atWPTpk20adPG/1hiYiKnT5+uVv/Zs2dJSEiodf0OhwOHw+G/X1hYGIjNqq6Zxjg++aSv2vR0GD8+YNWKiIhIKxKyPY5erxev10tYWOUm2u12ysvL/fcLCwsZNWoUERERbNmyhbZt21Yqn56ejsfjYe/evf5le/bswePxMGTIkKbdCCuaYYxjTg6sW+ebf+UVqKOjVURERKRWQe1xvHTpEkePHvXfz8vLIzc3l7i4OLp06cKwYcN47LHHcDqddO3alZ07d7J+/XpefvllwNfTOGrUKK5cucKGDRv8B7IAdOzYEbvdTu/evcnIyGDq1KmsWrUK8B2dPW7cuOAfUQ3Xg2NZGVy9CuHhAR3jaBgwZ45vftIkqHCMkIiIiEiDBDU47tu3jxEjRvjv/+lPfwJg8uTJrF27lo0bN7JgwQImTZrEhQsX6Nq1K4sWLWLGjBkA5OTksGfPHgB+8pOfVKo7Ly+Pbt26AfD2228ze/ZsRo0aBcCECRNqPXdks6u4O7q4GKKjA9rj+NJL8PnnvqqWLLnh6kRERKQVsxmGYQS7ETeDkydPkpKSwokTJ0hOTg5cxYYB5u54txvi46/fz8+HxMRGV710KTzxhG9+2TJ47LEbbKuIiMhNpsm+v1upkB3j2GrYbJXHOZaWXn+skQfHGIbvqjBmaHzmGZg//8aaKSIiIhKyR1W3KpGRvtBoTqZG7KouK4NHHoG33vLdX7wYFiwIUDtFRESkVVNwDAUVexzN8Y12O1Q4rZAVV6/CPffA//yP7+lvvOELkSIiIiKBoOAYCsyexYrBsYG9jYYBf/iDLzQ6nfD++1DHZcBFREREGkxjHEOB2eNYXNzok38vWQJvvuk7rmbjRoVGERERCTwFx1BQcVd1I87hmJvrOxgGYPlymDAhsM0TERERAQXH0FDTGEeLwdEwYN483+1vfwt//GMTtVFERERaPQXHUHADwfGf/4T/+z9wOHznbRQRERFpKgqOoaBicPR4fPMxMfU+zeu9fn7GuXPhxwvliIiIiDQJBcdQUDE4nj/vm+/Qod6nrVsHR45Ax446V6OIiEggPPPMM9hstkpTYoWruBmGwTPPPENSUhJOp5Phw4dz8ODBSnWUlpYya9YsbrnlFqKiopgwYQInT56sVKagoIDMzExcLhcul4vMzEwuXrzYHJt4QxQcQ0EjguPVq9evPb1ggaUOShEREbHgtttuIz8/3z/t37/f/9iyZct4+eWXWbFiBV988QWJiYnceeedFBUV+cvMmTOHTZs2sXHjRj7//HMuXbrEuHHjuHbtmr/MxIkTyc3NJSsri6ysLHJzc8nMzGzW7WwMnccxFFQMjuYbr57g+M47cOyYr7dx+vQmbp+IiEgrEh4eXqmX0WQYBq+++ipPPfUU99xzDwDr1q0jISGBd955h+nTp+PxePjb3/7GW2+9xciRIwHYsGEDKSkpfPLJJ4wePZrDhw+TlZXF7t27GTRoEACrV68mPT2dr7/+mtTU1Obb2AZSj2MoaGCP47VrsGiRb37+/EZf0lpERKTVKCoqorCw0D+VlpbWWvabb74hKSmJ7t2787vf/Y5jx44BkJeXh9vtZtSoUf6yDoeDYcOGsWvXLgBycnLwer2VyiQlJdGnTx9/mezsbFwulz80AgwePBiXy+UvE6oUHENBxSvHWAiOK1f6xjZ26ACPPtoM7RMREbnJpaWl+ccTulwulpjjvaoYNGgQ69ev5+OPP2b16tW43W6GDBnC+fPncbvdACQkJFR6TkJCgv8xt9tNREQEsbGxdZaJj4+vtu74+Hh/mVClXdWhoOKVYy5c8M3XEhw3b4Y5c3zzTz0F7do1ffNERERudocOHaJz587++w6Ho8ZyYypceq1v376kp6fTo0cP1q1bx+DBgwGw2WyVnmMYRrVlVVUtU1N5K/UEm3ocQ0FNu6rj4qoV270bfvc7KC+HKVOuB0gRERGpW3R0NDExMf6ptuBYVVRUFH379uWbb77xj3us2it45swZfy9kYmIiZWVlFBQU1Fnm9OnT1dZ19uzZar2ZoUbBMRRYHOM4bx6UlMC4cfDGGxDiP0pERERueqWlpRw+fJhOnTrRvXt3EhMT2bZtm//xsrIydu7cyZAhQwAYOHAgbdq0qVQmPz+fAwcO+Mukp6fj8XjYu3evv8yePXvweDz+MqFKu6pDgRkcCwvBPIdTleD41VewaxfY7fDXv0K4/nIiIiIBN3/+fMaPH0+XLl04c+YMzz//PIWFhUyePBmbzcacOXNYvHgxPXv2pGfPnixevJjIyEgmTpwIgMvlYsqUKcybN48OHToQFxfH/Pnz6du3r/8o6969e5ORkcHUqVNZtWoVANOmTWPcuHEhfUQ1KDiGBjM4/vDD9WVVdlWvWeO7/dWvoFOnZmqXiIhIK3Py5EkeeOABzp07R8eOHRk8eDC7d++ma9euADz++OMUFxfz6KOPUlBQwKBBg9i6dSvR0dH+Ol555RXCw8P57W9/S3FxMb/85S9Zu3YtdrvdX+btt99m9uzZ/qOvJ0yYwIoVK5p3YxvBZhiGEexG3AxOnjxJSkoKJ06cIDk5ObCVZ2dDxa5pl+t6zyO+SwumpMDp07BpE/z614FdvYiISEvVpN/frZDGOIaCqidirLKb+n//1xca4+Nh7NhmbJeIiIhIBQqOoaCe4Pjj8AcefBDatGmmNomIiIhUoeAYCswTgJsqBMe//x0++gjCwnyn4BEREREJFgXHUFC1x/HHA2P+9S/44x99i557Dv7jP5q5XSIiIiIVKDiGghp2VV+5Avfd5ztv469+BU88EZymiYiIiJgUHEOBw1H5bN4dOvD88/DNN5CcDOvX+3ZVi4iIiAST4kgosNkq9Toe9v6EF1/0za9YUetlq0VERESalYJjqPgxOBrAHz4YidcL48fDXXcFt1kiIiIiJgXHUPFjcNzE3ew8nIDTCcuXB7lNIiIiIhUoOIaKyEgMYBFPAfCnP0G3bkFtkYiIiEglCo6hIjKSjxnNlwwk0lnOnDnBbpCIiIhIZQqOoSIy0t/bOGOKl1tuCXJ7RERERKpQcAwRn5X8nM+5gwhKmfdERLCbIyIiIlKNgmOIWHR8EgAPOzeS1NlWT2kRERGR5qfgGAJycuDjMz/DzlUe7/xOsJsjIiIiUiMFxxCweLHv9gHe5dZOxcFtjIiIiEgtFByD7NAh+OAD3/wT/EWXiREREZGQpeAYZH/5i+/27l4HuI1DCo4iIiISshQcg6i8HIqLfZeqfnK+F269Fe6+O9jNEhEREalRUIPjp59+yvjx40lKSsJms/Hhhx9WevzSpUvMnDmT5ORknE4nvXv35vXXX69U5q9//SvDhw8nJiYGm83GxYsXq62noKCAzMxMXC4XLpeLzMzMGss1t7Aw+O//hm+/hf83dYBvZuzYYDdLREREpEZBDY6XL1+mf//+rFixosbH586dS1ZWFhs2bODw4cPMnTuXWbNmsXnzZn+ZK1eukJGRwZNPPlnreiZOnEhubi5ZWVlkZWWRm5tLZmZmwLensbp3D3YLREREROoXHsyVjxkzhjFjxtT6eHZ2NpMnT2b48OEATJs2jVWrVrFv3z7uuusuAOb8eG2+HTt21FjH4cOHycrKYvfu3QwaNAiA1atXk56eztdff01qamrAtkdERESkJQvpMY5Dhw5ly5Yt/PDDDxiGwfbt2zly5AijR4+2XEd2djYul8sfGgEGDx6My+Vi165dtT6vtLSUwsJC/1RUVHRD2yIiIiJyswvp4Lh8+XLS0tJITk4mIiKCjIwMVq5cydChQy3X4Xa7iY+Pr7Y8Pj4et9td6/OWLFniHxPpcrlIS0tr1DaIiIiItBQhHxx3797Nli1byMnJ4aWXXuLRRx/lk08+aVA9Nlv1S/gZhlHjctOCBQvweDz+6dChQw1uv4iIiEhLEtQxjnUpLi7mySefZNOmTYz98Ujjfv36kZuby4svvsjIkSMt1ZOYmMjp06erLT979iwJCQm1Ps/hcOBwOPz3CwsLG7gFIiIiIi1LyPY4er1evF4vYWGVm2i32ykvL7dcT3p6Oh6Ph7179/qX7dmzB4/Hw5AhQwLWXhEREZGWLqg9jpcuXeLo0aP++3l5eeTm5hIXF0eXLl0YNmwYjz32GE6nk65du7Jz507Wr1/Pyy+/7H+O2+3G7Xb769m/fz/R0dF06dKFuLg4evfuTUZGBlOnTmXVqlWA7+jscePG6YhqERERkQawGYZhBGvlO3bsYMSIEdWWT548mbVr1+J2u1mwYAFbt27lwoULdO3alWnTpjF37lz/+MRnnnmGP//5z9XqWLNmDQ899BAAFy5cYPbs2WzZsgWACRMmsGLFCtq3b2+5rSdPniQlJYUTJ06QnJzc8I0VERGRZqfv78AKanC8meiNJyIicvPR93dghewYRxEREREJLQqOIiIiImKJgqOIiIiIWKLgKCIiIiKWhOwJwEONee7I/Pz8ILdERERErDK/txtyDmipnYKjRebVZ26//fYgt0REREQa6vTp03Tp0iXYzbjpaVe1RQMGDGDv3r0cP36cEydOBGwyr4F96NAh/7x5lZvaboNZVu1UO9VOtTPUyqqdLWebDh06FNDv2BMnTnD8+HH27t3LgAEDkBunHkeLwsPD+fnPfx7wes1rYHfu3Nm/rFOnTnXeBrOs2ql2qp1qZ6iVVTtbzjZ17tyZmJgYAk09jYGjHkcRERERsUTBUUREREQs0a7qIHM4HCxcuBCHwwHAwoULiYmJqfM2mGXVTrVT7VQ7Q62s2tlytsksI6FL16oWEREREUu0q1pERERELFFwFBERERFLFBxFRERExBIFRxERERGxREdVB8jKlSt54YUXyM/PJz4+Hq/Xy7lz5wgLC+Pq1auArpMpIiLSEGFhYYSHhxMVFUVhYSHl5eXYbDZsNhsAHTt2ZOTIkSxdupSkpKQgt7Z10FHVAfDee++RmZnJypUrKSws5LHHHvO/sVNTUzl48CBt27alpKSEiIgIysrKgt1kEZGAstls6OtEKgoLC/MHvdreG7GxsZSXl1NUVER0dDQej8f//VleXk5ERATh4eE4HA68Xi/l5eXMnTsXl8vF0qVLufPOOzl+/DgAu3btas7Na70MuWG33367MWPGjErzbdu2NQYMGGD069fPAIyOHTsa0dHRBmBERUUZgGG32w3AP4WFhRmAYbPZjIiIiErLaprCw8NrfH5Nk/lY1XVamWw2m+UyVuuvWs7KOkJhMv8ugZratm3bJO10Op1N9ho05P1Q3xQbGxv0v6mVaeDAgc2+zmHDhgV9uxsyPfzww0Fb96xZsyyX7d+/v+Wy7dq1C9o2JSQk1PpYZGRktWVdu3Zt8PY19TR06FADMNq0aVPj43FxccYrr7xivPnmmwZgzJkzx4DK32VTpkwxAOPdd981ACM6Otr/3bts2TKje/fuxubNmw2bzWaUlZUFKwa0KhrjeIPKysrIyclh1KhR/vkRI0ZQWlpKSUkJBw8eBODs2bP069cPgLS0NACuXbtWqS5zV7ZhGNjt9krLamKWqfr8mpiPVV2nFYaFXgSzjNX6q5azso6atGnTplHPa6yG9haHhdX9L1ZSUlLt7xgIxcXFREREBLxeuL5N4eG1j3Sx+vcsKCgISJuairmt7du3t1zWCnM3W13M18ZK2YaqeM3guljZJvN9sHv3bsvrt1JvQ17PN954o94y5v/Zd999Z7ney5cvWy4baKdPn671sStXrlRbZva6/fvf/26yNgG0bdvWctl27doBEBcXV+PjxcXFvPDCC8ydOxeADz/8EACn00l4eDg2m42EhATA91npdDr9Q78APB4PMTExvP322wwZMqTZvw9aKwXHG3Tu3DmuXbtGQkKCf97hcGAYBh6Ph2vXrhEZGQlAYmIiAEePHq233uLi4nrLlJaW3ljjb2LmB4SVgNJUAcoKM7DX9SVY9YM4KioqIOu2EnLrCn+1MYNMxQ/wqqy+5g0JB8Fg/v0OHz5cb1nz/9wKK+9bMwA09kdVXX744QdL5ayMy27Ia9SYeq3wer31ljF/rHo8Hsv1NsVrfyNu9EdmQ0JfbZ8NDfnxfOzYMaD2EFxcXEzbtm2JioqiV69e/lB/+fJlrl69yn333ccrr7wCQH5+PjExMf6/yfTp01m0aBH/+te/+P7779m8ebPldsmNCe1P7ZtIxV6Bqj0Ev/jFLwB4//33ASgqKmq+hrUw5mtrflHUFTzMS1fVFXCaS11fgmavhhm2rPxoaEzgqqnnqupr06FDh3rrsfKFbvXLpUePHpbKBZuVL1yzd6Uu5l4HK8y/cTDCtblOK9ttvm9r61WqyAzXdQWgxvzQs9JO8/3fmB9L9WlIT5fV7YuJiam2rOqempr+p+sKuyUlJZbWXVHV19bKj2FTXl5enY9HRUVx7Ngx0tLS/D2L0dHR/u36xz/+wS233EJ8fDx2u92/badOnWLbtm3ce++9bN26FbvdzoMPPhhyQb+lUnC8Qbfccgt2ux232+2fLykpwWaz4XK5sNvt/n+CkSNHAjB69Gh/r1KvXr0AcLlcwPVdSBV3JVW9dmdsbGyl21BjtqshPTBWNeQXt9kjG8yj2RvyC98MW4HujTFZ+VA9f/58k6y7Nk6nM2B1NQUzZFj5O7rd7nrLFBYWWl63+To3xfvX6u5vK0HDDBBWypo/iioGoKpBqqZgUl9I6d+/f73rjo+PB64PFQokKz2eJqs/qm677bZ6yzRlUDJ/VNb2d7XyvqzvdTF/NH/55Zd89tlngO8HWM+ePQkLC8PpdDJ58mTOnTtH9+7d/Z0uI0aMYOjQobz33nvceeedbNy4kY8++qhBwyWk8RQcb1BERAQDBw5k27Zt/vkdO3bgcDho27YtAwcOJDs7m06dOrFv3z46derEZ599xuXLl/1Hi8H14GiOazJv7XZ7tV3S5j9b1Z5Lu93OrbfeCvh+tVVkflEEqqeqLh07dgQaNj7T6q/wqj1kgThCvSE9HA0JgtC4X/iB0NheKivPM98PgRh715Av3EBpSLvN99vFixfrLTthwoR6yzTkh0/37t0BSElJsfwcq+oLHA0Jq+Zr1KlTp3rL1vTaV11XTW2rb1zt7bffXu+6zdDakD0Qffr0qbbMymeAWcb80V/xM8bq501dYxxN3bp1s1RXUzA7Pepifg+ZPe1VXzvz/WD21ttsNiIjI3E4HJSXlxMXF+c/K0lsbCzFxcWUlpbys5/9jDVr1vjfF+Z7pjUP32pOOh1PAJin43njjTe4ePFipdPx9O7dm/3795OSksKJEyeIjY2loKCAqKgoSkpKKv3ybtOmDV6vV6e1CGHm6SVEqrLyf2u32y0fQNaQsqGgNX9utW/f3tKPi0Bpqte6IfU6nc56h9W0a9eOS5cu0atXL44cOVJj/eHh4bhcLv/ejri4OBwOB2VlZZw/f57w8HDuu+8+du3axalTp7h27RozZsygb9++pKWlcfbsWV599VXy8/M5ePBgtT10EngKjgGycuVKli1bRn5+PgkJCXi9Xs6ePYvdbsfr9bbaD1QREZGmNGPGDJ5++mnLZwuQG6PgKCIiIiKWaIyjiIiIiFii4CgiIiIilig4ioiIiIglCo4iIiIiYomCo4iIiIhYouAoIiIiIpYoOIqIiIiIJQqOItJi7dixA5vN1qxX9RARacl0AnARaTGGDx/OT3/6U1599VXAdy3zCxcukJCQEJBra4uItHY1XzFeRKQFiIiIIDExMdjNEBFpMbSrWkRahIceeoidO3fy2muvYbPZsNlsrF27ttKu6rVr19K+fXv++c9/kpqaSmRkJPfeey+XL19m3bp1dOvWjdjYWGbNmsW1a9f8dZeVlfH444/TuXNnoqKiGDRoEDt27AjOhoqIBJF6HEWkRXjttdc4cuQIffr04dlnnwXg4MGD1cpduXKF5cuXs3HjRoqKirjnnnu45557aN++PR999BHHjh3jN7/5DUOHDuX+++8H4OGHH+a7775j48aNJCUlsWnTJjIyMti/fz89e/Zs1u0UEQkmBUcRaRFcLhcRERFERkb6d09/9dVX1cp5vV5ef/11evToAcC9997LW2+9xenTp2nXrh1paWmMGDGC7du3c//99/Ptt9/y7rvvcvLkSZKSkgCYP38+WVlZrFmzhsWLFzffRoqIBJmCo4i0KpGRkf7QCJCQkEC3bt1o165dpWVnzpwB4Msvv8QwDHr16lWpntLSUjp06NA8jRYRCREKjiLSqrRp06bSfZvNVuOy8vJyAMrLy7Hb7eTk5GC32yuVqxg2RURaAwVHEWkxIiIiKh3UEggDBgzg2rVrnDlzhjvuuCOgdYuI3Gx0VLWItBjdunVjz549fPfdd5w7d87fa3gjevXqxaRJk3jwwQf54IMPyMvL44svvmDp0qV89NFHAWi1iMjNQ8FRRFqM+fPnY7fbSUtLo2PHjnz//fcBqXfNmjU8+OCDzJs3j9TUVCZMmMCePXtISUkJSP0iIjcLXTlGRERERCxRj6OIiIiIWKLgKCIiIiKWKDiKiIiIiCUKjiIiIiJiiYKjiIiIiFii4CgiIiIilig4ioiIiIglCo4iIiIiYomCo4iIiIhYouAoIiIiIpYoOIqIiIiIJQqOIiIiImLJ/wei6W2Lk657agAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["quotation = easyquotation.use(\"timekline\")\n", "querycode = '600519'\n", "Res_tx_mindata = quotation.real([querycode], prefix=True)\n", "querykey = 'sh' + querycode + '.js'\n", "mindata = pd.DataFrame(Res_tx_mindata[querykey]['time_data'], columns=['time', 'close', 'volume'])\n", "mindata['close'] = mindata['close'].map(lambda x: float(x))\n", "mindata['volume'] = mindata['volume'].map(lambda x: int(x))\n", "fig, ax = plt.subplots(1, 1)\n", "ax_sub = ax.twinx()  # 共享x轴，生成次坐标轴\n", "l1, = ax.plot(mindata.time, mindata.close, 'r-', label='price')\n", "l2, = ax_sub.plot(mindata.time, mindata.volume, 'b-', label='volume')\n", "plt.legend(handles=[l1, l2], labels=['price', 'volume'], loc=0)\n", "ax.set_ylabel('price')\n", "ax_sub.set_ylabel('volume')\n", "ax.set_xlabel('time')\n", "ax.set_title('stock realtime')\n", "x_major_locator = MultipleLocator(10)\n", "ax.xaxis.set_major_locator(x_major_locator)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Pytdx\n", "https://rainx.gitbooks.io/pytdx/content/pytdx_hq.html"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from pytdx.exhq import *\n", "from pytdx.hq import *\n", "api_hq = TdxHq_API()\n", "api_hq = api_hq.connect('**************', 7709)\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 582), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 1357), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 106), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 281), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 286), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 99), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 342), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 90), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 351), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 310), ('buyorsell', 1)]), OrderedDict([('time', '14:29'), ('price', 7.91), ('vol', 63), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 34), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 84), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 238), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 90), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 147), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 452), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 119), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 175), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 97), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 194), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 83), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 235), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 80), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 87), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 175), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 58), ('buyorsell', 1)]), OrderedDict([('time', '14:30'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 211), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 143), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 81), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 143), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 286), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 142), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 195), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 58), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 505), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 90), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 181), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 155), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 130), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 29), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 29), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 19), ('buyorsell', 1)]), OrderedDict([('time', '14:31'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 122), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 210), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 71), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 37), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 104), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 103), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 52), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 104), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 40), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 224), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 28), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 6), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 491), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 197), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 110), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 70), ('buyorsell', 1)]), OrderedDict([('time', '14:32'), ('price', 7.91), ('vol', 143), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 828), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 294), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 85), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 165), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 407), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 95), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 26), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 26), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 128), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 39), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 1486), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 83), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 107), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 219), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 57), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 55), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 6), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 47), ('buyorsell', 1)]), OrderedDict([('time', '14:33'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 11), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 220), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 72), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 38), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 32), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 50), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 254), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 70), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 204), ('buyorsell', 1)]), OrderedDict([('time', '14:34'), ('price', 7.91), ('vol', 110), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 162), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 29), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 361), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 221), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 247), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 52), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 51), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 45), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 220), ('buyorsell', 1)]), OrderedDict([('time', '14:35'), ('price', 7.91), ('vol', 65), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 21), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 79), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 218), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 37), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 94), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 237), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 43), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 64), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 40), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 122), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 82), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 54), ('buyorsell', 1)]), OrderedDict([('time', '14:36'), ('price', 7.91), ('vol', 341), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 138), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 75), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 54), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 123), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 101), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 23), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 179), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 67), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 8), ('buyorsell', 1)]), OrderedDict([('time', '14:37'), ('price', 7.91), ('vol', 53), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 50), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 49), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 43), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 54), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 62), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 16), ('buyorsell', 1)]), OrderedDict([('time', '14:38'), ('price', 7.91), ('vol', 73), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 24), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 37), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 14), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 443), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 58), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 19), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 26), ('buyorsell', 1)]), OrderedDict([('time', '14:39'), ('price', 7.91), ('vol', 74), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 201), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 101), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 502), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 70), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 96), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 320), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 37), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 365), ('buyorsell', 1)]), OrderedDict([('time', '14:40'), ('price', 7.91), ('vol', 96), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 715), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 18), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 16), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 74), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 79), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 173), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 154), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 43), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 16), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:41'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 31), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 120), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 55), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 50), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 117), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 67), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:42'), ('price', 7.91), ('vol', 249), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 8), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 166), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 23), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 31), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 49), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 153), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 78), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 23), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 43), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 40), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 8), ('buyorsell', 1)]), OrderedDict([('time', '14:43'), ('price', 7.91), ('vol', 450), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 43), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 93), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 11), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 19), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 24), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 49), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 113), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 214), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 61), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 53), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:44'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 229), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 15), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:45'), ('price', 7.91), ('vol', 9), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 77), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 16), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 8), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 526), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 126), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 18), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 26), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 11), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 24), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 273), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 204), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:46'), ('price', 7.91), ('vol', 40), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 51), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 56), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 38), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 44), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 21), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 248), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 66), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 183), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 36), ('buyorsell', 1)]), OrderedDict([('time', '14:47'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 48), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 111), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 56), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 27), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 18), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 31), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 101), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 38), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 74), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 161), ('buyorsell', 1)]), OrderedDict([('time', '14:48'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 50), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 40), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 493), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 531), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 38), ('buyorsell', 1)]), OrderedDict([('time', '14:49'), ('price', 7.91), ('vol', 194), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 19), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 24), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 18), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 19), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 32), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 28), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 1), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 84), ('buyorsell', 1)]), OrderedDict([('time', '14:50'), ('price', 7.91), ('vol', 233), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 8), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 53), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 143), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 75), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 110), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 48), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 29), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 11), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 14), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:51'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 1631), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 100), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 200), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 68), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 631), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 45), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 9), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 1392), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 200), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 59), ('buyorsell', 1)]), OrderedDict([('time', '14:52'), ('price', 7.91), ('vol', 919), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 668), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 48), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 175), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 2061), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 52), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 35), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 33), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 112), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 8), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 108), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 59), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 28), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 61), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 301), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:53'), ('price', 7.91), ('vol', 2), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 26), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 34), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 3), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 39), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 4), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 40), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 322), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 30), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 67), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 25), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 128), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 16), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 106), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 84), ('buyorsell', 1)]), OrderedDict([('time', '14:54'), ('price', 7.91), ('vol', 75), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 23), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 131), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 13), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 42), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 31), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 20), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 7), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 38), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 701), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 10), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 60), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:55'), ('price', 7.91), ('vol', 55), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 17), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 651), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 68), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 76), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 12), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 81), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 42), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 369), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 38), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 228), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 42), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 6), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 22), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 26), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 5), ('buyorsell', 1)]), OrderedDict([('time', '14:56'), ('price', 7.91), ('vol', 150), ('buyorsell', 1)]), OrderedDict([('time', '14:57'), ('price', 7.91), ('vol', 1030), ('buyorsell', 1)]), OrderedDict([('time', '15:00'), ('price', 7.91), ('vol', 4195), ('buyorsell', 2)])]\n"]}], "source": ["print(api_hq.get_history_transaction_data(TDXParams.MARKET_SZ, \"002560\", 0, 500, 20220916))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       time  price  vol  buyorsell\n", "0     14:09   7.73  148          0\n", "1     14:09   7.72  767          1\n", "2     14:09   7.72   90          0\n", "3     14:09   7.72   76          0\n", "4     14:10   7.72   54          0\n", "...     ...    ...  ...        ...\n", "3771  09:40   7.59  415          1\n", "3772  09:40   7.60  197          0\n", "3773  09:41   7.59  699          1\n", "3774  09:41   7.60  193          0\n", "3775  09:41   7.61  584          0\n", "\n", "[3776 rows x 4 columns]\n"]}], "source": ["# 查询完整历史分时数据\n", "# 在前面的示例中，我们查询了 002560 这个股票在 2022-09-16 的最后500条数据。\n", "# 如果我们想要查询当天的全部数据，需要不断改变start和limit，\n", "# 即api_hq.get_history_transaction_data的第三个参数和第四个参数。\n", "def get_all_trans_data(api, code, date):\n", "    start = 0\n", "    data = []\n", "    while True:\n", "        part = api.get_history_transaction_data(TDXParams.MARKET_SZ, code, start, 888, int(date))\n", "        data.extend(part)\n", "        if len(part) < 888:\n", "            break\n", "        start += 888\n", "    return data\n", "data = get_all_trans_data(api_hq, \"002560\", 20220916)\n", "print(pd.DataFrame(data))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}