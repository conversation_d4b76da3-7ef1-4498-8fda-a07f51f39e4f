"""
测试ONNX模型导出和推理

该脚本用于测试导出的ONNX模型在Python中的推理结果，并打印详细的数值信息，
以便与C++实现进行比较。
"""

import os
import sys
import argparse
import numpy as np
import torch
import onnxruntime as ort
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和tokenizer
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT

def test_model_with_pytorch(model_path, seq_len=30, vocab_size=1024, code_size=100, use_time_features=True):
    """
    使用PyTorch测试模型
    
    Args:
        model_path: 模型路径
        seq_len: 序列长度
        vocab_size: 词汇表大小
        code_size: 证券代码数量
        use_time_features: 是否使用时间特征
    """
    print("\n=== 使用PyTorch测试模型 ===")
    
    # 加载模型
    device = torch.device('cpu')
    model = CandlestickVQGPT.from_pretrained(model_path, device=device)
    model.eval()
    
    # 准备输入数据
    input_tokens = torch.randint(0, vocab_size, (1, seq_len), dtype=torch.long, device=device)
    code_ids = torch.tensor([0], dtype=torch.long, device=device)
    
    if use_time_features:
        time_features = torch.rand(1, seq_len, 8, device=device)
        # 前向传播
        with torch.no_grad():
            logits, _ = model(input_tokens, code_ids, time_features)
    else:
        # 前向传播
        with torch.no_grad():
            logits, _ = model(input_tokens, code_ids)
    
    # 打印结果
    print(f"PyTorch输出形状: {logits.shape}")
    print(f"PyTorch输出数据类型: {logits.dtype}")
    print(f"PyTorch输出最小值: {logits.min().item()}")
    print(f"PyTorch输出最大值: {logits.max().item()}")
    print(f"PyTorch输出平均值: {logits.mean().item()}")
    print(f"PyTorch输出前5个值: {logits[0, 0, :5]}")
    
    return logits

def test_model_with_onnx(onnx_path, seq_len=30, vocab_size=1024, code_size=100, use_time_features=True):
    """
    使用ONNX Runtime测试模型
    
    Args:
        onnx_path: ONNX模型路径
        seq_len: 序列长度
        vocab_size: 词汇表大小
        code_size: 证券代码数量
        use_time_features: 是否使用时间特征
    """
    print("\n=== 使用ONNX Runtime测试模型 ===")
    
    # 创建ONNX Runtime会话
    providers = ['CPUExecutionProvider']
    session_options = ort.SessionOptions()
    session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    session = ort.InferenceSession(onnx_path, sess_options=session_options, providers=providers)
    
    # 获取输入名称
    input_names = [input.name for input in session.get_inputs()]
    print(f"ONNX模型输入名称: {input_names}")
    
    # 获取输出名称
    output_names = [output.name for output in session.get_outputs()]
    print(f"ONNX模型输出名称: {output_names}")
    
    # 准备输入数据
    inputs = {}
    inputs['input_tokens'] = np.random.randint(0, vocab_size, (1, seq_len), dtype=np.int32)
    inputs['code_ids'] = np.array([0], dtype=np.int32)
    
    if use_time_features and 'time_features' in input_names:
        inputs['time_features'] = np.random.rand(1, seq_len, 8).astype(np.float32)
    
    # 打印输入数据类型
    for name, value in inputs.items():
        print(f"输入 {name} 形状: {value.shape}, 类型: {value.dtype}")
    
    # 运行推理
    outputs = session.run(None, inputs)
    
    # 打印结果
    logits = outputs[0]
    print(f"ONNX输出形状: {logits.shape}")
    print(f"ONNX输出数据类型: {logits.dtype}")
    print(f"ONNX输出最小值: {np.min(logits)}")
    print(f"ONNX输出最大值: {np.max(logits)}")
    print(f"ONNX输出平均值: {np.mean(logits)}")
    print(f"ONNX输出前5个值: {logits[0, 0, :5]}")
    
    return logits

def export_model_to_onnx(model_path, output_path, seq_len=30, use_time_features=True):
    """
    导出模型为ONNX格式
    
    Args:
        model_path: 模型路径
        output_path: 输出路径
        seq_len: 序列长度
        use_time_features: 是否使用时间特征
    """
    print("\n=== 导出模型为ONNX格式 ===")
    
    # 加载模型
    device = torch.device('cpu')
    model = CandlestickVQGPT.from_pretrained(model_path, device=device)
    model.eval()
    
    # 设置时间特征形状
    time_shape = None
    if use_time_features:
        time_shape = (1, seq_len, 8)
    
    # 导出模型
    onnx_path = model.to_onnx(
        output_path,
        input_shape=(1, seq_len),
        code_shape=(1,),
        time_shape=time_shape
    )
    
    return onnx_path

def main():
    parser = argparse.ArgumentParser(description='测试ONNX模型导出和推理')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--output_path', type=str, required=True, help='ONNX模型输出路径')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    args = parser.parse_args()
    
    # 导出模型为ONNX格式
    onnx_path = export_model_to_onnx(
        args.model_path, 
        args.output_path, 
        seq_len=args.seq_len, 
        use_time_features=args.use_time_features
    )
    
    # 使用PyTorch测试模型
    pytorch_logits = test_model_with_pytorch(
        args.model_path, 
        seq_len=args.seq_len, 
        use_time_features=args.use_time_features
    )
    
    # 使用ONNX Runtime测试模型
    onnx_logits = test_model_with_onnx(
        onnx_path, 
        seq_len=args.seq_len, 
        use_time_features=args.use_time_features
    )
    
    # 比较PyTorch和ONNX的输出
    print("\n=== 比较PyTorch和ONNX的输出 ===")
    pytorch_np = pytorch_logits.cpu().numpy()
    max_diff = np.max(np.abs(pytorch_np - onnx_logits))
    print(f"最大绝对误差: {max_diff}")
    
    if max_diff < 1e-4:
        print("PyTorch和ONNX的输出一致，模型导出成功！")
    else:
        print("警告: PyTorch和ONNX的输出存在差异，请检查模型导出过程。")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
