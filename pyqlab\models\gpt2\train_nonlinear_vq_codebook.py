"""
训练VQ-VAE码本权重

这个脚本用于训练VQ-VAE模型，并保存其码本权重，以便在NonlinearVQTokenizer中使用。
"""

import os
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from tqdm import tqdm
import glob
from datetime import datetime

# 导入NonlinearVQTokenizer中的向量化函数
from pyqlab.models.base.nonlinear_vq_tokenizer import NonlinearVQTokenizer
from pyqlab.models.base.nonlinear_tokenizer import (
    SigmoidMapping, SquareRootMapping, LogarithmicMapping
)


# 定义K线数据集
class CandlestickDataset(Dataset):
    def __init__(self, data_files, num_embeddings=512, atr_window=100, ma_volume_period=20,
                 mapping_functions=None, include_volume=False,
                 min_samples=1000, max_samples=100000):
        """
        初始化K线数据集

        Args:
            data_files: K线数据文件列表
            num_embeddings: 码本大小
            atr_window: ATR计算窗口
            ma_volume_period: 成交量移动平均周期
            mapping_functions: 特征映射函数
            include_volume: 是否包含交易量
            min_samples: 最小样本数
            max_samples: 最大样本数
        """
        self.data_files = data_files
        self.atr_window = atr_window
        self.ma_volume_period = ma_volume_period
        self.mapping_functions = mapping_functions
        self.include_volume = include_volume
        self.min_samples = min_samples
        self.max_samples = max_samples

        # 创建临时tokenizer用于向量化
        self.temp_tokenizer = NonlinearVQTokenizer(
            num_embeddings=num_embeddings,  # 这里的值不重要，因为我们只使用向量化功能
            embedding_dim=5 if include_volume else 4,
            atr_window=atr_window,
            ma_volume_period=ma_volume_period,
            mapping_functions=mapping_functions,
            include_volume=include_volume
        )

        # 加载并预处理数据
        self.vectors = self._load_and_preprocess_data()
        print(f"加载了 {len(self.vectors)} 个K线向量")

    def _process_single_df(self, df, all_vectors):
        """处理单个DataFrame"""
        # 确保列名小写
        df.columns = [col.lower() for col in df.columns]

        # 确保必要的列存在
        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            print(f"警告: DataFrame缺少必要的OHLC列，跳过")
            return

        if self.include_volume and 'volume' not in df.columns:
            print(f"警告: DataFrame缺少volume列，跳过")
            return

        # 预处理数据
        processed_df = self.temp_tokenizer._preprocess_df(df)

        # 提取向量
        vectors = []
        for i in range(len(processed_df)):
            row = processed_df.iloc[i]
            if pd.isna(row['atr']) or pd.isna(row['ma_volume']) or pd.isna(row['prev_close']):
                print(f"警告: 缺少必要的数据用于向量化: atr={row['atr']}, ma_volume={row['ma_volume']}, prev_close={row['prev_close']}")
                continue

            vector = self.temp_tokenizer.candlestick_to_vector(
                row,
                row['prev_close'],
                row['ma_volume'],
                row['atr']
            )

            # 检查向量是否有效
            if not np.all(vector == 0) and not np.any(np.isnan(vector)):
                vectors.append(vector)
            else:
                print(f"警告: 无效的向量: {vector}")

        all_vectors.extend(vectors)
        print(f"从DataFrame中提取了 {len(vectors)} / {len(processed_df)} 个有效向量，总计 {len(all_vectors)} 个向量")

    def _load_and_preprocess_data(self):
        """加载并预处理K线数据"""
        all_vectors = []

        for file_path in tqdm(self.data_files, desc="加载数据文件"):
            try:
                # 加载K线数据
                print(f"处理数据文件: {file_path}")
                try:
                    # 尝试读取Parquet文件
                    if file_path.endswith('.parquet'):
                        df = pd.read_parquet(file_path)
                        # 如果有code列，按code分组处理
                        if 'code' in df.columns:
                            for code, group_df in df.groupby('code'):
                                print(f"处理代码: {code} {group_df.shape}")
                                group_df = group_df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)
                                self._process_single_df(group_df, all_vectors)
                        else:
                            # 没有code列，直接处理整个DataFrame
                            df = df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)
                            self._process_single_df(df, all_vectors)
                    # 尝试读取CSV文件
                    elif file_path.endswith('.csv'):
                        df = pd.read_csv(file_path)
                        # 确保datetime列存在
                        if 'datetime' not in df.columns and 'date' in df.columns:
                            df['datetime'] = df['date']
                        # 如果有code列，按code分组处理
                        if 'code' in df.columns:
                            for code, group_df in df.groupby('code'):
                                print(f"处理代码: {code} {group_df.shape}")
                                group_df = group_df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)
                                self._process_single_df(group_df, all_vectors)
                        else:
                            # 没有code列，直接处理整个DataFrame
                            df = df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)
                            self._process_single_df(df, all_vectors)
                    else:
                        print(f"不支持的文件格式: {file_path}")
                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 时出错: {e}")
                    continue

                # 如果已经收集了足够的样本，就停止
                if len(all_vectors) >= self.max_samples:
                    all_vectors = all_vectors[:self.max_samples]
                    break

            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")

        # 确保有足够的样本
        if len(all_vectors) < self.min_samples:
            print(f"警告: 只收集到 {len(all_vectors)} 个样本，少于最小要求 {self.min_samples}")

        return np.array(all_vectors, dtype=np.float32)

    def __len__(self):
        return len(self.vectors)

    def __getitem__(self, idx):
        return self.vectors[idx]


# 定义VQ-VAE模型
class VQVAE(nn.Module):
    def __init__(self, input_dim=4, hidden_dim=64, num_embeddings=512, embedding_dim=4,
                 commitment_cost=0.25, decay=0.99):
        """
        初始化VQ-VAE模型

        Args:
            input_dim: 输入向量维度
            hidden_dim: 隐藏层维度
            num_embeddings: 码本大小
            embedding_dim: 码向量维度
            commitment_cost: 承诺损失权重
            decay: EMA更新率
        """
        super(VQVAE, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, embedding_dim)
        )

        # 向量量化层
        self.vq_layer = VectorQuantizer(num_embeddings, embedding_dim, commitment_cost, decay)

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, input_dim)
        )

    def forward(self, x):
        z_e = self.encoder(x)
        z_q, indices, vq_loss = self.vq_layer(z_e)
        x_recon = self.decoder(z_q)

        return x_recon, indices, vq_loss

    def get_codebook(self):
        """获取码本权重"""
        return self.vq_layer.embeddings.weight.data


# 定义向量量化层
class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25, decay=0.99):
        super(VectorQuantizer, self).__init__()

        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost
        self.decay = decay

        # 初始化码本
        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        self.embeddings.weight.data.uniform_(-1.0 / num_embeddings, 1.0 / num_embeddings)

        # 使用EMA更新码本
        self.register_buffer('ema_cluster_size', torch.zeros(num_embeddings))
        self.register_buffer('ema_w', self.embeddings.weight.data.clone())

    def forward(self, x):
        # 计算输入向量与码本中所有码向量的距离
        # x: [batch_size, embedding_dim]
        # self.embeddings.weight: [num_embeddings, embedding_dim]
        distances = torch.sum(x**2, dim=1, keepdim=True) - 2 * torch.matmul(x, self.embeddings.weight.t()) + torch.sum(self.embeddings.weight**2, dim=1)

        # 找到最近的码向量
        encoding_indices = torch.argmin(distances, dim=1)

        # 获取量化后的向量
        z_q = self.embeddings(encoding_indices)

        # 计算VQ损失
        vq_loss = F.mse_loss(z_q, x.detach()) + self.commitment_cost * F.mse_loss(x, z_q.detach())

        # 使用EMA更新码本
        if self.training:
            # 计算每个码向量的使用频率
            encodings = F.one_hot(encoding_indices, self.num_embeddings).float()
            self.ema_cluster_size = self.ema_cluster_size * self.decay + (1 - self.decay) * torch.sum(encodings, dim=0)

            # 计算新的码向量
            dw = torch.matmul(encodings.t(), x)
            self.ema_w = self.ema_w * self.decay + (1 - self.decay) * dw

            # 更新码本
            n = torch.sum(self.ema_cluster_size)
            cluster_size = (self.ema_cluster_size + 1e-5) / (n + self.num_embeddings * 1e-5) * n
            self.embeddings.weight.data = self.ema_w / cluster_size.unsqueeze(1)

        # 使用直通估计器
        z_q = x + (z_q - x).detach()

        return z_q, encoding_indices, vq_loss


# 训练VQ-VAE模型
def train_vqvae(model, dataloader, optimizer, num_epochs, device, save_dir, save_interval=5):
    """
    训练VQ-VAE模型

    Args:
        model: VQ-VAE模型
        dataloader: 数据加载器
        optimizer: 优化器
        num_epochs: 训练轮数
        device: 训练设备
        save_dir: 保存目录
        save_interval: 保存间隔
    """
    model.train()

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 记录训练损失
    train_losses = []
    recon_losses = []
    vq_losses = []

    for epoch in range(num_epochs):
        epoch_loss = 0
        epoch_recon_loss = 0
        epoch_vq_loss = 0

        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for batch in progress_bar:
            optimizer.zero_grad()

            # 将数据移动到设备
            batch = batch.to(device)

            # 前向传播
            recon_batch, _, vq_loss = model(batch)

            # 计算重建损失
            recon_loss = F.mse_loss(recon_batch, batch)

            # 计算总损失
            loss = recon_loss + vq_loss

            # 反向传播
            loss.backward()
            optimizer.step()

            # 更新进度条
            epoch_loss += loss.item()
            epoch_recon_loss += recon_loss.item()
            epoch_vq_loss += vq_loss.item()
            progress_bar.set_postfix({
                'loss': epoch_loss / (progress_bar.n + 1),
                'recon_loss': epoch_recon_loss / (progress_bar.n + 1),
                'vq_loss': epoch_vq_loss / (progress_bar.n + 1)
            })

        # 记录训练损失
        avg_loss = epoch_loss / len(dataloader)
        avg_recon_loss = epoch_recon_loss / len(dataloader)
        avg_vq_loss = epoch_vq_loss / len(dataloader)
        train_losses.append(avg_loss)
        recon_losses.append(avg_recon_loss)
        vq_losses.append(avg_vq_loss)

        print(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.6f}, Recon Loss: {avg_recon_loss:.6f}, VQ Loss: {avg_vq_loss:.6f}")

        # 保存模型和码本
        if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
            # 保存模型
            model_path = os.path.join(save_dir, f"vqvae_model_epoch_{epoch+1}.pt")
            torch.save(model.state_dict(), model_path)
            print(f"模型已保存到 {model_path}")

            # 保存码本权重
            codebook_path = os.path.join(save_dir, f"vqvae_codebook_epoch_{epoch+1}.pt")
            torch.save(model.get_codebook(), codebook_path)
            print(f"码本权重已保存到 {codebook_path}")

    # 绘制训练损失曲线
    plt.figure(figsize=(12, 4))
    plt.subplot(1, 3, 1)
    plt.plot(train_losses)
    plt.title('Total Loss')
    plt.subplot(1, 3, 2)
    plt.plot(recon_losses)
    plt.title('Reconstruction Loss')
    plt.subplot(1, 3, 3)
    plt.plot(vq_losses)
    plt.title('VQ Loss')
    plt.tight_layout()

    # 保存损失曲线
    loss_curve_path = os.path.join(save_dir, "loss_curve.png")
    plt.savefig(loss_curve_path)
    print(f"损失曲线已保存到 {loss_curve_path}")

    return train_losses, recon_losses, vq_losses


# 可视化码本
def visualize_codebook(model, save_path=None):
    """
    可视化码本

    Args:
        model: VQ-VAE模型
        save_path: 保存路径
    """
    # 获取码本权重
    codebook = model.get_codebook().cpu().numpy()

    # 计算码本的统计信息
    mean = np.mean(codebook, axis=0)
    std = np.std(codebook, axis=0)
    min_val = np.min(codebook, axis=0)
    max_val = np.max(codebook, axis=0)

    # 绘制码本分布
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))

    # 绘制码本向量的分布
    for i in range(codebook.shape[1]):
        axes[0, 0].hist(codebook[:, i], bins=50, alpha=0.5, label=f'Dim {i+1}')
    axes[0, 0].set_title('Codebook Vector Distribution')
    axes[0, 0].legend()

    # 绘制码本向量的均值和标准差
    x = np.arange(codebook.shape[1])
    axes[0, 1].bar(x, mean, yerr=std, capsize=5)
    axes[0, 1].set_title('Mean and Std of Codebook Vectors')
    axes[0, 1].set_xticks(x)
    axes[0, 1].set_xticklabels([f'Dim {i+1}' for i in x])

    # 绘制码本向量的最大值和最小值
    axes[1, 0].bar(x, max_val, label='Max')
    axes[1, 0].bar(x, min_val, label='Min')
    axes[1, 0].set_title('Min and Max of Codebook Vectors')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels([f'Dim {i+1}' for i in x])
    axes[1, 0].legend()

    # 绘制码本向量的2D投影（如果维度大于1）
    if codebook.shape[1] > 1:
        from sklearn.decomposition import PCA
        pca = PCA(n_components=2)
        codebook_2d = pca.fit_transform(codebook)
        axes[1, 1].scatter(codebook_2d[:, 0], codebook_2d[:, 1], alpha=0.5)
        axes[1, 1].set_title('2D PCA Projection of Codebook Vectors')
    else:
        axes[1, 1].text(0.5, 0.5, 'Dimension < 2, cannot project', ha='center', va='center')

    plt.tight_layout()

    # 保存图像
    if save_path:
        plt.savefig(save_path)
        print(f"码本可视化已保存到 {save_path}")

    return fig


# 主函数
def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='训练VQ-VAE码本权重')
    parser.add_argument('--data_dir', type=str, required=True, help='K线数据目录')
    parser.add_argument('--market', type=str, help='市场 (可选，用于过滤文件)')
    parser.add_argument('--block_name', type=str, help='板块名称 (可选，用于过滤文件)')
    parser.add_argument('--period', type=str, help='周期 (可选，用于过滤文件)')
    parser.add_argument('--save_dir', type=str, default='models/vqvae', help='保存目录')
    parser.add_argument('--output_name', type=str, help='输出文件名前缀 (可选)')
    parser.add_argument('--num_epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=128, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=1e-3, help='学习率')
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=4, help='码向量维度')
    parser.add_argument('--hidden_dim', type=int, default=64, help='隐藏层维度')
    parser.add_argument('--atr_window', type=int, default=100, help='ATR计算窗口')
    parser.add_argument('--ma_volume_period', type=int, default=100, help='成交量移动平均周期')
    parser.add_argument('--include_volume', action='store_true', help='是否包含交易量')
    parser.add_argument('--min_samples', type=int, default=1000, help='最小样本数')
    parser.add_argument('--max_samples', type=int, default=1000000, help='最大样本数')
    parser.add_argument('--save_interval', type=int, default=5, help='保存间隔')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='训练设备')
    args = parser.parse_args()

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = os.path.join(args.save_dir, f"vqvae_{timestamp}")
    os.makedirs(save_dir, exist_ok=True)

    # 查找K线数据文件
    parquet_files = glob.glob(os.path.join(args.data_dir, "*.parquet"))
    csv_files = glob.glob(os.path.join(args.data_dir, "*.csv"))
    data_files = parquet_files + csv_files

    if not data_files:
        print(f"错误: 在目录 {args.data_dir} 中未找到parquet或csv文件")
        return

    # 如果提供了市场、板块和周期参数，则进行过滤
    if hasattr(args, 'market') and args.market and hasattr(args, 'block_name') and args.block_name and hasattr(args, 'period') and args.period:
        data_files = [f for f in data_files if args.market in f and args.block_name in f and args.period in f]

    print(f"找到 {len(data_files)} 个数据文件")

    # 创建映射函数
    mapping_functions = {
        'change': SigmoidMapping((-5, 5), (-1, 1)),
        'entity': SigmoidMapping((-3, 3), (-1, 1)),
        'upline': SquareRootMapping((0, 3), (0, 1)),
        'downline': SquareRootMapping((0, 3), (0, 1))
    }

    if args.include_volume:
        mapping_functions['volume'] = LogarithmicMapping((0.1, 10), (-1, 1))

    # 创建数据集
    dataset = CandlestickDataset(
        data_files=data_files,
        num_embeddings=args.num_embeddings,
        atr_window=args.atr_window,
        ma_volume_period=args.ma_volume_period,
        mapping_functions=mapping_functions,
        include_volume=args.include_volume,
        min_samples=args.min_samples,
        max_samples=args.max_samples
    )

    # 创建数据加载器
    dataloader = DataLoader(
        dataset=dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    # 创建模型
    input_dim = 5 if args.include_volume else 4
    model = VQVAE(
        input_dim=input_dim,
        hidden_dim=args.hidden_dim,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim
    )

    # 将模型移动到设备
    device = torch.device(args.device)
    model = model.to(device)

    # 创建优化器
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)

    # 训练模型
    _ = train_vqvae(
        model=model,
        dataloader=dataloader,
        optimizer=optimizer,
        num_epochs=args.num_epochs,
        device=device,
        save_dir=save_dir,
        save_interval=args.save_interval
    )

    # 可视化码本
    visualize_path = os.path.join(save_dir, "codebook_visualization.png")
    visualize_codebook(model, save_path=visualize_path)

    # 保存最终的码本权重
    output_prefix = ""
    if hasattr(args, 'output_name') and args.output_name:
        output_prefix = f"{args.output_name}"
    elif hasattr(args, 'market') and args.market and hasattr(args, 'block_name') and args.block_name and hasattr(args, 'period') and args.period:
        output_prefix = f"{args.market}_{args.block_name}_{args.period}"
    else:
        output_prefix = "final"

    final_codebook_path = os.path.join(save_dir, f"vqvae_codebook_{output_prefix}.pt")
    torch.save(model.get_codebook(), final_codebook_path)
    print(f"最终码本权重已保存到 {final_codebook_path}")

    print("训练完成!")


if __name__ == "__main__":
    main()
