# coding=utf-8


import qlib
from qlib.config import REG_CN
from qlib.utils import init_instance_by_config, flatten_dict
from qlib.workflow import R

from pyqlab.data.dataset.handler import DataHandlerAHF
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import ipywidgets as widgets
import copy
import lightgbm as lgb
from pprint import pprint

import warnings
warnings.filterwarnings("ignore")

provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
qlib.init(provider_uri=provider_uri, region=REG_CN)

###################################
# train model
###################################
SEL_LONG_FACTOR_NAMES = [ # Slow period factor
    # "MACD", "MACD_DIFF", "MACD_DEA", "MOM", "RSI",
    # "NEW_CHANGE_PERCENT", 
    # "LR_SLOPE_FAST",
    # "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    # "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    # "SQUEEZE_ZERO_BARS", 
    # "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    # "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    # "BAND_POSITION", "BAND_WIDTH",
    # "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    # "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    # "TREND_LEVEL"
]

SEL_SHORT_FACTOR_NAMES = [ # Fast period factor
    # "VOLUME", # 在RangeBar下，Volume是Bar的时长seconds
    "NEW_CHANGE_PERCENT",

    # "AROON_UP", "AROON_DOWN",
    "MACD", "MACD_DIFF", "MACD_DEA", "RSI",

    # "LONG_TERM_HIGH", "LONG_TERM_LOW", "SHORT_TERM_HIGH", "SHORT_TERM_LOW", 

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]

SEL_CONTEXT_FACTOR_NAMES = [
  "STDDEV_RNG", "SHORT_RANGE",
  "FAST_QH_RSI", "FAST_QH_ZSCORE", "FAST_QH_DIRECT",
  "FAST_QH_NATR", "FAST_QH_NATR_ZSCORE", "FAST_QH_NATR_DIRECT",
  "FAST_QH_MOM", "FAST_QH_MOM_ZSCORE", "FAST_QH_MOM_DIRECT",
  "DAYOFWEEK", "HOUR"
]


"""
根据当前测试的结果来看，主要有以下结论：
1.训练的数据越多越好，如何仅用2023年的数据来训练，那么测试的结果就不好
2.CONTEXT_FACTOR的因子对结果会造成噪点，最后不用
3.WIN_SIZE的大小对结果有影响，WIN_SIZE越大，结果越好 10 > 5
"""
IS_CLASS=False # 是否是分类问题
FACTOR_NUM=65
WIN_SIZE=0
FUT_CODES=MAIN_SEL_FUT_CODES
# FUT_CODES=SF_FUT_CODES
# FUT_CODES=MAIN_FUT_CODES
VERSION="V1"

pfs = {
    # ------------ main -----------
    # only_trading_code": False
    # only_trading_code": True
    # ============================
    # FUT_CODES=MAIN_SEL_FUT_CODES
    # ============================
    # =====================================================================
    '01HF_3Y_SEL': ['main.2023',],
    # '15HF_3Y_SEL': ['main.2022', 'main.2023',],
    # '05HF_4Y_SEL': ['main.2020', 'main.2021', 'main.2022', 'main.2023',],
    # '10HF_4Y_SEL': ['main.2020', 'main.2021', 'main.2022', 'main.2023',],
    # '15HF_4Y_SEL': ['main.2020', 'main.2021', 'main.2022', 'main.2023',],
    # -------------sf--------------
    # =============================
    # FUT_CODES=SF_FUT_CODES
    # =============================
    # '10HF_5Y_SF': ['sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],
    # '15HF_5Y_SF': ['sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],
    # '10HF_7Y_SF': ['sf.2017', 'sf.2018', 'sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],
    # '15HF_7Y_SF': ['sf.2017', 'sf.2018', 'sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],
}

filter_win = { # default=1
    '01HF_3Y_SEL': 1,
}

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": ['2020',],
    "kwargs": {
        "win": WIN_SIZE,                # 采样窗口,与下面的num_channel通道数保持一致
        "step": 1,                      # 采样步长，通常为1
        "filter_win": 1,                # 是否过滤掉特征数据
        "is_class": IS_CLASS,           # 是否是分类问题
        "is_filter_extreme": True,      # 是否过滤极端值
        "is_normal": False,             # 是否归一化
        "verbose": False,               # 是否打印日志
        "direct": "long",
        "model_name": "GBDTR",
        "model_name_suff": "",          # 模型名称后缀，通常与上面的win保持一致
        "model_path": "e:/lab/RoboQuant/pylab/model",
        "sel_lf_names": SEL_LONG_FACTOR_NAMES,
        "sel_sf_names": SEL_SHORT_FACTOR_NAMES,
        "sel_ct_names": SEL_CONTEXT_FACTOR_NAMES,
    },

    "data_loader": {
        "class": "AHFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "data_path": "e:/featdata",
            "train_codes": FUT_CODES,   # 选期货交易标的
        },
    },
}

dataset_config = {
    "class": "AHFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAHF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    },
}


task = {
    "model_gbdt_ls": {
        "class": "LGBModel",
        "module_path": "pyqlab.contrib.model.gbdt",
        "kwargs": {
            "boosting_type": "gbdt",
            "objective": "regression", # "binary" "mse" "regression",
            "metric": {'l2', 'auc'}, # "binary_logloss", "auc"
            "verbose": 1,
            "num_threads": 4,
            'n_estimators': 300,  # 你可以根据需要调整

            'num_leaves': 235,
            'learning_rate': 0.02750959712039905,
            'feature_fraction': 0.6910011892032855,
            'bagging_fraction': 0.9005179786303538,
            'bagging_freq': 2,
            'max_depth': 41,
        },
    },

    "model_gbdt_long": {
        "class": "LGBModel",
        "module_path": "pyqlab.contrib.model.gbdt",
        "kwargs": {
            "objective": "binary", # "mse",
            "metric": {"binary_logloss", "auc"}, # "binary_logloss", "auc"
            "verbose": 1,
            "num_threads": 4,
            'n_estimators': 300,  # 你可以根据需要调整

            'num_leaves': 235,
            'learning_rate': 0.02750959712039905,
            'feature_fraction': 0.6910011892032855,
            'bagging_fraction': 0.9005179786303538,
            'bagging_freq': 2,
            'max_depth': 41,
        },
    },

    "model_gbdt_short": {
        "class": "LGBModel",
        "module_path": "pyqlab.contrib.model.gbdt",
        "kwargs": {
            "objective": "binary", # "mse",
            "metric": {"binary_logloss", "auc"}, # "binary_logloss", # "auc"
            "verbose": 1,
            "num_threads": 4,
            'n_estimators': 300,  # 你可以根据需要调整

            'num_leaves': 235,
            'learning_rate': 0.02750959712039905,
            'feature_fraction': 0.6910011892032855,
            'bagging_fraction': 0.9005179786303538,
            'bagging_freq': 2,
            'max_depth': 41,
        },
    },

    "model_xgbdt": {
        "class": "XGBModel",
        "module_path": "pyqlab.contrib.model.xgboost",
        "kwargs": {
            "max_depth": 8,
            "learning_rate": 0.1,
            "n_estimators": 1000,
            "verbosity": 0,
            "silent": None,
            "objective": 'binary:logistic',
            "booster": 'gbtree',
            "n_jobs": -1,
            "nthread": None,
            "gamma": 0,
            "min_child_weight": 1,
            "max_delta_step": 0,
            "subsample": 0.7,
            "colsample_bytree": 1,
            "colsample_bylevel": 1,
            "colsample_bynode": 1,
            "reg_alpha": 0,
            "reg_lambda": 1,
            "scale_pos_weight": 1,
            "base_score": 0.5,
            "random_state": 0,
            "seed": None
        }
    },

    "model_tabnet": {
        "class": "TabnetModel",
        "module_path": "pyqlab.contrib.model.pytorch_tabnet",
        "kwargs": {
            "d_feat": 87,
            "out_dim": 1,
            "batch_size": 128,
            "n_epochs": 100,
            "early_stop": 30,
            "pretrain": False,
            "seed": 993,
            "GPU": 0,
        }
    },
}

def plt_show(df, title=""):
    wdt = widgets.Output()
    wdt.clear_output(wait=False)
    with wdt:
        ylim = [df.min().min(), df.quantile(0.95).max()]
        ylim[0] -= (ylim[1] - ylim[0]) * 0.05
        df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)
        plt.show()

def display_result(evals_result):
    for key, val in evals_result.items():
        if not isinstance(val, dict):
            plt_show(pd.DataFrame(evals_result), key)
            break
        else:
            plt_show(pd.DataFrame(val), key)

def get_win_size(pfs_name: str):
    if pfs_name[:2] == "05":
        return 5
    elif pfs_name[:2] == "10":
        return 10
    elif pfs_name[:2] == "15":
        return 15
    else:
        return 0
    
def get_filter_win(pfs_name: str):
    if pfs_name in filter_win.keys():
        return filter_win[pfs_name]
    else:
        return 1

def get_dataset(pfs_name: str):

    handler_class_config = copy.deepcopy(dataset_config["kwargs"]["handler"])
    if pfs_name:
        if pfs_name not in pfs.keys():
            print(f"pfs_name: {pfs_name} not in pfs.keys() or pfs_win.keys()")
            return
        data_handler_config["instruments"] = pfs[pfs_name]
        data_handler_config["kwargs"]["model_name_suff"] = pfs_name

    handler_class_config["kwargs"] = data_handler_config
    hd: DataHandlerAHF = init_instance_by_config(handler_class_config)
    dataset_config["kwargs"]["handler"] = hd
    dataset = init_instance_by_config(dataset_config)
    dataset.setup_data(handler_kwargs=data_handler_config)
    return dataset

def trainer(dataset, show=True, pfs_name=None, train_result={}):
    IS_CLASS=False # 是否是分类问题
    for direct in ["ls"]:
    # for direct in ["long", "short"]:
        # if reset_hp:
        #     hparam_df=pd.read_csv("./data/HP_MLP.csv")
        #     if not hparam_df.empty:
        #         hparam_df.set_index("model_name", inplace=True)
        #         task["model"]["kwargs"]["dropout"]=hparam_df.loc[f"CONV1D_{pfs_name}_{direct}", "dropout"]
        #         task["model"]["kwargs"]["lr"]=hparam_df.loc[f"MLP_{pfs_name}_{direct}", "lr"]
        #         print(f'Set dropout: {task["model"]["kwargs"]["dropout"]}')
        #         print(f'Set lr: {task["model"]["kwargs"]["lr"]}')


        # model initiaiton
        model = init_instance_by_config(task[f"model_gbdt_{direct}"])

        # start exp to train model
        with R.start(experiment_name="train_model"):
            R.log_params(**flatten_dict(task))
            path=data_handler_config["kwargs"]["model_path"]
            name=data_handler_config["kwargs"]["model_name"]
            
            if pfs_name:
                model_name=f"{name}_{pfs_name}_{direct}"
            else:
                model_name=f"{name}_{direct}"
            model_path=f"{path}/{model_name}.txt"

            result={}
            x_train, y_train, x_valid, y_valid = dataset.prepare(
                ["train", "valid"],
                col_set=["feature", "label", "encoded"],
                data_key=None,  # DataHandlerLP.DK_L,
                direct=direct,
                win=0,
                filter_win=1,
                model_name_suff=pfs_name,
                is_class=IS_CLASS,
            )

            train_data = lgb.Dataset(x_train, label=y_train)
            valid_data = lgb.Dataset(x_valid, label=y_valid, reference=train_data)

            best_train, best_valid = model.fit(
                train_data,
                valid_data,
                evals_result=result,
                save_path=model_path,
                # save_jit_script=False
            )
            train_result[model_name] = []
            train_result[model_name].append(best_train)
            train_result[model_name].append(best_valid)
            # R.save_objects(trained_model=model)
            # rid = R.get_recorder().id
            # preds = np.array(model.predict(dataset, segment="valid"))
            # valid_auc = roc_auc_score(y_score=preds[:,1], y_true=y_valid)
            # train_result[model_name].append(model.test(dataset, segment="valid"))
            if show:
                display_result(result)

if __name__ == "__main__":
    result={}
    dataset = None
    for pf in pfs.keys():
        if dataset is None or dataset.instruments != pfs[pf]:
            dataset = get_dataset(pf)
        trainer(dataset, show=False, pfs_name=pf, train_result=result)
    print("============train result=============")
    for key, item in result.items():
        print(key, item)
    print("============train result=============")
