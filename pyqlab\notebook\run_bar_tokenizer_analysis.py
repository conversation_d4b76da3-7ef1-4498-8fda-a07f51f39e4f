#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行BarTokenizer可视化分析的脚本
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入分析脚本
from bar_tokenizer_visualization import main

if __name__ == "__main__":
    print("开始运行BarTokenizer可视化分析...")
    print("=" * 50)
    
    # 可以在这里修改数据路径
    # 例如: os.environ['DATA_PATH'] = 'f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv'
    
    try:
        main()
        print("\n分析完成！请查看生成的图表。")
    except Exception as e:
        print(f"\n运行出错: {e}")
        print("请检查BarTokenizer模块是否正确安装和配置。")
