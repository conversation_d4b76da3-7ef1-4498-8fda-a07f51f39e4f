# tdx_to_tsdb.py - 将pytdx中的各类数据归档到TimeSeriesDB中
# https://pytdx-docs.readthedocs.io/zh-cn/latest/
import os
import sys
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pytdx.hq import TdxHq_API
from pytdx.params import TDXParams
from pytdx.exhq import TdxExHq_API
from pytdx.util.best_ip import select_best_ip
# from pytdx.config.hosts import hq_hosts, ex_hosts
from concurrent.futures import ThreadPoolExecutor, as_completed
from argparse import ArgumentParser

# 导入TimeSeriesDB
from pyqlab.data.tsdb import TimeSeriesDB

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("tdx_to_tsdb.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TDX2TSDB")

class TdxDataCollector:
    """通达信数据采集器，用于从pytdx获取数据并存储到TimeSeriesDB"""

    def __init__(self, tsdb_path, max_workers=5):
        """
        初始化数据采集器

        参数:
            tsdb_path: TimeSeriesDB数据库路径
            max_workers: 最大工作线程数
        """
        self.tsdb = TimeSeriesDB(tsdb_path)
        self.hq_api = TdxHq_API(heartbeat=True)
        self.exhq_api = TdxExHq_API(heartbeat=True)
        self.max_workers = max_workers
        self.connected = False
        self.ex_connected = False

        # 市场代码缓存
        self.market_codes = None
        self.instruments_cache = {}

    def connect(self):
        """
        连接到行情服务器
        """
        # 尝试连接到标准行情服务器
        hq_hosts = [
            ('*************', 7709),
            ('***************', 7709),
            ('*************', 7709),
            ('*************', 7709)
        ]

        for host, port in hq_hosts:
            try:
                logger.info(f"尝试连接到标准行情服务器 {host}:{port}")
                if self.hq_api.connect(host, port):
                    logger.info(f"成功连接到标准行情服务器 {host}:{port}")
                    self.connected = True
                    break
            except Exception as e:
                logger.error(f"连接标准行情服务器 {host}:{port} 失败: {str(e)}")

        # 尝试连接到扩展行情服务器
        # 扩展行情配置在 connect.cfg->DSH
        ex_hosts = [
            ('*************', 7722),
            # ('**********', 7722),
            # ('**************', 7722),
            # ('************', 7722)
        ]

        for host, port in ex_hosts:
            try:
                logger.info(f"尝试连接到扩展行情服务器 {host}:{port}")
                if self.exhq_api.connect(host, port):
                    logger.info(f"成功连接到扩展行情服务器 {host}:{port}")
                    self.ex_connected = True
                    break
            except Exception as e:
                logger.error(f"连接扩展行情服务器 {host}:{port} 失败: {str(e)}")

        return self.connected or self.ex_connected

    def disconnect(self):
        """断开与行情服务器的连接"""
        if self.connected:
            self.hq_api.disconnect()
            self.connected = False

        if self.ex_connected:
            self.exhq_api.disconnect()
            self.ex_connected = False

    def get_stock_list(self):
        """获取股票列表"""

        if not self.connected:
            logger.error("未连接到标准行情服务器，无法获取股票列表")
            return []

        stock_list = []

        # 获取深圳股票列表
        try:
            sz_count = self.hq_api.get_security_count(0)
            logger.info(f"深圳市场股票数量: {sz_count}")

            for i in range(0, sz_count, 1000):
                sz_stocks = self.hq_api.get_security_list(0, i)

                # 添加市场标识
                if sz_stocks is None:
                    logger.warning(f"获取深圳市场股票列表失败，可能是因为请求过于频繁")
                    break

                for stock in sz_stocks:
                    # 将 OrderedDict 转换为普通 dict 并添加 market 字段
                    stock_dict = dict(stock)
                    stock_dict['market'] = 0  # 深圳市场
                    stock_list.append(stock_dict)

                logger.info(f"已获取深圳市场股票 {i} - {i + len(sz_stocks)}")

                # 避免请求过于频繁
                time.sleep(0.5)
        except Exception as e:
            logger.error(f"获取深圳股票列表失败: {str(e)}")

        # 获取上海股票列表
        try:
            sh_count = self.hq_api.get_security_count(1)
            logger.info(f"上海市场股票数量: {sh_count}")

            for i in range(0, sh_count, 1000):
                sh_stocks = self.hq_api.get_security_list(1, i)

                # 添加市场标识
                if sh_stocks is None:
                    logger.warning(f"获取上海市场股票列表失败，可能是因为请求过于频繁")
                    break

                for stock in sh_stocks:
                    # 将 OrderedDict 转换为普通 dict 并添加 market 字段
                    stock_dict = dict(stock)
                    stock_dict['market'] = 1  # 上海市场
                    stock_list.append(stock_dict)

                logger.info(f"已获取上海市场股票 {i} - {i + len(sh_stocks)}")

                # 避免请求过于频繁
                time.sleep(0.5)
        except Exception as e:
            logger.error(f"获取上海股票列表失败: {str(e)}")

        logger.info(f"共获取 {len(stock_list)} 只股票")
        return stock_list

    def save_stock_list_to_tsdb(self, stock_list):
        """将股票列表保存到TimeSeriesDB"""
        logger.info(f"开始保存股票列表到TimeSeriesDB，共 {len(stock_list)} 只股票")

        for stock in stock_list:
            try:
                # 解析股票信息
                if 'market' not in stock:
                    logger.warning(f"股票数据缺少市场信息: {stock}")
                    continue

                market_id = stock['market']
                market = "SZ" if market_id == 0 else "SH"

                if 'code' not in stock:
                    logger.warning(f"股票数据缺少代码信息: {stock}")
                    continue

                code = stock['code']

                if 'name' not in stock:
                    name = f"未知_{code}"
                    logger.warning(f"股票数据缺少名称信息: {stock}，使用默认名称: {name}")
                else:
                    name = stock['name']

                # 判断股票类型
                if code.startswith('00') and market == 'SZ':
                    industry = '深市A股'
                elif code.startswith('30') and market == 'SZ':
                    industry = '创业板'
                elif code.startswith('60') and market == 'SH':
                    industry = '沪市A股'
                elif code.startswith('68') and market == 'SH':
                    industry = '科创板'
                elif code.startswith('5') or code.startswith('1') or code.startswith('5'):
                    industry = '基金'
                else:
                    industry = '其他'

                # 获取额外字段
                volunit = stock.get('volunit', 100)  # 默认交易单位为100
                decimal_point = stock.get('decimal_point', 2)  # 默认小数点位数为2
                pre_close = stock.get('pre_close', 0.0)  # 默认前收盘价为0

                # 添加到TimeSeriesDB
                logger.debug(f"添加股票 {market}_{code} {name} {industry} 到TimeSeriesDB")
                self.tsdb.add_symbol(
                    symbol=code,
                    name=name,
                    market=market,
                    industry=industry,
                    is_active=True,
                    volunit=volunit,
                    decimal_point=decimal_point,
                    pre_close=pre_close
                )

                logger.debug(f"已保存股票 {market}_{code} {name}")
            except Exception as e:
                code = stock.get('code', '未知')
                logger.error(f"保存股票 {code} 失败: {str(e)}")

        logger.info("股票列表保存完成")

    def download_stock_daily_data(self, stock_list, start_date=None, end_date=None):
        """
        下载股票日线数据

        参数:
            stock_list: 股票列表
            start_date: 开始日期，如果为None则使用当前日期前365天
            end_date: 结束日期，如果为None则使用当前日期
        """
        if not self.connected:
            logger.error("未连接到标准行情服务器，无法下载股票日线数据")
            return

        # 设置日期范围
        if end_date is None:
            end_date = datetime.now().date()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

        if start_date is None:
            start_date = end_date - timedelta(days=365)
        elif isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()

        logger.info(f"开始下载股票日线数据，日期范围: {start_date} - {end_date}")

        # 创建线程池
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            futures = []
            for stock in stock_list:
                market = stock['market']
                code = stock['code']

                # 过滤掉非股票品种
                if (market == 0 and not (code.startswith('00') or code.startswith('30'))) or \
                   (market == 1 and not (code.startswith('60') or code.startswith('68'))):
                    continue

                future = executor.submit(
                    self._download_single_stock_daily_data,
                    market, code, start_date, end_date
                )
                futures.append(future)

            # 处理结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        market, code, count = result
                        logger.info(f"成功下载股票 {market}_{code} 日线数据，共 {count} 条记录")
                except Exception as e:
                    logger.error(f"下载股票日线数据失败: {str(e)}")

        logger.info("股票日线数据下载完成")

    def _download_single_stock_daily_data(self, market, code, start_date, end_date):
        """下载单只股票的日线数据"""
        try:
            # 获取日线数据，通达信最多返回800条记录，可能需要多次获取
            data_list = []
            current_date = end_date

            while current_date >= start_date:
                # 获取一批数据
                data = self.hq_api.get_security_bars(9, market, code, 0, 800)
                if not data:
                    break

                # 转换为DataFrame
                df = self.hq_api.to_df(data)
                if df.empty:
                    break

                # 处理日期
                df['datetime'] = pd.to_datetime(df['datetime'])

                # 过滤日期范围
                df = df[(df['datetime'].dt.date >= start_date) & (df['datetime'].dt.date <= end_date)]

                # 添加到列表
                data_list.append(df)

                # 更新日期
                if not df.empty:
                    current_date = df['datetime'].min().date() - timedelta(days=1)
                else:
                    break

                # 避免请求过于频繁
                time.sleep(0.5)

            # 合并数据
            if data_list:
                all_data = pd.concat(data_list)
                all_data = all_data.drop_duplicates(subset=['datetime']).sort_values('datetime')

                # 保存到TimeSeriesDB
                market_code = "SZ" if market == 0 else "SH"
                self.tsdb.write_market_data(
                    symbol=code,
                    data=all_data,
                    market=market_code,
                    data_type='bar',
                    freq='day'
                )

                return market_code, code, len(all_data)

            return None
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} 日线数据失败: {str(e)}")
            return None

    def download_stock_minute_data(self, stock_list, freq='min5', days=5):
        """
        下载股票分钟线数据

        参数:
            stock_list: 股票列表
            freq: 频率，可选 'min1', 'min5', 'min15'
            days: 获取最近几天的数据
        """
        if not self.connected:
            logger.error("未连接到标准行情服务器，无法下载股票分钟线数据")
            return

        # 设置频率对应的category
        freq_category = {
            'min1': 8,  # 1分钟线
            'min5': 0,  # 5分钟线
            'min15': 1,  # 15分钟线
        }

        if freq not in freq_category:
            logger.error(f"不支持的频率: {freq}")
            return

        category = freq_category[freq]
        logger.info(f"开始下载股票{freq}数据，最近 {days} 天")

        # 创建线程池
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            futures = []
            for stock in stock_list:
                market = stock['market']
                code = stock['code']

                # 过滤掉非股票品种
                if (market == 0 and not (code.startswith('00') or code.startswith('30'))) or \
                   (market == 1 and not (code.startswith('60') or code.startswith('68'))):
                    continue

                future = executor.submit(
                    self._download_single_stock_minute_data,
                    market, code, category, freq, days
                )
                futures.append(future)

            # 处理结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        market, code, count = result
                        logger.info(f"成功下载股票 {market}_{code} {freq}数据，共 {count} 条记录")
                except Exception as e:
                    logger.error(f"下载股票{freq}数据失败: {str(e)}")

        logger.info(f"股票{freq}数据下载完成")

    def _download_single_stock_minute_data(self, market, code, category, freq, days):
        """下载单只股票的分钟线数据"""
        try:
            # 获取分钟线数据，通达信最多返回800条记录
            data = self.hq_api.get_security_bars(category, market, code, 0, 800)
            if not data:
                return None

            # 转换为DataFrame
            df = self.hq_api.to_df(data)
            if df.empty:
                return None

            # 处理日期
            df['datetime'] = pd.to_datetime(df['datetime'])

            # 过滤最近几天的数据
            start_date = datetime.now().date() - timedelta(days=days)
            df = df[df['datetime'].dt.date >= start_date]

            if df.empty:
                return None

            # 保存到TimeSeriesDB
            market_code = "SZ" if market == 0 else "SH"
            self.tsdb.write_market_data(
                symbol=code,
                data=df,
                market=market_code,
                data_type='bar',
                freq=freq
            )

            return market_code, code, len(df)
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} {freq}数据失败: {str(e)}")
            return None

    def download_index_data(self, freq='day', days=365):
        """
        下载指数数据

        参数:
            freq: 频率，可选 'day', 'min1', 'min5', 'min15'
            days: 获取最近几天的数据
        """
        if not self.connected:
            logger.error("未连接到标准行情服务器，无法下载指数数据")
            return

        # 主要指数列表
        index_list = [
            {'market': 1, 'code': '000001', 'name': '上证指数'},
            {'market': 0, 'code': '399001', 'name': '深证成指'},
            {'market': 0, 'code': '399006', 'name': '创业板指'},
            {'market': 1, 'code': '000016', 'name': '上证50'},
            {'market': 1, 'code': '000300', 'name': '沪深300'},
            {'market': 1, 'code': '000905', 'name': '中证500'},
            {'market': 0, 'code': '399005', 'name': '中小板指'}
        ]

        # 设置频率对应的category
        freq_category = {
            'day': 9,    # 日线
            'min1': 8,   # 1分钟线
            'min5': 0,   # 5分钟线
            'min15': 1,  # 15分钟线
        }

        if freq not in freq_category:
            logger.error(f"不支持的频率: {freq}")
            return

        category = freq_category[freq]
        logger.info(f"开始下载指数{freq}数据，最近 {days} 天")

        # 添加指数到符号表
        for index in index_list:
            market_code = "SZ" if index['market'] == 0 else "SH"
            self.tsdb.add_symbol(
                symbol=index['code'],
                name=index['name'],
                market=market_code,
                industry='指数',
                is_active=True
            )

        # 下载指数数据
        for index in index_list:
            try:
                market = index['market']
                code = index['code']

                # 获取指数数据
                if freq == 'day':
                    # 日线数据
                    data = self.hq_api.get_index_bars(category, market, code, 0, 800)
                else:
                    # 分钟线数据
                    data = self.hq_api.get_index_bars(category, market, code, 0, 800)

                if not data:
                    continue

                # 转换为DataFrame
                df = self.hq_api.to_df(data)
                if df.empty:
                    continue

                # 处理日期
                df['datetime'] = pd.to_datetime(df['datetime'])

                # 过滤最近几天的数据
                start_date = datetime.now().date() - timedelta(days=days)
                df = df[df['datetime'].dt.date >= start_date]

                if df.empty:
                    continue

                # 保存到TimeSeriesDB
                market_code = "SZ" if market == 0 else "SH"
                self.tsdb.write_market_data(
                    symbol=code,
                    data=df,
                    market=market_code,
                    data_type='bar',
                    freq=freq
                )

                logger.info(f"成功下载指数 {market_code}_{code} {index['name']} {freq}数据，共 {len(df)} 条记录")

                # 避免请求过于频繁
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"下载指数 {index['code']} {freq}数据失败: {str(e)}")

        logger.info(f"指数{freq}数据下载完成")

    def download_basic_data(self, stock_list):
        """
        下载股票基本数据（公司信息、除权除息、财务数据）

        参数:
            stock_list: 股票列表
        """
        if not self.connected:
            logger.error("未连接到标准行情服务器，无法下载基本数据")
            return

        logger.info("开始下载股票基本数据")

        # 创建线程池
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            futures = []
            for stock in stock_list:
                market = stock['market']
                code = stock['code']

                # 过滤掉非股票品种
                if (market == 0 and not (code.startswith('00') or code.startswith('30'))) or \
                   (market == 1 and not (code.startswith('60') or code.startswith('68'))):
                    continue

                future = executor.submit(
                    self._download_single_stock_basic_data,
                    market, code
                )
                futures.append(future)

            # 处理结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        market, code, data_types = result
                        logger.info(f"成功下载股票 {market}_{code} 基本数据: {', '.join(data_types)}")
                except Exception as e:
                    logger.error(f"下载股票基本数据失败: {str(e)}")

        logger.info("股票基本数据下载完成")

    def _download_single_stock_basic_data(self, market, code):
        """
        下载单只股票的基本数据

        返回:
            tuple: (market_code, code, data_types)，其中 data_types 是成功下载的数据类型列表
        """
        try:
            market_code = "SZ" if market == 0 else "SH"
            data_types = []

            # 1. 下载公司信息目录
            company_info_category = self._download_company_info_category(market, code)
            if company_info_category is not None:
                data_types.append("company_category")

            # 2. 下载公司信息详情
            if company_info_category is not None:
                company_info_content = self._download_company_info_content(market, code, company_info_category)
                if company_info_content is not None:
                    data_types.append("company_info")

            # 3. 下载除权除息信息
            xdxr_info = self._download_xdxr_info(market, code)
            if xdxr_info is not None:
                data_types.append("xdxr")

            # 4. 下载财务信息
            finance_info = self._download_finance_info(market, code)
            if finance_info is not None:
                data_types.append("finance")

            return market_code, code, data_types
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} 基本数据失败: {str(e)}")
            return None

    def _download_company_info_category(self, market, code):
        """
        下载公司信息目录

        参数:
            market: 市场代码，0-深圳，1-上海
            code: 股票代码

        返回:
            list: 公司信息目录列表，如果失败则返回 None
        """
        try:
            # 获取公司信息目录
            category_data = self.hq_api.get_company_info_category(market, code)
            if not category_data:
                logger.warning(f"股票 {market}_{code} 没有公司信息目录")
                return None

            # 转换为DataFrame
            df = pd.DataFrame(category_data)
            # print(df)

            # 保存到TimeSeriesDB
            market_code = "SZ" if market == 0 else "SH"
            self.tsdb.write_fundamental_data(
                symbol=code,
                data=df,
                market=market_code,
                data_type='company_category',
                date=datetime.now()
            )

            logger.debug(f"成功下载股票 {market_code}_{code} 公司信息目录，共 {len(df)} 条记录")
            return category_data
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} 公司信息目录失败: {str(e)}")
            return None

    def _download_company_info_content(self, market, code, category_data):
        """
        下载公司信息详情

        参数:
            market: 市场代码，0-深圳，1-上海
            code: 股票代码
            category_data: 公司信息目录数据

        返回:
            bool: 是否成功下载
        """
        try:
            market_code = "SZ" if market == 0 else "SH"
            success = False

            # 遍历目录，获取每个文件的内容
            info = []
            for item in category_data:
                try:
                    name = item['name']
                    file_name = item['filename']
                    start_pos = item['start']
                    length = item['length']

                    # 获取公司信息详情
                    content = self.hq_api.get_company_info_content(market, code, file_name, start_pos, length)
                    if not content:
                        logger.warning(f"股票 {market_code}_{code} 文件 {file_name} 没有内容")
                        continue

                    # 将内容转换为DataFrame
                    # 注意：公司信息内容通常是文本格式，需要进行解析
                    # 这里简单地将其作为一个字符串保存
                    info.append({
                        'name': name,
                        'filename': file_name,
                        'content': content,
                    })

                    logger.debug(f"成功下载股票 {market_code}_{code} 公司信息文件 {name}")
                    success = True

                    # 避免请求过于频繁
                    time.sleep(0.5)
                except Exception as e:
                    logger.error(f"下载股票 {market_code}_{code} 公司信息文件 {file_name} 失败: {str(e)}")

            # 保存到TimeSeriesDB
            df = pd.DataFrame(info)
            # print(df.head())
            self.tsdb.write_fundamental_data(
                symbol=code,
                data=df,
                market=market_code,
                data_type=f'company_info',
                date=datetime.now()
            )

            return success
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} 公司信息详情失败: {str(e)}")
            return None

    def _download_xdxr_info(self, market, code):
        """
        下载除权除息信息

        参数:
            market: 市场代码，0-深圳，1-上海
            code: 股票代码

        返回:
            pd.DataFrame: 除权除息信息，如果失败则返回 None
        """
        try:
            # 获取除权除息信息
            xdxr_data = self.hq_api.get_xdxr_info(market, code)
            if len(xdxr_data) == 0:
                logger.warning(f"股票 {market}_{code} 没有除权除息信息")
                return None

            # 转换为DataFrame
            df = self.hq_api.to_df(xdxr_data)
            # print(df)

            # 保存到TimeSeriesDB
            market_code = "SZ" if market == 0 else "SH"
            self.tsdb.write_fundamental_data(
                symbol=code,
                data=df,
                market=market_code,
                data_type='xdxr',
                date=datetime.now()
            )

            logger.debug(f"成功下载股票 {market_code}_{code} 除权除息信息，共 {len(df)} 条记录")
            return df
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} 除权除息信息失败: {str(e)}")
            return None

    def _download_finance_info(self, market, code):
        """
        下载财务信息

        参数:
            market: 市场代码，0-深圳，1-上海
            code: 股票代码

        返回:
            pd.DataFrame: 财务信息，如果失败则返回 None
        """
        try:
            # 获取财务数据
            fin_data = self.hq_api.get_finance_info(market, code)
            if not fin_data:
                logger.warning(f"股票 {market}_{code} 没有财务信息")
                return None

            # 转换为DataFrame
            df = pd.DataFrame([fin_data])
            # print(df.head())

            # 保存到TimeSeriesDB
            market_code = "SZ" if market == 0 else "SH"
            self.tsdb.write_fundamental_data(
                symbol=code,
                data=df,
                market=market_code,
                data_type='financial',
                date=datetime.now()
            )

            logger.debug(f"成功下载股票 {market_code}_{code} 财务信息")
            return df
        except Exception as e:
            logger.error(f"下载股票 {market}_{code} 财务信息失败: {str(e)}")
            return None

    def download_block_data(self):
        """
        下载板块数据

        返回:
            Dict[str, pd.DataFrame]: 板块类型到板块数据的映射
        """
        if not self.connected:
            logger.error("未连接到标准行情服务器，无法下载板块数据")
            return {}

        logger.info("开始下载板块数据")

        # 板块类型列表
        block_types = [
            {'code': TDXParams.BLOCK_SZ, 'name': '指数板块'},
            {'code': TDXParams.BLOCK_FG, 'name': '风格板块'},
            {'code': TDXParams.BLOCK_GN, 'name': '概念板块'},
            {'code': TDXParams.BLOCK_DEFAULT, 'name': '默认板块'}
        ]

        results = {}

        for block_type in block_types:
            try:
                block_code = block_type['code']
                block_name = block_type['name']

                logger.info(f"获取板块数据: {block_name} ({block_code})")

                # 获取板块数据
                block_data = self.hq_api.get_and_parse_block_info(block_code)
                if len(block_data) == 0:
                    logger.warning(f"板块 {block_name} 没有数据")
                    continue

                # 转换为DataFrame
                df = pd.DataFrame(block_data)
                print(df)

                # 保存到TimeSeriesDB
                block_path = os.path.join(self.tsdb.metadata_path, f"block_{block_code}.parquet")
                df.to_parquet(block_path, index=False)

                # 为每个板块创建股票列表
                for _, block in df.iterrows():
                    block_file = block['block_file']
                    block_name = block['block_name']

                    # 获取板块内的股票
                    stocks_in_block = []
                    for stock_info in block['stock_list']:
                        market = 0 if stock_info['market'] == 0 else 1
                        code = stock_info['code']
                        market_code = "SZ" if market == 0 else "SH"

                        stocks_in_block.append({
                            'market': market_code,
                            'code': code,
                            'block_name': block_name,
                            'block_type': block_type['name'],
                            'datetime': pd.Timestamp(datetime.now().date())
                        })

                    if stocks_in_block:
                        # 创建DataFrame
                        stocks_df = pd.DataFrame(stocks_in_block)

                        # 保存到TimeSeriesDB
                        block_stocks_path = os.path.join(
                            self.tsdb.fundamental_path,
                            'block',
                            f"{block_file}.parquet"
                        )
                        os.makedirs(os.path.dirname(block_stocks_path), exist_ok=True)
                        stocks_df.to_parquet(block_stocks_path, index=False)

                        logger.debug(f"成功保存板块 {block_name} 的股票列表，共 {len(stocks_df)} 只股票")

                results[block_code] = df
                logger.info(f"成功下载板块 {block_name}，共 {len(df)} 个板块")

                # 避免请求过于频繁
                time.sleep(1)
            except Exception as e:
                logger.error(f"下载板块 {block_type['name']} 数据失败: {str(e)}")

        logger.info("板块数据下载完成")
        return results

    def get_stocks_in_block(self, block_name=None, block_type=None):
        """
        获取指定板块中的股票列表

        参数:
            block_name: 板块名称，如果为None则返回所有板块
            block_type: 板块类型，如果为None则返回所有类型

        返回:
            pd.DataFrame: 股票列表
        """
        # 板块数据路径
        block_path = os.path.join(self.tsdb.fundamental_path, 'block')

        if not os.path.exists(block_path):
            logger.warning("板块数据目录不存在，请先下载板块数据")
            return pd.DataFrame()

        # 获取所有板块文件
        block_files = [f for f in os.listdir(block_path) if f.endswith('.parquet')]

        if not block_files:
            logger.warning("没有找到板块数据文件，请先下载板块数据")
            return pd.DataFrame()

        # 读取并筛选板块数据
        dfs = []
        for file in block_files:
            file_path = os.path.join(block_path, file)
            df = pd.read_parquet(file_path)

            # 根据板块名称和类型筛选
            if block_name and 'block_name' in df.columns:
                df = df[df['block_name'] == block_name]

            if block_type and 'block_type' in df.columns:
                df = df[df['block_type'] == block_type]

            if not df.empty:
                dfs.append(df)

        if not dfs:
            return pd.DataFrame()

        # 合并所有数据
        result = pd.concat(dfs, ignore_index=True)

        return result

    def download_financial_data(self, stock_list):
        """
        下载财务数据（兼容旧接口）

        参数:
            stock_list: 股票列表
        """
        return self.download_basic_data(stock_list)

    def get_and_save_market_codes(self, force_update=False):
        """
        获取并保存所有市场代码表

        参数:
            force_update: 是否强制更新，即使本地文件存在且未过期

        返回:
            bool: 是否成功获取市场代码表
        """
        # 检查本地文件是否存在
        market_path = os.path.join(self.tsdb.metadata_path, "markets.parquet")

        # 如果本地文件存在且未强制更新，检查文件是否过期
        if not force_update and os.path.exists(market_path):
            try:
                # 读取本地文件
                market_df = pd.read_parquet(market_path)

                # 检查文件是否包含必要的列
                if 'datetime' in market_df.columns:
                    # 获取文件的更新时间
                    file_datetime = pd.to_datetime(market_df['datetime'].iloc[0])
                    current_datetime = pd.Timestamp(datetime.now())

                    # 计算文件的更新时间距离现在的天数
                    days_diff = (current_datetime - file_datetime).days

                    # 如果文件更新时间在一周内，直接使用本地文件
                    if days_diff < 7:
                        logger.info(f"使用本地市场代码表，上次更新时间: {file_datetime}")
                        self.market_codes = market_df
                        return True
                    else:
                        logger.info(f"本地市场代码表已过期 ({days_diff} 天)，将重新获取")
                else:
                    logger.warning("本地市场代码表格式不正确，将重新获取")
            except Exception as e:
                logger.error(f"读取本地市场代码表失败: {str(e)}，将重新获取")

        # 如果需要从服务器获取数据
        if not self.connected or not self.ex_connected:
            logger.error("未连接到行情服务器，无法获取市场代码表")

            # 如果本地文件存在，尝试使用本地文件
            if os.path.exists(market_path):
                try:
                    market_df = pd.read_parquet(market_path)
                    logger.info(f"使用本地市场代码表（未连接到服务器）")
                    self.market_codes = market_df
                    return True
                except Exception as e:
                    logger.error(f"读取本地市场代码表失败: {str(e)}")

            return False

        # 获取标准市场信息
        market_info = []

        # 添加深圳市场
        try:
            sz_count = self.hq_api.get_security_count(0)
            market_info.append({
                'market_id': 0,
                'market_name': 'SZ',
                'market_type': 'STOCK',
                'security_count': sz_count,
                'description': '深圳证券交易所'
            })
            logger.info(f"深圳市场股票数量: {sz_count}")
        except Exception as e:
            logger.error(f"获取深圳市场信息失败: {str(e)}")

        # 添加上海市场
        try:
            sh_count = self.hq_api.get_security_count(1)
            market_info.append({
                'market_id': 1,
                'market_name': 'SH',
                'market_type': 'STOCK',
                'security_count': sh_count,
                'description': '上海证券交易所'
            })
            logger.info(f"上海市场股票数量: {sh_count}")
        except Exception as e:
            logger.error(f"获取上海市场信息失败: {str(e)}")

        # 获取扩展市场信息
        try:
            ex_markets = self.exhq_api.get_markets()
            if ex_markets:
                ex_markets_df = self.exhq_api.to_df(ex_markets)

                for _, row in ex_markets_df.iterrows():
                    market_type = 'OTHER'
                    if row['category'] == 3:  # 期货
                        market_type = 'FUTURE'
                    elif row['category'] == 5:  # 指数
                        market_type = 'INDEX'
                    elif row['category'] == 2:  # 港股
                        market_type = 'HK_STOCK'

                    market_info.append({
                        'market_id': row['market'],
                        'market_name': row['name'],
                        'market_type': market_type,
                        'category': row['category'],
                        'short_name': row['short_name'],
                        'description': f"{row['name']}({row['short_name']})"
                    })
                logger.info(f"获取到 {len(ex_markets_df)} 个扩展市场信息")
        except Exception as e:
            logger.error(f"获取扩展市场信息失败: {str(e)}")

        # 如果没有获取到任何市场信息，尝试使用本地文件
        if not market_info and os.path.exists(market_path):
            try:
                market_df = pd.read_parquet(market_path)
                logger.info(f"使用本地市场代码表（未能从服务器获取数据）")
                self.market_codes = market_df
                return True
            except Exception as e:
                logger.error(f"读取本地市场代码表失败: {str(e)}")
                return False

        # 将市场信息保存到TimeSeriesDB
        market_df = pd.DataFrame(market_info)
        market_df['datetime'] = pd.Timestamp(datetime.now())

        # 保存到元数据目录
        os.makedirs(os.path.dirname(market_path), exist_ok=True)
        market_df.to_parquet(market_path, index=False)

        # 缓存市场代码
        self.market_codes = market_df

        logger.info(f"成功保存市场代码表，共 {len(market_info)} 个市场")
        return True

    def get_market_instruments(self, save_to_tsdb=True, force_update=False):
        """
        获取特定市场的所有合约信息

        参数:
            save_to_tsdb: 是否保存到TimeSeriesDB
            force_update: 是否强制更新，即使本地文件存在且未过期

        返回:
            pd.DataFrame: 合约信息
        """
        # 检查本地文件是否存在
        instruments_path = os.path.join(self.tsdb.metadata_path, "instruments.parquet")

        # 如果本地文件存在且未强制更新，检查文件是否过期
        if not force_update and os.path.exists(instruments_path):
            try:
                # 读取本地文件
                instruments_df = pd.read_parquet(instruments_path)

                # 检查文件是否包含必要的列
                if 'datetime' in instruments_df.columns:
                    # 获取文件的更新时间
                    file_datetime = pd.to_datetime(instruments_df['datetime'].iloc[0])
                    current_datetime = pd.Timestamp(datetime.now())

                    # 计算文件的更新时间距离现在的天数
                    days_diff = (current_datetime - file_datetime).days

                    # 如果文件更新时间在一周内，直接使用本地文件
                    if days_diff < 7:
                        logger.info(f"使用本地合约信息，上次更新时间: {file_datetime}")
                        self.instruments_cache = instruments_df
                        return instruments_df
                    else:
                        logger.info(f"本地合约信息已过期 ({days_diff} 天)，将重新获取")
                else:
                    logger.warning("本地合约信息格式不正确，将重新获取")
            except Exception as e:
                logger.error(f"读取本地合约信息失败: {str(e)}，将重新获取")

        # 如果需要从服务器获取数据但未连接，尝试使用本地文件
        if not self.ex_connected:
            logger.error("未连接到扩展行情服务器，无法获取合约信息")

            # 如果本地文件存在，尝试使用本地文件
            if os.path.exists(instruments_path):
                try:
                    instruments_df = pd.read_parquet(instruments_path)
                    logger.info(f"使用本地合约信息（未连接到服务器）")
                    self.instruments_cache = instruments_df
                    return instruments_df
                except Exception as e:
                    logger.error(f"读取本地合约信息失败: {str(e)}")

            return None

        # 获取市场中的合约数量
        try:
            count = self.exhq_api.get_instrument_count()
            if not count:
                logger.warning(f"市场没有合约信息")

            logger.info(f"市场共有 {count} 个合约")

            # 分批获取合约信息
            instruments = []
            batch_size = 1000

            for i in range(0, count, batch_size):
                batch = self.exhq_api.get_instrument_info(i, min(batch_size, count - i))
                if batch:
                    instruments.extend(batch)
                    logger.info(f"已获取市场合约 {i} - {i + len(batch)}")

            if not instruments:
                # 如果没有获取到任何合约信息，尝试使用本地文件
                if os.path.exists(instruments_path):
                    try:
                        instruments_df = pd.read_parquet(instruments_path)
                        logger.info(f"使用本地合约信息（未能从服务器获取数据）")
                        self.instruments_cache = instruments_df
                        return instruments_df
                    except Exception as e:
                        logger.error(f"读取本地合约信息失败: {str(e)}")
                return None

            # 转换为DataFrame
            df = pd.DataFrame(instruments)
            # df = self.exhq_api.to_df(instruments)
            if len(self.market_codes) > 0:
                future_markets = self.market_codes[self.market_codes['market_type'] == 'FUTURE']
                df = df[df['market'].isin(future_markets['market_id'].to_list())]

            # 添加日期列
            df['datetime'] = pd.Timestamp(datetime.now())

            # 保存到TimeSeriesDB
            if save_to_tsdb:
                os.makedirs(os.path.dirname(instruments_path), exist_ok=True)
                df.to_parquet(instruments_path, index=False)
                logger.info(f"成功保存市场的合约信息，共 {len(df)} 个合约")

            # 缓存合约信息
            self.instruments_cache = df

            return df
        except Exception as e:
            logger.error(f"获取市场合约信息失败: {str(e)}")

            # 如果获取失败，尝试使用本地文件
            if os.path.exists(instruments_path):
                try:
                    instruments_df = pd.read_parquet(instruments_path)
                    logger.info(f"使用本地合约信息（服务器获取失败）")
                    self.instruments_cache = instruments_df
                    return instruments_df
                except Exception as e2:
                    logger.error(f"读取本地合约信息失败: {str(e2)}")

            return None

    def get_all_market_instruments(self, save_to_tsdb=True, force_update=False):
        """
        获取所有市场的合约信息

        参数:
            save_to_tsdb: 是否保存到TimeSeriesDB
            force_update: 是否强制更新，即使本地文件存在且未过期

        返回:
            pd.DataFrame: 合约信息
        """
        if not self.ex_connected and force_update:
            logger.error("未连接到扩展行情服务器，无法强制更新合约信息")

            # 尝试使用本地文件
            instruments_path = os.path.join(self.tsdb.metadata_path, "instruments.parquet")
            if os.path.exists(instruments_path):
                try:
                    instruments_df = pd.read_parquet(instruments_path)
                    logger.info(f"使用本地合约信息（未连接到服务器）")
                    return instruments_df
                except Exception as e:
                    logger.error(f"读取本地合约信息失败: {str(e)}")

            return {}

        # 确保已获取市场代码
        if self.market_codes is None:
            self.get_and_save_market_codes(force_update=force_update)

        if self.market_codes is None:
            logger.error("获取市场代码失败，无法获取合约信息")
            return {}

        # 获取期货市场
        df = self.get_market_instruments(save_to_tsdb, force_update)
        if df is not None:
            return df
        else:
            return pd.DataFrame()

    def download_future_data(self, freq='day', days=30):
        """
        下载期货数据

        参数:
            freq: 频率，可选 'day', 'min1', 'min5'
            days: 获取最近几天的数据
        """
        if not self.ex_connected:
            logger.error("未连接到扩展行情服务器，无法下载期货数据")
            return

        # 设置频率对应的category
        freq_category = {
            'day': 4,    # 日线
            'min1': 7,   # 1分钟线
            'min5': 0,   # 5分钟线
        }

        if freq not in freq_category:
            logger.error(f"不支持的频率: {freq}")
            return

        category = freq_category[freq]
        logger.info(f"开始下载期货{freq}数据，最近 {days} 天")

        try:
            # 获取期货合约列表
            markets = self.tsdb.markets
            if markets.empty():
                logger.error("获取期货市场列表失败")
                return

            for market in markets:
                market_id = market['market']
                market_name = market['name']

                # 判断是否为期货市场
                if market['category'] != 3:  # 期货类别为3
                    continue

                logger.info(f"获取期货市场 {market_name} 合约列表")

                # 获取市场下的期货合约
                instruments = None

                # 检查是否有缓存的合约信息
                if isinstance(self.instruments_cache, pd.DataFrame) and not self.instruments_cache.empty:
                    # 从缓存的DataFrame中筛选出当前市场的合约
                    market_instruments = self.instruments_cache[self.instruments_cache['market'] == market_id]
                    if not market_instruments.empty:
                        instruments = market_instruments.to_dict('records')
                        logger.info(f"使用缓存的期货市场 {market_name} 合约列表，共 {len(instruments)} 个合约")

                # 如果缓存中没有当前市场的合约信息，则从服务器获取
                if not instruments:
                    # 获取合约数量
                    count = self.exhq_api.get_instrument_count(market_id)
                    if not count:
                        logger.warning(f"期货市场 {market_name} 没有合约信息")
                        continue

                    # 分批获取合约信息
                    instruments = []
                    batch_size = 100

                    for i in range(0, count, batch_size):
                        batch = self.exhq_api.get_instrument_info(market_id, i, min(batch_size, count - i))
                        if batch:
                            instruments.extend(batch)

                if not instruments:
                    logger.warning(f"期货市场 {market_name} 没有合约信息")
                    continue

                logger.info(f"期货市场 {market_name} 共有 {len(instruments)} 个合约")

                # 下载主力合约数据
                main_instruments = [inst for inst in instruments if 'main' in inst['code'].lower() or inst['code'].endswith('L9')]

                for inst in main_instruments:
                    try:
                        code = inst['code']
                        name = inst['name']

                        # 添加到符号表
                        self.tsdb.add_symbol(
                            symbol=code,
                            name=name,
                            market="FUT",
                            industry=market_name,
                            is_active=True
                        )

                        # 获取期货数据
                        data = self.exhq_api.get_instrument_bars(category, market_id, code, 0, 800)
                        if not data:
                            continue

                        # 转换为DataFrame
                        df = self.exhq_api.to_df(data)
                        if df.empty:
                            continue

                        # 处理日期
                        df['datetime'] = pd.to_datetime(df['datetime'])

                        # 过滤最近几天的数据
                        start_date = datetime.now().date() - timedelta(days=days)
                        df = df[df['datetime'].dt.date >= start_date]

                        if df.empty:
                            continue

                        # 保存到TimeSeriesDB
                        self.tsdb.write_market_data(
                            symbol=code,
                            data=df,
                            market="FUT",
                            data_type='bar',
                            freq=freq
                        )

                        logger.info(f"成功下载期货 {code} {name} {freq}数据，共 {len(df)} 条记录")

                        # 避免请求过于频繁
                        time.sleep(0.5)
                    except Exception as e:
                        logger.error(f"下载期货 {code} {freq}数据失败: {str(e)}")

            logger.info(f"期货{freq}数据下载完成")
        except Exception as e:
            logger.error(f"下载期货数据失败: {str(e)}")

    def run_daily_task(self):
        """运行每日数据采集任务"""
        try:
            logger.info("开始每日数据采集任务")

            # 连接服务器
            if not self.connect():
                logger.error("连接服务器失败，无法执行每日任务")
                return

            # 获取股票列表
            stock_list = self.get_stock_list()
            if not stock_list:
                logger.error("获取股票列表失败，无法执行每日任务")
                self.disconnect()
                return

            # 保存股票列表
            self.save_stock_list_to_tsdb(stock_list)

            # 下载指数日线数据
            self.download_index_data(freq='day', days=10)

            # 下载指数分钟线数据
            self.download_index_data(freq='min5', days=3)

            # 下载股票日线数据
            self.download_stock_daily_data(stock_list, start_date=datetime.now().date() - timedelta(days=10))

            # 下载股票分钟线数据
            self.download_stock_minute_data(stock_list, freq='min5', days=3)

            # 下载期货数据
            self.download_future_data(freq='day', days=10)
            self.download_future_data(freq='min5', days=3)
            self.download_future_data(freq='min1', days=3)

            # 断开连接
            self.disconnect()

            logger.info("每日数据采集任务完成")
        except Exception as e:
            logger.error(f"执行每日任务失败: {str(e)}")
            self.disconnect()

    def run_weekly_task(self):
        """运行每周数据采集任务"""
        try:
            logger.info("开始每周数据采集任务")

            # 连接服务器
            if not self.connect():
                logger.error("连接服务器失败，无法执行每周任务")
                return

            # 获取并保存市场代码表（每周强制更新）
            self.get_and_save_market_codes(force_update=True)

            # 获取股票列表
            stock_list = self.get_stock_list()
            if not stock_list:
                logger.error("获取股票列表失败，无法执行每周任务")
                self.disconnect()
                return

            # 保存股票列表
            self.save_stock_list_to_tsdb(stock_list)

            # 获取所有市场的合约信息
            self.get_all_market_instruments(force_update=True)

            # 下载指数日线数据
            self.download_index_data(freq='day', days=365)

            # 下载股票日线数据
            self.download_stock_daily_data(stock_list, start_date=datetime.now().date() - timedelta(days=365))

            # 下载基本数据（包括公司信息、除权除息、财务数据）
            self.download_basic_data(stock_list)

            # 下载板块数据
            self.download_block_data()

            # 下载期货数据
            self.download_future_data(freq='day', days=365)

            # 断开连接
            self.disconnect()

            logger.info("每周数据采集任务完成")
        except Exception as e:
            logger.error(f"执行每周任务失败: {str(e)}")
            self.disconnect()

    def run_full_history_task(self, stock_codes=None):
        """
        运行完整历史数据采集任务

        参数:
            stock_codes: 指定股票代码列表，如果为None则下载所有股票
        """
        try:
            logger.info("开始完整历史数据采集任务")

            # 连接服务器
            if not self.connect():
                logger.error("连接服务器失败，无法执行完整历史任务")
                return

            # 获取并保存市场代码表（完整历史任务强制更新）
            self.get_and_save_market_codes(force_update=True)

            # 获取股票列表
            if stock_codes:
                # 使用指定的股票代码
                stock_list = []
                for code in stock_codes:
                    if '.' in code:
                        parts = code.split('.')
                        code = parts[0]
                        market = 0 if parts[1] == 'SZ' else 1
                    else:
                        # 根据代码前缀判断市场
                        if code.startswith('6'):
                            market = 1  # 上海
                        else:
                            market = 0  # 深圳

                    stock_list.append({'market': market, 'code': code})

                logger.info(f"使用指定的股票列表，共 {len(stock_list)} 只股票")
            else:
                # 获取所有股票
                stock_list = self.get_stock_list()
                if not stock_list:
                    logger.error("获取股票列表失败，无法执行完整历史任务")
                    self.disconnect()
                    return

            # 保存股票列表
            self.save_stock_list_to_tsdb(stock_list)

            # 获取所有市场的合约信息
            self.get_all_market_instruments(force_update=True)

            # 下载指数日线数据
            self.download_index_data(freq='day', days=3650)  # 约10年

            # 下载股票日线数据
            self.download_stock_daily_data(stock_list, start_date=datetime.now().date() - timedelta(days=3650))

            # 下载基本数据（包括公司信息、除权除息、财务数据）
            self.download_basic_data(stock_list)

            # 下载板块数据
            self.download_block_data()

            # 下载期货数据
            self.download_future_data(freq='day', days=3650)

            # 断开连接
            self.disconnect()

            logger.info("完整历史数据采集任务完成")
        except Exception as e:
            logger.error(f"执行完整历史任务失败: {str(e)}")
            self.disconnect()

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='从通达信行情获取数据并存储到TimeSeriesDB')
    parser.add_argument('--db-path', type=str, default='f:/hqdata/tsdb', help='TimeSeriesDB数据库路径')
    parser.add_argument('--task', type=str,
                        choices=['daily', 'weekly', 'full', 'stock', 'index', 'future', 'market_codes', 'instruments', 'basic', 'block'],
                        default='daily', help='执行的任务类型')
    parser.add_argument('--freq', type=str, choices=['day', 'min1', 'min5', 'min15'],
                        default='day', help='数据频率')
    parser.add_argument('--days', type=int, default=10, help='获取最近几天的数据')
    parser.add_argument('--stocks', type=str, help='指定股票代码，多个代码用逗号分隔')
    parser.add_argument('--market-id', type=int, help='指定市场ID，用于获取特定市场的合约信息')
    parser.add_argument('--force-update', action='store_true', help='强制更新数据，忽略本地缓存')
    parser.add_argument('--max-workers', type=int, default=5, help='最大工作线程数')

    args = parser.parse_args()

    # 创建数据采集器
    collector = TdxDataCollector(args.db_path, max_workers=args.max_workers)

    # 根据任务类型执行相应的操作
    if args.task == 'daily':
        collector.run_daily_task()
    elif args.task == 'weekly':
        collector.run_weekly_task()
    elif args.task == 'full':
        stock_codes = args.stocks.split(',') if args.stocks else None
        collector.run_full_history_task(stock_codes)
    elif args.task == 'market_codes':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 获取并保存市场代码表
        collector.get_and_save_market_codes(force_update=args.force_update)

        # 断开连接
        collector.disconnect()
    elif args.task == 'instruments':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 获取市场合约信息
        if args.market_id is not None:
            # 获取特定市场的合约信息
            collector.get_market_instruments(save_to_tsdb=True, force_update=args.force_update)
        else:
            # 获取所有市场的合约信息
            collector.get_all_market_instruments(force_update=args.force_update)

        # 断开连接
        collector.disconnect()
    elif args.task == 'stock':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 获取股票列表
        stock_list = collector.get_stock_list()
        if not stock_list:
            logger.error("获取股票列表失败")
            collector.disconnect()
            return

        # 保存股票列表
        collector.save_stock_list_to_tsdb(stock_list)

        # 如果指定了股票代码，则只下载指定的股票
        if args.stocks:
            stock_codes = args.stocks.split(',')
            filtered_list = []

            for code in stock_codes:
                if '.' in code:
                    parts = code.split('.')
                    code = parts[0]
                    market = 0 if parts[1] == 'SZ' else 1
                else:
                    # 根据代码前缀判断市场
                    if code.startswith('6'):
                        market = 1  # 上海
                    else:
                        market = 0  # 深圳

                filtered_list.append({'market': market, 'code': code})

            stock_list = filtered_list

        # 下载股票数据
        if args.freq == 'day':
            collector.download_stock_daily_data(stock_list, start_date=datetime.now().date() - timedelta(days=args.days))
        else:
            collector.download_stock_minute_data(stock_list, freq=args.freq, days=args.days)

        # 断开连接
        collector.disconnect()
    elif args.task == 'index':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 下载指数数据
        collector.download_index_data(freq=args.freq, days=args.days)

        # 断开连接
        collector.disconnect()
    elif args.task == 'future':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 下载期货数据
        collector.download_future_data(freq=args.freq, days=args.days)

        # 断开连接
        collector.disconnect()
    elif args.task == 'basic':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 获取股票列表
        # stock_list = collector.get_stock_list()
        stock_list = collector.tsdb.symbols
        if len(stock_list) == 0:
            logger.error("获取股票列表失败")
            collector.disconnect()
            return

        # 如果指定了股票代码，则只下载指定的股票
        if args.stocks:
            stock_codes = args.stocks.split(',')
            filtered_list = []

            for code in stock_codes:
                if '.' in code:
                    parts = code.split('.')
                    code = parts[0]
                    market = 0 if parts[1] == 'SZ' else 1
                else:
                    # 根据代码前缀判断市场
                    if code.startswith('6'):
                        market = 1  # 上海
                    else:
                        market = 0  # 深圳

                filtered_list.append({'market': market, 'code': code})

            stock_list = filtered_list
            logger.info(f"使用指定的股票列表，共 {len(stock_list)} 只股票")

        # 下载基本数据
        collector.download_basic_data(stock_list)

        # 断开连接
        collector.disconnect()
    elif args.task == 'block':
        # 连接服务器
        if not collector.connect():
            logger.error("连接服务器失败")
            return

        # 下载板块数据
        collector.download_block_data()

        # 断开连接
        collector.disconnect()

if __name__ == "__main__":
    # 解析命令行参数
    main()
"""
使用说明
安装依赖
首先需要安装必要的依赖库：
pip install pytdx pandas numpy pyarrow schedule

基本用法
每日数据更新：
python tdx_dc.py --task daily --db-path f:/hqdata

每周数据更新：
python tdx_dc.py --task weekly --db-path f:/hqdata

下载完整历史数据：
python tdx_dc.py --task full --db-path f:/hqdata

获取市场代码表：
python tdx_dc.py --task market_codes --db-path f:/hqdata
python tdx_dc.py --task market_codes --force-update --db-path f:/hqdata  # 强制更新，忽略本地缓存

获取合约信息：
python tdx_dc.py --task instruments --db-path f:/hqdata
python tdx_dc.py --task instruments --market-id 28 --db-path f:/hqdata  # 获取特定市场(如郑州商品期货)的合约信息

下载指定股票的数据：
python tdx_dc.py --task stock --stocks 000001.SZ,600000.SH --freq day --days 365 --db-path f:/hqdata

下载指数数据：
python tdx_dc.py --task index --freq day --days 365 --db-path f:/hqdata

下载期货数据：
python tdx_dc.py --task future --freq day --days 365 --db-path f:/hqdata

下载股票基本数据（公司信息、除权除息、财务数据）：
python tdx_dc.py --task basic --db-path f:/hqdata
python tdx_dc.py --task basic --stocks 000001.SZ,600000.SH --db-path f:/hqdata  # 下载指定股票的基本数据

下载板块数据（深证指数、风格指数、概念板块等）：
python tdx_dc.py --task block --db-path f:/hqdata

定时任务
可以使用提供的调度脚本自动执行定时任务：
python run_tdx_collector.py

这将在每个交易日（周一至周五）收盘后自动执行每日数据更新，并在每周五晚上执行每周数据更新。

数据存储结构
数据将按照TimeSeriesDB的存储结构进行组织，主要包括：

行情数据：
日线数据：{db_path}/market/bar/day/{year}/{market}_{symbol}.parquet
分钟线数据：{db_path}/market/bar/{freq}/{year}/{month}/{market}_{symbol}.parquet

基础数据：
财务数据：{db_path}/fundamental/financial/{year}/Q{quarter}/{market}_{symbol}.parquet
公司信息目录：{db_path}/fundamental/company_category/{market}_{symbol}.parquet
公司信息详情：{db_path}/fundamental/company_info_{file_type}/{market}_{symbol}.parquet
除权除息信息：{db_path}/fundamental/xdxr/{market}_{symbol}.parquet
板块数据：{db_path}/fundamental/block/{block_file}.parquet

元数据：
证券列表：{db_path}/metadata/symbols.parquet
市场代码表：{db_path}/metadata/markets.parquet
合约信息：{db_path}/metadata/instruments_{market_id}.parquet
板块列表：{db_path}/metadata/block_{block_code}.parquet

注意事项
1. 通达信行情服务器可能会限制频繁访问，脚本中已添加适当的延时，但仍可能遇到连接问题。
2. 建议在非交易时间运行大规模数据下载任务，以避免影响实时行情获取。
3. 完整历史数据下载可能需要较长时间，请确保网络稳定。
4. 脚本会自动处理连接断开和重连，但如果遇到持续连接失败，可能需要检查网络或更换行情服务器。
5. 市场代码表和合约信息会缓存在内存中，以提高后续数据下载的效率。

这个脚本提供了一个完整的解决方案，可以从pytdx获取各类数据并归档到TimeSeriesDB中，
支持定时任务和各种数据类型的下载。
"""