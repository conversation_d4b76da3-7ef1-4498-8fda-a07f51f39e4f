{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import optuna"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def get_hp_result(name, db_path):\n", "    study = optuna.create_study(study_name=name, storage=db_path, load_if_exists=True)\n", "    df = study.trials_dataframe(attrs=('number', 'value', 'params', 'state'))\n", "    return study.best_value, study.best_params\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["names=[\"MLP\", \"GBDT\"]\n", "dbs=[\n", "    # \"sqlite:///db_gbdt_ALL_short.sqlite3\",\n", "    \"sqlite:///db_MLP_MIX_long.sqlite3\",\n", "    \"sqlite:///db_MLP_MIX_short.sqlite3\",\n", "]\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m[I 2022-01-20 20:09:24,706]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-20 20:09:25,113]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0.6416601339975992 {'lr': 0.15877368309955392}\n", "0.6669569611549377 {'lr': 0.2863916186959531}\n"]}], "source": ["for db in dbs:\n", "    bv, bp = get_hp_result(\"MLP\", db)\n", "    print(bv, bp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}