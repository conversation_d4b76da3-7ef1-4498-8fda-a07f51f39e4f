# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.


from __future__ import division
from __future__ import print_function

import numpy as np
import pandas as pd
import copy
from qlib.utils import get_or_create_path
from qlib.log import get_module_logger

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, random_split
from sklearn.metrics import accuracy_score

from .pytorch_utils import count_parameters
from qlib.model.base import Model
from qlib.data.dataset.handler import DataHandlerLP
from .tcn import TemporalConvNet
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES


class TCN(Model):
    """TCN Model

    Parameters
    ----------
    d_feat : int
        input dimension for each time step
    metric: str
        the evaluate metric used in early stop
    optimizer : str
        optimizer name
    GPU : str
        the GPU ID(s) used for training
    """

    def __init__(
        self,
        num_code=50,
        d_feat=6,
        n_chans=128,
        kernel_size=5,
        num_layers=2,
        dropout=0.0,
        n_epochs=200,
        lr=0.001,
        metric="",
        batch_size=2000,
        early_stop=20,
        loss="binary",
        optimizer="adam",
        n_jobs=10,
        GPU=0,
        seed=None,
        **kwargs
    ):
        # Set logger.
        self.logger = get_module_logger("TCN")
        self.logger.info("TCN pytorch version...")

        # set hyper-parameters.
        self.num_code = num_code
        self.d_feat = d_feat
        self.n_chans = n_chans + 1
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.n_epochs = n_epochs
        self.lr = lr
        self.metric = metric
        self.batch_size = batch_size
        self.early_stop = early_stop
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.device = torch.device("cuda:%d" % (GPU) if torch.cuda.is_available() and GPU >= 0 else "cpu")
        self.n_jobs = n_jobs
        self.seed = seed

        self.logger.info(
            "TCN parameters setting:"
            "\nd_feat : {}"
            "\nn_chans : {}"
            "\nkernel_size : {}"
            "\nnum_layers : {}"
            "\ndropout : {}"
            "\nn_epochs : {}"
            "\nlr : {}"
            "\nmetric : {}"
            "\nbatch_size : {}"
            "\nearly_stop : {}"
            "\noptimizer : {}"
            "\nloss_type : {}"
            "\ndevice : {}"
            "\nn_jobs : {}"
            "\nuse_GPU : {}"
            "\nseed : {}".format(
                d_feat,
                n_chans,
                kernel_size,
                num_layers,
                dropout,
                n_epochs,
                lr,
                metric,
                batch_size,
                early_stop,
                optimizer.lower(),
                loss,
                self.device,
                n_jobs,
                self.use_gpu,
                seed,
            )
        )

        if self.seed is not None:
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)

        self.TCN_model = TCNModel(
            num_code=self.num_code,
            num_input=self.d_feat,
            output_size=1,
            num_channels=[self.n_chans] * self.num_layers,
            kernel_size=self.kernel_size,
            dropout=self.dropout,
        )
        self.logger.info("model:\n{:}".format(self.TCN_model))
        self.logger.info("model size: {:.4f} MB".format(count_parameters(self.TCN_model)))

        if optimizer.lower() == "adam":
            self.train_optimizer = optim.Adam(self.TCN_model.parameters(), lr=self.lr)
        elif optimizer.lower() == "gd":
            self.train_optimizer = optim.SGD(self.TCN_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError("optimizer {} is not supported!".format(optimizer))

        self.fitted = False
        self.TCN_model.to(self.device)

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    def mse(self, pred, label):
        loss = (pred - label) ** 2
        return torch.mean(loss)

    def loss_fn(self, pred, label):
        mask = ~torch.isnan(label)
        if self.loss == "mse":
            return self.mse(pred[mask], label[mask])
        raise ValueError("unknown loss `%s`" % self.loss)

    def metric_fn(self, pred, label):

        mask = torch.isfinite(label)

        if self.metric in ("", "loss"):
            return -self.loss_fn(pred[mask], label[mask])

        raise ValueError("unknown metric `%s`" % self.metric)

    def accuracy(self, pred, label):
        if self.use_gpu:
            preds = (pred>0.5).type(torch.IntTensor).cuda()
        else:
            preds = (pred>0.5).type(torch.IntTensor)
        return (preds == label).float().mean()

    def loader(self, dataset, batch_size=32, shuffle=True):
        # 将数据集划分为训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        return train_dataloader, val_dataloader
    
    def train_epoch(self, dataloader):

        self.TCN_model.train()

        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)
            pred = self.TCN_model(encodeds, inputs)
            loss = self.loss_fn(pred, targets.float())
            self.train_optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_value_(self.TCN_model.parameters(), 3.0)
            self.train_optimizer.step()

    def test_epoch(self, dataloader):

        self.TCN_model.eval()

        losses = []
        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            with torch.no_grad():
                pred = self.TCN_model(encodeds, inputs)
                loss = self.loss_fn(pred, targets.float())
                losses.append(loss.item())

        return np.mean(losses)

    def fit(
        self,
        dataset,
        evals_result=dict(),
        save_path=None,
    ):
        x_data, y_data, encoded_data = dataset.prepare(
            ["train", "valid"],
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_dl, val_dl = self.loader(dataset, self.batch_size)

        save_path = get_or_create_path(save_path)

        stop_steps = 0
        train_loss = 0
        best_loss = -np.inf
        best_epoch = 0
        evals_result["loss"] = {}
        evals_result["loss"]["train"] = []
        evals_result["loss"]["valid"] = []

        # train
        self.logger.info("training...")
        self.fitted = True

        for step in range(self.n_epochs):
            self.logger.info("Epoch %d:", step)
            self.logger.info("training...")
            self.train_epoch(train_dl)
            self.logger.info("evaluating...")
            train_loss = self.test_epoch(train_dl)
            val_loss  = self.test_epoch(val_dl)
            self.logger.info("score train %.6f, valid %.6f" % (train_loss*10e3, val_loss*10e3))

            evals_result["loss"]["train"].append(train_loss)
            evals_result["loss"]["valid"].append(val_loss)

            if val_loss < best_loss:
                best_loss = val_loss

                stop_steps = 0
                best_epoch = step
                best_param = copy.deepcopy(self.TCN_model.state_dict())
            else:
                stop_steps += 1
                if stop_steps >= self.early_stop:
                    self.logger.info("early stop")
                    break

        self.logger.info("best loss: %.6lf @ %d" % (best_loss, best_epoch))
        self.TCN_model.load_state_dict(best_param)
        # torch.save(best_param, save_path)
        model = self.TCN_model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        # sm = torch.jit.script(model)
        embedding = torch.tensor([0])
        example_input = torch.rand(1, self.n_chans-1, self.d_feat)
        # print(embedding.shape, example_input.shape)
        sm = torch.jit.trace(model, (embedding, example_input))
        sm.save(save_path)

        if self.use_gpu:
            torch.cuda.empty_cache()

        return best_epoch, best_loss

    def predict(self, dataset):
        if not self.fitted:
            raise ValueError("model is not fitted yet!")

        dl_test = dataset.prepare("test", col_set=["feature", "label"], data_key=DataHandlerLP.DK_I)
        dl_test.config(fillna_type="ffill+bfill")
        test_loader = DataLoader(dl_test, batch_size=self.batch_size, num_workers=self.n_jobs)
        self.TCN_model.eval()
        preds = []

        for data in test_loader:

            feature = data[:, :, 0:-1].to(self.device)

            with torch.no_grad():
                pred = self.TCN_model(feature.float()).detach().cpu().numpy()

            preds.append(pred)

        return pd.Series(np.concatenate(preds), index=dl_test.get_index())


class TCNModel(nn.Module):
    def __init__(self, num_code, num_input, output_size, num_channels, kernel_size, dropout):
        super().__init__()
        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=num_input)
        self.num_input = num_input
        self.tcn = TemporalConvNet(num_input, num_channels, kernel_size, dropout=dropout)
        self.linear = nn.Linear(num_channels[-1], output_size)
        # self.sigmoid = nn.Sigmoid()

    def forward(self, code_ids, x):
        embedded_data = self.code_embeddings(code_ids)
        # 将嵌入向量序列和其他特征按维度拼接在一起x
        x = torch.cat((embedded_data, x), dim=-1)
        output = self.tcn(x)
        output = self.linear(output[:, :, -1])
        # output = self.sigmoid(output)
        return output.view(-1)


# 定义TCN模型
class TCNModelV2(nn.Module):
    def __init__(self, input_size, output_size, num_channels, kernel_size, dropout):
        super(TCNModelV2, self).__init__()
        self.tcn = nn.Sequential(
            nn.Conv1d(input_size, num_channels, kernel_size, padding=(kernel_size - 1)),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=2, padding=(2*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=4, padding=(4*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=8, padding=(8*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=16, padding=(16*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=32, padding=(32*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=64, padding=(64*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, num_channels, kernel_size, dilation=128, padding=(128*(kernel_size - 1))),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(num_channels, output_size, kernel_size=1)
        )

    def forward(self, code_ids, x):
        embedded_data = self.code_embeddings(code_ids)
        # 将嵌入向量序列和其他特征按维度拼接在一起x
        x = torch.cat((embedded_data, x), dim=-1)
        out = self.tcn(x)
        # return out[:, :, -1]
        return out.view(-1)