# pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# data handling
numpy>=1.19.0
pandas>=1.2.0
redis
fastparquet
# stockstats>=0.4.0
# elegantrl
#pyfolio
# plot
matplotlib>=3.3.0

# Model Building Requirements
scikit-learn>=0.24.0
gym>=0.17
# stable-baselines3[extra]
ray[rllib]
# ray[tune]
lz4
# tensorboardX
# gputil
--find-links https://download.pytorch.org/whl/torch_stable.html
torch==1.11.0+cpu

# market data & paper trading API
# exchange_calendars
# alpaca_trade_api
# ccxt>=1.66.32
backtrader>=1.9.76
tushare>=1.2.89

# testing requirements
# pytest

# packaging
#setuptools>=41.4.0
# setuptools==59.5.0
# wheel>=0.33.6

#hooks
