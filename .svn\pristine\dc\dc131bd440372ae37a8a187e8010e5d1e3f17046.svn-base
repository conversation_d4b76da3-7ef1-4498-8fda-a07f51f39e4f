2022-01-19 23:37:09: -- 分析开始 User Command
2022-01-19 23:37:09: 变更：20， 冲突：0， 复制时间：0， 复制状态：0， 错误： 0, All: 62
2022-01-19 23:37:09: Left to Right: Copy File: 20 
2022-01-19 23:37:09: -- 分析已结束。历时 00:00:00, 速度： Many 文件/秒
2022-01-19 23:37:09: 
2022-01-19 23:37:15: == 同步开始由 User Command
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_long.json' -> 'D:/RoboQuant/model/gbdt_long.json' (4,499)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_long.txt' -> 'D:/RoboQuant/model/gbdt_long.txt' (6,830,697)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_short.json' -> 'D:/RoboQuant/model/gbdt_short.json' (4,494)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_short.txt' -> 'D:/RoboQuant/model/gbdt_short.txt' (5,850,719)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_long.json' -> 'D:/RoboQuant/model/MLP_5R_long.json' (4,492)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_long.model' -> 'D:/RoboQuant/model/MLP_5R_long.model' (137,150)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_short.json' -> 'D:/RoboQuant/model/MLP_5R_short.json' (4,498)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_5R_short.model' -> 'D:/RoboQuant/model/MLP_5R_short.model' (137,195)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_long.json' -> 'D:/RoboQuant/model/MLP_7R_long.json' (4,485)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_long.model' -> 'D:/RoboQuant/model/MLP_7R_long.model' (137,150)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_short.json' -> 'D:/RoboQuant/model/MLP_7R_short.json' (4,487)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_7R_short.model' -> 'D:/RoboQuant/model/MLP_7R_short.model' (137,195)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_long.json' -> 'D:/RoboQuant/model/MLP_long.json' (4,503)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_long.model' -> 'D:/RoboQuant/model/MLP_long.model' (136,629)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_long.json' -> 'D:/RoboQuant/model/MLP_MIX_long.json' (4,499)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_long.model' -> 'D:/RoboQuant/model/MLP_MIX_long.model' (137,195)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_short.json' -> 'D:/RoboQuant/model/MLP_MIX_short.json' (4,494)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_MIX_short.model' -> 'D:/RoboQuant/model/MLP_MIX_short.model' (137,240)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_short.json' -> 'D:/RoboQuant/model/MLP_short.json' (4,495)
2022-01-19 23:37:15: 覆盖复制 'E:/lab/RoboQuant/pylab/model/MLP_short.model' -> 'D:/RoboQuant/model/MLP_short.model' (136,674)
2022-01-19 23:37:15: == 同步完成. 历时: 00:00:00, 速度: 0 字节/s, 完成: 20, 错误: 0
2022-01-19 23:37:15: 
