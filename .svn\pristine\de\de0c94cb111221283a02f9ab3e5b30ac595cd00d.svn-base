{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date\n", "import time\n", "import pytz\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "import json\n", "\n", "# sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### FactorKvDB"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class FactorsKvDB():\n", "    def __init__(self,\n", "        # dbfile=\"e:/featdata/kv.db\",\n", "        key_prefix='ffs:', #fsfs 股指期货因子 ffs 商品期货因子\n", "        years=[],\n", "        dbfile=\"d:/RoboQuant2/store/kv.db\",\n", "        save_path=\"e:/featdata\",\n", "        save_file=\"\",\n", "    ) -> None:\n", "        self.years = years\n", "        self._dbfile=dbfile\n", "        self._save_path = save_path\n", "        self._save_file = save_file\n", "        self._db=None\n", "        self._key_prefix=key_prefix\n", "        self._keys=[]\n", "        self._ls_col_names=[]\n", "        self._ct_col_names=[]\n", "        self._tz=pytz.timezone('Asia/Shanghai')\n", "\n", "    def open_db(self, mode):\n", "        if self._db:\n", "            self.close_db()\n", "        \n", "        try:\n", "            self._db=create_db(\"leveldb\", self._dbfile, mode)\n", "        except:\n", "            raise 'Fail to open db!'\n", "        self._ls_col_names, self._ct_col_names = self.get_factors_colnames()\n", "\n", "    def close_db(self):\n", "        if not self._db:\n", "            raise \"not db open.\"\n", "        self._db.close()\n", "        del self._db\n", "        self._db=None\n", "\n", "    def load_all_keys(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        self._keys.clear()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key().decode('gbk', 'ignore')\n", "            if len(key) >= 16 and key[0:len(self._key_prefix)] == self._key_prefix: #'ffs:'\n", "                self._keys.append(key)\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def get_all_labels(self)->set:\n", "        lbs = set()\n", "        if len(self._keys) == 0:\n", "            self.load_all_keys()\n", "            \n", "        for key in self._keys:\n", "            pos0=key.find(':')\n", "            pos1=key.find(':', pos0+1)\n", "            lb=key[pos0+1:pos1]\n", "            lbs.add(lb)\n", "        return lbs\n", "\n", "    def get_factors_colnames(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        ls_colnames = []\n", "        ct_colnames = []\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                value = cursor.value().decode()\n", "                s2 = value.split('|')\n", "                if len(s2) <= 3:\n", "                    print(key)\n", "                    cursor.next()\n", "                    continue\n", "                lf = json.loads(s2[0])\n", "                ls_colnames.append('code')\n", "                ls_colnames.append('date')\n", "                for k, v in lf.items():\n", "                    if isinstance(v, list):\n", "                        ls_colnames.append(f'{k}_1')\n", "                        ls_colnames.append(f'{k}_2')\n", "                    else:\n", "                        ls_colnames.append(f'{k}')\n", "                ls_colnames.append('change')\n", "                if len(s2) >= 4:\n", "                    ct_colnames.append('code')\n", "                    ct_colnames.append('date')\n", "                    ct = json.loads(s2[len(s2) - 2])\n", "                    for k, v in ct.items():\n", "                        if isinstance(v, list):\n", "                            ct_colnames.append(f'{k}_1')\n", "                            ct_colnames.append(f'{k}_2')\n", "                        else:\n", "                            ct_colnames.append(f'{k}')\n", "                del cursor\n", "                return ls_colnames, ct_colnames\n", "            cursor.next()\n", "\n", "    def get_all_factors(self, year: int):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        ldata = []\n", "        sdata = []\n", "        mdata = []\n", "        cdata = []\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) >= 16 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                litem = []\n", "                sitem = []\n", "                mitem = []\n", "                citem = []\n", "                value = cursor.value().decode()\n", "                s1 = key.decode().split(':')\n", "                dt = date.fromtimestamp(int(s1[2]))\n", "                if dt.year != year:\n", "                    cursor.next()\n", "                    continue\n", "                litem.append(s1[1])\n", "                litem.append(int(s1[2]))\n", "                sitem.append(s1[1])\n", "                sitem.append(int(s1[2]))\n", "                mitem.append(s1[1])\n", "                mitem.append(int(s1[2]))\n", "                citem.append(s1[1])\n", "                citem.append(int(s1[2]))\n", "                s2 = value.split('|')\n", "                assert len(s2) >= 4, f'invalid value: {value}'\n", "                lf = json.loads(s2[0])\n", "                sf = json.loads(s2[1])\n", "                if len(s2) == 5:\n", "                    mf = json.loads(s2[2])\n", "                ct = json.loads(s2[len(s2) - 2])\n", "                for _, v in lf.items():\n", "                    if isinstance(v, list):\n", "                        litem.append(v[1])\n", "                        litem.append(v[2])\n", "                    else:\n", "                        litem.append(v)\n", "                for _, v in sf.items():\n", "                    if isinstance(v, list):\n", "                        sitem.append(v[1])\n", "                        sitem.append(v[2])\n", "                    else:\n", "                        sitem.append(v)\n", "                if len(s2) == 5:\n", "                    for _, v in mf.items():\n", "                        if isinstance(v, list):\n", "                            mitem.append(v[1])\n", "                            mitem.append(v[2])\n", "                        else:\n", "                            mitem.append(v)\n", "                    mitem.append(s2[len(s2)-1]) # change\n", "                for _, v in ct.items():\n", "                    if isinstance(v, list):\n", "                        citem.append(v[1])\n", "                        citem.append(v[2])\n", "                    else:\n", "                        citem.append(v)\n", "                litem.append(s2[len(s2)-1]) # change\n", "                sitem.append(s2[len(s2)-1]) # change\n", "                ldata.append(litem)\n", "                sdata.append(sitem)\n", "                mdata.append(mitem)\n", "                cdata.append(citem)\n", "            cursor.next()\n", "        del cursor\n", "        ldf = pd.DataFrame(ldata, columns=self._ls_col_names)\n", "        sdf = pd.DataFrame(sdata, columns=self._ls_col_names)\n", "        mdf = pd.DataFrame(mdata, columns=self._ls_col_names)\n", "        cdf = pd.DataFrame(cdata, columns=self._ct_col_names)\n", "        # 修正Bar的时间,由于有休市和假期,所以时间不连续\n", "        sdf['VOLUME_1'] = sdf['VOLUME_1'] % 21600\n", "        sdf['VOLUME_2'] = sdf['VOLUME_2'] % 21600\n", "        return ldf, sdf, mdf, cdf\n", "    \n", "    def get_single_factors(self, year: int, name: str):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        data = []\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) >= 16 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                item = []\n", "                value = cursor.value().decode()\n", "                s1 = key.decode().split(':')\n", "                dt = date.fromtimestamp(int(s1[2]))\n", "                if dt.year != year:\n", "                    cursor.next()\n", "                    continue\n", "                item.append(s1[1])\n", "                item.append(int(s1[2]))\n", "                s2 = value.split('|')\n", "                assert len(s2) >= 4, f'invalid value: {value}'\n", "                if name == 'lf':\n", "                    lf = json.loads(s2[0])\n", "                    for _, v in lf.items():\n", "                        if isinstance(v, list):\n", "                            item.append(v[1])\n", "                            item.append(v[2])\n", "                        else:\n", "                            item.append(v)\n", "                    item.append(s2[len(s2)-1]) # change\n", "                elif name == 'sf':\n", "                    sf = json.loads(s2[1])\n", "                    for _, v in sf.items():\n", "                        if isinstance(v, list):\n", "                            item.append(v[1])\n", "                            item.append(v[2])\n", "                        else:\n", "                            item.append(v)\n", "                    item.append(s2[len(s2)-1]) # change\n", "                elif name == 'mf':\n", "                    mf = json.loads(s2[2])\n", "                    for _, v in mf.items():\n", "                        if isinstance(v, list):\n", "                            item.append(v[1])\n", "                            item.append(v[2])\n", "                        else:\n", "                            item.append(v)\n", "                    item.append(s2[len(s2)-1]) # change\n", "                elif name == 'ct':\n", "                    ct = json.loads(s2[len(s2)-2])\n", "                    for _, v in ct.items():\n", "                        if isinstance(v, list):\n", "                            item.append(v[1])\n", "                            item.append(v[2])\n", "                        else:\n", "                            item.append(v)\n", "                data.append(item)\n", "            cursor.next()\n", "        del cursor\n", "\n", "        if name == 'lf' or name == 'mf':\n", "            df = pd.DataFrame(data, columns=self._ls_col_names)\n", "        elif name == 'sf':\n", "            df = pd.DataFrame(data, columns=self._ls_col_names)\n", "            # 修正Bar的时间,由于有休市和假期,所以时间不连续\n", "            df['VOLUME_1'] = df['VOLUME_1'] % 21600\n", "            df['VOLUME_2'] = df['VOLUME_2'] % 21600\n", "        elif name == 'ct':\n", "            df = pd.DataFrame(data, columns=self._ct_col_names)\n", "        return df\n", "    \n", "\n", "    def write(self, key, value):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.put(key, value)\n", "        transaction.commit()\n", "        del transaction\n", "        \n", "    def delete(self, key):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        transaction.delete(key)\n", "        transaction.commit()\n", "        del transaction\n", "\n", "    def clear(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        transaction = self._db.new_transaction()\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            key = cursor.key()\n", "            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:\n", "                transaction.delete(key)\n", "                print(f'del key: {key}')\n", "            cursor.next()\n", "        transaction.commit()\n", "        del transaction\n", "        del cursor\n", "\n", "    def query(self):\n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        cursor = self._db.new_cursor()\n", "        while cursor.valid():\n", "            print(cursor.key())\n", "            # print(cursor.key(), cursor.value())\n", "            cursor.next()\n", "        del cursor\n", "\n", "    def export_all(self):\n", "        def trans_timestamp(dt):\n", "            # return int(time.mktime(dt.timetuple()))//300\n", "            return int(dt//300)\n", "\n", "        def log_return(series):\n", "            return np.log(series).diff()\n", "            \n", "        if not self._db:\n", "            raise \"first open a db.\"\n", "        # self.open_db(Mode.read)\n", "        df=self.read_all()\n", "        # self.close_db()\n", "        print(df.shape)\n", "\n", "        try:\n", "            cols=df.columns\n", "            df = df.groupby([\"label\", \"datetime\"]).agg({\"mean\"}).reset_index()\n", "            df.columns = cols\n", "            df[\"time_id\"]=df[\"datetime\"].apply(trans_timestamp)\n", "\n", "            for lb in df[\"label\"].unique():\n", "                # df.to_parquet(f\"../data/tickdata.parquet\", engine='fastparquet')\n", "                df2=df[df[\"label\"]==lb]\n", "                df2.to_parquet(f\"{self._save_path}/tickdata.{lb}.parquet\", engine='fastparquet')\n", "                # df = pd.read_parquet(f\"../data/tickdata.parquet\")\n", "                df2 = df2.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()\n", "                df2['return'] = log_return(df2['price'])\n", "                df2=df2.fillna(0)\n", "                df2['target'] = (df2['return']>0).astype(int)\n", "                df2=df2.drop(['price', 'return'], axis=1)\n", "                df2.to_parquet(f\"{self._save_path}/tickdata_target.{lb}.parquet\", engine='fastparquet')\n", "        except:\n", "            raise 'Fail to export all data!'\n", "\n", "    def export_to_file(self):\n", "        if not self._db:\n", "            self.open_db(Mode.read)\n", "        return\n", "        for year in self.years:\n", "            ldf, sdf, cdf = self.get_all_factors(year)\n", "            print(year, ldf.shape, sdf.shape, cdf.shape)\n", "            ldf.to_parquet(f\"{self._save_path}/ffs_lf.{self._save_file}.{year}.parquet\", engine='fastparquet')\n", "            sdf.to_parquet(f\"{self._save_path}/ffs_sf.{self._save_file}.{year}.parquet\", engine='fastparquet')\n", "            cdf.to_parquet(f\"{self._save_path}/ffs_ct.{self._save_file}.{year}.parquet\", engine='fastparquet')\n", "        self.close_db()\n", "        \n", "    def export_to_file_by_single(self):\n", "        if not self._db:\n", "            self.open_db(Mode.read)\n", "        for year in self.years:\n", "            for name in ['lf', 'sf', 'mf', 'ct']:\n", "                df = self.get_single_factors(year, name)\n", "                print(name, year, df.shape)\n", "                df.to_parquet(f\"{self._save_path}/ffs_{name}.{self._save_file}.{year}.parquet\", engine='fastparquet')\n", "        self.close_db()\n", "                "]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Export"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 直接导出到文件\n", "ff = FactorsKvDB(key_prefix='ffs:', # ffs or fsfs\n", "                 years=[2023],\n", "                 dbfile=\"d:/RoboQuant2/store/kv.db\",\n", "                 save_path=\"e:/featdata\",\n", "                 save_file=\"main\"\n", "                )\n", "\n", "ff.export_to_file()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["lf 2023 (754410, 235)\n", "sf 2023 (754410, 235)\n", "mf 2023 (754410, 235)\n", "ct 2023 (754410, 50)\n"]}], "source": ["# 当数据太大，内存不够时，分别单个类型导出\n", "ff = FactorsKvDB(key_prefix='ffs:', # ffs or fsfs\n", "                 years=[2023],\n", "                 dbfile=\"d:/RoboQuant2/store/kv.db\",\n", "                 save_path=\"e:/featdata\",\n", "                 save_file=\"main.new\"\n", "                )\n", "\n", "ff.export_to_file_by_single()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 先读入到内存，在做处理\n", "ff = FactorsKvDB(key_prefix='ffs:') # ffs or fsfs\n", "ff.open_db(Mode.read)\n", "ldf, sdf, cdf = ff.get_all_factors(year=2023)\n", "ff.close_db()\n", "print(ldf.shape)\n", "print(sdf.shape)\n", "print(cdf.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ldf.to_parquet(f\"{ff._save_path}/ffs_lf.main.2023.parquet\", engine='fastparquet')\n", "sdf.to_parquet(f\"{ff._save_path}/ffs_sf.main.2023.parquet\", engine='fastparquet')\n", "cdf.to_parquet(f\"{ff._save_path}/ffs_ct.main.2023.parquet\", engine='fastparquet')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 合并"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(692041, 235) (61209, 235)\n", "(753250, 235)\n", "(753250, 235)\n", "(692041, 235) (61209, 235)\n", "(753250, 235)\n", "(753250, 235)\n", "(692041, 50) (61209, 50)\n", "(753250, 50)\n", "(753250, 50)\n"]}], "source": ["# 将按月份导出的数据合并成一个文件\n", "import pandas as pd\n", "dtype = ['lf', 'sf', 'ct']\n", "for dt in dtype:\n", "    df = pd.read_parquet(f'e:/featdata/ffs_{dt}.main.2023.parquet')\n", "    dfa = pd.read_parquet(f'e:/featdata/ffs_{dt}.main.10.2023.parquet')\n", "    print(df.shape, dfa.shape)\n", "    df = pd.concat([df,dfa],axis=0)\n", "    print(df.shape)\n", "    df = df.drop_duplicates(subset=['code', 'date'], keep='last')\n", "    print(df.shape)\n", "    df.sort_values(by=['code', 'date'], ascending=True, inplace=True)\n", "    df.reset_index(drop=True, inplace=True)\n", "    df.to_parquet(f'e:/featdata/ffs_{dt}.main.2023.A.parquet', engine='fastparquet')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 增加dayofweek，hour字段"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "years = [2021,2022,2023]\n", "for year in years:\n", "    ct_df = pd.read_parquet(f'e:/featdata/ffs_ct.main.{year}.parquet')\n", "    ct_df['DAYOFWEEK'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.dayofweek\n", "    ct_df['HOUR'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.hour\n", "    ct_df.to_parquet(f'e:/featdata/ffs_ct.main.{year}.parquet', engine='fastparquet')\n", "\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_NATR\", \"FAST_QH_NATR_ZSCORE\", \"FAST_QH_NATR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]\n", "data_path='d:/RoboQuant2/rpt'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ldf = ldf.loc[ldf['RSI_2'] != 0.0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ldf.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sdf = sdf.loc[sdf['RSI_2'] != 0.0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sdf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sdf.describe().to_csv(f'{data_path}/sdf.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cdf = cdf[SEL_CONTEXT_FACTOR_NAMES]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cdf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cdf.dropna(axis=0, how='any', inplace=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nan_count = (cdf.isna().sum(axis=1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print((nan_count>0).sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cdf = cdf[cdf['FAST_QH_NATR_ZSCORE'] != 0.0]\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["##### SF"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "year = 2016\n", "lf_df=pd.read_parquet(f\"e:/featdata/ffs_lf.sf.{year}.parquet\")\n", "sf_df=pd.read_parquet(f\"e:/featdata/ffs_sf.sf.{year}.parquet\")\n", "ct_df=pd.read_parquet(f\"e:/featdata/ffs_ct.sf.{year}.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Clear"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 清除所有历史记录\n", "ff = FactorsKvDB(key_prefix='ffs:')\n", "ff.open_db(Mode.write)\n", "ff.clear()\n", "ff.close_db()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 清除所有历史记录\n", "ff = FactorsKvDB(key_prefix='fsfs:')\n", "ff.open_db(Mode.write)\n", "ff.clear()\n", "ff.close_db()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}