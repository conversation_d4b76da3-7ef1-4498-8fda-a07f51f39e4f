# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
"""
当谈到机器学习中的GRU模型时，我们首先需要了解它是一种递归神经网络
（Recurrent Neural Network，RNN）的变种。RNN是一种能够处理序列数据的神经网络，
它在处理语言、音频、时间序列等任务上非常有效。

GRU代表门控循环单元（Gated Recurrent Unit），它在RNN的基础上进行了改进，
以解决长期依赖问题。长期依赖是指当前时刻的预测或输出受到遥远过去时刻的输入影响的情况。

GRU模型引入了两个关键的门控机制：更新门和重置门。这些门控机制使得GRU模型能够自主
地决定在当前时刻应该从过去时刻保留多少信息，以及应该从当前时刻输入中更新多少信息。
"""

from __future__ import division
from __future__ import print_function

import numpy as np
import pandas as pd
import copy
from qlib.utils import get_or_create_path
from qlib.log import get_module_logger

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, random_split

from .pytorch_utils import count_parameters
from qlib.model.base import Model
from qlib.data.dataset.handler import DataHandlerLP
from qlib.model.utils import ConcatDataset
from qlib.data.dataset.weight import Reweighter


class GRU(Model):
    """GRU Model

    Parameters
    ----------
    d_feat : int
        input dimension for each time step
    metric: str
        the evaluation metric used in early stop
    optimizer : str
        optimizer name
    GPU : str
        the GPU ID(s) used for training
    """

    def __init__(
        self,
        d_feat=6,
        hidden_size=64,
        num_layers=2,
        dropout=0.0,
        n_epochs=200,
        lr=0.001,
        metric="",
        batch_size=512,
        early_stop=20,
        loss="mse",
        optimizer="adam",
        n_jobs=10,
        GPU=0,
        seed=None,
        **kwargs
    ):
        # Set logger.
        self.logger = get_module_logger("GRU")
        self.logger.info("GRU pytorch version...")

        # set hyper-parameters.
        self.d_feat = d_feat
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.n_epochs = n_epochs
        self.lr = lr
        self.metric = metric
        self.batch_size = batch_size
        self.early_stop = early_stop
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.device = torch.device("cuda:%d" % (GPU) if torch.cuda.is_available() and GPU >= 0 else "cpu")
        self.n_jobs = n_jobs
        self.seed = seed

        self.logger.info(
            "GRU parameters setting:"
            "\nd_feat : {}"
            "\nhidden_size : {}"
            "\nnum_layers : {}"
            "\ndropout : {}"
            "\nn_epochs : {}"
            "\nlr : {}"
            "\nmetric : {}"
            "\nbatch_size : {}"
            "\nearly_stop : {}"
            "\noptimizer : {}"
            "\nloss_type : {}"
            "\ndevice : {}"
            "\nn_jobs : {}"
            "\nuse_GPU : {}"
            "\nseed : {}".format(
                d_feat,
                hidden_size,
                num_layers,
                dropout,
                n_epochs,
                lr,
                metric,
                batch_size,
                early_stop,
                optimizer.lower(),
                loss,
                self.device,
                n_jobs,
                self.use_gpu,
                seed,
            )
        )

        if self.seed is not None:
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)

        self.GRU_model = GRUModel2(
            d_feat=self.d_feat,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            dropout=self.dropout,
        )
        self.logger.info("model:\n{:}".format(self.GRU_model))
        self.logger.info("model size: {:.4f} MB".format(count_parameters(self.GRU_model)))

        if optimizer.lower() == "adam":
            self.train_optimizer = optim.Adam(self.GRU_model.parameters(), lr=self.lr)
        elif optimizer.lower() == "gd":
            self.train_optimizer = optim.SGD(self.GRU_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError("optimizer {} is not supported!".format(optimizer))

        self.fitted = False
        self.GRU_model.to(self.device)

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    def mse(self, pred, label, weight):
        loss = weight * (pred - label) ** 2
        return torch.mean(loss)

    def loss_fn(self, pred, label, weight=None):
        mask = ~torch.isnan(label)

        if weight is None:
            weight = torch.ones_like(label)

        if self.loss == "mse":
            return self.mse(pred[mask], label[mask], weight[mask])

        raise ValueError("unknown loss `%s`" % self.loss)

    def metric_fn(self, pred, label):

        mask = torch.isfinite(label)

        if self.metric in ("", "loss"):
            return -self.loss_fn(pred[mask], label[mask])

        raise ValueError("unknown metric `%s`" % self.metric)

    def loader(self, dataset, batch_size=32, shuffle=True):
        # 将数据集划分为训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        return train_dataloader, val_dataloader
    
    def train_epoch(self, data_loader):

        self.GRU_model.train()

        for feature, label, encodeds in data_loader:
            feature, label, encodeds = feature.to(self.device), label.to(self.device), encodeds.to(self.device)

            pred = self.GRU_model(encodeds, feature)
            # loss = self.loss_fn(pred, label, weight.to(self.device))
            loss = self.loss_fn(pred, label)

            self.train_optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_value_(self.GRU_model.parameters(), 3.0)
            self.train_optimizer.step()

    def test_epoch(self, data_loader):

        self.GRU_model.eval()

        losses = []

        for inputs, targets, encodeds in data_loader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            with torch.no_grad():
                pred = self.GRU_model(encodeds, inputs)
                loss = self.loss_fn(pred, targets)
                losses.append(loss.item())

        return np.mean(losses)

    def fit(
        self,
        dataset,
        evals_result=dict(),
        save_path=None,
        reweighter=None,
    ):
        
        x_data, y_data, encoded_data = dataset.prepare(
            ["train", "valid"],
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_loader, valid_loader = self.loader(dataset, self.batch_size)

        save_path = get_or_create_path(save_path)

        stop_steps = 0
        train_loss = 0
        best_loss = np.inf
        best_epoch = 0
        evals_result["train"] = []
        evals_result["valid"] = []

        # train
        self.logger.info("training...")
        self.fitted = True

        for step in range(self.n_epochs):
            self.logger.info("Epoch%d:", step)
            self.logger.info("training...")
            self.train_epoch(train_loader)
            self.logger.info("evaluating...")
            train_loss = self.test_epoch(train_loader)
            val_loss = self.test_epoch(valid_loader)
            self.logger.info("train %.6f, valid %.6f" % (train_loss*10e3, val_loss*10e3))
            evals_result["train"].append(train_loss)
            evals_result["valid"].append(val_loss)

            if val_loss < best_loss:
                best_loss = val_loss

                stop_steps = 0
                best_epoch = step
                best_param = copy.deepcopy(self.GRU_model.state_dict())
            else:
                stop_steps += 1
                if stop_steps >= self.early_stop:
                    self.logger.info("early stop")
                    break

        self.logger.info("best loss: %.6lf @ %d" % (best_loss*10e3, best_epoch))
        self.GRU_model.load_state_dict(best_param)
        # torch.save(best_param, save_path)
        model = self.GRU_model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        sm.save(save_path)

        if self.use_gpu:
            torch.cuda.empty_cache()
        return best_epoch, best_loss

    def predict(self, dataset):
        if not self.fitted:
            raise ValueError("model is not fitted yet!")

        dl_test = dataset.prepare("test", col_set=["feature", "label"], data_key=DataHandlerLP.DK_I)
        dl_test.config(fillna_type="ffill+bfill")
        test_loader = DataLoader(dl_test, batch_size=self.batch_size, num_workers=self.n_jobs)
        self.GRU_model.eval()
        preds = []

        for data in test_loader:

            feature = data[:, :, 0:-1].to(self.device)

            with torch.no_grad():
                pred = self.GRU_model(feature.float()).detach().cpu().numpy()

            preds.append(pred)

        return pd.Series(np.concatenate(preds), index=dl_test.get_index())


class GRUModel(nn.Module):
    def __init__(self, d_feat=6, hidden_size=64, num_layers=2, dropout=0.0):
        super().__init__()

        self.code_embeddings = nn.Embedding(num_embeddings=60, embedding_dim=65)
        self.rnn = nn.GRU(
            input_size=d_feat,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout,
        )
        self.fc_out = nn.Linear(hidden_size, 1)

        self.d_feat = d_feat

    def forward(self, code_ids, x):
        embedded_code_ids = self.code_embeddings(code_ids)
        embedded_code_ids = torch.unsqueeze(embedded_code_ids, dim=1)
        x = torch.cat([embedded_code_ids, x], dim=-1)
        x = x.permute(0, 2, 1)
        
        out, _ = self.rnn(x)
        return self.fc_out(out[:, -1, :]).squeeze()

class GRUModel2(nn.Module):
    def __init__(self, d_feat=6, hidden_size=64, num_layers=2, dropout=0.0, embedding_size=5):
        super().__init__()

        self.code_embeddings = nn.Embedding(num_embeddings=60, embedding_dim=embedding_size)
        self.rnn = nn.GRU(
            input_size=d_feat + embedding_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout,
        )
        self.fc_out = nn.Linear(hidden_size, 1)

        self.d_feat = d_feat

    def forward(self, code_ids, x):
        embedded_data = self.code_embeddings(code_ids)

        # 将嵌入向量序列和其他特征按维度拼接在一起x
        x = torch.cat((embedded_data, x), dim=-1)
       
        out, _ = self.rnn(x)
        return self.fc_out(out[:, -1, :]).squeeze()
