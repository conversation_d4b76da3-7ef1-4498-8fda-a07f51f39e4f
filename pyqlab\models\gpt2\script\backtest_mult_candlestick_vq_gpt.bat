@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\llm\examples

python backtest_mult_candlestick_vq_gpt.py ^
--data_path f:/hqdata/fut_top_min1.parquet ^
--begin_date 2025-04-01 ^
--end_date 2025-12-31 ^
--max_codes 5 ^
--model_path e:/lab/RoboQuant/pylab/checkpoints/candlestick_vq_gpt_cv/ensemble_best_model.onnx ^
--codebook_path E:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250511\vqcb_percent_change_fut_top_min1_051116_0.0068.onnx ^
--vectorization_method percent_change ^
--use_time_features ^
--seq_len 30 ^
--initial_capital 100000 ^
--commission 0.001 ^
--threshold 0.6 ^
--stop_loss 0.05 ^
--take_profit 0.1 ^
--temperature 0.8 ^
--top_k 50 ^
--signal_type topk ^
--leverage 1.0 ^
--print_interval 10 ^
--equal_weight ^
--output_dir e:/lab/RoboQuant/pylab/results/candlestick_vq_gpt_mult_backtest ^
--seed 42

pause
