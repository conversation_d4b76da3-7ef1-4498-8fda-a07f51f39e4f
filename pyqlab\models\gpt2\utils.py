"""
K线LLM模型工具函数

提供用于K线LLM模型的各种工具函数，包括数据准备、可视化和评估
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Dict, Union, Tuple, Optional, Any
from datetime import datetime, timedelta
import torch
import glob
import re
from pathlib import Path
from pyqlab.const import MODEL_FUT_CODES
import matplotlib
matplotlib.rcParams['font.family'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif']

def get_code_id(code: Union[str, int]):
    """获取证券代码ID"""
    try:
        if isinstance(code, str) and len(code) > 2 and code[-2:] != "SH" and code[-2:] != "SZ":
            code_id = MODEL_FUT_CODES.index(code[: -7])
        elif isinstance(code, int):
            code_id = code
        elif code.isdigit():
            code_id = int(code)
    except (ValueError, IndexError) as e:
        print(f"警告: 无法为代码 {code} 获取code_id: {e}，使用默认值0")
        code_id = 0
    return code_id

def extract_time_features(df):
    """提取时间特征"""
    if 'datetime' not in df.columns:
        return None

    # 将datetime转换为pandas datetime
    if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
        df['datetime'] = pd.to_datetime(df['datetime'])

    # 提取时间特征
    features = []
    for dt in df['datetime']:
        # 小时 (0-23)
        hour = dt.hour / 23.0 - 0.5
        # 星期几 (0-6)
        day_of_week = dt.dayofweek / 6.0 - 0.5
        # 月份中的日期 (1-31)
        day_of_month = (dt.day - 1) / 30.0 - 0.5
        # 年份中的日期 (1-366)
        day_of_year = (dt.dayofyear - 1) / 365.0 - 0.5
        # 月份 (1-12)
        month = (dt.month - 1) / 11.0 - 0.5

        features.append([hour, day_of_week, day_of_month, day_of_year, month])

    return np.array(features, dtype=np.float32)

def get_model_name(args):
    """获取训练器名称"""
    # 检查是否有time_encoding属性
    if hasattr(args, 'time_encoding'):
        time_encoding = 'T' if args.time_encoding == 'timeF' else 'F'
    else:
        time_encoding = 'F'  # 默认值

    market = args.market.upper()
    block_name = args.block_name.upper()
    period = args.period.upper()

    # 检查是否有version属性
    version = getattr(args, 'version', 'v1')  # 默认为v1

    trainer_name = f'{market}_{block_name}_{period}_{args.seq_len}_{args.n_head}_{args.n_layer}'
    return trainer_name

def get_data_files(data_dir: str, market: str, block_name: str, period: str) -> List[str]:
    """获取数据文件列表"""
   # 查找数据文件
    data_files = []

    # 根据市场和周期筛选数据文件
    if market.lower() == 'all':
        market_pattern = '*'
    else:
        market_pattern = market.lower()

    # 构建文件匹配模式
    if block_name:
        file_pattern = f"{data_dir}/**/{market_pattern}_{block_name}_{period}*.parquet"
    else:
        file_pattern = f"{data_dir}/**/{market_pattern}_{period}*.parquet"

    # 查找匹配的文件
    data_files.extend(glob.glob(file_pattern, recursive=True))

    # 如果没有找到parquet文件，尝试查找csv文件
    if not data_files:
        if block_name:
            file_pattern = f"{data_dir}/**/{market_pattern}_{block_name}_{period}*.csv"
        else:
            file_pattern = f"{data_dir}/**/{market_pattern}_{period}*.csv"

        data_files.extend(glob.glob(file_pattern, recursive=True))

    if not data_files:
        print(f"错误: 在 {data_dir} 中未找到匹配的数据文件")
        return []

    return data_files

def load_single_data(file_path: str, begin_date: str = None, end_date: str = None):
    """
    从parquet或CSV文件加载K线数据

    Args:
        file_path: 文件路径
        begin_date: 开始日期，格式为'YYYY-MM-DD'
        end_date: 结束日期，格式为'YYYY-MM-DD'
        split_ratio: 训练集和验证集的比例，默认为0.0，表示不进行拆分

    Returns:
        包含OHLCV数据的DataFrame列表
    """
    train_data_list = []
    train_code_ids = []

    # 根据文件扩展名选择读取方法
    if file_path.endswith('.parquet'):
        try:
            df = pd.read_parquet(file_path)
        except Exception as e:
            print(f"读取Parquet文件失败: {e}")
            return None
    elif file_path.endswith('.csv'):
        try:
            df = pd.read_csv(file_path)
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
            return None
    else:
        print(f"不支持的文件格式: {file_path}")
        return None

    # 确保datetime列存在
    if 'datetime' not in df.columns:
        # 尝试查找日期时间列
        date_cols = [col for col in df.columns if 'date' in col.lower() or 'time' in col.lower()]
        if date_cols:
            df.rename(columns={date_cols[0]: 'datetime'}, inplace=True)
        else:
            print(f"警告: {file_path}中未找到日期时间列")
            # 创建一个默认的datetime列
            df['datetime'] = pd.date_range(start='2000-01-01', periods=len(df), freq='D')

    # 确保datetime列是datetime类型
    try:
        df['datetime'] = pd.to_datetime(df['datetime'])
    except:
        try:
            # 尝试使用秒级时间戳
            df['datetime'] = pd.to_datetime(df['datetime'], unit='s')
        except:
            print(f"警告: 无法将datetime列转换为日期时间类型，使用默认值")
            df['datetime'] = pd.date_range(start='2000-01-01', periods=len(df), freq='D')

    # 按日期过滤
    if begin_date:
        df = df[df['datetime'] >= datetime.strptime(begin_date, '%Y-%m-%d')]
    if end_date:
        df = df[df['datetime'] <= datetime.strptime(end_date, '%Y-%m-%d')]

    # 检查是否有code列
    if 'code' in df.columns:
        # 按code分组
        for code, group in df.groupby('code'):
            group.set_index('datetime', inplace=True)
            group.sort_index(inplace=True)
            group.reset_index(inplace=True)
            train_data_list.append(group)

            # 尝试获取code_id
            code_id = get_code_id(code)  # 默认值

            train_code_ids.append(code_id)
    else:
        # 没有code列，使用文件名作为code
        code = os.path.basename(file_path).split('.')[0]
        df.set_index('datetime', inplace=True)
        df.sort_index(inplace=True)
        df.reset_index(inplace=True)
        train_data_list.append(df)

        # 尝试获取code_id
        code_id = 0  # 默认值
        try:
            if len(code) > 2 and code[-2:] != "SH" and code[-2:] != "SZ":
                code_id = MODEL_FUT_CODES.index(code[: -7])
            elif isinstance(code, int):
                code_id = code
            elif code.isdigit():
                code_id = int(code)
        except (ValueError, IndexError) as e:
            print(f"警告: 无法为代码 {code} 获取code_id: {e}，使用默认值0")

        train_code_ids.append(code_id)

    # 检查是否有数据
    if len(train_data_list) == 0:
        print(f"警告: {file_path}中没有有效的数据")
        return None

    return train_data_list, train_code_ids

def generate_mock_data(n_samples=100, n_securities=2, trend='random'):
    """
    生成模拟的K线数据，用于测试和示例

    Args:
        n_samples: 每个证券的样本数量
        n_securities: 证券数量
        trend: 趋势类型，'up', 'down', 或 'random'

    Returns:
        data_list: K线数据列表
        code_ids: 证券代码ID列表
    """
    data_list = []
    code_ids = []

    for i in range(n_securities):
        # 生成日期序列
        dates = [datetime.now() - timedelta(days=n_samples-j) for j in range(n_samples)]

        # 生成价格序列
        prices = []
        price = 100.0 * (i + 1)  # 不同证券有不同的初始价格

        # 设置趋势
        if trend == 'up':
            drift = 0.0005
        elif trend == 'down':
            drift = -0.0005
        else:
            drift = 0.0

        # 生成价格
        for j in range(n_samples):
            # 添加随机波动
            change = np.random.normal(drift, 0.01)
            price *= (1 + change)
            prices.append(price)

        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'close': prices
        })

        # 生成open, high, low
        df['open'] = df['close'].shift(1)
        df.loc[0, 'open'] = df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.01))

        for j in range(len(df)):
            high_range = np.random.uniform(0, 0.02)
            low_range = np.random.uniform(0, 0.02)
            df.loc[j, 'high'] = max(df.loc[j, 'open'], df.loc[j, 'close']) * (1 + high_range)
            df.loc[j, 'low'] = min(df.loc[j, 'open'], df.loc[j, 'close']) * (1 - low_range)

        # 生成交易量
        df['volume'] = np.random.randint(1000, 10000, size=len(df))

        # 添加到列表
        data_list.append(df)
        code_ids.append(i)

    return data_list, code_ids

def plot_candlestick(df: pd.DataFrame, title: str = 'K线图', figsize: Tuple[int, int] = (10, 6)):
    """
    绘制K线图

    Args:
        df: 包含OHLC数据的DataFrame
        title: 图表标题
        figsize: 图表大小
    """
    plt.figure(figsize=figsize)

    for i in range(len(df)):
        x = i
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'

        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)

    plt.title(title)
    plt.grid(True)

    return plt.gcf()

def compare_candlesticks(input_df: pd.DataFrame, predicted_df: pd.DataFrame,
                         title: str = '输入与预测K线对比',
                         save_path: Optional[str] = None):
    """
    比较输入和预测的K线数据

    Args:
        input_df: 输入K线数据
        predicted_df: 预测K线数据
        title: 图表标题
        save_path: 保存路径，如果为None则不保存
    """
    plt.figure(figsize=(12, 6))

    # 绘制输入数据
    plt.subplot(1, 2, 1)
    for i in range(len(input_df)):
        x = i
        open_price = input_df['open'].iloc[i]
        close_price = input_df['close'].iloc[i]
        high_price = input_df['high'].iloc[i]
        low_price = input_df['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'

        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)

    plt.title('输入K线数据')
    plt.grid(True)

    # 绘制预测数据
    plt.subplot(1, 2, 2)
    for i in range(len(predicted_df)):
        x = i
        open_price = predicted_df['open'].iloc[i]
        close_price = predicted_df['close'].iloc[i]
        high_price = predicted_df['high'].iloc[i]
        low_price = predicted_df['low'].iloc[i]
        color = 'red' if close_price >= open_price else 'green'

        # 绘制实体
        plt.plot([x, x], [open_price, close_price], color=color, linewidth=6)
        # 绘制影线
        plt.plot([x, x], [low_price, high_price], color=color, linewidth=1)

    plt.title('预测K线数据')
    plt.grid(True)

    plt.suptitle(title)
    plt.tight_layout()

    if save_path:
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)

    return plt.gcf()

def evaluate_predictions(actual_df: pd.DataFrame, predicted_df: pd.DataFrame) -> Dict[str, float]:
    """
    评估K线预测结果

    Args:
        actual_df: 实际K线数据
        predicted_df: 预测K线数据

    Returns:
        评估指标字典
    """
    # 确保两个DataFrame有相同的长度
    min_len = min(len(actual_df), len(predicted_df))
    actual = actual_df.iloc[:min_len]
    predicted = predicted_df.iloc[:min_len]

    # 计算各种评估指标
    metrics = {}

    # 方向准确率
    actual_direction = (actual['close'] > actual['open']).astype(int)
    predicted_direction = (predicted['close'] > predicted['open']).astype(int)
    metrics['direction_accuracy'] = (actual_direction == predicted_direction).mean()

    # 收盘价RMSE
    metrics['close_rmse'] = np.sqrt(((actual['close'] - predicted['close']) ** 2).mean())

    # 收盘价相对误差
    metrics['close_relative_error'] = (np.abs(actual['close'] - predicted['close']) / actual['close']).mean()

    # 高低价范围误差
    actual_range = actual['high'] - actual['low']
    predicted_range = predicted['high'] - predicted['low']
    metrics['range_error'] = np.abs(actual_range - predicted_range).mean()

    return metrics

def create_model_summary(model, tokenizer, include_params=True):
    """
    创建模型摘要信息

    Args:
        model: CandlestickLLM或AdvancedCandlestickLLM模型
        tokenizer: 对应的tokenizer
        include_params: 是否包含参数数量信息

    Returns:
        摘要信息字符串
    """
    model_type = model.__class__.__name__
    tokenizer_type = tokenizer.__class__.__name__

    # 基本信息
    summary = [
        f"模型类型: {model_type}",
        f"Tokenizer类型: {tokenizer_type}",
        f"词汇表大小: {tokenizer.vocab_size}",
    ]

    # 尝试使用get_model_info方法获取模型信息
    if hasattr(model, 'get_model_info'):
        try:
            model_info = model.get_model_info()
            summary.append(f"最大序列长度: {model_info.get('block_size', 'N/A')}")
            summary.append(f"模型维度: {model_info.get('d_model', 'N/A')}")
            summary.append(f"注意力头数: {model_info.get('n_head', 'N/A')}")
            summary.append(f"Transformer层数: {model_info.get('n_layer', 'N/A')}")

            if include_params:
                summary.extend([
                    f"总参数数量: {model_info.get('total_params', 0):,}",
                    f"非嵌入层参数数量: {model_info.get('non_embedding_params', 0):,}"
                ])
        except Exception as e:
            print(f"获取模型信息时出错: {e}")
            # 回退到旧方法
            summary.append(f"最大序列长度: {model.block_size if hasattr(model, 'block_size') else 'N/A'}")

            if hasattr(model, 'd_model'):
                summary.append(f"模型维度: {model.d_model}")

            if hasattr(model, 'n_head'):
                summary.append(f"注意力头数: {model.n_head}")

            if hasattr(model, 'n_layer'):
                summary.append(f"Transformer层数: {model.n_layer}")

            if include_params:
                total_params = sum(p.numel() for p in model.parameters())
                if hasattr(model, 'get_num_params'):
                    non_embedding_params = model.get_num_params(non_embedding=True)
                else:
                    non_embedding_params = total_params
                summary.extend([
                    f"总参数数量: {total_params:,}",
                    f"非嵌入层参数数量: {non_embedding_params:,}"
                ])
    else:
        # 旧方法
        summary.append(f"最大序列长度: {model.block_size if hasattr(model, 'block_size') else 'N/A'}")

        if hasattr(model, 'd_model'):
            summary.append(f"模型维度: {model.d_model}")

        if hasattr(model, 'n_head'):
            summary.append(f"注意力头数: {model.n_head}")

        if hasattr(model, 'n_layer'):
            summary.append(f"Transformer层数: {model.n_layer}")

        if include_params:
            total_params = sum(p.numel() for p in model.parameters())
            if hasattr(model, 'get_num_params'):
                non_embedding_params = model.get_num_params(non_embedding=True)
            else:
                non_embedding_params = total_params
            summary.extend([
                f"总参数数量: {total_params:,}",
                f"非嵌入层参数数量: {non_embedding_params:,}"
            ])

    # 添加高级模型特有的属性
    if hasattr(model, 'use_multi_task') and model.use_multi_task:
        summary.append("多任务学习: 启用")

    if hasattr(model, 'use_multi_timeframe') and model.use_multi_timeframe:
        summary.append("多时间框架: 启用")

    return "\n".join(summary)

def load_kline_from_parquet_file(
    file_path: str,
    columns: List[str] = None,
    start_date: Union[str, datetime] = None,
    end_date: Union[str, datetime] = None
) -> pd.DataFrame:
    """
    从单个parquet文件加载K线数据

    Args:
        file_path: parquet文件路径
        columns: 需要的列名列表，如果为None则加载所有列
        start_date: 开始日期，格式为'YYYY-MM-DD'或datetime对象
        end_date: 结束日期，格式为'YYYY-MM-DD'或datetime对象

    Returns:
        K线DataFrame
    """
    # 标准化日期格式
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)

    # 确保file_path是绝对路径
    file_path = os.path.abspath(file_path)

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    try:
        # 读取parquet文件
        df = pd.read_parquet(file_path, columns=columns)

        # 确保datetime列存在
        if 'datetime' not in df.columns:
            # 尝试查找日期时间列
            date_cols = [col for col in df.columns if 'date' in col.lower() or 'time' in col.lower()]
            if date_cols:
                df.rename(columns={date_cols[0]: 'datetime'}, inplace=True)
            else:
                print(f"警告: {file_path}中未找到日期时间列")
                # 创建一个默认的datetime列
                df['datetime'] = pd.date_range(start='2000-01-01', periods=len(df), freq='D')

        # 确保datetime列是datetime类型
        df['datetime'] = pd.to_datetime(df['datetime'])

        # 按日期过滤
        if start_date:
            df = df[df['datetime'] >= start_date]
        if end_date:
            df = df[df['datetime'] <= end_date]

        # 确保按时间排序
        df = df.sort_values('datetime')

        # 检查必要的列是否存在
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in df.columns]

        if missing_cols:
            print(f"警告: {file_path}中缺少必要的列: {missing_cols}")
            # 尝试从其他列推断缺失的列
            if 'open' not in df.columns and 'close' in df.columns:
                df['open'] = df['close'].shift(1)
                df.loc[0, 'open'] = df.loc[0, 'close']
            if 'high' not in df.columns and 'close' in df.columns and 'open' in df.columns:
                df['high'] = df[['open', 'close']].max(axis=1)
            if 'low' not in df.columns and 'close' in df.columns and 'open' in df.columns:
                df['low'] = df[['open', 'close']].min(axis=1)

        # 添加volunit和decimal_point字段（如果不存在）
        if 'volunit' not in df.columns:
            df['volunit'] = 100  # 默认交易单位
        if 'decimal_point' not in df.columns:
            df['decimal_point'] = 2  # 默认小数点位数

        return df

    except Exception as e:
        print(f"读取{file_path}时出错: {e}")
        raise

def load_klines_from_parquet(
    data_dir: str,
    symbols: Union[List[str], str] = None,
    start_date: Union[str, datetime] = None,
    end_date: Union[str, datetime] = None,
    timeframe: str = 'daily',
    columns: List[str] = None
) -> Dict[str, pd.DataFrame]:
    """
    从parquet文件加载K线数据

    Args:
        data_dir: 数据目录，包含parquet文件
        symbols: 证券代码列表或单个证券代码，如果为None则加载所有证券
        start_date: 开始日期，格式为'YYYY-MM-DD'或datetime对象
        end_date: 结束日期，格式为'YYYY-MM-DD'或datetime对象
        timeframe: 时间周期，如'daily', '1min', '5min'等
        columns: 需要的列名列表，如果为None则加载所有列

    Returns:
        字典，键为证券代码，值为对应的K线DataFrame
    """
    # 标准化日期格式
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)

    # 确保data_dir是绝对路径
    data_dir = os.path.abspath(data_dir)

    # 根据timeframe确定文件路径模式
    if timeframe == 'daily':
        file_pattern = os.path.join(data_dir, 'daily', '*.parquet')
    else:
        file_pattern = os.path.join(data_dir, f'{timeframe}', '*.parquet')

    # 获取所有匹配的文件
    parquet_files = glob.glob(file_pattern)

    if not parquet_files:
        raise FileNotFoundError(f"在{data_dir}目录下未找到匹配的parquet文件")

    # 如果提供了单个证券代码，转换为列表
    if isinstance(symbols, str):
        symbols = [symbols]

    # 存储结果的字典
    result = {}

    # 处理每个文件
    for file_path in parquet_files:
        # 从文件名中提取证券代码
        file_name = os.path.basename(file_path)
        symbol_match = re.search(r'([A-Za-z0-9]+)\.parquet', file_name)

        if not symbol_match:
            continue

        symbol = symbol_match.group(1)

        # 如果指定了证券代码列表，且当前证券不在列表中，则跳过
        if symbols and symbol not in symbols:
            continue

        try:
            # 使用load_kline_from_parquet_file函数读取parquet文件
            df = load_kline_from_parquet_file(
                file_path=file_path,
                columns=columns,
                start_date=start_date,
                end_date=end_date
            )

            # 添加到结果字典
            result[symbol] = df

        except Exception as e:
            print(f"读取{file_path}时出错: {e}")

    return result

def load_klines_for_training(
    data_dir: str,
    symbols: Union[List[str], str] = None,
    start_date: Union[str, datetime] = None,
    end_date: Union[str, datetime] = None,
    timeframe: str = 'daily',
    train_ratio: float = 0.8,
    min_samples: int = 100
) -> Tuple[List[pd.DataFrame], List[int], List[pd.DataFrame], List[int]]:
    """
    从parquet文件加载K线数据，并分割为训练集和验证集

    Args:
        data_dir: 数据目录，包含parquet文件
        symbols: 证券代码列表或单个证券代码，如果为None则加载所有证券
        start_date: 开始日期，格式为'YYYY-MM-DD'或datetime对象
        end_date: 结束日期，格式为'YYYY-MM-DD'或datetime对象
        timeframe: 时间周期，如'daily', '1min', '5min'等
        train_ratio: 训练集比例，验证集比例为1-train_ratio
        min_samples: 最小样本数量，少于此数量的证券将被忽略

    Returns:
        训练数据列表，训练数据对应的证券代码ID列表，验证数据列表，验证数据对应的证券代码ID列表
    """
    # 加载K线数据
    klines_dict = load_klines_from_parquet(
        data_dir=data_dir,
        symbols=symbols,
        start_date=start_date,
        end_date=end_date,
        timeframe=timeframe
    )

    # 如果没有数据，返回空列表
    if not klines_dict:
        return [], [], [], []

    # 准备训练和验证数据
    train_data = []
    train_code_ids = []
    val_data = []
    val_code_ids = []

    # 为每个证券分配一个唯一的ID
    symbols = list(klines_dict.keys())
    symbol_to_id = {symbol: i for i, symbol in enumerate(symbols)}

    # 处理每个证券的数据
    for symbol, df in klines_dict.items():
        # 如果样本数量不足，跳过
        if len(df) < min_samples:
            print(f"警告: {symbol}的样本数量({len(df)})少于最小要求({min_samples})，已忽略")
            continue

        # 分割数据
        train_size = int(len(df) * train_ratio)

        # 训练集
        train_df = df.iloc[:train_size].copy()
        if len(train_df) >= min_samples:
            train_data.append(train_df)
            train_code_ids.append(symbol_to_id[symbol])

        # 验证集
        val_df = df.iloc[train_size:].copy()
        if len(val_df) >= min_samples // 5:  # 验证集可以更小
            val_data.append(val_df)
            val_code_ids.append(symbol_to_id[symbol])

    return train_data, train_code_ids, val_data, val_code_ids

def load_multi_timeframe_klines(
    data_dir: str,
    symbols: Union[List[str], str] = None,
    start_date: Union[str, datetime] = None,
    end_date: Union[str, datetime] = None,
    timeframes: List[str] = ['daily', '1h', '15min']
) -> Dict[str, Dict[str, pd.DataFrame]]:
    """
    加载多个时间周期的K线数据

    Args:
        data_dir: 数据目录，包含parquet文件
        symbols: 证券代码列表或单个证券代码，如果为None则加载所有证券
        start_date: 开始日期，格式为'YYYY-MM-DD'或datetime对象
        end_date: 结束日期，格式为'YYYY-MM-DD'或datetime对象
        timeframes: 时间周期列表，如['daily', '1h', '15min']

    Returns:
        嵌套字典，第一层键为时间周期，第二层键为证券代码，值为对应的K线DataFrame
    """
    result = {}

    for timeframe in timeframes:
        try:
            timeframe_data = load_klines_from_parquet(
                data_dir=data_dir,
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                timeframe=timeframe
            )

            if timeframe_data:
                result[timeframe] = timeframe_data

        except Exception as e:
            print(f"加载{timeframe}时间周期数据时出错: {e}")

    return result

def apply_model_to_klines(
    model,
    tokenizer,
    klines_dict: Dict[str, pd.DataFrame],
    symbol_to_id: Dict[str, int] = None,
    max_new_tokens: int = 5,
    temperature: float = 0.8,
    top_k: int = 50,
    save_dir: str = None
) -> Dict[str, Dict[str, Any]]:
    """
    将训练好的模型应用于K线数据，生成预测结果

    Args:
        model: CandlestickLLM或AdvancedCandlestickLLM模型
        tokenizer: 对应的tokenizer
        klines_dict: K线数据字典，键为证券代码，值为对应的K线DataFrame
        symbol_to_id: 证券代码到ID的映射字典，如果为None则自动生成
        max_new_tokens: 生成的最大新token数量
        temperature: 温度参数，控制采样的随机性
        top_k: 只考虑概率最高的前k个token
        save_dir: 保存预测结果的目录，如果为None则不保存

    Returns:
        预测结果字典，键为证券代码，值为包含预测DataFrame和评估指标的字典
    """
    # 确保模型处于评估模式
    model.eval()

    # 如果没有提供symbol_to_id，则自动生成
    if symbol_to_id is None:
        symbols = list(klines_dict.keys())
        symbol_to_id = {symbol: i for i, symbol in enumerate(symbols)}

    # 存储结果的字典
    results = {}

    # 处理每个证券的数据
    for symbol, df in klines_dict.items():
        try:
            # 确保数据足够长
            if len(df) < 30:
                print(f"警告: {symbol}的样本数量({len(df)})不足，已忽略")
                continue

            # 获取证券ID
            if symbol not in symbol_to_id:
                print(f"警告: {symbol}不在symbol_to_id中，已忽略")
                continue

            code_id = symbol_to_id[symbol]

            # 使用最后30个样本作为输入
            input_df = df.iloc[-30:].copy()

            # 生成预测
            with torch.no_grad():
                # 将输入数据转换为token
                input_tokens = torch.tensor(tokenizer.tokenize(input_df)).unsqueeze(0)
                code_ids = torch.tensor([code_id])

                # 生成预测
                generated_tokens = model.generate(
                    input_tokens=input_tokens,
                    code_ids=code_ids,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    top_k=top_k
                )

                # 提取生成的token
                generated_tokens = generated_tokens[0, -max_new_tokens:].cpu().numpy().tolist()

                # 获取最后一个K线的收盘价和ATR，用于还原K线
                last_close = input_df['close'].iloc[-1]
                atr = tokenizer._calculate_atr(input_df).iloc[-1]

                # 将token转换为K线数据
                if hasattr(tokenizer, 'tokens_to_candlesticks'):
                    predicted_df = tokenizer.tokens_to_candlesticks(
                        generated_tokens,
                        start_price=last_close,
                        atr=atr
                    )
                else:
                    # 如果tokenizer没有实现tokens_to_candlesticks方法，返回原始token
                    predicted_df = pd.DataFrame({'token': generated_tokens})

            # 如果需要保存结果
            if save_dir:
                os.makedirs(save_dir, exist_ok=True)

                # 保存预测结果
                predicted_df.to_csv(os.path.join(save_dir, f'{symbol}_prediction.csv'), index=False)

                # 可视化预测结果
                compare_candlesticks(
                    input_df=input_df.iloc[-10:],  # 只显示最后10个样本
                    predicted_df=predicted_df,
                    title=f'{symbol}预测结果',
                    save_path=os.path.join(save_dir, f'{symbol}_prediction.png')
                )

            # 计算评估指标
            # 注意：这里我们没有真实的未来数据，所以不能计算真实的评估指标
            # 这里只是演示如何使用评估函数
            metrics = {
                'predicted_tokens': generated_tokens,
                'predicted_df': predicted_df
            }

            # 添加到结果字典
            results[symbol] = metrics

        except Exception as e:
            print(f"处理{symbol}时出错: {e}")

    return results

def save_model_info(model, tokenizer, save_dir):
    """
    保存模型信息到文件

    Args:
        model: CandlestickLLM或AdvancedCandlestickLLM模型
        tokenizer: 对应的tokenizer
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)

    # 创建模型摘要
    summary = create_model_summary(model, tokenizer)

    # 保存到文件
    with open(os.path.join(save_dir, 'model_info.txt'), 'w', encoding='utf-8') as f:
        f.write(summary)

    # 保存模型架构图
    try:
        from torchviz import make_dot

        # 创建一个示例输入
        batch_size = 2
        seq_len = 10
        device = next(model.parameters()).device

        dummy_input = torch.randint(0, tokenizer.vocab_size, (batch_size, seq_len)).to(device)
        dummy_code_ids = torch.zeros(batch_size, dtype=torch.long).to(device)

        # 前向传播
        if hasattr(model, 'use_multi_task') and model.use_multi_task:
            outputs = model(dummy_input, dummy_code_ids)
        else:
            outputs = model(dummy_input, dummy_code_ids)

        # 创建计算图
        if isinstance(outputs, tuple):
            dot = make_dot(outputs[0], params=dict(model.named_parameters()))
        else:
            dot = make_dot(outputs, params=dict(model.named_parameters()))

        # 保存图
        dot.render(os.path.join(save_dir, 'model_architecture'), format='png')
    except Exception as e:
        print(f"无法生成模型架构图: {e}")
