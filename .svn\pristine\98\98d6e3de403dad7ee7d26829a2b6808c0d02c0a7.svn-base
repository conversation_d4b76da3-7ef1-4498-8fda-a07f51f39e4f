import threading
import pandas as pd
import time
from pyqlab.net.message import Message
from pyqlab.net.server_socket_utils import ServerSocketUtils
from pyqlab.const import MARKET_CODE_SET, MAIN_FUT_MARKET_CODES, MAIN_SEL_FUT_MARKET_CODES, SF_MARKET_FUT_CODES

import threading
import os
import struct
from datetime import datetime

MAIN_FUT = ['CU9999.SC', 'AL9999.SC', 'ZN9999.SC', 'PB9999.SC', 'NI9999.SC', 'SN9999.SC', 'AU9999.SC', 'AG9999.SC', 'RB9999.SC', 'HC9999.SC', 'BU9999.SC', 'RU9999.SC', 'FU9999.SC', 'SP9999.SC', 'WR9999.SC', 'SC9999.SC', 'NR9999.SC', 'SS9999.SC',
    'SR9999.ZC', 'CF9999.ZC', 'ZC9999.ZC', 'FG9999.ZC', 'TA9999.ZC', 'MA9999.ZC', 'OI9999.ZC', 'RM9999.ZC', 'CY9999.ZC', 'WH9999.ZC', 'PM9999.ZC', 'RI9999.ZC', 'LR9999.ZC', 'JR9999.ZC', 'RS9999.ZC', 'SF9999.ZC', 'SM9999.ZC', 'AP9999.ZC', 'CJ9999.ZC', 'UR9999.ZC', 'SA9999.ZC', 'PF9999.ZC', 'PK9999.ZC', 
    'M9999.DC', 'Y9999.DC', 'A9999.DC', 'B9999.DC', 'P9999.DC', 'J9999.DC', 'JM9999.DC', 'I9999.DC', 'RR9999.DC', 'C9999.DC', 'CS9999.DC', 'JD9999.DC', 'BB9999.DC', 'FB9999.DC', 'L9999.DC', 'V9999.DC', 'PP9999.DC', 'EG9999.DC', 'EB9999.DC', 'PG9999.DC', 'LH9999.DC']

MAIN_SEL_FUT = ['AG9999.SC', 'RB9999.SC', 'HC9999.SC', 'BU9999.SC', 'RU9999.SC', 'SP9999.SC', 'SC9999.SC', 'SS9999.SC',
    'SR9999.ZC', 'CF9999.ZC', 'FG9999.ZC', 'TA9999.ZC', 'MA9999.ZC', 'OI9999.ZC', 'RM9999.ZC', 'CY9999.ZC', 'SF9999.ZC', 'SM9999.ZC', 'AP9999.ZC', 'UR9999.ZC', 'SA9999.ZC', 'PK9999.ZC', 
    'M9999.DC', 'Y9999.DC', 'A9999.DC', 'P9999.DC', 'JM9999.DC', 'I9999.DC', 'C9999.DC', 'CS9999.DC', 'L9999.DC', 'V9999.DC', 'PP9999.DC', 'EG9999.DC', 'EB9999.DC']

MAIN_SF_FUT = ['IF9999.SF', 'IH9999.SF', 'IC9999.SF', 'IM9999.SF']

class WorkerThread(threading.Thread):
    def __init__(self, func):
        super().__init__()
        self._stop_event = threading.Event()  # 用于暂停线程的事件对象
        self._paused_event = threading.Event()  # 用于通知线程已暂停的事件对象
        self._paused_event.set()  # 初始状态下线程为暂停状态
        self.func = func

    def run(self):
        while not self._stop_event.is_set():
            self._paused_event.wait()  # 等待线程被重启
            
            # 线程主体逻辑
            self.func()

    def stop(self):
        self._stop_event.set()  # 设置停止事件

    def pause(self):
        self._paused_event.clear()  # 清除暂停事件

    def resume(self):
        self._paused_event.set()  # 设置暂停事件

class SimulationSvr(ServerSocketUtils):

    def __init__(self, ip: str, port: int,
                  data_path: str, start_date: str, end_date: str,
                  fn_pref = "", fn_suff = "", sel_fut = []
                ) -> None:
        super().__init__(ip, port)
        self.data_path = data_path
        self.start_date = start_date
        self.end_date = end_date
        self.last_datetime = 0
        self.wk_thread = None
        self.wk_status = 'stop'
        self.step_num = 0
        self.cur_dt = 0
        self.count = 0
        self.date_segments = None
        self.fn_pref = fn_pref
        self.fn_suff = fn_suff
        self.sel_fut = sel_fut

    def start(self):
        '''
        启动行情服务
        '''
        super().start_server()

    def generate_yyyymm(self):
        yyyymm = []
        year0 = int(self.start_date[:4])
        year1 = int(self.end_date[:4])
        month0 = int(self.start_date[4:6])
        month1 = int(self.end_date[4:6])
        for y in range(year0, year1+1):
            for m in range(1, 13):
                if y == year0 and y == year1:
                    if m >= month0 and m <= month1:
                        yyyymm.append(f'{y*100 + m}')                    
                elif y == year0:
                    if m >= month0:
                        yyyymm.append(f'{y*100 + m}')
                elif y == year1:
                    if m <= month1:
                        yyyymm.append(f'{y*100 + m}')
                elif y > year0 and y < year1:
                    yyyymm.append(f'{y*100 + m}')
        return yyyymm

    def handle_disconnect(self):
        if self.wk_thread:
            self.wk_thread.stop()
            self.wk_status = 'stop'
    
    def handle_data(self, msg: Message):
        '''
        接收客户端数据Message
        '''
        body = str(msg.body.decode()) # 'ascii', 'ignore'
        # print(f"recv msg: {body}")
        if body.count(':') == 3 and body[:6] == 'start:':
            # 解析传递的日期参数
            body = body.split(':')
            self.start_date = body[1]
            self.end_date = body[2]
            self.last_datetime = int(body[3])
            self.date_segments = self.generate_yyyymm()
            print(self.date_segments)
            body = body[0]
            self.step_num = 0
        if body == 'start' and self.wk_status == 'stop':
            self.count = 0
            self.wk_thread = WorkerThread(self.work_func)
            self.wk_thread.start()
            self.wk_status = 'running'
        elif body == 'pause' and self.wk_status != 'stop':
            self.wk_thread.pause()
            self.wk_status = 'pause'
        elif body == 'resume' and self.wk_status != 'stop':
            self.wk_thread.resume()
            self.wk_status = 'running'
        elif body == 'stop':
            self.wk_thread.stop()
            self.wk_thread.join(timeout=1)
            self.wk_status = 'stop'
        else:
            self.send_sync_time(-1, self.count)
            print(f"未处理的消息：{body}")

    def send_sync_time(self, dt, count):
        # 时间同步包
        msg = Message()
        msg.command = 2
        msg.business_type = 2 # 时间
        msg.app_id = 1
        msg.body = struct.pack('=iI', dt, count)
        self.send_all(msg.package_msg_bytes())

    def send_tick_data(self, dt, data):
        # 行情数据包
        msg = Message()
        msg.command = 2
        msg.business_type = 1 # 行情
        msg.app_id = 1
        msg.body = struct.pack('=i2f', dt,
                                float(data['price']),
                                float(data['volume']),
                                ) + data['code'].encode('ascii')
        self.send_all(msg.package_msg_bytes())

    def work_func(self):
        # 创建一个线程来发送tick行情数据
        # 每发完一秒的数据，就与客户端同步一次时间
        # 等待客户端的应答后，再继续发送下一秒的数据
        print('start load tick data...')
        yyyymm = self.date_segments[self.step_num]
        if self.last_datetime > 0 and self.step_num < len(self.date_segments):
            ldt = datetime.fromtimestamp(self.last_datetime)
            ltdt = ldt.year * 100 + ldt.month
            if ltdt > int(yyyymm):
                print(ltdt, yyyymm)
                self.step_num += 1
                return
        filename = f'{self.data_path}/{yyyymm[:4]}/{self.fn_pref}{yyyymm}{self.fn_suff}.parquet'
        if not os.path.isfile(filename):
            print(f"{filename} isn't exist.")
            self.step_num += 1
            return
        df = pd.read_parquet(filename)
        if len(self.sel_fut) > 0:
            df = df[df['code'].isin(self.sel_fut)]
        print(yyyymm, df.shape)

        self.cur_dt = 0
        cur_count = 0
        if df.shape[0] > 0:
            for _, row in df.iterrows():
                if self.wk_status == "stop":
                    break
                self.wk_thread._paused_event.wait()  # 等待线程被重启
                if row['datetime'].hour < 9: # or row['datetime'].hour >= 23:
                    continue
                dt = int(time.mktime(row['datetime'].timetuple()))
                if self.last_datetime > 0 and dt < self.last_datetime:
                    continue
                self.send_tick_data(dt, row)
                self.count += 1
                if dt != self.cur_dt:
                    self.send_sync_time(dt, self.count)
                    self.cur_dt = dt
                    time.sleep(0.0001)
                if self.count // 10000 > cur_count:
                    cur_count = self.count // 10000
                    print(f"time: {row['datetime']}  {self.count}")
        self.step_num += 1
        
        print(f'===Running: {self.step_num}/{len(self.date_segments)}===')
        if self.step_num >= len(self.date_segments):
            self.send_sync_time(-1, self.count)
            self.wk_thread.stop()
            self.wk_status = 'stop'


if __name__ == '__main__':
    ss = SimulationSvr('127.0.0.1', 10005,
                       'e:/hqdata/tick', '20190801', '20200731',
                       fn_pref="",
                       fn_suff=".sec", #.sec
                       sel_fut=MAIN_FUT
                    )
    ss.start()
    # while True:
    #     user_input = input("请输入内容，输入'exit'退出程序: ")
    #     if user_input == "exit":
    #         break