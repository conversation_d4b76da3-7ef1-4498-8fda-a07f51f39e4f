#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据集划分功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, Subset

# 导入自定义模块
from pyqlab.models.gpt2.train_candlestick_vq_gpt import split_dataset

class MockDataset(Dataset):
    """模拟数据集用于测试"""
    def __init__(self, size=1000):
        self.size = size
        # 创建模拟的时间序列数据
        self.data = []
        for i in range(size):
            # 模拟时间戳
            timestamp = pd.Timestamp('2023-01-01') + pd.Timedelta(minutes=i)
            # 模拟数据
            sample = {
                'timestamp': timestamp,
                'value': i,  # 简单的递增值，便于验证时间顺序
                'random_value': np.random.random()
            }
            self.data.append(sample)
    
    def __len__(self):
        return self.size
    
    def __getitem__(self, idx):
        return self.data[idx]

def test_time_ordered_split():
    """测试按时间顺序划分"""
    print("=== 测试按时间顺序划分 ===")
    
    # 创建模拟数据集
    dataset = MockDataset(1000)
    val_ratio = 0.2
    seed = 42
    
    # 按时间顺序划分
    train_dataset, val_dataset = split_dataset(dataset, val_ratio, seed, shuffle=False)
    
    print(f"总样本数: {len(dataset)}")
    print(f"训练集样本数: {len(train_dataset)}")
    print(f"验证集样本数: {len(val_dataset)}")
    
    # 验证划分结果
    expected_val_size = int(len(dataset) * val_ratio)
    expected_train_size = len(dataset) - expected_val_size
    
    assert len(train_dataset) == expected_train_size, f"训练集大小不正确: {len(train_dataset)} != {expected_train_size}"
    assert len(val_dataset) == expected_val_size, f"验证集大小不正确: {len(val_dataset)} != {expected_val_size}"
    
    # 验证时间顺序：训练集应该是前面的数据，验证集应该是后面的数据
    train_indices = train_dataset.indices
    val_indices = val_dataset.indices
    
    print(f"训练集索引范围: {min(train_indices)} - {max(train_indices)}")
    print(f"验证集索引范围: {min(val_indices)} - {max(val_indices)}")
    
    # 验证训练集是连续的前面部分
    assert train_indices == list(range(expected_train_size)), "训练集索引不是连续的前面部分"
    
    # 验证验证集是连续的后面部分
    assert val_indices == list(range(expected_train_size, len(dataset))), "验证集索引不是连续的后面部分"
    
    # 验证数据的时间顺序
    train_values = [train_dataset[i]['value'] for i in range(min(10, len(train_dataset)))]
    val_values = [val_dataset[i]['value'] for i in range(min(10, len(val_dataset)))]
    
    print(f"训练集前10个值: {train_values}")
    print(f"验证集前10个值: {val_values}")
    
    # 训练集的值应该小于验证集的值（因为是时间顺序）
    assert max(train_values) < min(val_values), "时间顺序划分不正确"
    
    print("✅ 按时间顺序划分测试通过！\n")

def test_random_split():
    """测试随机划分"""
    print("=== 测试随机划分 ===")
    
    # 创建模拟数据集
    dataset = MockDataset(1000)
    val_ratio = 0.2
    seed = 42
    
    # 随机划分
    train_dataset, val_dataset = split_dataset(dataset, val_ratio, seed, shuffle=True)
    
    print(f"总样本数: {len(dataset)}")
    print(f"训练集样本数: {len(train_dataset)}")
    print(f"验证集样本数: {len(val_dataset)}")
    
    # 验证划分结果
    expected_val_size = int(len(dataset) * val_ratio)
    expected_train_size = len(dataset) - expected_val_size
    
    assert len(train_dataset) == expected_train_size, f"训练集大小不正确: {len(train_dataset)} != {expected_train_size}"
    assert len(val_dataset) == expected_val_size, f"验证集大小不正确: {len(val_dataset)} != {expected_val_size}"
    
    # 验证随机性：索引应该不是连续的
    train_indices = train_dataset.indices
    val_indices = val_dataset.indices
    
    print(f"训练集索引范围: {min(train_indices)} - {max(train_indices)}")
    print(f"验证集索引范围: {min(val_indices)} - {max(val_indices)}")
    print(f"训练集前10个索引: {sorted(train_indices)[:10]}")
    print(f"验证集前10个索引: {sorted(val_indices)[:10]}")
    
    # 验证索引不重复
    all_indices = set(train_indices) | set(val_indices)
    assert len(all_indices) == len(dataset), "索引有重复或遗漏"
    
    # 验证索引覆盖了所有数据
    assert all_indices == set(range(len(dataset))), "索引没有覆盖所有数据"
    
    # 验证随机性：训练集索引不应该是连续的前面部分
    is_consecutive = train_indices == list(range(expected_train_size))
    assert not is_consecutive, "随机划分结果仍然是连续的，可能随机化失败"
    
    # 验证数据的随机性
    train_values = [dataset[i]['value'] for i in sorted(train_indices)[:10]]
    val_values = [dataset[i]['value'] for i in sorted(val_indices)[:10]]
    
    print(f"训练集前10个值（按索引排序）: {train_values}")
    print(f"验证集前10个值（按索引排序）: {val_values}")
    
    print("✅ 随机划分测试通过！\n")

def test_reproducibility():
    """测试随机划分的可重现性"""
    print("=== 测试随机划分的可重现性 ===")
    
    # 创建模拟数据集
    dataset = MockDataset(1000)
    val_ratio = 0.2
    seed = 42
    
    # 第一次随机划分
    train_dataset1, val_dataset1 = split_dataset(dataset, val_ratio, seed, shuffle=True)
    train_indices1 = train_dataset1.indices
    val_indices1 = val_dataset1.indices
    
    # 第二次随机划分（相同种子）
    train_dataset2, val_dataset2 = split_dataset(dataset, val_ratio, seed, shuffle=True)
    train_indices2 = train_dataset2.indices
    val_indices2 = val_dataset2.indices
    
    # 验证结果相同
    assert train_indices1 == train_indices2, "相同种子的随机划分结果不一致"
    assert val_indices1 == val_indices2, "相同种子的随机划分结果不一致"
    
    print("✅ 随机划分可重现性测试通过！\n")
    
    # 测试不同种子产生不同结果
    print("=== 测试不同种子产生不同结果 ===")
    
    # 使用不同种子
    train_dataset3, val_dataset3 = split_dataset(dataset, val_ratio, 123, shuffle=True)
    train_indices3 = train_dataset3.indices
    val_indices3 = val_dataset3.indices
    
    # 验证结果不同
    assert train_indices1 != train_indices3, "不同种子的随机划分结果相同，可能随机化失败"
    assert val_indices1 != val_indices3, "不同种子的随机划分结果相同，可能随机化失败"
    
    print("✅ 不同种子产生不同结果测试通过！\n")

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    # 测试小数据集
    small_dataset = MockDataset(10)
    train_dataset, val_dataset = split_dataset(small_dataset, 0.2, 42, shuffle=True)
    
    print(f"小数据集 - 总样本数: {len(small_dataset)}")
    print(f"小数据集 - 训练集样本数: {len(train_dataset)}")
    print(f"小数据集 - 验证集样本数: {len(val_dataset)}")
    
    assert len(train_dataset) + len(val_dataset) == len(small_dataset), "小数据集划分总数不正确"
    
    # 测试极端比例
    train_dataset, val_dataset = split_dataset(small_dataset, 0.9, 42, shuffle=True)
    
    print(f"极端比例 - 训练集样本数: {len(train_dataset)}")
    print(f"极端比例 - 验证集样本数: {len(val_dataset)}")
    
    assert len(train_dataset) + len(val_dataset) == len(small_dataset), "极端比例划分总数不正确"
    
    print("✅ 边界情况测试通过！\n")

def main():
    """主测试函数"""
    print("开始测试数据集划分功能...\n")
    
    test_time_ordered_split()
    test_random_split()
    test_reproducibility()
    test_edge_cases()
    
    print("🎉 所有测试通过！数据集划分功能工作正常。")
    print("\n使用说明：")
    print("- 默认情况下使用时间顺序划分（shuffle=False）")
    print("- 使用 --shuffle_split 参数启用随机划分")
    print("- 使用 --seed 参数控制随机种子以确保可重现性")

if __name__ == "__main__":
    main()
