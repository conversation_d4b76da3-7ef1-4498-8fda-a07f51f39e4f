
import torch
import torch.utils.data as data
from sklearn.model_selection import train_test_split
from sklearn.model_selection import KFold

class FactorSeriesData(data.Dataset):
    def __init__(self,
                 train=True,
                 x_data=None,
                 y_data=None,
                 embedding_data=None,
                 kfold=0,
                 kfold_idx=0):
        assert x_data is not None and len(x_data) > 0
        assert len(x_data) == len(y_data) and len(x_data) == len(embedding_data)
        self.train = train
        self.x_data = x_data
        self.y_data = y_data
        self.embedding_data = embedding_data
        self.kfold = kfold
        self.kfold_idx = kfold_idx
        self.setup()

    def setup(self):

        self.x_data = torch.tensor(self.x_data)
        self.y_data = torch.tensor(self.y_data)
        self.embedding_data = torch.tensor(self.embedding_data)
        # print(f"FactorSeriesData: {self.x_data.shape} {self.y_data.shape} {self.embedding_data.shape}")
        if self.kfold != 0:
            kf = KFold(n_splits=self.kfold, shuffle=True, random_state=42)
            train_index, val_index = list(kf.split(self.x_data))[self.kfold_idx]
            self.x_data = self.x_data[train_index] if self.train else self.x_data[val_index]
            self.y_data = self.y_data[train_index] if self.train else self.y_data[val_index]
            self.embedding_data = self.embedding_data[train_index] if self.train else self.embedding_data[val_index]
        else:
            train_size = int(0.8 * len(self.x_data))
            val_size = len(self.x_data) - train_size
            self.x_data, x_data_val, self.y_data, y_data_val, self.embedding_data, embedding_data_val = train_test_split(
                self.x_data, self.y_data, self.embedding_data, test_size=val_size, random_state=42)
            self.x_data = self.x_data if self.train else x_data_val
            self.y_data = self.y_data if self.train else y_data_val
            self.embedding_data = self.embedding_data if self.train else embedding_data_val
        '''
        t_dataset = data.TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))
        if self.kfold != 0:
            kf = KFold(n_splits=self.kfold, shuffle=True, random_state=42)
            train_index, val_index = list(kf.split(t_dataset))[self.fold_num]
            train_ds = data.Subset(dataset, train_index)
            val_ds = data.Subset(dataset, val_index)
        else:
            train_size = int(0.8 * len(t_dataset))
            val_size = len(dataset) - train_size
            train_ds, val_ds = data.random_split(t_dataset, [train_size, val_size])

        self.data = train_ds if self.train else val_ds
        
        print(f"FactorSeriesData: {self.ds_name} {self.direct} x_data={x_data.shape} y_data={y_data.shape} encoded_data={encoded_data.shape}")
        '''
    def __len__(self):
        return len(self.y_data)

    def __getitem__(self, idx):
        return self.x_data[idx], self.y_data[idx], self.embedding_data[idx]