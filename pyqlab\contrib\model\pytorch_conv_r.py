# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8
"""
markdays: 当模型训练与验证损失曲线之间呈喇叭口分开，是什么原因，如何优化

GitHub Copilot: 当模型的训练损失和验证损失曲线之间呈现"喇叭口"分开的现象，这通常是过拟合的表现。过拟合是指模型在训练数据上表现良好，但在未见过的数据（如验证数据）上表现较差。

优化方法包括：

1. 增加数据集的大小：这可以通过数据增强，或者收集更多的数据来实现。
2. 使用正则化技术：如L1和L2正则化，它们可以在损失函数中添加一个惩罚项，以减少模型复杂度。
3. 使用dropout：在训练过程中随机关闭一部分神经元，可以有效防止过拟合。
4. 早停：当验证损失不再显著下降时，停止训练。
5. 使用更简单的模型：减少模型的复杂度，例如减少神经网络的层数或每层的神经元数量。
"""

from __future__ import division
from __future__ import print_function

import numpy as np
import pandas as pd
from typing import Text, Union
import copy
import math
from qlib.utils import get_or_create_path
from qlib.log import get_module_logger, set_log_with_config

import torch
import torch.nn as nn
import torch.optim as optim
# from torch.nn.utils import weight_norm
from torch.optim import lr_scheduler
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset, Subset, random_split
from sklearn.model_selection import KFold
from .pytorch_utils import AdaptiveLayyer
from qlib.model.base import Model
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES
import logging

class ConvModelPytorch(Model):
    """CONV Model

    Parameters
    ----------
    d_feat : int
        input dimension for each time step
    n_chans: int
        number of channels
    metric: str
        the evaluate metric used in early stop
    optimizer : str
        optimizer name
    GPU : str
        the GPU ID(s) used for training
    """

    def __init__(
        self,
        num_embeds=[72],
        num_channel=5,
        num_input=51, #87
        out_channels=(32, 64, 1152, 256),
        ins_nums=(0,51,51,8),
        dropout=0.5,
        max_epochs=200,
        activation="relu",
        pooling="max",
        lr=0.0001,
        lr_decay=0.1,
        lr_decay_steps=10,
        metric="",
        batch_size=64,
        k_folds=5,
        early_stop=30,
        min_delta=0.0001,
        loss="mse",
        optimizer="adam",
        GPU=0,
        seed=None,
        best_cond="loss",
        version="2DR",
        best_model_path=None,
        **kwargs
    ):
        # Set logger.
        logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
        self.logger = logging.getLogger(__name__)
        self.logger.info("======================")

        # set hyper-parameters.
        self.num_embeds = num_embeds
        self.num_channel = num_channel
        self.num_input = num_input
        self.out_channels = out_channels
        self.dropout = dropout
        self.activation = activation
        self.pooling = pooling
        self.max_epochs = max_epochs
        self.lr = lr
        self.lr_decay = lr_decay
        self.lr_decay_steps = lr_decay_steps
        self.metric = metric
        self.batch_size = batch_size
        self.k_folds = k_folds
        self.early_stop = early_stop
        self.min_delta = min_delta
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.device = torch.device("cuda:%d" % (GPU) if torch.cuda.is_available() and GPU >= 0 else "cpu")
        self.seed = seed
        self.best_cond = best_cond
        self.version = version
        self.ins_nums = ins_nums
        self.best_model_path = best_model_path

        self.logger.info(
            "parameters setting:"
            "\nnum_embeds : {}"
            "\nnum_input : {}"
            "\nout_channels : {}"
            "\nins_nums : {}"
            "\ndropout : {}"
            "\nactivation : {}"
            "\npooling : {}"
            "\nmax_epochs : {}"
            "\nlr : {}"
            "\nmetric : {}"
            "\nbatch_size : {}"
            "\nkfold : {}"
            "\nearly_stop : {}"
            "\nmin_delta : {}"
            "\noptimizer : {}"
            "\nloss_type : {}"
            "\nseed : {}"
            "\nbest_cond : {}".format(
                num_embeds,
                num_input,
                out_channels,
                ins_nums,
                dropout,
                activation,
                pooling,
                max_epochs,
                lr,
                metric,
                batch_size,
                k_folds,
                early_stop,
                min_delta,
                optimizer.lower(),
                loss,
                seed,
                best_cond,
            )
        )

        if self.seed is not None:
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)

        if self.version == "CV1DR":
            self.conv_model = TimeSeriesModel1D(
                num_embeds=self.num_embeds,
                num_input=self.num_input,
                num_channel=self.num_channel,
                out_channels=self.out_channels,
                dropout=self.dropout,
                activation=self.activation,
                pooling=self.pooling,
            )
        elif self.version == "CV2DR":
            self.conv_model = TimeSeriesModel2D(
                num_embeds=self.num_embeds,
                num_input=self.num_input,
                num_channel=self.num_channel,
                out_channels=self.out_channels,
                dropout=self.dropout,
                ins_nums=self.ins_nums,
                activation=self.activation,
                pooling=self.pooling,
            )
        else:
            raise ValueError("unknown version `%s`" % self.version)
        

        # 将之前的训练模型加载到新模型中，如果有的话，否则不加载
        if self.best_model_path is not None:
            self.logger.info("load model from %s" % self.best_model_path)
            compiled_model = torch.jit.load(self.best_model_path)
            embedding = torch.zeros(1, self.num_channel, len(self.num_embeds)).to(torch.int32)
            example_input = torch.rand(1, self.num_channel, self.num_input)
            self.conv_model = torch.jit.trace(compiled_model, (embedding, example_input))
        
        self.logger.info("model:\n{:}".format(self.conv_model))
        # self.logger.info("model size: {:.4f} MB".format(count_parameters(self.conv_model)))

        if optimizer.lower() == "adam":
            # 初始化优化器，添加L2正则化
            self.train_optimizer = optim.Adam(self.conv_model.parameters(), lr=self.lr) # weight_decay=0.01
        elif optimizer.lower() == "gd":
            self.train_optimizer = optim.SGD(self.conv_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError("optimizer {} is not supported!".format(optimizer))

        self.fitted = False
        self.conv_model.to(self.device)

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    def mse(self, pred, label):
        loss = (pred - label) ** 2
        return torch.mean(loss)

    def loss_fn(self, pred, label):
        if self.loss == "mse":
            mask = ~torch.isnan(label)
            return self.mse(pred[mask], label[mask])
        # if self.loss == "mse":
        #     sqr_loss = torch.mul(pred - label, pred - label)
        #     loss = sqr_loss.mean()
        #     return loss
        elif self.loss == "binary":
            loss = nn.BCELoss()
            return loss(pred, label)
        raise ValueError("unknown loss `%s`" % self.loss)

    def metric_fn(self, pred, label):

        mask = torch.isfinite(label)

        if self.metric in ("", "loss"):
            return -self.loss_fn(pred[mask], label[mask])

        raise ValueError("unknown metric `%s`" % self.metric)
   
    
    def train_epoch(self, dataloader):

        self.conv_model.train()
        running_loss = 0.0

        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            # print("inputs.shape: ", inputs.shape)
            outputs = self.conv_model(encodeds, inputs)
            loss = self.loss_fn(outputs, targets.float())

            # 添加L1正则化
            # l1_regularization = torch.tensor(0.).to(inputs.device)
            # for param in self.conv_model.parameters():
            #     l1_regularization += torch.norm(param, 1)
            # loss += 0.001 * l1_regularization

            self.train_optimizer.zero_grad()
            loss.backward()
            nn.utils.clip_grad_value_(self.conv_model.parameters(), 3.0)
            self.train_optimizer.step()

            running_loss += loss.item()

        return running_loss / len(dataloader)


    def test_epoch(self, dataloader):

        self.conv_model.eval()
        losses = []
        with torch.no_grad():
            for inputs, targets, encodeds in dataloader:
                inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

                outputs = self.conv_model(encodeds, inputs)

                loss = self.loss_fn(outputs, targets.float())
                losses.append(loss.item())

        return np.mean(losses)
    

    def fit(
        self,
        x_data,
        y_data,
        encoded_data,
        evals_result=dict(),
        save_path=None,
    ):

        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))
        kf = KFold(n_splits=self.k_folds, shuffle=True, random_state=42)

        save_path = get_or_create_path(save_path)
        evals_result["loss"] = {}
        evals_result["score"] = {}
        evals_result["loss"]["train"] = []
        evals_result["loss"]["valid"] = []

        # train
        self.logger.info("training...")
        self.fitted = True

        # 初始化调度器
        scheduler = lr_scheduler.ReduceLROnPlateau(self.train_optimizer,
                                                   'min',
                                                   patience=self.lr_decay_steps,
                                                   factor=self.lr_decay
                                                )

        best_param=None

        for fold, (train_index, val_index) in enumerate(kf.split(dataset), 0):
            print(f'FOLD {fold}')
            print('--------------------------------')
            train_ds = Subset(dataset, train_index)
            val_ds = Subset(dataset, val_index)

            train_dl = DataLoader(train_ds, batch_size=self.batch_size, shuffle=True)
            val_dl = DataLoader(val_ds, batch_size=self.batch_size, shuffle=False)

            stop_steps = 0
            best_loss = np.inf
            best_epoch = 0
            for step in range(self.max_epochs):
                self.train_epoch(train_dl)

                train_loss = self.test_epoch(train_dl)
                valid_loss = self.test_epoch(val_dl)
                scheduler.step(valid_loss)

                evals_result["loss"]["train"].append(train_loss)
                evals_result["loss"]["valid"].append(valid_loss)

                # valid_loss - train_loss > 0.01 此参数不宜过大，否则会导致过拟合
                if best_loss - valid_loss < self.min_delta or valid_loss - train_loss > 0.01:
                    stop_steps += 1
                    self.logger.info(f"Fold {fold} Epoch {step} loss: train %.6f, valid %.6f [+]" % (train_loss, valid_loss))
                    if stop_steps >= self.early_stop:
                        self.logger.info("early stop")
                        break
                else:
                    best_loss = valid_loss
                    stop_steps = 0
                    best_epoch = step
                    best_param = copy.deepcopy(self.conv_model.state_dict())
                    self.logger.info(f"Fold {fold} Epoch {step} loss: train %.6f, valid %.6f" % (train_loss, valid_loss))

        self.logger.info("best epoch: %d -> %d loss: %.6lf" % (fold, best_epoch, best_loss))
        self.conv_model.load_state_dict(best_param)
        # save model
        # torch.save(best_param, save_path)
        model = self.conv_model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        # sm = torch.jit.script(model)
        embedding = torch.zeros(1, self.num_channel, len(self.num_embeds)).long()
        example_input = torch.rand(1, self.num_channel, sum(self.ins_nums))
        sm = torch.jit.trace(model, (embedding, example_input))
        sm.save(save_path)

        if self.use_gpu:
            torch.cuda.empty_cache()

        return best_epoch, best_loss


    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        """
        Predict the result of the given dataset.
        """
        if not self.fitted:
            raise ValueError("model is not fitted yet!")

        x_data, y_data, encoded_data = dataset.prepare(
            segment,
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_size = int(0.9 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = random_split(dataset, [train_size, test_size])
        test_dataloader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False)

        preds = []
        self.conv_model.eval()
        for inputs, targets, encodeds in test_dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            with torch.no_grad():
                outputs = self.conv_model(encodeds, inputs)
            preds.append(outputs.detach().cpu().numpy())

        return pd.Series(np.concatenate(preds))


class TimeSeriesModel1D(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_embeds=[60],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  out_channels=(32, 64, 1152, 256),
                  activation="relu",
                  pooling="max",
                  # is_drop_channel=False,
                ):
        super(TimeSeriesModel1D, self).__init__()
        self.embedding_layers = nn.ModuleList()
        for num_embed in num_embeds:
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embed, embedding_dim=math.ceil(np.sqrt(num_embed))))
        self.flatten = nn.Flatten()

        if activation == "relu":
            activation_layer = nn.ReLU()
        elif activation == "gelu":
            activation_layer = nn.GELU()
        elif activation == "prelu":
            activation_layer = nn.PReLU()
        elif activation == "leakyrelu":
            activation_layer = nn.LeakyReLU()
        else:    
            raise ValueError("unknown activation `%s`" % activation)
        
        if pooling == "max":
            pooling_layer = nn.MaxPool1d(kernel_size=2)
        elif pooling == "avg":
            pooling_layer = nn.AvgPool1d(kernel_size=2)
        else:
            raise ValueError("unknown pooling `%s`" % pooling)

        self.conv1 = nn.Sequential(
            nn.Conv1d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(out_channels[0]),
            activation_layer,
            pooling_layer,
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv1d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(out_channels[1]),
            activation_layer,
            pooling_layer,
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation_layer,
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Linear(out_channels[3], 1)


    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([embedded_data, x], dim=-1)

        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)

        return x.view(-1)

class TimeSeriesModel2D(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  out_channels=(32, 64, 1152, 256),
                  ins_nums=(0,51,51,8),
                  activation="relu",
                  pooling="max",
                  # is_drop_channel=False
                  ):
        super(TimeSeriesModel2D, self).__init__()
        assert len(ins_nums) == 4 and ins_nums[0] == 0 and ins_nums[1] == ins_nums[2]
        num_dims = []
        for num_embed in num_embeds:
            num_dims.append(math.ceil(np.sqrt(num_embed)))
        dims_sum = sum(num_dims)
        for i in range(len(num_dims)):
            num_dims[i] = int(num_dims[i]/dims_sum * (ins_nums[2] - ins_nums[3]))
        
        if sum(num_dims) > ins_nums[2] - ins_nums[3]:
            num_dims[0] -= (sum(num_dims) - (ins_nums[2] - ins_nums[3]))
        elif sum(num_dims) < ins_nums[2] - ins_nums[3]:
            num_dims[0] += ((ins_nums[2] - ins_nums[3]) - sum(num_dims))
        self.embedding_layers = nn.ModuleList()
        for i in range(len(num_dims)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i]))
        self.flatten = nn.Flatten()

        if activation == "relu":
            activation_layer = nn.ReLU()
        elif activation == "gelu":
            activation_layer = nn.GELU()
        elif activation == "prelu":
            activation_layer = nn.PReLU()
        elif activation == "leakyrelu":
            activation_layer = nn.LeakyReLU()
        else:    
            raise ValueError("unknown activation `%s`" % activation)
        
        if pooling == "max":
            pooling_layer = nn.MaxPool2d(kernel_size=2)
        elif pooling == "avg":
            pooling_layer = nn.AvgPool2d(kernel_size=2)
        else:
            raise ValueError("unknown pooling `%s`" % pooling)


        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=(3,3), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[0]),
            activation_layer,
            # nn.MaxPool2d(kernel_size=2),
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=(3,3), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[1]),
            activation_layer,
            pooling_layer,
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation_layer,
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Linear(out_channels[3], 1)


    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([x, embedded_data], dim=-1)
        x = x.reshape(x.shape[0], x.shape[1], 3, x.shape[2]//3)

        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)

        return x.view(-1)
        
