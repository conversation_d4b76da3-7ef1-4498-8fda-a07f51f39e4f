{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "import torch\n", "from torch.utils.data import Dataset\n", "from torch.utils.data.dataloader import DataLoader\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from pyqlab.models.mingpt.model import GPT\n", "from pyqlab.models.mingpt.trainer import Trainer\n", "from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN\n", "\n", "from pyqlab.data.dataset.pipeline import Pipeline\n", "# from pyqlab.data.bardataset import BarDataset\n", "from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class BarDataset(Dataset):\n", "    \"\"\"\n", "    Emits batches of bars\n", "    \"\"\"\n", "\n", "    @staticmethod\n", "    def get_default_config():\n", "        C = CN()\n", "        C.block_size = 15\n", "        C.is_sf = False\n", "        C.data_path = 'd:/RoboQuant2/store/barenc/sf'\n", "        C.start_year = None\n", "        C.end_year = None\n", "        C.start_time = \"\"\n", "        C.end_time = \"\"\n", "        <PERSON><PERSON>timeenc = 0\n", "        C.step_size = 1\n", "        return C\n", "    \n", "    def __init__(self, config, data):\n", "        self.config = config\n", "\n", "        bars = Pipeline.get_vocab()\n", "\n", "        self.itobar = { i:ch for i,ch in enumerate(bars) }\n", "\n", "        self.vocab_size = len(bars)\n", "        self.data = data\n", "        self.block_size = self.config.block_size\n", "        # 统计每个合约的数据量\n", "        codecount=self.data['code_encoded'].value_counts().to_dict()\n", "        codecount = [i for i in dict(sorted(codecount.items())).values()]\n", "        codecount = [i - self.block_size -1 for i in codecount]\n", "        # 每个值都必须大于等于0\n", "        assert all([i >= 0 for i in codecount])\n", "        self.codecount = np.cumsum(codecount)\n", "\n", "    def get_vocab_size(self):\n", "        return self.vocab_size\n", "\n", "    def get_block_size(self):\n", "        return self.block_size\n", "    \n", "    def i_to_idx(self, i):\n", "        n = np.searchsorted(self.codecount, i, side='right')\n", "        return i + n * (self.block_size + 1)\n", "\n", "    def __len__(self):\n", "        return self.codecount[-1]\n", "    \n", "    def __getitem__(self, idx):\n", "        idx = self.i_to_idx(idx)\n", "        code = self.data.iloc[idx:idx+self.block_size+1, 0].astype(int).values\n", "        assert code[0] == code[-1], \"Code mismatch!\"\n", "        bar = self.data.iloc[idx:idx+self.block_size+1, 1].astype(int).values\n", "        if self.config.timeenc == 1:\n", "            tf = self.data.iloc[idx:idx+self.block_size+1, -5:].astype(float).values\n", "            x_mark = torch.tensor(tf[:-1], dtype=torch.float)\n", "            y_mark = torch.tensor(tf[1:], dtype=torch.float)\n", "        else:\n", "            tf = self.data.iloc[idx:idx+self.block_size+1, -5:].astype(int).values\n", "            x_mark = torch.tensor(tf[:-1], dtype=torch.int64)\n", "            y_mark = torch.tensor(tf[1:], dtype=torch.int64)\n", "        code = torch.tensor(code[:-1], dtype=torch.long)\n", "        x = torch.tensor(bar[:-1], dtype=torch.long)\n", "        y = torch.tensor(bar[1:], dtype=torch.long)\n", "        return code, x, x_mark, y, y_mark\n", "    \n", "    \"\"\"\n", "    def __getitem__(self, idx):\n", "        code = self.data[idx][:, 0].astype(int)\n", "        dix = self.data[idx][:, 1].astype(int)\n", "        dtf = self.data_mark[idx][:, :].astype(float)\n", "        code = torch.tensor(code[:-1], dtype=torch.long)\n", "        x = torch.tensor(dix[:-1], dtype=torch.long)\n", "        x_mark = torch.tensor(dtf[:-1, :], dtype=torch.float)\n", "        y = torch.tensor(dix[1:], dtype=torch.long)\n", "        y_mark = torch.tensor(dtf[1:, :], dtype=torch.float)\n", "        return code, x, x_mark, y, y_mark\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def get_config():\n", "\n", "    C = CN()\n", "\n", "    # system\n", "    C.system = CN()\n", "    C.system.seed = 3407\n", "    C.system.work_dir = './out/chargpt'\n", "\n", "    # data\n", "    C.data = BarDataset.get_default_config()\n", "\n", "    # model\n", "    C.model = GPT.get_default_config()\n", "    C.model.model_type = 'gpt-mini'\n", "\n", "    # trainer\n", "    C.trainer = Trainer.get_default_config()\n", "    C.trainer.learning_rate = 5e-4 # the model we're using is so small that we can go a bit faster\n", "\n", "    return C"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['IF' 'IH' 'IC' 'IM']\n", "load data shape: (21696, 3)\n", "============================\n", "code_size: 69, bar_size: 40000\n", "df shape: (21696, 7)\n", "df head:    code_encoded    bar  Month  Day  DayOfWeek  Hour  Minute\n", "0            23  37568      1    2          1     9       5\n", "1            23  13478      1    2          1     9       7\n", "2            23  28320      1    2          1     9       8\n", "============================\n"]}], "source": ["\n", "# get default config and overrides from the command line, if any\n", "config = get_config()\n", "# config.merge_from_args(sys.argv[1:])\n", "setup_logging(config)\n", "set_seed(config.system.seed)\n", "\n", "# construct the training dataset\n", "# text = open(R'E:\\github\\minGPT\\projects\\chargpt\\input.tt', 'r').read() # don't worry we won't run out of file handles\n", "# data = pd.read_csv('d:/RoboQuant2/store/barenc/sf_min5_2023_2024.npy')\n", "pipe = Pipeline('d:/RoboQuant2/store/barenc/sf', 2024, 2024)\n", "data = pipe.get_data()\n", "# assert len(data) > 0 and len(data.shape) == 3, 'No data or wrong shape'\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 4448  8896 13344 17792]\n"]}], "source": ["config.data.is_sf = pipe.is_sf\n", "train_dataset = BarDataset(config.data, data)\n", "# test_dataset = BarDataset(config.data, data)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26]),\n", " tensor([13313, 11651, 28306, 13248, 13376, 16665,  9945,  6659, 28330, 21648,\n", "         23235, 21640, 13400, 18304, 38282]),\n", " tensor([[ 0.0932,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.1780,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.3475,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.4322,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [-0.5000,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.4153,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.3305,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.2458,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.1610,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.0763,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0085,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0932,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.1780,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.1087, -0.1667,  0.4333, -0.0918]]),\n", " tensor([11651, 28306, 13248, 13376, 16665,  9945,  6659, 28330, 21648, 23235,\n", "         21640, 13400, 18304, 38282,  6659]),\n", " tensor([[ 0.1780,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.3475,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.4322,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [-0.5000,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.4153,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.3305,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.2458,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.1610,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.0763,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0085,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0932,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.1780,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.3475,  0.1087, -0.1667,  0.4333, -0.0918]]))"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["train_dataset[17791]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["test_loader = DataLoader(\n", "    train_dataset,\n", "    shuffle=False,\n", "    batch_size=1,\n", "    num_workers=0,\n", "    pin_memory=True,\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[tensor([[23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23]]),\n", " tensor([[37568, 13478, 28320,  5074,  9993, 26636, 21633, 19993, 19972, 20232,\n", "           8323, 26643, 19972,  5000, 29952]]),\n", " tensor([[[-0.0763, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0932, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.2627, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.3475, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.4322, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [-0.5000, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.4153, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.3305, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.2458, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.1610, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.0763, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0085, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0932, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.0652, -0.3333, -0.4667, -0.4973]]]),\n", " tensor([[13478, 28320,  5074,  9993, 26636, 21633, 19993, 19972, 20232,  8323,\n", "          26643, 19972,  5000, 29952,  8321]]),\n", " tensor([[[ 0.0932, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.2627, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.3475, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.4322, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [-0.5000, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.4153, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.3305, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.2458, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.1610, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.0763, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0085, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0932, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.2627, -0.0652, -0.3333, -0.4667, -0.4973]]])]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["next(iter(test_loader))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# construct the model\n", "config.model.vocab_size = train_dataset.get_vocab_size()\n", "config.model.block_size = train_dataset.get_block_size()\n", "config.model.code_size = train_dataset.get_code_size()\n", "config.model.pos_size = train_dataset.get_pos_size()\n", "config.model.n_layer=6\n", "config.model.n_head=6\n", "config.model.n_embd=128\n", "print(config)\n", "\n", "model = GPT(config.model)\n", "\n", "# construct the trainer object\n", "trainer = Trainer(config.trainer, model, train_dataset)\n", "\n", "# iteration callback\n", "def batch_end_callback(trainer):\n", "\n", "    if trainer.iter_num % 50 == 0:\n", "        print(f\"iter_dt {trainer.iter_dt * 1000:.2f}ms; iter {trainer.iter_num}: train loss {trainer.loss.item():.5f}\")\n", "\n", "    if trainer.iter_num % 500 == 0:\n", "        # evaluate both the train and test score\n", "        model.eval()\n", "        with torch.no_grad():\n", "            # sample from the model...\n", "            # context = \"O <PERSON>, O God!\"\n", "            # x = torch.tensor([train_dataset.stoi[s] for s in context], dtype=torch.long)[None,...].to(trainer.device)\n", "            code, pos, x, y = next(iter(test_loader))\n", "\n", "            y = model.generate(code, pos, x, 5, temperature=1.0, do_sample=True, top_k=10)[0]\n", "            completion = [train_dataset.itobar[int(i)] for i in y]\n", "            print(completion)\n", "        # save the latest model\n", "        print(\"saving model\")\n", "        ckpt_path = os.path.join(config.system.work_dir, \"model.pt\")\n", "        torch.save(model.state_dict(), ckpt_path)\n", "        # revert model to training mode\n", "        model.train()\n", "    \n", "trainer.set_callback('on_batch_end', batch_end_callback)\n", "\n", "# run the optimization\n", "trainer.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}