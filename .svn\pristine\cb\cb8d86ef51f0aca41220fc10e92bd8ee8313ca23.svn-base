{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\ipykernel_launcher.py:1: ParserWarning: Falling back to the 'python' engine because the 'c' engine does not support regex separators (separators > 1 char and different from '\\s+' are interpreted as regex); you can avoid this warning by specifying engine='python'.\n", "  \"\"\"Entry point for launching an IPython kernel.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["             3      4            5       6       7       8       9        10  \\\n", "554   RU1909.SC   long   12030.0000  0.9564  0.9480  0.9626  0.9460  94.5747   \n", "611   CU1906.SC   long   49020.0000  1.0108  1.0055  1.0108  1.0047  11.3638   \n", "613   TA1909.ZC   long    6342.0000  0.9568  0.9410  0.9726  0.9410  75.6431   \n", "614   SF1909.ZC   long    6005.0000  0.9575  0.9479  0.9585  0.9479  21.7578   \n", "615    A1909.DC   long    3392.0000  0.9870  0.9767  0.9870  0.9699  27.5059   \n", "616   AU1906.SC   long     284.8500  0.9824  0.9800  0.9824  0.9791  58.6954   \n", "617   BU1906.SC   long    3362.0000  1.0405  1.0167  1.0405  1.0089  36.4366   \n", "619    P1909.DC   long    4480.0000  1.0353  1.0272  1.0379  1.0241  94.7206   \n", "620   RU1909.SC   long   11775.0000  0.9771  0.9686  0.9834  0.9665  19.6302   \n", "622   IF1905.SF   long    3826.8000  1.0802  1.0546  1.0802  1.0546  29.8207   \n", "623   IH1905.SF   long    2809.6001  1.0922  1.0640  1.0922  1.0640  38.1958   \n", "626   IF1905.SF   long    3808.0000  1.0855  1.0598  1.0855  1.0598  26.7741   \n", "627   NI1906.SC   long  100710.0000  0.9859  0.9805  0.9887  0.9794   8.5782   \n", "629   TA1909.ZC   long    6276.0000  0.9669  0.9509  0.9828  0.9509  12.5871   \n", "630   IC1905.SF   long    5586.7998  1.0363  1.0060  1.0385  1.0060  70.7311   \n", "631    Y1909.DC   long    5496.0000  1.0051  0.9996  1.0087  0.9993  16.5078   \n", "636   ZN1906.SC   long   21795.0000  0.9943  0.9890  0.9943  0.9828  35.2860   \n", "638   JD1909.DC   long    3510.0000  1.2291  1.2111  1.2291  1.2014  49.4681   \n", "639   MA1909.ZC   long    2498.0000  0.9776  0.9680  0.9876  0.9664  77.0522   \n", "642   SF1909.ZC   long    5930.0000  0.9696  0.9599  0.9707  0.9599  27.4484   \n", "610   SC1906.SC   long     458.7000  1.0397  1.0327  1.0397  1.0323  24.2342   \n", "643   SM1909.ZC   long    7710.0000  0.9787  0.9598  0.9787  0.9598  20.9159   \n", "609   RU1909.SC   long   11880.0000  0.9684  0.9600  0.9747  0.9579  38.9070   \n", "603   IH1905.SF   long    2826.0000  1.0858  1.0578  1.0858  1.0578  65.7763   \n", "567    I1909.DC   long     611.7200  1.0176  1.0070  1.0176  1.0029  13.8494   \n", "570   TA1909.ZC   long    6256.0000  0.9699  0.9540  0.9859  0.9540  13.6185   \n", "571   ZC1909.ZC   long     589.5200  1.0415  1.0313  1.0470  1.0313  19.7066   \n", "575   SN1909.SC   long  146774.0000  1.0124  1.0051  1.0124  1.0045  21.7883   \n", "576   ZN1906.SC   long   21780.0000  0.9949  0.9897  0.9949  0.9835  79.5811   \n", "577   SC1906.SC   long     456.3000  1.0451  1.0381  1.0451  1.0377  96.8400   \n", "..          ...    ...          ...     ...     ...     ...     ...      ...   \n", "138   RM1909.ZC   long    2151.0000  1.0409  1.0284  1.0409  1.0260   9.1902   \n", "3     BU1906.SC   long    3178.0000  1.1007  1.0755  1.1007  1.0673  14.4294   \n", "368   IC1905.SF   long    5388.3999  1.0745  1.0431  1.0768  1.0431  61.7127   \n", "320   IH1905.SF   long    2746.8000  1.1172  1.0883  1.1172  1.0883  14.9611   \n", "323   TA1909.Z<PERSON>   long    6642.0000  0.9136  0.8985  0.9286  0.8985  21.6799   \n", "324   OI1909.ZC   long    7080.0000  0.9921  0.9843  0.9959  0.9839  95.6654   \n", "362   IC1905.SF   long    5331.3999  1.0860  1.0542  1.0883  1.0542  15.2883   \n", "4     BU1906.SC   long    3160.0000  1.1070  1.0816  1.1070  1.0734  20.7075   \n", "367   IH1905.SF   long    2716.0000  1.1298  1.1007  1.1298  1.1007  41.1425   \n", "5     MA1909.ZC   long    2486.0000  0.9823  0.9726  0.9924  0.9710  12.0581   \n", "326   IC1905.SF   long    5400.0000  1.0722  1.0408  1.0744  1.0408  44.9964   \n", "41    PP1909.DC   long    8703.0000  0.9962  0.9876  1.0043  0.9876  21.5183   \n", "43    RM1909.ZC   long    2103.0000  1.0647  1.0518  1.0647  1.0495  66.3820   \n", "329   IC1905.SF   long    5402.0000  1.0718  1.0404  1.0740  1.0404  35.3220   \n", "330   IF1905.SF   long    3713.0000  1.1133  1.0869  1.1133  1.0869  19.7785   \n", "39    SN1909.SC   long  152670.0000  0.9733  0.9663  0.9733  0.9657  59.6189   \n", "38     I1909.DC   long     594.0000  1.0480  1.0370  1.0480  1.0328  10.5358   \n", "365   ZC1909.ZC   long     598.8000  1.0254  1.0154  1.0307  1.0154   7.9518   \n", "44    FG1909.ZC   long    1353.0000  0.9926  0.9756  0.9926  0.9719  39.0651   \n", "319   IF1905.SF   long    3760.3999  1.0992  1.0732  1.0992  1.0732  13.1655   \n", "318   SP1906.SC   long    5438.0000  0.9937  0.9801  0.9937  0.9787  24.6846   \n", "137   SP1906.SC   long    5556.0000  0.9726  0.9593  0.9726  0.9579  23.9344   \n", "46     M1909.DC   long    2511.0000  1.0438  1.0366  1.0438  1.0335  24.6490   \n", "6     RU1909.SC   long   12370.0000  0.9301  0.9220  0.9361  0.9200  18.1483   \n", "157   RM1909.ZC   long    2143.0000  1.0448  1.0322  1.0448  1.0299  29.9372   \n", "7     SC1906.SC   long     439.5000  1.0851  1.0778  1.0851  1.0774  17.6395   \n", "331   IH1905.SF   long    2714.0000  1.1307  1.1015  1.1307  1.1015  20.3803   \n", "47     A1909.DC   long    3393.0000  0.9867  0.9764  0.9867  0.9696  24.5643   \n", "1     RU1909.SC   long   12505.0000  0.9200  0.9120  0.9260  0.9100  25.9455   \n", "333    J1909.DC   long    2037.5000  1.0083  0.9890  1.0083  0.9845  10.0351   \n", "\n", "           11        12 ...      49   50       51   52       53      54  \\\n", "554  104.3883   41.0064 ... -1.7539  0.0  11.4687  0.0  -1.0819 -0.0050   \n", "611   18.3119   60.8761 ... -0.3927  0.0   1.3213  0.0  -3.4476 -0.0005   \n", "613   81.3385   37.0845 ... -0.3381  0.0  -2.0123  0.0  -9.5944 -0.0037   \n", "614   34.3898   24.4609 ... -0.8041  0.0  -2.1616  0.0  -0.4153 -0.0012   \n", "615   28.5710   32.6017 ...  0.0925  0.0 -10.6555  0.0   0.5491 -0.0010   \n", "616   59.8438   35.6310 ... -1.4069  0.0 -16.1569  0.0   0.2909  0.0002   \n", "617   53.0584   68.4524 ...  0.7817  0.0   0.0000  0.0  -0.5111 -0.0048   \n", "619   92.4931   12.2331 ... -1.9475  0.0   2.2135  0.0  -0.1167 -0.0018   \n", "620   23.0109   25.4870 ... -3.2263  0.0   2.5427  0.0  -0.5538  0.0006   \n", "622   40.5548   73.6358 ...  0.2781  0.0   0.3046  0.0  -0.6994 -0.0026   \n", "623   40.1382   70.5653 ...  0.1801  0.0  -1.5164  0.0  -0.7592 -0.0034   \n", "626   28.2831   71.0852 ... -0.0651  0.0   1.4927  0.0  -1.5611  0.0001   \n", "627   19.1680   54.9174 ... -1.6462  0.0  -2.0369  0.0  -4.9358 -0.0005   \n", "629   21.5301   30.9345 ... -0.6615  0.0   2.3127  0.0  -7.1562 -0.0019   \n", "630   75.4262   79.1817 ...  1.6169  0.0  -4.5448  0.0  -1.9345 -0.0052   \n", "631   20.0038   -9.9820 ... -2.4912  0.0   2.8789  0.0   2.6097 -0.0005   \n", "636   39.9520   58.0757 ... -0.3323  0.0  -1.4661  0.0  -2.3874 -0.0017   \n", "638   53.0385   34.6203 ...  0.8005  0.0  -7.1774  0.0  -2.6659 -0.0003   \n", "639   84.5908   33.7937 ... -2.9151  0.0  -2.1565  0.0   0.3098 -0.0018   \n", "642   37.6895   -3.1459 ... -2.0218  0.0  -1.2049  0.0   2.2599 -0.0049   \n", "610   26.3673   86.1700 ...  2.1503  0.0  -0.2670  0.0   0.1453 -0.0013   \n", "643   23.6455   63.9469 ...  0.7364  0.0   1.5131  0.0  -0.7740 -0.0018   \n", "609   40.3269   32.5350 ... -2.5183  0.0  -3.2834  0.0  -1.5078 -0.0040   \n", "603   64.3297   72.8385 ...  0.5622  0.0  -9.7417  0.0   0.0348 -0.0024   \n", "567   19.3231   66.8932 ... -0.4992  0.0  -0.3590  0.0  -1.4404 -0.0019   \n", "570   23.0023   31.5250 ... -0.8008  0.0   0.5225  0.0 -10.1300  0.0000   \n", "571   21.7860   44.5328 ... -0.7629  0.0  -2.3373  0.0  -0.8366  0.0004   \n", "575   23.8786    9.4163 ... -0.9760  0.0  -1.2467  0.0   1.2797 -0.0002   \n", "576   83.9183   61.9284 ...  0.0490  0.0   3.8353  0.0  -4.7154 -0.0015   \n", "577   92.6216   86.3323 ...  2.2172  0.0   6.5174  0.0   1.0310 -0.0023   \n", "..        ...       ... ...     ...  ...      ...  ...      ...     ...   \n", "138   17.8629   44.4010 ...  0.5023  0.0   3.3182  0.0  -0.0643  0.0000   \n", "3     19.8146   64.4217 ... -0.2857  0.0  -3.7393  0.0  -1.4750  0.0000   \n", "368   80.6064   89.3933 ...  6.2719  0.0  -9.4534  0.0   1.9910 -0.0086   \n", "320   18.5913   70.7420 ...  2.8465  0.0  -1.9030  0.0  -0.0726  0.0002   \n", "323   25.8800   72.3946 ...  1.6793  0.0  -1.1691  0.0  -2.7625 -0.0027   \n", "324   99.0420  114.9140 ...  3.1336  0.0   2.2908  0.0   6.2933 -0.0036   \n", "362   34.2572   86.2277 ...  6.8317  0.0  -0.9564  0.0   1.7318 -0.0026   \n", "4     22.6843   64.4217 ... -0.3786  0.0  -2.7429  0.0  -1.5484 -0.0006   \n", "367   53.0743   68.1656 ...  1.8107  0.0  -6.5617  0.0  -0.0234 -0.0066   \n", "5     16.5623   48.2171 ...  0.6893  0.0   2.8001  0.0  -0.1445  0.0008   \n", "326   46.4940   91.9560 ...  7.8748  0.0  -0.9055  0.0   2.1631 -0.0006   \n", "41    25.0963   62.8181 ... -0.3309  0.0   1.7896  0.0  -1.2624 -0.0010   \n", "43    79.0717   10.9255 ... -0.7057  0.0  -3.9755  0.0   0.1732 -0.0021   \n", "329   53.4138   91.9560 ...  7.8812  0.0  -5.4561  0.0   2.1771 -0.0074   \n", "330   23.2812   76.3034 ...  3.4687  0.0   0.5823  0.0   0.3563  0.0013   \n", "39    59.8819   98.9424 ...  1.3953  0.0  -0.4019  0.0   2.2977 -0.0012   \n", "38    15.9448    0.0000 ...  5.3083  0.0   3.6067  0.0   0.0000  0.0028   \n", "365   18.2837   66.5180 ...  1.4345  0.0   1.0924  0.0   0.1295  0.0002   \n", "44    43.2056   73.1050 ...  0.7387  0.0  -2.4629  0.0   0.1797  0.0003   \n", "319   21.6744   78.0307 ...  4.3829  0.0  -0.6396  0.0   0.6038  0.0005   \n", "318   26.5457   49.3908 ...  0.4563  0.0  -2.3054  0.0  -5.4848 -0.0011   \n", "137   24.7731   79.6198 ...  1.2379  0.0   1.4883  0.0  -0.1528 -0.0007   \n", "46    26.2531    1.1844 ... -0.8982  0.0  -5.2133  0.0   0.2980 -0.0004   \n", "6     20.6856   85.5653 ...  3.8157  0.0   1.2971  0.0   0.4797 -0.0006   \n", "157   48.3092   44.4010 ...  0.4401  0.0 -22.5260  0.0   0.0492 -0.0030   \n", "7     24.7376   69.1684 ...  2.0232  0.0   0.1878  0.0  -6.1010 -0.0016   \n", "331   23.2834   68.1656 ...  1.7988  0.0   1.6177  0.0  -0.0362  0.0017   \n", "47    26.4133   35.1395 ... -0.6518  0.0  -2.5825  0.0  -0.1817 -0.0004   \n", "1     27.6422   96.4858 ...  4.0198  0.0  -3.9673  0.0   1.9077  0.0006   \n", "333   19.6232   50.2441 ... -0.2071  0.0   3.2641  0.0  -7.5304  0.0009   \n", "\n", "         55      56      57  58  \n", "554 -0.0106  0.0007  0.0281   0  \n", "611  0.0025  0.0049  0.0161   0  \n", "613 -0.0099  0.0123  0.0292   0  \n", "614  0.0003  0.0145  0.0190   0  \n", "615  0.0038  0.0055  0.0009   0  \n", "616 -0.0001 -0.0073  0.0081   1  \n", "617 -0.0006  0.0065 -0.0048   0  \n", "619 -0.0047  0.0109  0.0201   0  \n", "620  0.0055  0.0102  0.0459   0  \n", "622 -0.0002 -0.0068 -0.0128   0  \n", "623  0.0003 -0.0081 -0.0041   0  \n", "626  0.0035 -0.0019 -0.0080   0  \n", "627  0.0059  0.0073  0.0247   0  \n", "629  0.0046  0.0229  0.0400   1  \n", "630 -0.0081 -0.0173 -0.0264   0  \n", "631  0.0049  0.0053  0.0322   0  \n", "636  0.0016  0.0005 -0.0047   1  \n", "638  0.0003 -0.0087 -0.0288   0  \n", "639 -0.0080 -0.0080  0.0270   0  \n", "642  0.0030  0.0067  0.0246   0  \n", "610  0.0023 -0.0096 -0.0283   0  \n", "643  0.0060  0.0096  0.0056   0  \n", "609  0.0015  0.0126  0.0410   0  \n", "603 -0.0041 -0.0175 -0.0099   0  \n", "567  0.0188  0.0131  0.0074   0  \n", "570  0.0042  0.0289  0.0433   1  \n", "571  0.0022  0.0057  0.0249   1  \n", "575  0.0020  0.0094  0.0215   1  \n", "576 -0.0069  0.0011 -0.0040   1  \n", "577 -0.0045 -0.0126 -0.0318   1  \n", "..      ...     ...     ...  ..  \n", "138  0.0056 -0.0121 -0.0046   0  \n", "3    0.0060  0.0183  0.0183   0  \n", "368 -0.0108  0.0014 -0.0550   1  \n", "320  0.0077  0.0165 -0.0026   0  \n", "323  0.0072  0.0075 -0.0122   0  \n", "324 -0.0081 -0.0266 -0.0385   1  \n", "362  0.0063  0.0121 -0.0483   1  \n", "4    0.0085  0.0225  0.0241   1  \n", "367  0.0007  0.0189  0.0177   0  \n", "5    0.0064  0.0032  0.0054   1  \n", "326  0.0042 -0.0022 -0.0624   0  \n", "41   0.0036  0.0028  0.0051   1  \n", "43  -0.0036  0.0219  0.0195   0  \n", "329 -0.0019 -0.0026 -0.0627   0  \n", "330  0.0142  0.0159 -0.0150   0  \n", "39  -0.0003 -0.0085 -0.0146   0  \n", "38   0.0185  0.0332  0.0084   1  \n", "365  0.0085  0.0140  0.0035   0  \n", "44   0.0022  0.0000  0.0041   1  \n", "319  0.0054  0.0071 -0.0274   0  \n", "318  0.0085  0.0143  0.0149   0  \n", "137  0.0043  0.0083 -0.0067   0  \n", "46   0.0016  0.0072  0.0223   0  \n", "6    0.0081  0.0154 -0.0263   1  \n", "157 -0.0021 -0.0084 -0.0009   1  \n", "7    0.0094  0.0232  0.0132   0  \n", "331  0.0161  0.0227  0.0185   0  \n", "47   0.0027  0.0050  0.0174   0  \n", "1    0.0026  0.0044 -0.0368   0  \n", "333  0.0109  0.0298  0.0337   0  \n", "\n", "[555 rows x 56 columns]\n", "             3       4            5       6       7       8       9        10  \\\n", "119   IF1905.SF   short    3687.3999  1.1210  1.0945  1.1210  1.0945  47.9294   \n", "893   SR1909.<PERSON><PERSON>   short    5093.0000  1.0654  1.0442  1.0654  1.0338  72.0519   \n", "888    P1909.DC   short    4476.0000  1.0362  1.0282  1.0389  1.0250  89.5648   \n", "798    I1909.DC   short     675.0000  0.9222  0.9126  0.9222  0.9089  86.6156   \n", "890   HC1910.SC   short    3685.0000  1.0024  0.9886  1.0024  0.9859  25.0907   \n", "145   CS1909.DC   short    2262.1399  1.0517  1.0455  1.0517  1.0402  80.6249   \n", "113   RB1910.SC   short    3757.0000  0.9968  0.9827  0.9968  0.9800  80.5092   \n", "891   SC1906.SC   short     479.2100  0.9952  0.9885  0.9952  0.9881  72.4365   \n", "814   RB1910.SC   short    3561.0000  1.0517  1.0368  1.0517  1.0340  21.7344   \n", "889   SP1906.SC   short    5436.0000  0.9941  0.9805  0.9941  0.9790  69.9565   \n", "813   RU1909.SC   short   11730.0000  0.9808  0.9723  0.9872  0.9702  72.5545   \n", "892   ZC1909.ZC   short     619.1400  0.9917  0.9820  0.9969  0.9820  85.5024   \n", "804   IH1905.SF   short    2885.6001  1.0634  1.0360  1.0634  1.0360  82.6843   \n", "811    A1909.DC   short    3322.0000  1.0078  0.9973  1.0078  0.9904  38.2447   \n", "902    J1909.DC   short    2033.5000  1.0103  0.9909  1.0103  0.9865  17.8505   \n", "152    L1909.DC   short    8691.0000  0.9608  0.9533  0.9734  0.9533  82.8752   \n", "903    I1909.DC   short     644.7800  0.9654  0.9554  0.9654  0.9515  18.4380   \n", "151   JM1909.DC   short    1312.5000  1.0187  1.0008  1.0187  1.0008  76.8661   \n", "120   IH1905.SF   short    2768.8000  1.1083  1.0797  1.1083  1.0797  69.8152   \n", "900   OI1909.Z<PERSON>   short    7028.0000  0.9994  0.9916  1.0033  0.9912  79.3951   \n", "150    I1909.DC   short     636.7800  0.9776  0.9674  0.9776  0.9634  79.4588   \n", "904   CF1909.ZC   short   15955.0000  0.9940  0.9906  1.0000  0.9872  83.9310   \n", "146   FG1909.Z<PERSON>   short    1331.2300  1.0088  0.9916  1.0088  0.9878  19.5268   \n", "153   MA1909.<PERSON><PERSON>   short    2633.0000  0.9275  0.9183  0.9370  0.9168  78.0725   \n", "898   IC1905.SF   short    5758.6001  1.0054  0.9760  1.0075  0.9760  23.3007   \n", "906   IH1905.SF   short    2953.2000  1.0391  1.0123  1.0391  1.0123  31.0620   \n", "148   SF1909.Z<PERSON>   short    6074.0000  0.9467  0.9371  0.9476  0.9371  67.6133   \n", "801   ZC1909.Z<PERSON>   short     605.6000  1.0139  1.0040  1.0192  1.0040  75.5423   \n", "118   IC1905.SF   short    4995.6001  1.1590  1.1251  1.1614  1.1251  18.6061   \n", "800   IC1905.SF   short    5729.6001  1.0105  0.9809  1.0126  0.9809  67.6512   \n", "..          ...     ...          ...     ...     ...     ...     ...      ...   \n", "683    V1909.DC   short    6535.0000  1.0398  1.0337  1.0413  1.0291  78.0365   \n", "204   ZC1909.Z<PERSON>   short     616.0000  0.9968  0.9870  1.0019  0.9870  81.8541   \n", "203    J1909.DC   short    2095.0000  0.9807  0.9618  0.9807  0.9575   4.0884   \n", "688    Y1909.DC   short    5444.0000  1.0147  1.0092  1.0184  1.0088  26.1076   \n", "689    I1909.DC   short     612.5000  1.0163  1.0057  1.0163  1.0016  54.0444   \n", "690   IC1905.SF   short    5369.2002  1.0783  1.0468  1.0806  1.0468  36.8877   \n", "202   PB1906.SC   short   17745.0000  0.9284  0.9172  0.9284  0.9143  83.2719   \n", "201   TA1909.<PERSON><PERSON>   short    6616.0000  0.9172  0.9021  0.9323  0.9021  71.5005   \n", "684    L1909.DC   short    8405.0000  0.9935  0.9857  1.0065  0.9857  79.9465   \n", "154   TA1909.Z<PERSON>   short    6572.0000  0.9233  0.9081  0.9385  0.9081  72.6260   \n", "672    V1909.DC   short    6470.0000  1.0502  1.0440  1.0518  1.0394  77.6314   \n", "213   IF1905.SF   short    3791.2000  1.0903  1.0645  1.0903  1.0645  39.7805   \n", "228   NI1906.SC   short  105360.0000  0.9424  0.9373  0.9450  0.9362  78.6539   \n", "649   BU1906.SC   short    3332.0000  1.0498  1.0258  1.0498  1.0180  70.7782   \n", "650   RM1909.ZC   short    2222.0000  1.0077  0.9955  1.0077  0.9932  89.7390   \n", "226    V1909.DC   short    6490.0000  1.0470  1.0408  1.0485  1.0362  72.5102   \n", "653   TA1909.Z<PERSON>   short    6389.0000  0.9498  0.9341  0.9654  0.9341  77.5084   \n", "655    V1909.DC   short    6425.0000  1.0576  1.0514  1.0591  1.0467  68.9827   \n", "223   OI1909.ZC   short    6966.0000  1.0083  1.0004  1.0122  1.0000  89.0695   \n", "671   AP1910.<PERSON><PERSON>   short   11301.0000  0.7367  0.7087  0.7367  0.7051  69.0293   \n", "657    J1909.DC   short    1973.0000  1.0413  1.0213  1.0413  1.0167  27.9120   \n", "221    J1909.DC   short    2083.5000  0.9861  0.9671  0.9861  0.9628  33.0341   \n", "661   CF1909.ZC   short   15285.0000  1.0376  1.0340  1.0438  1.0304  84.0778   \n", "219    Y1909.DC   short    5768.0000  0.9577  0.9525  0.9612  0.9521   8.1632   \n", "218    I1909.DC   short     616.5000  1.0097  0.9992  1.0097  0.9951  30.8112   \n", "215   OI1909.Z<PERSON>   short    6871.0000  1.0223  1.0143  1.0262  1.0138  85.5581   \n", "668   NI1906.SC   short  101000.0000  0.9831  0.9777  0.9858  0.9766  91.4122   \n", "669   SC1906.SC   short     459.0000  1.0390  1.0320  1.0390  1.0316  86.0837   \n", "222   SC1906.SC   short     435.9000  1.0941  1.0867  1.0941  1.0863  16.3983   \n", "0     NI1906.SC   short  102700.0000  0.9668  0.9615  0.9695  0.9605  50.3498   \n", "\n", "          11        12 ...      49   50       51   52      53      54      55  \\\n", "119  45.4693    0.0000 ...  7.9897  0.0  -6.9334  0.0  0.0000  0.0044  0.0006   \n", "893  67.4622   52.8243 ... -0.3570  0.0   0.7083  0.0 -1.3616  0.0017 -0.0013   \n", "888  77.4158   30.4181 ... -2.2290  0.0   1.1307  0.0  0.3081  0.0011 -0.0031   \n", "798  77.5346   86.2070 ...  3.9794  0.0  -1.7015  0.0 -2.7310  0.0019 -0.0107   \n", "890  24.5079   36.1079 ... -0.2225  0.0  -5.8736  0.0 -3.3275  0.0034  0.0252   \n", "145  77.9780   26.6367 ... -1.3650  0.0  -0.3925  0.0 -0.7858  0.0008 -0.0043   \n", "113  75.8116   82.9599 ...  0.8483  0.0   2.1126  0.0  1.0095  0.0011 -0.0053   \n", "891  54.1078  116.6204 ...  2.8650  0.0  -0.0799  0.0  5.1433  0.0028  0.0007   \n", "814  22.6662   23.9892 ... -1.6735  0.0  -4.4325  0.0 -8.5901  0.0028  0.0316   \n", "889  69.1333   46.2238 ... -1.6653  0.0   1.1536  0.0 -0.1700  0.0004 -0.0013   \n", "813  67.0309   33.4129 ... -3.8881  0.0 -16.0019  0.0 -1.4013  0.0009 -0.0058   \n", "892  81.7246   92.6681 ...  0.5145  0.0  -0.9010  0.0  1.9194 -0.0002 -0.0036   \n", "804  79.0837   80.9267 ...  0.4334  0.0   1.5123  0.0 -4.1888  0.0007 -0.0249   \n", "811  35.4516    0.3874 ... -0.9895  0.0  -3.2310  0.0  3.5316  0.0008  0.0024   \n", "902  19.4609   46.8131 ... -0.0873  0.0  -1.6671  0.0 -0.1310  0.0017  0.0101   \n", "152  80.2341   80.6322 ... -0.1734  0.0   2.4225  0.0 -0.5806  0.0007 -0.0044   \n", "903  16.2512   65.6375 ...  2.8181  0.0  -9.7040  0.0 -5.6747  0.0096  0.0281   \n", "151  74.8035   89.7305 ...  2.6264  0.0   0.6027  0.0  2.5225  0.0025 -0.0080   \n", "120  60.4864    0.0000 ...  6.6938  0.0  -5.0095  0.0  0.0000  0.0034 -0.0015   \n", "900  70.9152   66.9289 ... -0.0665  0.0  -4.6184  0.0 -2.0127  0.0015 -0.0032   \n", "150  75.3419   86.7605 ...  3.4228  0.0  -4.0917  0.0  0.9090  0.0058 -0.0164   \n", "904  78.9233  132.3553 ...  1.8069  0.0   1.6434  0.0  8.0118  0.0013 -0.0066   \n", "146  23.5139   52.8624 ... -0.7632  0.0   0.0929  0.0 -1.0916  0.0010  0.0141   \n", "153  76.2185  111.3359 ...  1.6167  0.0  -3.6533  0.0  3.4862 -0.0009 -0.0120   \n", "898  14.1951   91.0764 ...  3.7752  0.0   3.2655  0.0 -7.2956  0.0077  0.0120   \n", "906  24.2747   91.9235 ...  3.5053  0.0   1.4636  0.0 -4.4426  0.0049  0.0091   \n", "148  62.1043   80.4847 ...  0.4765  0.0   0.4553  0.0 -0.2995  0.0026 -0.0007   \n", "801  70.9137   72.8074 ... -0.9889  0.0  -4.2366  0.0 -2.3239  0.0028 -0.0054   \n", "118   9.9188    0.0000 ...  8.3852  0.0  -4.0699  0.0  0.0000  0.0069  0.0079   \n", "800  61.5695   85.3234 ...  1.8828  0.0  -9.6066  0.0 -3.9923  0.0083 -0.0143   \n", "..       ...       ... ...     ...  ...      ...  ...     ...     ...     ...   \n", "683  75.9418   71.9271 ...  0.1678  0.0  -1.1501  0.0  0.4293  0.0023 -0.0061   \n", "204  75.5653  123.0890 ...  2.2764  0.0  -5.9257  0.0  5.2570  0.0008 -0.0046   \n", "203   7.9284   71.9806 ...  0.3003  0.0   1.2445  0.0 -3.3357  0.0056  0.0169   \n", "688  21.1192   -9.3551 ... -2.6809  0.0   2.6000  0.0  3.3651  0.0018  0.0064   \n", "689  50.9999   68.1880 ... -0.1252  0.0  -4.0934  0.0 -2.3572  0.0024 -0.0012   \n", "690  35.5020   69.0888 ...  0.5335  0.0 -14.8415  0.0 -4.1181  0.0060  0.0021   \n", "202  74.9541   73.0443 ...  2.1044  0.0   2.1026  0.0 -0.1187  0.0018 -0.0039   \n", "201  67.9085   80.7748 ... -0.2965  0.0  -9.9576  0.0  1.0791  0.0009 -0.0067   \n", "684  71.2311    8.5598 ... -1.3224  0.0   0.8937  0.0  1.5833  0.0012 -0.0009   \n", "154  67.7657   80.7748 ... -0.4097  0.0  -6.3963  0.0  0.7965  0.0043 -0.0111   \n", "672  71.9798   55.7984 ... -0.2918  0.0   0.8229  0.0 -0.1395  0.0023 -0.0035   \n", "213  26.2926  101.6977 ...  7.6502  0.0  -0.3229  0.0  2.5269  0.0032  0.0103   \n", "228  76.0793   90.8148 ...  2.4262  0.0   0.2898  0.0  1.4777  0.0023 -0.0064   \n", "649  69.0997   68.7356 ... -0.4050  0.0  -1.8296  0.0 -2.6023  0.0009 -0.0018   \n", "650  78.8481   87.7156 ...  1.3452  0.0   1.5957  0.0  2.1393  0.0002 -0.0047   \n", "226  71.0381   48.9147 ... -0.1664  0.0   1.3030  0.0 -1.8446 -0.0008 -0.0062   \n", "653  73.7796   34.6808 ... -0.1675  0.0   1.5771  0.0 -6.0766  0.0028 -0.0091   \n", "655  67.4737   48.4585 ... -0.4429  0.0  -6.4243  0.0  0.1699  0.0016 -0.0062   \n", "223  78.8005  109.8310 ...  2.7457  0.0   1.3372  0.0  4.6844  0.0004 -0.0055   \n", "671  59.9016   48.6034 ...  0.0778  0.0  -0.4435  0.0 -0.1855  0.0060 -0.0015   \n", "657  26.6044    9.1858 ... -3.8364  0.0  -3.0825  0.0  2.3441  0.0027  0.0066   \n", "221  32.5558   60.9453 ...  0.3822  0.0 -18.2763  0.0 -3.4134  0.0006  0.0029   \n", "661  81.3421   57.8403 ...  0.2386  0.0   0.9702  0.0 -0.8436 -0.0008 -0.0033   \n", "219  11.2616   75.6888 ...  0.5955  0.0  -4.8437  0.0 -2.6061  0.0010  0.0036   \n", "218  23.8450   76.4244 ...  0.7460  0.0  -0.2766  0.0  0.2543  0.0081  0.0122   \n", "215  78.8673    0.0000 ...  2.4965  0.0   1.3591  0.0  0.0000  0.0012 -0.0096   \n", "668  81.0877   51.5565 ... -2.1373  0.0   0.7811  0.0 -6.3540  0.0003 -0.0064   \n", "669  75.9885   89.0707 ...  2.2218  0.0   2.1931  0.0  0.9424  0.0005 -0.0044   \n", "222  13.6867   53.0672 ... -1.5883  0.0   2.8126  0.0 -1.2010  0.0022  0.0086   \n", "0    43.3594  106.3981 ...  4.1844  0.0  -0.8244  0.0  2.8918  0.0015  0.0009   \n", "\n", "         56      57  58  \n", "119  0.0067 -0.0610   0  \n", "893 -0.0085 -0.0089   0  \n", "888 -0.0170 -0.0103   0  \n", "798 -0.0467 -0.0563   0  \n", "890  0.0350  0.0316   1  \n", "145 -0.0093  0.0077   0  \n", "113 -0.0212 -0.0037   0  \n", "891 -0.0216 -0.0342   1  \n", "814  0.0383  0.0383   0  \n", "889 -0.0169 -0.0024   1  \n", "813 -0.0237  0.0094   0  \n", "892 -0.0194 -0.0238   0  \n", "804 -0.0264 -0.0303   0  \n", "811  0.0080  0.0169   0  \n", "902 -0.0093 -0.0093   1  \n", "152 -0.0033 -0.0070   0  \n", "903  0.0542  0.0213   0  \n", "151 -0.0105 -0.0322   1  \n", "120  0.0049 -0.0568   0  \n", "900 -0.0087 -0.0038   0  \n", "150 -0.0322 -0.0315   1  \n", "904 -0.0164 -0.0277   1  \n", "146  0.0141  0.0205   1  \n", "153 -0.0285 -0.0348   0  \n", "898 -0.0052 -0.0271   0  \n", "906 -0.0145 -0.0358   0  \n", "148 -0.0040 -0.0028   0  \n", "801 -0.0084 -0.0054   0  \n", "118  0.0062 -0.0680   0  \n", "800 -0.0282 -0.0335   0  \n", "..      ...     ...  ..  \n", "683 -0.0199 -0.0168   1  \n", "204 -0.0164 -0.0273   1  \n", "203  0.0174  0.0063   1  \n", "688  0.0116  0.0388   1  \n", "689  0.0180  0.0061   1  \n", "690  0.0189  0.0131   1  \n", "202 -0.0161 -0.0320   1  \n", "201 -0.0195 -0.0166   1  \n", "684  0.0071  0.0184   1  \n", "154 -0.0158 -0.0100   0  \n", "672 -0.0155 -0.0070   0  \n", "213 -0.0056 -0.0636   0  \n", "228 -0.0122 -0.0393   0  \n", "649  0.0090  0.0042   0  \n", "650 -0.0149 -0.0142   0  \n", "226 -0.0062  0.0035   0  \n", "653 -0.0014  0.0216   1  \n", "655 -0.0121  0.0000   0  \n", "223 -0.0158 -0.0338   0  \n", "671 -0.0092 -0.0282   0  \n", "657  0.0023  0.0433   1  \n", "221  0.0176  0.0119   1  \n", "661 -0.0003  0.0051   1  \n", "219  0.0042 -0.0031   0  \n", "218  0.0028  0.0085   0  \n", "215 -0.0120 -0.0314   0  \n", "668  0.0018  0.0192   1  \n", "669 -0.0049 -0.0290   1  \n", "222  0.0114  0.0272   1  \n", "0   -0.0266 -0.0423   1  \n", "\n", "[554 rows x 56 columns]\n"]}], "source": ["df_train = pd.read_csv('d:/QuantLab/log/featuresdata.log', header=None, sep=']|,', skipinitialspace=True)\n", "df_train = df_train.drop([0,1,2], axis=1)\n", "df_train = df_train.sort_values(4,ascending=1)\n", "\n", "df_train_long, df_train_short = [x for _, x in df_train.groupby(df_train[4])]\n", "\n", "y_long = df_train_long[58]\n", "X_long = df_train_long.drop([3,4,5,58], axis=1)\n", "y_long.to_csv('d:/QuantLab/log/y_long.train', header=False, index=False)\n", "X_long.to_csv('d:/QuantLab/log/X_long.train', header=False, index=False)\n", "\n", "y_short = df_train_short[58]\n", "X_short = df_train_short.drop([3,4,5,58], axis=1)\n", "y_short.to_csv('d:/QuantLab/log/y_short.train', header=False, index=False)\n", "X_short.to_csv('d:/QuantLab/log/X_short.train', header=False, index=False)\n", "print(df_train_long)\n", "print(df_train_short)\n", "\n", "# y_train = df_train[0]\n", "# y_test = df_test[0]\n", "# X_train = df_train.drop(0, axis=1)\n", "# X_test = df_test.drop(0, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}