import unittest
import sys
import os

# 添加模块路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入测试模块
from pyqlab.data.tests.test_pipeline import TestPipeline
from pyqlab.data.tests.test_dataset_bar import TestBarDataset
from pyqlab.data.tests.test_dataset_fts import TestFTSDataset
from pyqlab.data.tests.test_data_api import TestDataAPI

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTests(unittest.defaultTestLoader.loadTestsFromTestCase(TestPipeline))
    test_suite.addTests(unittest.defaultTestLoader.loadTestsFromTestCase(TestBarDataset))
    test_suite.addTests(unittest.defaultTestLoader.loadTestsFromTestCase(TestFTSDataset))
    test_suite.addTests(unittest.defaultTestLoader.loadTestsFromTestCase(TestDataAPI))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(test_suite)

if __name__ == '__main__':
    run_tests() 