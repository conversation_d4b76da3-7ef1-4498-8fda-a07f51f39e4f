{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import finrl\n", "import os\n", "import pandas as pd\n", "\n", "from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv\n", "from finrl.agents.stablebaselines3.models import DRLAgent\n", "from stable_baselines3.common.logger import configure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from finrl import config_tickers\n", "from finrl.main import check_and_make_directories\n", "from finrl.config import INDICATORS, TRAINED_MODEL_DIR, RESULTS_DIR\n", "\n", "check_and_make_directories([TRAINED_MODEL_DIR])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- Load data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train = pd.read_csv('train_data.csv')\n", "\n", "# If you are not using the data generated from part 1 of this tutorial, make sure \n", "# it has the columns and index in the form that could be make into the environment. \n", "# Then you can comment and skip the following two lines.\n", "train = train.set_index(train.columns[0])\n", "train.index.names = ['']"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- Construct the environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stock_dimension = len(train.tic.unique())\n", "state_space = 1 + 2*stock_dimension + len(INDICATORS)*stock_dimension\n", "print(f\"Stock Dimension: {stock_dimension}, State Space: {state_space}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buy_cost_list = sell_cost_list = [0.001] * stock_dimension\n", "num_stock_shares = [0] * stock_dimension\n", "\n", "env_kwargs = {\n", "    \"hmax\": 100,\n", "    \"initial_amount\": 1000000,\n", "    \"num_stock_shares\": num_stock_shares,\n", "    \"buy_cost_pct\": buy_cost_list,\n", "    \"sell_cost_pct\": sell_cost_list,\n", "    \"state_space\": state_space,\n", "    \"stock_dim\": stock_dimension,\n", "    \"tech_indicator_list\": INDICATORS,\n", "    \"action_space\": stock_dimension,\n", "    \"reward_scaling\": 1e-4\n", "}\n", "\n", "\n", "e_train_gym = StockTradingEnv(df = train, **env_kwargs)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Environment for training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["env_train, _ = e_train_gym.get_sb_env()\n", "print(type(env_train))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "\n", "# Set the corresponding values to 'True' for the algorithms that you want to use\n", "if_using_a2c = True\n", "if_using_ddpg = False\n", "if_using_ppo = False\n", "if_using_td3 = False\n", "if_using_sac = False"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- Agent Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "model_a2c = agent.get_model(\"a2c\")\n", "\n", "if if_using_a2c:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/a2c'\n", "  new_logger_a2c = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_a2c.set_logger(new_logger_a2c)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "SAC_PARAMS = {\n", "    \"batch_size\": 128,\n", "    \"buffer_size\": 100000,\n", "    \"learning_rate\": 0.0001,\n", "    \"learning_starts\": 100,\n", "    \"ent_coef\": \"auto_0.1\",\n", "}\n", "\n", "model_sac = agent.get_model(\"sac\",model_kwargs = SAC_PARAMS)\n", "\n", "if if_using_sac:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/sac'\n", "  new_logger_sac = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_sac.set_logger(new_logger_sac)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trained_sac = agent.train_model(model=model_sac, \n", "                             tb_log_name='sac',\n", "                             total_timesteps=70000) if if_using_sac else None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trained_sac.save(TRAINED_MODEL_DIR + \"agent_sac\") if if_using_sac else None"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}