import qlib
from qlib.config import REG_CN
from qlib.utils import init_instance_by_config, flatten_dict
from qlib.workflow import R

import pandas as pd
import matplotlib.pyplot as plt
import ipywidgets as widgets

provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
qlib.init(provider_uri=provider_uri, region=REG_CN)

###################################
# train model
###################################

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": "",
    "data_loader": {
        "class": "AFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "direct": "long",
            "model_name": "MLP",
            "model_name_suff": "",
            "model_path": "e:/lab/RoboQuant/pylab/model",
            "data_path": "e:/lab/RoboQuant/pylab/data",
            'portfolios': ['00200910081133001', '00171106132928000'],  
        }
    },
}

dataset_config = {
    "class": "AFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    },
}

task = {
    "model": {
        "class": "MLPModelPytorch",
        "module_path": "pyqlab.contrib.model.pytorch_mlp",
        "kwargs": {
            "loss": "binary",
            "num_code": 60,
            "num_input": 87,
            "output_dim": 1,
            "lr": 0.001,
            "lr_decay": 0.96,
            "lr_decay_steps": 30,
            "optimizer": "adam",
            "batch_size": 128,
            "GPU": 0,
            "early_stop": 200,
            "best_cond": "accuracy" # or loss
        },
    },
}

def plt_show(df, title=""):
    wdt = widgets.Output()
    wdt.clear_output(wait=False)
    with wdt:
        ylim = [df.min().min(), df.quantile(0.95).max()]
        ylim[0] -= (ylim[1] - ylim[0]) * 0.05
        df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)
        plt.show()

def display_result(evals_result):
    for key, val in evals_result.items():
        if not isinstance(val, dict):
            plt_show(pd.DataFrame(evals_result), key)
            break
        else:
            plt_show(pd.DataFrame(val), key)
            
pfs = {
    'MIX': ['00200910081133001', '00171106132928000'],
    'ALL': ['00211229152555000', '00171106132928000', '00200910081133001', '00170623114649000'],
    '5R': ['00211229152555000', '00171106132928000'],
    '7R': ['00200910081133001', '00170623114649000'],
}

def trainer(show=True, pfs_name=None, train_result={}):
    for direct in ["long", "short"]:
        data_handler_config["data_loader"]["kwargs"]["direct"] = direct
        if pfs_name:
            data_handler_config["data_loader"]["kwargs"]["portfolios"] = pfs[pfs_name]
            data_handler_config["data_loader"]["kwargs"]["model_name_suff"] = pfs_name
        # model initiaiton
        model = init_instance_by_config(task["model"])
        dataset = init_instance_by_config(dataset_config)

        # start exp to train model
        with R.start(experiment_name="train_model"):
            R.log_params(**flatten_dict(task))
            path=data_handler_config["data_loader"]["kwargs"]["model_path"]
            name=data_handler_config["data_loader"]["kwargs"]["model_name"]
            
            if pfs_name:
                model_name=f"{name}_{pfs_name}_{direct}"
            else:
                model_name=f"{name}_{direct}"
            model_path=f"{path}/{model_name}.model"

            result={}
            best_epoch, best_loss, best_acc = model.fit(
                dataset,
                evals_result=result,
                save_path=model_path,
                # save_jit_script=False
            )
            train_result[model_name] = []
            train_result[model_name].append(best_epoch)
            train_result[model_name].append(best_loss)
            train_result[model_name].append(best_acc)
            # R.save_objects(trained_model=model)
            # rid = R.get_recorder().id
            # preds = np.array(model.predict(dataset, segment="valid"))
            # valid_auc = roc_auc_score(y_score=preds[:,1], y_true=y_valid)
            # print("valid accuracy: ", model.test(dataset, segment="valid"))
            if show:
                display_result(result)

if __name__ == "__main__":
    result={}
    for pf in pfs.keys():
        trainer(show=False, pfs_name=pf, train_result=result)
    print("============train result=============")
    for key, item in result.items():
        print(key, item)
    print("============train result=============")
