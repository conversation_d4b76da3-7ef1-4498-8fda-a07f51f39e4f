{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 标准化（Standardization）特征数据\n", "\n", "特征数据的标准化和归一化是在量化投资中常用的数据预处理技术，其主要目的是消除不同特征之间的尺度差异，使其具有相似的尺度范围，以便更好地适应模型的训练和优化过程。以下是标准化和归一化的作用和常见方法：\n", "\n", "作用：\n", "- 帮助优化算法更快地收敛：标准化和归一化可以使不同特征之间的尺度范围相似，使优化算法能够更快地找到最优解，避免因不同尺度带来的优化困难。\n", "\n", "- 防止某些特征对模型的主导影响：如果某个特征的数值范围较大，可能会对模型的训练产生过大的影响，而忽略了其他特征的作用。标准化和归一化可以平衡不同特征之间的影响，确保模型能够全面考虑各个特征。\n", "\n", "- 提高模型的稳定性和鲁棒性：标准化和归一化可以减少异常值和噪声对模型的干扰，提高模型的稳定性和鲁棒性。"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 1.Factors\n", "- lf: long period factors\n", "- sf: short period factors\n", "- ct: market and portfolio context factors"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["feat_path = 'e:/featdata'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['OPEN', 'HIGH', 'LOW', '<PERSON><PERSON><PERSON><PERSON>', 'VOLUM<PERSON>', 'TYPICAL_PRICE', 'NEW', 'NEW_CHANGE_PERCENT', 'SHORT_TERM_HIGH', 'LONG_TERM_HIGH', 'SHORT_TERM_LOW', 'LONG_TERM_LOW', 'AD', 'DX', 'ADX', 'ADXR', 'APO', 'AROON_UP', 'AROON_DOWN', 'ATR', 'BOLL_UP', 'BOLL_MID', 'BOLL_DOWN', 'CCI', 'CMO', 'MA_FAST', 'MA_SLOW', 'EMA_FAST', 'EMA_SLOW', 'DEMA_FAST', 'DEMA_SLOW', 'KAMA_FAST', 'KAMA_SLOW', 'MAMA_FAST', 'MAMA_SLOW', 'T3_FAST', 'T3_SLOW', 'TEMA_FAST', 'TEMA_SLOW', 'TRIMA_FAST', 'TRIMA_SLOW', 'TRIX_FAST', 'TRIX_SLOW', 'MACD', 'MACD_DIFF', 'MACD_DEA', 'MFI', 'MOM', 'NATR', 'OBV', 'ROC', 'RSI', 'SAR', 'TRANGE', 'TSF', 'ULTOSC', 'WILLR', 'KDJ_K', 'KDJ_D', 'LR_SLOPE_FAST', 'LR_SLOPE_MIDD', 'LR_SLOPE_SLOW', 'LR_SLOPE_FAST_THRESHOLD', 'LR_SLOPE_SLOW_THRESHOLD', 'STDDEV_FAST', 'STDDEV_SLOW', 'STDDEV_THRESHOLD', 'MOMENTUM_FAST', 'MOMENTUM_MIDD', 'MOMENTUM_SLOW', 'MOMENTUM', 'MOMENTUM_THRESHOLD', 'SQUEEZE', 'SQUEEZE_SIGNAL', 'SQUEEZE_ZERO_BARS', 'SQUEEZE_BAND_UPL', 'SQUEEZE_BAND_DWL', 'SQUEEZE_MDL', 'SQUEEZE_KC_UPL', 'SQUEEZE_KC_DWL', 'SQUEEZE_GAP', 'SQUEEZE_GAP_FAST', 'SQUEEZE_GAP_SLOW', 'SQUEEZE_GAP_THRESHOLD', 'SQUEEZE_NARROW_BARS', 'BAND_UPL', 'BAND_MDL', 'BAND_DWL', 'BAND_POSITION', 'BAND_WIDTH', 'BAND_EXPAND', 'BAND_GRADIENT', 'BAND_GRADIENT_THRESHOLD', 'BAND_GAP', 'BAND_BK_BARS', 'BAR_STICK_LENGTH', 'TL_FAST', 'TL_SLOW', 'TL_THRESHOLD', 'TREND_VALUE', 'TREND_BARS', 'TREND_INBARS', 'TREND_INPOSR', 'TREND_HIGHEST', 'TREND_LOWEST', 'TREND_HLR', 'TREND_LEVEL', 'HYO_TENKAN_SEN', 'HYO_KIJUN_SEN', 'HYO_CROSS_BARS', 'TATR', 'NATR_TL']\n"]}], "source": ["print(ALL_FACTOR_NAMES)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def get_factor_cols(factor_type=\"lf\"):\n", "    \"\"\"\n", "    因子列名称\n", "    \"\"\"\n", "    col_names = []\n", "    if factor_type == \"lf\":\n", "        for name in ALL_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "\n", "    if factor_type == \"sf\" or factor_type == \"mf\":\n", "        for name in ALL_FACTOR_NAMES: # SEL_SHORT_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "\n", "    if factor_type == \"ct\":\n", "        col_names.extend(SNAPSHOT_CONTEXT)\n", "\n", "    return col_names\n", "\n", "def get_factor_df(year, factor_type=\"lf\", is_sf=False):\n", "    if is_sf:\n", "        df = pd.read_parquet(f'{feat_path}/ffs_{factor_type}.sf.{year}.parquet')\n", "    else:\n", "        df = pd.read_parquet(f'{feat_path}/ffs_{factor_type}.main.{year}.parquet')\n", "    # df.dropna(axis=0, how='any', inplace=True)\n", "    # print(year, df.shape)\n", "    df.fillna(0.0, inplace=True)\n", "    if 'RSI_2' in df.columns:\n", "        df = df[df['RSI_2'] != 0.0]\n", "    if 'FAST_QH_NATR_ZSCORE' in df.columns:\n", "        df = df[df['FAST_QH_NATR_ZSCORE'] != 0.0]\n", "    if 'change' in df.columns:\n", "        df['change'] = df['change'].astype(float)\n", "        df = df[(df['change'] <= 0.01) & (df['change'] >= -0.01)]\n", "    # 剔除BAND_EXPAND_2大于20的极端数据\n", "    # 通常是主力合约换月或长假开盘后有较大的跳空导致的\n", "    if 'BAND_EXPAND_2' in df.columns:\n", "        df = df[df['BAND_EXPAND_2'] < 15.0] \n", "    # print(year, df.shape)\n", "    # 删除列 change，date\n", "    if 'change' in df.columns:\n", "        df.drop(['change', 'date'], axis=1, inplace=True)\n", "    elif 'date' in df.columns:\n", "        df.drop(['date'], axis=1, inplace=True)\n", "    return df\n", "\n", "def calc_factor_mean_std(year, factor_type=\"lf\", is_sf=False):\n", "    df = get_factor_df(year, factor_type, is_sf)\n", "    # 将因子数据按CODE分组求均值和标准差\n", "    df_mean = df.groupby('code').mean()\n", "    df_std = df.groupby('code').std()\n", "    return df_mean, df_std\n", "\n", "# FUT_YEARS = ['2019', '2020', '2021', '2022', '2023']\n", "FUT_YEARS = ['2023']\n", "SF_YEARS = ['2020', '2021', '2022', '2023']\n", "\n", "def get_fut_factors_mean_std(type=\"lf\"):\n", "    if type == \"lf\" or type == \"sf\" or type == \"mf\":\n", "        # 计算商品期货因子数据的均值和标准差\n", "        df_mean = {}\n", "        df_std = {}\n", "        for year in FUT_YEARS:\n", "            df_mean[year], df_std[year] = calc_factor_mean_std(year, type, is_sf=False)\n", "        # 将所有因子数据的mean和std合并求均值\n", "        df_mean_all = pd.concat(df_mean.values()).groupby('code').mean()\n", "        df_std_all = pd.concat(df_std.values()).groupby('code').mean()\n", "        df_mean_all = df_mean_all[get_factor_cols(type)] # 确保列的顺序一致\n", "        df_std_all = df_std_all[get_factor_cols(type)] # 确保列的顺序一致\n", "        return df_mean_all, df_std_all\n", "    elif type == \"ct\":\n", "        # 计算商品期货市场因子数据的均值和标准差\n", "        dfs = pd.DataFrame()\n", "        df_mean = {}\n", "        df_std = {}\n", "        for year in FUT_YEARS:\n", "            df = get_factor_df(year, type, is_sf=False)\n", "            df = df[['code']+ get_factor_cols('ct')] # 确保列的顺序一致\n", "            df_mean[year] = df.loc[:,'FAST_AG_RSI':].mean()\n", "            df_std[year] = df.loc[:,'FAST_AG_RSI':].std()\n", "            dfs = pd.concat([dfs, df])\n", "        # 多年Dict合并为DataFrame并行列交换\n", "        df_mean_all = pd.DataFrame(df_mean).T\n", "        df_std_all = pd.DataFrame(df_std).T\n", "        # 用df_mean_all和df_std_all填充df_mean和df_std\n", "        df_mean = dfs.groupby('code').mean()\n", "        df_std = dfs.groupby('code').std()\n", "        df_mean.loc[:,'FAST_AG_RSI':] = df_mean_all.mean().values\n", "        df_std.loc[:,'FAST_AG_RSI':] = df_std_all.mean().values\n", "        df_mean = df_mean[get_factor_cols(type)] # 确保列的顺序一致\n", "        df_std = df_std[get_factor_cols(type)] # 确保列的顺序一致\n", "        return df_mean, df_std\n", "    else:\n", "        raise ValueError(\"type must be lf or sf\")\n", "\n", "\n", "def get_sf_factors_mean_std(type=\"lf\"):\n", "    # 计算股指期货因子数据的均值和标准差\n", "    df_mean = {}\n", "    df_std = {}\n", "    for year in SF_YEARS:\n", "        df_mean[year], df_std[year] = calc_factor_mean_std(year, type, is_sf=True)\n", "    # 将所有因子数据的mean和std合并求均值\n", "    df_mean_all = pd.concat(df_mean.values()).groupby('code').mean()\n", "    df_std_all = pd.concat(df_std.values()).groupby('code').mean()\n", "    df_mean_all = df_mean_all[get_factor_cols(type)]\n", "    df_std_all = df_std_all[get_factor_cols(type)]\n", "    return df_mean_all, df_std_all"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fut:  lf (51, 127)\n", "sf:  lf (4, 127)\n", "fut:  sf (51, 127)\n", "sf:  sf (4, 127)\n", "fut:  mf (51, 127)\n", "sf:  mf (4, 127)\n", "fut:  ct (51, 64)\n", "sf:  ct (0, 64)\n"]}], "source": ["\n", "factor_types = ['lf', 'sf', 'mf', 'ct']\n", "for type in factor_types:\n", "    fut_mean, fut_std = get_fut_factors_mean_std(type) # 计算商品期货因子数据的均值和标准差\n", "    print(\"fut: \", type, fut_mean.shape)\n", "    fut_mean.to_csv(f'{feat_path}/{type}_mean.csv')\n", "    fut_std.to_csv(f'{feat_path}/{type}_std.csv')\n", "    sf_mean, sf_std = get_sf_factors_mean_std(type) # 计算股指期货因子数据的均值和标准差\n", "    print(\"sf: \", type, sf_mean.shape)\n", "    df_mean = pd.concat([fut_mean, sf_mean])\n", "    df_std = pd.concat([fut_std, sf_std])\n", "    df_mean.to_csv(f'{feat_path}/{type}_mean.csv')\n", "    df_std.to_csv(f'{feat_path}/{type}_std.csv')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["lf_mean:  lf (55, 128)\n", "lf_std:  (55, 128)\n", "sf_mean:  sf (55, 128)\n", "sf_std:  (55, 128)\n", "sf_mean:  sf (55, 128)\n", "sf_std:  (55, 128)\n", "ct_mean:  ct (51, 49)\n", "ct_std:  (51, 49)\n"]}], "source": ["# 两个dataframe合并，不要覆盖第一个dataframe已有code字段的数据\n", "factor_types = ['lf', 'sf', 'sf', 'ct']\n", "for type in factor_types:\n", "    df_mean1 = pd.read_csv(f'{feat_path}/{type}_mean.csv')\n", "    df_mean2 = pd.read_csv(f'{feat_path}/bak/{type}_mean.csv')\n", "    df_mean = pd.concat([df_mean1, df_mean2], ignore_index=True)\n", "    df_mean.drop_duplicates(subset=['code'], keep='first', inplace=True)\n", "    df_mean.to_csv(f'{feat_path}/{type}_mean.csv', index=False)\n", "    print(f\"{type}_mean: \", type, df_mean.shape)\n", "    df_std1 = pd.read_csv(f'{feat_path}/{type}_std.csv')\n", "    df_std2 = pd.read_csv(f'{feat_path}/bak/{type}_std.csv')\n", "    df_std = pd.concat([df_std1, df_std2], ignore_index=True)\n", "    df_std.drop_duplicates(subset=['code'], keep='first', inplace=True)\n", "    df_std.to_csv(f'{feat_path}/{type}_std.csv', index=False)\n", "    print(f\"{type}_std: \", df_std.shape)\n", "    \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Test"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# 计算商品期货因子数据的均值和标准差\n", "dfs = pd.DataFrame()\n", "df_mean = {}\n", "df_std = {}\n", "years = ['2022', '2023']\n", "# years = ['2019', '2020', '2021', '2022', '2023']\n", "for year in years:\n", "    df = get_factor_df(year, 'ct', is_sf=False)\n", "    df = df[['code']+ get_factor_cols('ct')]\n", "    df_mean[year] = df.loc[:,'FAST_AG_RSI':].mean()\n", "    df_std[year] = df.loc[:,'FAST_AG_RSI':].std()\n", "    dfs = pd.concat([dfs, df])\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["df_mean_all = pd.DataFrame(df_mean).T\n", "df_std_all = pd.DataFrame(df_std).T\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["\n", "df_mean = dfs.groupby('code').mean()\n", "df_std = dfs.groupby('code').std()\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>COST_RNG</th>\n", "      <th>DRAWDOWN_RNG</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>PNL</th>\n", "      <th>POS_DAYS</th>\n", "      <th>POS_SHORT_BARS</th>\n", "      <th>POS_LONG_BARS</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>PF_YIELD_TREND</th>\n", "      <th>...</th>\n", "      <th>SLOW_QH_NATR_STDDEV</th>\n", "      <th>SLOW_QH_NATR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>SLOW_QH_MOM</th>\n", "      <th>SLOW_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_STDDEV</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "      <th>SLOW_QH_MOM_STDDEV</th>\n", "      <th>SLOW_QH_MOM_DIRECT</th>\n", "    </tr>\n", "    <tr>\n", "      <th>code</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>A</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.308335</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.907303</td>\n", "      <td>44.703605</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.327977e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.000525</td>\n", "      <td>0.018972</td>\n", "      <td>-0.014669</td>\n", "      <td>0.059259</td>\n", "      <td>10.006683</td>\n", "      <td>-0.000401</td>\n", "      <td>0.624832</td>\n", "      <td>-0.089514</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.416027</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.966272</td>\n", "      <td>63.517821</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.326890e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001658</td>\n", "      <td>0.018746</td>\n", "      <td>-0.010194</td>\n", "      <td>0.066744</td>\n", "      <td>10.002899</td>\n", "      <td>-0.019987</td>\n", "      <td>0.624667</td>\n", "      <td>-0.057680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AL</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.281757</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21.941971</td>\n", "      <td>171.320743</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.030897e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003761</td>\n", "      <td>0.017049</td>\n", "      <td>-0.114053</td>\n", "      <td>-0.123983</td>\n", "      <td>9.224440</td>\n", "      <td>-0.046212</td>\n", "      <td>0.601001</td>\n", "      <td>-0.108251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AP</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.158251</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21.093450</td>\n", "      <td>144.303013</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.325457e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003840</td>\n", "      <td>0.018517</td>\n", "      <td>-0.040028</td>\n", "      <td>0.015585</td>\n", "      <td>10.001052</td>\n", "      <td>-0.019862</td>\n", "      <td>0.624826</td>\n", "      <td>-0.136182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.554282</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.257507</td>\n", "      <td>2.771095</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.033243e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.005318</td>\n", "      <td>0.016893</td>\n", "      <td>-0.045990</td>\n", "      <td>-0.010288</td>\n", "      <td>9.229635</td>\n", "      <td>-0.043724</td>\n", "      <td>0.601191</td>\n", "      <td>-0.043615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.430104</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.195288</td>\n", "      <td>61.923596</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.034545e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002745</td>\n", "      <td>0.017179</td>\n", "      <td>-0.079046</td>\n", "      <td>-0.065606</td>\n", "      <td>9.233815</td>\n", "      <td>-0.019382</td>\n", "      <td>0.601366</td>\n", "      <td>-0.105164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.361340</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>10.403587</td>\n", "      <td>70.534397</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.324336e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.002713</td>\n", "      <td>0.019165</td>\n", "      <td>-0.044576</td>\n", "      <td>0.011142</td>\n", "      <td>9.997073</td>\n", "      <td>-0.000717</td>\n", "      <td>0.624518</td>\n", "      <td>-0.150897</td>\n", "    </tr>\n", "    <tr>\n", "      <th>C</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.274398</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.052422</td>\n", "      <td>19.708644</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.319244e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001240</td>\n", "      <td>0.018745</td>\n", "      <td>-0.036771</td>\n", "      <td>0.022313</td>\n", "      <td>9.983785</td>\n", "      <td>-0.022244</td>\n", "      <td>0.624147</td>\n", "      <td>-0.158246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CF</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.360260</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>29.732671</td>\n", "      <td>135.171190</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.329201e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.004569</td>\n", "      <td>0.018457</td>\n", "      <td>-0.065148</td>\n", "      <td>-0.021079</td>\n", "      <td>10.009743</td>\n", "      <td>-0.022268</td>\n", "      <td>0.624958</td>\n", "      <td>-0.147979</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CJ</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.091931</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21.423896</td>\n", "      <td>157.179659</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032886e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.005800</td>\n", "      <td>0.016837</td>\n", "      <td>-0.161330</td>\n", "      <td>-0.202454</td>\n", "      <td>9.230444</td>\n", "      <td>-0.016013</td>\n", "      <td>0.601283</td>\n", "      <td>-0.075840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CS</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.333588</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.394046</td>\n", "      <td>27.232984</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.325323e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002139</td>\n", "      <td>0.018694</td>\n", "      <td>-0.031310</td>\n", "      <td>0.032967</td>\n", "      <td>9.999598</td>\n", "      <td>-0.013439</td>\n", "      <td>0.624675</td>\n", "      <td>-0.108020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.359949</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>73.772906</td>\n", "      <td>647.271276</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032823e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.000349</td>\n", "      <td>0.017430</td>\n", "      <td>-0.086150</td>\n", "      <td>-0.077710</td>\n", "      <td>9.228811</td>\n", "      <td>0.007757</td>\n", "      <td>0.601161</td>\n", "      <td>-0.017201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CY</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.462266</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>37.966126</td>\n", "      <td>231.069309</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.333010e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001672</td>\n", "      <td>0.018763</td>\n", "      <td>-0.049893</td>\n", "      <td>0.003214</td>\n", "      <td>10.019898</td>\n", "      <td>-0.004765</td>\n", "      <td>0.625160</td>\n", "      <td>-0.136216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EB</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.418218</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20.526124</td>\n", "      <td>141.188914</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.324070e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.000311</td>\n", "      <td>0.018924</td>\n", "      <td>-0.032010</td>\n", "      <td>0.030889</td>\n", "      <td>9.996236</td>\n", "      <td>-0.003536</td>\n", "      <td>0.624544</td>\n", "      <td>-0.099191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.355790</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.191382</td>\n", "      <td>73.113460</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.323638e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001528</td>\n", "      <td>0.018735</td>\n", "      <td>-0.031612</td>\n", "      <td>0.030738</td>\n", "      <td>9.995102</td>\n", "      <td>-0.013731</td>\n", "      <td>0.624520</td>\n", "      <td>-0.090314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.278369</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.244599</td>\n", "      <td>21.904472</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.334833e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.005891</td>\n", "      <td>0.018369</td>\n", "      <td>-0.047460</td>\n", "      <td>0.007884</td>\n", "      <td>10.024711</td>\n", "      <td>-0.026276</td>\n", "      <td>0.625379</td>\n", "      <td>-0.138623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HC</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.315666</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.121407</td>\n", "      <td>61.158119</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.321330e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002423</td>\n", "      <td>0.018644</td>\n", "      <td>-0.064500</td>\n", "      <td>-0.020288</td>\n", "      <td>9.988995</td>\n", "      <td>-0.018466</td>\n", "      <td>0.624392</td>\n", "      <td>-0.140130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>I</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.362339</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.970332</td>\n", "      <td>20.068803</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.327690e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001898</td>\n", "      <td>0.018726</td>\n", "      <td>-0.067775</td>\n", "      <td>-0.025757</td>\n", "      <td>10.006009</td>\n", "      <td>-0.014431</td>\n", "      <td>0.624825</td>\n", "      <td>-0.136703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>J</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.324596</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.227402</td>\n", "      <td>51.034996</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032817e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003468</td>\n", "      <td>0.017104</td>\n", "      <td>-0.088935</td>\n", "      <td>-0.082021</td>\n", "      <td>9.229462</td>\n", "      <td>-0.033721</td>\n", "      <td>0.601200</td>\n", "      <td>-0.074384</td>\n", "    </tr>\n", "    <tr>\n", "      <th>JD</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.049532</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.850970</td>\n", "      <td>27.269621</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.033172e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003155</td>\n", "      <td>0.017130</td>\n", "      <td>-0.120463</td>\n", "      <td>-0.135075</td>\n", "      <td>9.231273</td>\n", "      <td>-0.017376</td>\n", "      <td>0.601276</td>\n", "      <td>-0.126122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>JM</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.387770</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.416572</td>\n", "      <td>55.659547</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.327908e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.004350</td>\n", "      <td>0.018488</td>\n", "      <td>-0.050462</td>\n", "      <td>0.002657</td>\n", "      <td>10.006601</td>\n", "      <td>-0.022175</td>\n", "      <td>0.624823</td>\n", "      <td>-0.124776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>L</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.371406</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13.385564</td>\n", "      <td>90.856132</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.325771e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.001988</td>\n", "      <td>0.019094</td>\n", "      <td>-0.021190</td>\n", "      <td>0.048239</td>\n", "      <td>10.000907</td>\n", "      <td>-0.001636</td>\n", "      <td>0.624662</td>\n", "      <td>-0.087363</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LH</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.197404</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>36.258877</td>\n", "      <td>247.527522</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.030535e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.004434</td>\n", "      <td>0.016979</td>\n", "      <td>-0.156510</td>\n", "      <td>-0.194361</td>\n", "      <td>9.224462</td>\n", "      <td>0.000564</td>\n", "      <td>0.601010</td>\n", "      <td>-0.141837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>M</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.420459</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.798962</td>\n", "      <td>54.653205</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.319491e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.000535</td>\n", "      <td>0.018827</td>\n", "      <td>-0.025148</td>\n", "      <td>0.042640</td>\n", "      <td>9.983865</td>\n", "      <td>-0.010842</td>\n", "      <td>0.624236</td>\n", "      <td>-0.115610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MA</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.316457</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.028013</td>\n", "      <td>32.092211</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.328780e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001285</td>\n", "      <td>0.018793</td>\n", "      <td>-0.044096</td>\n", "      <td>0.011484</td>\n", "      <td>10.008745</td>\n", "      <td>-0.011072</td>\n", "      <td>0.624957</td>\n", "      <td>-0.110793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NI</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.452815</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>485.582805</td>\n", "      <td>4217.607565</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.031211e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.000569</td>\n", "      <td>0.017398</td>\n", "      <td>-0.064481</td>\n", "      <td>-0.041661</td>\n", "      <td>9.225043</td>\n", "      <td>-0.005599</td>\n", "      <td>0.601018</td>\n", "      <td>-0.067438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.301585</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>16.148652</td>\n", "      <td>111.783221</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032424e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003819</td>\n", "      <td>0.017051</td>\n", "      <td>-0.087750</td>\n", "      <td>-0.080761</td>\n", "      <td>9.228407</td>\n", "      <td>-0.019018</td>\n", "      <td>0.601140</td>\n", "      <td>0.025036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OI</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.457980</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>24.846301</td>\n", "      <td>123.715519</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.335872e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001136</td>\n", "      <td>0.018838</td>\n", "      <td>-0.013372</td>\n", "      <td>0.061454</td>\n", "      <td>10.027568</td>\n", "      <td>-0.013950</td>\n", "      <td>0.625440</td>\n", "      <td>-0.094578</td>\n", "    </tr>\n", "    <tr>\n", "      <th>P</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.459700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>26.181070</td>\n", "      <td>199.849136</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.324576e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001716</td>\n", "      <td>0.018718</td>\n", "      <td>-0.029400</td>\n", "      <td>0.034536</td>\n", "      <td>9.997815</td>\n", "      <td>-0.009131</td>\n", "      <td>0.624558</td>\n", "      <td>-0.093347</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PB</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.101865</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.538462</td>\n", "      <td>77.365911</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.028389e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001300</td>\n", "      <td>0.017306</td>\n", "      <td>-0.072433</td>\n", "      <td>-0.054799</td>\n", "      <td>9.217665</td>\n", "      <td>-0.014575</td>\n", "      <td>0.600736</td>\n", "      <td>-0.084049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PF</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.318193</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13.731264</td>\n", "      <td>73.907718</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032793e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001177</td>\n", "      <td>0.017343</td>\n", "      <td>-0.061444</td>\n", "      <td>-0.037086</td>\n", "      <td>9.229399</td>\n", "      <td>-0.031600</td>\n", "      <td>0.601188</td>\n", "      <td>-0.012164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.363922</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>10.390554</td>\n", "      <td>81.188801</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032229e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003385</td>\n", "      <td>0.017098</td>\n", "      <td>-0.094937</td>\n", "      <td>-0.092131</td>\n", "      <td>9.227893</td>\n", "      <td>-0.029711</td>\n", "      <td>0.601125</td>\n", "      <td>-0.076562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PK</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.119836</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>19.053528</td>\n", "      <td>120.376796</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.332415e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003776</td>\n", "      <td>0.018572</td>\n", "      <td>-0.043731</td>\n", "      <td>0.010276</td>\n", "      <td>10.019506</td>\n", "      <td>0.005658</td>\n", "      <td>0.625345</td>\n", "      <td>-0.135346</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PP</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.361919</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>12.916817</td>\n", "      <td>86.650819</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.320393e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.001594</td>\n", "      <td>0.019034</td>\n", "      <td>-0.020463</td>\n", "      <td>0.048285</td>\n", "      <td>9.986702</td>\n", "      <td>0.001052</td>\n", "      <td>0.624250</td>\n", "      <td>-0.104176</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RB</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.344634</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.208034</td>\n", "      <td>65.146183</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.321767e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003293</td>\n", "      <td>0.018560</td>\n", "      <td>-0.070445</td>\n", "      <td>-0.029905</td>\n", "      <td>9.990162</td>\n", "      <td>-0.028871</td>\n", "      <td>0.624443</td>\n", "      <td>-0.148579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.384334</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.628353</td>\n", "      <td>37.733005</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.325659e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.000754</td>\n", "      <td>0.018829</td>\n", "      <td>-0.030296</td>\n", "      <td>0.033855</td>\n", "      <td>10.000452</td>\n", "      <td>-0.011625</td>\n", "      <td>0.624699</td>\n", "      <td>-0.147925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.336432</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.711897</td>\n", "      <td>10.701371</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.029630e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002559</td>\n", "      <td>0.017178</td>\n", "      <td>-0.097779</td>\n", "      <td>-0.096707</td>\n", "      <td>9.221293</td>\n", "      <td>-0.016139</td>\n", "      <td>0.600852</td>\n", "      <td>-0.188327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.345808</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21.913011</td>\n", "      <td>145.752189</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.316985e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003376</td>\n", "      <td>0.018533</td>\n", "      <td>-0.065609</td>\n", "      <td>-0.022565</td>\n", "      <td>9.977381</td>\n", "      <td>-0.018735</td>\n", "      <td>0.624040</td>\n", "      <td>-0.123055</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SA</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.337577</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.058938</td>\n", "      <td>36.560889</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.320452e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003415</td>\n", "      <td>0.018549</td>\n", "      <td>-0.050457</td>\n", "      <td>0.001848</td>\n", "      <td>9.986559</td>\n", "      <td>-0.020028</td>\n", "      <td>0.624388</td>\n", "      <td>-0.137281</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SC</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.505273</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.060000</td>\n", "      <td>6.000000</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.413812e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003207</td>\n", "      <td>0.019011</td>\n", "      <td>-0.048334</td>\n", "      <td>0.016138</td>\n", "      <td>10.231479</td>\n", "      <td>-0.012503</td>\n", "      <td>0.631402</td>\n", "      <td>-0.013279</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SF</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.206875</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>23.925064</td>\n", "      <td>165.230017</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.323207e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003759</td>\n", "      <td>0.018535</td>\n", "      <td>-0.019233</td>\n", "      <td>0.049629</td>\n", "      <td>9.995177</td>\n", "      <td>-0.013752</td>\n", "      <td>0.624611</td>\n", "      <td>-0.122153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SM</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.135359</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>17.056113</td>\n", "      <td>113.903019</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.321556e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002343</td>\n", "      <td>0.018666</td>\n", "      <td>-0.057122</td>\n", "      <td>-0.010384</td>\n", "      <td>9.990715</td>\n", "      <td>-0.006654</td>\n", "      <td>0.624496</td>\n", "      <td>-0.176284</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SN</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.416552</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>533.335460</td>\n", "      <td>4442.137185</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.032090e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.000937</td>\n", "      <td>0.017376</td>\n", "      <td>-0.084799</td>\n", "      <td>-0.074609</td>\n", "      <td>9.227467</td>\n", "      <td>-0.010280</td>\n", "      <td>0.601099</td>\n", "      <td>-0.094411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SP</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.382182</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13.918692</td>\n", "      <td>99.808124</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.317897e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001445</td>\n", "      <td>0.018707</td>\n", "      <td>-0.047398</td>\n", "      <td>0.006408</td>\n", "      <td>9.979924</td>\n", "      <td>-0.011503</td>\n", "      <td>0.624107</td>\n", "      <td>-0.133034</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.316921</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.876087</td>\n", "      <td>26.923033</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.338946e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.003099</td>\n", "      <td>0.018658</td>\n", "      <td>-0.054399</td>\n", "      <td>-0.002761</td>\n", "      <td>10.035369</td>\n", "      <td>-0.010486</td>\n", "      <td>0.625726</td>\n", "      <td>-0.145731</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SS</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.383190</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>36.756344</td>\n", "      <td>251.289425</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.326115e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.000453</td>\n", "      <td>0.018951</td>\n", "      <td>-0.005766</td>\n", "      <td>0.072652</td>\n", "      <td>10.001620</td>\n", "      <td>-0.012877</td>\n", "      <td>0.624717</td>\n", "      <td>-0.082086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TA</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.383806</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>15.243255</td>\n", "      <td>74.556842</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.327081e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.002396</td>\n", "      <td>0.019150</td>\n", "      <td>-0.025888</td>\n", "      <td>0.040890</td>\n", "      <td>10.004199</td>\n", "      <td>-0.001653</td>\n", "      <td>0.624763</td>\n", "      <td>-0.073719</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.134047</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.056227</td>\n", "      <td>46.176832</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.323103e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.004685</td>\n", "      <td>0.018428</td>\n", "      <td>-0.069730</td>\n", "      <td>-0.030514</td>\n", "      <td>9.994783</td>\n", "      <td>-0.023178</td>\n", "      <td>0.624665</td>\n", "      <td>-0.139462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>V</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.401054</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>14.844567</td>\n", "      <td>106.261733</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.322351e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001636</td>\n", "      <td>0.018722</td>\n", "      <td>-0.031028</td>\n", "      <td>0.031954</td>\n", "      <td>9.991686</td>\n", "      <td>-0.011936</td>\n", "      <td>0.624435</td>\n", "      <td>-0.107304</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Y</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.427669</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20.000154</td>\n", "      <td>146.348542</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.325713e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.001795</td>\n", "      <td>0.018719</td>\n", "      <td>-0.023214</td>\n", "      <td>0.044691</td>\n", "      <td>10.000673</td>\n", "      <td>-0.006865</td>\n", "      <td>0.624662</td>\n", "      <td>-0.108261</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZN</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.358709</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>31.034884</td>\n", "      <td>259.513752</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.031186e+19</td>\n", "      <td>0.0</td>\n", "      <td>0.000280</td>\n", "      <td>0.017490</td>\n", "      <td>-0.117597</td>\n", "      <td>-0.130457</td>\n", "      <td>9.224970</td>\n", "      <td>0.004472</td>\n", "      <td>0.601022</td>\n", "      <td>-0.052773</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>51 rows × 46 columns</p>\n", "</div>"], "text/plain": ["      COST_RNG  DRAWDOWN_RNG  STDDEV_RNG  PNL  POS_DAYS  POS_SHORT_BARS  \\\n", "code                                                                      \n", "A          0.0           0.0    1.308335  0.0       0.0             0.0   \n", "AG         0.0           0.0    1.416027  0.0       0.0             0.0   \n", "AL         0.0           0.0    1.281757  0.0       0.0             0.0   \n", "AP         0.0           0.0    1.158251  0.0       0.0             0.0   \n", "AU         0.0           0.0    1.554282  0.0       0.0             0.0   \n", "B          0.0           0.0    1.430104  0.0       0.0             0.0   \n", "BU         0.0           0.0    1.361340  0.0       0.0             0.0   \n", "C          0.0           0.0    1.274398  0.0       0.0             0.0   \n", "CF         0.0           0.0    1.360260  0.0       0.0             0.0   \n", "CJ         0.0           0.0    1.091931  0.0       0.0             0.0   \n", "CS         0.0           0.0    1.333588  0.0       0.0             0.0   \n", "CU         0.0           0.0    1.359949  0.0       0.0             0.0   \n", "CY         0.0           0.0    1.462266  0.0       0.0             0.0   \n", "EB         0.0           0.0    1.418218  0.0       0.0             0.0   \n", "EG         0.0           0.0    1.355790  0.0       0.0             0.0   \n", "FG         0.0           0.0    1.278369  0.0       0.0             0.0   \n", "HC         0.0           0.0    1.315666  0.0       0.0             0.0   \n", "I          0.0           0.0    1.362339  0.0       0.0             0.0   \n", "J          0.0           0.0    1.324596  0.0       0.0             0.0   \n", "JD         0.0           0.0    1.049532  0.0       0.0             0.0   \n", "JM         0.0           0.0    1.387770  0.0       0.0             0.0   \n", "L          0.0           0.0    1.371406  0.0       0.0             0.0   \n", "LH         0.0           0.0    1.197404  0.0       0.0             0.0   \n", "M          0.0           0.0    1.420459  0.0       0.0             0.0   \n", "MA         0.0           0.0    1.316457  0.0       0.0             0.0   \n", "NI         0.0           0.0    1.452815  0.0       0.0             0.0   \n", "NR         0.0           0.0    1.301585  0.0       0.0             0.0   \n", "OI         0.0           0.0    1.457980  0.0       0.0             0.0   \n", "P          0.0           0.0    1.459700  0.0       0.0             0.0   \n", "PB         0.0           0.0    1.101865  0.0       0.0             0.0   \n", "PF         0.0           0.0    1.318193  0.0       0.0             0.0   \n", "PG         0.0           0.0    1.363922  0.0       0.0             0.0   \n", "PK         0.0           0.0    1.119836  0.0       0.0             0.0   \n", "PP         0.0           0.0    1.361919  0.0       0.0             0.0   \n", "RB         0.0           0.0    1.344634  0.0       0.0             0.0   \n", "RM         0.0           0.0    1.384334  0.0       0.0             0.0   \n", "RR         0.0           0.0    1.336432  0.0       0.0             0.0   \n", "RU         0.0           0.0    1.345808  0.0       0.0             0.0   \n", "SA         0.0           0.0    1.337577  0.0       0.0             0.0   \n", "SC         0.0           0.0    1.505273  0.0       0.0             0.0   \n", "SF         0.0           0.0    1.206875  0.0       0.0             0.0   \n", "SM         0.0           0.0    1.135359  0.0       0.0             0.0   \n", "SN         0.0           0.0    1.416552  0.0       0.0             0.0   \n", "SP         0.0           0.0    1.382182  0.0       0.0             0.0   \n", "SR         0.0           0.0    1.316921  0.0       0.0             0.0   \n", "SS         0.0           0.0    1.383190  0.0       0.0             0.0   \n", "TA         0.0           0.0    1.383806  0.0       0.0             0.0   \n", "UR         0.0           0.0    1.134047  0.0       0.0             0.0   \n", "V          0.0           0.0    1.401054  0.0       0.0             0.0   \n", "Y          0.0           0.0    1.427669  0.0       0.0             0.0   \n", "ZN         0.0           0.0    1.358709  0.0       0.0             0.0   \n", "\n", "      POS_LONG_BARS  SHORT_RANGE   LONG_RANGE  PF_YIELD_TREND  ...  \\\n", "code                                                           ...   \n", "A               0.0     7.907303    44.703605             0.0  ...   \n", "AG              0.0     6.966272    63.517821             0.0  ...   \n", "AL              0.0    21.941971   171.320743             0.0  ...   \n", "AP              0.0    21.093450   144.303013             0.0  ...   \n", "AU              0.0     0.257507     2.771095             0.0  ...   \n", "B               0.0     7.195288    61.923596             0.0  ...   \n", "BU              0.0    10.403587    70.534397             0.0  ...   \n", "C               0.0     3.052422    19.708644             0.0  ...   \n", "CF              0.0    29.732671   135.171190             0.0  ...   \n", "CJ              0.0    21.423896   157.179659             0.0  ...   \n", "CS              0.0     4.394046    27.232984             0.0  ...   \n", "CU              0.0    73.772906   647.271276             0.0  ...   \n", "CY              0.0    37.966126   231.069309             0.0  ...   \n", "EB              0.0    20.526124   141.188914             0.0  ...   \n", "EG              0.0    11.191382    73.113460             0.0  ...   \n", "FG              0.0     5.244599    21.904472             0.0  ...   \n", "HC              0.0     9.121407    61.158119             0.0  ...   \n", "I               0.0     2.970332    20.068803             0.0  ...   \n", "J               0.0     7.227402    51.034996             0.0  ...   \n", "JD              0.0     5.850970    27.269621             0.0  ...   \n", "JM              0.0     8.416572    55.659547             0.0  ...   \n", "L               0.0    13.385564    90.856132             0.0  ...   \n", "LH              0.0    36.258877   247.527522             0.0  ...   \n", "M               0.0     6.798962    54.653205             0.0  ...   \n", "MA              0.0     7.028013    32.092211             0.0  ...   \n", "NI              0.0   485.582805  4217.607565             0.0  ...   \n", "NR              0.0    16.148652   111.783221             0.0  ...   \n", "OI              0.0    24.846301   123.715519             0.0  ...   \n", "P               0.0    26.181070   199.849136             0.0  ...   \n", "PB              0.0     9.538462    77.365911             0.0  ...   \n", "PF              0.0    13.731264    73.907718             0.0  ...   \n", "PG              0.0    10.390554    81.188801             0.0  ...   \n", "PK              0.0    19.053528   120.376796             0.0  ...   \n", "PP              0.0    12.916817    86.650819             0.0  ...   \n", "RB              0.0     9.208034    65.146183             0.0  ...   \n", "RM              0.0     7.628353    37.733005             0.0  ...   \n", "RR              0.0     1.711897    10.701371             0.0  ...   \n", "RU              0.0    21.913011   145.752189             0.0  ...   \n", "SA              0.0     8.058938    36.560889             0.0  ...   \n", "SC              0.0     1.060000     6.000000             0.0  ...   \n", "SF              0.0    23.925064   165.230017             0.0  ...   \n", "SM              0.0    17.056113   113.903019             0.0  ...   \n", "SN              0.0   533.335460  4442.137185             0.0  ...   \n", "SP              0.0    13.918692    99.808124             0.0  ...   \n", "SR              0.0     5.876087    26.923033             0.0  ...   \n", "SS              0.0    36.756344   251.289425             0.0  ...   \n", "TA              0.0    15.243255    74.556842             0.0  ...   \n", "UR              0.0     7.056227    46.176832             0.0  ...   \n", "V               0.0    14.844567   106.261733             0.0  ...   \n", "Y               0.0    20.000154   146.348542             0.0  ...   \n", "ZN              0.0    31.034884   259.513752             0.0  ...   \n", "\n", "      SLOW_QH_NATR_STDDEV  SLOW_QH_NATR_DIRECT  FAST_QH_MOM  \\\n", "code                                                          \n", "A            4.327977e+19                  0.0     0.000525   \n", "AG           4.326890e+19                  0.0    -0.001658   \n", "AL           4.030897e+19                  0.0    -0.003761   \n", "AP           4.325457e+19                  0.0    -0.003840   \n", "AU           4.033243e+19                  0.0    -0.005318   \n", "B            4.034545e+19                  0.0    -0.002745   \n", "BU           4.324336e+19                  0.0     0.002713   \n", "C            4.319244e+19                  0.0    -0.001240   \n", "CF           4.329201e+19                  0.0    -0.004569   \n", "CJ           4.032886e+19                  0.0    -0.005800   \n", "CS           4.325323e+19                  0.0    -0.002139   \n", "CU           4.032823e+19                  0.0    -0.000349   \n", "CY           4.333010e+19                  0.0    -0.001672   \n", "EB           4.324070e+19                  0.0     0.000311   \n", "EG           4.323638e+19                  0.0    -0.001528   \n", "FG           4.334833e+19                  0.0    -0.005891   \n", "HC           4.321330e+19                  0.0    -0.002423   \n", "I            4.327690e+19                  0.0    -0.001898   \n", "J            4.032817e+19                  0.0    -0.003468   \n", "JD           4.033172e+19                  0.0    -0.003155   \n", "JM           4.327908e+19                  0.0    -0.004350   \n", "L            4.325771e+19                  0.0     0.001988   \n", "LH           4.030535e+19                  0.0    -0.004434   \n", "M            4.319491e+19                  0.0    -0.000535   \n", "MA           4.328780e+19                  0.0    -0.001285   \n", "NI           4.031211e+19                  0.0    -0.000569   \n", "NR           4.032424e+19                  0.0    -0.003819   \n", "OI           4.335872e+19                  0.0    -0.001136   \n", "P            4.324576e+19                  0.0    -0.001716   \n", "PB           4.028389e+19                  0.0    -0.001300   \n", "PF           4.032793e+19                  0.0    -0.001177   \n", "PG           4.032229e+19                  0.0    -0.003385   \n", "PK           4.332415e+19                  0.0    -0.003776   \n", "PP           4.320393e+19                  0.0     0.001594   \n", "RB           4.321767e+19                  0.0    -0.003293   \n", "RM           4.325659e+19                  0.0    -0.000754   \n", "RR           4.029630e+19                  0.0    -0.002559   \n", "RU           4.316985e+19                  0.0    -0.003376   \n", "SA           4.320452e+19                  0.0    -0.003415   \n", "SC           4.413812e+19                  0.0    -0.003207   \n", "SF           4.323207e+19                  0.0    -0.003759   \n", "SM           4.321556e+19                  0.0    -0.002343   \n", "SN           4.032090e+19                  0.0    -0.000937   \n", "SP           4.317897e+19                  0.0    -0.001445   \n", "SR           4.338946e+19                  0.0    -0.003099   \n", "SS           4.326115e+19                  0.0     0.000453   \n", "TA           4.327081e+19                  0.0     0.002396   \n", "UR           4.323103e+19                  0.0    -0.004685   \n", "V            4.322351e+19                  0.0    -0.001636   \n", "Y            4.325713e+19                  0.0    -0.001795   \n", "ZN           4.031186e+19                  0.0     0.000280   \n", "\n", "      FAST_QH_MOM_ZSCORE  SLOW_QH_MOM  SLOW_QH_MOM_ZSCORE  FAST_QH_MOM_STDDEV  \\\n", "code                                                                            \n", "A               0.018972    -0.014669            0.059259           10.006683   \n", "AG              0.018746    -0.010194            0.066744           10.002899   \n", "AL              0.017049    -0.114053           -0.123983            9.224440   \n", "AP              0.018517    -0.040028            0.015585           10.001052   \n", "AU              0.016893    -0.045990           -0.010288            9.229635   \n", "B               0.017179    -0.079046           -0.065606            9.233815   \n", "BU              0.019165    -0.044576            0.011142            9.997073   \n", "C               0.018745    -0.036771            0.022313            9.983785   \n", "CF              0.018457    -0.065148           -0.021079           10.009743   \n", "CJ              0.016837    -0.161330           -0.202454            9.230444   \n", "CS              0.018694    -0.031310            0.032967            9.999598   \n", "CU              0.017430    -0.086150           -0.077710            9.228811   \n", "CY              0.018763    -0.049893            0.003214           10.019898   \n", "EB              0.018924    -0.032010            0.030889            9.996236   \n", "EG              0.018735    -0.031612            0.030738            9.995102   \n", "FG              0.018369    -0.047460            0.007884           10.024711   \n", "HC              0.018644    -0.064500           -0.020288            9.988995   \n", "I               0.018726    -0.067775           -0.025757           10.006009   \n", "J               0.017104    -0.088935           -0.082021            9.229462   \n", "JD              0.017130    -0.120463           -0.135075            9.231273   \n", "JM              0.018488    -0.050462            0.002657           10.006601   \n", "L               0.019094    -0.021190            0.048239           10.000907   \n", "LH              0.016979    -0.156510           -0.194361            9.224462   \n", "M               0.018827    -0.025148            0.042640            9.983865   \n", "MA              0.018793    -0.044096            0.011484           10.008745   \n", "NI              0.017398    -0.064481           -0.041661            9.225043   \n", "NR              0.017051    -0.087750           -0.080761            9.228407   \n", "OI              0.018838    -0.013372            0.061454           10.027568   \n", "P               0.018718    -0.029400            0.034536            9.997815   \n", "PB              0.017306    -0.072433           -0.054799            9.217665   \n", "PF              0.017343    -0.061444           -0.037086            9.229399   \n", "PG              0.017098    -0.094937           -0.092131            9.227893   \n", "PK              0.018572    -0.043731            0.010276           10.019506   \n", "PP              0.019034    -0.020463            0.048285            9.986702   \n", "RB              0.018560    -0.070445           -0.029905            9.990162   \n", "RM              0.018829    -0.030296            0.033855           10.000452   \n", "RR              0.017178    -0.097779           -0.096707            9.221293   \n", "RU              0.018533    -0.065609           -0.022565            9.977381   \n", "SA              0.018549    -0.050457            0.001848            9.986559   \n", "SC              0.019011    -0.048334            0.016138           10.231479   \n", "SF              0.018535    -0.019233            0.049629            9.995177   \n", "SM              0.018666    -0.057122           -0.010384            9.990715   \n", "SN              0.017376    -0.084799           -0.074609            9.227467   \n", "SP              0.018707    -0.047398            0.006408            9.979924   \n", "SR              0.018658    -0.054399           -0.002761           10.035369   \n", "SS              0.018951    -0.005766            0.072652           10.001620   \n", "TA              0.019150    -0.025888            0.040890           10.004199   \n", "UR              0.018428    -0.069730           -0.030514            9.994783   \n", "V               0.018722    -0.031028            0.031954            9.991686   \n", "Y               0.018719    -0.023214            0.044691           10.000673   \n", "ZN              0.017490    -0.117597           -0.130457            9.224970   \n", "\n", "      FAST_QH_MOM_DIRECT  SLOW_QH_MOM_STDDEV  SLOW_QH_MOM_DIRECT  \n", "code                                                              \n", "A              -0.000401            0.624832           -0.089514  \n", "AG             -0.019987            0.624667           -0.057680  \n", "AL             -0.046212            0.601001           -0.108251  \n", "AP             -0.019862            0.624826           -0.136182  \n", "AU             -0.043724            0.601191           -0.043615  \n", "B              -0.019382            0.601366           -0.105164  \n", "BU             -0.000717            0.624518           -0.150897  \n", "C              -0.022244            0.624147           -0.158246  \n", "CF             -0.022268            0.624958           -0.147979  \n", "CJ             -0.016013            0.601283           -0.075840  \n", "CS             -0.013439            0.624675           -0.108020  \n", "CU              0.007757            0.601161           -0.017201  \n", "CY             -0.004765            0.625160           -0.136216  \n", "EB             -0.003536            0.624544           -0.099191  \n", "EG             -0.013731            0.624520           -0.090314  \n", "FG             -0.026276            0.625379           -0.138623  \n", "HC             -0.018466            0.624392           -0.140130  \n", "I              -0.014431            0.624825           -0.136703  \n", "J              -0.033721            0.601200           -0.074384  \n", "JD             -0.017376            0.601276           -0.126122  \n", "JM             -0.022175            0.624823           -0.124776  \n", "L              -0.001636            0.624662           -0.087363  \n", "LH              0.000564            0.601010           -0.141837  \n", "M              -0.010842            0.624236           -0.115610  \n", "MA             -0.011072            0.624957           -0.110793  \n", "NI             -0.005599            0.601018           -0.067438  \n", "NR             -0.019018            0.601140            0.025036  \n", "OI             -0.013950            0.625440           -0.094578  \n", "P              -0.009131            0.624558           -0.093347  \n", "PB             -0.014575            0.600736           -0.084049  \n", "PF             -0.031600            0.601188           -0.012164  \n", "PG             -0.029711            0.601125           -0.076562  \n", "PK              0.005658            0.625345           -0.135346  \n", "PP              0.001052            0.624250           -0.104176  \n", "RB             -0.028871            0.624443           -0.148579  \n", "RM             -0.011625            0.624699           -0.147925  \n", "RR             -0.016139            0.600852           -0.188327  \n", "RU             -0.018735            0.624040           -0.123055  \n", "SA             -0.020028            0.624388           -0.137281  \n", "SC             -0.012503            0.631402           -0.013279  \n", "SF             -0.013752            0.624611           -0.122153  \n", "SM             -0.006654            0.624496           -0.176284  \n", "SN             -0.010280            0.601099           -0.094411  \n", "SP             -0.011503            0.624107           -0.133034  \n", "SR             -0.010486            0.625726           -0.145731  \n", "SS             -0.012877            0.624717           -0.082086  \n", "TA             -0.001653            0.624763           -0.073719  \n", "UR             -0.023178            0.624665           -0.139462  \n", "V              -0.011936            0.624435           -0.107304  \n", "Y              -0.006865            0.624662           -0.108261  \n", "ZN              0.004472            0.601022           -0.052773  \n", "\n", "[51 rows x 46 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df_mean"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["# 用df_mk_mean和df_mk_std填充df_mean和df_std\n", "df_mean.loc[:,'FAST_AG_RSI':] = df_mean_all.mean().values\n", "df_std.loc[:,'FAST_AG_RSI':] = df_std_all.mean().values\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FAST_AG_RSI</th>\n", "      <th>FAST_AG_ZSCORE</th>\n", "      <th>SLOW_AG_RSI</th>\n", "      <th>SLOW_AG_ZSCORE</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_ZSCORE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_ZSCORE</th>\n", "      <th>FAST_AG_STDDEV</th>\n", "      <th>FAST_AG_DIRECT</th>\n", "      <th>...</th>\n", "      <th>SLOW_QH_NATR_STDDEV</th>\n", "      <th>SLOW_QH_NATR_DIRECT</th>\n", "      <th>FAST_QH_MOM</th>\n", "      <th>FAST_QH_MOM_ZSCORE</th>\n", "      <th>SLOW_QH_MOM</th>\n", "      <th>SLOW_QH_MOM_ZSCORE</th>\n", "      <th>FAST_QH_MOM_STDDEV</th>\n", "      <th>FAST_QH_MOM_DIRECT</th>\n", "      <th>SLOW_QH_MOM_STDDEV</th>\n", "      <th>SLOW_QH_MOM_DIRECT</th>\n", "    </tr>\n", "    <tr>\n", "      <th>code</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>A</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AL</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AP</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>C</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CF</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CJ</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CS</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CY</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EB</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HC</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>I</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>J</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>JD</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>JM</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>L</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LH</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>M</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MA</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NI</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OI</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>P</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PB</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PF</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PG</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PK</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PP</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RB</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RU</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SA</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SC</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SF</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SM</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SN</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SP</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SS</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TA</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UR</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>V</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Y</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZN</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.230359</td>\n", "      <td>-0.086156</td>\n", "      <td>-0.94409</td>\n", "      <td>-0.267296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.240288e+19</td>\n", "      <td>0.0</td>\n", "      <td>-0.002088</td>\n", "      <td>0.01827</td>\n", "      <td>-0.057081</td>\n", "      <td>-0.015521</td>\n", "      <td>9.775728</td>\n", "      <td>-0.015559</td>\n", "      <td>0.617821</td>\n", "      <td>-0.103023</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>51 rows × 33 columns</p>\n", "</div>"], "text/plain": ["      FAST_AG_RSI  FAST_AG_ZSCORE  SLOW_AG_RSI  SLOW_AG_ZSCORE  FAST_QH_RSI  \\\n", "code                                                                          \n", "A             0.0             0.0          0.0             0.0    -0.230359   \n", "AG            0.0             0.0          0.0             0.0    -0.230359   \n", "AL            0.0             0.0          0.0             0.0    -0.230359   \n", "AP            0.0             0.0          0.0             0.0    -0.230359   \n", "AU            0.0             0.0          0.0             0.0    -0.230359   \n", "B             0.0             0.0          0.0             0.0    -0.230359   \n", "BU            0.0             0.0          0.0             0.0    -0.230359   \n", "C             0.0             0.0          0.0             0.0    -0.230359   \n", "CF            0.0             0.0          0.0             0.0    -0.230359   \n", "CJ            0.0             0.0          0.0             0.0    -0.230359   \n", "CS            0.0             0.0          0.0             0.0    -0.230359   \n", "CU            0.0             0.0          0.0             0.0    -0.230359   \n", "CY            0.0             0.0          0.0             0.0    -0.230359   \n", "EB            0.0             0.0          0.0             0.0    -0.230359   \n", "EG            0.0             0.0          0.0             0.0    -0.230359   \n", "FG            0.0             0.0          0.0             0.0    -0.230359   \n", "HC            0.0             0.0          0.0             0.0    -0.230359   \n", "I             0.0             0.0          0.0             0.0    -0.230359   \n", "J             0.0             0.0          0.0             0.0    -0.230359   \n", "JD            0.0             0.0          0.0             0.0    -0.230359   \n", "JM            0.0             0.0          0.0             0.0    -0.230359   \n", "L             0.0             0.0          0.0             0.0    -0.230359   \n", "LH            0.0             0.0          0.0             0.0    -0.230359   \n", "M             0.0             0.0          0.0             0.0    -0.230359   \n", "MA            0.0             0.0          0.0             0.0    -0.230359   \n", "NI            0.0             0.0          0.0             0.0    -0.230359   \n", "NR            0.0             0.0          0.0             0.0    -0.230359   \n", "OI            0.0             0.0          0.0             0.0    -0.230359   \n", "P             0.0             0.0          0.0             0.0    -0.230359   \n", "PB            0.0             0.0          0.0             0.0    -0.230359   \n", "PF            0.0             0.0          0.0             0.0    -0.230359   \n", "PG            0.0             0.0          0.0             0.0    -0.230359   \n", "PK            0.0             0.0          0.0             0.0    -0.230359   \n", "PP            0.0             0.0          0.0             0.0    -0.230359   \n", "RB            0.0             0.0          0.0             0.0    -0.230359   \n", "RM            0.0             0.0          0.0             0.0    -0.230359   \n", "RR            0.0             0.0          0.0             0.0    -0.230359   \n", "RU            0.0             0.0          0.0             0.0    -0.230359   \n", "SA            0.0             0.0          0.0             0.0    -0.230359   \n", "SC            0.0             0.0          0.0             0.0    -0.230359   \n", "SF            0.0             0.0          0.0             0.0    -0.230359   \n", "SM            0.0             0.0          0.0             0.0    -0.230359   \n", "SN            0.0             0.0          0.0             0.0    -0.230359   \n", "SP            0.0             0.0          0.0             0.0    -0.230359   \n", "SR            0.0             0.0          0.0             0.0    -0.230359   \n", "SS            0.0             0.0          0.0             0.0    -0.230359   \n", "TA            0.0             0.0          0.0             0.0    -0.230359   \n", "UR            0.0             0.0          0.0             0.0    -0.230359   \n", "V             0.0             0.0          0.0             0.0    -0.230359   \n", "Y             0.0             0.0          0.0             0.0    -0.230359   \n", "ZN            0.0             0.0          0.0             0.0    -0.230359   \n", "\n", "      FAST_QH_ZSCORE  SLOW_QH_RSI  SLOW_QH_ZSCORE  FAST_AG_STDDEV  \\\n", "code                                                                \n", "A          -0.086156     -0.94409       -0.267296             0.0   \n", "AG         -0.086156     -0.94409       -0.267296             0.0   \n", "AL         -0.086156     -0.94409       -0.267296             0.0   \n", "AP         -0.086156     -0.94409       -0.267296             0.0   \n", "AU         -0.086156     -0.94409       -0.267296             0.0   \n", "B          -0.086156     -0.94409       -0.267296             0.0   \n", "BU         -0.086156     -0.94409       -0.267296             0.0   \n", "C          -0.086156     -0.94409       -0.267296             0.0   \n", "CF         -0.086156     -0.94409       -0.267296             0.0   \n", "CJ         -0.086156     -0.94409       -0.267296             0.0   \n", "CS         -0.086156     -0.94409       -0.267296             0.0   \n", "CU         -0.086156     -0.94409       -0.267296             0.0   \n", "CY         -0.086156     -0.94409       -0.267296             0.0   \n", "EB         -0.086156     -0.94409       -0.267296             0.0   \n", "EG         -0.086156     -0.94409       -0.267296             0.0   \n", "FG         -0.086156     -0.94409       -0.267296             0.0   \n", "HC         -0.086156     -0.94409       -0.267296             0.0   \n", "I          -0.086156     -0.94409       -0.267296             0.0   \n", "J          -0.086156     -0.94409       -0.267296             0.0   \n", "JD         -0.086156     -0.94409       -0.267296             0.0   \n", "JM         -0.086156     -0.94409       -0.267296             0.0   \n", "L          -0.086156     -0.94409       -0.267296             0.0   \n", "LH         -0.086156     -0.94409       -0.267296             0.0   \n", "M          -0.086156     -0.94409       -0.267296             0.0   \n", "MA         -0.086156     -0.94409       -0.267296             0.0   \n", "NI         -0.086156     -0.94409       -0.267296             0.0   \n", "NR         -0.086156     -0.94409       -0.267296             0.0   \n", "OI         -0.086156     -0.94409       -0.267296             0.0   \n", "P          -0.086156     -0.94409       -0.267296             0.0   \n", "PB         -0.086156     -0.94409       -0.267296             0.0   \n", "PF         -0.086156     -0.94409       -0.267296             0.0   \n", "PG         -0.086156     -0.94409       -0.267296             0.0   \n", "PK         -0.086156     -0.94409       -0.267296             0.0   \n", "PP         -0.086156     -0.94409       -0.267296             0.0   \n", "RB         -0.086156     -0.94409       -0.267296             0.0   \n", "RM         -0.086156     -0.94409       -0.267296             0.0   \n", "RR         -0.086156     -0.94409       -0.267296             0.0   \n", "RU         -0.086156     -0.94409       -0.267296             0.0   \n", "SA         -0.086156     -0.94409       -0.267296             0.0   \n", "SC         -0.086156     -0.94409       -0.267296             0.0   \n", "SF         -0.086156     -0.94409       -0.267296             0.0   \n", "SM         -0.086156     -0.94409       -0.267296             0.0   \n", "SN         -0.086156     -0.94409       -0.267296             0.0   \n", "SP         -0.086156     -0.94409       -0.267296             0.0   \n", "SR         -0.086156     -0.94409       -0.267296             0.0   \n", "SS         -0.086156     -0.94409       -0.267296             0.0   \n", "TA         -0.086156     -0.94409       -0.267296             0.0   \n", "UR         -0.086156     -0.94409       -0.267296             0.0   \n", "V          -0.086156     -0.94409       -0.267296             0.0   \n", "Y          -0.086156     -0.94409       -0.267296             0.0   \n", "ZN         -0.086156     -0.94409       -0.267296             0.0   \n", "\n", "      FAST_AG_DIRECT  ...  SLOW_QH_NATR_STDDEV  SLOW_QH_NATR_DIRECT  \\\n", "code                  ...                                             \n", "A                0.0  ...         4.240288e+19                  0.0   \n", "AG               0.0  ...         4.240288e+19                  0.0   \n", "AL               0.0  ...         4.240288e+19                  0.0   \n", "AP               0.0  ...         4.240288e+19                  0.0   \n", "AU               0.0  ...         4.240288e+19                  0.0   \n", "B                0.0  ...         4.240288e+19                  0.0   \n", "BU               0.0  ...         4.240288e+19                  0.0   \n", "C                0.0  ...         4.240288e+19                  0.0   \n", "CF               0.0  ...         4.240288e+19                  0.0   \n", "CJ               0.0  ...         4.240288e+19                  0.0   \n", "CS               0.0  ...         4.240288e+19                  0.0   \n", "CU               0.0  ...         4.240288e+19                  0.0   \n", "CY               0.0  ...         4.240288e+19                  0.0   \n", "EB               0.0  ...         4.240288e+19                  0.0   \n", "EG               0.0  ...         4.240288e+19                  0.0   \n", "FG               0.0  ...         4.240288e+19                  0.0   \n", "HC               0.0  ...         4.240288e+19                  0.0   \n", "I                0.0  ...         4.240288e+19                  0.0   \n", "J                0.0  ...         4.240288e+19                  0.0   \n", "JD               0.0  ...         4.240288e+19                  0.0   \n", "JM               0.0  ...         4.240288e+19                  0.0   \n", "L                0.0  ...         4.240288e+19                  0.0   \n", "LH               0.0  ...         4.240288e+19                  0.0   \n", "M                0.0  ...         4.240288e+19                  0.0   \n", "MA               0.0  ...         4.240288e+19                  0.0   \n", "NI               0.0  ...         4.240288e+19                  0.0   \n", "NR               0.0  ...         4.240288e+19                  0.0   \n", "OI               0.0  ...         4.240288e+19                  0.0   \n", "P                0.0  ...         4.240288e+19                  0.0   \n", "PB               0.0  ...         4.240288e+19                  0.0   \n", "PF               0.0  ...         4.240288e+19                  0.0   \n", "PG               0.0  ...         4.240288e+19                  0.0   \n", "PK               0.0  ...         4.240288e+19                  0.0   \n", "PP               0.0  ...         4.240288e+19                  0.0   \n", "RB               0.0  ...         4.240288e+19                  0.0   \n", "RM               0.0  ...         4.240288e+19                  0.0   \n", "RR               0.0  ...         4.240288e+19                  0.0   \n", "RU               0.0  ...         4.240288e+19                  0.0   \n", "SA               0.0  ...         4.240288e+19                  0.0   \n", "SC               0.0  ...         4.240288e+19                  0.0   \n", "SF               0.0  ...         4.240288e+19                  0.0   \n", "SM               0.0  ...         4.240288e+19                  0.0   \n", "SN               0.0  ...         4.240288e+19                  0.0   \n", "SP               0.0  ...         4.240288e+19                  0.0   \n", "SR               0.0  ...         4.240288e+19                  0.0   \n", "SS               0.0  ...         4.240288e+19                  0.0   \n", "TA               0.0  ...         4.240288e+19                  0.0   \n", "UR               0.0  ...         4.240288e+19                  0.0   \n", "V                0.0  ...         4.240288e+19                  0.0   \n", "Y                0.0  ...         4.240288e+19                  0.0   \n", "ZN               0.0  ...         4.240288e+19                  0.0   \n", "\n", "      FAST_QH_MOM  FAST_QH_MOM_ZSCORE  SLOW_QH_MOM  SLOW_QH_MOM_ZSCORE  \\\n", "code                                                                     \n", "A       -0.002088             0.01827    -0.057081           -0.015521   \n", "AG      -0.002088             0.01827    -0.057081           -0.015521   \n", "AL      -0.002088             0.01827    -0.057081           -0.015521   \n", "AP      -0.002088             0.01827    -0.057081           -0.015521   \n", "AU      -0.002088             0.01827    -0.057081           -0.015521   \n", "B       -0.002088             0.01827    -0.057081           -0.015521   \n", "BU      -0.002088             0.01827    -0.057081           -0.015521   \n", "C       -0.002088             0.01827    -0.057081           -0.015521   \n", "CF      -0.002088             0.01827    -0.057081           -0.015521   \n", "CJ      -0.002088             0.01827    -0.057081           -0.015521   \n", "CS      -0.002088             0.01827    -0.057081           -0.015521   \n", "CU      -0.002088             0.01827    -0.057081           -0.015521   \n", "CY      -0.002088             0.01827    -0.057081           -0.015521   \n", "EB      -0.002088             0.01827    -0.057081           -0.015521   \n", "EG      -0.002088             0.01827    -0.057081           -0.015521   \n", "FG      -0.002088             0.01827    -0.057081           -0.015521   \n", "HC      -0.002088             0.01827    -0.057081           -0.015521   \n", "I       -0.002088             0.01827    -0.057081           -0.015521   \n", "J       -0.002088             0.01827    -0.057081           -0.015521   \n", "JD      -0.002088             0.01827    -0.057081           -0.015521   \n", "JM      -0.002088             0.01827    -0.057081           -0.015521   \n", "L       -0.002088             0.01827    -0.057081           -0.015521   \n", "LH      -0.002088             0.01827    -0.057081           -0.015521   \n", "M       -0.002088             0.01827    -0.057081           -0.015521   \n", "MA      -0.002088             0.01827    -0.057081           -0.015521   \n", "NI      -0.002088             0.01827    -0.057081           -0.015521   \n", "NR      -0.002088             0.01827    -0.057081           -0.015521   \n", "OI      -0.002088             0.01827    -0.057081           -0.015521   \n", "P       -0.002088             0.01827    -0.057081           -0.015521   \n", "PB      -0.002088             0.01827    -0.057081           -0.015521   \n", "PF      -0.002088             0.01827    -0.057081           -0.015521   \n", "PG      -0.002088             0.01827    -0.057081           -0.015521   \n", "PK      -0.002088             0.01827    -0.057081           -0.015521   \n", "PP      -0.002088             0.01827    -0.057081           -0.015521   \n", "RB      -0.002088             0.01827    -0.057081           -0.015521   \n", "RM      -0.002088             0.01827    -0.057081           -0.015521   \n", "RR      -0.002088             0.01827    -0.057081           -0.015521   \n", "RU      -0.002088             0.01827    -0.057081           -0.015521   \n", "SA      -0.002088             0.01827    -0.057081           -0.015521   \n", "SC      -0.002088             0.01827    -0.057081           -0.015521   \n", "SF      -0.002088             0.01827    -0.057081           -0.015521   \n", "SM      -0.002088             0.01827    -0.057081           -0.015521   \n", "SN      -0.002088             0.01827    -0.057081           -0.015521   \n", "SP      -0.002088             0.01827    -0.057081           -0.015521   \n", "SR      -0.002088             0.01827    -0.057081           -0.015521   \n", "SS      -0.002088             0.01827    -0.057081           -0.015521   \n", "TA      -0.002088             0.01827    -0.057081           -0.015521   \n", "UR      -0.002088             0.01827    -0.057081           -0.015521   \n", "V       -0.002088             0.01827    -0.057081           -0.015521   \n", "Y       -0.002088             0.01827    -0.057081           -0.015521   \n", "ZN      -0.002088             0.01827    -0.057081           -0.015521   \n", "\n", "      FAST_QH_MOM_STDDEV  FAST_QH_MOM_DIRECT  SLOW_QH_MOM_STDDEV  \\\n", "code                                                               \n", "A               9.775728           -0.015559            0.617821   \n", "AG              9.775728           -0.015559            0.617821   \n", "AL              9.775728           -0.015559            0.617821   \n", "AP              9.775728           -0.015559            0.617821   \n", "AU              9.775728           -0.015559            0.617821   \n", "B               9.775728           -0.015559            0.617821   \n", "BU              9.775728           -0.015559            0.617821   \n", "C               9.775728           -0.015559            0.617821   \n", "CF              9.775728           -0.015559            0.617821   \n", "CJ              9.775728           -0.015559            0.617821   \n", "CS              9.775728           -0.015559            0.617821   \n", "CU              9.775728           -0.015559            0.617821   \n", "CY              9.775728           -0.015559            0.617821   \n", "EB              9.775728           -0.015559            0.617821   \n", "EG              9.775728           -0.015559            0.617821   \n", "FG              9.775728           -0.015559            0.617821   \n", "HC              9.775728           -0.015559            0.617821   \n", "I               9.775728           -0.015559            0.617821   \n", "J               9.775728           -0.015559            0.617821   \n", "JD              9.775728           -0.015559            0.617821   \n", "JM              9.775728           -0.015559            0.617821   \n", "L               9.775728           -0.015559            0.617821   \n", "LH              9.775728           -0.015559            0.617821   \n", "M               9.775728           -0.015559            0.617821   \n", "MA              9.775728           -0.015559            0.617821   \n", "NI              9.775728           -0.015559            0.617821   \n", "NR              9.775728           -0.015559            0.617821   \n", "OI              9.775728           -0.015559            0.617821   \n", "P               9.775728           -0.015559            0.617821   \n", "PB              9.775728           -0.015559            0.617821   \n", "PF              9.775728           -0.015559            0.617821   \n", "PG              9.775728           -0.015559            0.617821   \n", "PK              9.775728           -0.015559            0.617821   \n", "PP              9.775728           -0.015559            0.617821   \n", "RB              9.775728           -0.015559            0.617821   \n", "RM              9.775728           -0.015559            0.617821   \n", "RR              9.775728           -0.015559            0.617821   \n", "RU              9.775728           -0.015559            0.617821   \n", "SA              9.775728           -0.015559            0.617821   \n", "SC              9.775728           -0.015559            0.617821   \n", "SF              9.775728           -0.015559            0.617821   \n", "SM              9.775728           -0.015559            0.617821   \n", "SN              9.775728           -0.015559            0.617821   \n", "SP              9.775728           -0.015559            0.617821   \n", "SR              9.775728           -0.015559            0.617821   \n", "SS              9.775728           -0.015559            0.617821   \n", "TA              9.775728           -0.015559            0.617821   \n", "UR              9.775728           -0.015559            0.617821   \n", "V               9.775728           -0.015559            0.617821   \n", "Y               9.775728           -0.015559            0.617821   \n", "ZN              9.775728           -0.015559            0.617821   \n", "\n", "      SLOW_QH_MOM_DIRECT  \n", "code                      \n", "A              -0.103023  \n", "AG             -0.103023  \n", "AL             -0.103023  \n", "AP             -0.103023  \n", "AU             -0.103023  \n", "B              -0.103023  \n", "BU             -0.103023  \n", "C              -0.103023  \n", "CF             -0.103023  \n", "CJ             -0.103023  \n", "CS             -0.103023  \n", "CU             -0.103023  \n", "CY             -0.103023  \n", "EB             -0.103023  \n", "EG             -0.103023  \n", "FG             -0.103023  \n", "HC             -0.103023  \n", "I              -0.103023  \n", "J              -0.103023  \n", "JD             -0.103023  \n", "JM             -0.103023  \n", "L              -0.103023  \n", "LH             -0.103023  \n", "M              -0.103023  \n", "MA             -0.103023  \n", "NI             -0.103023  \n", "NR             -0.103023  \n", "OI             -0.103023  \n", "P              -0.103023  \n", "PB             -0.103023  \n", "PF             -0.103023  \n", "PG             -0.103023  \n", "PK             -0.103023  \n", "PP             -0.103023  \n", "RB             -0.103023  \n", "RM             -0.103023  \n", "RR             -0.103023  \n", "RU             -0.103023  \n", "SA             -0.103023  \n", "SC             -0.103023  \n", "SF             -0.103023  \n", "SM             -0.103023  \n", "SN             -0.103023  \n", "SP             -0.103023  \n", "SR             -0.103023  \n", "SS             -0.103023  \n", "TA             -0.103023  \n", "UR             -0.103023  \n", "V              -0.103023  \n", "Y              -0.103023  \n", "ZN             -0.103023  \n", "\n", "[51 rows x 33 columns]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["df_mean.loc[:, 'FAST_AG_RSI':]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 41 entries, FAST_AG_RSI to STDDEV_RNG\n", "Series name: None\n", "Non-Null Count  Dtype  \n", "--------------  -----  \n", "41 non-null     float64\n", "dtypes: float64(1)\n", "memory usage: 656.0+ bytes\n"]}], "source": ["df_mean_all.mean().info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算商品期货因子数据的均值和标准差\n", "type='ct'\n", "df_mean = {}\n", "df_std = {}\n", "years = ['2019', '2020', '2021', '2022', '2023']\n", "for year in years:\n", "    df_mean[year], df_std[year] = calc_factor_mean_std(year, type, is_sf=False)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df_mean_all = pd.concat(df_mean.values()).mean()\n", "df_std_all = pd.concat(df_std.values()).mean()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AD_PS_RATIO            0.000000e+00\n", "COST_RNG               0.000000e+00\n", "CURRENT_TIME           1.439267e+03\n", "DRAWDOWN_RNG           0.000000e+00\n", "FAST_AG_DIRECT         0.000000e+00\n", "FAST_AG_RSI            0.000000e+00\n", "FAST_AG_STDDEV         0.000000e+00\n", "FAST_AG_ZSCORE         0.000000e+00\n", "FAST_QH_DIRECT        -5.449819e-02\n", "FAST_QH_MOM           -6.907862e-02\n", "FAST_QH_MOM_DIRECT    -6.001617e-02\n", "FAST_QH_MOM_STDDEV     5.125988e+00\n", "FAST_QH_MOM_ZSCORE    -6.531642e-03\n", "FAST_QH_NATR           2.776541e-01\n", "FAST_QH_NATR_DIRECT    2.102947e-01\n", "FAST_QH_NATR_STDDEV    9.466473e-02\n", "FAST_QH_NATR_ZSCORE    3.905130e-01\n", "FAST_QH_RSI           -8.598927e-02\n", "FAST_QH_STDDEV         3.719098e+00\n", "FAST_QH_ZSCORE        -4.419087e-02\n", "LONG_RANGE             1.316022e+02\n", "PF_PS_RATIO            0.000000e+00\n", "PF_YIELD_HL            0.000000e+00\n", "PF_YIELD_TREND         0.000000e+00\n", "PNL                    0.000000e+00\n", "POS_DAYS               0.000000e+00\n", "POS_LONG_BARS          0.000000e+00\n", "POS_SHORT_BARS         0.000000e+00\n", "SHORT_RANGE            1.821831e+01\n", "SLOW_AG_DIRECT         0.000000e+00\n", "SLOW_AG_RSI            0.000000e+00\n", "SLOW_AG_STDDEV         0.000000e+00\n", "SLOW_AG_ZSCORE         0.000000e+00\n", "SLOW_QH_DIRECT         1.605505e-02\n", "SLOW_QH_MOM            1.616471e+00\n", "SLOW_QH_MOM_DIRECT    -6.396523e-02\n", "SLOW_QH_MOM_STDDEV     7.615938e-01\n", "SLOW_QH_MOM_ZSCORE     5.911737e-02\n", "SLOW_QH_NATR           1.907996e+20\n", "SLOW_QH_NATR_DIRECT   -1.028756e-01\n", "SLOW_QH_NATR_STDDEV    5.092580e+19\n", "SLOW_QH_NATR_ZSCORE    3.218342e-01\n", "SLOW_QH_RSI           -3.803222e-01\n", "SLOW_QH_STDDEV         2.993700e+00\n", "SLOW_QH_ZSCORE        -4.024966e-02\n", "STDDEV_RNG             1.346262e+00\n", "dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_mean_all"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# 将所有因子数据的mean和std合并求均值\n", "df_mean_all = pd.concat(df_mean.values()).groupby('code').mean()\n", "df_std_all = pd.concat(df_std.values()).groupby('code').mean()\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>AD_1</th>\n", "      <th>AD_2</th>\n", "      <th>ADX_1</th>\n", "      <th>ADX_2</th>\n", "      <th>ADXR_1</th>\n", "      <th>ADXR_2</th>\n", "      <th>APO_1</th>\n", "      <th>APO_2</th>\n", "      <th>AROON_DOWN_1</th>\n", "      <th>...</th>\n", "      <th>TSF_1</th>\n", "      <th>TSF_2</th>\n", "      <th>TYPICAL_PRICE_1</th>\n", "      <th>TYPICAL_PRICE_2</th>\n", "      <th>ULTOSC_1</th>\n", "      <th>ULTOSC_2</th>\n", "      <th>VOLUME_1</th>\n", "      <th>VOLUME_2</th>\n", "      <th>WILLR_1</th>\n", "      <th>WILLR_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>4.528238e+05</td>\n", "      <td>4.529545e+05</td>\n", "      <td>16.349598</td>\n", "      <td>16.338921</td>\n", "      <td>16.652684</td>\n", "      <td>16.628786</td>\n", "      <td>3.312706</td>\n", "      <td>2.365804</td>\n", "      <td>49.803590</td>\n", "      <td>...</td>\n", "      <td>5063.922603</td>\n", "      <td>5064.511394</td>\n", "      <td>5095.000000</td>\n", "      <td>5057.000000</td>\n", "      <td>50.951200</td>\n", "      <td>50.832162</td>\n", "      <td>6186.758544</td>\n", "      <td>5056.822658</td>\n", "      <td>-49.283130</td>\n", "      <td>-49.478221</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AG</td>\n", "      <td>2.749595e+04</td>\n", "      <td>2.695114e+04</td>\n", "      <td>16.833112</td>\n", "      <td>16.834971</td>\n", "      <td>16.881484</td>\n", "      <td>16.869892</td>\n", "      <td>-2.399900</td>\n", "      <td>-3.300638</td>\n", "      <td>50.012473</td>\n", "      <td>...</td>\n", "      <td>4807.210960</td>\n", "      <td>4807.843235</td>\n", "      <td>5450.000000</td>\n", "      <td>5460.000000</td>\n", "      <td>50.284327</td>\n", "      <td>50.391201</td>\n", "      <td>6621.929270</td>\n", "      <td>5564.029043</td>\n", "      <td>-49.745092</td>\n", "      <td>-49.686744</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AL</td>\n", "      <td>5.209930e+05</td>\n", "      <td>5.201685e+05</td>\n", "      <td>15.599240</td>\n", "      <td>15.573809</td>\n", "      <td>15.561840</td>\n", "      <td>15.557681</td>\n", "      <td>-17.509418</td>\n", "      <td>-18.400387</td>\n", "      <td>54.951818</td>\n", "      <td>...</td>\n", "      <td>18478.698894</td>\n", "      <td>18479.506808</td>\n", "      <td>18181.666667</td>\n", "      <td>18103.333333</td>\n", "      <td>49.157599</td>\n", "      <td>49.200670</td>\n", "      <td>7694.599388</td>\n", "      <td>5633.095062</td>\n", "      <td>-55.042814</td>\n", "      <td>-54.106552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AP</td>\n", "      <td>-1.216039e+06</td>\n", "      <td>-1.216761e+06</td>\n", "      <td>20.396594</td>\n", "      <td>20.383789</td>\n", "      <td>20.537499</td>\n", "      <td>20.508696</td>\n", "      <td>3.760520</td>\n", "      <td>-1.179525</td>\n", "      <td>53.246499</td>\n", "      <td>...</td>\n", "      <td>8150.063123</td>\n", "      <td>8150.451699</td>\n", "      <td>8416.333333</td>\n", "      <td>8535.000000</td>\n", "      <td>48.762141</td>\n", "      <td>48.749624</td>\n", "      <td>8921.435208</td>\n", "      <td>7250.164675</td>\n", "      <td>-52.266243</td>\n", "      <td>-51.937836</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AU</td>\n", "      <td>-1.819423e+05</td>\n", "      <td>-1.837474e+05</td>\n", "      <td>15.449693</td>\n", "      <td>15.404730</td>\n", "      <td>16.171460</td>\n", "      <td>16.089117</td>\n", "      <td>0.281289</td>\n", "      <td>0.240427</td>\n", "      <td>39.896309</td>\n", "      <td>...</td>\n", "      <td>430.297268</td>\n", "      <td>430.369490</td>\n", "      <td>450.186666</td>\n", "      <td>447.720011</td>\n", "      <td>53.625420</td>\n", "      <td>53.464274</td>\n", "      <td>5448.128888</td>\n", "      <td>5927.171416</td>\n", "      <td>-46.269014</td>\n", "      <td>-46.893249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>B</td>\n", "      <td>1.240542e+06</td>\n", "      <td>1.241539e+06</td>\n", "      <td>19.923577</td>\n", "      <td>19.853892</td>\n", "      <td>20.216168</td>\n", "      <td>20.132826</td>\n", "      <td>-23.490576</td>\n", "      <td>-23.966154</td>\n", "      <td>60.290109</td>\n", "      <td>...</td>\n", "      <td>4448.744387</td>\n", "      <td>4444.960476</td>\n", "      <td>4377.333333</td>\n", "      <td>4276.333333</td>\n", "      <td>46.264677</td>\n", "      <td>46.330859</td>\n", "      <td>6649.885332</td>\n", "      <td>5724.938227</td>\n", "      <td>-63.941597</td>\n", "      <td>-63.980150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>BU</td>\n", "      <td>-3.807377e+04</td>\n", "      <td>-3.845493e+04</td>\n", "      <td>16.542685</td>\n", "      <td>16.527629</td>\n", "      <td>16.647042</td>\n", "      <td>16.636328</td>\n", "      <td>5.423934</td>\n", "      <td>4.065884</td>\n", "      <td>47.431178</td>\n", "      <td>...</td>\n", "      <td>3304.260506</td>\n", "      <td>3305.118763</td>\n", "      <td>3588.333333</td>\n", "      <td>3569.000000</td>\n", "      <td>50.833213</td>\n", "      <td>50.758071</td>\n", "      <td>6806.814788</td>\n", "      <td>5594.969767</td>\n", "      <td>-46.649608</td>\n", "      <td>-47.009658</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>-1.863768e+05</td>\n", "      <td>-1.926606e+05</td>\n", "      <td>17.429061</td>\n", "      <td>17.428198</td>\n", "      <td>17.613708</td>\n", "      <td>17.605493</td>\n", "      <td>5.037697</td>\n", "      <td>4.511916</td>\n", "      <td>47.066604</td>\n", "      <td>...</td>\n", "      <td>2475.732553</td>\n", "      <td>2476.389728</td>\n", "      <td>2705.333333</td>\n", "      <td>2699.666667</td>\n", "      <td>51.198153</td>\n", "      <td>51.037252</td>\n", "      <td>6543.401547</td>\n", "      <td>5140.231054</td>\n", "      <td>-46.886310</td>\n", "      <td>-47.039477</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>CF</td>\n", "      <td>-1.229374e+04</td>\n", "      <td>-1.231886e+04</td>\n", "      <td>15.517519</td>\n", "      <td>15.540882</td>\n", "      <td>15.665222</td>\n", "      <td>15.660186</td>\n", "      <td>1.039567</td>\n", "      <td>-2.342819</td>\n", "      <td>46.599137</td>\n", "      <td>...</td>\n", "      <td>15305.596920</td>\n", "      <td>15305.687655</td>\n", "      <td>16255.000000</td>\n", "      <td>16315.000000</td>\n", "      <td>50.161710</td>\n", "      <td>50.159223</td>\n", "      <td>5492.240746</td>\n", "      <td>4364.467854</td>\n", "      <td>-47.465017</td>\n", "      <td>-47.658979</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>CJ</td>\n", "      <td>5.837177e+05</td>\n", "      <td>5.836709e+05</td>\n", "      <td>22.263041</td>\n", "      <td>22.233492</td>\n", "      <td>22.094757</td>\n", "      <td>22.195861</td>\n", "      <td>-21.273037</td>\n", "      <td>-25.939457</td>\n", "      <td>52.021819</td>\n", "      <td>...</td>\n", "      <td>10203.681920</td>\n", "      <td>10198.569783</td>\n", "      <td>10218.333333</td>\n", "      <td>10081.666667</td>\n", "      <td>48.004223</td>\n", "      <td>47.707286</td>\n", "      <td>9398.616576</td>\n", "      <td>7208.907267</td>\n", "      <td>-52.836495</td>\n", "      <td>-52.240345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>CS</td>\n", "      <td>-1.928995e+05</td>\n", "      <td>-1.926590e+05</td>\n", "      <td>16.100741</td>\n", "      <td>16.104075</td>\n", "      <td>16.160516</td>\n", "      <td>16.157368</td>\n", "      <td>-0.396829</td>\n", "      <td>-0.782506</td>\n", "      <td>51.557623</td>\n", "      <td>...</td>\n", "      <td>2821.734750</td>\n", "      <td>2821.831097</td>\n", "      <td>3044.333333</td>\n", "      <td>3060.666667</td>\n", "      <td>50.150106</td>\n", "      <td>50.158779</td>\n", "      <td>6599.123063</td>\n", "      <td>5125.489337</td>\n", "      <td>-50.206866</td>\n", "      <td>-50.375873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CU</td>\n", "      <td>-9.585520e+05</td>\n", "      <td>-9.601900e+05</td>\n", "      <td>15.012181</td>\n", "      <td>14.979564</td>\n", "      <td>14.918207</td>\n", "      <td>14.898788</td>\n", "      <td>59.588361</td>\n", "      <td>36.410298</td>\n", "      <td>49.343826</td>\n", "      <td>...</td>\n", "      <td>67998.744079</td>\n", "      <td>68002.590077</td>\n", "      <td>68410.000000</td>\n", "      <td>67813.333333</td>\n", "      <td>48.778253</td>\n", "      <td>48.909719</td>\n", "      <td>7398.264643</td>\n", "      <td>6328.062395</td>\n", "      <td>-50.613082</td>\n", "      <td>-50.477104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CY</td>\n", "      <td>3.237824e+04</td>\n", "      <td>3.317910e+04</td>\n", "      <td>16.380942</td>\n", "      <td>16.396723</td>\n", "      <td>16.464980</td>\n", "      <td>16.462096</td>\n", "      <td>6.406836</td>\n", "      <td>0.329843</td>\n", "      <td>48.350234</td>\n", "      <td>...</td>\n", "      <td>22613.956951</td>\n", "      <td>22614.817989</td>\n", "      <td>23001.666667</td>\n", "      <td>23058.333333</td>\n", "      <td>50.173291</td>\n", "      <td>50.288950</td>\n", "      <td>6229.050710</td>\n", "      <td>4972.084027</td>\n", "      <td>-48.312433</td>\n", "      <td>-48.406234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>EB</td>\n", "      <td>-2.906985e+05</td>\n", "      <td>-2.905999e+05</td>\n", "      <td>18.732944</td>\n", "      <td>18.733715</td>\n", "      <td>18.748264</td>\n", "      <td>18.739819</td>\n", "      <td>16.839863</td>\n", "      <td>12.096473</td>\n", "      <td>52.450018</td>\n", "      <td>...</td>\n", "      <td>7928.873073</td>\n", "      <td>7928.999366</td>\n", "      <td>7062.666667</td>\n", "      <td>7045.000000</td>\n", "      <td>49.243644</td>\n", "      <td>49.001581</td>\n", "      <td>6997.424220</td>\n", "      <td>5355.181820</td>\n", "      <td>-52.199615</td>\n", "      <td>-52.046524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>EG</td>\n", "      <td>-3.434496e+05</td>\n", "      <td>-3.437930e+05</td>\n", "      <td>16.314795</td>\n", "      <td>16.321468</td>\n", "      <td>16.353045</td>\n", "      <td>16.353356</td>\n", "      <td>-4.332100</td>\n", "      <td>-5.443385</td>\n", "      <td>51.259602</td>\n", "      <td>...</td>\n", "      <td>4515.982231</td>\n", "      <td>4515.841985</td>\n", "      <td>3876.333333</td>\n", "      <td>3910.000000</td>\n", "      <td>49.076001</td>\n", "      <td>49.177518</td>\n", "      <td>6969.599589</td>\n", "      <td>5452.814937</td>\n", "      <td>-51.411554</td>\n", "      <td>-51.347841</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>FG</td>\n", "      <td>1.585976e+05</td>\n", "      <td>1.591683e+05</td>\n", "      <td>16.822965</td>\n", "      <td>16.810612</td>\n", "      <td>17.003279</td>\n", "      <td>16.989486</td>\n", "      <td>-0.250840</td>\n", "      <td>-0.738284</td>\n", "      <td>48.903553</td>\n", "      <td>...</td>\n", "      <td>1723.865303</td>\n", "      <td>1723.962298</td>\n", "      <td>1496.000000</td>\n", "      <td>1502.000000</td>\n", "      <td>49.945797</td>\n", "      <td>50.109394</td>\n", "      <td>5499.603821</td>\n", "      <td>4175.139499</td>\n", "      <td>-48.322692</td>\n", "      <td>-48.198438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>HC</td>\n", "      <td>-1.862129e+05</td>\n", "      <td>-1.859495e+05</td>\n", "      <td>17.053435</td>\n", "      <td>17.041742</td>\n", "      <td>17.191521</td>\n", "      <td>17.168440</td>\n", "      <td>4.409038</td>\n", "      <td>3.344717</td>\n", "      <td>46.152372</td>\n", "      <td>...</td>\n", "      <td>4206.219524</td>\n", "      <td>4207.495294</td>\n", "      <td>3785.666667</td>\n", "      <td>3832.666667</td>\n", "      <td>50.639811</td>\n", "      <td>50.515103</td>\n", "      <td>6898.172828</td>\n", "      <td>5697.970679</td>\n", "      <td>-45.940936</td>\n", "      <td>-45.924633</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>I</td>\n", "      <td>7.268248e+05</td>\n", "      <td>7.267780e+05</td>\n", "      <td>17.055961</td>\n", "      <td>17.053740</td>\n", "      <td>17.154298</td>\n", "      <td>17.141298</td>\n", "      <td>0.176584</td>\n", "      <td>-0.246479</td>\n", "      <td>47.315115</td>\n", "      <td>...</td>\n", "      <td>798.775974</td>\n", "      <td>798.917863</td>\n", "      <td>805.833333</td>\n", "      <td>824.666667</td>\n", "      <td>51.006743</td>\n", "      <td>51.033967</td>\n", "      <td>6955.589799</td>\n", "      <td>6058.584462</td>\n", "      <td>-47.261279</td>\n", "      <td>-47.524463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>J</td>\n", "      <td>-4.472174e+05</td>\n", "      <td>-4.485791e+05</td>\n", "      <td>19.527575</td>\n", "      <td>19.642140</td>\n", "      <td>19.327607</td>\n", "      <td>19.368462</td>\n", "      <td>-9.667106</td>\n", "      <td>-11.441386</td>\n", "      <td>59.508832</td>\n", "      <td>...</td>\n", "      <td>2597.446668</td>\n", "      <td>2594.926019</td>\n", "      <td>2057.833333</td>\n", "      <td>2088.166667</td>\n", "      <td>48.455391</td>\n", "      <td>48.275157</td>\n", "      <td>7814.369510</td>\n", "      <td>6106.726410</td>\n", "      <td>-58.386827</td>\n", "      <td>-58.669251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>JD</td>\n", "      <td>4.059120e+05</td>\n", "      <td>4.059249e+05</td>\n", "      <td>17.041390</td>\n", "      <td>17.015549</td>\n", "      <td>17.342035</td>\n", "      <td>17.314644</td>\n", "      <td>4.225972</td>\n", "      <td>2.939628</td>\n", "      <td>45.761043</td>\n", "      <td>...</td>\n", "      <td>4321.507773</td>\n", "      <td>4321.126099</td>\n", "      <td>4143.666667</td>\n", "      <td>4146.333333</td>\n", "      <td>50.448135</td>\n", "      <td>50.238596</td>\n", "      <td>5291.122375</td>\n", "      <td>4196.997538</td>\n", "      <td>-45.397101</td>\n", "      <td>-46.162769</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>JM</td>\n", "      <td>2.183824e+05</td>\n", "      <td>2.181957e+05</td>\n", "      <td>17.265471</td>\n", "      <td>17.271197</td>\n", "      <td>17.437821</td>\n", "      <td>17.423748</td>\n", "      <td>-0.813430</td>\n", "      <td>-2.080572</td>\n", "      <td>50.199597</td>\n", "      <td>...</td>\n", "      <td>1746.896483</td>\n", "      <td>1746.470624</td>\n", "      <td>1308.333333</td>\n", "      <td>1328.833333</td>\n", "      <td>50.154944</td>\n", "      <td>50.150546</td>\n", "      <td>6830.416612</td>\n", "      <td>5469.594642</td>\n", "      <td>-49.541534</td>\n", "      <td>-49.506711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>L</td>\n", "      <td>-8.226094e+05</td>\n", "      <td>-9.306320e+05</td>\n", "      <td>15.448092</td>\n", "      <td>15.642733</td>\n", "      <td>15.347912</td>\n", "      <td>15.433214</td>\n", "      <td>-7.521542</td>\n", "      <td>-7.798375</td>\n", "      <td>52.741926</td>\n", "      <td>...</td>\n", "      <td>7986.577781</td>\n", "      <td>7980.712863</td>\n", "      <td>7776.333333</td>\n", "      <td>7798.666667</td>\n", "      <td>48.805337</td>\n", "      <td>48.689968</td>\n", "      <td>6715.124316</td>\n", "      <td>5594.843577</td>\n", "      <td>-53.070999</td>\n", "      <td>-52.282082</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>LH</td>\n", "      <td>-6.215002e+05</td>\n", "      <td>-6.223448e+05</td>\n", "      <td>28.233445</td>\n", "      <td>28.318807</td>\n", "      <td>29.002919</td>\n", "      <td>28.876838</td>\n", "      <td>-48.564882</td>\n", "      <td>-65.267168</td>\n", "      <td>62.909387</td>\n", "      <td>...</td>\n", "      <td>15830.586851</td>\n", "      <td>15810.479048</td>\n", "      <td>15578.333333</td>\n", "      <td>15558.333333</td>\n", "      <td>45.519453</td>\n", "      <td>45.627772</td>\n", "      <td>8647.239714</td>\n", "      <td>6357.465527</td>\n", "      <td>-64.627088</td>\n", "      <td>-63.838839</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>M</td>\n", "      <td>1.246119e+05</td>\n", "      <td>1.253348e+05</td>\n", "      <td>15.248108</td>\n", "      <td>15.189115</td>\n", "      <td>15.667833</td>\n", "      <td>15.615675</td>\n", "      <td>-0.137395</td>\n", "      <td>-1.031199</td>\n", "      <td>47.870025</td>\n", "      <td>...</td>\n", "      <td>3357.685788</td>\n", "      <td>3358.008154</td>\n", "      <td>3783.333333</td>\n", "      <td>3715.000000</td>\n", "      <td>50.177384</td>\n", "      <td>50.403170</td>\n", "      <td>6616.131990</td>\n", "      <td>5760.288670</td>\n", "      <td>-49.202636</td>\n", "      <td>-49.035460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MA</td>\n", "      <td>2.062131e+04</td>\n", "      <td>2.140952e+04</td>\n", "      <td>18.295513</td>\n", "      <td>18.220152</td>\n", "      <td>18.534119</td>\n", "      <td>18.491618</td>\n", "      <td>7.909077</td>\n", "      <td>9.011354</td>\n", "      <td>50.279056</td>\n", "      <td>...</td>\n", "      <td>2526.898644</td>\n", "      <td>2530.706673</td>\n", "      <td>2061.666667</td>\n", "      <td>2090.000000</td>\n", "      <td>50.755926</td>\n", "      <td>50.453485</td>\n", "      <td>5964.099643</td>\n", "      <td>4726.270236</td>\n", "      <td>-49.097540</td>\n", "      <td>-49.331149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>NI</td>\n", "      <td>-1.227856e+06</td>\n", "      <td>-1.229279e+06</td>\n", "      <td>17.634819</td>\n", "      <td>17.695064</td>\n", "      <td>17.308826</td>\n", "      <td>17.352046</td>\n", "      <td>-1799.969773</td>\n", "      <td>-1899.740633</td>\n", "      <td>66.090166</td>\n", "      <td>...</td>\n", "      <td>190299.831958</td>\n", "      <td>189922.870902</td>\n", "      <td>161423.333333</td>\n", "      <td>159140.000000</td>\n", "      <td>46.374614</td>\n", "      <td>45.991209</td>\n", "      <td>7184.158144</td>\n", "      <td>5997.248227</td>\n", "      <td>-63.324733</td>\n", "      <td>-62.231607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>NR</td>\n", "      <td>-6.480540e+05</td>\n", "      <td>-6.484740e+05</td>\n", "      <td>12.842226</td>\n", "      <td>12.804922</td>\n", "      <td>13.234865</td>\n", "      <td>13.201383</td>\n", "      <td>-3.562393</td>\n", "      <td>-2.712138</td>\n", "      <td>47.262148</td>\n", "      <td>...</td>\n", "      <td>9746.784638</td>\n", "      <td>9750.424223</td>\n", "      <td>9561.666667</td>\n", "      <td>9586.666667</td>\n", "      <td>51.002307</td>\n", "      <td>51.231404</td>\n", "      <td>7110.959552</td>\n", "      <td>5803.051884</td>\n", "      <td>-47.138495</td>\n", "      <td>-47.646467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>OI</td>\n", "      <td>3.693481e+04</td>\n", "      <td>3.699424e+04</td>\n", "      <td>15.658421</td>\n", "      <td>15.673990</td>\n", "      <td>15.700773</td>\n", "      <td>15.705904</td>\n", "      <td>-1.402399</td>\n", "      <td>-3.638312</td>\n", "      <td>50.514462</td>\n", "      <td>...</td>\n", "      <td>9483.964557</td>\n", "      <td>9483.820079</td>\n", "      <td>8646.333333</td>\n", "      <td>8504.000000</td>\n", "      <td>50.307927</td>\n", "      <td>50.107439</td>\n", "      <td>5765.739916</td>\n", "      <td>4594.288865</td>\n", "      <td>-49.598390</td>\n", "      <td>-49.649839</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>P</td>\n", "      <td>7.171566e+04</td>\n", "      <td>7.082602e+04</td>\n", "      <td>17.055646</td>\n", "      <td>17.083156</td>\n", "      <td>16.847632</td>\n", "      <td>16.877135</td>\n", "      <td>5.216223</td>\n", "      <td>2.587392</td>\n", "      <td>49.637618</td>\n", "      <td>...</td>\n", "      <td>7120.231250</td>\n", "      <td>7121.548328</td>\n", "      <td>7173.333333</td>\n", "      <td>7116.666667</td>\n", "      <td>50.996979</td>\n", "      <td>50.775861</td>\n", "      <td>6809.322846</td>\n", "      <td>5771.008880</td>\n", "      <td>-47.787959</td>\n", "      <td>-48.147661</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>PB</td>\n", "      <td>-1.342454e+06</td>\n", "      <td>-1.342290e+06</td>\n", "      <td>12.822550</td>\n", "      <td>12.833243</td>\n", "      <td>13.382870</td>\n", "      <td>13.365039</td>\n", "      <td>-25.090030</td>\n", "      <td>-27.397150</td>\n", "      <td>60.746019</td>\n", "      <td>...</td>\n", "      <td>15313.744086</td>\n", "      <td>15308.950406</td>\n", "      <td>15530.000000</td>\n", "      <td>15473.333333</td>\n", "      <td>46.024730</td>\n", "      <td>46.627893</td>\n", "      <td>6948.700729</td>\n", "      <td>6614.934575</td>\n", "      <td>-60.850136</td>\n", "      <td>-59.525948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>PF</td>\n", "      <td>-8.955862e+05</td>\n", "      <td>-8.956822e+05</td>\n", "      <td>15.968695</td>\n", "      <td>15.958785</td>\n", "      <td>16.343917</td>\n", "      <td>16.298404</td>\n", "      <td>0.592468</td>\n", "      <td>-1.345955</td>\n", "      <td>56.222968</td>\n", "      <td>...</td>\n", "      <td>7273.852394</td>\n", "      <td>7273.700079</td>\n", "      <td>7007.333333</td>\n", "      <td>7010.666667</td>\n", "      <td>49.945763</td>\n", "      <td>49.812388</td>\n", "      <td>6400.103468</td>\n", "      <td>4830.958753</td>\n", "      <td>-51.050783</td>\n", "      <td>-50.514133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>PG</td>\n", "      <td>9.760508e+04</td>\n", "      <td>9.948867e+04</td>\n", "      <td>20.850204</td>\n", "      <td>20.814836</td>\n", "      <td>20.211820</td>\n", "      <td>20.202833</td>\n", "      <td>-8.568021</td>\n", "      <td>-9.504012</td>\n", "      <td>53.072626</td>\n", "      <td>...</td>\n", "      <td>4573.134905</td>\n", "      <td>4573.272109</td>\n", "      <td>3664.666667</td>\n", "      <td>3662.666667</td>\n", "      <td>47.961949</td>\n", "      <td>48.520860</td>\n", "      <td>7147.673057</td>\n", "      <td>5995.517395</td>\n", "      <td>-53.457080</td>\n", "      <td>-52.975123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>PK</td>\n", "      <td>1.240021e+06</td>\n", "      <td>1.242444e+06</td>\n", "      <td>20.866282</td>\n", "      <td>20.858805</td>\n", "      <td>20.933152</td>\n", "      <td>20.953002</td>\n", "      <td>-6.716302</td>\n", "      <td>-10.117609</td>\n", "      <td>50.451297</td>\n", "      <td>...</td>\n", "      <td>9853.919544</td>\n", "      <td>9853.376387</td>\n", "      <td>9877.333333</td>\n", "      <td>9903.333333</td>\n", "      <td>50.294482</td>\n", "      <td>50.284737</td>\n", "      <td>8325.160463</td>\n", "      <td>6585.318395</td>\n", "      <td>-52.040200</td>\n", "      <td>-52.553569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>PP</td>\n", "      <td>-1.531453e+05</td>\n", "      <td>-1.532289e+05</td>\n", "      <td>15.317421</td>\n", "      <td>15.328989</td>\n", "      <td>15.413143</td>\n", "      <td>15.415251</td>\n", "      <td>-2.490520</td>\n", "      <td>-4.408838</td>\n", "      <td>53.167634</td>\n", "      <td>...</td>\n", "      <td>8069.093692</td>\n", "      <td>8068.909098</td>\n", "      <td>6973.000000</td>\n", "      <td>6992.333333</td>\n", "      <td>49.169030</td>\n", "      <td>49.108600</td>\n", "      <td>6862.600986</td>\n", "      <td>5566.897771</td>\n", "      <td>-51.943956</td>\n", "      <td>-51.884814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>RB</td>\n", "      <td>1.728588e+05</td>\n", "      <td>1.737780e+05</td>\n", "      <td>16.087582</td>\n", "      <td>16.095602</td>\n", "      <td>16.130547</td>\n", "      <td>16.138311</td>\n", "      <td>4.346934</td>\n", "      <td>3.523435</td>\n", "      <td>47.613192</td>\n", "      <td>...</td>\n", "      <td>4113.234214</td>\n", "      <td>4114.731698</td>\n", "      <td>3690.666667</td>\n", "      <td>3724.333333</td>\n", "      <td>51.480813</td>\n", "      <td>51.352088</td>\n", "      <td>6891.904915</td>\n", "      <td>5903.577437</td>\n", "      <td>-46.326241</td>\n", "      <td>-46.433114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>RM</td>\n", "      <td>1.201258e+05</td>\n", "      <td>1.199033e+05</td>\n", "      <td>15.575397</td>\n", "      <td>15.559345</td>\n", "      <td>15.859055</td>\n", "      <td>15.830452</td>\n", "      <td>0.225991</td>\n", "      <td>-0.420755</td>\n", "      <td>49.862346</td>\n", "      <td>...</td>\n", "      <td>2775.092321</td>\n", "      <td>2775.192917</td>\n", "      <td>3319.666667</td>\n", "      <td>3245.666667</td>\n", "      <td>50.739285</td>\n", "      <td>50.618976</td>\n", "      <td>5778.382062</td>\n", "      <td>4491.235564</td>\n", "      <td>-48.771570</td>\n", "      <td>-49.118112</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>RR</td>\n", "      <td>6.396208e+05</td>\n", "      <td>6.399234e+05</td>\n", "      <td>15.678845</td>\n", "      <td>15.760159</td>\n", "      <td>15.227795</td>\n", "      <td>15.315929</td>\n", "      <td>0.629252</td>\n", "      <td>0.511863</td>\n", "      <td>49.397727</td>\n", "      <td>...</td>\n", "      <td>3385.579206</td>\n", "      <td>3385.842335</td>\n", "      <td>3457.000000</td>\n", "      <td>3545.666667</td>\n", "      <td>50.386615</td>\n", "      <td>50.134126</td>\n", "      <td>6157.899405</td>\n", "      <td>5050.206650</td>\n", "      <td>-48.470547</td>\n", "      <td>-48.843028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>RU</td>\n", "      <td>-1.851206e+05</td>\n", "      <td>-1.857754e+05</td>\n", "      <td>15.782989</td>\n", "      <td>15.793250</td>\n", "      <td>15.884909</td>\n", "      <td>15.868904</td>\n", "      <td>-13.943172</td>\n", "      <td>-14.600552</td>\n", "      <td>50.978444</td>\n", "      <td>...</td>\n", "      <td>12686.429547</td>\n", "      <td>12688.638020</td>\n", "      <td>11910.000000</td>\n", "      <td>11940.000000</td>\n", "      <td>49.595247</td>\n", "      <td>49.778241</td>\n", "      <td>6361.537911</td>\n", "      <td>5691.363067</td>\n", "      <td>-50.326872</td>\n", "      <td>-50.287450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>SA</td>\n", "      <td>-3.999166e+03</td>\n", "      <td>-3.852648e+03</td>\n", "      <td>17.919553</td>\n", "      <td>17.920833</td>\n", "      <td>17.895665</td>\n", "      <td>17.899517</td>\n", "      <td>-1.243327</td>\n", "      <td>-2.017166</td>\n", "      <td>52.814963</td>\n", "      <td>...</td>\n", "      <td>2253.351890</td>\n", "      <td>2253.219229</td>\n", "      <td>1682.333333</td>\n", "      <td>1658.666667</td>\n", "      <td>48.911598</td>\n", "      <td>49.075333</td>\n", "      <td>5391.058364</td>\n", "      <td>4161.175488</td>\n", "      <td>-51.704330</td>\n", "      <td>-51.762480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>SC</td>\n", "      <td>-1.379037e+05</td>\n", "      <td>-1.382123e+05</td>\n", "      <td>18.471357</td>\n", "      <td>18.448910</td>\n", "      <td>18.511007</td>\n", "      <td>18.488503</td>\n", "      <td>0.141267</td>\n", "      <td>0.028621</td>\n", "      <td>48.837047</td>\n", "      <td>...</td>\n", "      <td>477.730729</td>\n", "      <td>477.797181</td>\n", "      <td>540.966654</td>\n", "      <td>538.033325</td>\n", "      <td>50.154445</td>\n", "      <td>50.236434</td>\n", "      <td>4895.340857</td>\n", "      <td>4110.676158</td>\n", "      <td>-47.757176</td>\n", "      <td>-48.004464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>SF</td>\n", "      <td>1.122002e+06</td>\n", "      <td>1.124224e+06</td>\n", "      <td>19.988702</td>\n", "      <td>19.964785</td>\n", "      <td>19.836340</td>\n", "      <td>19.859754</td>\n", "      <td>-28.402802</td>\n", "      <td>-32.035090</td>\n", "      <td>52.600844</td>\n", "      <td>...</td>\n", "      <td>7525.527063</td>\n", "      <td>7520.742766</td>\n", "      <td>7170.666667</td>\n", "      <td>7167.333333</td>\n", "      <td>49.789901</td>\n", "      <td>49.806574</td>\n", "      <td>9173.718555</td>\n", "      <td>6844.754603</td>\n", "      <td>-51.814726</td>\n", "      <td>-51.877296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>SM</td>\n", "      <td>5.282130e+05</td>\n", "      <td>5.280547e+05</td>\n", "      <td>19.015104</td>\n", "      <td>19.005489</td>\n", "      <td>19.185097</td>\n", "      <td>19.157229</td>\n", "      <td>-18.799631</td>\n", "      <td>-22.054228</td>\n", "      <td>55.604767</td>\n", "      <td>...</td>\n", "      <td>7383.821474</td>\n", "      <td>7380.439424</td>\n", "      <td>6599.333333</td>\n", "      <td>6625.333333</td>\n", "      <td>48.082182</td>\n", "      <td>48.407973</td>\n", "      <td>9069.356418</td>\n", "      <td>6917.090200</td>\n", "      <td>-54.219513</td>\n", "      <td>-53.613107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>SP</td>\n", "      <td>4.793835e+05</td>\n", "      <td>4.802398e+05</td>\n", "      <td>15.832423</td>\n", "      <td>15.843556</td>\n", "      <td>15.953265</td>\n", "      <td>15.953272</td>\n", "      <td>-0.362014</td>\n", "      <td>-2.281457</td>\n", "      <td>53.095161</td>\n", "      <td>...</td>\n", "      <td>5718.375461</td>\n", "      <td>5718.362691</td>\n", "      <td>5097.333333</td>\n", "      <td>5135.333333</td>\n", "      <td>50.167205</td>\n", "      <td>50.235443</td>\n", "      <td>6623.542185</td>\n", "      <td>5458.095026</td>\n", "      <td>-50.743679</td>\n", "      <td>-50.768455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>SR</td>\n", "      <td>3.179478e+05</td>\n", "      <td>3.181491e+05</td>\n", "      <td>15.853011</td>\n", "      <td>15.873500</td>\n", "      <td>15.851179</td>\n", "      <td>15.863418</td>\n", "      <td>0.786600</td>\n", "      <td>-0.014846</td>\n", "      <td>47.827275</td>\n", "      <td>...</td>\n", "      <td>5638.238756</td>\n", "      <td>5638.526720</td>\n", "      <td>6806.666667</td>\n", "      <td>6658.333333</td>\n", "      <td>50.800423</td>\n", "      <td>50.703902</td>\n", "      <td>5651.911741</td>\n", "      <td>4426.382154</td>\n", "      <td>-48.704430</td>\n", "      <td>-48.905501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>SS</td>\n", "      <td>2.018455e+05</td>\n", "      <td>2.024196e+05</td>\n", "      <td>15.400860</td>\n", "      <td>15.411622</td>\n", "      <td>15.324959</td>\n", "      <td>15.340765</td>\n", "      <td>-15.568294</td>\n", "      <td>-17.817231</td>\n", "      <td>53.855356</td>\n", "      <td>...</td>\n", "      <td>15689.088866</td>\n", "      <td>15687.927330</td>\n", "      <td>14688.333333</td>\n", "      <td>14791.666667</td>\n", "      <td>49.699355</td>\n", "      <td>49.802957</td>\n", "      <td>7184.826948</td>\n", "      <td>5237.367493</td>\n", "      <td>-52.664563</td>\n", "      <td>-52.502208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>TA</td>\n", "      <td>-1.254018e+05</td>\n", "      <td>-1.257164e+05</td>\n", "      <td>16.475755</td>\n", "      <td>16.484813</td>\n", "      <td>16.456920</td>\n", "      <td>16.459796</td>\n", "      <td>-2.816738</td>\n", "      <td>-4.328573</td>\n", "      <td>50.329854</td>\n", "      <td>...</td>\n", "      <td>5112.392173</td>\n", "      <td>5111.980190</td>\n", "      <td>5486.666667</td>\n", "      <td>5488.000000</td>\n", "      <td>49.498154</td>\n", "      <td>49.586318</td>\n", "      <td>5810.022450</td>\n", "      <td>4565.976852</td>\n", "      <td>-51.164018</td>\n", "      <td>-51.240672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>UR</td>\n", "      <td>-6.886537e+05</td>\n", "      <td>-6.890078e+05</td>\n", "      <td>19.449682</td>\n", "      <td>19.555531</td>\n", "      <td>18.965660</td>\n", "      <td>19.054286</td>\n", "      <td>-7.085095</td>\n", "      <td>-8.174760</td>\n", "      <td>54.224941</td>\n", "      <td>...</td>\n", "      <td>2105.808470</td>\n", "      <td>2104.505646</td>\n", "      <td>1726.000000</td>\n", "      <td>1761.000000</td>\n", "      <td>48.166125</td>\n", "      <td>48.387505</td>\n", "      <td>9129.112160</td>\n", "      <td>7014.147954</td>\n", "      <td>-51.978660</td>\n", "      <td>-52.207432</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>V</td>\n", "      <td>7.582406e+04</td>\n", "      <td>7.563803e+04</td>\n", "      <td>15.761078</td>\n", "      <td>15.783047</td>\n", "      <td>15.863929</td>\n", "      <td>15.856456</td>\n", "      <td>-5.489686</td>\n", "      <td>-7.001944</td>\n", "      <td>49.761694</td>\n", "      <td>...</td>\n", "      <td>7134.277382</td>\n", "      <td>7133.918333</td>\n", "      <td>5730.000000</td>\n", "      <td>5747.333333</td>\n", "      <td>49.906378</td>\n", "      <td>49.799369</td>\n", "      <td>6771.787436</td>\n", "      <td>5347.065671</td>\n", "      <td>-50.612477</td>\n", "      <td>-50.631405</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>Y</td>\n", "      <td>2.441756e+05</td>\n", "      <td>2.440912e+05</td>\n", "      <td>16.445270</td>\n", "      <td>16.467482</td>\n", "      <td>16.455478</td>\n", "      <td>16.471773</td>\n", "      <td>3.497958</td>\n", "      <td>1.088555</td>\n", "      <td>50.057038</td>\n", "      <td>...</td>\n", "      <td>7854.771295</td>\n", "      <td>7855.545294</td>\n", "      <td>7682.000000</td>\n", "      <td>7577.333333</td>\n", "      <td>50.622792</td>\n", "      <td>50.416558</td>\n", "      <td>6584.329001</td>\n", "      <td>5460.684267</td>\n", "      <td>-47.900175</td>\n", "      <td>-48.332724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>ZN</td>\n", "      <td>-1.235995e+06</td>\n", "      <td>-1.237253e+06</td>\n", "      <td>15.384040</td>\n", "      <td>15.373001</td>\n", "      <td>15.539274</td>\n", "      <td>15.500534</td>\n", "      <td>-70.330046</td>\n", "      <td>-71.926468</td>\n", "      <td>59.707066</td>\n", "      <td>...</td>\n", "      <td>22447.269708</td>\n", "      <td>22439.932454</td>\n", "      <td>20030.000000</td>\n", "      <td>20041.666667</td>\n", "      <td>47.378710</td>\n", "      <td>47.118554</td>\n", "      <td>6396.072898</td>\n", "      <td>6398.085532</td>\n", "      <td>-59.153386</td>\n", "      <td>-58.930451</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>50 rows × 233 columns</p>\n", "</div>"], "text/plain": ["   code          AD_1          AD_2      ADX_1      ADX_2     ADXR_1  \\\n", "0     A  4.528238e+05  4.529545e+05  16.349598  16.338921  16.652684   \n", "1    AG  2.749595e+04  2.695114e+04  16.833112  16.834971  16.881484   \n", "2    AL  5.209930e+05  5.201685e+05  15.599240  15.573809  15.561840   \n", "3    AP -1.216039e+06 -1.216761e+06  20.396594  20.383789  20.537499   \n", "4    AU -1.819423e+05 -1.837474e+05  15.449693  15.404730  16.171460   \n", "5     B  1.240542e+06  1.241539e+06  19.923577  19.853892  20.216168   \n", "6    BU -3.807377e+04 -3.845493e+04  16.542685  16.527629  16.647042   \n", "7     C -1.863768e+05 -1.926606e+05  17.429061  17.428198  17.613708   \n", "8    CF -1.229374e+04 -1.231886e+04  15.517519  15.540882  15.665222   \n", "9    CJ  5.837177e+05  5.836709e+05  22.263041  22.233492  22.094757   \n", "10   CS -1.928995e+05 -1.926590e+05  16.100741  16.104075  16.160516   \n", "11   CU -9.585520e+05 -9.601900e+05  15.012181  14.979564  14.918207   \n", "12   CY  3.237824e+04  3.317910e+04  16.380942  16.396723  16.464980   \n", "13   EB -2.906985e+05 -2.905999e+05  18.732944  18.733715  18.748264   \n", "14   EG -3.434496e+05 -3.437930e+05  16.314795  16.321468  16.353045   \n", "15   FG  1.585976e+05  1.591683e+05  16.822965  16.810612  17.003279   \n", "16   HC -1.862129e+05 -1.859495e+05  17.053435  17.041742  17.191521   \n", "17    I  7.268248e+05  7.267780e+05  17.055961  17.053740  17.154298   \n", "18    J -4.472174e+05 -4.485791e+05  19.527575  19.642140  19.327607   \n", "19   JD  4.059120e+05  4.059249e+05  17.041390  17.015549  17.342035   \n", "20   JM  2.183824e+05  2.181957e+05  17.265471  17.271197  17.437821   \n", "21    L -8.226094e+05 -9.306320e+05  15.448092  15.642733  15.347912   \n", "22   LH -6.215002e+05 -6.223448e+05  28.233445  28.318807  29.002919   \n", "23    M  1.246119e+05  1.253348e+05  15.248108  15.189115  15.667833   \n", "24   MA  2.062131e+04  2.140952e+04  18.295513  18.220152  18.534119   \n", "25   NI -1.227856e+06 -1.229279e+06  17.634819  17.695064  17.308826   \n", "26   NR -6.480540e+05 -6.484740e+05  12.842226  12.804922  13.234865   \n", "27   OI  3.693481e+04  3.699424e+04  15.658421  15.673990  15.700773   \n", "28    P  7.171566e+04  7.082602e+04  17.055646  17.083156  16.847632   \n", "29   PB -1.342454e+06 -1.342290e+06  12.822550  12.833243  13.382870   \n", "30   PF -8.955862e+05 -8.956822e+05  15.968695  15.958785  16.343917   \n", "31   PG  9.760508e+04  9.948867e+04  20.850204  20.814836  20.211820   \n", "32   PK  1.240021e+06  1.242444e+06  20.866282  20.858805  20.933152   \n", "33   PP -1.531453e+05 -1.532289e+05  15.317421  15.328989  15.413143   \n", "34   RB  1.728588e+05  1.737780e+05  16.087582  16.095602  16.130547   \n", "35   RM  1.201258e+05  1.199033e+05  15.575397  15.559345  15.859055   \n", "36   RR  6.396208e+05  6.399234e+05  15.678845  15.760159  15.227795   \n", "37   RU -1.851206e+05 -1.857754e+05  15.782989  15.793250  15.884909   \n", "38   SA -3.999166e+03 -3.852648e+03  17.919553  17.920833  17.895665   \n", "39   SC -1.379037e+05 -1.382123e+05  18.471357  18.448910  18.511007   \n", "40   SF  1.122002e+06  1.124224e+06  19.988702  19.964785  19.836340   \n", "41   SM  5.282130e+05  5.280547e+05  19.015104  19.005489  19.185097   \n", "42   SP  4.793835e+05  4.802398e+05  15.832423  15.843556  15.953265   \n", "43   SR  3.179478e+05  3.181491e+05  15.853011  15.873500  15.851179   \n", "44   SS  2.018455e+05  2.024196e+05  15.400860  15.411622  15.324959   \n", "45   TA -1.254018e+05 -1.257164e+05  16.475755  16.484813  16.456920   \n", "46   UR -6.886537e+05 -6.890078e+05  19.449682  19.555531  18.965660   \n", "47    V  7.582406e+04  7.563803e+04  15.761078  15.783047  15.863929   \n", "48    Y  2.441756e+05  2.440912e+05  16.445270  16.467482  16.455478   \n", "49   ZN -1.235995e+06 -1.237253e+06  15.384040  15.373001  15.539274   \n", "\n", "       ADXR_2        APO_1        APO_2  AROON_DOWN_1  ...          TSF_1  \\\n", "0   16.628786     3.312706     2.365804     49.803590  ...    5063.922603   \n", "1   16.869892    -2.399900    -3.300638     50.012473  ...    4807.210960   \n", "2   15.557681   -17.509418   -18.400387     54.951818  ...   18478.698894   \n", "3   20.508696     3.760520    -1.179525     53.246499  ...    8150.063123   \n", "4   16.089117     0.281289     0.240427     39.896309  ...     430.297268   \n", "5   20.132826   -23.490576   -23.966154     60.290109  ...    4448.744387   \n", "6   16.636328     5.423934     4.065884     47.431178  ...    3304.260506   \n", "7   17.605493     5.037697     4.511916     47.066604  ...    2475.732553   \n", "8   15.660186     1.039567    -2.342819     46.599137  ...   15305.596920   \n", "9   22.195861   -21.273037   -25.939457     52.021819  ...   10203.681920   \n", "10  16.157368    -0.396829    -0.782506     51.557623  ...    2821.734750   \n", "11  14.898788    59.588361    36.410298     49.343826  ...   67998.744079   \n", "12  16.462096     6.406836     0.329843     48.350234  ...   22613.956951   \n", "13  18.739819    16.839863    12.096473     52.450018  ...    7928.873073   \n", "14  16.353356    -4.332100    -5.443385     51.259602  ...    4515.982231   \n", "15  16.989486    -0.250840    -0.738284     48.903553  ...    1723.865303   \n", "16  17.168440     4.409038     3.344717     46.152372  ...    4206.219524   \n", "17  17.141298     0.176584    -0.246479     47.315115  ...     798.775974   \n", "18  19.368462    -9.667106   -11.441386     59.508832  ...    2597.446668   \n", "19  17.314644     4.225972     2.939628     45.761043  ...    4321.507773   \n", "20  17.423748    -0.813430    -2.080572     50.199597  ...    1746.896483   \n", "21  15.433214    -7.521542    -7.798375     52.741926  ...    7986.577781   \n", "22  28.876838   -48.564882   -65.267168     62.909387  ...   15830.586851   \n", "23  15.615675    -0.137395    -1.031199     47.870025  ...    3357.685788   \n", "24  18.491618     7.909077     9.011354     50.279056  ...    2526.898644   \n", "25  17.352046 -1799.969773 -1899.740633     66.090166  ...  190299.831958   \n", "26  13.201383    -3.562393    -2.712138     47.262148  ...    9746.784638   \n", "27  15.705904    -1.402399    -3.638312     50.514462  ...    9483.964557   \n", "28  16.877135     5.216223     2.587392     49.637618  ...    7120.231250   \n", "29  13.365039   -25.090030   -27.397150     60.746019  ...   15313.744086   \n", "30  16.298404     0.592468    -1.345955     56.222968  ...    7273.852394   \n", "31  20.202833    -8.568021    -9.504012     53.072626  ...    4573.134905   \n", "32  20.953002    -6.716302   -10.117609     50.451297  ...    9853.919544   \n", "33  15.415251    -2.490520    -4.408838     53.167634  ...    8069.093692   \n", "34  16.138311     4.346934     3.523435     47.613192  ...    4113.234214   \n", "35  15.830452     0.225991    -0.420755     49.862346  ...    2775.092321   \n", "36  15.315929     0.629252     0.511863     49.397727  ...    3385.579206   \n", "37  15.868904   -13.943172   -14.600552     50.978444  ...   12686.429547   \n", "38  17.899517    -1.243327    -2.017166     52.814963  ...    2253.351890   \n", "39  18.488503     0.141267     0.028621     48.837047  ...     477.730729   \n", "40  19.859754   -28.402802   -32.035090     52.600844  ...    7525.527063   \n", "41  19.157229   -18.799631   -22.054228     55.604767  ...    7383.821474   \n", "42  15.953272    -0.362014    -2.281457     53.095161  ...    5718.375461   \n", "43  15.863418     0.786600    -0.014846     47.827275  ...    5638.238756   \n", "44  15.340765   -15.568294   -17.817231     53.855356  ...   15689.088866   \n", "45  16.459796    -2.816738    -4.328573     50.329854  ...    5112.392173   \n", "46  19.054286    -7.085095    -8.174760     54.224941  ...    2105.808470   \n", "47  15.856456    -5.489686    -7.001944     49.761694  ...    7134.277382   \n", "48  16.471773     3.497958     1.088555     50.057038  ...    7854.771295   \n", "49  15.500534   -70.330046   -71.926468     59.707066  ...   22447.269708   \n", "\n", "            TSF_2  TYPICAL_PRICE_1  TYPICAL_PRICE_2   ULTOSC_1   ULTOSC_2  \\\n", "0     5064.511394      5095.000000      5057.000000  50.951200  50.832162   \n", "1     4807.843235      5450.000000      5460.000000  50.284327  50.391201   \n", "2    18479.506808     18181.666667     18103.333333  49.157599  49.200670   \n", "3     8150.451699      8416.333333      8535.000000  48.762141  48.749624   \n", "4      430.369490       450.186666       447.720011  53.625420  53.464274   \n", "5     4444.960476      4377.333333      4276.333333  46.264677  46.330859   \n", "6     3305.118763      3588.333333      3569.000000  50.833213  50.758071   \n", "7     2476.389728      2705.333333      2699.666667  51.198153  51.037252   \n", "8    15305.687655     16255.000000     16315.000000  50.161710  50.159223   \n", "9    10198.569783     10218.333333     10081.666667  48.004223  47.707286   \n", "10    2821.831097      3044.333333      3060.666667  50.150106  50.158779   \n", "11   68002.590077     68410.000000     67813.333333  48.778253  48.909719   \n", "12   22614.817989     23001.666667     23058.333333  50.173291  50.288950   \n", "13    7928.999366      7062.666667      7045.000000  49.243644  49.001581   \n", "14    4515.841985      3876.333333      3910.000000  49.076001  49.177518   \n", "15    1723.962298      1496.000000      1502.000000  49.945797  50.109394   \n", "16    4207.495294      3785.666667      3832.666667  50.639811  50.515103   \n", "17     798.917863       805.833333       824.666667  51.006743  51.033967   \n", "18    2594.926019      2057.833333      2088.166667  48.455391  48.275157   \n", "19    4321.126099      4143.666667      4146.333333  50.448135  50.238596   \n", "20    1746.470624      1308.333333      1328.833333  50.154944  50.150546   \n", "21    7980.712863      7776.333333      7798.666667  48.805337  48.689968   \n", "22   15810.479048     15578.333333     15558.333333  45.519453  45.627772   \n", "23    3358.008154      3783.333333      3715.000000  50.177384  50.403170   \n", "24    2530.706673      2061.666667      2090.000000  50.755926  50.453485   \n", "25  189922.870902    161423.333333    159140.000000  46.374614  45.991209   \n", "26    9750.424223      9561.666667      9586.666667  51.002307  51.231404   \n", "27    9483.820079      8646.333333      8504.000000  50.307927  50.107439   \n", "28    7121.548328      7173.333333      7116.666667  50.996979  50.775861   \n", "29   15308.950406     15530.000000     15473.333333  46.024730  46.627893   \n", "30    7273.700079      7007.333333      7010.666667  49.945763  49.812388   \n", "31    4573.272109      3664.666667      3662.666667  47.961949  48.520860   \n", "32    9853.376387      9877.333333      9903.333333  50.294482  50.284737   \n", "33    8068.909098      6973.000000      6992.333333  49.169030  49.108600   \n", "34    4114.731698      3690.666667      3724.333333  51.480813  51.352088   \n", "35    2775.192917      3319.666667      3245.666667  50.739285  50.618976   \n", "36    3385.842335      3457.000000      3545.666667  50.386615  50.134126   \n", "37   12688.638020     11910.000000     11940.000000  49.595247  49.778241   \n", "38    2253.219229      1682.333333      1658.666667  48.911598  49.075333   \n", "39     477.797181       540.966654       538.033325  50.154445  50.236434   \n", "40    7520.742766      7170.666667      7167.333333  49.789901  49.806574   \n", "41    7380.439424      6599.333333      6625.333333  48.082182  48.407973   \n", "42    5718.362691      5097.333333      5135.333333  50.167205  50.235443   \n", "43    5638.526720      6806.666667      6658.333333  50.800423  50.703902   \n", "44   15687.927330     14688.333333     14791.666667  49.699355  49.802957   \n", "45    5111.980190      5486.666667      5488.000000  49.498154  49.586318   \n", "46    2104.505646      1726.000000      1761.000000  48.166125  48.387505   \n", "47    7133.918333      5730.000000      5747.333333  49.906378  49.799369   \n", "48    7855.545294      7682.000000      7577.333333  50.622792  50.416558   \n", "49   22439.932454     20030.000000     20041.666667  47.378710  47.118554   \n", "\n", "       VOLUME_1     VOLUME_2    WILLR_1    WILLR_2  \n", "0   6186.758544  5056.822658 -49.283130 -49.478221  \n", "1   6621.929270  5564.029043 -49.745092 -49.686744  \n", "2   7694.599388  5633.095062 -55.042814 -54.106552  \n", "3   8921.435208  7250.164675 -52.266243 -51.937836  \n", "4   5448.128888  5927.171416 -46.269014 -46.893249  \n", "5   6649.885332  5724.938227 -63.941597 -63.980150  \n", "6   6806.814788  5594.969767 -46.649608 -47.009658  \n", "7   6543.401547  5140.231054 -46.886310 -47.039477  \n", "8   5492.240746  4364.467854 -47.465017 -47.658979  \n", "9   9398.616576  7208.907267 -52.836495 -52.240345  \n", "10  6599.123063  5125.489337 -50.206866 -50.375873  \n", "11  7398.264643  6328.062395 -50.613082 -50.477104  \n", "12  6229.050710  4972.084027 -48.312433 -48.406234  \n", "13  6997.424220  5355.181820 -52.199615 -52.046524  \n", "14  6969.599589  5452.814937 -51.411554 -51.347841  \n", "15  5499.603821  4175.139499 -48.322692 -48.198438  \n", "16  6898.172828  5697.970679 -45.940936 -45.924633  \n", "17  6955.589799  6058.584462 -47.261279 -47.524463  \n", "18  7814.369510  6106.726410 -58.386827 -58.669251  \n", "19  5291.122375  4196.997538 -45.397101 -46.162769  \n", "20  6830.416612  5469.594642 -49.541534 -49.506711  \n", "21  6715.124316  5594.843577 -53.070999 -52.282082  \n", "22  8647.239714  6357.465527 -64.627088 -63.838839  \n", "23  6616.131990  5760.288670 -49.202636 -49.035460  \n", "24  5964.099643  4726.270236 -49.097540 -49.331149  \n", "25  7184.158144  5997.248227 -63.324733 -62.231607  \n", "26  7110.959552  5803.051884 -47.138495 -47.646467  \n", "27  5765.739916  4594.288865 -49.598390 -49.649839  \n", "28  6809.322846  5771.008880 -47.787959 -48.147661  \n", "29  6948.700729  6614.934575 -60.850136 -59.525948  \n", "30  6400.103468  4830.958753 -51.050783 -50.514133  \n", "31  7147.673057  5995.517395 -53.457080 -52.975123  \n", "32  8325.160463  6585.318395 -52.040200 -52.553569  \n", "33  6862.600986  5566.897771 -51.943956 -51.884814  \n", "34  6891.904915  5903.577437 -46.326241 -46.433114  \n", "35  5778.382062  4491.235564 -48.771570 -49.118112  \n", "36  6157.899405  5050.206650 -48.470547 -48.843028  \n", "37  6361.537911  5691.363067 -50.326872 -50.287450  \n", "38  5391.058364  4161.175488 -51.704330 -51.762480  \n", "39  4895.340857  4110.676158 -47.757176 -48.004464  \n", "40  9173.718555  6844.754603 -51.814726 -51.877296  \n", "41  9069.356418  6917.090200 -54.219513 -53.613107  \n", "42  6623.542185  5458.095026 -50.743679 -50.768455  \n", "43  5651.911741  4426.382154 -48.704430 -48.905501  \n", "44  7184.826948  5237.367493 -52.664563 -52.502208  \n", "45  5810.022450  4565.976852 -51.164018 -51.240672  \n", "46  9129.112160  7014.147954 -51.978660 -52.207432  \n", "47  6771.787436  5347.065671 -50.612477 -50.631405  \n", "48  6584.329001  5460.684267 -47.900175 -48.332724  \n", "49  6396.072898  6398.085532 -59.153386 -58.930451  \n", "\n", "[50 rows x 233 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_mean_all.reset_index()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mean_all = df_mean_all[get_factor_cols(type)]\n", "df_std_all = df_std_all[get_factor_cols(type)]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 归一化"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["112\n", "15\n"]}], "source": ["print(len(ALL_FACTOR_NAMES))\n", "print(len(TWO_VAL_FACTOR_NAMES))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["127\n"]}], "source": ["colnames = get_factor_cols()\n", "print(len(colnames))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(55, 127) (55, 127)\n"]}], "source": ["df_mean = pd.read_csv(f'{feat_path}/sf_mean.csv', index_col=0)\n", "df_std = pd.read_csv(f'{feat_path}/sf_std.csv', index_col=0)\n", "print(df_mean.shape, df_std.shape)\n", "def standardize(group):\n", "    code = group.name\n", "    mean = df_mean.loc[code]\n", "    std = df_std.loc[code]\n", "    group = (group - mean) / std\n", "    return group"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022 (625289, 235)\n", "2022 (625289, 235)\n", "2023 (203139, 235)\n", "2023 (203139, 235)\n", "(828428, 233)\n"]}], "source": ["# years = ['2019', '2020', '2021', '2022', '2023']\n", "years = ['2022', '2023']\n", "type = 'sf'\n", "dfs = pd.DataFrame()\n", "for year in years:\n", "    df = get_factor_df(year, type)\n", "    dfs = pd.concat([dfs, df])\n", "# dfs.sort_values(by=['code', 'date'], inplace=True)\n", "print(dfs.shape)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_32048\\23522937.py:1: FutureWarning: Not prepending group keys to the result index of transform-like apply. In the future, the group keys will be included in the index, regardless of whether the applied function returns a like-indexed object.\n", "To preserve the previous behavior, use\n", "\n", "\t>>> .groupby(..., group_keys=False)\n", "\n", "To adopt the future behavior and silence this warning, use \n", "\n", "\t>>> .groupby(..., group_keys=True)\n", "  df_standardized = dfs.groupby('code').apply(standardize)\n"]}], "source": ["df_standardized = dfs.groupby('code').apply(standardize)\n", "df_standardized.fillna(0.0, inplace=True)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["df_standardized"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["col_names = get_factor_cols(factor_type=\"sf\")\n", "col_names += ['code', 'date', 'change', 'label_long', 'label_short']\n", "sel_sf_df = sf_df[col_names]\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(35, 49) (35, 49)\n"]}], "source": ["# 将因子数据按CODE分组求均值和标准差\n", "sf_df_mean = sel_sf_df.groupby('code').mean()\n", "sf_df_std = sel_sf_df.groupby('code').std()\n", "print(sf_df_mean.shape, sf_df_std.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}