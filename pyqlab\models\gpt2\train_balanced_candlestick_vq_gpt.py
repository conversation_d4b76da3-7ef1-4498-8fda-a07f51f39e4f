"""
平衡训练的CandlestickVQGPT模型
专门解决token分布不均衡问题，特别是高频token（如795）过度集中的问题
"""

import os
import sys
import torch
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from collections import Counter
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, WeightedRandomSampler
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.candlestick_vq_dataset import CandlestickDataset
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer
from pyqlab.models.gpt.token_balance_utils import TokenBalancer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BalancedCandlestickTrainer:
    """平衡训练器，专门处理token分布不均衡问题"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.tokenizer = None
        self.model = None
        self.class_weights = None
        self.token_balancer = None
        
    def load_tokenizer(self):
        """加载tokenizer"""
        logger.info(f"加载tokenizer: {self.args.codebook_path}")
        
        self.tokenizer = CandlestickVQTokenizer(
            codebook_weights_path=self.args.codebook_path,
            num_embeddings=self.args.num_embeddings,
            embedding_dim=self.args.embedding_dim,
            atr_period=self.args.atr_period,
            ma_volume_period=self.args.ma_volume_period,
            vectorization_method=self.args.vectorization_method
        )
        
        logger.info(f"Tokenizer词汇表大小: {self.tokenizer.vocab_size}")
        return self.tokenizer
    
    def analyze_token_distribution(self, dataset):
        """分析训练数据的token分布"""
        logger.info("分析token分布...")
        
        # 收集所有token序列
        token_sequences = []
        for i in range(len(dataset)):
            sample = dataset[i]
            if 'targets' in sample:
                tokens = sample['targets'].tolist()
            else:
                tokens = sample['x'].tolist()
            
            # 过滤无效token
            valid_tokens = [t for t in tokens if t != -1]
            if valid_tokens:
                token_sequences.append(valid_tokens)
        
        # 创建token平衡器
        self.token_balancer = TokenBalancer(self.tokenizer.vocab_size)
        
        # 分析分布
        distribution_stats = self.token_balancer.analyze_token_distribution(
            token_sequences,
            save_plot=True,
            plot_path=os.path.join(self.args.save_dir, "token_distribution.png")
        )
        
        # 计算类别权重
        self.class_weights = self.token_balancer.compute_class_weights(
            method='sqrt_inverse_freq',
            smooth_factor=1.0
        )
        
        # 保存分析结果
        self._save_analysis_results(distribution_stats)
        
        return distribution_stats
    
    def _save_analysis_results(self, stats):
        """保存分析结果"""
        results_path = os.path.join(self.args.save_dir, "token_analysis.txt")
        
        with open(results_path, 'w', encoding='utf-8') as f:
            f.write("Token分布分析结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"总token数: {stats['total_tokens']:,}\n")
            f.write(f"唯一token数: {stats['unique_tokens']}\n")
            f.write(f"词汇表利用率: {stats['vocab_utilization']:.2%}\n")
            f.write(f"高频token数量: {len(stats['high_freq_tokens'])}\n")
            
            f.write("\n高频Token列表:\n")
            for token_id, count, percentage in stats['high_freq_tokens']:
                f.write(f"Token {token_id}: {count:,}次 ({percentage:.2%})\n")
        
        logger.info(f"分析结果已保存到: {results_path}")
    
    def create_balanced_dataloader(self, dataset, shuffle=True):
        """创建平衡的数据加载器"""
        if not self.args.use_balanced_sampling:
            return DataLoader(
                dataset,
                batch_size=self.args.batch_size,
                shuffle=shuffle,
                num_workers=4,
                pin_memory=True
            )
        
        logger.info("创建平衡数据加载器...")
        
        # 计算样本权重
        sample_weights = []
        for i in range(len(dataset)):
            sample = dataset[i]
            if 'targets' in sample:
                # 使用最后一个有效token作为目标
                targets = sample['targets']
                valid_targets = targets[targets != -1]
                if len(valid_targets) > 0:
                    target_token = valid_targets[-1].item()
                else:
                    target_token = 0
            else:
                target_token = sample['x'][-1].item()
            
            # 使用类别权重作为样本权重
            if self.class_weights is not None:
                weight = self.class_weights[target_token].item()
            else:
                weight = 1.0
            
            sample_weights.append(weight)
        
        # 创建加权采样器
        sampler = WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(dataset),
            replacement=True
        )
        
        return DataLoader(
            dataset,
            batch_size=self.args.batch_size,
            sampler=sampler,
            num_workers=4,
            pin_memory=True
        )
    
    def create_model(self):
        """创建模型"""
        logger.info("创建CandlestickVQGPT模型...")
        
        self.model = CandlestickVQGPT(
            vocab_size=self.tokenizer.vocab_size,
            code_size=self.args.code_size,
            seq_len=self.args.seq_len,
            n_layer=self.args.n_layer,
            n_head=self.args.n_head,
            d_model=self.args.d_model,
            dropout=self.args.dropout,
            bias=False,
            use_time_features=self.args.use_time_features,
            n_time_features=self.args.n_time_features,
            label_smoothing=self.args.label_smoothing,
            use_auxiliary_loss=True  # 启用辅助损失
        )
        
        self.model = self.model.to(self.device)
        logger.info(f"模型已创建，参数数量: {self.model.get_num_params():,}")
        
        return self.model
    
    def train_epoch(self, model, dataloader, optimizer, scheduler, scaler):
        """训练一个epoch"""
        model.train()
        total_loss = 0
        num_batches = 0
        
        # 用于监控预测多样性
        all_predictions = []
        
        for batch_idx, batch in enumerate(dataloader):
            # 解析batch数据
            if isinstance(batch, dict):
                input_tokens = batch['x'].to(self.device)
                code_ids = batch['code_ids'].to(self.device)
                targets = batch['targets'].to(self.device)
                time_features = batch.get('time_features', None)
                if time_features is not None:
                    time_features = time_features.to(self.device)
            else:
                input_tokens, code_ids, targets = [item.to(self.device) for item in batch[:3]]
                time_features = batch[3].to(self.device) if len(batch) > 3 else None
            
            optimizer.zero_grad()
            
            # 前向传播，传入类别权重
            with torch.cuda.amp.autocast():
                logits, loss = model(
                    input_tokens=input_tokens,
                    code_ids=code_ids,
                    time_features=time_features,
                    targets=targets,
                    class_weights=self.class_weights
                )
            
            # 反向传播
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            
            if scheduler is not None:
                scheduler.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 收集预测用于多样性监控
            if batch_idx % 100 == 0:
                with torch.no_grad():
                    preds = logits.argmax(dim=-1)
                    all_predictions.extend(preds[:, -1].cpu().tolist())
            
            # 定期打印进度
            if batch_idx % self.args.log_interval == 0:
                logger.info(f"Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / num_batches
        
        # 分析预测多样性
        if all_predictions:
            pred_counter = Counter(all_predictions)
            diversity = len(pred_counter) / len(set(all_predictions))
            logger.info(f"训练预测多样性: {diversity:.2%}")
            
            # 显示最常见的预测
            most_common = pred_counter.most_common(5)
            logger.info(f"最常见预测: {most_common}")
        
        return avg_loss


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='平衡训练CandlestickVQGPT模型')
    
    # 数据相关参数
    parser.add_argument('--data_path', type=str, required=True, help='训练数据路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本权重路径')
    parser.add_argument('--save_dir', type=str, default='./balanced_models', help='模型保存目录')
    
    # Tokenizer参数
    parser.add_argument('--num_embeddings', type=int, default=1024, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=5, help='嵌入维度')
    parser.add_argument('--atr_period', type=int, default=14, help='ATR周期')
    parser.add_argument('--ma_volume_period', type=int, default=20, help='成交量移动平均周期')
    parser.add_argument('--vectorization_method', type=str, default='atr_based', help='向量化方法')
    
    # 模型参数
    parser.add_argument('--code_size', type=int, default=100, help='证券代码数量')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--n_layer', type=int, default=4, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--dropout', type=float, default=0.15, help='Dropout比例')
    parser.add_argument('--use_time_features', action='store_true', help='使用时间特征')
    parser.add_argument('--n_time_features', type=int, default=8, help='时间特征数量')
    parser.add_argument('--label_smoothing', type=float, default=0.05, help='标签平滑')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=3e-5, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.1, help='权重衰减')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--log_interval', type=int, default=100, help='日志打印间隔')
    
    # 平衡训练参数
    parser.add_argument('--use_balanced_sampling', action='store_true', help='使用平衡采样')
    parser.add_argument('--analyze_distribution', action='store_true', help='分析token分布')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 创建训练器
    trainer = BalancedCandlestickTrainer(args)
    
    # 加载tokenizer
    trainer.load_tokenizer()
    
    # TODO: 加载数据集
    # 这里需要根据实际数据格式实现数据加载
    logger.info("请实现数据加载逻辑...")
    
    logger.info("平衡训练脚本已准备就绪！")


if __name__ == "__main__":
    main()
