{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sklearn.cross_validation import train_test_split"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def trim_all_columns(df):\n", "    \"\"\"\n", "    Trim whitespace from ends of each value across all series in dataframe\n", "    \"\"\"\n", "    trim_strings = lambda x: x.strip() if type(x) is str else x\n", "    return df.applymap(trim_strings)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               datetime          3     4       6       7        8        9  \\\n", "546 2019-03-13 09:20:00  RB1910.SC  long  0.0298  0.0418  79.3254  82.3177   \n", "632 2019-03-26 09:00:00  TA1909.Z<PERSON>  long  0.0093  0.0137  84.5061  35.6006   \n", "635 2019-03-26 14:05:00  ZN1907.SC  long  0.0070  0.0109  83.9520  79.1352   \n", "637 2019-03-22 13:30:00  OI1909.Z<PERSON>  long  0.0082  0.0120  91.8235  82.3308   \n", "639 2019-03-25 10:30:00   V1909.DC  long  0.0124  0.0194  97.3256  46.9358   \n", "\n", "         10      11      12 ...      38      39      40      41      42  \\\n", "546  3.9231  0.6246  7.3736 ...  0.5781  0.0047  0.0004  0.0134 -0.1662   \n", "632  4.1250  0.4336  5.4010 ... -0.0632  0.0129 -0.0313 -0.1262 -0.0290   \n", "635  4.6207  0.5473  9.1323 ... -4.1681  0.0076  0.0079 -0.3698 -0.2284   \n", "637  3.9444  0.5710  7.0212 ... -0.1012  0.0080 -0.0071  0.5635  0.3883   \n", "639  2.7097  0.4065  3.5717 ... -2.4595  0.0101 -0.0127  0.4364  0.3382   \n", "\n", "         43      44      45      46  47  \n", "546 -0.1438 -0.1931 -0.0313  0.4254   0  \n", "632  0.1462  2.5612  2.7098  2.3370   0  \n", "635 -0.0553  0.3653  1.0229  1.1331   1  \n", "637  0.4954  3.8747  3.1239  1.8296   1  \n", "639  0.2037  1.8996  1.1905  0.4281   1  \n", "\n", "[5 rows x 45 columns]\n", "     0       1       2        3        4       5       6       7       8  \\\n", "546  0  0.0298  0.0418  79.3254  82.3177  3.9231  0.6246  7.3736  0.5676   \n", "632  0  0.0093  0.0137  84.5061  35.6006  4.1250  0.4336  5.4010  0.8285   \n", "635  1  0.0070  0.0109  83.9520  79.1352  4.6207  0.5473  9.1323  0.6524   \n", "637  1  0.0082  0.0120  91.8235  82.3308  3.9444  0.5710  7.0212  1.4448   \n", "639  1  0.0124  0.0194  97.3256  46.9358  2.7097  0.4065  3.5717  1.3486   \n", "\n", "          9   ...         32      33      34      35      36      37      38  \\\n", "546  1.2119   ...   -10.1925  0.5781  0.0047  0.0004  0.0134 -0.1662 -0.1438   \n", "632  2.4970   ...     2.7084 -0.0632  0.0129 -0.0313 -0.1262 -0.0290  0.1462   \n", "635  1.5252   ...     1.4344 -4.1681  0.0076  0.0079 -0.3698 -0.2284 -0.0553   \n", "637  3.0836   ...     5.5318 -0.1012  0.0080 -0.0071  0.5635  0.3883  0.4954   \n", "639  2.2700   ...     8.0725 -2.4595  0.0101 -0.0127  0.4364  0.3382  0.2037   \n", "\n", "         39      40      41  \n", "546 -0.1931 -0.0313  0.4254  \n", "632  2.5612  2.7098  2.3370  \n", "635  0.3653  1.0229  1.1331  \n", "637  3.8747  3.1239  1.8296  \n", "639  1.8996  1.1905  0.4281  \n", "\n", "[5 rows x 42 columns]\n", "     0       1       2        3        4       5       6       7       8  \\\n", "866  0  0.0253  0.0305  21.4358  74.4098  3.5238  0.4905  3.3722  0.5530   \n", "864  0  0.0253  0.0305   7.4552  72.7451  3.5238  0.4905  3.3126  0.7180   \n", "118  0  0.0133  0.0166  17.6058  -0.0703  3.0837  0.6028  5.9131  0.6499   \n", "885  1  0.0205  0.0234  18.4571  76.9589  3.5333  0.4631  3.4696  0.7789   \n", "799  0  0.0180  0.0255 -13.2212  45.8456  3.2571  0.4197  2.6319  1.0339   \n", "\n", "          9   ...         32      33      34      35      36      37      38  \\\n", "866  1.4518   ...     0.8729 -3.4248 -0.0116  0.0328 -0.1584 -0.1722  0.0040   \n", "864  1.9715   ...     6.6063 -3.5416 -0.0121  0.0329  0.2306  0.1671  0.2125   \n", "118  2.2059   ...    -0.3340  3.2663 -0.0064 -0.0014  0.0126 -0.0066  0.1642   \n", "885  1.2434   ...     3.4214 -4.0990 -0.0029  0.0399  0.1072  0.0175  0.0883   \n", "799  0.9676   ...    16.7570 -1.0688 -0.0023  0.0077  0.3311  0.0984  0.0492   \n", "\n", "         39      40      41  \n", "866  0.2082  0.6389  0.6881  \n", "864  1.3022  0.9069  0.4205  \n", "118  1.8269  1.8588  1.5671  \n", "885 -0.2501 -0.3453 -0.4337  \n", "799 -0.6741 -1.0355 -1.1008  \n", "\n", "[5 rows x 42 columns]\n"]}], "source": ["FD_PATH = \"d:/QuantLab/log/\"\n", "\n", "#用pandas将时间转为标准格式\n", "dateparse = lambda dates: pd.datetime.strptime(dates,' %Y-%m-%d %H:%M:%S')\n", "df_train = pd.read_csv(FD_PATH + 'featuresdata.log', header=None, sep=']|,', skipinitialspace=True,\n", "                       parse_dates={'datetime': [5]},date_parser=dateparse)\n", "# df_train = pd.read_csv(FD_PATH + 'featuresdata.201701_201905.log', header=None, sep=']|,', skipinitialspace=True)\n", "\n", "temp=pd.DatetimeIndex(df_train['datetime'])\n", "# df_train['date'] = temp.date\n", "# df_train['time'] = temp.time\n", "#由于时间的部分最小粒度为小时，所以把time变为hour更加简洁\n", "# df_train['hour']=pd.to_datetime(temp.time,format=\"%H:%M:%S\")#变换格式\n", "# df_train['hour']=pd.Index(df_train[\"hour\"]).hour\n", "# df_train['dayofweek']=pd.DatetimeIndex(temp.date).dayofweek #提取出星期几这个特征\n", "\n", "# df_train[3].apply(lambda x: x.str.strip())\n", "# df_obj = df_train.select_dtypes([3])\n", "# df_train[3] = df_train[3].str.strip()\n", "df_train = trim_all_columns(df_train)\n", "df_train = df_train[~df_train.isin([np.nan, np.inf, -np.inf]).any(1)]\n", "df_train = df_train[df_train[3] != \"IF1905.SF\"]\n", "df_train = df_train[df_train[3] != \"IH1905.SF\"]\n", "df_train = df_train[df_train[3] != \"IC1905.SF\"]\n", "# df_train = df_train[df_train[3].str != \"IH1905.SF\"]\n", "# df_train = df_train[df_train[3].str != \"IC1905.SF\"]\n", "\n", "df_train = df_train.drop([0,1,2], axis=1)\n", "df_train = df_train.sort_values(4,ascending=1)\n", "print(df_train.head())\n", "\n", "df_train_long, df_train_short = [x for _, x in df_train.groupby(df_train[4])]\n", "\n", "train_long = df_train_long.drop([3,4,'datetime'], axis=1)\n", "train_long = train_long[[47] + [c for c in train_long if c not in [47]]]\n", "train_long.columns = [str(n) for n in range(0, (train_long.shape[1]))]\n", "# train_long = train_long.drop([1,2,3,6,8,10,12,14,24,26,30,32,35,36,38,39,42,44,50], axis=1)\n", "print(train_long.head())\n", "\n", "train_long, test_long = train_test_split(train_long, random_state=42)\n", "train_long.to_csv(FD_PATH + 'long.train', header=False, index=False)\n", "test_long.to_csv(FD_PATH + 'long.test', header=False, index=False)\n", "# print(train_long)\n", "# print(test_long)\n", "\n", "train_short = df_train_short.drop([3,4,'datetime'], axis=1)\n", "train_short = train_short[[47] + [c for c in train_short if c not in [47]]]\n", "train_short.columns = [str(n) for n in range(0, (train_short.shape[1]))]\n", "# train_short = train_short.drop([1,2,3,6,8,10,12,14,24,26,30,32,35,36,38,39,42,44,50], axis=1)\n", "print(train_short.head())\n", "\n", "train_short, test_short = train_test_split(train_short, random_state=42)\n", "train_short.to_csv(FD_PATH + 'short.train', header=False, index=False)\n", "test_short.to_csv(FD_PATH + 'short.test', header=False, index=False)\n", "# print(train_short)\n", "# print(test_short)\n", "\n", "# y_train = df_train[0]\n", "# y_test = df_test[0]\n", "# X_train = df_train.drop(0, axis=1)\n", "# X_test = df_test.drop(0, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}