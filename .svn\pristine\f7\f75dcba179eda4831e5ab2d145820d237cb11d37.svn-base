{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["import os\n", "import sys\n", "\n", "import torch\n", "from torch.utils.data import Dataset\n", "from torch.utils.data.dataloader import DataLoader\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from pyqlab.models.mingpt.model import GPT\n", "from pyqlab.models.mingpt.trainer import Trainer\n", "from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN\n", "\n", "from pyqlab.data.pipeline import Pipeline\n", "# from pyqlab.data.bardataset import BarDataset\n", "from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.5, -0.4152542372881356, -0.3305084745762712, -0.2457627118644068, -0.1610169491525424, -0.07627118644067798, 0.008474576271186418, 0.09322033898305082, 0.17796610169491522, 0.2627118644067796, 0.34745762711864403, 0.43220338983050843]\n"]}], "source": ["min = []\n", "for i in range(0, 60, 5):\n", "    min.append(i/59.0 - 0.5)\n", "print(min)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n", "0.0847457627118644\n"]}], "source": ["for i in range(len(min)-1):\n", "    print(min[i+1] - min[i])"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.5, -0.4565217391304348, -0.41304347826086957, -0.3695652173913043, -0.32608695652173914, -0.28260869565217395, -0.2391304347826087, -0.19565217391304346, -0.15217391304347827, -0.10869565217391303, -0.06521739130434784, -0.021739130434782594, 0.021739130434782594, 0.06521739130434778, 0.10869565217391308, 0.15217391304347827, 0.19565217391304346, 0.23913043478260865, 0.28260869565217395, 0.32608695652173914, 0.3695652173913043, 0.4130434782608695, 0.4565217391304348, 0.5]\n"]}], "source": ["hour = []\n", "for i in range(0, 24):\n", "    hour.append(i/23.0 - 0.5)\n", "print(hour)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.04347826086956519\n", "0.043478260869565244\n", "0.043478260869565244\n", "0.04347826086956519\n", "0.04347826086956519\n", "0.043478260869565244\n", "0.043478260869565244\n", "0.04347826086956519\n", "0.043478260869565244\n", "0.04347826086956519\n", "0.043478260869565244\n", "0.04347826086956519\n", "0.04347826086956519\n", "0.0434782608695653\n", "0.04347826086956519\n", "0.04347826086956519\n", "0.04347826086956519\n", "0.0434782608695653\n", "0.04347826086956519\n", "0.04347826086956519\n", "0.04347826086956519\n", "0.0434782608695653\n", "0.04347826086956519\n"]}], "source": ["for i in range(len(hour)-1):\n", "    print(hour[i+1] - hour[i])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class BarDataset(Dataset):\n", "    \"\"\"\n", "    Emits batches of bars\n", "    \"\"\"\n", "\n", "    @staticmethod\n", "    def get_default_config():\n", "        C = CN()\n", "        C.block_size = 15\n", "        C.is_sf = False\n", "        C.data_path = 'd:/RoboQuant2/store/barenc/sf'\n", "        C.start_year = None\n", "        C.end_year = None\n", "        C.step_size = 1\n", "        return C\n", "    \n", "    def __init__(self, config, data):\n", "        self.config = config\n", "\n", "        bars = Pipeline.get_vocab()\n", "\n", "        self.itobar = { i:ch for i,ch in enumerate(bars) }\n", "\n", "        self.vocab_size = len(bars)\n", "        self.data = data\n", "        self.block_size = self.config.block_size\n", "        # 统计每个合约的数据量\n", "        codecount=self.data['code_encoded'].value_counts().to_list()\n", "        codecount = [i - self.block_size -1 for i in codecount]\n", "        # 每个值都必须大于等于0\n", "        assert all([i >= 0 for i in codecount])\n", "        self.data_len = sum(codecount)\n", "        # codes_len2累加\n", "        self.codecount = np.cumsum(codecount)\n", "        print(self.codecount)\n", "\n", "    def get_vocab_size(self):\n", "        return self.vocab_size\n", "\n", "    def get_block_size(self):\n", "        return self.block_size\n", "    \n", "    def i_to_idx(self, i):\n", "        n = np.searchsorted(self.codecount, i, side='right')\n", "        return i + n * (self.block_size + 1)\n", "\n", "    def __len__(self):\n", "        return self.data.shape[0] - self.block_size -1\n", "    \n", "    def __getitem__(self, idx):\n", "        idx = self.i_to_idx(idx)\n", "        code = self.data.iloc[idx:idx+self.block_size+1, 0].astype(int).values\n", "        bar = self.data.iloc[idx:idx+self.block_size+1, 1].astype(int).values\n", "        tf = self.data.iloc[idx:idx+self.block_size+1, -5:].astype(float).values\n", "        code = torch.tensor(code[:-1], dtype=torch.long)\n", "        x = torch.tensor(bar[:-1], dtype=torch.long)\n", "        x_mark = torch.tensor(tf[:-1], dtype=torch.float)\n", "        y = torch.tensor(bar[1:], dtype=torch.long)\n", "        y_mark = torch.tensor(tf[1:], dtype=torch.float)\n", "        return code, x, x_mark, y, y_mark"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def get_config():\n", "\n", "    C = CN()\n", "\n", "    # system\n", "    C.system = CN()\n", "    C.system.seed = 3407\n", "    C.system.work_dir = './out/chargpt'\n", "\n", "    # data\n", "    C.data = BarDataset.get_default_config()\n", "\n", "    # model\n", "    C.model = GPT.get_default_config()\n", "    C.model.model_type = 'gpt-mini'\n", "\n", "    # trainer\n", "    C.trainer = Trainer.get_default_config()\n", "    C.trainer.learning_rate = 5e-4 # the model we're using is so small that we can go a bit faster\n", "\n", "    return C"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================\n", "code_size: 69, bar_size: 40000\n", "df shape: (17856, 7)\n", "df head:    code_encoded    bar  MinuteOfHour  HourOfDay  DayOfWeek  DayOfMonth  \\\n", "0            23  37568     -0.076271  -0.108696  -0.333333   -0.466667   \n", "1            23  13478      0.093220  -0.108696  -0.333333   -0.466667   \n", "2            23  28320      0.177966  -0.108696  -0.333333   -0.466667   \n", "\n", "   DayOfYear  \n", "0   -0.49726  \n", "1   -0.49726  \n", "2   -0.49726  \n", "============================\n"]}], "source": ["\n", "# get default config and overrides from the command line, if any\n", "config = get_config()\n", "# config.merge_from_args(sys.argv[1:])\n", "setup_logging(config)\n", "set_seed(config.system.seed)\n", "\n", "# construct the training dataset\n", "# text = open(R'E:\\github\\minGPT\\projects\\chargpt\\input.tt', 'r').read() # don't worry we won't run out of file handles\n", "# data = pd.read_csv('d:/RoboQuant2/store/barenc/sf_min5_2023_2024.npy')\n", "pipe = Pipeline('d:/RoboQuant2/store/barenc/sf', 2024, 2024)\n", "data = pipe.get_data()\n", "# assert len(data) > 0 and len(data.shape) == 3, 'No data or wrong shape'\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 4448  8896 13344 17792]\n"]}], "source": ["config.data.is_sf = pipe.is_sf\n", "train_dataset = BarDataset(config.data, data)\n", "# test_dataset = BarDataset(config.data, data)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26, 26]),\n", " tensor([13313, 11651, 28306, 13248, 13376, 16665,  9945,  6659, 28330, 21648,\n", "         23235, 21640, 13400, 18304, 38282]),\n", " tensor([[ 0.0932,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.1780,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.3475,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.4322,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [-0.5000,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.4153,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.3305,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.2458,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.1610,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.0763,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0085,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0932,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.1780,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.1087, -0.1667,  0.4333, -0.0918]]),\n", " tensor([11651, 28306, 13248, 13376, 16665,  9945,  6659, 28330, 21648, 23235,\n", "         21640, 13400, 18304, 38282,  6659]),\n", " tensor([[ 0.1780,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.3475,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [ 0.4322,  0.0652, -0.1667,  0.4333, -0.0918],\n", "         [-0.5000,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.4153,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.3305,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.2458,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.1610,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [-0.0763,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0085,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.0932,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.1780,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.2627,  0.1087, -0.1667,  0.4333, -0.0918],\n", "         [ 0.3475,  0.1087, -0.1667,  0.4333, -0.0918]]))"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["train_dataset[17791]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["test_loader = DataLoader(\n", "    train_dataset,\n", "    shuffle=False,\n", "    batch_size=1,\n", "    num_workers=0,\n", "    pin_memory=True,\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[tensor([[23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23]]),\n", " tensor([[37568, 13478, 28320,  5074,  9993, 26636, 21633, 19993, 19972, 20232,\n", "           8323, 26643, 19972,  5000, 29952]]),\n", " tensor([[[-0.0763, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0932, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.2627, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.3475, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.4322, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [-0.5000, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.4153, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.3305, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.2458, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.1610, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.0763, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0085, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0932, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.0652, -0.3333, -0.4667, -0.4973]]]),\n", " tensor([[13478, 28320,  5074,  9993, 26636, 21633, 19993, 19972, 20232,  8323,\n", "          26643, 19972,  5000, 29952,  8321]]),\n", " tensor([[[ 0.0932, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.2627, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.3475, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [ 0.4322, -0.1087, -0.3333, -0.4667, -0.4973],\n", "          [-0.5000, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.4153, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.3305, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.2458, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.1610, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [-0.0763, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0085, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.0932, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.1780, -0.0652, -0.3333, -0.4667, -0.4973],\n", "          [ 0.2627, -0.0652, -0.3333, -0.4667, -0.4973]]])]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["next(iter(test_loader))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# construct the model\n", "config.model.vocab_size = train_dataset.get_vocab_size()\n", "config.model.block_size = train_dataset.get_block_size()\n", "config.model.code_size = train_dataset.get_code_size()\n", "config.model.pos_size = train_dataset.get_pos_size()\n", "config.model.n_layer=6\n", "config.model.n_head=6\n", "config.model.n_embd=128\n", "print(config)\n", "\n", "model = GPT(config.model)\n", "\n", "# construct the trainer object\n", "trainer = Trainer(config.trainer, model, train_dataset)\n", "\n", "# iteration callback\n", "def batch_end_callback(trainer):\n", "\n", "    if trainer.iter_num % 50 == 0:\n", "        print(f\"iter_dt {trainer.iter_dt * 1000:.2f}ms; iter {trainer.iter_num}: train loss {trainer.loss.item():.5f}\")\n", "\n", "    if trainer.iter_num % 500 == 0:\n", "        # evaluate both the train and test score\n", "        model.eval()\n", "        with torch.no_grad():\n", "            # sample from the model...\n", "            # context = \"O <PERSON>, O God!\"\n", "            # x = torch.tensor([train_dataset.stoi[s] for s in context], dtype=torch.long)[None,...].to(trainer.device)\n", "            code, pos, x, y = next(iter(test_loader))\n", "\n", "            y = model.generate(code, pos, x, 5, temperature=1.0, do_sample=True, top_k=10)[0]\n", "            completion = [train_dataset.itobar[int(i)] for i in y]\n", "            print(completion)\n", "        # save the latest model\n", "        print(\"saving model\")\n", "        ckpt_path = os.path.join(config.system.work_dir, \"model.pt\")\n", "        torch.save(model.state_dict(), ckpt_path)\n", "        # revert model to training mode\n", "        model.train()\n", "    \n", "trainer.set_callback('on_batch_end', batch_end_callback)\n", "\n", "# run the optimization\n", "trainer.run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}