@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt

python train_balanced_bar_gpt4.py ^
    --data_path "f:/featdata/barenc/db2" ^
    --market "fut" ^
    --block_name "top" ^
    --period "min1" ^
    --start_year 2025 ^
    --end_year 2025 ^
    --block_size 20 ^
    --batch_size 32 ^
    --num_workers 0 ^
    --n_head 16 ^
    --n_layer 4 ^
    --d_model 96 ^
    --vocab_size 1602 ^
    --code_size 96 ^
    --lr 1e-4 ^
    --max_epochs 3 ^
    --k_folds 3 ^
    --time_encoding "timeF" ^
    --pos_embed_type "rope" ^
    --time_embed_type "time_feature" ^
    --use_class_weights ^
    --balance_method "none" ^
    --data_balance_method "none" ^
    --log_dir "lightning_logs" ^
    --model_dir "model" ^
    --early_stop 3 ^
    --min_delta 1e-2
