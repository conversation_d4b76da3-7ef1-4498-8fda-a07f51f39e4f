{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from copy import deepcopy\n", "from pathlib import Path\n", "import pickle\n", "from pprint import pprint\n", "import subprocess\n", "import yaml\n", "from qlib.log import TimeInspector\n", "\n", "from qlib import init\n", "from qlib.data.dataset.handler import DataHandlerLP\n", "from qlib.utils import init_instance_by_config\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[23612:MainThread](2021-12-06 23:29:45,180) INFO - qlib.Initialization - [config.py:386] - default_conf: client.\n", "[23612:MainThread](2021-12-06 23:29:47,231) WARNING - qlib.Initialization - [config.py:411] - redis connection failed(host=127.0.0.1 port=6379), DiskExpressionCache and DiskDatasetCache will not be used!\n", "[23612:MainThread](2021-12-06 23:29:47,250) INFO - qlib.Initialization - [__init__.py:56] - qlib successfully initialized based on client settings.\n", "[23612:MainThread](2021-12-06 23:29:47,255) INFO - qlib.Initialization - [__init__.py:58] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["# For general purpose, we use relative path\n", "DIRNAME = Path(\"e:/github/qlib/examples/data_demo/data_cache_demo.py\").absolute().resolve().parent\n", "\n", "init()\n", "\n", "config_path = DIRNAME.parent / \"benchmarks/LightGBM/workflow_config_lightgbm_Alpha158_test.yaml\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[23612:MainThread](2021-12-06 23:04:39,293) INFO - qlib.timer - [log.py:113] - Time cost: 249.186s | The original time without handler cache: Done\n"]}], "source": ["# 1) show original time\n", "with TimeInspector.logt(\"The original time without handler cache:\"):\n", "    subprocess.run(f\"qrun {config_path}\", shell=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'class': 'Alpha158',\n", " 'kwargs': {'end_time': datetime.date(2020, 8, 1),\n", "            'fit_end_time': datetime.date(2019, 12, 31),\n", "            'fit_start_time': datetime.date(2018, 1, 1),\n", "            'instruments': 'csi300',\n", "            'start_time': datetime.date(2018, 1, 1)},\n", " 'module_path': 'qlib.contrib.data.handler'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[23612:MainThread](2021-12-06 23:34:22,786) INFO - qlib.timer - [log.py:113] - Time cost: 70.737s | Loading data Done\n", "[23612:MainThread](2021-12-06 23:34:23,023) INFO - qlib.timer - [log.py:113] - Time cost: 0.201s | DropnaLabel Done\n", "d:\\Anaconda3\\lib\\site-packages\\pandas\\core\\frame.py:3191: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self[k1] = value[k2]\n", "[23612:MainThread](2021-12-06 23:34:24,580) INFO - qlib.timer - [log.py:113] - Time cost: 1.555s | CSZScoreNorm Done\n", "[23612:MainThread](2021-12-06 23:34:24,589) INFO - qlib.timer - [log.py:113] - Time cost: 1.801s | fit & process data Done\n", "[23612:MainThread](2021-12-06 23:34:24,591) INFO - qlib.timer - [log.py:113] - Time cost: 72.542s | Init data Done\n"]}], "source": ["# 2) dump handler\n", "task_config = yaml.safe_load(config_path.open())\n", "hd_conf = task_config[\"task\"][\"dataset\"][\"kwargs\"][\"handler\"]\n", "pprint(hd_conf)\n", "hd: DataHandlerLP = init_instance_by_config(hd_conf)\n", "hd_path = DIRNAME / \"handler.pkl\"\n", "hd.to_pickle(hd_path, dump_all=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3) create new task with handler cache\n", "new_task_config = deepcopy(task_config)\n", "new_task_config[\"task\"][\"dataset\"][\"kwargs\"][\"handler\"] = f\"file://{hd_path}\"\n", "new_task_config[\"sys\"] = {\"path\": [str(config_path.parent.resolve())]}\n", "new_task_path = DIRNAME / \"new_task.yaml\"\n", "print(\"The location of the new task\", new_task_path)\n", "\n", "# save new task\n", "with new_task_path.open(\"w\") as f:\n", "    yaml.safe_dump(new_task_config, f, indent=4, sort_keys=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4) train model with new task\n", "with TimeInspector.logt(\"The time for task with handler cache:\"):\n", "    subprocess.run(f\"qrun {new_task_path}\", shell=True)"]}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}