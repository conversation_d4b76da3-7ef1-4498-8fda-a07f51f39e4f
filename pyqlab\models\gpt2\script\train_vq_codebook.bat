@echo off
REM Batch script for training CandlestickVQTokenizer codebook weights

REM Set default parameters
set DATA_DIR=f:\hqdata\tsdb
set MARKET=fut
set BLOCK_NAME=top
set PERIOD=min1
set SAVE_DIR=e:\lab\RoboQuant\pylab\models\vqvae
set NUM_EPOCHS=10
set BATCH_SIZE=64
set LEARNING_RATE=0.001
set NUM_EMBEDDINGS=5120
set EMBEDDING_DIM=4
set HIDDEN_DIM=64
set ATR_WINDOW=14
set MA_VOLUME_PERIOD=14
@REM set INCLUDE_VOLUME=--include_volume
set MAX_SAMPLES_PER_CODE=30000
set MIN_SAMPLES=10000
set MAX_SAMPLES=500000
set SAVE_INTERVAL=100
set DEVICE=cpu
set OUTPUT_NAME=
set VECTORIZATION_METHOD=atr_based
set USE_CODE_DIM=--use_code_dim
set CODE_DIM=5
set CODE_DROPOUT=0.1

REM Parse command line arguments
:parse
if "%~1"=="" goto :execute
if /i "%~1"=="--data_dir" set DATA_DIR=%~2& shift & shift & goto :parse
if /i "%~1"=="--market" set MARKET=%~2& shift & shift & goto :parse
if /i "%~1"=="--block_name" set BLOCK_NAME=%~2& shift & shift & goto :parse
if /i "%~1"=="--period" set PERIOD=%~2& shift & shift & goto :parse
if /i "%~1"=="--save_dir" set SAVE_DIR=%~2& shift & shift & goto :parse
if /i "%~1"=="--num_epochs" set NUM_EPOCHS=%~2& shift & shift & goto :parse
if /i "%~1"=="--batch_size" set BATCH_SIZE=%~2& shift & shift & goto :parse
if /i "%~1"=="--learning_rate" set LEARNING_RATE=%~2& shift & shift & goto :parse
if /i "%~1"=="--num_embeddings" set NUM_EMBEDDINGS=%~2& shift & shift & goto :parse
if /i "%~1"=="--embedding_dim" set EMBEDDING_DIM=%~2& shift & shift & goto :parse
if /i "%~1"=="--hidden_dim" set HIDDEN_DIM=%~2& shift & shift & goto :parse
if /i "%~1"=="--atr_window" set ATR_WINDOW=%~2& shift & shift & goto :parse
if /i "%~1"=="--ma_volume_period" set MA_VOLUME_PERIOD=%~2& shift & shift & goto :parse
if /i "%~1"=="--include_volume" set INCLUDE_VOLUME=--include_volume& shift & goto :parse
if /i "%~1"=="--max_samples_per_code" set MAX_SAMPLES_PER_CODE=%~2& shift & shift & goto :parse
if /i "%~1"=="--min_samples" set MIN_SAMPLES=%~2& shift & shift & goto :parse
if /i "%~1"=="--max_samples" set MAX_SAMPLES=%~2& shift & shift & goto :parse
if /i "%~1"=="--save_interval" set SAVE_INTERVAL=%~2& shift & shift & goto :parse
if /i "%~1"=="--device" set DEVICE=%~2& shift & shift & goto :parse
if /i "%~1"=="--output_name" set OUTPUT_NAME=--output_name %~2& shift & shift &
if /i "%~1"=="--vectorization_method" set VECTORIZATION_METHOD=%~2& shift & shift & goto :parse
if /i "%~1"=="--no_code_dim" set USE_CODE_DIM=& shift & goto :parse
if /i "%~1"=="--code_dim" set CODE_DIM=%~2& shift & shift & goto :parse
if /i "%~1"=="--code_dropout" set CODE_DROPOUT=%~2& shift & shift & goto :parse
goto :parse
shift & goto :parse

:execute
REM Display parameters
echo Training CandlestickVQTokenizer Codebook
echo ===================================
echo Data directory: %DATA_DIR%
echo Market: %MARKET%
echo Block: %BLOCK_NAME%
echo Period: %PERIOD%
echo Save directory: %SAVE_DIR%
echo Number of epochs: %NUM_EPOCHS%
echo Batch size: %BATCH_SIZE%
echo Learning rate: %LEARNING_RATE%
echo Codebook size: %NUM_EMBEDDINGS%
echo Embedding dimension: %EMBEDDING_DIM%
echo Hidden dimension: %HIDDEN_DIM%
echo ATR window: %ATR_WINDOW%
echo MA volume period: %MA_VOLUME_PERIOD%
if defined INCLUDE_VOLUME (
    echo Include volume: Yes
) else (
    echo Include volume: No
)
echo Maximum samples per code: %MAX_SAMPLES_PER_CODE%
echo Minimum samples: %MIN_SAMPLES%
echo Maximum samples: %MAX_SAMPLES%
echo Save interval: %SAVE_INTERVAL%
echo Training device: %DEVICE%
echo Vectorization method: %VECTORIZATION_METHOD%
if "%USE_CODE_DIM%"=="--use_code_dim" (
    echo Use code dimension: Yes
    echo Code dimension: %CODE_DIM%
    echo Code dropout: %CODE_DROPOUT%
) else (
    echo Use code dimension: No
)
echo.

REM Run training script
python -m pyqlab.models.gpt2.train_vq_codebook ^
    --data_dir %DATA_DIR% ^
    --market %MARKET% ^
    --block_name %BLOCK_NAME% ^
    --period %PERIOD% ^
    --save_dir %SAVE_DIR% ^
    --num_epochs %NUM_EPOCHS% ^
    --batch_size %BATCH_SIZE% ^
    --learning_rate %LEARNING_RATE% ^
    --num_embeddings %NUM_EMBEDDINGS% ^
    --embedding_dim %EMBEDDING_DIM% ^
    --hidden_dim %HIDDEN_DIM% ^
    --atr_window %ATR_WINDOW% ^
    --ma_volume_period %MA_VOLUME_PERIOD% ^
    %INCLUDE_VOLUME% ^
    --max_samples_per_code %MAX_SAMPLES_PER_CODE% ^
    --min_samples %MIN_SAMPLES% ^
    --max_samples %MAX_SAMPLES% ^
    --save_interval %SAVE_INTERVAL% ^
    --vectorization_method %VECTORIZATION_METHOD% ^
    --device %DEVICE% ^
    %USE_CODE_DIM% ^
    --code_dim %CODE_DIM% ^
    --code_dropout %CODE_DROPOUT% ^
    %OUTPUT_NAME%

echo.
if %ERRORLEVEL% EQU 0 (
    echo Training completed successfully!
) else (
    echo Training failed with error code: %ERRORLEVEL%
)
