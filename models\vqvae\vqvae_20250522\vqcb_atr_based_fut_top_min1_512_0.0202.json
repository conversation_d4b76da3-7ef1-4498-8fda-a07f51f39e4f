{"data_dir": "f:\\hqdata\\tsdb", "market": "fut", "block_name": "top", "period": "min1", "output_name": null, "save_dir": "e:\\lab\\RoboQuant\\pylab\\models\\vqvae", "num_epochs": 10, "batch_size": 64, "learning_rate": 0.001, "num_embeddings": 512, "embedding_dim": 4, "hidden_dim": 64, "use_code_dim": true, "code_dim": 5, "code_dropout": 0.1, "vectorization_method": "atr_based", "atr_window": 14, "ma_volume_period": 14, "include_volume": false, "min_samples": 10000, "max_samples": 500000, "max_samples_per_code": 50000, "save_interval": 100, "device": "cpu"}