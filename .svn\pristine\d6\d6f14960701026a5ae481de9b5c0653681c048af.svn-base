{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 数据预处理\n", "特征向量的构建是机器学习的关键步骤"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### AICM系统中的因子数据作为机器学习的主要特征向量数据\n", "- 市场数据\n", "- 技术指标数据\n", "- 财务数据\n", "- 订单上下文数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import time\n", "import datetime\n", "import json\n", "import sqlite3\n", "import pandas as pd\n", "import numpy as np\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyqlab.data.dataset.factors import AicmFactorsData"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 配置\n", "config_server = {\n", "    \"db_path\": \"c:/TRobot\",\n", "    \"data_path\": 'e:/lab/RoboQuant/pylab/data',\n", "    \"host\": \"**********\",\n", "    \"port\": 51301,\n", "    \"password\": \"wdljshbsjzsszsbbzyjcsz~1974\"\n", "}\n", "\n", "config_local = {\n", "    \"db_path\": \"d:/RoboQuant\",\n", "    \"data_path\": 'e:/lab/RoboQuant/pylab/data',\n", "    \"host\": \"**********\",\n", "    \"port\": 51301,\n", "    \"password\": \"wdljshbsjzsszsbbzyjcsz~1974\"\n", "}\n", "\n", "pfs_name_ids = {\n", "    \"zxjt_pjj\": {\n", "        \"FUT-ZXJT-P21\": \"00210102215917000\",\n", "        \"FUT-ZXJT-JMO\": \"00171009141918000\",\n", "        \"FUT-ZXJT-JHL\": \"00210303161416000\",\n", "    },\n", "    \"zxjt_xzy\": {\n", "        \"FUT-ZXJT-X21\": \"00210102224248000\",\n", "        \"FUT-ZXJT-XMO\": \"00171009141918000\",\n", "        \"FUT-ZXJT-XHL\": \"00210419180454000\",\n", "    },\n", "    \"nhqh_xzy\": {\n", "        \"FUT-NHQH-X21\": \"00210102223323000\",\n", "        \"FUT-NHQH-W1B\": \"00170908115033000\",\n", "        \"FUT-NHQH-NN1\": \"00220123224825000\",\n", "    },\n", "    \"gtja_xzy\": {\n", "        \"FUT-GTJA-X21\": \"00210102225821000\",\n", "        \"FUT-GTJA-W1B\": \"00171009141918000\",\n", "    }\n", "}\n", "\n", "pfs_main = ['00211229152555000', '00170623114649000']\n", "pfs_zxjt_pjj = ['00210102215917000', '00171009141918000', '00210303161416000']\n", "pfs_zxjt_xzy = ['00210102224248000', '00171009141918000', '00210419180454000']\n", "pfs_zxjt_nhqh = ['00210102223323000', '00170908115033000', '00220123224825000']\n", "pfs_zxjt_gtja = ['00210102225821000', '00171009141918000']\n", "pfs_local = ['00200910081133001', '00171106132928000']\n", "# pfs_local = ['00200910081133001', '00171106132928000', '00170607084458001', '00171122123535000']\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["pfs = {\n", "    'ALL': ['00211229152555000', '00171106132928000', '00200910081133001', '00170623114649000'],\n", "    # 'MIX': ['00200910081133001', '00171106132928000'],\n", "    # '5R': ['00211229152555000', '00171106132928000'],\n", "    '7R': ['00200910081133001', '00170623114649000'],\n", "}\n", "\n", "data_path = \"e:/lab/RoboQuant/pylab/data\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["afd = AicmFactorsData(direct='long', data_path=data_path)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["lb_df, lf_df, sf_df, ct_df = afd.get_pf_data(pfs['7R'])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5046, 12)\n"]}], "source": ["print(ct_df.shape)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "      <th>CODE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2916</th>\n", "      <td>220421090426033</td>\n", "      <td>JD2209.DC</td>\n", "      <td>20220422 09:15:00</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>JD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2918</th>\n", "      <td>220420210102023</td>\n", "      <td>P2209.DC</td>\n", "      <td>20220422 10:45:57</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>P</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2919</th>\n", "      <td>220419222927147</td>\n", "      <td>RM2209.ZC</td>\n", "      <td>20220422 11:10:44</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "      <td>RM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2920</th>\n", "      <td>220419091237025</td>\n", "      <td>SP2209.SC</td>\n", "      <td>20220421 21:00:16</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>SP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2921</th>\n", "      <td>220419210323037</td>\n", "      <td>Y2209.DC</td>\n", "      <td>20220421 22:37:44</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               ord_id instrument           datetime direct  label CODE\n", "2916  220421090426033  JD2209.DC  20220422 09:15:00      L      0   JD\n", "2918  220420210102023   P2209.DC  20220422 10:45:57      L      1    P\n", "2919  220419222927147  RM2209.ZC  20220422 11:10:44      L      0   RM\n", "2920  220419091237025  SP2209.SC  20220421 21:00:16      L      1   SP\n", "2921  220419210323037   Y2209.DC  20220421 22:37:44      L      1    Y"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["lb_df.tail()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['A', 'AG', 'AP', 'B', 'BU', 'C', 'CF', 'CJ', 'CS', 'CY', 'EB',\n", "       'EG', 'FG', '<PERSON>', 'I', 'J<PERSON>', 'J<PERSON>', 'L', 'M', 'MA', 'OI', 'P', 'PF',\n", "       'PG', 'PK', 'PP', 'RB', 'RM', 'RU', 'SA', 'SF', 'SM', 'SP', 'SR',\n", "       'SS', 'TA', 'UR', 'V', 'Y', 'AL', 'NI', 'PB', 'ZN'], dtype=object)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["lb_df.CODE.unique()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "    </tr>\n", "    <tr>\n", "      <th>CODE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>A</th>\n", "      <td>126</td>\n", "      <td>126</td>\n", "      <td>126</td>\n", "      <td>126</td>\n", "      <td>126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AG</th>\n", "      <td>211</td>\n", "      <td>211</td>\n", "      <td>211</td>\n", "      <td>211</td>\n", "      <td>211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AL</th>\n", "      <td>37</td>\n", "      <td>37</td>\n", "      <td>37</td>\n", "      <td>37</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AP</th>\n", "      <td>128</td>\n", "      <td>128</td>\n", "      <td>128</td>\n", "      <td>128</td>\n", "      <td>128</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B</th>\n", "      <td>19</td>\n", "      <td>19</td>\n", "      <td>19</td>\n", "      <td>19</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BU</th>\n", "      <td>189</td>\n", "      <td>189</td>\n", "      <td>189</td>\n", "      <td>189</td>\n", "      <td>189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>C</th>\n", "      <td>120</td>\n", "      <td>120</td>\n", "      <td>120</td>\n", "      <td>120</td>\n", "      <td>120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CF</th>\n", "      <td>156</td>\n", "      <td>156</td>\n", "      <td>156</td>\n", "      <td>156</td>\n", "      <td>156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CJ</th>\n", "      <td>82</td>\n", "      <td>82</td>\n", "      <td>82</td>\n", "      <td>82</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CS</th>\n", "      <td>103</td>\n", "      <td>103</td>\n", "      <td>103</td>\n", "      <td>103</td>\n", "      <td>103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CY</th>\n", "      <td>23</td>\n", "      <td>23</td>\n", "      <td>23</td>\n", "      <td>23</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EB</th>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EG</th>\n", "      <td>134</td>\n", "      <td>134</td>\n", "      <td>134</td>\n", "      <td>134</td>\n", "      <td>134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FG</th>\n", "      <td>158</td>\n", "      <td>158</td>\n", "      <td>158</td>\n", "      <td>158</td>\n", "      <td>158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HC</th>\n", "      <td>159</td>\n", "      <td>159</td>\n", "      <td>159</td>\n", "      <td>159</td>\n", "      <td>159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>I</th>\n", "      <td>169</td>\n", "      <td>169</td>\n", "      <td>169</td>\n", "      <td>169</td>\n", "      <td>169</td>\n", "    </tr>\n", "    <tr>\n", "      <th>JD</th>\n", "      <td>144</td>\n", "      <td>144</td>\n", "      <td>144</td>\n", "      <td>144</td>\n", "      <td>144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>JM</th>\n", "      <td>94</td>\n", "      <td>94</td>\n", "      <td>94</td>\n", "      <td>94</td>\n", "      <td>94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>L</th>\n", "      <td>144</td>\n", "      <td>144</td>\n", "      <td>144</td>\n", "      <td>144</td>\n", "      <td>144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>M</th>\n", "      <td>172</td>\n", "      <td>172</td>\n", "      <td>172</td>\n", "      <td>172</td>\n", "      <td>172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MA</th>\n", "      <td>164</td>\n", "      <td>164</td>\n", "      <td>164</td>\n", "      <td>164</td>\n", "      <td>164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NI</th>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OI</th>\n", "      <td>175</td>\n", "      <td>175</td>\n", "      <td>175</td>\n", "      <td>175</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>P</th>\n", "      <td>196</td>\n", "      <td>196</td>\n", "      <td>196</td>\n", "      <td>196</td>\n", "      <td>196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PB</th>\n", "      <td>22</td>\n", "      <td>22</td>\n", "      <td>22</td>\n", "      <td>22</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PF</th>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PG</th>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PK</th>\n", "      <td>18</td>\n", "      <td>18</td>\n", "      <td>18</td>\n", "      <td>18</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PP</th>\n", "      <td>145</td>\n", "      <td>145</td>\n", "      <td>145</td>\n", "      <td>145</td>\n", "      <td>145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RB</th>\n", "      <td>178</td>\n", "      <td>178</td>\n", "      <td>178</td>\n", "      <td>178</td>\n", "      <td>178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM</th>\n", "      <td>158</td>\n", "      <td>158</td>\n", "      <td>158</td>\n", "      <td>158</td>\n", "      <td>158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RU</th>\n", "      <td>172</td>\n", "      <td>172</td>\n", "      <td>172</td>\n", "      <td>172</td>\n", "      <td>172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SA</th>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "      <td>182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SF</th>\n", "      <td>81</td>\n", "      <td>81</td>\n", "      <td>81</td>\n", "      <td>81</td>\n", "      <td>81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SM</th>\n", "      <td>87</td>\n", "      <td>87</td>\n", "      <td>87</td>\n", "      <td>87</td>\n", "      <td>87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SP</th>\n", "      <td>123</td>\n", "      <td>123</td>\n", "      <td>123</td>\n", "      <td>123</td>\n", "      <td>123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SR</th>\n", "      <td>106</td>\n", "      <td>106</td>\n", "      <td>106</td>\n", "      <td>106</td>\n", "      <td>106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SS</th>\n", "      <td>35</td>\n", "      <td>35</td>\n", "      <td>35</td>\n", "      <td>35</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TA</th>\n", "      <td>166</td>\n", "      <td>166</td>\n", "      <td>166</td>\n", "      <td>166</td>\n", "      <td>166</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UR</th>\n", "      <td>141</td>\n", "      <td>141</td>\n", "      <td>141</td>\n", "      <td>141</td>\n", "      <td>141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>V</th>\n", "      <td>133</td>\n", "      <td>133</td>\n", "      <td>133</td>\n", "      <td>133</td>\n", "      <td>133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Y</th>\n", "      <td>166</td>\n", "      <td>166</td>\n", "      <td>166</td>\n", "      <td>166</td>\n", "      <td>166</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZN</th>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      ord_id  instrument  datetime  direct  label\n", "CODE                                             \n", "A        126         126       126     126    126\n", "AG       211         211       211     211    211\n", "AL        37          37        37      37     37\n", "AP       128         128       128     128    128\n", "B         19          19        19      19     19\n", "BU       189         189       189     189    189\n", "C        120         120       120     120    120\n", "CF       156         156       156     156    156\n", "CJ        82          82        82      82     82\n", "CS       103         103       103     103    103\n", "CY        23          23        23      23     23\n", "EB        72          72        72      72     72\n", "EG       134         134       134     134    134\n", "FG       158         158       158     158    158\n", "HC       159         159       159     159    159\n", "I        169         169       169     169    169\n", "JD       144         144       144     144    144\n", "JM        94          94        94      94     94\n", "L        144         144       144     144    144\n", "M        172         172       172     172    172\n", "MA       164         164       164     164    164\n", "NI        33          33        33      33     33\n", "OI       175         175       175     175    175\n", "P        196         196       196     196    196\n", "PB        22          22        22      22     22\n", "PF        72          72        72      72     72\n", "PG        20          20        20      20     20\n", "PK        18          18        18      18     18\n", "PP       145         145       145     145    145\n", "RB       178         178       178     178    178\n", "RM       158         158       158     158    158\n", "RU       172         172       172     172    172\n", "SA       182         182       182     182    182\n", "SF        81          81        81      81     81\n", "SM        87          87        87      87     87\n", "SP       123         123       123     123    123\n", "SR       106         106       106     106    106\n", "SS        35          35        35      35     35\n", "TA       166         166       166     166    166\n", "UR       141         141       141     141    141\n", "V        133         133       133     133    133\n", "Y        166         166       166     166    166\n", "ZN        33          33        33      33     33"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["lb_df.groupby('CODE').count()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>lf_MACD</th>\n", "      <th>lf_MACD_DIFF</th>\n", "      <th>lf_MACD_DEA</th>\n", "      <th>lf_MOM</th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>...</th>\n", "      <th>lf_TREND_VALUE_1</th>\n", "      <th>lf_TREND_VALUE_2</th>\n", "      <th>lf_TREND_BARS_1</th>\n", "      <th>lf_TREND_BARS_2</th>\n", "      <th>lf_TREND_INBARS_1</th>\n", "      <th>lf_TREND_INBARS_2</th>\n", "      <th>lf_TREND_INPOSR_1</th>\n", "      <th>lf_TREND_INPOSR_2</th>\n", "      <th>lf_TREND_HLR</th>\n", "      <th>lf_TREND_LEVEL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1613</th>\n", "      <td>220302090658065</td>\n", "      <td>3.269461</td>\n", "      <td>-0.768722</td>\n", "      <td>-4.038182</td>\n", "      <td>65.0</td>\n", "      <td>3.286440</td>\n", "      <td>51.953386</td>\n", "      <td>-1.5</td>\n", "      <td>-4.9</td>\n", "      <td>3.084848</td>\n", "      <td>...</td>\n", "      <td>-2.0</td>\n", "      <td>-1.0</td>\n", "      <td>14.0</td>\n", "      <td>20.0</td>\n", "      <td>16.0</td>\n", "      <td>18.0</td>\n", "      <td>0.863636</td>\n", "      <td>2.272727</td>\n", "      <td>5.068182</td>\n", "      <td>-1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1614</th>\n", "      <td>220302090832067</td>\n", "      <td>4.497810</td>\n", "      <td>31.520725</td>\n", "      <td>27.022915</td>\n", "      <td>177.0</td>\n", "      <td>1.396108</td>\n", "      <td>72.079040</td>\n", "      <td>1.5</td>\n", "      <td>18.2</td>\n", "      <td>1.575758</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>21.0</td>\n", "      <td>29.0</td>\n", "      <td>-4.0</td>\n", "      <td>0.0</td>\n", "      <td>-2.920000</td>\n", "      <td>0.240000</td>\n", "      <td>10.280000</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1615</th>\n", "      <td>220302095319219</td>\n", "      <td>30.123383</td>\n", "      <td>27.039780</td>\n", "      <td>-3.083604</td>\n", "      <td>255.0</td>\n", "      <td>0.431566</td>\n", "      <td>58.631214</td>\n", "      <td>30.4</td>\n", "      <td>120.8</td>\n", "      <td>9.993939</td>\n", "      <td>...</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>-13.0</td>\n", "      <td>1.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.128378</td>\n", "      <td>0.000000</td>\n", "      <td>3.695946</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1616</th>\n", "      <td>220302103003293</td>\n", "      <td>17.507546</td>\n", "      <td>47.867602</td>\n", "      <td>30.360056</td>\n", "      <td>262.0</td>\n", "      <td>2.346889</td>\n", "      <td>66.130286</td>\n", "      <td>0.8</td>\n", "      <td>68.4</td>\n", "      <td>5.636364</td>\n", "      <td>...</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>-9.0</td>\n", "      <td>2.0</td>\n", "      <td>-2.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.684932</td>\n", "      <td>0.000000</td>\n", "      <td>5.082192</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1617</th>\n", "      <td>220302104244343</td>\n", "      <td>2.112493</td>\n", "      <td>29.643313</td>\n", "      <td>27.530820</td>\n", "      <td>127.0</td>\n", "      <td>-2.024042</td>\n", "      <td>66.655645</td>\n", "      <td>-8.3</td>\n", "      <td>6.1</td>\n", "      <td>0.915152</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>22.0</td>\n", "      <td>30.0</td>\n", "      <td>-3.0</td>\n", "      <td>1.0</td>\n", "      <td>-1.880000</td>\n", "      <td>1.280000</td>\n", "      <td>10.280000</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 46 columns</p>\n", "</div>"], "text/plain": ["               ord_id    lf_MACD  lf_MACD_DIFF  lf_MACD_DEA  lf_MOM  lf_RSI_1  \\\n", "1613  220302090658065   3.269461     -0.768722    -4.038182    65.0  3.286440   \n", "1614  220302090832067   4.497810     31.520725    27.022915   177.0  1.396108   \n", "1615  220302095319219  30.123383     27.039780    -3.083604   255.0  0.431566   \n", "1616  220302103003293  17.507546     47.867602    30.360056   262.0  2.346889   \n", "1617  220302104244343   2.112493     29.643313    27.530820   127.0 -2.024042   \n", "\n", "       lf_RSI_2  lf_LR_SLOPE_FAST_1  lf_LR_SLOPE_FAST_2  lf_LR_SLOPE_MIDD_1  \\\n", "1613  51.953386                -1.5                -4.9            3.084848   \n", "1614  72.079040                 1.5                18.2            1.575758   \n", "1615  58.631214                30.4               120.8            9.993939   \n", "1616  66.130286                 0.8                68.4            5.636364   \n", "1617  66.655645                -8.3                 6.1            0.915152   \n", "\n", "      ...  lf_TREND_VALUE_1  lf_TREND_VALUE_2  lf_TREND_BARS_1  \\\n", "1613  ...              -2.0              -1.0             14.0   \n", "1614  ...               0.0               1.0             21.0   \n", "1615  ...               2.0               1.0            -13.0   \n", "1616  ...               2.0               1.0             -9.0   \n", "1617  ...               0.0               1.0             22.0   \n", "\n", "      lf_TREND_BARS_2  lf_TREND_INBARS_1  lf_TREND_INBARS_2  \\\n", "1613             20.0               16.0               18.0   \n", "1614             29.0               -4.0                0.0   \n", "1615              1.0               -1.0                0.0   \n", "1616              2.0               -2.0                0.0   \n", "1617             30.0               -3.0                1.0   \n", "\n", "      lf_TREND_INPOSR_1  lf_TREND_INPOSR_2  lf_TREND_HLR  lf_TREND_LEVEL  \n", "1613           0.863636           2.272727      5.068182            -1.0  \n", "1614          -2.920000           0.240000     10.280000             3.0  \n", "1615          -0.128378           0.000000      3.695946             2.0  \n", "1616          -1.684932           0.000000      5.082192             2.0  \n", "1617          -1.880000           1.280000     10.280000             3.0  \n", "\n", "[5 rows x 46 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["lf_df.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_STDDEV</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_STDDEV</th>\n", "      <th>FAST_QH_LR_SLOPE_PREV</th>\n", "      <th>FAST_QH_LR_SLOPE</th>\n", "      <th>SLOW_QH_LR_SLOPE_PREV</th>\n", "      <th>SLOW_QH_LR_SLOPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>210922090006095</td>\n", "      <td>1.232618</td>\n", "      <td>3.74</td>\n", "      <td>41.0</td>\n", "      <td>-1.606082</td>\n", "      <td>0.0</td>\n", "      <td>0.766325</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>210922090015109</td>\n", "      <td>1.095635</td>\n", "      <td>8.00</td>\n", "      <td>80.0</td>\n", "      <td>-1.606082</td>\n", "      <td>0.0</td>\n", "      <td>0.766325</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>210922090500167</td>\n", "      <td>1.366270</td>\n", "      <td>15.00</td>\n", "      <td>122.0</td>\n", "      <td>0.920621</td>\n", "      <td>0.0</td>\n", "      <td>1.517871</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>210922090502169</td>\n", "      <td>1.428389</td>\n", "      <td>10.00</td>\n", "      <td>76.0</td>\n", "      <td>0.920621</td>\n", "      <td>0.0</td>\n", "      <td>1.517871</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>210922090503177</td>\n", "      <td>1.449321</td>\n", "      <td>22.00</td>\n", "      <td>170.0</td>\n", "      <td>0.920621</td>\n", "      <td>0.0</td>\n", "      <td>1.517871</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            ord_id  STDDEV_RNG  SHORT_RANGE  LONG_RANGE  FAST_QH_RSI  \\\n", "0  210922090006095    1.232618         3.74        41.0    -1.606082   \n", "1  210922090015109    1.095635         8.00        80.0    -1.606082   \n", "2  210922090500167    1.366270        15.00       122.0     0.920621   \n", "3  210922090502169    1.428389        10.00        76.0     0.920621   \n", "4  210922090503177    1.449321        22.00       170.0     0.920621   \n", "\n", "   FAST_QH_STDDEV  SLOW_QH_RSI  SLOW_QH_STDDEV  FAST_QH_LR_SLOPE_PREV  \\\n", "0             0.0     0.766325             0.0                    0.0   \n", "1             0.0     0.766325             0.0                    0.0   \n", "2             0.0     1.517871             0.0                    0.0   \n", "3             0.0     1.517871             0.0                    0.0   \n", "4             0.0     1.517871             0.0                    0.0   \n", "\n", "   FAST_QH_LR_SLOPE  SLOW_QH_LR_SLOPE_PREV  SLOW_QH_LR_SLOPE  \n", "0               0.0                    0.0               0.0  \n", "1               0.0                    0.0               0.0  \n", "2               0.0                    0.0               0.0  \n", "3               0.0                    0.0               0.0  \n", "4               0.0                    0.0               0.0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["ct_df.head()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["ct = ct_df[ct_df.FAST_QH_LR_SLOPE>0.0]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_STDDEV</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_STDDEV</th>\n", "      <th>FAST_QH_LR_SLOPE_PREV</th>\n", "      <th>FAST_QH_LR_SLOPE</th>\n", "      <th>SLOW_QH_LR_SLOPE_PREV</th>\n", "      <th>SLOW_QH_LR_SLOPE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1357</th>\n", "      <td>220207215328003</td>\n", "      <td>1.210352</td>\n", "      <td>11.0</td>\n", "      <td>89.0</td>\n", "      <td>5.043548</td>\n", "      <td>1.755591</td>\n", "      <td>12.745402</td>\n", "      <td>0.0</td>\n", "      <td>0.489954</td>\n", "      <td>0.489954</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1368</th>\n", "      <td>220208210004029</td>\n", "      <td>1.450600</td>\n", "      <td>29.0</td>\n", "      <td>264.0</td>\n", "      <td>-2.046294</td>\n", "      <td>1.707413</td>\n", "      <td>11.600708</td>\n", "      <td>0.0</td>\n", "      <td>0.106363</td>\n", "      <td>0.106363</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1375</th>\n", "      <td>220209090625059</td>\n", "      <td>1.287838</td>\n", "      <td>16.0</td>\n", "      <td>133.0</td>\n", "      <td>-2.699128</td>\n", "      <td>1.466816</td>\n", "      <td>9.628484</td>\n", "      <td>0.0</td>\n", "      <td>0.388763</td>\n", "      <td>0.388763</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1384</th>\n", "      <td>220210090239055</td>\n", "      <td>1.729244</td>\n", "      <td>7.0</td>\n", "      <td>56.0</td>\n", "      <td>5.440031</td>\n", "      <td>1.228322</td>\n", "      <td>9.585157</td>\n", "      <td>0.0</td>\n", "      <td>0.154525</td>\n", "      <td>0.154525</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1386</th>\n", "      <td>220210110007179</td>\n", "      <td>1.395059</td>\n", "      <td>9.0</td>\n", "      <td>62.0</td>\n", "      <td>4.261201</td>\n", "      <td>0.710493</td>\n", "      <td>9.771053</td>\n", "      <td>0.0</td>\n", "      <td>0.163166</td>\n", "      <td>0.163166</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               ord_id  STDDEV_RNG  SHORT_RANGE  LONG_RANGE  FAST_QH_RSI  \\\n", "1357  220207215328003    1.210352         11.0        89.0     5.043548   \n", "1368  220208210004029    1.450600         29.0       264.0    -2.046294   \n", "1375  220209090625059    1.287838         16.0       133.0    -2.699128   \n", "1384  220210090239055    1.729244          7.0        56.0     5.440031   \n", "1386  220210110007179    1.395059          9.0        62.0     4.261201   \n", "\n", "      FAST_QH_STDDEV  SLOW_QH_RSI  SLOW_QH_STDDEV  FAST_QH_LR_SLOPE_PREV  \\\n", "1357        1.755591    12.745402             0.0               0.489954   \n", "1368        1.707413    11.600708             0.0               0.106363   \n", "1375        1.466816     9.628484             0.0               0.388763   \n", "1384        1.228322     9.585157             0.0               0.154525   \n", "1386        0.710493     9.771053             0.0               0.163166   \n", "\n", "      FAST_QH_LR_SLOPE  SLOW_QH_LR_SLOPE_PREV  SLOW_QH_LR_SLOPE  \n", "1357          0.489954                    0.0               0.0  \n", "1368          0.106363                    0.0               0.0  \n", "1375          0.388763                    0.0               0.0  \n", "1384          0.154525                    0.0               0.0  \n", "1386          0.163166                    0.0               0.0  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ct.head()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(678, 12) (5046, 12)\n"]}], "source": ["print(ct.shape, ct_df.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}