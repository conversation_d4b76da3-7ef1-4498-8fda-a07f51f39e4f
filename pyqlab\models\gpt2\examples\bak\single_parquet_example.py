"""
读取单个parquet文件示例

演示如何读取单个parquet文件并使用CandlestickLLM模型进行训练和预测
"""

import os
import sys
import argparse
import torch
import pandas as pd
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和工具函数
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.utils import (
    load_kline_from_parquet_file,
    plot_candlestick
)

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='读取单个parquet文件示例')
    parser.add_argument('--file_path', type=str, required=True, help='parquet文件路径')
    parser.add_argument('--start_date', type=str, help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--output_dir', type=str, default='./output', help='输出目录')
    args = parser.parse_args()
    
    # 读取parquet文件
    print(f"读取parquet文件: {args.file_path}")
    try:
        df = load_kline_from_parquet_file(
            file_path=args.file_path,
            start_date=args.start_date,
            end_date=args.end_date
        )
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 打印数据信息
    print(f"读取到 {len(df)} 条K线数据")
    print("数据范围:", df['datetime'].min(), "至", df['datetime'].max())
    print("数据列:", df.columns.tolist())
    print("数据样例:")
    print(df.head())
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 保存数据信息
    with open(os.path.join(args.output_dir, 'data_info.txt'), 'w', encoding='utf-8') as f:
        f.write(f"文件路径: {args.file_path}\n")
        f.write(f"数据条数: {len(df)}\n")
        f.write(f"数据范围: {df['datetime'].min()} 至 {df['datetime'].max()}\n")
        f.write(f"数据列: {df.columns.tolist()}\n")
        f.write("\n数据统计:\n")
        f.write(df.describe().to_string())
    
    # 绘制K线图
    print("绘制K线图...")
    
    # 如果数据太多，只绘制最近的100条
    if len(df) > 100:
        plot_df = df.iloc[-100:].copy()
    else:
        plot_df = df.copy()
    
    # 绘制K线图
    fig = plot_candlestick(plot_df, title=f"K线图 ({os.path.basename(args.file_path)})")
    fig.savefig(os.path.join(args.output_dir, 'candlestick.png'))
    plt.close(fig)
    
    # 创建tokenizer
    print("创建tokenizer...")
    tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )
    
    # 将K线数据转换为token
    print("将K线数据转换为token...")
    tokens = tokenizer.tokenize(df)
    
    # 保存token信息
    with open(os.path.join(args.output_dir, 'token_info.txt'), 'w', encoding='utf-8') as f:
        f.write(f"Token数量: {len(tokens)}\n")
        f.write(f"Token范围: {min(tokens)} 至 {max(tokens)}\n")
        f.write(f"Token分布: {pd.Series(tokens).value_counts().to_dict()}\n")
        f.write("\nToken序列 (前100个):\n")
        f.write(str(tokens[:100]))
    
    # 如果数据足够多，尝试将token转换回K线数据
    if len(tokens) >= 10:
        print("将token转换回K线数据...")
        # 使用最后10个token
        sample_tokens = tokens[-10:]
        
        # 获取最后一个K线的收盘价和ATR
        last_close = df['close'].iloc[-1]
        atr = tokenizer._calculate_atr(df).iloc[-1]
        
        # 将token转换为K线数据
        reconstructed_df = tokenizer.tokens_to_candlesticks(
            sample_tokens,
            start_price=last_close,
            atr=atr
        )
        
        # 保存重建的K线数据
        reconstructed_df.to_csv(os.path.join(args.output_dir, 'reconstructed.csv'), index=False)
        
        # 绘制重建的K线图
        fig = plot_candlestick(reconstructed_df, title="重建的K线图")
        fig.savefig(os.path.join(args.output_dir, 'reconstructed.png'))
        plt.close(fig)
    
    print(f"处理完成，结果保存在 {args.output_dir} 目录下")

if __name__ == '__main__':
    main()
