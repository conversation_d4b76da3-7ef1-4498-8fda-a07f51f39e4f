"""
Candlestick Data Augmentation

K线数据增强，用于扩充训练数据
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Any
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer

class CandlestickDataAugmentation:
    """K线数据增强"""
    
    def __init__(self, tokenizer: CandlestickTokenizer = None):
        """
        初始化K线数据增强器
        
        Args:
            tokenizer: 用于tokenize增强后数据的tokenizer
        """
        self.tokenizer = tokenizer
        
    def time_warp(self, df: pd.DataFrame, warp_factor: float = 0.2) -> pd.DataFrame:
        """
        时间扭曲增强
        
        Args:
            df: 包含OHLCV数据的DataFrame
            warp_factor: 扭曲因子，控制扭曲程度
            
        Returns:
            增强后的DataFrame
        """
        # 复制数据
        augmented_df = df.copy()
        
        # 确保datetime列存在
        if 'datetime' not in augmented_df.columns:
            # 如果没有datetime列，添加一个假的
            base_date = datetime.now()
            dates = [base_date + timedelta(minutes=i) for i in range(len(augmented_df))]
            augmented_df['datetime'] = dates
            
        # 获取时间索引
        augmented_df['datetime'] = pd.to_datetime(augmented_df['datetime'])
        
        # 计算时间间隔
        time_deltas = []
        for i in range(1, len(augmented_df)):
            delta = (augmented_df['datetime'].iloc[i] - augmented_df['datetime'].iloc[i-1]).total_seconds()
            time_deltas.append(delta)
            
        # 随机扭曲时间间隔
        np.random.seed(None)  # 确保随机性
        random_factors = np.random.normal(1.0, warp_factor, len(time_deltas))
        random_factors = np.clip(random_factors, 0.5, 2.0)  # 限制扭曲范围
        new_deltas = [td * rf for td, rf in zip(time_deltas, random_factors)]
        
        # 创建新的时间索引
        new_datetimes = [augmented_df['datetime'].iloc[0]]
        for delta in new_deltas:
            new_dt = new_datetimes[-1] + timedelta(seconds=delta)
            new_datetimes.append(new_dt)
            
        # 应用新的时间索引
        augmented_df['datetime'] = new_datetimes
        
        return augmented_df
        
    def magnitude_warp(self, df: pd.DataFrame, warp_factor: float = 0.2) -> pd.DataFrame:
        """
        幅度扭曲增强
        
        Args:
            df: 包含OHLCV数据的DataFrame
            warp_factor: 扭曲因子，控制扭曲程度
            
        Returns:
            增强后的DataFrame
        """
        # 复制数据
        augmented_df = df.copy()
        
        # 生成随机扭曲因子
        np.random.seed(None)  # 确保随机性
        n_samples = len(augmented_df)
        
        # 创建平滑的扭曲因子序列
        num_points = max(3, int(n_samples / 10))  # 控制点数量
        control_points = np.linspace(0, n_samples - 1, num_points, dtype=int)
        control_values = 1.0 + np.random.normal(0, warp_factor, num_points)
        
        # 使用样条插值创建平滑的扭曲因子
        from scipy.interpolate import interp1d
        interpolator = interp1d(control_points, control_values, kind='cubic', 
                               fill_value='extrapolate')
        warp_factors = interpolator(np.arange(n_samples))
        
        # 应用扭曲因子
        for col in ['open', 'high', 'low', 'close']:
            if col in augmented_df.columns:
                base_price = augmented_df[col].iloc[0]
                price_changes = (augmented_df[col] / augmented_df[col].shift(1)).fillna(1.0)
                
                # 应用扭曲
                warped_changes = price_changes * warp_factors
                
                # 重建价格序列
                augmented_df[col] = base_price
                for i in range(1, n_samples):
                    augmented_df.loc[augmented_df.index[i], col] = augmented_df[col].iloc[i-1] * warped_changes.iloc[i]
                    
        # 确保OHLC关系保持一致
        if all(col in augmented_df.columns for col in ['open', 'high', 'low', 'close']):
            augmented_df['high'] = augmented_df[['high', 'open', 'close']].max(axis=1)
            augmented_df['low'] = augmented_df[['low', 'open', 'close']].min(axis=1)
            
        # 如果有交易量，也应用扭曲
        if 'volume' in augmented_df.columns:
            base_volume = augmented_df['volume'].iloc[0]
            volume_changes = (augmented_df['volume'] / augmented_df['volume'].shift(1)).fillna(1.0)
            
            # 应用扭曲
            warped_volume_changes = volume_changes * warp_factors
            
            # 重建交易量序列
            augmented_df['volume'] = base_volume
            for i in range(1, n_samples):
                augmented_df.loc[augmented_df.index[i], 'volume'] = max(0, augmented_df['volume'].iloc[i-1] * warped_volume_changes.iloc[i])
                
        return augmented_df
        
    def jitter(self, df: pd.DataFrame, jitter_factor: float = 0.01) -> pd.DataFrame:
        """
        抖动增强
        
        Args:
            df: 包含OHLCV数据的DataFrame
            jitter_factor: 抖动因子，控制抖动程度
            
        Returns:
            增强后的DataFrame
        """
        # 复制数据
        augmented_df = df.copy()
        
        # 生成随机抖动
        np.random.seed(None)  # 确保随机性
        n_samples = len(augmented_df)
        
        for col in ['open', 'high', 'low', 'close']:
            if col in augmented_df.columns:
                # 计算价格范围
                price_range = augmented_df[col].max() - augmented_df[col].min()
                
                # 生成抖动
                jitter = np.random.normal(0, jitter_factor * price_range, n_samples)
                
                # 应用抖动
                augmented_df[col] = augmented_df[col] + jitter
                
        # 确保OHLC关系保持一致
        if all(col in augmented_df.columns for col in ['open', 'high', 'low', 'close']):
            augmented_df['high'] = augmented_df[['high', 'open', 'close']].max(axis=1)
            augmented_df['low'] = augmented_df[['low', 'open', 'close']].min(axis=1)
            
        # 如果有交易量，也应用抖动
        if 'volume' in augmented_df.columns:
            volume_range = augmented_df['volume'].max() - augmented_df['volume'].min()
            volume_jitter = np.random.normal(0, jitter_factor * volume_range, n_samples)
            augmented_df['volume'] = np.maximum(0, augmented_df['volume'] + volume_jitter)
            
        return augmented_df
        
    def scaling(self, df: pd.DataFrame, scale_factor: float = None) -> pd.DataFrame:
        """
        缩放增强
        
        Args:
            df: 包含OHLCV数据的DataFrame
            scale_factor: 缩放因子，如果为None则随机生成
            
        Returns:
            增强后的DataFrame
        """
        # 复制数据
        augmented_df = df.copy()
        
        # 生成随机缩放因子
        if scale_factor is None:
            np.random.seed(None)  # 确保随机性
            scale_factor = np.random.uniform(0.8, 1.2)
            
        # 应用缩放
        for col in ['open', 'high', 'low', 'close']:
            if col in augmented_df.columns:
                augmented_df[col] = augmented_df[col] * scale_factor
                
        # 如果有交易量，也应用缩放
        if 'volume' in augmented_df.columns:
            # 交易量可以使用不同的缩放因子
            volume_scale = np.random.uniform(0.7, 1.3)
            augmented_df['volume'] = augmented_df['volume'] * volume_scale
            
        return augmented_df
        
    def window_slice(self, df: pd.DataFrame, slice_ratio: float = 0.8) -> pd.DataFrame:
        """
        窗口切片增强
        
        Args:
            df: 包含OHLCV数据的DataFrame
            slice_ratio: 切片比例，控制切片大小
            
        Returns:
            增强后的DataFrame
        """
        # 确保切片比例有效
        slice_ratio = max(0.5, min(1.0, slice_ratio))
        
        # 计算切片大小
        n_samples = len(df)
        slice_size = int(n_samples * slice_ratio)
        
        # 随机选择起始位置
        np.random.seed(None)  # 确保随机性
        start_idx = np.random.randint(0, n_samples - slice_size + 1)
        
        # 切片
        sliced_df = df.iloc[start_idx:start_idx+slice_size].copy()
        
        # 重置索引
        sliced_df = sliced_df.reset_index(drop=True)
        
        return sliced_df
        
    def pattern_mix(self, df1: pd.DataFrame, df2: pd.DataFrame, mix_point: int = None) -> pd.DataFrame:
        """
        模式混合增强
        
        Args:
            df1: 第一个DataFrame
            df2: 第二个DataFrame
            mix_point: 混合点，如果为None则随机生成
            
        Returns:
            混合后的DataFrame
        """
        # 确保两个DataFrame有相同的列
        common_cols = set(df1.columns).intersection(set(df2.columns))
        df1 = df1[common_cols].copy()
        df2 = df2[common_cols].copy()
        
        # 确定混合点
        if mix_point is None:
            np.random.seed(None)  # 确保随机性
            mix_point = np.random.randint(1, min(len(df1), len(df2)) - 1)
            
        # 混合数据
        mixed_df = pd.concat([df1.iloc[:mix_point], df2.iloc[mix_point:mix_point+len(df1)-mix_point]])
        
        # 重置索引
        mixed_df = mixed_df.reset_index(drop=True)
        
        # 调整价格，使连接点平滑
        if all(col in mixed_df.columns for col in ['open', 'high', 'low', 'close']):
            # 计算连接点的价格差
            price_diff = mixed_df['close'].iloc[mix_point-1] - mixed_df['open'].iloc[mix_point]
            
            # 调整连接点之后的价格
            for col in ['open', 'high', 'low', 'close']:
                mixed_df.loc[mixed_df.index[mix_point:], col] += price_diff
                
        return mixed_df
        
    def generate_augmented_dataset(self, df: pd.DataFrame, n_augmentations: int = 5, 
                                  methods: List[str] = None) -> List[pd.DataFrame]:
        """
        生成增强数据集
        
        Args:
            df: 原始DataFrame
            n_augmentations: 生成的增强数据数量
            methods: 使用的增强方法列表，如果为None则使用所有方法
            
        Returns:
            增强后的DataFrame列表
        """
        # 默认使用所有方法
        if methods is None:
            methods = ['time_warp', 'magnitude_warp', 'jitter', 'scaling', 'window_slice']
            
        # 验证方法
        valid_methods = ['time_warp', 'magnitude_warp', 'jitter', 'scaling', 'window_slice']
        methods = [m for m in methods if m in valid_methods]
        
        if not methods:
            raise ValueError("没有有效的增强方法")
            
        # 生成增强数据
        augmented_dfs = [df]  # 包含原始数据
        
        for i in range(n_augmentations):
            # 随机选择增强方法
            method = np.random.choice(methods)
            
            # 应用增强方法
            if method == 'time_warp':
                aug_df = self.time_warp(df)
            elif method == 'magnitude_warp':
                aug_df = self.magnitude_warp(df)
            elif method == 'jitter':
                aug_df = self.jitter(df)
            elif method == 'scaling':
                aug_df = self.scaling(df)
            elif method == 'window_slice':
                aug_df = self.window_slice(df)
                
            augmented_dfs.append(aug_df)
            
        return augmented_dfs
        
    def visualize_augmentation(self, original_df: pd.DataFrame, augmented_df: pd.DataFrame, 
                              method: str = None):
        """
        可视化增强效果
        
        Args:
            original_df: 原始DataFrame
            augmented_df: 增强后的DataFrame
            method: 使用的增强方法
        """
        # 创建图表
        fig, axes = plt.subplots(2, 1, figsize=(12, 10), sharex=False)
        
        # 绘制原始K线
        ax1 = axes[0]
        ax1.set_title('原始K线数据')
        
        for i in range(len(original_df)):
            # 计算位置和颜色
            x = i
            open_price = original_df['open'].iloc[i]
            close_price = original_df['close'].iloc[i]
            high_price = original_df['high'].iloc[i]
            low_price = original_df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)
            
        # 绘制增强后的K线
        ax2 = axes[1]
        title = f'增强后的K线数据 (方法: {method})' if method else '增强后的K线数据'
        ax2.set_title(title)
        
        for i in range(len(augmented_df)):
            # 计算位置和颜色
            x = i
            open_price = augmented_df['open'].iloc[i]
            close_price = augmented_df['close'].iloc[i]
            high_price = augmented_df['high'].iloc[i]
            low_price = augmented_df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制实体
            ax2.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax2.plot([x, x], [low_price, high_price], color=color, linewidth=1)
            
        # 如果有交易量，绘制交易量
        if 'volume' in original_df.columns and 'volume' in augmented_df.columns:
            # 添加交易量子图
            fig, axes = plt.subplots(4, 1, figsize=(12, 15), sharex=False, 
                                    gridspec_kw={'height_ratios': [3, 1, 3, 1]})
            
            # 重新绘制K线
            ax1, ax_vol1, ax2, ax_vol2 = axes
            
            # 绘制原始K线
            ax1.set_title('原始K线数据')
            for i in range(len(original_df)):
                x = i
                open_price = original_df['open'].iloc[i]
                close_price = original_df['close'].iloc[i]
                high_price = original_df['high'].iloc[i]
                low_price = original_df['low'].iloc[i]
                color = 'red' if close_price >= open_price else 'green'
                ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
                ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)
                
            # 绘制原始交易量
            ax_vol1.set_title('原始交易量')
            ax_vol1.bar(range(len(original_df)), original_df['volume'], color='blue', alpha=0.5)
            
            # 绘制增强后的K线
            ax2.set_title(title)
            for i in range(len(augmented_df)):
                x = i
                open_price = augmented_df['open'].iloc[i]
                close_price = augmented_df['close'].iloc[i]
                high_price = augmented_df['high'].iloc[i]
                low_price = augmented_df['low'].iloc[i]
                color = 'red' if close_price >= open_price else 'green'
                ax2.plot([x, x], [open_price, close_price], color=color, linewidth=6)
                ax2.plot([x, x], [low_price, high_price], color=color, linewidth=1)
                
            # 绘制增强后的交易量
            ax_vol2.set_title('增强后的交易量')
            ax_vol2.bar(range(len(augmented_df)), augmented_df['volume'], color='blue', alpha=0.5)
            
        # 添加网格线
        for ax in axes:
            ax.grid(True, linestyle='--', alpha=0.7)
            
        plt.tight_layout()
        plt.show()
        
        return fig, axes
        
    def tokenize_and_compare(self, original_df: pd.DataFrame, augmented_df: pd.DataFrame):
        """
        对原始数据和增强数据进行tokenize并比较
        
        Args:
            original_df: 原始DataFrame
            augmented_df: 增强后的DataFrame
            
        Returns:
            原始tokens和增强后tokens
        """
        if self.tokenizer is None:
            raise ValueError("需要提供tokenizer才能进行tokenize")
            
        # tokenize
        original_tokens = self.tokenizer.tokenize(original_df)
        augmented_tokens = self.tokenizer.tokenize(augmented_df)
        
        # 打印token统计信息
        print(f"原始数据: {len(original_df)}条K线, {len(original_tokens)}个tokens")
        print(f"增强数据: {len(augmented_df)}条K线, {len(augmented_tokens)}个tokens")
        
        # 计算token分布差异
        if len(original_tokens) > 0 and len(augmented_tokens) > 0:
            # 统计token频率
            from collections import Counter
            original_counter = Counter(original_tokens)
            augmented_counter = Counter(augmented_tokens)
            
            # 计算最常见的tokens
            top_original = original_counter.most_common(5)
            top_augmented = augmented_counter.most_common(5)
            
            print("\n最常见的原始tokens:")
            for token_idx, count in top_original:
                token_str = self.tokenizer.idx2token[token_idx]
                print(f"  {token_str}: {count}次 ({count/len(original_tokens):.2%})")
                
            print("\n最常见的增强tokens:")
            for token_idx, count in top_augmented:
                token_str = self.tokenizer.idx2token[token_idx]
                print(f"  {token_str}: {count}次 ({count/len(augmented_tokens):.2%})")
                
        return original_tokens, augmented_tokens
