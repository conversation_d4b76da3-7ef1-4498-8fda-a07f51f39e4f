import torch
import torch.nn as nn
import numpy as np
import math

import torch
import torch.nn as nn
import numpy as np
import math

class TimeSeriesModel2dr(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self,
                 num_embeds=[72],
                 num_channel=5,
                 num_input=45,
                 dropout=0.5,
                 kernel_size=3,
                 out_channels=(32, 64, 1152, 256),
                 ins_nums=(0,51,51,8),
                 activation="relu",
                 pooling="max",
                 ):
        super(TimeSeriesModel2dr, self).__init__()
        print(num_embeds, num_channel, num_input, dropout, out_channels, ins_nums)
        assert len(ins_nums) == 4 and ins_nums[1] == ins_nums[2]
        
        self.n_dims, n_feats = self._determine_dims(ins_nums)
        num_dims = self._calculate_num_dims(num_embeds, n_feats, ins_nums[3])
        
        self.embedding_layers = nn.ModuleList([
            nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i])
            for i in range(len(num_dims))
        ])
        self.flatten = nn.Flatten()
        activation_layer = self._get_activation_layer(activation)
        pooling_layer = self._get_pooling_layer(pooling)
        
        self.conv1 = self._create_conv_layer(num_channel, out_channels[0], kernel_size, activation_layer)
        self.conv2 = self._create_conv_layer(out_channels[0], out_channels[1], kernel_size, activation_layer, pooling_layer)
        
        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation_layer,
            nn.Dropout(dropout),
        )
        self.linear2 = nn.Linear(out_channels[3], 1)

    def _determine_dims(self, ins_nums):
        if ins_nums[0] > 0 and ins_nums[1] == 0 and ins_nums[2] == 0:
            return 2, ins_nums[0]
        elif ins_nums[0] == 0 and ins_nums[1] > 0 and ins_nums[2] > 0:
            return 3, ins_nums[1]
        else:
            raise ValueError("Invalid ins_nums configuration")

    def _calculate_num_dims(self, num_embeds, n_feats, ins_num_3):
        num_dims = [math.ceil(np.sqrt(num_embed)) for num_embed in num_embeds if num_embed > 0]
        dims_sum = sum(num_dims)
        num_dims = [int(num_dim / dims_sum * (n_feats - ins_num_3)) for num_dim in num_dims]
        
        diff = sum(num_dims) - (n_feats - ins_num_3)
        if diff != 0:
            num_dims[0] -= diff
        
        return num_dims

    def _get_activation_layer(self, activation):
        activations = {
            "relu": nn.ReLU(),
            "gelu": nn.GELU(),
            "prelu": nn.PReLU(),
            "leakyrelu": nn.LeakyReLU()
        }
        if activation not in activations:
            raise ValueError("activation must be relu, gelu, prelu, or leakyrelu")
        return activations[activation]

    def _get_pooling_layer(self, pooling):
        poolings = {
            "max": nn.MaxPool2d(kernel_size=2),
            "avg": nn.AvgPool2d(kernel_size=2)
        }
        if pooling not in poolings:
            raise ValueError("pooling must be max or avg")
        return poolings[pooling]

    def _create_conv_layer(self, in_channels, out_channels, kernel_size, activation_layer, pooling_layer=None):
        layers = [
            nn.Conv2d(in_channels=in_channels, out_channels=out_channels, kernel_size=(kernel_size, kernel_size), stride=1, padding=1),
            nn.BatchNorm2d(out_channels),
            activation_layer
        ]
        if pooling_layer:
            layers.append(pooling_layer)
        return nn.Sequential(*layers)

    def forward(self, embds, x):
        assert len(embds.shape) > 2
        if len(self.embedding_layers) > 0:
            embedded_data = self._embed_data(embds)
            x = torch.cat([x, embedded_data], dim=-1)
        
        x = x.reshape(x.shape[0], x.shape[1], self.n_dims, x.shape[2] // self.n_dims)
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)
        return x.view(-1)

    def _embed_data(self, embds):
        embedded_data = None
        n = embds.shape[-1] - len(self.embedding_layers) if embds.shape[-1] > len(self.embedding_layers) else 0
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](embds[:, :, i + n])
            embedded_data = category_data if embedded_data is None else torch.cat([embedded_data, category_data], dim=-1)
        return embedded_data
        

class TimeSeriesModel2dr2(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self,
                  num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  kernel_size=3,
                  out_channels=(32, 64, 1152, 256),
                  ins_nums=(0,51,51,8),
                  activation="relu",
                  pooling="max",
                ):
        super(TimeSeriesModel2dr, self).__init__()
        print(num_embeds, num_channel, num_input, dropout, out_channels, ins_nums)
        assert len(ins_nums) == 4 and ins_nums[1] == ins_nums[2]
        if ins_nums[0] > 0 and ins_nums[1] == 0 and ins_nums[2] == 0:
            self.n_dims = 2
            n_feats = ins_nums[0]
        elif ins_nums[0] == 0 and ins_nums[1] > 0 and ins_nums[2] > 0:
            self.n_dims = 3
            n_feats = ins_nums[1]
        num_dims = []
        for num_embed in num_embeds:
            if num_embed == 0:
                continue
            num_dims.append(math.ceil(np.sqrt(num_embed)))
        dims_sum = sum(num_dims)
        for i in range(len(num_dims)):
            num_dims[i] = int(num_dims[i]/dims_sum * (n_feats - ins_nums[3]))
        
        if sum(num_dims) > n_feats - ins_nums[3]:
            num_dims[0] -= (sum(num_dims) - (n_feats - ins_nums[3]))
        elif sum(num_dims) < n_feats - ins_nums[3]:
            num_dims[0] += ((n_feats - ins_nums[3]) - sum(num_dims))
        self.embedding_layers = nn.ModuleList()
        for i in range(len(num_dims)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i]))
        self.flatten = nn.Flatten()

        if activation == "relu":
            activation = nn.ReLU()
        elif activation == "gelu":
            activation = nn.GELU()
        elif activation == "prelu":
            activation = nn.PReLU()
        elif activation == "leakyrelu":
            activation = nn.LeakyReLU()
        else:
            raise Exception("activation must be relu or gelu")
        
        if pooling == "max":
            pooling = nn.MaxPool2d(kernel_size=2)
        elif pooling == "avg":
            pooling = nn.AvgPool2d(kernel_size=2)
        else:
            raise Exception("pooling must be max or avg")
        
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=(kernel_size,kernel_size), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[0]),
            activation, # nn.ReLU(),
            # nn.MaxPool2d(kernel_size=2),
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=(kernel_size,kernel_size), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[1]),
            activation, # nn.ReLU(),
            pooling, # nn.MaxPool2d(kernel_size=2),
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation, # nn.ReLU(),
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Linear(out_channels[3], 1)


    def forward(self, embds, x):
        assert len(embds.shape) > 2
        # assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        if len(self.embedding_layers) > 0:
            embedded_data = None
            n = 0
            if embds.shape[-1] > len(self.embedding_layers):
                # 当对单个合约进行预测时，跳过对合约代码的嵌入特征
                n = embds.shape[-1] - len(self.embedding_layers)
            for i in range(len(self.embedding_layers)):
                category_data = self.embedding_layers[i](embds[:, :, i+n])
                if embedded_data is None:
                    embedded_data = category_data
                else:
                    embedded_data = torch.cat([embedded_data, category_data], dim=-1)
        
            x = torch.cat([x, embedded_data], dim=-1)
        x = x.reshape(x.shape[0], x.shape[1], self.n_dims, x.shape[2]//self.n_dims)
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)
        return x.view(-1)
        