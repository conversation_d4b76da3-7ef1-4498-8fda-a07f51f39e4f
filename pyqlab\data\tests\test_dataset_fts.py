import unittest
import pandas as pd
import numpy as np
import torch
from unittest.mock import patch, MagicMock
from pyqlab.data.dataset.dataset_fts import FTSDataset

class TestFTSDataset(unittest.TestCase):
    """测试FTSDataset类的功能"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建模拟的DataHandler
        self.handler = MagicMock()
        self.handler.timeenc = 0
        self.handler.ct_cat_cols_names = ['market_type', 'weekday']
        
        # 创建FTSDataset实例
        self.model_type = 1
        self.seq_len = 20
        self.label_len = 5
        self.pred_len = 10
        self.dataset = FTSDataset(
            handler=self.handler,
            model_type=self.model_type,
            seq_len=self.seq_len,
            label_len=self.label_len,
            pred_len=self.pred_len
        )
        
        # 创建测试数据
        # 模拟两个合约的数据，每个合约有50条记录
        code1 = [1] * 50
        code2 = [2] * 50
        codes = code1 + code2
        
        # 创建市场类型和星期几列
        market_type = [0] * 100
        weekday = [1] * 100
        
        # 创建涨跌幅列
        change = [0.01] * 100
        
        # 创建bar_length列
        bar_length = [60] * 100
        
        # 创建时间特征列
        tf0 = [0.1] * 100
        tf1 = [0.2] * 100
        tf2 = [0.3] * 100
        tf3 = [0.4] * 100
        tf4 = [0.5] * 100
        
        # 创建因子列
        factor1 = [1.0] * 100
        factor2 = [2.0] * 100
        factor3 = [3.0] * 100
        
        # 创建标签DataFrame
        self.lb_df = pd.DataFrame({
            'code_encoded': codes,
            'market_type': market_type,
            'weekday': weekday,
            'change': change,
            'bar_length': bar_length,
            'tf0': tf0,
            'tf1': tf1,
            'tf2': tf2,
            'tf3': tf3,
            'tf4': tf4
        })
        
        # 创建因子DataFrame
        self.ft_df = pd.DataFrame({
            'factor1': factor1,
            'factor2': factor2,
            'factor3': factor3
        })
        
        # 设置handler的属性
        self.handler.lb_df = self.lb_df
        self.handler.ft_df = self.ft_df
    
    def test_init(self):
        """测试初始化功能"""
        # 验证属性是否正确设置
        self.assertEqual(self.dataset.model_type, self.model_type)
        self.assertEqual(self.dataset.seq_len, self.seq_len)
        self.assertEqual(self.dataset.label_len, self.label_len)
        self.assertEqual(self.dataset.pred_len, self.pred_len)
        self.assertEqual(self.dataset.handler, self.handler)
    
    def test_config(self):
        """测试配置功能"""
        handler_kwargs = {'param1': 'value1', 'param2': 'value2'}
        self.dataset.config(handler_kwargs=handler_kwargs)
        self.handler.config.assert_called_once_with(**handler_kwargs)
    
    def test_setup_data(self):
        """测试设置数据功能"""
        handler_kwargs = {'param1': 'value1', 'param2': 'value2'}
        self.dataset.setup_data(handler_kwargs=handler_kwargs)
        self.handler.config.assert_called_once_with(**handler_kwargs)
        self.handler.setup_data.assert_called_once()
    
    def test_get_ins_nums(self):
        """测试获取实例数量功能"""
        self.handler._get_ins_nums.return_value = 10
        result = self.dataset.get_ins_nums()
        self.assertEqual(result, 10)
        self.handler._get_ins_nums.assert_called_once()
    
    def test_save_model_inputs_config(self):
        """测试保存模型输入配置功能"""
        save_path = 'test_save_path'
        self.dataset.save_model_inputs_config(save_path)
        self.handler._dump_input_param_json.assert_called_once_with(
            save_path,
            self.model_type,
            self.seq_len,
            self.label_len,
            self.pred_len
        )
    
    def test_prepare(self):
        """测试准备数据功能"""
        kwargs = {'param1': 'value1', 'param2': 'value2'}
        self.handler.fetch.return_value = (self.lb_df, self.ft_df)
        result = self.dataset.prepare(**kwargs)
        self.handler.fetch.assert_called_once_with(**kwargs)
        self.assertEqual(result, (self.lb_df, self.ft_df))
    
    def test_load_data(self):
        """测试加载数据功能"""
        # 调用load_data方法
        self.dataset.load_data()
        
        # 验证数据是否正确加载
        pd.testing.assert_frame_equal(self.dataset.lb_df, self.lb_df)
        pd.testing.assert_frame_equal(self.dataset.ft_df, self.ft_df)
        
        # 验证codecount计算是否正确
        # 每个合约有50条记录，减去seq_len(20)和pred_len(10)，剩余20条可用作样本
        expected_codecount = np.array([20, 40])  # 第一个合约20个样本，两个合约共40个样本
        np.testing.assert_array_equal(self.dataset.codecount, expected_codecount)
    
    def test_len(self):
        """测试数据集长度功能"""
        # 先加载数据
        self.dataset.load_data()
        
        # 测试长度
        length = len(self.dataset)
        # 两个合约共40个样本
        self.assertEqual(length, 40)
    
    def test_getitem_model_type_0(self):
        """测试获取样本功能 - 模型类型0"""
        # 设置模型类型为0
        self.dataset.model_type = 0
        
        # 加载数据
        self.dataset.load_data()
        
        # 模拟__i_to_idx方法
        with patch.object(self.dataset, '_FTSDataset__i_to_idx', return_value=0):
            # 获取样本
            emb, x, y = self.dataset[0]
            
            # 验证返回值类型
            self.assertIsInstance(emb, torch.Tensor)
            self.assertIsInstance(x, torch.Tensor)
            self.assertIsInstance(y, torch.Tensor)
            
            # 验证形状
            self.assertEqual(emb.shape[0], self.seq_len)  # seq_len行
            self.assertEqual(emb.shape[1], 1 + len(self.handler.ct_cat_cols_names))  # code_encoded + cat_cols列
            self.assertEqual(x.shape[0], self.seq_len)  # seq_len行
            self.assertEqual(x.shape[1], self.ft_df.shape[1])  # 因子列数
            self.assertEqual(y.shape, ())  # 标量
    
    def test_getitem_model_type_1(self):
        """测试获取样本功能 - 模型类型1"""
        # 设置模型类型为1
        self.dataset.model_type = 1
        
        # 加载数据
        self.dataset.load_data()
        
        # 模拟__i_to_idx方法
        with patch.object(self.dataset, '_FTSDataset__i_to_idx', return_value=0):
            # 获取样本
            emb, x_data, x_mark, y_data, y_mark = self.dataset[0]
            
            # 验证返回值类型
            self.assertIsInstance(emb, torch.Tensor)
            self.assertIsInstance(x_data, torch.Tensor)
            self.assertIsInstance(x_mark, torch.Tensor)
            self.assertIsInstance(y_data, torch.Tensor)
            self.assertIsInstance(y_mark, torch.Tensor)
            
            # 验证形状
            self.assertEqual(emb.shape[0], self.seq_len)  # seq_len行
            self.assertEqual(emb.shape[1], 1 + len(self.handler.ct_cat_cols_names))  # code_encoded + cat_cols列
            self.assertEqual(x_data.shape[0], self.seq_len)  # seq_len行
            self.assertEqual(x_data.shape[1], self.ft_df.shape[1])  # 因子列数
            self.assertEqual(x_mark.shape[0], self.seq_len)  # seq_len行
            self.assertEqual(x_mark.shape[1], 5)  # 5个时间特征
            self.assertEqual(y_data.shape[0], self.label_len + self.pred_len)  # label_len + pred_len行
            self.assertEqual(y_data.shape[1], self.ft_df.shape[1])  # 因子列数
            self.assertEqual(y_mark.shape[0], self.label_len + self.pred_len)  # label_len + pred_len行
            self.assertEqual(y_mark.shape[1], 5)  # 5个时间特征

if __name__ == '__main__':
    unittest.main() 