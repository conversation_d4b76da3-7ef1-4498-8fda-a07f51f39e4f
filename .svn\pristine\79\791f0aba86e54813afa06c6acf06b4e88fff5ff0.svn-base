"""
历史数据准备
"""

import os
# import sqlite3
import talib
import pandas as pd
from datetime import datetime
from pyqlab.const import *

class HistoryDataToBar:
    """
    用于通过历史行情数据生成训练数据
    主要支持两个周期数据：1. 日数据   2. 5分钟数据
    支持两个市场：1. A股   2. 期货
    数据源来自通达信pytdx导出，保存数据格式为parquet
    将历史行情数据转换为Bar训练数据
    """
    def __init__(self, data_path: str, market: str, period: str):
        self.data_path = data_path # d:/RoboQuant2/data
        self.market = market
        self.period = period
        self.code_list = []
        self.df = pd.DataFrame()
        self.atr = dict()
        self.window = 15
        self.scale = 10

    def _update_atr(self):
        """
        通过历史行情数据更新atr表
        """
        for code in self.code_list:
            # 技术指标ATR
            atr = talib.ATR(self.df[self.df['code']==code]['high'], 
                           self.df[self.df['code']==code]['low'], 
                           self.df[self.df['code']==code]['close'], timeperiod=self.window)
            self.atr[code] = atr

    def load_data(self):
        path = f'{self.data_path}/store/{self.market}_{self.period}.parquet'
        self.df = pd.read_parquet(path)
        self.code_list = self.df['code'].unique()
        self._update_atr()

    def save_data(self, bar_data):
        df = pd.DataFrame(bar_data, columns=['symbol', 'timestamp', 'change', 'body', 'up_shadow', 'down_shadow'])
        if self.period == 'day':
            path = f'{self.data_path}/store/barenc/bar_{self.market}_{self.period}.csv'
            df.to_csv(path, index=False)
            print(f'save {path} success')
        else:
            years = self._get_years(df['timestamp'])
            for year in years:
                path = f'{self.data_path}/store/barenc/bar_{self.market}_{self.period}_{year}.csv'
                df_year = df[pd.to_datetime(df['timestamp'], unit='s').dt.year == year]
                df_year.to_csv(path, index=False)
                print(f'save {path} success')

    def _get_years(self, date: pd.Series):
        # 将timestamp转换为datetime格式
        date = pd.to_datetime(date, unit='s')
        return date.dt.year.unique()

    def encode_to_bar(self):
        """
        将历史行情数据转换为Bar训练数据
        """
        if self.market == "fut" and self.period == "day":
            self.scale = 10
        bar_data = []
        for code in self.code_list:
            open = self.df[(self.df['code']==code)]['open']
            high = self.df[(self.df['code']==code)]['high']
            low = self.df[(self.df['code']==code)]['low']
            close = self.df[(self.df['code']==code)]['close']
            date = self.df[(self.df['code']==code)]['datetime']
            symbol = code[:len(code)-7]
            cur_date = date.iloc[self.window + 1].date()
            atr = self.atr[code][self.window + 1]/self.scale
            for i in range(self.window + 1, len(open)):
                # 期货有主力期货切换，行情数据不连续
                # 切换日期时，前后两日有较大的跳空，需要跳过，否则，数据会失真
                chg = (close.iloc[i] - close.iloc[i-1])/close.iloc[i-1] * 100
                if abs(chg) > 7:
                    print(f'{date.iloc[i]} {code} 跳空 {chg:.2f}%')
                    i += self.window + 1
                    continue
                barenc = []
                if date.iloc[i].date() != cur_date:
                    cur_date = date.iloc[i].date()
                    atr = self.atr[code][i]/self.scale
                barenc.append(symbol)
                # 日期 to timestamp
                barenc.append(int(date.iloc[i].timestamp()))
                # 涨跌
                barenc.append(int((close.iloc[i] - close.iloc[i-1])/atr))
                # 实体
                barenc.append(int((close.iloc[i] - open.iloc[i])/atr))

                if close.iloc[i] > open.iloc[i]: # 阳线
                    # 上影线
                    barenc.append(int((high.iloc[i] - close.iloc[i])/atr))
                    # 下影线
                    barenc.append(int((open.iloc[i] - low.iloc[i])/atr))
                else: # 阴线
                    # 上影线
                    barenc.append(int((high.iloc[i] - open.iloc[i])/atr))
                    # 下影线
                    barenc.append(int((close.iloc[i] - low.iloc[i])/atr))

                bar_data.append(barenc)
        return bar_data


if __name__ == '__main__':
    hd = HistoryDataToBar('d:/RoboQuant2', 'fut', 'day')
    hd.load_data()
    bar_data = hd.encode_to_bar()
    hd.save_data(bar_data)

