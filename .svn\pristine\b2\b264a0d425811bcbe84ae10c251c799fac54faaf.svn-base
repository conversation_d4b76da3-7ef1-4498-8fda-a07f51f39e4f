# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
"""
Handler对原始数据做处理，按需提供给模型
"""
# coding=utf-8
import abc
import bisect
import logging
import warnings
from inspect import getfullargspec
from typing import Callable, Dict, Union, Tuple, List, Iterator, Optional

import pandas as pd
import numpy as np
import json
from copy import deepcopy
from sklearn.model_selection import KFold
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

from qlib.log import get_module_logger, TimeInspector
# from qlib.data import D
# from qlib.config import C
# from qlib.utils import parse_config, transform_end_date, init_instance_by_config
# from qlib.utils.serial import Serializable
# from qlib.utils import fetch_df_by_index, fetch_df_by_col
# from qlib.utils import lazy_sort_index
from pathlib import Path
from qlib.data.dataset.loader import DataLoader
from qlib.data.dataset.handler import DataHandler
from .loader import AFDataLoader, AHFDataLoader
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES

# from . import processor as processor_module
from . import loader as data_loader_module


class DataHandlerAF(DataHandler):
    """
    加载raw data进行加工处理的地方
    """
    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader: Union[dict, str, DataLoader]=None,
        valid_fold = 5,
        is_normal = True,
        init_data=True,
        fetch_orig=True
    ):
        self._valid_fold = valid_fold
        self._is_normal = is_normal
        self.train_ft_df = pd.DataFrame()
        self.train_lb_df = pd.DataFrame()
        self.valid_ft_df = pd.DataFrame()
        self.valid_lb_df = pd.DataFrame()

        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        self.logger = get_module_logger("DataHandlerAF")

        super().__init__(
            instruments=instruments,
            start_time=start_time,
            end_time=end_time,
            data_loader=data_loader,
        )

    def config(self, **kwargs):
        if "data_loader" in kwargs and isinstance(self.data_loader, AFDataLoader):
            self.data_loader.config(**kwargs["data_loader"]["kwargs"])
            kwargs.pop("data_loader")
        super().config(**kwargs)

    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AFDataLoader):
            return


        with TimeInspector.logt("Loading data"):
            # 加载原始数据
            lb_df, lf_df, sf_df, ct_df = self.data_loader.load(self.instruments, self.start_time, self.end_time)
            self.logger.info(f"\n============================\n\nlb{lb_df.shape} sf{sf_df.shape} lf{lf_df.shape} ct{ct_df.shape}\n\n============================")

            # todo： 当前未做col_sel选择，直接合并特征数据
            ft_df = pd.merge(lf_df, sf_df, how='inner', left_index=True, right_index=True)
            ft_df = pd.merge(ft_df, ct_df, how='inner', left_index=True, right_index=True)

            # self.codes = sorted(lb_df.CODE.unique().tolist())
            # self.ft_lens["lf_len"] = lf_df.shape[1]
            # self.ft_lens["sf_len"] = sf_df.shape[1]
            # self.ft_lens["ct_len"] = ct_df.shape[1]
            self.ft_mean = ft_df.mean()
            self.ft_std = ft_df.std()

            # 划分测试集与验证集
            cv = KFold(self._valid_fold, shuffle=True, random_state=1)
            train_ids, valid_ids = next(cv.split(lb_df.index))
            self.train_lb_df = lb_df.iloc[train_ids,:]
            self.valid_lb_df = lb_df.iloc[valid_ids,:]
            # print(self.train_lb_df, self.valid_lb_df)

            self.train_ft_df = ft_df[ft_df.index.isin(self.train_lb_df.index)]
            self.train_lb_df.sort_index(inplace=True)
            self.train_ft_df.sort_index(inplace=True)

            self.valid_ft_df = ft_df[ft_df.index.isin(self.valid_lb_df.index)]
            self.valid_lb_df.sort_index(inplace=True)
            self.valid_ft_df.sort_index(inplace=True)

            assert len(self.train_lb_df) == len(self.train_ft_df) and self.train_lb_df.index[-1] == self.train_ft_df.index[-1],\
                f'dataset processing error: {len(self.train_lb_df)} == {len(self.train_ft_df)} and {self.train_lb_df.index[-1]} == {self.train_ft_df.index[-1]}'
            assert len(self.valid_lb_df) == len(self.valid_ft_df) and self.valid_lb_df.index[-1] == self.valid_ft_df.index[-1],\
                f'dataset processing error: {len(self.valid_lb_df)} == {len(self.valid_ft_df)} and {self.valid_lb_df.index[-1]} == {self.valid_ft_df.index[-1]}'

            # 归一化
            # data_val = self.train_ft_df.values
            # self.train_ft_df = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
            # data_val = self.valid_ft_df.values
            # self.valid_ft_df = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
            if self._is_normal:
                self.train_ft_df = (self.train_ft_df - self.train_ft_df.mean()) / self.train_ft_df.std()
                self.valid_ft_df = (self.valid_ft_df - self.valid_ft_df.mean()) / self.valid_ft_df.std()
                self.train_ft_df.fillna(value=0.0, inplace=True)
                self.valid_ft_df.fillna(value=0.0, inplace=True)

            # self._dump_input_param_json(self.data_loader.direct, self.data_loader.model_name, self.data_loader.model_path)

        # super().setup_data(enable_cache=enable_cache)
        self.logger.info(f"train shape{self.train_ft_df.shape} valid shape{self.valid_ft_df.shape}")

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set: Union[str, List[str]] = DataHandler.CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None
    ) -> pd.DataFrame:
        """
        
        Returns
        -------
        pd.DataFrame
        """

        if isinstance(selector, str):
            if selector == "train":
                if col_set == DataHandler.CS_ALL:
                    return self.train_ft_df, self.train_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]], "encoded": self.train_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            elif selector == "valid":
                if col_set == DataHandler.CS_ALL:
                    return self.valid_ft_df, self.valid_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]], "encoded": self.valid_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            else:
                raise NotImplementedError(f"{selector} type not supported.")            
        else:
            raise NotImplementedError(f"{selector} type not supported.")


class DataHandlerAHF(DataHandler):
    """
    加载raw data进行加工处理的地方
    """
    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader: Union[dict, str, DataLoader]=None,
        valid_fold = 5,
        win = 5,
        step = 1,
        is_normal = True,
        init_data=False,
        fetch_orig=True,
        **kwargs
    ):
        self.win = win
        self.step = step
        self._valid_fold = valid_fold
        self._is_normal = is_normal
        self.x_data = []
        self.y_data = []
        self.encoded_data = []
        self.direct = 'long'
        self.model_name = ''
        self.model_name_suff = ''
        self.model_path = ''
        self.data_path = ''
        self.sel_lf_names=''
        self.sel_sf_names=''
        self.sel_ct_names=''
        self.le = LabelEncoder()

        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        self.logger = get_module_logger("DataHandlerAHF")
        super().__init__(
            instruments=instruments,
            start_time=start_time,
            end_time=end_time,
            data_loader=data_loader,
            init_data=init_data,
            fetch_orig=fetch_orig,
        )

    def config(self, **kwargs):
        if "data_loader" in kwargs and isinstance(self.data_loader, AHFDataLoader):
            #self.data_loader.config(**kwargs["data_loader"]["kwargs"])
            kwargs2 = deepcopy(kwargs)
            kwargs2.pop("data_loader")
        attr_list = {"win", "step", "direct", "model_name", "model_name_suff", "model_path", 
                     "sel_lf_names", "sel_sf_names", "sel_ct_names"}
        for k, v in kwargs2["kwargs"].items():
            if k in attr_list:
                setattr(self, k, v)
        kwargs2.pop("kwargs")
        super().config(**kwargs2)

    def _long_factor_select(self, n):
        if len(self.sel_lf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_lf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _short_factor_select(self, n):
        if len(self.sel_sf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_sf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _context_select(self, n):
        if len(self.sel_ct_names) == 0:
            return 0
        if SNAPSHOT_CONTEXT[n] in self.sel_ct_names:
            return 1
        return 0


    def _get_factor_cols(self, factor_type="lf"):
        """
        因子列名称
        """
        col_names = []
        if factor_type == "lf":
            for name in self.sel_lf_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "sf":
            for name in self.sel_sf_names: # SEL_SHORT_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "ct":
            col_names.extend(self.sel_ct_names)

        return col_names
    
    def _dump_input_param_json(self, ft_df):
        """
        """
        #if self.model_name_suff == "":
        #    return
        f_sel = {}
        f_sel['codes'] = self.le.classes_.tolist()
        f_sel['slow'] = [self._long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [self._short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [self._context_select(n) for n in range(len(SNAPSHOT_CONTEXT))]
        # f_sel['codes'] = sorted(MAIN_FUT_CODES)# sorted(lb_df.code.unique().tolist())

        f_sel['mean'] = (ft_df.mean(axis=0).tolist())
        f_sel['std'] = (ft_df.std(axis=0).tolist())

        f_sel['lf_len'] = sum(f_sel['slow'])
        f_sel['sf_len'] = sum(f_sel['fast'])
        f_sel['ct_len'] = sum(f_sel['context'])
    
        #with open(f'{self.data_path}/using_factor.json', 'r') as using_file:
        #    using_factor = json.load(using_file)
        #    f_sel.update(using_factor)
        #f_sel.update(self.interface_params)
        f_sel["input_dim"] = 2
        f_sel["code_encoding"] = 2
        f_sel["win"] = self.win
        if self.model_name_suff != "":
            jfile_name = f"{self.model_name}_{self.model_name_suff}_{self.direct}"
        else:
            jfile_name = f"{self.model_name}_{self.direct}"
        with open(f'{self.model_path}/{jfile_name}.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)

       
    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AHFDataLoader):
            return


        with TimeInspector.logt("Loading data"):
            # 加载原始数据
            lf_df, sf_df, ct_df = self.data_loader.load(self.instruments, self.start_time, self.end_time)

            if not sf_df.empty:

                sf_df['code_encoded'] = self.le.fit_transform(sf_df['code'].values)

                if self.direct == 'long':
                    sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)
                else:
                    sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)
                
                col_names = self._get_factor_cols(factor_type="lf")
                if len(col_names) > 0:
                    lf_df = lf_df[col_names]
                    # lf_df = lf_df.astype(np.float32)
                else:
                    lf_df = pd.DataFrame()

                col_names = self._get_factor_cols(factor_type="sf")
                col_names += ['code', 'date', 'change', 'code_encoded', 'label']
                if len(col_names) > 0:
                    sf_df = sf_df[col_names]
                    # sf_df = sf_df.astype(np.float32)
                else:
                    sf_df = pd.DataFrame()

                col_names = self._get_factor_cols(factor_type="ct")
                if len(col_names) > 0:
                    ct_df = ct_df[col_names]
                    ct_df = ct_df.astype(np.float32)
                else:
                    ct_df = pd.DataFrame()

                ft_df = pd.concat([lf_df, sf_df, ct_df], axis=1)
                self.logger.info(f"\n===============\n\nlf{lf_df.shape} sf{sf_df.shape} ct{ct_df.shape}\n\n================\n")
                # 合并后清除数据
                ft_df.dropna(axis=0, how='any', inplace=True)
                if 'RSI_2' in ft_df.columns:
                    ft_df = ft_df[ft_df['RSI_2'] != 0.0]
                if 'FAST_QH_NATR_ZSCORE' in ft_df.columns:
                    ft_df = ft_df[ft_df['FAST_QH_NATR_ZSCORE'] != 0.0]

                lb_df = ft_df[['code', 'date', 'change', 'code_encoded', 'label']]
                ft_df.drop(['code', 'date', 'change', 'code_encoded', 'label'], axis=1, inplace=True)
                ft_df = ft_df.astype(np.float32)

                self.logger.info(f"\n===============\n\nlb{lb_df.shape} ft{ft_df.shape}\n\n================\n")
                print(ft_df)
                print(lb_df)
                print(lb_df['label'].value_counts())

                self._dump_input_param_json(ft_df)

                data1 = ft_df.values
                data2 = lb_df.values[:, -1]
                data3 = lb_df.values[:, -2]
                # 归一化
                # data_val = data1.values
                # data1 = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
                if self._is_normal:
                    data1 = (data1 - np.mean(data1)) / np.std(data1)
                    #data1.fillna(value=0.0, inplace=True)
                    mask = np.isnan(data1)
                    data1[mask] = 0.0

                # 防止跨code取值
                for i in range(0, len(data1) - self.win, self.step):
                    if data3[i] != data3[i + self.win]:
                        continue
                    self.x_data.append(data1[i:i + self.win])
                    self.y_data.append(data2[i + self.win])
                    self.encoded_data.append(data3[i + self.win])
                # self.x_data = [data1[i:i + self.win] for i in range(0, len(data1) - self.win, self.step)]
                # self.y_data = [data2[i + self.win] for i in range(0, len(data2) - self.win, self.step)]                
                # self.encoded_data = [data3[i + self.win] for i in range(0, len(data3) - self.win, self.step)]                
            # self._dump_input_param_json(self.data_loader.direct, self.data_loader.model_name, self.data_loader.model_path)
        # super().setup_data(enable_cache=enable_cache)
        # self.logger.info(f"x_data shape{self.x_df.shape} valid shape{self.valid_ft_df.shape}")


    def fetch2(self):
        return self.x_data, self.y_data, self.encoded_data

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set: Union[str, List[str]] = DataHandler.CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None
    ) :
        """
        
        Returns
        -------
        pd.DataFrame
        """
        if isinstance(selector, str):
            if selector == "train":
                if col_set == DataHandler.CS_ALL:
                    return self.train_ft_df, self.train_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]], "encoded": self.train_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            elif selector == "valid":
                if col_set == DataHandler.CS_ALL:
                    return self.valid_ft_df, self.valid_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]], "encoded": self.valid_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            else:
                raise NotImplementedError(f"{selector} type not supported.")            
        else:
            raise NotImplementedError(f"{selector} type not supported.")
