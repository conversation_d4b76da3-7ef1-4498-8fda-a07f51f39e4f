{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## OHCL w/ Volume"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3e5094b040c64b7bbab10f8ab4e49c56", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=100000, description='t', max=1000000, min=1), FloatSlider(value=0.61, de…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import ipywidgets as widgets\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from math import pi\n", "from stochastic.continuous import FractionalBrownianMotion\n", "from stochastic.noise import GaussianNoise\n", "from bokeh.plotting import figure, show, output_notebook\n", "from bokeh.layouts import column\n", "\n", "\n", "\n", "def show_plot(t, hurst, freq='1h'):\n", "    price_fbm = FractionalBrownianMotion(t=t, hurst=hurst)\n", "    volume_gen = GaussianNoise(t=t*t)\n", "\n", "    start_date = pd.to_datetime('2010-01-01', format='%Y-%m-%d')\n", "\n", "    price_volatility = price_fbm.sample(t, zero=False)\n", "    prices = price_volatility + 10000\n", "    volume_volatility = volume_gen.sample(t) * np.random.lognormal(5, 1.5, t)\n", "    volumes = volume_volatility * (10 * price_volatility) + 1\n", "\n", "    price_frame = pd.DataFrame([], columns=['date', 'price'], dtype=float)\n", "    volume_frame = pd.DataFrame(\n", "        [], columns=['date', 'volume'], dtype=float)\n", "\n", "    price_frame['date'] = pd.date_range(start=start_date, periods=t, freq=\"1min\")\n", "    price_frame['price'] = abs(prices)\n", "\n", "    volume_frame['date'] = price_frame['date'].copy()\n", "    volume_frame['volume'] = abs(volumes)\n", "\n", "    price_frame.set_index('date')\n", "    price_frame.index = pd.to_datetime(price_frame.index, unit='m', origin=start_date)\n", "\n", "    volume_frame.set_index('date')\n", "    volume_frame.index = pd.to_datetime(volume_frame.index, unit='m', origin=start_date)\n", "\n", "    ohlc = price_frame['price'].resample(freq).ohlc()\n", "    volumes = volume_frame['volume'].resample(freq).sum() / 1e7\n", "    \n", "    TOOLS = \"xpan,wheel_zoom,box_zoom,crosshair,reset,save\"\n", "    \n", "    price_plt = figure(title=\"Simulated Price\",\n", "                       y_axis_label='Price ($)',\n", "                       x_axis_type='datetime',\n", "                       tools=TOOLS)\n", "    price_plt.xaxis.major_label_orientation = pi/4\n", "    price_plt.grid.grid_line_alpha=0.3\n", "    \n", "    width = 60*1000*10\n", "    inc = ohlc.close > ohlc.open\n", "    dec = ohlc.open > ohlc.close\n", "    \n", "    price_plt.segment(ohlc.index, ohlc.high, ohlc.index, ohlc.low, color=\"black\")\n", "    price_plt.vbar(ohlc.index[inc], width, ohlc.open[inc], ohlc.close[inc], fill_color=\"#9BDE39\", line_color=\"black\")\n", "    price_plt.vbar(ohlc.index[dec], width, ohlc.open[dec], ohlc.close[dec], fill_color=\"#F2583E\", line_color=\"black\")\n", "\n", "    volume_plt = figure(title=\"Simulated Volume\",\n", "                        y_axis_label=\"Volume\",\n", "                        x_axis_label=\"Time\",\n", "                        x_axis_type=\"datetime\",\n", "                        x_range=price_plt.x_range,\n", "                        plot_height=250,\n", "                        tools=TOOLS)\n", "    \n", "    volume_plt.vbar(ohlc.index, bottom=0, top=volumes, width=width, color=\"#72B5C8\", alpha=0.3)\n", "    \n", "    output_notebook()\n", "\n", "    show(column(price_plt, volume_plt))\n", "\n", "widgets.interact(show_plot,\n", "                 t=widgets.IntSlider(min=1, max=1000000, value=100000),\n", "                 hurst=widgets.FloatSlider(min=0.5, max=0.8, step=0.01, value=0.61));"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## FBMExchange OHCLV Example"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "DLL load failed: 找不到指定的模块。", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[1;32m<ipython-input-2-01aa53c27f7e>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[0mwarnings\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mfilterwarnings\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'ignore'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 11\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mtensortrade\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mexchanges\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msimulated\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mFBMExchange\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     12\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     13\u001b[0m \u001b[0mexchange\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mFBMExchange\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mrewards\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mslippage\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 7\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mstrategies\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      8\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtrades\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\strategies\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtrading_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mstable_baselines_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mStableBaselinesTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtensorforce_trading_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTensorforceTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\strategies\\stable_baselines_strategy.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     23\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtyping\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mUnion\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mCallable\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mList\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mDict\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 25\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvec_env\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDummyVecEnv\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     26\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpolicies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mBasePolicy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbase_class\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mBaseRLModel\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[1;32mif\u001b[0m \u001b[0mmpi4py\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 17\u001b[1;33m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDDPG\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     18\u001b[0m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgail\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mGAIL\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     19\u001b[0m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mppo1\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mPPO1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\ddpg\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnoise\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mAdaptiveParamNoiseSpec\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mNormalActionNoise\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mOrnsteinUhlenbeckActionNoise\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDDPG\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpolicies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMlpPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mCnnPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLnMlpPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLnCnnPolicy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\ddpg\\ddpg.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtf\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcontrib\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtc\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 12\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mmpi4py\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMPI\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     13\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     14\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mlogger\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mImportError\u001b[0m: DLL load failed: 找不到指定的模块。"]}], "source": ["import sys\n", "import os\n", "import time\n", "import warnings\n", "import numpy as np\n", "\n", "sys.path.append(os.path.dirname(os.path.abspath('')))\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "from tensortrade.exchanges.simulated import FBMExchange\n", "\n", "exchange = FBMExchange()\n", "\n", "exchange.reset()\n", "\n", "TOOLS = \"xpan,wheel_zoom,box_zoom,crosshair,reset,save\"\n", "\n", "price_plt = figure(title=\"Simulated Price\",\n", "                   y_axis_label='Price ($)',\n", "                   x_axis_type='datetime',\n", "                   tools=TOOLS)\n", "price_plt.xaxis.major_label_orientation = pi/4\n", "price_plt.grid.grid_line_alpha=0.3\n", "\n", "width = 60*1000*10\n", "inc = exchange.data_frame.close > exchange.data_frame.open\n", "dec = exchange.data_frame.open > exchange.data_frame.close\n", "\n", "price_plt.segment(exchange.data_frame.index, exchange.data_frame.high, exchange.data_frame.index, exchange.data_frame.low, color=\"black\")\n", "price_plt.vbar(exchange.data_frame.index[inc], width, exchange.data_frame.open[inc], exchange.data_frame.close[inc], fill_color=\"#9BDE39\", line_color=\"black\")\n", "price_plt.vbar(exchange.data_frame.index[dec], width, exchange.data_frame.open[dec], exchange.data_frame.close[dec], fill_color=\"#F2583E\", line_color=\"black\")\n", "\n", "volume_plt = figure(title=\"Simulated Volume\",\n", "                    y_axis_label=\"Volume\",\n", "                    x_axis_label=\"Time\",\n", "                    x_axis_type=\"datetime\",\n", "                    x_range=price_plt.x_range,\n", "                    plot_height=250,\n", "                    tools=TOOLS)\n", "\n", "volume_plt.vbar(exchange.data_frame.index, bottom=0, top=exchange.data_frame['volume'], width=width, color=\"#72B5C8\", alpha=0.3)\n", "\n", "output_notebook()\n", "\n", "show(column(price_plt, volume_plt))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tick trade data and volume"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# TODO"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}