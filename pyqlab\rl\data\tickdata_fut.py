
import os
import random
# from tqdm.notebook import tqdm

import numpy as np
import pandas as pd
pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 500)
pd.set_option('display.width', 1000)

# import matplotlib.pyplot as plt
# import seaborn as sns

from pyqlab.rl.data.utils import calc_5min_tick_features

from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder

# import torch
# import torch.nn as nn
# from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler
# import torch.optim as optim
# from fastai.layers import SigmoidRange

# import datetime
import warnings
warnings.filterwarnings("ignore")

class PreprocessingPipeline:
    """
    生成df table: code, time_id
    """
    def __init__(self, label, data_path, split_percent=0.8):
        self.df = pd.DataFrame()
        self.df_train = pd.DataFrame()
        self.df_valid = pd.DataFrame()
        self.split_percent=split_percent
        self.LABEL = label
        self.data_path = data_path

    def _load_data(self):
        self.df = pd.read_parquet(f"{self.data_path}/tickdata_target.{self.LABEL}.parquet")
        # self.df = df[["label","time_id"]]
        # self.df.drop_duplicates(subset="time_id", inplace=True)
        
    def _label_encode(self):

        # Encoding stock_id for embeddings
        le = LabelEncoder()
        self.df['stock_id_encoded'] = le.fit_transform(self.df['stock_id'].values)
        # self.df_test['stock_id_encoded'] = le.transform(self.df_test['stock_id'].values)
    
           
    def transform(self):
        
        # self._label_encode()
        self._load_data()
        pos = int(len(self.df)*self.split_percent)
        self.df_train=self.df[0:pos]
        if self.split_percent < 1:
            self.df_valid=self.df[pos+1:len(self.df)]
        return self.df_train, self.df_valid


class TickFeatureDataset():

    def __init__(self, label, data_path, mode='train'):

        self.LABEL = label #"RB8888.SC"
        self.data_path = data_path
        self.mode = mode
        self.df = None

    def load(self, split_percent=0.8):
        if self.mode == 'train' or self.mode == 'test':
            ppp = PreprocessingPipeline(self.LABEL, self.data_path, split_percent)
            self.df_train, self.df_valid = ppp.transform()
            if self.mode =='train':
                self.df = self.df_train
            else:
                self.df = self.df_valid
        self.data_df = pd.read_parquet(f"{self.data_path}/tickdata.{self.LABEL}.parquet")
        self.data_df['datetime'].astype(int)
        self.data_df.set_index(["label", "time_id"], inplace=True)
        return self.len()

    def len(self):
        return len(self.df)

    def getitem(self, idx):

        """
        Get the idxth element in the dataset

        Parameters
        ----------
        idx (int): Index of the sample (0 <= idx < len(self.df))

        Returns
        -------
        """

        # sample = 
        stock_id = self.df.iloc[idx]['label']
        time_id = self.df.iloc[idx]['time_id']

        # Sequences from book data
        df = self.data_df.loc[(stock_id, time_id)].reset_index()
        return calc_5min_tick_features(df)

        """
        if len(df) < 300:
            df = self._completion(df)
        # df['datetime']=df['datetime'].apply(datetime.datetime.fromtimestamp)
        # print(df['datetime'])

        # Calculate Wap
        df['wap1'] = calc_wap1(df)
        df['wap2'] = calc_wap2(df)
        df['wap3'] = calc_wap3(df)
        df['wap4'] = calc_wap4(df)
        # Calculate log returns
        df['log_return1'] = df.groupby(['time_id'])['wap1'].apply(log_return)
        df['log_return2'] = df.groupby(['time_id'])['wap2'].apply(log_return)
        df['log_return3'] = df.groupby(['time_id'])['wap3'].apply(log_return)
        df['log_return4'] = df.groupby(['time_id'])['wap4'].apply(log_return)
        # Calculate wap balance
        df['wap_balance'] = abs(df['wap1'] - df['wap2'])
        # Calculate spread
        df['price_spread'] = (df['ask_price1'] - df['bid_price1']) / ((df['ask_price1'] + df['bid_price1']) / 2)
        df['price_spread2'] = (df['ask_price2'] - df['bid_price2']) / ((df['ask_price2'] + df['bid_price2']) / 2)
        df['bid_spread'] = df['bid_price1'] - df['bid_price2']
        df['ask_spread'] = df['ask_price1'] - df['ask_price2']
        df["bid_ask_spread"] = abs(df['bid_spread'] - df['ask_spread'])
        df['total_volume'] = (df['ask_size1'] + df['ask_size2']) + (df['bid_size1'] + df['bid_size2'])
        df['volume_imbalance'] = abs((df['ask_size1'] + df['ask_size2']) - (df['bid_size1'] + df['bid_size2']))
        
        # Dict for aggregations
        create_feature_dict = {
            'wap1': [np.sum, np.std],
            'wap2': [np.sum, np.std],
            'wap3': [np.sum, np.std],
            'wap4': [np.sum, np.std],
            'log_return1': [realized_volatility],
            'log_return2': [realized_volatility],
            'log_return3': [realized_volatility],
            'log_return4': [realized_volatility],
            'wap_balance': [np.sum, np.max],
            'price_spread':[np.sum, np.max],
            'price_spread2':[np.sum, np.max],
            'bid_spread':[np.sum, np.max],
            'ask_spread':[np.sum, np.max],
            'total_volume':[np.sum, np.max],
            'volume_imbalance':[np.sum, np.max],
            "bid_ask_spread":[np.sum,  np.max],
        }
        create_feature_dict_time = {
            'log_return1': [realized_volatility],
            'log_return2': [realized_volatility],
            'log_return3': [realized_volatility],
            'log_return4': [realized_volatility],
        }
        
        # Function to get group stats for different windows (seconds in bucket)
        def get_stats_window(fe_dict,seconds_in_bucket, add_suffix = False):
            # Group by the window
            df_feature = df[df['datetime'] >= seconds_in_bucket].groupby(['time_id']).agg(fe_dict).reset_index()
            # Rename columns joining suffix
            df_feature.columns = ['_'.join(col) for col in df_feature.columns]
            # Add a suffix to differentiate windows
            if add_suffix:
                df_feature = df_feature.add_suffix('_' + str(seconds_in_bucket))
            return df_feature
        
        # Get the stats for different windows
        df_feature = get_stats_window(create_feature_dict,seconds_in_bucket = 0, add_suffix = False)
        # df_feature_500 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 500, add_suffix = True)
        # df_feature_400 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 400, add_suffix = True)
        # df_feature_300 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 300, add_suffix = True)
        df_feature_200 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 200, add_suffix = True)
        df_feature_100 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 100, add_suffix = True)

        # Merge all
        # df_feature = df_feature.merge(df_feature_500, how = 'left', left_on = 'time_id_', right_on = 'time_id__500')
        # df_feature = df_feature.merge(df_feature_400, how = 'left', left_on = 'time_id_', right_on = 'time_id__400')
        # df_feature = df_feature.merge(df_feature_300, how = 'left', left_on = 'time_id_', right_on = 'time_id__300')
        df_feature = df_feature.merge(df_feature_200, how = 'left', left_on = 'time_id_', right_on = 'time_id__200')
        df_feature = df_feature.merge(df_feature_100, how = 'left', left_on = 'time_id_', right_on = 'time_id__100')
        # Drop unnecesary time_ids
        df_feature.drop(['time_id_', 'time_id__200','time_id__100'], axis = 1, inplace = True)
        
        
        # Create row_id so we can merge
        # stock_id = file_path.split('=')[1]
        # df_feature['row_id'] = df_feature['time_id_'].apply(lambda x: f'{stock_id}-{x}')
        # df_feature.drop(['time_id_'], axis = 1, inplace = True)
        return df_feature, df['price'][-1]
        """


def train_cnn1d():

    preprocessing_parameters = {
        'n_splits': 5,
        'shuffle': True,
        'random_state': 42,
        # 'only_trading_code': True,
        # 'data_path': 'e:/lab/RoboQuant/pylab/data',
        # 'portfolios': ['00200910081133001', '00171106132928000', '00170623114649000'],
        # 'interface_params': {
        #     'input_dim': 1, # 1: expression call 2: API call
        #     'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
        # }
    }

    ppp = PreprocessingPipeline(**preprocessing_parameters)

    directs = ['long', 'short']
    for direct in directs:
        print(f'\n{"*" * 30}\n{direct}\n{"*" * 30}')
        df_train, df_valid = ppp.transform()


