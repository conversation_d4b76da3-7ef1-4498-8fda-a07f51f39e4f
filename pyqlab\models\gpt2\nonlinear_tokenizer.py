"""
Nonlinear Candlestick Tokenizer

使用非线性映射方法将K线数据离散化为token，以便用于LLM模型
"""

import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import os
import pickle
from typing import List, Dict, Tuple, Optional, Union, Any, Callable
from datetime import datetime
from scipy import stats

from pyqlab.models.base.candlestick_tokenizer import CandlestickTokenizer
import talib

class NonlinearMappingFunction:
    """非线性映射函数基类"""

    def __init__(self, input_range: Tuple[float, float], output_range: Tuple[int, int]):
        """
        初始化非线性映射函数

        Args:
            input_range: 输入值范围 (min, max)
            output_range: 输出值范围 (min, max)，通常是离散整数范围
        """
        self.input_min, self.input_max = input_range
        self.output_min, self.output_max = output_range

    def __call__(self, x: float) -> int:
        """
        将输入值映射到输出范围

        Args:
            x: 输入值

        Returns:
            映射后的整数值
        """
        raise NotImplementedError("子类必须实现此方法")

    def inverse(self, y: int) -> float:
        """
        反向映射，将输出值映射回输入范围

        Args:
            y: 输出值

        Returns:
            映射回的输入值
        """
        raise NotImplementedError("子类必须实现此方法")

    def visualize(self, num_points: int = 100):
        """
        可视化映射函数

        Args:
            num_points: 可视化点的数量
        """
        x = np.linspace(self.input_min, self.input_max, num_points)
        y = np.array([self.__call__(xi) for xi in x])

        plt.figure(figsize=(10, 6))
        plt.plot(x, y)
        plt.xlabel('输入值')
        plt.ylabel('映射后的值')
        plt.title(f'{self.__class__.__name__} 映射函数')
        plt.grid(True)
        plt.show()


class LinearMapping(NonlinearMappingFunction):
    """线性映射函数（作为基准比较）"""

    def __call__(self, x: float) -> int:
        """线性映射"""
        # 限制输入范围
        x = max(self.input_min, min(x, self.input_max))

        # 线性映射
        normalized = (x - self.input_min) / (self.input_max - self.input_min)
        mapped = self.output_min + normalized * (self.output_max - self.output_min)

        # 四舍五入到最接近的整数
        return int(round(mapped))

    def inverse(self, y: int) -> float:
        """线性反向映射"""
        # 限制输出范围
        y = max(self.output_min, min(y, self.output_max))

        # 反向映射
        normalized = (y - self.output_min) / (self.output_max - self.output_min)
        return self.input_min + normalized * (self.input_max - self.input_min)


class LogarithmicMapping(NonlinearMappingFunction):
    """对数映射函数，在小值区域提供更精细的分辨率"""

    def __init__(self, input_range: Tuple[float, float], output_range: Tuple[int, int], base: float = 10.0):
        """
        初始化对数映射函数

        Args:
            input_range: 输入值范围 (min, max)，必须为正数
            output_range: 输出值范围 (min, max)
            base: 对数的底数
        """
        super().__init__(input_range, output_range)
        self.base = base

        # 确保输入范围为正数
        if self.input_min <= 0:
            raise ValueError("对数映射的输入范围必须为正数")

    def __call__(self, x: float) -> int:
        """对数映射"""
        # 限制输入范围
        x = max(self.input_min, min(x, self.input_max))

        # 对数映射
        log_min = np.log(self.input_min) / np.log(self.base)
        log_max = np.log(self.input_max) / np.log(self.base)
        log_x = np.log(x) / np.log(self.base)

        normalized = (log_x - log_min) / (log_max - log_min)
        mapped = self.output_min + normalized * (self.output_max - self.output_min)

        # 四舍五入到最接近的整数
        return int(round(mapped))

    def inverse(self, y: int) -> float:
        """对数反向映射"""
        # 限制输出范围
        y = max(self.output_min, min(y, self.output_max))

        # 反向映射
        normalized = (y - self.output_min) / (self.output_max - self.output_min)

        # 确保normalized在有效范围内
        normalized = max(0, min(normalized, 1))

        log_min = np.log(max(self.input_min, 1e-10)) / np.log(self.base)  # 避免对0或负数取对数
        log_max = np.log(max(self.input_max, 1e-9)) / np.log(self.base)
        log_x = log_min + normalized * (log_max - log_min)

        # 确保结果在输入范围内
        result = self.base ** log_x
        return max(self.input_min, min(result, self.input_max))


class ExponentialMapping(NonlinearMappingFunction):
    """指数映射函数，在大值区域提供更精细的分辨率"""

    def __init__(self, input_range: Tuple[float, float], output_range: Tuple[int, int], exponent: float = 2.0):
        """
        初始化指数映射函数

        Args:
            input_range: 输入值范围 (min, max)
            output_range: 输出值范围 (min, max)
            exponent: 指数
        """
        super().__init__(input_range, output_range)
        self.exponent = exponent

    def __call__(self, x: float) -> int:
        """指数映射"""
        # 限制输入范围
        x = max(self.input_min, min(x, self.input_max))

        # 指数映射
        normalized = (x - self.input_min) / (self.input_max - self.input_min)
        normalized_exp = normalized ** self.exponent
        mapped = self.output_min + normalized_exp * (self.output_max - self.output_min)

        # 四舍五入到最接近的整数
        return int(round(mapped))

    def inverse(self, y: int) -> float:
        """指数反向映射"""
        # 限制输出范围
        y = max(self.output_min, min(y, self.output_max))

        # 反向映射
        normalized = (y - self.output_min) / (self.output_max - self.output_min)

        # 确保normalized在有效范围内
        normalized = max(0, min(normalized, 1))

        # 计算根
        normalized_root = normalized ** (1 / self.exponent)

        # 确保结果在输入范围内
        result = self.input_min + normalized_root * (self.input_max - self.input_min)
        return max(self.input_min, min(result, self.input_max))


class SquareRootMapping(NonlinearMappingFunction):
    """平方根映射函数，在小值区域提供更精细的分辨率"""

    def __init__(self, input_range: Tuple[float, float], output_range: Tuple[int, int]):
        """
        初始化平方根映射函数

        Args:
            input_range: 输入值范围 (min, max)，必须为非负数
            output_range: 输出值范围 (min, max)
        """
        super().__init__(input_range, output_range)

        # 确保输入范围为非负数
        if self.input_min < 0:
            raise ValueError("平方根映射的输入范围必须为非负数")

    def __call__(self, x: float) -> int:
        """平方根映射"""
        # 限制输入范围
        x = max(self.input_min, min(x, self.input_max))

        # 平方根映射
        normalized = (x - self.input_min) / (self.input_max - self.input_min)
        normalized_sqrt = np.sqrt(normalized)
        mapped = self.output_min + normalized_sqrt * (self.output_max - self.output_min)

        # 四舍五入到最接近的整数
        return int(round(mapped))

    def inverse(self, y: int) -> float:
        """平方根反向映射"""
        # 限制输出范围
        y = max(self.output_min, min(y, self.output_max))

        # 反向映射
        normalized = (y - self.output_min) / (self.output_max - self.output_min)

        # 确保normalized在有效范围内
        normalized = max(0, min(normalized, 1))

        # 计算平方
        normalized_squared = normalized ** 2

        # 确保结果在输入范围内
        result = self.input_min + normalized_squared * (self.input_max - self.input_min)
        return max(self.input_min, min(result, self.input_max))


class SigmoidMapping(NonlinearMappingFunction):
    """S形映射函数，在中间区域提供更精细的分辨率"""

    def __init__(self, input_range: Tuple[float, float], output_range: Tuple[int, int], steepness: float = 1.0):
        """
        初始化S形映射函数

        Args:
            input_range: 输入值范围 (min, max)
            output_range: 输出值范围 (min, max)
            steepness: 陡度参数，控制S形曲线的陡峭程度
        """
        super().__init__(input_range, output_range)
        self.steepness = steepness

        # 计算中点
        self.mid_input = (self.input_min + self.input_max) / 2

    def __call__(self, x: float) -> int:
        """S形映射"""
        # 限制输入范围
        x = max(self.input_min, min(x, self.input_max))

        # 将输入值转换为[-6, 6]范围，以便应用sigmoid函数
        normalized = (x - self.input_min) / (self.input_max - self.input_min)
        sigmoid_input = (normalized * 12 - 6) * self.steepness

        # 应用sigmoid函数
        sigmoid_output = 1 / (1 + np.exp(-sigmoid_input))

        # 映射到输出范围
        mapped = self.output_min + sigmoid_output * (self.output_max - self.output_min)

        # 四舍五入到最接近的整数
        return int(round(mapped))

    def inverse(self, y: int) -> float:
        """S形反向映射"""
        # 限制输出范围
        y = max(self.output_min, min(y, self.output_max))

        # 反向映射
        normalized = (y - self.output_min) / (self.output_max - self.output_min)

        # 处理边界情况，避免除零错误
        if normalized <= 0:
            normalized = 1e-6  # 接近0的小值
        elif normalized >= 1:
            normalized = 1 - 1e-6  # 接近1的值

        # 反向应用sigmoid函数
        sigmoid_input = -np.log(1 / normalized - 1) / self.steepness

        # 转换回原始输入范围
        normalized_input = (sigmoid_input + 6) / 12

        # 确保结果在输入范围内
        normalized_input = max(0, min(normalized_input, 1))

        return self.input_min + normalized_input * (self.input_max - self.input_min)


class QuantileMapping(NonlinearMappingFunction):
    """
    分位数映射函数，基于数据分布进行映射
    根据数据的实际分布情况，将数据划分为等概率的区间
    """

    def __init__(self, data: np.ndarray, output_range: Tuple[int, int], n_quantiles: int = None):
        """
        初始化分位数映射函数

        Args:
            data: 用于计算分位数的数据
            output_range: 输出值范围 (min, max)
            n_quantiles: 分位数数量，如果为None则使用output_range的范围
        """
        self.data = np.sort(data)
        self.input_min, self.input_max = np.min(data), np.max(data)
        self.output_min, self.output_max = output_range

        # 如果没有指定分位数数量，则使用输出范围的大小
        if n_quantiles is None:
            n_quantiles = self.output_max - self.output_min + 1

        self.n_quantiles = n_quantiles

        # 计算分位数边界
        self.quantiles = np.linspace(0, 100, n_quantiles + 1)
        self.boundaries = np.percentile(data, self.quantiles)

    def __call__(self, x: float) -> int:
        """分位数映射"""
        # 限制输入范围
        x = max(self.input_min, min(x, self.input_max))

        # 找到x所在的分位数区间
        bin_idx = np.digitize(x, self.boundaries) - 1
        bin_idx = min(bin_idx, self.n_quantiles - 1)  # 确保不超出边界

        # 映射到输出范围
        mapped = self.output_min + bin_idx * (self.output_max - self.output_min) / (self.n_quantiles - 1)

        # 四舍五入到最接近的整数
        return int(round(mapped))

    def inverse(self, y: int) -> float:
        """分位数反向映射"""
        # 限制输出范围
        y = max(self.output_min, min(y, self.output_max))

        # 计算对应的分位数区间
        normalized = (y - self.output_min) / (self.output_max - self.output_min)

        # 确保normalized在有效范围内
        normalized = max(0, min(normalized, 1))

        # 计算bin索引，确保在有效范围内
        bin_idx = int(normalized * (self.n_quantiles - 1))
        bin_idx = max(0, min(bin_idx, self.n_quantiles - 2))  # 确保索引有效

        # 安全地获取边界值
        try:
            lower_bound = self.boundaries[bin_idx]
            upper_bound = self.boundaries[bin_idx + 1]

            # 返回该区间的中点值
            return (lower_bound + upper_bound) / 2
        except (IndexError, ValueError):
            # 如果出现任何错误，返回输入范围的中点
            return (self.input_min + self.input_max) / 2


class NonlinearCandlestickTokenizer(CandlestickTokenizer):
    """
    使用非线性映射方法的K线tokenizer
    """

    def __init__(self,
                 change_range: Tuple[int, int] = (-12, 12),
                 entity_range: Tuple[int, int] = (-12, 12),
                 shadow_range: Tuple[int, int] = (0, 7),
                 volume_range: Tuple[int, int] = (-9, 9),
                 atr_window: int = 100,
                 atr_mult: float = 0.88,
                 scale: int = 10,
                 special_tokens: bool = True,
                 include_volume: bool = False,
                 detect_anomalies_value: bool = False,
                 anomaly_threshold: float = 7.0,
                 mapping_functions: Dict[str, NonlinearMappingFunction] = None,
                 cache_dir: str = None):
        """
        初始化非线性K线tokenizer

        Args:
            change_range: change值的离散范围
            entity_range: entity值的离散范围
            shadow_range: 影线值的离散范围
            volume_range: 交易量变化值的离散范围
            atr_window: ATR计算窗口
            atr_mult: ATR乘数
            scale: 缩放因子
            special_tokens: 是否使用特殊token
            include_volume: 是否包含交易量特征
            detect_anomalies_value: 是否检测和处理异常值
            anomaly_threshold: 异常检测阈值
            mapping_functions: 特征到映射函数的字典，可用的特征有'change', 'entity', 'upline', 'downline', 'volume'
            cache_dir: 缓存目录
        """
        super().__init__(
            change_range=change_range,
            entity_range=entity_range,
            shadow_range=shadow_range,
            volume_range=volume_range,
            atr_window=atr_window,
            atr_mult=atr_mult,
            scale=scale,
            special_tokens=special_tokens,
            include_volume=include_volume,
            detect_anomalies_value=detect_anomalies_value,
            anomaly_threshold=anomaly_threshold,
            cache_dir=cache_dir
        )

        # 初始化映射函数
        self.mapping_functions = mapping_functions or {}

        # 打印映射函数信息
        for feature, mapping_func in self.mapping_functions.items():
            print(f"使用 {mapping_func.__class__.__name__} 映射 {feature} 特征")

    def _create_default_mapping_function(self, feature: str, data: np.ndarray) -> NonlinearMappingFunction:
        """
        为特定特征创建默认的映射函数

        Args:
            feature: 特征名称，如'change', 'entity', 'upline', 'downline', 'volume'
            data: 用于计算分布的数据

        Returns:
            映射函数
        """
        # 确定输入和输出范围
        if feature == 'change':
            output_range = self.change_range
        elif feature == 'entity':
            output_range = self.entity_range
        elif feature in ['upline', 'downline']:
            output_range = self.shadow_range
        elif feature == 'volume':
            output_range = self.volume_range
        else:
            raise ValueError(f"不支持的特征: {feature}")

        # 检查数据是否为空
        if len(data) == 0:
            # 如果数据为空，返回默认映射函数
            if feature == 'change':
                return LinearMapping((-1, 1), self.change_range)
            elif feature == 'entity':
                return LinearMapping((-1, 1), self.entity_range)
            elif feature in ['upline', 'downline']:
                return LinearMapping((0, 1), self.shadow_range)
            elif feature == 'volume':
                return LinearMapping((-1, 1), self.volume_range)
            else:
                return LinearMapping((-1, 1), (-1, 1))

        # 计算数据范围，排除异常值
        q1, q3 = np.percentile(data, [1, 99])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        # 限制数据范围
        filtered_data = data[(data >= lower_bound) & (data <= upper_bound)]

        # 如果数据太少，使用原始数据
        if len(filtered_data) < 10:
            filtered_data = data

        input_range = (np.min(filtered_data), np.max(filtered_data))

        # 根据数据分布选择合适的映射函数
        # 计算偏度
        skewness = stats.skew(filtered_data)

        if abs(skewness) < 0.5:
            # 数据接近正态分布，使用线性映射
            return LinearMapping(input_range, output_range)
        elif skewness > 0.5:
            # 数据右偏（有长尾），使用对数映射
            # 确保所有值为正
            if input_range[0] <= 0:
                # 如果有负值或零，进行偏移
                offset = abs(input_range[0]) + 1e-6
                adjusted_data = filtered_data + offset
                input_range = (np.min(adjusted_data), np.max(adjusted_data))

            return LogarithmicMapping(input_range, output_range)
        else:
            # 数据左偏，使用指数映射
            return ExponentialMapping(input_range, output_range)

    def tokenize(self, df: pd.DataFrame) -> List[int]:
        """
        使用非线性映射将K线数据转换为token序列

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            token索引列表
        """
        # 确保数据按时间排序
        if 'datetime' in df.columns:
            df = df.sort_values('datetime')

        # 检测并修正异常值
        if self.detect_anomalies_value:
            df = self.detect_anomalies(df)
            df = self.correct_anomalies(df, method='interpolate')

        # 计算ATR
        atr = self._calculate_atr(df)
        atr = atr.bfill()  # 填充开始的NaN值

        # 应用ATR乘数和缩放因子
        atr = atr * self.atr_mult / self.scale

        # 计算K线特征
        features = pd.DataFrame(index=df.index)

        # 计算原始特征值（未离散化）
        raw_features = {
            'change': (df['close'] - df['close'].shift(1)) / atr,
            'entity': (df['close'] - df['open']) / atr,
            'upline': (df['high'] - df[['open', 'close']].max(axis=1)) / atr,
            'downline': (df[['open', 'close']].min(axis=1) - df['low']) / atr
        }

        # 如果包含交易量特征
        if self.include_volume and 'volume' in df.columns:
            # 计算交易量变化
            volume_change = df['volume'].pct_change()
            # 计算交易量的标准差
            volume_std = volume_change.rolling(window=self.atr_window).std().bfill()
            # 标准化交易量变化
            raw_features['volume'] = volume_change / volume_std

        # 对每个特征应用映射函数
        for feature, raw_values in raw_features.items():
            # 获取或创建映射函数
            if feature in self.mapping_functions:
                mapping_func = self.mapping_functions[feature]
            else:
                # 创建默认映射函数
                values = raw_values.dropna().values
                if len(values) == 0:
                    # 如果没有有效值，使用默认映射函数
                    if feature == 'change':
                        mapping_func = LinearMapping((-1, 1), self.change_range)
                    elif feature == 'entity':
                        mapping_func = LinearMapping((-1, 1), self.entity_range)
                    elif feature in ['upline', 'downline']:
                        mapping_func = LinearMapping((0, 1), self.shadow_range)
                    elif feature == 'volume':
                        mapping_func = LinearMapping((-1, 1), self.volume_range)
                    else:
                        mapping_func = LinearMapping((-1, 1), (-1, 1))
                else:
                    mapping_func = self._create_default_mapping_function(feature, values)
                self.mapping_functions[feature] = mapping_func

            # 应用映射函数，处理NaN和无穷值
            features[feature] = raw_values.apply(lambda x: mapping_func(x) if not pd.isna(x) and not np.isinf(x) else 0)

        # 检测交易日间隔
        # 创建一个标记数组，用于记录特殊日期
        special_date_markers = np.zeros(len(features), dtype=object)

        if 'datetime' in df.columns:
            dt = pd.to_datetime(df['datetime'])
            # 计算日期差
            date_diff = dt.dt.date.diff().dt.days

            # 标记交易日间隔和假期
            for i in range(1, len(features)):
                if date_diff.iloc[i] > 3:  # 假期间隔
                    special_date_markers[i] = self.HOLIDAY_TOKEN
                elif date_diff.iloc[i] > 1:  # 交易日间隔
                    special_date_markers[i] = self.DAY_GAP_TOKEN

        # 标记异常值
        if self.detect_anomalies_value and 'is_anomaly' in df.columns:
            anomaly_indices = df.index[df['is_anomaly']]
            if len(anomaly_indices) > 0:
                for idx in anomaly_indices:
                    if idx in features.index:
                        # 将异常值标记为特殊token
                        idx_loc = features.index.get_loc(idx)
                        special_date_markers[idx_loc] = self.ANOMALY_TOKEN

        # 生成token字符串
        token_strs = []
        for i, (_, row) in enumerate(features.iterrows()):
            # 检查是否有特殊标记
            if special_date_markers[i]:
                # 使用特殊标记
                token_strs.append(special_date_markers[i])
            else:
                # 使用正常的特征值
                if self.include_volume and 'volume' in row:
                    token_str = f"{int(row['change'])}|{int(row['entity'])}|{int(row['upline'])}|{int(row['downline'])}|{int(row['volume'])}"
                else:
                    token_str = f"{int(row['change'])}|{int(row['entity'])}|{int(row['upline'])}|{int(row['downline'])}"
                token_strs.append(token_str)

        # 转换为token索引
        tokens = []
        # 删除添加 BOS_TOKEN 的代码，因为行情数据是连续的，不需要序列开始标记

        for token_str in token_strs:
            if token_str in self.token2idx:
                tokens.append(self.token2idx[token_str])
            else:
                tokens.append(self.token2idx['<UNK>'])

        # 删除添加 EOS_TOKEN 的代码，因为行情数据是连续的，不需要序列结束标记

        return tokens

    def visualize_mapping_functions(self):
        """可视化所有映射函数"""
        if not self.mapping_functions:
            print("没有定义映射函数")
            return

        # 计算需要的子图数量
        n_funcs = len(self.mapping_functions)
        n_cols = min(2, n_funcs)
        n_rows = (n_funcs + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(12, 4 * n_rows))

        # 确保axes是二维数组
        if n_funcs == 1:
            axes = np.array([[axes]])
        elif n_rows == 1:
            axes = axes.reshape(1, -1)

        # 绘制每个映射函数
        for i, (feature, mapping_func) in enumerate(self.mapping_functions.items()):
            row, col = i // n_cols, i % n_cols
            ax = axes[row, col]

            # 生成输入值
            x = np.linspace(mapping_func.input_min, mapping_func.input_max, 100)
            y = np.array([mapping_func(xi) for xi in x])

            # 绘制映射函数
            ax.plot(x, y)
            ax.set_title(f'{feature}: {mapping_func.__class__.__name__}')
            ax.set_xlabel('输入值')
            ax.set_ylabel('映射后的值')
            ax.grid(True)

        # 调整布局
        plt.tight_layout()
        plt.show()

    def tokens_to_candlesticks(self, tokens: List[int], start_price: float, atr: float,
                              start_volume: float = None) -> pd.DataFrame:
        """
        将token序列转换回K线数据，使用非线性映射函数的inverse方法提高还原保真度

        Args:
            tokens: token索引列表
            start_price: 起始价格
            atr: ATR值
            start_volume: 起始交易量（可选）

        Returns:
            包含OHLC数据的DataFrame，如果包含交易量特征，还会包含volume列
        """
        # 解码token
        features = self.decode(tokens)

        if not features:
            columns = ['open', 'high', 'low', 'close']
            if self.include_volume:
                columns.append('volume')
            return pd.DataFrame(columns=columns)

        # 创建DataFrame
        df = pd.DataFrame(features)

        # 打印解码后的特征统计信息
        print("\n解码后的特征统计:")
        print(f"数据形状: {df.shape}")
        print(df.head())

        # 打印change值的统计信息
        if 'change' in df.columns:
            print("\n解码后的change值统计:")
            print(f"平均值: {df['change'].mean()}")
            print(f"最小值: {df['change'].min()}")
            print(f"最大值: {df['change'].max()}")
            print(f"正值比例: {(df['change'] > 0).mean():.2%}")
            print(f"负值比例: {(df['change'] < 0).mean():.2%}")
            print(f"零值比例: {(df['change'] == 0).mean():.2%}")

        # 应用ATR乘数和缩放因子
        atr_scaled = atr * self.atr_mult / self.scale
        print(f"ATR缩放值: {atr_scaled}")

        # 创建OHLC数据
        ohlc = pd.DataFrame(index=range(len(df)))

        # 计算收盘价 - 使用非线性映射的inverse方法
        prices = [start_price]
        for i in range(len(df)):
            change_value = df.iloc[i]['change']

            # 打印当前处理的值
            if i < 5 or i >= len(df) - 5:  # 只打印前5个和后5个，避免输出过多
                print(f"处理第{i}个K线: change={change_value}, 当前价格={prices[-1]}")

            # 处理特殊标记和边界值
            if change_value in [-77, -88, -99] or change_value <= self.change_range[0] or change_value >= self.change_range[1]:
                # 对于特殊标记或边界值，使用前一个价格
                if change_value <= self.change_range[0] or change_value >= self.change_range[1]:
                    print(f"警告: change值{change_value}在边界或超出范围{self.change_range}，使用前一个价格")
                curr_price = prices[-1]
            else:
                try:
                    # 使用映射函数的inverse方法计算原始变化值
                    if 'change' in self.mapping_functions:
                        # 将离散的change值映射回连续值
                        raw_change = self.mapping_functions['change'].inverse(change_value)
                        # 计算价格变动
                        price_change = raw_change * atr_scaled
                    else:
                        # 如果没有映射函数，使用线性计算
                        price_change = change_value * atr_scaled

                    # 计算当前价格
                    curr_price = prices[-1] + price_change

                    # 检查计算结果
                    if not np.isfinite(curr_price) or curr_price <= 0:
                        print(f"警告: 计算的价格无效 ({curr_price})，使用前一个价格")
                        curr_price = prices[-1]

                    if i < 5 or i >= len(df) - 5:
                        print(f"  价格变化: {price_change}, 新价格: {curr_price}")
                except Exception as e:
                    print(f"计算价格时出错: {str(e)}，使用前一个价格")
                    curr_price = prices[-1]

            prices.append(curr_price)

        # 移除第一个价格（它只是起始价格）
        prices = prices[1:]

        # 检查价格列表
        print(f"\n价格列表检查:")
        print(f"价格数量: {len(prices)}")
        print(f"前5个价格: {prices[:5]}")
        print(f"后5个价格: {prices[-5:] if len(prices) >= 5 else prices}")

        # 检查是否有无效价格
        invalid_prices = [p for p in prices if not np.isfinite(p) or p <= 0]
        if invalid_prices:
            print(f"警告: 发现{len(invalid_prices)}个无效价格，将进行修复")
            # 替换无效价格
            for i in range(len(prices)):
                if not np.isfinite(prices[i]) or prices[i] <= 0:
                    # 使用前一个有效价格或起始价格
                    valid_prices = [p for p in prices[:i] if np.isfinite(p) and p > 0]
                    prices[i] = valid_prices[-1] if valid_prices else start_price

        ohlc['close'] = prices

        # 计算开盘价 - 使用非线性映射的inverse方法
        for i in range(len(df)):
            try:
                entity_value = df.iloc[i]['entity']

                # 使用映射函数的inverse方法计算原始实体值
                if 'entity' in self.mapping_functions:
                    raw_entity = self.mapping_functions['entity'].inverse(entity_value)
                    entity_change = raw_entity * atr_scaled
                else:
                    entity_change = entity_value * atr_scaled

                # 计算开盘价
                open_price = ohlc.loc[i, 'close'] - entity_change

                # 确保开盘价为正
                if not np.isfinite(open_price) or open_price <= 0:
                    # 如果开盘价无效，使用收盘价的一个小偏移
                    open_price = ohlc.loc[i, 'close'] * 0.99

                ohlc.loc[i, 'open'] = open_price
            except Exception as e:
                print(f"计算第{i}个K线开盘价时出错: {str(e)}，使用收盘价的偏移值")
                ohlc.loc[i, 'open'] = ohlc.loc[i, 'close'] * 0.99

        # 计算最高价和最低价 - 使用非线性映射的inverse方法
        for i in range(len(df)):
            try:
                upline_value = df.iloc[i]['upline']
                downline_value = df.iloc[i]['downline']

                # 使用映射函数的inverse方法计算原始影线值
                if 'upline' in self.mapping_functions:
                    raw_upline = self.mapping_functions['upline'].inverse(upline_value)
                    upline_change = raw_upline * atr_scaled
                else:
                    upline_change = upline_value * atr_scaled

                if 'downline' in self.mapping_functions:
                    raw_downline = self.mapping_functions['downline'].inverse(downline_value)
                    downline_change = raw_downline * atr_scaled
                else:
                    downline_change = downline_value * atr_scaled

                # 计算最高价和最低价
                high_price = max(ohlc.loc[i, 'open'], ohlc.loc[i, 'close']) + upline_change
                low_price = min(ohlc.loc[i, 'open'], ohlc.loc[i, 'close']) - downline_change

                # 确保价格有效
                if not np.isfinite(high_price) or high_price <= 0:
                    high_price = max(ohlc.loc[i, 'open'], ohlc.loc[i, 'close']) * 1.01

                if not np.isfinite(low_price) or low_price <= 0:
                    low_price = min(ohlc.loc[i, 'open'], ohlc.loc[i, 'close']) * 0.99

                # 确保最高价 >= 最低价
                if high_price < low_price:
                    high_price, low_price = low_price, high_price

                ohlc.loc[i, 'high'] = high_price
                ohlc.loc[i, 'low'] = low_price
            except Exception as e:
                print(f"计算第{i}个K线最高/最低价时出错: {str(e)}，使用开盘/收盘价的偏移值")
                # 使用开盘价和收盘价的偏移值
                ohlc.loc[i, 'high'] = max(ohlc.loc[i, 'open'], ohlc.loc[i, 'close']) * 1.01
                ohlc.loc[i, 'low'] = min(ohlc.loc[i, 'open'], ohlc.loc[i, 'close']) * 0.99

        # 确保OHLC关系正确
        ohlc['high'] = ohlc[['high', 'open', 'close']].max(axis=1)
        ohlc['low'] = ohlc[['low', 'open', 'close']].min(axis=1)

        # 如果包含交易量特征，计算交易量 - 使用非线性映射的inverse方法
        if self.include_volume and 'volume' in df.columns:
            if start_volume is None or not np.isfinite(start_volume) or start_volume <= 0:
                # 如果没有提供起始交易量或无效，使用一个默认值
                start_volume = 1000000  # 默认交易量
                print(f"使用默认起始交易量: {start_volume}")

            # 计算交易量
            volumes = [start_volume]
            volume_std = max(1.0, start_volume * 0.2)  # 确保标准差为正

            for i in range(len(df)):
                try:
                    volume_value = df.iloc[i]['volume']

                    # 使用映射函数的inverse方法计算原始交易量变化
                    if 'volume' in self.mapping_functions:
                        raw_volume_change = self.mapping_functions['volume'].inverse(volume_value)
                        volume_change = raw_volume_change * volume_std
                    else:
                        volume_change = volume_value * volume_std

                    curr_volume = max(1.0, volumes[-1] + volume_change)  # 确保交易量为正
                    volumes.append(curr_volume)
                except Exception as e:
                    print(f"计算第{i}个K线交易量时出错: {str(e)}，使用前一个交易量")
                    volumes.append(volumes[-1])

            # 移除第一个交易量（它只是起始交易量）
            volumes = volumes[1:]

            # 添加交易量列
            ohlc['volume'] = volumes

        # 打印结果统计
        print("\nOHLC数据统计:")
        print(f"数据形状: {ohlc.shape}")
        print(ohlc.head())
        print(ohlc.describe())

        return ohlc

    def compare_mappings(self, data: np.ndarray, feature: str = 'change'):
        """
        比较不同映射函数对同一数据的效果

        Args:
            data: 要映射的数据
            feature: 特征名称，用于确定输出范围
        """
        # 确定输出范围
        if feature == 'change':
            output_range = self.change_range
        elif feature == 'entity':
            output_range = self.entity_range
        elif feature in ['upline', 'downline']:
            output_range = self.shadow_range
        elif feature == 'volume':
            output_range = self.volume_range
        else:
            raise ValueError(f"不支持的特征: {feature}")

        # 计算数据范围，排除异常值
        q1, q3 = np.percentile(data, [1, 99])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        # 限制数据范围
        filtered_data = data[(data >= lower_bound) & (data <= upper_bound)]

        # 如果数据太少，使用原始数据
        if len(filtered_data) < 10:
            filtered_data = data

        input_range = (np.min(filtered_data), np.max(filtered_data))

        # 创建不同的映射函数
        mapping_funcs = {
            'Linear': LinearMapping(input_range, output_range),
            'Logarithmic': LogarithmicMapping(input_range, output_range) if input_range[0] > 0 else None,
            'Exponential': ExponentialMapping(input_range, output_range),
            'Sigmoid': SigmoidMapping(input_range, output_range),
            'Quantile': QuantileMapping(filtered_data, output_range)
        }

        # 移除无效的映射函数
        mapping_funcs = {k: v for k, v in mapping_funcs.items() if v is not None}

        # 计算每个映射函数的映射结果
        results = {}
        for name, func in mapping_funcs.items():
            results[name] = np.array([func(x) for x in filtered_data])

        # 可视化比较
        fig, axes = plt.subplots(len(mapping_funcs) + 1, 1, figsize=(12, 3 * (len(mapping_funcs) + 1)))

        # 绘制原始数据分布
        axes[0].hist(filtered_data, bins=30, alpha=0.7)
        axes[0].set_title(f'原始{feature}数据分布')
        axes[0].set_xlabel('值')
        axes[0].set_ylabel('频率')

        # 绘制每个映射函数的结果分布
        for i, (name, mapped_data) in enumerate(results.items()):
            axes[i+1].hist(mapped_data, bins=output_range[1] - output_range[0] + 1,
                          range=(output_range[0] - 0.5, output_range[1] + 0.5),
                          alpha=0.7)
            axes[i+1].set_title(f'{name}映射后的分布')
            axes[i+1].set_xlabel('离散值')
            axes[i+1].set_ylabel('频率')

        plt.tight_layout()
        plt.show()

        return mapping_funcs, results

    def compare_reconstruction(self, df: pd.DataFrame, use_nonlinear: bool = True):
        """
        比较线性还原和非线性还原的效果

        Args:
            df: 原始K线数据
            use_nonlinear: 是否使用非线性还原

        Returns:
            原始数据、线性还原数据和非线性还原数据的元组
        """
        print("\n=== 比较K线还原效果 ===")

        # 创建线性tokenizer（作为基准）
        linear_tokenizer = CandlestickTokenizer(
            change_range=self.change_range,
            entity_range=self.entity_range,
            shadow_range=self.shadow_range,
            include_volume=self.include_volume
        )

        # 对数据进行tokenize
        tokens = self.tokenize(df)

        # 计算ATR
        atr = self._calculate_atr(df).iloc[-1]
        start_price = df['close'].iloc[0]
        start_volume = df['volume'].iloc[0] if 'volume' in df.columns else None

        # 使用线性方法还原
        # 为了公平比较，我们使用父类的tokens_to_candlesticks方法
        linear_reconstructed = super().tokens_to_candlesticks(tokens, start_price, atr, start_volume)

        # 使用非线性方法还原
        nonlinear_reconstructed = self.tokens_to_candlesticks(tokens, start_price, atr, start_volume)

        # 计算还原误差
        def calculate_error(original, reconstructed):
            """计算还原误差"""
            if len(original) != len(reconstructed):
                print(f"警告: 长度不匹配 - 原始: {len(original)}, 重建: {len(reconstructed)}")
                min_len = min(len(original), len(reconstructed))
                original = original.iloc[:min_len]
                reconstructed = reconstructed.iloc[:min_len]

            errors = {}
            for col in ['close', 'open', 'high', 'low']:
                if col in original.columns and col in reconstructed.columns:
                    # 计算绝对误差
                    abs_error = np.abs(original[col].values - reconstructed[col].values)
                    # 计算相对误差
                    rel_error = abs_error / original[col].values

                    errors[col] = {
                        'mean_abs_error': np.mean(abs_error),
                        'max_abs_error': np.max(abs_error),
                        'mean_rel_error': np.mean(rel_error) * 100,  # 转换为百分比
                        'max_rel_error': np.max(rel_error) * 100     # 转换为百分比
                    }

            return errors

        # 计算线性还原误差
        linear_errors = calculate_error(df, linear_reconstructed)

        # 计算非线性还原误差
        nonlinear_errors = calculate_error(df, nonlinear_reconstructed)

        # 打印误差统计
        print("\n线性还原误差统计:")
        for col, errors in linear_errors.items():
            print(f"  {col}:")
            for metric, value in errors.items():
                print(f"    {metric}: {value:.4f}")

        print("\n非线性还原误差统计:")
        for col, errors in nonlinear_errors.items():
            print(f"  {col}:")
            for metric, value in errors.items():
                print(f"    {metric}: {value:.4f}")

        # 可视化比较
        fig, axes = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

        # 绘制收盘价比较
        axes[0].plot(df.index, df['close'], 'b-', label='原始数据')
        axes[0].plot(linear_reconstructed.index, linear_reconstructed['close'], 'g--', label='线性还原')
        axes[0].plot(nonlinear_reconstructed.index, nonlinear_reconstructed['close'], 'r-.', label='非线性还原')
        axes[0].set_title('收盘价比较')
        axes[0].set_ylabel('价格')
        axes[0].legend()
        axes[0].grid(True)

        # 绘制相对误差比较
        linear_rel_error = np.abs(df['close'].values - linear_reconstructed['close'].values) / df['close'].values * 100
        nonlinear_rel_error = np.abs(df['close'].values - nonlinear_reconstructed['close'].values) / df['close'].values * 100

        axes[1].plot(df.index, linear_rel_error, 'g-', label='线性还原相对误差')
        axes[1].plot(df.index, nonlinear_rel_error, 'r-', label='非线性还原相对误差')
        axes[1].set_title('相对误差比较 (%)')
        axes[1].set_ylabel('相对误差 (%)')
        axes[1].set_xlabel('样本索引')
        axes[1].legend()
        axes[1].grid(True)

        plt.tight_layout()
        plt.show()

        # 可视化K线对比
        self.visualize_tokenization(df, title="原始K线数据")

        # 使用父类的visualize_tokenization方法显示线性还原结果
        linear_tokenizer.visualize_tokenization(df, reconstructed_df=linear_reconstructed, title="线性还原K线数据")

        # 使用当前类的visualize_tokenization方法显示非线性还原结果
        self.visualize_tokenization(df, reconstructed_df=nonlinear_reconstructed, title="非线性还原K线数据")

        return df, linear_reconstructed, nonlinear_reconstructed

    def visualize_tokenization(self, df: pd.DataFrame, tokens: List[int] = None,
                              reconstructed_df: pd.DataFrame = None, title: str = None,
                              show_volume: bool = True):
        """
        可视化原始K线数据、token化后的数据和重建的K线数据
        重写父类方法，使用非线性还原方法

        Args:
            df: 原始K线数据
            tokens: token索引列表（可选）
            reconstructed_df: 重建的K线数据（可选）
            title: 图表标题
            show_volume: 是否显示交易量
        """
        if tokens is not None and reconstructed_df is None:
            # 如果提供了tokens但没有提供重建的数据，则使用非线性方法重建
            atr = self._calculate_atr(df).iloc[-1]
            start_price = df['close'].iloc[0]
            start_volume = df['volume'].iloc[0] if 'volume' in df.columns else None
            # 使用自己的tokens_to_candlesticks方法，而不是父类的方法
            reconstructed_df = self.tokens_to_candlesticks(tokens, start_price, atr, start_volume)

        # 确定是否显示交易量
        has_volume = 'volume' in df.columns and show_volume
        has_reconstructed_volume = reconstructed_df is not None and 'volume' in reconstructed_df.columns and show_volume

        # 确定子图数量和布局
        n_rows = 1 + (reconstructed_df is not None)
        if has_volume or has_reconstructed_volume:
            n_rows *= 2

        # 创建图表
        fig, axes = plt.subplots(n_rows, 1, figsize=(12, 4 * n_rows), sharex=True,
                                gridspec_kw={'height_ratios': [3, 1] * (1 + (reconstructed_df is not None)) if has_volume or has_reconstructed_volume else [1] * n_rows})

        # 确保axes是数组
        if n_rows == 1:
            axes = [axes]

        # 绘制原始K线
        ax_idx = 0
        ax1 = axes[ax_idx]
        ax_idx += 1
        ax1.set_title('原始K线数据' if title is None else title)

        # 绘制K线
        for i in range(len(df)):
            # 计算位置和颜色
            x = i
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 绘制实体
            ax1.plot([x, x], [open_price, close_price], color=color, linewidth=6)
            # 绘制影线
            ax1.plot([x, x], [low_price, high_price], color=color, linewidth=1)

        # 如果有交易量，绘制交易量
        if has_volume:
            ax_vol = axes[ax_idx]
            ax_idx += 1
            ax_vol.set_title('原始交易量')
            ax_vol.bar(range(len(df)), df['volume'], color='blue', alpha=0.5)

        # 如果有重建的数据，绘制重建的K线
        if reconstructed_df is not None:
            ax2 = axes[ax_idx]
            ax_idx += 1
            ax2.set_title('非线性重建的K线数据' if title is None else f"非线性重建: {title}")

            for i in range(len(reconstructed_df)):
                # 计算位置和颜色
                x = i
                open_price = reconstructed_df['open'].iloc[i]
                close_price = reconstructed_df['close'].iloc[i]
                high_price = reconstructed_df['high'].iloc[i]
                low_price = reconstructed_df['low'].iloc[i]
                color = 'red' if close_price >= open_price else 'green'

                # 绘制实体
                ax2.plot([x, x], [open_price, close_price], color=color, linewidth=6)
                # 绘制影线
                ax2.plot([x, x], [low_price, high_price], color=color, linewidth=1)

            # 如果有重建的交易量，绘制重建的交易量
            if has_reconstructed_volume:
                ax_vol2 = axes[ax_idx]
                ax_vol2.set_title('重建的交易量')
                ax_vol2.bar(range(len(reconstructed_df)), reconstructed_df['volume'], color='purple', alpha=0.5)

        # 添加网格线
        for ax in axes:
            ax.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.show()

        return fig, axes
