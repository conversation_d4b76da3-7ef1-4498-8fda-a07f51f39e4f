"""
训练CandlestickGPT4模型

使用优化的训练策略训练CandlestickGPT4模型，包括：
1. 梯度累积
2. 学习率预热和余弦退火
3. 混合精度训练
4. 梯度裁剪
5. 早停策略
6. 标签平滑
7. 辅助损失
"""

import os
import sys
import math
import time
import json
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.cuda.amp import autocast, GradScaler
import logging
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入相关模块
from pyqlab.models.gpt2.bak.candlestick_gpt4 import CandlestickGPT4
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import (
    NonlinearCandlestickTokenizer,
    LogarithmicMapping,
    SquareRootMapping,
    ExponentialMapping,
    SigmoidMapping
)
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset
from pyqlab.models.gpt2.utils import load_single_data, generate_mock_data

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('candlestick_gpt4_training.log')
    ]
)
logger = logging.getLogger('CandlestickGPT4Trainer')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练CandlestickGPT4模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--begin_date', type=str, default='2023-01-01', help='开始日期')
    parser.add_argument('--end_date', type=str, default='2025-12-31', help='结束日期')
    parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集比例')
    parser.add_argument('--stride', type=int, default=1, help='滑动窗口步长')

    # 模型参数
    parser.add_argument('--block_size', type=int, default=64, help='序列长度')
    parser.add_argument('--n_layer', type=int, default=4, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=4, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=64, help='模型维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比例')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    parser.add_argument('--nonlinear_tokenizer', action='store_true', help='是否使用非线性tokenizer')

    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批大小')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=5e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='权重衰减')
    parser.add_argument('--warmup_ratio', type=float, default=0.1, help='预热比例')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪')
    parser.add_argument('--grad_accum_steps', type=int, default=1, help='梯度累积步数')
    parser.add_argument('--label_smoothing', type=float, default=0.1, help='标签平滑系数')
    parser.add_argument('--use_auxiliary_loss', action='store_true', help='是否使用辅助损失')
    parser.add_argument('--early_stopping', type=int, default=5, help='早停轮数')
    parser.add_argument('--mixed_precision', action='store_true', help='是否使用混合精度训练')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--save_dir', type=str, default='checkpoints/candlestick_gpt4', help='保存目录')
    parser.add_argument('--log_interval', type=int, default=100, help='日志间隔')
    parser.add_argument('--eval_interval', type=int, default=500, help='评估间隔')
    parser.add_argument('--save_interval', type=int, default=1000, help='保存间隔')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def create_tokenizer(args):
    """创建tokenizer"""
    logger.info("创建tokenizer")

    # 使用更小的范围来减小词汇表大小
    change_range = (-12, 12)
    entity_range = (-12, 12)
    shadow_range = (0, 7)

    if args.nonlinear_tokenizer:
        logger.info("使用非线性tokenizer")
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=change_range,
            entity_range=entity_range,
            shadow_range=shadow_range,
            atr_window=100,
            atr_mult=0.88,
            scale=10,  # 增加缩放因子，进一步减小词汇表
            include_volume=False,
            # 添加非线性映射函数
            mapping_functions={
                'change': SigmoidMapping((-60, 125), change_range),
                'entity': SigmoidMapping((-37, 47), entity_range),
                'upline': SquareRootMapping((0, 28), shadow_range),
                'downline': SquareRootMapping((0, 28), shadow_range)
            }
        )
    else:
        logger.info("使用线性tokenizer")
        tokenizer = CandlestickTokenizer(
            change_range=change_range,
            entity_range=entity_range,
            shadow_range=shadow_range,
            atr_window=100,
            atr_mult=0.88,
            scale=10,  # 增加缩放因子，进一步减小词汇表
            include_volume=False
        )

    logger.info(f"词汇表大小: {tokenizer.vocab_size}")
    return tokenizer

def create_datasets(train_data, train_code_ids, val_data, val_code_ids, tokenizer, args):
    """创建数据集"""
    logger.info("创建数据集")

    # 创建训练集
    train_dataset = CandlestickDataset(
        data=train_data,
        tokenizer=tokenizer,
        seq_len=args.block_size,
        pred_len=1,  # 预测未来5个时间步
        code_ids=train_code_ids,
        use_time_features=args.use_time_features,
        stride=args.stride
    )

    # 创建验证集
    val_dataset = CandlestickDataset(
        data=val_data,
        tokenizer=tokenizer,
        seq_len=args.block_size,
        pred_len=1,
        code_ids=val_code_ids,
        use_time_features=args.use_time_features,
        stride=args.stride
    )

    logger.info(f"训练集大小: {len(train_dataset)}")
    logger.info(f"验证集大小: {len(val_dataset)}")

    return train_dataset, val_dataset

def create_dataloaders(train_dataset, val_dataset, args):
    """创建数据加载器"""
    logger.info("创建数据加载器")

    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        persistent_workers=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        persistent_workers=True
    )

    logger.info(f"训练集批次数: {len(train_loader)}")
    logger.info(f"验证集批次数: {len(val_loader)}")

    return train_loader, val_loader

def create_model(tokenizer, args):
    """创建模型"""
    logger.info("创建模型")

    model = CandlestickGPT4(
        vocab_size=tokenizer.vocab_size,
        code_size=100,  # 假设最多100个不同的证券代码
        block_size=args.block_size,
        n_layer=args.n_layer,
        n_head=args.n_head,
        d_model=args.d_model,
        dropout=args.dropout,
        use_time_features=args.use_time_features,
        n_time_features=5,  # 假设有5个时间特征
        label_smoothing=args.label_smoothing,
        use_auxiliary_loss=args.use_auxiliary_loss
    )

    # 移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    logger.info(f"模型参数数量: {model.get_num_params():,}")
    logger.info(f"使用设备: {device}")

    return model, device

def create_optimizer_and_scheduler(model, train_loader, args):
    """创建优化器和学习率调度器"""
    logger.info("创建优化器和学习率调度器")

    # 创建优化器
    optimizer = model.configure_optimizers(
        weight_decay=args.weight_decay,
        learning_rate=args.learning_rate,
        betas=(0.9, 0.95),
        device_type='cuda' if torch.cuda.is_available() else 'cpu'
    )

    # 计算总训练步数
    num_training_steps = len(train_loader) * args.epochs // args.grad_accum_steps

    # 计算预热步数
    warmup_steps = int(num_training_steps * args.warmup_ratio)

    # 创建学习率调度器
    def lr_lambda(step):
        # 线性预热
        if step < warmup_steps:
            return float(step) / float(max(1, warmup_steps))
        # 余弦退火
        progress = float(step - warmup_steps) / float(max(1, num_training_steps - warmup_steps))
        return 0.5 * (1.0 + math.cos(math.pi * progress))

    scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

    logger.info(f"总训练步数: {num_training_steps}")
    logger.info(f"预热步数: {warmup_steps}")

    return optimizer, scheduler

def validate(model, val_loader, device, args):
    """验证模型"""
    model.eval()
    total_loss = 0
    total_samples = 0

    with torch.no_grad():
        for batch in val_loader:
            # 获取数据
            input_tokens = batch['input_tokens'].to(device)
            targets = batch['target_tokens'].to(device)
            code_id = batch['code_id'].to(device)
            time_features = batch['time_features'].to(device) if args.use_time_features else None

            # 前向传播
            with autocast(enabled=args.mixed_precision):
                _, loss = model(
                    input_tokens=input_tokens,
                    code_ids=code_id,
                    time_features=time_features,
                    targets=targets
                )

            # 累加损失
            batch_size = input_tokens.size(0)
            total_loss += loss.item() * batch_size
            total_samples += batch_size

    # 计算平均损失
    avg_loss = total_loss / total_samples

    model.train()
    return avg_loss

def train(model, train_loader, val_loader, optimizer, scheduler, device, args):
    """训练模型"""
    logger.info("开始训练")

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 保存配置
    with open(os.path.join(args.save_dir, 'config.json'), 'w') as f:
        json.dump(vars(args), f, indent=4)

    # 初始化混合精度训练
    scaler = GradScaler(enabled=args.mixed_precision)

    # 初始化训练状态
    best_val_loss = float('inf')
    no_improve_count = 0
    global_step = 0

    # 训练循环
    for epoch in range(args.epochs):
        logger.info(f"Epoch {epoch+1}/{args.epochs}")

        # 初始化进度条
        progress_bar = tqdm(total=len(train_loader), desc=f"Epoch {epoch+1}/{args.epochs}")

        # 初始化统计
        epoch_loss = 0
        epoch_samples = 0
        epoch_start_time = time.time()

        # 批次循环
        for batch_idx, batch in enumerate(train_loader):
            # 获取数据
            input_tokens = batch['input_tokens'].to(device)
            targets = batch['target_tokens'].to(device)
            code_id = batch['code_id'].to(device)
            time_features = batch['time_features'].to(device) if args.use_time_features else None

            # 前向传播
            with autocast(enabled=args.mixed_precision):
                _, loss = model(
                    input_tokens=input_tokens,
                    code_ids=code_id,
                    time_features=time_features,
                    targets=targets
                )

                # 缩放损失以适应梯度累积
                loss = loss / args.grad_accum_steps

            # 反向传播
            scaler.scale(loss).backward()

            # 梯度累积
            if (batch_idx + 1) % args.grad_accum_steps == 0 or (batch_idx + 1) == len(train_loader):
                # 梯度裁剪
                if args.grad_clip > 0:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)

                # 更新参数
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True)

                # 更新学习率
                scheduler.step()

                # 更新全局步数
                global_step += 1

            # 累加损失
            batch_size = input_tokens.size(0)
            epoch_loss += loss.item() * args.grad_accum_steps * batch_size
            epoch_samples += batch_size

            # 更新进度条
            progress_bar.update(1)
            progress_bar.set_postfix({
                'loss': loss.item() * args.grad_accum_steps,
                'lr': scheduler.get_last_lr()[0]
            })

            # 记录日志
            if global_step % args.log_interval == 0:
                lr = scheduler.get_last_lr()[0]
                logger.info(f"Step {global_step} | Loss: {loss.item() * args.grad_accum_steps:.4f} | LR: {lr:.6f} | {time.time() - epoch_start_time:.2f}s elapsed")

            # 验证
            if global_step % args.eval_interval == 0:
                val_loss = validate(model, val_loader, device, args)
                logger.info(f"Validation Loss: {val_loss:.4f}")

                # 检查是否有改进
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    no_improve_count = 0

                    # 保存最佳模型
                    torch.save(
                        {
                            'model_state_dict': model.state_dict(),
                            'optimizer_state_dict': optimizer.state_dict(),
                            'scheduler_state_dict': scheduler.state_dict(),
                            'val_loss': val_loss,
                            'global_step': global_step,
                            'epoch': epoch
                        },
                        os.path.join(args.save_dir, 'best_model.pt')
                    )
                    logger.info(f"保存最佳模型，验证损失: {val_loss:.4f}")
                else:
                    no_improve_count += 1

                # 早停
                if args.early_stopping > 0 and no_improve_count >= args.early_stopping:
                    logger.info(f"早停: {args.early_stopping}轮未改进")
                    return

            # 保存检查点
            if global_step % args.save_interval == 0:
                torch.save(
                    {
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'scheduler_state_dict': scheduler.state_dict(),
                        'global_step': global_step,
                        'epoch': epoch
                    },
                    os.path.join(args.save_dir, f'checkpoint_step_{global_step}.pt')
                )
                logger.info(f"保存检查点到 {args.save_dir}/checkpoint_step_{global_step}.pt")

        # 关闭进度条
        progress_bar.close()

        # 计算epoch平均损失
        epoch_avg_loss = epoch_loss / epoch_samples
        logger.info(f"Epoch {epoch+1} 完成，平均损失: {epoch_avg_loss:.4f}")

        # 每个epoch结束后验证
        val_loss = validate(model, val_loader, device, args)
        logger.info(f"Epoch {epoch+1} 验证损失: {val_loss:.4f}")

        # 检查是否有改进
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            no_improve_count = 0

            # 保存最佳模型
            torch.save(
                {
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'val_loss': val_loss,
                    'global_step': global_step,
                    'epoch': epoch
                },
                os.path.join(args.save_dir, 'best_model.pt')
            )
            logger.info(f"保存最佳模型，验证损失: {val_loss:.4f}")
        else:
            no_improve_count += 1

        # 早停
        if args.early_stopping > 0 and no_improve_count >= args.early_stopping:
            logger.info(f"早停: {args.early_stopping}轮未改进")
            break

    logger.info("训练完成")

def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 加载数据
    if args.data_path is not None:
        # 加载数据
        print(f"从{args.data_path}加载数据...")
        train_data, train_code_ids, val_data, val_code_ids = load_single_data(args.data_path, args.begin_date, args.end_date)
    else:
        # 生成模拟数据
        train_data, train_code_ids = generate_mock_data(n_samples=args.samples, n_securities=args.securities, trend='random')
        val_data, val_code_ids = generate_mock_data(n_samples=args.samples//5, n_securities=args.securities//2, trend='random')

    print(f"训练数据: {len(train_data)} 个证券，每个证券 {len(train_data[0])} 个样本")
    print(f"验证数据: {len(val_data)} 个证券，每个证券 {len(val_data[0])} 个样本")

    # 创建tokenizer
    tokenizer = create_tokenizer(args)

    # 创建数据集
    train_dataset, val_dataset = create_datasets(train_data, train_code_ids, val_data, val_code_ids, tokenizer, args)

    # 创建数据加载器
    train_loader, val_loader = create_dataloaders(train_dataset, val_dataset, args)

    # 创建模型
    model, device = create_model(tokenizer, args)

    # 创建优化器和学习率调度器
    optimizer, scheduler = create_optimizer_and_scheduler(model, train_loader, args)

    # 训练模型
    train(model, train_loader, val_loader, optimizer, scheduler, device, args)

if __name__ == "__main__":
    main()
