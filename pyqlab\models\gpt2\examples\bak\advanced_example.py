"""
高级版K线预测模型示例
"""

import os
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import argparse
import json
from datetime import datetime

from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset, TimeSeriesCandlestickDataset
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.bak.advanced_candlestick_llm import AdvancedCandlestickLLM
from pyqlab.models.gpt2.bak.multi_timeframe_tokenizer import MultiTimeframeTokenizer
from pyqlab.models.gpt2.bak.data_augmentation import CandlestickDataAugmentation

def load_sample_data(file_path=None):
    """
    加载示例数据，如果没有提供文件路径，则生成随机数据
    """
    if file_path and os.path.exists(file_path):
        # 加载真实数据
        df = pd.read_csv(file_path)
        # 确保列名正确
        if 'date' in df.columns and 'datetime' not in df.columns:
            df.rename(columns={'date': 'datetime'}, inplace=True)
        return df
    else:
        # 生成随机数据
        print("未找到数据文件，生成随机数据...")
        np.random.seed(42)
        n_samples = 500
        
        # 生成日期时间
        start_date = pd.Timestamp('2020-01-01')
        dates = [start_date + pd.Timedelta(days=i) for i in range(n_samples)]
        
        # 生成价格
        close = np.random.normal(loc=100, scale=1, size=n_samples).cumsum() + 1000
        daily_volatility = 0.01
        
        high = close * (1 + np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        low = close * (1 - np.random.normal(loc=0, scale=daily_volatility, size=n_samples).clip(0, 0.05))
        open_price = low + (high - low) * np.random.random(size=n_samples)
        volume = np.random.normal(loc=1000000, scale=200000, size=n_samples).clip(100000, None)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': dates,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
        
        return df

def create_advanced_model(vocab_size, code_size):
    """
    创建高级版K线预测模型
    """
    model = AdvancedCandlestickLLM(
        vocab_size=vocab_size,
        code_size=code_size,
        block_size=50,
        n_layer=6,
        n_head=8,
        d_model=512,
        dropout=0.1,
        use_time_features=True,
        use_multi_timeframe=True,
        timeframes=['5m', '15m', '1h', '1d'],
        use_mixture_of_experts=True,
        n_experts=4,
        use_cross_security=True,
        use_multi_task=True
    )
    
    return model

def demo_nonlinear_tokenization(df):
    """
    演示非线性tokenization
    """
    print("\n=== 演示非线性Tokenization ===")
    
    # 创建线性tokenizer（作为基准）
    linear_tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )
    
    # 创建非线性tokenizer
    nonlinear_tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )
    
    # 对数据进行tokenize
    linear_tokens = linear_tokenizer.tokenize(df)
    nonlinear_tokens = nonlinear_tokenizer.tokenize(df)
    
    print(f"线性tokenizer生成的token数量: {len(linear_tokens)}")
    print(f"非线性tokenizer生成的token数量: {len(nonlinear_tokens)}")
    
    # 可视化tokenization结果
    linear_tokenizer.visualize_tokenization(df, linear_tokens, title="线性Tokenizer结果")
    nonlinear_tokenizer.visualize_tokenization(df, nonlinear_tokens, title="非线性Tokenizer结果")
    
    # 可视化映射函数
    nonlinear_tokenizer.visualize_mapping_functions()
    
    return linear_tokenizer, nonlinear_tokenizer, linear_tokens, nonlinear_tokens

def demo_multi_timeframe(df):
    """
    演示多时间框架功能
    """
    print("\n=== 演示多时间框架功能 ===")
    
    # 创建基础tokenizer
    base_tokenizer = NonlinearCandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=True
    )
    
    # 创建多时间框架tokenizer
    mtf_tokenizer = MultiTimeframeTokenizer(
        base_tokenizer=base_tokenizer,
        timeframes=['5m', '15m', '1h', '1d']
    )
    
    # 对多个时间框架进行tokenize
    tokens_dict = mtf_tokenizer.tokenize_multi_timeframe(df)
    
    # 打印每个时间框架的token数量
    for tf, tokens in tokens_dict.items():
        print(f"时间框架 {tf}: {len(tokens)}个tokens")
        
    # 组合tokens
    combined_tokens = mtf_tokenizer.combine_tokens(tokens_dict, method='concat')
    print(f"组合后的token数量: {len(combined_tokens)}")
    
    # 可视化多时间框架
    mtf_tokenizer.visualize_multi_timeframe(df, tokens_dict)
    
    return mtf_tokenizer, tokens_dict, combined_tokens

def demo_data_augmentation(df, tokenizer):
    """
    演示数据增强功能
    """
    print("\n=== 演示数据增强 ===")
    
    # 创建数据增强器
    augmenter = CandlestickDataAugmentation(tokenizer)
    
    # 演示时间扭曲
    print("\n--- 时间扭曲 ---")
    time_warped_df = augmenter.time_warp(df, warp_factor=0.2)
    augmenter.visualize_augmentation(df, time_warped_df, method='time_warp')
    
    # 演示幅度扭曲
    print("\n--- 幅度扭曲 ---")
    magnitude_warped_df = augmenter.magnitude_warp(df, warp_factor=0.2)
    augmenter.visualize_augmentation(df, magnitude_warped_df, method='magnitude_warp')
    
    # 演示抖动
    print("\n--- 抖动 ---")
    jittered_df = augmenter.jitter(df, jitter_factor=0.01)
    augmenter.visualize_augmentation(df, jittered_df, method='jitter')
    
    # 生成增强数据集
    print("\n--- 生成增强数据集 ---")
    augmented_dfs = augmenter.generate_augmented_dataset(df, n_augmentations=5)
    print(f"生成了 {len(augmented_dfs)} 个数据集")
    
    return augmenter, augmented_dfs

def demo_advanced_model(df, tokenizer):
    """
    演示高级版K线预测模型
    """
    print("\n=== 演示高级版K线预测模型 ===")
    
    # 对数据进行tokenize
    tokens = tokenizer.tokenize(df)
    
    # 创建数据集
    seq_len = 30
    dataset = CandlestickDataset(tokens, seq_len=seq_len)
    
    # 创建模型
    vocab_size = tokenizer.vocab_size
    code_size = 10  # 假设有10个证券代码
    
    # 创建基础模型（作为基准）
    base_model = CandlestickLLM(
        vocab_size=vocab_size,
        code_size=code_size,
        block_size=seq_len,
        n_layer=4,
        n_head=4,
        d_model=256
    )
    
    # 创建高级模型
    advanced_model = AdvancedCandlestickLLM(
        vocab_size=vocab_size,
        code_size=code_size,
        block_size=seq_len,
        n_layer=4,
        n_head=4,
        d_model=256,
        use_time_features=True,
        use_multi_timeframe=True,
        timeframes=['5m', '15m', '1h', '1d'],
        use_mixture_of_experts=True,
        n_experts=2,
        use_cross_security=True,
        use_multi_task=True
    )
    
    # 打印模型信息
    print(f"基础模型参数数量: {base_model.get_num_params()/1e6:.2f}M")
    print(f"高级模型参数数量: {advanced_model.get_num_params()/1e6:.2f}M")
    
    # 创建数据加载器
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
    
    # 获取一个批次的数据
    batch = next(iter(dataloader))
    input_tokens = batch['input_tokens']
    target_tokens = batch['target_tokens']
    
    # 创建随机code_ids
    code_ids = torch.randint(0, code_size, (input_tokens.size(0),))
    
    # 使用基础模型进行前向传播
    base_logits, base_loss = base_model(input_tokens, code_ids, targets=target_tokens)
    
    # 使用高级模型进行前向传播
    advanced_outputs, advanced_loss = advanced_model(input_tokens, code_ids, targets=target_tokens)
    
    if isinstance(advanced_outputs, dict):
        advanced_logits = advanced_outputs['token_logits']
    else:
        advanced_logits = advanced_outputs
    
    print(f"基础模型损失: {base_loss.item():.4f}")
    print(f"高级模型损失: {advanced_loss.item():.4f}")
    
    # 生成预测
    print("\n--- 生成预测 ---")
    
    # 使用基础模型生成预测
    base_generated = base_model.generate(
        input_tokens[:1, :20],  # 使用第一个样本的前20个token
        code_ids[:1],
        max_new_tokens=10,
        temperature=0.8,
        top_k=5
    )
    
    # 解码生成的token
    base_generated_tokens = base_generated[0, 20:].tolist()
    base_generated_strings = [tokenizer.idx2token.get(t, '<UNK>') for t in base_generated_tokens]
    
    print("基础模型生成的tokens:")
    for i, token_str in enumerate(base_generated_strings):
        print(f"  {i+1}. {token_str}")
    
    # 使用高级模型生成预测
    # 注意：这里简化处理，实际上高级模型可能需要更复杂的生成逻辑
    advanced_generated = advanced_model.generate(
        input_tokens[:1, :20],
        code_ids[:1],
        max_new_tokens=10,
        temperature=0.8,
        top_k=5
    )
    
    # 解码生成的token
    advanced_generated_tokens = advanced_generated[0, 20:].tolist()
    advanced_generated_strings = [tokenizer.idx2token.get(t, '<UNK>') for t in advanced_generated_tokens]
    
    print("\n高级模型生成的tokens:")
    for i, token_str in enumerate(advanced_generated_strings):
        print(f"  {i+1}. {token_str}")
    
    return base_model, advanced_model

def demo_multi_task_prediction(df, tokenizer, model):
    """
    演示多任务预测功能
    """
    print("\n=== 演示多任务预测功能 ===")
    
    # 对数据进行tokenize
    tokens = tokenizer.tokenize(df)
    
    # 创建输入
    seq_len = 30
    input_tokens = torch.tensor(tokens[-seq_len:], dtype=torch.long).unsqueeze(0)
    code_id = torch.tensor([0], dtype=torch.long)
    
    # 使用模型进行预测
    with torch.no_grad():
        outputs, _ = model(input_tokens, code_id)
        
    # 解析预测结果
    if isinstance(outputs, dict):
        # 获取方向预测
        direction_logits = outputs['direction_logits']
        direction_probs = torch.softmax(direction_logits, dim=-1)[0].numpy()
        direction_labels = ['下跌', '持平', '上涨']
        
        # 获取波动性预测
        volatility_logits = outputs['volatility_logits']
        volatility_probs = torch.softmax(volatility_logits, dim=-1)[0].numpy()
        volatility_labels = ['极低', '低', '中等', '高', '极高']
        
        # 获取交易量预测
        volume_logits = outputs['volume_logits']
        volume_probs = torch.softmax(volume_logits, dim=-1)[0].numpy()
        volume_labels = ['极低', '低', '中等', '高', '极高']
        
        # 可视化预测结果
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(10, 12))
        
        # 方向预测
        ax1.bar(direction_labels, direction_probs)
        ax1.set_title('方向预测')
        ax1.set_ylabel('概率')
        for i, p in enumerate(direction_probs):
            ax1.text(i, p + 0.01, f'{p:.2f}', ha='center')
            
        # 波动性预测
        ax2.bar(volatility_labels, volatility_probs)
        ax2.set_title('波动性预测')
        ax2.set_ylabel('概率')
        for i, p in enumerate(volatility_probs):
            ax2.text(i, p + 0.01, f'{p:.2f}', ha='center')
            
        # 交易量预测
        ax3.bar(volume_labels, volume_probs)
        ax3.set_title('交易量预测')
        ax3.set_ylabel('概率')
        for i, p in enumerate(volume_probs):
            ax3.text(i, p + 0.01, f'{p:.2f}', ha='center')
            
        plt.tight_layout()
        plt.show()
        
        # 打印预测结果
        print("\n预测结果:")
        print(f"方向预测: {direction_labels[direction_probs.argmax()]} (概率: {direction_probs.max():.2f})")
        print(f"波动性预测: {volatility_labels[volatility_probs.argmax()]} (概率: {volatility_probs.max():.2f})")
        print(f"交易量预测: {volume_labels[volume_probs.argmax()]} (概率: {volume_probs.max():.2f})")
        
        return {
            'direction': {
                'label': direction_labels[direction_probs.argmax()],
                'probs': direction_probs
            },
            'volatility': {
                'label': volatility_labels[volatility_probs.argmax()],
                'probs': volatility_probs
            },
            'volume': {
                'label': volume_labels[volume_probs.argmax()],
                'probs': volume_probs
            }
        }
    else:
        print("模型不支持多任务预测")
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='高级版K线预测模型示例')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, default=None, help='数据文件路径')
    parser.add_argument('--demo', type=str, default='all', 
                       choices=['all', 'tokenization', 'multi_timeframe', 'data_augmentation', 'model', 'multi_task'],
                       help='要运行的演示')
    
    args = parser.parse_args()
    
    # 加载数据
    df = load_sample_data(args.data_path)
    print(f'数据形状: {df.shape}')
    
    # 运行演示
    if args.demo in ['all', 'tokenization']:
        linear_tokenizer, nonlinear_tokenizer, linear_tokens, nonlinear_tokens = demo_nonlinear_tokenization(df)
        
    if args.demo in ['all', 'multi_timeframe']:
        mtf_tokenizer, tokens_dict, combined_tokens = demo_multi_timeframe(df)
        
    if args.demo in ['all', 'data_augmentation']:
        # 使用非线性tokenizer
        if 'nonlinear_tokenizer' not in locals():
            nonlinear_tokenizer = NonlinearCandlestickTokenizer(
                change_range=(-12, 12),
                entity_range=(-12, 12),
                shadow_range=(0, 7),
                include_volume=True
            )
        augmenter, augmented_dfs = demo_data_augmentation(df, nonlinear_tokenizer)
        
    if args.demo in ['all', 'model']:
        # 使用非线性tokenizer
        if 'nonlinear_tokenizer' not in locals():
            nonlinear_tokenizer = NonlinearCandlestickTokenizer(
                change_range=(-12, 12),
                entity_range=(-12, 12),
                shadow_range=(0, 7),
                include_volume=True
            )
        base_model, advanced_model = demo_advanced_model(df, nonlinear_tokenizer)
        
    if args.demo in ['all', 'multi_task']:
        # 使用非线性tokenizer和高级模型
        if 'nonlinear_tokenizer' not in locals():
            nonlinear_tokenizer = NonlinearCandlestickTokenizer(
                change_range=(-12, 12),
                entity_range=(-12, 12),
                shadow_range=(0, 7),
                include_volume=True
            )
        if 'advanced_model' not in locals():
            vocab_size = nonlinear_tokenizer.vocab_size
            code_size = 10
            advanced_model = AdvancedCandlestickLLM(
                vocab_size=vocab_size,
                code_size=code_size,
                block_size=30,
                n_layer=4,
                n_head=4,
                d_model=256,
                use_time_features=True,
                use_multi_timeframe=False,  # 简化演示
                use_mixture_of_experts=False,  # 简化演示
                use_cross_security=False,  # 简化演示
                use_multi_task=True
            )
        predictions = demo_multi_task_prediction(df, nonlinear_tokenizer, advanced_model)
        
    print("\n演示完成!")

if __name__ == '__main__':
    main()
