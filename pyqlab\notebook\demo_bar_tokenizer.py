#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BarTokenizer演示脚本
展示如何使用不同的BarTokenizer方法并可视化结果
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import os
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def generate_sample_data(n_samples: int = 5000) -> pd.DataFrame:
    """生成示例K线数据"""
    np.random.seed(42)
    
    # 生成价格序列
    base_price = 100
    returns = np.random.normal(0, 0.02, n_samples)
    prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLCV数据
    df = pd.DataFrame({
        'open': prices,
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n_samples))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n_samples))),
        'close': prices * (1 + np.random.normal(0, 0.005, n_samples)),
        'volume': np.random.lognormal(10, 1, n_samples)
    })
    
    # 确保OHLC数据的合理性
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    return df

def demo_single_tokenizer():
    """演示单个tokenizer的使用"""
    print("=" * 60)
    print("演示1: 单个BarTokenizer的使用")
    print("=" * 60)
    
    try:
        from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
        
        # 生成示例数据
        df = generate_sample_data(1000)
        print(f"生成示例数据: {df.shape}")
        
        # 创建tokenizer
        tokenizer = BarTokenizer(
            mapping_strategy='quantile',
            balancing_strategy='frequency',
            n_bins=50,
            features=['change', 'body', 'upper_shadow', 'lower_shadow'],
            atr_period=14
        )
        
        print(f"创建tokenizer: {tokenizer.mapping_strategy_name} + {tokenizer.balancing_strategy_name}")
        
        # 拟合和转换
        tokens = tokenizer.fit_transform(df)
        print(f"生成tokens: {len(tokens)} 个")
        print(f"词汇表大小: {tokenizer.get_vocab_size()}")
        
        # 分析分布
        balance_metrics = tokenizer.analyze_balance(tokens)
        print(f"\n分布质量指标:")
        print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
        print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
        print(f"  变异系数: {balance_metrics['coefficient_of_variation']:.4f}")
        
        # 显示token分布
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        print(f"  唯一tokens数: {len(unique_tokens)}")
        print(f"  最高频token: {unique_tokens[np.argmax(counts)]} (出现{np.max(counts)}次)")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入BarTokenizer失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        return False

def demo_compare_methods():
    """演示不同方法的对比"""
    print("\n" + "=" * 60)
    print("演示2: 不同BarTokenizer方法对比")
    print("=" * 60)
    
    try:
        from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
        
        # 生成示例数据
        df = generate_sample_data(2000)
        
        # 定义要测试的方法
        methods = {
            'Linear': {'mapping_strategy': 'linear', 'balancing_strategy': 'none'},
            'Quantile': {'mapping_strategy': 'quantile', 'balancing_strategy': 'none'},
            'Quantile+Balance': {'mapping_strategy': 'quantile', 'balancing_strategy': 'frequency'},
        }
        
        results = {}
        
        for method_name, config in methods.items():
            print(f"\n测试方法: {method_name}")
            
            # 创建tokenizer
            tokenizer = BarTokenizer(
                mapping_strategy=config['mapping_strategy'],
                balancing_strategy=config['balancing_strategy'],
                n_bins=50,
                features=['change', 'body', 'upper_shadow', 'lower_shadow'],
                atr_period=14
            )
            
            # 拟合和转换
            tokens = tokenizer.fit_transform(df)
            
            # 分析分布
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            results[method_name] = {
                'tokens': tokens,
                'balance_metrics': balance_metrics,
                'vocab_size': tokenizer.get_vocab_size()
            }
            
            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
        
        # 可视化对比
        plot_comparison(results)
        
        return True
        
    except Exception as e:
        print(f"❌ 对比分析出错: {e}")
        return False

def plot_comparison(results: Dict):
    """绘制对比图表"""
    
    methods = list(results.keys())
    gini_coeffs = [results[m]['balance_metrics']['gini_coefficient'] for m in methods]
    entropies = [results[m]['balance_metrics']['normalized_entropy'] for m in methods]
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 基尼系数对比
    axes[0].bar(methods, gini_coeffs, color='skyblue', alpha=0.7)
    axes[0].set_title('基尼系数对比\n(越小越好)', fontweight='bold')
    axes[0].set_ylabel('基尼系数')
    axes[0].tick_params(axis='x', rotation=45)
    
    # 标准化熵对比
    axes[1].bar(methods, entropies, color='lightgreen', alpha=0.7)
    axes[1].set_title('标准化熵对比\n(越大越好)', fontweight='bold')
    axes[1].set_ylabel('标准化熵')
    axes[1].tick_params(axis='x', rotation=45)
    
    # Token分布对比
    for i, (method_name, result) in enumerate(results.items()):
        tokens = result['tokens']
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        frequencies = counts / len(tokens)
        
        # 只显示前20个最频繁的tokens
        if len(frequencies) > 20:
            sorted_indices = np.argsort(frequencies)[::-1]
            top_frequencies = frequencies[sorted_indices[:20]]
        else:
            top_frequencies = frequencies
        
        axes[2].plot(range(len(top_frequencies)), sorted(top_frequencies, reverse=True), 
                    marker='o', label=method_name, alpha=0.7)
    
    axes[2].set_title('Token频率分布对比', fontweight='bold')
    axes[2].set_xlabel('Token排名')
    axes[2].set_ylabel('频率')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.suptitle('BarTokenizer方法对比', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

def demo_real_data():
    """演示使用真实数据"""
    print("\n" + "=" * 60)
    print("演示3: 使用真实数据（如果可用）")
    print("=" * 60)
    
    # 尝试加载真实数据
    data_paths = [
        'f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv',
        'e:/hqdata/tick/2024/SF202409.parquet',
        # 可以添加更多路径
    ]
    
    df = None
    for path in data_paths:
        try:
            if os.path.exists(path):
                if path.endswith('.csv'):
                    df = pd.read_csv(path)
                elif path.endswith('.parquet'):
                    df = pd.read_parquet(path)
                
                print(f"✅ 成功加载数据: {path}")
                print(f"   数据形状: {df.shape}")
                print(f"   数据列: {df.columns.tolist()}")
                
                # 如果数据太大，采样
                if len(df) > 5000:
                    df = df.sample(n=5000, random_state=42)
                    print(f"   采样到: {len(df)} 行")
                
                break
        except Exception as e:
            print(f"❌ 加载 {path} 失败: {e}")
    
    if df is None:
        print("❌ 未找到可用的真实数据，使用模拟数据")
        df = generate_sample_data(2000)
    
    # 检查数据格式
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    if not all(col in df.columns for col in required_cols):
        print("❌ 数据不包含必需的OHLCV列，使用模拟数据")
        df = generate_sample_data(2000)
    
    # 使用最佳配置进行分析
    try:
        from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
        
        tokenizer = BarTokenizer(
            mapping_strategy='quantile',
            balancing_strategy='frequency',
            n_bins=100,
            features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
            atr_period=14
        )
        
        tokens = tokenizer.fit_transform(df)
        balance_metrics = tokenizer.analyze_balance(tokens)
        
        print(f"\n真实数据分析结果:")
        print(f"  数据量: {len(df)}")
        print(f"  生成tokens: {len(tokens)}")
        print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
        print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
        print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
        print(f"  变异系数: {balance_metrics['coefficient_of_variation']:.4f}")
        
        # 显示token分布统计
        unique_tokens, counts = np.unique(tokens, return_counts=True)
        frequencies = counts / len(tokens)
        
        print(f"\nToken分布统计:")
        print(f"  唯一tokens: {len(unique_tokens)}")
        print(f"  最高频率: {np.max(frequencies):.4f}")
        print(f"  最低频率: {np.min(frequencies):.4f}")
        print(f"  前10%tokens累计频率: {np.sum(np.sort(frequencies)[-len(frequencies)//10:]):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据分析出错: {e}")
        return False

def main():
    """主函数"""
    print("BarTokenizer演示程序")
    print("本程序将演示BarTokenizer的基本使用方法")
    
    success_count = 0
    
    # 演示1: 基本使用
    if demo_single_tokenizer():
        success_count += 1
    
    # 演示2: 方法对比
    if demo_compare_methods():
        success_count += 1
    
    # 演示3: 真实数据
    if demo_real_data():
        success_count += 1
    
    print(f"\n" + "=" * 60)
    print(f"演示完成！成功运行 {success_count}/3 个演示")
    
    if success_count == 0:
        print("❌ 所有演示都失败了，请检查BarTokenizer模块是否正确安装")
    elif success_count < 3:
        print("⚠️  部分演示失败，但基本功能正常")
    else:
        print("✅ 所有演示都成功运行！")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
