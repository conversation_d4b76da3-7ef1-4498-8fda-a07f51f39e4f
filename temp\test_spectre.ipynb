{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import torch\n", "from pyqlab import spectre\n", "from pyqlab.spectre import factors"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "loader=spectre.data.ProviderLoader()\n", "engine=spectre.factors.FactorEngine(loader)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "engine.remove_all_factors()\n", "fast_ema=factors.EMA(20)\n", "slow_ema=factors.EMA(40)\n", "engine.add(fast_ema, 'fast_ema')\n", "engine.add(slow_ema, 'slow_ema')\n", "engine.add(fast_ema > slow_ema, 'buy_signal')\n", "engine.add(fast_ema < slow_ema, 'sell_signal')\n", "engine.add(factors.OHLCV.close, 'close')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["DEBUG:_prepare_tensor:load df shape: (8743, 6)\n", "DEBUG:_prepare_tensor:process df shape: (8743, 6)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(8840, 5)\n"]}], "source": ["ret=engine.run('2021-10-14', '2022-11-03', False)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>fast_ema</th>\n", "      <th>slow_ema</th>\n", "      <th>buy_signal</th>\n", "      <th>sell_signal</th>\n", "      <th>close</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2021-10-14 04:48:00+00:00</th>\n", "      <th>A8888.DC</th>\n", "      <td>577.238095</td>\n", "      <td>295.658537</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>6061.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AG8888.SC</th>\n", "      <td>469.904762</td>\n", "      <td>240.682927</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>4934.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AP8888.ZC</th>\n", "      <td>1293.868485</td>\n", "      <td>678.903029</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>7122.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BU8888.SC</th>\n", "      <td>319.047619</td>\n", "      <td>163.414634</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>3350.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>C8888.DC</th>\n", "      <td>240.666667</td>\n", "      <td>123.268293</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>2527.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2022-11-02 04:48:00+00:00</th>\n", "      <th>SS8888.SC</th>\n", "      <td>16965.419092</td>\n", "      <td>16809.750591</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>16660.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TA8888.ZC</th>\n", "      <td>5299.481302</td>\n", "      <td>5420.453486</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>5152.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>UR8888.ZC</th>\n", "      <td>2313.817039</td>\n", "      <td>2339.682178</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2324.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>V8888.DC</th>\n", "      <td>5882.846882</td>\n", "      <td>6067.473529</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>5782.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Y8888.DC</th>\n", "      <td>9364.436018</td>\n", "      <td>9402.222841</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>9528.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8738 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                         fast_ema      slow_ema  buy_signal  \\\n", "date                      asset                                               \n", "2021-10-14 04:48:00+00:00 A8888.DC     577.238095    295.658537        True   \n", "                          AG8888.SC    469.904762    240.682927        True   \n", "                          AP8888.ZC   1293.868485    678.903029        True   \n", "                          BU8888.SC    319.047619    163.414634        True   \n", "                          C8888.DC     240.666667    123.268293        True   \n", "...                                           ...           ...         ...   \n", "2022-11-02 04:48:00+00:00 SS8888.SC  16965.419092  16809.750591        True   \n", "                          TA8888.ZC   5299.481302   5420.453486       False   \n", "                          UR8888.ZC   2313.817039   2339.682178       False   \n", "                          V8888.DC    5882.846882   6067.473529       False   \n", "                          Y8888.DC    9364.436018   9402.222841       False   \n", "\n", "                                     sell_signal    close  \n", "date                      asset                            \n", "2021-10-14 04:48:00+00:00 A8888.DC         False   6061.0  \n", "                          AG8888.SC        False   4934.0  \n", "                          AP8888.ZC        False   7122.0  \n", "                          BU8888.SC        False   3350.0  \n", "                          C8888.DC         False   2527.0  \n", "...                                          ...      ...  \n", "2022-11-02 04:48:00+00:00 SS8888.SC        False  16660.0  \n", "                          TA8888.ZC         True   5152.0  \n", "                          UR8888.ZC         True   2324.0  \n", "                          V8888.DC          True   5782.0  \n", "                          Y8888.DC          True   9528.0  \n", "\n", "[8738 rows x 5 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["ret"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["factors=engine.get_factors_raw_value()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(8743, 6)\n", "fast_ema\n", "<PERSON>.<PERSON><PERSON>([34, 258])\n", "slow_ema\n", "<PERSON>.<PERSON><PERSON>([34, 258])\n", "buy_signal\n", "<PERSON>.<PERSON><PERSON>([34, 258])\n", "sell_signal\n", "<PERSON>.<PERSON><PERSON>([34, 258])\n", "close\n", "None\n"]}], "source": ["print(engine._dataframe.shape)\n", "for f, v in factors.items():\n", "    print(f)\n", "    if isinstance(v, torch.Tensor):\n", "        print(v.shape)\n", "    else:\n", "        print(v)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["engine._dataframe.to_csv(f\"E:/lab/RoboQuant/pylab/temp/data/df.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["open            0\n", "high            0\n", "low             0\n", "close           0\n", "volume          0\n", "_time_cat_id    0\n", "dtype: int64\n", "                                        open     high      low    close  \\\n", "date                      asset                                           \n", "2021-10-13 04:48:00+00:00 AP8888.ZC   7250.0   7313.0   6670.0   7144.0   \n", "                          PK8888.ZC   8780.0   8856.0   8440.0   8668.0   \n", "                          SF8888.ZC  17450.0  17500.0  15630.0  15630.0   \n", "                          SM8888.ZC  12400.0  12500.0  11200.0  11200.0   \n", "                          UR8888.ZC   3296.0   3296.0   3035.0   3036.0   \n", "...                                      ...      ...      ...      ...   \n", "2022-11-02 04:48:00+00:00 SS8888.SC  16750.0  16960.0  16575.0  16660.0   \n", "                          TA8888.ZC   5180.0   5192.0   5090.0   5152.0   \n", "                          UR8888.ZC   2315.0   2339.0   2283.0   2324.0   \n", "                          V8888.DC    5730.0   5815.0   5672.0   5782.0   \n", "                          Y8888.DC    9350.0   9606.0   9286.0   9528.0   \n", "\n", "                                        volume  _time_cat_id  \n", "date                      asset                               \n", "2021-10-13 04:48:00+00:00 AP8888.ZC   791305.0             0  \n", "                          PK8888.ZC   217905.0             0  \n", "                          SF8888.ZC   470295.0             0  \n", "                          SM8888.ZC   338728.0             0  \n", "                          UR8888.ZC   345660.0             0  \n", "...                                        ...           ...  \n", "2022-11-02 04:48:00+00:00 SS8888.SC   122655.0           257  \n", "                          TA8888.ZC  1920408.0           257  \n", "                          UR8888.ZC   159732.0           257  \n", "                          V8888.DC   1052893.0           257  \n", "                          Y8888.DC    906306.0           257  \n", "\n", "[8743 rows x 6 columns]\n"]}], "source": ["print(engine._dataframe.isna().sum())\n", "print(engine._dataframe)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}