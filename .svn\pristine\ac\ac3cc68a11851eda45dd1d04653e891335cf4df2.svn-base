{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 波动率量化\n", "volatility\n", "- 历史行情\n", "- 波动率指标\n", "- 波动率趋势预测"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "import talib as ta"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# sys.path.append(\"d:/QuantLab\")\n", "# from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs\n", "from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["zxqh = ds.get_block_data('主选期货')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["zxqh=[ds.get_fut_lx_label(x) for x in zxqh]\n", "zxqh"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 主要波动技术指标\n", "- ATR 真实波动幅度均值 [ TRANGE的SMA ]\n", "- NATR 归一化波动幅度均值 [ NATR = (ATR(period) / Close) * 100 ]\n", "- TRANGE 真正的范围\n", "- STDDEV 标准偏差\n", "- 参考：https://www.bookstack.cn/read/talib-zh/func_groups-volatility_indicators.md"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["# 历史行情\n", "len=260\n", "# symbols=['RM2301.ZC', 'AP2301.ZC']\n", "symbols=['RM2301.ZC']\n", "barsize=BarSize.day\n", "assets=[]\n"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [], "source": ["import pytz"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [{"data": {"text/plain": ["<UTC>"]}, "execution_count": 134, "metadata": {}, "output_type": "execute_result"}], "source": ["tz=pytz.timezone('Asia/Shanghai')\n", "pytz.UTC"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["hist=ds.get_history_data(symbols[0], len, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close,BarData.volume], barsize)\n"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["# 历史行情\n", "len=260\n", "symbols=['RM2301.ZC', 'AP2301.ZC']\n", "barsize=BarSize.day\n", "dfs={}\n", "data=None\n", "for symbol in symbols:\n", "    hist=ds.get_history_data(symbol, len, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close,BarData.volume], barsize)\n", "    df=pd.DataFrame(hist, columns=[\"date\", \"open\", \"high\", \"low\", \"close\", \"volume\"])\n", "    # df[\"date\"] -= df[\"date\"] % 8640\n", "    df[\"date\"] = df[\"date\"].apply(datetime.fromtimestamp, args=(pytz.timezone('Asia/Shanghai'),)) #.apply(lambda x: x.strftime(\"%Y-%m-%d\"))\n", "    df.set_index([\"date\"], inplace=True, drop=True)\n", "    dfs[symbol]=df\n"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["df=pd.concat(dfs, sort=False)\n", "df=df.rename_axis(['asset', 'date'])\n", "df=df.swaplevel(0,1).sort_index(level=0)\n"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Timestamp('2021-10-13 15:00:00+0800', tz='Asia/Shanghai'), 'RM2301.ZC')"]}, "execution_count": 138, "metadata": {}, "output_type": "execute_result"}], "source": ["df.index[1]"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2021-10-13 15:00:00+08:00</th>\n", "      <th>AP2301.ZC</th>\n", "      <td>7250.0</td>\n", "      <td>7313.0</td>\n", "      <td>6670.0</td>\n", "      <td>7144.0</td>\n", "      <td>791305.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM2301.ZC</th>\n", "      <td>2681.0</td>\n", "      <td>2690.0</td>\n", "      <td>2611.0</td>\n", "      <td>2620.0</td>\n", "      <td>1094049.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2021-10-14 15:00:00+08:00</th>\n", "      <th>AP2301.ZC</th>\n", "      <td>7088.0</td>\n", "      <td>7168.0</td>\n", "      <td>6958.0</td>\n", "      <td>7122.0</td>\n", "      <td>431021.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM2301.ZC</th>\n", "      <td>2614.0</td>\n", "      <td>2647.0</td>\n", "      <td>2575.0</td>\n", "      <td>2638.0</td>\n", "      <td>1036147.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-15 15:00:00+08:00</th>\n", "      <th>AP2301.ZC</th>\n", "      <td>7140.0</td>\n", "      <td>7230.0</td>\n", "      <td>7013.0</td>\n", "      <td>7040.0</td>\n", "      <td>396390.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-11-02 14:00:00+08:00</th>\n", "      <th>RM2301.ZC</th>\n", "      <td>3122.0</td>\n", "      <td>3145.0</td>\n", "      <td>3049.0</td>\n", "      <td>3069.0</td>\n", "      <td>917986.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2022-11-03 14:00:00+08:00</th>\n", "      <th>AP2301.ZC</th>\n", "      <td>8280.0</td>\n", "      <td>8488.0</td>\n", "      <td>8260.0</td>\n", "      <td>8457.0</td>\n", "      <td>206476.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM2301.ZC</th>\n", "      <td>3055.0</td>\n", "      <td>3070.0</td>\n", "      <td>3015.0</td>\n", "      <td>3038.0</td>\n", "      <td>803684.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2022-11-04 14:00:00+08:00</th>\n", "      <th>AP2301.ZC</th>\n", "      <td>8424.0</td>\n", "      <td>8595.0</td>\n", "      <td>8393.0</td>\n", "      <td>8591.0</td>\n", "      <td>154398.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RM2301.ZC</th>\n", "      <td>3017.0</td>\n", "      <td>3113.0</td>\n", "      <td>3009.0</td>\n", "      <td>3078.0</td>\n", "      <td>899412.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>520 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                       open    high     low   close     volume\n", "date                      asset                                               \n", "2021-10-13 15:00:00+08:00 AP2301.ZC  7250.0  7313.0  6670.0  7144.0   791305.0\n", "                          RM2301.ZC  2681.0  2690.0  2611.0  2620.0  1094049.0\n", "2021-10-14 15:00:00+08:00 AP2301.ZC  7088.0  7168.0  6958.0  7122.0   431021.0\n", "                          RM2301.ZC  2614.0  2647.0  2575.0  2638.0  1036147.0\n", "2021-10-15 15:00:00+08:00 AP2301.ZC  7140.0  7230.0  7013.0  7040.0   396390.0\n", "...                                     ...     ...     ...     ...        ...\n", "2022-11-02 14:00:00+08:00 RM2301.ZC  3122.0  3145.0  3049.0  3069.0   917986.0\n", "2022-11-03 14:00:00+08:00 AP2301.ZC  8280.0  8488.0  8260.0  8457.0   206476.0\n", "                          RM2301.ZC  3055.0  3070.0  3015.0  3038.0   803684.0\n", "2022-11-04 14:00:00+08:00 AP2301.ZC  8424.0  8595.0  8393.0  8591.0   154398.0\n", "                          RM2301.ZC  3017.0  3113.0  3009.0  3078.0   899412.0\n", "\n", "[520 rows x 5 columns]"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["dti = pd.DatetimeIndex(data[\"date\"])"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["data.set_index([dti, \"asset\"], inplace=True, drop=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["data.set_index([\"date\", \"asset\"], inplace=True, drop=True)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "MultiIndex: 520 entries, ('2021-10-13', 'RM2301.ZC') to ('2022-11-04', 'AP2301.ZC')\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   open    520 non-null    float64\n", " 1   high    520 non-null    float64\n", " 2   low     520 non-null    float64\n", " 3   close   520 non-null    float64\n", " 4   volume  520 non-null    float64\n", "dtypes: float64(5)\n", "memory usage: 32.2+ KB\n", "None\n"]}], "source": ["print(data.info())"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["WIN = 20\n", "atr = ta.ATR(hist[:,2], hist[:,3], hist[:,4], WIN)\n", "natr = ta.NATR(hist[:,2], hist[:,3], hist[:,4], WIN)\n", "trange = ta.TRANGE(hist[:,2], hist[:,3], hist[:,4])\n", "stddev = ta.STDDEV(hist[:,4], WIN)\n", "mom = ta.MOM(hist[:,4], WIN)\n"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["rsi = ta.RSI(hist[:,4], WIN)"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [], "source": ["mom = mom/hist[:,4]*100"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [], "source": ["rsi = rsi -50"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-0.62166434, -0.62546692, -0.56695181, -0.54804824, -0.46207543])"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["nstd = nstddev[~np.isnan(nstddev)]\n", "zscore = (nstd - np.mean(nstd))/np.std(nstd)\n", "zscore[-5:]"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(rsi)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Crosssection NATR"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["def crosssection_natr(blkname=\"ZLQH\", barsize=BarSize.day, win=20, length=250):\n", "    count = 0\n", "    total=None\n", "    blocks = ds.get_block_data(blkname)\n", "    for symbol in blocks:\n", "        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)\n", "        if hist.shape[0] < length:\n", "            print(f\"{symbol} len {hist.shape[0]}\")\n", "            continue\n", "        natr = ta.NATR(hist[:,2], hist[:,3], hist[:,4], win)\n", "        if total is None:\n", "            total = natr\n", "        else:\n", "            total += natr\n", "        count += 1\n", "    avg_natr=total/count\n", "    return hist[:, 0].copy(), avg_natr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def block_natrs(blkname=\"ZLQH\", barsize=BarSize.day, win=20, length=250):\n", "    natrs={}\n", "    blocks = ds.get_block_data(blkname)\n", "    for symbol in blocks:\n", "        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)\n", "        if hist.shape[0] < length:\n", "            print(f\"{symbol} len {hist.shape[0]}\")\n", "            continue\n", "        natr = ta.NATR(hist[:,2], hist[:,3], hist[:,4], win)\n", "        natrs[symbol]=natr[-1]\n", "    return natrs"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['20221025 22:05', '20221025 22:10', '20221025 22:15', '20221025 22:20', '20221025 22:25', '20221025 22:30', '20221025 22:35', '20221025 22:40', '20221025 22:45', '20221025 22:50']\n"]}], "source": ["dates, natr = crosssection_natr(blkname='主选期货', barsize=BarSize.min5, win=15, length=150)\n", "fmdates=list(map(lambda x:datetime.fromtimestamp(x).strftime(\"%Y%m%d %H:%M\"), dates.tolist()))\n", "print(fmdates[-10:])"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(natr)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Crosssection STDDEV"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [], "source": ["def crosssection_nstd(blkname=\"ZLQH\", barsize=BarSize.day, win=20, length=250):\n", "    count = 0\n", "    total=None\n", "    blocks = ds.get_block_data(blkname)\n", "    for symbol in blocks:\n", "        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.close], barsize)\n", "        if hist.shape[0] < length:\n", "            print(f\"{symbol} len {hist.shape[0]}\")\n", "            continue\n", "        nstddev = ta.STDDEV(hist[:,1], win) / hist[:,1] * 100\n", "        if total is None:\n", "            total = nstddev\n", "        else:\n", "            total += nstddev\n", "        count += 1\n", "    avg_nstddev=total/count\n", "    return hist[:, 0].copy(), avg_nstddev"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['20221025 23:25', '20221025 23:30', '20221025 23:35', '20221025 23:40', '20221025 23:45']\n"]}], "source": ["dates, nstd = crosssection_nstd(blkname='主选期货'.encode('gbk'), barsize=BarSize.min5, win=15, length=150)\n", "fmdates=list(map(lambda x:datetime.fromtimestamp(x).strftime(\"%Y%m%d %H:%M\"), dates.tolist()))\n", "print(fmdates[-5:])"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiwAAAGfCAYAAAB8wYmvAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy89olMNAAAACXBIWXMAAA9hAAAPYQGoP6dpAABy0UlEQVR4nO3deXhU5dk/8O+Zmcxk38keSAKBsCaRACJSraagtWqtVrRUKG3xVUSrWIu0L9C3vhZQykurFCoWxRYr7U+xWhUXJKIS1hCQfQ0J2ffJOjOZOb8/Zs5JAtkmmZkzy/dzXXNdMHPOyTOHkNxzP/dzP4IoiiKIiIiI3JhK6QEQERER9YcBCxEREbk9BixERETk9hiwEBERkdtjwEJERERujwELERERuT0GLEREROT2GLAQERGR22PAQkRERG6PAQsRERG5Pc1gTtqwYQNefPFFVFRUIDMzEy+99BKmTp3a47HvvPMOfv/73+P8+fMwmUxIT0/H008/jYceekg+RhRFrFy5Eps3b0ZDQwNmzJiBjRs3Ij09fcBjslgsKCsrQ0hICARBGMzbIiIiIhcTRRFNTU1ISEiAStVHHkW001tvvSVqtVpxy5Yt4okTJ8SFCxeK4eHhYmVlZY/H7969W3znnXfEkydPiufPnxfXr18vqtVqcefOnfIxq1evFsPCwsR3331XPHr0qHjXXXeJqampYltb24DHVVJSIgLggw8++OCDDz488FFSUtLn73lBFO3b/HDatGmYMmUKXn75ZQDWzEZycjIef/xxPPvsswO6xnXXXYc77rgDzz33HERRREJCAp5++mn88pe/BAA0NjYiNjYWr7/+Oh544IEBXbOxsRHh4eEoKSlBaGioPW+JiIiIFKLX65GcnIyGhgaEhYX1epxdU0JGoxGHDx/GsmXL5OdUKhVyc3ORn5/f7/miKOLzzz/HmTNnsGbNGgDApUuXUFFRgdzcXPm4sLAwTJs2Dfn5+b0GLAaDAQaDQf57U1MTACA0NJQBCxERkYfpr5zDrqLbmpoamM1mxMbGdns+NjYWFRUVvZ7X2NiI4OBgaLVa3HHHHXjppZfwne98BwDk8+y95qpVqxAWFiY/kpOT7XkrRERE5EFcskooJCQEhYWFOHjwIJ5//nksWbIEeXl5Q7rmsmXL0NjYKD9KSkocM1giIiJyO3ZNCUVHR0OtVqOysrLb85WVlYiLi+v1PJVKhVGjRgEAsrKycOrUKaxatQo333yzfF5lZSXi4+O7XTMrK6vXa+p0Ouh0OnuGT0RERB7KrgyLVqvF5MmTsWvXLvk5i8WCXbt2Yfr06QO+jsViketPUlNTERcX1+2aer0e+/fvt+uaRERE5L3s7sOyZMkSzJ8/Hzk5OZg6dSrWr1+PlpYWLFiwAAAwb948JCYmYtWqVQCstSY5OTkYOXIkDAYDPvzwQ/ztb3/Dxo0bAViLbJ588kn87//+L9LT05Gamorly5cjISEB3//+9x33TomIiMhj2R2wzJkzB9XV1VixYgUqKiqQlZWFnTt3ykWzxcXF3Rq/tLS0YNGiRbhy5QoCAgKQkZGBv//975gzZ458zK9+9Su0tLTg4YcfRkNDA2688Ubs3LkT/v7+DniLRERE5Ons7sPirvR6PcLCwtDY2MhlzURERB5ioL+/uZcQERERuT0GLEREROT2GLAQERGR22PAQkRERG6PAQsRERG5PQYsRER9OFfZhI15F6BvNyk9FCKfxoCFiKgPv33/BNbsPI17/7wXJXWtSg+HyGcxYCEi6oXZIuJIcQMA4FxVM+7589c4Ulyv7KCIfBQDFiKiXpytbEKr0YwgrRrj4kNR02zEA6/swxdnq5UeGpHPYcBCRNSLwpIGAEBmcjj+9ch03JIRA0OHBas+PKXswIh8EAMWIqJeFNqmg7KSwxGk02Dd/ZnQqAScrmjCxepmZQdH5GMYsBAR9eJIibVeJSs5HAAQHqjFDaOiAQAfHa9QalhEPokBCxFRD5raTThXZc2iZA0Pl5+/Y2IcAOCDY+VKDIvIZzFgISLqwTdXGiGKQGJ4AGJC/OXnvzMuDmqVgJPlehTVtCg4QiLfwoCFiKgHR2wFt12zKwAQGaTFDSOjAAAfHmeWhchVGLAQEfVA6r+Sbatf6er2CfEAgA+/YcBC5CoMWIiIriKKorykOauHgGX2+FioBOB4qR7Ftex+S+QKDFiIiK5S2tCGmmYDNCoBExLDrnk9KliH69M4LUTkSgxYiIiuIk0HjY0Phb+fusdjvjvROi30EaeFiFyCAQsR0VX6mg6SzB5vXd589Eoj6lqMLhgVkW9jwEJEdBUpYMm+aoVQV8NCdBgRFQgAOFmmd8GoiHwbAxYioi7ajGYcL20E0HeGBQDGJ4QCAE6WNzp7WEQ+jwGLQkxmC947WoZf7/gGJXVcZUDkLv5dWApDhwVJEQFIjQ7q89hx8baAhRkWIqfTKD0AX6NvN+Fv+Zfxt/zLqNC3AwDaTWasuz9L2YEREURRxOt7iwAA86enQBCEPo8fZ8uwnGDAQuR0DFhcSBRFzPvrAXl+PFCrRqvRjAOX6pQdGBEBAPZdrMPpiiYE+Klxf05yv8ePT7Aueb5Q3Yx2k7nXFUVENHScEnKhL8/VoLCkAYFaNf7ww0x8vfQWqATgSn0byhralB4ekc97fe8lAMAPrktEWKBfv8fHhOgQFaSFRQTOVDQ5e3hEPo0Biwtt/vIiAGDOlGTcOzkJEUFauSnVwSJmWYiUVFLXik9PVgIAfnJDyoDOEQRBnhY6Wc5pISJnYsDiIifL9PjyXA1UAvDTGany81NTIgGA00JECvv7vsuwiMDM9Gikx4YM+LzOOhauFCJyJgYsLvLqV9bsyncnxiM5MlB+fkoqAxYipbUaO/CPA8UABp5dkXClEJFrMGBxgfLGNrxXWAYAWDgzrdtrU2wZlnNVzeyWSaSQ/xwth769AyOiAvHtMTF2nSv1Yjld0QSzRXTG8IgIDFhc4vW9ReiwiJiaGonMqxpRRQZpkR4TDIB1LERKee+o9QPF/TnJUKn6Xsp8tdToYPj7qdBqNKOotsUZwyMiMGBxusu1LXhzvzXV/PBV2RXJVE4LESmmptmAvRdqAAB3Tkqw+3y1SkBGHKeFiJxtUAHLhg0bkJKSAn9/f0ybNg0HDhzo9djNmzdj5syZiIiIQEREBHJzc685vrm5GYsXL0ZSUhICAgIwbtw4bNq0aTBDcyufn67EnS99hab2DoyJDcEtGT2nmhmwECnno2/KYRGBzKQwDI8K7P+EHnClEJHz2R2wbN++HUuWLMHKlStRUFCAzMxMzJ49G1VVVT0en5eXhwcffBC7d+9Gfn4+kpOTMWvWLJSWlsrHLFmyBDt37sTf//53nDp1Ck8++SQWL16M9957b/DvTEEWi4j1n53FT18/BH17B7KHh2PrT6f2mmqW6lhOlDWi2dDhyqES+bz3j5YDAO7MtD+7ImHhLZHz2R2wrFu3DgsXLsSCBQvkTEhgYCC2bNnS4/Hbtm3DokWLkJWVhYyMDLz66quwWCzYtWuXfMzevXsxf/583HzzzUhJScHDDz+MzMzMPjM3BoMBer2+28MdNLaa8PM3DmH9Z+cAAA9dPwLbH56OuDD/Xs9JCA9AcmQALCJw+HK9q4ZK5PPKG9twwFY7dsek+EFfZzxb9BM5nV0Bi9FoxOHDh5Gbm9t5AZUKubm5yM/PH9A1WltbYTKZEBkZKT93ww034L333kNpaSlEUcTu3btx9uxZzJo1q9frrFq1CmFhYfIjObn/NtrOdqpcj7s2fIXPT1dBp1Fh7Q8z8dz3J0Cr6f82S1mWg5wWInKZD45ZsytTUyIRHxYw6OtkxIVCJVjrYaqa2h01PCLqwq6ApaamBmazGbGxsd2ej42NRUVFxYCusXTpUiQkJHQLel566SWMGzcOSUlJ0Gq1uO2227BhwwZ861vf6vU6y5YtQ2Njo/woKSmx56043McnKvCDP+/F5dpWJEUE4O1Hb8B9k5MGfP40Wx3L/ku1zhoiEV3lfdvqoDszB59dAYAArVre2ZlZFiLncOnmh6tXr8Zbb72FvLw8+Pt3TpG89NJL2LdvH9577z2MGDECe/bswWOPPXZNYNOVTqeDTqdz1dD71G4y45f/Ooo2kxkz06PxpweyERGktesa16dFAQCOFDegsdU0oH1MiGjwLte24OiVRqgE4PaJQwtYAGBiYhguVLfgaEmD3b1ciKh/dmVYoqOjoVarUVlZ2e35yspKxMXF9Xnu2rVrsXr1anzyySeYNGmS/HxbWxt+/etfY926dbjzzjsxadIkLF68GHPmzMHatWvtGZ5iPj5Rgab2DiSGB+D1BVPtDlYAYERUEMbEhqDDIuLTU5X9n0BEQ/If23TQjFHRiA4e+oefySMiALAOjchZ7ApYtFotJk+e3K1gViqgnT59eq/nvfDCC3juueewc+dO5OTkdHvNZDLBZDJBpeo+FLVaDYvFYs/wFPP/Dl8BANw7OQlqO5tOdXX7RGvQt/N4uUPGRUS9++qctffKbRP6/rA1UJNHWKd1jxQ3sOMtkRPYvUpoyZIl2Lx5M7Zu3YpTp07h0UcfRUtLCxYsWAAAmDdvHpYtWyYfv2bNGixfvhxbtmxBSkoKKioqUFFRgebmZgBAaGgobrrpJjzzzDPIy8vDpUuX8Prrr+ONN97APffc46C36TxlDW346rz1B9991w28ZqUnt0+wpqX3nK1BU7tpyGMjot5V6K3FsSOHBTvkemPiQhCi06DZ0IHTFaxjIXI0uwMWaapmxYoVyMrKQmFhIXbu3CkX4hYXF6O8vDNDsHHjRhiNRtx3332Ij4+XH12ne9566y1MmTIFc+fOxbhx47B69Wo8//zzeOSRRxzwFp3rnYIrEEVr0exgm05JRscGIy06CEazBZ+f7rmvDRE5RpUtYIkJcUwtnFolIGt4OABOCxE5w6CKbhcvXozFixf3+FpeXl63vxcVFfV7vbi4OLz22muDGYqiRFGUp4N+mDP0ZdWCIOD2iXHYsPsCPvqmAndnJQ75mkR0rWZDB1qMZgBATGjvPZLslTMiEl+eq8GhonrMm57isOsSEfcSGpJDl+tRVNuKQK0atztoHlyaFso7W4VWI7veEjmDlF0J0qoRrHPcYsmcFBbeEjkLA5Yh+Ncha++XOybGI8hBP/TGJ4QiKSIA7SYLvjhT7ZBrElF3lXoDACDWgdkVAMhKDodaJaC0oQ3ljW0OvTaRr2PAMkitxg65S6Y9DeL6IwgCvmvrCfHh8YE14yMi+0jdaIc5qH5FEqTTYGx8CADgUBGzLESOxIBlkPZfrEOL0YzE8AB5t2VHkZZZfn6qEpv3XMQ7BVew72ItRJFLJYkcocpJGRbAWscCcFqIyNFc2unWmxwptv4wuj4tCoIw+N4rPclKCkdCmD/KGtvx/Ien5Ofn5CRjzX2T+jiTiAZCyrA4aoVQV5NHROD1vUU4WMR9wYgciRmWQTpS0gAAyLYtY3QklUrAxh9Pxs9uTMX3sxJw46hoqARg+6ESfPRN703limtb8d/vfoOzlU0OHxORN3FWDQvQWXh7qlyPZgML54kchRmWQbBYRBQWNwBwTsACAJnJ4chM7rz2CztP4895F7Bsxze4bkTENT9oyxvb8ODmfdZiv4Z2/PUnU5wyLiJvIGdYQh2fYYkPC0BieABKG9pQWNyAG9OjHf41iHwRMyyDcL66GU2GDgRq1RgTG+KSr/lk7mhMSAxFQ6sJz/y/Y93qWepajHjorwdQ2mBdlfDl+Rp+siPqg1TDEhPi+AwL0Lmv0KHLnBYichQGLIMg1a9MSgqDRu2aW6jVqLB+ThZ0GhX2nK3G/7x/ErtOVeJ4aSMWvHYA56uaERfqj8TwABg7uCSaqC9VTdKUkHN2fJemhQptU8dENHQMWAbhiDwdFOHSrzsqJgS//u5YAMDre4vws62H8L2XvsLRK42ICPTD338+Fd+bZF0S/fEJLokm6kmLoUPOQDqyy21Xo2Ks+xNdrm11yvWJfBFrWAZBDli61Ji4yrzpI6ASgK/P1+JKQyuu1LdBoxLw1/lTMComBLMnxOEvey5i9+********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(nstd)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Z-Score"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["def z<PERSON>re(series):\n", "    nonans = series[~np.isnan(series)]\n", "    zscore = (nonans - np.mean(nonans))/np.std(nonans)\n", "    return zscore"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-1.8177213838850605\n"]}], "source": ["print(zscore(nstd))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Crosssection MOM"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def crosssection_mom(blkname=\"ZLQH\", barsize=BarSize.day, win=20, length=250):\n", "    count = 0\n", "    total=None\n", "    blocks = ds.get_block_data(blkname)\n", "    for symbol in blocks:\n", "        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.close], barsize)\n", "        if hist.shape[0] < length:\n", "            print(f\"{symbol} len {hist.shape[0]}\")\n", "            continue\n", "        mom = ta.MOM(hist[:,1], win) / hist[:,1] * 100\n", "        if total is None:\n", "            total = mom\n", "        else:\n", "            total += mom\n", "        count += 1\n", "    avg_mom=total/count\n", "    return hist[:, 0].copy(), avg_mom"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MOM Z-Score: -0.22138303446359966\n", "['20221020 15:00', '20221021 15:00', '20221024 14:00', '20221025 15:00', '20221026 15:00']\n"]}], "source": ["dates, mom = crosssection_mom(blkname='主选期货', barsize=BarSize.day, win=15, length=250)\n", "fmdates=list(map(lambda x:datetime.fromtimestamp(x).strftime(\"%Y%m%d %H:%M\"), dates.tolist()))\n", "# print(f\"MOM Z-<PERSON><PERSON>score(mom)core: {zscore}\")\n", "print(fmdates[-5:])"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plt.plot(mom)\n", "plt.plot(<PERSON><PERSON><PERSON>(mom))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 截面指标择时检验"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["(120, 5)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["wfi_hist=ds.get_history_data(\"WFI999.IX\", 120, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], BarSize.day)\n", "wfi_hist.shape"]}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "4442a059423b1d8cb2d566f1d7a9e596fd1852f4b9b9e9d6f69b72f942b31330"}}}, "nbformat": 4, "nbformat_minor": 2}