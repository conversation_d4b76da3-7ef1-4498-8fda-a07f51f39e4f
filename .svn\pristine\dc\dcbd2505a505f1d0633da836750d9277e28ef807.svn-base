import pandas as pd
import numpy as np
from pyqlab.const import MODEL_FUT_CODES
from pyqlab.utils.timefeatures import time_features, datetime_features


class Pipeline:
    bar_set = []

    def __init__(self, data_path, market, block_name, period,
                  start_year, end_year,
                  start_date="", end_date="",
                  block_size=15, timeenc=2,
                  sel_codes=None, is_bar=True):
        self.data_path = data_path  # d:/RoboQuant2/store/barenc/sf
        self.market = market
        self.block_name = block_name
        self.period = period
        self.start_year = start_year
        self.end_year = end_year
        self.start_date = start_date
        self.end_date = end_date
        self.block_size = block_size
        self.code_size = len(MODEL_FUT_CODES)
        self.is_sf = False
        self.sel_codes =sel_codes
        self.is_bar = is_bar
        self.fut_codes_dict = {c: i for i, c in enumerate(MODEL_FUT_CODES)}
        self.timeenc = timeenc
        self.context_cols = []
        Pipeline.get_vocab()
        
    @staticmethod
    def get_vocab():
        if len(Pipeline.bar_set) == 0:
            for i in range(-12, 13):
                for j in range(-12, 13):
                    for k in range(0, 8):
                        for l in range(0, 8):
                            Pipeline.bar_set.append(f'{i}|{j}|{k}|{l}')
            # 添加特殊标记token
            Pipeline.bar_set.append('-88|0|0|0')  # 交易日间隔标记
            Pipeline.bar_set.append('-99|0|0|0')  # 假期(周末/节假日)间隔标记
            # bar_set = sorted(bar_set) 为了与平台所有的一致，不能排序
        print(f"bar_set: {len(Pipeline.bar_set)}  {Pipeline.bar_set[-1]}")
        return Pipeline.bar_set
    
    @staticmethod
    def get_bar_value(bar_index):
        val = Pipeline.bar_set[bar_index]
        change, entity, upline, downline = val.split('|')
        return int(change), int(entity), int(upline), int(downline)
    
    def _load_data(self):
        df = pd.DataFrame()
        if self.is_bar:
            if self.period == 'day' or self.block_name == 'sf':
                df = pd.read_csv(f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}.csv', header=0)
                # 获取context_cols
                if len(df.columns.tolist()) > 3:
                    self.context_cols = df.columns.tolist()[3:]
                    print(f"context_cols: {self.context_cols}")

                print(df.head())
                if self.block_name == 'sf':
                    print("sf min5:", df.shape)
                    df = df[(pd.to_datetime(df['datetime'], unit='s').dt.year >= self.start_year) & (pd.to_datetime(df['datetime'], unit='s').dt.year <= self.end_year)]
                    print("sf min5:", df.shape)
            elif self.period == 'min5' or self.period == 'min1':
                for y in range(self.start_year, self.end_year + 1):
                    df2 = pd.read_csv(f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}_{y}.csv', header=0)
                    df = pd.concat([df, df2], axis=0, ignore_index=True)
                # df['datetime']是timestamp， 删除每日时间在00:00:00到06:00:00之间的数据
                # 获取context_cols
                if len(df.columns.tolist()) > 3:
                    self.context_cols = df.columns.tolist()[3:]
                    print(f"context_cols: {self.context_cols}")

                print(df)
                # print(df.shape)
                # df = df[(pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 6]
                # print(df.shape)
            else:
                raise ValueError(f"Invalid period: {self.period}")
        else:
            for y in range(self.start_year, self.end_year + 1):
                df2 = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
                df = pd.concat([df, df2], axis=0, ignore_index=True)
            df.columns = ['symbol', 'datetime', 'change', 'entity', 'upline', 'downline']
        # print(df['symbol'].unique())
        if "IF" in df['symbol'].values:
            self.is_sf = True

        print(f"load data shape: {df.shape}")
        if self.start_date != "" and self.end_date !="" and self.start_date < self.end_date:
            start_time = int(pd.Timestamp(self.start_date).timestamp())
            end_time = int(pd.Timestamp(self.end_date).timestamp())
            df = df[(df['datetime'] >= start_time) & (df['datetime'] <= end_time)]
            print(f"after time filter: {df.shape}")

        return df
    

    @staticmethod
    def to_bar(df):
        """
        生成bar值的token数据，code，timestamp，bar token embedding index
        """
        if len(Pipeline.bar_set) == 0:
            Pipeline.get_vocab()
        # 将列change的值限定在-12到12之间
        df.loc[(df['change'] <= -30) | (df['change'] >= 30), 'change'] = -99
        df.loc[(df['change'] < -12) & (df['change'] > -30), 'change'] = -12
        df.loc[(df['change'] > 12) & (df['change'] < 30), 'change'] = 12
        df.loc[df['entity'] < -12, 'entity'] = -12
        df.loc[df['entity'] > 12, 'entity'] = 12
        df.loc[df['upline'] > 7, 'upline'] = 7
        df.loc[df['downline'] > 7, 'downline'] = 7

        # 处理特殊标记token（-99表示非交易日间隔）
        # 将特殊标记转换为特定的bar值
        holiday_mask = df['change'] == -99
        if holiday_mask.any():
            # 为了避免与现有bar冲突，使用一个特殊的组合
            # 使用-99|0|0|0作为特殊标记
            df.loc[holiday_mask, 'change'] = -99
            df.loc[holiday_mask, 'entity'] = 0
            df.loc[holiday_mask, 'upline'] = 0
            df.loc[holiday_mask, 'downline'] = 0

              
        df.insert(2, 'bar', df['change'].astype(str) + '|' + df['entity'].astype(str) + '|' + df['upline'].astype(str) + '|' + df['downline'].astype(str))
        # 对于特殊标记，如果不在bar_set中，则添加
        # special_bars = df.loc[holiday_mask, 'bar'].unique()
        # for special_bar in special_bars:
        #     if special_bar not in Pipeline.bar_set:
        #         Pipeline.bar_set.append(special_bar)
                
        df['bar'] = df['bar'].apply(lambda x: Pipeline.bar_set.index(x))
        df['bar'] = df['bar'].astype(int)
        df.drop(columns=['change', 'entity', 'upline', 'downline'], inplace=True)
        return df
    
    def _to_time_tf(self, df):
        # 将date列由timestamp转换为东8区日期时间
        df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)
        
        # 创建一个副本以保存原始的bar值，用于后续识别特殊标记
        if 'bar' in df.columns:
            df['is_holiday'] = False
            # 获取特殊标记的索引
            if hasattr(Pipeline, 'bar_set'):
                holiday_bar_index = Pipeline.bar_set.index('-99|-99|0|0') if '-99|-99|0|0' in Pipeline.bar_set else -1
                if holiday_bar_index != -1:
                    df.loc[df['bar'] == holiday_bar_index, 'is_holiday'] = True
        
        if self.timeenc == 0:
            # 使用更高效的向量化操作替代apply
            df['Month'] = df.datetime.dt.month - 1
            df['Day'] = df.datetime.dt.day - 1
            # 星期日设为0为一周的开端,weekday()返回0-6，对应星期一到星期日
            df['DayOfWeek'] = np.where(df.datetime.dt.weekday < 6, df.datetime.dt.weekday + 1, 0)
            df['Hour'] = df.datetime.dt.hour
            df['Minute'] = df.datetime.dt.minute // 5
            
            # 对于特殊标记（非交易日），设置特殊的时间特征
            if 'is_holiday' in df.columns:
                # 对于非交易日，设置特殊的时间特征值
                # 使用-1表示这是一个非交易日
                df.loc[df['is_holiday'], 'DayOfWeek'] = -1
                
                # 删除临时列
                df.drop(columns=['is_holiday'], inplace=True)
            
            # df = df.drop(['date'], axis=1).values
        elif self.timeenc == 1:
            df_stamp= time_features(pd.to_datetime(df['datetime'].values), freq='t').transpose(1, 0)
            df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])
            df = pd.concat([df, df_tf], axis=1)
            
            # 对于特殊标记（非交易日），设置特殊的时间特征
            if 'is_holiday' in df.columns:
                # 对于非交易日，设置特殊的DayOfWeek值
                df.loc[df['is_holiday'], 'DayOfWeek'] = -1
                
                # 删除临时列
                df.drop(columns=['is_holiday'], inplace=True)
        elif self.timeenc == 2:
            df_stamp= datetime_features(pd.to_datetime(df['datetime'].values)) #.transpose(1, 0)
            df_tf = pd.DataFrame(df_stamp, columns=
                                 ['hour_sin', 'hour_cos',
                                'dow_sin', 'dow_cos',
                                'month_sin', 'month_cos',
                                'doy_sin', 'doy_cos'])
            df = pd.concat([df, df_tf], axis=1)
        else:
            raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
        return df

    def _prepare_data(self):
        # 将列change的值限定在-12到12之间
        df=self._load_data()
        if self.sel_codes is not None and self.market == 'fut' and self.block_name == 'main':
            df = df[df['symbol'].isin(self.sel_codes)]

        df['code_encoded'] = df['symbol'].apply(lambda x: self.fut_codes_dict[x])
        df.drop(columns=['symbol'], inplace=True)
        # move code_encoded to the first column
        cols = df.columns.tolist()
        cols = cols[-1:] + cols[:-1]
        df = df[cols]
        # TODO: remove this line
        # print(f"{df.shape}=====remove========")
        # df = df.iloc[-3000:]
        # print(f"{df.shape}=====remove========")
        df = df.sort_values(by=['code_encoded', 'datetime']).reset_index(drop=True)

        if not self.is_bar:
            df=self._to_bar(df)

        df=self._to_time_tf(df)
        if self.timeenc == 0:
            df = df[['code_encoded', 'bar', 'Month', 'Day', 'DayOfWeek', 'Hour', 'Minute'] + self.context_cols]
        elif self.timeenc == 1:
            df = df[['code_encoded', 'bar', 'MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear'] + self.context_cols]
        elif self.timeenc == 2:
            df = df[['code_encoded', 'bar', 'hour_sin', 'hour_cos', 'dow_sin', 'dow_cos', 'month_sin', 'month_cos', 'doy_sin', 'doy_cos'] + self.context_cols]
        else:
            raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")

        return df
    
    def _convert_to_bar_file(self):
        for y in range(self.start_year, self.end_year + 1):
            df = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
            df.columns = ['symbol', 'datetime', 'change', 'entity', 'upline', 'downline']
            df = Pipeline.to_bar(df)
            df.to_csv(f'{self.data_path}/bar_{y}.csv', index=False, header=True)
            print(f'bar_{y}.csv saved.')
    
    def get_data(self):
        df = self._prepare_data()
        print('============================')
        print(f'code_size: {self.code_size}, bar_size: {len(self.get_vocab())}')
        print(f'df shape: {df.shape}')
        print(f'df head: {df.head()}')
        print('============================')
        return df
        """
        # dataframe group by symbol to ndarray
        df = df.groupby('code_encoded')
        data = []
        data_mark = []
        for _, group in df:
            for i in range(0, len(group) - self.block_size, self.step_size):
                data.append(group.iloc[i:i+self.block_size+1, :2].values)
                data_mark.append(group.iloc[i:i+self.block_size+1, 2:].values)
        data = np.array(data)
        data_mark = np.array(data_mark)
        return data, data_mark
        """

if __name__ == '__main__':
    data_path = 'f:/featdata/barenc/db2'
    start_year = 2024
    end_year = 2024
    pipeline = Pipeline(data_path,
                        'fut',
                        'sf',
                        'min5',
                        start_year, end_year)
    # pipeline._convert_to_bar_file()
    df = pipeline.get_data()
    print(df.head())
