# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
"""
Handler对原始数据做处理，按需提供给模型
"""
# coding=utf-8
import abc
import bisect
import logging
import warnings
from inspect import getfullargspec
from typing import Callable, Dict, Union, Tuple, List, Iterator, Optional

import pandas as pd
import numpy as np
import json
from pprint import pprint
from copy import deepcopy
from sklearn.model_selection import KFold
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号
from pyqlab.utils import init_instance_by_config

from pathlib import Path
from pyqlab.data.loader import AHFDataLoader
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES, SF_FUT_CODES, CATEGORY_FACTOR_NAMES, CATEGORY_FACTOR_NUMS
from . import loader as data_loader_module
from pyqlab.utils.timefeatures import time_features
from functools import partial
from sklearn import model_selection
warnings.filterwarnings("ignore")

class DataHandler():
    """
    加载raw data进行加工处理的地方
    原则：按需读取数据，尽量减少内存占用
    """
    def __init__(
        self,
        instruments=None,
        start_time='',
        end_time='',
        data_loader: Union[dict, str, AHFDataLoader]=None,
        valid_fold = 5,
        win = 10,
        step = 1,
        filter_win = 0,
        is_filter_extreme = False,
        is_normal = True,
        init_data=False,
        fetch_orig=True,
        verbose=False,
        timeenc=None,
        **kwargs
    ):
        self.win = win
        self.step = step
        self.filter_win = filter_win
        self.is_filter_extreme = is_filter_extreme # 是否过滤极端值
        self.valid_fold = valid_fold
        self.is_normal = is_normal # 是否归一化
        self.x_data = []
        self.y_data = []
        self.encoded_data = []
        self.direct = 'long'
        self.data_path = ''
        self.sel_lf_names=[]
        self.sel_sf_names=[]
        self.sel_mf_names=[]
        self.sel_ct_names=[]
        self.ct_cat_cols_names = []
        self.ct_cat_num_embeds = [64,]
        self.feat_names = []
        self.verbose = verbose
        self.timeenc = timeenc
        self.ins_nums = tuple()
        # self.le = LabelEncoder()
        self.start_time = start_time
        self.end_time = end_time
        self.data_loader = data_loader
        self.instruments = instruments
        print(f"DataHandlerAHF init: {instruments}, {start_time}, {end_time}")

        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        # self.logger = get_module_logger("DataHandlerAHF")
        # super().__init__(
        #     instruments=instruments,
        #     start_time=start_time,
        #     end_time=end_time,
        #     data_loader=data_loader,
        #     init_data=init_data,
        #     fetch_orig=fetch_orig,
        # )
                # Setup data loader
        assert data_loader is not None  # to make start_time end_time could have None default value

        # what data source to load data
        print(f"DataHandler init: {data_loader}")
        self.data_loader = init_instance_by_config(
            data_loader,
            None if (isinstance(data_loader, dict) and "module_path" in data_loader) else data_loader_module,
            accept_types=AHFDataLoader,
        )
        self.fetch_orig = fetch_orig
        if init_data:
            self.setup_data()

        self.lb_df = pd.DataFrame()
        self.ft_df = pd.DataFrame()

    def standardize(self, group, means, stds):
        code = group.name
        mean = means.loc[code]
        std = stds.loc[code]
        group = (group - mean) / std
        return group
    
    def config(self, **kwargs):
        if "data_loader" in kwargs and isinstance(self.data_loader, AHFDataLoader):
            #self.data_loader.config(**kwargs["data_loader"]["kwargs"])
            # print(">>>> data_handler")
            # pprint(kwargs)
            kwargs2 = deepcopy(kwargs)
            kwargs2.pop("data_loader")

            # TODO: 对DataHandler新的参数config进行修改，使其支持直接传入参数
            attr_list = {"win", "step", "direct", 
                        "sel_lf_names", "sel_sf_names", "sel_mf_names", "sel_ct_names", "filter_win",
                        "is_filter_extreme", "is_normal", "verbose"}
            for k, v in kwargs2["kwargs"].items():
                if k in attr_list:
                    setattr(self, k, v)
            kwargs2.pop("kwargs")
            # super().config(**kwargs2)

        self.lf_mean = pd.read_csv(f'{self.data_loader.data_path}/lf_mean.csv', index_col=0)
        self.lf_std = pd.read_csv(f'{self.data_loader.data_path}/lf_std.csv', index_col=0)
        self.sf_mean = pd.read_csv(f'{self.data_loader.data_path}/sf_mean.csv', index_col=0)
        self.sf_std = pd.read_csv(f'{self.data_loader.data_path}/sf_std.csv', index_col=0)
        self.mf_mean = pd.read_csv(f'{self.data_loader.data_path}/mf_mean.csv', index_col=0)
        self.mf_std = pd.read_csv(f'{self.data_loader.data_path}/mf_std.csv', index_col=0)
        self.ct_mean = pd.read_csv(f'{self.data_loader.data_path}/ct_mean.csv', index_col=0)
        self.ct_std = pd.read_csv(f'{self.data_loader.data_path}/ct_std.csv', index_col=0)

    def _long_factor_select(self, n):
        if len(self.sel_lf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_lf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _short_factor_select(self, n):
        if len(self.sel_sf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_sf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _main_factor_select(self, n):
        if len(self.sel_mf_names) == 0:
            return 0
        if ALL_FACTOR_NAMES[n] in self.sel_mf_names:
            if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
                return 2
            else:
                return 1
        return 0

    def _context_select(self, n):
        if len(self.sel_ct_names) == 0:
            return 0
        if SNAPSHOT_CONTEXT[n] in self.sel_ct_names:
            return 1
        return 0

    def _context_select_not_cat(self, n):
        if len(self.sel_ct_names) == 0:
            return 0
        if self.win > 0:
            if SNAPSHOT_CONTEXT[n] in self.sel_ct_names\
                and SNAPSHOT_CONTEXT[n] not in self.ct_cat_cols_names:
                return 1
        else:
            if SNAPSHOT_CONTEXT[n] in self.sel_ct_names:
                return 1
        return 0

    def _context_cat_select(self, n):
        if len(self.ct_cat_cols_names) == 0 or self.win == 0:
            return 0
        if SNAPSHOT_CONTEXT[n] in self.ct_cat_cols_names:
            return 1
        return 0

    def _factor_select_name(self, sel_list):
        sel_name=[]
        if len(sel_list) == 0:
            return sel_name
        for n in range(len(ALL_FACTOR_NAMES)):
            if sel_list[n] > 0:
                sel_name.append(ALL_FACTOR_NAMES[n])
        return sel_name

    def _context_select_name(self, sel_list):
        sel_name=[]
        if len(sel_list) == 0:
            return sel_name
        for n in range(len(SNAPSHOT_CONTEXT)):
            if sel_list[n] > 0:
                sel_name.append(SNAPSHOT_CONTEXT[n])
        return sel_name

    def _get_factor_cols(self, factor_type="lf", is_all=False):
        """
        因子列名称
        """
        col_names = []
        sel_names = []
        if is_all:
            if factor_type == "lf" or factor_type == "sf" or factor_type == "mf":
                sel_names = ALL_FACTOR_NAMES
            elif factor_type == "ct":
                sel_names = SNAPSHOT_CONTEXT
        else:
            if factor_type == "lf":
                sel_names = self.sel_lf_names
            elif factor_type == "sf":
                sel_names = self.sel_sf_names
            elif factor_type == "mf":
                sel_names = self.sel_mf_names
            elif factor_type == "ct":
                sel_names = self.sel_ct_names
            else:
                raise ValueError(f"factor_type {factor_type} is not supported")            

        if factor_type == "lf" or factor_type == "sf" or factor_type == "mf":
            for name in sel_names:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")
        elif factor_type == "ct":
            col_names = sel_names

        return col_names
    
    def _recover_factor_cols(self):
        # 重新生成因子名称，以保证因子顺序与系统一致 
        f_sel = {}
        f_sel['slow'] = [self._long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [self._short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['main'] = [self._main_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [self._context_select(n) for n in range(len(SNAPSHOT_CONTEXT))]

        self.sel_lf_names = self._factor_select_name(f_sel['slow'])
        self.sel_sf_names = self._factor_select_name(f_sel['fast'])
        self.sel_mf_names = self._factor_select_name(f_sel['main'])
        self.sel_ct_names = self._context_select_name(f_sel['context'])
        self.ct_cat_cols_names = [col for col in self.sel_ct_names if col in CATEGORY_FACTOR_NAMES]
        self.ins_nums = (sum(f_sel['slow']),
                sum(f_sel['fast']),
                sum(f_sel['main']),
                sum(f_sel['context']) - len(self.ct_cat_cols_names))

    def _get_ins_nums(self):
        return self.ins_nums
    
    def _dump_input_param_json(self, save_path: str = None, model_type=0, seq_len=0, label_len=0, pred_len=0):
        """
        注意：两边特征向量的顺序要一致
        """
        #if self.model_name_suff == "":
        #    return
        f_sel = {}
        f_sel['model_type'] = model_type
        f_sel['seq_len'] = seq_len
        f_sel['label_len'] = label_len
        f_sel['pred_len'] = pred_len
        f_sel['timeenc'] = self.timeenc
        # f_sel['codes'] = self.le.classes_.tolist()
        f_sel['codes'] = sorted(MAIN_FUT_CODES + SF_FUT_CODES)
        # f_sel['cat_cols'] = self.ct_cat_cols_names
        f_sel['slow'] = [self._long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['fast'] = [self._short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['main'] = [self._main_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [self._context_select_not_cat(n) for n in range(len(SNAPSHOT_CONTEXT))]
        f_sel['context_cat'] = [self._context_cat_select(n) for n in range(len(SNAPSHOT_CONTEXT))]
        # f_sel['codes'] = sorted(MAIN_FUT_CODES)# sorted(lb_df.code.unique().tolist())

        # 重新生成因子名称，以保证因子顺序与系统一致 
        self.sel_lf_names = self._factor_select_name(f_sel['slow'])
        self.sel_sf_names = self._factor_select_name(f_sel['fast'])
        self.sel_mf_names = self._factor_select_name(f_sel['main'])
        self.sel_ct_names = self._context_select_name(f_sel['context'])

        all_col_names = self._get_factor_cols("lf", is_all=True)
        col_names = self._get_factor_cols("lf")
        f_sel['lf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self._get_factor_cols("sf", is_all=True)
        col_names = self._get_factor_cols("sf")
        f_sel['sf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self._get_factor_cols("mf", is_all=True)
        col_names = self._get_factor_cols("mf")
        f_sel['mf_sel_index'] = [all_col_names.index(name) for name in col_names]

        all_col_names = self._get_factor_cols("ct", is_all=True)
        col_names = self._get_factor_cols("ct")
        f_sel['ct_sel_index'] = [all_col_names.index(name) for name in col_names]

        f_sel['lf_len'] = sum(f_sel['slow'])
        f_sel['sf_len'] = sum(f_sel['fast'])
        f_sel['mf_len'] = sum(f_sel['main'])
        f_sel['ct_len'] = sum(f_sel['context'])
        f_sel['cat_len'] = sum(f_sel['context_cat'])

        assert f_sel['lf_len'] == len(f_sel['lf_sel_index'])
        assert f_sel['sf_len'] == len(f_sel['sf_sel_index'])
        assert f_sel['mf_len'] == len(f_sel['mf_sel_index'])
        assert f_sel['ct_len'] == len(f_sel['ct_sel_index'])
    
        if self.win == 0:
            f_sel["input_dim"] = 1
        else:
            f_sel["input_dim"] = 2
        f_sel["code_encoding"] = 2 # 1.onehot 2.标签编码
        f_sel["win"] = self.win
        f_sel["filter_win"] = self.filter_win
        f_sel["step"] = self.step

        if save_path is not None:
            with open(save_path, 'w') as save_path:
                json.dump(f_sel, save_path)
        return json.dumps(f_sel)

    
    # TODO: 优化数据处理，减少内存占用
    # 当数据太大时，会出现内存不足的情况
    # 1. 将数据集分成多个子集，分集训练模型
    # 2. 及时释放内存
    # 3. 增加对树形模型的支持：a.不需要归一化 b.分类特征数据仅需要标签编码
    # 4. 增加时间维度，hour，dayofweek
    # 5. 增加Conv2d模型的支持，增加range、min5，day等维度特征
    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AHFDataLoader):
            raise ValueError("DataHandlerAHF init parameter data_loader is not AHFDataLoader")

        # 去重整序因子名称
        self._recover_factor_cols()


        # 0. 加载原始数据
        lf_df, sf_df, mf_df, ct_df = self.data_loader.load(self.instruments, self.start_time, self.end_time)
        if not enable_cache:
            self.data_loader.clear_data()
        if self.start_time != "" and self.end_time !="" and self.start_time < self.end_time:
            start_time = int(pd.Timestamp(self.start_time).timestamp())
            end_time = int(pd.Timestamp(self.end_time).timestamp())
            lf_df = lf_df[(lf_df['date'] >= start_time) & (lf_df['date'] <= end_time)]
            sf_df = sf_df[(sf_df['date'] >= start_time) & (sf_df['date'] <= end_time)]
            mf_df = mf_df[(mf_df['date'] >= start_time) & (mf_df['date'] <= end_time)]
            ct_df = ct_df[(ct_df['date'] >= start_time) & (ct_df['date'] <= end_time)]
            print(lf_df.shape, sf_df.shape, mf_df.shape, ct_df.shape)
        lf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
        sf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
        mf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
        ct_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
        print("Clear before:", lf_df.shape, sf_df.shape, mf_df.shape, ct_df.shape)
        # 1. 清洗数据
        lf_df.fillna(0.0, inplace=True)
        sf_df.fillna(0.0, inplace=True)
        mf_df.fillna(0.0, inplace=True)
        ct_df.fillna(0.0, inplace=True)
        sf_df['change'] = sf_df['change'] * 100.0
        lf_df['change'] = lf_df['change'] * 100.0
        if 'RSI_2' in lf_df.columns:
            # 对于涨跌幅过大的数据，过滤掉
            condition = (lf_df['RSI_2'] != 0) & (lf_df['change'] <= 7.0) & (lf_df['change'] >= -7.0)
            lf_df = lf_df[condition]
            sf_df = sf_df[condition]
            mf_df = mf_df[condition]
            ct_df = ct_df[condition]
        if 'RSI_2' in sf_df.columns:
            # 对于涨跌幅过大的数据，过滤掉
            condition = (sf_df['RSI_2'] != 0) & (sf_df['change'] <= 1.0) & (sf_df['change'] >= -1.0)
            lf_df = lf_df[condition]
            sf_df = sf_df[condition]
            mf_df = mf_df[condition]
            ct_df = ct_df[condition]
        if 'RSI_2' in mf_df.columns:
            condition = (mf_df['RSI_2'] != 0)
            lf_df = lf_df[condition]
            sf_df = sf_df[condition]
            mf_df = mf_df[condition]
            ct_df = ct_df[condition]
        if 'FAST_QH_NATR_ZSCORE' in ct_df.columns and len(ct_df) > 0 and "IF" not in ct_df['code'].values:
            condition = (ct_df['FAST_QH_NATR_ZSCORE'] != 0.0)
            lf_df = lf_df[condition]
            sf_df = sf_df[condition]
            mf_df = mf_df[condition]
            ct_df = ct_df[condition]
        lf_df.reset_index(drop=True, inplace=True)
        sf_df.reset_index(drop=True, inplace=True)
        mf_df.reset_index(drop=True, inplace=True)
        ct_df.reset_index(drop=True, inplace=True)
        print("Clear after: ", lf_df.shape, sf_df.shape, mf_df.shape, ct_df.shape)

        if not sf_df.empty:
            # 2. 导出特征数据筛选配置文件
            # sf_df['code_encoded'] = self.le.fit_transform(sf_df['code'].values)
            fut_codes_dict = {code: i for i, code in enumerate(sorted(MAIN_FUT_CODES + SF_FUT_CODES))}
            sf_df['code_encoded'] = sf_df['code'].apply(lambda x: fut_codes_dict[x])

            # 3. 生成标签
            sf_df['long_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.2 else 0)
            sf_df['short_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.2 else 0)
            if 'BAR_LENGTH_2' in sf_df.columns:
                sf_df['bar_length'] = sf_df['BAR_LENGTH_2']
            else:
                sf_df['bar_length'] = 0
            sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 0 if x < -0.25 else 2 if x > 0.25 else 1)

            # 4. 归一化
            col_names = self._get_factor_cols(factor_type="lf")
            if len(col_names) > 0:
                if self.is_normal:
                    lf_df = lf_df[col_names + ['code']]
                    df_mean = self.lf_mean[col_names]
                    df_std = self.lf_std[col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = lf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    lf_df[col_names] = df_standardized[col_names]
                    lf_df = lf_df[['code', 'date'] + col_names]
                else:
                    lf_df = lf_df[['code', 'date'] + col_names]
            else:
                lf_df = pd.DataFrame()

            col_names = self._get_factor_cols(factor_type="sf")
            if len(col_names) > 0 and self.is_normal:
                df_mean = self.sf_mean[col_names]
                df_std = self.sf_std[col_names]
                partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                df_standardized = sf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                df_standardized.fillna(0.0, inplace=True)
                df_standardized.reset_index(drop=False, inplace=True)
                sf_df[col_names] = df_standardized[col_names]
            if len(col_names) > 0:
                sf_df = sf_df[col_names + ['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'label', 'bar_length']]
            else:
                sf_df = sf_df[['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'label', 'bar_length']]

            col_names = self._get_factor_cols(factor_type="mf")
            if len(col_names) > 0:
                if self.is_normal:
                    df_mean = self.mf_mean[col_names]
                    df_std = self.mf_std[col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = mf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    mf_df[col_names] = df_standardized[col_names]
                    mf_df = mf_df[['code', 'date'] + col_names]
                else:
                    mf_df = mf_df[['code', 'date'] + col_names]
            else:
                mf_df = pd.DataFrame()

            ct_embedding_df = pd.DataFrame()
            col_names = self._get_factor_cols(factor_type="ct")
            if len(col_names) > 0:
                if self.is_normal:
                    # self.ct_cat_cols_names = [col for col in col_names if col in CATEGORY_FACTOR_NAMES]
                    col_names = [col for col in col_names if col not in self.ct_cat_cols_names]
                    if len(self.ct_cat_cols_names) > 0:
                        for col in self.ct_cat_cols_names:
                            if col != 'DAYOFWEEK' and col != 'HOUR':
                                ct_df[col] = ct_df[col] + 4
                        if 'HOUR' in self.ct_cat_cols_names:
                            hour_mapping = {0:0, 1:1, 2:2, 9:3, 10:4, 11:5, 13:6, 14:7, 21:8, 22:9, 23:10}
                            ct_df['HOUR'] = ct_df['HOUR'].map(hour_mapping)
                        ct_embedding_df = ct_df[self.ct_cat_cols_names]
                        # print(ct_embedding_df)
                        ct_embedding_df.fillna(0.0, inplace=True)
                        ct_embedding_df = ct_embedding_df.astype(np.int16)
                        ct_embedding_df.reset_index(drop=True, inplace=True)
                        # ct_cat_cols_names在CATEGORY_FACTOR_NAMES中的位置对应CATEGORY_FACTOR_NUMS中的值
                        pos = [CATEGORY_FACTOR_NAMES.index(col) for col in self.ct_cat_cols_names]
                        self.ct_cat_num_embeds = [72] + [CATEGORY_FACTOR_NUMS[i] for i in pos]

                if self.is_normal and len(col_names) > 0 and "IF" not in ct_df['code'].values:
                    df_mean = self.ct_mean[col_names]
                    df_std = self.ct_std[col_names]
                    partial_func = partial(self.standardize, means=df_mean, stds=df_std)
                    df_standardized = ct_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
                    df_standardized.fillna(0.0, inplace=True)
                    df_standardized.reset_index(drop=False, inplace=True)
                    # df_standardized.sort_values(by=['code'], inplace=True)
                    ct_df[col_names] = df_standardized[col_names]
                    del df_mean, df_std, df_standardized

                if not self.is_normal and self.win > 0:
                    # 对于用GBDT模型的情况，不需要归一化和分类特征进行标签编码
                    cat_cols_names = [col for col in col_names if col in CATEGORY_FACTOR_NAMES]
                    for col in cat_cols_names:
                        if col != 'DAYOFWEEK' and col != 'HOUR':
                            ct_df[col] = ct_df[col] + 4
                    ct_df = ct_df[['code', 'date'] + col_names]
                    if "HOUR" in cat_cols_names:
                        hour_mapping = {0:0, 1:1, 2:2, 9:3, 10:4, 11:5, 13:6, 14:7, 21:8, 22:9, 23:11}
                        ct_df['HOUR'] = ct_df['HOUR'].map(hour_mapping)
                else:
                    if len(col_names) == 0:
                        ct_df = pd.DataFrame()
                    else:
                        ct_df = ct_df[['code', 'date'] + col_names]
            else:
                ct_df = pd.DataFrame()

            # 分箱处理change
            # step=2
            # midd=4
            # bins = [x/100.0 for x in range(-100, 100+step, step)]
            # bins = bins[:100//step-midd] + [-0.07, 0.07] + bins[-100//step+midd:]
            # # bins = [-np.inf] + bins + [np.inf]
            # sf_df['change_bins'] = pd.cut(sf_df['change'], bins=bins, labels=False)
            # sf_df['change_bins'].fillna(0, inplace=True)
            # sf_df['change_bins'] = sf_df['change_bins'].astype(np.int32)
            # sf_df['change'] = sf_df['change_bins'].apply(lambda x: bins[x])
            # sf_df.drop(['change_bins'], axis=1, inplace=True)

            # 5. 生成时间特征数据
            # 将date列由timestamp转换为东8区日期时间
            tf_columns = []
            sf_df['date'] = pd.to_datetime(sf_df['date'], unit='s') + pd.Timedelta(hours=8)
            if self.timeenc == 0:  # fixed编码(int)
                tf_columns = ['tf0', 'tf1', 'tf2', 'tf3', 'tf4']
                sf_df['tf0'] = sf_df.date.apply(lambda row: row.month, 1)   # Month
                sf_df['tf1'] = sf_df.date.apply(lambda row: row.day, 1)     # Day
                sf_df['tf2'] = sf_df.date.apply(lambda row: row.weekday(), 1) # DayOfWeek
                sf_df['tf3'] = sf_df.date.apply(lambda row: row.hour, 1)    # Hour
                sf_df['tf4'] = sf_df.date.apply(lambda row: row.minute, 1)
                sf_df['tf4'] = sf_df.tf4.map(lambda x: x // 5)              # Minute
            elif self.timeenc == 1: # timeF编码(float)
                tf_columns = ['tf0', 'tf1', 'tf2', 'tf3', 'tf4']
                df_stamp= time_features(pd.to_datetime(sf_df['date'].values), freq='t').transpose(1, 0)
                # 'MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear'
                df_tf = pd.DataFrame(df_stamp, columns=tf_columns)
                sf_df = pd.concat([sf_df, df_tf], axis=1)

            # 6. 合并特征数据
            if (len(lf_df) != 0 and len(lf_df) != len(sf_df)) or len(sf_df) != len(mf_df) or (len(sf_df) != len(ct_df) and len(ct_df) > 0):
                raise ValueError(f"lf_df {len(lf_df)}  sf_df {len(sf_df)}  mf_df {len(mf_df)}  ct_df {len(ct_df)}")

            if self.verbose:
                print(sf_df[['code','date']].tail(5))
                if len(lf_df) > 0:
                    print(lf_df[['code','date']].tail(5))
                if len(mf_df) > 0:
                    print(mf_df[['code','date']].tail(5))
                if len(ct_df) > 0:
                    print(ct_df[['code','date']].tail(5))
            lb_df = sf_df[['code', 'date', 'change', 'long_label', 'short_label', 'label', 'bar_length', 'code_encoded'] + tf_columns]
            assert len(lb_df) == len(sf_df)
            if len(ct_embedding_df) > 0:
                lb_df = pd.concat([lb_df, ct_embedding_df], axis=1)

            sf_df.drop(['code', 'date', 'change', 'code_encoded', 'long_label', 'short_label', 'bar_length', 'label'] + tf_columns, axis=1, inplace=True)
            if len(lf_df) > 0:
                lf_df.drop(['code', 'date'], axis=1, inplace=True)
            if len(mf_df) > 0:
                mf_df.drop(['code', 'date'], axis=1, inplace=True)
            if len(ct_df) > 0:
                ct_df.drop(['code', 'date'], axis=1, inplace=True)

            # ins_nums = (lf_df.shape[1], sf_df.shape[1], mf_df.shape[1], ct_df.shape[1])
            # if len(self.ins_nums) > 0:
            #     assert self.ins_nums == ins_nums, f"ins_nums {self.ins_nums} != {ins_nums}"
            ft_df = pd.concat([lf_df, sf_df, mf_df, ct_df], axis=1)
            self.feat_names = ft_df.columns.tolist()

            if len(ft_df) != len(sf_df) or len(ft_df) != len(lb_df):
                raise ValueError(f"ft_df {len(ft_df)}  sf_df {len(sf_df)}  lb_df {len(lb_df)}")
            
            if self.verbose:
                # print(self.ins_nums)
                print(f"===============\nft{ft_df.shape} lf{lf_df.shape} sf{sf_df.shape} mf{mf_df.shape} ct{ct_df.shape}\n================")

            self.ft_df = ft_df
            self.lb_df = lb_df
            del lf_df, sf_df, mf_df, ct_df, ct_embedding_df
            # print(self.lb_df['bar_length'].describe())

            if self.verbose:
                print(f"===============\nft{self.ft_df.columns.to_list()}\n================")
                print(f"===============\nlb{self.lb_df.shape} ft{self.ft_df.shape}\n================")
                print(self.ft_df)
                print(self.lb_df)
                print(self.lb_df['long_label'].value_counts(), lb_df['short_label'].value_counts())
                # print(self.lb_df['DAYOFWEEK'].value_counts())
                # print(self.lb_df['HOUR'].value_counts())

    def fetch(self, direct: str, win: int, filter_win: int):

        self.direct = direct
        self.win = win
        self.filter_win = filter_win
        if self.direct != 'ls' and self.direct != 'mls' and self.direct != 'long' and self.direct != 'short':
            raise ValueError(f"direct {self.direct} is not supported")
        
        if self.win == 0:
            raise ValueError(f"win {self.win} is not supported")

        # 生成模型输入数据配置文件
        self._dump_input_param_json()

        x_data = []
        y_data = []
        x_mark = []
        y_mark = []
        emb_data = []

        data1 = self.ft_df.astype(np.float32).values

        if self.direct == 'long':
            data2 = self.lb_df['long_label'].values
        elif self.direct == 'short':
            data2 = self.lb_df['short_label'].values
        elif self.direct == 'mls':
            data2 = self.lb_df['label'].values
        elif self.direct == 'ls':
            data2 = self.lb_df['change'].values
        else:
            raise ValueError(f"direct {self.direct} is not supported")
        
        if self.verbose:
            print(f"categorical column: {self.ct_cat_cols_names}")
            print(self.lb_df.tail(5))
            print(self.ft_df.tail(5))

        data3 = self.lb_df[['code_encoded'] + self.ct_cat_cols_names].astype(np.int32).values

        if self.timeenc == 0:
            data4 = self.lb_df[['tf0', 'tf1', 'tf2', 'tf3', 'tf4']].astype(np.int32).values
        elif self.timeenc == 1:
            data4 = self.lb_df[['tf0', 'tf1', 'tf2', 'tf3', 'tf4']].astype(np.float32).values
        print(f"===============\nfeatures: {data1.shape} label: {data2.shape} embedding: {data3.shape}\n================")

        filter_count = 0
        extreme_threshold = 3.0 # 过滤极端值的阈值
        if self.filter_win > 1: # 过滤采样，只用窗口内change的绝对值最大值的样本
            # 计算绝对值最大的行序号，目前固定滑动窗口
            rolling_max_index = self.lb_df['change'].rolling(self.filter_win).apply(lambda x: abs(x).idxmax())
            # 移动窗口的第一个值会返回 NaN，所以可以使用 fillna 方法填充为 0 或其他合适的值
            rolling_max_index = rolling_max_index.fillna(0)
            # 将结果转换为整数
            rolling_max_index = rolling_max_index.astype(int)

            for i in range(self.win, len(rolling_max_index)):
                if rolling_max_index[i] == self.lb_df.index[i]:
                    if self.win > 0 and data3[i][0] != data3[i - self.win][0]:
                        # print(i, data3[i], data3[i - self.win])
                        continue
                    # 筛选过于极端的值
                    if self.is_filter_extreme :
                        if (data1[i - self.win:i, 1] > extreme_threshold).any() or (data1[i - self.win:i, 1] < -1.0*extreme_threshold).any():
                            filter_count += 1
                            continue
                x_data.append(data1[i - self.win:i])
                y_data.append(data2[i])
                emb_data.append(data3[i - self.win:i].tolist())
                if self.timeenc is not None:
                    x_mark.append(data4[i - self.win:i])
                    y_mark.append(data4[i])
        else:
            # 防止跨code取值
            for i in range(self.win, len(data1), self.step):
                if self.win > 0 and data3[i][0] != data3[i - self.win][0]:
                    # print(i, data3[i], data3[i - self.win])
                    continue
                if self.is_filter_extreme: 
                    if (data1[i - self.win:i, 1] > extreme_threshold).any() or (data1[i - self.win:i, 1] < -1.0*extreme_threshold).any():
                        filter_count += 1
                        continue

                x_data.append(data1[i - self.win:i])
                y_data.append(data2[i])
                emb_data.append(data3[i - self.win:i].tolist())
                if self.timeenc is not None:
                    x_mark.append(data4[i - self.win:i])
                    y_mark.append(data4[i])

        del data1, data2, data3
        if self.timeenc is not None:
            del data4

        print("******************************")
        print(f"label: {round(sum(y_data) / len(y_data) * 100, 3)}%, total: {len(y_data)} filter win: {self.filter_win} {self.is_filter_extreme} filter: {filter_count} is_normal: {self.is_normal}")
        print("******************************")
        # encoded_data = np.array(encoded_data)
        # if encoded_data.shape[-1] == 1:
        #     encoded_data = np.squeeze(encoded_data, axis=-1)
        if self.timeenc is not None:
            return np.array(emb_data), np.array(x_data), np.array(y_data), x_mark, y_mark
        else:
            return np.array(emb_data), np.array(x_data), np.array(y_data)

 
