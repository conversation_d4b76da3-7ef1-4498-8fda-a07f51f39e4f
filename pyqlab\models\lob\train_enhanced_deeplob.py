"""
训练增强版DeepLOB模型
"""
import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, random_split
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import logging
import argparse
from tqdm import tqdm
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入自定义模块
from pyqlab.models.lob.enhanced_deeplob import EnhancedDeepLOBRegression
from pyqlab.data.orderbook_processor import OrderBookProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("train_enhanced_deeplob.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TrainEnhancedDeepLOB")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练增强版DeepLOB模型')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, default='./data/processed_orderbook',
                        help='处理后的数据路径')
    parser.add_argument('--code', type=str, default=None,
                        help='要训练的证券代码，如果为None则训练所有可用的代码')
    parser.add_argument('--data_type', type=str, default='stock',
                        choices=['stock', 'future', 'all'],
                        help='数据类型，stock、future或all')
    
    # 模型参数
    parser.add_argument('--input_channels', type=int, default=1,
                        help='输入通道数')
    parser.add_argument('--time_steps', type=int, default=100,
                        help='时间步长')
    parser.add_argument('--num_features', type=int, default=40,
                        help='每个时间步的特征数')
    parser.add_argument('--output_size', type=int, default=1,
                        help='输出维度')
    parser.add_argument('--horizon', type=int, default=10,
                        help='预测时间范围')
    parser.add_argument('--dropout', type=float, default=0.1,
                        help='Dropout比率')
    parser.add_argument('--use_transformer', action='store_true',
                        help='是否使用Transformer')
    parser.add_argument('--num_transformer_layers', type=int, default=2,
                        help='Transformer层数')
    parser.add_argument('--num_heads', type=int, default=4,
                        help='注意力头数')
    parser.add_argument('--head_dim', type=int, default=16,
                        help='每个头的维度')
    parser.add_argument('--ff_dim', type=int, default=256,
                        help='前馈网络维度')
    parser.add_argument('--lstm_hidden_size', type=int, default=64,
                        help='LSTM隐藏层大小')
    parser.add_argument('--lstm_layers', type=int, default=1,
                        help='LSTM层数')
    parser.add_argument('--use_gru', action='store_true',
                        help='是否使用GRU代替LSTM')
    parser.add_argument('--use_code_embedding', action='store_true',
                        help='是否使用证券代码嵌入')
    parser.add_argument('--num_codes', type=int, default=100,
                        help='证券代码数量')
    parser.add_argument('--code_embedding_dim', type=int, default=16,
                        help='证券代码嵌入维度')
    parser.add_argument('--probabilistic', action='store_true',
                        help='是否进行概率预测')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--epochs', type=int, default=50,
                        help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='权重衰减')
    parser.add_argument('--patience', type=int, default=10,
                        help='早停耐心值')
    parser.add_argument('--train_ratio', type=float, default=0.7,
                        help='训练集比例')
    parser.add_argument('--val_ratio', type=float, default=0.15,
                        help='验证集比例')
    parser.add_argument('--test_ratio', type=float, default=0.15,
                        help='测试集比例')
    
    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./models/saved',
                        help='模型保存目录')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu',
                        help='训练设备')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    
    return parser.parse_args()

def set_seed(seed):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_data(data_path, code=None, data_type='stock'):
    """
    加载数据
    
    参数:
        data_path: 数据路径
        code: 证券代码，如果为None则加载所有可用的代码
        data_type: 数据类型，'stock'、'future'或'all'
        
    返回:
        dict: 包含数据的字典，格式为 {code: (X, y, code_id)}
    """
    processor = OrderBookProcessor(data_path=data_path)
    
    data_types = []
    if data_type == 'stock' or data_type == 'all':
        data_types.append('stock')
    if data_type == 'future' or data_type == 'all':
        data_types.append('future')
    
    data = {}
    code_to_id = {}
    next_code_id = 0
    
    for dt in data_types:
        dt_path = os.path.join(data_path, dt)
        if not os.path.exists(dt_path):
            logger.warning(f"数据路径 {dt_path} 不存在")
            continue
        
        # 获取所有可用的代码
        available_codes = []
        for file in os.listdir(dt_path):
            if file.endswith('_X.npy'):
                available_code = file.split('_')[0]
                available_codes.append(available_code)
        
        # 如果指定了代码，则只加载该代码的数据
        if code is not None:
            if code in available_codes:
                available_codes = [code]
            else:
                logger.warning(f"代码 {code} 在 {dt} 数据中不可用")
                continue
        
        # 加载数据
        for available_code in available_codes:
            X, y = processor.load_processed_data(available_code, dt)
            
            if X is not None and y is not None:
                # 为代码分配ID
                if available_code not in code_to_id:
                    code_to_id[available_code] = next_code_id
                    next_code_id += 1
                
                data[available_code] = (X, y, code_to_id[available_code])
                logger.info(f"加载代码 {available_code} 的数据: X.shape={X.shape}, y.shape={y.shape}")
    
    if not data:
        logger.error(f"没有找到可用的数据")
        return None
    
    return data, code_to_id

def prepare_dataloaders(X, y, code_id, batch_size, train_ratio, val_ratio, test_ratio, seed=42):
    """
    准备数据加载器
    
    参数:
        X: 特征数据
        y: 目标数据
        code_id: 证券代码ID
        batch_size: 批次大小
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        seed: 随机种子
        
    返回:
        tuple: (train_loader, val_loader, test_loader)
    """
    # 转换为PyTorch张量
    X_tensor = torch.FloatTensor(X)
    y_tensor = torch.FloatTensor(y)
    code_id_tensor = torch.LongTensor([code_id] * len(X))
    
    # 创建数据集
    dataset = TensorDataset(X_tensor, y_tensor, code_id_tensor)
    
    # 计算数据集大小
    total_size = len(dataset)
    train_size = int(train_ratio * total_size)
    val_size = int(val_ratio * total_size)
    test_size = total_size - train_size - val_size
    
    # 分割数据集
    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size],
        generator=torch.Generator().manual_seed(seed)
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    return train_loader, val_loader, test_loader

def train_epoch(model, train_loader, criterion, optimizer, device):
    """
    训练一个epoch
    
    参数:
        model: 模型
        train_loader: 训练数据加载器
        criterion: 损失函数
        optimizer: 优化器
        device: 设备
        
    返回:
        float: 训练损失
    """
    model.train()
    total_loss = 0
    
    for X, y, code_ids in tqdm(train_loader, desc="Training"):
        X, y, code_ids = X.to(device), y.to(device), code_ids.to(device)
        
        # 前向传播
        if model.probabilistic:
            mean, log_var = model(X, code_ids)
            
            # 计算负对数似然损失
            loss = criterion(mean, y, log_var)
        else:
            outputs = model(X, code_ids)
            loss = criterion(outputs, y)
        
        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
    
    return total_loss / len(train_loader)

def validate(model, val_loader, criterion, device):
    """
    验证模型
    
    参数:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 设备
        
    返回:
        float: 验证损失
    """
    model.eval()
    total_loss = 0
    
    with torch.no_grad():
        for X, y, code_ids in tqdm(val_loader, desc="Validating"):
            X, y, code_ids = X.to(device), y.to(device), code_ids.to(device)
            
            # 前向传播
            if model.probabilistic:
                mean, log_var = model(X, code_ids)
                
                # 计算负对数似然损失
                loss = criterion(mean, y, log_var)
            else:
                outputs = model(X, code_ids)
                loss = criterion(outputs, y)
            
            total_loss += loss.item()
    
    return total_loss / len(val_loader)

def test(model, test_loader, device):
    """
    测试模型
    
    参数:
        model: 模型
        test_loader: 测试数据加载器
        device: 设备
        
    返回:
        dict: 包含测试指标的字典
    """
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for X, y, code_ids in tqdm(test_loader, desc="Testing"):
            X, y, code_ids = X.to(device), y.to(device), code_ids.to(device)
            
            # 前向传播
            if model.probabilistic:
                mean, _ = model(X, code_ids)
                outputs = mean
            else:
                outputs = model(X, code_ids)
            
            # 收集预测和目标
            all_preds.append(outputs.cpu().numpy())
            all_targets.append(y.cpu().numpy())
    
    # 合并所有批次的预测和目标
    all_preds = np.concatenate(all_preds, axis=0)
    all_targets = np.concatenate(all_targets, axis=0)
    
    # 计算指标
    metrics = {}
    
    # 对于每个预测步骤计算指标
    for step in range(all_preds.shape[1]):
        step_preds = all_preds[:, step, 0]  # 假设输出维度为1
        step_targets = all_targets[:, step, 0]
        
        mse = mean_squared_error(step_targets, step_preds)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(step_targets, step_preds)
        r2 = r2_score(step_targets, step_preds)
        
        metrics[f'step_{step+1}'] = {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2
        }
    
    # 计算所有步骤的平均指标
    avg_mse = np.mean([metrics[f'step_{step+1}']['mse'] for step in range(all_preds.shape[1])])
    avg_rmse = np.mean([metrics[f'step_{step+1}']['rmse'] for step in range(all_preds.shape[1])])
    avg_mae = np.mean([metrics[f'step_{step+1}']['mae'] for step in range(all_preds.shape[1])])
    avg_r2 = np.mean([metrics[f'step_{step+1}']['r2'] for step in range(all_preds.shape[1])])
    
    metrics['average'] = {
        'mse': avg_mse,
        'rmse': avg_rmse,
        'mae': avg_mae,
        'r2': avg_r2
    }
    
    return metrics, all_preds, all_targets

def negative_log_likelihood_loss(mean, target, log_var):
    """
    计算负对数似然损失
    
    参数:
        mean: 预测均值
        target: 目标值
        log_var: 预测对数方差
        
    返回:
        torch.Tensor: 损失值
    """
    # 计算负对数似然
    precision = torch.exp(-log_var)
    loss = torch.mean(0.5 * (log_var + (target - mean) ** 2 * precision))
    return loss

def plot_predictions(predictions, targets, code, save_dir):
    """
    绘制预测结果
    
    参数:
        predictions: 预测值
        targets: 目标值
        code: 证券代码
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 对于每个预测步骤绘制一张图
    for step in range(predictions.shape[1]):
        plt.figure(figsize=(10, 6))
        
        # 选择前100个样本进行可视化
        n_samples = min(100, predictions.shape[0])
        
        plt.plot(targets[:n_samples, step, 0], label='Actual')
        plt.plot(predictions[:n_samples, step, 0], label='Predicted')
        
        plt.title(f'Code: {code}, Step: {step+1}')
        plt.xlabel('Sample')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True)
        
        plt.savefig(os.path.join(save_dir, f'{code}_step_{step+1}.png'))
        plt.close()
    
    # 绘制所有步骤的平均预测
    plt.figure(figsize=(10, 6))
    
    # 选择前100个样本进行可视化
    n_samples = min(100, predictions.shape[0])
    
    avg_targets = np.mean(targets[:n_samples, :, 0], axis=1)
    avg_predictions = np.mean(predictions[:n_samples, :, 0], axis=1)
    
    plt.plot(avg_targets, label='Actual')
    plt.plot(avg_predictions, label='Predicted')
    
    plt.title(f'Code: {code}, Average of All Steps')
    plt.xlabel('Sample')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    plt.savefig(os.path.join(save_dir, f'{code}_average.png'))
    plt.close()

def train_model(args):
    """
    训练模型
    
    参数:
        args: 命令行参数
    """
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 加载数据
    data, code_to_id = load_data(args.data_path, args.code, args.data_type)
    if data is None:
        return
    
    # 设置设备
    device = torch.device(args.device)
    logger.info(f"使用设备: {device}")
    
    # 对每个代码训练一个模型
    for code, (X, y, code_id) in data.items():
        logger.info(f"训练代码 {code} 的模型")
        
        # 准备数据加载器
        train_loader, val_loader, test_loader = prepare_dataloaders(
            X, y, code_id, args.batch_size, args.train_ratio, args.val_ratio, args.test_ratio, args.seed
        )
        
        # 创建模型
        model = EnhancedDeepLOBRegression(
            input_channels=args.input_channels,
            time_steps=args.time_steps,
            num_features=args.num_features,
            output_size=args.output_size,
            horizon=args.horizon,
            dropout=args.dropout,
            use_transformer=args.use_transformer,
            num_transformer_layers=args.num_transformer_layers,
            num_heads=args.num_heads,
            head_dim=args.head_dim,
            ff_dim=args.ff_dim,
            lstm_hidden_size=args.lstm_hidden_size,
            lstm_layers=args.lstm_layers,
            use_gru=args.use_gru,
            use_code_embedding=args.use_code_embedding,
            num_codes=args.num_codes,
            code_embedding_dim=args.code_embedding_dim,
            probabilistic=args.probabilistic
        ).to(device)
        
        # 定义损失函数和优化器
        if args.probabilistic:
            criterion = negative_log_likelihood_loss
        else:
            criterion = nn.MSELoss()
        
        optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
        
        # 训练模型
        best_val_loss = float('inf')
        patience_counter = 0
        train_losses = []
        val_losses = []
        
        for epoch in range(args.epochs):
            # 训练一个epoch
            train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
            train_losses.append(train_loss)
            
            # 验证
            val_loss = validate(model, val_loader, criterion, device)
            val_losses.append(val_loss)
            
            logger.info(f"Epoch {epoch+1}/{args.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # 检查是否需要保存模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                
                # 保存模型
                model_save_path = os.path.join(args.save_dir, f"{code}_model.pth")
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                    'args': vars(args),
                    'code_to_id': code_to_id
                }, model_save_path)
                
                logger.info(f"模型已保存到 {model_save_path}")
            else:
                patience_counter += 1
                if patience_counter >= args.patience:
                    logger.info(f"早停: {args.patience} 个epoch没有改善")
                    break
        
        # 绘制损失曲线
        plt.figure(figsize=(10, 6))
        plt.plot(train_losses, label='Train Loss')
        plt.plot(val_losses, label='Validation Loss')
        plt.title(f'Code: {code}, Loss Curves')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(args.save_dir, f'{code}_loss.png'))
        plt.close()
        
        # 加载最佳模型
        checkpoint = torch.load(os.path.join(args.save_dir, f"{code}_model.pth"))
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # 测试模型
        metrics, predictions, targets = test(model, test_loader, device)
        
        # 保存测试指标
        with open(os.path.join(args.save_dir, f"{code}_metrics.json"), 'w') as f:
            json.dump(metrics, f, indent=4)
        
        # 绘制预测结果
        plot_predictions(predictions, targets, code, args.save_dir)
        
        logger.info(f"代码 {code} 的模型训练完成")
        logger.info(f"测试指标: {metrics['average']}")

if __name__ == "__main__":
    args = parse_args()
    train_model(args)
