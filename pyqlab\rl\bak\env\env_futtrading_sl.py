# import os
# import time
# import sys
from copy import deepcopy

# import torch
# import torch.nn as nn
import numpy as np
import numpy.random as rd
import pandas as pd
import gym
from gym import spaces

from rl.data.tickdata_fut import PreprocessingPipeline
from rl.data.tickdata_fut import FactorDataset

"""
- state的含义、设置,如account,price,technical indicator,
  具体看代码 https://github.com/AI4Finance-Foundation/FinRL/blob/master/finrl/finrl_meta/env_stock_trading/env_stocktrading.py
- action的含义,买入卖出,以及小额交易忽略
- reward function的设置(直接影响 能否训练出更加谨慎,回撤更小的智能体)交易滑点,
- turbulence达到阈值就强制卖出,等
- env reset 里的随机性对于强化学习训练的益处

金融强化学习算法与其他自动交易算法的区别如下：
1. 强化学习算法中的无模型算法 不需要对环境进行建模（也就是它不预测市场）。
   这与部分深度学习的交易算法对市场进行一定程度的预测不同。市场的预测一直是一个难题,而深度强化学习
   有机会在不对市场进行预测的情况下,直接学习交易策略。
2. 数据导向,增量学习。
   深度强化学习算法和其他深度学习算法一样,都是数据导向的。这与一些基于基本逻辑语句写就的交易算法明显不同。
   市场每时每刻都在产出大量数据,依靠人类经验总结出来的交易策略大家都在使用。而深度学习可以使用这些数据,
   提高交易策略的自动化程度。

"""

class FutureTradingEnv(gym.Env):
    """
    def __init__(self, 
        initial_amount=1e6,  # 初始本金
        max_stock=1e2,  # 最大交易额度,买入或卖出100个单位
        buy_cost_pct=1e-3,  # 交易损耗率设为 0.001
        sell_cost_pct=1e-3,  # 交易损耗率设为 0.001
        gamma=0.99,  # 强化学习的折扣比率,给人为设置的终止状态的reward进行补偿的时候会用到
        beg_idx=0,   # 使用数据集的这个位置之后的数据
        end_idx=1113  # 使用数据集的这个位置之前的数据
     ):

    """
    def __init__(self, name="FutTradingEnv", initial_amount=1e5, buy_cost_pct=1e-5, sell_cost_pct=1e-5, gamma=0.99):

        print(initial_amount, buy_cost_pct, sell_cost_pct, gamma)

        ppp = PreprocessingPipeline(n_splits=5, shuffle=True, random_state=42)
        self.df_train, self.df_valid = ppp.transform()
        self.fd = FactorDataset(self.df_train)
        self.tick_data, close = self.fd.getitem(0)

        self.max_shares = 10

        # self.close_ary, self.tech_ary = self.load_data_from_disk()
        # self.close_ary = self.close_ary[beg_idx:end_idx]
        # self.tech_ary = self.tech_ary[beg_idx:end_idx]
        # print(f"| StockTradingEnv: close_ary.shape {self.close_ary.shape}")
        # print(f"| StockTradingEnv: tech_ary.shape {self.tech_ary.shape}")
        self.name=name
        self.buy_cost_rate = 1 + buy_cost_pct
        self.sell_cost_rate = 1 - sell_cost_pct
        self.initial_amount = initial_amount
        self.gamma = gamma

        # reset()
        self.cur_idx = 0
        self.rewards = None
        self.total_asset = None
        self.cumulative_returns = 0
        self.if_random_reset = False

        self.amount = 0
        self.shares = 0
        self.shares_num = 1 # self.close_ary.shape[1]
        amount_dim = 1

        # environment information
        self.env_name = 'FutureTradingEnv-v1'
        # self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
        self.action_space = spaces.Discrete(3)
        # self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(self.state_space,))        
        # self.state_dim = self.shares_num + self.close_ary.shape[1] + self.tech_ary.shape[1] + amount_dim
        # self.state_dim = self.shares_num + self.tick_data.shape[1] + amount_dim
        self.state_dim = self.tick_data.shape[1]
        self.action_dim = self.shares_num

        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(self.state_dim,), dtype=np.float32
        )
        self.if_discrete = True
        self.max_step = self.fd.len()

        # self.target_return = 100

    def reset(self):
        self.cur_idx = 0
        if self.if_random_reset:
            self.amount = self.initial_amount * rd.uniform(0.9, 1.1)
            self.shares = (np.abs(rd.randn(self.shares_num).clip(-2, +2)) * 2 ** 6).astype(int)
        else:
            self.amount = self.initial_amount
            self.shares = np.zeros(self.shares_num, dtype=np.float32)

        self.rewards = list()
        # self.total_asset = (self.close_ary[self.day] * self.shares).sum() + self.amount
        self.total_asset = self.amount
        print("====================reset=====================\n")
        print(self.get_state())
        return self.get_state()

    def get_state(self):
        # state = np.hstack((
        #                    np.array(self.amount * 2 ** -16),
        #                    np.array(self.shares * 2 ** -9),
        #                    self.tick_data.values.reshape(-1) * 2 ** -7,))
        state = self.tick_data.values.reshape(-1) * 2 ** -7
        return state

    def step(self, action):
        self.cur_idx += 1

        self.tick_data, close_price = self.fd.getitem(self.cur_idx)
        # print(self.tick_data)

        # action = action.copy()
        print(f"step={self.cur_idx}, action={action}")
        # action[(-0.1 < action) & (action < 0.1)] = 0
        # action_int = (action * self.max_stock).astype(int)
        # actions initially is scaled between -1 and 1
        # convert into integer because we can't buy fraction of shares

        # adj_close_price = self.tick_data.iloc[0, 0]  # `adjcp` denotes adjusted close price
        if action == 1:  # buy_stock
            if self.shares == 0:
                delta_stock = min(self.max_shares, self.amount // close_price) #, stock_action)
                self.amount -= close_price * delta_stock * self.buy_cost_rate
                self.shares += int(delta_stock)
                print(f"buy shares: {self.shares}")
        elif action == 2:  # sell_stock
            # delta_stock = min(-stock_action, self.shares)
            if self.shares > 0:
                print(f"sell shares: {self.shares}")
                self.amount += close_price * self.shares * self.sell_cost_rate
                self.shares = 0
        # else:


        state = self.get_state()

        # 计算reward
        total_asset = (close_price * self.shares) + self.amount
        reward = 100*(total_asset - self.total_asset) * 2 ** -6

        self.rewards.append(reward)
        self.total_asset = total_asset
        # if reward != 0.0:
        #     print(f"total_asset: {total_asset}, reward: {reward}")

        done = self.cur_idx == self.max_step - 1
        if done:
            reward += 1 / (1 - self.gamma) * np.mean(self.rewards)
            self.cumulative_returns = total_asset / self.initial_amount
            print("=============================")
            print(f"cumulative returns: {self.cumulative_returns}")
            print("=============================")
        # print(type(reward))
        # print("step", total_asset, reward)
        return state, reward[0], done, {}



