# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8

from __future__ import division
from __future__ import print_function

import numpy as np
import pandas as pd
from typing import Text, Union
import copy
import math
from qlib.utils import get_or_create_path
from qlib.log import get_module_logger, set_log_with_config

import torch
import torch.nn as nn
import torch.optim as optim
# from torch.nn.utils import weight_norm
from torch.optim import lr_scheduler
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset, random_split, Subset
from sklearn.model_selection import KFold

from .pytorch_utils import AdaptiveLayyer
from qlib.model.base import Model
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES
from sklearn.metrics import roc_auc_score, accuracy_score, f1_score
from sklearn.model_selection import StratifiedShuffleSplit
import logging

class ConvModelPytorch(Model):
    """MLP Model

    Parameters
    ----------
    d_feat : int
        input dimension for each time step
    n_chans: int
        number of channels
    metric: str
        the evaluate metric used in early stop
    optimizer : str
        optimizer name
    GPU : str
        the GPU ID(s) used for training
    """

    def __init__(
        self,
        num_embeds=[64,],
        num_channel=5,
        num_input=95, #87
        out_channels=(32, 64, 1200, 256),
        dropout=0.5,
        max_epochs=200,
        activation="relu",
        pooling="max",
        lr=0.000001,
        lr_decay=0.1,
        lr_decay_steps=10,
        metric="",
        batch_size=64,
        k_folds=5,
        early_stop=30,
        loss="binary",
        optimizer="adam",
        GPU=0,
        seed=None,
        best_cond="loss",
        version="CV2DC",
        ins_nums=(0,51,51,17),
        **kwargs
    ):
        # Set logger.
        logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
        self.logger = logging.getLogger(__name__)
        self.logger.info("=======================================")

        # set hyper-parameters.
        self.num_embeds = num_embeds
        self.num_channel = num_channel
        self.num_input = num_input
        self.out_channels = out_channels
        self.dropout = dropout
        self.max_epochs = max_epochs
        self.activation = activation
        self.pooling = pooling
        self.lr = lr
        self.lr_decay = lr_decay
        self.lr_decay_steps = lr_decay_steps
        self.metric = metric
        self.batch_size = batch_size
        self.k_folds = k_folds
        self.early_stop = early_stop
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.device = torch.device("cuda:%d" % (GPU) if torch.cuda.is_available() and GPU >= 0 else "cpu")
        self.seed = seed
        self.best_cond = best_cond
        self.version = version
        self.ins_nums = ins_nums

        self.logger.info(
            "MLP parameters setting:"
            "\nnum_code : {}"
            "\nnum_input : {}"
            "\nout_channels : {}"
            "\ndropout : {}"
            "\nmax_epochs : {}"
            "\nactivation : {}"
            "\npooling : {}"
            "\nlr : {}"
            "\nmetric : {}"
            "\nbatch_size : {}"
            "\nk_folds : {}"
            "\nearly_stop : {}"
            "\noptimizer : {}"
            "\nloss_type : {}"
            "\nvisible_GPU : {}"
            "\nuse_GPU : {}"
            "\nseed : {}"
            "\nbest_cond : {}".format(
                num_embeds,
                num_input,
                out_channels,
                dropout,
                max_epochs,
                activation,
                pooling,
                lr,
                metric,
                batch_size,
                k_folds,
                early_stop,
                optimizer.lower(),
                loss,
                GPU,
                self.use_gpu,
                seed,
                best_cond,
            )
        )

        if self.seed is not None:
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)

        # todo: batch_norm drop_out
        if self.version == "CV1DC":
            self.conv_model = TimeSeriesModel1D(
                num_embeds=self.num_embeds,
                num_channel=self.num_channel,
                num_input=self.num_input,
                out_channels=self.out_channels,
                dropout=self.dropout,
                activation=self.activation,
                pooling=self.pooling,
            )
        elif self.version == "CV2DC":
            self.conv_model = TimeSeriesModel2D(
                num_embeds=self.num_embeds,
                num_channel=self.num_channel,
                num_input=self.num_input,
                out_channels=self.out_channels,
                dropout=self.dropout,
                ins_nums=self.ins_nums,
                activation=self.activation,
                pooling=self.pooling,
            )
        else:
            raise ValueError("unknown version `%s`" % self.version)
        
        self.logger.info("model:\n{:}".format(self.conv_model))
        # self.logger.info("model size: {:.4f} MB".format(count_parameters(self.conv_model)))

        if optimizer.lower() == "adam":
            self.train_optimizer = optim.Adam(self.conv_model.parameters(), lr=self.lr)
        elif optimizer.lower() == "gd":
            self.train_optimizer = optim.SGD(self.conv_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError("optimizer {} is not supported!".format(optimizer))

        self.fitted = False
        self.conv_model.to(self.device)

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    def mse(self, pred, label):
        loss = (pred - label) ** 2
        return torch.mean(loss)

    def loss_fn(self, pred, label):
        mask = ~torch.isnan(pred)
        # if self.loss == "mse":
        #     return self.mse(pred[mask], label[mask])
        pred0 = pred[mask]
        label0 = label[mask]
        if self.loss == "mse":
            sqr_loss = torch.mul(pred - label, pred - label)
            loss = sqr_loss.mean()
            return loss
        elif self.loss == "binary":
            bce_loss = nn.BCELoss()
            ret = bce_loss(pred0, label0)
            return ret
        raise ValueError("unknown loss `%s`" % self.loss)

    def accuracy(self, pred, label):
        if self.use_gpu:
            preds = (pred>0.5).type(torch.IntTensor).cuda()
        else:
            preds = (pred>0.5).type(torch.IntTensor)
        return (preds == label).float().mean()

   

    # def loader(self, dataset, batch_size=32, shuffle=True):
    #     # 将数据集划分为训练集和验证集
    #     train_size = int(0.8 * len(dataset))
    #     val_size = len(dataset) - train_size
    #     train_dataset, valid_dataset = random_split(dataset, [train_size, val_size])

    #     train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
    #     valid_dataloader = DataLoader(valid_dataset, batch_size=batch_size, shuffle=False)
    #     return train_dataloader, valid_dataloader
    
    def loader(self, dataset, batch_size=32, shuffle=True):
        """
        在数据集划分上，你可以考虑使用分层抽样（Stratified Sampling）
        来确保训练集和验证集在类别分布上的一致性。这在数据集类别分布不均衡时特别有用。
        """
        # 获取数据集的标签
        targets = [data[1] for data in dataset]

        # 创建分层抽样对象
        split = StratifiedShuffleSplit(n_splits=1, test_size=0.2)

        # 获取训练集和验证集的索引
        for train_index, val_index in split.split(dataset, targets):
            train_dataset = Subset(dataset, train_index)
            val_dataset = Subset(dataset, val_index)

        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        return train_dataloader, val_dataloader  

    def train_epoch(self, dataloader):

        self.conv_model.train()
        running_loss = 0.0

        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            # print("inputs.shape: ", inputs.shape)
            outputs = self.conv_model(encodeds, inputs)
            loss = self.loss_fn(outputs, targets.float())
            # l2_reg = self.conv_model.L2_regularization(weight_decay=0.01)
            # loss = loss + l2_reg
            self.train_optimizer.zero_grad()
            loss.backward()
            nn.utils.clip_grad_value_(self.conv_model.parameters(), 3.0)
            self.train_optimizer.step()

            running_loss += loss.item()

        return running_loss / len(dataloader)


    def test_epoch(self, dataloader):

        self.conv_model.eval()
        losses = []
        accs = []
        total = 0
        with torch.no_grad():
            for inputs, targets, encodeds in dataloader:
                inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

                outputs = self.conv_model(encodeds, inputs)
                loss = self.loss_fn(outputs, targets.float())
                losses.append(loss.item())
                accs.append(roc_auc_score(targets.cpu().numpy(), outputs.cpu().numpy())) 
                total += targets.size(0)

        return np.mean(losses), np.mean(accs)
    
    # 特征数据集是按年保存在文件中的，每次只加载一年的数据

    

    def fit(
        self,
        x_data,
        y_data,
        encoded_data,
        evals_result=dict(),
        save_path=None,
    ):
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))
        kf = KFold(n_splits=self.k_folds, shuffle=True, random_state=42)

        save_path = get_or_create_path(save_path)
        evals_result["loss"] = {}
        evals_result["accuracy"] = {}
        evals_result["loss"]["train"] = []
        evals_result["loss"]["valid"] = []
        evals_result["accuracy"]["train"] = []
        evals_result["accuracy"]["valid"] = []

        # train
        self.logger.info("training...")
        self.fitted = True

        # 学习速率衰减设置
        scheduler = lr_scheduler.ReduceLROnPlateau(self.train_optimizer,
                                                   'min',
                                                   patience=self.lr_decay_steps,
                                                   factor=self.lr_decay
                                                )
        for fold, (train_index, val_index) in enumerate(kf.split(dataset), 0):
            print(f'FOLD {fold}')
            print('--------------------------------')
            train_ds = Subset(dataset, train_index)
            val_ds = Subset(dataset, val_index)
            train_dl = DataLoader(train_ds, batch_size=self.batch_size, shuffle=True)
            val_dl = DataLoader(val_ds, batch_size=self.batch_size, shuffle=False)

            stop_steps = 0
            train_loss = 0
            best_loss = np.inf
            best_acc = -np.inf
            best_epoch = 0

            for step in range(self.max_epochs):
                self.logger.info(f"Fold {fold} Epoch {step}")
                self.train_epoch(train_dl)

                train_loss, train_acc = self.test_epoch(train_dl)
                val_loss, val_acc = self.test_epoch(val_dl)
                scheduler.step(val_loss)
                self.logger.info("loss: train %.6f, valid %.6f" % (train_loss, val_loss))
                self.logger.info("accuracy: train %.3f, valid %.3f" % (train_acc, val_acc))
                evals_result["loss"]["train"].append(train_loss)
                evals_result["loss"]["valid"].append(val_loss)
                evals_result["accuracy"]["train"].append(train_acc)
                evals_result["accuracy"]["valid"].append(val_acc)

                if self.best_cond == "loss":
                    if best_loss < val_loss:
                        stop_steps += 1
                        if stop_steps >= self.early_stop:
                            break
                    else:
                        best_loss = val_loss
                        if val_acc > best_acc:
                            best_acc = val_acc

                        stop_steps = 0
                        best_epoch = step
                        best_param = copy.deepcopy(self.conv_model.state_dict())
                else:
                    if val_acc < best_acc:
                        stop_steps += 1
                        if stop_steps >= self.early_stop:
                            print("early stop")
                            break
                    else:
                        best_acc = val_acc

                        if val_loss < best_loss:
                            best_loss = val_loss

                        stop_steps = 0
                        best_epoch = step
                        best_param = copy.deepcopy(self.conv_model.state_dict())

        self.logger.info("best epoch: %d loss: %.6lf accuracy: %.3lf" % (best_epoch, best_loss, best_acc))
        self.conv_model.load_state_dict(best_param)
        # save model
        # torch.save(best_param, save_path)
        model = self.conv_model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        # torch.onnx.export(model, torch.randn(1, self.num_channel, 8), save_path, verbose=False, input_names=["code_ids", "x"], output_names=["y"])
        # sm = torch.jit.script(model)
        embedding = torch.zeros(1, self.num_channel, len(self.num_embeds)).to(torch.int32)
        example_input = torch.rand(1, self.num_channel, sum(self.ins_nums))
        sm = torch.jit.trace(model, (embedding, example_input))
        sm.save(save_path)

        if self.use_gpu:
            torch.cuda.empty_cache()

        return best_epoch, best_loss, best_acc

    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        """
        Predict the result of the given dataset.
        """
        if not self.fitted:
            raise ValueError("model is not fitted yet!")

        x_data, y_data, encoded_data = dataset.prepare(
            segment,
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_size = int(0.9 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = random_split(dataset, [train_size, test_size])
        test_dataloader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False)

        preds = []
        self.conv_model.eval()
        for inputs, targets, encodeds in test_dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            with torch.no_grad():
                outputs = self.conv_model(encodeds, inputs)
            preds.append(outputs.detach().cpu().numpy())

        return pd.Series(np.concatenate(preds))


class TimeSeriesModel1D(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  out_channels=(32, 64, 1152, 256),
                  activation="relu",
                  pooling="max",
                ):
        super(TimeSeriesModel1D, self).__init__()
        self.embedding_layers = nn.ModuleList()
        for num_embed in num_embeds:
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embed, embedding_dim=math.ceil(np.sqrt(num_embed))))
        self.flatten = nn.Flatten()

        if activation == "relu":
            activation_layer = nn.ReLU()
        elif activation == "gelu":
            activation_layer = nn.GELU()
        elif activation == "prelu":
            activation_layer = nn.PReLU()
        elif activation == "leakyrelu":
            activation_layer = nn.LeakyReLU()
        else:
            raise ValueError("unknown activation `%s`" % activation)
        
        if pooling == "max":
            pooling_layer = nn.MaxPool1d(kernel_size=2)
        elif pooling == "avg":
            pooling_layer = nn.AvgPool1d(kernel_size=2)
        else:
            raise ValueError("unknown pooling `%s`" % pooling)
        
        self.conv1 = nn.Sequential(
            nn.Conv1d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(out_channels[0]),
            activation_layer, # nn.ReLU(),nn.GELU(),nn.SiLU(),nn.LeakyReLU
            pooling_layer,
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv1d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(out_channels[1]),
            activation_layer,
            pooling_layer,
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation_layer,
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Sequential(
            nn.Linear(out_channels[3], 1),
            nn.BatchNorm1d(1),
            nn.Sigmoid(),
        )

    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([embedded_data, x], dim=-1)

        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)
        return x.view(-1)

class TimeSeriesModel2D(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_embeds=[72],
                  num_channel=5,
                  num_input=45,
                  dropout=0.5,
                  out_channels=(32, 64, 1152, 256),
                  ins_nums=(0,51,51,8),
                  activation="relu",
                  pooling="max",
                ):
        super(TimeSeriesModel2D, self).__init__()
        assert len(ins_nums) == 4 and ins_nums[0] == 0 and ins_nums[1] == ins_nums[2]
        num_dims = []
        for num_embed in num_embeds:
            num_dims.append(math.ceil(np.sqrt(num_embed)))
        dims_sum = sum(num_dims)
        for i in range(len(num_dims)):
            num_dims[i] = int(num_dims[i]/dims_sum * (ins_nums[2] - ins_nums[3]))
        if sum(num_dims) > ins_nums[2] - ins_nums[3]:
            num_dims[0] -= (sum(num_dims) - (ins_nums[2] - ins_nums[3]))
        elif sum(num_dims) < ins_nums[2] - ins_nums[3]:
            num_dims[0] += ((ins_nums[2] - ins_nums[3]) - sum(num_dims))
        self.embedding_layers = nn.ModuleList()
        for i in range(len(num_dims)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i]))
        self.flatten = nn.Flatten()
        if activation == "relu":
            activation_layer = nn.ReLU()
        elif activation == "gelu":
            activation_layer = nn.GELU()
        elif activation == "prelu":
            activation_layer = nn.PReLU()
        elif activation == "leakyrelu":
            activation_layer = nn.LeakyReLU()
        else:
            raise ValueError("unknown activation `%s`" % activation)
        
        if pooling == "max":
            pooling_layer = nn.MaxPool2d(kernel_size=2)
        elif pooling == "avg":
            pooling_layer = nn.AvgPool2d(kernel_size=2)
        else:
            raise ValueError("unknown pooling `%s`" % pooling)
        
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=num_channel, out_channels=out_channels[0], kernel_size=(3,3), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[0]),
            activation_layer,
            # nn.MaxPool2d(kernel_size=2),
        )

        # if is_drop_channel:
        #     self.drop_channel = DropChannelLayyer(in_channels=32, out_channels=64)

        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels=out_channels[0], out_channels=out_channels[1], kernel_size=(3,3), stride=1, padding=1),
            nn.BatchNorm2d(out_channels[1]),
            activation_layer,
            pooling_layer,
        )

        self.linear1 = nn.Sequential(
            nn.Linear(out_channels[2], out_channels[3]),
            nn.BatchNorm1d(out_channels[3]),
            activation_layer,
            nn.Dropout(dropout),
        )

        self.linear2 = nn.Sequential(
            nn.Linear(out_channels[3], 1),
            nn.BatchNorm1d(1),
            nn.Sigmoid(),
        )


    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([x, embedded_data], dim=-1)
        x = x.reshape(x.shape[0], x.shape[1], 3, x.shape[2]//3)
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.flatten(x)
        x = self.linear1(x)
        x = self.linear2(x)

        return x.view(-1)


