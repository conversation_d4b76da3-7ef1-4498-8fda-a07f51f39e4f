import re
import glob
import os
import time
from pprint import pprint
import numpy as np

from ray import train, tune
from ray.rllib.algorithms.algorithm_config import AlgorithmConfig
from ray.rllib.policy.policy import PolicySpec
from ray.rllib.algorithms.callbacks import DefaultCallbacks
from ray.rllib.utils.metrics import (
    ENV_RUNNER_RESULTS,
    EPISODE_RETURN_MEAN,
)
from ray.rllib.utils.test_utils import (
    add_rllib_example_script_args,
    check_learning_achieved,
)
from ray.tune.registry import get_trainable_cls, register_env
from ray.air.integrations.wandb import WandbLoggerCallback
from argparse import ArgumentParser
# from pyqlab.rl.env.env_market_timing_vt import MarketTimingVtEnv
from pyqlab.rl.env.env_market_timing_v2 import MarketTimingEnv

def export_onnx(block_name: str,version: str, checkpoint_path: str):
    import os
    from datetime import datetime
    from time import time
    import torch
    from ray.rllib.algorithms.ppo import PPO, PPOConfig

    # 加载训练好的模型
    print(f"加载模型 {checkpoint_path}")
    algo = PPO.from_checkpoint(checkpoint=checkpoint_path, )
    policy = algo.get_policy()

    # 获取模型的输入和输出
    if version == "v1":
        dummy_input = torch.zeros((1, 10), dtype=torch.float32)
    elif version == "v2":
        dummy_input = torch.zeros((1, 20), dtype=torch.float32)

    # 导出模型为 ONNX 格式
    # onnx_path = "E:/lab/RoboQuant/pylab/model_rl/markettiming_ppo_v1.onnx"
    result_pytorch, _ = policy.model({
        "obs": dummy_input,
    })

    # Evaluate tensor to fetch numpy array
    result_pytorch = result_pytorch.detach().numpy()

    # This line will export the model to ONNX.
    outdir = "E:/lab/RoboQuant/pylab/model"
    policy.export_model(outdir, onnx=18)

    tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
    model_name = f"{outdir}/mktm_ppo_{block_name}_{tm_str}_{version}.onnx"
    # 如果文件存在，则删除
    if os.path.exists(model_name):
        os.remove(model_name)
    os.rename(f"{outdir}/model.onnx", model_name)

    print(f"模型已成功导出为 {outdir}")

def export_onnx_v2(block_name: str, version: str, checkpoint_path: str):
    import torch
    from datetime import datetime
    from time import time
    # Restore the best checkpoint
    algo = config.algo_class(config=config)
    algo.restore(checkpoint_path) # best_result.checkpoint
    
    # Export the model to ONNX format
    outdir = "E:/lab/RoboQuant/pylab/model"
    tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
    model_name = f"mktm_ppo_{block_name}_{tm_str}_{version}.onnx"
    policy = algo.get_policy()
    input_dict = policy.model.get_input_dict_for_inference()
    torch.onnx.export(policy.model, 
                      (input_dict,), 
                      model_name, 
                      export_params=True, 
                      opset_version=18, 
                      do_constant_folding=True,
                      input_names=['obs'], 
                      output_names=['output'], 
                      dynamic_axes={'obs' : {0 : 'batch_size'}, 
                                    'output' : {0 : 'batch_size'}})
    
    print(f"Model exported to {model_name}")   

class CrashAfterNIters(DefaultCallbacks):
    """Callback that makes the algo crash after a certain avg. return is reached."""

    def __init__(self):
        super().__init__()
        # We have to delay crashing by one iteration just so the checkpoint still
        # gets created by Tune after(!) we have reached the trigger avg. return.
        self._should_crash = False

    def on_train_result(self, *, algorithm, metrics_logger, result, **kwargs):
        # We had already reached the mean-return to crash, the last checkpoint written
        # (the one from the previous iteration) should yield that exact avg. return.
        if self._should_crash:
            raise RuntimeError("Intended crash after reaching trigger return.")
        # Reached crashing criterion, crash on next iteration.
        elif result[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward:
            print(
                "Reached trigger return of "
                f"{result[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]}"
            )
            self._should_crash = True

class ValidationCallback(DefaultCallbacks):
    def __init__(self):
        super().__init__()
        self.best_val_return = -float("inf")
        self.no_improvement_count = 0
        self.patience = 50  # 早停耐心值
        
    def on_train_result(self, *, algorithm, result, **kwargs):
        # 每N个训练迭代执行一次验证
        if result["training_iteration"] % 10 == 0:
            # 切换到验证模式
            algorithm.workers.foreach_worker(
                lambda w: w.foreach_env(
                    lambda env: setattr(env, "mode", "validation")
                )
            )
            
            # 执行验证
            val_results = algorithm.evaluate()
            val_return = val_results["evaluation"]["episode_reward_mean"]
            
            # 检查是否有改善
            if val_return > self.best_val_return:
                self.best_val_return = val_return
                self.no_improvement_count = 0
            else:
                self.no_improvement_count += 1
                
            # 早停检查
            if self.no_improvement_count >= self.patience:
                print(f"Early stopping triggered! No improvement for {self.patience} validations")
                return True
                
            # 切回训练模式
            algorithm.workers.foreach_worker(
                lambda w: w.foreach_env(
                    lambda env: setattr(env, "mode", "train")
                )
            )
            
class MarketValidationCallback(DefaultCallbacks):
    """市场择时验证回调，用于防止过拟合"""
    
    def __init__(self):
        super().__init__()
        # 最优指标
        self.best_metrics = {
            "val_return": -float("inf"),
            "val_sharpe": -float("inf"),
            "val_max_drawdown": float("inf")
        }
        # 早停参数
        self.no_improvement_count = 0
        self.patience = 5  # 早停耐心值
        self.min_delta = 0.01  # 最小改进幅度
        
    def on_train_result(self, *, algorithm, result, **kwargs):
        """训练结果回调"""
        # 获取验证集指标
        val_metrics = self._evaluate(algorithm)
        
        # 记录指标
        result["custom_metrics"].update({
            "val_episode_return": val_metrics["return"],
            "val_sharpe_ratio": val_metrics["sharpe"],
            "val_max_drawdown": val_metrics["max_drawdown"]
        })
        
        # 检查是否有改进
        improved = False
        
        # 检查收益率
        if val_metrics["return"] > self.best_metrics["val_return"] + self.min_delta:
            self.best_metrics["val_return"] = val_metrics["return"]
            improved = True
            
        # 检查夏普比率
        if val_metrics["sharpe"] > self.best_metrics["val_sharpe"] + self.min_delta:
            self.best_metrics["val_sharpe"] = val_metrics["sharpe"]
            improved = True
            
        # 检查最大回撤
        if val_metrics["max_drawdown"] < self.best_metrics["val_max_drawdown"] - self.min_delta:
            self.best_metrics["val_max_drawdown"] = val_metrics["max_drawdown"]
            improved = True
        
        # 更新早停计数器
        if improved:
            self.no_improvement_count = 0
        else:
            self.no_improvement_count += 1
            
        # 检查是否需要早停
        if self.no_improvement_count >= self.patience:
            print(f"\n=== 训练早停! 已经 {self.patience} 轮无改进 ===")
            print(f"最优指标:")
            print(f"- 验证集收益率: {self.best_metrics['val_return']:.2f}")
            print(f"- 验证集夏普比率: {self.best_metrics['val_sharpe']:.2f}")
            print(f"- 验证集最大回撤: {self.best_metrics['val_max_drawdown']:.2%}")
            
            # 设置done标志
            result["done"] = True

    def _evaluate(self, algorithm) -> dict:
        """在验证集上评估模型"""
        # 修改创建验证环境的方式
        env_config = algorithm.config["env_config"].copy()
        env_config["mode"] = "validation"  # 切换到验证模式
        
        # 使用注册的环境创建器
        val_env = MarketTimingEnv(env_config)
        
        # 收集验证集数据
        returns = []
        episode_length = []
        
        for _ in range(10):  # 运行10个episode
            state, _ = val_env.reset()
            done = False
            episode_return = 0
            steps = 0
            
            while not done:
                action = algorithm.compute_single_action(state)
                state, reward, done, _, _ = val_env.step(action)
                episode_return += reward
                steps += 1
            
            returns.append(episode_return)
            episode_length.append(steps)
        
        # 计算指标
        avg_return = np.mean(returns)
        std_return = np.std(returns)
        sharpe = avg_return / (std_return + 1e-6)
        
        # 计算最大回撤
        cumulative_returns = np.cumsum(returns)
        rolling_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (rolling_max - cumulative_returns) / rolling_max
        max_drawdown = np.max(drawdowns)
        
        return {
            "return": avg_return,
            "sharpe": sharpe,
            "max_drawdown": max_drawdown
        }

def trainer(config: AlgorithmConfig):
    # Tune config.
    # Need a WandB callback?
    tune_callbacks = []
    if args.wandb_key:
        tune_callbacks.append(
            WandbLoggerCallback(
                project=args.wandb_project or "rllib-examples",
                api_key=args.wandb_key,
                log_config=True,
            )
        )

    # 添加市场验证回调
    # config.callbacks(MarketValidationCallback)
    
    # 配置训练参数
    stop = {
        "training_iteration": args.stop_iters,
        "timesteps_total": args.stop_timesteps,
        f"{ENV_RUNNER_RESULTS}/{EPISODE_RETURN_MEAN}": args.stop_reward,
    }
    
    # 创建训练器
    tuner = tune.Tuner(
        "PPO",  # 使用PPO算法
        param_space=config.to_dict(),
        run_config=train.RunConfig(
            name=args.exp_name,
            stop=stop,
            callbacks=tune_callbacks,
            checkpoint_config=train.CheckpointConfig(
                checkpoint_frequency=50,  # 每5轮保存一次检查点
                checkpoint_at_end=True,  # 训练结束时保存检查点
            ),
            storage_path="E:/lab/RoboQuant/pylab/ray_results",
        ),
    )
    
    # 开始训练
    tuner_results = tuner.fit()
    
    # 获取最佳结果
    metric = f"{ENV_RUNNER_RESULTS}/{EPISODE_RETURN_MEAN}"
    best_result = tuner_results.get_best_result(
        metric=metric,
        mode="max"
    )
    
    return tuner, best_result

def retrain_from_checkpoint(config: AlgorithmConfig, checkpoint_path: str):
    # - Change our config, such that the restored algo will have an env on the local
    # EnvRunner (to perform evaluation) and won't crash anymore (remove the crashing
    # callback).
    config.callbacks(None)
    # Rebuild the algorithm (just for testing purposes).
    test_algo = config.build()
    # Load algo's state from best checkpoint.
    test_algo.restore(checkpoint_path) # best_result.checkpoint
    # Perform some checks on the restored state.
    assert test_algo.training_iteration > 0
    # Evaluate on the restored algorithm.
    test_eval_results = test_algo.evaluate()
    # assert (test_eval_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward), test_eval_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]
    # Train one iteration to make sure, the performance does not collapse (e.g. due
    # to the optimizer weights not having been restored properly).
    test_results = test_algo.train()
    # assert (test_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward), test_results[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]
    # Stop the test algorithm again.
    test_algo.stop()

def continue_training(config: AlgorithmConfig, experiment_path: str):
    # Create a new Tuner from the existing experiment path (which contains the tuner's
    # own checkpoint file). Note that even the WandB logging will be continued without
    # creating a new WandB run name.
    restored_tuner = tune.Tuner.restore(
        path= experiment_path, # tuner_results.experiment_path,
        trainable=config.algo_class,
        param_space=config,
        # Important to set this to True b/c the previous trial had failed (due to our
        # `CrashAfterNIters` callback).
        resume_errored=True,
    )
    # Continue the experiment exactly where we left off.
    tuner_results = restored_tuner.fit()
    
    metric = f"{ENV_RUNNER_RESULTS}/{EPISODE_RETURN_MEAN}"
    best_result = tuner_results.get_best_result(metric=metric, mode="max")
    # assert best_result.metrics[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN] >= args.stop_reward

    # Not sure, whether this is really necessary, but we have observed the WandB
    # logger sometimes not logging some of the last iterations. This sleep here might
    # give it enough time to do so.
    # time.sleep(20)

    if args.as_test:
        check_learning_achieved(tuner_results, args.stop_reward, metric=metric)
        
    return best_result.checkpoint, best_result.metrics[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]


if __name__ == "__main__":
    # parser = ArgumentParser()
    parser = add_rllib_example_script_args(
        default_reward=2000.0,
        default_timesteps=10000000,
        default_iters=10000
    )

    parser.add_argument("--block_name", type=str, default="fut", help="market block name")
    parser.add_argument("--version", type=str, default="v2", help="model version")
    parser.add_argument("--data_file", type=str, default="", help="data file")
    parser.add_argument("--run_mode", type=str, default="train")
    parser.add_argument("--initial_amount", type=int, default=1e7, help="initial amount")

    parser.add_argument("--train_iteration", type=int, default=100, help="train iteration")
    parser.add_argument("--train_test_split", type=float, default=0.8, help="train test split")
    parser.add_argument("--validation_split", type=float, default=0.1, help="validation split")
    parser.add_argument("--sample_length", type=int, default=300, help="sample length")
    parser.add_argument("--random_episode", type=bool, default=True, help="random episode")

    parser.add_argument("--max_drawdown", type=float, default=0.5, help="max drawdown")
    parser.add_argument("--stop_loss", type=float, default=0.3, help="stop loss")
    parser.add_argument("--transaction_cost", type=float, default=0.0001, help="transaction cost")

    # By default, set `args.checkpoint_freq` to 1 and `args.checkpoint_at_end` to True.
    parser.set_defaults(
        checkpoint_freq=200,
        checkpoint_at_end=True,
        run_mode="train",
        num_env_runners=1,
        algo="PPO",  # 设置默认算法为PPO
        exp_name="market_timing_ppo",  # 设置实验名称
    )

    args = parser.parse_args()

    print("========================Arguments========================")
    pprint(vars(args))
    print("========================Arguments========================")

    register_env("markettiming_env", lambda env_config: MarketTimingEnv(env_config))
    env_config = {
        "name": "MarketTimingEnv",
        "version": args.version,
        "initial_amount": args.initial_amount,
        "gamma": 0.98,
        "mode": "train",
        "split_percent": args.train_test_split,
        "data_path": "E:/lab/RoboQuant/pylab/data",
        "data_file": args.data_file,
        "max_drawdown": args.max_drawdown,
        "stop_loss": args.stop_loss,
        "transaction_cost": args.transaction_cost,
    }
    
    # 配置PPO算法
    config = (
        get_trainable_cls(args.algo)
        .get_default_config()
        .api_stack( # 使用RLlib新的API堆栈
            enable_rl_module_and_learner=False, # ONNX is not supported by RLModule API yet.
            enable_env_runner_and_connector_v2=True,
        )
        .environment("markettiming_env", env_config=env_config)
        .training(
            gamma=0.99,
            lr=[
                [0, 1e-5],  # <- initial value at timestep 0
                [1000000, 1e-4],  # <- final value at 1M timesteps
            ],
            kl_coeff=0.3,
            # train_batch_size=4000,
            train_batch_size_per_learner=512,
        )
        .learners(
            num_learners=1,  # or >2 Learner 工作线程数
            num_cpus_per_learner=1,  # <- default
            num_gpus_per_learner=0,  # <- default
        )
        .resources(
            num_cpus_for_main_process=1,
            # num_gpus=0,
        )
        .env_runners(
            create_env_on_local_worker=True,
            num_env_runners=0,  # use this instead of `num_workers`
        )
        # .framework("torch")
        # .resources(num_gpus=int(os.environ.get("RLLIB_NUM_GPUS", "0")))
    )

    if args.run_mode == "train":
        tuner, best_result = trainer(config)
        if best_result:
            print(f"Best result: {best_result.metrics[ENV_RUNNER_RESULTS][EPISODE_RETURN_MEAN]}")
            export_onnx_v2(args.block_name, args.version, best_result.checkpoint)
    elif args.run_mode == "retrain_from_checkpoint":
        retrain_from_checkpoint(config, args.checkpoint_path)
    elif args.run_mode == "continue_training":
        best_checkpoint, best_avg_return = continue_training(config, args.experiment_path)
        print(f"Best checkpoint: {best_checkpoint}")
        print(f"Best avg. return: {best_avg_return}")
        export_onnx(args.block_name, args.version, best_checkpoint)
    else:
        raise ValueError("Invalid run mode.")

# C:/Users/<USER>/ray_results/market_timing_ppo/