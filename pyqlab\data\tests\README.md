# data模块单元测试

本目录包含pyqlab.data模块的单元测试。

## 测试内容

测试覆盖了以下组件：

1. **Pipeline类**：测试数据处理管道的功能，包括：
   - 词汇表生成
   - 数据加载
   - K线数据转换为bar token

2. **BarDataset类**：测试K线数据集的功能，包括：
   - 初始化
   - 词汇表大小获取
   - 块大小获取
   - 索引转换
   - 数据集长度计算
   - 样本获取

3. **FTSDataset类**：测试因子时序数据集的功能，包括：
   - 初始化
   - 配置
   - 数据设置
   - 实例数量获取
   - 模型输入配置保存
   - 数据准备
   - 数据加载
   - 数据集长度计算
   - 不同模型类型的样本获取

4. **data_api模块**：测试数据API功能，包括：
   - 数据集获取

## 运行测试

可以通过以下方式运行测试：

1. 运行所有测试：
   ```
   python -m pyqlab.data.tests.run_tests
   ```

2. 运行单个测试文件：
   ```
   python -m pyqlab.data.tests.test_pipeline
   python -m pyqlab.data.tests.test_dataset_bar
   python -m pyqlab.data.tests.test_dataset_fts
   python -m pyqlab.data.tests.test_data_api
   ```

3. 运行特定测试方法：
   ```
   python -m pyqlab.data.tests.test_pipeline TestPipeline.test_get_vocab
   ```

## 测试依赖

测试需要以下依赖：
- unittest
- pandas
- numpy
- torch
- unittest.mock 