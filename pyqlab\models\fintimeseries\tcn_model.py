import torch
import torch.nn as nn
from pyqlab.models.layers.common import TemporalConvNet
import math
import numpy as np

class TcnModel(nn.Module):
    def __init__(self, 
                  num_embeds=[72],
                  # num_input=120,
                  # output_size=1,
                  num_channels=10,
                  kernel_size=3,
                  dropout=0.2,
                  out_channels=(200, 200, 200),
                  ins_nums=(0,51,51,0),
                 ):
        super().__init__()
        print(num_embeds, num_channels, dropout, out_channels, ins_nums)
        assert len(ins_nums) == 4 and ins_nums[0] == 0 and ins_nums[1] != 0
        num_dims = []
        for num_embed in num_embeds:
            num_dims.append(math.ceil(np.sqrt(num_embed)))

        self.embedding_layers = nn.ModuleList()
        for i in range(len(num_dims)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=num_embeds[i], embedding_dim=num_dims[i]))
        num_input = sum(ins_nums) + sum(num_dims)
        num_outpt = 1
        self.tcn = TemporalConvNet(num_input, out_channels, kernel_size, dropout=dropout)
        self.linear = nn.Linear(out_channels[-1], num_outpt)
        # self.sigmoid = nn.Sigmoid()

    def forward(self, code_ids, x):
        assert len(code_ids.shape) > 2
        assert code_ids.shape[-1] == len(self.embedding_layers) and len(self.embedding_layers) > 0
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](code_ids[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data = torch.cat([embedded_data, category_data], dim=-1)
    
        x = torch.cat([x, embedded_data], dim=-1)
        x = x.transpose(1, 2)
        # x = x.reshape(x.shape[0], x.shape[1], 3, x.shape[2]//3)

        output = self.tcn(x)
        output = self.linear(output[:, :, -1])
        # output = self.sigmoid(output)
        return output.view(-1)
