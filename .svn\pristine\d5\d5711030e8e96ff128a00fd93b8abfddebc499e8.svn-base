import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils import weight_norm
import math

# 位置编码 --------------------------------------------

class PositionalEmbedding(nn.Module):
    # 位置编码
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        # Compute the positional encodings once in log space.
        pe = torch.zeros(max_len, d_model).float()
        pe.require_grad = False

        position = torch.arange(0, max_len).float().unsqueeze(1)
        div_term = (torch.arange(0, d_model, 2).float() * -(math.log(10000.0) / d_model)).exp()

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        return self.pe[:, :x.size(1)]

  
class RotaryPositionalEmbedding(nn.Module):
    # 旋转位置编码
    def __init__(self, d_model, max_len=5000, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, d_model, 2).float() / d_model))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_len
        self.precompute_freqs_cis(max_len)

    def precompute_freqs_cis(self, max_seq_len):
        t = torch.arange(max_seq_len, device=self.inv_freq.device, dtype=self.inv_freq.dtype)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x, seq_len=None):
        if seq_len > self.max_seq_len_cached:
            self.precompute_freqs_cis(seq_len)
        return self.cos_cached[:, :, :seq_len, ...], self.sin_cached[:, :, :seq_len, ...]

def apply_rotary_pos_emb(q, k, cos, sin, offset: int = 0):
    cos = cos[:, :, offset : q.shape[-2] + offset, :]
    sin = sin[:, :, offset : q.shape[-2] + offset, :]
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed

def rotate_half(x):
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

class PositionalEmbeddingFactory:
    # 位置编码工厂
    @staticmethod
    def get_positional_embedding(embed_type, d_model, max_len=5000):
        if embed_type == 'learned':
            return nn.Embedding(max_len, d_model)
        elif embed_type == 'sinusoidal':
            return PositionalEmbedding(d_model, max_len)
        elif embed_type == 'rope':
            return RotaryPositionalEmbedding(d_model, max_len)
        else:
            raise ValueError(f"Unknown positional embedding type: {embed_type}")


class TokenEmbedding(nn.Module):
    # 标记嵌入
    def __init__(self, c_in, d_model):
        super(TokenEmbedding, self).__init__()
        padding = 1 if torch.__version__ >= '1.5.0' else 2
        self.tokenConv = nn.Conv1d(in_channels=c_in, out_channels=d_model,
                                   kernel_size=3, padding=padding, padding_mode='circular', bias=False)
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(
                    m.weight, mode='fan_in', nonlinearity='leaky_relu')

    def forward(self, x):
        x = self.tokenConv(x.permute(0, 2, 1)).transpose(1, 2)
        return x


class FixedEmbedding(nn.Module):
    # 固定嵌入
    def __init__(self, c_in, d_model):
        super(FixedEmbedding, self).__init__()

        w = torch.zeros(c_in, d_model).float()
        w.require_grad = False

        position = torch.arange(0, c_in).float().unsqueeze(1)
        div_term = (torch.arange(0, d_model, 2).float()
                    * -(math.log(10000.0) / d_model)).exp()

        w[:, 0::2] = torch.sin(position * div_term)
        w[:, 1::2] = torch.cos(position * div_term)

        self.emb = nn.Embedding(c_in, d_model)
        self.emb.weight = nn.Parameter(w, requires_grad=False)

    def forward(self, x):
        return self.emb(x).detach()
    
class CommonEmbedding(nn.Module):
    # 通用嵌入
    def __init__(self, embds_num: list, d_model) -> None:
        super(CommonEmbedding, self).__init__()
        self.embedding_layers = nn.ModuleList()
        for i in range(len(embds_num)):
            self.embedding_layers.append(nn.Embedding(num_embeddings=embds_num[i], embedding_dim=d_model))

    def forward(self, embds):
        # print(embds.shape)
        embedded_data = None
        for i in range(len(self.embedding_layers)):
            category_data = self.embedding_layers[i](embds[:, :, i])
            if embedded_data is None:
                embedded_data = category_data
            else:
                embedded_data += category_data
        return embedded_data

# 时间编码 --------------------------------------------

class PeriodicTimeEncoding(nn.Module):
    # 周期性时间编码
    def __init__(self, d_model, max_len=1000):
        """
        初始化周期性时间编码。

        参数:
        - d_model: 嵌入维度
        - max_len: 最大序列长度，默认为1000。如果训练序列长度为30，建议将max_len设置为大于等于30。
        """
        super().__init__()
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        return self.pe[:, :x.size(1)]
  

class TimeFeatureEmbedding(nn.Module):
    # 时间特征编码
    def __init__(self, d_model, embed_type='timeF', freq='h'):
        super().__init__()
        freq_map = {'h': 4, 't': 5, 's': 6, 'm': 1, 'a': 1, 'w': 2, 'd': 3, 'b': 3}
        d_inp = freq_map[freq]
        self.embed = nn.Linear(d_inp, d_model, bias=False)

    def forward(self, x):
        return self.embed(x)

class RelativeTimeEncoding(nn.Module):
    # 相对时间编码
    def __init__(self, d_model, max_len=1000):
        super().__init__()
        self.encoding = nn.Parameter(torch.randn(max_len, d_model))

    def forward(self, x):
        seq_len = x.size(1)
        return self.encoding[:seq_len, :]

class ContinuousTimeEmbedding(nn.Module):
    # 连续时间编码
    def __init__(self, d_model):
        super().__init__()
        self.linear = nn.Linear(1, d_model)

    def forward(self, timestamp):
        return self.linear(timestamp.unsqueeze(-1))

class MultiScaleTimeEncoding(nn.Module):
    # 多尺度时间编码
    def __init__(self, d_model, n_scales=3):
        """
        初始化多尺度时间编码。

        参数:
        - d_model: 嵌入维度
        - n_scales: 多尺度数量
        """
        super().__init__()
        self.encodings = nn.ModuleList([
            PeriodicTimeEncoding(d_model // n_scales, 10**i) 
            for i in range(n_scales)
        ])
    def forward(self, x):
        return torch.cat([enc(x) for enc in self.encodings], dim=-1)

class AdaptiveTimeEncoding(nn.Module):
    # 自适应时间编码
    def __init__(self, d_model, max_len=1000):
        super().__init__()
        self.encoding = nn.Parameter(torch.randn(max_len, d_model))
        self.adaptive_weight = nn.Parameter(torch.ones(1))

    def forward(self, x):
        return self.adaptive_weight * self.encoding[:x.size(1), :]
    
class TemporalEmbedding(nn.Module):
    def __init__(self, d_model, time_encoding='fixed', freq='t'):
        super(TemporalEmbedding, self).__init__()

        minute_size = 12
        hour_size = 24
        weekday_size = 7
        day_size = 32
        month_size = 13

        Embed = FixedEmbedding if time_encoding == 'fixed' else nn.Embedding
        if freq == 't':
            self.minute_embed = Embed(minute_size, d_model)
        self.hour_embed = Embed(hour_size, d_model)
        self.weekday_embed = Embed(weekday_size, d_model)
        self.day_embed = Embed(day_size, d_model)
        self.month_embed = Embed(month_size, d_model)

    def forward(self, x):
        x = x.long()
        minute_x = self.minute_embed(x[:, :, 4]) if hasattr(
            self, 'minute_embed') else 0.
        hour_x = self.hour_embed(x[:, :, 3])
        weekday_x = self.weekday_embed(x[:, :, 2])
        day_x = self.day_embed(x[:, :, 1])
        month_x = self.month_embed(x[:, :, 0])

        return hour_x + weekday_x + day_x + month_x + minute_x


class TimeFeatureEmbedding(nn.Module):
    # The following frequencies are supported:
    # Y   - yearly
    #     alias: A
    # M   - monthly
    # W   - weekly
    # D   - daily
    # B   - business days
    # H   - hourly
    # T   - minutely
    #     alias: min
    # S   - secondly
    def __init__(self, d_model, freq='t'):
        super(TimeFeatureEmbedding, self).__init__()

        freq_map = {'h': 4, 't': 5, 's': 6,
                    'm': 1, 'a': 1, 'w': 2, 'd': 3, 'b': 3}
        d_inp = freq_map[freq]
        self.embed = nn.Linear(d_inp, d_model, bias=False)

    def forward(self, x):
        return self.embed(x)

# 数据嵌入 --------------------------------------------
# 数据维度嵌入：代码+时间+位置
# 代码：分期货与股票两种情况：期货取值范围[0, 68]，共69个值，股票取值范围[0, 1799]，共1800个值
# 时间：时间先进行编码，编码有两种方法：TemporalEmbedding和TimeFeatureEmbedding，编码后再嵌入
# 位置：位置编码有两种方法：PositionalEmbedding和RotaryEmbedding，RotaryEmbedding等方法



class DataEmbedding(nn.Module):
    def __init__(self, c_in, d_model, embed_type='fixed', freq='h', dropout=0.1):
        super(DataEmbedding, self).__init__()

        self.value_embedding = TokenEmbedding(c_in=c_in, d_model=d_model)
        self.position_embedding = PositionalEmbedding(d_model=d_model)
        self.temporal_embedding = TemporalEmbedding(d_model=d_model, embed_type=embed_type,
                                                    freq=freq) if embed_type != 'timeF' else TimeFeatureEmbedding(
            d_model=d_model, embed_type=embed_type, freq=freq)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, x, x_mark):
        if x_mark is None:
            x = self.value_embedding(x) + self.position_embedding(x)
        else:
            x = self.value_embedding(x) + \
                self.temporal_embedding(x_mark) + \
                self.position_embedding(x)
        return self.dropout(x)

class DataEmbedding2(nn.Module):
    """
    增加了embds参数，用于处理除位置，时间维度外的如代码，分类等维度的embedding
    """
    def __init__(self, c_in, d_model, embed_type='fixed', freq='h', embds_num=[], dropout=0.1):
        super(DataEmbedding2, self).__init__()

        self.value_embedding = TokenEmbedding(c_in=c_in, d_model=d_model)
        self.position_embedding = PositionalEmbedding(d_model=d_model)
        self.temporal_embedding = TemporalEmbedding(d_model=d_model, embed_type=embed_type,
                                                    freq=freq) if embed_type != 'timeF' else TimeFeatureEmbedding(
            d_model=d_model, embed_type=embed_type, freq=freq)
        self.other_embedding = CommonEmbedding(embds_num, d_model)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, embds, x, x_mark):
        if x_mark is None:
            x = self.value_embedding(x) + \
                self.position_embedding(x) + \
                self.other_embedding(embds)
        else:
            x = self.value_embedding(x) + \
                self.temporal_embedding(x_mark) + \
                self.position_embedding(x) + \
                self.other_embedding(embds)
        return self.dropout(x)


class DataEmbedding_inverted(nn.Module):
    def __init__(self, c_in, d_model, embed_type='fixed', freq='t', embds_num=[], dropout=0.1):
        super(DataEmbedding_inverted, self).__init__()
        self.value_embedding = nn.Linear(c_in, d_model)
        self.dropout = nn.Dropout(p=dropout)
        if len(embds_num) > 0:
            self.other_embedding = CommonEmbedding(embds_num, d_model)
        else:
            self.other_embedding = None

    def forward(self, embds, x, x_mark):
        x = x.permute(0, 2, 1)
        # x: [Batch Variate Time]
        if x_mark is None:
            x = self.value_embedding(x)
        else:
            x = self.value_embedding(torch.cat([x, x_mark.permute(0, 2, 1)], 1))

        if self.other_embedding is not None:
            embds = embds.permute(0, 2, 1)
            x += self.other_embedding(embds)
        # x: [Batch Variate d_model]
        return self.dropout(x)


class DataEmbedding_wo_pos(nn.Module):
    def __init__(self, c_in, d_model, embed_type='fixed', freq='h', embds_num=[], dropout=0.1):
        super(DataEmbedding_wo_pos, self).__init__()

        self.value_embedding = TokenEmbedding(c_in=c_in, d_model=d_model)
        self.position_embedding = PositionalEmbedding(d_model=d_model)
        self.temporal_embedding = TemporalEmbedding(d_model=d_model, embed_type=embed_type,
                                                    freq=freq) if embed_type != 'timeF' else TimeFeatureEmbedding(
            d_model=d_model, embed_type=embed_type, freq=freq)
        if len(embds_num) > 0:
            self.other_embedding = CommonEmbedding(embds_num, d_model)
        else:
            self.other_embedding = None
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, embds, x, x_mark):
        if x_mark is None:
            x = self.value_embedding(x)
        else:
            x = self.value_embedding(x) + self.temporal_embedding(x_mark)
            
        if self.other_embedding is not None:
            x += self.other_embedding(embds)

        return self.dropout(x)


class PatchEmbedding(nn.Module):
    def __init__(self, d_model, patch_len, stride, padding, dropout):
        super(PatchEmbedding, self).__init__()
        # Patching
        self.patch_len = patch_len
        self.stride = stride
        self.padding_patch_layer = nn.ReplicationPad1d((0, padding))

        # Backbone, Input encoding: projection of feature vectors onto a d-dim vector space
        self.value_embedding = nn.Linear(patch_len, d_model, bias=False)

        # Positional embedding
        self.position_embedding = PositionalEmbedding(d_model)

        # Residual dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # do patching
        n_vars = x.shape[1]
        x = self.padding_patch_layer(x)
        x = x.unfold(dimension=-1, size=self.patch_len, step=self.stride)
        x = torch.reshape(x, (x.shape[0] * x.shape[1], x.shape[2], x.shape[3]))
        # Input encoding
        x = self.value_embedding(x) + self.position_embedding(x)
        return self.dropout(x), n_vars
