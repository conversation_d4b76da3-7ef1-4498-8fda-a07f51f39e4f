# K线数据样本优化方案

## 概述

针对当前K线数据训练样本极其不平衡的问题，特别是min5和min1周期数据的质量问题，本方案从源头优化样本数据，提供了一套完整的数据质量检查和样本平衡处理工具。

## 主要问题分析

### 1. Token分布严重不平衡
- 某些token（如795）占比过高（>30%）
- 模型总是预测高频token，缺乏多样性
- 基尼系数过高（>0.7），表明严重不平衡

### 2. 短周期数据质量问题
- Min1/Min5数据噪声大，异常跳空频繁
- 期货合约切换导致的数据不连续
- 成交量过低的无效数据点

### 3. 词汇表利用率低
- 大量token从未出现，浪费模型容量
- 有效词汇表利用率<30%

## 优化方案

### 1. 数据质量检查 (`check_data_quality`)

#### 基本数据完整性检查
- 移除缺失值（NaN）
- 验证价格逻辑性（high >= low, high >= open/close等）
- 检查价格为正值

#### 异常数据过滤
- **跳空检测**: 识别异常价格跳空（>5%为min1/min5，>10%为日线）
- **成交量过滤**: 移除成交量过低的数据点
- **ATR异常值检测**: 识别ATR计算异常的数据

#### 时间连续性检查
- 检测交易日间隔
- 识别数据缺失段

### 2. 样本平衡处理 (`balance_token_distribution`)

#### 自适应平衡策略
```python
if gini > 0.7:
    # 严重不平衡 - 激进下采样
    max_frequency = 0.10
elif gini > 0.5:
    # 中度不平衡 - 温和下采样  
    max_frequency = 0.15
else:
    # 相对平衡 - 无需处理
    pass
```

#### 下采样方法
- **概率下采样**: 为高频token计算保留概率
- **频率限制**: 限制单个token最大出现频率
- **随机种子**: 确保结果可重现

### 3. 参数优化

#### 周期特定参数
```python
# Min1/Min5 短周期
max_gap_threshold = 5.0%      # 更严格的跳空阈值
min_volume_threshold = 100    # 最小成交量要求
noise_filter_enabled = True   # 启用噪声过滤

# 日线数据
max_gap_threshold = 10.0%     # 相对宽松的跳空阈值
min_volume_threshold = 0      # 无成交量要求
noise_filter_enabled = False  # 禁用噪声过滤
```

## 使用方法

### 1. 数据质量分析

```bash
# 分析现有数据质量
python analyze_data_quality.py \
    --data_path "f:/featdata/barenc/db2" \
    --market "fut" \
    --period "min1" \
    --block_name "top" \
    --save_plots True
```

### 2. 优化数据生成

```bash
# 生成优化的训练数据
python hdl_bar2.py \
    --data_path "f:/hqdata" \
    --save_path "f:/featdata/barenc/optimized" \
    --market "fut" \
    --period "min1" \
    --block_name "top" \
    --enable_quality_check True \
    --enable_balance True \
    --balance_method "adaptive" \
    --max_token_frequency 0.12 \
    --max_gap_threshold 5.0 \
    --min_volume_threshold 100
```

### 3. 批量处理

```bash
# 运行完整的优化流程
optimize_data_quality.bat
```

## 新增参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--enable_quality_check` | True | 启用数据质量检查 |
| `--enable_balance` | True | 启用样本平衡处理 |
| `--balance_method` | adaptive | 平衡方法：none/undersample/adaptive |
| `--max_token_frequency` | 0.15 | 单个token最大频率 |
| `--max_gap_threshold` | auto | 最大跳空阈值(%) |
| `--min_volume_threshold` | auto | 最小成交量阈值 |

## 效果预期

### 1. Token分布改善
- 基尼系数从>0.8降低到<0.6
- 高频token占比从>30%降低到<15%
- 词汇表利用率提升到>50%

### 2. 数据质量提升
- 移除5-15%的低质量数据点
- 减少异常跳空对模型的干扰
- 提高数据的时间连续性

### 3. 模型训练改善
- 预测多样性显著提升
- 收敛速度加快
- 验证集性能提升
- 减少过拟合风险

## 质量监控

### 自动生成的统计报告
```
数据质量统计报告
============================================================
数据质量检查:
  总数据条数: 1,234,567
  过滤条数: 123,456 (10.0%)
  异常跳空: 12,345
  保留条数: 1,111,111

样本平衡设置:
  平衡方法: adaptive
  最大token频率: 12.0%
  噪声过滤: 启用
============================================================
```

### 可视化分析图表
- Token分布直方图
- 累积分布曲线
- 频率分析图
- 基尼系数趋势

## 最佳实践建议

### 1. 分阶段优化
1. 先运行数据质量分析，了解现状
2. 根据分析结果调整参数
3. 生成优化数据
4. 对比训练效果

### 2. 参数调优
- **严重不平衡**: `balance_method=adaptive`, `max_token_frequency=0.10`
- **中度不平衡**: `balance_method=undersample`, `max_token_frequency=0.15`
- **轻度不平衡**: `balance_method=none`

### 3. 周期特定设置
- **Min1**: 最严格的质量检查和平衡处理
- **Min5**: 中等强度的处理
- **日线**: 相对宽松的处理

### 4. 训练配合
- 使用Focal Loss或加权交叉熵
- 启用多样性正则化
- 监控预测分布的多样性

## 注意事项

1. **数据备份**: 处理前备份原始数据
2. **参数测试**: 在小数据集上测试参数效果
3. **效果验证**: 通过训练对比验证优化效果
4. **持续监控**: 定期检查数据质量和分布变化

## 故障排除

### 常见问题
1. **内存不足**: 使用批处理模式，减小batch_size
2. **处理时间长**: 启用多进程，或分批处理
3. **过度过滤**: 调整阈值参数，避免过度过滤数据

### 调试建议
- 查看质量统计报告
- 检查生成的分析图表
- 对比处理前后的数据分布
- 监控训练过程中的指标变化
