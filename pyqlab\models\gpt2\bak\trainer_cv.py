import torch
from torch.utils.data import Dataset, DataLoader, Subset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    AdamW,
    get_linear_schedule_with_warmup,
    logging as hf_logging,
)
from sklearn.model_selection import KFold
import numpy as np
from tqdm.auto import tqdm
import logging
import os

# 减少Hugging Face不必要的日志输出
hf_logging.set_verbosity_error()

# --- 1. 配置类 ---
class TrainingConfig:
    MODEL_NAME = "gpt2"  # 例如: "gpt2", "EleutherAI/gpt-neo-125M"
    NUM_EPOCHS = 3       # 每个fold训练的轮数
    BATCH_SIZE = 4       # 根据GPU内存调整
    LEARNING_RATE = 5e-5
    MAX_LENGTH = 128     # 输入序列的最大长度
    NUM_FOLDS = 5        # 交叉验证的折数
    DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    RANDOM_SEED = 42
    SAVE_BEST_MODEL_PER_FOLD = True # 是否保存每折中表现最好的模型
    MODEL_SAVE_DIR = "cv_models"    # 模型保存目录

# --- 2. 自定义数据集 ---
class CustomTextDataset(Dataset):
    def __init__(self, texts, tokenizer, max_length):
        self.tokenizer = tokenizer
        self.texts = texts
        self.max_length = max_length

        # GPT-2等模型可能没有pad_token，可以使用eos_token作为pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            logging.info(f"Tokenizer pad_token was None. Set to eos_token: {self.tokenizer.eos_token}")

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = str(self.texts[idx]) # 确保是字符串
        # 对于因果语言模型 (Causal LM)，输入和标签通常是相同的序列
        # 模型的目标是预测序列中的下一个token
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding="max_length", # 填充到max_length
            return_tensors="pt"   # 返回PyTorch张量
        )
        # input_ids将用作输入和标签 (模型内部会处理标签的移位)
        return {
            "input_ids": encoding["input_ids"].squeeze(), # (max_length)
            "attention_mask": encoding["attention_mask"].squeeze(), # (max_length)
            "labels": encoding["input_ids"].squeeze() # (max_length)
        }

# --- 3. 训练和评估函数 ---
def train_epoch(model, data_loader, optimizer, scheduler, device):
    model.train()
    total_loss = 0
    progress_bar = tqdm(data_loader, desc="Training", leave=False)
    for batch_idx, batch in enumerate(progress_bar):
        optimizer.zero_grad()

        input_ids = batch["input_ids"].to(device)
        attention_mask = batch["attention_mask"].to(device)
        labels = batch["labels"].to(device)

        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels
        )
        loss = outputs.loss
        loss.backward()
        optimizer.step()
        scheduler.step()

        total_loss += loss.item()
        progress_bar.set_postfix({'loss': loss.item()})
        # if batch_idx % 50 == 0: # Optional: Log progress
        #     logging.info(f"  Batch {batch_idx}/{len(data_loader)}, Loss: {loss.item():.4f}")


    return total_loss / len(data_loader)

def evaluate_epoch(model, data_loader, device):
    model.eval()
    total_loss = 0
    total_perplexity_proxy = 0 # 基于loss的困惑度代理
    progress_bar = tqdm(data_loader, desc="Evaluating", leave=False)

    with torch.no_grad():
        for batch in progress_bar:
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)

            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            loss = outputs.loss
            total_loss += loss.item()
            # Perplexity = exp(loss) 是一个常用的代理指标
            total_perplexity_proxy += torch.exp(loss).item()
            progress_bar.set_postfix({'val_loss': loss.item()})


    avg_loss = total_loss / len(data_loader)
    avg_perplexity_proxy = total_perplexity_proxy / len(data_loader)
    return avg_loss, avg_perplexity_proxy

# --- 4. 交叉验证主函数 ---
def cross_validate_gpt(texts_data, config: TrainingConfig):
    # 设置随机种子
    np.random.seed(config.RANDOM_SEED)
    torch.manual_seed(config.RANDOM_SEED)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(config.RANDOM_SEED)

    # K-Fold交叉验证器
    kf = KFold(n_splits=config.NUM_FOLDS, shuffle=True, random_state=config.RANDOM_SEED)
    fold_results = []

    # 加载分词器 (只需一次)
    logging.info(f"Loading tokenizer: {config.MODEL_NAME}")
    tokenizer = AutoTokenizer.from_pretrained(config.MODEL_NAME)
    # 确保pad_token存在
    if tokenizer.pad_token is None:
        tokenizer.add_special_tokens({'pad_token': '[PAD]'}) # 或者 tokenizer.pad_token = tokenizer.eos_token
        logging.info(f"Added [PAD] token to tokenizer.")
    
    full_dataset = CustomTextDataset(texts_data, tokenizer, config.MAX_LENGTH)
    logging.info(f"Dataset created with {len(full_dataset)} samples.")

    for fold, (train_indices, val_indices) in enumerate(kf.split(texts_data)):
        logging.info(f"\n--- Fold {fold + 1}/{config.NUM_FOLDS} ---")

        # 创建当前fold的数据子集和DataLoader
        train_subset = Subset(full_dataset, train_indices)
        val_subset = Subset(full_dataset, val_indices)

        train_loader = DataLoader(train_subset, batch_size=config.BATCH_SIZE, shuffle=True)
        val_loader = DataLoader(val_subset, batch_size=config.BATCH_SIZE, shuffle=False)
        logging.info(f"Fold {fold+1}: Train size: {len(train_subset)}, Val size: {len(val_subset)}")

        # 为每个fold重新加载模型 (保证权重是初始的)
        logging.info(f"Loading model for fold {fold+1}: {config.MODEL_NAME}")
        model = AutoModelForCausalLM.from_pretrained(config.MODEL_NAME)
        model.resize_token_embeddings(len(tokenizer)) # 如果添加了新token，需要调整
        model.to(config.DEVICE)

        # 优化器和学习率调度器
        optimizer = AdamW(model.parameters(), lr=config.LEARNING_RATE)
        total_steps = len(train_loader) * config.NUM_EPOCHS
        scheduler = get_linear_schedule_with_warmup(optimizer,
                                                    num_warmup_steps=0, # 可以根据需要调整预热步数
                                                    num_training_steps=total_steps)

        best_val_loss = float('inf')
        fold_metrics = {'train_losses': [], 'val_losses': [], 'val_perplexities': []}

        for epoch in range(config.NUM_EPOCHS):
            logging.info(f"  Epoch {epoch + 1}/{config.NUM_EPOCHS}")

            train_loss = train_epoch(model, train_loader, optimizer, scheduler, config.DEVICE)
            val_loss, val_perplexity = evaluate_epoch(model, val_loader, config.DEVICE)

            logging.info(f"    Train Loss: {train_loss:.4f}")
            logging.info(f"    Val Loss  : {val_loss:.4f} | Val Perplexity (proxy): {val_perplexity:.4f}")

            fold_metrics['train_losses'].append(train_loss)
            fold_metrics['val_losses'].append(val_loss)
            fold_metrics['val_perplexities'].append(val_perplexity)

            if val_loss < best_val_loss:
                best_val_loss = val_loss
                logging.info(f"    New best validation loss: {best_val_loss:.4f}")
                if config.SAVE_BEST_MODEL_PER_FOLD:
                    if not os.path.exists(config.MODEL_SAVE_DIR):
                        os.makedirs(config.MODEL_SAVE_DIR)
                    model_save_path = os.path.join(config.MODEL_SAVE_DIR, f"model_fold_{fold+1}_best.pt")
                    torch.save(model.state_dict(), model_save_path)
                    logging.info(f"    Best model for fold {fold+1} saved to {model_save_path}")

        fold_results.append({
            'fold': fold + 1,
            'best_val_loss': best_val_loss,
            'final_val_perplexity': fold_metrics['val_perplexities'][-1] if fold_metrics['val_perplexities'] else float('inf'),
            'metrics_history': fold_metrics
        })

        # 清理GPU内存 (可选，但对多fold有帮助)
        del model, optimizer, scheduler
        if config.DEVICE.type == 'cuda':
            torch.cuda.empty_cache()

    # --- 5. 结果汇总 ---
    logging.info("\n--- Cross-Validation Summary ---")
    all_best_val_losses = [res['best_val_loss'] for res in fold_results]
    all_final_val_perplexities = [res['final_val_perplexity'] for res in fold_results]

    for i, res in enumerate(fold_results):
        logging.info(f"Fold {res['fold']}: Best Val Loss = {res['best_val_loss']:.4f}, Final Val Perplexity = {res['final_val_perplexity']:.4f}")

    avg_best_val_loss = np.mean(all_best_val_losses)
    std_best_val_loss = np.std(all_best_val_losses)
    avg_final_val_ppl = np.mean(all_final_val_perplexities)
    std_final_val_ppl = np.std(all_final_val_perplexities)

    logging.info(f"\nAverage Best Validation Loss: {avg_best_val_loss:.4f} ± {std_best_val_loss:.4f}")
    logging.info(f"Average Final Validation Perplexity (proxy): {avg_final_val_ppl:.4f} ± {std_final_val_ppl:.4f}")

    return fold_results

# --- 6. 主程序入口和示例数据 ---
if __name__ == "__main__":
    # 配置日志记录
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 创建配置实例
    config = TrainingConfig()
    
    # 减小参数以便快速演示
    config.NUM_EPOCHS = 1 # 演示时只跑1轮
    config.BATCH_SIZE = 2 # 演示时用小批量
    config.MAX_LENGTH = 64 # 演示时用短序列
    config.NUM_FOLDS = 2   # 演示时只跑2折
    # config.MODEL_NAME = "EleutherAI/gpt-neo-125M" # 也可以换成小一点的预训练模型

    # 示例数据 (用你自己的文本数据替换)
    # 对于语言模型微调，这里应该是文本列表
    sample_texts = [
        "This is the first sample document for our GPT model.",
        "Another piece of text to illustrate the process.",
        "Cross-validation helps in getting a more robust estimate of model performance.",
        "GPT models are pre-trained on vast amounts of text data.",
        "Fine-tuning adapts these models to specific tasks or domains.",
        "The quick brown fox jumps over the lazy dog.",
        "Language modeling is a fundamental task in NLP.",
        "Let's ensure our framework handles various inputs.",
        "This framework should be general enough for different GPT variants.",
        "One more sentence to make the dataset slightly larger for folding."
    ] * 5 # 复制几次以增加数据量，使其能被分成fold

    logging.info(f"Starting cross-validation with {config.NUM_FOLDS} folds.")
    logging.info(f"Using device: {config.DEVICE}")
    logging.info(f"Model: {config.MODEL_NAME}, Epochs/fold: {config.NUM_EPOCHS}, Batch: {config.BATCH_SIZE}")

    # 运行交叉验证
    cv_results = cross_validate_gpt(sample_texts, config)

    # 你可以进一步处理或保存 `cv_results`
    # 例如，打印详细的每折指标历史
    # for res in cv_results:
    #     logging.info(f"\nFold {res['fold']} Metrics History:")
    #     for epoch_num, (tl, vl, vp) in enumerate(zip(res['metrics_history']['train_losses'],
    #                                                  res['metrics_history']['val_losses'],
    #                                                  res['metrics_history']['val_perplexities'])):
    #         logging.info(f"  Epoch {epoch_num+1}: TrainLoss={tl:.4f}, ValLoss={vl:.4f}, ValPPL={vp:.4f}")

    logging.info("Cross-validation finished.")