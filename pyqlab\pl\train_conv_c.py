
import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger

from pyqlab.models import PLData, PLDataModule
from pyqlab.models import PLModel

from pyqlab.data.data_api import get_dataset, get_model_name

import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import KFold
import os

def get_best_saved_model_filename(log_dir, sub_dir):
    model_files = {}
    log_dir = os.path.join(log_dir, sub_dir)
    for root, dirs, files in os.walk(log_dir):
        for file in files:
            if file.startswith("best-"):
                model_files[file] = root
    if model_files:
        file = min(model_files.keys())
        return f"{model_files[file]}\\{file}"
    else:
        return None
    
def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    return callbacks


def main(args):
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    print(args)

    dataset = get_dataset(ds_files=args.ds_files)

    for direct in ['long', 'short']:
        args.direct = direct
        x_data, y_data, embedding_data = dataset.prepare(
            direct=args.direct,
            win=args.num_channel,
            filter_win=args.filter_win,
        )

        pl.seed_everything(args.seed)

        # 创建回调函数
        callbacks = load_callbacks(args)

        full_dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(embedding_data))
        # 使用 KFold 分割数据集
        kfold = KFold(n_splits=args.k_folds, shuffle=True)

        # 创建一个数据模块列表，每个数据模块对应一个 fold
        for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
            print(f"=== Training fold {fold} ===")
            train_data = Subset(full_dataset, train_idx)
            val_data = Subset(full_dataset, val_idx)
            data_module = PLData(train_data, val_data, batch_size=args.batch_size, num_workers=args.num_workers, seed=args.seed)


            if fold > 0 and callbacks[0].stopped_epoch is not None:
                # 加载之前训练的模型
                print(f"Fold: {fold} Loading model from {callbacks[1].best_model_path}")
                model = PLModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
            else:
                if args.restart:
                    model_path = get_best_saved_model_filename(log_dir=args.log_dir, sub_dir=args.sub_dir)
                    if model_path is None:
                        raise Exception("No saved model found!")
                    print(f"=== Restart Training ===")
                    print(f"Loading model from {model_path}")
                    try:
                        model = PLModel.load_from_checkpoint(checkpoint_path=model_path)
                    except Exception as e:
                        print(f"Error: {e}")
                else:
                    print(f"=== New Training ===")
                    model = PLModel(**vars(args))

            # 创建训练器
            out_channels = eval(args.out_channels)
            logger = TensorBoardLogger(save_dir=args.log_dir, name=f'TimeSeriesConv2dc_{out_channels[2]}')
            trainer = Trainer(
                max_epochs=args.max_epochs,
                callbacks=callbacks,
                logger=logger,
            )
            trainer.fit(model, data_module)
            # 在每个数据集的训练之后，执行其他操作（如保存模型、记录日志等）
            # log_metrics(logger, trainer)

        # 训练完成后，保存编译最后一个模型
        if callbacks[0].stopped_epoch is not None:
            # 加载之前训练的模型
            best_score=callbacks[1].best_model_score.cpu().numpy()
            print(f"Best model to save {callbacks[1].best_model_path}")
            model = PLModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
            model.freeze()
            embedding = torch.zeros(1, args.num_channel, len(args.num_embeds)).to(torch.int32)
            example_input = torch.rand(1, args.num_channel, sum(args.ins_nums))
            model_name = get_model_name(args.version, args.ds_name, args.out_channels[2], best_score, args.direct)
            model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (embedding, example_input), export_params=True)
            dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")
            print(f"Model saved to： {model_name}.onnx")
            print("=== Training Finished ===\n\n")

if __name__ == '__main__':
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', choices=['10HF', '15HF'], type=str)
    parser.add_argument('--ds_files', default=["main.2023"], type=list)
    parser.add_argument('--direct', default='long', choices=['long', 'short', 'mls'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)

    # Data module ===========================
    parser.add_argument('--batch_size', default=256, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)

    # Model Hyperparameters =================
    parser.add_argument('--version', default='CV2DC', type=str)
    parser.add_argument('--model_name', default='time_series_model2dc', type=str)
    parser.add_argument('--loss', default='bce', choices=['bce', 'mse', 'l1'], type=str)
    parser.add_argument('--lr', default=1e-4, type=float)

    # model
    parser.add_argument('--num_embeds', default=[64, 5, 11], type=list)
    parser.add_argument('--num_channel', default=10, type=int) # 通道数,与上面的DataHander中的win保持一致
    parser.add_argument('--num_input', default=51, type=int)
    # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
    # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
    # "out_channels": (16, 32, 800, 256), 
    # "out_channels": (24, 48, 1200, 256), 
    parser.add_argument('--out_channels', default='(24, 48, 1200, 256)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 17)', type=str)
    parser.add_argument('--dropout', default=0.5, type=float)
    parser.add_argument('--activation', default='relu', choices=['relu', 'gelu', 'prelu', 'leakyrelu'], type=str)
    parser.add_argument('--pooling', default='max', choices=['max', 'avg'], type=str)

    # LR Scheduler
    # lr_scheduler 可以有以下选项：
    #     'step'：使用步进式学习率调度器
    #     'cosine'：使用余弦退火学习率调度器
    #     'plateau'：使用学习率衰减调度器
    #     'exponential'：使用指数衰减学习率调度器
    #     'reduce_on_plateau'：使用学习率衰减调度器，并在验证集上监测指标不再改善时降低学习率
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)

    # Restart Control
    parser.add_argument('--restart', default=False, type=bool)

    # Training Info
    parser.add_argument('--max_epochs', default=8, type=int)
    parser.add_argument('--early_stop', default=3, type=int)
    parser.add_argument('--min_delta', default=1e-4, type=float)
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--weight_decay', default=1e-5, type=float)
    parser.add_argument('--no_augment', action='store_true')
    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    parser.add_argument('--sub_dir', default='', type=str)
    
    # Others
    parser.add_argument('--model_dir', default='model', type=str)
    
    # Reset Some Default Trainer Arguments' Default Values
    # parser.set_defaults(max_epochs=10)

    args = parser.parse_args()

    main(args)
