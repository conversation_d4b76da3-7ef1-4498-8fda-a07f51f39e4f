# @OldAPIStack

from packaging.version import Version
import numpy as np
import ray
import ray.rllib.algorithms.ppo as ppo
import onnxruntime
import os
import shutil
import torch
from ray.tune.registry import get_trainable_cls, register_env
from pyqlab.rl.env.env_market_timing import MarketTimingEnv

if __name__ == "__main__":

    register_env("markettiming_env", lambda env_config: MarketTimingEnv(env_config))

    env_config = {
        "name": "MarketTimingEnv",
        "initial_amount": 1e5,
        "gamma": 0.98,
        "mode": "train",
        "split_percent": 0.9,
        "data_path": "e:/lab/roboquant/pylab/data/tickdata",
    }
    
    # Configure our PPO Algorithm.
    config = (
        ppo.PPOConfig()
        # ONNX is not supported by RLModule API yet.
        .api_stack(enable_rl_module_and_learner=False)
        .environment(env="markettiming_env", env_config=env_config)
        .env_runners(num_env_runners=1)
        .framework("torch")
    )

    outdir = "model_rl/MarketTimingEnv"
    if os.path.exists(outdir):
        shutil.rmtree(outdir)

    np.random.seed(1234)

    # We will run inference with this test batch
    test_data = {
        "obs": np.random.randint(0, 10, size=(10, 6)).astype(np.int32),
        "state_ins": np.array([0.0], dtype=np.float32),
    }

    # Start Ray and initialize a PPO Algorithm.
    ray.init()
    algo = config.build(env="markettiming_env")

    # You could train the model here
    # algo.train()

    # Let's run inference on the torch model
    policy = algo.get_policy()
    # 将输入数据转换为 PyTorch 张量
    result_pytorch, _ = policy.model(
        {
            "obs": torch.tensor(test_data["obs"], dtype=torch.int32),
        }
    )

    # Evaluate tensor to fetch numpy array
    result_pytorch = result_pytorch.detach().numpy()

    # This line will export the model to ONNX.
    policy.export_model(outdir, onnx=18)
    # Equivalent to:
    # algo.export_policy_model(outdir, onnx=18)

    # Import ONNX model.
    exported_model_file = os.path.join(outdir, "model.onnx")

    # Start an inference session for the ONNX model
    session = onnxruntime.InferenceSession(exported_model_file, None)

    # Pass the same test batch to the ONNX model
    if Version(torch.__version__) < Version("1.9.0"):
        # In torch < 1.9.0 the second input/output name gets mixed up
        test_data["state_outs"] = test_data.pop("state_ins")

    result_onnx = session.run(["output"], test_data)

    # These results should be equal!
    print("PYTORCH", result_pytorch)
    print("ONNX", result_onnx)
    probs = torch.nn.functional.softmax(torch.tensor(result_pytorch), dim=1)
    max_prob = torch.argmax(probs, dim=1)
    print("Max Probabilities", max_prob)

    assert np.allclose(
        result_pytorch, result_onnx
    ), "Model outputs are NOT equal. FAILED"
    print("Model outputs are equal. PASSED")
