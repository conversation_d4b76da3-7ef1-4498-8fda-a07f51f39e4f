import os
import sys
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import argparse
from datetime import datetime, timedelta
import time

# 尝试导入onnxruntime
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("警告: onnxruntime未安装，无法加载ONNX模型。可以使用 'pip install onnxruntime' 安装。")

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入回测器和相关模块
from pyqlab.models.gpt2.backtester import CandlestickLLMBacktester
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_llm import CandlestickLLM
from pyqlab.models.gpt2.utils import load_single_data
from pyqlab.models.gpt2.onnx_model_wrapper import OnnxModelWrapper

def load_onnx_model(model_path, device=None):
    """
    加载ONNX格式的CandlestickLLM模型

    Args:
        model_path: ONNX模型路径
        device: 计算设备

    Returns:
        加载的ONNX模型包装器
    """
    if not ONNX_AVAILABLE:
        raise ImportError("onnxruntime未安装，无法加载ONNX模型。请使用 'pip install onnxruntime' 安装。")

    print(f"加载ONNX模型: {model_path}")

    # 确定设备
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 配置ONNX运行时选项
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']
    session_options = ort.SessionOptions()
    session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    session_options.intra_op_num_threads = 4

    # 创建ONNX运行时会话
    try:
        session = ort.InferenceSession(
            model_path,
            providers=providers,
            sess_options=session_options
        )

        # 创建ONNX模型包装器
        model = OnnxModelWrapper(session, device=device, block_size=30)

        return model
    except Exception as e:
        print(f"加载ONNX模型失败: {str(e)}")
        raise

def load_model(model_path, vocab_size=None, code_size=10, device=None):
    """
    加载预训练的CandlestickLLM模型

    Args:
        model_path: 模型路径
        vocab_size: 词汇表大小，如果为None则从模型状态字典中推断
        code_size: 证券代码数量，如果为None则从模型状态字典中推断
        device: 计算设备

    Returns:
        加载的模型
    """
    # 确定设备
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 检查是否是ONNX模型
    if model_path.endswith('.onnx'):
        return load_onnx_model(model_path, device)

    # 尝试先加载检查点，获取模型配置
    try:
        checkpoint = torch.load(model_path, map_location=device)

        # 检查是否是训练器保存的检查点
        if 'model_state_dict' in checkpoint:
            # 使用model_state_dict
            state_dict = checkpoint['model_state_dict']

            # 尝试从state_dict推断模型配置
            if 'blocks.0.attn.c_attn.weight' in state_dict:
                # 从注意力权重推断d_model和n_head
                attn_weight_shape = state_dict['blocks.0.attn.c_attn.weight'].shape
                d_model = attn_weight_shape[1]  # 输入维度
                # 假设c_attn.weight的输出维度是3*d_model
                n_head = 4  # 默认值
                if d_model % 64 == 0:  # 假设每个头的维度是64
                    n_head = d_model // 64
            else:
                d_model = 768
                n_head = 12

            # 推断n_layer
            n_layer = 0
            while f'blocks.{n_layer}.ln_1.weight' in state_dict:
                n_layer += 1

            if n_layer == 0:
                n_layer = 12  # 默认值

            # 推断block_size
            if 'position_encoding.pe' in state_dict:
                block_size = state_dict['position_encoding.pe'].shape[1]
            else:
                block_size = 30  # 默认值

            # 推断vocab_size和code_size
            if 'token_embedding.weight' in state_dict:
                vocab_size = state_dict['token_embedding.weight'].shape[0]

            if 'code_embedding.weight' in state_dict:
                code_size = state_dict['code_embedding.weight'].shape[0]

            print(f"从state_dict推断的配置: vocab_size={vocab_size}, n_layer={n_layer}, n_head={n_head}, d_model={d_model}, block_size={block_size}, code_size={code_size}")
        else:
            # 使用默认配置
            n_layer = 8
            n_head = 16
            d_model = 128
            block_size = 30
    except Exception as e:
        print(f"加载检查点获取配置时出错: {e}")
        print("使用默认模型配置")
        n_layer = 12
        n_head = 12
        d_model = 768
        block_size = 30

    # 创建模型
    print(f"创建模型: n_layer={n_layer}, n_head={n_head}, d_model={d_model}, block_size={block_size}")
    model = CandlestickLLM(
        vocab_size=40005,
        code_size=27,
        block_size=30,
        n_layer=8,
        n_head=16,
        d_model=128,
        dropout=0.0,
        use_time_features=True
    )

    # 加载检查点已经在前面完成，这里直接使用

    # 检查检查点格式
    if 'model_state_dict' in checkpoint:
        # 训练器保存的检查点格式
        model.load_state_dict(checkpoint['model_state_dict'])
    elif isinstance(checkpoint, dict) and all(k.startswith('module.') for k in checkpoint.keys()):
        # DataParallel包装的模型
        from collections import OrderedDict
        new_state_dict = OrderedDict()
        for k, v in checkpoint.items():
            name = k[7:]  # 去掉 'module.' 前缀
            new_state_dict[name] = v
        model.load_state_dict(new_state_dict)
    else:
        # 尝试直接加载
        try:
            model.load_state_dict(checkpoint)
        except Exception as e:
            print(f"加载模型时出错: {e}")
            print("尝试使用宽松加载模式...")
            model.load_state_dict(checkpoint, strict=False)
            print("警告: 模型参数不完全匹配，某些参数可能未加载")

    model.eval()

    return model


def load_data(data_path):
    """
    加载K线数据

    Args:
        data_path: 数据路径

    Returns:
        包含OHLCV数据的DataFrame
    """
    # 根据文件扩展名加载数据
    if data_path.endswith('.csv'):
        df = pd.read_csv(data_path)
    elif data_path.endswith('.parquet'):
        df = pd.read_parquet(data_path)
    else:
        raise ValueError(f"不支持的文件格式: {data_path}")

    # 确保数据包含必要的列
    required_columns = ['datetime', 'open', 'high', 'low', 'close']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"数据缺少必要的列: {col}")

    # 确保datetime列是日期时间类型
    if 'datetime' in df.columns:
        df['datetime'] = pd.to_datetime(df['datetime'])

    # 按日期排序
    df = df.sort_values('datetime')

    return df


def generate_mock_data(n_samples=100, trend='up', volatility=0.01, start_price=100.0):
    """
    生成模拟的K线数据

    Args:
        n_samples: 样本数量
        trend: 趋势 ('up', 'down', 'random')
        volatility: 波动率
        start_price: 起始价格

    Returns:
        包含OHLCV数据的DataFrame
    """
    # 生成日期序列
    dates = [datetime.now() - timedelta(days=n_samples-i) for i in range(n_samples)]

    # 生成价格序列
    prices = []
    price = start_price

    for i in range(n_samples):
        # 根据趋势生成价格变动
        if trend == 'up':
            change = np.random.normal(0.001, volatility)
        elif trend == 'down':
            change = np.random.normal(-0.001, volatility)
        else:
            change = np.random.normal(0, volatility)

        price *= (1 + change)
        prices.append(price)

    # 生成OHLC数据
    df = pd.DataFrame({
        'datetime': dates,
        'close': prices,
    })

    # 生成open, high, low
    df['open'] = df['close'].shift(1)
    df.loc[0, 'open'] = df.loc[0, 'close'] * (1 - np.random.uniform(0, 0.01))

    for i in range(len(df)):
        high_range = np.random.uniform(0, 0.02)
        low_range = np.random.uniform(0, 0.02)
        df.loc[i, 'high'] = max(df.loc[i, 'open'], df.loc[i, 'close']) * (1 + high_range)
        df.loc[i, 'low'] = min(df.loc[i, 'open'], df.loc[i, 'close']) * (1 - low_range)

    # 生成交易量
    df['volume'] = np.random.randint(1000, 10000, size=len(df))

    return df


class MockModel(torch.nn.Module):
    """
    用于测试的模拟模型

    Args:
        vocab_size: 词汇表大小
        trend: 趋势 ('up', 'down', 'random')
    """
    def __init__(self, vocab_size, trend='up', block_size=30):
        super().__init__()
        self.vocab_size = vocab_size
        self.trend = trend  # 'up', 'down', 'random'
        self.block_size = block_size  # 添加block_size属性

    def forward(self, input_tokens, code_ids, time_features=None, targets=None):
        batch_size, seq_len = input_tokens.size()

        # 创建logits
        logits = torch.zeros(batch_size, seq_len, self.vocab_size)

        # 根据趋势设置概率
        if self.trend == 'up':
            # 偏向上涨的token，设置较高的概率
            logits[:, :, :] = 0.1  # 基础值
            for i in range(self.vocab_size):
                try:
                    token_str = str(i)
                    if '|' in token_str:
                        parts = token_str.split('|')
                        if len(parts) > 0 and parts[0].strip():
                            change_value = int(parts[0])
                            if change_value > 0:
                                # 上涨token设置高概率
                                logits[:, :, i] = 10.0 + change_value
                except:
                    pass
        elif self.trend == 'down':
            # 偏向下跌的token，设置较高的概率
            logits[:, :, :] = 0.1  # 基础值
            for i in range(self.vocab_size):
                try:
                    token_str = str(i)
                    if '|' in token_str:
                        parts = token_str.split('|')
                        if len(parts) > 0 and parts[0].strip():
                            change_value = int(parts[0])
                            if change_value < 0:
                                # 下跌token设置高概率
                                logits[:, :, i] = 10.0 - change_value
                except:
                    pass
        else:
            # 随机趋势，随机设置概率
            logits = torch.randn(batch_size, seq_len, self.vocab_size)

        # 返回logits和空损失
        return logits, None


def run_backtest(args):
    """
    运行回测

    Args:
        args: 命令行参数
    """
    # 创建tokenizer
    tokenizer = CandlestickTokenizer(
        change_range=(-12, 12),
        entity_range=(-12, 12),
        shadow_range=(0, 7),
        include_volume=False
    )

    # 加载或生成数据
    if args.data_path:
        train_data, train_code_ids, val_data, val_code_ids = load_single_data(args.data_path)
        df = val_data[0][-150:]
        args.code_id = val_code_ids[0]
        print(f"已加载数据: {args.data_path}")
        print(f"数据形状: {df.shape}")
    else:
        df = generate_mock_data(n_samples=args.n_samples, trend=args.trend)
        print(f"已生成模拟数据")
        print(f"数据形状: {df.shape}")
        print(df)

    # 加载或创建模型
    if args.model_path:
        # 检查是否是ONNX模型或者使用--use_onnx参数
        if args.model_path.endswith('.onnx') or args.use_onnx:
            if not ONNX_AVAILABLE:
                print("警告: onnxruntime未安装，无法加载ONNX模型。将使用模拟模型。")
                model = MockModel(tokenizer.vocab_size, trend=args.model_trend)
                print(f"已创建模拟模型，趋势: {args.model_trend}")
            else:
                try:
                    model = load_onnx_model(args.model_path)
                    print(f"已加载ONNX模型: {args.model_path}")
                except Exception as e:
                    print(f"加载ONNX模型失败: {e}")
                    print("将使用模拟模型")
                    model = MockModel(tokenizer.vocab_size, trend=args.model_trend)
                    print(f"已创建模拟模型，趋势: {args.model_trend}")
        else:
            # 加载PyTorch模型
            model = load_model(args.model_path, tokenizer.vocab_size)
            print(f"已加载PyTorch模型: {args.model_path}")
    else:
        # 创建模拟模型
        model = MockModel(tokenizer.vocab_size, trend=args.model_trend)
        print(f"已创建模拟模型，趋势: {args.model_trend}")

    # 创建回测器
    backtester = CandlestickLLMBacktester(
        model,
        tokenizer,
        initial_capital=args.initial_capital,
        signal_type=args.signal_type,
        leverage=args.leverage  # 添加杠杆参数
    )

    # 运行回测
    print("正在运行回测...")
    # 确保seq_len不超过模型的block_size
    seq_len = min(args.seq_len, model.block_size - 2)  # 留出2个位置用于预测
    print(f"使用序列长度: {seq_len}")

    # 记录开始时间
    start_time = time.time()

    # 执行回测
    results = backtester.backtest(
        df,
        code_id=args.code_id,
        seq_len=seq_len,
        commission=args.commission,
        threshold=args.threshold,
        stop_loss=args.stop_loss,
        take_profit=args.take_profit,
        time_features=None,
        temperature=args.temperature,
        print_interval=args.print_interval,
        leverage=args.leverage  # 添加杠杆参数
    )

    # 计算耗时
    elapsed_time = time.time() - start_time
    print(f"\n回测完成，总耗时: {elapsed_time:.2f}秒 ({elapsed_time/60:.2f}分钟)")

    # 计算每个样本的平均处理时间
    samples_processed = len(df) - seq_len
    if samples_processed > 0:
        avg_time_per_sample = elapsed_time / samples_processed
        print(f"平均每个样本处理时间: {avg_time_per_sample*1000:.2f}毫秒")

    # 打印回测结果
    print("\n回测结果:")
    print(f"初始资金: ${results['initial_capital']:.2f}")
    print(f"最终权益: ${results['final_equity']:.2f}")
    print(f"总收益率: {results['total_return']:.2%}")
    print(f"年化收益率: {results['annual_return']:.2%}")
    print(f"夏普比率: {results['sharpe_ratio']:.2f}")
    print(f"最大回撤: {results['max_drawdown']:.2%}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"盈亏比: {results['profit_loss_ratio']:.2f}")
    print(f"杠杆倍数: {results.get('leverage', 1.0):.1f}倍")
    print(f"交易次数: {len(results['trades'])}")

    # 打印交易详情
    if len(results['trades']) > 0:
        print("\n交易详情:")
        print(f"{'日期':<20} {'操作':<12} {'价格':<10} {'持仓':<10} {'权益':<12} {'利润':<10}")
        print("-" * 80)

        # 显示前5笔交易
        for i, trade in enumerate(results['trades'][:5]):
            date_str = str(trade['datetime'])[:19] if isinstance(trade['datetime'], (pd.Timestamp, datetime)) else str(trade['datetime'])
            action = trade['action']
            price = f"{trade['price']:.2f}"
            position = f"{trade.get('position', 0):.2f}"
            equity = f"${trade.get('equity', 0):.2f}"
            profit = f"{trade.get('profit', 'N/A'):.2f}" if isinstance(trade.get('profit', 'N/A'), (int, float)) else 'N/A'

            print(f"{date_str:<20} {action:<12} {price:<10} {position:<10} {equity:<12} {profit:<10}")

        # 如果交易数量超过5笔，显示省略号
        if len(results['trades']) > 5:
            print("...")

        # 显示最后5笔交易
        for i, trade in enumerate(results['trades'][-5:]):
            if i == 0 and len(results['trades']) > 10:  # 避免重复显示
                continue

            date_str = str(trade['datetime'])[:19] if isinstance(trade['datetime'], (pd.Timestamp, datetime)) else str(trade['datetime'])
            action = trade['action']
            price = f"{trade['price']:.2f}"
            position = f"{trade.get('position', 0):.2f}"
            equity = f"${trade.get('equity', 0):.2f}"
            profit = f"{trade.get('profit', 'N/A'):.2f}" if isinstance(trade.get('profit', 'N/A'), (int, float)) else 'N/A'

            print(f"{date_str:<20} {action:<12} {price:<10} {position:<10} {equity:<12} {profit:<10}")

    # 打印信号统计
    if 'signals' in results and len(results['signals']) > 0:
        # 检查信号中是否包含direction_probs
        has_direction_probs = any('direction_probs' in s for s in results['signals'])

        if has_direction_probs:
            # 使用direction_probs中的up/down概率
            up_signals = sum(1 for s in results['signals'] if 'direction_probs' in s and s['direction_probs'].get('up', 0) > args.threshold)
            down_signals = sum(1 for s in results['signals'] if 'direction_probs' in s and s['direction_probs'].get('down', 0) > args.threshold)
        else:
            # 尝试使用up_prob/down_prob (兼容旧版本)
            up_signals = sum(1 for s in results['signals'] if s.get('up_prob', 0) > args.threshold)
            down_signals = sum(1 for s in results['signals'] if s.get('down_prob', 0) > args.threshold)

        total_signals = len(results['signals'])

        print(f"\n信号统计:")
        print(f"总信号数: {total_signals}")
        print(f"上涨信号: {up_signals} ({up_signals/total_signals:.2%})")
        print(f"下跌信号: {down_signals} ({down_signals/total_signals:.2%})")
        print(f"无信号: {total_signals - up_signals - down_signals} ({(total_signals - up_signals - down_signals)/total_signals:.2%})")

    # 可视化回测结果
    # 确保使用与回测相同的序列长度
    backtester.visualize_backtest(df, results, seq_len=seq_len, save_path=args.save_fig)

    # 保存回测结果
    if args.save_results:
        backtester.save_results(results, args.save_results)
        print(f"回测结果已保存到: {args.save_results}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='K线LLM模型回测器示例')

    # 数据参数
    parser.add_argument('--data_path', type=str, default=None, help='K线数据路径 (.csv或.parquet)')
    parser.add_argument('--n_samples', type=int, default=100, help='生成模拟数据的样本数量')
    parser.add_argument('--trend', type=str, default='up', choices=['up', 'down', 'random'], help='生成模拟数据的趋势')

    # 模型参数
    parser.add_argument('--model_path', type=str, default='e:/lab/RoboQuant/pylab/checkpoints/basic_candlestick_llm/best_model.pt', help='预训练模型路径 (.pt, .pth 或 .onnx)')
    parser.add_argument('--model_trend', type=str, default='up', choices=['up', 'down', 'random'], help='模拟模型的趋势')
    parser.add_argument('--code_id', type=int, default=0, help='证券代码ID')
    parser.add_argument('--use_onnx', action='store_true', help='强制将模型视为ONNX格式 (如果文件扩展名不是.onnx)')

    # 策略信号参数
    parser.add_argument('--signal_type', type=str, default='topk', choices=['threshold', 'topk', 'momentum', 'ensemble'], help='信号生成策略')

    # 回测参数
    parser.add_argument('--initial_capital', type=float, default=10000.0, help='初始资金')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--commission', type=float, default=0.001, help='交易手续费率')
    parser.add_argument('--threshold', type=float, default=0.03, help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float, default=None, help='止损比例')
    parser.add_argument('--take_profit', type=float, default=None, help='止盈比例')
    parser.add_argument('--leverage', type=float, default=1.0, help='杠杆倍数，默认为1.0（无杠杆）')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数，控制预测的随机性 (1.0表示正常温度，<1.0更确定，>1.0更随机)')
    parser.add_argument('--print_interval', type=int, default=10, help='打印详细信息的间隔步数')

    # 输出参数
    parser.add_argument('--save_fig', type=str, default=None, help='保存图表的路径')
    parser.add_argument('--save_results', type=str, default=None, help='保存回测结果的路径')

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()
    run_backtest(args)
