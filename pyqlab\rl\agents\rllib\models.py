# DRL models from RLlib
from pprint import pprint
import ray
from ray.rllib.agents.a3c import a2c
from ray.rllib.agents.ddpg import ddpg, td3
from ray.rllib.agents.ppo import ppo
from ray.rllib.agents.sac import sac
from ray.tune.logger import pretty_print

MODELS = {"a2c": a2c, "ddpg": ddpg, "td3": td3, "sac": sac, "ppo": ppo}
# MODEL_KWARGS = {x: config.__dict__[f"{x.upper()}_PARAMS"] for x in MODELS.keys()}

class DRLAgent:
    """Implementations for DRL algorithms

    Attributes
    ----------
        env: gym environment class
            user-defined class
    Methods
    -------
        get_model()
            setup DRL algorithms
        train_model()
            train DRL algorithms in a train dataset
            and output the trained model
        DRL_prediction()
            make a prediction in a test dataset and get results
    """

    def __init__(self, env):
        self.env = env

    def get_model(
            self,
            model_name,
    ):
        if model_name not in MODELS:
            raise NotImplementedError("NotImplementedError")

        # if model_kwargs is None:
        #    model_kwargs = MODEL_KWARGS[model_name]

        model = MODELS[model_name]

        # get algorithm default configration based on algorithm in RLlib
        if model_name == "a2c":
            model_config = model.A2C_DEFAULT_CONFIG.copy()
        elif model_name == "td3":
            model_config = model.TD3_DEFAULT_CONFIG.copy()
        else:
            model_config = model.DEFAULT_CONFIG.copy()
        # pass env, log_level, price_array, tech_array, and turbulence_array to config
        model_config["env"] = self.env
        model_config["log_level"] = "ERROR" # "WARN"
        model_config["framework"] = "torch"
        model_config["num_workers"] = 3
        model_config["env_config"] = {
            "name": "FuturesAgent",
            "initial_amount": 1e5,
            "gamma": 0.98,
            "direct": "long",
            "mode": "train",
        }

        return model, model_config

    def train_model(self, model, model_name, model_config, agent_path,
                    total_episodes=100, init_ray=True, reload=False):
        if model_name not in MODELS:
            raise NotImplementedError("NotImplementedError")
        if init_ray: 
            ray.init(
                # num_gpus=1, # 默认为不限cpu与gpu
                # num_cpus=2,
                local_mode=True, # 在调试模式下请使用
                ignore_reinit_error=True
            )  # Other Ray APIs will not work until `ray.init()` is called.

        gpus_available = ray.get_gpu_ids()
        # assert len(gpus_available) > 0, "Not enough GPUs for this env!"
        print("Env can see these GPUs: {}".format(gpus_available))

        if model_name == "ppo":
            trainer = model.PPOTrainer(env=self.env, config=model_config)
        elif model_name == "a2c":
            trainer = model.A2CTrainer(env=self.env, config=model_config)
        elif model_name == "ddpg":
            trainer = model.DDPGTrainer(env=self.env, config=model_config)
        elif model_name == "td3":
            trainer = model.TD3Trainer(env=self.env, config=model_config)
        elif model_name == "sac":
            trainer = model.SACTrainer(env=self.env, config=model_config)

        # pprint(model_config)
        # if reload:
            # cwd = agent_path
            # trainer.restore(checkpoint_path)
            # last_checkpoint = analysis.get_last_checkpoint()
            # trainer.restore(last_checkpoint)

        for i in range(total_episodes):
            results = trainer.train()
            if i%10 == 0:
                # print(pretty_print(results))
                print(f"Iter: {i}; avg. reward={results['episode_reward_mean']}")

        ray.shutdown()

        # save the trained model
        trainer.save(agent_path)

        return trainer

    @staticmethod
    def DRL_prediction(
            model_name,
            env,
            agent_path="./test_ppo/checkpoint_000100/checkpoint-100",
    ):
        if model_name not in MODELS:
            raise NotImplementedError("NotImplementedError")

        if model_name == "a2c":
            model_config = MODELS[model_name].A2C_DEFAULT_CONFIG.copy()
        elif model_name == "td3":
            model_config = MODELS[model_name].TD3_DEFAULT_CONFIG.copy()
        else:
            model_config = MODELS[model_name].DEFAULT_CONFIG.copy()
        model_config["env"] = env
        model_config["log_level"] = "WARN"
        model_config["env_config"] = {
            "mode": "pred",
            "direct": "long"
        }
        model_config["framework"] = "torch"

        env_instance = env(**model_config["env_config"])

        # ray.init() # Other Ray APIs will not work until `ray.init()` is called.
        if model_name == "ppo":
            trainer = MODELS[model_name].PPOTrainer(env=env, config=model_config)
        elif model_name == "a2c":
            trainer = MODELS[model_name].A2CTrainer(env=env, config=model_config)
        elif model_name == "ddpg":
            trainer = MODELS[model_name].DDPGTrainer(env=env, config=model_config)
        elif model_name == "td3":
            trainer = MODELS[model_name].TD3Trainer(env=env, config=model_config)
        elif model_name == "sac":
            trainer = MODELS[model_name].SACTrainer(env=env, config=model_config)

        try:
            trainer.restore(agent_path)
            print("Restoring from checkpoint path", agent_path)
        except BaseException:
            raise ValueError("Fail to load agent!")

        # test on the testing env
        state = env_instance.reset()
        episode_returns = []  # the cumulative_return / initial_account
        episode_total_assets = [env_instance.initial_amount]
        done = False
        while not done:
            action = trainer.compute_single_action(state)
            if (action == 0):
                print(f"action={action}")
            state, reward, done, _ = env_instance.step(action)

            total_asset = env_instance.initial_amount + reward
            episode_total_assets.append(total_asset)
            episode_return = total_asset / env_instance.initial_amount
            episode_returns.append(episode_return)
        ray.shutdown()
        print("episode return: " + str(episode_return))
        print("Test Finished!")
        return episode_total_assets
