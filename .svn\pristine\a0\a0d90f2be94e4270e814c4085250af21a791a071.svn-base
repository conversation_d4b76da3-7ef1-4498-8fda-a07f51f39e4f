import torch
from torch.utils.data import Dataset


# 定义数据集
class TimeSeriesDataset(Dataset):
    def __init__(self, data, seq_length):
        self.data = data
        self.seq_length = seq_length
    
    def __len__(self):
        return len(self.data) - self.seq_length
    
    def __getitem__(self, idx):
        x = self.data[idx:idx + self.seq_length]
        y = self.data[idx + 1:idx + self.seq_length + 1]
        return torch.tensor(x, dtype=torch.float32), torch.tensor(y, dtype=torch.float32)


# 定义Transformer模型
# class TimeSeriesGpt(nn.Module):
#     def __init__(self, input_dim, model_dim, num_heads, num_layers, output_dim, dropout=0.1):
#         super(TimeSeriesGpt, self).__init__()
#         self.model_dim = model_dim
#         self.embedding = nn.Linear(input_dim, model_dim)
#         encoder_layer = nn.TransformerEncoderLayer(d_model=model_dim, nhead=num_heads, dropout=dropout)
#         self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
#         self.fc = nn.Linear(model_dim, output_dim)
    
#     def forward(self, x):
#         x = self.embedding(x)
#         x = x.permute(1, 0, 2)  # Transformer expects (seq_len, batch_size, model_dim)
#         x = self.transformer(x)
#         x = x.permute(1, 0, 2)
#         return self.fc(x)
    