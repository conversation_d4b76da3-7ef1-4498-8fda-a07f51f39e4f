# BarTokenizer - ATR标准化K线数据Token化器

BarTokenizer 是一个专为金融时序数据设计的高级Token化器，采用ATR（平均真实波幅）标准化方法，支持多周期、多种映射策略，并通过多种技术解决token分布不平衡问题。

## 🎯 核心特性

### 1. ATR标准化
- 使用ATR值对价格变化进行标准化，消除不同证券和时期的价格尺度差异
- 支持多种技术指标特征提取（RSI、布林带位置、波动率等）
- 自动处理NaN值和异常值

### 2. 多种映射策略
- **线性映射（LinearMapping）**：基于标准差裁剪的等间距映射
- **分位数映射（QuantileMapping）**：基于数据分布的分位数映射
- **自适应映射（AdaptiveMapping）**：混合等频和等宽的自适应映射

### 3. 分布平衡优化
- **频率平衡（FrequencyBalancing）**：通过重映射减少token分布不平衡
- **基尼系数监控**：实时监控token分布的不平衡程度
- **多种平衡性指标**：熵、变异系数、频率范围等

### 4. 多周期支持
- **MultiPeriodBarTokenizer**：同时处理多个时间周期的数据
- **灵活组合策略**：支持拼接、加权等多种组合方法
- **统一token表示**：生成跨周期的一致性token

## 🚀 快速开始

### 基本使用

```python
from pyqlab.data.tokenizers import BarTokenizer
import pandas as pd
import numpy as np

# 创建示例OHLCV数据
df = pd.DataFrame({
    'datetime': pd.date_range('2024-01-01', periods=1000, freq='5min'),
    'open': 100 + np.cumsum(np.random.randn(1000) * 0.1),
    'high': 0, 'low': 0, 'close': 0,
    'volume': np.random.randint(1000, 10000, 1000)
})

# 生成合理的OHLC数据
for i in range(len(df)):
    open_price = df.loc[i, 'open']
    change = np.random.randn() * 0.5
    close_price = open_price + change
    df.loc[i, 'close'] = close_price
    df.loc[i, 'high'] = max(open_price, close_price) + abs(np.random.randn() * 0.2)
    df.loc[i, 'low'] = min(open_price, close_price) - abs(np.random.randn() * 0.2)

# 创建tokenizer
tokenizer = BarTokenizer(
    mapping_strategy='adaptive',    # 自适应映射
    balancing_strategy='frequency', # 频率平衡
    n_bins=100,                    # token数量
    features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
)

# 拟合和转换
tokens = tokenizer.fit_transform(df)
print(f"生成tokens数量: {len(tokens)}")
print(f"词汇表大小: {tokenizer.get_vocab_size()}")

# 分析分布平衡性
balance_info = tokenizer.analyze_balance(tokens)
print(f"基尼系数: {balance_info['gini_coefficient']:.4f}")
print(f"标准化熵: {balance_info['normalized_entropy']:.4f}")
```

### 多周期使用

```python
from pyqlab.data.tokenizers import MultiPeriodBarTokenizer

# 准备多周期数据
data_dict = {
    '5min': df,
    '15min': df[::3].reset_index(drop=True),  # 15分钟数据
    '1h': df[::12].reset_index(drop=True)     # 1小时数据
}

# 创建多周期tokenizer
multi_tokenizer = MultiPeriodBarTokenizer(
    periods=['5min', '15min', '1h'],
    base_tokenizer_config={
        'mapping_strategy': 'quantile',
        'n_bins': 50,
        'features': ['change', 'body', 'volume_ratio']
    },
    combination_method='concatenate'
)

# 拟合和转换
multi_tokens = multi_tokenizer.fit(data_dict).transform(data_dict)
print(f"多周期tokens形状: {multi_tokens.shape}")
```

## 📊 映射策略详解

### 1. 线性映射（LinearMapping）
```python
tokenizer = BarTokenizer(
    mapping_strategy='linear',
    n_bins=100
)
```
- **优点**：简单直观，计算快速
- **适用场景**：数据分布相对均匀的情况
- **参数**：`clip_std` 控制异常值裁剪的标准差倍数

### 2. 分位数映射（QuantileMapping）
```python
tokenizer = BarTokenizer(
    mapping_strategy='quantile',
    n_bins=100
)
```
- **优点**：自动适应数据分布，对异常值鲁棒
- **适用场景**：数据分布不均匀或有较多异常值
- **参数**：`output_distribution` 可选择 'uniform' 或 'normal'

### 3. 自适应映射（AdaptiveMapping）
```python
tokenizer = BarTokenizer(
    mapping_strategy='adaptive',
    n_bins=100
)
```
- **优点**：平衡等频和等宽映射的优势
- **适用场景**：需要在分布均匀性和保持原始分布特征间平衡
- **参数**：`balance_factor` 控制等频和等宽的混合比例

## 🔧 特征工程

### 支持的特征类型

```python
features = [
    'change',        # 价格变化（基于ATR标准化）
    'body',          # K线实体（基于ATR标准化）
    'upper_shadow',  # 上影线（基于ATR标准化）
    'lower_shadow',  # 下影线（基于ATR标准化）
    'volume_ratio',  # 成交量比率（对数变换）
    'volatility',    # 波动率特征
    'rsi',           # 相对强弱指标
    'bb_position'    # 布林带位置
]

tokenizer = BarTokenizer(features=features)
```

### 自定义特征
可以通过继承 `BarTokenizer` 并重写 `_extract_features` 方法来添加自定义特征。

## 📈 分布平衡分析

### 平衡性指标

```python
# 分析token分布
balance_metrics = tokenizer.analyze_balance(tokens, plot=True)

print("平衡性指标:")
print(f"基尼系数: {balance_metrics['gini_coefficient']:.4f}")
print(f"标准化熵: {balance_metrics['normalized_entropy']:.4f}")
print(f"变异系数: {balance_metrics['coefficient_of_variation']:.4f}")
print(f"频率范围: {balance_metrics['frequency_range']:.4f}")
print(f"Top 10%占比: {balance_metrics['top_10_percent_share']:.4f}")
```

### 基尼系数解释
- **0.0**：完全平衡（所有token频率相等）
- **0.3以下**：相对平衡
- **0.3-0.5**：中等不平衡
- **0.5以上**：严重不平衡

## 💾 模型持久化

```python
# 保存模型
tokenizer.save_model('tokenizer_model.pkl')

# 加载模型
loaded_tokenizer = BarTokenizer.load_model('tokenizer_model.pkl')

# 使用加载的模型
new_tokens = loaded_tokenizer.transform(new_data)
```

## 🧪 测试

运行单元测试：

```bash
python -m pyqlab.data.tests.test_bar_tokenizer
```

## 📋 最佳实践

### 1. 选择合适的映射策略
- **金融数据通常推荐 `adaptive` 策略**，能够平衡分布均匀性和保持原始特征
- 对于异常值较多的数据，使用 `quantile` 策略
- 对于分布相对均匀的数据，可以使用 `linear` 策略

### 2. 优化token数量
- **n_bins 建议在 50-200 之间**
- 过少会损失信息，过多会导致稀疏性问题
- 可以通过分析基尼系数来调整

### 3. 特征选择
- **核心特征**：change, body, upper_shadow, lower_shadow
- **增强特征**：volume_ratio, volatility, rsi
- 根据具体应用场景选择合适的特征组合

### 4. 多周期策略
- 短周期捕捉细节，长周期捕捉趋势
- 建议使用 2-4 个周期，避免过度复杂化
- 根据预测目标选择合适的周期组合

## 🔄 与现有系统集成

BarTokenizer 可以轻松集成到现有的 PyQLab 数据处理流程中：

```python
from pyqlab.data import get_dataset
from pyqlab.data.tokenizers import BarTokenizer

# 获取原始数据
dataset = get_dataset(...)

# 使用BarTokenizer进行token化
tokenizer = BarTokenizer(mapping_strategy='adaptive')
tokens = tokenizer.fit_transform(raw_data)

# 集成到训练流程
# ...
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进 BarTokenizer！
