from datetime import datetime
import mplfinance as mpf
import matplotlib as mpl
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
# %matplotlib inline
mpl.rcParams['font.sans-serif'] = ['SimHei'] # 指定默认字体
mpl.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题

def plot_kline(symbol, hist_data, plt_volume=False):

    fig = plt.figure(figsize=(12,10))
    grid = plt.GridSpec(12, 10, wspace=0.5, hspace=0.5)

    #（1）绘制K线图
    # K线数据
    # ohlc = hist_data[['Date','open_price','high_price','low_price','close_price']]
    ohlc = hist_data.copy()
    ohlc[:,0:1] = np.array([i for i in range(ohlc.shape[0])]).reshape(ohlc.shape[0], 1)     # 重新赋值横轴数据，绘制K线图无间隔
    # 绘制K线
    ax1 = fig.add_subplot(grid[0:8,0:12])   # 设置K线图的尺寸
    mpf.plot(ax1, ohlc, width=.7, type='candle', colorup='red', colordown='green')
#     plt.title(symbol,fontsize = 14)     # 设置图片标题
#     plt.ylabel('价 格',fontsize = 14)   # 设置纵轴标题
#     ax1.set_xticks([])                      # 日期标注在成交量中，故清空此处x轴刻度
#     ax1.set_xticklabels([])                 # 日期标注在成交量中，故清空此处x轴 
    # ax1.xaxis.set_major_formatter(mdates.num2date) # 设置横轴日期格式
    plt.xticks(rotation=30)                                        # 日期显示的旋转角度
    plt.title(symbol,fontsize = 14)                            # 设置图片标题
    plt.xlabel('日 期',fontsize = 14)                               # 设置横轴标题
    plt.ylabel('价 格',fontsize = 14)                          # 设置纵轴标题
    # 修改横轴标注日期
    date_list = hist_data[:, 0]           # 获取日期列表
    print(ax1.get_xticks().shape[0]-1)
    xticks_len = round(date_list.shape[0]/(ax1.get_xticks().shape[0]-1))    # 获取默认横轴标注的间隔
    xticks_num = range(0,date_list.shape[0],xticks_len)                # 生成横轴标注位置列表
    xticks_str = list(map(lambda x:datetime.fromtimestamp(date_list[int(x)]).strftime("%Y%m%d %H:%M"),xticks_num))  # 生成正在标注日期列表
    ax1.set_xticks(xticks_num)                                      # 设置横轴标注位置
    ax1.set_xticklabels(xticks_str)                                 # 设置横轴标注日期
    plt.show()
    '''
    #（2）绘制成交量
    # 成交量数据
    volume_list = hist_data[['Date','close_price','open_price','business_amount']]
    # color_list = data_volume.apply(lambda row: 1 if row['close_price'] >= row['open_price'] else 0, axis=1)        # 计算成交量柱状图对应的颜色，使之与K线颜色一致
    date_list = hist_data[:, 0]
    # 绘制成交量
    ax2 = fig.add_subplot(grid[8:10,0:12])  # 设置成交量图形尺寸
    ax2.bar(data_volume.query('color==1')['Date']
            , data_volume.query('color==1')['business_amount']
            , color='r')                    # 绘制红色柱状图
    ax2.bar(data_volume.query('color==0')['Date']
            , data_volume.query('color==0')['business_amount']
            , color='g')                    # 绘制绿色柱状图
    plt.xticks(rotation=30) 
    plt.xlabel('日 期',fontsize = 14)                               # 设置横轴标题

    # 修改横轴日期标注
    date_list = ohlc.index.tolist()           # 获取日期列表
    xticks_len = round(len(date_list)/(len(ax2.get_xticks())-1))      # 获取默认横轴标注的间隔
    xticks_num = range(0,len(date_list),xticks_len)                   # 生成横轴标注位置列表
    xticks_str = list(map(lambda x:date_list[int(x)],xticks_num))     # 生成正在标注日期列表
    ax2.set_xticks(xticks_num)                                        # 设置横轴标注位置
    ax2.set_xticklabels(xticks_str)                                   # 设置横轴标注日期
    plt.show()'''