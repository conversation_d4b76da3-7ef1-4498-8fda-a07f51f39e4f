{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              name          stategy        pnl  times  long_atr_mult  \\\n", "label                                                                  \n", "A1909.DC    豆一1909  FUT-STR-BK-a-RL   34650.00      8            0.4   \n", "AG1912.SC   白银1912  FUT-STR-BK-a-RL    7955.70      2            0.7   \n", "AL1907.SC   沪铝1907  FUT-STR-BK-a-RL   -1780.00      7            0.0   \n", "AP1910.ZC   苹果1910  FUT-STR-BK-a-RL   52700.00      3            0.9   \n", "AU1912.SC   黄金1912  FUT-STR-BK-a-RL    1240.02      3            0.0   \n", "BU1912.SC   沥青1912  FUT-STR-BK-a-RL   36140.00      2            0.0   \n", "C1909.DC    玉米1909  FUT-STR-BK-a-RL    3410.00      2            0.0   \n", "CF1909.ZC   郑棉1909  FUT-STR-BK-a-RL   61675.00     17            0.7   \n", "CS1909.DC   淀粉1909  FUT-STR-BK-a-RL  -18170.00      3            0.0   \n", "CU1907.SC   沪铜1907  FUT-STR-BK-a-RL   14100.00      6            0.9   \n", "CY2001.ZC   棉纱2001  FUT-STR-BK-a-RL   28975.00     20            0.5   \n", "FG1909.ZC   玻璃1909     FUT-GAP-b-RL   37500.00     10            0.7   \n", "HC1910.SC   热卷1910     FUT-GAP-b-RL   14000.00      8            1.1   \n", "I1909.DC    铁矿1909     FUT-GAP-b-RL  158050.00      6            0.7   \n", "J1909.DC    焦炭1909  FUT-STR-BK-a-RL   51250.00      4            0.9   \n", "JD1909.DC   鸡蛋1909  FUT-STR-BK-a-RL   12880.00      3            0.0   \n", "JM1909.DC   焦煤1909  FUT-STR-BK-a-RL    7020.00      6            0.5   \n", "L1909.DC    乙烯1909  FUT-STR-BK-a-RL   51210.00     11            0.5   \n", "M1909.DC    豆粕1909  FUT-STR-BK-a-RL   64090.00     10            0.8   \n", "MA1909.ZC   甲醇1909  FUT-STR-BK-a-RL   72170.00     33            0.5   \n", "NI1907.SC   沪镍1907  FUT-STR-BK-a-RL   10240.00     14            0.8   \n", "OI1909.ZC   菜油1909     FUT-GAP-b-RL   34000.00      5            1.0   \n", "P1909.DC    棕榈1909  FUT-STR-BK-a-RL   25400.00      9            0.5   \n", "PB1907.SC   沪铅1907  FUT-STR-BK-a-RL   11730.00      7            0.9   \n", "PP1909.DC   丙烯1909  FUT-STR-BK-a-RL   42285.00      5            0.9   \n", "RB1910.SC   螺纹1910  FUT-STR-BK-a-RL   25010.00     28            0.7   \n", "RM1909.ZC   菜粕1909     FUT-GAP-b-RL   48620.00      1            1.0   \n", "RU1909.SC   橡胶1909  FUT-STR-BK-a-RL    6640.00      2            0.9   \n", "SC1907.SC   原油1907  FUT-STR-BK-a-RL   17300.00      1            0.4   \n", "SF1909.ZC   硅铁1909     FUT-GAP-b-RL   43795.00      4            1.1   \n", "SM1909.ZC   锰硅1909     FUT-GAP-b-RL   19020.00      5            0.8   \n", "SN1909.SC   沪锡1909  FUT-STR-BK-a-RL   -3712.00      7            0.0   \n", "SP1909.SC   纸浆1909  FUT-STR-BK-a-RL   32950.00     15            0.5   \n", "SR1909.ZC   白糖1909  FUT-STR-BK-a-RL   28020.00      2            0.0   \n", "TA1909.ZC  PTA1909  FUT-STR-BK-a-RL  138850.00      5            0.9   \n", "V1909.DC   PVC1909  FUT-STR-BK-a-RL   23055.00     13            0.6   \n", "Y1909.DC    豆油1909  FUT-STR-BK-a-RL   44920.00      9            0.5   \n", "ZC1909.ZC   动煤1909  FUT-STR-BK-a-RL   26567.90      1            0.8   \n", "ZN1907.SC   沪锌1907  FUT-STR-BK-a-RL   10750.00      6            0.6   \n", "\n", "           short_atr_mult  long_atr  short_atr  \n", "label                                           \n", "A1909.DC              1.6         7      24.00  \n", "AG1912.SC             1.8        17       9.00  \n", "AL1907.SC             0.0        52      23.00  \n", "AP1910.ZC             1.8       120     146.00  \n", "AU1912.SC             0.0         1       0.31  \n", "BU1912.SC             0.0        60      24.00  \n", "C1909.DC              0.0        13       5.00  \n", "CF1909.ZC             1.0        62      60.00  \n", "CS1909.DC             0.0        15       5.00  \n", "CU1907.SC             1.4       131     202.00  \n", "CY2001.ZC             0.8        71      44.00  \n", "FG1909.ZC             1.0        11       5.00  \n", "HC1910.SC             1.0        58      14.00  \n", "I1909.DC              1.6        11       8.00  \n", "J1909.DC              0.8        31      15.00  \n", "JD1909.DC             0.0        22      29.00  \n", "JM1909.DC             1.6        10      11.00  \n", "L1909.DC              1.0        29      34.00  \n", "M1909.DC              1.0        13      13.00  \n", "MA1909.ZC             0.8         9      10.00  \n", "NI1907.SC             1.2       464     412.00  \n", "OI1909.ZC             1.4        72      29.00  \n", "P1909.DC              0.8        20      14.00  \n", "PB1907.SC             1.2        63      46.00  \n", "PP1909.DC             0.8        74      21.00  \n", "RB1910.SC             1.0        14      14.00  \n", "RM1909.ZC             1.8        41      25.00  \n", "RU1909.SC             1.0       201     115.00  \n", "SC1907.SC             0.8         4       2.00  \n", "SF1909.ZC             1.8        66      34.00  \n", "SM1909.ZC             1.8        67      45.00  \n", "SN1909.SC             0.0       486     186.00  \n", "SP1909.SC             1.8        14      31.00  \n", "SR1909.ZC             0.0        50      18.00  \n", "TA1909.ZC             0.8        52      22.00  \n", "V1909.DC              1.0        20      36.00  \n", "Y1909.DC              1.4        21      24.00  \n", "ZC1909.ZC             1.8         5       4.00  \n", "ZN1907.SC             0.8       164      44.00  \n"]}], "source": ["CSV_PATH = \"d:/QuantLab/rpt/\"\n", "JSON_PATH = \"d:/QuantLab/\"\n", "\n", "def read_backtest_range(csv_file):\n", "    df = pd.read_csv(CSV_PATH + csv_file, encoding=\"gbk\")\n", "    df.set_index('label',inplace=True)\n", "    return df\n", "\n", "csv_file1 = \"FUT-STR-BK-a.225733.ord2.csv\"\n", "csv_file2 = \"FUT-GAP-b.223544.ord2.csv\"\n", "df1 = pd.read_csv(CSV_PATH + csv_file1, encoding=\"gbk\")\n", "df2 = pd.read_csv(CSV_PATH + csv_file2, encoding=\"gbk\")\n", "\n", "df1.set_index('label',inplace=True)\n", "df2.set_index('label',inplace=True)\n", "\n", "df = df1\n", "for index,row in df2.iterrows():\n", "    if index in df.index:\n", "        if row['pnl'] > df.loc[index, 'pnl']:\n", "            df.loc[index] = row\n", "    else:\n", "        df.loc[index] = row\n", "        print(index)\n", "print(df)\n", "df.to_csv(CSV_PATH + \"combine.rangebar.csv\", encoding=\"gbk\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["with open(JSO<PERSON>_PATH + \"RangeBar.json\",'r') as json_f:\n", "    range_dict = json.load(json_f)\n", "\n", "for index,row in df.iterrows():\n", "    range_dict['fut']['long_range_bar'][index] = row['long_atr']\n", "    range_dict['fut']['short_range_bar'][index] = row['short_atr']\n", "    range_dict['fut']['long_range_bar'][index[:-7]] = row['long_atr']\n", "    range_dict['fut']['short_range_bar'][index[:-7]] = row['short_atr']\n", "    if index in range_dict['fut']['long_range_bar']:\n", "        del range_dict['fut']['long_range_bar'][index]\n", "    if index in range_dict['fut']['short_range_bar']:\n", "        del range_dict['fut']['short_range_bar'][index]\n", "\n", "with open(J<PERSON><PERSON>_PATH + \"RangeBar.com.\"+ csv_file1[-15:-9] + \".json\",\"w\") as json_f:\n", "    json.dump(range_dict,json_f,indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}