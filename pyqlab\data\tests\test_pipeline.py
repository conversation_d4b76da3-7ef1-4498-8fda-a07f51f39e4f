import unittest
import pandas as pd
import numpy as np
import os
from unittest.mock import patch, MagicMock
from pyqlab.data.dataset.pipeline import Pipeline

class TestPipeline(unittest.TestCase):
    """测试Pipeline类的功能"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.data_path = "test_data_path"
        self.market = "fut"
        self.block_name = "sf"
        self.period = "day"
        self.start_year = 2020
        self.end_year = 2021
        self.pipeline = Pipeline(
            data_path=self.data_path,
            market=self.market,
            block_name=self.block_name,
            period=self.period,
            start_year=self.start_year,
            end_year=self.end_year,
            block_size=15,
            timeenc=0
        )
    
    def test_get_vocab(self):
        """测试获取词汇表功能"""
        vocab = Pipeline.get_vocab()
        # 验证词汇表大小
        # 25 * 25 * 8 * 8 + 2 = 40002
        expected_size = (13 - (-12)) * (13 - (-12)) * 8 * 8 + 2
        self.assertEqual(len(vocab), expected_size)
        
        # 验证特殊标记存在
        self.assertIn('-88|-88|0|0', vocab)  # 交易日间隔标记
        self.assertIn('-99|-99|0|0', vocab)  # 假期间隔标记
        
        # 验证普通标记格式
        self.assertIn('0|0|0|0', vocab)
        self.assertIn('-12|-12|0|0', vocab)
        self.assertIn('12|12|7|7', vocab)
    
    @patch('pandas.read_csv')
    def test_load_data(self, mock_read_csv):
        """测试加载数据功能"""
        # 模拟CSV数据
        mock_df = pd.DataFrame({
            'symbol': ['A2001', 'A2001', 'A2001'],
            'datetime': ['2020-01-01', '2020-01-02', '2020-01-03'],
            'bar': [1, 2, 3]
        })
        mock_read_csv.return_value = mock_df
        
        # 调用_load_data方法
        self.pipeline._load_data()
        
        # 验证read_csv被正确调用
        expected_path = f'{self.data_path}/bar_{self.market}_{self.block_name}_{self.period}.csv'
        mock_read_csv.assert_called_once_with(expected_path, header=0)
    
    def test_to_bar(self):
        """测试to_bar方法，将K线数据转换为bar token"""
        # 创建测试数据
        df = pd.DataFrame({
            'change': [-15, -5, 0, 5, 15],
            'entity': [-15, -5, 0, 5, 15],
            'upline': [0, 2, 4, 6, 10],
            'downline': [0, 2, 4, 6, 10]
        })
        
        # 模拟bar_set属性
        with patch.object(Pipeline, 'bar_set', Pipeline.get_vocab()):
            # 调用to_bar方法
            result_df = Pipeline.to_bar(df)
            
            # 验证结果
            self.assertIn('bar', result_df.columns)
            self.assertNotIn('change', result_df.columns)
            self.assertNotIn('entity', result_df.columns)
            self.assertNotIn('upline', result_df.columns)
            self.assertNotIn('downline', result_df.columns)
            
            # 验证值被限制在范围内
            # 由于to_bar方法会将值限制在范围内，我们期望极端值被截断
            # 但由于我们不知道具体的索引映射，这里只能验证结果是整数
            self.assertTrue(all(isinstance(x, int) for x in result_df['bar']))

if __name__ == '__main__':
    unittest.main() 