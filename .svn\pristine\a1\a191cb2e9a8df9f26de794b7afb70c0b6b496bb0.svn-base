
import os
import pytorch_lightning as pl
from argparse import ArgumentParser
from pytorch_lightning import Trainer
import pytorch_lightning.callbacks as plc
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.utilities.model_summary import ModelSummary

from pyqlab.models import PLData, PLDataModule
from pyqlab.models import PLModel

from pyqlab.data.data_api import get_dataset, get_model_name
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import KFold
import os
import random

def get_best_saved_model_filename(log_dir, sub_dir):
    model_files = {}
    log_dir = os.path.join(log_dir, sub_dir)
    for root, dirs, files in os.walk(log_dir):
        for file in files:
            if file.startswith("best-"):
                model_files[file] = root
    if model_files:
        file = min(model_files.keys())
        return f"{model_files[file]}\\{file}"
    else:
        return None
    
def load_callbacks(args):
    callbacks = []
    callbacks.append(plc.EarlyStopping(
        monitor='val_loss',
        mode='min',
        patience=args.early_stop,
        min_delta=args.min_delta,
    ))

    callbacks.append(plc.ModelCheckpoint(
        monitor='val_loss',
        filename='best-{epoch:02d}-{val_loss:.3f}',
        save_top_k=1,
        mode='min',
        save_last=False
    ))

    callbacks.append(plc.LearningRateMonitor(
        logging_interval='epoch'
    ))
    
    callbacks.append(plc.RichProgressBar())

    return callbacks


def main(args):
    args.out_channels = eval(args.out_channels)
    args.ins_nums = eval(args.ins_nums)
    args.num_embeds = eval(args.num_embeds)
    args.ds_files = eval(args.ds_files)
    args.fut_codes = eval(args.fut_codes)
    args.seed = random.randint(0, 1000)
    print(args)
    dataset = get_dataset(ds_files=args.ds_files,
                          ins_nums=args.ins_nums,
                          is_normal=args.is_normal,
                          verbose=args.verbose,
                          fut_codes=args.fut_codes,
                          data_path=args.data_path,
                          start_time=args.start_time,
                          end_time=args.end_time,
                          )
    x_data, y_data, embedding_data = dataset.prepare(
        direct=args.direct,
        win=args.num_channels,
        filter_win=args.filter_win,
    )
    args.ins_nums = dataset.get_ins_nums()
    print(f"ins_nums: {args.ins_nums}")

    pl.seed_everything(args.seed)

    # 创建回调函数
    callbacks = load_callbacks(args)

    full_dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(embedding_data))
    # 使用 KFold 分割数据集
    kfold = KFold(n_splits=args.k_folds, shuffle=True)

    # 创建一个数据模块列表，每个数据模块对应一个 fold
    for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
        print(f"=== Training fold {fold} ===")
        train_data = Subset(full_dataset, train_idx)
        val_data = Subset(full_dataset, val_idx)
        data_module = PLData(train_data, val_data, batch_size=args.batch_size, num_workers=args.num_workers, seed=args.seed)


        if fold > 0 and callbacks[0].stopped_epoch is not None:
            # 加载之前训练的模型
            print(f"Fold: {fold} Loading model from {callbacks[1].best_model_path}")
            model = PLModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        else:
            if args.restart:
                model_path = get_best_saved_model_filename(log_dir=args.log_dir, sub_dir=args.sub_dir)
                if model_path is None:
                    raise Exception("No saved model found!")
                print(f"=== Restart Training ===")
                print(f"Loading model from {model_path}")
                try:
                    model = PLModel.load_from_checkpoint(checkpoint_path=model_path)
                except Exception as e:
                    print(f"Error: {e}")
            else:
                print(f"=== New Training ===")
                model = PLModel(**vars(args))

        # 创建训练器
        logger = TensorBoardLogger(save_dir=args.log_dir, name=f'{args.model_name}_{sum(args.out_channels)}')
        trainer = Trainer(
            max_epochs=args.max_epochs,
            callbacks=callbacks,
            logger=logger,
        )
        trainer.fit(model, data_module)
        # 在每个数据集的训练之后，执行其他操作（如保存模型、记录日志等）
        # log_metrics(logger, trainer)

    # 训练完成后，保存编译最后一个模型
    if callbacks[0].stopped_epoch is not None:
        # 加载之前训练的模型
        print(f"Best model to save {callbacks[1].best_model_path}")
        best_score=callbacks[1].best_model_score.cpu().numpy()
        model = PLModel.load_from_checkpoint(checkpoint_path=callbacks[1].best_model_path)
        model.freeze()
        embedding = torch.zeros(1, args.num_channels, len(args.num_embeds)).to(torch.int32)
        example_input = torch.rand(1, args.num_channels, sum(args.ins_nums))
        model_name = get_model_name(args.version, args.ds_name, sum(args.out_channels), best_score, args.direct)
        model_name = f"{model_name}"
        model.to_onnx(f"{args.model_dir}/{model_name}.onnx", (embedding, example_input), export_params=True)
        dataset.save_model_inputs_config(f"{args.model_dir}/{model_name}.json")

        #查看模型大小
        model.example_input_array = (embedding, example_input)
        summary = ModelSummary(model,max_depth=-1)
        print(summary) 

        print(f"Model saved to： {model_name}.onnx")
        print("=== Training Finished ===\n\n")

if __name__ == '__main__':
    parser = ArgumentParser()

    # Data API ==============================
    parser.add_argument('--ds_name', default='10HF', choices=['8HF', '10HF', '15HF', '16HF'], type=str)
    parser.add_argument('--ds_files', default='["main.2023"]', type=str)
    parser.add_argument('--start_time', default='', type=str)
    parser.add_argument('--end_time', default='', type=str)
    parser.add_argument('--direct', default='ls', choices=['ls', 'long', 'short'], type=str)
    parser.add_argument('--filter_win', default=0, type=int)
    parser.add_argument('--is_normal', default=True, type=bool)
    parser.add_argument('--verbose', default=False, type=bool)
    parser.add_argument('--fut_codes', default='MAIN_SEL_FUT_CODES', choices=['MAIN_FUT_CODES', 'MAIN_SEL_FUT_CODES', 'SF_FUT_CODES'], type=str)
    parser.add_argument('--data_path', default='e:/featdata', type=str)

    # Data module ===========================
    parser.add_argument('--batch_size', default=256, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)

    # Model Hyperparameters =================
    parser.add_argument('--version', default='TCN', type=str)
    parser.add_argument('--model_name', default='tcn_model', type=str)
    parser.add_argument('--loss', default='mse', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)

    # model
    parser.add_argument('--num_embeds', default='[72]', type=str)
    parser.add_argument('--num_channels', default=10, type=int)
    parser.add_argument('--kernel_size', default=2, type=int)
    # 59 (16, 32, 576, 128) (32, 64, 1152, 256) (24, 48, 864, 256)
    # 110 (16, 32, 800, 128) (24, 48, 1200, 256) (32, 64, 1600, 256)
    # "out_channels": (16, 32, 800, 256), 
    # "out_channels": (24, 48, 1200, 256), 
    # parser.add_argument('--out_channels', default='(16, 32, 800, 256)', type=str)
    parser.add_argument('--out_channels', default='(24, 48)', type=str)
    parser.add_argument('--ins_nums', default='(0, 51, 51, 0)', type=str)
    parser.add_argument('--dropout', default=0.2, type=float)
    parser.add_argument('--activation', default='relu', choices=['relu', 'gelu', 'prelu', 'leakyrelu'], type=str)
    # parser.add_argument('--pooling', default='max', choices=['max', 'avg'], type=str)

    # LR Scheduler
    # lr_scheduler 可以有以下选项：
    #     'step'：使用步进式学习率调度器
    #     'cosine'：使用余弦退火学习率调度器
    #     'plateau'：使用学习率衰减调度器
    #     'exponential'：使用指数衰减学习率调度器
    #     'reduce_on_plateau'：使用学习率衰减调度器，并在验证集上监测指标不再改善时降低学习率
    parser.add_argument('--lr_scheduler', default='reduce_on_plateau', choices=['step', 'cosine', 'plateau', 'reduce_on_plateau'], type=str)
    parser.add_argument('--lr_decay_steps', default=5, type=int)
    parser.add_argument('--lr_decay_rate', default=0.1, type=float)
    parser.add_argument('--lr_decay_min_lr', default=1e-6, type=float)

    # Restart Control
    parser.add_argument('--restart', default=False, type=bool)

    # Training Info
    parser.add_argument('--max_epochs', default=7, type=int)
    parser.add_argument('--early_stop', default=5, type=int)
    parser.add_argument('--min_delta', default=1e-6, type=float) 
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--log_dir', default='lightning_logs', type=str)
    parser.add_argument('--sub_dir', default='', type=str)
    
    # Others
    parser.add_argument('--model_dir', default='model', type=str)
    # Reset Some Default Trainer Arguments' Default Values
    # parser.set_defaults(max_epochs=10)

    args = parser.parse_args()

    main(args)
