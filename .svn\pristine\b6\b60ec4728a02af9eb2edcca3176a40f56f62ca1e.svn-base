{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["learning a [<PERSON><PERSON><PERSON>](http://www.scholarpedia.org/article/<PERSON><PERSON>-<PERSON>_equation) system"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test error: \n", "0.139603909616\n"]}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x10c5b9dd8>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"142pt\" version=\"1.1\" viewBox=\"0 0 669 142\" width=\"669pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 142.603363 \n", "L 669.207813 142.603363 \n", "L 669.207813 0 \n", "L -0 0 \n", "L -0 142.603363 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 35.482813 121.725237 \n", "L 649.282813 121.725237 \n", "L 649.282813 38.025238 \n", "L 35.482813 38.025238 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"line2d_1\">\n", "    <path clip-path=\"url(#pcf74c83c01)\" d=\"M 35.482813 61.288481 \n", "L 36.096612 82.739583 \n", "L 36.710413 97.100515 \n", "L 37.170763 103.535119 \n", "L 37.784562 109.375053 \n", "L 38.091462 110.992598 \n", "L 38.244913 109.889543 \n", "L 38.551813 102.853275 \n", "L 39.472513 75.136883 \n", "L 39.932862 69.684215 \n", "L 40.239763 68.331553 \n", "L 40.393213 68.240572 \n", "L 40.546663 68.465968 \n", "L 40.853563 69.28482 \n", "L 41.007013 69.166567 \n", "L 41.160463 68.275562 \n", "L 41.467363 63.996622 \n", "L 41.927713 57.381637 \n", "L 42.081163 56.919442 \n", "L 42.234613 57.394318 \n", "L 42.541513 60.252995 \n", "L 43.462213 70.446643 \n", "L 43.922563 73.887704 \n", "L 44.229463 78.779108 \n", "L 45.150163 96.315482 \n", "L 45.457063 98.001487 \n", "L 45.610513 97.829472 \n", "L 45.917413 95.79209 \n", "L 46.377763 90.274833 \n", "L 46.838113 82.030488 \n", "L 47.758813 62.082327 \n", "L 48.219163 57.495091 \n", "L 49.600213 46.798519 \n", "L 49.753663 47.423054 \n", "L 50.060563 52.219473 \n", "L 50.674363 70.053572 \n", "L 51.595063 95.51512 \n", "L 52.208863 107.800126 \n", "L 52.669213 113.478359 \n", "L 52.822663 114.315303 \n", "L 52.976113 114.37 \n", "L 53.129563 113.419218 \n", "L 53.436463 107.755961 \n", "L 54.510613 75.815239 \n", "L 54.817513 72.818289 \n", "L 54.970963 72.393852 \n", "L 55.124413 72.522511 \n", "L 55.431313 73.84846 \n", "L 55.738213 75.344669 \n", "L 55.891663 75.561587 \n", "L 56.045113 75.081156 \n", "L 56.352013 71.440652 \n", "L 56.965813 60.570795 \n", "L 57.119263 59.653075 \n", "L 57.272713 59.562749 \n", "L 57.579613 60.699593 \n", "L 57.886513 61.881497 \n", "L 58.039963 62.089271 \n", "L 58.193413 61.98317 \n", "L 58.807213 60.644637 \n", "L 58.960663 61.213003 \n", "L 59.267563 65.272946 \n", "L 60.495163 90.418256 \n", "L 61.262413 97.207344 \n", "L 61.722763 100.149712 \n", "L 61.876213 100.17552 \n", "L 62.029663 99.247058 \n", "L 62.336563 93.830694 \n", "L 63.564163 62.967609 \n", "L 64.024513 57.72609 \n", "L 64.484863 55.293449 \n", "L 64.791763 53.839294 \n", "L 65.559013 48.749023 \n", "L 65.712463 49.930871 \n", "L 66.019362 55.717072 \n", "L 67.860763 102.934775 \n", "L 68.474563 111.713602 \n", "L 68.781463 113.072929 \n", "L 68.934913 112.423331 \n", "L 69.241813 107.401373 \n", "L 70.469413 72.86191 \n", "L 70.776313 70.593458 \n", "L 70.929763 70.408722 \n", "L 71.083213 70.68958 \n", "L 71.697012 72.885983 \n", "L 71.850463 72.536664 \n", "L 72.003913 71.363168 \n", "L 72.310813 66.548222 \n", "L 72.771163 59.22556 \n", "L 72.924613 58.585833 \n", "L 73.078063 58.890208 \n", "L 73.384963 61.159442 \n", "L 73.845313 64.720035 \n", "L 74.152213 65.615979 \n", "L 74.459113 65.768882 \n", "L 74.612563 66.10117 \n", "L 74.766013 67.006263 \n", "L 75.072913 71.417353 \n", "L 75.993613 90.626122 \n", "L 76.300513 93.240878 \n", "L 76.607413 94.062857 \n", "L 76.914313 94.147411 \n", "L 77.221213 94.068246 \n", "L 77.374662 93.811991 \n", "L 77.528113 93.13188 \n", "L 77.835013 89.35452 \n", "L 78.448813 73.522814 \n", "L 79.062613 61.330274 \n", "L 79.676413 54.062494 \n", "L 80.443663 47.952202 \n", "L 80.750563 45.970776 \n", "L 80.904013 45.824097 \n", "L 81.057463 46.76026 \n", "L 81.364363 52.150117 \n", "L 81.978163 70.915909 \n", "L 82.898863 97.622036 \n", "L 83.512663 109.48431 \n", "L 83.973013 114.749593 \n", "L 84.126463 115.445803 \n", "L 84.279913 115.349502 \n", "L 84.433363 114.212329 \n", "L 84.740263 107.965455 \n", "L 85.814413 76.570457 \n", "L 86.121313 74.20255 \n", "L 86.274763 74.032849 \n", "L 86.428213 74.371839 \n", "L 87.195463 77.743108 \n", "L 87.348913 77.149635 \n", "L 87.655813 73.102404 \n", "L 88.269613 61.577483 \n", "L 88.576513 59.936831 \n", "L 88.729963 59.917726 \n", "L 89.036863 60.190736 \n", "L 89.190313 60.116835 \n", "L 89.497213 59.181739 \n", "L 90.111013 56.493794 \n", "L 90.264463 56.823129 \n", "L 90.417913 58.139944 \n", "L 90.724813 63.99803 \n", "L 91.645513 85.87812 \n", "L 92.412763 97.226819 \n", "L 93.026563 104.355231 \n", "L 93.180013 105.195259 \n", "L 93.333463 105.22412 \n", "L 93.486913 104.176254 \n", "L 93.793813 98.399046 \n", "L 95.021413 65.808221 \n", "L 95.328313 62.425668 \n", "L 95.635213 61.002923 \n", "L 96.249013 60.035818 \n", "L 96.555913 57.476394 \n", "L 97.016263 52.361396 \n", "L 97.169713 52.179311 \n", "L 97.323163 53.268648 \n", "L 97.630063 58.644645 \n", "L 99.011113 89.224759 \n", "L 100.085263 106.908032 \n", "L 100.238713 107.957698 \n", "L 100.392163 108.165396 \n", "L 100.545613 107.356494 \n", "L 100.852513 102.306454 \n", "L 101.466313 83.561258 \n", "L 101.926663 71.196015 \n", "L 102.233563 66.247836 \n", "L 102.540463 64.110195 \n", "L 102.693913 63.865614 \n", "L 102.847363 63.963617 \n", "L 103.154263 64.371574 \n", "L 103.307713 64.220605 \n", "L 103.461163 63.545637 \n", "L 103.768063 60.296106 \n", "L 104.228413 54.48338 \n", "L 104.381863 54.251431 \n", "L 104.535313 55.300451 \n", "L 104.842213 60.340865 \n", "L 105.762913 78.16867 \n", "L 106.530163 89.365952 \n", "L 107.297413 102.536901 \n", "L 107.450863 103.668997 \n", "L 107.604313 103.913667 \n", "L 107.757763 103.16553 \n", "L 108.064663 98.702182 \n", "L 108.678463 82.746401 \n", "L 109.445713 64.407148 \n", "L 109.752613 60.927032 \n", "L 110.059513 59.594095 \n", "L 110.366413 59.030244 \n", "L 110.673313 57.644115 \n", "L 111.133663 52.866118 \n", "L 111.440563 50.492242 \n", "L 111.594013 50.759078 \n", "L 111.747463 52.302385 \n", "L 112.054363 58.677018 \n", "L 113.281963 89.30481 \n", "L 114.356113 108.194172 \n", "L 114.663013 110.487066 \n", "L 114.816463 110.387895 \n", "L 114.969913 109.184073 \n", "L 115.276813 103.094653 \n", "L 116.504413 69.336096 \n", "L 116.811313 66.944816 \n", "L 116.964763 66.67641 \n", "L 117.118213 66.847782 \n", "L 117.578563 68.081973 \n", "L 117.732013 67.942101 \n", "L 117.885463 67.14092 \n", "L 118.192363 63.318964 \n", "L 118.652713 56.746725 \n", "L 118.806163 56.349569 \n", "L 118.959613 57.137796 \n", "L 119.266513 61.166618 \n", "L 120.033763 72.224485 \n", "L 120.801013 79.390317 \n", "L 122.028613 98.818067 \n", "L 122.182063 98.628067 \n", "L 122.488963 95.977595 \n", "L 122.949313 88.548198 \n", "L 123.716563 72.05288 \n", "L 124.176913 62.6341 \n", "L 124.637263 57.774568 \n", "L 125.404513 51.766622 \n", "L 125.864863 47.836324 \n", "L 126.018313 47.283879 \n", "L 126.171763 47.533073 \n", "L 126.325213 48.841058 \n", "L 126.632113 54.851888 \n", "L 128.473513 104.005289 \n", "L 128.933863 111.202506 \n", "L 129.240763 113.874945 \n", "L 129.394213 114.215594 \n", "L 129.547663 113.611342 \n", "L 129.701113 111.822565 \n", "L 130.008013 104.338803 \n", "L 130.928713 76.27939 \n", "L 131.235613 72.732177 \n", "L 131.389063 72.07878 \n", "L 131.542513 72.018154 \n", "L 131.849413 73.120271 \n", "L 132.309763 75.04909 \n", "L 132.463213 74.79702 \n", "L 132.616663 73.703482 \n", "L 132.923563 68.890114 \n", "L 133.383913 60.701047 \n", "L 133.537363 59.596012 \n", "L 133.690813 59.401802 \n", "L 133.997713 60.622053 \n", "L 134.458063 62.59899 \n", "L 134.611513 62.706736 \n", "L 134.764963 62.519441 \n", "L 135.225313 61.652162 \n", "L 135.378763 62.085191 \n", "L 135.532213 63.379462 \n", "L 135.839113 68.943817 \n", "L 136.606363 86.482755 \n", "L 137.066713 91.945163 \n", "L 137.987413 98.252987 \n", "L 138.140863 98.82587 \n", "L 138.294313 98.808433 \n", "L 138.447763 97.886822 \n", "L 138.754663 92.566953 \n", "L 139.982263 62.289819 \n", "L 140.442613 56.921404 \n", "L 140.902963 54.111698 \n", "L 141.209863 52.45478 \n", "L 141.823663 47.881031 \n", "L 141.977113 48.185974 \n", "L 142.130563 49.725558 \n", "L 142.437463 56.091686 \n", "L 144.125413 101.593743 \n", "L 144.739213 111.309725 \n", "L 145.046113 113.689884 \n", "L 145.199563 113.81813 \n", "L 145.353013 112.945548 \n", "L 145.659913 107.356537 \n", "L 146.734063 75.369191 \n", "L 147.040963 72.161774 \n", "L 147.194413 71.622848 \n", "L 147.347863 71.645079 \n", "L 147.654763 72.803356 \n", "L 147.961663 74.22128 \n", "L 148.115113 74.435191 \n", "L 148.268563 73.96599 \n", "L 148.575463 70.365112 \n", "L 149.189263 59.917734 \n", "L 149.342713 59.184276 \n", "L 149.496163 59.30518 \n", "L 149.803063 60.946765 \n", "L 150.263413 63.204148 \n", "L 150.416863 63.374287 \n", "L 150.570313 63.262906 \n", "L 150.877213 62.787995 \n", "L 151.030663 62.904131 \n", "L 151.184113 63.665695 \n", "L 151.491013 68.031517 \n", "L 152.565163 90.232032 \n", "L 153.025513 93.742552 \n", "L 153.792763 97.375829 \n", "L 153.946213 97.500548 \n", "L 154.099663 96.908878 \n", "L 154.406563 92.525 \n", "L 155.787613 60.280192 \n", "L 156.247963 55.422047 \n", "L 157.475563 47.20446 \n", "L 157.629013 47.440947 \n", "L 157.782463 48.892185 \n", "L 158.089363 55.132844 \n", "L 159.930763 104.712563 \n", "L 160.391113 111.690963 \n", "L 160.698013 114.212122 \n", "L 160.851463 114.46752 \n", "L 161.004913 113.770338 \n", "L 161.311813 108.594528 \n", "L 162.539413 74.33107 \n", "L 162.846313 72.538118 \n", "L 162.999763 72.559853 \n", "L 163.306663 73.762881 \n", "L 163.613563 75.324974 \n", "L 163.767013 75.662953 \n", "L 163.920463 75.347077 \n", "L 164.073913 74.158692 \n", "L 164.380813 69.126892 \n", "L 164.841163 60.898879 \n", "L 164.994613 59.776234 \n", "L 165.148063 59.513562 \n", "L 165.301513 59.842482 \n", "L 165.915313 61.914649 \n", "L 166.068763 61.856136 \n", "L 166.375663 61.037855 \n", "L 166.682563 60.396107 \n", "L 166.836013 60.801438 \n", "L 166.989463 62.10389 \n", "L 167.296363 67.756687 \n", "L 168.217063 88.081683 \n", "L 168.677413 93.147433 \n", "L 169.598113 100.361002 \n", "L 169.751563 100.576862 \n", "L 169.905013 99.900634 \n", "L 170.211913 95.024126 \n", "L 171.592963 61.49268 \n", "L 172.053313 57.167237 \n", "L 172.974013 51.987723 \n", "L 173.280913 49.238278 \n", "L 173.434363 48.850423 \n", "L 173.587813 49.643779 \n", "L 173.894713 54.78457 \n", "L 174.661963 77.116276 \n", "L 175.429213 95.899044 \n", "L 176.196463 109.277915 \n", "L 176.503363 112.285136 \n", "L 176.656813 112.832838 \n", "L 176.810263 112.486331 \n", "L 176.963713 111.010397 \n", "L 177.270613 104.165953 \n", "L 178.191313 75.748678 \n", "L 178.498213 71.457785 \n", "L 178.805113 70.069798 \n", "L 178.958563 70.20196 \n", "L 179.572363 72.407759 \n", "L 179.725813 72.24961 \n", "L 179.879263 71.323661 \n", "L 180.186163 66.94858 \n", "L 180.646513 59.31257 \n", "L 180.799963 58.445114 \n", "L 180.953413 58.59578 \n", "L 181.260313 60.852727 \n", "L 181.720663 64.914668 \n", "L 182.027563 66.217527 \n", "L 182.487913 66.913733 \n", "L 182.641363 67.711352 \n", "L 182.948263 71.732184 \n", "L 184.022413 92.531208 \n", "L 184.329313 94.069336 \n", "L 184.482763 94.199332 \n", "L 184.789663 93.875913 \n", "L 185.096563 93.315567 \n", "L 185.250013 92.860031 \n", "L 185.403463 92.042717 \n", "L 185.710363 88.218095 \n", "L 186.324163 72.678636 \n", "L 186.784513 63.207404 \n", "L 187.398313 55.261993 \n", "L 188.165563 48.590172 \n", "L 188.625913 45.661515 \n", "L 188.779363 45.750981 \n", "L 188.932813 46.964888 \n", "L 189.239713 52.842959 \n", "L 190.006963 76.935735 \n", "L 190.774213 98.409429 \n", "L 191.388013 110.075281 \n", "L 191.848363 115.057057 \n", "L 192.001813 115.607817 \n", "L 192.155263 115.321352 \n", "L 192.308713 113.942508 \n", "L 192.615613 107.156021 \n", "L 193.536313 78.535833 \n", "L 193.843213 74.927143 \n", "L 193.996663 74.273916 \n", "L 194.150113 74.232523 \n", "L 194.457013 75.441991 \n", "L 194.917363 77.873426 \n", "L 195.070813 77.920449 \n", "L 195.224263 77.150809 \n", "L 195.531163 72.736098 \n", "L 196.144963 61.403504 \n", "L 196.451863 59.94225 \n", "L 196.605313 59.925902 \n", "L 196.912213 60.06966 \n", "L 197.065663 59.894994 \n", "L 197.372563 58.771229 \n", "L 197.832913 56.369206 \n", "L 197.986363 56.095874 \n", "L 198.139813 56.557689 \n", "L 198.293263 58.05344 \n", "L 198.600163 64.220173 \n", "L 199.520863 86.093588 \n", "L 200.441563 99.81091 \n", "L 200.901913 104.897733 \n", "L 201.055363 105.68133 \n", "L 201.208813 105.621258 \n", "L 201.362263 104.460478 \n", "L 201.669163 98.463989 \n", "L 202.896763 65.910215 \n", "L 203.203663 62.690242 \n", "L 203.510563 61.437357 \n", "L 203.970913 61.102787 \n", "L 204.124363 60.601379 \n", "L 204.431263 57.959194 \n", "L 204.891613 52.719049 \n", "L 205.045063 52.516693 \n", "L 205.198513 53.585649 \n", "L 205.505413 58.88263 \n", "L 206.733013 85.961305 \n", "L 208.114063 107.328921 \n", "L 208.267513 107.548969 \n", "L 208.420963 106.756337 \n", "L 208.727863 101.801402 \n", "L 209.341663 83.400291 \n", "L 209.955463 68.030656 \n", "L 210.262363 64.342054 \n", "L 210.569263 63.166987 \n", "L 210.722713 63.187908 \n", "L 211.029613 63.46895 \n", "L 211.183063 63.274466 \n", "L 211.336513 62.57808 \n", "L 211.643413 59.380302 \n", "L 212.103763 53.839229 \n", "L 212.257213 53.712961 \n", "L 212.410663 54.879792 \n", "L 212.717563 60.217703 \n", "L 213.638263 79.241555 \n", "L 214.558963 94.079126 \n", "L 215.172763 103.925847 \n", "L 215.326213 104.957462 \n", "L 215.479663 105.087364 \n", "L 215.633113 104.193056 \n", "L 215.940013 99.296584 \n", "L 216.553813 82.310024 \n", "L 217.167613 66.854325 \n", "L 217.474513 62.562487 \n", "L 217.781413 60.789855 \n", "L 218.088313 60.424895 \n", "L 218.241763 60.26115 \n", "L 218.395213 59.82486 \n", "L 218.702113 57.587251 \n", "L 219.315913 51.443086 \n", "L 219.469363 51.793824 \n", "L 219.776263 56.234382 \n", "L 221.464213 93.963238 \n", "L 222.231463 106.933118 \n", "L 222.538363 109.089571 \n", "L 222.691813 108.852298 \n", "L 222.845263 107.494024 \n", "L 223.152163 101.249073 \n", "L 224.379763 67.863645 \n", "L 224.686663 65.241678 \n", "L 224.840113 64.85571 \n", "L 224.993563 64.905231 \n", "L 225.453913 65.690689 \n", "L 225.607363 65.38214 \n", "L 225.760813 64.43884 \n", "L 226.067713 60.586025 \n", "L 226.528063 55.066022 \n", "L 226.681513 55.229244 \n", "L 226.988413 58.905242 \n", "L 228.062563 77.35434 \n", "L 228.676363 85.150069 \n", "L 229.597063 101.097735 \n", "L 229.750513 102.029893 \n", "L 229.903963 102.05248 \n", "L 230.057413 101.11316 \n", "L 230.364313 96.592618 \n", "L 230.978113 81.874999 \n", "L 231.745363 63.950263 \n", "L 232.052263 60.179655 \n", "L 232.359163 58.450331 \n", "L 232.819513 56.639309 \n", "L 233.126413 54.179099 \n", "L 233.586763 49.694468 \n", "L 233.740213 49.16481 \n", "L 233.893663 49.69447 \n", "L 234.200563 54.379173 \n", "L 234.967813 76.203953 \n", "L 235.735063 94.209679 \n", "L 236.502313 108.158458 \n", "L 236.809213 111.462897 \n", "L 236.962663 112.174675 \n", "L 237.116113 111.999907 \n", "L 237.269563 110.710032 \n", "L 237.576463 104.318869 \n", "L 238.650613 72.848818 \n", "L 238.957513 69.608529 \n", "L 239.110963 69.03904 \n", "L 239.264413 69.018646 \n", "L 239.571313 70.019021 \n", "L 239.878213 71.068353 \n", "L 240.031663 70.989079 \n", "L 240.185113 70.188269 \n", "L 240.492013 66.140535 \n", "L 240.952363 58.68803 \n", "L 241.105813 57.863755 \n", "L 241.259263 58.140245 \n", "L 241.566163 60.909004 \n", "L 242.179963 67.32985 \n", "L 242.486863 68.743422 \n", "L 242.793763 69.705919 \n", "L 243.100663 72.273693 \n", "L 243.407563 77.86336 \n", "L 244.021363 90.925814 \n", "L 244.328263 94.347819 \n", "L 244.481713 95.009718 \n", "L 244.635163 95.058412 \n", "L 244.942063 93.863713 \n", "L 245.555863 89.510254 \n", "L 245.862763 85.708069 \n", "L 246.323113 75.203418 \n", "L 246.936913 62.300817 \n", "L 247.550713 54.964457 \n", "L 248.471413 46.793409 \n", "L 248.778313 45.547225 \n", "L 248.931763 46.048765 \n", "L 249.238663 50.526756 \n", "L 249.699013 63.630395 \n", "L 250.773163 95.976032 \n", "L 251.386963 108.531722 \n", "L 251.847313 114.354605 \n", "L 252.154213 115.601828 \n", "L 252.307663 114.945157 \n", "L 252.461113 113.10616 \n", "L 252.768013 105.399145 \n", "L 253.535263 80.429558 \n", "L 253.842163 75.698635 \n", "L 254.149063 74.171199 \n", "L 254.302513 74.319092 \n", "L 254.609413 75.747156 \n", "L 255.069763 77.941916 \n", "L 255.223213 77.718788 \n", "L 255.376663 76.609754 \n", "L 255.683563 71.583815 \n", "L 256.143913 62.522147 \n", "L 256.450813 60.138842 \n", "L 256.604263 59.916428 \n", "L 257.064613 60.064905 \n", "L 257.218063 59.801956 \n", "L 257.524963 58.519937 \n", "L 257.985313 56.257942 \n", "L 258.138763 56.214251 \n", "L 258.292213 57.021775 \n", "L 258.599113 61.924215 \n", "L 259.826713 89.551533 \n", "L 260.747413 102.402123 \n", "L 261.054313 105.20384 \n", "L 261.207763 105.707343 \n", "L 261.361213 105.267927 \n", "L 261.514663 103.653758 \n", "L 261.821563 96.835144 \n", "L 262.895713 67.365175 \n", "L 263.202613 63.414069 \n", "L 263.509513 61.597085 \n", "L 263.816413 61.199097 \n", "L 263.969863 61.139221 \n", "L 264.123313 60.878691 \n", "L 264.276763 60.192722 \n", "L 264.583663 57.154556 \n", "L 265.044013 52.442322 \n", "L 265.197463 52.726647 \n", "L 265.504363 56.854778 \n", "L 267.499213 97.974828 \n", "L 267.959563 105.353915 \n", "L 268.266463 107.617779 \n", "L 268.419913 107.466684 \n", "L 268.573363 106.247987 \n", "L 268.880263 100.447037 \n", "L 270.107863 67.151299 \n", "L 270.414763 64.029759 \n", "L 270.721663 63.251033 \n", "L 271.182013 63.579606 \n", "L 271.335463 63.219147 \n", "L 271.642363 60.744954 \n", "L 272.256163 53.728828 \n", "L 272.409613 54.086446 \n", "L 272.716513 58.295249 \n", "L 273.944113 82.125554 \n", "L 274.557913 91.988923 \n", "L 275.171713 102.696416 \n", "L 275.478613 104.938888 \n", "L 275.632063 104.704945 \n", "L 275.785513 103.424459 \n", "L 276.092413 97.874342 \n", "L 277.473463 63.558806 \n", "L 277.780363 61.014728 \n", "L 278.087263 60.316825 \n", "L 278.394163 59.935731 \n", "L 278.547613 59.34153 \n", "L 278.854513 56.752551 \n", "L 279.314863 51.687735 \n", "L 279.468313 51.276993 \n", "L 279.621763 52.118562 \n", "L 279.928663 57.381533 \n", "L 281.309713 89.628834 \n", "L 282.383863 107.782214 \n", "L 282.690763 109.353012 \n", "L 282.844213 108.718909 \n", "L 283.151113 103.901711 \n", "L 283.764913 84.897785 \n", "L 284.225263 72.303765 \n", "L 284.532163 67.359315 \n", "L 284.839063 65.296776 \n", "L 284.992513 65.119907 \n", "L 285.299413 65.683195 \n", "L 285.452863 66.001574 \n", "L 285.606313 66.019575 \n", "L 285.759763 65.505171 \n", "L 286.066663 62.414202 \n", "L 286.527013 55.890071 \n", "L 286.680463 55.194699 \n", "L 286.833913 55.765274 \n", "L 287.140813 59.938079 \n", "L 288.061513 75.732346 \n", "L 288.828763 85.336106 \n", "L 289.749463 100.95185 \n", "L 289.902913 101.623313 \n", "L 290.056363 101.371079 \n", "L 290.363263 98.12139 \n", "L 290.823613 88.496022 \n", "L 291.897763 63.374067 \n", "L 292.204663 59.780479 \n", "L 292.511563 58.071263 \n", "L 292.971913 55.954936 \n", "L 293.432263 51.692943 \n", "L 293.739163 49.124731 \n", "L 293.892613 48.859391 \n", "L 294.046063 49.68662 \n", "L 294.352963 54.907495 \n", "L 296.347813 104.237756 \n", "L 296.808163 110.81191 \n", "L 297.115063 112.603061 \n", "L 297.268513 112.215619 \n", "L 297.421963 110.671503 \n", "L 297.728863 103.751883 \n", "L 298.649563 75.339396 \n", "L 298.956463 70.964213 \n", "L 299.263363 69.538444 \n", "L 299.416813 69.661476 \n", "L 300.030613 71.828713 \n", "L 300.184063 71.637847 \n", "L 300.337513 70.678323 \n", "L 300.644413 66.299048 \n", "L 301.104763 58.89737 \n", "L 301.258213 58.178583 \n", "L 301.411663 58.503714 \n", "L 301.718563 61.128699 \n", "L 302.332363 66.640678 \n", "L 302.639263 67.591496 \n", "L 302.946163 68.293623 \n", "L 303.099613 69.213283 \n", "L 303.406513 73.417453 \n", "L 304.327213 92.012056 \n", "L 304.634113 94.199619 \n", "L 304.787563 94.423432 \n", "L 304.941013 94.238201 \n", "L 305.401363 92.60879 \n", "L 305.708263 91.087714 \n", "L 306.015163 87.985619 \n", "L 306.322063 81.722757 \n", "L 307.089313 63.803237 \n", "L 307.703113 55.696858 \n", "L 308.470363 48.592973 \n", "L 308.930713 45.578465 \n", "L 309.084163 45.480364 \n", "L 309.237613 46.416064 \n", "L 309.544513 51.798785 \n", "L 310.158313 70.587409 \n", "L 311.079013 97.56277 \n", "L 311.692813 109.595725 \n", "L 312.153163 114.914222 \n", "L 312.306613 115.641375 \n", "L 312.460063 115.588785 \n", "L 312.613513 114.51237 \n", "L 312.920413 108.421944 \n", "L 313.994563 76.904301 \n", "L 314.301463 74.515321 \n", "L 314.454913 74.344332 \n", "L 314.608363 74.686797 \n", "L 315.375613 78.186079 \n", "L 315.529063 77.650003 \n", "L 315.835963 73.710547 \n", "L 316.449763 61.940979 \n", "L 316.756663 60.04383 \n", "L 316.910113 59.907235 \n", "L 317.217013 59.975783 \n", "L 317.370463 59.814825 \n", "L 317.677363 58.733437 \n", "L 318.291163 55.754954 \n", "L 318.444613 55.959403 \n", "L 318.598063 57.127809 \n", "L 318.904963 62.75331 \n", "L 319.979113 87.789681 \n", "L 320.899813 101.450258 \n", "L 321.360163 105.886471 \n", "L 321.513613 106.157732 \n", "L 321.667063 105.41052 \n", "L 321.973963 100.252756 \n", "L 323.355013 64.921672 \n", "L 323.661913 62.47121 \n", "L 323.968813 61.782187 \n", "L 324.275713 61.727246 \n", "L 324.429163 61.406818 \n", "L 324.582613 60.597765 \n", "L 324.889513 57.277835 \n", "L 325.196413 53.515449 \n", "L 325.349863 52.767779 \n", "L 325.503313 53.262276 \n", "L 325.810213 57.68185 \n", "L 327.344713 89.234535 \n", "L 328.418863 106.345682 \n", "L 328.572313 106.974212 \n", "L 328.725763 106.660719 \n", "L 328.879213 105.271952 \n", "L 329.186113 99.25024 \n", "L 330.413713 66.405982 \n", "L 330.720613 63.303474 \n", "L 331.027513 62.485575 \n", "L 331.487863 62.565345 \n", "L 331.641313 62.079487 \n", "L 331.948213 59.415346 \n", "L 332.408563 53.65894 \n", "L 332.562013 53.065049 \n", "L 332.715463 53.756743 \n", "L 333.022363 58.568389 \n", "L 334.249963 83.870608 \n", "L 335.017213 97.196291 \n", "L 335.477563 104.450565 \n", "L 335.784463 106.225549 \n", "L 335.937913 105.699505 \n", "L 336.244813 101.383738 \n", "L 336.705163 88.881005 \n", "L 337.472413 68.130669 \n", "L 337.779313 63.648923 \n", "L 338.086213 61.802605 \n", "L 338.239663 61.580619 \n", "L 338.546563 61.590799 \n", "L 338.700013 61.375909 \n", "L 338.853463 60.73954 \n", "L 339.160363 57.864201 \n", "L 339.620713 52.61202 \n", "L 339.774163 52.390348 \n", "L 339.927613 53.470576 \n", "L 340.234513 58.965994 \n", "L 341.308663 83.317112 \n", "L 342.843163 107.367023 \n", "L 342.996613 107.548223 \n", "L 343.150063 106.68146 \n", "L 343.456963 101.538811 \n", "L 344.224213 78.512642 \n", "L 344.684563 67.826916 \n", "L 344.991463 64.176924 \n", "L 345.298363 63.046401 \n", "L 345.451813 63.088829 \n", "L 345.758713 63.385414 \n", "L 345.912163 63.172983 \n", "L 346.065613 62.442048 \n", "L 346.372513 59.176685 \n", "L 346.832863 53.737319 \n", "L 346.986313 53.6932 \n", "L 347.139763 54.943698 \n", "L 347.446663 60.416255 \n", "L 348.367363 79.541885 \n", "L 349.288063 94.434042 \n", "L 349.901863 104.147935 \n", "L 350.055313 105.118673 \n", "L 350.208763 105.174477 \n", "L 350.362213 104.193164 \n", "L 350.669113 99.108781 \n", "L 351.436363 77.523472 \n", "L 351.896713 66.625132 \n", "L 352.203613 62.477711 \n", "L 352.510513 60.822702 \n", "L 352.817413 60.516652 \n", "L 352.970863 60.354372 \n", "L 353.124313 59.900297 \n", "L 353.431213 57.589102 \n", "L 354.045013 51.519168 \n", "L 354.198463 51.973113 \n", "L 354.505363 56.589368 \n", "L 356.039863 91.238718 \n", "L 356.960563 106.935742 \n", "L 357.267463 108.966577 \n", "L 357.420913 108.639018 \n", "L 357.574363 107.178842 \n", "L 357.881263 100.759619 \n", "L 358.955413 69.934169 \n", "L 359.262313 65.96337 \n", "L 359.569213 64.671381 \n", "L 359.722663 64.733792 \n", "L 360.183013 65.453504 \n", "L 360.336463 65.087553 \n", "L 360.643363 62.383654 \n", "L 361.257163 54.885548 \n", "L 361.410613 55.197577 \n", "L 361.717513 59.117678 \n", "L 362.791663 77.84522 \n", "L 363.405463 85.874928 \n", "L 364.326163 101.580843 \n", "L 364.479613 102.399207 \n", "L 364.633063 102.292986 \n", "L 364.786513 101.212995 \n", "L 365.093413 96.404789 \n", "L 365.860663 77.225627 \n", "L 366.474463 63.551369 \n", "L 366.781363 60.054117 \n", "L 367.088263 58.52289 \n", "L 367.395163 57.538457 \n", "L 367.702063 55.682474 \n", "L 368.469313 49.379746 \n", "L 368.622763 50.092998 \n", "L 368.929663 55.143389 \n", "L 370.771063 100.574613 \n", "L 371.384863 110.098997 \n", "L 371.691763 111.963628 \n", "L 371.845213 111.605134 \n", "L 371.998663 110.093245 \n", "L 372.305563 103.282517 \n", "L 373.379713 72.193264 \n", "L 373.686613 69.147459 \n", "L 373.840063 68.6513 \n", "L 373.993513 68.684009 \n", "L 374.300413 69.708915 \n", "L 374.607313 70.628187 \n", "L 374.760763 70.419968 \n", "L 374.914213 69.4653 \n", "L 375.221113 65.180293 \n", "L 375.681463 58.147546 \n", "L 375.834913 57.616285 \n", "L 375.988363 58.165395 \n", "L 376.295263 61.329678 \n", "L 376.909063 68.080011 \n", "L 377.369413 70.15499 \n", "L 377.676313 71.985526 \n", "L 377.983213 76.451865 \n", "L 378.903913 94.127558 \n", "L 379.210813 95.613926 \n", "L 379.364263 95.382535 \n", "L 379.671163 93.626773 \n", "L 380.284963 88.032626 \n", "L 380.591863 83.430351 \n", "L 381.819463 58.910284 \n", "L 382.740163 49.773679 \n", "L 383.200513 46.307997 \n", "L 383.353963 45.765655 \n", "L 383.507413 45.967304 \n", "L 383.660863 47.237284 \n", "L 383.967763 53.214645 \n", "L 384.888463 81.886875 \n", "L 385.655713 101.775431 \n", "L 386.269513 112.150007 \n", "L 386.576413 114.970349 \n", "L 386.729863 115.469841 \n", "L 386.883313 115.109277 \n", "L 387.036763 113.638655 \n", "L 387.343663 106.70768 \n", "L 388.264363 78.202702 \n", "L 388.571263 74.655582 \n", "L 388.724713 74.028031 \n", "L 388.878163 74.005852 \n", "L 389.185063 75.230491 \n", "L 389.645413 77.588583 \n", "L 389.798863 77.576644 \n", "L 389.952313 76.740245 \n", "L 390.259213 72.228072 \n", "L 390.873013 61.145374 \n", "L 391.179913 59.908966 \n", "L 391.486813 60.172132 \n", "L 391.640263 60.269419 \n", "L 391.793713 60.147192 \n", "L 392.100613 59.107617 \n", "L 392.560963 56.85768 \n", "L 392.714413 56.672547 \n", "L 392.867863 57.250001 \n", "L 393.174763 61.613427 \n", "L 394.402363 89.127588 \n", "L 395.323063 101.596187 \n", "L 395.629963 104.479249 \n", "L 395.783413 105.099991 \n", "L 395.936863 104.828584 \n", "L 396.090313 103.41432 \n", "L 396.397213 96.948387 \n", "L 397.471363 67.389866 \n", "L 397.931713 61.926048 \n", "L 398.238613 60.698868 \n", "L 398.698963 60.147197 \n", "L 398.852413 59.502975 \n", "L 399.159313 56.622076 \n", "L 399.619663 51.999157 \n", "L 399.773113 52.25951 \n", "L 400.080013 56.384049 \n", "L 402.228313 101.591635 \n", "L 402.688663 107.635492 \n", "L 402.842113 108.409504 \n", "L 402.995563 108.281545 \n", "L 403.149013 107.080754 \n", "L 403.455913 101.217598 \n", "L 404.683513 67.71808 \n", "L 404.990413 64.79782 \n", "L 405.143863 64.276367 \n", "L 405.297313 64.205493 \n", "L 405.757663 64.786327 \n", "L 405.911113 64.49188 \n", "L 406.064563 63.607496 \n", "L 406.371463 59.939813 \n", "L 406.831813 54.534353 \n", "L 406.985263 54.694403 \n", "L 407.292163 58.463119 \n", "L 408.366313 78.340939 \n", "L 408.980113 86.712682 \n", "L 409.900813 102.374512 \n", "L 410.054263 103.270431 \n", "L 410.207713 103.246641 \n", "L 410.361163 102.228319 \n", "L 410.668063 97.379724 \n", "L 411.435313 77.384195 \n", "L 412.049113 63.729581 \n", "L 412.356013 60.423212 \n", "L 412.662913 59.119546 \n", "L 412.969813 58.385126 \n", "L 413.276713 56.713444 \n", "L 414.043963 50.021363 \n", "L 414.197413 50.640277 \n", "L 414.504313 55.552144 \n", "L 416.345713 99.549345 \n", "L 416.959513 109.213121 \n", "L 417.266413 111.129624 \n", "L 417.419863 110.779944 \n", "L 417.573313 109.279757 \n", "L 417.880213 102.566407 \n", "L 418.954363 71.479626 \n", "L 419.261263 68.170028 \n", "L 419.414713 67.552968 \n", "L 419.568163 67.478099 \n", "L 419.875063 68.325874 \n", "L 420.181963 69.113173 \n", "L 420.335413 68.862984 \n", "L 420.488863 67.893607 \n", "L 420.795763 63.70242 \n", "L 421.256113 57.180588 \n", "L 421.409563 56.907592 \n", "L 421.563013 57.753444 \n", "L 422.023363 63.940381 \n", "L 422.483713 69.865775 \n", "L 422.944063 73.039266 \n", "L 423.250963 75.487228 \n", "L 423.557863 80.271411 \n", "L 424.325113 95.234944 \n", "L 424.632013 97.433035 \n", "L 424.785463 97.346957 \n", "L 425.092363 95.172788 \n", "L 425.706163 86.759645 \n", "L 426.166513 77.990708 \n", "L 426.933763 61.788369 \n", "L 427.394113 56.986579 \n", "L 428.621713 46.817461 \n", "L 428.775163 46.611145 \n", "L 428.928613 47.294882 \n", "L 429.235513 52.068048 \n", "L 429.849313 70.236221 \n", "L 430.770013 96.096912 \n", "L 431.383813 108.302887 \n", "L 431.844163 113.859802 \n", "L 431.997613 114.675886 \n", "L 432.151063 114.712002 \n", "L 432.304513 113.731188 \n", "L 432.611413 107.929197 \n", "L 433.685563 76.080204 \n", "L 433.992463 73.262414 \n", "L 434.145913 72.909006 \n", "L 434.299363 73.094793 \n", "L 434.606263 74.502114 \n", "L 434.913163 76.045817 \n", "L 435.066613 76.267928 \n", "L 435.220063 75.772977 \n", "L 435.526963 72.043036 \n", "L 436.140763 60.938038 \n", "L 436.294213 59.922832 \n", "L 436.447663 59.703182 \n", "L 436.754563 60.510915 \n", "L 437.061463 61.329713 \n", "L 437.214913 61.363935 \n", "L 437.368363 61.098081 \n", "L 437.982163 59.270739 \n", "L 438.135613 59.739467 \n", "L 438.289063 61.160154 \n", "L 438.595963 67.071321 \n", "L 439.363213 85.145685 \n", "L 439.823563 91.515279 \n", "L 440.744263 100.683964 \n", "L 441.051163 101.97619 \n", "L 441.204613 101.411141 \n", "L 441.511513 96.716587 \n", "L 442.892563 62.44375 \n", "L 443.352913 58.246605 \n", "L 443.813263 56.642249 \n", "L 444.120163 55.087825 \n", "L 444.733963 49.748664 \n", "L 444.887413 50.026486 \n", "L 445.040863 51.565229 \n", "L 445.347763 57.834799 \n", "L 446.882263 96.791149 \n", "L 447.649513 109.437756 \n", "L 447.956413 111.71483 \n", "L 448.109863 111.690921 \n", "L 448.263313 110.607705 \n", "L 448.570213 104.664258 \n", "L 449.644363 73.005909 \n", "L 449.951263 69.459892 \n", "L 450.104713 68.745458 \n", "L 450.258163 68.597571 \n", "L 450.411613 68.876644 \n", "L 450.871963 70.431753 \n", "L 451.025413 70.414581 \n", "L 451.178863 69.721712 \n", "L 451.485763 65.916814 \n", "L 451.946113 58.479675 \n", "L 452.099563 57.581874 \n", "L 452.253013 57.80693 \n", "L 452.559913 60.628711 \n", "L 453.173713 67.695225 \n", "L 453.480613 69.510816 \n", "L 453.787513 70.751608 \n", "L 454.094413 73.378922 \n", "L 454.401313 78.868682 \n", "L 455.168563 93.853202 \n", "L 455.475463 95.797721 \n", "L 455.628913 95.750557 \n", "L 455.935813 94.191581 \n", "L 456.549613 88.571068 \n", "L 456.856513 84.1741 \n", "L 458.237563 57.720047 \n", "L 459.311713 47.682063 \n", "L 459.618613 45.944891 \n", "L 459.772063 45.914054 \n", "L 459.925513 46.883514 \n", "L 460.232413 52.308817 \n", "L 460.846213 70.965138 \n", "L 461.766913 97.382996 \n", "L 462.380713 109.387521 \n", "L 462.841063 114.650026 \n", "L 462.994513 115.331596 \n", "L 463.147963 115.208372 \n", "L 463.301413 114.038443 \n", "L 463.608313 107.781686 \n", "L 464.682463 76.382549 \n", "L 464.989363 74.001152 \n", "L 465.142813 73.827682 \n", "L 465.296263 74.162815 \n", "L 466.063513 77.4703 \n", "L 466.216963 76.856725 \n", "L 466.523863 72.803536 \n", "L 467.137663 61.410452 \n", "L 467.444563 59.908245 \n", "L 467.598013 59.96025 \n", "L 467.904913 60.362663 \n", "L 468.058363 60.344615 \n", "L 468.211813 60.056467 \n", "L 468.518713 58.754625 \n", "L 468.825613 57.277078 \n", "L 468.979063 56.979479 \n", "L 469.132513 57.365939 \n", "L 469.285963 58.742351 \n", "L 469.592863 64.679872 \n", "L 470.513563 86.262595 \n", "L 471.280813 97.135613 \n", "L 471.894613 103.944075 \n", "L 472.048063 104.679643 \n", "L 472.201513 104.575935 \n", "L 472.354963 103.367945 \n", "L 472.661863 97.284034 \n", "L 473.736013 67.601162 \n", "L 474.196363 61.805493 \n", "L 474.503263 60.372219 \n", "L 474.963613 59.721218 \n", "L 475.117063 59.128502 \n", "L 475.423963 56.408834 \n", "L 475.884313 51.750415 \n", "L 476.037763 51.910863 \n", "L 476.191213 53.340053 \n", "L 476.498113 59.246083 \n", "L 477.725713 88.039863 \n", "L 478.953313 108.016508 \n", "L 479.106763 108.853867 \n", "L 479.260213 108.809792 \n", "L 479.413663 107.702874 \n", "L 479.720563 101.971006 \n", "L 480.948163 68.246302 \n", "L 481.255063 65.346727 \n", "L 481.408513 64.843382 \n", "L 481.561963 64.798815 \n", "L 482.022313 65.535946 \n", "L 482.175763 65.317052 \n", "L 482.329213 64.504674 \n", "L 482.636113 60.888178 \n", "L 483.096463 55.084835 \n", "L 483.249913 55.023924 \n", "L 483.403363 56.198342 \n", "L 483.710263 61.168906 \n", "L 484.477513 75.203317 \n", "L 485.398213 87.686661 \n", "L 486.165463 101.05052 \n", "L 486.318913 102.131212 \n", "L 486.472363 102.321668 \n", "L 486.625813 101.553687 \n", "L 486.932713 97.30202 \n", "L 487.546513 82.623953 \n", "L 488.313763 64.408116 \n", "L 488.620663 60.478437 \n", "L 488.927563 58.677914 \n", "L 489.387913 56.991031 \n", "L 489.694813 54.662475 \n", "L 490.308613 49.332563 \n", "L 490.462063 49.646219 \n", "L 490.615513 51.183216 \n", "L 490.922413 57.610797 \n", "L 492.303463 93.49097 \n", "L 493.070713 107.59906 \n", "L 493.377613 111.092324 \n", "L 493.531063 111.934489 \n", "L 493.684513 111.924306 \n", "L 493.837963 110.834863 \n", "L 494.144863 104.87221 \n", "L 495.372463 70.946024 \n", "L 495.679363 68.85904 \n", "L 495.832813 68.733473 \n", "L 495.986263 69.041427 \n", "L 496.446613 70.702957 \n", "L 496.600063 70.712322 \n", "L 496.753513 70.037869 \n", "L 497.060413 66.254432 \n", "L 497.520763 58.707249 \n", "L 497.674213 57.743825 \n", "L 497.827663 57.913339 \n", "L 498.134563 60.648112 \n", "L 498.748363 67.518352 \n", "L 499.055263 69.192292 \n", "L 499.362163 70.273083 \n", "L 499.669063 72.748243 \n", "L 499.975963 78.1503 \n", "L 500.743213 93.309853 \n", "L 501.050113 95.372914 \n", "L 501.203563 95.396279 \n", "L 501.510463 94.017815 \n", "L 502.124263 88.970194 \n", "L 502.431163 84.932464 \n", "L 502.891513 74.444182 \n", "L 503.505313 61.865917 \n", "L 504.119113 54.81874 \n", "L 505.039813 46.702415 \n", "L 505.346713 45.688482 \n", "L 505.500163 46.391093 \n", "L 505.807063 51.283305 \n", "L 506.420863 69.671393 \n", "L 507.341563 96.60571 \n", "L 507.955363 108.921045 \n", "L 508.415713 114.502745 \n", "L 508.569163 115.355026 \n", "L 508.722613 115.460091 \n", "L 508.876063 114.585328 \n", "L 509.182963 109.009553 \n", "L 510.257113 77.061302 \n", "L 510.564013 74.325859 \n", "L 510.717463 74.018589 \n", "L 510.870913 74.252209 \n", "L 511.177813 75.763467 \n", "L 511.484713 77.448193 \n", "L 511.638163 77.765825 \n", "L 511.791613 77.376579 \n", "L 511.945063 76.072162 \n", "L 512.251963 70.742455 \n", "L 512.712313 62.008921 \n", "L 513.019213 60.01496 \n", "L 513.172663 59.916461 \n", "L 513.479563 60.194298 \n", "L 513.633013 60.172914 \n", "L 513.786463 59.897756 \n", "L 514.093363 58.600479 \n", "L 514.553713 56.52802 \n", "L 514.707163 56.661018 \n", "L 514.860613 57.710226 \n", "L 515.167513 63.082176 \n", "L 516.241663 87.802432 \n", "L 517.162363 100.743805 \n", "L 517.622713 105.042938 \n", "L 517.776163 105.299577 \n", "L 517.929613 104.541062 \n", "L 518.236513 99.357647 \n", "L 519.617563 64.268267 \n", "L 519.924463 61.683165 \n", "L 520.231363 60.811846 \n", "L 520.538263 60.579585 \n", "L 520.691713 60.186257 \n", "L 520.998613 57.897694 \n", "L 521.458963 52.580534 \n", "L 521.612413 52.098114 \n", "L 521.765863 52.873708 \n", "L 522.072763 57.798726 \n", "L 523.607263 91.241774 \n", "L 524.527963 106.554307 \n", "L 524.834863 108.21757 \n", "L 524.988313 107.674747 \n", "L 525.295213 103.185591 \n", "L 525.755563 89.763413 \n", "L 526.369363 72.016464 \n", "L 526.676263 66.708944 \n", "L 526.983163 64.257482 \n", "L 527.136613 63.9036 \n", "L 527.290063 63.935935 \n", "L 527.596963 64.372917 \n", "L 527.750413 64.322558 \n", "L 527.903863 63.793779 \n", "L 528.210763 60.842697 \n", "L 528.671113 54.749745 \n", "L 528.824563 54.201778 \n", "L 528.978013 54.939205 \n", "L 529.284913 59.577652 \n", "L 530.205613 77.605449 \n", "L 531.126313 91.544694 \n", "L 531.740113 102.100873 \n", "L 532.047013 103.912339 \n", "L 532.200463 103.419366 \n", "L 532.507363 99.430817 \n", "L 532.967713 88.14244 \n", "L 533.888413 65.030948 \n", "L 534.195313 61.213306 \n", "L 534.502213 59.665352 \n", "L 534.962563 58.648542 \n", "L 535.269463 56.666924 \n", "L 535.883263 50.585884 \n", "L 536.036713 50.552221 \n", "L 536.190163 51.774203 \n", "L 536.497063 57.679392 \n", "L 537.878113 91.55798 \n", "L 538.798813 107.762519 \n", "L 539.105713 110.394869 \n", "L 539.259163 110.541597 \n", "L 539.412613 109.633283 \n", "L 539.719513 104.145051 \n", "L 540.947113 69.870607 \n", "L 541.254013 67.136668 \n", "L 541.407463 66.738322 \n", "L 541.560913 66.817924 \n", "L 542.174713 68.089829 \n", "L 542.328163 67.472231 \n", "L 542.635063 63.994187 \n", "L 543.095413 57.080658 \n", "L 543.248863 56.363342 \n", "L 543.402313 56.859479 \n", "L 543.709213 60.546505 \n", "L 544.476463 71.778985 \n", "L 545.243713 78.688791 \n", "L 545.704063 87.513425 \n", "L 546.164413 96.086214 \n", "L 546.471313 98.648544 \n", "L 546.624763 98.684876 \n", "L 546.778213 97.908022 \n", "L 547.085113 94.383715 \n", "L 547.698913 83.498114 \n", "L 548.926513 59.41988 \n", "L 550.307563 48.062916 \n", "L 550.461013 47.334822 \n", "L 550.614463 47.32285 \n", "L 550.767913 48.30085 \n", "L 551.074813 53.687747 \n", "L 551.995513 81.205902 \n", "L 552.762763 100.088809 \n", "L 553.376563 110.674768 \n", "L 553.683463 113.662555 \n", "L 553.836913 114.237563 \n", "L 553.990363 113.93995 \n", "L 554.143813 112.52607 \n", "L 554.450713 105.785697 \n", "L 555.371413 77.132453 \n", "L 555.678313 73.105059 \n", "L 555.831763 72.257037 \n", "L 555.985213 72.038283 \n", "L 556.138663 72.319283 \n", "L 556.752463 75.064026 \n", "L 556.905913 75.026148 \n", "L 557.059363 74.200455 \n", "L 557.366263 69.857918 \n", "L 557.826613 61.279635 \n", "L 558.133513 59.401746 \n", "L 558.286963 59.670705 \n", "L 559.054213 62.622199 \n", "L 559.207663 62.498819 \n", "L 559.668013 61.502134 \n", "L 559.821463 61.722522 \n", "L 559.974913 62.719685 \n", "L 560.281813 67.713118 \n", "L 561.202513 87.997623 \n", "L 561.662863 92.7595 \n", "L 562.583563 98.86215 \n", "L 562.737013 99.090442 \n", "L 562.890463 98.513253 \n", "L 563.197363 93.972587 \n", "L 564.578413 60.926554 \n", "L 565.038763 56.251212 \n", "L 566.419813 48.032352 \n", "L 566.573263 49.096661 \n", "L 566.880163 54.708386 \n", "L 567.647413 77.832675 \n", "L 568.414663 97.288096 \n", "L 569.181913 110.530974 \n", "L 569.488813 113.336728 \n", "L 569.642263 113.773765 \n", "L 569.795713 113.299797 \n", "L 569.949163 111.667125 \n", "L 570.256063 104.426878 \n", "L 571.176763 76.219672 \n", "L 571.483663 72.403038 \n", "L 571.637113 71.612138 \n", "L 571.790563 71.42903 \n", "L 571.944013 71.724662 \n", "L 572.557813 74.225282 \n", "L 572.711263 74.031057 \n", "L 572.864713 73.019349 \n", "L 573.171613 68.37044 \n", "L 573.631963 60.31338 \n", "L 573.785413 59.248504 \n", "L 573.938863 59.126672 \n", "L 574.245763 60.624396 \n", "L 574.706113 63.251322 \n", "L 574.859563 63.588609 \n", "L 575.013013 63.614269 \n", "L 575.473363 63.204905 \n", "L 575.626813 63.729496 \n", "L 575.780263 65.085786 \n", "L 576.087163 70.66891 \n", "L 576.854413 87.795683 \n", "L 577.314763 92.626194 \n", "L 577.928563 95.627042 \n", "L 578.235463 96.823566 \n", "L 578.388913 97.027924 \n", "L 578.542363 96.621371 \n", "L 578.695813 95.289375 \n", "L 579.002713 89.280245 \n", "L 579.923413 65.557118 \n", "L 580.383763 58.597048 \n", "L 580.844113 54.285752 \n", "L 581.918263 47.021761 \n", "L 582.071713 47.104596 \n", "L 582.225163 48.378682 \n", "L 582.532063 54.340825 \n", "L 583.452763 82.76321 \n", "L 584.220013 101.518882 \n", "L 584.833813 111.58791 \n", "L 585.140713 114.285837 \n", "L 585.294163 114.677779 \n", "L 585.447613 114.162819 \n", "L 585.601063 112.484015 \n", "L 585.907963 105.091119 \n", "L 586.828663 76.985758 \n", "L 587.135563 73.496419 \n", "L 587.289013 72.862054 \n", "L 587.442463 72.819796 \n", "L 587.749363 73.963933 \n", "L 588.209713 76.031493 \n", "L 588.363163 75.856552 \n", "L 588.516613 74.834212 \n", "L 588.823513 70.053107 \n", "L 589.283863 61.453835 \n", "L 589.590763 59.616023 \n", "L 589.744213 59.773743 \n", "L 590.358013 61.526955 \n", "L 590.511463 61.434059 \n", "L 590.818363 60.520934 \n", "L 591.125263 59.655021 \n", "L 591.278713 59.86367 \n", "L 591.432163 60.919667 \n", "L 591.739063 66.151438 \n", "L 592.659763 87.074642 \n", "L 593.273563 94.233627 \n", "L 594.040813 101.005317 \n", "L 594.194263 101.526538 \n", "L 594.347713 101.242284 \n", "L 594.501163 99.865826 \n", "L 594.808063 93.520682 \n", "L 595.882213 65.219755 \n", "L 596.342563 59.332696 \n", "L 596.649463 57.395088 \n", "L 597.263263 54.900926 \n", "L 597.877063 49.554402 \n", "L 598.030513 49.611221 \n", "L 598.183963 50.918402 \n", "L 598.490863 56.859733 \n", "L 600.178813 99.482041 \n", "L 600.792613 109.4177 \n", "L 601.099513 111.940678 \n", "L 601.252963 112.108301 \n", "L 601.406413 111.267013 \n", "L 601.713313 105.841186 \n", "L 602.940913 71.579124 \n", "L 603.247813 69.279578 \n", "L 603.401263 69.06609 \n", "L 603.554713 69.306405 \n", "L 604.168513 71.0957 \n", "L 604.321963 70.576045 \n", "L 604.628863 67.083971 \n", "L 605.242663 58.024722 \n", "L 605.396113 57.905315 \n", "L 605.549563 58.730585 \n", "L 606.623713 68.395008 \n", "L 607.084063 70.108579 \n", "L 607.390963 73.629221 \n", "L 608.465113 93.944218 \n", "L 608.772013 95.195015 \n", "L 608.925463 94.968976 \n", "L 609.232363 93.511099 \n", "L 609.692713 90.3274 \n", "L 609.999613 86.943416 \n", "L 610.306513 80.88259 \n", "L 611.073763 63.389016 \n", "L 611.687563 55.728028 \n", "L 612.608263 47.355677 \n", "L 612.915163 45.67592 \n", "L 613.068613 45.733907 \n", "L 613.222063 46.851211 \n", "L 613.528963 52.56333 \n", "L 614.296213 76.412797 \n", "L 615.063463 97.957075 \n", "L 615.677263 109.81702 \n", "L 616.137613 114.925844 \n", "L 616.291063 115.534917 \n", "L 616.444513 115.321993 \n", "L 616.597963 114.040033 \n", "L 616.904863 107.509317 \n", "L 617.825563 78.709554 \n", "L 618.132463 74.931638 \n", "L 618.285913 74.213111 \n", "L 618.439363 74.118696 \n", "L 618.592813 74.51703 \n", "L 619.360063 77.819356 \n", "L 619.513513 77.125958 \n", "L 619.820413 72.882829 \n", "L 620.434213 61.471617 \n", "L 620.741113 59.949984 \n", "L 620.894563 59.942015 \n", "L 621.201463 60.15615 \n", "L 621.354913 60.029397 \n", "L 621.661813 58.991197 \n", "L 622.275613 56.317844 \n", "L 622.429063 56.722929 \n", "L 622.582513 58.140061 \n", "L 622.889413 64.171034 \n", "L 623.810113 86.009299 \n", "L 624.730813 99.54006 \n", "L 625.191163 104.608008 \n", "L 625.344613 105.410601 \n", "L 625.498063 105.38206 \n", "L 625.651513 104.261337 \n", "L 625.958413 98.342903 \n", "L 627.186013 65.801425 \n", "L 627.492913 62.508656 \n", "L 627.799813 61.174627 \n", "L 628.260163 60.761047 \n", "L 628.413613 60.25582 \n", "L 628.720513 57.639625 \n", "L 629.180863 52.493639 \n", "L 629.334313 52.32822 \n", "L 629.487763 53.434751 \n", "L 629.794663 58.815274 \n", "L 631.022263 86.391897 \n", "L 632.249863 106.673001 \n", "L 632.403313 107.713965 \n", "L 632.556763 107.906928 \n", "L 632.710213 107.081264 \n", "L 633.017113 102.021472 \n", "L 633.630913 83.388263 \n", "L 634.244713 68.158981 \n", "L 634.551613 64.618257 \n", "L 634.858513 63.570884 \n", "L 635.011963 63.643001 \n", "L 635.318863 63.996347 \n", "L 635.472313 63.81878 \n", "L 635.625763 63.12246 \n", "L 635.932663 59.86966 \n", "L 636.393013 54.198804 \n", "L 636.546463 54.039642 \n", "L 636.699913 55.165673 \n", "L 637.006813 60.365574 \n", "L 637.927513 78.674391 \n", "L 638.694763 90.175496 \n", "L 639.462013 103.156023 \n", "L 639.615463 104.226394 \n", "L 639.768913 104.399914 \n", "L 639.922363 103.56539 \n", "L 640.229263 98.873916 \n", "L 640.843063 82.441779 \n", "L 641.456863 66.916856 \n", "L 641.763763 62.355 \n", "L 642.070663 60.320289 \n", "L 642.377563 59.748057 \n", "L 642.531013 59.509971 \n", "L 642.684463 59.026561 \n", "L 642.991363 56.799608 \n", "L 643.605163 50.872329 \n", "L 643.758613 51.212642 \n", "L 644.065513 55.639107 \n", "L 646.060363 100.61738 \n", "L 646.520713 107.762155 \n", "L 646.827613 109.949518 \n", "L 646.981063 109.75725 \n", "L 647.134513 108.445089 \n", "L 647.441413 102.196742 \n", "L 648.669013 68.65901 \n", "L 648.975913 66.229179 \n", "L 649.129363 65.934406 \n", "L 649.129363 65.934406 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"line2d_2\">\n", "    <path clip-path=\"url(#pcf74c83c01)\" d=\"M 342.382813 102.232068 \n", "L 342.536263 104.475303 \n", "L 342.689713 106.210124 \n", "L 342.843163 107.223659 \n", "L 342.996613 107.416715 \n", "L 343.150063 106.575891 \n", "L 343.456963 101.467744 \n", "L 344.838013 65.746051 \n", "L 345.144913 63.365735 \n", "L 345.298363 63.026299 \n", "L 345.451813 63.077471 \n", "L 345.758713 63.383158 \n", "L 345.912163 63.174404 \n", "L 346.065613 62.454176 \n", "L 346.372513 59.150786 \n", "L 346.832863 53.609652 \n", "L 346.986313 53.547096 \n", "L 347.139763 54.704429 \n", "L 347.446663 59.994934 \n", "L 348.367363 79.014647 \n", "L 349.288063 94.215203 \n", "L 349.901863 104.171722 \n", "L 350.055313 105.239256 \n", "L 350.208763 105.426548 \n", "L 350.362213 104.61156 \n", "L 350.669113 99.851192 \n", "L 351.282913 82.896751 \n", "L 351.896713 67.317121 \n", "L 352.203613 62.934068 \n", "L 352.510513 61.09963 \n", "L 352.817413 60.731605 \n", "L 352.970863 60.610851 \n", "L 353.124313 60.302128 \n", "L 353.431213 58.318918 \n", "L 354.045013 51.90966 \n", "L 354.198463 52.086447 \n", "L 354.351913 53.534901 \n", "L 354.658813 59.588113 \n", "L 355.732963 85.010424 \n", "L 357.114013 107.559606 \n", "L 357.267463 108.369434 \n", "L 357.420913 108.270903 \n", "L 357.574363 107.104172 \n", "L 357.881263 101.275269 \n", "L 359.108863 67.707621 \n", "L 359.415763 64.730827 \n", "L 359.569213 64.159324 \n", "L 359.722663 64.060012 \n", "L 360.183013 64.659318 \n", "L 360.336463 64.383371 \n", "L 360.489913 63.500356 \n", "L 360.796813 59.777959 \n", "L 361.257163 54.352985 \n", "L 361.410613 54.533346 \n", "L 361.717513 58.360356 \n", "L 362.791663 78.242691 \n", "L 363.405463 86.885356 \n", "L 364.326163 102.785438 \n", "L 364.479613 103.679339 \n", "L 364.633063 103.634503 \n", "L 364.786513 102.625785 \n", "L 365.093413 97.748101 \n", "L 365.860663 77.582613 \n", "L 366.474463 63.758857 \n", "L 366.781363 60.368553 \n", "L 367.088263 59.193548 \n", "L 367.395163 58.649792 \n", "L 367.702063 57.161101 \n", "L 368.469313 50.430689 \n", "L 368.622763 51.051615 \n", "L 368.929663 55.91358 \n", "L 370.771063 99.294892 \n", "L 371.384863 108.910975 \n", "L 371.691763 110.802126 \n", "L 371.845213 110.394371 \n", "L 371.998663 108.846624 \n", "L 372.305563 102.063786 \n", "L 373.379713 71.38826 \n", "L 373.686613 67.951617 \n", "L 373.840063 67.290868 \n", "L 373.993513 67.142539 \n", "L 374.300413 67.843716 \n", "L 374.607313 68.445496 \n", "L 374.760763 68.139417 \n", "L 374.914213 67.122805 \n", "L 375.221113 62.965669 \n", "L 375.681463 56.664461 \n", "L 375.834913 56.542653 \n", "L 375.988363 57.52511 \n", "L 376.448713 64.174857 \n", "L 377.062513 72.141285 \n", "L 377.676313 77.139041 \n", "L 377.983213 82.063938 \n", "L 378.750463 96.740269 \n", "L 379.057363 98.662425 \n", "L 379.210813 98.324983 \n", "L 379.517713 95.50506 \n", "L 380.131513 85.349138 \n", "L 381.512563 59.219969 \n", "L 383.047063 47.208275 \n", "L 383.200513 47.616328 \n", "L 383.353963 49.03375 \n", "L 383.660863 55.031997 \n", "L 385.655713 106.76803 \n", "L 386.116063 112.953162 \n", "L 386.422963 114.381082 \n", "L 386.576413 113.755102 \n", "L 386.883313 108.868132 \n", "L 387.343663 93.833891 \n", "L 387.804013 79.404822 \n", "L 388.110913 74.233919 \n", "L 388.417813 72.199631 \n", "L 388.571263 72.110395 \n", "L 388.724713 72.434046 \n", "L 389.338513 75.076857 \n", "L 389.491963 74.834274 \n", "L 389.645413 73.769926 \n", "L 389.952313 69.04057 \n", "L 390.412663 60.791684 \n", "L 390.566113 59.626872 \n", "L 390.719563 59.375626 \n", "L 391.026463 60.454233 \n", "L 391.333363 61.722654 \n", "L 391.640263 62.11079 \n", "L 391.947163 61.567405 \n", "L 392.254063 61.060317 \n", "L 392.407513 61.466079 \n", "L 392.560963 62.754148 \n", "L 392.867863 68.40149 \n", "L 393.788563 89.272165 \n", "L 394.248913 94.091606 \n", "L 395.169613 100.498215 \n", "L 395.323063 100.5361 \n", "L 395.476513 99.661458 \n", "L 395.783413 94.273136 \n", "L 397.011013 63.029036 \n", "L 397.471363 58.092323 \n", "L 397.931713 56.057247 \n", "L 398.238613 54.720207 \n", "L 398.698963 50.419906 \n", "L 398.852413 49.182334 \n", "L 399.005863 48.901596 \n", "L 399.159313 49.881401 \n", "L 399.466213 55.490654 \n", "L 401.307613 101.789567 \n", "L 401.921413 110.924788 \n", "L 402.228313 112.673538 \n", "L 402.381763 112.230651 \n", "L 402.535213 110.612274 \n", "L 402.842113 103.368593 \n", "L 403.762813 75.456499 \n", "L 404.069713 71.120638 \n", "L 404.376613 69.735812 \n", "L 404.530063 69.865227 \n", "L 404.836963 70.831988 \n", "L 405.143863 71.654273 \n", "L 405.297313 71.406513 \n", "L 405.450763 70.415177 \n", "L 405.757663 66.064952 \n", "L 406.218013 58.938901 \n", "L 406.371463 58.282607 \n", "L 406.524913 58.651803 \n", "L 406.831813 61.26164 \n", "L 407.445613 66.717621 \n", "L 407.752513 67.531778 \n", "L 407.905963 67.735175 \n", "L 408.059413 68.168488 \n", "L 408.212863 69.135608 \n", "L 408.519763 73.391387 \n", "L 409.440463 91.669209 \n", "L 409.747363 93.770072 \n", "L 409.900813 94.014924 \n", "L 410.054263 93.885835 \n", "L 410.361163 92.903392 \n", "L 410.821513 90.565366 \n", "L 411.128413 87.303743 \n", "L 411.588763 77.329573 \n", "L 412.202563 63.629924 \n", "L 412.662913 57.331494 \n", "L 413.430163 50.136814 \n", "L 414.043963 45.867772 \n", "L 414.197413 45.829952 \n", "L 414.350863 46.791629 \n", "L 414.657763 52.187722 \n", "L 415.271563 70.864347 \n", "L 416.192263 97.553631 \n", "L 416.806063 109.400132 \n", "L 417.266413 114.701397 \n", "L 417.419863 115.416753 \n", "L 417.573313 115.335774 \n", "L 417.726763 114.215581 \n", "L 418.033663 108.069528 \n", "L 419.107813 76.660661 \n", "L 419.414713 74.179712 \n", "L 419.568163 73.927845 \n", "L 419.721613 74.205965 \n", "L 420.181963 76.551271 \n", "L 420.488863 77.419126 \n", "L 420.642313 76.857936 \n", "L 420.949213 72.964215 \n", "L 421.563013 61.616612 \n", "L 421.869913 60.035088 \n", "L 422.023363 60.025465 \n", "L 422.330263 60.453594 \n", "L 422.483713 60.466414 \n", "L 422.637163 60.273422 \n", "L 422.944063 59.067276 \n", "L 423.404413 57.305872 \n", "L 423.557863 57.646415 \n", "L 423.711313 58.918124 \n", "L 424.018213 64.511309 \n", "L 424.938913 86.050414 \n", "L 425.706163 96.594864 \n", "L 426.319963 103.393001 \n", "L 426.473413 104.165256 \n", "L 426.626863 104.175026 \n", "L 426.780313 103.0647 \n", "L 427.087213 97.219482 \n", "L 428.314813 65.116027 \n", "L 428.775163 60.44122 \n", "L 429.082063 59.429941 \n", "L 429.388963 58.877966 \n", "L 429.542413 58.272472 \n", "L 429.849313 55.612433 \n", "L 430.309663 51.072387 \n", "L 430.463113 51.279522 \n", "L 430.616563 52.777223 \n", "L 430.923463 58.839594 \n", "L 432.151063 88.679007 \n", "L 433.225213 107.452023 \n", "L 433.532113 109.790618 \n", "L 433.685563 109.755483 \n", "L 433.839013 108.632804 \n", "L 434.145913 102.706326 \n", "L 435.373513 68.74839 \n", "L 435.680413 66.178878 \n", "L 435.833863 65.827261 \n", "L 435.987313 65.963146 \n", "L 436.447663 67.205764 \n", "L 436.601113 67.137369 \n", "L 436.754563 66.422317 \n", "L 437.061463 62.811556 \n", "L 437.521813 56.491637 \n", "L 437.675263 56.147819 \n", "L 437.828713 57.027225 \n", "L 438.135613 61.343122 \n", "L 438.902863 73.247332 \n", "L 439.670113 81.14943 \n", "L 440.744263 99.262293 \n", "L 440.897713 99.738509 \n", "L 441.051163 99.29855 \n", "L 441.358063 96.056178 \n", "L 441.971863 84.387988 \n", "L 443.199463 59.735793 \n", "L 443.659813 56.596881 \n", "L 444.120163 53.305834 \n", "L 444.733963 48.369024 \n", "L 444.887413 48.214901 \n", "L 445.040863 49.042959 \n", "L 445.347763 53.963577 \n", "L 446.115013 76.16309 \n", "L 446.882263 95.282648 \n", "L 447.649513 109.342001 \n", "L 447.956413 112.51767 \n", "L 448.109863 113.199448 \n", "L 448.263313 112.977349 \n", "L 448.416763 111.6546 \n", "L 448.723663 105.193263 \n", "L 449.797813 73.804023 \n", "L 450.104713 70.969369 \n", "L 450.258163 70.526099 \n", "L 450.411613 70.588209 \n", "L 450.718513 71.797396 \n", "L 451.025413 72.985305 \n", "L 451.178863 72.940193 \n", "L 451.332313 72.159693 \n", "L 451.639213 68.088713 \n", "L 452.099563 60.13193 \n", "L 452.253013 59.00136 \n", "L 452.406463 58.909101 \n", "L 452.713363 60.789153 \n", "L 453.327163 65.270271 \n", "L 453.634063 65.831673 \n", "L 453.787513 65.81361 \n", "L 453.940963 65.913363 \n", "L 454.094413 66.401203 \n", "L 454.247863 67.632361 \n", "L 454.554763 72.830362 \n", "L 455.322013 89.287166 \n", "L 455.628913 92.389747 \n", "L 455.935813 93.509698 \n", "L 456.242713 93.622369 \n", "L 456.703063 93.61171 \n", "L 456.856513 93.225506 \n", "L 457.009963 92.244633 \n", "L 457.316863 87.526755 \n", "L 458.544463 60.037797 \n", "L 459.158263 53.123723 \n", "L 459.925513 47.225294 \n", "L 460.232413 45.909912 \n", "L 460.385863 46.381163 \n", "L 460.539313 47.994535 \n", "L 460.846213 54.52057 \n", "L 462.841063 108.592735 \n", "L 463.301413 114.254442 \n", "L 463.454863 115.121483 \n", "L 463.608313 115.287831 \n", "L 463.761763 114.588632 \n", "L 464.068663 109.548975 \n", "L 465.296263 75.636992 \n", "L 465.603163 74.111574 \n", "L 465.756613 74.22908 \n", "L 466.063513 75.649539 \n", "L 466.370413 77.237685 \n", "L 466.523863 77.53528 \n", "L 466.677313 77.133425 \n", "L 466.830763 75.867744 \n", "L 467.137663 70.64368 \n", "L 467.598013 61.925195 \n", "L 467.904913 59.774771 \n", "L 468.058363 59.622895 \n", "L 468.518713 59.798698 \n", "L 468.672163 59.590601 \n", "L 468.979063 58.502112 \n", "L 469.439413 56.692124 \n", "L 469.592863 56.878798 \n", "L 469.746313 57.953727 \n", "L 470.053213 63.202404 \n", "L 471.127363 88.124148 \n", "L 472.048063 101.005707 \n", "L 472.508413 105.210014 \n", "L 472.661863 105.431306 \n", "L 472.815313 104.683766 \n", "L 473.122213 99.582964 \n", "L 474.503263 64.562674 \n", "L 474.810163 61.993024 \n", "L 475.117063 61.204242 \n", "L 475.423963 60.937765 \n", "L 475.577413 60.474404 \n", "L 475.884313 58.114359 \n", "L 476.344663 52.796104 \n", "L 476.498113 52.290607 \n", "L 476.651563 53.001506 \n", "L 476.958463 57.763883 \n", "L 478.646413 93.921717 \n", "L 479.413663 106.427181 \n", "L 479.720563 108.102381 \n", "L 479.874013 107.593276 \n", "L 480.180913 103.152291 \n", "L 480.641263 89.748402 \n", "L 481.255063 71.956151 \n", "L 481.561963 66.581833 \n", "L 481.868863 64.167823 \n", "L 482.022313 63.814995 \n", "L 482.175763 63.841381 \n", "L 482.482663 64.305164 \n", "L 482.636113 64.288792 \n", "L 482.789563 63.850029 \n", "L 483.096463 61.049048 \n", "L 483.556813 54.954914 \n", "L 483.710263 54.33028 \n", "L 483.863713 54.983796 \n", "L 484.170613 59.560522 \n", "L 485.091313 77.435954 \n", "L 486.012013 91.106555 \n", "L 486.625813 101.654387 \n", "L 486.932713 103.466242 \n", "L 487.086163 102.979353 \n", "L 487.393063 99.058298 \n", "L 487.853413 87.91388 \n", "L 488.774113 65.062847 \n", "L 489.081013 61.116239 \n", "L 489.387913 59.429524 \n", "L 489.848263 57.999381 \n", "L 490.155163 55.88786 \n", "L 490.768963 49.837148 \n", "L 490.922413 50.002837 \n", "L 491.075863 51.428197 \n", "L 491.382763 57.583065 \n", "L 492.763813 92.586486 \n", "L 493.684513 108.619642 \n", "L 493.991413 110.989922 \n", "L 494.144863 111.003995 \n", "L 494.298313 109.979293 \n", "L 494.605213 104.368457 \n", "L 495.832813 70.36793 \n", "L 496.139713 68.064254 \n", "L 496.293163 67.803751 \n", "L 496.446613 67.997577 \n", "L 497.060413 69.581189 \n", "L 497.213863 68.990656 \n", "L 497.520763 65.460363 \n", "L 497.981113 58.098986 \n", "L 498.134563 57.161584 \n", "L 498.288013 57.406075 \n", "L 498.594913 60.498243 \n", "L 499.362163 70.091521 \n", "L 500.129413 75.444937 \n", "L 500.589763 84.3035 \n", "L 501.050113 93.493121 \n", "L 501.357013 96.497566 \n", "L 501.510463 96.838693 \n", "L 501.663913 96.476784 \n", "L 501.970813 94.058334 \n", "L 502.584613 86.09142 \n", "L 503.044963 77.377396 \n", "L 503.812213 61.270854 \n", "L 504.426013 54.964785 \n", "L 505.500163 46.281094 \n", "L 505.653613 46.1401 \n", "L 505.807063 46.942001 \n", "L 506.113963 52.0734 \n", "L 506.881213 75.593199 \n", "L 507.648463 96.650385 \n", "L 508.262263 108.755442 \n", "L 508.722613 114.259861 \n", "L 508.876063 115.064148 \n", "L 509.029513 115.032794 \n", "L 509.182963 113.893852 \n", "L 509.489863 107.766941 \n", "L 510.564013 75.965471 \n", "L 510.870913 73.450274 \n", "L 511.024363 73.257352 \n", "L 511.177813 73.55175 \n", "L 511.945063 76.902111 \n", "L 512.098513 76.399883 \n", "L 512.405413 72.654853 \n", "L 513.019213 61.524503 \n", "L 513.326113 60.173184 \n", "L 513.479563 60.295571 \n", "L 513.939913 61.03728 \n", "L 514.093363 60.893436 \n", "L 514.400263 59.835984 \n", "L 514.860613 57.957032 \n", "L 515.014063 58.347361 \n", "L 515.167513 59.784291 \n", "L 515.474413 65.790933 \n", "L 516.395113 86.622461 \n", "L 517.162363 96.165336 \n", "L 517.776163 102.221485 \n", "L 517.929613 102.778212 \n", "L 518.083063 102.440649 \n", "L 518.236513 101.010613 \n", "L 518.543413 94.68669 \n", "L 519.617563 66.174177 \n", "L 520.077913 60.273762 \n", "L 520.384813 58.571605 \n", "L 520.845163 57.41687 \n", "L 521.152063 55.406766 \n", "L 521.612413 50.748626 \n", "L 521.765863 50.339076 \n", "L 521.919313 51.119308 \n", "L 522.226213 56.304244 \n", "L 524.067613 100.404828 \n", "L 524.681413 109.775037 \n", "L 524.988313 111.277398 \n", "L 525.141763 110.713058 \n", "L 525.295213 108.952237 \n", "L 525.602113 101.72125 \n", "L 526.522813 73.883384 \n", "L 526.829713 69.343519 \n", "L 527.136613 67.721948 \n", "L 527.290063 67.699109 \n", "L 527.903863 69.239849 \n", "L 528.057313 68.842473 \n", "L 528.364213 65.873448 \n", "L 528.978013 57.313524 \n", "L 529.131463 57.20593 \n", "L 529.284913 58.162266 \n", "L 529.745263 64.284585 \n", "L 530.205613 69.825903 \n", "L 530.972863 75.352043 \n", "L 531.279763 80.446873 \n", "L 532.047013 95.059613 \n", "L 532.353913 97.046346 \n", "L 532.507363 96.883213 \n", "L 532.814263 94.688969 \n", "L 533.428063 86.700495 \n", "L 533.888413 77.761208 \n", "L 534.655663 61.626191 \n", "L 535.269463 55.453229 \n", "L 536.190163 47.574385 \n", "L 536.343613 46.825709 \n", "L 536.497063 46.639292 \n", "L 536.650513 47.353949 \n", "L 536.957413 52.163287 \n", "L 537.571213 70.460585 \n", "L 538.491913 96.43459 \n", "L 539.105713 108.572103 \n", "L 539.566063 113.986192 \n", "L 539.719513 114.785609 \n", "L 539.872963 114.730397 \n", "L 540.026413 113.613487 \n", "L 540.333313 107.639512 \n", "L 541.407463 75.963672 \n", "L 541.714363 73.311508 \n", "L 541.867813 73.009513 \n", "L 542.021263 73.225247 \n", "L 542.328163 74.605162 \n", "L 542.635063 76.078071 \n", "L 542.788513 76.220566 \n", "L 542.941963 75.661497 \n", "L 543.248863 72.052426 \n", "L 543.862663 61.160794 \n", "L 544.016113 60.142107 \n", "L 544.169563 59.915629 \n", "L 544.476463 60.672644 \n", "L 544.783363 61.381542 \n", "L 544.936813 61.402292 \n", "L 545.090263 61.15943 \n", "L 545.704063 59.323933 \n", "L 545.857513 59.740521 \n", "L 546.010963 61.142522 \n", "L 546.317863 67.056035 \n", "L 547.238563 87.542524 \n", "L 547.852363 94.737736 \n", "L 548.619613 101.834688 \n", "L 548.773063 102.198123 \n", "L 548.926513 101.660588 \n", "L 549.233413 97.008515 \n", "L 550.614463 62.665033 \n", "L 551.074813 58.429334 \n", "L 551.842063 55.211486 \n", "L 552.455863 49.570105 \n", "L 552.609313 49.732307 \n", "L 552.762763 51.160978 \n", "L 553.069663 57.321958 \n", "L 554.604163 96.794176 \n", "L 555.371413 109.454469 \n", "L 555.678313 111.789357 \n", "L 555.831763 111.80339 \n", "L 555.985213 110.81903 \n", "L 556.292113 104.98833 \n", "L 557.366263 73.399534 \n", "L 557.673163 69.807765 \n", "L 557.826613 69.049759 \n", "L 557.980063 68.862495 \n", "L 558.133513 69.111052 \n", "L 558.593863 70.764835 \n", "L 558.747313 70.85223 \n", "L 558.900763 70.251429 \n", "L 559.207663 66.657212 \n", "L 559.668013 59.230662 \n", "L 559.821463 58.225065 \n", "L 559.974913 58.310261 \n", "L 560.281813 60.811102 \n", "L 560.895613 67.414164 \n", "L 561.202513 68.973765 \n", "L 561.509413 69.98363 \n", "L 561.816313 72.336074 \n", "L 562.123213 77.517268 \n", "L 562.890463 92.425263 \n", "L 563.197363 94.540084 \n", "L 563.350813 94.625754 \n", "L 563.504263 94.206003 \n", "L 563.811163 92.42833 \n", "L 564.424963 87.075739 \n", "L 564.731863 82.001304 \n", "L 565.806013 59.973736 \n", "L 566.419813 53.146743 \n", "L 567.340513 45.747656 \n", "L 567.493963 45.406904 \n", "L 567.647413 45.959083 \n", "L 567.954313 50.460114 \n", "L 568.414663 63.722788 \n", "L 569.488813 96.277514 \n", "L 570.102613 108.937 \n", "L 570.562963 114.62319 \n", "L 570.716413 115.542312 \n", "L 570.869863 115.713839 \n", "L 571.023313 114.948502 \n", "L 571.330213 109.761847 \n", "L 572.557813 75.649162 \n", "L 572.864713 74.2233 \n", "L 573.018163 74.452175 \n", "L 573.325063 76.006427 \n", "L 573.785413 78.310378 \n", "L 573.938863 78.077542 \n", "L 574.092313 76.981372 \n", "L 574.399213 71.902181 \n", "L 574.859563 62.77437 \n", "L 575.166463 60.412499 \n", "L 575.319913 60.230147 \n", "L 575.780263 60.210641 \n", "L 575.933713 59.883604 \n", "L 576.240613 58.437086 \n", "L 576.700963 55.885786 \n", "L 576.854413 55.796819 \n", "L 577.007863 56.589758 \n", "L 577.314763 61.473078 \n", "L 578.542363 88.989042 \n", "L 579.616513 103.835542 \n", "L 579.923413 105.748253 \n", "L 580.076863 105.440089 \n", "L 580.230313 104.004184 \n", "L 580.537213 97.517599 \n", "L 581.611363 67.887383 \n", "L 582.071713 62.265797 \n", "L 582.378613 61.095748 \n", "L 582.838963 60.706143 \n", "L 582.992413 60.095905 \n", "L 583.299313 57.296485 \n", "L 583.759663 52.541555 \n", "L 583.913113 52.621051 \n", "L 584.066563 53.943512 \n", "L 584.373463 59.687401 \n", "L 585.601063 87.445531 \n", "L 586.828663 107.019098 \n", "L 586.982113 107.810294 \n", "L 587.135563 107.740943 \n", "L 587.289013 106.567569 \n", "L 587.595913 100.76683 \n", "L 588.823513 67.408775 \n", "L 589.130413 64.417301 \n", "L 589.283863 63.8962 \n", "L 589.437313 63.865519 \n", "L 589.897663 64.509546 \n", "L 590.051113 64.279869 \n", "L 590.204563 63.504387 \n", "L 590.511463 59.955698 \n", "L 590.971813 54.685374 \n", "L 591.125263 54.843005 \n", "L 591.432163 58.668598 \n", "L 592.659763 80.792194 \n", "L 593.273563 90.023145 \n", "L 593.887363 100.845279 \n", "L 594.194263 103.186967 \n", "L 594.347713 103.063619 \n", "L 594.501163 101.971856 \n", "L 594.808063 97.010246 \n", "L 595.575313 76.918199 \n", "L 596.189113 63.51544 \n", "L 596.496013 60.343292 \n", "L 596.802913 59.004428 \n", "L 597.109813 58.078193 \n", "L 597.416713 56.206103 \n", "L 598.030513 50.023358 \n", "L 598.183963 49.74462 \n", "L 598.337413 50.635728 \n", "L 598.644313 55.890739 \n", "L 600.485713 100.49904 \n", "L 601.099513 110.121519 \n", "L 601.406413 111.863813 \n", "L 601.559863 111.359205 \n", "L 601.713313 109.681221 \n", "L 602.020213 102.649598 \n", "L 602.940913 74.629328 \n", "L 603.247813 70.015282 \n", "L 603.554713 68.34088 \n", "L 603.708163 68.381036 \n", "L 604.015063 69.460222 \n", "L 604.321963 70.369006 \n", "L 604.475413 70.102014 \n", "L 604.628863 69.079089 \n", "L 604.935763 64.674359 \n", "L 605.396113 57.695513 \n", "L 605.549563 57.243813 \n", "L 605.703013 57.930462 \n", "L 606.163363 63.422153 \n", "L 606.623713 68.379846 \n", "L 606.930613 69.909776 \n", "L 607.237513 71.172157 \n", "L 607.544413 74.396209 \n", "L 608.004763 83.985085 \n", "L 608.465113 92.829554 \n", "L 608.772013 95.516231 \n", "L 608.925463 95.784819 \n", "L 609.078913 95.454461 \n", "L 609.385813 93.559834 \n", "L 609.999613 87.62814 \n", "L 610.306513 82.588997 \n", "L 611.380663 60.472105 \n", "L 612.301363 50.867388 \n", "L 612.915163 45.927521 \n", "L 613.068613 45.497784 \n", "L 613.222063 45.841348 \n", "L 613.375513 47.270187 \n", "L 613.682413 53.54511 \n", "L 615.677263 107.935539 \n", "L 616.137613 114.306459 \n", "L 616.444513 115.982685 \n", "L 616.597963 115.526054 \n", "L 616.751413 113.927816 \n", "L 617.058313 106.699617 \n", "L 617.979013 78.119145 \n", "L 618.285913 74.74043 \n", "L 618.439363 74.143979 \n", "L 618.592813 74.141929 \n", "L 618.899713 75.400489 \n", "L 619.360063 77.7777 \n", "L 619.513513 77.744061 \n", "L 619.666963 76.952239 \n", "L 619.973863 72.531562 \n", "L 620.587663 61.580603 \n", "L 620.741113 60.684376 \n", "L 620.894563 60.446855 \n", "L 621.354913 60.822993 \n", "L 621.508363 60.616989 \n", "L 621.815263 59.402795 \n", "L 622.275613 56.834742 \n", "L 622.429063 56.613872 \n", "L 622.582513 57.160035 \n", "L 622.889413 61.419856 \n", "L 624.117013 88.18807 \n", "L 625.191163 102.262137 \n", "L 625.498063 104.335035 \n", "L 625.651513 104.247664 \n", "L 625.804963 103.039391 \n", "L 626.111863 97.073812 \n", "L 627.339463 65.102055 \n", "L 627.646363 61.601918 \n", "L 627.953263 60.023468 \n", "L 628.567063 58.621638 \n", "L 628.873963 56.012095 \n", "L 629.334313 51.686402 \n", "L 629.487763 51.799154 \n", "L 629.641213 53.160374 \n", "L 629.948113 58.950596 \n", "L 631.329163 91.080242 \n", "L 632.249863 106.583896 \n", "L 632.556763 109.003646 \n", "L 632.710213 109.004187 \n", "L 632.863663 107.926023 \n", "L 633.170563 102.249676 \n", "L 634.398163 68.624796 \n", "L 634.705063 65.821394 \n", "L 634.858513 65.379493 \n", "L 635.011963 65.395176 \n", "L 635.472313 66.161314 \n", "L 635.625763 65.959587 \n", "L 635.779213 65.161512 \n", "L 636.086113 61.595049 \n", "L 636.546463 55.557533 \n", "L 636.699913 55.298744 \n", "L 636.853363 56.209885 \n", "L 637.160263 60.579242 \n", "L 637.927513 73.654447 \n", "L 638.848213 85.542457 \n", "L 639.615463 99.713304 \n", "L 639.922363 101.569402 \n", "L 640.075813 101.186284 \n", "L 640.382713 97.901338 \n", "L 640.843063 88.745108 \n", "L 642.070663 61.629795 \n", "L 642.377563 59.264512 \n", "L 642.991363 56.221367 \n", "L 643.451713 51.810174 \n", "L 643.758613 49.212622 \n", "L 643.912063 48.979157 \n", "L 644.065513 49.819793 \n", "L 644.372413 54.842591 \n", "L 645.139663 76.575435 \n", "L 645.906913 94.931591 \n", "L 646.674163 108.730801 \n", "L 646.981063 111.851919 \n", "L 647.134513 112.520964 \n", "L 647.287963 112.275337 \n", "L 647.441413 110.885527 \n", "L 647.748313 104.28415 \n", "L 648.822463 73.229719 \n", "L 649.129363 70.209319 \n", "L 649.129363 70.209319 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"line2d_3\">\n", "    <path clip-path=\"url(#pcf74c83c01)\" d=\"M 342.382813 121.725237 \n", "L 342.382813 38.025238 \n", "\" style=\"fill:none;stroke:#000000;stroke-dasharray:1.000000,3.000000;stroke-dashoffset:0.0;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 35.482813 121.725237 \n", "L 35.482813 38.025238 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 35.482813 121.725237 \n", "L 649.282813 121.725237 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 649.282813 121.725237 \n", "L 649.282813 38.025238 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 35.482813 38.025238 \n", "L 649.282813 38.025238 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 -4 \n", "\" id=\"m73bc570cc1\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 4 \n", "\" id=\"mb6be7a02d9\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "\" id=\"BitstreamVeraSans-Roman-30\"/>\n", "      </defs>\n", "      <g transform=\"translate(32.3015625 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"112.2078125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"112.2078125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-35\"/>\n", "      </defs>\n", "      <g transform=\"translate(102.6640625 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"188.9328125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"188.9328125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <defs>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-31\"/>\n", "      </defs>\n", "      <g transform=\"translate(176.2078125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"265.6578125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"265.6578125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1500 -->\n", "      <g transform=\"translate(252.9328125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"342.3828125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"342.3828125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2000 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "\" id=\"BitstreamVeraSans-Roman-32\"/>\n", "      </defs>\n", "      <g transform=\"translate(329.6578125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"419.1078125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"419.1078125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2500 -->\n", "      <g transform=\"translate(406.3828125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"495.8328125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"495.8328125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 3000 -->\n", "      <defs>\n", "       <path d=\"M 40.578125 39.3125 \n", "Q 47.65625 37.796875 51.625 33 \n", "Q 55.609375 28.21875 55.609375 21.1875 \n", "Q 55.609375 10.40625 48.1875 4.484375 \n", "Q 40.765625 -1.421875 27.09375 -1.421875 \n", "Q 22.515625 -1.421875 17.65625 -0.515625 \n", "Q 12.796875 0.390625 7.625 2.203125 \n", "L 7.625 11.71875 \n", "Q 11.71875 9.328125 16.59375 8.109375 \n", "Q 21.484375 6.890625 26.8125 6.890625 \n", "Q 36.078125 6.890625 40.9375 10.546875 \n", "Q 45.796875 14.203125 45.796875 21.1875 \n", "Q 45.796875 27.640625 41.28125 31.265625 \n", "Q 36.765625 34.90625 28.71875 34.90625 \n", "L 20.21875 34.90625 \n", "L 20.21875 43.015625 \n", "L 29.109375 43.015625 \n", "Q 36.375 43.015625 40.234375 45.921875 \n", "Q 44.09375 48.828125 44.09375 54.296875 \n", "Q 44.09375 59.90625 40.109375 62.90625 \n", "Q 36.140625 65.921875 28.71875 65.921875 \n", "Q 24.65625 65.921875 20.015625 65.03125 \n", "Q 15.375 64.15625 9.8125 62.3125 \n", "L 9.8125 71.09375 \n", "Q 15.4375 72.65625 20.34375 73.4375 \n", "Q 25.25 74.21875 29.59375 74.21875 \n", "Q 40.828125 74.21875 47.359375 69.109375 \n", "Q 53.90625 64.015625 53.90625 55.328125 \n", "Q 53.90625 49.265625 50.4375 45.09375 \n", "Q 46.96875 40.921875 40.578125 39.3125 \n", "\" id=\"BitstreamVeraSans-Roman-33\"/>\n", "      </defs>\n", "      <g transform=\"translate(483.1078125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-33\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"572.5578125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"572.5578125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 3500 -->\n", "      <g transform=\"translate(559.8328125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-33\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#m73bc570cc1\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#mb6be7a02d9\" y=\"38.0252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 4000 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-34\"/>\n", "      </defs>\n", "      <g transform=\"translate(636.5578125 133.323675)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-34\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_22\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 4 0 \n", "\" id=\"m3d602d7dcf\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#m3d602d7dcf\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_23\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -4 0 \n", "\" id=\"m0a2a3cb190\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#m0a2a3cb190\" y=\"121.7252375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −0.6 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-2e\"/>\n", "       <path d=\"M 33.015625 40.375 \n", "Q 26.375 40.375 22.484375 35.828125 \n", "Q 18.609375 31.296875 18.609375 23.390625 \n", "Q 18.609375 15.53125 22.484375 10.953125 \n", "Q 26.375 6.390625 33.015625 6.390625 \n", "Q 39.65625 6.390625 43.53125 10.953125 \n", "Q 47.40625 15.53125 47.40625 23.390625 \n", "Q 47.40625 31.296875 43.53125 35.828125 \n", "Q 39.65625 40.375 33.015625 40.375 \n", "M 52.59375 71.296875 \n", "L 52.59375 62.3125 \n", "Q 48.875 64.0625 45.09375 64.984375 \n", "Q 41.3125 65.921875 37.59375 65.921875 \n", "Q 27.828125 65.921875 22.671875 59.328125 \n", "Q 17.53125 52.734375 16.796875 39.40625 \n", "Q 19.671875 43.65625 24.015625 45.921875 \n", "Q 28.375 48.1875 33.59375 48.1875 \n", "Q 44.578125 48.1875 50.953125 41.515625 \n", "Q 57.328125 34.859375 57.328125 23.390625 \n", "Q 57.328125 12.15625 50.6875 5.359375 \n", "Q 44.046875 -1.421875 33.015625 -1.421875 \n", "Q 20.359375 -1.421875 13.671875 8.265625 \n", "Q 6.984375 17.96875 6.984375 36.375 \n", "Q 6.984375 53.65625 15.1875 63.9375 \n", "Q 23.390625 74.21875 37.203125 74.21875 \n", "Q 40.921875 74.21875 44.703125 73.484375 \n", "Q 48.484375 72.75 52.59375 71.296875 \n", "\" id=\"BitstreamVeraSans-Roman-36\"/>\n", "       <path d=\"M 10.59375 35.5 \n", "L 73.1875 35.5 \n", "L 73.1875 27.203125 \n", "L 10.59375 27.203125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-2212\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 124.4846125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-2212\"/>\n", "       <use x=\"83.7890625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"147.412109375\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"179.19921875\" xlink:href=\"#BitstreamVeraSans-Roman-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#m3d602d7dcf\" y=\"104.9852375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#m0a2a3cb190\" y=\"104.9852375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −0.4 -->\n", "      <g transform=\"translate(7.2 107.7446125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-2212\"/>\n", "       <use x=\"83.7890625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"147.412109375\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"179.19921875\" xlink:href=\"#BitstreamVeraSans-Roman-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#m3d602d7dcf\" y=\"88.2452375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#m0a2a3cb190\" y=\"88.2452375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- −0.2 -->\n", "      <g transform=\"translate(7.2 91.0046125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-2212\"/>\n", "       <use x=\"83.7890625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"147.412109375\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"179.19921875\" xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#m3d602d7dcf\" y=\"71.5052375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#m0a2a3cb190\" y=\"71.5052375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(15.5796875 74.2646125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"35.4828125\" xlink:href=\"#m3d602d7dcf\" y=\"54.7652375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"649.2828125\" xlink:href=\"#m0a2a3cb190\" y=\"54.7652375\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(15.5796875 57.5246125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"text_15\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(15.5796875 40.7846125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 409.900813 29.655237 \n", "L 493.893247 29.655237 \n", "L 493.893247 7.2 \n", "L 409.900813 7.2 \n", "z\n", "\" style=\"fill:#ffffff;stroke:#000000;stroke-linejoin:miter;\"/>\n", "    </g>\n", "    <g id=\"line2d_32\">\n", "     <path d=\"M 414.758813 12.820316 \n", "L 424.474813 12.820316 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_33\"/>\n", "    <g id=\"text_16\">\n", "     <!-- target system -->\n", "     <defs>\n", "      <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "\" id=\"BitstreamVeraSans-Roman-73\"/>\n", "      <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "\" id=\"BitstreamVeraSans-Roman-61\"/>\n", "      <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "      <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "      <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-67\"/>\n", "      <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "      <path d=\"M 32.171875 -5.078125 \n", "Q 28.375 -14.84375 24.75 -17.8125 \n", "Q 21.140625 -20.796875 15.09375 -20.796875 \n", "L 7.90625 -20.796875 \n", "L 7.90625 -13.28125 \n", "L 13.1875 -13.28125 \n", "Q 16.890625 -13.28125 18.9375 -11.515625 \n", "Q 21 -9.765625 23.484375 -3.21875 \n", "L 25.09375 0.875 \n", "L 2.984375 54.6875 \n", "L 12.5 54.6875 \n", "L 29.59375 11.921875 \n", "L 46.6875 54.6875 \n", "L 56.203125 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-79\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "     </defs>\n", "     <g transform=\"translate(432.1088125 15.249315625)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "      <use x=\"100.48828125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"141.5859375\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "      <use x=\"205.0625\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"266.5859375\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"305.794921875\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "      <use x=\"337.58203125\" xlink:href=\"#BitstreamVeraSans-Roman-73\"/>\n", "      <use x=\"389.681640625\" xlink:href=\"#BitstreamVeraSans-Roman-79\"/>\n", "      <use x=\"448.861328125\" xlink:href=\"#BitstreamVeraSans-Roman-73\"/>\n", "      <use x=\"500.9609375\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"540.169921875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"601.693359375\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_34\">\n", "     <path d=\"M 414.758813 23.006934 \n", "L 424.474813 23.006934 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_35\"/>\n", "    <g id=\"text_17\">\n", "     <!-- free running ESN -->\n", "     <defs>\n", "      <path d=\"M 9.8125 72.90625 \n", "L 23.09375 72.90625 \n", "L 55.421875 11.921875 \n", "L 55.421875 72.90625 \n", "L 64.984375 72.90625 \n", "L 64.984375 0 \n", "L 51.703125 0 \n", "L 19.390625 60.984375 \n", "L 19.390625 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-4e\"/>\n", "      <path d=\"M 37.109375 75.984375 \n", "L 37.109375 68.5 \n", "L 28.515625 68.5 \n", "Q 23.6875 68.5 21.796875 66.546875 \n", "Q 19.921875 64.59375 19.921875 59.515625 \n", "L 19.921875 54.6875 \n", "L 34.71875 54.6875 \n", "L 34.71875 47.703125 \n", "L 19.921875 47.703125 \n", "L 19.921875 0 \n", "L 10.890625 0 \n", "L 10.890625 47.703125 \n", "L 2.296875 47.703125 \n", "L 2.296875 54.6875 \n", "L 10.890625 54.6875 \n", "L 10.890625 58.5 \n", "Q 10.890625 67.625 15.140625 71.796875 \n", "Q 19.390625 75.984375 28.609375 75.984375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-66\"/>\n", "      <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-69\"/>\n", "      <path d=\"M 53.515625 70.515625 \n", "L 53.515625 60.890625 \n", "Q 47.90625 63.578125 42.921875 64.890625 \n", "Q 37.9375 66.21875 33.296875 66.21875 \n", "Q 25.25 66.21875 20.875 63.09375 \n", "Q 16.5 59.96875 16.5 54.203125 \n", "Q 16.5 49.359375 19.40625 46.890625 \n", "Q 22.3125 44.4375 30.421875 42.921875 \n", "L 36.375 41.703125 \n", "Q 47.40625 39.59375 52.65625 34.296875 \n", "Q 57.90625 29 57.90625 20.125 \n", "Q 57.90625 9.515625 50.796875 4.046875 \n", "Q 43.703125 -1.421875 29.984375 -1.421875 \n", "Q 24.8125 -1.421875 18.96875 -0.25 \n", "Q 13.140625 0.921875 6.890625 3.21875 \n", "L 6.890625 13.375 \n", "Q 12.890625 10.015625 18.65625 8.296875 \n", "Q 24.421875 6.59375 29.984375 6.59375 \n", "Q 38.421875 6.59375 43.015625 9.90625 \n", "Q 47.609375 13.234375 47.609375 19.390625 \n", "Q 47.609375 24.75 44.3125 27.78125 \n", "Q 41.015625 30.8125 33.5 32.328125 \n", "L 27.484375 33.5 \n", "Q 16.453125 35.6875 11.515625 40.375 \n", "Q 6.59375 45.0625 6.59375 53.421875 \n", "Q 6.59375 63.09375 13.40625 68.65625 \n", "Q 20.21875 74.21875 32.171875 74.21875 \n", "Q 37.3125 74.21875 42.625 73.28125 \n", "Q 47.953125 72.359375 53.515625 70.515625 \n", "\" id=\"BitstreamVeraSans-Roman-53\"/>\n", "      <path d=\"M 9.8125 72.90625 \n", "L 55.90625 72.90625 \n", "L 55.90625 64.59375 \n", "L 19.671875 64.59375 \n", "L 19.671875 43.015625 \n", "L 54.390625 43.015625 \n", "L 54.390625 34.71875 \n", "L 19.671875 34.71875 \n", "L 19.671875 8.296875 \n", "L 56.78125 8.296875 \n", "L 56.78125 0 \n", "L 9.8125 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-45\"/>\n", "      <path d=\"M 8.5 21.578125 \n", "L 8.5 54.6875 \n", "L 17.484375 54.6875 \n", "L 17.484375 21.921875 \n", "Q 17.484375 14.15625 20.5 10.265625 \n", "Q 23.53125 6.390625 29.59375 6.390625 \n", "Q 36.859375 6.390625 41.078125 11.03125 \n", "Q 45.3125 15.671875 45.3125 23.6875 \n", "L 45.3125 54.6875 \n", "L 54.296875 54.6875 \n", "L 54.296875 0 \n", "L 45.3125 0 \n", "L 45.3125 8.40625 \n", "Q 42.046875 3.421875 37.71875 1 \n", "Q 33.40625 -1.421875 27.6875 -1.421875 \n", "Q 18.265625 -1.421875 13.375 4.4375 \n", "Q 8.5 10.296875 8.5 21.578125 \n", "\" id=\"BitstreamVeraSans-Roman-75\"/>\n", "      <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "\" id=\"BitstreamVeraSans-Roman-6e\"/>\n", "     </defs>\n", "     <g transform=\"translate(432.1088125 25.435934375)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-66\"/>\n", "      <use x=\"35.205078125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"76.287109375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"137.810546875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"199.333984375\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "      <use x=\"231.12109375\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"272.234375\" xlink:href=\"#BitstreamVeraSans-Roman-75\"/>\n", "      <use x=\"335.61328125\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "      <use x=\"398.9921875\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "      <use x=\"462.37109375\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "      <use x=\"490.154296875\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "      <use x=\"553.533203125\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "      <use x=\"617.009765625\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "      <use x=\"648.796875\" xlink:href=\"#BitstreamVeraSans-Roman-45\"/>\n", "      <use x=\"711.98046875\" xlink:href=\"#BitstreamVeraSans-Roman-53\"/>\n", "      <use x=\"775.45703125\" xlink:href=\"#BitstreamVeraSans-Roman-4e\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pcf74c83c01\">\n", "   <rect height=\"83.7\" width=\"613.8\" x=\"35.4828125\" y=\"38.0252375\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x10c4fb8d0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "from pyESN import ESN\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline\n", "\n", "data = np.load('mackey_glass_t17.npy') #  http://minds.jacobs-university.de/mantas/code\n", "esn = ESN(n_inputs = 1,\n", "          n_outputs = 1,\n", "          n_reservoir = 500,\n", "          spectral_radius = 1.5,\n", "          random_state=42)\n", "\n", "trainlen = 2000\n", "future = 2000\n", "pred_training = esn.fit(np.ones(trainlen),data[:trainlen])\n", "\n", "prediction = esn.predict(np.ones(future))\n", "print(\"test error: \\n\"+str(np.sqrt(np.mean((prediction.flatten() - data[trainlen:trainlen+future])**2))))\n", "\n", "plt.figure(figsize=(11,1.5))\n", "plt.plot(range(0,trainlen+future),data[0:trainlen+future],'k',label=\"target system\")\n", "plt.plot(range(trainlen,trainlen+future),prediction,'r', label=\"free running ESN\")\n", "lo,hi = plt.ylim()\n", "plt.plot([trainlen,trainlen],[lo+np.spacing(1),hi-np.spacing(1)],'k:')\n", "plt.legend(loc=(0.61,1.1),fontsize='x-small')"]}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 1}