
import qlib
from qlib.config import R<PERSON>_CN
from qlib.utils import init_instance_by_config, flatten_dict
from qlib.workflow import R
import optuna
from sklearn.metrics import mean_squared_error

from pyqlab.data.dataset.handler import DataHandlerAHF
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import ipywidgets as widgets
import copy
import lightgbm as lgb
from pprint import pprint

import warnings
warnings.filterwarnings("ignore")

provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
qlib.init(provider_uri=provider_uri, region=REG_CN)

###################################
# train model
###################################
SEL_LONG_FACTOR_NAMES = [ # Slow period factor
    # "MACD", "MACD_DIFF", "MACD_DEA", "MOM", "RSI",
    # "NEW_CHANGE_PERCENT", 
    # "LR_SLOPE_FAST",
    # "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    # "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    # "SQUEEZE_ZERO_BARS", 
    # "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    # "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    # "BAND_POSITION", "BAND_WIDTH",
    # "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    # "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    # "TREND_LEVEL"
]

SEL_SHORT_FACTOR_NAMES = [ # Fast period factor
    # "VOLUME", # 在RangeBar下，Volume是Bar的时长seconds
    "NEW_CHANGE_PERCENT",

    # "AROON_UP", "AROON_DOWN",
    "MACD", "MACD_DIFF", "MACD_DEA", "RSI",

    # "LONG_TERM_HIGH", "LONG_TERM_LOW", "SHORT_TERM_HIGH", "SHORT_TERM_LOW", 

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]

SEL_CONTEXT_FACTOR_NAMES = [
  "STDDEV_RNG", "SHORT_RANGE",
  "FAST_QH_RSI", "FAST_QH_ZSCORE", "FAST_QH_DIRECT",
  "FAST_QH_NATR", "FAST_QH_NATR_ZSCORE", "FAST_QH_NATR_DIRECT",
  "FAST_QH_MOM", "FAST_QH_MOM_ZSCORE", "FAST_QH_MOM_DIRECT",
  "DAYOFWEEK", "HOUR"
]


"""
根据当前测试的结果来看，主要有以下结论：
1.训练的数据越多越好，如何仅用2023年的数据来训练，那么测试的结果就不好
2.CONTEXT_FACTOR的因子对结果会造成噪点，最后不用
3.WIN_SIZE的大小对结果有影响，WIN_SIZE越大，结果越好 10 > 5
"""
IS_CLASS=False # 是否是分类问题
FACTOR_NUM=65
WIN_SIZE=0
FUT_CODES=MAIN_SEL_FUT_CODES
# FUT_CODES=SF_FUT_CODES
# FUT_CODES=MAIN_FUT_CODES
VERSION="V1"

pfs = {
    # ------------ main -----------
    # ============================
    '01HF_3Y_SEL': ['main.2023',],
    # -------------sf--------------
    # =============================
    # FUT_CODES=SF_FUT_CODES
    # =============================
    # '10HF_5Y_SF': ['sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],
}

filter_win = { # default=1
    '01HF_3Y_SEL': 1,
}

'''
param_dist = {
    'boosting_type': ['gbdt', 'dart', 'rf'],
    'num_leaves': sp.stats.randint(2, 1001),
    'subsample_for_bin': sp.stats.randint(10, 1001),
    'min_split_gain': sp.stats.uniform(0, 5.0),
    'min_child_weight': sp.stats.uniform(1e-6, 1e-2),
    'reg_alpha': sp.stats.uniform(0, 1e-2),
    'reg_lambda': sp.stats.uniform(0, 1e-2),
    'tree_learner': ['data', 'feature', 'serial', 'voting' ],
    'application': ['regression_l1', 'regression_l2', 'regression'],
    'bagging_freq': sp.stats.randint(1, 11),
    'bagging_fraction': sp.stats.uniform(1e-3, 0.99),
    'feature_fraction': sp.stats.uniform(1e-3, 0.99),
    'learning_rate': sp.stats.uniform(1e-6, 0.99),
    'max_depth': sp.stats.randint(1, 501),
    'n_estimators': sp.stats.randint(100, 20001),
    'gpu_use_dp': [True, False],
}
'''

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": ['2020',],
    "kwargs": {
        "win": WIN_SIZE,                # 采样窗口,与下面的num_channel通道数保持一致
        "step": 1,                      # 采样步长，通常为1
        "filter_win": 1,                # 是否过滤掉特征数据
        "is_class": IS_CLASS,           # 是否是分类问题
        "is_filter_extreme": False,     # 是否过滤极端值
        "is_normal": False,             # 是否归一化
        "verbose": True,                # 是否打印日志
        "direct": "long",
        "model_name": "GBDT",
        "model_name_suff": "",          # 模型名称后缀，通常与上面的win保持一致
        "model_path": "e:/lab/RoboQuant/pylab/model",
        "sel_lf_names": SEL_LONG_FACTOR_NAMES,
        "sel_sf_names": SEL_SHORT_FACTOR_NAMES,
        "sel_ct_names": SEL_CONTEXT_FACTOR_NAMES,
    },

    "data_loader": {
        "class": "AHFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "data_path": "e:/featdata",
            "train_codes": FUT_CODES,   # 选期货交易标的
        },
    },
}

dataset_config = {
    "class": "AHFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAHF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    },
}

def get_dataset(pfs_name: str):

    handler_class_config = copy.deepcopy(dataset_config["kwargs"]["handler"])
    if pfs_name:
        if pfs_name not in pfs.keys():
            print(f"pfs_name: {pfs_name} not in pfs.keys() or pfs_win.keys()")
            return
        data_handler_config["instruments"] = pfs[pfs_name]
        data_handler_config["kwargs"]["model_name_suff"] = pfs_name

    handler_class_config["kwargs"] = data_handler_config
    hd: DataHandlerAHF = init_instance_by_config(handler_class_config)
    dataset_config["kwargs"]["handler"] = hd
    dataset = init_instance_by_config(dataset_config)
    dataset.setup_data(handler_kwargs=data_handler_config)
    return dataset


if __name__=="__main__":

    # item = input("select DIRECT:\n 1: long\n 2: short\n")
    # if item == '1':
    #     direct='long'
    # else:
    #     direct='short'
        
    direct='ls'
    pfs_name = "01HF_3Y_SEL"
    dataset = get_dataset(pfs_name)
    x_train, y_train, x_valid, y_valid = dataset.prepare(
        ["train", "valid"],
        col_set=["feature", "label", "encoded"],
        data_key=None,  # DataHandlerLP.DK_L,
        direct=direct,
        win=0,
        filter_win=5,
        model_name_suff=pfs_name,
        is_class=IS_CLASS,
    )

    train_data = lgb.Dataset(x_train, label=y_train)
    valid_data = lgb.Dataset(x_valid, label=y_valid, reference=train_data)

    def objective(trial):

        params = {
            'objective': 'regression',
            'metric': 'mse', # "binary_logloss", "auc"
            'boosting_type': 'gbdt',
            'num_leaves': trial.suggest_int('num_leaves', 2, 256),
            'learning_rate': trial.suggest_uniform('learning_rate', 0.001, 0.1),
            'feature_fraction': trial.suggest_uniform('feature_fraction', 0.1, 1.0),
            'bagging_fraction': trial.suggest_uniform('bagging_fraction', 0.1, 1.0),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 10),
            'max_depth': trial.suggest_int('max_depth', 2, 100),
            # "subsample": trial.suggest_uniform("subsample", 0, 1),
            # "lambda_l1": trial.suggest_loguniform("lambda_l1", 1e-8, 1e1),
            # "lambda_l2": trial.suggest_loguniform("lambda_l2", 1e-8, 1e1),
            # "colsample_bytree": trial.suggest_uniform("colsample_bytree", 0.5, 1),
        }

        # 训练 LightGBM 模型
        model = lgb.train(params, train_data, valid_sets=[valid_data], early_stopping_rounds=50, verbose_eval=False)

        # 验证模型性能
        y_pred = model.predict(x_valid)
        mse = mean_squared_error(y_valid, y_pred)

        return mse
    
    study = optuna.create_study(direction='minimize', study_name="gbdt", storage=f"sqlite:///db_gbdt_{pfs_name}_{direct}.sqlite3", load_if_exists=True)
    study.optimize(objective, n_trials=100, n_jobs=1)

    print(study.best_value)
    print(study.best_params)
