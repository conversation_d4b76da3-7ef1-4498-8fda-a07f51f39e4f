import os
import torch
import numpy as np
import onnxruntime as ort
from datetime import datetime
import argparse

def test_onnx_model(model_path: str, block_size: int = 30):
    """测试ONNX模型推理
    
    Args:
        model_path: ONNX模型路径
        block_size: 序列长度
    """
    try:
        # 1. 加载ONNX模型
        print(f"正在加载模型: {model_path}")
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] \
            if 'CUDAExecutionProvider' in ort.get_available_providers() \
            else ['CPUExecutionProvider']
            
        session = ort.InferenceSession(
            model_path,
            providers=providers
        )
        
        # 2. 准备测试数据
        code = np.zeros((1, block_size), dtype=np.int32)  # 模拟代码序列
        x = np.zeros((1, block_size), dtype=np.int32)     # 模拟输入序列
        x_mark = np.zeros((1, block_size, 5), dtype=np.float32)  # 模拟时间特征
        
        # 填充一些测试数据
        for i in range(block_size):
            code[0, i] = i % 10
            x[0, i] = i * 100
            x_mark[0, i] = [i/block_size, (i%5)/5, i%2, i%3/3, i%4/4]
        
        # 3. 获取模型输入信息并准备输入字典
        print("\n模型输入信息:")
        input_name_mapping = {
            'code': 'input.3',
            'x': 'input.1',
            'x_mark': 'onnx::MatMul_2'
        }
        
        ort_inputs = {}
        for input_meta in session.get_inputs():
            name = input_meta.name
            print(f"名称: {name}")
            print(f"形状: {input_meta.shape}")
            print(f"类型: {input_meta.type}")
            print("---")
            
            # 根据输入名称映射关系设置数据
            if name == input_name_mapping['code']:
                ort_inputs[name] = code
            elif name == input_name_mapping['x']:
                ort_inputs[name] = x
            elif name == input_name_mapping['x_mark']:
                ort_inputs[name] = x_mark
        
        # 4. 执行推理
        print("\n开始推理...")
        start_time = datetime.now()
        outputs = session.run(None, ort_inputs)
        end_time = datetime.now()
        inference_time = (end_time - start_time).total_seconds() * 1000
        
        # 5. 处理输出
        logits = torch.tensor(outputs[0])
        probs = torch.softmax(logits[:, -1, :], dim=-1)
        prediction = torch.argmax(probs, dim=-1).item()
        confidence = probs[0][prediction].item()
        
        # 6. 打印结果
        print("\n推理结果:")
        print(f"预测的下一个token: {prediction}")
        print(f"预测置信度: {confidence:.4f}")
        print(f"推理时间: {inference_time:.2f}ms")
        
        # 7. 输出形状信息
        print("\n输出张量形状:")
        for i, output in enumerate(outputs):
            print(f"输出 {i}: shape={output.shape}, type={output.dtype}")
            
        return True
        
    except Exception as e:
        print(f"\n错误: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='测试ONNX模型推理')
    parser.add_argument('--model_dir', type=str, default='E:/lab/RoboQuant/pylab/model',
                      help='模型目录路径')
    parser.add_argument('--block_size', type=int, default=30,
                      help='序列长度')
    args = parser.parse_args()
    
    # 获取目录下所有ONNX模型
    model_files = [f for f in os.listdir(args.model_dir) if f.endswith('.onnx')]
    
    if not model_files:
        print(f"在 {args.model_dir} 目录下未找到ONNX模型文件")
        return
    
    print(f"找到 {len(model_files)} 个ONNX模型文件:")
    for i, model_file in enumerate(model_files):
        print(f"{i+1}. {model_file}")
    
    # 测试每个模型
    for model_file in model_files:
        if 'GPT' not in model_file:
            continue
        model_path = os.path.join(args.model_dir, model_file)
        print(f"\n===== 测试模型: {model_file} =====")
        success = test_onnx_model(model_path, args.block_size)
        if success:
            print(f"模型 {model_file} 测试成功")
        else:
            print(f"模型 {model_file} 测试失败")
        break

if __name__ == "__main__":
    main()
