"""
使用PyTorch Lightning框架训练CandlestickLLM模型
参照train_bar_gpt.bat的训练流程
"""

import os
import sys
import argparse
import logging
import numpy as np
import pandas as pd
import torch
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import TimeSeriesSplit, KFold
import pytorch_lightning as pl
from pytorch_lightning.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
    RichProgressBar,
    TQDMProgressBar,
    RichModelSummary,
    DeviceStatsMonitor,
    GradientAccumulationScheduler,
    BatchSizeFinder,
    StochasticWeightAveraging
)
from pytorch_lightning.loggers import TensorBoard<PERSON>ogger, CSVLogger
from pytorch_lightning.utilities.model_summary import ModelSummary
from datetime import datetime
from time import time

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入自定义模块
from pyqlab.models.gpt2.bak.pl_candlestick_llm import PLCandlestickLLM, PLCandlestickLLMDataModule
from pyqlab.models.gpt2.candlestick_tokenizer import CandlestickTokenizer
from pyqlab.models.gpt2.nonlinear_tokenizer import NonlinearCandlestickTokenizer
from pyqlab.models.gpt2.bak.candlestick_dataset import CandlestickDataset
from pyqlab.const import MODEL_FUT_CODES, MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES
from pyqlab.models.gpt2.utils import get_model_name
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('train_pl_candlestick_llm.log')
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用PyTorch Lightning训练CandlestickLLM模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='训练数据路径')
    parser.add_argument('--market', default='fut', choices=['fut', 'stk'], type=str, help='市场类型')
    parser.add_argument('--block_name', default='sf', type=str, help='板块名称')
    parser.add_argument('--period', default='min5', choices=['day', 'min5', 'min1'], type=str, help='周期')
    parser.add_argument('--start_year', default=2024, type=int, help='开始年份')
    parser.add_argument('--end_year', default=2024, type=int, help='结束年份')
    parser.add_argument('--start_date', default='', type=str, help='开始日期')
    parser.add_argument('--end_date', default='', type=str, help='结束日期')

    # 数据处理参数
    parser.add_argument('--seq_len', type=int, default=30, help='输入序列长度')
    parser.add_argument('--pred_len', type=int, default=1, help='预测序列长度')
    parser.add_argument('--stride', type=int, default=1, help='滑动窗口步长')
    parser.add_argument('--batch_size', type=int, default=32, help='批大小')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器的工作进程数')

    # Tokenizer参数
    parser.add_argument('--tokenizer_type', type=str, default='linear', choices=['linear', 'nonlinear'], help='Tokenizer类型')
    parser.add_argument('--nonlinear_mapping', type=str, default='logarithmic',
                        choices=['logarithmic', 'exponential', 'sigmoid', 'quantile'],
                        help='非线性映射类型')
    parser.add_argument('--tokenizer_path', type=str, default=None, help='预训练Tokenizer路径')
    # 注意：vocab_size参数已移除，因为CandlestickTokenizer会根据范围自动计算词汇表大小

    # 模型参数
    parser.add_argument('--n_layer', type=int, default=6, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=256, help='模型维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比例')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')

    # 训练参数
    parser.add_argument('--learning_rate', type=float, default=3e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='权重衰减')
    parser.add_argument('--max_epochs', type=int, default=10, help='最大训练轮数')
    parser.add_argument('--lr_scheduler', type=str, default='cosine',
                        choices=['cosine', 'reduce_on_plateau', 'one_cycle'],
                        help='学习率调度器类型')
    parser.add_argument('--warmup_ratio', type=float, default=0.1, help='预热步数比例')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪值')
    parser.add_argument('--label_smoothing', type=float, default=0.0, help='标签平滑系数')
    parser.add_argument('--early_stopping', type=int, default=5, help='早停轮数')
    parser.add_argument('--min_delta', type=float, default=0.00, help='早停最小变化阈值')
    parser.add_argument('--use_swa', action='store_true', help='是否使用随机权重平均')
    parser.add_argument('--swa_lrs', type=float, default=1e-4, help='SWA学习率')
    parser.add_argument('--swa_epoch_start', type=float, default=0.8, help='SWA开始的轮数比例')
    parser.add_argument('--accumulate_grad_batches', type=int, default=1, help='梯度累积批次数')
    parser.add_argument('--find_batch_size', action='store_true', help='是否自动寻找最佳批大小')
    parser.add_argument('--k_folds', type=int, default=5, help='交叉验证折数')

    # 回调参数
    parser.add_argument('--use_rich_progress_bar', action='store_true', help='是否使用Rich进度条')
    parser.add_argument('--use_tqdm_progress_bar', action='store_true', help='是否使用TQDM进度条')
    parser.add_argument('--use_model_summary', action='store_true', help='是否显示模型摘要')
    parser.add_argument('--use_device_stats', action='store_true', help='是否监控设备状态')
    parser.add_argument('--model_summary_depth', type=int, default=1, help='模型摘要深度')
    parser.add_argument('--save_top_k', type=int, default=3, help='保存最佳模型的数量')
    parser.add_argument('--save_last', action='store_true', help='是否保存最后一个模型')
    parser.add_argument('--use_csv_logger', action='store_true', help='是否使用CSV日志记录器')
    parser.add_argument('--log_every_n_steps', type=int, default=10, help='每多少步记录一次日志')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--checkpoint_dir', type=str, default='./checkpoints', help='检查点保存目录')
    parser.add_argument('--log_dir', type=str, default='./logs', help='日志保存目录')
    parser.add_argument('--resume_from_checkpoint', type=str, default=None, help='从检查点恢复训练')
    parser.add_argument('--precision', type=str, default='32-true', help='精度')
    parser.add_argument('--accelerator', type=str, default='auto', help='加速器类型')
    parser.add_argument('--devices', type=int, default=1, help='设备数量')
    parser.add_argument('--strategy', type=str, default=None, help='分布式策略')
    parser.add_argument('--version', type=str, default='v1', help='模型版本')
    parser.add_argument('--time_encoding', type=str, default='timeF', choices=['timeF', 'fixed'], help='时间编码类型')
    parser.add_argument('--model_dir', type=str, default='./models', help='模型保存目录')

    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    import random
    import numpy as np
    import torch

    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    # if torch.cuda.is_available():
    #     torch.cuda.manual_seed(seed)
    #     torch.cuda.manual_seed_all(seed)
    # torch.backends.cudnn.deterministic = True
    # torch.backends.cudnn.benchmark = False


def load_data(args):
    """加载数据"""
    logger.info("加载数据...")

    # 加载训练数据
    data_list = []
    code_ids = []

    if args.data_path.endswith('.parquet'):
        df = pd.read_parquet(args.data_path)
    elif args.data_path.endswith('.csv'):
        df = pd.read_csv(args.data_path)
    else:
        raise ValueError(f"不支持的文件格式: {args.data_path}")

    # 过滤日期
    if 'datetime' in df.columns:
        df['datetime'] = pd.to_datetime(df['datetime'])
        if args.start_date and args.end_date:
            # 将字符串日期转换为datetime类型
            start_date = pd.to_datetime(args.start_date)
            end_date = pd.to_datetime(args.end_date)
            df = df[(df['datetime'] >= start_date) & (df['datetime'] <= end_date)]
        elif args.start_year and args.end_year:
            # 使用年份过滤
            df = df[(df['datetime'].dt.year >= args.start_year) & (df['datetime'].dt.year <= args.end_year)]

    # 设置代码列表
    if args.market == 'fut':
        if args.block_name == 'sf':
            args.sel_codes = SF_FUT_CODES
        elif args.block_name == 'main':
            args.sel_codes = MAIN_FUT_CODES
        else:
            args.sel_codes = MAIN_SEL_FUT_CODES

    # 按代码分组
    if 'code' in df.columns:
        # 如果有sel_codes，过滤代码
        # if hasattr(args, 'sel_codes') and args.sel_codes:
        #     df = df[df['code'].isin([f"{code}.SF" for code in args.sel_codes])]
        # print(df)

        grouped = df.groupby('code')
        for code, group in grouped:
            data_list.append(group)
            if isinstance(code, str) and len(code) > 2 and code[-2:] == "SF":
                try:
                    # 尝试从期货代码中提取基础代码
                    base_code = code[:-7]  # 去掉.SF
                    if base_code in MODEL_FUT_CODES:
                        code_id = MODEL_FUT_CODES.index(base_code)
                    else:
                        # 如果找不到基础代码，使用整数代码
                        code_id = int(base_code)
                    code_ids.append(code_id)
                except (ValueError, IndexError):
                    # 如果出现错误，使用整数代码
                    logger.warning(f"无法解析期货代码: {code}，使用整数代码")
                    code_ids.append(0)
            else:
                code_ids.append(0)
    else:
        data_list = [df]
        code_ids = [0]

    logger.info(f"训练数据: {len(data_list)}个证券, 总数据量: {sum(len(df) for df in data_list)}")

    return data_list, code_ids


def load_callbacks(args):
    """加载回调函数"""
    logger.info("加载回调函数...")
    callbacks = []

    # 模型检查点回调
    checkpoint_callback = ModelCheckpoint(
        dirpath=os.path.join(args.checkpoint_dir, 'pl_candlestick_llm'),
        filename='model-{epoch:02d}-{val_loss:.4f}',
        save_top_k=args.save_top_k,
        monitor='val_loss',
        mode='min',
        save_last=args.save_last
    )
    callbacks.append(checkpoint_callback)

    # 早停回调
    if args.early_stopping > 0:
        early_stopping_callback = EarlyStopping(
            monitor='val_loss',
            patience=args.early_stopping,
            mode='min',
            min_delta=args.min_delta
        )
        callbacks.append(early_stopping_callback)

    # 学习率监控回调
    lr_monitor = LearningRateMonitor(logging_interval='step')
    callbacks.append(lr_monitor)

    # 进度条回调
    if args.use_rich_progress_bar:
        try:
            callbacks.append(RichProgressBar())
            logger.info("已添加Rich进度条")
        except Exception as e:
            logger.warning(f"无法添加Rich进度条: {e}")

    if args.use_tqdm_progress_bar:
        try:
            callbacks.append(TQDMProgressBar())
            logger.info("已添加TQDM进度条")
        except Exception as e:
            logger.warning(f"无法添加TQDM进度条: {e}")

    # 模型摘要回调
    if args.use_model_summary:
        try:
            callbacks.append(RichModelSummary(max_depth=args.model_summary_depth))
            logger.info(f"已添加模型摘要 (深度: {args.model_summary_depth})")
        except Exception as e:
            logger.warning(f"无法添加模型摘要: {e}")

    # 设备状态监控回调
    if args.use_device_stats:
        try:
            callbacks.append(DeviceStatsMonitor())
            logger.info("已添加设备状态监控")
        except Exception as e:
            logger.warning(f"无法添加设备状态监控: {e}")

    # 随机权重平均回调
    if args.use_swa:
        try:
            callbacks.append(StochasticWeightAveraging(
                swa_lrs=args.swa_lrs,
                swa_epoch_start=args.swa_epoch_start
            ))
            logger.info(f"已添加随机权重平均 (学习率: {args.swa_lrs}, 开始轮数比例: {args.swa_epoch_start})")
        except Exception as e:
            logger.warning(f"无法添加随机权重平均: {e}")

    # 批大小查找回调
    if args.find_batch_size:
        try:
            callbacks.append(BatchSizeFinder())
            logger.info("已添加批大小查找器")
        except Exception as e:
            logger.warning(f"无法添加批大小查找器: {e}")

    # 梯度累积调度回调
    if args.accumulate_grad_batches > 1:
        try:
            # 创建梯度累积调度，从1开始，在训练的前10%逐渐增加到指定值
            accumulate_grad_batches = {
                0: 1,
                int(args.max_epochs * 0.1): args.accumulate_grad_batches
            }
            callbacks.append(GradientAccumulationScheduler(scheduling=accumulate_grad_batches))
            logger.info(f"已添加梯度累积调度 (批次数: {args.accumulate_grad_batches})")
        except Exception as e:
            logger.warning(f"无法添加梯度累积调度: {e}")

    logger.info(f"共加载了 {len(callbacks)} 个回调函数")
    return callbacks


def save_model_as_onnx(args, model, best_score):
    """将模型保存为ONNX格式"""
    try:
        model.freeze()
        code = torch.zeros(1, args.seq_len).to(torch.int32)
        x = torch.zeros(1, args.seq_len).to(torch.int32)
        x_mark = torch.zeros(1, args.seq_len, 5).to(torch.float32)

        trainer_name = get_model_name(args)
        tm_str = datetime.fromtimestamp(time()).strftime('%m%d%H')
        model_name = f"{trainer_name}_{tm_str}_{best_score:.3f}_ls"

        os.makedirs(args.checkpoint_dir, exist_ok=True)
        model.to_onnx(f"{args.checkpoint_dir}/{model_name}.onnx", (code, x, x_mark, None), export_params=True)

        print(f"Model saved to: {args.checkpoint_dir}/{model_name}.onnx")
        return True
    except Exception as e:
        print(f"Error exporting model to ONNX: {e}")
        return False

def create_tokenizer(args, train_data_list=None):
    """创建Tokenizer"""
    logger.info("创建Tokenizer...")
    # train_data_list参数保留用于未来可能的扩展，目前未使用

    # 如果提供了预训练Tokenizer路径，加载它
    if args.tokenizer_path and os.path.exists(args.tokenizer_path):
        logger.info(f"加载预训练Tokenizer: {args.tokenizer_path}")
        if args.tokenizer_type == 'linear':
            tokenizer = CandlestickTokenizer.load(args.tokenizer_path)
        else:
            tokenizer = NonlinearCandlestickTokenizer.load(args.tokenizer_path)
        return tokenizer

    # 否则，创建新的Tokenizer
    if args.tokenizer_type == 'linear':
        logger.info("创建线性Tokenizer...")
        # CandlestickTokenizer不接受vocab_size参数，它会根据范围自动计算词汇表大小
        tokenizer = CandlestickTokenizer(
            # 默认参数
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            volume_range=(-9, 9),
            special_tokens=True,
            include_volume=False,
            verbose=False
        )
    else:
        logger.info(f"创建非线性Tokenizer (映射类型: {args.nonlinear_mapping})...")
        # 创建映射函数字典
        mapping_functions = {}

        # 根据映射类型创建映射函数
        if args.nonlinear_mapping == 'logarithmic':
            # 对数映射函数将在fit时自动创建
            logger.info("将使用对数映射函数")
        elif args.nonlinear_mapping == 'exponential':
            # 指数映射函数将在fit时自动创建
            logger.info("将使用指数映射函数")
        elif args.nonlinear_mapping == 'sigmoid':
            # S形映射函数将在fit时自动创建
            logger.info("将使用S形映射函数")
        elif args.nonlinear_mapping == 'quantile':
            # 分位数映射函数将在fit时自动创建
            logger.info("将使用分位数映射函数")

        # 创建非线性Tokenizer
        tokenizer = NonlinearCandlestickTokenizer(
            change_range=(-12, 12),
            entity_range=(-12, 12),
            shadow_range=(0, 7),
            volume_range=(-9, 9),
            mapping_functions=mapping_functions
        )

    # 注意：CandlestickTokenizer和NonlinearCandlestickTokenizer没有fit方法
    # 它们在初始化时已经创建了词汇表，不需要额外的拟合步骤
    logger.info("Tokenizer已初始化，无需额外拟合")

    # 保存Tokenizer
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    tokenizer_path = os.path.join(args.checkpoint_dir, f"{args.tokenizer_type}_tokenizer.pkl")
    tokenizer.save(tokenizer_path)
    logger.info(f"Tokenizer已保存到: {tokenizer_path}")

    return tokenizer


def main():
    """主函数"""
    # 导入必要的模块
    import os

    # 解析命令行参数
    args = parse_args()

    # 检查是否在分布式环境中
    is_distributed = 'RANK' in os.environ and 'WORLD_SIZE' in os.environ
    if is_distributed:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ.get('LOCAL_RANK', 0))
        logger.info(f"分布式训练环境: RANK={rank}, WORLD_SIZE={world_size}, LOCAL_RANK={local_rank}")
    else:
        logger.info("单进程训练环境")

    # 设置随机种子
    set_seed(args.seed)

    # 加载数据
    data_list, code_ids = load_data(args)

    # 创建Tokenizer
    tokenizer = create_tokenizer(args, data_list)

    pl.seed_everything(args.seed)

    # trainer_name = get_trainer_name(args)
    # print(f"Trainer Name: {trainer_name}")
    dataset = CandlestickDataset(
        data=data_list,
        tokenizer=tokenizer,
        seq_len=args.seq_len,
        pred_len=args.pred_len,
        stride=args.stride,
        add_special_tokens=True,
        transform=None,
        code_ids=code_ids,
        use_time_features=args.use_time_features
    )

    model=None
    # 定义交叉验证
    # 使用 KFold 分割数据集
    kfold = KFold(n_splits=args.k_folds, shuffle=True, random_state=args.seed)
    # 创建一个数据模块列表，每个数据模块对应一个 fold
    for fold, (train_idx, val_idx) in enumerate(kfold.split(dataset)):

        print(f"=== Training fold {fold} ===")
        train_data = Subset(dataset, train_idx)
        val_data = Subset(dataset, val_idx)
        print(f"Train data: {len(train_data)}, Val data: {len(val_data)}, Total data: {len(dataset)}")

        # 创建数据模块
        logger.info("创建数据模块...")
        data_module = PLCandlestickLLMDataModule(
            train_set=train_data,
            val_set=val_data,
            test_set=None,
            batch_size=args.batch_size,
            num_workers=args.num_workers,
        )

        # 创建模型
        logger.info("创建模型...")

        # 获取tokenizer的词汇表大小
        vocab_size = tokenizer.vocab_size
        logger.info(f"Tokenizer词汇表大小: {vocab_size}")
        del tokenizer

        model = PLCandlestickLLM(
            vocab_size=vocab_size,
            code_size=len(MODEL_FUT_CODES),
            block_size=args.seq_len,
            n_layer=args.n_layer,
            n_head=args.n_head,
            d_model=args.d_model,
            dropout=args.dropout,
            bias=True,
            use_time_features=args.use_time_features,
            n_time_features=5,  # 假设有5个时间特征
            learning_rate=args.learning_rate,
            weight_decay=args.weight_decay,
            betas=(0.9, 0.999),
            lr_scheduler=args.lr_scheduler,
            warmup_ratio=args.warmup_ratio,
            max_epochs=args.max_epochs,
            grad_clip=args.grad_clip,
            label_smoothing=args.label_smoothing
        )

        # 加载回调函数
        callbacks = load_callbacks(args)

        # 获取检查点回调，用于后续获取最佳模型路径
        checkpoint_callback = next((cb for cb in callbacks if isinstance(cb, ModelCheckpoint)), None)
        if checkpoint_callback is None:
            logger.warning("未找到检查点回调，将无法获取最佳模型路径")

        # 创建日志记录器
        logger.info("创建日志记录器...")
        loggers = []

        # TensorBoard日志记录器
        version = datetime.now().strftime('%Y%m%d_%H%M%S')
        tb_logger = TensorBoardLogger(
            save_dir=args.log_dir,
            name='pl_candlestick_llm',
            version=version
        )
        loggers.append(tb_logger)

        # CSV日志记录器
        if args.use_csv_logger:
            try:
                csv_logger = CSVLogger(
                    save_dir=args.log_dir,
                    name='pl_candlestick_llm_csv',
                    version=version
                )
                loggers.append(csv_logger)
                logger.info("已添加CSV日志记录器")
            except Exception as e:
                logger.warning(f"无法添加CSV日志记录器: {e}")

        # 创建训练器
        logger.info("创建训练器...")

        # 检查GPU是否可用
        gpu_available = torch.cuda.is_available()
        if not gpu_available and args.accelerator == 'gpu':
            logger.warning("GPU不可用，将使用CPU进行训练")
            accelerator = 'cpu'
            precision = '32-true'  # CPU不支持混合精度训练
        else:
            accelerator = args.accelerator
            precision = args.precision # 16-mixed

        # 准备训练器参数
        trainer_kwargs = {
            'max_epochs': args.max_epochs,
            'callbacks': callbacks,
            'logger': loggers if len(loggers) > 1 else loggers[0],
            'precision': precision,
            'accelerator': accelerator,
            'gradient_clip_val': args.grad_clip,
            'log_every_n_steps': args.log_every_n_steps,
            'deterministic': True
        }

        # 根据加速器类型设置设备
        if accelerator == 'gpu':
            trainer_kwargs['devices'] = args.devices
        elif accelerator == 'cpu':
            # 对于CPU，设置为使用的CPU核心数量
            # trainer_kwargs['devices'] = 1  # 使用最多4个CPU核心
            trainer_kwargs['devices'] = min(os.cpu_count() or 1, 1)  # 使用最多4个CPU核心
        # 对于'auto'加速器，不设置devices参数，让PyTorch Lightning自动选择

        # 处理分布式训练策略
        if args.strategy is not None:
            if args.strategy == 'ddp':
                # 检查是否在分布式环境中
                if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
                    logger.info(f"检测到分布式环境: RANK={os.environ['RANK']}, WORLD_SIZE={os.environ['WORLD_SIZE']}")
                    # 使用PyTorch Lightning的DDP策略
                    trainer_kwargs['strategy'] = 'ddp'
                else:
                    logger.warning("未检测到分布式环境变量，将使用单进程训练")
                    # 如果没有分布式环境变量，则不设置策略
            else:
                # 使用用户指定的其他策略
                trainer_kwargs['strategy'] = args.strategy

        # 设置梯度累积批次数
        if args.accumulate_grad_batches > 1 and not any(isinstance(cb, GradientAccumulationScheduler) for cb in callbacks):
            trainer_kwargs['accumulate_grad_batches'] = args.accumulate_grad_batches
            logger.info(f"设置梯度累积批次数: {args.accumulate_grad_batches}")

        # 创建训练器
        logger.info(f"训练器参数: {trainer_kwargs}")
        trainer = pl.Trainer(**trainer_kwargs)

        # 训练模型
        logger.info("开始训练...")
        trainer.fit(model, data_module, ckpt_path=args.resume_from_checkpoint)

        # 测试模型
        # if test_data_list is not None:
        #     logger.info("开始测试...")
        #     trainer.test(model, data_module)

        logger.info("训练完成!")
        logger.info(f"最佳模型路径: {checkpoint_callback.best_model_path}")
        logger.info(f"最佳验证损失: {checkpoint_callback.best_model_score:.4f}")

        # 导出ONNX模型
        if checkpoint_callback and checkpoint_callback.best_model_path:
            try:
                # 加载最佳模型
                best_model = PLCandlestickLLM.load_from_checkpoint(checkpoint_callback.best_model_path)
                # 导出为ONNX格式
                save_model_as_onnx(args, best_model, checkpoint_callback.best_model_score)
                logger.info("成功导出ONNX模型")
            except Exception as e:
                logger.error(f"导出ONNX模型失败: {e}")


if __name__ == "__main__":
    main()
