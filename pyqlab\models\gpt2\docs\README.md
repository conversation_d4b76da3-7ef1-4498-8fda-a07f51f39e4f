# K线数据离散化与LLM预测模型

本模块实现了将K线（Candlestick）数据离散化为token，并利用现有的LLM（大型语言模型）架构进行预测的方法。

## 核心思想

将金融时间序列数据（K线）视为一种"语言"，通过将连续的价格变动离散化为有限的token集合，使其可以被LLM模型处理。这种方法的优势在于：

1. **利用现有的LLM架构**：可以直接使用Transformer等成熟的模型架构和训练方法
2. **捕捉复杂模式**：LLM擅长捕捉序列中的长期依赖关系和复杂模式
3. **概率化预测**：模型输出是下一个token的概率分布，提供了预测的不确定性估计

## 模块组件

### 1. CandlestickTokenizer

K线数据的tokenizer，将连续的OHLC（开盘价、最高价、最低价、收盘价）数据转换为离散的token。

主要特点：

- 使用ATR（平均真实范围）进行标准化，使不同证券的K线数据可比
- 将K线分解为四个关键特征：change（价格变动）、entity（实体大小）、upline（上影线）和downline（下影线）
- 支持特殊token，如交易日间隔、假期等
- 提供可视化工具，方便查看tokenization效果

### 2. CandlestickDataset

为LLM模型提供K线数据的PyTorch数据集。

主要特点：

- 支持单个或多个证券的数据
- 创建滑动窗口样本，用于训练和评估
- 支持时间特征，如小时、分钟、星期几等
- 提供批处理函数，方便与DataLoader一起使用

### 3. CandlestickLLM

基于Transformer架构的K线预测模型。

主要特点：

- 基于GPT架构，使用因果自注意力机制
- 支持证券代码嵌入，捕获不同证券的特性
- 支持时间特征嵌入，捕获时间相关的模式
- 提供生成函数，用于预测未来的K线序列

### 4. CandlestickGPT4

基于GPT-4架构的优化版K线预测模型。

主要特点：

- 使用RMSNorm替代LayerNorm，提高训练稳定性
- 实现旋转位置编码(RoPE)，更好地处理序列位置信息
- 支持标签平滑和辅助损失函数
- 优化的权重初始化和残差连接
- 提供更稳定的生成函数

### 5. CandlestickLLMBacktester

K线LLM模型回测器，支持对CandlestickLLM和CandlestickGPT4模型进行回测。

主要特点：

- 支持多种信号生成策略（阈值、TopK、动量、集成）
- 提供止损止盈功能
- 详细的回测指标计算（收益率、夏普比率、最大回撤等）
- 可视化回测结果
- 支持保存和加载回测结果

## 使用方法

### 1. 初始化Tokenizer

```python
from pyqlab.models.llm.candlestick_tokenizer import CandlestickTokenizer

# 创建tokenizer
tokenizer = CandlestickTokenizer(
    change_range=(-12, 12),
    entity_range=(-12, 12),
    shadow_range=(0, 7),
    atr_window=100,
    atr_mult=0.88,
    scale=10
)
```

### 2. 准备数据集

```python
from pyqlab.models.llm.candlestick_dataset import CandlestickDataset

# 创建数据集
dataset = CandlestickDataset(
    data=df,  # 包含OHLCV数据的DataFrame
    tokenizer=tokenizer,
    seq_len=30,  # 输入序列长度
    pred_len=5,  # 预测序列长度
    stride=1     # 滑动窗口步长
)

# 创建DataLoader
dataloader = dataset.get_dataloader(batch_size=32)
```

### 3. 创建和训练模型

```python
from pyqlab.models.llm.candlestick_llm import CandlestickLLM

# 创建模型
model = CandlestickLLM(
    vocab_size=tokenizer.vocab_size,
    code_size=10,  # 证券代码数量
    block_size=30,
    n_layer=12,
    n_head=12,
    d_model=768
)

# 训练模型
optimizer = model.configure_optimizers(
    weight_decay=0.1,
    learning_rate=3e-4,
    betas=(0.9, 0.95),
    device_type='cuda'
)

for epoch in range(num_epochs):
    for batch in dataloader:
        input_tokens = batch['input_tokens'].to(device)
        target_tokens = batch['target_tokens'].to(device)
        code_ids = batch['code_ids'].to(device)

        # 前向传播
        logits, loss = model(input_tokens, code_ids, targets=target_tokens)

        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
```

### 4. 生成预测

```python
# 生成预测
with torch.no_grad():
    input_tokens = batch['input_tokens'][:1].to(device)  # 取一个样本
    code_ids = batch['code_ids'][:1].to(device)

    # 生成未来的K线序列
    generated_tokens = model.generate(
        input_tokens,
        code_ids,
        max_new_tokens=10,
        temperature=0.8,
        top_k=50
    )

    # 将生成的token转换回K线数据
    atr = tokenizer._calculate_atr(df).iloc[-1]
    start_price = df['close'].iloc[-1]
    predicted_ohlc = tokenizer.tokens_to_candlesticks(
        generated_tokens[0].cpu().numpy(),
        start_price,
        atr
    )
```

## 高级用法

### 1. 使用时间特征

```python
from pyqlab.models.llm.candlestick_dataset import TimeSeriesCandlestickDataset

# 创建带有时间特征的数据集
dataset = TimeSeriesCandlestickDataset(
    data=df,
    tokenizer=tokenizer,
    seq_len=30,
    pred_len=5,
    time_features=True
)
```

### 2. 可视化tokenization效果

```python
# 可视化原始K线和tokenization后重建的K线
tokens = tokenizer.tokenize(df)
tokenizer.visualize_tokenization(df, tokens)
```

### 3. 保存和加载tokenizer

```python
# 保存tokenizer
tokenizer.save('tokenizer.pkl')

# 加载tokenizer
loaded_tokenizer = CandlestickTokenizer.load('tokenizer.pkl')
```

### 4. 使用回测器评估模型

```python
from pyqlab.models.llm.backtester import CandlestickLLMBacktester

# 创建回测器
backtester = CandlestickLLMBacktester(
    model=model,                # CandlestickLLM或CandlestickGPT4模型
    tokenizer=tokenizer,        # CandlestickTokenizer实例
    initial_capital=10000.0,    # 初始资金
    device=device,              # 计算设备
    signal_type='topk'          # 信号生成器类型（可选：'threshold', 'topk', 'momentum', 'ensemble'）
)

# 执行回测
results = backtester.backtest(
    df=data,                    # 包含OHLCV数据的DataFrame
    code_id=0,                  # 证券代码ID
    seq_len=64,                 # 序列长度
    commission=0.001,           # 交易手续费率
    threshold=0.6,              # 交易信号阈值
    stop_loss=0.05,             # 止损比例（可选）
    take_profit=0.1,            # 止盈比例（可选）
    temperature=0.8             # 温度参数
)

# 可视化回测结果
backtester.visualize_backtest(
    df=data,
    results=results,
    seq_len=64,
    save_path='backtest_chart.png'  # 保存图表的路径（可选）
)

# 保存回测结果
backtester.save_results(results, 'backtest_results.json')

# 加载回测结果
loaded_results = backtester.load_results('backtest_results.json')

# 比较多个回测策略
backtester.compare_strategies(
    results_list=[results1, results2, results3],
    names=['策略1', '策略2', '策略3'],
    save_path='strategy_comparison.png'
)
```

## 参考文献

1. Vaswani, A., et al. (2017). "Attention is All You Need." NeurIPS.
2. Brown, T. B., et al. (2020). "Language Models are Few-Shot Learners." NeurIPS.
3. Radford, A., et al. (2019). "Language Models are Unsupervised Multitask Learners." OpenAI Blog.



通过这些改进和未来方向的探索，K线数据离散化与LLM预测模型可以不断提升其预测能力和实用性，为量化交易提供更加可靠的决策支持。

## 10. 实现路线图

为了系统地实施上述改进，我建议按照以下路线图进行：

### 第一阶段：基础增强（1-2个月）

1. **特征工程增强**
   * 实现交易量特征的离散化
   * 添加基本技术指标（RSI、MACD等）
   * 实现异常检测与处理
2. **模型架构优化**
   * 实现多尺度时间特征
   * 优化位置编码
   * 添加证券代码嵌入的增强版本
3. **数据处理改进**
   * 实现数据增强技术
   * 开发多粒度K线表示
   * 改进tokenization过程

### 第二阶段：高级功能（2-3个月）

1. **高级模型架构**
   * 实现混合专家模型
   * 添加跨证券注意力机制
   * 开发多任务学习框架
2. **训练策略优化**
   * 实现课程学习
   * 添加对抗训练
   * 开发自适应学习率调度
3. **可视化与解释性**
   * 实现注意力可视化
   * 开发特征重要性分析
   * 添加预测不确定性可视化

### 第三阶段：部署与集成（1-2个月）

1. **模型优化**
   * 实现模型量化
   * 优化推理性能
   * 导出为ONNX格式
2. **实时预测服务**
   * 开发API接口
   * 实现实时数据处理
   * 添加缓存机制
3. **回测与评估**
   * 完善回测框架
   * 添加多种评估指标
   * 实现结果可视化

### 第四阶段：高级集成（2-3个月）

1. **多模型集成**
   * 实现不同架构模型的集成
   * 开发动态权重调整
   * 添加模型选择机制
2. **多市场适应**
   * 开发市场特定参数
   * 实现跨市场知识迁移
   * 添加市场特征提取
3. **多模态融合**
   * 集成新闻数据
   * 添加市场情绪分析
   * 开发多模态融合策略
4. **交易策略集成**
   * 实现概率交易框架
   * 开发风险管理模块
   * 集成强化学习优化

## 11. 实际应用案例

以下是几个实际应用案例，展示如何将这些改进应用到实际交易中：

### 案例1：日内交易策略

```python
# 示例：使用增强的K线LLM模型进行日内交易

# 1. 初始化模型和tokenizer
tokenizer = CandlestickTokenizer(
    change_range=(-15, 15),  # 扩大范围以捕捉更大的价格变动
    entity_range=(-15, 15),
    shadow_range=(0, 10),
    atr_window=50,  # 较短的窗口以适应日内波动
    atr_mult=0.95,
    scale=8
)

# 2. 加载预训练模型
model = CandlestickLLM.load_from_checkpoint('path/to/intraday_model.pt')

# 3. 创建实时预测服务
prediction_service = RealTimePredictionService(model, tokenizer)

# 4. 创建风险管理器
risk_manager = RiskManager(
    max_position=0.2,  # 最大仓位20%
    max_drawdown=0.05,  # 最大回撤5%
    stop_loss=0.01,     # 止损1%
    take_profit=0.03    # 止盈3%
)

# 5. 日内交易循环
def intraday_trading_loop(symbols, interval='5min'):
    while market_is_open():
        for symbol in symbols:
            # 获取最新K线数据
            latest_data = fetch_latest_data(symbol, interval, lookback=50)

            # 更新预测服务
            prediction_service.update_data(symbol, latest_data.iloc[-1].to_dict())

            # 获取预测
            prediction = prediction_service.predict(symbol, get_code_id(symbol))

            # 获取交易信号
            signal = prediction_service.get_trading_signal(symbol, threshold=0.7)

            # 当前价格
            current_price = latest_data['close'].iloc[-1]

            # 计算波动性
            volatility = latest_data['high'].pct_change().rolling(20).std().iloc[-1]

            if signal['signal'] in ['BUY', 'SELL']:
                # 调整仓位大小
                position_size = risk_manager.adjust_position_size(
                    symbol, signal, current_price, signal['confidence'], volatility
                )

                # 执行交易
                if signal['signal'] == 'BUY':
                    place_order(symbol, 'BUY', position_size)
                else:
                    place_order(symbol, 'SELL', position_size)

            # 检查止损止盈
            stop_action = risk_manager.check_stop_orders(symbol, current_price)
            if stop_action:
                place_order(symbol, stop_action['action'], None)

            # 检查风险限制
            risk_ok, message = risk_manager.check_risk_limits(calculate_total_equity())
            if not risk_ok:
                close_all_positions()
                send_alert(message)
                break

        # 等待下一个间隔
        time.sleep(60)  # 1分钟
```

### 案例2：多时间框架交易策略

```PYTHON
# 示例：使用多时间框架融合进行交易

# 1. 初始化多时间框架tokenizer
multi_tf_tokenizer = MultiTimeframeTokenizer(
    base_tokenizer=CandlestickTokenizer(),
    timeframes=['5m', '15m', '1h', '4h', '1d']
)

# 2. 加载多个模型（每个对应一个时间框架）
models = {
    '5m': CandlestickLLM.load_from_checkpoint('path/to/5m_model.pt'),
    '15m': CandlestickLLM.load_from_checkpoint('path/to/15m_model.pt'),
    '1h': CandlestickLLM.load_from_checkpoint('path/to/1h_model.pt'),
    '4h': CandlestickLLM.load_from_checkpoint('path/to/4h_model.pt'),
    '1d': CandlestickLLM.load_from_checkpoint('path/to/1d_model.pt')
}

# 3. 创建多时间框架预测器
class MultiTimeframePredictorService:
    def __init__(self, models, tokenizer):
        self.models = models
        self.tokenizer = tokenizer

    def predict(self, data_dict, code_id=0):
        """生成多时间框架预测"""
        predictions = {}

        # 对每个时间框架进行预测
        for tf, df in data_dict.items():
            if tf in self.models:
                # Tokenize
                tokens_dict = self.tokenizer.tokenize_multi_timeframe(df)
                tokens = tokens_dict[tf]

                # 预测
                input_tokens = torch.tensor(tokens, dtype=torch.long).unsqueeze(0)
                code_tensor = torch.tensor([code_id], dtype=torch.long)

                with torch.no_grad():
                    logits, _ = self.models[tf](input_tokens, code_tensor)

                # 获取最后一个时间步的logits
                last_logits = logits[0, -1]

                # 计算概率分布
                probs = F.softmax(last_logits, dim=-1).cpu().numpy()

                # 计算方向概率
                direction_probs = {
                    'up': sum(probs[i] for i, token in enumerate(self.tokenizer.base_tokenizer.idx2token.values())
                             if '|' in token and int(token.split('|')[0]) > 0),
                    'flat': sum(probs[i] for i, token in enumerate(self.tokenizer.base_tokenizer.idx2token.values())
                               if '|' in token and int(token.split('|')[0]) == 0),
                    'down': sum(probs[i] for i, token in enumerate(self.tokenizer.base_tokenizer.idx2token.values())
                               if '|' in token and int(token.split('|')[0]) < 0)
                }

                predictions[tf] = direction_probs

        # 融合预测
        fused_prediction = self._fuse_timeframe_predictions(predictions)

        return fused_prediction

    def _fuse_timeframe_predictions(self, predictions):
        """融合不同时间框架的预测"""
        # 时间框架权重（较长时间框架权重更高）
        weights = {
            '5m': 0.1,
            '15m': 0.15,
            '1h': 0.2,
            '4h': 0.25,
            '1d': 0.3
        }

        # 初始化融合预测
        fused = {
            'up': 0.0,
            'flat': 0.0,
            'down': 0.0
        }

        # 加权融合
        total_weight = 0
        for tf, pred in predictions.items():
            weight = weights.get(tf, 0.0)
            total_weight += weight

            for direction in fused:
                fused[direction] += pred.get(direction, 0.0) * weight

        # 归一化
        if total_weight > 0:
            for direction in fused:
                fused[direction] /= total_weight

        return fused

# 4. 创建交易策略
def multi_timeframe_trading_strategy(symbols):
    # 创建预测服务
    predictor = MultiTimeframePredictorService(models, multi_tf_tokenizer)

    # 创建风险管理器
    risk_manager = RiskManager()

    for symbol in symbols:
        # 获取多时间框架数据
        data_dict = {}
        for tf in ['5m', '15m', '1h', '4h', '1d']:
            data_dict[tf] = fetch_data(symbol, tf, lookback=100)

        # 获取预测
        prediction = predictor.predict(data_dict, get_code_id(symbol))

        # 生成交易信号
        signal = None
        if prediction['up'] > 0.7:
            signal = {'signal': 'BUY', 'strength': prediction['up']}
        elif prediction['down'] > 0.7:
            signal = {'signal': 'SELL', 'strength': prediction['down']}

        # 执行交易
        if signal:
            current_price = data_dict['5m']['close'].iloc[-1]
            volatility = data_dict['1h']['close'].pct_change().std() * np.sqrt(20)

            position_size = risk_manager.adjust_position_size(
                symbol, signal, current_price, signal['strength'], volatility
            )

            execute_trade(symbol, signal['signal'], position_size)
```

### 案例3：多模态交易策略

```python
# 示例：结合K线、新闻和情绪数据的多模态交易策略

# 1. 初始化模型
candlestick_model = CandlestickLLM.load_from_checkpoint('path/to/candlestick_model.pt')
news_model = NewsModel.load_from_checkpoint('path/to/news_model.pt')
sentiment_model = SentimentModel.load_from_checkpoint('path/to/sentiment_model.pt')

# 2. 创建多模态融合器
fusion_model = MultiModalFusion(
    candlestick_model=candlestick_model,
    news_model=news_model,
    sentiment_model=sentiment_model,
    fusion_weights={
        'candlestick': 0.6,
        'news': 0.25,
        'sentiment': 0.15
    }
)

# 3. 创建回测器
backtester = MultiModalBacktester(fusion_model, initial_capital=100000)

# 4. 回测策略
def backtest_multimodal_strategy(symbol, start_date, end_date):
    # 获取K线数据
    candlestick_data = fetch_candlestick_data(symbol, start_date, end_date)

    # 获取新闻数据
    news_data = fetch_news_data(symbol, start_date, end_date)

    # 获取情绪数据
    sentiment_data = fetch_sentiment_data(symbol, start_date, end_date)

    # 执行回测
    results = backtester.backtest(
        candlestick_data=candlestick_data,
        news_data=news_data,
        sentiment_data=sentiment_data,
        code_id=get_code_id(symbol),
        threshold=0.65,
        commission=0.001
    )

    # 可视化结果
    backtester.visualize_backtest(candlestick_data, results)

    return results
```

## 12. 性能基准与评估

为了评估改进的效果，我们可以设立以下性能基准：

### 预测准确性指标

* **方向准确率** ：预测价格变动方向的准确率
* **RMSE** ：预测价格与实际价格的均方根误差
* **MAE** ：预测价格与实际价格的平均绝对误差
* **信息系数(IC)** ：预测值与实际值的相关系数

### 交易性能指标

* **年化收益率** ：策略的年化收益
* **夏普比率** ：风险调整后的收益
* **最大回撤** ：最大的亏损幅度
* **胜率** ：盈利交易占总交易的比例
* **盈亏比** ：平均盈利交易与平均亏损交易的比率
* **卡玛比率** ：年化收益率与最大回撤的比率

### 计算性能指标

* **推理时间** ：生成一个预测所需的时间
* **内存使用** ：模型运行时的内存占用
* **吞吐量** ：单位时间内能处理的预测请求数

通过这些指标，我们可以全面评估改进的效果，并针对性地进行进一步优化。

## 结论

K线数据离散化与LLM预测模型是一个充满潜力的研究方向，通过将金融时间序列视为一种"语言"，我们可以利用现有的LLM架构和技术来捕捉市场中的复杂模式。本文提出的改进建议涵盖了从特征工程、模型架构到部署优化的多个方面，旨在提高模型的预测能力和实用性。

通过系统地实施这些改进，并结合实际交易策略，我们可以构建一个强大的量化交易系统，为投资决策提供可靠的支持。同时，这些技术也可以应用到其他时间序列预测领域，如能源需求预测、气象预测等。

未来，随着深度学习和自然语言处理技术的不断发展，我们可以期待更多创新方法的出现，进一步提升K线数据离散化与LLM预测模型的性能和应用范围。

通过这些改进和未来方向的探索，K线数据离散化与LLM预测模型可以不断提升其预测能力和实用性，为量化交易提供更加可靠的决策支持。

